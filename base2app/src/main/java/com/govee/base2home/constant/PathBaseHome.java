package com.govee.base2home.constant;

public class PathBaseHome {

    /**
     * 服务相关URL定义
     */
    public static final String URL_BASE2LIGHT_MODULE = "/base2light/service";

    /**
     * 通用配对页面
     */
    public static final String URL_COMMON_PAIR = "/base2light/common/pair";

    /**
     * 通用的命名页
     */
    public static final String URL_COMMON_NAME = "/base2light/common/name";

    /**
     * 通用的节能模式wifi设置页
     */
    public static final String URL_COMMON_ENERGY_SAVING_WIFI = "/base2light/common/EnergySavingWifi";

    /**
     * 通用的普通模式wifi设置页
     */
    public static final String URL_COMMON_NORMAL_WIFI = "/base2light/common/NormalWifi";

    /**
     * tag视频列表ac
     */
    public static final String URL_TAG_VIDEO_LIST = "/base2light/tag/videoList";

    /**
     * H6057详情页
     */
    public static final String URL_H6057_DETAIL = "/h6057/detail";

    /**
     * H70b1详情页
     */
    public static final String URL_H70B1_DETAIL = "/h70b1/detail";

    /**
     * H70b1校准方向
     */
    public static final String URL_H70B1_CHECK_DIRECTION = "/h70b1/checkDirection";

    /**
     * H61c3详情页
     */
    public static final String URL_H61C3_DETAIL = "/h61c3/detail";

    /**
     * H61c3校准页面
     */
    public static final String URL_H61C3_CHECK_DIRECTION = "/h61c3/checkDirection";

    /**
     * H705a详情页
     */
    public static final String URL_H705a_DETAIL = "/h705a/detail";

    /**
     * H705a段数设置
     */
    public static final String URL_H705a_Segment_Setting = "/h705a/segmentSetting";

    /**
     * H7026 段数设置
     */
    public static final String URL_H7026_Segment_Setting = "/h7020/segmentSetting";


    /**
     * 段数设置页面  H706A/H706B/H706C
     */
    public static final String URL_H706a_Segment_Setting = "/h706a/segmentSetting";

    /**
     * App的page界面跳转关联
     */
    public static final String URL_APP_PAGE = "/app/page";

    /**
     * base2home模块下的统计
     */
    public static final String URL_APP_ANALLYTIC = "/base2home/analytic";

    /**
     * H6601详情页
     */
    public static final String URL_H6601_DETAIL = "/h6601/detail";

    /**
     * H6601详情页
     */
    public static final String URL_H6602_DETAIL = "/h6602/detail";

    /**
     * 6601-4, hdmi详情页
     */
    public static final String URL_HDMI_DETAIL_6601_4 = "/hdmi/detail";

    /**
     * dreamcolorlightv1的wifi设置页
     */
    public static final String URL_DREAM_COLOR_LIGHT_V1_WIFI = "/dreamcolorlightv1/wifisetting";

    /**
     * H5 通用的设备交互
     */
    public static final String URL_COMMON_DEVICE_INTERACTION = "/common/device/interaction";

    /**
     * 分享设备消息详情页
     */
    public static final String URL_SHARE_MSG_DETAIL = "/share/device/msg/detail/activity";

    /**
     * 分享设备列表页
     */
    public static final String URL_SHARE_DEVICE_LIST = "/share/device/list/activity";


    /**
     * H608a裁剪校准
     */
    public static final String URL_H608a_cut_cali = "/h608a/cutcali";

    /**
     * H608a详情页
     */
    public static final String URL_H608a_DETAIL = "/h608a/detail";

    /**
     * 球泡灯新详情页
     */
    public static final String URL_BULB_NEW_DETAIL = "/h7020/new_detail";

    /**
     * sku 配件购买入口管理
     */
    public static final String URL_SKU_ACCESSORY_MGR = "/h71xx/sku/shop_accessory";

    /**
     * 61D3 wifi设置页
     */
    public static final String URL_WIFI_CHOOSE_H61D3 = "/61d3/wifi/choose/activity";

    /**
     * 61D3 拍照识别页
     */
    public static final String URL_TAKE_PHOTO_H61D3 = "/61d3/take/photo/activity";


    /**
     * 703A/B 灯数设置
     */
    public static final String URL_H703A_CUT_CALI = "/703a/cut/cali";

    /**
     * 703A/B 详情页
     */
    public static final String URL_H703A_DETAIL = "/703a/detail";

    /**
     * 6032 详情页
     */
    public static final String URL_H6032_DETAIL = "/h6032/detail";
    /**
     * 6032 最低亮度设置
     */
    public static final String URL_H6032_BRIGHTNESS_ADJUST = "/h6032/brightness/adjust";

    /**
     * 6032 安装引导
     */
    public static final String URL_VIDEO_GUIDE_H6032 = "/h6032/guide/video";

    /**
     * h6020 详情页
     */
    public static final String URL_SKU_DETAIL_H6020 = "/h6020/detail/activity";

    /**
     * H6022 详情页
     */
    public static final String URL_SKU_DETAIL_H6022 = "/h6020/detail/activityH6022";

    public static final String URL_H6022_WIFI = "/h6020/detail/H6022Wifi";

    /**
     * H7184详情页
     */
    public static final String URL_H7184_DETAIL = "/h7184/detail";
    /**
     * H7184引导
     */
    public static final String URL_H7184_GUIDE = "/h7184/guide";
    /**
     * H7129详情页
     */
    public static final String URL_H7129_DETAIL = "/h7129/detail";
    /**
     * H7149详情页
     */
    public static final String URL_H7149_DETAIL = "/h7149/detail";
    /**
     * H70b345校准方向
     */
    public static final String URL_H70B345_CHECK_DIRECTION = "/h70b345/checkDirection";

    /**
     * H681x配置安装校准
     */
    public static final String URL_H681X_CHECK_DIRECTION = "/h70b345/h681x/checkDirection";

    /**
     * H70B345详情页
     */
    public static final String URL_H70B345_DETAIL = "/h70b345/detail/activity";

    public static final String URL_H70B6_CHECK_DIRECTION = "/h70b345/checkDirection4H70B6";
    public static final String URL_H70B6_DETAIL = "/h70b345/detail4h70b6/activity";
    public static final String URL_H70B6_SET_WIFI = "/h70b345/setWifi4H70B6";

    /**
     * H705DEF/H805ABC详情页
     */
    public static final String URL_H705DEF_H805ABC_NEW_DETAIL = "/h705def/new_detail";

    /**
     * H706789 详情页
     */
    public static final String URL_H706789_NEW_DETAIL = "/h705def/new_detail_h706789";
    /**
     * H7112详情页
     */
    public static final String URL_H7112_DETAIL = "/h7111/h7112detail";


    //rgbic灯带新详情页
    public static final String URL_RGBIC_NEW_DETAIL = "/rgbic/newdetail";

    public static final String URL_RGBIC_NEW_DETAIL_H6105 = "/rgbic/newdetailH6105";

    public static final String URL_CAR_LIGHT_NEW_DETAIL = "/h7090/newdetail";

    /**
     * H705DEF/H805ABC场景分段设置
     */
    public static final String URL_H705DEF_H805ABC_SEGMENT_SET = "/h705def/segment_set";


    /**
     * H6640\6641\61E5\61E6 详情页
     */
    public static final String URL_H664041E5E6_NEW_DETAIL = "/61d3/new_detail_h664041e5e6";


    //612x长度校准
    public static final String URL_H612X_CUT_CALI = "/h612x/cut/cutcali";

    //61c3灯带校准
    public static final String URL_H61C3_CUT_CALI = "/h61c3/cut/cutcali";


    /**
     * H7126详情页
     */
    public static final String URL_H7126_DETAIL = "/h7126/detail";

    /**
     * H7057/H7058详情页
     */
    public static final String URL_H7057_H7058_NEW_DETAIL = "/h7057/new_detail";

    //rgb灯带新详情页
    public static final String URL_RGB_NEW_DETAIL = "/rgb/newdetail";

    /**
     * stringlightv2的wifi设置页
     */
    public static final String URL_STRING_LIGHT_V2_WIFI = "/stringlightv2/wifisetting";

    /**
     * 联名款扫码界面
     */
    public static final String URL_JOINTLY_SCAN = "/jointly/scan";

    /**
     * H70D123校准方向
     */
    public static final String URL_H70D123_CHECK_DIRECTION = "/h70dx/checkDirection";

    /**
     * H70D123详情页
     */
    public static final String URL_H70D123_DETAIL = "/h70dx/detail/activity";

    /**
     * H7140详情页
     */
    public static final String URL_H7140_DETAIL = "/h7140/detail";

    /**
     * AI效果跳转DIY编辑页面编辑
     */
    public static final String URL_EDIT_AI_EFFECT = "/h70b345/aiEffectEdit/toDiy";

    public static final String URL_ONE_OTA_CLICK = "/app/onekeyUpgrade";

    /**
     * H7173 H717C详情页
     */
    public static final String URL_H7173_DETAIL = "/h7173/detail";

    /**
     * 71线秘钥存储的读写
     */
    public static final String URL_H71XX_SECRET_KEY_CONFIG = "/h71xx/common/secretKey";

    public static final String URL_ONE_CLICK_EDIT_AC = "/app/oneclick_edit";

    public static final String URL_ADD_RHYTHM_DETAIL_AC = "/app/rhythm_detail";

    public static final String URL_ECO_ONE_CLICK_EDIT_AC = "/eco/oneclick/edit";

    /**
     * 自动执行详情页
     */
    public static final String URL_ECO_AUTO_EXECUTE_EDIT_AC = "/eco/autoExecute/edit";

    /**
     * 自动执行：选择日出/日落页面
     */
    public static final String URL_ECO_TRIGGER_SUNRISE_SUNSET_AC = "/eco/trigger/sunrise_sunset";

    /**
     * 自动执行：选择设备的触发动作页面
     */
    public static final String URL_ECO_TRIGGER_ACTION_AC = "/eco/trigger/deviceAction";

    /**
     * 自动执行：选择触发条件页面
     */
    public static final String URL_ECO_SELECT_TRIGGER_CONDITION = "/eco/select/triggerCondition";

    //灯泡新详情页
    public static final String URL_BULB_LIGHT_NEW_DETAIL = "/bulb/newdetail";

    //灯泡wifi页
    public static final String URL_BULB_LIGHT_WIFI = "/bulblightv3/wifi";

    /**
     * H6630、H6631 详情页
     */
    public static final String URL_H6630_DETAIL = "/h6630/detail";

    /**
     * H6630、H6631 wifi设置页
     */
    public static final String URL_H6630_WIFI = "/h6630/wifi";
    /**
     * h682021详情页
     */
    public static final String URL_H682021_DETAIL = "/h682021/detail/activity";

    /**
     * H6800详情页
     */
    public static final String URL_H6800_DETAIL = "/h70b345/detail/h6800activity";

    //6840详情页
    public static final String URL_H6840_DETAIL = "/h6840/detailsAc";
    //6840选择Wi-Fi页
    public static final String URL_H6840_CHOOSE_WIFI = "/h6840/choose_wifi";

    //6038设备校准页面
    public static final String URL_H6038_DEVICE_CALIBRATION = "/h6047/h6038_device_calibration";
    //6038详情页
    public static final String URL_H6038_DETAIL = "/h6047/h6038_detail";
    //6840安装校准
    public static final String url_h6840_device_calibration = "/h6840/device_calibration";
    //H6840/41命名页面
    public static final String url_h6840_device_setting_name = "/h6840/setting_device_name";

    //空间音乐盛宴详情页
    public static final String URL_ROOM_FEAST = "/room/feast";

    //群控列表fragment
    public static final String URL_APP_FRAGMENT_GROUP_CONTROL_LIST = "/app/fragment/group_control_list";
    //盛宴列表fragment
    public static final String URL_APP_FRAGMENT_FEAST_LIST = "/app/fragment/feast_list";

    //H7086 详情页
    public static final String URL_SKU_DETAIL_H7086 = "/h7086/detailsAc";
    //H7086 灯头校准
    public static final String URL_SKU_DETAIL_H7086_CAP_CALI = "/h7086/capCaliAc";
    //H7076 详情页
    public static final String URL_SKU_DETAIL_H7076 = "/h7075/h7076_detailsAc";

    //H60B1 详情页
    public static final String URL_SKU_DETAIL_H60B1 = "/h60B1/detailsAc";
    public static final String URL_SKU_WIFI_H60B1 = "/h60B1/choose_wifi";

    public static final String URL_WIDGET_SERVICE_VERSION1 = "/thnew/widget/service";
    public static final String URL_WIDGET_SERVICE_H5106 = "/h5106/widget/service";
    public static final String URL_WIDGET_SERVICE_H5140 = "/h5140/widget/service";
    public static final String URL_WIDGET_SERVICE_H5107 = "/h5043/widget/service";
    public static final String URL_WIDGET_SERVICE_H5109 = "/h5042/widget/service";

    //6001 timer
    public static final String URL_H6001_TIMER_AC = "/h6001/URL_H6001_TIMER_AC";

    //H60B0选择Wi-Fi页
    public static final String URL_H60B0_CHOOSE_WIFI = "/h60b0/choose_wifi";
    //H6OBO详情页
    public static final String URL_H60B0_DETAIL = "/h60b0/detailsAc";

    //6003 wifi
    public static final String URL_H6003_WIFI_AC = "/h6003/URL_H6003_WIFI_AC";

    //H6076详情页
    public static final String URL_SKU_DETAIL_STRAIGHTFLOORLAMP = "/straightfloorlamp/detailsAc";

    /**
     * RoadMap详情
     */
    public static final String URL_APP_ROADMAP_REVIEW = "/app/RoadMapReviewAc";

    /**
     * 社区应用
     */
    public static final String URL_COMMUNITY_APPLY = "/base2light/community/apply";


    //H7056 详情页
    public static final String URL_SKU_DETAIL_H7056 = "/h7056/detailsAc";
    public static final String URL_SKU_WIFI_H7056 = "/h7056/choose_wifi";

    /**
     * H8121详情页
     */
    public static final String URL_ICE_MACHINE_H8121_DETAIL = "/iceMachine/H8121Detail";
    /**
     * H8120详情页
     */
    public static final String URL_ICE_MACHINE_H8120_DETAIL = "/iceMachine/H8120Detail";

    //H60C1详情页
    public static final String URL_H60C1_DETAIL = "/h60c1/detailsAc";

    //生态执行动作选择页面
    public static final String ECO_ACTION_SELECT_AC = "/eco/action/selectAc";

    /**
     * 拍照/视频页面
     */
    public static final String URL_CAPTURE_IMAGE_VIDEO = "/base2home/VideoRecorder";

    /**
     * 卡片通信路由
     */
    public static final String URL_CARD_COMM = "/service/card/comm";


    /**
     * 卡片灯效相关路由
     */
    public static final String URL_CARD_LIGHT_EFFECT = "/service/lighteffect";

    /**
     * 社区module跳转
     */
    public static final String URL_COMMUNITY_PAGE = "/community/page";


    //H609d 详情页
    public static final String URL_SKU_DETAIL_H609D = "/h6092/H609dDetail";
    public static final String URL_SKU_GUIDE_H609D = "/h6092/H609dGuide";

    /**
     * H6099/H6098详情页
     */
    public static final String URL_DETAIL_H6099_H6098 = "/h6099/detail";

    /**
     * H605A详情页
     */
    public static final String URL_DETAIL_H605A = "/h605a/detail";

    /**
     * H6097详情页
     */
    public static final String URL_DETAIL_H6097 = "/h6097/detail";

    /**
     * H605C/H605B
     */
    public static final String URL_DETAIL_H605C = "/h605c/detail";

    //H707ABC 详情页
    public static final String URL_SKU_DETAIL_H707ABC = "/h7075/h707a_detailsAc";
    //707ABC 段数设置
    public static final String URL_707ABC_SEGMENT_SETTING = "/h7075/h707a_segmentSetting";
    public static final String URL_707ABC_SEGMENT_SET = "/h7075/h707a_set";
    public static final String URL_707ABC_DIRECTION_SETTING = "/h7075/h707a_set_direction";
    /**
     * 温湿度计数据库相关的服务
     */
    public static final String URL_TH_DATA_SERVICE = "/base2newth/thDataService";


    //H6671/72新详情页
    public static final String URL_H6671_72_DETAIL = "/h667172/newdetail";

    /**
     * H7073详情页
     */
    public static final String URL_SKU_DETAIL_H7073 = "/h6092/H7073dDetail";
}