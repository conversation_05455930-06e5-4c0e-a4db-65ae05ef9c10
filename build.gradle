import java.nio.charset.Charset

//<editor-fold desc="基础项目配置-此配置之上-不再允许补充其他内容，相关内容需补充在下方">
buildscript {
    ext.kotlin_version = '2.0.21'
    /*ksp版本格式（Kotlin版本-KSP版本）*/
    ext.ksp_version = '2.0.21-1.0.28'
    ext.objectboxVersion = '4.0.3'
    ext.composeCompilerVersion = '2.0.21'
    repositories {
        mavenLocal()
        google()
        mavenCentral()
        maven {
            url 'https://oss.sonatype.org/content/repositories/snapshots/'
        }
        maven {
            url "https://dl.bintray.com/yahoo/maven/"//Yahoo's Maven repository
        }
        maven { url 'https://csspeechstorage.blob.core.windows.net/maven/' }
        maven { url uri(project.rootDir.path + "/repo") }
        maven {
            url "https://oss.sonatype.org/content/repositories/snapshots"
        }
        // 处理jcenter仓库无法下载问题
        maven { url 'https://maven.aliyun.com/repository/public' }
        maven { url 'https://www.jitpack.io' }
        gradlePluginPortal()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.11.1'
        classpath "io.objectbox:objectbox-gradle-plugin:$objectboxVersion"
        classpath 'com.google.gms:google-services:4.4.3'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.5'
        classpath 'com.jakewharton:butterknife-gradle-plugin:10.2.3'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
        classpath 'com.govee.lifecycle:lifecycle-plugin:2.0.1'
        classpath "com.github.jadepeakpoet.ARouter:arouter-register:1.0.3"
        classpath "org.jetbrains.kotlin:compose-compiler-gradle-plugin:$composeCompilerVersion"
        classpath "com.google.devtools.ksp:com.google.devtools.ksp.gradle.plugin:$ksp_version"
    }
}

plugins {
    id 'com.google.devtools.ksp' version "$ksp_version" apply false
}

allprojects {
    apply from: project.rootDir.path + "/module_utils.gradle"
    repositories {
        mavenLocal()
        google()
        mavenCentral()
        maven {
            /*Google's Maven repository*/
            url "https://maven.google.com"
        }
        maven {
            /*Git相关*/
            url 'https://jitpack.io'
        }
        maven {
            /*Yahoo's Maven repository*/
            url "https://dl.bintray.com/yahoo/maven/"
        }
        maven {
            url uri(project.rootDir.path + "/repo")
        }
        maven {
            url "https://oss.sonatype.org/content/repositories/snapshots"
        }
        // 处理jcenter仓库无法下载问题
        maven { url 'https://maven.aliyun.com/repository/public' }

        maven { url 'https://www.jitpack.io' }
        gradlePluginPortal()
    }
}
//</editor-fold>

/*打包的服务器环境配置*/
def RUN_MODE = "ci"/*dev环境*/
//def RUN_MODE = "pr"/*pda环境*/
//def RUN_MODE = "qa_inner"/*pre环境*/
//def RUN_MODE = "qa"/*正式环境*/

/*APP版本Code*/
def VERSION_CODE = 960
/*APP版本名称*/
def VERSION_NAME = "7.1.00"

/*release情况是否打开shrinkResources*/
def ATTR_SHRINKRESOURCES_RELEASE = true

/*是否打包全量apk(包含动态包)*/
def APP_PACKAGE_WHOLE = false

/**是否是官方全量APK*/
def OFFICIAL_FULL_APK = false

/**是否是开发运行打包-此字段值不允许开发变更属性值，默认就是true*/
def DEVELOPER_RUN_MODE = true

/*默认使用 release,打开 fastRun 使用 debug */
def IS_FAST_RUN = Boolean.parseBoolean(System.properties.getProperty("GoveeHomeIsFastRun"))
println("IS_FAST_RUN = " + IS_FAST_RUN)
def USE_RELEASE_VARIANT = !IS_FAST_RUN

/**是否不使用AAR的依赖关系而是使用源码-默认带FastRun文件的都是才有aar进行默认配置依赖*/
def APP_SUB_LIB_DEPENDENCIES_SOURCE = !IS_FAST_RUN

apply from: project.rootDir.path + "/module_config.gradle"
apply from: project.rootDir.path + "/module_utils.gradle"
try {
    apply from: project.rootDir.path + "/FastRun/fastrun_config.gradle"
} catch (Exception e) {
    e.printStackTrace()
}

tasks.register('changeAppConfigProperties') {
    group("goveehometask")
    description("依据配置的执行环境更改<app>lib下的config.properties文件信息")
    def versionNameSplit = VERSION_NAME.split("\\.")
    println "VERSION_NAME = $VERSION_NAME ; versionNameSplit.len = $versionNameSplit.length ; versionNameSplit = $versionNameSplit"
    if (versionNameSplit.length != 3 || versionNameSplit[2].length() != 2) {
        throw new MissingPropertyException("versionName = " + VERSION_NAME + " 格式不对，必须是\"x.x.xx\"!!")
    }
    /**若是打官方全量APK包，则默认打全量包APK文件格式*/
    if (OFFICIAL_FULL_APK) {
        APP_PACKAGE_WHOLE = true
    }
    println("APP_PACKAGE_WHOLE = " + APP_PACKAGE_WHOLE)
    /**判断是否是Jenkins打包机执行*/
    println "execute ---> changeAppConfigProperties RUN_MODE = $RUN_MODE  envName:${System.getenv("envName")}"
    String envName = System.getenv("envName")
    if (envName != null && !envName.isEmpty()) {
        /*配置是否打全量apk（包含动态库）*/
        APP_PACKAGE_WHOLE = Boolean.parseBoolean(System.getenv("packageWhole"))
        Boolean checkerTask = Boolean.parseBoolean(System.getenv("CodeCheckerTask"))
        if (checkerTask) {
            apply from: "./config/CodeCheckerTask.gradle"
        }
        ATTR_SHRINKRESOURCES_RELEASE = false
        if (envName == "dev环境") {
            RUN_MODE = "ci"
        } else if (envName == "pda环境") {
            RUN_MODE = "pr"
        } else if (envName == "pre环境") {
            RUN_MODE = "qa_inner"
        } else if (envName == "正式环境") {
            RUN_MODE = "qa"
        }
    } else if (DEVELOPER_RUN_MODE) {
        VERSION_NAME = VERSION_NAME.substring(0, 5)
        println "VERSION_NAME = $VERSION_NAME"
    }
    File configFile = file("./app/src/main/assets/config.properties")
    List<String> newLines = new ArrayList<>()
    /*第一行配置-runMode*/
    newLines.add("run_mode=" + RUN_MODE)
    /*写入文件*/
    FileWriter fileWriter = new FileWriter(configFile)
    BufferedWriter bufferedWriter = new BufferedWriter(fileWriter)
    for (String line : newLines) {
        bufferedWriter.write(line)
        bufferedWriter.newLine()
    }
    bufferedWriter.flush()
    bufferedWriter.close()
    fileWriter.close()

    println "execute ---> APP_PACKAGE_WHOLE = $APP_PACKAGE_WHOLE"
}

tasks.register("subLibDependenciesSourceCheck") {
    group("goveehometask")
    description("当前编译是否使用子项目源码")
    println "execute ---> subLibDependenciesSourceCheck APP_SUB_LIB_DEPENDENCIES_SOURCE = " + APP_SUB_LIB_DEPENDENCIES_SOURCE
    File f = file("gradle.properties")
    List<String> lines = f.readLines()
    List<String> newLines = new ArrayList<>()
    for (String line : lines) {
        /*更新过滤参数*/
        if (line.startsWith("subLibDependenciesSource=")) {
            newLines.add("subLibDependenciesSource=" + APP_SUB_LIB_DEPENDENCIES_SOURCE)
        } else {
            newLines.add(line)
        }
    }
    /*写入文件*/
    FileWriter fileWriter = new FileWriter(f)
    BufferedWriter bufferedWriter = new BufferedWriter(fileWriter)
    for (String line : newLines) {
        bufferedWriter.write(line)
        bufferedWriter.newLine()
    }
    bufferedWriter.flush()
    bufferedWriter.close()
    fileWriter.close()
}


/*非正式环境下的Lib是否需要被屏蔽引用*/
tasks.register('noQaLibFilterCheck') {
    group("goveehometask")
    description("非正式环境下的Lib是否需要被屏蔽引用")
    println "execute ---> noQaLibFilterCheck"

    File f = file("gradle.properties")
    List<String> lines = f.readLines()
    List<String> newLines = new ArrayList<>()
    for (String line : lines) {
        /*更新过滤参数*/
        if (line.startsWith("noQaLibFilter=")) {
            def filter = "qa" == RUN_MODE
            println "noQaLibFilter = $filter"
            newLines.add("noQaLibFilter=" + filter)
        } else {
            newLines.add(line)
        }
    }
    /*写入文件*/
    FileWriter fileWriter = new FileWriter(f)
    BufferedWriter bufferedWriter = new BufferedWriter(fileWriter)
    for (String line : newLines) {
        bufferedWriter.write(line)
        bufferedWriter.newLine()
    }
    bufferedWriter.flush()
    bufferedWriter.close()
    fileWriter.close()
}
/*网络安全配置放开配置检测*/
tasks.register('networkSecurityCheck') {
    group("goveehometask")
    description("是否放开网络明文限制")
    Boolean networkSecurity4Debug = Boolean.parseBoolean(System.getenv("networkSecurity4Debug"))
    if (networkSecurity4Debug == null || !networkSecurity4Debug) {
        return
    }
    println "execute ---> networkSecurityCheck networkSecurity4Debug = " + networkSecurity4Debug
    File f = file("./app/src/main/res/xml/app_network_security_config.xml")
    List<String> lines = f.readLines("UTF-8")
    List<String> newLines = new ArrayList<>()
    for (String line : lines) {
        /* 更新过滤参数 */
        if (line.contains("<base-config cleartextTrafficPermitted=")) {
            newLines.add("    <base-config cleartextTrafficPermitted=\"true\">\n" +
                    "        <trust-anchors>\n" +
                    "            <certificates src=\"system\" />\n" +
                    "            <certificates src=\"user\" />\n" +
                    "        </trust-anchors>\n" +
                    "    </base-config>")
        } else {
            newLines.add(line)
        }
    }
    /*写入文件*/
    FileWriter fileWriter = new FileWriter(f, Charset.forName("UTF-8"))
    BufferedWriter bufferedWriter = new BufferedWriter(fileWriter)
    for (String line : newLines) {
        bufferedWriter.write(line)
        bufferedWriter.newLine()
    }
    bufferedWriter.flush()
    bufferedWriter.close()
    fileWriter.close()
}

noQaLibFilterCheck.dependsOn changeAppConfigProperties
networkSecurityCheck.dependsOn noQaLibFilterCheck
subLibDependenciesSourceCheck.dependsOn networkSecurityCheck


/*配置多module引用的第三方库的版本号*/
ext {
    BUTTERKNIFE_VERSION = "10.2.3"
    /*aws-android-iot版本2.16.x以上需要android api >=24*/
    IOT_VERSION_FINAL = "2.15.2"
    /*OkHttp网络框架*/
    OKHTTP_VERSION_FINAL = "4.10.0"
    /*Retrofit版本2020.5.20号后已不再更新*/
    RETROFIT_VERSION_FINAL = "2.9.0"
    /*appcompat lib的版本-高版本限制了最低API不符合我们最低Api21的要求*/
    APP_COMPAT_VERSION_FINAL = "1.4.1"
    /*core-ktx lib的版本-高版本限制了最低API不符合我们最低Api21的要求*/
    CORE_KTX_VERSION_FINAL = "1.7.0"
    /*annotation lib的版本-高版本限制了最低API不符合我们最低Api21的要求*/
    KAPT_ANNOTATION_VERSION_FINAL = "1.4.0"
    /*kotlin-stdlib的版本-版本号变更-需要考虑COMPOSE的版本号同步变更*/
    KOTLIN_STDLIB_JDK8_VERSION = "2.0.21"
    /*Compose的与Kotlin的兼容对应版本-https://developer.android.com/jetpack/androidx/releases/compose-kotlin*/
    COMPOSE_WITH_KOTLIN_VERSION = "2.0.21"
    LIFECYCLE_API_VERSION = "1.0.1"
    LIFECYCLE_COMPILER_VERSION = "1.0.0"
    APNG_VERSION = "2.26.0"

    sdk = [
            arouter_register: 'com.github.jadepeakpoet.ARouter:arouter-register:1.0.3',
            arouter_api     : 'com.github.jadepeakpoet.ARouter:arouter-api:1.0.3',
            arouter_compiler: 'com.github.jadepeakpoet.ARouter:arouter-compiler:1.0.3'
    ]

    /*编译环境SDK*/
    COMPILE_SDK_VERSION = 35
    /*最低支持SDK*/
    MIN_SDK_VERSION = 28
    /*目标设备SDK*/
    TARGET_SDK_VERSION = 35
    /*服务器环境配置*/
    APP_SERVICE_RUN_MODE = RUN_MODE
    /*APP版本Code*/
    APP_VERSION_CODE = VERSION_CODE
    /*APP版本名称*/
    APP_VERSION_NAME = VERSION_NAME

    /*非QA环境下的Lib依赖*/
    NO_QA_LIB_FILTER = Boolean.parseBoolean(ext.getProperty("noQaLibFilter"))
    /*非QA环境下的Lib内存泄漏配置*/
    NO_QA_LIB_FILTER_4_LEAK_CANARY = Boolean.parseBoolean(ext.getProperty("inNoQaLibFilter4LeakCanary"))

    //todo: isModule 目前使用的库有CamPhotos,希望后续能够模块化的功能都能参考CamPhotos的方式搭建，规范化，为以后的模块性重构打下基础
    isModule = true // false: 组件模式 单独运行  true ：集成模式 合成app

    /*release情况是否打开shrinkResources*/
    SHRINKRESOURCES_RELEASE = ATTR_SHRINKRESOURCES_RELEASE

    PACKAGE_WHOLE = APP_PACKAGE_WHOLE

    H5_OFFICIAL_FULL_APK = OFFICIAL_FULL_APK

    // 本地maven仓库目录
    MAVEN_LOCAL_URL = project.rootDir.path + "/repo"

    /*是否Release是默认的buildVariant配置*/
    RELEASE_BUILD_VARIANT_IS_DEFAULT = USE_RELEASE_VARIANT

    println("APP_VERSION_NAME = $APP_VERSION_NAME")
}
