apply from: "../module_build.gradle"
android {
    namespace 'com.govee.matisse'
    compileSdk COMPILE_SDK_VERSION
    buildFeatures {
        buildConfig true
    }
    defaultConfig {
        if (!isModule) {
            applicationId "com.govee.camphotos"
            minSdkVersion 34
            targetSdkVersion 34
        } else {
            minSdkVersion MIN_SDK_VERSION
            targetSdkVersion TARGET_SDK_VERSION
        }

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [AROUTER_MODULE_NAME: project.getName()]
            }
        }
    }
    buildTypes {
        release {
            isDefault = RELEASE_BUILD_VARIANT_IS_DEFAULT
            minifyEnabled false
            consumerProguardFiles 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }
    lint {
        abortOnError false
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'it.sephiroth.android.library.imagezoom:library:1.0.4'
    implementation project(':base2app')
    implementation project(':ui')
    /*动态权限库-JAVA*/
    compileOnly 'com.github.permissions-dispatcher:permissionsdispatcher:4.9.2'
    kapt 'com.github.permissions-dispatcher:permissionsdispatcher-processor:4.9.2'
    //noinspection DependencyNotationArgument
    implementation rootProject.ext.sdk.arouter_api
    //noinspection DependencyNotationArgument
    kapt rootProject.ext.sdk.arouter_compiler
}

tasks.register('clearJar', Delete) {
    delete 'libs/CamPhotos.jar' //sdk是你自己jar包的名字，随便命名
}
tasks.register('makeJar', Jar) {
    archiveBaseName.set('sdk')  //指定生成的jar名
    //从哪里打包class文件，这个是你module中生成的class文件，如果没有classes文件夹，不妨先运行下你的应用
    from('build/intermediates/classes/debug/com/****/*****')
    //打包到jar后的目录结构,这里建议直接用包名
    into('com/*****/******')
    exclude('test/', 'BuildConfig.class', 'R.class')//去掉不需要打包的目录和文件
    exclude { it.name.startsWith('R') }//去掉R开头的文件
}
makeJar.dependsOn(clearJar, build)