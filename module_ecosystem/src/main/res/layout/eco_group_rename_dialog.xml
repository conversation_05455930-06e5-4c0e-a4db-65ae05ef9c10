<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/component_bg_style_2"
        >

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="30dp"
            android:text="@string/app_group_name_label"
            android:textColor="@color/font_style_32_1_textColor"
            android:textSize="18sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <View
            android:id="@+id/gapViewLine"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginHorizontal="36dp"
            android:layout_marginTop="138dp"
            android:layout_marginBottom="95dp"
            android:background="@color/FFE6E6E6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <EditText
            android:id="@+id/etGroupName"
            android:layout_width="match_parent"
            android:layout_height="40dp"
            android:layout_marginHorizontal="36dp"
            android:layout_marginBottom="10dp"
            android:background="@null"
            android:gravity="center_vertical|start"
            android:maxLength="22"
            android:paddingHorizontal="10dp"
            android:singleLine="true"
            android:textColor="@color/font_style_32_2_textColor"
            app:layout_constraintBottom_toTopOf="@+id/gapViewLine"
            tools:text="sssss"
            />

        <Button
            android:id="@+id/btnCancel"
            android:layout_width="0dp"
            android:layout_height="45dp"
            android:layout_marginStart="32dp"
            android:layout_marginEnd="5dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/eco_yello_button_bg"
            android:gravity="center"
            android:text="@string/cancel"
            android:textColor="@color/ui_btn_style_5_1_text_color"
            android:textSize="@dimen/ui_btn_style_5_1_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/btnOk"
            app:layout_constraintStart_toStartOf="parent"
            />

        <Button
            android:id="@+id/btnOk"
            android:layout_width="0dp"
            android:layout_height="45dp"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="32dp"
            android:layout_marginBottom="20dp"
            android:background="@drawable/eco_group_rename_btn"
            android:enabled="false"
            android:gravity="center"
            android:text="@string/confirm"
            android:textColor="@color/ui_btn_style_3_1_text_color"
            android:textSize="@dimen/ui_btn_style_3_1_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/btnCancel"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>