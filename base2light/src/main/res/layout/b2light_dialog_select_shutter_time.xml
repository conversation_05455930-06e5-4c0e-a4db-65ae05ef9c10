<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/component_bg_style_2"
    >

    <ImageView
        android:id="@+id/btClose"
        android:layout_width="30dp"
        android:layout_height="30dp"
        android:layout_marginTop="10dp"
        android:layout_marginEnd="10dp"
        android:contentDescription="@null"
        android:src="@mipmap/new_btn_password_close"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        />

    <TextView
        android:id="@+id/notice1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32.5dp"
        android:text="@string/sample_pic"
        android:textColor="@color/font_style_136_3_textColor"
        android:textSize="@dimen/font_style_136_3_textSize"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/btClose"
        />

    <androidx.constraintlayout.utils.widget.ImageFilterView
        android:id="@+id/noticeImg"
        android:layout_width="179dp"
        android:layout_height="100dp"
        android:layout_marginHorizontal="32.5dp"
        android:layout_marginTop="9dp"
        android:background="@drawable/component_bg_style_20"
        android:scaleType="fitXY"
        android:contentDescription="@null"
        app:round="10dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/notice1"
        />

    <TextView
        android:id="@+id/notice2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32.5dp"
        android:layout_marginTop="20dp"
        android:text="@string/select_shutter_time_notice1"
        android:textColor="@color/font_style_34_2_textColor"
        android:textSize="@dimen/font_style_34_2_textSize"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/noticeImg"
        />

    <View
        android:id="@+id/height4retry"
        android:layout_width="wrap_content"
        android:layout_height="225dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/notice2"
        />


    <TextView
        android:id="@+id/retryTv"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32.5dp"
        android:gravity="center"
        android:text="@string/str_share_fail"
        android:textColor="@color/font_style_34_2_textColor"
        android:textSize="@dimen/font_style_34_2_textSize"
        app:layout_constraintBottom_toBottomOf="@+id/height4retry"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="@+id/height4retry"
        android:visibility="gone"
        />

    <androidx.recyclerview.widget.RecyclerView
        android:id="@+id/shutterTimeList"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginHorizontal="32.5dp"
        android:layout_marginTop="5dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/notice2"
        />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrierRetryAndList"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="height4retry, shutterTimeList"
        />

    <TextView
        android:id="@+id/btnNext"
        android:layout_width="0dp"
        android:layout_height="55dp"
        android:layout_gravity="center"
        android:layout_marginHorizontal="32.5dp"
        android:layout_marginTop="15dp"
        android:layout_marginBottom="10dp"
        android:background="@drawable/component_btn_style_3_5"
        android:clickable="true"
        android:gravity="center_horizontal"
        android:lines="1"
        android:paddingHorizontal="27.5dp"
        android:paddingTop="12.5dp"
        android:text="@string/calibration_state_next"
        android:textColor="@color/ui_btn_style_3_1_text_color"
        android:textSize="@dimen/ui_btn_style_3_1_text_size"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/barrierRetryAndList"
        />


</androidx.constraintlayout.widget.ConstraintLayout>