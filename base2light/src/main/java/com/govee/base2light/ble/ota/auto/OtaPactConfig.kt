package com.govee.base2light.ble.ota.auto

import androidx.collection.arrayMapOf
import com.govee.base2home.iot.AbsCmd
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.pact.Pact
import com.govee.base2home.pact.Protocol
import com.govee.base2light.ble.controller.AbsSingleController
import com.govee.base2light.pact.iot.CmdPtReal

/**
 * @author：Yang<PERSON><PERSON>.Chen
 * @date：2024/7/17 11:40
 * @description：配置支持自动固件升级的PactCode和PactType
 */
object OtaPactConfig {

    /**
     * 支持通过PtIoT发生送pact信息的sku和pact版本
     */
    private val supportPtIot = arrayMapOf(
        GoodsType.GOODS_TYPE_VALUE_H6630 to Protocol(1, 1),// H6630
        GoodsType.GOODS_TYPE_VALUE_H6631 to Protocol(1, 1),// H6631
        GoodsType.GOODS_TYPE_VALUE_H717D to Protocol(2, 1),// H717D
    )

    /**
     * 支持通过PtReal发送pact信息的sku和pact版本
     */
    private val supportPtRealGoodsType = arrayMapOf(
        GoodsType.GOODS_TYPE_VALUE_HEATER_7135 to Protocol(2, 1),// H7135
        GoodsType.GOODS_TYPE_VALUE_H7148 to Protocol(2, 1),// H7148
        GoodsType.GOODS_TYPE_VALUE_H7128 to Protocol(2, 1),// H7128
        GoodsType.GOODS_TYPE_VALUE_H7129 to Protocol(2, 1), // H7129
        GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER_V4 to Protocol(2, 1), // H7143
        GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER_V6 to Protocol(2, 1), // H7140
        GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER_170 to Protocol(2, 1), // H7145
        GoodsType.GOODS_TYPE_VALUE_H7149 to Protocol(2, 1), // H7149
        GoodsType.GOODS_TYPE_VALUE_H7147 to Protocol(2, 1), // H7147
        GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER_V6 to Protocol(2, 1), // H7140迭代款
        GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER to Protocol(2, 1), // H7141
        GoodsType.GOODS_TYPE_VALUE_H714E to Protocol(2, 1), // H714E
        GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER_V3 to Protocol(2, 1), // H7160
        GoodsType.GOODS_TYPE_VALUE_146 to Protocol(2, 1), // H7161
        GoodsType.GOODS_TYPE_VALUE_189 to Protocol(2, 1), // H7162
        GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER_V2 to Protocol(2, 1), // H7142
        GoodsType.GOODS_TYPE_VALUE_AIR_CLEANER_V4 to Protocol(2, 1),// H7120
        GoodsType.GOODS_TYPE_VALUE_AIR_CLEANER_V2 to Protocol(2, 1),// H7122
        GoodsType.GOODS_TYPE_VALUE_AIR_CLEANER_V3 to Protocol(2, 1),// H7123
        GoodsType.GOODS_TYPE_VALUE_160 to Protocol(2, 1),// H7126
        GoodsType.GOODS_TYPE_VALUE_H7124 to Protocol(2, 1),// H7124
        GoodsType.GOODS_TYPE_VALUE_H712C to Protocol(2, 1),// H712C
        GoodsType.GOODS_TYPE_VALUE_H7127 to Protocol(2, 1),// H7127
        GoodsType.GOODS_TYPE_VALUE_HEATER to Protocol(2, 1),// H7130
        GoodsType.GOODS_TYPE_VALUE_HEATER_7132 to Protocol(2, 1),// H7131/H7132
        GoodsType.GOODS_TYPE_VALUE_h713A to Protocol(2, 1),// H713A
        GoodsType.GOODS_TYPE_VALUE_h713B to Protocol(2, 1),// H713B
        GoodsType.GOODS_TYPE_VALUE_h713C to Protocol(2, 1),// H713C
        GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H7134 to Protocol(2, 1),// H7134
        GoodsType.GOODS_TYPE_VALUE_HEATER_7133 to Protocol(2, 1),// H7133
        GoodsType.GOODS_TYPE_VALUE_DEHUMIDIFIER_V2 to Protocol(2, 1),// H7151
        GoodsType.GOODS_TYPE_VALUE_AIR_CLEANER to Protocol(2, 1),// H7121
        GoodsType.GOODS_TYPE_VALUE_BLE_IOT_TOWER_FAN_V1 to Protocol(2, 1),// H7102
        GoodsType.GOODS_TYPE_VALUE_TOWER_FAN_BLE_IOT to Protocol(2, 1),// H7100
        GoodsType.GOODS_TYPE_VALUE_4_H7106 to Protocol(2, 1),// H7106
        GoodsType.GOODS_TYPE_VALUE_4_H7105 to Protocol(2, 1),// H7105
        GoodsType.GOODS_TYPE_VALUE_4_H7107 to Protocol(2, 1),// H7107
        GoodsType.GOODS_TYPE_VALUE_BLE_IOT_TOWER_FAN_V1 to Protocol(2, 1),// H7101
        GoodsType.GOODS_TYPE_VALUE_CIRCULATION_FAN_BLE_IOT_DESKTOP_AIR_V1 to Protocol(2, 1),// H7111
        GoodsType.GOODS_TYPE_VALUE_H8121 to Protocol(1, 1),// H8121
        GoodsType.GOODS_TYPE_VALUE_H8120 to Protocol(1, 1),// H8121
        GoodsType.GOODS_TYPE_VALUE_H7153 to Protocol(1, 1),// H7153
    )

    /**
     * 判断设备当前的固件版本 大于等于 最小的支持固件升级的版本
     * @param goodsType
     * @param protocol 当前设备的pact版本信息
     */
    fun isSupportVersion(goodsType: Int?, protocol: Protocol?): Boolean {
        val supportPtReal = isSupportPtRealVersion(goodsType, protocol?.pactType, protocol?.pactCode)
        val supportPtIoT = isSupportPtIoTVersion(goodsType, protocol?.pactType, protocol?.pactCode)
        return supportPtReal || supportPtIoT
    }

    /**
     * 如果此设备支持上报pact信息，则返回对应的AbsSingleController指令
     */
    fun getReportPactController(goodsType: Int?, pactType: Int?, pactCode: Int?): AbsSingleController? {
        if (goodsType == null || pactType == null || pactCode == null) {
            return null
        }
        if (isSupportVersion(goodsType, Protocol(pactType, pactCode))) {
            val lastPactInfo = Pact.getInstance.getLatestPact(goodsType) ?: return null
            return ReportOtaPactController(lastPactInfo.pactCode, lastPactInfo.pactType)
        }
        return null
    }

    /**
     * 如果此设备支持上报pact信息，则返回对应的AbsCmd指令
     */
    fun getReportPactCmd(goodsType: Int?, pactType: Int?, pactCode: Int?): AbsCmd? {
        if (goodsType == null || pactType == null || pactCode == null) {
            return null
        }
        val supportPtReal = isSupportPtRealVersion(goodsType, pactType, pactCode)
        val supportPtIoT = isSupportPtIoTVersion(goodsType, pactType, pactCode)
        if (supportPtReal || supportPtIoT) {
            val lastPactInfo = Pact.getInstance.getLatestPact(goodsType) ?: return null
            val controller = ReportOtaPactController(lastPactInfo.pactCode, lastPactInfo.pactType)
            return if (supportPtIoT) {
                CmdPtIot(controller)
            } else {
                CmdPtReal(controller)
            }
        }
        return null
    }

    /**
     * 获取pactType和pactCode版本
     */
    fun getPactTypeCodeVersion(pactType: Int, pactCode: Int) = pactType * 10000 + pactCode * 100

    fun isSupportPtRealVersion(goodsType: Int?, pactType: Int?, pactCode: Int?): Boolean {
        if (goodsType == null || pactType == null || pactCode == null) {
            return false
        }
        if (!supportPtRealGoodsType.containsKey(goodsType)) {
            return false
        }
        val minProtocol = supportPtRealGoodsType[goodsType] ?: return false
        // 判断当前设备固件版本是否已支持自动升级
        val currentVersion = getPactTypeCodeVersion(pactType, pactCode)
        val supportAutoOtaMinVersion = getPactTypeCodeVersion(minProtocol.pactType, minProtocol.pactCode)
        // 设备当前的固件版本 大于等于 最小的支持固件升级的版本
        return currentVersion >= supportAutoOtaMinVersion
    }

    fun isSupportPtIoTVersion(goodsType: Int?, pactType: Int?, pactCode: Int?): Boolean {
        if (goodsType == null || pactType == null || pactCode == null) {
            return false
        }
        if (!supportPtIot.containsKey(goodsType)) {
            return false
        }
        val minProtocol = supportPtIot[goodsType] ?: return false
        // 判断当前设备固件版本是否已支持自动升级
        val currentVersion = getPactTypeCodeVersion(pactType, pactCode)
        val supportAutoOtaMinVersion = getPactTypeCodeVersion(minProtocol.pactType, minProtocol.pactCode)
        // 设备当前的固件版本 大于等于 最小的支持固件升级的版本
        return currentVersion >= supportAutoOtaMinVersion
    }

}