package com.govee.base2light.ac.diy.v1;

import android.annotation.SuppressLint;
import android.content.Context;
import android.graphics.Point;
import android.graphics.drawable.GradientDrawable;
import android.os.Build;
import android.text.TextUtils;
import android.util.SparseIntArray;
import android.view.DragEvent;
import android.view.Gravity;
import android.view.View;
import android.view.ViewGroup;
import android.view.WindowManager;
import android.widget.ImageView;
import android.widget.TextView;

import com.govee.base2home.ac.IAcSize;
import com.govee.base2home.color.IColorChoose;
import com.govee.base2home.color.IPalette;
import com.govee.base2home.color.PaletteDialogNew;
import com.govee.base2home.color.PaletteInterfaceKt;
import com.govee.base2home.util.ClickUtil;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ac.diy.v2.DiyProtocolParser;
import com.govee.ui.R;
import com.govee.ui.component.LinearProgressSeekBarV1;
import com.govee.ui.dialog.ConfirmDialog;
import com.ihoment.base2app.dialog.BaseEventDialog;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.ResUtil;
import com.zhy.android.percent.support.PercentLayoutHelper;
import com.zhy.android.percent.support.PercentRelativeLayout;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * Create by xieyingwu on 1/5/21
 * 涂鸦编辑弹窗$
 */
@SuppressLint("NonConstantResourceId")
public class GraffitiEditDialog extends BaseEventDialog {
    private static final int def_base_color = 0xFFFFFFFF;

    @BindView(com.govee.base2light.R2.id.graffiti_edit_base_color)
    ImageView color4BaseIv;
    @BindView(com.govee.base2light.R2.id.edit_sub_diy_graffiti_base_color_brightness_bar)
    LinearProgressSeekBarV1 brightnessSeekBar;
    @BindView(com.govee.base2light.R2.id.sub_diy_graffiti_paint_foreground)
    View paintForeground;
    @BindView(com.govee.base2light.R2.id.sub_diy_graffiti_paint_color)
    View paintColorV;
    @BindView(com.govee.base2light.R2.id.sub_diy_graffiti_paint_edit)
    View paintEditIv;
    @BindView(com.govee.base2light.R2.id.sub_diy_graffiti_eraser)
    View paintEraserV;
    @BindView(com.govee.base2light.R2.id.graffiti_edit_piece)
    ViewGraffiti graffitiEditView;


    @BindView(com.govee.base2light.R2.id.graffiti_edit_btn_exit)
    ImageView editExitBtn;
    @BindView(com.govee.base2light.R2.id.graffiti_edit_diy_apply)
    TextView editDiyApplyBtn;
    @BindView(com.govee.base2light.R2.id.float_edit_btn_container)
    ViewGroup floatEditBtnContainer;
    @BindView(com.govee.base2light.R2.id.graffiti_edit_container)
    ViewGroup graffitiEditContainer;
    @BindView(com.govee.base2light.R2.id.iv_revoke)
    ImageView ivRevoke;
    @BindView(com.govee.base2light.R2.id.iv_restore)
    ImageView ivRestore;
    @BindView(com.govee.base2light.R2.id.iv_clear)
    ImageView ivClear;

    private GradientDrawable drawableRTB;
    private GradientDrawable drawableRTBStroke;

    private int baseColor = def_base_color;/*底色-默认白色*/
    private int paintColor;/*画笔颜色-默认红色*/

    private boolean paintChoose;
    private final String sku;
    private GraffitiEditResultListener listener;

    //回退
    public List<SparseIntArray> revokeList;
    //恢复
    public List<SparseIntArray> restoreList;

    private static class MyShadowBuilder extends View.DragShadowBuilder {
        @Override
        public void onProvideShadowMetrics(Point outShadowSize, Point outShadowTouchPoint) {
            super.onProvideShadowMetrics(outShadowSize, outShadowTouchPoint);
            if (outShadowSize.x == 0 || outShadowSize.y == 0) {
                outShadowSize.x = 1;
                outShadowSize.y = 1;
            }
        }
    }

    protected GraffitiEditDialog(Context context, String sku, int baseColor, int brightness,
                                 int[][] colors, int paintColor, List<SparseIntArray> revokeList,
                                 List<SparseIntArray> restoreList, GraffitiEditResultListener listener) {
        super(context);
        /*更新位置*/
        changeDialogHeight(ViewGroup.LayoutParams.MATCH_PARENT, ViewGroup.LayoutParams.MATCH_PARENT, Gravity.BOTTOM);
        /*监听返回健操作*/
        listenerBackPressed(this::toMinimize);
        /*不允许点击外部隐藏dialog*/
        changeDialogOutside(false);
        if (context instanceof IAcSize) {
            Point wh = ((IAcSize) context).getAcFullScreenWH();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "GraffitiEditDialog() wh " + wh.toString());
            }
            int marginTop = (int) (AppUtil.getScreenWidth() * 20.5f / 375);
            int w = Math.max(1, Math.min(wh.x, wh.y));
            int h = Math.max(1, Math.max(wh.x, wh.y) - marginTop);
            float whScale = w * 1.0f / h;
            float defScale = 375f / 647;
            /*若当前宽高比大于默认配比，表明界面无法展示全视图，需要将操作按钮进行悬浮展示*/
            boolean isSmallScreen = whScale > defScale;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "GraffitiEditDialog() whScale = " + whScale + " ; isSmallScreen = " + isSmallScreen + " defScale = " + defScale);
            }
            uiScreen(isSmallScreen);
            if (isSmallScreen) {
                /*小屏幕手机；需要加入拖拽*/
                floatEditBtnContainer.setOnLongClickListener(v -> {
                    /*进入长按-则进行拖拽操作*/
                    View.DragShadowBuilder builder = new MyShadowBuilder();
                    boolean dragResult;
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                        dragResult = v.startDragAndDrop(null, builder, null, 0);
                    } else {
                        dragResult = v.startDrag(null, builder, null, 0);
                    }
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "onLongClick() dragResult = " + dragResult);
                    }
                    return true;
                });
                graffitiEditContainer.setOnDragListener((v, event) -> onDrag4FloatEdit(event));
            }
        }
        //updateApplyBtnLP();
        this.paintColor = paintColor;
        this.revokeList = revokeList;
        this.restoreList = restoreList;
        this.sku = sku;
        this.listener = listener;
        graffitiEditView.setOnRollbackListener(this::updateRollback);
        if (isH6078()) {
            graffitiEditView.resetParamsH6078(0);
            graffitiEditView.setCanvasSize(28, 8);
        }
        updateRollback();
        brightnessSeekBar.setMax(100 - 1);
        brightnessSeekBar.setProgress(0);
        brightnessSeekBar.setListener(new LinearProgressSeekBarV1.ISeekBarListener() {
            @Override
            public void onProgressChangeEnd(int progress) {
            }

            @Override
            public void onProgressUser(int progress) {
                baseBrightnessChange();
            }
        });
        updateGraffiti(baseColor, brightness, colors);
    }

    private void updateApplyBtnLP() {
        PercentRelativeLayout.LayoutParams lp = (PercentRelativeLayout.LayoutParams) editDiyApplyBtn.getLayoutParams();
        if (lp != null) {
            PercentLayoutHelper.PercentLayoutInfo percentLayoutInfo = lp.getPercentLayoutInfo();
            percentLayoutInfo.topMarginPercent.percent = -11f / 375;
            editDiyApplyBtn.setLayoutParams(lp);
        }
    }

    private boolean onDrag4FloatEdit(DragEvent event) {
        int action = event.getAction();
        float y = event.getY();
        if (action == DragEvent.ACTION_DRAG_STARTED) {
            calculateOffsetY(y);
        } else if (action == DragEvent.ACTION_DROP) {
            updateFloatLP(y);
        } else if (action == DragEvent.ACTION_DRAG_LOCATION) {
            updateFloatLP(y);
        }
        return true;
    }

    private void calculateOffsetY(float y) {
        if (floatEditBtnContainer != null) {
            float y1 = floatEditBtnContainer.getY();
            offsetDragY = (int) Math.abs(y - y1);
        } else {
            offsetDragY = 0;
        }
    }

    private final static float minTopPercent = 14f / 375;
    private final static float maxTopPercent = (606 - 94/*viewHeight*/ - 14/*top*/ - 14/*bottom*/) / 375f;
    private int offsetDragY = 0;

    private void updateFloatLP(float y) {
        if (floatEditBtnContainer != null) {
            y = y - offsetDragY;
            float percent = y / AppUtil.getScreenWidth();
            percent = Math.max(minTopPercent, percent);
            percent = Math.min(maxTopPercent, percent);
            PercentRelativeLayout.LayoutParams lp = (PercentRelativeLayout.LayoutParams) floatEditBtnContainer.getLayoutParams();
            PercentLayoutHelper.PercentLayoutInfo percentLayoutInfo = lp.getPercentLayoutInfo();
            PercentLayoutHelper.PercentLayoutInfo.PercentVal topMarginPercent = percentLayoutInfo.topMarginPercent;
            topMarginPercent.percent = percent;
            floatEditBtnContainer.setLayoutParams(lp);
        }
    }

    private void uiScreen(boolean isSmallScreen) {
        floatEditBtnContainer.setVisibility(isSmallScreen ? View.VISIBLE : View.GONE);
        editExitBtn.setVisibility(isSmallScreen ? View.GONE : View.VISIBLE);
        editDiyApplyBtn.setVisibility(isSmallScreen ? View.GONE : View.VISIBLE);
    }

    public static void showDialog(Context context, String sku, int baseColor, int brightness,
                                  int[][] colors, int paintColor, List<SparseIntArray> revokeList,
                                  List<SparseIntArray> restoreList, GraffitiEditResultListener listener) {
        new GraffitiEditDialog(context, sku, baseColor, brightness, colors, paintColor, revokeList,
                restoreList, listener).show();
    }

    private void baseBrightnessChange() {
        if (graffitiEditView != null) graffitiEditView.updateBaseColor(getBrightness(), baseColor);
    }

    private void uiBrightness(int progress) {
        brightnessSeekBar.setProgress(progress - 1);
    }

    private void uiPaint(boolean choose) {
        this.paintChoose = choose;
        paintForeground.setSelected(choose);
        paintEditIv.setSelected(choose);
        paintEraserV.setSelected(!choose);
        boolean nearWhiteColor = ColorUtils.isNearWhiteColor(paintColor);
        boolean noColor = ColorUtils.isNoColor(paintColor);
        if (noColor) {
            paintColorV.setBackground(ResUtil.getDrawable(R.mipmap.new_public_diy_btn_pen_color_un));
        } else if (nearWhiteColor) {
            if (drawableRTBStroke == null) {
                drawableRTBStroke = (GradientDrawable) ResUtil.getDrawable(R.drawable.component_color_block_rtb_radius_stroke_5);
            }
            drawableRTBStroke.setColor(paintColor);
            paintColorV.setBackground(drawableRTBStroke);
        } else {
            if (drawableRTB == null) {
                drawableRTB = (GradientDrawable) ResUtil.getDrawable(R.drawable.component_color_block_rtb_radius_5);
            }
            drawableRTB.setColor(paintColor);
            paintColorV.setBackground(drawableRTB);
        }
        graffitiEditView.updatePaint(paintColor, !choose);
    }

    private GradientDrawable drawableBg;

    private void uiBase() {
        boolean noColor = ColorUtils.isNoColor(baseColor);
        if (noColor) {
            color4BaseIv.setBackground(ResUtil.getDrawable(R.mipmap.new_diy_btn_bg_color_un_color));
        } else {
            if (drawableBg == null) {
                drawableBg = (GradientDrawable) ResUtil.getDrawable(R.drawable.component_bg_color_block_1_3);
            }
            drawableBg.setColor(baseColor);
            color4BaseIv.setBackground(drawableBg);
        }
        color4BaseIv.setImageDrawable(ResUtil.getDrawable(R.mipmap.new_6052_diy_color_arrow));
        graffitiEditView.updateBaseColor(getBrightness(), baseColor);
    }

    private int getBrightness() {
        if (brightnessSeekBar != null) return brightnessSeekBar.getProgress() + 1;
        return 1;
    }

    @Override
    public void hide() {
        if (floatEditBtnContainer != null) floatEditBtnContainer.setOnLongClickListener(null);
        if (graffitiEditContainer != null) graffitiEditContainer.setOnDragListener(null);
        super.hide();
        listener = null;
    }

    @Override
    protected int getLayout() {
        return com.govee.base2light.R.layout.b2light_dialog_graffiti_edit;
    }

    @OnClick({com.govee.base2light.R2.id.graffiti_edit_diy_apply, com.govee.base2light.R2.id.graffiti_edit_diy_apply_v1})
    public void onClickBtnApply() {
        if (ClickUtil.getInstance.clickQuick()) return;
        if (listener != null) listener.applyResult(getGraffitiEffect());
    }

    @OnClick({com.govee.base2light.R2.id.graffiti_edit_btn_exit, com.govee.base2light.R2.id.graffiti_edit_btn_exit_v1})
    public void onClickMinimize() {
        if (ClickUtil.getInstance.clickQuick()) return;
        toMinimize();
    }

    private void toMinimize() {
        if (listener != null) listener.editResult(getGraffitiEffect(), paintColor);
        hide();
    }

    @OnClick({com.govee.base2light.R2.id.sub_diy_graffiti_paint_color, com.govee.base2light.R2.id.sub_diy_graffiti_paint_edit})
    public void onClickPaintColorChoose() {
        if (ClickUtil.getInstance.clickQuick()) return;
        if (paintChoose) {
            IColorChoose iColorChoose = new IColorChoose() {
                @Override
                public boolean isSupportChooseMultiColors() {
                    return false;
                }

                @Override
                public void chooseColor(@NonNull int[] newColors) {
                    paintColor = newColors[0];
                    uiPaint(true);
                }

                @Override
                public void chooseColorRealTime(int color, boolean isFirstDown) {

                }
            };
            showPaletteDialog(paintColor, iColorChoose);
        } else {
            uiPaint(true);
        }
    }

    private void showPaletteDialog(int curColor, IColorChoose iColorChoose) {
        IPalette iPalette = PaletteInterfaceKt.getIPalette(sku, null, true, iColorChoose, null, null, true);
        PaletteDialogNew.Companion.createDialog(context, iPalette, curColor, true).show();
    }
    @OnClick(com.govee.base2light.R2.id.graffiti_edit_base_color)
    public void onClickSetBaseColor() {
        if (ClickUtil.getInstance.clickQuick()) return;
        IColorChoose iColorChoose = new IColorChoose() {
            @Override
            public boolean isSupportChooseMultiColors() {
                return false;
            }

            @Override
            public void chooseColor(@NonNull int[] newColors) {
                baseColor = newColors[0];
                uiBase();
            }

            @Override
            public void chooseColorRealTime(int color, boolean isFirstDown) {

            }
        };
        showPaletteDialog(baseColor, iColorChoose);
    }

    @OnClick(com.govee.base2light.R2.id.sub_diy_graffiti_eraser)
    public void onClickEraser() {
        if (ClickUtil.getInstance.clickQuick()) return;
        uiPaint(false);
    }

    /**
     * 更新涂鸦基本参数
     *
     * @param baseColor
     * @param brightness
     * @param colors
     */
    private void updateGraffiti(int baseColor, int brightness, int[][] colors) {
        this.baseColor = baseColor;
        uiBase();
        uiBrightness(brightness);
        graffitiEditView.updateBaseColor(getBrightness(), baseColor);
        graffitiEditView.updatePaint(paintColor, false);
        graffitiEditView.initColors(colors);
        graffitiEditView.updateOpList(restoreList, revokeList);
        uiPaint(true);
        updateRollback();
    }

    GraffitiEffect getGraffitiEffect() {
        GraffitiEffect graffitiEffect = new GraffitiEffect();
        graffitiEffect.version = DiyProtocolParser.diy_protocol_version_graffiti;
        graffitiEffect.baseColor = baseColor;
        graffitiEffect.baseBrightness = getBrightness();
        boolean graffitiNotNull = graffitiEditView != null;
        graffitiEffect.colors = graffitiNotNull ? graffitiEditView.getColors() : null;
        List<SparseIntArray> revokeList = graffitiNotNull ? graffitiEditView.getRevokeList() : new ArrayList<>();
        this.revokeList.clear();
        this.revokeList.addAll(revokeList);
        List<SparseIntArray> restoreList = graffitiNotNull ? graffitiEditView.getRestoreList() : new ArrayList<>();
        this.restoreList.clear();
        this.restoreList.addAll(restoreList);
        return graffitiEffect;
    }

    @Override
    protected int getWidth() {
        return WindowManager.LayoutParams.MATCH_PARENT;
    }

    @Override
    protected int getHeight() {
        return WindowManager.LayoutParams.MATCH_PARENT;
    }

    @Override
    protected int getDialogStyle() {
        return com.ihoment.base2app.R.style.BottomPopupDialogStyle;
    }

    public interface GraffitiEditResultListener {
        void editResult(@NonNull GraffitiEffect graffitiEffect, int paintColor);

        void applyResult(@NonNull GraffitiEffect graffitiEffect);
    }

    public static void hideDialog() {
        DialogHideEvent.sendDialogHideEvent(GraffitiEditDialog.class.getName());
    }

    @OnClick(com.govee.base2light.R2.id.iv_revoke)
    public void onClickRevoke() {
        if (ClickUtil.getInstance.clickQuick()) return;
        graffitiEditView.onRevoke();
        updateRollback();
    }

    @OnClick(com.govee.base2light.R2.id.iv_restore)
    public void onClickRestore() {
        if (ClickUtil.getInstance.clickQuick()) return;
        graffitiEditView.onRestore();
        updateRollback();
    }

    @OnClick(com.govee.base2light.R2.id.iv_clear)
    public void onClickClear() {
        if (ClickUtil.getInstance.clickQuick()) return;
        ConfirmDialog.showConfirmDialog(context,
                ResUtil.getString(R.string.b2light_clean_canvas_des),
                ResUtil.getString(R.string.cancel),
                ResUtil.getString(R.string.done), () -> {
                    graffitiEditView.onClearCanvas();
                    updateRollback();
                });
    }

    private void updateRollback() {
        boolean canRestore = graffitiEditView.canRestore();
        boolean canRevoke = graffitiEditView.canRevoke();
        boolean canClear = graffitiEditView.canClear();
        ivRevoke.setEnabled(canRevoke);
        ivRevoke.setAlpha(canRevoke ? 1.0f : 0.3f);
        ivRestore.setEnabled(canRestore);
        ivRestore.setAlpha(canRestore ? 1.0f : 0.3f);
        ivClear.setEnabled(canClear);
        ivClear.setAlpha(canClear ? 1.0f : 0.3f);
    }

    private boolean isH6078() {
        return TextUtils.equals(sku, "H6078");
    }
}