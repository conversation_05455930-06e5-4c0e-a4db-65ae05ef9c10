package com.govee.base2light.ac.diyNew.views

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.util.TypedValue
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.annotation.ColorRes
import androidx.annotation.DimenRes
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.LifecycleOwner
import androidx.recyclerview.widget.GridLayoutManager
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.govee.base2kt.ext.autoSize
import com.govee.base2kt.ext.dp4Int
import com.govee.base2kt.ext.gone
import com.govee.base2kt.ext.visible
import com.govee.base2light.databinding.B2lightViewDiyNewDirectionBinding
import com.govee.base2light.util.setRecyclerViewItemDecoration4Grid
import com.govee.ui.R
import com.ihoment.base2app.Con4FontSize
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.util.ResUtil
import me.jessyan.autosize.AutoSizeCompat

/**
 *     author  : sinrow
 *     time    : 2024/5/7
 *     version : 1.0.0
 *     desc    : DIY 方向为同一套，如若有 UI 不一样则让 UI 改。
 */
class DiyNewDirectionView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0, defStyleRes: Int = 0
) : ConstraintLayout(context, attrs, defStyleAttr, defStyleRes) {

    private val binding: B2lightViewDiyNewDirectionBinding = DataBindingUtil.inflate(
        LayoutInflater.from(context),
        com.govee.base2light.R.layout.b2light_view_diy_new_direction,
        this,
        true
    )
    val directions = mutableListOf<DiyNewDirection>()
    private val adapter =
        Adapter(com.govee.base2light.R.layout.b2light_item_diy_new_view_direction, directions)
    private var curDirection = 0
    private var clickListener: ((code: Int, uiCode: Int) -> Unit)? = null

    private var clickMultiListener: ((codes: MutableList<Int>) -> Unit)? = null

    private var curSelectedDirections = mutableListOf<Int>()

    /*是否为多选模式*/
    var isMultiSelectedMode = false
        set(value) {
            field = value
            adapter.isMultiMode = value
        }

    /*最少选择项*/
    var minSelectedItems = 1


    init {
        context.apply {
            if (this is LifecycleOwner) binding.lifecycleOwner = this
        }
        adapter.setOnItemClickListener { _, _, position ->
            val item = directions[position]
            if (isMultiSelectedMode) {
                if (curSelectedDirections.contains(item.code)) {
                    if (curSelectedDirections.size == (minSelectedItems)) {
                        toast(R.string.b2light_diy_606a_area_select_tip)
                        return@setOnItemClickListener
                    }
                    curSelectedDirections.removeIf { it == item.code }
                } else {
                    curSelectedDirections.add(item.code)
                }
                clickMultiListener?.invoke(curSelectedDirections)
            } else {
                curDirection = item.code
                clickListener?.invoke(item.code, item.uiCode)
            }
            updateUI()
        }
        val glm = GridLayoutManager(context, 3)
        binding.recycleDirection.setRecyclerViewItemDecoration4Grid(3, 8, 12)
        binding.recycleDirection.layoutManager = glm
        binding.recycleDirection.adapter = adapter
    }

    fun resetItemDecoration(spanCount: Int, spacingLeftToRight: Int, spacingTopToBottom: Int = 0) {
        val glm = GridLayoutManager(context, spanCount)
        binding.recycleDirection.setRecyclerViewItemDecoration4Grid(spanCount, spacingLeftToRight, spacingTopToBottom)
        binding.recycleDirection.layoutManager = glm
        binding.recycleDirection.adapter = adapter
    }

    fun setDirections(directions: MutableList<DiyNewDirection>) {
        this.directions.clear()
        this.directions.addAll(directions)
    }

    fun setIsLandscape(isLandscape: Boolean) {
        adapter.isLandscape = isLandscape
    }

    fun setCurDirection(curDirection: Int) {
        this.curDirection = curDirection
        updateUI()
    }

    fun setCurMultiDirection(selectDirections: MutableList<Int>) {
        this.curSelectedDirections.clear()
        this.curSelectedDirections.addAll(selectDirections)
        if (selectDirections.size < minSelectedItems) {
            // 低于最低选择，则默认选择
            this.directions.filterIndexed { index, _ -> index < minSelectedItems }.forEach {
                if (this.curSelectedDirections.size < minSelectedItems) {
                    this.curSelectedDirections.add(it.code)
                }
            }
        }
        updateUI()
    }

    fun setTitleName(titleName: String) {
        if (titleName.isNotEmpty()) {
            binding.tvTitle.text = titleName
        } else {
            binding.tvTitle.text = ResUtil.getText(R.string.b2light_direction_des)
        }
    }

    fun setTitleColorStyle(@ColorRes colorResId: Int, @DimenRes sizeResId: Int) {
        binding.tvTitle.setTextColor(ResUtil.getColor(colorResId))
        binding.tvTitle.setTextSize(
            TypedValue.COMPLEX_UNIT_SP,
            Con4FontSize.calculateFontSizeSp(sizeResId)
        )
    }

    fun setClickListener(clickListener: ((direction: Int, uiCode: Int) -> Unit)) {
        this.clickListener = clickListener
    }

    fun setMultiClickListener(clickMultiListener: ((codes: MutableList<Int>) -> Unit)) {
        this.clickMultiListener = clickMultiListener
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun updateUI() {
        if (isMultiSelectedMode) {
            adapter.setSelectDirections(curSelectedDirections)
        } else {
            adapter.setSelectDirection(curDirection)
        }
        adapter.notifyDataSetChanged()
    }

    fun setMargin(titleStart: Int, recyclerViewStart: Int, recyclerViewEnd: Int) {
        val lpTitle = binding.tvTitle.layoutParams as LayoutParams
        lpTitle.marginStart = titleStart.dp4Int
        lpTitle.leftMargin = titleStart.dp4Int
        binding.tvTitle.layoutParams = lpTitle

        val lpRv = binding.recycleDirection.layoutParams as LayoutParams
        lpRv.marginStart = recyclerViewStart.dp4Int
        lpRv.leftMargin = recyclerViewStart.dp4Int
        lpRv.marginEnd = recyclerViewEnd.dp4Int
        lpRv.rightMargin = recyclerViewEnd.dp4Int
        binding.recycleDirection.layoutParams = lpRv
    }

    fun setMarginTop(marginTop: Int) {
        val lpTitle = binding.tvTitle.layoutParams as LayoutParams
        lpTitle.topMargin = marginTop.dp4Int
        binding.tvTitle.layoutParams = lpTitle
    }

    /**
     * 重置spanCount
     */
    fun resetSpanCont(spanCount: Int) {
        val glm = GridLayoutManager(context, spanCount)
        binding.recycleDirection.setRecyclerViewItemDecoration4Grid(spanCount, 8, 12)
        binding.recycleDirection.layoutManager = glm
        binding.recycleDirection.adapter = adapter
    }

    class Adapter(layoutResId: Int, data: MutableList<DiyNewDirection>) :
        BaseQuickAdapter<DiyNewDirection, BaseViewHolder>(layoutResId, data) {

        private var selectDirection = 0
        var isLandscape: Boolean = false
            //是否是横屏使用
            @SuppressLint("NotifyDataSetChanged")
            set(value) {
                field = value
                notifyDataSetChanged()
            }
        private val selectDirections: MutableList<Int> by lazy { mutableListOf<Int>() }
        var isMultiMode: Boolean = false

        fun setSelectDirection(selectDirection: Int) {
            this.selectDirection = selectDirection
        }

        fun setSelectDirections(directions: MutableList<Int>) {
            this.selectDirections.clear()
            this.selectDirections.addAll(directions)
        }

        override fun createBaseViewHolder(view: View): BaseViewHolder {
            if (isLandscape) {
                /*适配横屏展示弹窗的居于高进行适配*/
                AutoSizeCompat.autoConvertDensityBaseOnHeight(view.resources, 375f)
            } else {
                /*适配设计比例*/
                view.autoSize()
            }
            return super.createBaseViewHolder(view)
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
            if (isLandscape) {
                /*适配横屏展示弹窗的居于高进行适配*/
                AutoSizeCompat.autoConvertDensityBaseOnHeight(parent.resources, 375f)
            } else {
                /*适配设计比例*/
                parent.autoSize()
            }
            return super.onCreateViewHolder(parent, viewType)
        }

        override fun onCreateDefViewHolder(parent: ViewGroup, viewType: Int): BaseViewHolder {
            if (isLandscape) {
                /*适配横屏展示弹窗的居于高进行适配*/
                AutoSizeCompat.autoConvertDensityBaseOnHeight(parent.resources, 375f)
            } else {
                /*适配设计比例*/
                parent.autoSize()
            }
            return super.onCreateDefViewHolder(parent, viewType)
        }

        override fun createBaseViewHolder(parent: ViewGroup, layoutResId: Int): BaseViewHolder {
            if (isLandscape) {
                /*适配横屏展示弹窗的居于高进行适配*/
                AutoSizeCompat.autoConvertDensityBaseOnHeight(parent.resources, 375f)
            } else {
                /*适配设计比例*/
                parent.autoSize()
            }
            return super.createBaseViewHolder(parent, layoutResId)
        }

        override fun convert(holder: BaseViewHolder, item: DiyNewDirection) {
            var isSelect = item.code == selectDirection

            if (isMultiMode) {
                isSelect = selectDirections.contains(item.code)
            }

            val ivDirection = holder.getView<ImageView>(com.govee.base2light.R.id.ivDirection)
            ivDirection.setImageDrawable(ResUtil.getDrawable(item.icon))
            ivDirection.isSelected = isSelect

            val tvDirection = holder.getView<TextView>(com.govee.base2light.R.id.tvDirection)
            tvDirection.gone()
            if (item.strRes != 0) {
                ResUtil.getText(item.strRes)?.let {
                    tvDirection.visible()
                    tvDirection.text = it
                }
            } else if (item.strName.isNotEmpty()) {
                tvDirection.visible()
                tvDirection.text = item.strName
            }
            tvDirection.setTextColor(ResUtil.getColor(if (isSelect) R.color.font_style_60_2_textColor else R.color.font_style_230_1_textColor))
        }
    }
}

data class DiyNewDirection(
    val code: Int,// 协议 code
    val icon: Int,// 图片资源 id
    val strRes: Int, // 名称资源 id
    @Deprecated("可不用配置此参数了，目前判断是否同一个UI采用 icon 和 strRes完全相等则表示为相同") val uiCode: Int = 0 // 对应 UI 的code
) {
    var strName: String = ""

    companion object {


        /*上下聚拢*/
        fun sxjl(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_sxjl,
                R.string.b2light_diy_new_direction_sxjl,
                1
            )
        }

        /*顺时针*/
        fun ssz(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_ssz,
                R.string.b2light_diy_clockwise,
                2
            )
        }

        /*逆时针*/
        fun nsz(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_nsz,
                R.string.b2light_diy_anticlockwise,
                3
            )
        }
        /*左上*/

        fun zs(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_zs,
                R.string.b2light_diy_new_direction_zs,
                4
            )
        }
        /*右上*/

        fun ys(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_ys,
                R.string.b2light_diy_new_direction_ys,
                5
            )
        }
        /*左下*/

        fun zx(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_zx,
                R.string.b2light_diy_new_direction_zx,
                6
            )
        }

        /*右下*/

        fun yx(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_yx,
                R.string.b2light_diy_new_direction_yx,
                7
            )
        }

        /*上*/
        fun up(code: Int, strRes: Int = R.string.b2light_effect_up): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_up,
                strRes,
                8
            )
        }

        /*下*/
        fun down(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_down,
                R.string.b2light_effect_down,
                9
            )
        }

        /**
         * 循环
         */
        fun cycle(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_cycle,
                R.string.b2light_diy_circulation,
                10
            )
        }

        /**
         * 渐变
         */
        fun gradual(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_gradient,
                R.string.app_work_shop_gradual,
                11
            )
        }

        /**
         * 闪烁
         */
        fun flash(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_flashing,
                R.string.b2light_scenes_blinking,
                12
            )
        }

        /*上下扩散*/
        fun sxks(code: Int, defStrId: Int = R.string.b2light_diy_new_direction_sxks): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_sxks,
                defStrId,
                13
            )
        }

        /*61dx上下扩散*/
        fun zongxiangkuosan(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_zongxiang_kuosan,
                R.string.b2light_diy_new_direction_sxks,
            )
        }

        /*61dx上下收缩*/
        fun zongxiangshousuo(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_zongxiang_shousuo,
                R.string.b2light_diy_new_direction_sxjl,
            )
        }

        /*61dx左右收缩*/
        fun hengxiangshousuo(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_hengxiang_shousuo,
                R.string.b2light_diy_directon_zyjulong,
            )
        }

        /*61dx左右扩散*/
        fun hengxiangkuosan(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_hengxiang_kuosan,
                R.string.b2light_diy_directon_zykuosan,
            )
        }

        /*左*/
        fun left(code: Int, strRes: Int = R.string.device_direction_left): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_left,
                strRes,
                14
            )
        }

        /*右*/
        fun right(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_right,
                R.string.device_direction_right,
                15
            )
        }

        /**中心点位置-左上*/
        fun centerLeftTop(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_center_zs,
                R.string.b2light_diy_new_direction_zs,
                16
            )
        }

        /**中心点位置-左下*/
        fun centerLeftBottom(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_center_zuoxia,
                R.string.b2light_diy_new_direction_zx,
                17
            )
        }

        /**中心点位置-中心*/
        fun center(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_center_center,
                R.string.b2light_diy_new_direction_center_center,
                18
            )
        }

        /**中心点位置-右上*/
        fun centerRightTop(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_center_ys,
                R.string.b2light_diy_new_direction_ys,
                19
            )
        }

        /**中心点位置-右下*/
        fun centerRightBottom(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_center_yx,
                R.string.b2light_diy_new_direction_yx,
                20
            )
        }


        /**中心点位置-中上*/
        fun centerMidTop(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_center_zhongshang,
                R.string.b2light_diy_new_direction_zhongshang,
                21
            )
        }

        /**中心点位置-中下*/
        fun centerMidBottom(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_center_zhongxia,
                R.string.b2light_diy_new_direction_zhongxia,
                22
            )
        }


        /**中心点位置-左中*/
        fun centerLeftMid(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_center_zuozhong,
                R.string.b2light_diy_new_direction_zuozhong,
                23
            )
        }

        fun breathe(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_breathe,
                R.string.b2light_scenes_breath,
                24
            )
        }

        /**中心点位置-右中*/
        fun centerrightMid(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_center_youzhong,
                R.string.b2light_diy_new_direction_youzhong,
                25
            )
        }

        /**
         * 扩散
         */
        fun directionKuosan(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.h70b3_h6800_diy_new_kuosan,
                R.string.b2light_diy_kuo_san,
                26
            )

        }

        /**
         * 收缩
         */
        fun directionShouSuo(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.h70b3_h6800_diy_new_julong,
                R.string.b2light_diy_gathered,
                27
            )

        }

        /**
         * 左右聚拢
         */
        fun zyjl(code: Int, strRes: Int = R.string.b2light_diy_gathered): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_zuo_you_julong,
                strRes,
                28
            )
        }

        /**
         * 左右扩散
         */
        fun zyks(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_zuo_you_kuosan,
                R.string.b2light_diy_kuo_san,
                29
            )
        }

        fun shuangXinag(code: Int, defStrId: Int = R.string.direction_two_way): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_zuo_you_kuosan,
                defStrId,
                29
            )
        }

        fun power2End(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_power_end,
                R.string.b2light_power_to_end,
                30
            )
        }

        fun end2Power(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_end_power,
                R.string.b2light_end_to_power,
                31
            )
        }

        /**
         * 文字区域-顶部
         */
        fun textAreaTop(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.component_text_align_top_stroke,
                R.string.b2light_text_top,
                32
            )
        }

        /**
         * 文字区域-中间
         */
        fun textAreaCenter(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.component_text_align_center_stroke,
                R.string.b2light_text_center,
                33
            )
        }

        /**
         * 文字区域-底部
         */
        fun textAreaBottom(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.component_text_align_bottom_stroke,
                R.string.b2light_text_bottom,
                34
            )
        }

        /**
         * 直线
         */
        fun straightLine(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_straight_line,
                R.string.b2light_diy_all,
                24
            )
        }

        /**
         * 顺时针向上
         */
        fun sszxs(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_sszxs,
                R.string.b2light_diy_new_direction_sszxs,
                24
            )
        }

        /**
         * 逆时针向上
         */
        fun nszxs(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_nszxs,
                R.string.b2light_diy_new_direction_nszxs,
                25
            )
        }

        /**
         * 顺时针向下
         */
        fun sszxx(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_sszxx,
                R.string.b2light_diy_new_direction_sszxx,
                26
            )
        }

        /**
         * 逆时针向下
         */
        fun nszxx(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_nszxx,
                R.string.b2light_diy_new_direction_nszxx,
                27
            )
        }

        /**
         * 文本-小
         */
        fun textSmall(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_text_small,
                R.string.b2light_small,
                34
            )
        }

        /**
         * 文本-大
         */
        fun textLarge(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_text_large,
                R.string.b2light_big,
                34
            )
        }

        /**
         * 专业涂鸦动作列表
         */
        fun makeProGraffitiActions(): MutableList<DiyNewDirection> {
            return mutableListOf(
                flash(0),
                up(1),
                down(2),
                left(3),
                right(4),
                zs(5),
                ys(6),
                zx(7),
                yx(8)
            )
        }

        /**
         * 聚拢
         */
        fun jvlong(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.h70b3_h6800_diy_new_julong,
                R.string.b2light_diy_gathered
            )
        }

        /**
         * 扩散
         */
        fun kuosan(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.h70b3_h6800_diy_new_kuosan,
                R.string.b2light_diy_kuo_san
            )
        }

        /**
         * 周转
         */
        fun zhouzhuan(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_zhouzhuan,
                R.string.b2light_diy_zhouzhuan
            )
        }

        /**
         * 旋转
         */
        fun xuanzhuan(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_ssz,
                R.string.b2light_scenes_spin
            )
        }

        /**
         * 随机
         */
        fun suiJi(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_suiji,
                R.string.b2light_effect_random
            )
        }

        /**
         * 随机
         */
        fun shunXu(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_shunxu,
                R.string.b2light_play_mode_order
            )
        }

        /**
         * 同色
         */
        fun tongse(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_tongse,
                R.string.video_mode_samecolor
            )
        }

        /**
         * 多色
         */
        fun duose(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_duose,
                R.string.b2light_diy_new_direction_duose
            )
        }

        /**
         * 单个
         */
        fun dange(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_dange,
                R.string.b2light_diy_num_single
            )
        }

        /**
         * 多个
         */
        fun duoge(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_duoge,
                R.string.b2light_diy_num_multiple
            )
        }

        /**
         * 折返
         */
        fun retrace(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_zhefan,
                R.string.str_turn_back
            )
        }

        /**
         * 从左到右
         * */
        fun leftToRight(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_right,
                R.string.b2light_left_to_right,
            )
        }

        /**
         *从右到左
         */
        fun rightToLeft(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_left,
                R.string.b2light_right_to_left,
            )
        }

        /**
         * 圆形
         */
        fun shapeCircle(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.h70b3_shape_yuanxing,
                R.string.b2light_shape_circle
            )
        }

        /**
         * 正方形/矩形
         */
        fun shapeRect(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.h70b3_shape_juxing,
                R.string.b2light_shape_square
            )
        }

        /**
         * 菱形
         */
        fun shapeRhombus(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.h70b3_shape_lingxing,
                R.string.b2light_shape_diamond,
            )
        }


        /**
         * 径直移动
         */
        fun jzyd(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_jxyd,
                R.string.b2light_diy_jzyd
            )
        }

        /**
         * 叠加
         */
        fun overlay(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.h6630_icon_diy_tuya_overlay,
                R.string.b2light_effect_superimpose
            )
        }

        /**
         * 轮播
         */
        fun carousel(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.h6630_icon_diy_tuya_play,
                R.string.b2light_carousel
            )
        }

        /**
         * 自定义
         */
        fun customize(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.h6022_icon_diy_tuya_custom,
                R.string.b2light_diy_606a_custom
            )
        }

        /**
         * 图层等级 高
         */
        fun layerHigh(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.component_layer_level1_6069,
                R.string.b2light_diy_graffiti_priority_high
            )
        }

        /**
         * 图层等级 中
         */
        fun layerMedium(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.component_layer_level2_6069,
                R.string.b2light_diy_graffiti_priority_medium
            )
        }

        /**
         * 图层等级 低
         */
        fun layerLow(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.component_layer_level3_6069,
                R.string.b2light_diy_graffiti_priority_low
            )
        }

        /**
         * 往返
         */
        fun wangfan(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_direction_wangfan,
                R.string.b2light_effect_ply
            )
        }

        /**
         * 同色
         */
        fun sameColor(code: Int, iconDrawable: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                iconDrawable,
                R.string.video_mode_samecolor
            )
        }

        /**
         * 不同色
         */
        fun diffColor(code: Int, iconDrawable: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                iconDrawable,
                R.string.video_mode_different_color
            )
        }

        /**跳跃*/
        fun jump(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_jump,
                R.string.b2light_diy_jumping
            )
        }

        /**常亮*/
        fun lightOn(code: Int): DiyNewDirection {
            return DiyNewDirection(
                code,
                R.drawable.b2light_diy_new_changliang,
                R.string.b2light_scenes_steady_on
            )
        }
    }
}