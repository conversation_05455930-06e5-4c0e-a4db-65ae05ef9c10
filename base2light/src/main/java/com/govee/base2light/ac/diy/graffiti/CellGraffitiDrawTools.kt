package com.govee.base2light.ac.diy.graffiti

import android.content.Context
import android.graphics.drawable.GradientDrawable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.LinearLayout
import androidx.lifecycle.MutableLiveData
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.color.IColorChoose
import com.govee.base2home.color.PaletteDialogNew
import com.govee.base2home.color.getIPalette
import com.govee.base2home.custom.palette.dialog.LandscapePaletteDialog
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.utils.ColorUtils
import com.govee.base2light.R2.id.paintColor
import com.govee.base2light.databinding.B2lightCellGraffitiDrawToolsBinding
import com.govee.base2light.widget.graffiti.present.draw_type_eraser
import com.govee.base2light.widget.graffiti.present.draw_type_graphics_drawing
import com.govee.base2light.widget.graffiti.present.draw_type_ordinary_graffiti
import com.govee.base2light.widget.graffiti.present.draw_type_paint_buckets
import com.govee.base2light.widget.graffiti.present.draw_type_paint_eyedropper
import com.govee.base2light.widget.graffiti.present.draw_type_symmetry
import com.govee.base2light.widget.graffiti.present.draw_type_symmetry_horizontal
import com.govee.base2light.widget.graffiti.present.draw_type_symmetry_vertical
import com.govee.base2light.widget.graffiti.present.graphics_drawing_type_hollow_circle
import com.govee.base2light.widget.graffiti.present.graphics_drawing_type_hollow_rectangle
import com.govee.base2light.widget.graffiti.present.graphics_drawing_type_line
import com.govee.base2light.widget.graffiti.present.graphics_drawing_type_solid_circle
import com.govee.base2light.widget.graffiti.present.graphics_drawing_type_solid_rectangle
import com.govee.base2light.widget.graffiti.view.CellGraffitiCanZoomView
import com.govee.base2light.widget.graffiti.view.EyeDropperListener
import com.govee.base2light.widget.graffiti.view.SelectDialog
import com.govee.ui.R
import com.govee.ui.dialog.ConfirmDialog
import com.ihoment.base2app.util.ResUtil
import me.jessyan.autosize.AutoSizeCompat


/**
 * @package：com.govee.base2light.ac.diy.graffiti
 * @time：2025/1/21 下午2:42
 * @author：zhangchenxiang
 * @des：
 */
class CellGraffitiDrawTools(
    private val context: Context,
    private val cGCanZoomView: CellGraffitiCanZoomView,
    private val sku: String,
    private val isLandscape: Boolean = false
) {

    companion object {
        private const val STATE_PAINT = 0
        private const val STATE_SYMMETRY = 1
        private const val STATE_SHAPE = 2
        private const val STATE_ERASER = 3
        private const val STATE_YOUQITONG = 4
        private const val STATE_PEEK = 5//吸管
    }

    init {
        if (isLandscape) {
            AutoSizeCompat.autoConvertDensityBaseOnHeight(context.resources, 375f)
        }
    }

    val binding: B2lightCellGraffitiDrawToolsBinding by lazy {
        B2lightCellGraffitiDrawToolsBinding.inflate(LayoutInflater.from(context))
    }
    private var drawableRTB: GradientDrawable? = null
    private var drawableRTBStroke: GradientDrawable? = null

    //镜像对称选项
    private val duiChenSelect: MutableMap<Int, Int> by lazy {
        mutableMapOf<Int, Int>().apply {
            put(draw_type_symmetry_horizontal, R.drawable.h70b345_btn_tuya_duicheng_zuoyou)
            put(draw_type_symmetry_vertical, R.drawable.h70b345_btn_tuya_duicheng_shangxia)
        }
    }

    //图形工具
    private val tuxing: MutableMap<Int, Int> by lazy {
        mutableMapOf<Int, Int>().apply {
            put(graphics_drawing_type_solid_rectangle, R.drawable.h70b345_btn_tuya_xingzhuang_fang1)
            put(
                graphics_drawing_type_hollow_rectangle,
                R.drawable.h70b345_btn_tuya_xingzhuang_fang2
            )
            put(graphics_drawing_type_solid_circle, R.drawable.h70b345_btn_tuya_xingzhuang_yuan1)
            put(graphics_drawing_type_hollow_circle, R.drawable.h70b345_btn_tuya_xingzhuang_yuan2)
            put(graphics_drawing_type_line, R.drawable.h70b345_btn_tuya_xingzhuang_line)
        }
    }

    private val supportedDrawTools = mutableListOf<View>()//支持的绘制工具
    private val drawTools = mutableMapOf<Int, View>()

    //上一个选中的工具
    var mLastState = STATE_PAINT
    var toolsRemind: ((strResId: Int) -> Unit)? = null

    init {
        drawTools.clear()
        supportedDrawTools.clear()
    }

    private var column: Int = 0
    private var row: Int = 0

    private var selectView: View? = null

    /**
     * 必须调用这个，否则在用到这俩参数的地方会抛出异常
     */
    fun initColumnRow(column: Int, row: Int): CellGraffitiDrawTools {
        this.column = column
        this.row = row
        initAllTools()
        selectView = binding.ivHuaBi//默认为画笔
        binding.ivHuaBi.isSelected = true
        mLastState = STATE_PAINT
        toolsRemind?.invoke(R.string.b2light_paint_tips)
        cGCanZoomView.setGraphicsDraw(draw_type_ordinary_graffiti, column, row)
        return this
    }

    /**
     * AI素材
     */
    fun clickAI(clickAI: (View) -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivAIBoot)) {
            binding.clToolsContent.removeView(binding.ivAIBoot)
            drawTools[0] = binding.ivAIBoot
        }
        binding.ivAIBoot.clickDelay {
            clickAI(binding.ivAIBoot)
        }
        return this
    }

    /**
     * 选择颜色
     * @param selectColorPre 选择颜色弹窗弹出来之前的准动作
     * @param curColor 当前颜色
     * @param selectResult 选择的颜色
     */
    fun clickSelectColor(
        selectColorPre: (View) -> Unit,
        curColor: () -> Int,
        selectResult: MutableLiveData<MutableList<Int>>
    ): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.clSelectColor)) {
            binding.clToolsContent.removeView(binding.clSelectColor)
            drawTools[1] = binding.clSelectColor
        }
        binding.clSelectColor.clickDelay {
            selectColorPre(binding.clSelectColor)
            if (isLandscape) {
                LandscapePaletteDialog(
                    context = context,
                    liveData = selectResult,
                    paintColor = paintColor,
                    sku,
                    supportColorLess = false
                ).show()
            } else {
                showPaletteDialog(curColor.invoke()) { newColors: IntArray ->
                    selectResult.value = newColors.toMutableList()
                }
            }

        }
        return this
    }

    private fun showPaletteDialog(curColor: Int, chooseColor: (IntArray) -> Unit) {
        val iColorChoose = object : IColorChoose {
            override fun isSupportChooseMultiColors(): Boolean {
                return false
            }

            override fun chooseColor(newColors: IntArray) {
                chooseColor.invoke(newColors)
            }

            override fun chooseColorRealTime(color: Int, isFirstDown: Boolean) {
            }
        }

        val iPalette = getIPalette(sku, null, false, iColorChoose, null, null, true)
        PaletteDialogNew.createDialog(context, iPalette, curColor).show()
    }
    /**
     * 画笔
     */
    fun clickPen(): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivHuaBi)) {
            binding.clToolsContent.removeView(binding.ivHuaBi)
            drawTools[2] = binding.ivHuaBi
        }
        binding.ivHuaBi.clickDelay {
            selectView?.isSelected = false
            selectView = binding.ivHuaBi//默认为画笔
            selectView?.isSelected = true
            mLastState = STATE_PAINT
            toolsRemind?.invoke(R.string.b2light_paint_tips)
            cGCanZoomView.graffiti = true
            cGCanZoomView.setGraphicsDraw(draw_type_ordinary_graffiti, column, row)
            analytics(ParamFixedValue.graffiti_full_click_paint)
        }
        return this
    }

    fun updateColor(color: Int) {
        val noColor = ColorUtils.isNoColor(color)
        val nearWhiteColor = ColorUtils.isNearWhiteColor(color)
        if (noColor) {
            binding.ivPaintColor.background =
                ResUtil.getDrawable(R.mipmap.color_icon_un)
        } else if (nearWhiteColor) {
            if (drawableRTBStroke == null) {
                drawableRTBStroke =
                    ResUtil.getDrawable(R.drawable.component_diy_color_block_boder_radius_round) as GradientDrawable
            }
            drawableRTBStroke?.setColor(color)
            binding.ivPaintColor.background = drawableRTBStroke
        } else {
            if (drawableRTB == null) {
                drawableRTB =
                    ResUtil.getDrawable(R.drawable.component_diy_color_block_fill_radius_round) as GradientDrawable
            }
            drawableRTB?.setColor(color)
            binding.ivPaintColor.background = drawableRTB
        }
        cGCanZoomView.updatePaintColor(color)
    }

    private var peek: ((peekColor: Int) -> Unit)? = null

    /**
     * 吸管
     */
    fun clickEyeDropper(peek: (peekColor: Int) -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivEyeDropper)) {
            binding.clToolsContent.removeView(binding.ivEyeDropper)
            drawTools[3] = binding.ivEyeDropper
        }
        this.peek = peek
        binding.ivEyeDropper.clickDelay {
            mLastState = STATE_PEEK
            selectView?.isSelected = false
            selectView = binding.ivEyeDropper
            selectView?.isSelected = true
            toolsRemind?.invoke(R.string.h6630_b2light_paint_xi_guan_desc)
            cGCanZoomView.setGraphicsDraw(draw_type_paint_eyedropper, column, row)
            cGCanZoomView.setEyeDropperListener(object : EyeDropperListener {
                override fun onDrop(peek: Int) {
                    val noColor = ColorUtils.isNoColor(peek)
                    if (noColor) return
                    peek(peek)
                    updateColor(peek)
                }
            })
            analytics(ParamFixedValue.graffiti_full_click_pipette)
        }
        return this
    }

    /**
     * 撤回一步
     */
    fun clickRevoke(revoke: () -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivTuiHou)) {
            binding.clToolsContent.removeView(binding.ivTuiHou)
            drawTools[4] = binding.ivTuiHou
        }
        binding.ivTuiHou.clickDelay {
            revoke()
            analytics(ParamFixedValue.graffiti_full_click_revoke)
            cGCanZoomView.clickRevoke()
        }
        return this
    }

    /**
     * 是否可撤回
     */
    fun canRevoke(canRevoke: Boolean) {
        binding.ivTuiHou.isEnabled = canRevoke
        binding.ivTuiHou.alpha = if (canRevoke) 1.0f else 0.3f
    }

    /**
     * 恢复撤回的一步
     */
    fun clickRestore(restore: () -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivQianJin)) {
            binding.clToolsContent.removeView(binding.ivQianJin)
            drawTools[5] = binding.ivQianJin
        }
        binding.ivQianJin.clickDelay {
            restore()
            analytics(ParamFixedValue.graffiti_full_click_recover)
            cGCanZoomView.clickRestore()
        }
        return this
    }

    /**
     * 是否可以恢复撤回的操作
     */
    fun canRestore(canRestore: Boolean) {
        binding.ivQianJin.isEnabled = canRestore
        binding.ivQianJin.alpha = if (canRestore) 1.0f else 0.3f
    }

    /**
     * 橡皮擦
     */
    fun clickEraser(clickEraser: (View) -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivErazer)) {
            binding.clToolsContent.removeView(binding.ivErazer)
            drawTools[6] = binding.ivErazer
        }
        binding.ivErazer.clickDelay {
            clickEraser(binding.ivErazer)
            mLastState = STATE_ERASER
            selectView?.isSelected = false
            selectView = binding.ivErazer
            selectView?.isSelected = true
            cGCanZoomView.graffiti = false
            toolsRemind?.invoke(R.string.b2light_paint_type_erase)
            cGCanZoomView.setGraphicsDraw(draw_type_eraser, column, row)
            analytics(ParamFixedValue.graffiti_full_click_rubber)
        }
        return this
    }

    fun canClear(canClear: Boolean) {
        binding.ivQingKong.isEnabled = canClear
        binding.ivQingKong.alpha = if (canClear) 1.0f else 0.3f
    }

    /**
     * 清空画布
     */
    fun clickClearCanvas(clearCanvas: () -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivQingKong)) {
            binding.clToolsContent.removeView(binding.ivQingKong)
            drawTools[7] = binding.ivQingKong
        }
        binding.ivQingKong.clickDelay {
            analytics(ParamFixedValue.graffiti_full_click_clear)
            if (isLandscape) {
                AutoSizeCompat.autoConvertDensityBaseOnHeight(context.resources, 375f)
            }
            ConfirmDialog.showConfirmDialog(
                context,
                ResUtil.getString(R.string.b2light_clean_canvas_des),
                ResUtil.getString(R.string.cancel),
                ResUtil.getString(R.string.confirm)
            ) {
                cGCanZoomView.clickClearCanvas()
                clearCanvas()
            }
        }
        return this
    }

    /**
     * 素材库
     */
    fun clickMaterial(clickMaterial: (View) -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivSuCaiKu)) {
            binding.clToolsContent.removeView(binding.ivSuCaiKu)
            drawTools[8] = binding.ivSuCaiKu
        }
        binding.ivSuCaiKu.clickDelay {
            analytics(ParamFixedValue.graffiti_full_click_material)
            clickMaterial(binding.ivSuCaiKu)
        }
        return this
    }

    /**
     * 选择图片生成涂鸦
     */
    fun clickSelectImage(clickSelectImage: (View) -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivSelectPic)) {
            binding.clToolsContent.removeView(binding.ivSelectPic)
            drawTools[9] = binding.ivSelectPic
        }
        binding.ivSelectPic.clickDelay {
            analytics(ParamFixedValue.graffiti_full_click_upload_image)
            clickSelectImage(binding.ivSelectPic)
        }
        return this
    }

    /**
     * 选择GIF图生成图层
     */
    fun clickSelectGif(clickSelectGif: (View) -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivSelectGif)) {
            binding.clToolsContent.removeView(binding.ivSelectGif)
            drawTools[10] = binding.ivSelectGif
        }
        binding.ivSelectGif.clickDelay {
            analytics(ParamFixedValue.graffiti_full_click_upload_gif)
            clickSelectGif(binding.ivSelectGif)
        }
        return this
    }

    /**
     * 油漆桶
     */
    fun clickBucket(clickBucket: (View) -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivYouQiTong)) {
            binding.clToolsContent.removeView(binding.ivYouQiTong)
            drawTools[11] = binding.ivYouQiTong
        }
        binding.ivYouQiTong.clickDelay {
            mLastState = STATE_YOUQITONG
            analytics(ParamFixedValue.graffiti_full_click_bucket)
            clickBucket(binding.ivYouQiTong)
            selectView?.isSelected = false
            selectView = binding.ivYouQiTong
            selectView?.isSelected = true
            cGCanZoomView.graffiti = true
            toolsRemind?.invoke(R.string.h70b3_b2light_paint_you_qi_tong)
            cGCanZoomView.setGraphicsDraw(draw_type_paint_buckets, column, row)
        }
        return this
    }

    /**
     * 框选
     */
    fun clickBoxSelect(clickBoxSelect: (View) -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivKuangXuan)) {
            binding.clToolsContent.removeView(binding.ivKuangXuan)
            drawTools[12] = binding.ivKuangXuan
        }
        binding.ivKuangXuan.clickDelay {
            analytics(ParamFixedValue.graffiti_full_click_selection)
            clickBoxSelect(binding.ivKuangXuan)
        }
        return this
    }

    /**
     * 对称绘画
     */
    fun clickSymmetry(): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivDuiChen)) {
            //默认水平对成
            binding.ivDuiChen.tag = draw_type_symmetry_horizontal
            binding.clToolsContent.removeView(binding.ivDuiChen)
            drawTools[13] = binding.ivDuiChen
        }
        binding.ivDuiChen.clickDelay {
            selectView?.isSelected = false
            selectView = binding.ivDuiChen
            selectView?.isSelected = true
            analytics(ParamFixedValue.graffiti_full_click_symmetry)
            mLastState = STATE_SYMMETRY
            cGCanZoomView.graffiti = true
            toolsRemind?.invoke(R.string.b2light_paint_type_symmetry_new)
            val subType = binding.ivDuiChen.tag as Int
            cGCanZoomView.setGraphicsDraw(
                draw_type_symmetry,
                subType,
                column,
                row
            )
        }
        binding.ivDuiChen.setOnLongClickListener {
            //长按切换对称方式
            val subType = binding.ivDuiChen.tag as Int
            SelectDialog(
                context,
                subType,
                duiChenSelect,
                R.string.b2light_select_dui_cheng_title,
                isLandscape
            ) {
                selectView?.isSelected = false
                selectView = binding.ivDuiChen
                selectView?.isSelected = true
                mLastState = STATE_SYMMETRY
                binding.ivDuiChen.tag = it
                cGCanZoomView.setGraphicsDraw(
                    draw_type_symmetry,
                    it,
                    column,
                    row
                )
                toolsRemind?.invoke(R.string.b2light_paint_type_symmetry_new)
                binding.ivDuiChen.setImageDrawable(duiChenSelect[it]?.let { it1 ->
                    ResUtil.getDrawable(
                        it1
                    )
                })
            }.show()
            false
        }
        return this
    }

    /**
     * 图形绘画
     */
    fun clickShape(clickShape: () -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivTuXing)) {
            binding.clToolsContent.removeView(binding.ivTuXing)
            drawTools[15] = binding.ivTuXing
        }
        //图形绘制 默认实心矩形
        binding.ivTuXing.tag = graphics_drawing_type_solid_rectangle
        binding.ivTuXing.clickDelay {
            selectView?.isSelected = false
            selectView = binding.ivTuXing
            selectView?.isSelected = true
            mLastState = STATE_SHAPE
            toolsRemind?.invoke(R.string.b2light_paint_type_graphics)
            analytics(ParamFixedValue.graffiti_full_click_image)
            cGCanZoomView.graffiti = true
            //图形绘制
            val subType = binding.ivTuXing.tag as Int
            cGCanZoomView.setGraphicsDraw(
                draw_type_graphics_drawing,
                subType,
                column,
                row
            )
            clickShape()
        }
        binding.ivTuXing.setOnLongClickListener {
            //长按切换图形绘制类型
            val subType = binding.ivTuXing.tag as Int
            SelectDialog(
                context,
                subType,
                tuxing,
                R.string.b2light_select_graphics_title,
                isLandscape
            ) {
                selectView?.isSelected = false
                selectView = binding.ivTuXing
                selectView?.isSelected = true
                toolsRemind?.invoke(R.string.b2light_paint_type_graphics)
                mLastState = STATE_SHAPE
                binding.ivTuXing.tag = it
                cGCanZoomView.setGraphicsDraw(
                    draw_type_graphics_drawing,
                    it,
                    column,
                    row
                )
                binding.ivTuXing.setImageDrawable(tuxing[it]?.let { it1 ->
                    ResUtil.getDrawable(
                        it1
                    )
                })
            }.show()
            clickShape()
            false
        }
        return this
    }

    /**
     * 渐变
     */
    fun clickGradient(clickGradient: (View) -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivJianBian)) {
            binding.clToolsContent.removeView(binding.ivJianBian)
            drawTools[14] = binding.ivJianBian
        }
        binding.ivJianBian.clickDelay {
            clickGradient(binding.ivJianBian)
            cGCanZoomView.graffiti = true
            analytics(ParamFixedValue.graffiti_full_click_gradual)
        }
        return this
    }

    /**
     * 参考线
     */
    fun clickReferenceLine(): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivCanKaoXian)) {
            binding.clToolsContent.removeView(binding.ivCanKaoXian)
            drawTools[16] = binding.ivCanKaoXian
        }
        binding.ivCanKaoXian.isSelected = cGCanZoomView.showGuideLines
        binding.ivCanKaoXian.clickDelay {
            analytics(ParamFixedValue.graffiti_click_guide_line)
            binding.ivCanKaoXian.isSelected = !binding.ivCanKaoXian.isSelected
            cGCanZoomView.showGuideLines = binding.ivCanKaoXian.isSelected
        }
        return this
    }

    fun setReferenceLineStatus(isSelected: Boolean) {
        if (isLandscape) {
            AutoSizeCompat.autoConvertDensityBaseOnHeight(cGCanZoomView.context.resources, 375f)
        }
        binding.ivCanKaoXian.isSelected = isSelected
        cGCanZoomView.showGuideLines = binding.ivCanKaoXian.isSelected
    }

    /**
     * 预览当前图层
     */
    fun clickPreviewLayer(clickPreviewLayer: (View) -> Unit): CellGraffitiDrawTools {
        if (!supportedDrawTools.contains(binding.ivPreview)) {
            binding.clToolsContent.removeView(binding.ivPreview)
            drawTools[17] = binding.ivPreview
        }
        binding.ivPreview.clickDelay {
            clickPreviewLayer(binding.ivPreview)
            analytics(ParamFixedValue.graffiti_full_click_preview)
        }
        return this
    }

    /**
     * 恢复所有工具为默认没有选中
     */
    fun initAllTools() {
        val selectCanKaoXian = binding.ivCanKaoXian.isSelected
        supportedDrawTools.forEach {
            it.isSelected = false
        }
        binding.ivCanKaoXian.isSelected = selectCanKaoXian
    }


    fun initSupportToolsList(): RecyclerView.Adapter<BaseViewHolder> {
        val drawToolsAdapter = DrawToolsAdapter()
        supportedDrawTools.clear()
        val sorted = drawTools.keys.sorted()
        sorted.forEach { key ->
            drawTools[key]?.let {
                it.isClickable = true
                supportedDrawTools.add(it)
            }
        }
        drawToolsAdapter.setList(supportedDrawTools)
        return drawToolsAdapter
    }

    /**
     * 设置控件是否支持点击
     */
    fun setViewClickEnabled(enabled: Boolean) {
        supportedDrawTools.forEach {
            it.isClickable = enabled
        }
    }


    fun backToLastState() {
        selectView?.isSelected = false
        cGCanZoomView.graffiti = true
        when (mLastState) {
            STATE_PAINT -> {
                selectView = binding.ivHuaBi
                cGCanZoomView.setGraphicsDraw(draw_type_ordinary_graffiti, column, row)
                toolsRemind?.invoke(R.string.b2light_paint_tips)
            }

            STATE_SYMMETRY -> {
                selectView = binding.ivDuiChen
                val subType = binding.ivDuiChen.tag as Int
                cGCanZoomView.setGraphicsDraw(
                    draw_type_symmetry,
                    subType,
                    column,
                    row
                )
                toolsRemind?.invoke(R.string.b2light_paint_type_symmetry_new)
            }

            STATE_SHAPE -> {
                selectView = binding.ivTuXing
                cGCanZoomView.setGraphicsDraw(
                    draw_type_graphics_drawing,
                    binding.ivTuXing.tag as Int,
                    column,
                    row
                )
                toolsRemind?.invoke(R.string.b2light_paint_type_graphics)
            }

            STATE_ERASER -> {
                selectView = binding.ivErazer
                toolsRemind?.invoke(R.string.b2light_paint_type_erase)
                cGCanZoomView.setGraphicsDraw(
                    draw_type_eraser,
                    column,
                    row
                )
            }

            STATE_YOUQITONG -> {
                selectView = binding.ivYouQiTong
                toolsRemind?.invoke(R.string.h70b3_b2light_paint_you_qi_tong)
                cGCanZoomView.setGraphicsDraw(
                    draw_type_paint_buckets,
                    column,
                    row
                )
            }

            STATE_PEEK -> {
                peek?.let {
                    selectView = binding.ivEyeDropper
                    toolsRemind?.invoke(R.string.h6630_b2light_paint_xi_guan_desc)
                    cGCanZoomView.setGraphicsDraw(draw_type_paint_eyedropper, column, row)
                    cGCanZoomView.setEyeDropperListener(object : EyeDropperListener {
                        override fun onDrop(peek: Int) {
                            it(peek)
                            updateColor(peek)
                        }
                    })
                }
            }

            else -> {
                //画笔
                selectView = binding.ivHuaBi
                toolsRemind?.invoke(R.string.b2light_paint_tips)
                cGCanZoomView.setGraphicsDraw(
                    draw_type_ordinary_graffiti,
                    column,
                    row
                )
            }

        }

        selectView?.isSelected = true
    }

    private fun analytics(value: String) {
        if (value.isEmpty()) return
        AnalyticsRecorder.getInstance()
            .recordUseCount(sku, value)
    }

    private inner class DrawToolsAdapter :
        BaseQuickAdapter<View, BaseViewHolder>(com.govee.base2light.R.layout.b2light_item_draw_tools) {
        override fun convert(holder: BaseViewHolder, item: View) {
            val content = holder.getView<LinearLayout>(com.govee.base2light.R.id.llContent)
            content.removeAllViews()
            if (item.parent != null) {
                (item.parent as ViewGroup).removeView(item)
            }
            content.addView(item)
        }
    }
}