package com.govee.base2light.ac.diy.v1;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.text.TextUtils;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;

import com.govee.base2home.Constant;
import com.govee.base2home.account.LoginActivity;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.color.IColorChoose;
import com.govee.base2home.color.IPalette;
import com.govee.base2home.color.PaletteDialogNew;
import com.govee.base2home.color.PaletteInterfaceKt;
import com.govee.base2home.custom.DragSortRecyclerView;
import com.govee.base2home.sku.ColorsM;
import com.govee.base2home.util.ClickUtil;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.home.account.config.AccountConfig;
import com.govee.ui.R;
import com.govee.ui.dialog.ConfirmDialogV3;
import com.govee.ui.dialog.DefScrollHintDialog;
import com.govee.ui.dialog.PicsHintDialog;
import com.ihoment.base2app.adapter.BaseListAdapter;
import com.ihoment.base2app.infra.SharedPreManager;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.ResUtil;
import com.ihoment.base2app.util.ToastUtil;
import com.zhy.android.percent.support.PercentRelativeLayout;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;
import butterknife.ButterKnife;
import butterknife.OnClick;

/**
 * Create by xieyingwu on 2020/8/4
 * diy颜色编辑器$
 */
public class ViewDiyColorEdit extends PercentRelativeLayout {
    DragSortRecyclerView diyColorList;

    private EditColorAdapter adapter;
    private final List<ColorModel> colorModels = new ArrayList<>();
    private SmartPickColorListener smartPickColorListener;
    private RecommendColorListener recommendColorListener;
    private boolean showTransparent;
    private String sku;

    public void setSku(String sku) {
        this.sku = sku;
    }

    public ViewDiyColorEdit(Context context) {
        super(context);
        layout();
    }

    public ViewDiyColorEdit(Context context, AttributeSet attrs) {
        super(context, attrs);
        layout();
    }

    public ViewDiyColorEdit(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        layout();
    }

    public void setShowTransparent(boolean showTransparent) {
        this.showTransparent = showTransparent;
    }

    private void layout() {
        View.inflate(getContext(), com.govee.base2light.R.layout.b2light_layout_diy_color_edit, this);
        diyColorList = findViewById(com.govee.base2light.R.id.diy_color_list);
        ButterKnife.bind(this);
        diyColorList = findViewById(com.govee.base2light.R.id.diy_color_list);
        adapter = new EditColorAdapter();
        diyColorList.setItemType(1);
        adapter.setItems(colorModels);
        adapter.setColorListener(new ColorListener() {
            @Override
            public void addColor(int pos) {
                toAddNewColor(pos);
            }

            @Override
            public void deleteColor(int pos) {
                toDeleteColor(pos);
            }

            @Override
            public void editColor(int pos) {
                toEditColor(pos);
            }
        });
        diyColorList.setOnDragListener(new DragSortRecyclerView.OnDragListener() {
            @Override
            public void onStart() {

            }

            @Override
            public void onStop() {
                if (!diyColorList.isComputingLayout()) {
                    adapter.notifyDataSetChanged();
                }
            }
        });
        diyColorList.setAdapter(adapter);
        diyColorList.setNestedScrollingEnabled(false);
        diyColorList.setFocusable(false);
        diyColorList.setFocusableInTouchMode(false);
        GridLayoutManager gridLayoutManager = new GridLayoutManager(getContext(), 5);
        diyColorList.setLayoutManager(gridLayoutManager);
    }

    public void setSmartPickColorListener(SmartPickColorListener smartPickColorListener) {
        this.smartPickColorListener = smartPickColorListener;
    }

    public void setRecommendColorListener(RecommendColorListener recommendColorListener) {
        this.recommendColorListener = recommendColorListener;
    }

    /**
     * 更新支持的颜色
     *
     * @param minColorNum
     * @param maxColorNum
     * @param colors
     * @param showTransparent
     */
    public void updateColor(int minColorNum, int maxColorNum, int[] colors, boolean showTransparent) {
        this.showTransparent = showTransparent;
        /*更新颜色数量限制*/
        adapter.setColorRange(minColorNum, maxColorNum);
        colorModels.clear();
        boolean needAddColorModel = true;
        if (colors != null && colors.length > 0) {
            for (int color : colors) {
                if (!showTransparent && color == ColorUtils.toColor(1, 1, 1)) continue;
                ColorModel colorModel = ColorModel.makeShowing(color);
                colorModels.add(colorModel);
                /*若已达到最大支持颜色数值，则不再添加*/
                if (colorModels.size() == maxColorNum) break;
            }
            needAddColorModel = colorModels.size() < maxColorNum;
        }
        if (needAddColorModel) {
            colorModels.add(ColorModel.makeAdd());
        }
        adapter.notifyDataSetChanged();
        int[] curColors = getColors();
        if (curColors != null) {
            diyColorList.setEnableDragNum(curColors.length);
        }
    }

    private void toEditColor(int pos) {
        int size = colorModels.size();
        if (pos < 0 || pos >= size) return;
        ColorModel colorModel = colorModels.get(pos);
        if (colorModel.isShowingColor()) {
            IColorChoose iColorChoose = new IColorChoose() {
                @Override
                public boolean isSupportChooseMultiColors() {
                    return false;
                }

                @Override
                public void chooseColor(@NonNull int[] newColors) {
                    colorModel.color = newColors[0];
                    adapter.notifyItemChanged(pos);
                }

                @Override
                public void chooseColorRealTime(int color, boolean isFirstDown) {

                }
            };
            showPaletteDialog(colorModel.color, iColorChoose);
        }
    }

    private void toDeleteColor(int pos) {
        int size = colorModels.size();
        if (pos < 0 || pos >= size) return;
        colorModels.remove(pos);
        if (colorModels.isEmpty()) {
            colorModels.add(ColorModel.makeAdd());
        } else {
            int sizeNow = colorModels.size();
            ColorModel lastColorModel = colorModels.get(sizeNow - 1);
            if (lastColorModel.isShowingColor() && adapter.needAddColorShowing(sizeNow)) {
                colorModels.add(ColorModel.makeAdd());
            }
        }
        adapter.notifyDataSetChanged();
        int[] curColors = getColors();
        if (curColors != null) {
            diyColorList.setEnableDragNum(curColors.length);
        }
    }

    private void checkColorDragHint() {
        boolean color_drag_hint = SharedPreManager.getInstance().getBoolean("color_drag_hint", false);
        if (!color_drag_hint) {
            int w = AppUtil.getScreenWidth() * 300 / 375;
            int h = AppUtil.getScreenWidth() * 156 / 375;
            PicsHintDialog.showPicsHintDialog(getContext(), R.mipmap.new_guide_pic_color_sort, w, h, ResUtil.getString(R.string.app_sort_color_hint_2));
            SharedPreManager.getInstance().saveBoolean("color_drag_hint", true);
        }
    }

    private void toAddNewColor(int pos) {
        int size = colorModels.size();
        if (pos < 0 || pos >= size) return;
        ColorModel colorModel = colorModels.get(pos);
        if (colorModel.isShowingColor()) return;

        IColorChoose iColorChoose = new IColorChoose() {
            @Override
            public boolean isSupportChooseMultiColors() {
                return true;
            }

            @Override
            public void chooseColor(@NonNull int[] newColors) {
                colorModel.toShowingColor(newColors[0]);
                int length = newColors.length;
                int curSize = size;
                if (length > 1) {
                    for (int i = 1; i < length; i++) {
                        int newColor = newColors[i];
                        if (adapter.needAddColorShowing(curSize)) {
                            colorModels.add(ColorModel.makeShowing(newColor));
                        }
                        curSize++;
                    }
                }
                if (curSize >= 2) {
                    checkColorDragHint();
                }
                if (adapter.needAddColorShowing(curSize)) {
                    colorModels.add(ColorModel.makeAdd());
                }
                adapter.notifyDataSetChanged();
                int[] curColors = getColors();
                if (curColors != null) {
                    diyColorList.setEnableDragNum(curColors.length);
                }
            }

            @Override
            public void chooseColorRealTime(int color, boolean isFirstDown) {

            }
        };
        showPaletteDialog(colorModel.color, iColorChoose);
    }

    private void showPaletteDialog(int curColor, IColorChoose iColorChoose) {
        IPalette iPalette = PaletteInterfaceKt.getIPalette(sku, null, showTransparent, iColorChoose, null, null, true);
        PaletteDialogNew.Companion.createDialog(getContext(), iPalette, curColor, true).show();
    }
    @OnClick({com.govee.base2light.R2.id.smart_color_hint})
    public void onClickColorHint() {
        if (ClickUtil.getInstance.clickQuick()) return;
        DefScrollHintDialog.createDialog(getContext(),
                ResUtil.getString(R.string.b2light_choose_color_hint),
                ResUtil.getString(R.string.b2light_photo_choose_hint1),
                ResUtil.getString(R.string.hint_done_got_it), true
        ).show();
    }

    @OnClick(com.govee.base2light.R2.id.recommend_color_container)
    public void onClickRecommendColorContainer() {
        if (ClickUtil.getInstance.clickQuick()) return;
        if (recommendColorListener != null) recommendColorListener.toRecommendColor();
    }

    @OnClick(com.govee.base2light.R2.id.tv_save_color)
    public void onClickSaveColor() {
        if (ClickUtil.getInstance.clickQuick()) return;
        if (!AccountConfig.read().isHadToken()) {
            ConfirmDialogV3.showConfirmDialog(getContext(), ResUtil.getString(R.string.login_first_label),
                    ResUtil.getString(R.string.cancel), ResUtil.getString(R.string.to_login_now),
                    new ConfirmDialogV3.DoneListener() {
                        @Override
                        public void doDone() {
                            LoginActivity.jump2LoginAc(getContext(), "", false);
                        }

                        @Override
                        public void doCancel() {

                        }
                    });
            return;
        }
        int[] colors = getColors();
        if (colors == null || colors.length == 0) {
            ToastUtil.getInstance().toast(R.string.app_set_color_first);
            return;
        }
        List<Integer> saveColors = new ArrayList<>();
        for (int color : colors) {
            if (Constant.colorTemperatureArray.contains(color)) {
                color = ColorUtils.toWhite();
            }
            if (!saveColors.contains(color)) {
                saveColors.add(color);
            }
        }
        if (saveColors.size() == 0) return;
        if (!TextUtils.isEmpty(sku)) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.my_color_save, ParamFixedValue.saveMyColor(sku, ParamFixedValue.diy));
        }
        ColorsM.getInstance.saveColors(getContext(), saveColors, null);
    }

    @OnClick(com.govee.base2light.R2.id.smart_color_container)
    public void onClickPicPickColor() {
        if (ClickUtil.getInstance.clickQuick()) return;
        if (smartPickColorListener != null) smartPickColorListener.smartPickColor();
    }

    private static class ColorModel {
        public static final int view_type_showing = 0;
        public static final int view_type_add = 1;
        public int viewType;
        public int color;

        public boolean isShowingColor() {
            return viewType == view_type_showing;
        }

        public void toShowingColor(int color) {
            viewType = view_type_showing;
            this.color = color;
        }

        static ColorModel makeAdd() {
            ColorModel colorModel = new ColorModel();
            colorModel.viewType = view_type_add;
            return colorModel;
        }

        static ColorModel makeShowing(int color) {
            ColorModel colorModel = new ColorModel();
            colorModel.viewType = view_type_showing;
            colorModel.color = color;
            return colorModel;
        }
    }

    /**
     * 回收
     */
    public void recycler() {
        PaletteDialogNew.Companion.hideDialog();
        DefScrollHintDialog.hideDialog();
        smartPickColorListener = null;
        recommendColorListener = null;
    }

    interface ColorListener {
        void addColor(int pos);

        void deleteColor(int pos);

        void editColor(int pos);
    }

    public static class EditColorAdapter extends BaseListAdapter<ColorModel> {

        private int minColorNum;
        private int maxColorNum;
        private ColorListener colorListener;

        public void setColorListener(ColorListener colorListener) {
            this.colorListener = colorListener;
        }

        public void setColorRange(int minColorNum, int maxColorNum) {
            this.minColorNum = minColorNum;
            this.maxColorNum = maxColorNum;
        }

        public boolean needAddColorShowing(int allSize) {
            return maxColorNum > allSize;
        }

        @Override
        protected int getLayout(int viewType) {
            if (viewType == ColorModel.view_type_add)
                return com.govee.base2light.R.layout.b2light_diy_color_view_type_add;
            return com.govee.base2home.R.layout.activity_edit_color_item;
        }

        @Override
        public int getItemViewType(int position) {
            return getItem(position).viewType;
        }

        @Override
        protected RecyclerView.ViewHolder createViewHolder(View v, int viewType) {
            if (viewType == ColorModel.view_type_add) {
                return new ViewHolder4AddColor(v);
            }
            return new ViewHolder4ShowingColor(v);
        }

        public class ViewHolder4ShowingColor extends ListItemViewHolder<ColorModel> {
            @BindView(com.govee.base2home.R2.id.icon)
            ImageView icon;
            @BindView(com.govee.base2home.R2.id.color_delete)
            View delete;

            public ViewHolder4ShowingColor(View v) {
                super(v, false, false);
            }

            @Override
            protected void bind(ColorModel item, int position) {
                int color = item.color;
                if (color == ColorUtils.toColor(1, 1, 1)) {
                    icon.setImageDrawable(ResUtil.getDrawable(R.mipmap.new_diy_btn_bg_color_un));
                } else {
                    boolean isWhite = ColorUtils.isNearWhiteColor(color);
                    Drawable drawable = ResUtil.getDrawable(isWhite ? R.drawable.component_color_round_1 : R.drawable.component_color_round_2);
                    if (drawable instanceof GradientDrawable) {
                        ((GradientDrawable) drawable).setColor(color);
                    }
                    icon.setImageDrawable(drawable);
                }
                boolean noDelete = noDelete();
                delete.setEnabled(!noDelete);
                delete.setVisibility(noDelete ? View.INVISIBLE : View.VISIBLE);
            }

            @OnClick(com.govee.base2home.R2.id.icon)
            public void onClickEditColor() {
                if (ClickUtil.getInstance.clickQuick()) return;
                if (colorListener != null) colorListener.editColor(position);
            }

            @OnClick(com.govee.base2home.R2.id.color_delete)
            public void onClickDeleteColor() {
                if (ClickUtil.getInstance.clickQuick()) return;
                if (colorListener != null) colorListener.deleteColor(position);
            }
        }

        private boolean noDelete() {
            if (minColorNum > 0) {
                return getItemCount() - 1 <= minColorNum;/*最少支持颜色数量内都不支持删除*/
            }
            /*默认支持删除*/
            return false;
        }

        public class ViewHolder4AddColor extends ListItemViewHolder<ColorModel> {
            public ViewHolder4AddColor(View v) {
                super(v, false, false);
            }

            @Override
            protected void bind(ColorModel item, int position) {
            }

            @OnClick(com.govee.base2light.R2.id.color_add)
            public void onClickColorAdd() {
                if (ClickUtil.getInstance.clickQuick()) return;
                if (colorListener != null) colorListener.addColor(position);
            }
        }
    }

    public int[] getColors() {
        int size = colorModels.size();
        if (size == 0) return null;
        ColorModel colorModel = colorModels.get(size - 1);
        if (colorModel.isShowingColor() && size == 1) return null;
        int[] colors = new int[colorModel.isShowingColor() ? size : size - 1];
        for (int i = 0; i < colors.length; i++) {
            colors[i] = colorModels.get(i).color;
        }
        return colors;
    }

    public interface SmartPickColorListener {
        void smartPickColor();
    }

    public interface RecommendColorListener {
        void toRecommendColor();
    }


}