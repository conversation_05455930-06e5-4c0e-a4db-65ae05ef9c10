package com.govee.base2light.ac.diy.v1.h61d3

import android.graphics.Bitmap
import androidx.annotation.Keep
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.govee.base2home.main.bean.DeviceBaseInfo
import com.govee.base2kt.utils.BleUtils
import com.govee.base2kt.utils.ColorUtils
import com.govee.base2light.ac.diy.v1.h606a.DiyColorEditViewH606A
import com.govee.base2light.ble.BleUtil
import com.govee.ui.R
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ByteUtil
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.ResUtil

/**
 *     author  : sinrow
 *     time    : 2023/9/25
 *     version : 1.0.0
 *     desc    :
 */

interface IProMsg {
    fun toBytes(pos: Int, msg: ((reason: String) -> Unit)?): MutableList<Byte>?

    fun getSubEffectModel(): MutableList<IntArray> {
        return mutableListOf()
    }

    fun fetchSubEffect(): Byte = 0
    fun getColorModels(): MutableList<DiyColorEditViewH606A.ColorModel>

    fun setSubEffectValue(subEffect: Byte) {}

    fun checkCondition(): Pair<MutableList<Int>, IntArray>

    fun getCurColors(): MutableList<Int>
    fun changeColors(newColors: MutableList<Int>)

    fun changeDirection(direction: Byte) {

    }
}

class Pro {
    interface IPro {
        fun bytes(pageIndex: Int): MutableList<Byte>?
    }

    interface IMsg {
        fun msg(msg: String?)
    }
}


class Pro4H61D3Diy : Pro.IPro {

    companion object {
        const val TAG = "Pro4H61D3Diy"
        const val rule_4_dieJia: Byte = 0x00
        const val rule_4_youXianJiZuiGao: Byte = 0x01
        const val rule_4_youXianJiZuiDi: Byte = 0x02
        const val rule_4_yicichengxian: Byte = 0x03

        const val sub_effect_4_liuDong: Byte = 0x00/*流动*/
        const val sub_effect_4_duiJi: Byte = 0x01/*堆积*/
        const val sub_effect_4_suiJi: Byte = 0x02/*随机*/
        const val sub_effect_4_jihe: Byte = 0x03 /*几何*/
        const val sub_effect_4_xianxing: Byte = 0x04 /*线性*/


        const val pro_direction_zuo: Byte = 0x00
        const val pro_direction_you: Byte = 0x01
        const val pro_direction_shang: Byte = 0x02
        const val pro_direction_xia: Byte = 0x03
        const val pro_direction_kuosan_shangxia: Byte = 0x04
        const val pro_direction_kuosan_zuoyou: Byte = 0x05
        const val pro_direction_jvlong_shangxia: Byte = 0x06
        const val pro_direction_jvlong_zuoyou: Byte = 0x07
        const val pro_direction_shunshizhen: Byte = 0x08/*顺时针*/
        const val pro_direction_nishizhen: Byte = 0x09/*逆时针*/
        const val pro_direction_jvlong: Byte = 0x0a/*聚拢*/
        const val pro_direction_kuosan: Byte = 0x0b/*扩散*/
        const val pro_direction_xian_shunshizhen: Byte = 0x0c/*线性-顺时针*/
        const val pro_direction_xian_nishizhen: Byte = 0x0d/*线性-逆时针*/

        fun subEffectModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(sub_effect_4_liuDong.toInt(), R.string.b2light_scenes_flow))
                add(intArrayOf(sub_effect_4_suiJi.toInt(), R.string.b2light_effect_random))
                add(intArrayOf(sub_effect_4_duiJi.toInt(), R.string.b2light_diy_effect_accumulation))
                add(intArrayOf(sub_effect_4_jihe.toInt(), R.string.b2light_diy_geometry))
                add(intArrayOf(sub_effect_4_xianxing.toInt(), R.string.b2light_diy_xianxin))
            }
        }

        fun getRuleHashMap(): HashMap<Byte, String> {
            return HashMap<Byte, String>().apply {
                put(rule_4_dieJia, ResUtil.getString(R.string.b2light_effect_superimpose))
                put(rule_4_yicichengxian, ResUtil.getString(R.string.b2light_carousel))
            }
        }

        fun byCopy(page4H61D3Effect: Page4H61D3Effect): Page4H61D3Effect {
            return Page4H61D3Effect.copy(page4H61D3Effect)
        }

        fun parse(protocolsBytes: ByteArray): Pro4H61D3Diy? {
            try {
                val layerSize = protocolsBytes[0]
                SafeLog.i(TAG) { "parse layerSize 图层数量 = $layerSize" }
                if (layerSize > 3 || layerSize <= 0) {
                    return null
                }
                val pageEffects = mutableListOf<Page4H61D3Effect>()
                pageEffects.clear()
                val rule = protocolsBytes[1]
                SafeLog.i(TAG) { "parse 重叠规则 $rule" }
                var index = 2
                for (i in 1..layerSize) {
                    val lenBytes = ByteArray(2)
                    System.arraycopy(protocolsBytes, index, lenBytes, 0, 2)
                    val len = BleUtil.getSignedInt(lenBytes, true)
                    SafeLog.i(TAG) { "parse 图层: $i , 图层长度: $len" }
                    val onePageBytes = ByteArray(2 + len)
                    val onePageLen = onePageBytes.size
                    System.arraycopy(protocolsBytes, index, onePageBytes, 0, onePageLen)
                    val page4H61D3Effect = Page4H61D3Effect.parse(onePageBytes) ?: return null
                    pageEffects.add(page4H61D3Effect)
                    index += onePageLen
                }
                val pro4H61D3Diy = Pro4H61D3Diy()
                pro4H61D3Diy.rule = rule
                pro4H61D3Diy.pageEffects.addAll(pageEffects)
                return pro4H61D3Diy
            } catch (e: Exception) {
                e.printStackTrace()
                return null
            }
        }

        /**
         * 生成默认 DIY 效果参数
         */
        fun makeDef(): Pro4H61D3Diy {
            return Pro4H61D3Diy().apply {
                this.rule = rule_4_dieJia
                val page4H61D3Effect = Page4H61D3Effect.makeDef(false)
                val add = Page4H61D3Effect.makeDef(true)
                this.pageEffects.add(page4H61D3Effect)
                this.pageEffects.add(add)
            }
        }

    }

    /*叠加规则*/
    var rule: Byte = rule_4_dieJia

    /*效果*/
    val pageEffects = mutableListOf<Page4H61D3Effect>()

    var curPage: Pair<Int, Page4H61D3Effect?>? = null

    fun queryEffect(pos: Int): Page4H61D3Effect? {
        if (pos < 0 || pos >= pageEffects.size) return null
        return pageEffects[pos]
    }

    fun deleteEffect(pos: Int): Boolean {
        if (pos < 0 || pos >= pageEffects.size) return false
        pageEffects.removeAt(pos)
        return true
    }

    override fun bytes(pageIndex: Int): MutableList<Byte> {
        return mutableListOf<Byte>().apply {
            queryEffect(pageIndex)?.let { page ->
                SafeLog.i(TAG) { "bytes() 预览单层" }
                add(1)
                add(rule)
                page.toBytes(pageIndex, null)?.let {
                    val lenValue = BleUtils.getSignedBytesFor2(it.size, true)
                    this.add(lenValue[0])
                    this.add(lenValue[1])
                    this.addAll(it)
                }
            } ?: run {
                SafeLog.i(TAG) { "bytes() 预览多层" }
                var pageSize = 0
                val effectsSnapshot = pageEffects.toList()
                val effectSnapshot = mutableListOf<Byte>()
                for ((index, effect) in effectsSnapshot.withIndex()) {
                    if (!effect.isAdd) {
                        pageSize++
                        effect.toBytes(index) {
                            SafeLog.i(TAG) { "bytes result = $it" }
                        }?.let {
                            val lenValue = BleUtils.getSignedBytesFor2(it.size, true)
                            effectSnapshot.add(lenValue[0])
                            effectSnapshot.add(lenValue[1])
                            effectSnapshot.addAll(it)
                        }
                    }
                }
                add(pageSize.toByte())
                add(rule)
                addAll(effectSnapshot)
            }
        }
    }

}


/**
 * 单层效果
 */
@Keep
class Page4H61D3Effect : MultiItemEntity {

    companion object {
        const val TAG = "Page4H61D3Effect"
        fun makeDef(isAdd: Boolean): Page4H61D3Effect {
            return Page4H61D3Effect().apply {
                this.isAdd = isAdd
            }
        }

        fun copy(page4H61D3Effect: Page4H61D3Effect): Page4H61D3Effect {
            page4H61D3Effect.toBytes(0) {}?.let {
                it.add(0, 0)
                it.add(0, 0)
                parse(it.toByteArray())?.let { newH61D3Effect ->
                    SafeLog.i(TAG) { "copy 成功" }
                    return newH61D3Effect
                }
            }
            SafeLog.i(TAG) { "copy 失败" }
            return Page4H61D3Effect()
        }

        fun parse(effectValue: ByteArray): Page4H61D3Effect? {
            SafeLog.i(TAG) { "parse 解析图层参数 , effectValue = ${JsonUtil.toJson(effectValue)}" }
            val speed = effectValue[2]
            val action = effectValue[3]

            val subEffectValue = ByteArray(effectValue.size - 4)
            val onePageLen = subEffectValue.size
            System.arraycopy(effectValue, 4, subEffectValue, 0, onePageLen)

            val page4H61D3Effect = Page4H61D3Effect()

            when (action) {
                Pro4H61D3Diy.sub_effect_4_liuDong -> {
                    val parse = SubEffect4LiuDong.parse(subEffectValue) ?: return null
                    page4H61D3Effect.subEffect4LiuDong = parse
                    SafeLog.i(TAG) { "parse subEffect4LiuDong = $parse" }
                }

                Pro4H61D3Diy.sub_effect_4_duiJi -> {
                    val parse = SubEffect4DuiJi.parse(subEffectValue) ?: return null
                    page4H61D3Effect.subEffect4DuiJi = parse
                    SafeLog.i(TAG) { "parse subEffect4DuiJi = $parse" }
                }

                Pro4H61D3Diy.sub_effect_4_suiJi -> {
                    val parse = SubEffect4SuiJi.parse(subEffectValue) ?: return null
                    page4H61D3Effect.subEffect4SuiJi = parse
                    SafeLog.i(TAG) { "parse subEffect4SuiJi = $parse" }
                }

                Pro4H61D3Diy.sub_effect_4_jihe -> {
                    val parse = SubEffect4JiHe.parse(subEffectValue) ?: return null
                    page4H61D3Effect.subEffect4JiHe = parse
                    SafeLog.i(TAG) { "parse sub_effect_4_jihe = $parse" }
                }

                Pro4H61D3Diy.sub_effect_4_xianxing -> {
                    val parse = SubEffect4XianXing.parse(subEffectValue) ?: return null
                    page4H61D3Effect.subEffect4XianXing = parse
                    SafeLog.i(TAG) { "parse subEffect4XianXing = $parse" }
                }

                else -> {
                    return null
                }
            }
            page4H61D3Effect.actionEffect = action//需要先赋值，不然设置模式出错
            page4H61D3Effect.speed = speed
            page4H61D3Effect.changeActionEffectMode()
            return page4H61D3Effect
        }
    }

    var isAdd = false

    /*速度*/
    var speed: Byte = 50

    /*子效果类型*/
    var actionEffect: Byte = Pro4H61D3Diy.sub_effect_4_liuDong

    /*流动*/
    var subEffect4LiuDong: SubEffect4LiuDong? = null

    /*堆积*/
    var subEffect4DuiJi: SubEffect4DuiJi? = null

    /*随机*/
    var subEffect4SuiJi: SubEffect4SuiJi? = null

    /*几何*/
    var subEffect4JiHe: SubEffect4JiHe? = null

    /*线性*/
    var subEffect4XianXing: SubEffect4XianXing? = null


    var deviceBaseInfo: DeviceBaseInfo? = null

    var iProMsg: IProMsg? = null

    init {
        actionEffect = Pro4H61D3Diy.sub_effect_4_liuDong
        changeActionEffectMode()
    }

    fun toBytes(pos: Int, msg: ((reason: String) -> Unit)?): MutableList<Byte>? {
        SafeLog.i(TAG) { "toBytes pos = $pos" }
        return mutableListOf<Byte>().apply {
            add(speed)
            add(actionEffect)
            when (actionEffect) {
                Pro4H61D3Diy.sub_effect_4_liuDong -> {
                    subEffect4LiuDong?.toBytes(pos, msg)?.let {
                        this.addAll(it)
                    } ?: return null
                }

                Pro4H61D3Diy.sub_effect_4_duiJi -> {
                    subEffect4DuiJi?.toBytes(pos, msg)?.let {
                        this.addAll(it)
                    } ?: return null
                }

                Pro4H61D3Diy.sub_effect_4_suiJi -> {
                    subEffect4SuiJi?.toBytes(pos, msg)?.let {
                        this.addAll(it)
                    } ?: return null
                }

                Pro4H61D3Diy.sub_effect_4_jihe -> {
                    subEffect4JiHe?.toBytes(pos, msg)?.let {
                        this.addAll(it)
                    } ?: return null
                }

                Pro4H61D3Diy.sub_effect_4_xianxing -> {
                    subEffect4XianXing?.toBytes(pos, msg)?.let {
                        this.addAll(it)
                    } ?: return null
                }

                else -> {
                    msg?.invoke("图层${pos + 1}的subEffect = $actionEffect is not support!")
                    return null
                }
            }

        }
    }


    fun changeActionEffectMode(): Boolean {
        val subEffect = actionEffect
        if (subEffect == Pro4H61D3Diy.sub_effect_4_liuDong) {
            if (subEffect4LiuDong == null) subEffect4LiuDong = SubEffect4LiuDong()
            iProMsg = subEffect4LiuDong
            return true
        }
        if (subEffect == Pro4H61D3Diy.sub_effect_4_suiJi) {
            if (subEffect4SuiJi == null) subEffect4SuiJi = SubEffect4SuiJi()
            iProMsg = subEffect4SuiJi
            return true
        }
        if (subEffect == Pro4H61D3Diy.sub_effect_4_duiJi) {
            if (subEffect4DuiJi == null) subEffect4DuiJi = SubEffect4DuiJi()
            iProMsg = subEffect4DuiJi
            return true
        }
        if (subEffect == Pro4H61D3Diy.sub_effect_4_jihe) {
            if (subEffect4JiHe == null) subEffect4JiHe = SubEffect4JiHe()
            iProMsg = subEffect4JiHe
            return true
        }
        if (subEffect == Pro4H61D3Diy.sub_effect_4_xianxing) {
            if (subEffect4XianXing == null) subEffect4XianXing = SubEffect4XianXing()
            iProMsg = subEffect4XianXing
            return true
        }

        SafeLog.e(TAG) { "changeSubEffect subEffect = ${BleUtils.byteToHexString(subEffect)} is not support!" }
        return false
    }

    fun getSubEffectModels(): MutableList<IntArray> {
        return iProMsg?.getSubEffectModel() ?: mutableListOf()
    }

    override val itemType: Int
        get() = if (isAdd) 1 else 0

    fun getSubEffect(): Byte {
        return iProMsg?.fetchSubEffect() ?: SubEffect4LiuDong.liuDong_sub_effect_dieJia
    }

    fun checkCondition(): Pair<MutableList<Int>, IntArray>? {
        return iProMsg?.checkCondition()
    }


    fun getColors(): MutableList<Int> {
        return mutableListOf<Int>().apply {
            iProMsg?.getCurColors()?.let {
                addAll(it)
            }
        }
    }

    fun changeColors(newColors: MutableList<Int>) {
        iProMsg?.changeColors(newColors)
    }

    fun changeDirection(direction: Byte) {
        iProMsg?.changeDirection(direction)
    }
}

/**
 * 流动
 */
@Keep
class SubEffect4LiuDong : IProMsg {
    companion object {
        const val TAG = "SubEffect4LiuDong"
        const val liuDong_sub_effect_dieJia: Byte = 0x00
        const val liuDong_sub_effect_fanFu: Byte = 0x01
        const val liuDong_sub_effect_gunDong: Byte = 0x02
        const val liuDong_sub_effect_yaoBai: Byte = 0x03

        const val direction_hor: Byte = 0x00
        const val direction_ver: Byte = 0x01

        const val start_point_first: Byte = 0x00
        const val start_point_end: Byte = 0xFF.toByte()
        const val start_point_edit: Byte = 0xFE.toByte()

        const val start_point: Byte = 0x00
        const val end_point: Byte = 0x01
        const val ju_long: Byte = 0x02
        const val kuo_san: Byte = 0x03
        const val xian_shunshizhen: Byte = 0x04
        const val xian_nishizhen: Byte = 0x05
        const val nishizhen: Byte = 0x06
        const val shunshizhen: Byte = 0x07
        const val mian_kuosan: Byte = 0x08
        const val mian_jvlong: Byte = 0x09

        const val direction_effect_not_support: Byte = 0xFF.toByte()

        const val color_type_zhengDuan: Byte = 0x00
        const val color_type_fenduan: Byte = 0x01

        const val brightness_gradual_type_piece: Byte = 0x00
        const val brightness_gradual_type_color_num: Byte = 0x01

        const val pro_brightness_change_style_changLiang: Int = 0
        const val pro_brightness_change_style_custom: Int = 1


        fun directionModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(direction_ver.toInt(), R.string.b2light_effect_direct_vertical))
                add(intArrayOf(direction_hor.toInt(), R.string.b2light_effect_direct_horizontal))
            }
        }

        fun musicDirectionModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(direction_hor.toInt(), R.string.b2light_effect_direct_vertical))
                add(intArrayOf(direction_ver.toInt(), R.string.b2light_effect_direct_horizontal))
            }
        }

        fun effectTypeModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(kuo_san.toInt(), R.string.b2light_diy_kuo_san))
                add(intArrayOf(ju_long.toInt(), R.string.b2light_diy_gathered))
            }
        }

        fun colorTypeModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(color_type_zhengDuan.toInt(), R.string.b2light_effect_all_block))
                add(intArrayOf(color_type_fenduan.toInt(), R.string.b2light_effect_part_block))
            }
        }

        fun brightnessGradualModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(brightness_gradual_type_piece.toInt(), R.string.b2light_scenes_steady_on))
                add(intArrayOf(brightness_gradual_type_color_num.toInt(), R.string.b2light_scenes_steady_on))
            }
        }

        val steadyLightModels by lazy {
            hashMapOf<Int, String>().apply {
                put(pro_brightness_change_style_changLiang, ResUtil.getString(R.string.b2light_scenes_steady_on))
                put(pro_brightness_change_style_custom, ResUtil.getString(R.string.b2light_diy_606a_custom))
            }
        }

        fun directionModelsFlow2(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_shang.toInt(),
                        R.drawable.component_diy_action_up
                    )
                )
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_xia.toInt(),
                        R.drawable.component_diy_action_down
                    )
                )
                add(intArrayOf(Pro4H61D3Diy.pro_direction_you.toInt(), R.drawable.component_diy_action_right))
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_zuo.toInt(),
                        R.drawable.component_diy_action_left
                    )
                )
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_kuosan_shangxia.toInt(),
                        R.drawable.component_btn_direct_kuosan_zhongxiang
                    )
                )
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_kuosan_zuoyou.toInt(),
                        R.drawable.component_btn_direct_kuosan_hengxaing
                    )
                )
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_jvlong_shangxia.toInt(),
                        R.drawable.component_btn_direct_jvlong_zhongxiang
                    )
                )
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_jvlong_zuoyou.toInt(),
                        R.drawable.component_btn_direct_jvlong_hengxaing
                    )
                )
            }
        }

        fun directionModelsFlow(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(Pro4H61D3Diy.pro_direction_you.toInt(), R.drawable.component_diy_action_right))
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_zuo.toInt(),
                        R.drawable.component_diy_action_left
                    )
                )
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_shang.toInt(),
                        R.drawable.component_diy_action_up
                    )
                )
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_xia.toInt(),
                        R.drawable.component_diy_action_down
                    )
                )
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_kuosan_shangxia.toInt(),
                        R.drawable.component_btn_direct_kuosan_zhongxiang
                    )
                )
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_kuosan_zuoyou.toInt(),
                        R.drawable.component_btn_direct_kuosan_hengxaing
                    )
                )
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_jvlong_shangxia.toInt(),
                        R.drawable.component_btn_direct_jvlong_zhongxiang
                    )
                )
                add(
                    intArrayOf(
                        Pro4H61D3Diy.pro_direction_jvlong_zuoyou.toInt(),
                        R.drawable.component_btn_direct_jvlong_hengxaing
                    )
                )
            }
        }

        fun directionMap(isNew: Boolean = false): MutableMap<Int, Int> {
            return mutableMapOf<Int, Int>().apply {
                put(
                    Pro4H61D3Diy.pro_direction_you.toInt(),
                    if (isNew) R.drawable.component_btn_direct_right_new else R.drawable.component_diy_action_right
                )
                put(
                    Pro4H61D3Diy.pro_direction_zuo.toInt(),
                    if (isNew) R.drawable.component_btn_direct_left_new else R.drawable.component_diy_action_left
                )
                put(
                    Pro4H61D3Diy.pro_direction_shang.toInt(),
                    if (isNew) R.drawable.component_diy_action_up_new else R.drawable.component_diy_action_up
                )
                put(
                    Pro4H61D3Diy.pro_direction_xia.toInt(),
                    if (isNew) R.drawable.component_diy_action_down_new else R.drawable.component_diy_action_down
                )
                put(
                    Pro4H61D3Diy.pro_direction_kuosan_shangxia.toInt(),
                    if (isNew) R.drawable.component_btn_direct_kuosan_zhongxiang_new
                    else R.drawable.component_btn_direct_kuosan_zhongxiang
                )
                put(
                    Pro4H61D3Diy.pro_direction_kuosan_zuoyou.toInt(),
                    if (isNew) R.drawable.component_btn_direct_kuosan_hengxaing_new else
                        R.drawable.component_btn_direct_kuosan_hengxaing
                )
                put(
                    Pro4H61D3Diy.pro_direction_jvlong_shangxia.toInt(),
                    if (isNew) R.drawable.component_btn_direct_jvlong_zhongxiang_new else
                        R.drawable.component_btn_direct_jvlong_zhongxiang
                )
                put(
                    Pro4H61D3Diy.pro_direction_jvlong_zuoyou.toInt(),
                    if (isNew) R.drawable.component_btn_direct_jvlong_hengxaing_new else
                        R.drawable.component_btn_direct_jvlong_hengxaing
                )
                put(
                    Pro4H61D3Diy.pro_direction_shunshizhen.toInt(),
                    if (isNew) R.drawable.component_btn_direct_shun_new else
                        R.drawable.component_btn_direct_shun
                )
                put(
                    Pro4H61D3Diy.pro_direction_nishizhen.toInt(),
                    if (isNew) R.drawable.component_btn_direct_ni_new else
                        R.drawable.component_btn_direct_ni
                )
                put(
                    Pro4H61D3Diy.pro_direction_jvlong.toInt(),
                    if (isNew) R.drawable.component_diy_direction13_new else
                        R.drawable.component_diy_direction13
                )
                put(
                    Pro4H61D3Diy.pro_direction_kuosan.toInt(),
                    if (isNew) R.drawable.component_diy_direction14_new else
                        R.drawable.component_diy_direction14
                )
                put(
                    Pro4H61D3Diy.pro_direction_xian_shunshizhen.toInt(),
                    if (isNew) R.drawable.component_btn_direct_shun_new else
                        R.drawable.component_btn_direct_shun
                )
                put(
                    Pro4H61D3Diy.pro_direction_xian_nishizhen.toInt(),
                    if (isNew) R.drawable.component_btn_direct_ni_new else
                        R.drawable.component_btn_direct_ni
                )
            }
        }

        /**
         * 需要根据方向、起点、扩散/聚拢参数形成对应的效果
         */
        fun getDirectionSpecificParams(direction: Byte, effectType: Byte): Byte {
            return if (direction == direction_ver && effectType == end_point) {
                Pro4H61D3Diy.pro_direction_shang
            } else if (direction == direction_ver && effectType == start_point) {
                Pro4H61D3Diy.pro_direction_xia
            } else if (direction == direction_hor && effectType == end_point) {
                Pro4H61D3Diy.pro_direction_zuo
            } else if (direction == direction_hor && effectType == start_point) {
                Pro4H61D3Diy.pro_direction_you
            } else if (direction == direction_ver && effectType == kuo_san) {
                Pro4H61D3Diy.pro_direction_kuosan_shangxia
            } else if (direction == direction_hor && effectType == kuo_san) {
                Pro4H61D3Diy.pro_direction_kuosan_zuoyou
            } else if (direction == direction_ver && effectType == ju_long) {
                Pro4H61D3Diy.pro_direction_jvlong_shangxia
            } else if (direction == direction_hor && effectType == ju_long) {
                Pro4H61D3Diy.pro_direction_jvlong_zuoyou
            } else if (direction == direction_effect_not_support || effectType == direction_effect_not_support) {
                direction_effect_not_support
            } else if (direction == direction_hor && effectType == xian_shunshizhen) {
                Pro4H61D3Diy.pro_direction_xian_shunshizhen
            } else if (direction == direction_hor && effectType == xian_nishizhen) {
                Pro4H61D3Diy.pro_direction_xian_nishizhen
            } else if (direction == direction_hor && effectType == shunshizhen) {
                Pro4H61D3Diy.pro_direction_shunshizhen
            } else if (direction == direction_hor && effectType == nishizhen) {
                Pro4H61D3Diy.pro_direction_nishizhen
            } else if (direction == direction_hor && effectType == mian_jvlong) {
                Pro4H61D3Diy.pro_direction_jvlong
            } else if (direction == direction_hor && effectType == mian_kuosan) {
                Pro4H61D3Diy.pro_direction_kuosan
            } else {
                Pro4H61D3Diy.pro_direction_shang
            }
        }

        fun changeDirection(subEffect: Byte): Pair<Byte, Byte> {
            // 传入的是方向，转换成对应的协议参数
            when (subEffect) {
                Pro4H61D3Diy.pro_direction_shang -> {
                    // 解析不同的参数
                    return Pair(direction_ver, end_point)
                }

                Pro4H61D3Diy.pro_direction_xia -> {
                    return Pair(direction_ver, start_point)
                }

                Pro4H61D3Diy.pro_direction_zuo -> {
                    return Pair(direction_hor, end_point)
                }

                Pro4H61D3Diy.pro_direction_you -> {
                    return Pair(direction_hor, start_point)
                }

                Pro4H61D3Diy.pro_direction_kuosan_shangxia -> {
                    return Pair(direction_ver, kuo_san)
                }

                Pro4H61D3Diy.pro_direction_kuosan_zuoyou -> {
                    return Pair(direction_hor, kuo_san)
                }

                Pro4H61D3Diy.pro_direction_jvlong_shangxia -> {
                    return Pair(direction_ver, ju_long)
                }

                Pro4H61D3Diy.pro_direction_jvlong_zuoyou -> {
                    return Pair(direction_hor, ju_long)
                }

                Pro4H61D3Diy.pro_direction_shunshizhen -> {
                    return Pair(direction_hor, shunshizhen)
                }

                Pro4H61D3Diy.pro_direction_nishizhen -> {
                    return Pair(direction_hor, nishizhen)
                }

                Pro4H61D3Diy.pro_direction_jvlong -> {
                    return Pair(direction_hor, mian_jvlong)
                }

                Pro4H61D3Diy.pro_direction_kuosan -> {
                    return Pair(direction_hor, mian_kuosan)
                }

                Pro4H61D3Diy.pro_direction_xian_shunshizhen -> {
                    return Pair(direction_hor, xian_shunshizhen)
                }

                Pro4H61D3Diy.pro_direction_xian_nishizhen -> {
                    return Pair(direction_hor, xian_nishizhen)
                }

                else -> {
                    SafeLog.i(TAG) { "changeSubEffect 子效果切换异常 subEffect = $subEffect" }
                    return Pair(direction_effect_not_support, direction_effect_not_support)
                }
            }
        }

        fun parse(effectValue: ByteArray): SubEffect4LiuDong? {
            return try {
                val subEffect4LiuDong = SubEffect4LiuDong()
                var index = 0
                val subEffect = effectValue[index++]
                subEffect4LiuDong.pieceNum = effectValue[index++].toInt()
                subEffect4LiuDong.direction = effectValue[index++]
                subEffect4LiuDong.effectType = effectValue[index++]
                subEffect4LiuDong.colorType = effectValue[index++]
                subEffect4LiuDong.colorGradual = effectValue[index++].toInt() == 0x01
                subEffect4LiuDong.entryAndExitEnable = effectValue[index++].toInt() == 0x01

                when (subEffect) {
                    liuDong_sub_effect_dieJia, liuDong_sub_effect_fanFu -> {
                        val colorLen = BleUtil.getUnsignedByte(effectValue[index++])
                        SafeLog.i(TAG) { "parse() colorLen = $colorLen" }

                        for (i in 0 until colorLen) {
                            val rgbBytes = ByteArray(3)
                            System.arraycopy(effectValue, index, rgbBytes, 0, rgbBytes.size)
                            subEffect4LiuDong.colors?.add(ColorUtils.toColor(rgbBytes))
                            index += 3
                        }
                        SafeLog.i(TAG) { "parse index = $index" }
                    }

                    liuDong_sub_effect_gunDong, liuDong_sub_effect_yaoBai -> {
                        val low = effectValue[index++].toInt()
                        val high = effectValue[index++].toInt()
                        subEffect4LiuDong.brightnessLowChange(low)
                        subEffect4LiuDong.brightnessHighChange(high)
                        if (low == high) {
                            subEffect4LiuDong.range4Brightness[0] = pro_brightness_change_style_changLiang
                            subEffect4LiuDong.brightnessLowChange(0)
                            SafeLog.i(TAG) { "parse brightnessLowChange 亮度统一，把最低亮度改为默认值 0" }
                        } else {
                            subEffect4LiuDong.range4Brightness[0] = pro_brightness_change_style_custom
                        }
                        SafeLog.i(TAG) { "parse low = $low , high = $high" }
                        subEffect4LiuDong.brightnessGradualType = effectValue[index++]

                        val colorLen = BleUtil.getUnsignedByte(effectValue[index++])
                        SafeLog.i(TAG) { "parse() colorLen = $colorLen" }

                        for (i in 0 until colorLen) {
                            val rgbBytes = ByteArray(3)
                            System.arraycopy(effectValue, index, rgbBytes, 0, rgbBytes.size)
                            subEffect4LiuDong.colors?.add(ColorUtils.toColor(rgbBytes))
                            index += 3
                        }
                        SafeLog.i(TAG) { "parse index = $index" }
                    }
                }
                SafeLog.i(TAG) { "parse subEffect = $subEffect" }
                subEffect4LiuDong.subEffect = subEffect
                return subEffect4LiuDong
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    override fun changeDirection(direction: Byte) {
        val dirEffectPair = SubEffect4LiuDong.changeDirection(direction)
        if (dirEffectPair.first != SubEffect4LiuDong.direction_effect_not_support
            && dirEffectPair.second != SubEffect4LiuDong.direction_effect_not_support
        ) {
            this.direction = dirEffectPair.first
            this.effectType = dirEffectPair.second
        }
    }

    private val subEffectModels by lazy {
        mutableListOf<IntArray>().apply {
            add(intArrayOf(liuDong_sub_effect_dieJia.toInt(), R.string.b2light_effect_superimpose))
            add(intArrayOf(liuDong_sub_effect_fanFu.toInt(), R.string.b2light_effect_repeatedly))
            add(intArrayOf(liuDong_sub_effect_gunDong.toInt(), R.string.b2light_scenes_in_waves))
            add(intArrayOf(liuDong_sub_effect_yaoBai.toInt(), R.string.b2light_scenes_swing))
        }
    }
    val bgColorRangeArray = intArrayOf(0, 0)

    /*颜色长度范围*/
    val colorRangeArray = intArrayOf(2, 8)

    /*子效果*/
    var subEffect: Byte = liuDong_sub_effect_dieJia

    /*块数*/
    var pieceNum: Int = 20

    /*均分块数范围*/
    val blockNumRang = intArrayOf(1, 20)

    /*方向*/
    var direction: Byte = direction_hor

    /*效果方式-扩散或聚拢*/
    var effectType: Byte = start_point

    /*颜色分布-整段或分段*/
    var colorType: Byte = color_type_zhengDuan

    /*颜色是否渐变*/
    var colorGradual: Boolean = false

    /*是否支持出入场效果*/
    var entryAndExitEnable: Boolean = false

    /*颜色集合*/
    var colors: MutableList<Int>? = mutableListOf()

    /*亮度范围*/
    val range4Brightness = intArrayOf(pro_brightness_change_style_custom, 1, 100)
    val defRangeBrightness = intArrayOf(1, 100)

    /*亮度渐变方式*/
    var brightnessGradualType: Byte = brightness_gradual_type_piece

    fun isJuLong(): Boolean {
        return effectType == ju_long
    }

    override fun toBytes(pos: Int, msg: ((reason: String) -> Unit)?): MutableList<Byte>? {
        return mutableListOf<Byte>().apply {
            add(subEffect)
            add(pieceNum.toByte())
            add(direction)// direction 参数与 effectType 组合起来形成 UI 上展示的方向(上下左右、聚拢、扩散)
            add(effectType)
            add(colorType)
            add(if (colorGradual) 0x01 else 0x00)
            add(if (entryAndExitEnable) 0x01 else 0x00)

            when (subEffect) {
                liuDong_sub_effect_dieJia, liuDong_sub_effect_fanFu -> {
                    val size4Colors = colors?.size ?: 0
                    if (size4Colors <= 0) {
                        msg?.invoke("图层${pos + 1},请添加颜色!")
                        return null
                    }
                    add(size4Colors.toByte())
                    colors?.let {
                        for (color in it) {
                            val rgbBytes = ColorUtils.getRgbBytes(color)
                            for (rgbByte in rgbBytes) {
                                add(rgbByte)
                            }
                        }
                    }
                }

                liuDong_sub_effect_yaoBai, liuDong_sub_effect_gunDong -> {
                    if (range4Brightness[1] > range4Brightness[2]) {
                        msg?.invoke("图层${pos + 1},最低亮度比最高亮度大！")
                        return null
                    }
                    val type = range4Brightness[0]
                    if (type == pro_brightness_change_style_changLiang) {
                        add((100).toByte())
                        add((100).toByte())
                    } else {
                        add(range4Brightness[1].toByte())
                        add(range4Brightness[2].toByte())
                    }

                    add(brightnessGradualType)
                    val size4Colors = colors?.size ?: 0
                    if (size4Colors <= 0) {
                        msg?.invoke("图层${pos + 1},请添加颜色!")
                        return null
                    }
                    add(size4Colors.toByte())
                    colors?.let {
                        for (color in it) {
                            val rgbBytes = ColorUtils.getRgbBytes(color)
                            for (rgbByte in rgbBytes) {
                                add(rgbByte)
                            }
                        }
                    }
                }
            }
        }
    }

    override fun getSubEffectModel(): MutableList<IntArray> {
        return subEffectModels
    }

    override fun fetchSubEffect(): Byte {
        return subEffect
    }

    override fun getColorModels(): MutableList<DiyColorEditViewH606A.ColorModel> {
        return mutableListOf<DiyColorEditViewH606A.ColorModel>().apply {
            SafeLog.i(TAG) { "getColorModels colors size = ${colors?.size}" }
            colors?.onEach {
                val colorModel = DiyColorEditViewH606A.ColorModel.makeShowing(it)
                add(colorModel)
            }
        }
    }

    override fun setSubEffectValue(subEffect: Byte) {
        this.subEffect = subEffect
    }

    override fun checkCondition(): Pair<MutableList<Int>, IntArray> {
        return Pair(this.colors ?: mutableListOf(), colorRangeArray)
    }

    override fun getCurColors(): MutableList<Int> {
        return this.colors ?: mutableListOf()
    }

    override fun changeColors(newColors: MutableList<Int>) {
        if (newColors.isNotEmpty()) {
            this.colors?.clear()
            newColors.forEach {
                this.colors?.add(it)
            }
        }
    }

    fun colorChanged(rgbSetBytes: MutableList<ByteArray>) {
        colors?.clear()
        for (rgbSetByte in rgbSetBytes) {
            colors?.add(ColorUtils.toColor(rgbSetByte))
        }
    }

    fun getColorDistributedStr(): String {
        return ResUtil.getString(
            if (colorType == color_type_zhengDuan) {
                R.string.video_effect_all
            } else {
                R.string.video_effect_part
            }
        )
    }

    fun isSegmentationType(): Boolean {
        return colorType == color_type_fenduan
    }

    fun showBrightnessGradualType(): Boolean {
        return subEffect == liuDong_sub_effect_gunDong || subEffect == liuDong_sub_effect_yaoBai
    }

    fun isSteadyLight(): Boolean {
        val style = range4Brightness[0]
        return style == pro_brightness_change_style_custom
    }

    fun getSteadyLightStr(): String {
        val style = range4Brightness[0]
        return steadyLightModels[style] ?: ""
    }

    fun changeBrightnessType() {
        val type = range4Brightness[0]
        if (type == 0) {
            range4Brightness[0] = 1
        } else {
            range4Brightness[0] = 0
        }
    }

    fun brightnessLowChange(value: Int) {
        range4Brightness[1] = value
    }

    fun brightnessHighChange(value: Int) {
        range4Brightness[2] = value
    }

}

/**
 * 随机
 */
@Keep
class SubEffect4SuiJi : IProMsg {
    companion object {

        const val TAG = "SubEffect4SuiJi"

        private const val BG_COLOR_INVALID = 1000000000

        const val sub_effect_dieJia: Byte = 0x00
        const val sub_effect_pingPu: Byte = 0x01
        const val sub_effect_yueDong: Byte = 0x02
        const val sub_effect_zhanFang: Byte = 0x03
        const val sub_effect_caiHui: Byte = 0x04

        const val direction_you2Zuo: Byte = 0x00/*左*/
        const val direction_zuo2You: Byte = 0x01/*右*/
        const val direction_shang2Xia: Byte = 0x02/*上*/
        const val direction_xia2Shang: Byte = 0x03/*下*/
        private const val direction_juLong: Byte = 0x04
        private const val direction_kuoSan: Byte = 0x05

        const val effect_type_xianXing: Byte = 0x00
        const val effect_type_mianXing: Byte = 0x01
        fun directionModelsPingPu(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(direction_shang2Xia.toInt(), R.drawable.component_diy_action_up))
                add(intArrayOf(direction_xia2Shang.toInt(), R.drawable.component_diy_action_down))
                add(intArrayOf(direction_you2Zuo.toInt(), R.drawable.component_diy_action_left))
                add(intArrayOf(direction_zuo2You.toInt(), R.drawable.component_diy_action_right))
                add(intArrayOf(direction_juLong.toInt(), R.drawable.component_diy_direction13))
                add(intArrayOf(direction_kuoSan.toInt(), R.drawable.component_diy_direction14))
            }
        }

        fun suiJiEffectTypeModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(effect_type_xianXing.toInt(), R.string.b2light_diy_xianxin))
                add(intArrayOf(effect_type_mianXing.toInt(), R.string.b2light_diy_mianxing))
            }
        }

        fun parse(effectValue: ByteArray): SubEffect4SuiJi? {
            return try {
                SafeLog.i(TAG) { "parse " }
                var index = 0
                val subEffect4SuiJi = SubEffect4SuiJi()
                subEffect4SuiJi.subEffect = effectValue[index++]

                when (subEffect4SuiJi.subEffect) {
                    sub_effect_dieJia -> {
                        val dieJiaType = effectValue[index++]
                        val pieceNum = effectValue[index++].toInt()
                        subEffect4SuiJi.dieJiaType = dieJiaType
                        subEffect4SuiJi.pieceNum = pieceNum
                        if (dieJiaType == SubEffect4DuiJi.effect_type_xianXing) {
                            subEffect4SuiJi.dieJia4SuiJiRange[2] = effectValue[index++]
                            subEffect4SuiJi.dieJia4SuiJiRange[3] = effectValue[index++]
                        } else {
                            subEffect4SuiJi.dieJia4SuiJiRange[6] = effectValue[index++]
                            subEffect4SuiJi.dieJia4SuiJiRange[7] = effectValue[index++]
                        }
                        subEffect4SuiJi.dieJia4SuiJiRange[8] = pieceNum.toByte()

                        val colorLen = BleUtil.getUnsignedByte(effectValue[index++])
                        SafeLog.i(SubEffect4LiuDong.TAG) { "parse() colorLen = $colorLen" }
                        for (i in 0 until colorLen) {
                            val rgbBytes = ByteArray(3)
                            System.arraycopy(effectValue, index, rgbBytes, 0, rgbBytes.size)
                            subEffect4SuiJi.colors.add(ColorUtils.toColor(rgbBytes))
                            index += 3
                        }
                    }

                    sub_effect_pingPu -> {
                        val pieceNum = effectValue[index++].toInt()
                        val colorPartSwitch = effectValue[index++].toInt()
                        val colorGradual = effectValue[index++].toInt()
                        subEffect4SuiJi.pieceNum = pieceNum
                        subEffect4SuiJi.colorPartSwitch = colorPartSwitch == 1
                        subEffect4SuiJi.colorGradual = colorGradual == 1
                        val colorLen = BleUtil.getUnsignedByte(effectValue[index++])
                        SafeLog.i(SubEffect4LiuDong.TAG) { "parse() colorLen = $colorLen" }
                        for (i in 0 until colorLen) {
                            val rgbBytes = ByteArray(3)
                            System.arraycopy(effectValue, index, rgbBytes, 0, rgbBytes.size)
                            subEffect4SuiJi.colors.add(ColorUtils.toColor(rgbBytes))
                            index += 3
                        }
                    }

                    sub_effect_yueDong -> {
                        val yueDongType = effectValue[index++]
                        val pieceNum = effectValue[index++].toInt()
                        subEffect4SuiJi.yueDongType = yueDongType
                        subEffect4SuiJi.pieceNum = pieceNum

                        if (yueDongType == effect_type_xianXing) {
                            subEffect4SuiJi.yueDong4ShanSuoRange[2] = effectValue[index++]
                            subEffect4SuiJi.yueDong4ShanSuoRange[3] = effectValue[index++]
                        } else {
                            subEffect4SuiJi.yueDong4ShanSuoRange[6] = effectValue[index++]
                            subEffect4SuiJi.yueDong4ShanSuoRange[7] = effectValue[index++]
                        }
                        subEffect4SuiJi.yueDong4ShanSuoRange[8] = pieceNum.toByte()

                        val bgColorR = effectValue[index++]
                        val bgColorG = effectValue[index++]
                        val bgColorB = effectValue[index++]

                        subEffect4SuiJi.bgColor = ColorUtils.toColor(bgColorR, bgColorG, bgColorB)

                        val colorLen = BleUtil.getUnsignedByte(effectValue[index++])
                        SafeLog.i(SubEffect4LiuDong.TAG) { "parse() colorLen = $colorLen" }
                        for (i in 0 until colorLen) {
                            val rgbBytes = ByteArray(3)
                            System.arraycopy(effectValue, index, rgbBytes, 0, rgbBytes.size)
                            subEffect4SuiJi.colors.add(ColorUtils.toColor(rgbBytes))
                            index += 3
                        }
                    }

                    sub_effect_zhanFang -> {

                        val bgColorR = effectValue[index++]
                        val bgColorG = effectValue[index++]
                        val bgColorB = effectValue[index++]

                        subEffect4SuiJi.bgColor = ColorUtils.toColor(bgColorR, bgColorG, bgColorB)

                        val colorLen = BleUtil.getUnsignedByte(effectValue[index++])
                        SafeLog.i(SubEffect4LiuDong.TAG) { "parse() colorLen = $colorLen" }
                        for (i in 0 until colorLen) {
                            val rgbBytes = ByteArray(3)
                            System.arraycopy(effectValue, index, rgbBytes, 0, rgbBytes.size)
                            subEffect4SuiJi.colors.add(ColorUtils.toColor(rgbBytes))
                            index += 3
                        }
                    }

                    sub_effect_caiHui -> {
                        subEffect4SuiJi.caiHuiType = effectValue[index++]
                        subEffect4SuiJi.pieceNum = effectValue[index++].toInt()
                        val colorGradual = effectValue[index++].toInt()
                        subEffect4SuiJi.colorGradual = colorGradual == 1
                        val colorLen = BleUtil.getUnsignedByte(effectValue[index++])

                        SafeLog.i(SubEffect4LiuDong.TAG) { "parse() colorLen = $colorLen" }
                        for (i in 0 until colorLen) {
                            val rgbBytes = ByteArray(3)
                            System.arraycopy(effectValue, index, rgbBytes, 0, rgbBytes.size)
                            subEffect4SuiJi.colors.add(ColorUtils.toColor(rgbBytes))
                            index += 3
                        }
                    }

                    else -> {
                        return null
                    }
                }
                return subEffect4SuiJi
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    private val subEffectModels by lazy {
        mutableListOf<IntArray>().apply {
            add(intArrayOf(sub_effect_dieJia.toInt(), R.string.b2light_diy_sub_effect_tianchong))
            add(intArrayOf(sub_effect_pingPu.toInt(), R.string.b2light_diy_cover))
            add(intArrayOf(sub_effect_yueDong.toInt(), R.string.b2light_scenes_blinking))
            add(intArrayOf(sub_effect_zhanFang.toInt(), R.string.diy_name_bloom))
            add(intArrayOf(sub_effect_caiHui.toInt(), R.string.b2light_scenes_meteor))
        }
    }

    /*二级效果*/
    var subEffect: Byte = sub_effect_dieJia

    /*块数量*/
    var pieceNum: Int = 20

    /*叠加-叠加方式*/
    private var dieJiaType: Byte = SubEffect4DuiJi.effect_type_xianXing

    /*叠加-随机范围*/
    var dieJia4SuiJiRange = ByteArray(9).apply {
        this[0] = 1 /*线性：ic 范围 最小值*/
        this[1] = 25 /*线性：ic 范围 最大值，根据 sku 来赋值*/
        this[2] = 1 /*线性：闪烁ic范围具体参数，最小值*/
        this[3] = 3 /*线性：闪烁ic范围具体参数，最大值*/
        this[4] = 1 /*面性：块 范围 最小值，由产品确定*/
        this[5] = 20 /*面性：块 范围 最大值，由灯光提供*/
        this[6] = 2 /*面性：块范围具体参数，最小值*/
        this[7] = 5 /*面性：块范围具体参数，最大值*/
        this[8] = 20 /*面性：块的值*/
    }

    /*平铺-方向*/
    var direction: Byte = direction_zuo2You

    /*平铺-颜色-分段*/
    var colorPartSwitch: Boolean = false

    /*平铺-颜色是否渐变*/
    var colorGradual: Boolean = false

    /*悦动-方式*/
    private var yueDongType: Byte = effect_type_xianXing

    /*悦动-底色*/
    var bgColor: Int = ResUtil.getColor(R.color.blue)

    /*悦动-闪烁范围*/
    var yueDong4ShanSuoRange = ByteArray(9).apply {
        this[0] = 1 /*线性：ic 范围 最小值*/
        this[1] = 25 /*线性：ic 范围 最大值，根据 sku 来赋值*/
        this[2] = 1 /*线性：闪烁ic范围具体参数，最小值*/
        this[3] = 3 /*线性：闪烁ic范围具体参数，最大值*/
        this[4] = 1 /*面性：块 范围 最小值，由产品确定*/
        this[5] = 20 /*面性：块 范围 最大值，由灯光提供*/
        this[6] = 2 /*面性：块范围具体参数，最小值*/
        this[7] = 5 /*面性：块范围具体参数，最大值*/
        this[8] = 20 /*面性：块的值*/
    }

    /*彩绘-方式*/
    private var caiHuiType: Byte = effect_type_xianXing

    /*颜色*/
    val colors = mutableListOf<Int>()

    val bgColorRangeArray: IntArray = intArrayOf(1, 1)
    private val effectColorRangeArray: IntArray = intArrayOf(2, 8)
    private val caiHuiEffectColorRangeArray: IntArray = intArrayOf(1, 8)

    fun isBgColorInvalid(): Boolean {
        return bgColor == BG_COLOR_INVALID
    }

    fun getEffectColorRangeArray(): IntArray {
        return if (subEffect == sub_effect_caiHui) {
            caiHuiEffectColorRangeArray
        } else {
            effectColorRangeArray
        }
    }

    fun updateType(protocolType: Byte) {
        when (subEffect) {
            sub_effect_dieJia -> {
                dieJiaType = protocolType
            }

            sub_effect_yueDong -> {
                yueDongType = protocolType
            }

            sub_effect_caiHui -> {
                caiHuiType = protocolType
            }
        }
    }

    fun getType(): Byte? {
        return when (subEffect) {
            sub_effect_dieJia -> {
                dieJiaType
            }

            sub_effect_yueDong -> {
                yueDongType
            }

            sub_effect_caiHui -> {
                caiHuiType
            }

            else -> {
                null
            }
        }
    }

    fun updateTypeRangValue(min: Byte, max: Byte) {
        when (subEffect) {
            sub_effect_dieJia -> {
                if (dieJiaType == effect_type_xianXing) {
                    dieJia4SuiJiRange[2] = min
                    dieJia4SuiJiRange[3] = max
                } else {
                    dieJia4SuiJiRange[6] = min
                    dieJia4SuiJiRange[7] = max
                }
            }

            sub_effect_yueDong -> {
                if (yueDongType == effect_type_xianXing) {
                    yueDong4ShanSuoRange[2] = min
                    yueDong4ShanSuoRange[3] = max
                } else {
                    yueDong4ShanSuoRange[6] = min
                    yueDong4ShanSuoRange[7] = max
                }
            }
        }
    }

    fun getTypeRangValue(): IntArray? {
        return when (subEffect) {
            sub_effect_dieJia -> {
                return if (dieJiaType == effect_type_xianXing) {
                    intArrayOf(dieJia4SuiJiRange[2].toInt(), dieJia4SuiJiRange[3].toInt())
                } else {
                    intArrayOf(dieJia4SuiJiRange[6].toInt(), dieJia4SuiJiRange[7].toInt())
                }
            }

            sub_effect_yueDong -> {
                if (yueDongType == effect_type_xianXing) {
                    intArrayOf(yueDong4ShanSuoRange[2].toInt(), yueDong4ShanSuoRange[3].toInt())
                } else {
                    intArrayOf(yueDong4ShanSuoRange[6].toInt(), yueDong4ShanSuoRange[7].toInt())
                }
            }

            else -> {
                return null
            }
        }
    }

    /**
     * 获取当前范围拖动条的允许范围值
     */
    fun getTypeAllowableRangValue(): IntArray? {
        return when (subEffect) {
            sub_effect_dieJia -> {
                return if (dieJiaType == effect_type_xianXing) {
                    intArrayOf(dieJia4SuiJiRange[0].toInt(), dieJia4SuiJiRange[1].toInt())
                } else {
                    intArrayOf(dieJia4SuiJiRange[4].toInt(), dieJia4SuiJiRange[5].toInt())
                }
            }

            sub_effect_yueDong -> {
                if (yueDongType == effect_type_xianXing) {
                    intArrayOf(yueDong4ShanSuoRange[0].toInt(), yueDong4ShanSuoRange[1].toInt())
                } else {
                    intArrayOf(yueDong4ShanSuoRange[4].toInt(), yueDong4ShanSuoRange[5].toInt())
                }
            }

            else -> {
                return null
            }
        }
    }

    fun getTypeRangTitle(): String? {
        return when (subEffect) {
            sub_effect_dieJia -> {
                return if (dieJiaType == effect_type_xianXing) {
                    ResUtil.getString(R.string.b2light_diy_effect_flash_length_range)
                } else {
                    ResUtil.getString(R.string.b2light_diy_effect_block_number_range)
                }
            }

            sub_effect_yueDong -> {
                if (yueDongType == effect_type_xianXing) {
                    ResUtil.getString(R.string.b2light_diy_effect_flash_length_range)
                } else {
                    ResUtil.getString(R.string.b2light_diy_effect_block_number_range)
                }
            }

            else -> {
                return null
            }
        }
    }

    fun getTypeRangIconBitMap(): Bitmap? {
        return when (subEffect) {
            sub_effect_dieJia -> {
                return if (dieJiaType == effect_type_xianXing) {
                    ResUtil.getBitmap(R.mipmap.h61d3_diy_icon_shanshuo_changdufanwei)
                } else {
                    ResUtil.getBitmap(R.mipmap.h61d3_diy_icon_shanshuo_kuaishufanwei)
                }
            }

            sub_effect_yueDong -> {
                if (yueDongType == effect_type_xianXing) {
                    ResUtil.getBitmap(R.mipmap.h61d3_diy_icon_shanshuo_changdufanwei)
                } else {
                    ResUtil.getBitmap(R.mipmap.h61d3_diy_icon_shanshuo_kuaishufanwei)
                }
            }

            else -> {
                return null
            }
        }
    }

    fun showDirectionView(): Boolean {
        return false
    }

    fun showDiyTypeStyleChangeView(): Boolean {
        return subEffect == sub_effect_dieJia
            || subEffect == sub_effect_caiHui
            || subEffect == sub_effect_yueDong
    }

    fun showBalanceBlockNumberView(): Boolean {
        return (subEffect == sub_effect_dieJia && dieJiaType == SubEffect4DuiJi.effect_type_mianXing) ||
            (subEffect == sub_effect_yueDong && yueDongType == SubEffect4DuiJi.effect_type_mianXing) ||
            subEffect == sub_effect_pingPu
    }

    fun showRangView(): Boolean {
        return subEffect == sub_effect_dieJia || subEffect == sub_effect_yueDong
    }

    fun showBgColorView(): Boolean {
        return subEffect == sub_effect_yueDong || subEffect == sub_effect_zhanFang
    }

    fun showColorEffectPartView(): Boolean {
        return subEffect == sub_effect_pingPu
    }

    fun showColorEffectGradientView(): Boolean {
        return subEffect == sub_effect_pingPu || subEffect == sub_effect_caiHui
    }

    /**
     * 更新块数
     */
    fun updatePieceNum(pieceNum: Int) {
        when (subEffect) {
            sub_effect_dieJia -> {
                dieJia4SuiJiRange[8] = pieceNum.toByte()
            }

            sub_effect_yueDong -> {
                yueDong4ShanSuoRange[8] = pieceNum.toByte()
            }

            else -> {
                this.pieceNum = pieceNum
            }
        }
    }

    fun getCurPieceNum(): Int {
        return when (subEffect) {
            sub_effect_dieJia -> {
                dieJia4SuiJiRange[8].toInt()
            }

            sub_effect_yueDong -> {
                yueDong4ShanSuoRange[8].toInt()
            }

            else -> {
                this.pieceNum
            }
        }
    }

    fun isMianXingType(): Boolean {
        return getType() == effect_type_mianXing
    }


    override fun toBytes(pos: Int, msg: ((reason: String) -> Unit)?): MutableList<Byte>? {
        return mutableListOf<Byte>().apply {
            add(subEffect)
            when (subEffect) {
                sub_effect_dieJia -> {
                    add(dieJiaType)
                    if (dieJiaType == SubEffect4DuiJi.effect_type_xianXing) {
                        add(0x00)/*线性：块数默认参数*/
                        add(dieJia4SuiJiRange[2])
                        add(dieJia4SuiJiRange[3])
                    } else {
                        add(dieJia4SuiJiRange[8])
                        add(dieJia4SuiJiRange[6])
                        add(dieJia4SuiJiRange[7])
                    }
                    val size4Color = colors.size
                    add(size4Color.toByte())
                    for (color in colors) {
                        val rgbBytes = ColorUtils.getRgbBytes(color)
                        for (rgbByte in rgbBytes) {
                            add(rgbByte)
                        }
                    }
                }

                sub_effect_pingPu -> {
                    add(pieceNum.toByte())
                    add(if (colorPartSwitch) 0x01 else 0x00)
                    add(if (colorGradual) 0x01 else 0x00)
                    val size4Color = colors.size
                    add(size4Color.toByte())
                    for (color in colors) {
                        val rgbBytes = ColorUtils.getRgbBytes(color)
                        for (rgbByte in rgbBytes) {
                            add(rgbByte)
                        }
                    }
                }

                sub_effect_yueDong -> {
                    add(yueDongType)
                    if (yueDongType == effect_type_xianXing) {
                        add(0x00)/*线性：块数默认参数*/
                        add(yueDong4ShanSuoRange[2])
                        add(yueDong4ShanSuoRange[3])
                    } else {
                        add(yueDong4ShanSuoRange[8])
                        add(yueDong4ShanSuoRange[6])
                        add(yueDong4ShanSuoRange[7])
                    }
                    val rgbBytes4Bg = ColorUtils.getRgbBytes(bgColor)
                    for (byte in rgbBytes4Bg) {
                        add(byte)
                    }
                    val size4Color = colors.size
                    add(size4Color.toByte())
                    for (color in colors) {
                        val rgbBytes = ColorUtils.getRgbBytes(color)
                        for (rgbByte in rgbBytes) {
                            add(rgbByte)
                        }
                    }
                }

                sub_effect_zhanFang -> {
                    val rgbBytes4Bg = ColorUtils.getRgbBytes(bgColor)
                    for (byte in rgbBytes4Bg) {
                        add(byte)
                    }
                    val size4Color = colors.size
                    add(size4Color.toByte())
                    for (color in colors) {
                        val rgbBytes = ColorUtils.getRgbBytes(color)
                        for (rgbByte in rgbBytes) {
                            add(rgbByte)
                        }
                    }
                }

                sub_effect_caiHui -> {
                    add(caiHuiType)
                    // TODO: @陈信儒 2023/10/31 后续协议删除改字段，等通知
                    add(20)
                    add(if (colorGradual) 1 else 0)
                    val size4Color = colors.size
                    if (size4Color <= 0) {
                        msg?.invoke("图层${pos + 1},请添加颜色!")
                        return null
                    }
                    add(size4Color.toByte())
                    for (color in colors) {
                        val rgbBytes = ColorUtils.getRgbBytes(color)
                        for (rgbByte in rgbBytes) {
                            add(rgbByte)
                        }
                    }
                }

                else -> {
                    msg?.invoke("图层${pos + 1}的随机的二级效果 subEffect = $subEffect is not support!")
                    return null
                }
            }

        }
    }

    override fun getSubEffectModel(): MutableList<IntArray> {
        return subEffectModels
    }

    override fun fetchSubEffect(): Byte {
        return subEffect
    }

    override fun getColorModels(): MutableList<DiyColorEditViewH606A.ColorModel> {
        return mutableListOf<DiyColorEditViewH606A.ColorModel>().apply {
            colors.onEach {
                val colorModel = DiyColorEditViewH606A.ColorModel.makeShowing(it)
                add(colorModel)
            }
        }
    }

    override fun setSubEffectValue(subEffect: Byte) {
        this.subEffect = subEffect
    }

    override fun checkCondition(): Pair<MutableList<Int>, IntArray> {
        if (subEffect == sub_effect_caiHui) {
            return Pair(this.colors, caiHuiEffectColorRangeArray)
        }
        return Pair(this.colors, effectColorRangeArray)
    }

    override fun getCurColors(): MutableList<Int> {
        return this.colors
    }

    override fun changeColors(newColors: MutableList<Int>) {
        if (newColors.isNotEmpty()) {
            this.colors.clear()
            newColors.forEach {
                this.colors.add(it)
            }
        }
    }

    fun bgColorChanged(rgbBytes: IntArray) {
        bgColor = 0
        if (rgbBytes.isNotEmpty()) {
            bgColor = rgbBytes[0]
        }
    }

    fun colorChanged(rgbBytes: IntArray) {
        colors.clear()
        for (rgb in rgbBytes) {
            colors.add(rgb)
        }
    }

    fun initData(deviceBaseInfo: DeviceBaseInfo) {
//        val maxIcNum = deviceBaseInfo.icNum
//        dieJia4SuiJiRange[1] = maxIcNum.toByte()
//        yueDong4ShanSuoRange[1] = maxIcNum.toByte()
    }

    fun getColorSegmented(): Int {
        return if (colorPartSwitch) 1 else 0
    }
}


/**
 * 堆积
 */
@Keep
class SubEffect4DuiJi : IProMsg {
    companion object {
        const val TAG = "SubEffect4DuiJi"
        const val sub_effect_fuGai: Byte = 0x00
        const val sub_effect_shiFang: Byte = 0x01
        const val sub_effect_xiaoxiaole: Byte = 0x02

        const val effect_type_xianXing: Byte = 0x00
        const val effect_type_mianXing: Byte = 0x01

        const val direction_hor: Byte = 0x00
        const val direction_ver: Byte = 0x01

        const val direction_start: Byte = 0x04
        const val direction_end: Byte = 0x05


        const val color_type_zhengDuan: Byte = 0x00
        const val color_type_fenduan: Byte = 0x01

        fun directionModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(SubEffect4SuiJi.direction_shang2Xia.toInt(), R.drawable.component_diy_action_up))
                add(intArrayOf(SubEffect4SuiJi.direction_xia2Shang.toInt(), R.drawable.component_diy_action_down))
                add(intArrayOf(SubEffect4SuiJi.direction_you2Zuo.toInt(), R.drawable.component_diy_action_left))
                add(intArrayOf(SubEffect4SuiJi.direction_zuo2You.toInt(), R.drawable.component_diy_action_right))
            }
        }

        fun duiJiEffectTypeModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(effect_type_xianXing.toInt(), R.string.b2light_diy_xianxin))
                add(intArrayOf(effect_type_mianXing.toInt(), R.string.b2light_diy_mianxing))
            }
        }

        fun lineDirectionModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(direction_start.toInt(), R.string.b2light_diy_clockwise))
                add(intArrayOf(direction_end.toInt(), R.string.b2light_diy_anticlockwise))
            }
        }

        fun colorDistributionModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(SubEffect4LiuDong.color_type_zhengDuan.toInt(), R.string.b2light_effect_all_block))
                add(intArrayOf(SubEffect4LiuDong.color_type_fenduan.toInt(), R.string.b2light_effect_part_block))
            }
        }

        fun parse(effectValue: ByteArray): SubEffect4DuiJi? {
            return try {
                SafeLog.i(TAG) { "parse " }
                var index = 0
                val subEffect4DuiJi = SubEffect4DuiJi()
                val subEffect = effectValue[index++]
                subEffect4DuiJi.subEffect = subEffect
                when (subEffect) {
                    sub_effect_fuGai -> {
                        subEffect4DuiJi.fgValidValue.apply {
                            val type = effectValue[index++]
                            this[0] = type
                            if (type == effect_type_xianXing) {
                                val def = effectValue[index++]
                                SafeLog.i(TAG) { "parse() 占位 def = $def" }
                                this[1] = effectValue[index++]
                            } else {
                                this[2] = effectValue[index++]
                                this[3] = effectValue[index++]
                            }
                            this[4] = effectValue[index++]
                            this[5] = effectValue[index++]
                        }
                    }

                    sub_effect_shiFang -> {
                        // 覆盖,释放
                        subEffect4DuiJi.sfValidValue.apply {
                            val type = effectValue[index++]
                            this[0] = type
                            if (type == effect_type_xianXing) {
                                val def = effectValue[index++]
                                SafeLog.i(TAG) { "parse() 占位 def = $def" }
                                this[1] = effectValue[index++]
                            } else {
                                this[2] = effectValue[index++]
                                this[3] = effectValue[index++]
                            }
                            this[4] = effectValue[index++]
                            this[5] = effectValue[index++]
                        }
                    }

                    sub_effect_xiaoxiaole -> {
                        // 消消乐
                        subEffect4DuiJi.xxlValidValue.apply {
                            this[0] = effectValue[index++]
                            this[1] = effectValue[index++]
                            this[2] = effectValue[index++]
                            this[3] = effectValue[index++]
                        }
                    }
                }

                val colorLen = BleUtil.getUnsignedByte(effectValue[index++])
                SafeLog.i(SubEffect4LiuDong.TAG) { "parse() colorLen = $colorLen" }
                for (i in 0 until colorLen) {
                    val rgbBytes = ByteArray(3)
                    System.arraycopy(effectValue, index, rgbBytes, 0, rgbBytes.size)
                    subEffect4DuiJi.colors.add(ColorUtils.toColor(rgbBytes))
                    index += 3
                }
                return subEffect4DuiJi
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    private val subEffectModels by lazy {
        mutableListOf<IntArray>().apply {
            add(intArrayOf(sub_effect_fuGai.toInt(), R.string.b2light_diy_cover))
            add(intArrayOf(sub_effect_shiFang.toInt(), R.string.b2light_scenes_acquittal))
            add(intArrayOf(sub_effect_xiaoxiaole.toInt(), R.string.b2light_music_xiaoxiaole))
        }
    }

    val bgColorRangeArray = intArrayOf(0, 0)

    /*颜色长度范围*/
    val colorRangeArray = intArrayOf(1, 8)


    /*二级效果*/
    var subEffect: Byte = sub_effect_fuGai

    /*方向*/
    var direction: Byte = SubEffect4LiuDong.direction_ver

    /*效果方式*/
    var effectType: Byte = SubEffect4LiuDong.end_point

    /*色块个数*/
    var colorPieceNum: Int = 1

    /*颜色*/
    val colors = mutableListOf<Int>()

    /*块数量*/
    var pieceNum: Int = 1

    /*覆盖效果*/
    var fgValidValue = ByteArray(6).apply {
        this[0] = effect_type_xianXing/*方式*/
        this[1] = direction_start/*线性-方向*/
        this[2] = SubEffect4LiuDong.direction_ver/*面性方向*/
        this[3] = SubEffect4LiuDong.start_point/*面性方向*/
        this[4] = color_type_zhengDuan/*颜色分布*/
        this[5] = 20/*块数*/
    }

    /*释放效果*/
    var sfValidValue = ByteArray(6).apply {
        this[0] = effect_type_xianXing
        this[1] = direction_start
        this[2] = SubEffect4LiuDong.direction_ver
        this[3] = SubEffect4LiuDong.start_point/*面性方向*/
        this[4] = color_type_zhengDuan
        this[5] = 20
    }

    /*消消乐*/
    var xxlValidValue = ByteArray(4).apply {
        this[0] = SubEffect4LiuDong.direction_ver
        this[1] = SubEffect4LiuDong.start_point
        this[2] = 20
        this[3] = colorPieceNum.toByte()
    }

    override fun toBytes(pos: Int, msg: ((reason: String) -> Unit)?): MutableList<Byte> {
        return mutableListOf<Byte>().apply {
            add(subEffect)
            when (subEffect) {
                sub_effect_fuGai -> {
                    val type = fgValidValue[0]
                    add(type)
                    if (type == effect_type_xianXing) {
                        add(0x00)
                        add(fgValidValue[1])
                    } else {
                        add(fgValidValue[2])
                        add(fgValidValue[3])
                    }
                    add(fgValidValue[4])
                    add(fgValidValue[5])
                }

                sub_effect_shiFang -> {
                    val type = sfValidValue[0]
                    add(type)
                    if (type == effect_type_xianXing) {
                        add(0x00)
                        add(sfValidValue[1])
                    } else {
                        add(sfValidValue[2])
                        add(sfValidValue[3])
                    }
                    add(sfValidValue[4])
                    add(sfValidValue[5])
                }

                sub_effect_xiaoxiaole -> {
                    // 消消乐
                    xxlValidValue.forEach {
                        add(it)
                    }
                }
            }
            val size4Color = colors.size
            add(size4Color.toByte())
            for (color in colors) {
                val rgbBytes = ColorUtils.getRgbBytes(color)
                for (rgbByte in rgbBytes) {
                    add(rgbByte)
                }
            }
        }
    }

    override fun getSubEffectModel(): MutableList<IntArray> {
        return subEffectModels
    }

    override fun fetchSubEffect(): Byte {
        return subEffect
    }

    override fun getColorModels(): MutableList<DiyColorEditViewH606A.ColorModel> {
        return mutableListOf<DiyColorEditViewH606A.ColorModel>().apply {
            colors.onEach {
                val colorModel = DiyColorEditViewH606A.ColorModel.makeShowing(it)
                add(colorModel)
            }
        }
    }

    override fun setSubEffectValue(subEffect: Byte) {
        this.subEffect = subEffect
    }

    override fun checkCondition(): Pair<MutableList<Int>, IntArray> {
        return Pair(this.colors, colorRangeArray)
    }

    override fun getCurColors(): MutableList<Int> {
        return this.colors
    }

    override fun changeColors(newColors: MutableList<Int>) {
        if (newColors.isNotEmpty()) {
            this.colors.clear()
            newColors.forEach {
                this.colors.add(it)
            }
        }
    }

    override fun changeDirection(direction: Byte) {
        val dirEffectPair = SubEffect4LiuDong.changeDirection(direction)
        if (dirEffectPair.first != SubEffect4LiuDong.direction_effect_not_support
            && dirEffectPair.second != SubEffect4LiuDong.direction_effect_not_support
        ) {
            updateDirection(dirEffectPair.first, dirEffectPair.second)
        }
    }

    fun colorChanged(rgbSetBytes: MutableList<ByteArray>) {
        colors.clear()
        for (rgbSetByte in rgbSetBytes) {
            colors.add(ColorUtils.toColor(rgbSetByte))
        }
    }

    fun updateEffectType(protocolType: Byte) {
        when (subEffect) {
            sub_effect_fuGai -> {
                fgValidValue[0] = protocolType
            }

            sub_effect_shiFang -> {
                sfValidValue[0] = protocolType
            }
        }
    }

    fun updateLineDirection(direction: Byte) {
        when (subEffect) {
            sub_effect_fuGai -> {
                fgValidValue[1] = direction
            }

            sub_effect_shiFang -> {
                sfValidValue[1] = direction
            }
        }
    }

    fun getLineDirection(): Int {
        return when (subEffect) {
            sub_effect_fuGai -> {
                fgValidValue[1].toInt()
            }

            sub_effect_shiFang -> {
                sfValidValue[1].toInt()
            }

            else -> {
                0
            }
        }
    }

    fun updatePieceNum(pieceNum: Int) {
        when (subEffect) {
            sub_effect_fuGai -> {
                fgValidValue[5] = pieceNum.toByte()
            }

            sub_effect_shiFang -> {
                sfValidValue[5] = pieceNum.toByte()
            }

            sub_effect_xiaoxiaole -> {
                xxlValidValue[2] = pieceNum.toByte()
            }
        }
        this.pieceNum = pieceNum
    }

    fun colorChanged(rgbBytes: IntArray) {
        colors.clear()
        for (rgb in rgbBytes) {
            colors.add(rgb)
        }
    }

    fun initData(deviceBaseInfo: DeviceBaseInfo) {

    }

    fun showFaceDirectionView(): Boolean {
        val subEffectType = when (subEffect) {
            sub_effect_fuGai -> fgValidValue[0]
            sub_effect_shiFang -> sfValidValue[0]
            sub_effect_xiaoxiaole -> effect_type_mianXing
            else -> effect_type_mianXing
        }
        return subEffectType == effect_type_mianXing
    }

    fun getFaceDirection(): Int {
        return when (subEffect) {
            sub_effect_fuGai -> {
                SubEffect4LiuDong.getDirectionSpecificParams(fgValidValue[2], fgValidValue[3])
                    .toInt()
            }

            sub_effect_shiFang -> {
                SubEffect4LiuDong.getDirectionSpecificParams(sfValidValue[2], sfValidValue[3])
                    .toInt()
            }

            sub_effect_xiaoxiaole -> {
                SubEffect4LiuDong.getDirectionSpecificParams(xxlValidValue[0], xxlValidValue[1])
                    .toInt()
            }

            else -> {
                SubEffect4LiuDong.getDirectionSpecificParams(direction, effectType)
                    .toInt()
            }
        }.apply {
            SafeLog.i(TAG) { "getFaceDirection() direction = $this" }
        }
    }

    fun showDiyTypeStyleChangeView(): Boolean {
        return subEffect == sub_effect_fuGai || subEffect == sub_effect_shiFang
    }

    fun showLineDirection(): Boolean {
        return getType() == effect_type_xianXing
    }

    fun getType(): Byte? {
        return when (subEffect) {
            sub_effect_fuGai -> {
                fgValidValue[0]
            }

            sub_effect_shiFang -> {
                sfValidValue[0]
            }

            else -> {
                null
            }
        }
    }

    fun showBalanceBlockNumberView(): Boolean {
        return when (subEffect) {
            sub_effect_fuGai -> {
                fgValidValue[0] == effect_type_mianXing
            }

            sub_effect_shiFang -> {
                sfValidValue[0] == effect_type_mianXing
            }

            else -> true
        }
    }

    fun showXXLLenNumberView(): Boolean {
        return subEffect == sub_effect_xiaoxiaole
    }

    fun getCurPieceNum(): Int {
        return when (subEffect) {
            sub_effect_fuGai -> {
                fgValidValue[5].toInt()
            }

            sub_effect_shiFang -> {
                sfValidValue[5].toInt()
            }

            else -> {
                xxlValidValue[2].toInt()
            }
        }
    }

    fun getLenNumber(): Int {
        return xxlValidValue[3].toInt()
    }

    fun updateLenNum(num: Int) {
        xxlValidValue[3] = num.toByte()
    }

    fun updateDirection(direction: Byte, effectType: Byte) {
        fgValidValue[2] = direction
        fgValidValue[3] = effectType
        sfValidValue[2] = direction
        sfValidValue[3] = effectType
        xxlValidValue[0] = direction
        xxlValidValue[1] = effectType
        this.direction = direction
        this.effectType = effectType
    }

    fun showColorDistributionView(): Boolean {
        return subEffect == sub_effect_fuGai || subEffect == sub_effect_shiFang
    }

    fun getColorSegmented(): Byte {
        return when (subEffect) {
            sub_effect_fuGai -> {
                fgValidValue[4]
            }

            sub_effect_shiFang -> {
                sfValidValue[4]
            }

            else -> {
                0
            }
        }
    }

    fun updateColorType(type: Byte) {
        when (subEffect) {
            sub_effect_fuGai -> {
                fgValidValue[4] = type
            }

            sub_effect_shiFang -> {
                sfValidValue[4] = type
            }
        }
    }

}


/**
 * 几何
 */
@Keep
class SubEffect4JiHe : IProMsg {
    companion object {
        const val TAG = "SubEffect4DuiJi"
        const val sub_effect_xuanzhuan: Byte = 0x00/*旋转*/
        const val sub_effect_yuanxing: Byte = 0x01/*圆形*/
        const val sub_effect_huoguang: Byte = 0x02/*火光*/

        /*顺时针、逆时针*/
        const val effectDirectionNi: Byte = 0x06
        const val effectDirectionShun: Byte = 0x07

        /*扩散、收缩*/
        const val effectDirectionKuosan: Byte = 0x08
        const val effectDirectionShousuo: Byte = 0x09

        /*基点信息*/
        const val centerPointLeftTop: Byte = 0x00
        const val centerPointLeftBottom: Byte = 0x01
        const val centerPointRightTop: Byte = 0x02
        const val centerPointRightBottom: Byte = 0x03
        const val centerPointCenter: Byte = 0x04
        const val centerPointCustom: Int = 0xFF

        fun directionModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(SubEffect4SuiJi.direction_shang2Xia.toInt(), R.drawable.component_diy_action_up))
                add(intArrayOf(SubEffect4SuiJi.direction_xia2Shang.toInt(), R.drawable.component_diy_action_down))
                add(intArrayOf(SubEffect4SuiJi.direction_you2Zuo.toInt(), R.drawable.component_diy_action_left))
                add(intArrayOf(SubEffect4SuiJi.direction_zuo2You.toInt(), R.drawable.component_diy_action_right))
            }
        }

        fun duiJiEffectTypeModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(centerPointLeftTop.toInt(), R.drawable.component_diy_action_left_top_point))
                add(intArrayOf(centerPointLeftBottom.toInt(), R.drawable.component_diy_action_left_bottom_point))
                add(intArrayOf(centerPointRightTop.toInt(), R.drawable.component_diy_action_right_top_point))
                add(intArrayOf(centerPointRightBottom.toInt(), R.drawable.component_diy_action_right_bottom_point))
                add(intArrayOf(centerPointCenter.toInt(), R.drawable.component_diy_action_center_point))
                add(intArrayOf(centerPointCustom, R.drawable.component_diy_action_custom))
            }
        }

        fun xzDirectionModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(effectDirectionShun.toInt(), R.string.b2light_diy_clockwise))
                add(intArrayOf(effectDirectionNi.toInt(), R.string.b2light_diy_anticlockwise))
            }
        }

        fun yxDistributionModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(effectDirectionKuosan.toInt(), R.string.b2light_diy_kuo_san))
                add(intArrayOf(effectDirectionShousuo.toInt(), R.string.b2light_diy_gathered))
            }
        }

        fun parse(effectValue: ByteArray): SubEffect4JiHe? {
            return try {
                SafeLog.i(TAG) { "parse " }
                var index = 0
                val subEffect4JiHe = SubEffect4JiHe()
                val subEffect = effectValue[index++]
                subEffect4JiHe.subEffect = subEffect
                subEffect4JiHe.pieceNum = effectValue[index++].toInt()

                when (subEffect) {
                    sub_effect_xuanzhuan -> {
                        subEffect4JiHe.xzValidValue.apply {
                            val type = effectValue[index++]
                            this[0] = type
                            this[1] = effectValue[index++]
                            this[2] = effectValue[index++]
                            this[3] = effectValue[index++]
                            this[4] = effectValue[index++]
                            this[5] = effectValue[index++]
                            this[6] = effectValue[index++]
                            this[7] = effectValue[index++]
                            this[8] = effectValue[index++]
                        }
                    }

                    sub_effect_yuanxing -> {
                        subEffect4JiHe.yxValidValue.apply {
                            val type = effectValue[index++]
                            this[0] = type
                            this[1] = effectValue[index++]
                            this[2] = effectValue[index++]
                            this[3] = effectValue[index++]
                            this[4] = effectValue[index++]
                            this[5] = effectValue[index++]
                            this[6] = effectValue[index++]
                            this[7] = effectValue[index++]
                            this[8] = effectValue[index++]
                        }
                    }

                    sub_effect_huoguang -> {
                        subEffect4JiHe.hgValidValue.apply {
                            this[0] = effectValue[index++]
                            this[1] = effectValue[index++]
                            this[2] = effectValue[index++]
                        }
                    }
                }

                val colorLen = BleUtil.getUnsignedByte(effectValue[index++])
                SafeLog.i(SubEffect4LiuDong.TAG) { "parse() colorLen = $colorLen" }
                for (i in 0 until colorLen) {
                    val rgbBytes = ByteArray(3)
                    System.arraycopy(effectValue, index, rgbBytes, 0, rgbBytes.size)
                    subEffect4JiHe.colors.add(ColorUtils.toColor(rgbBytes))
                    index += 3
                }
                return subEffect4JiHe
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    private val subEffectModels by lazy {
        mutableListOf<IntArray>().apply {
            add(intArrayOf(sub_effect_xuanzhuan.toInt(), R.string.b2light_scenes_spin))
            add(intArrayOf(sub_effect_yuanxing.toInt(), R.string.b2light_scenes_ripple))
            add(intArrayOf(sub_effect_huoguang.toInt(), R.string.b2light_scenes_fire))
        }
    }

    /*旋转方向*/
    private val xzDirectionModels by lazy { xzDirectionModels() }

    /*涟漪方向*/
    private val yxDistributionModels by lazy { yxDistributionModels() }

    val bgColorRangeArray = intArrayOf(0, 0)

    /*颜色长度范围*/
    val colorRangeArray = intArrayOf(1, 8)
    val xzColorRangeArray = intArrayOf(2, 8)

    /*二级效果*/
    var subEffect: Byte = sub_effect_xuanzhuan

    /*方向*/
    var direction: Byte = SubEffect4LiuDong.direction_ver

    /*效果方式*/
    var effectType: Byte = SubEffect4LiuDong.end_point

    /*出入场效果*/
    var entryAndExitEnable: Boolean = true
        set(value) {
            val code: Byte = if (value) 0x01 else 0x00
            when (subEffect) {
                sub_effect_xuanzhuan -> {
                    xzValidValue[8] = code
                }

                sub_effect_yuanxing -> {
                    yxValidValue[8] = code
                }
            }
            field = value
        }
        get() {
            return if (subEffect == sub_effect_xuanzhuan) {
                xzValidValue[8].toInt() == 0x01
            } else {
                yxValidValue[8].toInt() == 0x01
            }
        }

    /*渐变效果*/
    var colorGradual: Boolean = false
        set(value) {
            val code: Byte = if (value) 0x01 else 0x00
            when (subEffect) {
                sub_effect_xuanzhuan -> {
                    xzValidValue[7] = code
                }

                sub_effect_yuanxing -> {
                    yxValidValue[7] = code
                }

                sub_effect_huoguang -> {
                    hgValidValue[2] = code
                }
            }
            field = value
        }
        get() {
            return when (subEffect) {
                sub_effect_xuanzhuan -> {
                    xzValidValue[7].toInt() == 0x01
                }

                sub_effect_yuanxing -> {
                    yxValidValue[7].toInt() == 0x01
                }

                sub_effect_huoguang -> {
                    hgValidValue[2].toInt() == 0x01
                }

                else -> {
                    false
                }
            }
        }

    /*颜色*/
    val colors = mutableListOf<Int>()

    /*均分块数量*/
    var pieceNum: Int = 20

    /*旋转效果*/
    var xzValidValue = ByteArray(9).apply {
        this[0] = 0x00/*方向*/
        this[1] = effectDirectionShun/*效果*/
        this[2] = centerPointCenter/*默认位置，默认基点*/
        this[3] = 0x00/*x轴*/
        this[4] = 0x00/*x轴*/
        this[5] = 0x00/*y轴*/
        this[6] = 0x00/*y轴*/
        this[7] = 0x00/*颜色是否渐变*/
        this[8] = 0x01/*是否支持出入场效果*/
    }

    /*圆形效果*/
    var yxValidValue = ByteArray(9).apply {
        this[0] = 0x00/*方向*/
        this[1] = effectDirectionKuosan/*效果*/
        this[2] = centerPointLeftTop/*默认位置，默认基点*/
        this[3] = 0x00/*x轴*/
        this[4] = 0x00/*x轴*/
        this[5] = 0x00/*y轴*/
        this[6] = 0x00/*y轴*/
        this[7] = 0x00/*颜色是否渐变*/
        this[8] = 0x01/*是否支持出入场效果*/
    }

    /*火光效果*/
    var hgValidValue = ByteArray(3).apply {
        this[0] = SubEffect4LiuDong.direction_ver
        this[1] = SubEffect4LiuDong.end_point
        this[2] = 0x00/*颜色渐变*/
    }

    override fun toBytes(pos: Int, msg: ((reason: String) -> Unit)?): MutableList<Byte> {
        return mutableListOf<Byte>().apply {
            add(subEffect)
            add(pieceNum.toByte())
            when (subEffect) {
                sub_effect_xuanzhuan -> {
                    xzValidValue.forEach {
                        add(it)
                    }
                }

                sub_effect_yuanxing -> {
                    yxValidValue.forEach {
                        add(it)
                    }
                }

                sub_effect_huoguang -> {
                    hgValidValue.forEach {
                        add(it)
                    }
                }
            }
            val size4Color = colors.size
            add(size4Color.toByte())
            for (color in colors) {
                val rgbBytes = ColorUtils.getRgbBytes(color)
                for (rgbByte in rgbBytes) {
                    add(rgbByte)
                }
            }
        }
    }

    override fun getSubEffectModel(): MutableList<IntArray> {
        return subEffectModels
    }

    override fun fetchSubEffect(): Byte {
        return subEffect
    }

    override fun getColorModels(): MutableList<DiyColorEditViewH606A.ColorModel> {
        return mutableListOf<DiyColorEditViewH606A.ColorModel>().apply {
            colors.onEach {
                val colorModel = DiyColorEditViewH606A.ColorModel.makeShowing(it)
                add(colorModel)
            }
        }
    }

    override fun setSubEffectValue(subEffect: Byte) {
        this.subEffect = subEffect
    }

    override fun checkCondition(): Pair<MutableList<Int>, IntArray> {
        return Pair(this.colors, getColorRang())
    }

    override fun getCurColors(): MutableList<Int> {
        return this.colors
    }

    override fun changeColors(newColors: MutableList<Int>) {
        if (newColors.isNotEmpty()) {
            this.colors.clear()
            newColors.forEach {
                this.colors.add(it)
            }
        }
    }

    fun colorChanged(rgbSetBytes: MutableList<ByteArray>) {
        colors.clear()
        for (rgbSetByte in rgbSetBytes) {
            colors.add(ColorUtils.toColor(rgbSetByte))
        }
    }

    fun getColorRang(): IntArray {
        if (subEffect == sub_effect_xuanzhuan) {
            return xzColorRangeArray
        }
        return colorRangeArray
    }

    fun isCustomType(protocolType: Byte): Boolean {
        return ByteUtil.getUnsignedByte(protocolType) == centerPointCustom
    }

    fun getCenterPoint(): Pair<Int, Int>? {
        return when (subEffect) {
            sub_effect_xuanzhuan -> {
                var x = BleUtils.getSignedInt(byteArrayOf(xzValidValue[3], xzValidValue[4]), false)
                var y = BleUtils.getSignedInt(byteArrayOf(xzValidValue[5], xzValidValue[6]), false)
                if (x == 0 && y == 0) {
                    x = -1
                    y = -1
                }
                Pair(x, y)
            }

            sub_effect_yuanxing -> {
                var x = BleUtils.getSignedInt(byteArrayOf(yxValidValue[3], yxValidValue[4]), false)
                var y = BleUtils.getSignedInt(byteArrayOf(yxValidValue[5], yxValidValue[6]), false)
                if (x == 0 && y == 0) {
                    x = -1
                    y = -1
                }
                Pair(x, y)
            }

            else -> {
                null
            }
        }
    }

    fun setCenterPoint(x: Int, y: Int) {
        val xValue = BleUtils.getSignedBytesFor2(x, false)
        val yValue = BleUtils.getSignedBytesFor2(y, false)
        when (subEffect) {
            sub_effect_xuanzhuan -> {
                xzValidValue[3] = xValue[0]
                xzValidValue[4] = xValue[1]
                xzValidValue[5] = yValue[0]
                xzValidValue[6] = yValue[1]
            }

            sub_effect_yuanxing -> {
                yxValidValue[3] = xValue[0]
                yxValidValue[4] = xValue[1]
                yxValidValue[5] = yValue[0]
                yxValidValue[6] = yValue[1]
            }
        }
    }

    fun updateBaseCenterPoint(protocolType: Byte) {
        when (subEffect) {
            sub_effect_xuanzhuan -> {
                xzValidValue[2] = protocolType
            }

            sub_effect_yuanxing -> {
                yxValidValue[2] = protocolType
            }
        }
    }

    fun getDirectionSubCode(): IntArray {
        return when (subEffect) {
            sub_effect_xuanzhuan -> {
                xzDirectionModels.map { it[0] }.toIntArray()
            }

            else -> {
                yxDistributionModels.map { it[0] }.toIntArray()
            }
        }
    }

    fun getDirectionSubCodeRes(): IntArray {
        return when (subEffect) {
            sub_effect_xuanzhuan -> {
                xzDirectionModels.map { it[1] }.toIntArray()
            }

            else -> {
                yxDistributionModels.map { it[1] }.toIntArray()
            }
        }
    }

    fun getSpecialDirectionCode(): Byte {
        return when (subEffect) {
            sub_effect_xuanzhuan -> {
                xzValidValue[1]
            }

            else -> {
                yxValidValue[1]
            }
        }
    }

    fun updateSpecialDirection(direction: Byte) {
        when (subEffect) {
            sub_effect_xuanzhuan -> {
                xzValidValue[1] = direction
            }

            sub_effect_yuanxing -> {
                yxValidValue[1] = direction
            }
        }
    }

    fun updatePieceNum(pieceNum: Int) {
        this.pieceNum = pieceNum
    }

    fun colorChanged(rgbBytes: IntArray) {
        colors.clear()
        for (rgb in rgbBytes) {
            colors.add(rgb)
        }
    }

    fun initData(deviceBaseInfo: DeviceBaseInfo) {

    }

    fun showEntryAndExitEnable(): Boolean {
        return subEffect == sub_effect_xuanzhuan || subEffect == sub_effect_yuanxing
    }

    fun getType(): Byte? {
        return when (subEffect) {
            sub_effect_xuanzhuan -> {
                xzValidValue[0]
            }

            sub_effect_yuanxing -> {
                yxValidValue[0]
            }

            else -> {
                null
            }
        }
    }

    fun getCurPieceNum(): Int {
        return pieceNum
    }

    fun updateDirection(direction: Byte, effectType: Byte) {
        hgValidValue[0] = direction
        hgValidValue[1] = effectType
        this.direction = direction
        this.effectType = effectType
    }

    override fun changeDirection(direction: Byte) {
        val dirEffectPair = SubEffect4LiuDong.changeDirection(direction)
        if (dirEffectPair.first != SubEffect4LiuDong.direction_effect_not_support
            && dirEffectPair.second != SubEffect4LiuDong.direction_effect_not_support
        ) {
            updateDirection(dirEffectPair.first, dirEffectPair.second)
        }
    }

    /**
     * 默认位置，默认基点
     */
    fun getCenterPointDirection(): Int {
        return when (subEffect) {
            sub_effect_xuanzhuan -> {
                ByteUtil.getUnsignedByte(xzValidValue[2])
            }

            sub_effect_yuanxing -> {
                ByteUtil.getUnsignedByte(yxValidValue[2])
            }

            else -> {
                0
            }
        }
    }

    fun showNormalDirectionView(): Boolean {
        return subEffect == sub_effect_huoguang
    }

    fun showCenterPointView(): Boolean {
        return subEffect == sub_effect_xuanzhuan || subEffect == sub_effect_yuanxing
    }
}


/**
 * 线性
 */
@Keep
class SubEffect4XianXing : IProMsg {
    companion object {
        const val TAG = "SubEffect4DuiJi"
        const val sub_effect_danrudanchu: Byte = 0x00/*淡入淡出*/
        const val sub_effect_huxi: Byte = 0x01/*呼吸*/
        const val sub_effect_caihong: Byte = 0x02/*彩虹*/
        const val sub_effect_liuxing: Byte = 0x03/*流星*/

        /*分段、整段*/
        const val color_type_zhengDuan: Byte = 0x00
        const val color_type_fenduan: Byte = 0x01

        /*顺时针、逆时针*/
        const val effectDirectionShun: Byte = 0x04
        const val effectDirectionNi: Byte = 0x05

        const val meteorsNum_single: Byte = 0x01
        const val meteorsNum_multiple: Byte = 0x05

        /*扩散、收缩*/
        const val effectDirectionKuosan: Byte = 0x08
        private const val effectDirectionShousuo: Byte = 0x09

        /*基点信息*/
        const val centerPointLeftTop: Byte = 0x00
        private const val centerPointLeftBottom: Byte = 0x01
        private const val centerPointRightTop: Byte = 0x02
        private const val centerPointRightBottom: Byte = 0x03
        private const val centerPointCenter: Byte = 0x04
        const val centerPointCustom: Byte = 0x05
        fun colorTypeModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(color_type_zhengDuan.toInt(), R.string.b2light_diy_whole))
                add(intArrayOf(color_type_fenduan.toInt(), R.string.b2light_diy_subsection))
            }
        }

        fun directionModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(effectDirectionShun.toInt(), R.string.b2light_diy_clockwise))
                add(intArrayOf(effectDirectionNi.toInt(), R.string.b2light_diy_anticlockwise))
            }
        }

        fun meteorsNumModels(): MutableList<IntArray> {
            return mutableListOf<IntArray>().apply {
                add(intArrayOf(meteorsNum_single.toInt(), R.string.b2light_diy_num_single))
                add(intArrayOf(meteorsNum_multiple.toInt(), R.string.b2light_diy_num_multiple))
            }
        }

        fun parse(effectValue: ByteArray): SubEffect4XianXing? {
            return try {
                SafeLog.i(TAG) { "parse " }
                var index = 0
                val subEffect4XianXing = SubEffect4XianXing()
                val subEffect = effectValue[index++]
                subEffect4XianXing.subEffect = subEffect
                when (subEffect) {
                    sub_effect_danrudanchu, sub_effect_huxi -> {
                        subEffect4XianXing.colorDisplayModel = effectValue[index++]
                    }

                    sub_effect_caihong -> {
                        subEffect4XianXing.direction = effectValue[index++]
                        subEffect4XianXing.effectType = effectValue[index++]
                        subEffect4XianXing.colorGradual = effectValue[index++].toInt() == 0x01
                        subEffect4XianXing.entryAndExitEnable = effectValue[index++].toInt() == 0x01
                    }

                    sub_effect_liuxing -> {
                        subEffect4XianXing.meteorsNum = effectValue[index++].toInt()
                        subEffect4XianXing.direction = effectValue[index++]
                        subEffect4XianXing.effectType = effectValue[index++]
                        subEffect4XianXing.colorGradual = effectValue[index++].toInt() == 0x01
                        subEffect4XianXing.entryAndExitEnable = effectValue[index++].toInt() == 0x01
                    }
                }

                val colorLen = BleUtil.getUnsignedByte(effectValue[index++])
                SafeLog.i(SubEffect4LiuDong.TAG) { "parse() colorLen = $colorLen" }
                for (i in 0 until colorLen) {
                    val rgbBytes = ByteArray(3)
                    System.arraycopy(effectValue, index, rgbBytes, 0, rgbBytes.size)
                    subEffect4XianXing.colors.add(ColorUtils.toColor(rgbBytes))
                    index += 3
                }
                return subEffect4XianXing
            } catch (e: Exception) {
                e.printStackTrace()
                null
            }
        }
    }

    private val subEffectModels by lazy {
        mutableListOf<IntArray>().apply {
            add(intArrayOf(sub_effect_danrudanchu.toInt(), R.string.b2light_diy_fade))
            add(intArrayOf(sub_effect_huxi.toInt(), R.string.b2light_scenes_breath))
            add(intArrayOf(sub_effect_caihong.toInt(), R.string.b2light_scenes_stream))
            add(intArrayOf(sub_effect_liuxing.toInt(), R.string.b2light_scenes_meteor))
        }
    }

    val bgColorRangeArray = intArrayOf(0, 0)

    /*颜色长度范围*/
    val colorRangeArray = intArrayOf(1, 8)

    /*二级效果*/
    var subEffect: Byte = sub_effect_danrudanchu

    /*颜色展示方式*/
    var colorDisplayModel: Byte = color_type_zhengDuan

    /*方向*/
    var direction: Byte = SubEffect4LiuDong.direction_hor

    /*效果方式*/
    var effectType: Byte = effectDirectionNi

    /*出入场效果*/
    var entryAndExitEnable: Boolean = false

    /*渐变效果*/
    var colorGradual: Boolean = true

    /*颜色*/
    val colors = mutableListOf<Int>()

    /*流星个数*/
    var meteorsNum: Int = meteorsNum_single.toInt()

    override fun toBytes(pos: Int, msg: ((reason: String) -> Unit)?): MutableList<Byte> {
        return mutableListOf<Byte>().apply {
            add(subEffect)
            when (subEffect) {
                sub_effect_danrudanchu, sub_effect_huxi -> {
                    add(colorDisplayModel)
                }

                sub_effect_caihong -> {
                    add(direction)
                    add(effectType)
                    add(if (colorGradual) 0x01 else 0x00)
                    add(if (entryAndExitEnable) 0x01 else 0x00)
                }

                sub_effect_liuxing -> {
                    add(meteorsNum.toByte())
                    add(direction)
                    add(effectType)
                    add(if (colorGradual) 0x01 else 0x00)
                    add(0x01)
                }
            }
            val size4Color = colors.size
            add(size4Color.toByte())
            for (color in colors) {
                val rgbBytes = ColorUtils.getRgbBytes(color)
                for (rgbByte in rgbBytes) {
                    add(rgbByte)
                }
            }
        }
    }

    override fun getSubEffectModel(): MutableList<IntArray> {
        return subEffectModels
    }

    override fun fetchSubEffect(): Byte {
        return subEffect
    }

    override fun getColorModels(): MutableList<DiyColorEditViewH606A.ColorModel> {
        return mutableListOf<DiyColorEditViewH606A.ColorModel>().apply {
            colors.onEach {
                val colorModel = DiyColorEditViewH606A.ColorModel.makeShowing(it)
                add(colorModel)
            }
        }
    }

    override fun setSubEffectValue(subEffect: Byte) {
        this.subEffect = subEffect
    }

    override fun checkCondition(): Pair<MutableList<Int>, IntArray> {
        return Pair(this.colors, colorRangeArray)
    }

    override fun getCurColors(): MutableList<Int> {
        return this.colors
    }

    override fun changeColors(newColors: MutableList<Int>) {
        if (newColors.isNotEmpty()) {
            this.colors.clear()
            newColors.forEach {
                this.colors.add(it)
            }
        }
    }

    fun colorChanged(rgbSetBytes: MutableList<ByteArray>) {
        colors.clear()
        for (rgbSetByte in rgbSetBytes) {
            colors.add(ColorUtils.toColor(rgbSetByte))
        }
    }

    fun colorChanged(rgbBytes: IntArray) {
        colors.clear()
        for (rgb in rgbBytes) {
            colors.add(rgb)
        }
    }

    fun initData(deviceBaseInfo: DeviceBaseInfo) {

    }

    fun showEntryAndExitEnable(): Boolean {
        return subEffect == sub_effect_caihong
    }

    fun showColorEffectGradient(): Boolean {
        return subEffect == sub_effect_caihong || subEffect == sub_effect_liuxing
    }

    fun showDirectionView(): Boolean {
        return subEffect == sub_effect_caihong || subEffect == sub_effect_liuxing
    }

    fun showMeteorsNumView(): Boolean {
        return subEffect == sub_effect_liuxing
    }

    fun showColorDistributedView(): Boolean {
        return subEffect == sub_effect_danrudanchu || subEffect == sub_effect_huxi
    }

    fun updateDirection(direction: Byte) {
        this.direction = direction
    }

    override fun changeDirection(direction: Byte) {
        if (direction == SubEffect4LiuDong.xian_shunshizhen || direction == SubEffect4LiuDong.xian_nishizhen) {
            this.effectType = direction
        }
    }

}
