package com.govee.base2light.iot;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-11
 * 定义iot的cmd指令常量$
 */
public final class Cmd {
    private Cmd() {
    }

    public static final String parse_json_key_state = "state";
    public static final String parse_json_op = "op";
    public static final String parse_json_wifi_hard_version = "wifiHardVersion";

    public static String getCmdReadParseKey(String cmd) {
        if (pt.equals(cmd) || ptReal.equals(cmd)) return parse_json_op;
        return parse_json_key_state;
    }

    public static final String online = "online";
    public static final String turn = "turn";
    public static final String brightness = "brightness";
    public static final String status = "status";
    public static final String color = "color";
    public static final String timer = "timer";
    public static final String colorTem = "colorTem";
    public static final String bulb = "bulb";
    public static final String pt = "pt";
    /*bk新增*/
    public static final String colorwc = "colorwc";
    public static final String ptReal = "ptReal";
    public static final String multiSync = "multiSync";

    // 一键白平衡获取快照
    public static final String snapshotAWB = "snapshotAWB";
    public static final String ptIot = "ptIot";
}