package com.govee.base2light.videomode.newdetail.view.whiteBalance.autowb

import android.view.LayoutInflater
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewbinding.ViewBinding
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2kt.ext.clickWithTrigger
import com.govee.base2kt.ext.setAlphaClickByBoolean
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2light.databinding.B2lightDialogSelectShutterTimeBinding
import com.govee.base2light.kt.comm.Info4BleIotDevice
import com.ihoment.base2app.dialog.BaseEventDialog
import com.ihoment.base2app.glide.ImageResConfigManager
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.AppUtil

/**
 * 白平衡-选择快门时间
 */
class SelectShutterTimeDialog private constructor(
    val ac: AppCompatActivity,
    val deviceInfo: Info4BleIotDevice?
) : BaseEventDialog(ac) {

    private var vm: AutoCaliWbVm = ViewModelProvider(ac)[AutoCaliWbVm::class.java]

    companion object {
        fun createDialog(
            context: AppCompatActivity,
            deviceInfo: Info4BleIotDevice?
        ): SelectShutterTimeDialog {
            return SelectShutterTimeDialog(context, deviceInfo)
        }
    }

    private lateinit var binding: B2lightDialogSelectShutterTimeBinding
    private lateinit var shutterTimeAdapter: SelectShutterTimeAdapter

    override fun getLayout(): Int {
        return com.govee.base2light.R.layout.b2light_dialog_select_shutter_time
    }

    init {
        SafeLog.i(TAG, "${this.javaClass.simpleName} init")
        ignoreBackPressed()
        changeDialogOutside(false)

        initParams()
        intView()
        initObserver()

        vm.initIotOp(deviceInfo?.sku, deviceInfo?.topic, deviceInfo?.device)
        vm.iotOp.register()
        vm.readSnapshot()
        vm.registerCaliNotify()
    }

    private fun iotOpUnRegister() {
        vm.iotOp.unRegister()
    }

    private fun intView() {
        binding.apply {
            shutterTimeAdapter =
                SelectShutterTimeAdapter(object : SelectShutterTimeAdapter.OnItemSelectListener {
                    override fun onItemSelected(shutterTimeBean: ShutterTimeBean) {
                        setNextButtonStatus(true)
                    }
                })
            shutterTimeList.adapter = shutterTimeAdapter
            shutterTimeAdapter.setList(vm.getLoadingShowList())
            shutterTimeList.layoutManager = LinearLayoutManager(ac)
            btClose.clickWithTrigger {
                iotOpUnRegister()
                hide()
            }
            btnNext.clickWithTrigger {
                shutterTimeAdapter.getSelectItem()?.let {
                    vm.sendCaliWb(it.exposeLevel)
                }
            }
            retryTv.clickWithTrigger {
                switchShowAndRetry(false)
                vm.readSnapshot()
            }

            ImageResConfigManager.loadImage(noticeImg, "h66a0_pics_jiaozhun_02")
        }
        setNextButtonStatus(false)
    }

    private fun initObserver() {
        vm.shutterTimeListLD.observe(ac) {
            if (it != null && it.size >= vm.snapShotNum) {
                val sortList = it.sortedBy { bean -> bean.exposeLevel }
                shutterTimeAdapter.setList(sortList)
            }
        }
        vm.waitSnapshotOvertimeLD.observe(ac) {
            if (it == true) {
                switchShowAndRetry(true)
            }
        }
        vm.caliWbControllerResultLD.observe(ac) {
            if (it == null) return@observe
            if (it) {
                showWaitCaliResultDialog()
            } else {
                SafeLog.i(TAG, "白平衡校准指令失败")
            }
        }
        vm.loadingLiveData.observe(ac) {
            if (it) {
                showLoading()
            } else {
                hideLoading()
            }
        }
        vm.iotOp.overtimeLiveData.observe(ac) {
            if (!it.isOvertime) return@observe
            vm.loadingLiveData.postValue(false)
        }
    }

    private fun initParams() {
        vm.hasShowResultDialog = false
        vm.shutterTimeListLD.value = null
        vm.waitSnapshotOvertimeLD.value = null
        vm.caliWbControllerResultLD.value = null
    }

    /**
     * 展示等待校准结果的弹窗
     */
    private fun showWaitCaliResultDialog() {
        hide()
        if (!vm.hasShowResultDialog) {
            vm.hasShowResultDialog = true
            vm.caliWbResultLD.value = null
            AutoWhitBalanceResultDialog.createDialog(ac).show()
        }
    }

    /**
     * 切换重试和展示模式
     */
    private fun switchShowAndRetry(isRetry: Boolean) {
        binding.apply {
            retryTv.visibleByBoolean(isRetry)
            shutterTimeList.visibleByBoolean(!isRetry)
        }
    }

    private fun setNextButtonStatus(enable: Boolean) {
        setAlphaClickByBoolean(binding.btnNext, enable)
    }

    private fun showLoading() {
        LoadingDialog.createDialog(ac, com.ihoment.base2app.R.style.DialogDim, 30 * 1000L)
            .setEventKey(TAG).show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(TAG)
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth() * 335 / 375
    }

    override fun useViewBinding(): ViewBinding {
        binding = B2lightDialogSelectShutterTimeBinding.inflate(LayoutInflater.from(context))
        return binding
    }

}