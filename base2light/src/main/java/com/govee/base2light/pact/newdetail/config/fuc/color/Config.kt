package com.govee.base2light.pact.newdetail.config.fuc.color

import com.govee.base2home.Constant
import com.govee.base2home.ota.OtaType
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.pact.support.NewRgb2TLUtil
import com.govee.base2home.pact.support.OldBulbUtil
import com.govee.base2home.pact.support.OldDreamColorUtil
import com.govee.base2home.pact.support.OldRgbBkUtil
import com.govee.base2home.pact.support.Support4DreamColorLightV2
import com.govee.base2home.pact.support.Support4DreamColorV1
import com.govee.base2kt.utils.BleUtils
import com.govee.base2kt.utils.ColorUtils
import com.govee.base2light.ble.controller.IControllerNoEvent
import com.govee.base2light.kt.comm.AbsCmd4Op
import com.govee.base2light.kt.comm.Info4BleIotDevice
import com.govee.base2light.kt.general_controller.ControllerMode
import com.govee.base2light.kt.iot.CmdColor
import com.govee.base2light.kt.iot.CmdColorTem
import com.govee.base2light.kt.iot.CmdColorWc
import com.govee.base2light.kt.iot.CmdOldColor
import com.govee.base2light.kt.iot.CmdOldColorTem
import com.govee.base2light.kt.iot.Old4CmdBulb
import com.govee.base2light.kt.iot.Old4CmdPt
import com.govee.base2light.pact.newdetail.config.fuc.ColorPieceConfig
import com.ihoment.base2app.infra.SafeLog
import java.util.Arrays

/**
 * Create By xieyingwu on 2023/12/28
 * 配置类
 */
class Config {
    companion object {
        private const val TAG = "Config"
        const val VERSION_NOT_SUPPORT = -1

        //<editor-fold desc="颜色模式指令-蓝牙指令配置条件">
        const val COLOR_MODE_VERSION_0X15 = 0
        const val COLOR_MODE_VERSION_0X14 = 1
        const val COLOR_MODE_VERSION_0X0D = 2
        const val COLOR_MODE_VERSION_0X0B = 3
        const val COLOR_MODE_VERSION_0X02 = 4
        const val COLOR_MODE_VERSION_0X15_NO_TEMP_RGB = 5/*没有色温转 rgb 的效果字段*/
        const val COLOR_MODE_VERSION_0X0B_KEVIN =
            6 //相比于COLOR_MODE_VERSION_0X0B多了色温配置,ByteArray的size为8
        const val COLOR_MODE_VERSION_0X08 = 7
        const val COLOR_MODE_VERSION_0X6E = 8 //颜色分段超过56段

        const val RELATIVE_BRIGHTNESS_VERSION_0X15 = 0

        const val GRADUAL_VERSION_0xA3 = 0
        const val GRADUAL_VERSION_0x14 = 1

        //颜色，相对亮度度支持分组设置
        private const val COLOR_BRIGHTNESS_GROUP_1 = 1//组数从1开始
        private const val COLOR_BRIGHTNESS_GROUP_0 = 0//组数从0开始

        fun isVersionSupport(version: Int): Boolean {
            return version != VERSION_NOT_SUPPORT
        }

        private val colorBytes4SkuCompareMap =
            hashMapOf<String, (info: Info4BleIotDevice) -> Int>().apply {
                put("H7022") {
                    COLOR_MODE_VERSION_0X0B
                }
                put("H7023") {
                    COLOR_MODE_VERSION_0X08
                }
                put("H7001") {
                    COLOR_MODE_VERSION_0X08
                }
                put("H7024") {
                    COLOR_MODE_VERSION_0X0B
                }
                put("H7310") {
                    COLOR_MODE_VERSION_0X02
                }
                put("H7312") {
                    COLOR_MODE_VERSION_0X02
                }
                put("H7317") {
                    COLOR_MODE_VERSION_0X02
                }
                put("H7318") {
                    COLOR_MODE_VERSION_0X02
                }
                put("H6163") {
                    if (it.pactType >= 2 && it.pactCode >= 1) {
                        COLOR_MODE_VERSION_0X15
                    } else if (BleUtils.isHigherVersion(it.versionHard, "1.00.02") &&
                        BleUtils.isHigherVersion(it.versionSoft, "1.06.00")
                    ) {
                        COLOR_MODE_VERSION_0X15
                    } else {
                        COLOR_MODE_VERSION_0X0B
                    }
                }
                put("H6117") {
                    if (it.pactType >= 2 && it.pactCode >= 1) {
                        COLOR_MODE_VERSION_0X15
                    } else if (BleUtils.isHigherVersion(it.versionHard, "1.00.02") &&
                        BleUtils.isHigherVersion(it.versionSoft, "1.06.00")
                    ) {
                        COLOR_MODE_VERSION_0X15
                    } else {
                        COLOR_MODE_VERSION_0X0B
                    }
                }
                put("H6102") {
                    getOldDreamColorModeVersion(it, COLOR_MODE_VERSION_0X02)
                }
                put("H6125") {
                    getOldDreamColorModeVersion(it, COLOR_MODE_VERSION_0X0B)
                }
                put("H6126") {
                    getOldDreamColorModeVersion(it, COLOR_MODE_VERSION_0X0B)
                }
                put("H6127") {
                    getOldDreamColorModeVersion(it, COLOR_MODE_VERSION_0X02)
                }
                put("H6116") {
                    getOldDreamColorModeVersion(it, COLOR_MODE_VERSION_0X02)
                }
                put("H6161") {
                    getOldDreamColorModeVersion(it, COLOR_MODE_VERSION_0X02)
                }
                put("H6107") {
                    getOldDreamColorModeVersion(it, COLOR_MODE_VERSION_0X02)
                }
            }

        /**配置每个GoodsType的颜色指令版本配置*/
        private val colorBytes4GoodsTypeCompareMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Int>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_H7025) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_H7095) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7066) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_SHOOT_LIGHT_V1) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1) { bleIotInfo ->
                    val supportPartBrightness =
                        (BleUtils.isHigherVersion(bleIotInfo.versionHard, "1.00.01") &&
                            OtaType.isTelinkOta(bleIotInfo.versionHard) && BleUtils.isHigherVersion(
                            bleIotInfo.versionSoft,
                            "1.06.00"
                        ))
                            || BleUtils.isHigherVersion(bleIotInfo.versionHard, "2.01.00")
                    if (supportPartBrightness) {
                        COLOR_MODE_VERSION_0X15
                    } else {
                        COLOR_MODE_VERSION_0X02
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7053) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7063) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_LED_PATH_LIGHTS) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7052) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT_V2) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_h7033) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7037) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H702ABC) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB_V2) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V2) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V3) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V4) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_RGBWW_BALL_LIGHT) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT_V2) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_FLOOD_LIGHT) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7075) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6022) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7078) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7072) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6641) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6640) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E5) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E6) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61B0_B3_B6) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61F6) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61b8) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) { info ->
                    val versionSoft = info.versionSoft
                    val versionHard = info.versionHard
                    SafeLog.i(TAG) { "colorMode4GoodsTypeCompareMap() goodsType =${info.goodsType} ; versionSoft  =$versionSoft ; versionHard = $versionHard" }
                    if (BleUtils.isHigherVersion(
                            versionSoft, "1.00.11"
                        ) && BleUtils.isHigherVersion(versionHard, "1.00.01")
                    ) {
                        COLOR_MODE_VERSION_0X15
                    } else {
                        COLOR_MODE_VERSION_0X14
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A1) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61D3_D5) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A0) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B3) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B4) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B5) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70BC) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H705DEF_H805ABC) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_OUTDOOR_LAMP) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H706A_B_C) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7067_68_69) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1) {
                    fun support(): Boolean {
                        if (it.pactType >= 10 && it.pactCode >= 1) {
                            return true
                        }
                        return BleUtils.isHigherVersion(
                            it.versionSoft,
                            "1.06.00"
                        ) && BleUtils.isHigherVersion(it.versionHard, "1.00.03")
                    }
                    if (support()) {
                        COLOR_MODE_VERSION_0X15
                    } else {
                        COLOR_MODE_VERSION_0X0B
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1) {
                    fun support(): Boolean {
                        if (it.pactType >= 10 && it.pactCode >= 1) {
                            return true
                        }
                        return it.sku == "H6102" && BleUtils.isHigherVersion(
                            it.versionSoft,
                            "1.06.00"
                        ) && BleUtils.isHigherVersion(it.versionHard, "1.00.03")
                    }
                    if (support()) {
                        COLOR_MODE_VERSION_0X15
                    } else {
                        COLOR_MODE_VERSION_0X0B
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA) {
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_HEATER_h6079) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6641) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6640) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1) {
                    if (it.sku != "H6163" && it.sku != "H6117") {
                        COLOR_MODE_VERSION_0X15
                    } else {
                        if (it.pactType >= 2 && it.pactCode >= 1) {
                            COLOR_MODE_VERSION_0X15
                        } else if (BleUtils.isHigherVersion(it.versionHard, "1.00.02") &&
                            BleUtils.isHigherVersion(it.versionSoft, "1.06.00")
                        ) {
                            COLOR_MODE_VERSION_0X15
                        } else {
                            COLOR_MODE_VERSION_0X0B
                        }
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1) {
                    if (BleUtils.isHigherVersion(
                            it.versionHard,
                            "1.00.01"
                        ) && BleUtils.isHigherVersion(it.versionSoft, "1.06.00")
                    ) {
                        COLOR_MODE_VERSION_0X15
                    } else {
                        COLOR_MODE_VERSION_0X0B
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D1) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D2) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D3) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_HEATER_h6079) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7057) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7058) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C) { _ ->
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_H605A) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70C4_5_6_7_8_9) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT) { _ ->
                    COLOR_MODE_VERSION_0X15_NO_TEMP_RGB
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_V2) { _ ->
                    COLOR_MODE_VERSION_0X15_NO_TEMP_RGB
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_606A) { _ ->
                    COLOR_MODE_VERSION_0X15_NO_TEMP_RGB
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6063) { _ ->
                    COLOR_MODE_VERSION_0X15_NO_TEMP_RGB
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6069) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_TRIANGLE) { _ ->
                    COLOR_MODE_VERSION_0X15_NO_TEMP_RGB
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_Y) { _ ->
                    COLOR_MODE_VERSION_0X15_NO_TEMP_RGB
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_RGBIC_LIGHT_V1) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V2) {
                    if (it.pactType == 1) {
                        COLOR_MODE_VERSION_0X0B_KEVIN
                    } else {
                        COLOR_MODE_VERSION_0X15
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6800) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6097) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6810) {
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6811) {
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6039) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6038) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H703A_B) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_PC_LIGHT) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6608) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP) {
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7085) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H70C1_H70C2) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6099) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6690) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H66A0) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6042) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODES_TYPE_NO_SUPPORT) {
                    when (it.sku) {
                        "H6185" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6119" -> {
                            COLOR_MODE_VERSION_0X15
                        }

                        "H6107" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6105" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6129" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6001" -> {
                            if (OldBulbUtil.isH6001Update(
                                    it.sku,
                                    it.pactType,
                                    it.pactCode
                                )
                            ) {
                                COLOR_MODE_VERSION_0X0D
                            } else {
                                COLOR_MODE_VERSION_0X02
                            }
                        }

                        "H6160" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6181" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6182" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6109" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H614A" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6104" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6110" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6113" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6114" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6138" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6139" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H614B" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H614E" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6159" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6197" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        "H6188" -> {
                            COLOR_MODE_VERSION_0X0D
                        }

                        "H6165", "H6166" -> {
                            COLOR_MODE_VERSION_0X02
                        }

                        else -> {
                            VERSION_NOT_SUPPORT
                        }
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT_HDMI) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_H6603) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_AI_HDMI_H6604) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H601EF) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6821) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6820) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6840) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6841) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_TABLE_LAMP_BLE_WIFI_V1) {
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGB_BLE_WIFI_CHILDREN_LAMP_V1) {
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7093) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7094) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H8066) {
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_H7030) { _ ->
                    COLOR_MODE_VERSION_0X6E
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70C4_5_6_7_8_9) {
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) {
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) {
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6020) {
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_H616C) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H612x) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_DESK_LIGHT) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1) {
                    COLOR_MODE_VERSION_0X0B
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAMPING_LIGHT_BLE_V1) {
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1) {
                    COLOR_MODE_VERSION_0X15
                }//H617A
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1) {
                    COLOR_MODE_VERSION_0X15
                }//H618A
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2) {
                    COLOR_MODE_VERSION_0X15
                }//H619A
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_BLE_WIFI_V1) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H8015) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGB_FLUSH_LIGHT_PANEL) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H608a) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_MUSIC_LIGHT_BAR) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2) { info ->
                    val supportPartBrightness =
                        Support4DreamColorLightV2.dreamColorLightV2SupportSubModeColor4PartBrightness(
                            info.goodsType,
                            info.versionSoft,
                            info.versionHard
                        )
                    var colorVersion = COLOR_MODE_VERSION_0X02
                    if (supportPartBrightness) colorVersion = COLOR_MODE_VERSION_0X15
                    colorVersion
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END) { info ->
                    val supportPartBrightness =
                        Support4DreamColorV1.supportPartBrightness(
                            info.goodsType,
                            info.pactType,
                            info.pactCode,
                            info.sku,
                            info.versionSoft,
                            info.versionHard,
                            info.wifiSoftVersion,
                        )
                    var colorVersion = COLOR_MODE_VERSION_0X02
                    if (supportPartBrightness == 2) colorVersion = COLOR_MODE_VERSION_0X15
                    else if (Support4DreamColorV1.supportDreamcolorlightv1(info.goodsType)) colorVersion =
                        COLOR_MODE_VERSION_0X0B
                    colorVersion
                }
                put(GoodsType.GOODS_TYPE_VALUE_LED_BULB_BLE_V1) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_MOVE_ATMOSPHERE_LIGHT_V2) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3) {
                    COLOR_MODE_VERSION_0X15
                }//H619Z、H6167
                put(GoodsType.GOODS_TYPE_VALUE_H61A9) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_LIGHT_BLE_V1) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT) {
                    COLOR_MODE_VERSION_0X15
                }//B7080
                put(GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_8) {
                    COLOR_MODE_VERSION_0X15
                }//B7081
                put(GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_6) {
                    COLOR_MODE_VERSION_0X15
                }//B7082
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_CHILD_LIGHT) {
                    COLOR_MODE_VERSION_0X0D
                }//H6057
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_MOVE_ATMOSPHERE_LIGHT) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_VERTICAL_HOLLOW_LAMP_BLE_WIFI_V1) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_BED_LIGHT_BLE_WIFI_V1) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7086) {
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_HOME_LIGHT_RGBWW_V1) { //6148
                    COLOR_MODE_VERSION_0X0B
                }

                put(GoodsType.GOODS_TYPE_VALUE_UPGLOW_FLOOR_LAMP_WW_BLE_WIFI_V1) { //6075
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_GOOSENECK_FLOOR_LAMP_BLE_WIFI_V1) { //6071
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_UPGLOW_LAMP_BLE_WIFI_V1) { //6073
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_BLE_V1) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }

                put(GoodsType.GOODS_TYPE_VALUE_STRING_LIGHT_BLE_WIFI_V2) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }

                put(GoodsType.GOODS_TYPE_VALUE_STRING_LIGHT_BLE_V2) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_ALEXA_RGB) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_ALEXA_RGB_LIMIT) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }

                put(GoodsType.GOODS_TYPE_VALUE_BK_BLE_RGB) { info ->
                    val old = NewRgb2TLUtil.isNewRgb2TL(info.sku, info.pactType, info.pactCode)
                    if (old) {
                        COLOR_MODE_VERSION_0X02
                    } else {
                        COLOR_MODE_VERSION_0X0D
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_BK_BLE_WIFI_RGB) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGB_LIGHT) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60B1) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_IOT_V1) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7076) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60B2) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6048) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A4) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7026) {
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_H7056) { _ ->
                    COLOR_MODE_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60A6) { info ->
                    if (info.pactType == GoodsType.PACT_TYPE_VALUE_H60A6 && info.pactCode >= GoodsType.PACT_CODE_VALUE_H60A6_2) {
                        COLOR_MODE_VERSION_0X15
                    } else {
                        COLOR_MODE_VERSION_0X0D
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7087) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B6) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60C1) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H707ABC) {
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6671_72) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7073) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7070) { _ ->
                    COLOR_MODE_VERSION_0X15
                }
            }

        /**配置每个GoodsType的相对亮度指令版本配置*/
        private val relativeBrightnessBytes4SkuCompareMap =
            hashMapOf<String, (info: Info4BleIotDevice) -> Int>().apply {
                put("H6163") {
                    if (it.pactType >= 2 && it.pactCode >= 1) {
                        RELATIVE_BRIGHTNESS_VERSION_0X15
                    } else if (BleUtils.isHigherVersion(it.versionHard, "1.00.02") &&
                        BleUtils.isHigherVersion(it.versionSoft, "1.06.00")
                    ) {
                        RELATIVE_BRIGHTNESS_VERSION_0X15
                    } else {
                        VERSION_NOT_SUPPORT
                    }
                }
                put("H6117") {
                    if (it.pactType >= 2 && it.pactCode >= 1) {
                        RELATIVE_BRIGHTNESS_VERSION_0X15
                    } else if (BleUtils.isHigherVersion(it.versionHard, "1.00.02") &&
                        BleUtils.isHigherVersion(it.versionSoft, "1.06.00")
                    ) {
                        RELATIVE_BRIGHTNESS_VERSION_0X15
                    } else {
                        VERSION_NOT_SUPPORT
                    }
                }
            }

        /**配置每个GoodsType的相对亮度指令版本配置*/
        private val relativeBrightnessBytes4GoodsTypeCompareMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Int>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_H7086) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7025) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6020) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_H7095) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7066) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_SHOOT_LIGHT_V1) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT_V2) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7052) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7053) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7063) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_LED_PATH_LIGHTS) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7037) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H702ABC) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_h7033) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB_V2) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V2) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V3) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V4) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_RGBWW_BALL_LIGHT) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT_V2) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_FLOOD_LIGHT) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7075) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6022) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7078) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7072) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6641) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6640) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7085) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E5) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E6) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61B0_B3_B6) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61F6) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61b8) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) { info ->
                    val versionSoft = info.versionSoft
                    val versionHard = info.versionHard
                    SafeLog.i(TAG) { "relativeBrightnessBytes4GoodsTypeCompareMap() goodsType =${info.goodsType} ; versionSoft  =$versionSoft ; versionHard = $versionHard" }
                    if (BleUtils.isHigherVersion(
                            versionSoft, "1.00.11"
                        ) && BleUtils.isHigherVersion(versionHard, "1.00.01")
                    ) {
                        RELATIVE_BRIGHTNESS_VERSION_0X15
                    } else {
                        VERSION_NOT_SUPPORT
                    }

                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A1) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A0) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61D3_D5) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_HEATER_h6079) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6641) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6640) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1) {
                    fun support(): Boolean {
                        if (it.pactType >= 10 && it.pactCode >= 1) {
                            return true
                        }
                        return BleUtils.isHigherVersion(
                            it.versionSoft,
                            "1.06.00"
                        ) && BleUtils.isHigherVersion(it.versionHard, "1.00.03")
                    }
                    if (support()) {
                        RELATIVE_BRIGHTNESS_VERSION_0X15
                    } else {
                        VERSION_NOT_SUPPORT
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1) {
                    fun support(): Boolean {
                        if (it.pactType >= 10 && it.pactCode >= 1) {
                            return true
                        }
                        return it.sku == "H6102" && BleUtils.isHigherVersion(
                            it.versionSoft,
                            "1.06.00"
                        ) && BleUtils.isHigherVersion(it.versionHard, "1.00.03")
                    }
                    if (support()) {
                        RELATIVE_BRIGHTNESS_VERSION_0X15
                    } else {
                        VERSION_NOT_SUPPORT
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7067_68_69) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H705DEF_H805ABC) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_OUTDOOR_LAMP) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H706A_B_C) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70C4_5_6_7_8_9) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_H605A) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_RGBIC_LIGHT_V1) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_HEATER_h6079) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1) {
                    if (it.sku != "H6163" && it.sku != "H6117") {
                        RELATIVE_BRIGHTNESS_VERSION_0X15
                    } else {
                        if (it.pactType >= 2 && it.pactCode >= 1) {
                            RELATIVE_BRIGHTNESS_VERSION_0X15
                        } else if (BleUtils.isHigherVersion(it.versionHard, "1.00.02") &&
                            BleUtils.isHigherVersion(it.versionSoft, "1.06.00")
                        ) {
                            RELATIVE_BRIGHTNESS_VERSION_0X15
                        } else {
                            VERSION_NOT_SUPPORT
                        }
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1) {
                    if (BleUtils.isHigherVersion(
                            it.versionHard,
                            "1.00.01"
                        ) && BleUtils.isHigherVersion(it.versionSoft, "1.06.00")
                    ) {
                        RELATIVE_BRIGHTNESS_VERSION_0X15
                    } else {
                        VERSION_NOT_SUPPORT
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7057) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7058) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_V2) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_606A) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6063) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6069) { _ ->
                    COLOR_MODE_VERSION_0X0D
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_TRIANGLE) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_Y) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V2) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6097) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D1) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D2) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D3) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H703A_B) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6039) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6038) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_PC_LIGHT) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6608) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP) {
                    VERSION_NOT_SUPPORT
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H70C1_H70C2) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6099) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6690) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H66A0) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT_HDMI) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_H6603) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_AI_HDMI_H6604) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H601EF) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6042) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V4) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V2) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_ATMOSPHERE_LIGHT) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_ATMOSPHERE_LIGHT_V2) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_GAME_FEAST) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6043) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A4) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7093) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7094) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H8066) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7030) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H616C) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H612x) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_DESK_LIGHT) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1) {
                    VERSION_NOT_SUPPORT
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAMPING_LIGHT_BLE_V1) {
                    VERSION_NOT_SUPPORT
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }//H617A
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }//H618A
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }//H619A
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H608a) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_MUSIC_LIGHT_BAR) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }//H619Z、H6167
                put(GoodsType.GOODS_TYPE_VALUE_H61A9) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }//B7080
                put(GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_8) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }//B7081
                put(GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_6) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }//B7082
                put(GoodsType.GOODS_TYPE_VALUE_H7076) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60B2) { _ ->
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60B1) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }//H60B1
                put(GoodsType.GOODS_TYPE_VALUE_H6048) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                //H60C1
                put(GoodsType.GOODS_TYPE_VALUE_H60C1) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A6) {
                    if (it.pactType == GoodsType.PACT_TYPE_VALUE_H60A6 && it.pactCode >= GoodsType.PACT_CODE_VALUE_H60A6_2) {
                        RELATIVE_BRIGHTNESS_VERSION_0X15
                    } else {
                        VERSION_NOT_SUPPORT
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7026) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7056) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }//H7056
                put(GoodsType.GOODS_TYPE_VALUE_H7087) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }//H7087
                put(GoodsType.GOODS_TYPE_VALUE_H707ABC) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6671_72) {
                    RELATIVE_BRIGHTNESS_VERSION_0X15
                }
            }

        /**
         * 非必要配置，sku支持颜色分组指令 0x33 0x05 0x14 0x03+1byte第几组+3bytesRgb+3byteRGB+3N..;相对亮度分组 0x33 0x05 0x14 0x04+1byteBrightness+1byteBrightness+N...
         * 配置每个GoodsType的颜色及相对亮度支持分组整组设置的版本配置
         */
        private val supportGroupColorAndBrightnessGoodsTypeCompareMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Int>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A1) { _ ->
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A0) { _ ->
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C) { _ ->
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_H705DEF_H805ABC) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_H706A_B_C) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7067_68_69) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_PC_LIGHT) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6099) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6690) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_H66A0) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT_HDMI) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_H6603) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_AI_HDMI_H6604) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_H601EF) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A4) { _ ->
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60C1) { _ ->
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A6) { _ ->
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_V2) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_606A) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6063) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_TRIANGLE) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_Y) {
                    COLOR_BRIGHTNESS_GROUP_1
                }
                put(GoodsType.GOODS_TYPE_VALUE_H707ABC) { _ ->
                    COLOR_BRIGHTNESS_GROUP_1
                }
            }

        private val supportGroupBrightnessSpecialGoodsTypeCompareMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Boolean>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A1) { _ ->
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61b8) { _ ->
                    true
                }
            }

        /**
         * 非必要配置，如sku支持颜色多包，才需要配置
         * 配置每个GoodsType的颜色及相对亮度支持多包的配置
         */
        private val supportMultiColorAndBrightnessGoodsTypeCompareMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Boolean>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_OUTDOOR_LAMP) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1) {
                    false
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1) {
                    false
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_RGBIC_LIGHT_V1) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6608) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7037) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H702ABC) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H616C) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H612x) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61A9) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7030) {
                    true
                }

                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V2) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB_V2) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7052) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7053) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6048) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
                    it.pactType == GoodsType.PACT_TYPE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V3
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_h7033) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7067_68_69) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7025) {
                    true
                }
            }

        /**
         * 非必要配置，如sku支持颜色多包，才需要配置
         * 配置每个GoodsType的颜色及相对亮度支持多包的配置
         */
        private val supportMultiColorTemAndBrightnessGoodsTypeCompareMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Boolean>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_H7026) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H702ABC) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60C1) {
                    true
                }
            }

        /**
         * 非必要配置，sku颜色分段超过56（>56）段才设置，同时需要配置${supportMultiColorAndBrightnessGoodsTypeCompareMap}
         * 配置每个GoodsType的颜色及相对亮度支持多包的配置
         *
         */
        private val supportMultiColorAndColorPieceOver56GoodsTypeCompareMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Boolean>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_H7030) {
                    true
                }
            }

        private val supportRealTempGoodsTypeCompareMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Boolean>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C) {
                    true
                }
            }


        /**配置每个GoodsType的渐变指令版本配置*/
        private val gradualBytes4GoodsTypeCompareMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Int>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_H6020) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7086) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_H7095) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7075) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6022) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7078) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7072) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6641) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6640) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7085) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E5) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E6) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61B0_B3_B6) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61F6) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61b8) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A1) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H705DEF_H805ABC) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_OUTDOOR_LAMP) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H706A_B_C) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7067_68_69) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A0) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61D3_D5) { _ ->
                    GRADUAL_VERSION_0xA3
                }

                put(GoodsType.GOODS_TYPE_VALUE_H605A) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_RGBIC_LIGHT_V1) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6641) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6640) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1) {
                    if (it.pactType >= 10) {
                        GRADUAL_VERSION_0xA3
                    } else {
                        GRADUAL_VERSION_0x14
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1) {
                    if (it.pactType >= 10) {
                        GRADUAL_VERSION_0xA3
                    } else {
                        GRADUAL_VERSION_0x14
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_HEATER_h6079) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA) {
                    VERSION_NOT_SUPPORT
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70C4_5_6_7_8_9) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_HEATER_h6079) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1) {
                    GRADUAL_VERSION_0x14
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7057) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7058) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V2) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6097) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D1) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D2) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D3) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6039) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6038) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_PC_LIGHT) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6608) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP) {
                    VERSION_NOT_SUPPORT
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H70C1_H70C2) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6099) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6690) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H66A0) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT_HDMI) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_H6603) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_AI_HDMI_H6604) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H601EF) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_GAME_FEAST) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6043) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6042) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7093) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7094) {
                    GRADUAL_VERSION_0xA3
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) {
                    GRADUAL_VERSION_0xA3
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) {
                    GRADUAL_VERSION_0xA3
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H616C) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H612x) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_DESK_LIGHT) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1) {
                    VERSION_NOT_SUPPORT
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAMPING_LIGHT_BLE_V1) {
                    VERSION_NOT_SUPPORT
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H608a) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_MUSIC_LIGHT_BAR) { _ ->
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61A9) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7030) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60B2) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6048) {
                    GRADUAL_VERSION_0xA3
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60B1) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60C1) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7076) {
                    GRADUAL_VERSION_0xA3
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60B1) {
                    GRADUAL_VERSION_0xA3
                }

                put(GoodsType.GOODS_TYPE_VALUE_H7056) {
                    GRADUAL_VERSION_0xA3
                }
                put(GoodsType.GOODS_TYPE_VALUE_H707ABC) {
                    GRADUAL_VERSION_0xA3
                }

                put(GoodsType.GOODS_TYPE_VALUE_H6671_72) {
                    GRADUAL_VERSION_0xA3
                }
            }

        private val normalFuc4SkuCompareMap =
            hashMapOf<String, (info: Info4BleIotDevice) -> BooleanArray>().apply {
                put("H7022") {
                    booleanArrayOf(false, false, false, false, true)
                }
                put("H7024") {
                    booleanArrayOf(false, false, false, false, true)
                }
                put("H7001") {
                    booleanArrayOf(false, false, false, false, false)
                }
                put("H6001") {
                    booleanArrayOf(false, false, false, false, true)
                }
            }

        /**
         * 配置每个GoodsType的通用配置
         * BooleanArray(5)【0】-是否支持渐变;【1】-是否支持推荐配色；【2】-是否支持无色色块；【3】-是否支持分段亮度;【4】-是否支持色温
         */
        private val normalFuc4GoodsTypeCompareMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> BooleanArray>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_H7026) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7025) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7066) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_SHOOT_LIGHT_V1) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT_V2) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7052) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7053) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7063) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_LED_PATH_LIGHTS) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7037) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H702ABC) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_h7033) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB_V2) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V2) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V3) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V4) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_RGBWW_BALL_LIGHT) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT_V2) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_FLOOD_LIGHT) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6097) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7075) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6022) { _ ->
                    booleanArrayOf(true, true, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7078) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7072) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6641) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6640) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7085) { _ ->
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E5) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E6) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61B0_B3_B6) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61F6) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61F6) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61b8) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) { info ->
                    val versionSoft = info.versionSoft
                    val versionHard = info.versionHard
                    SafeLog.i(TAG) { "normalFuc4GoodsTypeCompareMap() goodsType =${info.goodsType} ; versionSoft  =$versionSoft ; versionHard = $versionHard" }
                    val supportPartBrightness = BleUtils.isHigherVersion(
                        versionSoft, "1.00.11"
                    ) && BleUtils.isHigherVersion(versionHard, "1.00.01")
                    SafeLog.i(TAG) { "normalFuc4GoodsTypeCompareMap() supportPartBrightness = $supportPartBrightness" }
                    booleanArrayOf(true, true, true, supportPartBrightness, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A1) { _ ->
                    booleanArrayOf(false, true, true, true, false)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61D3_D5) { _ ->
                    booleanArrayOf(true, true, true, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6810) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6811) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A0) { _ ->
                    booleanArrayOf(false, true, true, true, false)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B3) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B4) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B5) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70BC) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H705DEF_H805ABC) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_OUTDOOR_LAMP) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H706A_B_C) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7067_68_69) { _ ->
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_IOT_V1) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_LED_BULB_BLE_V1) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1) {
                    fun supportPartBrightness(): Boolean {
                        if (it.pactType >= 10 && it.pactCode >= 1) {
                            return true
                        }
                        return BleUtils.isHigherVersion(
                            it.versionSoft,
                            "1.06.00"
                        ) && BleUtils.isHigherVersion(it.versionHard, "1.00.03")
                    }
                    booleanArrayOf(true, true, true, supportPartBrightness(), true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1) {
                    fun supportPartBrightness(): Boolean {
                        if (it.pactType >= 10 && it.pactCode >= 1) {
                            return true
                        }
                        return it.sku == "H6102" && BleUtils.isHigherVersion(
                            it.versionSoft,
                            "1.06.00"
                        ) && BleUtils.isHigherVersion(it.versionHard, "1.00.03")
                    }
                    booleanArrayOf(true, true, true, supportPartBrightness(), true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
                    fun supportPartBrightness(): Boolean {
                        if (it.pactType >= 3 && it.pactCode >= 2) {
                            return true
                        }
                        return BleUtils.isHigherVersion(
                            it.versionSoft,
                            "1.06.00"
                        ) && BleUtils.isHigherVersion(it.versionHard, "1.00.02")
                    }
                    booleanArrayOf(true, true, true, supportPartBrightness(), true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_HEATER_h6079) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6641) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6640) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70C4_5_6_7_8_9) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET) {
                    booleanArrayOf(true, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_H605A) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_RGBIC_LIGHT_V1) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1) {
                    val supportPartBrightness = if (it.sku != "H6163" && it.sku != "H6117") {
                        true
                    } else {
                        if (it.pactType >= 2 && it.pactCode >= 1) {
                            true
                        } else BleUtils.isHigherVersion(it.versionHard, "1.00.02") &&
                            BleUtils.isHigherVersion(it.versionSoft, "1.06.00")
                    }
                    booleanArrayOf(true, true, true, supportPartBrightness, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1) {
                    val supportPartBrightness = BleUtils.isHigherVersion(
                        it.versionHard,
                        "1.00.01"
                    ) && BleUtils.isHigherVersion(it.versionSoft, "1.06.00")
                    booleanArrayOf(true, true, true, supportPartBrightness, true)
                }


                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7057) { _ ->
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7058) { _ ->
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D1) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D2) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D3) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT) { _ ->
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_V2) { _ ->
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_606A) { _ ->
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6063) { _ ->
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6069) { _ ->
                    booleanArrayOf(false, true, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_TRIANGLE) { _ ->
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_Y) { _ ->
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V2) {
                    val brightness = !(it.pactType == 1 && it.pactCode == 1)
                    booleanArrayOf(true, true, true, brightness, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRING_LIGHT_BLE_V2) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BK_BLE_RGB) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_ALEXA_RGB) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_ALEXA_RGB_LIMIT) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BK_BLE_WIFI_RGB) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_CAR_LIGHT) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGB_LIGHT) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRING_LIGHT_BLE_WIFI_V2) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODES_TYPE_NO_SUPPORT) { info ->
                    when (info.sku) {
                        "H6119" -> {
                            val supportPartBrightness =
                                BleUtils.isHigherVersion(info.versionHard, "1.00.03") &&
                                    BleUtils.isHigherVersion(info.versionSoft, "1.06.00")
                            booleanArrayOf(
                                true,
                                true,
                                true,
                                supportPartBrightness,
                                supportPartBrightness
                            )
                        }

                        "H7310", "H7312", "H7317", "H7318" -> {
                            booleanArrayOf(false, false, false, false, false)
                        }

                        else -> {
                            val supportTem =
                                (info.sku == "H6109" || info.sku == "H614A" || info.sku == "H6185" || info.sku == "H6107" || info.sku == "H6105" || info.sku == "H6129")
                            booleanArrayOf(false, false, false, false, supportTem)
                        }
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H8015) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGB_FLUSH_LIGHT_PANEL) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6800) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B6) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H703A_B) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_BLE_V1) { _ ->
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6840) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6841) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6039) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6038) { _ ->
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_PC_LIGHT) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6608) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H70C1_H70C2) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6099) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6690) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H66A0) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT_HDMI) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_H6603) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_AI_HDMI_H6604) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H601EF) {
                    booleanArrayOf(false, true, true, true, it.pactType == 1 && it.pactCode > 1)
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V4) {
                    val isNewColor =
                        it.versionHard == "1.00.00"
                            && BleUtil.parseVersion(it.versionSoft) >= 10014
                            && BleUtil.parseVersion(it.wifiSoftVersion) >= 10018
                    if (it.pactType == 1 && it.pactCode == 1 && !isNewColor) {
                        booleanArrayOf(false, true, true, false, true)
                    } else {
                        booleanArrayOf(false, true, true, true, true)
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V2) {
                    val isNewColor =
                        it.versionHard == "1.00.00"
                            && BleUtil.parseVersion(it.versionSoft) >= 10012
                    if (it.pactType == 1 && it.pactCode == 1 && !isNewColor) {
                        booleanArrayOf(false, false, true, false, true)
                    } else {
                        booleanArrayOf(false, true, true, true, true)
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_ATMOSPHERE_LIGHT) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_ATMOSPHERE_LIGHT_V2) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_GAME_FEAST) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6043) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6042) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6820) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6821) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1) {
                    val supportPartBrightness = BleUtils.isHigherVersion(
                        it.versionHard,
                        "1.00.01"
                    ) && BleUtils.isHigherVersion(it.versionSoft, "1.06.00")
                    booleanArrayOf(true, true, true, supportPartBrightness, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_TABLE_LAMP_BLE_WIFI_V1) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGB_BLE_WIFI_CHILDREN_LAMP_V1) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_H7095) {
                    booleanArrayOf(true, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) {
                    booleanArrayOf(true, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) {
                    booleanArrayOf(true, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6020) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H616C) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H612x) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_DESK_LIGHT) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAMPING_LIGHT_BLE_V1) {
                    booleanArrayOf(false, false, false, false, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1) {
                    booleanArrayOf(false, true, true, true, true)
                }//H617A
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1) {
                    booleanArrayOf(false, true, true, true, true)
                }//H618A
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2) {
                    booleanArrayOf(true, true, true, true, true)
                }//H619A
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61A9) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT) {
                    booleanArrayOf(false, false, false, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_8) {
                    booleanArrayOf(false, false, false, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_6) {
                    booleanArrayOf(false, false, false, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7086) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7093) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7094) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H8066) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7030) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6048) {
                    booleanArrayOf(true, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60B1) {
                    booleanArrayOf(false, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_UPGLOW_FLOOR_LAMP_WW_BLE_WIFI_V1) { //6075
                    booleanArrayOf(false, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_GOOSENECK_FLOOR_LAMP_BLE_WIFI_V1) { //6071
                    booleanArrayOf(false, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_UPGLOW_LAMP_BLE_WIFI_V1) { //6073
                    booleanArrayOf(false, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_HOME_LIGHT_RGBWW_V1) { //6148
                    booleanArrayOf(false, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_H7056) {
                    booleanArrayOf(false, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_H7072) {
                    booleanArrayOf(false, true, true, true, false)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7076) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60B2) {
                    booleanArrayOf(false, true, true, true, true)
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60B0) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A4) {
                    booleanArrayOf(false, true, true, true, false)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A6) { info ->
                    val supportPartColor =
                        info.pactType == GoodsType.PACT_TYPE_VALUE_H60A6 && info.pactCode >= GoodsType.PACT_CODE_VALUE_H60A6_2
                    //旧版本支持色温，支持分段颜色的迭代版本不支持色温,在后续版本又支持分段色温，总结起来就是pactCode=2版本不支持分段色温
                    val supportTem1 = info.pactType == GoodsType.PACT_TYPE_VALUE_H60A6 && info.pactCode == GoodsType.PACT_CODE_VALUE_H60A6
                    val supportTem2 = info.pactType == GoodsType.PACT_TYPE_VALUE_H60A6 && info.pactCode >= GoodsType.PACT_CODE_VALUE_H60A6_3
                    booleanArrayOf(false, supportPartColor, supportPartColor, supportPartColor, supportTem1 || supportTem2)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7087) {
                    booleanArrayOf(false, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60C1) {
                    booleanArrayOf(false, true, true, true, false)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H707ABC) {
                    booleanArrayOf(true, true, true, true, true)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6671_72) {
                    booleanArrayOf(true, true, true, true, true)
                }
            }


        /**
         * 检测通用配置
         */
        fun checkNormalFucConfig(info: Info4BleIotDevice): BooleanArray? {
            SafeLog.i(TAG) { "checkNormalFucConfig() info = ${info.fucKey()}" }
            /**是否是依据sku进行配置的*/
            normalFuc4SkuCompareMap[info.sku]?.let {
                return it.invoke(info)
            }
            /**是否是依据goodsType进行配置的*/
            normalFuc4GoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(info)
            }
            return null
        }

        /**
         * 检测颜色指令的版本
         */
        fun checkColorBytesVersion(info: Info4BleIotDevice): Int? {
            SafeLog.i(TAG) { "checkColorBytesVersion() info = ${info.fucKey()}" }
            /** 是否依据 sku 进行配置的*/
            colorBytes4SkuCompareMap[info.sku]?.let {
                return it.invoke(info)
            }
            /**是否是依据goodsType进行配置的*/
            colorBytes4GoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(info)
            }
            return null
        }

        /**
         * 检测相对亮度指令的版本
         */
        fun checkRelativeBrightnessBytesVersion(info: Info4BleIotDevice): Int? {
            SafeLog.i(TAG) { "checkRelativeBrightnessBytesVersion() info = ${info.fucKey()}" }
            /**是否是依据sku进行配置的*/
            relativeBrightnessBytes4SkuCompareMap[info.sku]?.let {
                return it.invoke(info)
            }
            /**是否是依据goodsType进行配置的*/
            relativeBrightnessBytes4GoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(info)
            }
            return null
        }

        /**
         * 检测颜色相对亮度是否支持分组设置
         */
        fun checkSupportColorBrightnessGroup(info: Info4BleIotDevice): Int? {
            SafeLog.i(TAG) { "checkSupportColorBrightnessGroup() info = ${info.fucKey()}" }
            supportGroupColorAndBrightnessGoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(info)
            }
            return null
        }

        fun checkSupportBrightnessGroupSpecial(info: Info4BleIotDevice): Boolean? {
            SafeLog.i(TAG) { "checkSupportBrightnessGroupSpecial() info = ${info.fucKey()}" }
            supportGroupBrightnessSpecialGoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(info)
            }
            return null
        }

        /**
         * 检测颜色相对亮度是否支持多包设置
         */
        fun checkSupportColorBrightnessMulti(info: Info4BleIotDevice): Boolean {
            SafeLog.i(TAG) { "checkSupportColorBrightnessMulti() info = ${info.fucKey()}" }
            supportMultiColorAndBrightnessGoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(info)
            }
            return false
        }

        /**
         * 检测颜色相对亮度是否支持多包设置
         */
        fun checkSupportColorTemBrightnessMulti(info: Info4BleIotDevice): Boolean {
            SafeLog.i(TAG) { "checkSupportColorBrightnessMulti() info = ${info.fucKey()}" }
            supportMultiColorTemAndBrightnessGoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(info)
            }
            return false
        }

        /**
         * 检测颜色模式分段否超过56分段
         */
        fun checkSupportColorModeOver56Piece(info: Info4BleIotDevice): Boolean {
            SafeLog.i(TAG) { "checkSupportColorModeOver56Piece() info = ${info.fucKey()}" }
            supportMultiColorAndColorPieceOver56GoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(info)
            }
            return false
        }

        /**
         * 检测渐变指令的版本
         */
        fun checkGradualBytesVersion(info: Info4BleIotDevice): Int? {
            SafeLog.i(TAG) { "checkGradualBytesVersion() info = ${info.fucKey()}" }
            /**是否是依据goodsType进行配置的*/
            gradualBytes4GoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(info)
            }
            return null
        }

        fun checkSupportRealTempVersion(info: Info4BleIotDevice): Boolean? {
            SafeLog.i(TAG) { "checkSupportRealTempVersion() info = ${info.fucKey()}" }
            supportRealTempGoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(info)
            }
            return null
        }

        /**配置颜色指令构建的version的map*/
        val color4VersionBytesMap =
            hashMapOf<Int, (rgb: Int, posSet: BooleanArray, tempTypeRGB: Int) -> ByteArray>().apply {
                put(COLOR_MODE_VERSION_0X15) { rgb, posSet, tempTypeRGB ->
                    val colorTemp = Constant.isColorTemp(rgb)
                    if (colorTemp) {
                        val kelvin = Constant.getColorTemKelvin(rgb)
                        Bytes.colorTemByte0x15(rgb, kelvin, posSet, tempTypeRGB)
                    } else {
                        Bytes.colorByte0x15(rgb, posSet)
                    }
                }
                put(COLOR_MODE_VERSION_0X15_NO_TEMP_RGB) { rgb, posSet, tempTypeRGB ->
                    val colorTemp = Constant.isColorTemp(rgb)
                    if (colorTemp) {
                        val kelvin = Constant.getColorTemKelvin(rgb)
                        Bytes.colorTemByteNoRgb0x15(kelvin, posSet, rgb)
                    } else {
                        Bytes.colorByteNoTempRgb0x15(rgb, posSet)
                    }
                }
                put(COLOR_MODE_VERSION_0X14) { rgb, posSet, _ ->
                    val colorTemp = Constant.isColorTemp(rgb)
                    if (colorTemp) {
                        val kelvin = Constant.getColorTemKelvin(rgb)
                        Bytes.colorTemByte0x14(rgb, kelvin, posSet)
                    } else {
                        Bytes.colorByte0x14(rgb, posSet)
                    }
                }
                put(COLOR_MODE_VERSION_0X0D) { rgb, _, _ ->
                    val colorTemp = Constant.isColorTemp(rgb)
                    if (colorTemp) {
                        val kelvin = Constant.getColorTemKelvin(rgb)
                        Bytes.colorTemByte0x0D(rgb, kelvin)
                    } else {
                        Bytes.colorByte0x0D(rgb)
                    }
                }
                put(COLOR_MODE_VERSION_0X0B) { rgb, posSet, _ ->
                    Bytes.colorByte0x0B(rgb, posSet)
                }
                put(COLOR_MODE_VERSION_0X08) { rgb, posSet, _ ->
                    Bytes.colorByte0x08(rgb, posSet)
                }
                put(COLOR_MODE_VERSION_0X0B_KEVIN) { rgb, posSet, _ ->
                    val kelvin = Constant.getColorTemKelvin(rgb)
                    Bytes.colorByte0x0B(rgb, kelvin, posSet)
                }
                put(COLOR_MODE_VERSION_0X02) { rgb, _, _ ->
                    val colorTemp = Constant.isColorTemp(rgb)
                    if (colorTemp) {
                        Bytes.colorTemByte0x02(ColorUtils.toWhite(), rgb)
                    } else {
                        Bytes.colorByte0x02(rgb)
                    }
                }
                put(COLOR_MODE_VERSION_0X6E) { rgb, posSet, tempTypeRGB ->
                    val colorTemp = Constant.isColorTemp(rgb)
                    if (colorTemp) {
                        val kelvin = Constant.getColorTemKelvin(rgb)
                        Bytes.colorTemByte0x6E(rgb, kelvin, posSet, tempTypeRGB)
                    } else {
                        Bytes.colorByte0x6E(rgb, posSet)
                    }
                }
            }

        /**配置相对亮度指令构建的version的map - 全段设置*/
        val relativeAllBrightness4VersionMap =
            hashMapOf<Int, (brightnessSet: IntArray) -> ByteArray>().apply {
                put(RELATIVE_BRIGHTNESS_VERSION_0X15) { brightnessSet ->
                    Bytes.relativeAllBrightnessByte0x15(brightnessSet)
                }
            }

        /**配置相对亮度指令构建的version的map - 单亮度设置**/
        val relativeBrightness4VersionMap =
            hashMapOf<Int, (brightness: Int, posSet: BooleanArray) -> ByteArray>().apply {
                put(RELATIVE_BRIGHTNESS_VERSION_0X15) { brightness, posSet ->
                    Bytes.relativeBrightnessByte0x15(brightness, posSet)
                }
            }

        /**配置渐变指令构建的version的map*/
        val gradual4VersionMap = hashMapOf<Int, (gradual: Boolean) -> ByteArray>().apply {
            put(GRADUAL_VERSION_0xA3) { gradual ->
                Bytes.gradualByte0xa3(gradual)
            }
            put(GRADUAL_VERSION_0x14) { gradual ->
                Bytes.gradualByte0x14(gradual)
            }
        }

        /**配置色温指令构建的version的map - 全段设置*/
        val kelvinAll4VersionMap =
            hashMapOf<Int, (colorPiece: Int, kelvin: Int, tempRGBType: Int) -> ByteArray>().apply {
                put((COLOR_MODE_VERSION_0X15)) { colorPiece, kelvin, tempRGBType ->
                    Bytes.kelvinByte0x15(kelvin, colorPiece, tempRGBType)
                }
                put(COLOR_MODE_VERSION_0X14) { colorPiece, kelvin, _ ->
                    Bytes.kelvinByte0x14(kelvin, colorPiece)
                }
                put(COLOR_MODE_VERSION_0X0D) { _, kelvin, tempRGBType ->
                    Bytes.kelvinByte0x0D(kelvin, tempRGBType)
                }
                put(COLOR_MODE_VERSION_0X0B) { colorPiece, kelvin, _ ->
                    Bytes.kelvinByte0x0B(kelvin, colorPiece)
                }
                put(COLOR_MODE_VERSION_0X02) { _, kelvin, _ ->
                    Bytes.kelvinByte0x02(kelvin)
                }
            }


        //</editor-fold>
        private val colorTemType4SkuCompareMap =
            hashMapOf<String, (info: Info4BleIotDevice) -> Int>().apply {
                put("H7022") {
                    CmdColorWc.COLOR_TEM_TYPE_NOT_SUPPORT
                }
                put("H7024") {
                    CmdColorWc.COLOR_TEM_TYPE_NOT_SUPPORT
                }
                put("H614E") {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
            }

        /**配置发送iot的色温指令时，rgb填充类型*/
        private val colorTemType4GoodsTypeCompareMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Int>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A1) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61D3_D5) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B3) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B4) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B5) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70BC) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A0) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H705DEF_H805ABC) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_OUTDOOR_LAMP) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H706A_B_C) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_BLACK
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7067_68_69) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6810) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6811) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6022) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_BLACK
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E5) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_SELF_TEM_COLOR
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E6) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_SELF_TEM_COLOR
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61F6) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61b8) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_HEATER_h6079) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D1) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D2) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D3) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6097) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_H605A) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6800) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B6) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_SELF
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_V2) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_SELF
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_606A) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_SELF
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6063) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_SELF
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6069) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_SELF
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_TRIANGLE) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_SELF
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_Y) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_SELF
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_RGBIC_LIGHT_V1) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V2) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7078) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7072) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6039) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6038) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6820) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6821) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7057) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7058) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7085) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H703A_B) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_PC_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6608) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6099) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6690) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H66A0) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT_HDMI) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_H6603) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_AI_HDMI_H6604) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H601EF) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6042) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_FLOOD_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT_V2) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7075) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_RGBWW_BALL_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V4) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V3) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V2) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB_V2) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7037) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H702ABC) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_h7033) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT_V2) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7052) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7053) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7063) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_LED_PATH_LIGHTS) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7066) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_SHOOT_LIGHT_V1) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H70C1_H70C2) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_H7095) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7086) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_MOVE_ATMOSPHERE_LIGHT_V2) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6020) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6840) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6841) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_TABLE_LAMP_BLE_WIFI_V1) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGB_BLE_WIFI_CHILDREN_LAMP_V1) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7030) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70C4_5_6_7_8_9) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A4) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A6) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H616C) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H612x) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_DESK_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }//H618A
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }//H619A
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_BLE_WIFI_V1) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H8015) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGB_FLUSH_LIGHT_PANEL) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H608a) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_MUSIC_LIGHT_BAR) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61A9) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_IOT_V1) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7025) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7093) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7094) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H8066) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_H6048) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60B1) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }


                put(GoodsType.GOODS_TYPE_VALUE_HOME_LIGHT_RGBWW_V1) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }


                put(GoodsType.GOODS_TYPE_VALUE_UPGLOW_FLOOR_LAMP_WW_BLE_WIFI_V1) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_GOOSENECK_FLOOR_LAMP_BLE_WIFI_V1) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }


                put(GoodsType.GOODS_TYPE_VALUE_UPGLOW_LAMP_BLE_WIFI_V1) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_BLACK
                }

                put(GoodsType.GOODS_TYPE_VALUE_H6640) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_CHILD_LIGHT) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }//H6057

                put(GoodsType.GOODS_TYPE_VALUE_H7076) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60B2) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_H61B0_B3_B6) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7087) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7026) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_H7056) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6641) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H707ABC) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6671_72) { _ ->
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60C1) {
                    CmdColorWc.COLOR_TEM_TYPE_RGB_WHITE
                }
            }

        fun checkColorTemType(info: Info4BleIotDevice): Int {
            SafeLog.i(TAG) { "checkColorTemType() info = ${info.fucKey()}" }
            colorTemType4SkuCompareMap[info.sku]?.let {
                return it.invoke(info)
            }
            colorTemType4GoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(info)
            }
            return 1
        }


        //<editor-fold desc="颜色模式指令-iot指令配置条件">
        private const val IOT_COLOR_CMD_4_COLOR_NO_SUPPORT = -1
        const val IOT_COLOR_CMD_4_COLOR_WC = 0
        private const val IOT_COLOR_CMD_4_COLOR_TEM = 1
        private const val IOT_COLOR_CMD_4_COLOR = 2
        private const val IOT_COLOR_CMD_4_BULB = 3
        private const val IOT_COLOR_CMD_4_MODE = 4
        private const val IOT_COLOR_CMD_4_COLOR_OLD = 6
        private const val IOT_COLOR_CMD_4_ONLY_COLOR = 7
        private const val IOT_COLOR_CMD_4_H6003 = 8

        private val colorModeIot4SkuCompareMap =
            hashMapOf<String, (isKelvin: Boolean, info: Info4BleIotDevice) -> Int>().apply {
                put("H7022") { _, _ ->
                    IOT_COLOR_CMD_4_BULB
                }
                put("H6109") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR
                }
                put("H614A") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR
                }
                put("H6104") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_OLD
                }
                put("H6110") { _, info ->
                    if (OldRgbBkUtil.isOldRgbBk4BleWifi(info.sku, info.pactType, info.pactCode)) {
                        IOT_COLOR_CMD_4_COLOR_WC
                    } else {
                        IOT_COLOR_CMD_4_COLOR
                    }
                }
                put("H6113") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put("H6114") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put("H6138") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put("H6139") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put("H614B") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put("H614E") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR
                }
                put("H6159") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put("H6197") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put("H6197") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put("H6188") { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_OLD
                }
                put("H6160") { _, _ ->
                    IOT_COLOR_CMD_4_ONLY_COLOR
                }
            }

        /**配置每个GoodsType的颜色模式iot版本配置*/
        private val colorModeIot4GoodsTypeCompareMap =
            hashMapOf<Int, (isKelvin: Boolean, info: Info4BleIotDevice) -> Int>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_H7086) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_MOVE_ATMOSPHERE_LIGHT_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7075) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6022) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7078) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7072) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6641) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6640) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7085) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E5) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61E6) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61B0_B3_B6) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61F6) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61F6) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61b8) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61D3_D5) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B3) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B4) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B5) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70BC) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6641) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6640) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A0) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H705DEF_H805ABC) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_OUTDOOR_LAMP) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H706A_B_C) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7067_68_69) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6810) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6811) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_HEATER_h6079) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70D3) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_HEATER_h6079) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1) { _, info ->
                    /**旧协议使用颜色cmd=bulb的iot指令*/
                    if (info.pactType == 1 && (info.pactCode == 1 || info.pactCode == 2)) {
                        IOT_COLOR_CMD_4_BULB
                    } else {
                        IOT_COLOR_CMD_4_COLOR_WC
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7057) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7058) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_H605A) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6800) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B6) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_606A) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6063) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6069) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_TRIANGLE) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_Y) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_RGBIC_LIGHT_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V2) { _, info ->
                    /**旧协议使用颜色cmd=bulb的iot指令*/
                    if (info.pactType == 1) {
                        IOT_COLOR_CMD_4_BULB
                    } else {
                        IOT_COLOR_CMD_4_COLOR_WC
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6097) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6039) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6038) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70C4_5_6_7_8_9) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6840) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6841) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6820) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6821) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H703A_B) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_FLOOD_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_PC_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6608) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H70C1_H70C2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6099) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6690) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H66A0) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT_HDMI) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_H6603) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_AI_HDMI_H6604) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H601EF) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6042) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_RGBWW_BALL_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V4) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V3) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_STRING_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7037) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H702ABC) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_h7033) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7052) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7053) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7063) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_LED_PATH_LIGHTS) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7066) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_SHOOT_LIGHT_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_TABLE_LAMP_BLE_WIFI_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_RGB_BLE_WIFI_CHILDREN_LAMP_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7025) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7030) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70C4_5_6_7_8_9) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A4) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H616C) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H612x) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_DESK_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }//H618A
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }//H619A
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_BLE_WIFI_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H8015) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGB_FLUSH_LIGHT_PANEL) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H608a) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_MUSIC_LIGHT_BAR) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61A9) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_IOT_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_OLD
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7093) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7094) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H8066) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRING_LIGHT_BLE_WIFI_V2) { _, info ->
                    if (info.pactType == 1 && (info.pactCode == 1 || info.pactCode == 2)) {
                        IOT_COLOR_CMD_4_MODE
                    } else {
                        IOT_COLOR_CMD_4_COLOR_WC
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_STRING_LIGHT_BLE_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_BLE_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_ALEXA_RGB) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_ALEXA_RGB_LIMIT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BK_BLE_RGB) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BK_BLE_WIFI_RGB) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGB_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }


                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60B1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_H6048) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }


                put(GoodsType.GOODS_TYPE_VALUE_H60B1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_HOME_LIGHT_RGBWW_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }


                put(GoodsType.GOODS_TYPE_VALUE_UPGLOW_FLOOR_LAMP_WW_BLE_WIFI_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_GOOSENECK_FLOOR_LAMP_BLE_WIFI_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_UPGLOW_LAMP_BLE_WIFI_V1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_CHILD_LIGHT) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }//H6057
                put(GoodsType.GOODS_TYPE_VALUE_H7076) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_IOT_V1) { _, _ ->
                    IOT_COLOR_CMD_4_H6003
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7026) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_H7056) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_H6020) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60B2) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60A6) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7087) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H707ABC) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60C1) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6671_72) { _, _ ->
                    IOT_COLOR_CMD_4_COLOR_WC
                }
            }

        fun checkColorModeIotVersion(isKelvin: Boolean, info: Info4BleIotDevice): Int? {
            SafeLog.i(TAG) { "checkColorModeIotVersion() isKelvin = $isKelvin ; info = ${info.fucKey()}" }
            /**是否是依据goodsType进行配置的*/
            colorModeIot4SkuCompareMap[info.sku]?.let {
                return it.invoke(isKelvin, info)
            }
            colorModeIot4GoodsTypeCompareMap[info.goodsType]?.let {
                return it.invoke(isKelvin, info)
            }
            return null
        }

        private fun cmd4OldBulbMaker(
            rgb: Int,
            kelvin: Int,
            info: Info4BleIotDevice
        ): AbsCmd4Op? {
            val finalRgb = if (kelvin > 0) {
                Constant.getTemColorByKelvin(kelvin)[2]
            } else {
                rgb
            }
            val version = checkColorBytesVersion(info)
            val extBytes = color4VersionBytesMap[version]?.let { maker ->
                val colorPiece = ColorPieceConfig.configColorPiece(info)
                val checkPre = BooleanArray(colorPiece)
                Arrays.fill(checkPre, true)
                maker.invoke(finalRgb, checkPre, checkColorTemType(info))
            } ?: return null
            ControllerMode.generateWriteControllerByExtBytes(extBytes).getValue()?.let {
                if (it.isNotEmpty()) {
                    return Old4CmdBulb.makeOldCmdBulb(it)
                }
            }
            return null
        }


        private fun cmd4ColorTemMaker(
            rgb: Int,
            kelvin: Int,
            info: Info4BleIotDevice
        ): AbsCmd4Op {
            return if (kelvin != 0) {
                com.govee.base2light.kt.iot.h6003.CmdColorTem(kelvin)
            } else {
                com.govee.base2light.kt.iot.h6003.CmdColor(rgb)
            }
        }

        private fun cmd4ColorWcMaker(
            checkRgbTem: Boolean,
            rgb: Int,
            kelvin: Int,
            info: Info4BleIotDevice
        ): AbsCmd4Op? {
            SafeLog.i(TAG) {
                "cmd4ColorWcMaker() \ncheckRgbTem = $checkRgbTem \nrgb = ${ColorUtils.getRgb(rgb)} \nkelvin = $kelvin \n info = ${info.fucKey()}"
            }
            val finalKelvin = if (kelvin > 0) kelvin else {
                if (checkRgbTem && ColorUtils.isColorTem(rgb)) {
                    Constant.getColorTemKelvin(rgb)
                } else {
                    0
                }
            }
            SafeLog.i(TAG) { "cmd4ColorWcMaker() finalKelvin = $finalKelvin" }
            if (finalKelvin > 0) {
                val colorTemType = colorTemType4GoodsTypeCompareMap[info.goodsType]?.invoke(info)
                SafeLog.i(TAG) { "cmd4ColorWcMaker() colorTemType = $colorTemType" }
                if (colorTemType == null) {
                    SafeLog.e(TAG) { "cmd4ColorWcMaker() 未配置色温iot指令的类型!" }
                    return null
                }
                if (colorTemType == CmdColorWc.COLOR_TEM_TYPE_NOT_SUPPORT) {
                    SafeLog.e(TAG) { "cmd4ColorWcMaker() 配置不支持色温iot指令，却需要构建色温iot指令！请检测功能是否异常！" }
                    return null
                }
                return CmdColorWc.makeCmdColorWcByKelvin(finalKelvin, colorTemType)
            }
            return CmdColorWc.makeCmdColorWcByColorInt(rgb)

        }

        private fun cmd4ColorMaker(rgb: Int, isOld: Boolean): AbsCmd4Op {
            if (ColorUtils.isColorTem(rgb)) {
                return if (isOld) CmdOldColorTem(rgb) else CmdColorTem(rgb)
            }
            return if (isOld) CmdOldColor(rgb) else CmdColor(rgb)
        }

        private fun cmd4OnlyColorMaker(rgb: Int, isOld: Boolean): AbsCmd4Op {
            return if (isOld) CmdOldColor(rgb) else CmdColor(rgb)
        }

        private fun cmd4ColorMode(info: Info4BleIotDevice, rgb: Int): AbsCmd4Op? {
            val version = checkColorBytesVersion(info)
            val controller: IControllerNoEvent? = color4VersionBytesMap[version]?.let { maker ->
                val piece = ColorPieceConfig.configColorPiece(info)
                val array = BooleanArray(piece) { true }

                /**构建单包颜色指令*/
                val extBytes = maker.invoke(rgb, array, 1)
                ControllerMode.generateWriteControllerByExtBytes(extBytes)
            }
            controller?.let {
                return Old4CmdPt.makeCmdPt(Old4CmdPt.pt_op_mode, it)
            }
            return null
        }

        /**
         * 配置颜色的iot指令构建的version的map
         */
        val cmd4VersionMap =
            hashMapOf<Int, (checkRgbTem: Boolean, rgb: Int, kelvin: Int, info: Info4BleIotDevice) -> AbsCmd4Op?>().apply {
                put(IOT_COLOR_CMD_4_COLOR_NO_SUPPORT) { _, _, _, _ ->
                    null
                }
                put(IOT_COLOR_CMD_4_COLOR_WC) { checkRgbTem, rgb, kelvin, info ->
                    cmd4ColorWcMaker(checkRgbTem, rgb, kelvin, info)
                }
                put(IOT_COLOR_CMD_4_BULB) { _, rgb, kelvin, info ->
                    cmd4OldBulbMaker(rgb, kelvin, info)
                }
                put(IOT_COLOR_CMD_4_COLOR_OLD) { _, rgb, _, _ ->
                    cmd4ColorMaker(rgb, true)
                }
                put(IOT_COLOR_CMD_4_COLOR) { _, rgb, _, _ ->
                    cmd4ColorMaker(rgb, false)
                }
                put(IOT_COLOR_CMD_4_MODE) { _, rgb, kelvin, info ->
                    cmd4ColorMode(info, rgb)
                }
                put(IOT_COLOR_CMD_4_ONLY_COLOR) { _, rgb, _, _ ->
                    cmd4OnlyColorMaker(rgb, false)
                }
                put(IOT_COLOR_CMD_4_H6003) { _, rgb, kelvin, info ->
                    cmd4ColorTemMaker(rgb, kelvin, info)
                }
            }


        //</editor-fold>


        const val REAL_TIME_COLOR_VERSION_NO_SUPPORT = -1
        const val REAL_TIME_COLOR_VERSION_COLOR_MODE = 0
        const val REAL_TIME_COLOR_VERSION_NEW_MIC = 2
        const val REAL_TIME_COLOR_VERSION_05 = 3 //33 05 05

        /**配置-支持蓝牙下实时颜色应用的goodsType配置*/
        internal val goodsType4RealTimeColorApplyMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Int>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1) {
                    if (BleUtils.isHigherVersion(it.versionSoft, "1.06.00")) {
                        REAL_TIME_COLOR_VERSION_NEW_MIC
                    } else {
                        REAL_TIME_COLOR_VERSION_COLOR_MODE
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1) {
                    if (BleUtils.isHigherVersion(it.versionSoft, "1.06.00")) {
                        REAL_TIME_COLOR_VERSION_NEW_MIC
                    } else {
                        REAL_TIME_COLOR_VERSION_COLOR_MODE
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1) {
                    if (it.sku == "H6163" || it.sku == "H6117") {
                        REAL_TIME_COLOR_VERSION_NO_SUPPORT
                    } else if (BleUtils.isHigherVersion(it.versionSoft, "1.05.00")) {
                        REAL_TIME_COLOR_VERSION_NEW_MIC
                    } else {
                        REAL_TIME_COLOR_VERSION_COLOR_MODE
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61F6) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61b8) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7085) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6800) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70B6) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6840) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6841) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT_HDMI) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_H6603) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_AI_HDMI_H6604) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7093) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7094) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H8066) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6039) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H703A_B) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6020) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7030) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6038) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_BULB) {
                    REAL_TIME_COLOR_VERSION_05
                }
                put(GoodsType.GOODS_TYPE_VALUE_H70C4_5_6_7_8_9) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7025) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7086) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6069) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6048) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61A9) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }

                put(GoodsType.GOODS_TYPE_VALUE_H7072) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7076) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60B2) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60B0) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }

                put(GoodsType.GOODS_TYPE_VALUE_H60B1) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H601EF) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A4) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H702ABC) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7026) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }

                put(GoodsType.GOODS_TYPE_VALUE_H7056) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A6) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60C1) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098) {
                    if (it.pactType == GoodsType.PACT_TYPE_H8098 && it.pactCode == GoodsType.PACT_CODE_H8098) {
                        REAL_TIME_COLOR_VERSION_COLOR_MODE
                    } else {
                        REAL_TIME_COLOR_VERSION_NO_SUPPORT
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END) {
                    if (it.pactType == GoodsType.PACT_TYPE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END_V2) {
                        REAL_TIME_COLOR_VERSION_COLOR_MODE
                    } else {
                        REAL_TIME_COLOR_VERSION_NO_SUPPORT
                    }
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6690) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7087) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H66A0) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGB_FLUSH_LIGHT_PANEL) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6095) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6094) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6671_72) {
                    REAL_TIME_COLOR_VERSION_NEW_MIC
                }
                put(GoodsType.GOODS_TYPE_VALUE_H707ABC) {
                    REAL_TIME_COLOR_VERSION_COLOR_MODE
                }
            }

        /**
         * 是否支持分段实时颜色
         * 只支持颜色模式指令，不支持mic
         */
        fun supportSegmentRealColor(info: Info4BleIotDevice): Boolean {
            return segmentRealColorMap[info.goodsType]?.invoke(info) ?: false
        }

        private val segmentRealColorMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Boolean>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_H61F6) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H61b8) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H7085) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H6038) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H6039) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H7093) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H7094) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H8066) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H7072) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H601EF) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H60B0) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H7076) { true }
                put(GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098) {
                    it.pactType == GoodsType.PACT_TYPE_H8098 && it.pactCode == GoodsType.PACT_CODE_H8098
                }
                put(GoodsType.GOODS_TYPE_VALUE_H7076) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H6690) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H7087) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H66A0) { true }
                put(GoodsType.GOODS_TYPE_VALUE_H707ABC) { true }
            }

        /**
         * 实时颜色发送指令的时间间隔
         * 默认需要间隔100ms发送一包-发送太快-指令无法响应
         */
        fun realColorTimeInterval(info: Info4BleIotDevice): Int {
            if (GoodsType.GOODS_TYPE_VALUE_H61F6 == info.goodsType) {
                //H61F6设置颜色时带渐变，指令发快了设备颜色反而变得慢。改成300ms后的效果，灯光了确认可以
                return 300
            }
            if (GoodsType.GOODS_TYPE_VALUE_H703A_B == info.goodsType) {
                return 400
            }
            //默认100ms
            return 100
        }


        /**
         * 组色条时，组装相对亮度指令
         */
        fun makeRlBrightnessControllers(
            info: Info4BleIotDevice,
            brightnessSet: IntArray
        ): List<IControllerNoEvent>? {
            return relativeBrightnessColorStripMap[info.goodsType]?.invoke(info, brightnessSet)
        }

        private val relativeBrightnessColorStripMap =
            hashMapOf<Int, (info: Info4BleIotDevice, brightnessSet: IntArray) -> List<IControllerNoEvent>>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT) { info, brightnessSet ->
                    ColorModeControllerConfig.makeRlBrightnessBySingle(info, brightnessSet)
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB_V2) { info, brightnessSet ->
                    ColorModeControllerConfig.makeRlBrightnessBySingle(info, brightnessSet)
                }
                put(GoodsType.GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB) { info, brightnessSet ->
                    ColorModeControllerConfig.makeRlBrightnessBySingle(info, brightnessSet)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61F6) { _, brightnessSet ->
                    ColorModeControllerConfig.makeGroupRlBrightness(brightnessSet)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H61b8) { _, brightnessSet ->
                    ColorModeControllerConfig.makeGroupRlBrightnessNoneIndex(brightnessSet)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT) { _, brightnessSet ->
                    ColorModeControllerConfig.makeGroupRlBrightness(brightnessSet)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_V2) { _, brightnessSet ->
                    ColorModeControllerConfig.makeGroupRlBrightness(brightnessSet)
                }
                put(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_606A) { _, brightnessSet ->
                    ColorModeControllerConfig.makeGroupRlBrightness(brightnessSet)
                }
                put(GoodsType.GOODS_TYPE_VALUE_H6063) { _, brightnessSet ->
                    ColorModeControllerConfig.makeGroupRlBrightness(brightnessSet)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_Y) { _, brightnessSet ->
                    ColorModeControllerConfig.makeGroupRlBrightness(brightnessSet)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_TRIANGLE) { _, brightnessSet ->
                    ColorModeControllerConfig.makeGroupRlBrightness(brightnessSet)
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST) { info, brightnessSet ->
                    ColorModeControllerConfig.makeRlBrightnessBySingle(info, brightnessSet)
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2) { info, brightnessSet ->
                    ColorModeControllerConfig.makeRlBrightnessBySingle(info, brightnessSet)
                }
                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3) { info, brightnessSet ->
                    ColorModeControllerConfig.makeRlBrightnessBySingle(info, brightnessSet)
                }

                put(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4) { info, brightnessSet ->
                    ColorModeControllerConfig.makeRlBrightnessBySingle(info, brightnessSet)
                }
            }


        /**
         * 颜色快照单独处理
         */
        val colorSnapshotNotConfigMap =
            hashMapOf<Int, (info: Info4BleIotDevice) -> Boolean>().apply {
                put(GoodsType.GOODS_TYPE_VALUE_TV_RGBIC_LIGHT_V1) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H707ABC) {
                    true
                }
                put(GoodsType.GOODS_TYPE_VALUE_H60A6) {
                    it.pactType == GoodsType.PACT_TYPE_VALUE_H60A6 && it.pactCode >= GoodsType.PACT_CODE_VALUE_H60A6_3
                }
            }

        private fun getOldDreamColorModeVersion(it: Info4BleIotDevice, colorModeVersion:Int): Int {
            fun support(): Boolean {
                if (it.pactType >= 10 && it.pactCode >= 1) {
                    return true
                }
                return BleUtils.isHigherVersion(
                    it.versionSoft,
                    "1.06.00"
                ) && BleUtils.isHigherVersion(it.versionHard, "1.00.03")
            }
            return if (!OldDreamColorUtil.checkSupportNewDreamColorFuc(
                    it.sku,
                    it.versionSoft
                )
            ) {
                colorModeVersion
            } else {
                if (support()) {
                    COLOR_MODE_VERSION_0X15
                } else {
                    COLOR_MODE_VERSION_0X0B
                }
            }
        }

        /**
         * 色条应用单独处理，来源于之前色条仅支持颜色，后续支持色温，无法在色条应用方法里边做区分，只能由子模块构建
         */
        val colorStripNotConfigMap = hashMapOf<Int, (info: Info4BleIotDevice) -> Boolean>().apply {
            put(GoodsType.GOODS_TYPE_VALUE_H60A6) {
                it.pactType == GoodsType.PACT_TYPE_VALUE_H60A6 && it.pactCode >= GoodsType.PACT_CODE_VALUE_H60A6_3
            }
        }
    }

}