package com.govee.base2light.pact.newdetail.config.fuc.color

import com.govee.base2home.Constant
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.sku.Colors
import com.govee.base2kt.utils.BleUtils
import com.govee.base2kt.utils.ColorUtils
import com.govee.base2kt.utils.NumberUtils
import com.govee.base2light.ac.club.EffectData.ColorEffect
import com.govee.base2light.ble.controller.BleProtocolConstants
import com.govee.base2light.ble.controller.IControllerNoEvent
import com.govee.base2light.kt.comm.AbsCmd4Op
import com.govee.base2light.kt.comm.Info4BleIotDevice
import com.govee.base2light.kt.general_controller.Controller4ExtBytes
import com.govee.base2light.kt.general_controller.ControllerMode
import com.govee.base2light.kt.general_controller.MultiColorStripControllerNoEvent
import com.govee.base2light.kt.general_controller.MultiColorTemStripControllerNoEvent
import com.govee.base2light.kt.general_controller.MultiController
import com.govee.base2light.kt.iot.Cmd4PtReal
import com.govee.base2light.kt.iot.Old4CmdBulb
import com.govee.base2light.kt.iot.h6003.CmdColor
import com.govee.base2light.pact.newdetail.config.fuc.ColorPieceConfig
import com.govee.base2light.pact.newdetail.sub.SubMode4Color
import com.ihoment.base2app.infra.SafeLog

/**
 * Create by xieyingwu on 2023/12/26
 * 颜色模式指令配置
 */
class ColorModeControllerConfig private constructor() {
    companion object {
        private const val TAG = "ColorModeControllerConfig"

        /**
         * 配置颜色模式指令-色温kelvin构建
         */
        fun configColorModeKelvinExtBytes(kelvin: Int, info: Info4BleIotDevice): ByteArray? {
            val version = Config.checkColorBytesVersion(info)
            if (version == null) {
                SafeLog.e(TAG) { "configColorModeKelvinExtBytes() 未配置对应的kelvin转颜色模式指令配置！" }
                return null
            }
            val colorPiece = ColorPieceConfig.configColorPiece(info)
            SafeLog.i(TAG) { "configColorModeKelvinExtBytes() version = $version ; colorPiece = $colorPiece" }
            Config.kelvinAll4VersionMap[version]?.let {
                return it.invoke(colorPiece, kelvin, Config.checkColorTemType(info))
            }
            SafeLog.e(TAG) { "configColorModeKelvinExtBytes() version  =$version 未配置对应指令构成" }
            return null
        }

        /**
         * 配置颜色的iot指令-色温kelvin构建
         */
        fun configColorModeKelvinCmd(kelvin: Int, info: Info4BleIotDevice): AbsCmd4Op? {
            val version = Config.checkColorModeIotVersion(true, info)
            if (version == null) {
                SafeLog.e(TAG) { "configColorModeKelvinCmd() 未配置对应的iot色温指令！" }
                return null
            }
            SafeLog.i(TAG) { "configColorModeKelvinCmd() version = $version" }
            return Config.cmd4VersionMap[version]?.invoke(false, 0, kelvin, info)
        }

        /**
         * 配置颜色的iot指令-单颜色值
         */
        private fun configColorModeCmd(rgb: Int, info: Info4BleIotDevice): AbsCmd4Op? {
            val colorTem = ColorUtils.isColorTem(rgb)
            val version = Config.checkColorModeIotVersion(colorTem, info)
            if (version == null) {
                SafeLog.e(TAG) { "configColorModeCmd() 未配置对应的iot指令！" }
                return null
            }
            SafeLog.i(TAG) { "configColorModeCmd() colorTem = $colorTem ; version = $version" }
            return Config.cmd4VersionMap[version]?.invoke(true, rgb, 0, info)
        }

        /**
         * 是否纯iot设备，是的话直接返回对应的颜色iot指令
         */
        private fun getOnlyIotDeviceCmd(info: Info4BleIotDevice, color: Int): AbsCmd4Op? {
            if (!isOnlyIotDevice(info)) return null
            if (info.goodsType == GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_IOT_V1) {
                val kelvin = Constant.getColorTemKelvin(color)
                return if (ColorUtils.isColorTem(color)) com.govee.base2light.kt.iot.h6003.CmdColorTem(
                    kelvin
                ) else CmdColor(color)
            }
            return null
        }

        fun isOnlyIotDevice(info: Info4BleIotDevice): Boolean {
            return info.goodsType == GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_IOT_V1
        }

        private fun configCustomColorPieceCmd(
            info: Info4BleIotDevice,
            controllers4ColorWrite: MutableList<IControllerNoEvent>
        ): MutableList<AbsCmd4Op>? {
            val sku = info.sku
            when (sku) {
                "H7022" -> {
                    val list = mutableListOf<ByteArray>()
                    controllers4ColorWrite.forEach {
                        it.getValue()?.let {
                            list.addAll(it)
                        }
                    }
                    return mutableListOf(Old4CmdBulb.makeOldCmdBulb(list))
                }
            }
            return null
        }

        /**
         * 配置-灯效库应用指令构建
         */
        fun configColorEffectController(
            colorEffect: ColorEffect,
            info: Info4BleIotDevice
        ): Triple<MutableList<IControllerNoEvent>, MutableList<AbsCmd4Op>?, ColorStrip>? {
            SafeLog.i(TAG) { "configColorEffectController() info = ${info.fucKey()}" }
            val noFadeController = colorEffect.noFadeController()
            val colorSet: IntArray? = colorEffect.colorSet
            val brightnessSet: IntArray? = colorEffect.brightnessSet
            SafeLog.i(TAG) { "configColorEffectController() noFadeController = $noFadeController ; colorSet = ${colorSet?.contentToString()} ; brightnessSet =${brightnessSet?.contentToString()}" }
            ColorPieceConfig.configColorStripApply(info, colorSet, brightnessSet)?.let {
                val finalColorSet = it.first
                val finalBrightnessSet = it.second
                val version = Config.checkColorBytesVersion(info)
                if (version == null) {
                    SafeLog.e(TAG) { "configColorEffectController() 未配置对应的颜色指令版本配置！" }
                    return null
                }
                val isApplySingleColor = if (Config.checkSupportColorModeOver56Piece(info)) {
                    //超过56个ic，应用单色，也是走多包指令
                    false
                } else {
                    //如果是应用单色，应该走颜色单包指令
                    colorSet?.size == 1 && brightnessSet == null
                }
                if (!isApplySingleColor) {
                    //不是单色，走颜色分组/颜色多包指令构建逻辑
                    val triple =
                        makeColorStripMultiControllers(info, finalColorSet, finalBrightnessSet)
                    if (triple != null) return triple
                }
                val colorMap = BleUtils.generatePosSet(
                    finalColorSet,
                    ColorPieceConfig.configColorPosSetMaxLen(info)
                )
                if (colorMap.isNullOrEmpty()) {
                    SafeLog.e(TAG) { "configColorEffectController() 颜色数量超过生成位置逻辑color.size = ${finalColorSet.size}" }
                    return null
                }
                val controllers4ColorWrite = mutableListOf<IControllerNoEvent>()
                val hadColorBytesVersionConfig =
                    Config.color4VersionBytesMap[version]?.let { maker ->
                        colorMap.forEach { map ->
                            /**构建单包颜色指令*/
                            val extBytes =
                                maker.invoke(map.key, map.value, Config.checkColorTemType(info))
                            controllers4ColorWrite.add(
                                ControllerMode.generateWriteControllerByExtBytes(
                                    extBytes
                                )
                            )
                        }
                        true
                    } ?: false
                if (!hadColorBytesVersionConfig) {
                    SafeLog.e(TAG) { "configColorEffectController() 未配置对应指令版本的生成颜色指令配置！" }
                    return null
                }
                if (controllers4ColorWrite.isEmpty()) {
                    SafeLog.i(TAG) { "configColorEffectController() 未能构建颜色指令集合" }
                    return null
                }
                val cmds = mutableListOf<AbsCmd4Op>()

                /**判断是否需要构建单独的cmd的颜色指令*/
                val allColorSame = ColorUtils.isAllColorSame(finalColorSet)
                SafeLog.i(TAG) { "configColorEffectController() allColorSame  =$allColorSame" }
                if (allColorSame) {
                    val rgb = finalColorSet[0]
                    configColorModeCmd(rgb, info)?.let { cmd ->
                        cmds.add(cmd)
                    }
                }

                val controllers4RelativeBrightnessWrite = mutableListOf<IControllerNoEvent>()
                finalBrightnessSet?.let brightness@{ set ->
                    val rlBControllers =
                        Config.makeRlBrightnessControllers(info, finalBrightnessSet)
                    if (!rlBControllers.isNullOrEmpty()) {
                        controllers4RelativeBrightnessWrite.addAll(rlBControllers)
                        SafeLog.e(TAG) { "makeRlBrightnessControllers() 配置相对亮度生效！" }
                        return@brightness
                    }
                    val version4RelativeBrightness =
                        Config.checkRelativeBrightnessBytesVersion(info)
                    if (version4RelativeBrightness == null) {
                        SafeLog.e(TAG) { "configColorEffectController() 未配置对应的相对亮度指令版本配置！" }
                        return null
                    }
                    if (Config.isVersionSupport(version4RelativeBrightness)) {
                        val hadConfig4AllBrightnessVersionConfig =
                            Config.relativeAllBrightness4VersionMap[version4RelativeBrightness]?.let { maker ->
                                /**构建整段设置相对亮度指令*/
                                val extBytes = maker.invoke(set)
                                controllers4RelativeBrightnessWrite.add(
                                    ControllerMode.generateWriteControllerByExtBytes(
                                        extBytes
                                    )
                                )
                                true
                            } ?: false
                        if (!hadConfig4AllBrightnessVersionConfig) {
                            SafeLog.e(TAG) { "configColorEffectController() 未配置对应指令版本的生成相对亮度指令配置！" }
                            return null
                        }
                    }
                }
                /**构建渐变指令*/
                var finalGradual: Boolean? = null
                if (noFadeController.not()) {
                    val gradual = colorEffect.isFade
                    finalGradual = gradual
                    SafeLog.i(TAG) { "configColorEffectController() gradual = $gradual" }
                    val version4GradualBytes = Config.checkGradualBytesVersion(info)
                    if (version4GradualBytes == null) {
                        SafeLog.e(TAG) { "configColorEffectController() 未配置渐变指令版本配置" }
                        return null
                    }
                    if (Config.isVersionSupport(version4GradualBytes)) {
                        val hadConfigGradualVersionConfig =
                            Config.gradual4VersionMap[version4GradualBytes]?.let { maker ->
                                /**构建设置渐变指令*/
                                controllers4RelativeBrightnessWrite.add(
                                    Controller4ExtBytes.makeWriteController(maker.invoke(gradual))
                                )
                                true
                            } ?: false
                        if (!hadConfigGradualVersionConfig) {
                            SafeLog.e(TAG) { "configColorEffectController() 未配置对应指令版本的生成渐变指令配置！" }
                            return null
                        }
                    }
                }
                /**需要构建额外的iot指令时，透传指令-相对亮度设置指令也需要单独构建*/
                if (cmds.isNotEmpty()) {
                    for (controller in controllers4RelativeBrightnessWrite) {
                        Cmd4PtReal.makeCmdPtReal(controller)?.let { cmd ->
                            cmds.add(cmd)
                        }
                    }
                }
                /**合并全部的蓝牙指令*/
                if (controllers4RelativeBrightnessWrite.isNotEmpty()) {
                    controllers4ColorWrite.addAll(controllers4RelativeBrightnessWrite)
                }
                return Triple(
                    controllers4ColorWrite,
                    cmds,
                    ColorStrip(finalColorSet, finalBrightnessSet, finalGradual)
                )
            } ?: return null
        }


        /**
         * 配置-颜色模式的快照透传指令集合
         */
        fun configColorModeSnapshot(
            subColor: SubMode4Color,
            info: Info4BleIotDevice
        ): MutableList<ByteArray>? {
            SafeLog.i(TAG) { "configColorModeSnapshot() info = ${info.fucKey()}" }
            if (Config.colorSnapshotNotConfigMap[info.goodsType]?.invoke(info) == true) return null
            val colorSet = subColor.colorSet
            val colorBrightnessSet = subColor.colorBrightnessSet
            val posSet = subColor.posSet
            val gradual = subColor.gradual
            SafeLog.i(TAG) { "configColorModeSnapshot() \ncolorSet = ${colorSet.contentToString()}\ncolorBrightnessSet = ${colorBrightnessSet.contentToString()}\ngradual = $gradual" }
            //支持颜色多包/颜色分组走checkSpecialSkuBuildColorPiece方法，否则走下方颜色单包指令
            val isMulti = Config.checkSupportColorModeOver56Piece(info)
            if (isMulti && ColorUtils.isAllColorSame(colorSet)) {
                val color = colorSet.first()
                configColorMultiController(color, posSet, info)?.first?.getValue()?.let {
                    return it
                }
            }

            //判断是否全段都是色温 是的话发色温指令 颜色多包指令只对应rgb 和设备实际显示不一定对上
            val configNormalFucSupport = configNormalFucSupport(info)
            val supportColorTem = configNormalFucSupport?.get(4) ?: true
            val isAllSameColorTem =
                ColorUtils.isAllColorSame(colorSet) && ColorUtils.isColorTem(colorSet[0]) && supportColorTem
            if (!isAllSameColorTem) {
                val specialBytes =
                    checkSpecialSkuBuildColorPiece(
                        info,
                        colorSet,
                        colorBrightnessSet,
                        gradual,
                        posSet
                    )
                if (specialBytes.isNotEmpty()) return specialBytes
            }
            val version = Config.checkColorBytesVersion(info)
            if (version == null) {
                SafeLog.e(TAG) { "configColorModeSnapshot() 未配置对应的颜色指令版本配置！" }
                return null
            }
            val colorMap =
                BleUtils.generatePosSet(colorSet, ColorPieceConfig.configColorPiece(info))
            if (colorMap.isNullOrEmpty()) {
                SafeLog.e(TAG) { "configColorModeSnapshot() 颜色数量超过生成位置逻辑color.size = ${colorSet.size}" }
                return null
            }
            val controllerBytes = mutableListOf<ByteArray>()
            val hadColorBytesVersionConfig =
                Config.color4VersionBytesMap[version]?.let { maker ->
                    colorMap.forEach { map ->
                        /**构建单包颜色指令*/
                        val extBytes =
                            maker.invoke(map.key, map.value, Config.checkColorTemType(info))
                        ControllerMode.generateWriteControllerByExtBytes(
                            extBytes
                        ).getValue()?.let {
                            controllerBytes.addAll(it)
                        }
                    }
                    true
                } ?: false
            if (!hadColorBytesVersionConfig) {
                SafeLog.e(TAG) { "configColorModeSnapshot() 未配置对应指令版本的生成颜色指令配置！" }
                return null
            }
            if (controllerBytes.isEmpty()) {
                SafeLog.i(TAG) { "configColorModeSnapshot() 未能构建颜色指令集合" }
                return null
            }
            val supportPartBrightness = configNormalFucSupport?.let { it[3] } ?: false
            val supportGradual = configNormalFucSupport?.let { it[0] } ?: false
            SafeLog.i(TAG) { "configColorModeSnapshot() supportPartBrightness = $supportPartBrightness ; supportGradual = $supportGradual" }
            if (supportPartBrightness) {
                val rlBControllers =
                    Config.makeRlBrightnessControllers(info, colorBrightnessSet)
                if (!rlBControllers.isNullOrEmpty()) {
                    rlBControllers.forEach {
                        it.getValue()?.let { bytes ->
                            controllerBytes.addAll(bytes)
                        }
                    }
                } else {
                    val version4RelativeBrightness =
                        Config.checkRelativeBrightnessBytesVersion(info)
                    if (version4RelativeBrightness == null) {
                        SafeLog.e(TAG) { "configColorModeSnapshot() 未配置对应的相对亮度指令版本配置！" }
                        return null
                    }
                    if (Config.isVersionSupport(version4RelativeBrightness)) {
                        val hadConfig4AllBrightnessVersionConfig =
                            Config.relativeAllBrightness4VersionMap[version4RelativeBrightness]?.let { maker ->
                                /**构建整段设置相对亮度指令*/
                                val extBytes = maker.invoke(colorBrightnessSet)
                                ControllerMode.generateWriteControllerByExtBytes(
                                    extBytes
                                ).getValue()?.let {
                                    controllerBytes.addAll(it)
                                }
                                true
                            } ?: false
                        if (!hadConfig4AllBrightnessVersionConfig) {
                            SafeLog.e(TAG) { "configColorModeSnapshot() 未配置对应指令版本的生成相对亮度指令配置！" }
                            return null
                        }
                    }
                }
            }
            if (supportGradual) {
                val version4GradualBytes = Config.checkGradualBytesVersion(info)
                if (version4GradualBytes == null) {
                    SafeLog.e(TAG) { "configColorModeSnapshot() 未配置渐变指令版本配置" }
                    return null
                }
                if (Config.isVersionSupport(version4GradualBytes)) {
                    val hadConfigGradualVersionConfig =
                        Config.gradual4VersionMap[version4GradualBytes]?.let { maker ->
                            /**构建设置渐变指令*/
                            Controller4ExtBytes.makeWriteController(maker.invoke(gradual))
                                .getValue()
                                ?.let {
                                    controllerBytes.addAll(it)
                                }
                            true
                        } ?: false
                    if (!hadConfigGradualVersionConfig) {
                        SafeLog.e(TAG) { "configColorModeSnapshot() 未配置对应指令版本的生成渐变指令配置！" }
                        return null
                    }
                }
            }
            return controllerBytes
        }

        /**
         * 配置颜色指令-单颜色值+选中位置
         */
        fun configColorController(
            rgb: Int,
            posSet: BooleanArray,
            info: Info4BleIotDevice
        ): Pair<IControllerNoEvent, AbsCmd4Op?>? {
            SafeLog.i(TAG) { "configColorController() info = ${info.fucKey()}" }
            val onlyIotDeviceCmd = getOnlyIotDeviceCmd(info, rgb)
            if (onlyIotDeviceCmd != null) {
                SafeLog.i(TAG) { "configColorController() 纯iot设备 只发送iot指令" }
                return Pair(
                    ControllerMode.generateWriteControllerByExtBytes(byteArrayOf()),
                    onlyIotDeviceCmd
                )
            }
            val version = Config.checkColorBytesVersion(info)
            if (version == null) {
                SafeLog.e(TAG) { "configColorController() 未配置对应的颜色指令版本配置！" }
                return null
            }
            SafeLog.i(TAG) { "configColorController() version = $version" }
            val controller: IControllerNoEvent? =
                Config.color4VersionBytesMap[version]?.let { maker ->
                    /**构建单包颜色指令*/
                    val extBytes = maker.invoke(rgb, posSet, Config.checkColorTemType(info))
                    ControllerMode.generateWriteControllerByExtBytes(extBytes)
                }
            if (controller == null) {
                SafeLog.e(TAG) { "configColorController() 未配置对应指令版本的生成颜色指令配置！" }
                return null
            }
            val allSame = NumberUtils.isAllSame(posSet, true)
            SafeLog.i(TAG) { "configColorController() allSame = $allSame" }
            val cmd = if (allSame) configColorModeCmd(rgb, info) else null
            return Pair(controller, cmd)
        }

        /**
         * 配置颜色指令-单颜色值+选中位置
         *
         * 使用多包发送
         */
        fun configColorMultiController(
            rgb: Int,
            posSet: BooleanArray,
            info: Info4BleIotDevice
        ): Pair<IControllerNoEvent, AbsCmd4Op?>? {
            SafeLog.i(TAG) { "configColorController() info = ${info.fucKey()}" }
            val version = Config.checkColorBytesVersion(info)
            if (version == null) {
                SafeLog.e(TAG) { "configColorController() 未配置对应的颜色指令版本配置！" }
                return null
            }
            SafeLog.i(TAG) { "configColorController() version = $version" }
            val controller: IControllerNoEvent? =
                Config.color4VersionBytesMap[version]?.let { maker ->
                    /**构建多包颜色指令*/
                    val extBytes = maker.invoke(rgb, posSet, Config.checkColorTemType(info))
                    MultiController.makeMultiController(
                        extBytes,
                        BleProtocolConstants.sub_mode_color_multi
                    )
                }
            if (controller == null) {
                SafeLog.e(TAG) { "configColorController() 未配置对应指令版本的生成颜色指令配置！" }
                return null
            }
            val allSame = NumberUtils.isAllSame(posSet, true)
            SafeLog.i(TAG) { "configColorController() allSame = $allSame" }
            val cmd = if (allSame) configColorModeCmd(rgb, info) else null
            return Pair(controller, cmd)
        }

        /**
         * 配置颜色指令-单颜色值+选中位置
         */
        private fun configColorController4OnlyBle(
            rgb: Int,
            posSet: BooleanArray,
            info: Info4BleIotDevice
        ): ByteArray? {
            SafeLog.i(TAG) { "configColorController4OnlyBle() info = ${info.fucKey()}" }
            val version = Config.checkColorBytesVersion(info)
            if (version == null) {
                SafeLog.e(TAG) { "configColorController4OnlyBle() 未配置对应的颜色指令版本配置！" }
                return null
            }
            SafeLog.i(TAG) { "configColorController4OnlyBle() version = $version" }
            val controller: IControllerNoEvent? =
                Config.color4VersionBytesMap[version]?.let { maker ->
                    /**构建单包颜色指令*/
                    val extBytes = maker.invoke(rgb, posSet, Config.checkColorTemType(info))
                    ControllerMode.generateWriteControllerByExtBytes(extBytes)
                }
            if (controller == null) {
                SafeLog.e(TAG) { "configColorController4OnlyBle() 未配置对应指令版本的生成颜色指令配置！" }
                return null
            }
            return controller.getNextCommBytes()
        }

        /**
         * 配置-色条应用指令构建
         */
        fun configColorStripController(
            colorSet: IntArray,
            brightnessSet: IntArray?,
            info: Info4BleIotDevice
        ): Triple<MutableList<IControllerNoEvent>, MutableList<AbsCmd4Op>?, ColorStrip>? {
            SafeLog.i(TAG) { "configColorStripController() info = ${info.fucKey()}" }
            if (Config.colorStripNotConfigMap[info.goodsType]?.invoke(info) == true) return null
            GradientColorControllerConfig.configColorStripMultiController(colorSet, brightnessSet, info)?.let {
                SafeLog.i(TAG) { "configColorStripController() 渐变色统一配置生成指令成功" }
                return it
            }
            ColorPieceConfig.configColorStripApply(info, colorSet, brightnessSet)?.let {
                val finalColorSet = it.first
                val finalBrightnessSet = it.second
                val version = Config.checkColorBytesVersion(info)
                if (version == null) {
                    SafeLog.e(TAG) { "configColorStripController() 未配置对应的颜色指令版本配置！" }
                    return null
                }

                //判断是否全段都是色温 是的话发色温指令 颜色多包指令只对应rgb 和设备实际显示不一定对上
                val configNormalFucSupport = configNormalFucSupport(info)
                val supportColorTem = configNormalFucSupport?.get(4) ?: true
                val isAllSameColorTem =
                    ColorUtils.isAllColorSame(finalColorSet) && ColorUtils.isColorTem(finalColorSet[0]) && supportColorTem
                if (!isAllSameColorTem) {
                    //处理支持颜色分组/颜色多包指令构建
                    val triple =
                        makeColorStripMultiControllers(info, finalColorSet, finalBrightnessSet)
                    if (triple != null) return triple
                }

                val colorMap = BleUtils.generatePosSet(
                    finalColorSet,
                    ColorPieceConfig.configColorPosSetMaxLen(info)
                )
                if (colorMap.isNullOrEmpty()) {
                    SafeLog.e(TAG) { "configColorStripController() 颜色数量超过生成位置逻辑color.size = ${finalColorSet.size}" }
                    return null
                }
                val controllers4ColorWrite = mutableListOf<IControllerNoEvent>()
                val hadColorBytesVersionConfig =
                    Config.color4VersionBytesMap[version]?.let { maker ->
                        colorMap.forEach { map ->
                            /**构建单包颜色指令*/
                            val extBytes =
                                maker.invoke(map.key, map.value, Config.checkColorTemType(info))
                            controllers4ColorWrite.add(
                                ControllerMode.generateWriteControllerByExtBytes(
                                    extBytes
                                )
                            )
                        }
                        true
                    } ?: false
                if (!hadColorBytesVersionConfig) {
                    SafeLog.e(TAG) { "configColorStripController() 未配置对应指令版本的生成颜色指令配置！" }
                    return null
                }
                if (controllers4ColorWrite.isEmpty()) {
                    SafeLog.i(TAG) { "configColorStripController() 未能构建颜色指令集合" }
                    return null
                }
                val cmds = mutableListOf<AbsCmd4Op>()

                /**判断是否需要构建单独的cmd的颜色指令*/
                val allColorSame = ColorUtils.isAllColorSame(finalColorSet)
                SafeLog.i(TAG) { "configColorEffectController() allColorSame  =$allColorSame" }
                if (allColorSame) {
                    val rgb = finalColorSet[0]
                    configColorModeCmd(rgb, info)?.let { cmd ->
                        cmds.add(cmd)
                    }
                } else {
                    /**特殊的 sku 中发送的颜色 iot 指令不一样，所以需要做区分*/
                    configCustomColorPieceCmd(info, controllers4ColorWrite)?.let {
                        cmds.addAll(it)
                    }
                }

                val controllers4RelativeBrightnessWrite = mutableListOf<IControllerNoEvent>()
                finalBrightnessSet?.let brightness@{ set ->
                    val rlBControllers =
                        Config.makeRlBrightnessControllers(info, finalBrightnessSet)
                    if (!rlBControllers.isNullOrEmpty()) {
                        controllers4RelativeBrightnessWrite.addAll(rlBControllers)
                        SafeLog.e(TAG) { "makeRlBrightnessControllers() 配置相对亮度生效！" }
                        return@brightness
                    }
                    val version4RelativeBrightness =
                        Config.checkRelativeBrightnessBytesVersion(info)
                    if (version4RelativeBrightness == null) {
                        SafeLog.e(TAG) { "configColorStripController() 未配置对应的相对亮度指令版本配置！" }
                        return null
                    }
                    if (Config.isVersionSupport(version4RelativeBrightness)) {
                        val hadConfig4AllBrightnessVersionConfig =
                            Config.relativeAllBrightness4VersionMap[version4RelativeBrightness]?.let { maker ->
                                /**构建整段设置相对亮度指令*/
                                val extBytes = maker.invoke(set)
                                controllers4RelativeBrightnessWrite.add(
                                    ControllerMode.generateWriteControllerByExtBytes(
                                        extBytes
                                    )
                                )
                                true
                            } ?: false
                        if (!hadConfig4AllBrightnessVersionConfig) {
                            SafeLog.e(TAG) { "configColorStripController() 未配置对应指令版本的生成相对亮度指令配置！" }
                            return null
                        }
                    }
                }
                /**需要构建额外的iot指令时，透传指令-相对亮度设置指令也需要单独构建*/
                if (cmds.isNotEmpty()) {
                    for (controller in controllers4RelativeBrightnessWrite) {
                        Cmd4PtReal.makeCmdPtReal(controller)?.let { cmd ->
                            cmds.add(cmd)
                        }
                    }
                }
                /**合并全部的蓝牙指令*/
                if (controllers4RelativeBrightnessWrite.isNotEmpty()) {
                    controllers4ColorWrite.addAll(controllers4RelativeBrightnessWrite)
                }
                return Triple(
                    controllers4ColorWrite,
                    cmds,
                    ColorStrip(finalColorSet, finalBrightnessSet, null)
                )
            }
            return null
        }

        /**
         * 配置-相对亮度指令构建
         */
        fun configRelativeBrightnessController(
            brightness: Int,
            posSet: BooleanArray,
            info: Info4BleIotDevice
        ): IControllerNoEvent? {
            SafeLog.i(TAG) { "configRelativeBrightnessController() info = ${info.fucKey()}" }
            val version4RelativeBrightness =
                Config.checkRelativeBrightnessBytesVersion(info)
            if (version4RelativeBrightness == null) {
                SafeLog.e(TAG) { "configRelativeBrightnessController() 未配置对应的相对亮度指令版本配置！" }
                return null
            }
            if (Config.isVersionSupport(version4RelativeBrightness)) {
                Config.relativeBrightness4VersionMap[version4RelativeBrightness]?.let { maker ->
                    /**构建设置相对亮度指令*/
                    val extBytes = maker.invoke(brightness, posSet)
                    return ControllerMode.generateWriteControllerByExtBytes(
                        extBytes
                    )
                }
                SafeLog.e(TAG) { "configRelativeBrightnessController() 未配置对应指令版本的生成相对亮度指令配置！" }
                return null
            }
            SafeLog.e(TAG) { "configRelativeBrightnessController() 配置不支持-却需要生成对应指令-请检查UI或业务逻辑是否异常！" }
            return null
        }

        /**
         * 配置-渐变开关指令构建
         */
        fun configGradualController(
            gradual: Boolean,
            info: Info4BleIotDevice
        ): IControllerNoEvent? {
            SafeLog.i(TAG) { "configGradualController() info = ${info.fucKey()}" }
            val version4GradualBytes = Config.checkGradualBytesVersion(info)
            if (version4GradualBytes == null) {
                SafeLog.e(TAG) { "configGradualController() 未配置渐变指令版本配置" }
                return null
            }
            if (Config.isVersionSupport(version4GradualBytes)) {
                Config.gradual4VersionMap[version4GradualBytes]?.let {
                    /**构建设置渐变指令*/
                    return Controller4ExtBytes.makeWriteController(it.invoke(gradual))
                }
                SafeLog.e(TAG) { "configGradualController() 未配置对应指令版本的生成渐变指令配置！" }
                return null
            }
            SafeLog.e(TAG) { "configColorEffectController() 配置不支持-却需要生成对应指令-请检查UI或业务逻辑是否异常！" }
            return null
        }

        /**
         * 配置-色条应用分组指令构建
         */
        private fun configColorStripInGroupController(
            colorSet: IntArray,
            brightnessSet: IntArray?,
            info: Info4BleIotDevice,
            groupStartIndex: Int
        ): Triple<MutableList<IControllerNoEvent>, MutableList<AbsCmd4Op>?, ColorStrip>? {
            SafeLog.i(TAG) { "configColorStripInGroupController() info = ${info.fucKey()}" }

            /*颜色组*/
            val controllers4ColorWrite = mutableListOf<IControllerNoEvent>()
            val size = colorSet.size
            val moreOver = size % 4 == 0
            val lastRgbSetLen = if (moreOver) 4 else size % 4
            val group = size / 4 + if (moreOver) 0 else 1
            for (i in 0 until group) {
                val len = if (i == group - 1) lastRgbSetLen else 4
                SafeLog.i(TAG) { "makeColorStrip() i = $i ; len = $len" }
                val rgbSet4OneGroup = IntArray(len)
                System.arraycopy(colorSet, i * 4, rgbSet4OneGroup, 0, rgbSet4OneGroup.size)
                controllers4ColorWrite.add(
                    ControllerMode.generateWriteControllerByExtBytes(
                        Bytes.colorGroupByte0x15(groupStartIndex + i, rgbSet4OneGroup)
                    )
                )
            }
            /*亮度组*/
            val controllers4RelativeBrightnessWrite = mutableListOf<IControllerNoEvent>()
            brightnessSet?.run {
                if (this.isEmpty()) return@run
                val controllers = if (Config.checkSupportBrightnessGroupSpecial(info) == true) {
                    makeGroupRlBrightnessNoneIndex(this)
                } else {
                    makeGroupRlBrightness(this, groupStartIndex)
                }
                controllers4RelativeBrightnessWrite.addAll(controllers)
            }
            if (controllers4ColorWrite.isEmpty()) {
                SafeLog.i(TAG) { "configColorStripInGroupController() 未能构建颜色指令集合" }
                return null
            }

            val cmds = mutableListOf<AbsCmd4Op>()

            /**判断是否需要构建单独的cmd的颜色指令*/
            val allColorSame = ColorUtils.isAllColorSame(colorSet)
            SafeLog.i(TAG) { "configColorStripInGroupController() allColorSame  =$allColorSame" }
            if (allColorSame) {
                val rgb = colorSet[0]
                configColorModeCmd(rgb, info)?.let { cmd ->
                    cmds.add(cmd)
                }
            }

            /**需要构建额外的iot指令时，透传指令-相对亮度设置指令也需要单独构建*/
            if (cmds.isNotEmpty()) {
                for (controller in controllers4RelativeBrightnessWrite) {
                    Cmd4PtReal.makeCmdPtReal(controller)?.let { cmd ->
                        cmds.add(cmd)
                    }
                }
            }
            /**合并全部的蓝牙指令*/
            if (controllers4RelativeBrightnessWrite.isNotEmpty()) {
                controllers4ColorWrite.addAll(controllers4RelativeBrightnessWrite)
            }
            return Triple(
                controllers4ColorWrite,
                cmds,
                ColorStrip(colorSet, brightnessSet, null)
            )
        }

        /**
         * 配置-色条应用分组指令构建
         */
        private fun configRealTempColorController(
            colorSet: IntArray,
            posSet: BooleanArray,
            info: Info4BleIotDevice,
        ): Pair<MutableList<IControllerNoEvent>, MutableList<AbsCmd4Op>?>? {
            SafeLog.i(TAG) { "configColorStripInGroupController() info = ${info.fucKey()}" }
            val allColorSame = ColorUtils.isAllColorSame(colorSet)

            if (colorSet.isEmpty()) {
                return null
            }
            if (!allColorSame) {
                return null
            }
            if (!ColorUtils.isColorTem(colorSet.first())) {
                return null
            }
            if (posSet.isEmpty()) {
                return null
            }
            val rgb = colorSet[0]
            SafeLog.i(TAG) { "configRealTempColorController() 真色温指令构建,带相对亮度" }

            // 真色温情况下，构建亮度指令，构建色温指令
            val configColorController = configColorController(rgb, posSet, info) ?: return null

            val cmds: MutableList<AbsCmd4Op>? = configColorController.second?.let { cmd ->
                val cmdList = mutableListOf(cmd)
                cmdList
            }
            return Pair(mutableListOf(configColorController.first), cmds)
        }

        /**
         * 配置-色条应用多包指令构建
         */
        fun configColorStripMultiController(
            colorSet: IntArray,
            brightnessSet: IntArray?,
            info: Info4BleIotDevice,
        ): Triple<MutableList<IControllerNoEvent>, MutableList<AbsCmd4Op>?, ColorStrip> {
            SafeLog.i(TAG) { "configColorStripMultiController() info = ${info.fucKey()}" }
            /*颜色组*/
            val controllers4ColorWrite = mutableListOf<IControllerNoEvent>()
            val color = Colors().apply {
                this.colorSet = colorSet
                this.brightnessSet = brightnessSet
            }
            val multiControllers = MultiColorStripControllerNoEvent.makeMultiController(color)
            controllers4ColorWrite.add(multiControllers)

            controllers4ColorWrite.add(
                ControllerMode.generateWriteControllerByExtBytes(
                    byteArrayOf(BleProtocolConstants.sub_mode_color)
                )
            )

            return Triple(
                controllers4ColorWrite,
                mutableListOf(),
                ColorStrip(colorSet, brightnessSet, null, true)
            )
        }

        /**
         * 配置-色条应用多包指令构建
         */
        private fun configColorTemStripMultiController(
            colorSet: IntArray,
            brightnessSet: IntArray?,
            info: Info4BleIotDevice,
        ): Triple<MutableList<IControllerNoEvent>, MutableList<AbsCmd4Op>?, ColorStrip> {
            SafeLog.i(TAG) { "configColorTemStripMultiController() info = ${info.fucKey()}" }
            /*颜色组*/
            val controllers4ColorWrite = mutableListOf<IControllerNoEvent>()
            val color = Colors().apply {
                this.colorSet = colorSet
                this.brightnessSet = brightnessSet
            }
            val multiControllers = MultiColorTemStripControllerNoEvent.makeMultiController(color)
            controllers4ColorWrite.add(multiControllers)

            controllers4ColorWrite.add(
                ControllerMode.generateWriteControllerByExtBytes(
                    byteArrayOf(BleProtocolConstants.sub_mode_color)
                )
            )

            return Triple(
                controllers4ColorWrite,
                mutableListOf(),
                ColorStrip(colorSet, brightnessSet, null, true)
            )
        }

        /**
         * 组颜色多包加模式包,快照ios需要
         */
        private fun checkNeedModeController(info: Info4BleIotDevice): Boolean {
            return info.goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_OUTDOOR_LAMP
                || info.goodsType == GoodsType.GOODS_TYPE_VALUE_TV_RGBIC_LIGHT_V1
        }


        /**
         * 配置通用功能点的支持
         */
        fun configNormalFucSupport(info: Info4BleIotDevice): BooleanArray? {
            return Config.checkNormalFucConfig(info)
        }

        /**
         * 是否支持实时颜色应用
         */
        fun supportRealColorApply(info: Info4BleIotDevice): Boolean {
            Config.goodsType4RealTimeColorApplyMap[info.goodsType]?.invoke(info)?.let {
                return it != Config.REAL_TIME_COLOR_VERSION_NO_SUPPORT
            }
            return false
        }

        /**
         * 构建实时颜色应用的bytes数组
         */
        fun configRealColorApplyBytes(
            info: Info4BleIotDevice,
            rgb: Int,
            posSet: BooleanArray,
            down: Boolean
        ): ByteArray? {
            Config.goodsType4RealTimeColorApplyMap[info.goodsType]?.invoke(info)?.let { version ->
                if (version == Config.REAL_TIME_COLOR_VERSION_NO_SUPPORT) return null
                if (version == Config.REAL_TIME_COLOR_VERSION_COLOR_MODE) {
                    return configColorController4OnlyBle(rgb, posSet, info)
                }
                if (version == Config.REAL_TIME_COLOR_VERSION_05) {
                    val rgbBytes = ColorUtils.getRgbBytes(rgb)
                    val bytes = byteArrayOf(
                        0x05.toByte(),
                        if (down) 0x01 else 0x00,
                        if (!down) rgbBytes[0] else 0x00,
                        if (!down) rgbBytes[1] else 0x00,
                        if (!down) rgbBytes[2] else 0x00,
                    )
                    return ControllerMode.generateWriteControllerByExtBytes(bytes)
                        .getNextCommBytes()
                }
                if (version == Config.REAL_TIME_COLOR_VERSION_NEW_MIC) {
                    val rgbBytes = ColorUtils.getRgbBytes(rgb)
                    val bytes = byteArrayOf(
                        0xa5.toByte(),
                        0x02,
                        0x83.toByte(),
                        rgbBytes[0],
                        rgbBytes[1],
                        rgbBytes[2],
                        0x00
                    )
                    bytes[6] = BleUtils.getByteSum(bytes)
                    return bytes
                }
            }
            return null
        }

        /**
         * 0x15 0x03 + 组序号
         */
        fun makeGroupRlBrightness(
            brightnessSet: IntArray,
            groupStartIndex: Int = 1
        ): List<IControllerNoEvent> {
            val controllers4RelativeBrightnessWrite = mutableListOf<IControllerNoEvent>()
            brightnessSet.run {
                if (this.isEmpty()) return@run
                val brightnessSize = this.size
                //一组能放下最多14个亮度
                val moreOverBrightness = brightnessSize % 14 == 0
                val lastBrightnessSetLen = if (moreOverBrightness) 14 else brightnessSize % 14
                val groupNum = brightnessSize / 14 + if (moreOverBrightness) 0 else 1
                for (i in 0 until groupNum) {
                    val brightnessLen = if (i == groupNum - 1) lastBrightnessSetLen else 14
                    SafeLog.i(TAG) { "makeBrightnessGroup() i = $i ; len = $brightnessLen" }
                    val brightness4OneGroup = IntArray(brightnessLen)
                    System.arraycopy(this, i * 14, brightness4OneGroup, 0, brightness4OneGroup.size)
                    controllers4RelativeBrightnessWrite.add(
                        ControllerMode.generateWriteControllerByExtBytes(
                            Bytes.relativeBrightnessGroupByte0x15(
                                groupStartIndex + i,
                                brightness4OneGroup
                            )
                        )
                    )
                }
            }
            return controllers4RelativeBrightnessWrite
        }

        /**
         * 0x15 0x03 整组亮度，不需要序号
         */
        fun makeGroupRlBrightnessNoneIndex(brightnessSet: IntArray): List<IControllerNoEvent> {
            val controllers4RelativeBrightnessWrite = mutableListOf<IControllerNoEvent>()
            brightnessSet.run {
                if (this.isEmpty()) return@run
                val extBytes = Bytes.relativeAllBrightnessByte0x15(brightnessSet)
                controllers4RelativeBrightnessWrite.add(
                    ControllerMode.generateWriteControllerByExtBytes(
                        extBytes
                    )
                )
            }
            return controllers4RelativeBrightnessWrite
        }

        /**
         * 0x15 0x02
         */
        fun makeRlBrightnessBySingle(
            info: Info4BleIotDevice,
            brightnessSet: IntArray
        ): List<IControllerNoEvent> {
            val brightMap = BleUtils.generatePosSet(
                brightnessSet,
                ColorPieceConfig.configColorPosSetMaxLen(info)
            )
            val brightnessByteList = mutableListOf<IControllerNoEvent>()
            brightMap?.forEach {
                val bytes = Bytes.relativeBrightnessByte0x15(it.key, it.value)
                brightnessByteList.add(ControllerMode.generateWriteControllerByExtBytes(bytes))
            }
            return brightnessByteList
        }

        private fun checkSpecialSkuBuildColorPiece(
            info: Info4BleIotDevice,
            finalColorSet: IntArray,
            finalBrightnessSet: IntArray?,
            gradual: Boolean,
            posSet: BooleanArray
        ): MutableList<ByteArray> {
            val controllerBytes = mutableListOf<ByteArray>()
            var controllers: MutableList<IControllerNoEvent>?

            Config.checkSupportRealTempVersion(info)?.let {
                controllers = configRealTempColorController(finalColorSet, posSet, info)?.first
                controllers?.forEach {
                    it.getValue()?.let { bytes ->
                        controllerBytes.addAll(bytes)
                    }
                    SafeLog.i(TAG) { "checkSpecialSkuBuildColorPiece() controllers isEmpty = ${controllers?.isEmpty()}" }
                    return controllerBytes
                }
            }

            controllers =
                makeColorStripMultiControllers(info, finalColorSet, finalBrightnessSet)?.first
            controllers?.forEach {
                it.getValue()?.let { bytes ->
                    controllerBytes.addAll(bytes)
                }
            }
            //不支持多包或者分组颜色，直接返回controllerBytes，用外边的单包构建颜色亮度指令
            if (controllerBytes.isEmpty()) return controllerBytes
            val configNormalFucSupport = configNormalFucSupport(info)
            val supportGradual = configNormalFucSupport?.let { it[0] } ?: false
            if (supportGradual) {
                val gradualController = configGradualController(gradual, info)
                gradualController?.getValue()?.let { bytes ->
                    //渐变包不能放在多包和模式包之间
                    controllerBytes.addAll(0, bytes)
                }
            }
            val modeController = ControllerMode.generateWriteControllerByExtBytes(
                byteArrayOf(BleProtocolConstants.sub_mode_color)
            )
            controllerBytes.add(modeController.getNextCommBytes())
            return controllerBytes
        }

        //构建色条多组单包/多包颜色指令
        fun makeColorStripMultiControllers(
            info: Info4BleIotDevice,
            finalColorSet: IntArray,
            finalBrightnessSet: IntArray?
        ): Triple<MutableList<IControllerNoEvent>, MutableList<AbsCmd4Op>?, ColorStrip>? {
            val groupStartIndex = Config.checkSupportColorBrightnessGroup(info)
            if (Config.checkSupportColorTemBrightnessMulti(info)) {
                SafeLog.i(TAG, "configColorModeSnapshot 支持颜色多包(带色温)")
                return configColorTemStripMultiController(
                    finalColorSet,
                    finalBrightnessSet,
                    info
                )
            } else if (groupStartIndex != null) {
                SafeLog.i(TAG, "configColorModeSnapshot 支持分组设置颜色")
                return configColorStripInGroupController(
                    finalColorSet,
                    finalBrightnessSet,
                    info,
                    groupStartIndex
                )
            } else if (Config.checkSupportColorBrightnessMulti(info)) {
                SafeLog.i(TAG, "configColorModeSnapshot 支持颜色多包")
                return configColorStripMultiController(
                    finalColorSet,
                    finalBrightnessSet,
                    info,
                )
            }
            return null
        }
    }


}