package com.govee.base2light.pact.newdetail.sameMode

import android.os.Parcel
import android.os.Parcelable
import com.govee.base2home.scenes.model.DeviceModel
import com.govee.base2light.ac.diy.DiyGraffitiV2
import com.govee.base2light.ac.diy.v2.DiyValue
import com.govee.base2light.ble.scenes.CategoryV1.SceneV1
import com.govee.base2light.kt.comm.Info4BleIotDevice
import com.govee.base2light.mode.music.bean.MusicEffect
import com.govee.base2light.pact.BleIotInfo
import com.govee.base2light.pact.newdetail.sub.SubMode4Color
import com.govee.base2light.pact.newdetail.sub.SubMode4MusicV1

/**
 * Create by xieyingwu on 2023/12/4
 * 默认信息
 */
class SameModeInfo : Info4BleIotDevice {
    var factorVersion = 0
    var subDeviceNum = 0 //子设备数量 拼接的子设备数量 1  2

    //同型号里面设备最高的版本号
    var maxVersionSoft = ""

    //70b3新增设备数
    var deviceInstallCount = 0
    var curBrightness = 254
    var kelvin = 0
    var subMode4Color = SubMode4Color()

    //新音乐模式应用缓存
    var musicEffect: MusicEffect? = null
    var oldMusicEffect: SubMode4MusicV1? = null

    //重组装指令用到
    var diyValue: DiyValue? = null
    var diyGraffitiV2: DiyGraffitiV2? = null
    var scene: SceneV1? = null
    var supportPartBrightness = true

    //是否存在不支持的diy 弹窗提示
    var showDiyModeHint = false

    var allDeviceIsSameGoodsType = true//同型号组内所有设备是否是同一个goodsType的

    //是否显示音乐模式的弹窗提示（同时存在支持和不支持新音乐模式的sku）
    var showMusicModeHint = false

    //是否显示分段亮度的弹窗提示（同时存在支持和不支持分段亮度的sku）
    var showPartBrightnessHint = false

    //H60B0同型号是否同时具备旧固件、新固件
    var h60b0HasOldAndNew = false

    //进入同型号页面，是否toast提示背灯不支持色温
    var isToastColorTemNotice = false

    //是否显示分段的弹窗提示（同时存在支持和不支持分段的sku）
    var showColorPartHint = false

    constructor()
    constructor(parcel: Parcel) : super(parcel)
    constructor(modeInfo: DeviceModel) {
        sku = modeInfo.sku ?: ""
        device = modeInfo.device ?: ""
        goodsType = modeInfo.goodsType
        versionSoft = modeInfo.versionSoft ?: ""
        versionHard = modeInfo.versionHard ?: ""
        pactCode = modeInfo.pactCode
        pactType = modeInfo.pactType
        ic = modeInfo.ic
        segmentCount = modeInfo.segmentCount
        topic = modeInfo.topic ?: ""
        bleAddress = modeInfo.bleAddress ?: ""
        wifiSoftVersion = modeInfo.wifiSoftVersion ?: ""
        wifiHardVersion = modeInfo.wifiHardVersion ?: ""
        deviceName = modeInfo.name ?: ""
        maxVersionSoft = modeInfo.maxVersionSoft ?: ""

        //<editor-fold desc="h70b345 B1/BC这类的需要知道设备最少的安装片数和状态是否变更">
        var minInstallNum = 1
        val installCount = modeInfo.deviceInstallCount
        if (installCount != 0) {
            minInstallNum = installCount
        } else if (modeInfo.deviceSplicingStatus == DeviceModel.splicing_main_device) {
            //类似70B1/BC这样拼接后设备数量要一致的
            minInstallNum = modeInfo.subDeviceNum + 1
        }
        deviceInstallCount = minInstallNum
        deviceNum = deviceInstallCount
        if (modeInfo.ic_sub_1 != 0) {
            icSubList.add(modeInfo.ic_sub_1)
        }
        if (modeInfo.ic_sub_2 != 0) {
            icSubList.add(modeInfo.ic_sub_2)
        }
        //</editor-fold>
    }

    fun changeIllumination(brightnessNew: Int, kelvinNew: Int): Boolean {
        var change = false
        if (brightnessNew != curBrightness) {
            curBrightness = brightnessNew
            change = true
        }
        if (kelvinNew != kelvin) {
            kelvin = kelvinNew
            change = true
        }
        return change
    }

    fun changeBrightness(brightnessNew: Int): Boolean {
        if (brightnessNew != curBrightness) {
            curBrightness = brightnessNew
            return true
        }
        return false
    }

    companion object CREATOR : Parcelable.Creator<SameModeInfo> {
        override fun createFromParcel(parcel: Parcel): SameModeInfo {
            return SameModeInfo(parcel)
        }

        override fun newArray(size: Int): Array<SameModeInfo?> {
            return arrayOfNulls(size)
        }


        /**
         * 将BleIotInfo转换成SameModeInfo对象
         */
        fun transformInfo4BleIotDevice(bleIotInfo: BleIotInfo): SameModeInfo {
            return SameModeInfo().apply {
                sku = bleIotInfo.sku ?: ""
                device = bleIotInfo.device ?: ""
                deviceName = bleIotInfo.deviceName ?: ""
                goodsType = bleIotInfo.goodsType
                spec = bleIotInfo.spec ?: ""
                bleName = bleIotInfo.bleName ?: ""
                bleAddress = bleIotInfo.bleAddress ?: ""
                wifiMac = bleIotInfo.wifiMac ?: ""
                topic = bleIotInfo.topic ?: ""
                secretCode = bleIotInfo.secretCode ?: ""
                versionSoft = bleIotInfo.versionSoft ?: ""
                versionHard = bleIotInfo.versionHard ?: ""
                dspVersion = bleIotInfo.dspVersion ?: ""
                wifiSoftVersion = bleIotInfo.wifiSoftVersion ?: ""
                wifiHardVersion = bleIotInfo.wifiHardVersion ?: ""
                wifiDspVersion = bleIotInfo.wifiSoftVersion ?: ""
                pactType = bleIotInfo.pactType
                pactCode = bleIotInfo.pactCode
                ic = bleIotInfo.ic
                sceneSeg = bleIotInfo.sceneSeg
            }

        }
    }

}