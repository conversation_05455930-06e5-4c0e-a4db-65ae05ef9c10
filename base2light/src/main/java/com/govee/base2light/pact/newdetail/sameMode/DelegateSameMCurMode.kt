package com.govee.base2light.pact.newdetail.sameMode

import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.ViewModelProvider
import com.govee.base2light.ac.diyNew.config.DiyNewConfig
import com.govee.base2light.pact.newdetail.BaseViewDelegate
import com.govee.base2light.pact.newdetail.DeviceDetailConfig
import com.govee.base2light.pact.newdetail.IApplyFuc
import com.govee.base2light.pact.newdetail.IAutoDelegateImp
import com.govee.base2light.pact.newdetail.IColorModeFuc
import com.govee.base2light.pact.newdetail.IDiyFuc
import com.govee.base2light.pact.newdetail.IIlluminationFuc
import com.govee.base2light.pact.newdetail.IMicFuc
import com.govee.base2light.pact.newdetail.IMusicFuc
import com.govee.base2light.pact.newdetail.ISceneFuc
import com.govee.base2light.pact.newdetail.content.color.ColorViewModel
import com.govee.base2light.pact.newdetail.content.color.DelegateColor
import com.govee.base2light.pact.newdetail.content.diy.DelegateDiy
import com.govee.base2light.pact.newdetail.content.diy.DiyVM
import com.govee.base2light.pact.newdetail.content.illumination.DelegateLighting
import com.govee.base2light.pact.newdetail.content.music.DelegateMusic
import com.govee.base2light.pact.newdetail.content.operate.DelegateOperate
import com.govee.base2light.pact.newdetail.content.scene.DelegateScene
import com.govee.base2light.pact.newdetail.content.scene.SceneViewMode
import com.govee.base2light.pact.newdetail.content.scene.partScenes.DelegateSegmentScene
import com.govee.base2light.pact.newdetail.content.scrawl.DelegateHighColor
import com.govee.base2light.pact.newdetail.content.scrawl.DelegateNewHighColor
import com.govee.base2light.pact.newdetail.content.userShare.DelegateShare
import com.govee.base2light.pact.newdetail.net.Component
import com.govee.base2light.pact.newdetail.sameMode.config.SameModeDetailConfig
import com.govee.base2light.pact.newdetail.sameMode.vm.SameModeDetailVM
import com.govee.base2light.pact.newdetail.sameMode.vm.SameModelCmdOp
import com.govee.ui.R
import com.govee.ui.dialog.HintDialog1
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.ToastUtil
import java.util.concurrent.ConcurrentHashMap

/**
 *Create by hey on 2023/11/23
 *$当前模式容器
 */
class DelegateSameMCurMode<VM : SameModeDetailVM>(
    private val ac: AppCompatActivity,
    private val container: ViewGroup,
    vmClass: Class<VM>
) : BaseViewDelegate() {
    private val vm = ViewModelProvider(ac)[vmClass]

    /**记录支持的模式列表*/
    private val map4Mode = ConcurrentHashMap<Int, BaseViewDelegate?>()

    override fun onBind() {
        super.onBind()
        initOb()
        initCommonVm()
        createView()
    }

    private fun createView() {
        val deviceInfo = vm.sameModeInfo
        val modeList = vm.getModeList()
        /**颜色模式*/
        if (supportColorMode() && (vm is IColorModeFuc)) {
            val configColorDelegate =
                SameModeDetailConfig.getColorViewDelegate(ac, container, deviceInfo)
                    ?: (DeviceDetailConfig.getColorViewDelegate(ac, container, deviceInfo))
            if (configColorDelegate == null) {
                val colorViewModel = ViewModelProvider(ac)[ColorViewModel::class.java].apply {
                    vm.initColorViewModel(this)
                    initColor4Device(vm.getInitColorData())
                    iColorModeFuc = vm
                }
                val colorDelegate = DelegateColor(
                    ac,
                    container,
                    deviceInfo,
                    colorViewModel
                )
                map4Mode[Component.sub_component_type_mode_color] = colorDelegate
            } else {
                map4Mode[Component.sub_component_type_mode_color] = configColorDelegate
            }
        }

        /**涂鸦模式*/
        val scrawlDelegate = SameModeDetailConfig.getScrawlViewDelegate(ac, container, deviceInfo)
            ?: (DeviceDetailConfig.getScrawlViewDelegate(ac, container, deviceInfo)
                ?: run {
                    val supportDiyNewEdit =
                        DiyNewConfig.isSupportDiyNewEdit(deviceInfo.goodsType, deviceInfo.sku)
                    if (supportDiyNewEdit) {
                        DelegateNewHighColor(
                            ac,
                            container,
                            deviceInfo,
                            vm as IApplyFuc,
                            null,
                            false
                        )
                    } else {
                        DelegateHighColor(ac, container, deviceInfo, vm as IApplyFuc, null, false)
                    }
                })
        map4Mode[Component.sub_component_type_mode_tuYa] = scrawlDelegate
        if (scrawlDelegate is IAutoDelegateImp) {
            scrawlDelegate.setDeviceFucImp(vm as IDiyFuc)
        }
        /**DIY模式*/
        val diyDelegate =
            SameModeDetailConfig.getDiyViewDelegate(ac, container, deviceInfo)
                ?: (DeviceDetailConfig.getDiyViewDelegate(ac, container, deviceInfo)
                    ?: DelegateDiy(ac, container, vm.sameModeInfo, vm as IDiyFuc))
        if (diyDelegate is IAutoDelegateImp) {
            diyDelegate.setDeviceFucImp(vm as IDiyFuc)
        }

        map4Mode[Component.sub_component_type_mode_diy] = diyDelegate
        /**音乐模式*/
        val musicDelegate =
            SameModeDetailConfig.getMusicViewDelegate(ac, container, deviceInfo)
                ?: (DeviceDetailConfig.getMusicViewDelegate(ac, container, deviceInfo)
                    ?: DelegateMusic(
                        ac,
                        container,
                        vm.sameModeInfo,
                        vm as IMicFuc,
                        vm as IMusicFuc
                    ))
        if (musicDelegate is IAutoDelegateImp) {
            musicDelegate.setDeviceFucImp(vm as IMicFuc)
        }
        map4Mode[Component.sub_component_type_mode_music] = musicDelegate

        /**照明模式*/
        if (vm is IIlluminationFuc) {
            val lightingDelegate =
                SameModeDetailConfig.getLightingViewDelegate(ac, container, deviceInfo)
                    ?: DeviceDetailConfig.getLightingViewDelegate(ac, container, deviceInfo)
                    ?: DelegateLighting(ac, container, deviceInfo, vm as IIlluminationFuc)
            map4Mode[Component.sub_component_type_mode_illumination] = lightingDelegate
        }
        /**场景模式*/
        var sceneDelegate =
            SameModeDetailConfig.getSceneViewDelegate(ac, container, deviceInfo)
                ?: (DeviceDetailConfig.getSceneViewDelegate(ac, container, deviceInfo)
                    ?: DelegateScene(ac, container, vm.sameModeInfo, iSceneFuc = vm as ISceneFuc))
        if (sceneDelegate is DelegateSegmentScene<*>) {
            //目前同型号不支持分段场景
            sceneDelegate =
                DelegateScene(ac, container, vm.sameModeInfo, iSceneFuc = vm as ISceneFuc)
        }
        val sceneViewMode = ViewModelProvider(ac)[SceneViewMode::class.java]
        sceneViewMode.fromSameGroup = true
        sceneViewMode.registerEvent(false)
        map4Mode[Component.sub_component_type_mode_scene] = sceneDelegate
        /**用户分享模式*/
        val shareDelegate =
            DeviceDetailConfig.getShareViewDelegate(ac, container, deviceInfo) ?: DelegateShare(
                ac, container, deviceInfo
            )
        if (shareDelegate is IAutoDelegateImp) {
            shareDelegate.setDeviceFucImp(vm as IDiyFuc)
        }
        map4Mode[Component.sub_component_type_mode_share] = shareDelegate
        /**操作模式*/
        if (modeList.contains(Component.sub_component_type_mode_operate)) {
            val operateDelegate =
                DeviceDetailConfig.getOperateViewDelegate(ac, container, deviceInfo)
                    ?: DelegateOperate()
            map4Mode[Component.sub_component_type_mode_operate] = operateDelegate
        }
    }

    private fun supportColorMode(): Boolean {
        if (vm is SameModelCmdOp) {
            val sameModeInfo = SameModeDetailConfig.getModelConfig(vm.sameModeInfo)
            if (sameModeInfo == null) {
                return true
            } else {
                return sameModeInfo.supportColorMode
            }
        } else {
            return true
        }
    }

    private fun initOb() {
        /**监听当前选中模式*/
        vm.curChooseModeChange.observe(ac) {
            if (!hasBind) return@observe
            SafeLog.i(TAG) { "initOb() curChooseModeChange = $it" }
            if (it == true) {
                var curChooseMode = vm.curChooseMode
                if (vm.showingModeList.size == 1 && vm.showingModeList[0] != curChooseMode) {
                    curChooseMode = vm.showingModeList[0]
                    vm.changeChooseMode(0)
                }
                checkFucHint(vm.curChooseMode)
                SafeLog.i(TAG) { "initOb() curChooseMode = $curChooseMode" }
                showModeDelegate(curChooseMode)
            }
        }
    }

    private fun checkFucHint(curChooseMode: Int) {
        if (curChooseMode == Component.sub_component_type_mode_music) {
            if (vm.showMicModeHint && (vm as SameModelCmdOp).inMicMode) {
                vm.showMicModeHint = false
                showHindDialog(ResUtil.getString(R.string.base2light_no_support_pick_up_by_phone))

            } else if (vm.showMusicModeHint) {
                vm.showMusicModeHint = false
                showHindDialog(ResUtil.getString(R.string.hint_no_support_new_music))
            }
        }
        if (curChooseMode == Component.sub_component_type_mode_color && vm.showPartBrightnessHint) {
            vm.showPartBrightnessHint = false
            showHindDialog(ResUtil.getString(R.string.no_support_section_color_now))
        }
        if (curChooseMode == Component.sub_component_type_mode_diy && vm.showDiyModeHint) {
            vm.showDiyModeHint = false
            showHindDialog(ResUtil.getString(R.string.b2light_part_device_not_support_new_diy))
        }
        if (curChooseMode == Component.sub_component_type_mode_color && vm.showColorPartHint) {
            vm.showColorPartHint = false
            showHindDialog(ResUtil.getString(R.string.part_device_no_support_color_section))
        }
    }

    private fun showHindDialog(hintStr: String) {
        HintDialog1.createHintDialog1(
            ac,
            hintStr,
            ResUtil.getString(R.string.hint_done_got_it)
        ).setEventKey(this.TAG).show()
    }

    private fun showModeDelegate(curMode: Int) {
        if (vm.groupDetailsModel.noDevices()) {
            return
        }
        if (!map4Mode.containsKey(curMode)) {
            SafeLog.e(TAG) { "showModeDelegate() curMode = $curMode is not config ui!" }
        }
        val curModeInShowingList = vm.showingModeList.contains(curMode)
        SafeLog.i(TAG) { "showModeDelegate() curModeInShowingList = $curModeInShowingList" }
        val keys = map4Mode.keys()
        for (key in keys) {
            /**显示具体模式的条件是当前模式在展示列表*/
            val choose = curModeInShowingList && key == curMode
            if (choose) {
                map4Mode[key]?.let {
                    if (!it.hasBind) {
                        SafeLog.i("DengFei_debug") { "showModeDelegate() bind() $key" }
                        SafeLog.i("DengFei_debug") { "showModeDelegate() bind() $it" }
                        it.bind()
                    }
                    if (!it.isVisible) {
                        it.visible()
                    }
                }
            } else {
                map4Mode[key]?.invisible()
            }
        }
    }

    /**
     * 进入同型号，弹toast
     */
    private fun checkToast() {
        if (vm.isToastColorTemNotice) {
            vm.isToastColorTemNotice = false
            ToastUtil.getInstance().toast(ResUtil.getString(R.string.same_model_color_tem_notice))
        }
    }

    private fun unbindModeDelegate() {
        val values = map4Mode.values
        for (value in values) {
            value?.unbind()
        }
    }

    private fun visCurModeDelegate(curMode: Int) {
        SafeLog.i(TAG) { "visCueModeDelegate() curMode = $curMode" }
        val notFound = map4Mode[curMode]?.let {
            if (!it.isVisible) {
                if (!it.hasBind) {
                    checkFucHint(curMode)
                    SafeLog.i(TAG) { "initOb() curChooseMode = $curMode" }
                    showModeDelegate(curMode)
                    checkToast()
                }
                it.visible()
            }
            false
        } ?: true
        if (notFound) {
            SafeLog.e(TAG) { "visCueModeDelegate() curMode = $curMode is not config ui!" }
        }
    }

    /**
     * 部分VM在其它模式也会用到，需要提前创建
     */
    private fun initCommonVm() {
        val diyVm = ViewModelProvider(ac)[DiyVM::class.java]
        diyVm.initInfo(vm.sameModeInfo, vm as IDiyFuc)
    }

    private fun goneModeDelegate() {
        val values = map4Mode.values
        for (value in values) {
            value?.invisible()
        }
    }

    override fun onUnbind() {
        super.onUnbind()
        unbindModeDelegate()
    }

    override fun onVisible() {
        super.onVisible()
        visCurModeDelegate(vm.curChooseMode)
    }

    override fun onInvisible() {
        super.onInvisible()
        goneModeDelegate()
    }

}
