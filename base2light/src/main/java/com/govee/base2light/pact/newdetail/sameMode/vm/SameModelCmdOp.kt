package com.govee.base2light.pact.newdetail.sameMode.vm

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModelProvider
import androidx.lifecycle.ViewModelStoreOwner
import androidx.lifecycle.viewModelScope
import com.govee.base2home.Constant
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.iot.AbsCmd
import com.govee.base2home.scenes.model.DeviceModel
import com.govee.base2kt.utils.BleUtils
import com.govee.base2kt.utils.ColorUtils
import com.govee.base2kt.utils.ColorUtils.Companion.toColor
import com.govee.base2light.ac.club.EffectData
import com.govee.base2light.ac.diy.DiyGraffitiV2
import com.govee.base2light.ac.diy.EventDiyApplyResult
import com.govee.base2light.ac.diy.v2.DiySendData
import com.govee.base2light.ac.diyNew.details.DiyNewCacheCenter
import com.govee.base2light.ac.diyNew.parse.EffectOtherInfo
import com.govee.base2light.ac.diyNew.parse.RgbIcGraffitiShare0x08
import com.govee.base2light.ble.comm.MultipleControllerCommV1
import com.govee.base2light.ble.controller.AbsComposeControllerWithOpResult
import com.govee.base2light.ble.controller.AbsSingleController
import com.govee.base2light.ble.controller.BleProtocolConstants
import com.govee.base2light.ble.controller.DefControllerNoEvent4MultiImp
import com.govee.base2light.ble.controller.DiyControllerNoEventV3
import com.govee.base2light.ble.controller.IComposeController
import com.govee.base2light.ble.controller.IControllerNoEvent
import com.govee.base2light.ble.controller.ModeControllerNoEvent
import com.govee.base2light.ble.mic.controller.AbsMicController
import com.govee.base2light.ble.mic.controller.MicColorCmdController
import com.govee.base2light.ble.mic.controller.MicSetRgbController
import com.govee.base2light.effectPlay.effect.mode.SubModeNewDiy
import com.govee.base2light.group.iot.IotCmdOp
import com.govee.base2light.kt.ble.Compose4DefWrite
import com.govee.base2light.kt.ble.Compose4DefWrite4Multi
import com.govee.base2light.kt.comm.AbsCmd4Op
import com.govee.base2light.kt.comm.Info4BleIotDevice
import com.govee.base2light.kt.comm.MultiCmd4OpWriteResult
import com.govee.base2light.kt.general_controller.ControllerMode
import com.govee.base2light.kt.general_controller.ControllerSwitch
import com.govee.base2light.kt.general_controller.MultiController
import com.govee.base2light.kt.iot.Cmd4PtReal
import com.govee.base2light.pact.newdetail.DeviceDetailConfig
import com.govee.base2light.pact.newdetail.IBrightnessFuc
import com.govee.base2light.pact.newdetail.IColorModeFuc
import com.govee.base2light.pact.newdetail.IConnectStatus
import com.govee.base2light.pact.newdetail.IDiyFuc
import com.govee.base2light.pact.newdetail.IIlluminationFuc
import com.govee.base2light.pact.newdetail.IMicFuc
import com.govee.base2light.pact.newdetail.IMusicFuc
import com.govee.base2light.pact.newdetail.IOperateFuc
import com.govee.base2light.pact.newdetail.ISceneFuc
import com.govee.base2light.pact.newdetail.ble.MtuConfig
import com.govee.base2light.pact.newdetail.config.fuc.BrightnessConfig
import com.govee.base2light.pact.newdetail.config.fuc.ColorPieceConfig
import com.govee.base2light.pact.newdetail.config.fuc.KelvinConfig
import com.govee.base2light.pact.newdetail.config.fuc.ModeConfig
import com.govee.base2light.pact.newdetail.config.fuc.MusicConfig
import com.govee.base2light.pact.newdetail.config.fuc.OldMusic
import com.govee.base2light.pact.newdetail.config.fuc.color.ColorModeControllerConfig
import com.govee.base2light.pact.newdetail.config.fuc.color.ColorStrip
import com.govee.base2light.pact.newdetail.config.fuc.color.Config
import com.govee.base2light.pact.newdetail.content.color.ColorViewModel
import com.govee.base2light.pact.newdetail.content.diy.DiyApplyVM.Companion.makePtUrlCallBack
import com.govee.base2light.pact.newdetail.content.diy.DiyEffectBean
import com.govee.base2light.pact.newdetail.content.diy.DiyVM
import com.govee.base2light.pact.newdetail.content.mic.MicController
import com.govee.base2light.pact.newdetail.content.mic.MicSpConfig
import com.govee.base2light.pact.newdetail.content.music.IMusicSupportEffect
import com.govee.base2light.pact.newdetail.content.scene.BaseSceneViewMode
import com.govee.base2light.pact.newdetail.net.Component
import com.govee.base2light.pact.newdetail.sameMode.SameModeInfo
import com.govee.base2light.pact.newdetail.sameMode.communication.OldGroupOp
import com.govee.base2light.pact.newdetail.sameMode.config.SameModeDeviceItem
import com.govee.base2light.pact.newdetail.sub.ISubModeMusic
import com.govee.base2light.pact.newdetail.sub.SubMode4Color
import com.govee.base2light.widget.diy.bean.EffectCellGraffiti
import com.govee.base2light.widget.diy.constant.DiyConstant
import com.govee.base2light.widget.new_diy.NewDiyEditConfig
import com.govee.ble.BleController
import com.govee.ble.group.v1.GroupBleConfig
import com.govee.ble.group.v1.GroupControllerV1
import com.govee.kt.isAcOk
import com.govee.ui.R
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.Arrays
import java.util.concurrent.ConcurrentHashMap
import kotlin.math.roundToInt

open class SameModelCmdOp : SameModeDetailVM(), ISceneFuc, IDiyFuc, IMicFuc, IMusicFuc,
    IColorModeFuc, IIlluminationFuc, IConnectStatus, IBrightnessFuc, IOperateFuc {

    /**记录当前设备的实际模式*/
    private var curOpModeValue: Int = Component.sub_component_type_mode_unknown

    /**亮度变化通知*/
    final override val brightnessChange4LD: MutableLiveData<Boolean?> = MutableLiveData()

    /**开关变化通知*/
    final override val switchChange4LD: MutableLiveData<Boolean?> = MutableLiveData()
    override val closeMicModeLD: MutableLiveData<Boolean?> = MutableLiveData()
    override val micStatus: MutableLiveData<Boolean?> = MutableLiveData()
    override val bleStatusLDMic: MutableLiveData<Int?> = MutableLiveData()
    override val operateInfoChange4LD: MutableLiveData<Boolean> = MutableLiveData()
    var autoCmdOp: SameModelCmdOp? = null
    var colorViewModel: ColorViewModel? = null

    /**是否处于mic模式*/
    var inMicMode: Boolean = false
    private val groupOp: OldGroupOp = OldGroupOp(connectChangedListener)

    override fun getDiyEffectId(): Int {
        return 0
    }

    override fun getSceneCode(): Int {
        return -1
    }

    override fun getCurMusic(): ISubModeMusic? {
        return null
    }

    override fun inSameModel(): Boolean {
        return true
    }

    override fun initColorViewModel(colorViewModel: ColorViewModel) {
        colorViewModel.isSameModelDetail = (true)
        this.colorViewModel = colorViewModel
    }

    override fun getInitColorData(): SubMode4Color {
        return autoCmdOp?.getInitColorData() ?: sameModeInfo.subMode4Color.apply {
            colorBrightnessSet = IntArray(ColorPieceConfig.configColorPiece(sameModeInfo)) {
                100
            }
        }
    }

    override fun syncKelvin(kelvinNew: Int) {
        colorViewModel?.curColor?.value?.kelvin = kelvinNew
        sameModeInfo.kelvin = kelvinNew
        illuminationChange4LD.postValue(true)
        autoCmdOp?.syncKelvin(kelvinNew)
    }

    override fun syncColorStrip(strip: ColorStrip) {
    }

    override fun curIsColorMode(): Boolean {
        return curOpModeValue == Component.sub_component_type_mode_color
    }

    override fun syncIlluminationInfo(brightnessNew: Int, kelvinNew: Int) {
        colorViewModel?.curColor?.value?.run {
            kelvin = kelvinNew
        }
        SafeLog.i(TAG) { "syncIlluminationInfo() brightnessNew = $brightnessNew ; kelvinNew = $kelvinNew" }
        val changeIllumination = sameModeInfo.changeIllumination(brightnessNew, kelvinNew)
        SafeLog.i(TAG) { "illuminationChange() brightnessNew = $brightnessNew ; kelvinNew = $kelvinNew ; changeIllumination = $changeIllumination" }
        if (changeIllumination) {
            brightnessChange4LD.postValue(true)
            illuminationChange4LD.postValue(true)
            val pieceNum = ColorPieceConfig.configColorPiece(sameModeInfo)
            SafeLog.i(TAG) { "illuminationChange() pieceNum = $pieceNum ; kelvin = $kelvinNew" }
            sameModeInfo.subMode4Color.syncIlluminationMode(pieceNum, kelvinNew)
            colorViewModel?.syncByOtherWay(sameModeInfo, sameModeInfo.subMode4Color)
        }
        autoCmdOp?.syncIlluminationInfo(brightnessNew, kelvinNew)
    }

    override fun syncBrightnessNew(brightnessNew: Int) {
        SafeLog.i(TAG) { "syncBrightnessNew() brightnessNew = $brightnessNew" }
        val changeBrightness = sameModeInfo.changeBrightness(brightnessNew)
        if (changeBrightness) {
            brightnessChange4LD.postValue(true)
        }
    }

    override fun brightnessCur(): Int = sameModeInfo.curBrightness


    override fun illuminationCur(): IntArray {
        return intArrayOf(sameModeInfo.curBrightness, sameModeInfo.kelvin)
    }

    override fun colorTemRange(): IntArray {
        return KelvinConfig.kelvinRange(sameModeInfo)
    }

    override fun colorTemRange4Showing(): IntArray {
        return KelvinConfig.kelvin4ShowingRangeAndTranslateVersion(sameModeInfo)
    }

    override val illuminationChange4LD: MutableLiveData<Boolean?> = MutableLiveData()

    private val diyVmOp by lazy {
        ViewModelProvider(BaseApplication.getBaseApplication().topActivity as ViewModelStoreOwner)[DiyVM::class.java]
    }
    private val sceneVmOp by lazy {
        BaseSceneViewMode()
    }

    override fun applyController(
        controller: IComposeController,
        modeType: Int,
        closeMicModeReason: String?,
        cmd: MutableList<AbsCmd4Op>?,
        onlyBle: Boolean
    ) {

        val byteArrays = controller.getAllBytes4Command()
        if (byteArrays.isEmpty() || checkConnectAndSetResult(controller)) {
            return
        }
        //当前是mic模式，需要延迟发送其它指令。若用颜色模式实现的mic，在指令发送成功后，设备会回赋一条颜色模式切成功的指令；
        //这个回复会导致清除下一条发送的compose指令
        val delaySendMs = if (inMicMode) 150L else 0L
        /**统计发送指令的类型*/
        if (!closeMicModeReason.isNullOrEmpty()) {
            closeMicMode(closeMicModeReason)
        }
        scopeLaunchSingleException {
            delay(delaySendMs)
            //需要重组装指令发送给其它设备
            var assembleCmdSuc = false
            if (needAssembleCmdType && groupDetailsModel.devices.size > 1) {
                SafeLog.i(TAG) { "applyController() modeType = $modeType" }
                assembleCmdTypePreCheck(modeType, groupDetailsModel.devices, colorViewModel?.controlAll?.value, colorViewModel?.curColor?.value)
                when (modeType) {
                    Component.cmd_type_mode_color_strip,
                    Component.cmd_type_mode_color_single -> {
                        assembleCmdSuc = makeDifColorController(controller)
                    }

                    Component.cmd_type_mode_color_header_brightness -> {
                        assembleCmdSuc = makeDifBrightnessController(controller)
                    }

                    Component.cmd_type_mode_color_tem -> {
                        assembleCmdSuc = makeDifColorTemController(controller)
                    }

                    Component.cmd_type_mode_color_relative_brightness -> {
                        assembleCmdSuc = makeDifRelativeBrightnessController(controller)
                    }

                    Component.sub_component_type_mode_illumination -> {
                        colorViewModel?.curColor?.value?.kelvin?.run {
                            for (device in groupDetailsModel.devices) {
                                val sameModeInfo = SameModeInfo(device)
                                val curBrightness = <EMAIL>
                                val info = SameModeInfo(device)
                                val newKelvin = KelvinConfig.calibrationKelvin(info, this)
                                SafeLog.i(TAG) { "makeDifColorTemController() newKelvin = $newKelvin" }
                                val kelvinPair = configKelvinController(sameModeInfo, newKelvin)
                                    ?: KelvinConfig.configKelvinController(sameModeInfo, newKelvin)
                                    ?: return@run
                                val brightnessPair =
                                    configBrightnessController(sameModeInfo, curBrightness)
                                        ?: BrightnessConfig.configBrightnessController(
                                            sameModeInfo,
                                            curBrightness
                                        )
                                val completeCommBytes = mutableListOf<ByteArray>()
                                val newController =
                                    Compose4DefWrite.makeWriteControllers(mutableListOf<IControllerNoEvent>().apply {
                                        add(brightnessPair.first)
                                        /**照明模式传递快照完整指令仅需要传递模式包-亮度指令统一补充*/
                                        add(kelvinPair.first.apply {
                                            this.getValue()?.let { bytes ->
                                                completeCommBytes.addAll(bytes)
                                            }
                                        })
                                        assembleCmdSuc = true
                                    }) { suc ->
                                        SafeLog.i(TAG) { "opComm() suc = $suc" }
                                        controller.result2Suc()
                                    }
                                val cmds = mutableListOf<AbsCmd4Op>().apply {
                                    brightnessPair.second?.let { cmd ->
                                        this.add(cmd)
                                    }
                                    kelvinPair.second?.let { cmd ->
                                        this.add(cmd)
                                    }
                                }
                                MultiCmd4OpWriteResult.makeMultiCmdOpWriteResult(cmds) { suc, _ ->
                                    SafeLog.i(TAG) { "opComm() iot suc = $suc" }
                                    controller.result2Suc()
                                }
                                groupOp.sendComposeControllerSingle(
                                    device,
                                    newController,
                                    cmds
                                )
                            }
                        }
                    }

                    Component.cmd_type_mode_color_gradual -> {
                        assembleCmdSuc = makeDifGradualController(controller)
                    }

                    Component.sub_component_type_mode_music -> {
                        //是否是旧音乐模式
                        var isOldMusic = false
                        //是否存在只支持旧音乐模式的设备
                        val oldDeviceList: MutableList<SameModeInfo> = ArrayList()
                        var iMusicOp: IMusicSupportEffect? = null
                        sameModeInfo.musicEffect?.run {
                            for (device in groupDetailsModel.devices) {
                                val sameModeInfo = SameModeInfo(device)
                                val iMusicSupportEffect = DeviceDetailConfig.getMusicSupportEffect(
                                    BaseApplication.getBaseApplication().topActivity as AppCompatActivity,
                                    sameModeInfo
                                )
                                if (!isOldMusic) {
                                    iMusicSupportEffect?.getDefaultMusicEffect()?.forEach {
                                        if (it.subMusicMode.musicCode == this.subMusicMode.musicCode) {
                                            if (!it.isNewMusic) {
                                                isOldMusic = true
                                            }
                                            return@forEach
                                        }
                                    }
                                }
                                if (iMusicSupportEffect == null) {
                                    oldDeviceList.add(sameModeInfo)
                                } else {
                                    iMusicOp = iMusicSupportEffect
                                }
                                iMusicSupportEffect?.applyDefaultMusic(this)?.let {
                                    if (it is AbsComposeControllerWithOpResult) {
                                        it.changeOpResult { suc ->
                                            /**同型号下-忽略指令的结果准确性-都触发成功指令操作*/
                                            LogInfra.Log.i(TAG, "applyController() suc = $suc")
                                            controller.result2Suc()
                                        }
                                    }
                                    assembleCmdSuc = true
                                    groupOp.sendComposeControllerSingle(
                                        device,
                                        it,
                                        ArrayList()
                                    )
                                }
                            }
                            //点击的是支持新音乐模式设备的音乐模式中的 旧音乐 item，此时需要把该效果同步发给旧设备
                            if (isOldMusic && oldDeviceList.isNotEmpty()) {
                                for (sameModeInfo in oldDeviceList) {
                                    iMusicOp?.applyDefaultMusic(this)?.let {
                                        if (it is AbsComposeControllerWithOpResult) {
                                            it.changeOpResult { suc ->
                                                /**同型号下-忽略指令的结果准确性-都触发成功指令操作*/
                                                LogInfra.Log.i(TAG, "applyController() suc = $suc")
                                                controller.result2Suc()
                                            }
                                        }
                                        assembleCmdSuc = true
                                        groupOp.sendComposeControllerSingle(
                                            DeviceModel(sameModeInfo),
                                            it,
                                            ArrayList()
                                        )
                                    }
                                }
                            }
                        }
                        sameModeInfo.oldMusicEffect?.run {
                            for (device in groupDetailsModel.devices) {
                                val sameModeInfo = SameModeInfo(device)
                                val mController = MusicConfig.configMusicController4Old(
                                    sameModeInfo,
                                    OldMusic(effect.toByte(), sensitivity, subEffect, auto, rgb)
                                )

                                mController?.let {
                                    val newController =
                                        Compose4DefWrite4Multi.makeWriteController4Multi(
                                            it.first, sameModeInfo.getCmdDelayMs()
                                        ) {
                                            controller.result2Suc()
                                        }
                                    assembleCmdSuc = true
                                    groupOp.sendComposeControllerSingle(
                                        device,
                                        newController,
                                        it.second ?: ArrayList()
                                    )
                                }
                            }
                        }
                        sameModeInfo.musicEffect = null
                        sameModeInfo.oldMusicEffect = null
                    }

                    Component.sub_component_type_mode_scene -> {
                        assembleCmdSuc = makeDifSceneController(controller)
                    }

                    Component.sub_component_type_mode_diy -> {

                        sameModeInfo.diyValue?.run {
                            for (device in groupDetailsModel.devices) {// 5
                                val sameModeInfo = SameModeInfo(device)
                                val mController =
                                    diyVmOp.getCompleteController(sameModeInfo, this) {
                                        controller.result2Suc()
                                    }
                                mController?.let {
                                    assembleCmdSuc = true
                                    groupOp.sendComposeControllerSingle(
                                        device,// 2
                                        mController,
                                        ArrayList()
                                    )
                                }

                                SafeLog.i("DengFei_debug") { "重组装diy指令 device = ${sameModeInfo.deviceName} " }
                                mController?.getAllBytes4Command()?.forEach {
                                    SafeLog.i("DengFei_debug") {
                                        "applyController() ${
                                            BleUtils.bytesToHexString(
                                                it
                                            )
                                        }"
                                    }
                                }
                            }
                        }
                        sameModeInfo.diyValue = null
                    }

                    Component.sub_component_type_mode_tuYa -> {
                        sameModeInfo.diyGraffitiV2?.run {
                            assembleCmdSuc = makeDifGraController(controller, this)
                        }
                    }
                }
            }
            if (!assembleCmdSuc) {
                delay(delaySendMs)
                val useMTUController = controller.isUseMTUController()
                if (useMTUController) {
                    applyMtuController(controller, modeType, protocol = controller.getProtocol(), closeMicModeReason, controller.getEffectBytes(), onlyBle)
                } else if (!cmd.isNullOrEmpty()) {
                    doCommController4SyncCmdWrite(controller, cmd)
                } else {
                    val cmd4Ops: MutableList<AbsCmd4Op> = ArrayList()
                    IotCmdOp.makeController4Cmd(sameModeInfo, controller)?.run {
                        cmd4Ops.add(this)
                    }
                    if (cmd4Ops.isEmpty()) {
                        doCommController(controller, onlyBle)
                    } else {
                        groupOp.sendComposeController(controller, cmd4Ops)
                    }
                }
            }
        }
        AnalyticsRecorder.getInstance()
            .recordUseCount(ParamKey.group_same_model, ParamFixedValue.send_cmd_in_detail)
    }

    override fun applyIOTCmd(
        modeType: Int,
        closeMicModeReason: String?,
        cmd: MutableList<AbsCmd4Op>
    ) {
        var assembleCmdSuc = false
        if (needAssembleCmdType && groupDetailsModel.devices.size > 1) {
            sameModeInfo.diyValue?.run {
                for (device in groupDetailsModel.devices) {// 5
                    val sameModeInfo = SameModeInfo(device)
                    val mController =
                        diyVmOp.getCompleteController(sameModeInfo, this) {
                            cmd[0].controllerWriteResultCallback?.invoke(false, true, "")
                        }
                    mController?.let {
                        assembleCmdSuc = true
                        groupOp.sendComposeControllerSingle(
                            device,// 2
                            mController,
                            ArrayList()
                        )
                    }

                    SafeLog.i("DengFei_debug") { "重组装diy指令 device = ${sameModeInfo.deviceName} " }
                }
            }
            sameModeInfo.diyValue = null
        } else {
            groupOp.sendIotCmds(cmd)
        }
    }

    private fun DeviceModel(info: SameModeInfo): DeviceModel {
        return DeviceModel().apply {
            sku = info.sku
            device = info.device
            bleAddress = info.bleAddress
            topic = info.topic
            goodsType = info.goodsType
            bleName = info.bleName
        }
    }

    private fun makeDifRelativeBrightnessController(controller: IComposeController): Boolean {
        if (!hasDiffPartBrightnessDevice) {
            return false
        }
        val posSet = colorViewModel?.curColor?.value?.posSet ?: return false
        val brightness = colorViewModel?.curColor?.value?.brightness ?: return false
        SafeLog.i(TAG) { "makeDifRelativeBrightnessController() brightness = $brightness" }
        var hadDif = false
        deviceList.forEachIndexed { index, info ->
            //重新给缓存address赋值
            if (info.bleAddress.isEmpty()) {
                info.bleAddress = groupDetailsModel.devices[index].bleAddress
            }
            //支持相对亮度的才发送指令
            if (info.supportPartBrightness) {
                SafeLog.i("DengFei_debug") { "make() 不支持分段亮度 ${info.deviceName}" }

                val mController = ColorModeControllerConfig.configRelativeBrightnessController(
                    brightness,
                    posSet,
                    info
                )
                    ?: configRelativeBrightnessController(brightness, posSet, info)
                mController?.let {
                    val newController = Compose4DefWrite.makeWriteController(it) { _ ->
                        controller.result2Suc()
                    }
                    hadDif = true
                    groupOp.sendComposeControllerSingle(
                        DeviceModel(info),
                        newController,
                        ArrayList()
                    )
                }
            }
        }
        return hadDif
    }

    private fun makeDifGradualController(controller: IComposeController): Boolean {
        SafeLog.i(TAG) { "makeDifGradualController() " }
        val gradual = colorViewModel?.curColor?.value?.gradual ?: return false
        SafeLog.i(TAG) { "makeDifGradualController() gradual = $gradual" }
        var hadDif = false
        for (device in groupDetailsModel.devices) {
            val info = SameModeInfo(device)
            val mController = ColorModeControllerConfig.configGradualController(gradual, info)
                ?: configGradualController(gradual, info)
            mController?.let {
                val newController = Compose4DefWrite.makeWriteController(it) { _ ->
                    controller.result2Suc()
                }
                hadDif = true
                groupOp.sendComposeControllerSingle(
                    device,
                    newController,
                    ArrayList()
                )
            }
        }
        return hadDif
    }

    private fun makeDifSceneController(controller: IComposeController): Boolean {
        SafeLog.i("DengFei_debug") { "重组装场景指令 sameModeInfo = $sameModeInfo" }
        var assemblySuc = false
        sameModeInfo.scene?.run {
            for (device in groupDetailsModel.devices) {
                val sameModeInfo = SameModeInfo(device)
                val mController =
                    sceneVmOp.getSceneComposeController(this, 0, infoInput = sameModeInfo) {
                    controller.result2Suc()
                }
                mController.let {
                    groupOp.sendComposeControllerSingle(
                        device,
                        mController,
                        ArrayList()
                    )
                    assemblySuc = true
                }
            }
        }
        sameModeInfo.scene = null
        return assemblySuc
    }

    private fun makeDifGraController(
        controllerOld: IComposeController,
        diyGraffitiV2: DiyGraffitiV2
    ): Boolean {
        SafeLog.i("DengFei_debug") { "重组装涂鸦指令 sameModeInfo = $sameModeInfo" }
        var assemblySuc = false
        for (device in groupDetailsModel.devices) {
            val sameModeInfo = SameModeInfo(device)

            val controller = ControllerMode.generateWriteControllerByExtBytes(
                byteArrayOf(0x0a, 0x20, 0x03) //20 03表示高阶的code为固定800
            )
            val tempDiyGraffitiV2 = diyGraffitiV2.copy()
            RgbIcGraffitiShare0x08.checkDiyValue4RgbicGraffiti(tempDiyGraffitiV2, device.ic)
            val completeCmds = mutableListOf<ByteArray>()
            val mController = Compose4DefWrite4Multi.makeWriteController4Multi(
                mutableListOf(
                    DiyControllerNoEventV3(tempDiyGraffitiV2),
                    controller
                ), sameModeInfo.getCmdDelayMs()
            ) { result ->
                controllerOld.result2Suc()
            }

            mController.let {
                groupOp.sendComposeControllerSingle(
                    device,
                    mController,
                    ArrayList()
                )
                assemblySuc = true
            }
        }

        sameModeInfo.diyGraffitiV2 = null
        return assemblySuc
    }

    private fun makeDifColorTemController(controller: IComposeController): Boolean {
        val kelvin = colorViewModel?.curColor?.value?.kelvin ?: return false
        val posSet = colorViewModel?.curColor?.value?.posSet ?: return false
        SafeLog.i(TAG) { "makeDifColorTemController() kelvin = $kelvin" }
        var hadDif = false
        for (device in groupDetailsModel.devices) {
            val info = SameModeInfo(device)
            val supportTemp = Config.checkNormalFucConfig(info)?.get(4) == true
            if (!supportTemp) continue
            val newKelvin = KelvinConfig.calibrationKelvin(info, kelvin)
            SafeLog.i(TAG) { "makeDifColorTemController() newKelvin = $newKelvin" }

            val colorTem = Constant.getTemColorByKelvin(newKelvin)[2]
            val isMulti = Config.checkSupportColorModeOver56Piece(info)
            var controllerPair =
                if (isMulti) {
                    //多包发送Controller
                    ColorModeControllerConfig.configColorMultiController(
                        colorTem,
                        posSet,
                        info
                    )
                } else {
                    ColorModeControllerConfig.configColorController(colorTem, posSet, info)
                }
            if (controllerPair != null) {
                SafeLog.i(TAG) { "applyColorTem() 统一配置生成指令成功" }
            } else {
                SafeLog.i(TAG) { "applyColorTem() 统一配置生成指令失败" }
                controllerPair = configColorController(colorTem, posSet, info)
                if (controllerPair == null) {
                    SafeLog.i(TAG) { "applyColorTem() 模块自生成指令失败" }
                }
            }
            controllerPair?.run {
                val composeController = if (isMulti) {
                    //多包发送
                    Compose4DefWrite4Multi.makeWriteController4Multi(mutableListOf(controllerPair.first)) { suc ->
                        controller.result2Suc()
                    }
                } else {
                    Compose4DefWrite.makeWriteController(controllerPair.first) { _ ->
                        controller.result2Suc()
                    }
                }
                val cmds = mutableListOf<AbsCmd4Op>().apply {
                    controllerPair.second?.let { this.add(it) }
                }
                if (cmds.isNotEmpty()) {
                    MultiCmd4OpWriteResult.makeMultiCmdOpWriteResult(cmds) { _, _ ->
                        controller.result2Suc()
                    }
                }
                hadDif = true
                groupOp.sendComposeControllerSingle(
                    device,
                    composeController,
                    cmds
                )
            }
        }
        return hadDif
    }

    private fun makeDifBrightnessController(controller: IComposeController): Boolean {
        val progressRange = BrightnessConfig.configBrightnessRange(sameModeInfo)
        val percent =
            calculateBrightnessPercentage(sameModeInfo.curBrightness, progressRange[0], progressRange[1])
        SafeLog.i(TAG) { "makeDifBrightnessController() brightnessPercent = ${percent}%" }
        if (percent < 0) return false
        var hadDif = false
        for (device in groupDetailsModel.devices) {
            val sameModeInfo = SameModeInfo(device)
            val brightness = BrightnessConfig.configBrightnessControllerByBrightnessPercent(
                sameModeInfo,
                percent
            )
            val config = BrightnessConfig.configBrightnessController(sameModeInfo, brightness)
            hadDif = true
            val newController = Compose4DefWrite.makeWriteController(config.first) { suc ->
                SafeLog.i(TAG) { "makeDifBrightnessController() op brightness-> suc = $suc" }
                controller.result2Suc()
            }
            val cmds = mutableListOf<AbsCmd4Op>().apply {
                config.second?.let { this.add(it) }
            }
            if (cmds.isNotEmpty()) {
                MultiCmd4OpWriteResult.makeMultiCmdOpWriteResult(cmds) { _, _ ->
                    controller.result2Suc()
                }
            }
            groupOp.sendComposeControllerSingle(device, newController, cmds)
        }
        return hadDif
    }

    private fun calculateBrightnessPercentage(current: Int, min: Int, max: Int): Int {
        val clamped = current.coerceIn(min, max)
        return ((clamped - min).toDouble() / (max - min) * 99 + 1).roundToInt()
    }

    private fun makeDifColorController(curController: IComposeController): Boolean {
        SafeLog.i(TAG) { "makeDifColorController() " }
        val subMode4Color = colorViewModel?.curColor?.value ?: return false
        SafeLog.e(TAG) { "makeDifColorController() colorSet = ${subMode4Color.colorSet.contentToString()}\ncolorBrightnessSet = ${subMode4Color.colorBrightnessSet.contentToString()}" }
        var hadDif = false
        for (device in groupDetailsModel.devices) {
            val info = SameModeInfo(device)
            SafeLog.e(TAG) { "makeDifColorController() bleAddress = ${device.bleAddress} ; info = ${info.fucKey()}" }
            var controller = autoCmdOp?.configColorStripController(
                subMode4Color.colorSet,
                subMode4Color.colorBrightnessSet,
                info
            )
            if (controller == null) {
                val allColorSame =
                    checkColorAllSame(subMode4Color.colorSet, subMode4Color.posSet, groupDetailsModel.devices)
                        ?: ColorUtils.isAllColorSame(subMode4Color.colorSet)
                if (allColorSame) {
                    val isMulti = Config.checkSupportColorModeOver56Piece(info)
                    val opPair = if (isMulti) {
                        //多包发送Controller
                        ColorModeControllerConfig.configColorMultiController(
                            subMode4Color.colorSet[0],
                            subMode4Color.posSet,
                            info
                        )
                    } else {
                        ColorModeControllerConfig.configColorController(
                            subMode4Color.colorSet[0],
                            subMode4Color.posSet,
                            info
                        )
                    }
                    opPair?.let {
                        controller = Triple(
                            ArrayList<IControllerNoEvent>().apply {
                                add(it.first)
                            },
                            ArrayList<AbsCmd4Op>().apply {
                                add(it.second ?: return@apply)
                            },
                            ColorStrip(subMode4Color.colorSet, null, false)
                        )
                    }
                } else {
                    controller = ColorModeControllerConfig.configColorStripController(
                        subMode4Color.colorSet,
                        subMode4Color.colorBrightnessSet,
                        info
                    )
                }
            }
            if (controller == null) {
                SafeLog.e("DengFei_debug") { "makeDifColorController() 颜色指令未配置 name = ${info.deviceName}" }
            }
            controller?.let {
                hadDif = true
                val bleControllers = it.first
                var cmd4Ops = it.second
                val composeController =
                    Compose4DefWrite4Multi.makeWriteController4Multi(bleControllers) { _ ->
                        curController.result2Suc()
                    }
                if (cmd4Ops.isNullOrEmpty()) {
                    IotCmdOp.makeController4Cmd(info, composeController)?.run {
                        cmd4Ops = ArrayList()
                        cmd4Ops.add(this)
                    }
                }
                cmd4Ops?.let { cmdOpList ->
                    MultiCmd4OpWriteResult.makeMultiCmdOpWriteResult(cmdOpList) { _, _ ->
                        curController.result2Suc()
                    }
                }
                groupOp.sendComposeControllerSingle(
                    device,
                    composeController,
                    cmd4Ops ?: ArrayList()
                )
            }
        }
        return hadDif
    }

    override fun applyDiyEditEffect(
        diySendData: DiySendData,
        modeType: Int,
        needToast: Boolean,
        needShow: Boolean,
        resultCallback: ((suc: Boolean) -> Unit)?
    ) {
        if (GroupBleConfig.isSupportRequestMtu(sameModeInfo.goodsType)) {
            if (diySendData.effectBytes[0] == BleProtocolConstants.MULTI_GRAFFITI_GIF_v1_MULTI_COMMON
                || diySendData.effectBytes[0].toInt() == com.govee.base2light.widget.diy.bean.h70b345.graffiti.EffectCellGraffiti.protocol.toInt()
                || diySendData.effectBytes[0].toInt() == 0x16
            ) {
                applyEffectMtu(diySendData, modeType, needToast, needShow, resultCallback)
            } else {
                applyEffect(diySendData, modeType, needToast, needShow, resultCallback)
            }
        } else {
            applyEffect(diySendData, modeType, needToast, needShow, resultCallback)
        }
    }

    private fun applyEffectMtu(
        diySendData: DiySendData,
        modeType: Int,
        needToast: Boolean,
        needShow: Boolean,
        resultCallback: ((suc: Boolean) -> Unit)?
    ) {
        if (needShow) {
            showApplyLoading(BaseApplication.getBaseApplication().topActivity ?: return)
        }
        val commandType = diySendData.protocol
        val controller = ModeControllerNoEvent(SubModeNewDiy(diySendData.diyCode))
        val completeCmds = mutableListOf<ByteArray>()
        val controllers = Compose4DefWrite4Multi.makeWriteController4MtuMulti(
            commandType,
            diySendData.effectBytes,
            getMtuSize(sameModeInfo.sku),
            controller
        ) {
            SafeLog.i(TAG, "----------------applyDiyMtu 结果：$it")
            resultCallback?.invoke(it)
            if (it) {
                notifyCompleteCmds(completeCmds)
                notifyCustomSnapshotInfo(
                    diySendData.effectBytes,
                    diySendData.protocol,
                    controller.getNextCommBytes() ?: byteArrayOf()
                )
                syncOpMode(modeType)
            }
            viewModelScope.launch(Dispatchers.Main) {
                toast(
                    if (it) {
                        R.string.b2light_diy_apply_suc
                    } else {
                        R.string.b2light_diy_apply_fail
                    }
                )
            }
            hideApplyLoading()
        }
        completeCmds.addAll(controllers.getAllBytes4Command())
        applyMtuController(
            controllers,
            modeType,
            diySendData.protocol,
            "send tuya effect",
            diySendData.effectBytes
        )
    }

    private fun applyEffect(
        diySendData: DiySendData,
        subComponentType: Int,
        needToast: Boolean = true,
        needShow: Boolean = true, resultCallback: ((suc: Boolean) -> Unit)?
    ) {
        if (needShow) {
            showApplyLoading(BaseApplication.getBaseApplication().topActivity ?: return)
        }
        val supportDiyMultiController = DiyConstant.supportDiyMultiController(diySendData.protocol)
        if (supportDiyMultiController) {
            //发多个多包
            val sendBytesV3 = MultipleControllerCommV1.makeSendBytesV3(
                BleProtocolConstants.MULTIPLE_WRITE_V1,
                diySendData.protocol,
                diySendData.effectBytes,
                EffectCellGraffiti.max_package_num
            )
            val modeControllerNoEvent = ModeControllerNoEvent(SubModeNewDiy(diySendData.diyCode))
            val completeCmds = mutableListOf<ByteArray>()
            val listener: ((suc: Boolean) -> Unit) = {
                resultCallback?.invoke(it)
                SafeLog.i(TAG, "----------------applyDiy 结果：$it")
                hideApplyLoading()
                if (it) {
                    notifyCompleteCmds(completeCmds)
                }
                if (needToast) {
                    viewModelScope.launch(Dispatchers.Main) {
                        toast(
                            if (it) {
                                R.string.b2light_diy_apply_suc
                            } else {
                                R.string.b2light_diy_apply_fail
                            }
                        )
                    }
                }
                EventDiyApplyResult.sendEventDiyApplyResult(it, diySendData.diyCode)
            }
            val controller = Compose4DefWrite4Multi.makeWriteController4Multi(
                diySendData.protocol,
                sendBytesV3,
                modeControllerNoEvent,
                listener
            )
            val cmd: MutableList<AbsCmd4Op> = mutableListOf()
            for (bytes in sendBytesV3) {
                val cmdPtReal =
                    if (sendBytesV3.indexOf(bytes) == sendBytesV3.lastIndex) {
                        //最后一包 创建一个多包controller 生成解析结果的ptReal
                        val controllers = mutableListOf<IControllerNoEvent>().apply {
                            add(
                                DefControllerNoEvent4MultiImp(
                                    diySendData.protocol,
                                    bytes
                                )
                            )
                            add(modeControllerNoEvent)
                        }
                        val write4Multi =
                            Compose4DefWrite4Multi.makeWriteController4Multi(
                                controllers,
                                listener
                            )
                        Cmd4PtReal.makeCmdPtReal(write4Multi, resultCallback = listener)
                    } else {
                        Cmd4PtReal.makeCmdPtReal(bytes)
                    }
                cmd.add(cmdPtReal)
            }
            completeCmds.addAll(controller.getAllBytes4Command())
            doCommController4SyncCmdWrite(controller, cmd)
        } else {
            val controllers = mutableListOf<IControllerNoEvent>().apply {
                add(
                    MultiController.makeMultiController(
                        diySendData.effectBytes,
                        diySendData.protocol
                    )
                )
                //模式包
                add(
                    ModeControllerNoEvent(SubModeNewDiy(diySendData.diyCode))
                )
            }
            val completeCmds = mutableListOf<ByteArray>()

            val contro = Compose4DefWrite4Multi.makeWriteController4Multi(
                controllers,
                result = {
                    SafeLog.i(TAG, "----------------applyDiy 结果：$it")
                    hideApplyLoading()
                    if (it) {
                        notifyCompleteCmds(completeCmds)
                        syncOpMode(subComponentType)
                    }
                    viewModelScope.launch(Dispatchers.Main) {
                        toast(
                            if (it) {
                                R.string.b2light_diy_apply_suc
                            } else {
                                R.string.b2light_diy_apply_fail
                            }
                        )
                    }
                    EventDiyApplyResult.sendEventDiyApplyResult(
                        it,
                        diySendData.diyCode
                    )
                })
            completeCmds.addAll(contro.getAllBytes4Command())
            doCommController(contro)
        }
    }

    override fun applyMtuController(
        controller: IComposeController,
        modeType: Int,
        protocol: Byte,
        closeMicModeReason: String?,
        effectBytes: ByteArray,
        only4Ble: Boolean
    ) {
        if (checkConnectAndSetResult(controller)) {
            return
        }
        /**统计发送指令的类型*/
        ModeConfig.configAnalyticSendCmdType(sameModeInfo.sku, modeType)
        if (!closeMicModeReason.isNullOrEmpty()) {
            closeMicMode(closeMicModeReason)
        }
        sameModeInfo.run {
            doCommController4PtUrl(
                controller,
                device,
                protocol,
                sku,
                effectBytes,
                only4Ble
            )
        }
    }

    /**
     * 通信-iot
     * -例如颜色读取/亮度写入 (cmd的iot指令都会主动上传)
     * @param writeCmd iot通信要写的类型 例如 colorWc,brightness
     */
    open fun doCommController4SyncCmdWrite(
        controller: IComposeController, writeCmd: MutableList<AbsCmd4Op>, only4Ble: Boolean = false,
    ) {
        SafeLog.i(TAG) { "doCommController4SyncCmdWrite() only4Ble = $only4Ble" }
        groupOp.sendComposeController(controller, writeCmd)
    }

    fun doCommController(controller: IComposeController, only4Ble: Boolean = false) {
        SafeLog.i(TAG) { "doCommController() only4Ble = $only4Ble" }
        if (groupOp.isConnected) {
            groupOp.sendComposeController(controller)
            return
        }
    }

    private fun doCommController4PtUrl(
        controller: IComposeController,
        device: String,
        protocol: Byte,
        sku: String,
        values: ByteArray,
        only4Ble: Boolean = false,
    ) {
        SafeLog.i(TAG) { "doCommController() only4Ble = $only4Ble" }
        if (deviceConnectType == SameModeDeviceItem.DEVICE_TYPE_BLE) {
            groupOp.sendComposeController(controller)
            return
        }
        if (only4Ble) return
        if (isIotConnectSuc()) {
            SafeLog.e(TAG) {
                "doCommController() controller can not make cmd!! \nbytes--> ${
                    BleUtils.bytesToHexString(
                        values
                    )
                } "
            }
            makePtUrl(controller, protocol, values)
        }
    }

    private fun makePtUrl(
        controller: IComposeController,
        protocol: Byte,
        values: ByteArray,
    ) {
        makePtUrlCallBack(controller, sameModeInfo, protocol, values, null) { cmd ->
            if (cmd is AbsCmd4Op) {
                groupOp.sendWriteCmd(cmd)
            }
        }
    }

    private fun closeMicMode(reason: String) {
        if (inMicMode) {
            inMicMode = false
            SafeLog.i(TAG) { "closeMicMode() reason = $reason" }
            closeMicModeLD.postValue(true)
        }
    }

    private fun checkConnectAndSetResult(controller: IComposeController): Boolean {
        if (!groupOp.isConnected) {
            toast(R.string.app_ble_group_no_connected_device_msg)
            controller.onResult(false, null)
            return true
        }
        return false
    }

    fun isConnected(): Boolean {
        return groupOp.isConnected
    }

    override fun notifyCompleteCmds(cmds: MutableList<ByteArray>?) {
    }

    override fun notifyCustomSnapshotInfo(
        effectBytes: ByteArray,
        protocol: Byte,
        modeBytes: ByteArray,
        cmdList: List<AbsCmd>?,
        otherInfo: EffectOtherInfo?
    ) {
    }

    /**当前操作的模式-即设备实际模式*/
    final override val opModeChange4LD: MutableLiveData<Int?> = MutableLiveData()

    override fun curOpMode(): Int {
        return curChooseMode
    }

    override fun curShowOpMode(): Int {
        return curChooseMode
    }

    override fun syncOpMode(opMode: Int, fromReadInfo: Boolean) {
        SafeLog.i(TAG) { "syncOpMode() fromReadInfo = $fromReadInfo ; opMode = $opMode ; curOpModeValue = $curOpModeValue" }
        if (opMode == Component.cmd_type_mode_color_gradual) return
        if (this.curOpModeValue != opMode) {
            if (fromReadInfo) {
                ModeConfig.configAnalyticReadMode(sameModeInfo.sku, opMode)
            }
            this.curOpModeValue = opMode
            opModeChange4LD.postValue(if (fromReadInfo) 1 else 2)
        }
    }

    override fun getCurConnectedState(): BooleanArray {
        return booleanArrayOf(
            deviceConnectType == SameModeDeviceItem.DEVICE_TYPE_BLE && BleController.getInstance().isBlueToothOpen,
            deviceConnectType == SameModeDeviceItem.DEVICE_TYPE_IOT
        )
    }

    override val deviceInfoChanged: MutableLiveData<Boolean?> = MutableLiveData()
    override fun getDeviceModeInfo(): Info4BleIotDevice {
        return sameModeInfo
    }

    override fun notifyDeviceListUpdate(
        devices: MutableList<DeviceModel>,
        deviceConnectType: Int
    ) {
        bleStatusLDMic.postValue(1)
        groupOp.updateDevices(devices, deviceConnectType)
    }

    override fun refreshDeviceListStatus(onlineRealtimeMap: ConcurrentHashMap<String, Boolean>) {
        super.refreshDeviceListStatus(onlineRealtimeMap)
        //设备连接状态发生变化
        bleStatusLDMic.postValue(1)
    }

    override fun onSendMicCmd(controller: AbsSingleController) {
        if (!inMicMode || controller == null) return
        if (!getBleConnected()) {
            return
        }
        //存在
        if (hasDifferMicCmd) {
            var micNewCmdController: AbsMicController? = null
            val rgb = if (controller is MicColorCmdController) {
                controller.rgb
            } else {
                micNewCmdController = controller as AbsMicController?
                toColor(
                    controller.data[0], controller.data[1],
                    controller.data[2]
                )
            }
            for (device in groupDetailsModel.devices) {
                val sameModeInfo = SameModeInfo(device)
                when (MicSpConfig.getInstance(device.sku, device.device)
                    .getPickUpMode(sameModeInfo)) {
                    2 -> {
                        val colorPiece = ColorPieceConfig.configColorPiece(sameModeInfo)
                        val afterOpColorSet = IntArray(colorPiece)
                        val posSet = BooleanArray(colorPiece)

                        Arrays.fill(afterOpColorSet, rgb)
                        Arrays.fill(posSet, true)

                        val mController =
                            ColorModeControllerConfig.configColorController(
                                rgb,
                                posSet,
                                sameModeInfo
                            )
                                ?: configColorController(rgb, posSet, sameModeInfo)
                        mController?.let { pair ->
                            val byteArrays = pair.first.getValue() ?: return
                            micNewCmdController = MicColorCmdController(byteArrays[0], rgb)
                        }
                    }

                    0 -> {
                        micNewCmdController = null
                    }

                    1 -> {
                        micNewCmdController = MicSetRgbController(ColorUtils.getRgbBytes(rgb))
                    }

                    3 -> {
                        micNewCmdController = MicController(
                            controller.data[0].toInt(),
                            controller.data[1].toInt(),
                            controller.data[2].toInt()
                        )
                    }
                }

                micNewCmdController?.run {
                    groupOp.sendExtController(
                        device.bleAddress,
                        this
                    )
                }
            }
        } else {
            groupOp.sendExtController(controller)
        }
    }

    override fun configColorController(
        rgb: Int,
        posSet: BooleanArray,
        info: Info4BleIotDevice
    ): Pair<IControllerNoEvent, AbsCmd4Op?>? {
        return autoCmdOp?.configColorController(rgb, posSet, info)
    }

    override fun configDiyController(
        diyEffectBean: DiyEffectBean,
        info: Info4BleIotDevice
    ): MutableList<IControllerNoEvent>? {
        return autoCmdOp?.configDiyController(diyEffectBean, info)
    }

    override fun configBrightnessController(
        info: Info4BleIotDevice,
        brightness: Int
    ): Pair<IControllerNoEvent, AbsCmd4Op?>? {
        return autoCmdOp?.configBrightnessController(info, brightness)
    }

    override fun configColorEffectController(
        colorEffect: EffectData.ColorEffect,
        info: Info4BleIotDevice
    ): Triple<MutableList<IControllerNoEvent>, MutableList<AbsCmd4Op>?, ColorStrip>? {
        return autoCmdOp?.configColorEffectController(colorEffect, info)
    }

    override fun configGradualController(
        gradual: Boolean,
        info: Info4BleIotDevice
    ): IControllerNoEvent? {
        return autoCmdOp?.configGradualController(gradual, info)
    }

    override fun configColorStripController(
        colorSet: IntArray,
        brightnessSet: IntArray?,
        info: Info4BleIotDevice
    ): Triple<MutableList<IControllerNoEvent>, MutableList<AbsCmd4Op>?, ColorStrip>? {
        return autoCmdOp?.configColorStripController(colorSet, brightnessSet, info)
    }

    override fun configKelvinController(
        info: Info4BleIotDevice,
        kelvin: Int
    ): Pair<IControllerNoEvent, AbsCmd4Op?>? {
        return autoCmdOp?.configKelvinController(info, kelvin)
    }

    override fun configRelativeBrightnessController(
        brightness: Int,
        posSet: BooleanArray,
        info: Info4BleIotDevice
    ): IControllerNoEvent? {
        return autoCmdOp?.configRelativeBrightnessController(brightness, posSet, info)
    }

    override fun setMicRecordStatus(status: Boolean) {
        if (status) {
            /**若开启录音-则通知模式变动-全部模式都不做选中逻辑-直接设置成未知模式*/
            syncOpMode(Component.sub_component_type_mode_unknown)
        }
        inMicMode = status
        micStatus.postValue(status)
    }

    override fun switchTrigger(
        showLoading: () -> Unit,
        resultCallback: (suc: Boolean) -> Unit,
        turnOn: Boolean?
    ) {

        val open = turnOn ?: false
        SafeLog.i(TAG) { "switchTrigger() open = $open" }

        val controller = Compose4DefWrite.makeWriteController(
            ControllerSwitch.generateWriteController(
                open
            ),
        ) { suc ->
            SafeLog.i(TAG) { "switchTrigger() suc = $suc" }
            hideLoading()
            resultCallback.invoke(suc)
        }
        if (checkConnectAndSetResult(controller)) {
            return
        }
        showLoading(
            BaseApplication.getBaseApplication().topActivity ?: return,
            delayTimeMills = 15 * 1000
        )
        doCommController(controller)
    }

    override fun switchCur(): Boolean {
        return true
    }

    private fun isIotConnectSuc(): Boolean {
        return !groupOp.iotOnlineMap.isNullOrEmpty()
    }

    override fun getBleConnected(): Boolean {
        if (deviceConnectType == SameModeDeviceItem.DEVICE_TYPE_IOT) {
            return false
        }
        return GroupControllerV1.getInstance.connectSize > 0
    }

    fun onRelease() {
        groupOp.destroy()
        NewDiyEditConfig.releaseDiyParam()
        DiyNewCacheCenter.releaseDiyParam()
    }

    override fun connectStatusChange(): LiveData<Int?> {
        return bleStatusLDMic
    }

    override fun connectSuccessType(): Int {
        return if (deviceConnectType == SameModeDeviceItem.DEVICE_TYPE_BLE) 1 else 0
    }

    private fun showApplyLoading(
        context: Context,
        style: Int = com.ihoment.base2app.R.style.DialogDim,
        delayTimeMills: Long = 60 * 1000L
    ) {
        val canShow = (context as? AppCompatActivity)?.isAcOk() ?: true
        if (canShow) {
            LoadingDialog.createDialog(context, style, delayTimeMills).setEventKey(TAG).show()
        }
    }

    private fun hideApplyLoading() {
        LoadingDialog.hideDialog(TAG)
    }

    private fun getMtuSize(sku: String): Int {
        val mtuSize = MtuConfig.readData().getAvailableMtuSize(sku)
        return mtuSize
    }

    open fun assembleCmdTypePreCheck(
        modeType: Int,
        deviceModes: MutableList<DeviceModel>,
        isControlAll: Boolean?,
        subMode4Color: SubMode4Color?
    ) {
        autoCmdOp?.assembleCmdTypePreCheck(modeType, deviceModes, isControlAll, subMode4Color)
    }

    open fun checkColorAllSame(colorSet: IntArray?, posSet: BooleanArray?, deviceModes: MutableList<DeviceModel>): Boolean? {
        return autoCmdOp?.checkColorAllSame(colorSet, posSet, deviceModes)
    }

    open fun specialSkuTagInSameMode(deviceModes: MutableList<DeviceModel>): String {
        return autoCmdOp?.specialSkuTagInSameMode(deviceModes) ?: ""
    }

    override fun specialSkuTag(): String {
        return specialSkuTagInSameMode(groupDetailsModel.devices)
    }

}

interface ConnectChangedListener {
    fun onConnectChanged(
        connectType: Int,
        onlineRealtimeMap: ConcurrentHashMap<String, Boolean>
    )
}
