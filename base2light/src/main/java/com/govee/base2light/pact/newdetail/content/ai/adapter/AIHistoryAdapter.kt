package com.govee.base2light.pact.newdetail.content.ai.adapter

import android.graphics.drawable.LayerDrawable
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import androidx.annotation.DrawableRes
import com.bumptech.glide.Glide
import com.bumptech.glide.load.resource.bitmap.CenterCrop
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestOptions
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ext.autoSize
import com.govee.base2kt.ext.dp4Int
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2light.databinding.B2lightItemAiHistoryRecordBinding
import com.govee.base2light.pact.newdetail.content.ai.VmAi2Common
import com.govee.base2light.pact.newdetail.content.ai.bean.AIDiyConfig
import com.govee.base2light.pact.newdetail.content.ai.bean.AiHistory
import com.govee.ui.R
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.ResUtil

/**
 * Create by yu on 2024/6/5
 */
class AIHistoryAdapter(var list: MutableList<AiHistory>?, private val isNightMode: Boolean) :
    BaseQuickAdapter<AiHistory, BaseDataBindingHolder<B2lightItemAiHistoryRecordBinding>>(
        com.govee.base2light.R.layout.b2light_item_ai_history_record, list
    ) {


    val options: RequestOptions = RequestOptions().transform(
        CenterCrop(),
        RoundedCorners(AppUtil.getScreenWidth() * 10 / 375)
    )


    init {
        addChildClickViewIds(
            com.govee.base2light.R.id.tvApply,
            com.govee.base2light.R.id.tvSave,
            com.govee.base2light.R.id.ivSource,
            com.govee.base2light.R.id.tvEdit

        )
    }

    override fun convert(
        holder: BaseDataBindingHolder<B2lightItemAiHistoryRecordBinding>,
        item: AiHistory
    ) {
        holder.dataBinding?.apply {
            val imgUrl = item.getShowImgUrl()
            val isShowImgUrl = item.getShowImgUrl().trim().isNotEmpty()

            /*优先显示发送图片（图生灯效/图生图都是先显示发送图片）*/
            if (isShowImgUrl) {
                ivSource.post {
                    val drawableWidth = if (ivSource.width > 0) ivSource.width else 46f.dp4Int
                    val drawableHeight =
                        if (ivSource.height > 0) ivSource.height else 46f.dp4Int
                    val placeholderDrawable = createLayerDrawable(
                        drawableWidth,
                        drawableHeight,
                        12f.dp4Int,
                        10f.dp4Int,
                        R.mipmap.new_zhanweitu
                    )
                    val errorDrawable = createLayerDrawable(
                        drawableWidth,
                        drawableHeight,
                        12f.dp4Int,
                        10f.dp4Int,
                        R.mipmap.new_loading_shibai
                    )
                    /*优先加载本地*/
                    val options = RequestOptions()
                        .centerCrop()
                        .placeholder(placeholderDrawable)
                        .error(errorDrawable)
                    Glide.with(context)
                        .load(imgUrl)
                        .apply(options)
                        .into(ivSource)

                }
            }
            tvContent.text = if (item.outputType == 0) item.originInput else ""
            tvContent.visibleByBoolean(item.outputType == 0)
            ivSource.visibleByBoolean(isShowImgUrl)
            ivGif.visibleByBoolean(isShowImgUrl && VmAi2Common.isGIF(imgUrl))
            tvSave.text =
                if (item.saveStatus == 1) ResUtil.getString(R.string.app_save_diy_content) else ResUtil.getString(
                    com.govee.ui.R.string.app_save_to_diy
                )
            tvTime.text = TimeFormatM.getInstance().formatTimeToHMYMD(item.createTime)
            val isEdit = AIDiyConfig.isEffective(item.config)
            tvSave.visibleByBoolean(item.showButton && !isEdit)
            tvEdit.visibleByBoolean(isEdit)
            constraintLeft.visibleByBoolean(item.showButton || isEdit)
            tvApply.visibleByBoolean(true)
        }
    }

    private fun createLayerDrawable(
        drawableWidth: Int,
        drawableHeight: Int,
        drawableImgWidth: Int,
        drawableImgHeight: Int,
        @DrawableRes drawableResId: Int
    ): LayerDrawable? {
        val drawableBg = if (isNightMode) {
            ResUtil.getDrawable(R.color.FF000000_80)
        } else ResUtil.getDrawable(R.color.ui_shadow_style_15)
        val drawableImg = ResUtil.getDrawable(drawableResId)

        return if (drawableBg != null && drawableImg != null) LayerDrawable(
            arrayOf(
                drawableBg,
                drawableImg
            )
        ).apply {
            setLayerSize(0, drawableWidth, drawableHeight)
            setLayerSize(1, drawableImgWidth, drawableImgHeight)
            setLayerGravity(1, Gravity.CENTER)
        } else null
    }

    override fun createBaseViewHolder(view: View): BaseDataBindingHolder<B2lightItemAiHistoryRecordBinding> {
        view.autoSize()
        return super.createBaseViewHolder(view)
    }

    override fun onCreateDefViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): BaseDataBindingHolder<B2lightItemAiHistoryRecordBinding> {
        parent.autoSize()
        return super.onCreateDefViewHolder(parent, viewType)
    }

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): BaseDataBindingHolder<B2lightItemAiHistoryRecordBinding> {
        parent.autoSize()
        return super.onCreateViewHolder(parent, viewType)
    }

    override fun createBaseViewHolder(
        parent: ViewGroup,
        layoutResId: Int
    ): BaseDataBindingHolder<B2lightItemAiHistoryRecordBinding> {
        parent.autoSize()
        return super.createBaseViewHolder(parent, layoutResId)
    }

}