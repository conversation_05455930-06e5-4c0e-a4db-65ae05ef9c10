@file:Suppress("UsePropertyAccessSyntax")

package com.govee.base2light.pact.newdetail.sameMode.vm

import android.annotation.SuppressLint
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.MutableLiveData
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.pact.support.OldDreamColorUtil
import com.govee.base2home.pact.support.OldRgbBkUtil
import com.govee.base2home.pact.support.OldRgbicBkUtil
import com.govee.base2home.scenes.model.DeviceModel
import com.govee.base2home.scenes.model.GroupDetailsModel
import com.govee.base2home.util.SkuToGoodsTypeUtil
import com.govee.base2kt.utils.deepCopy
import com.govee.base2light.Constant
import com.govee.base2light.Constant.OldDreamColorSkuArray
import com.govee.base2light.ac.diyNew.config.DiyNewConfig
import com.govee.base2light.group.FactorOpM
import com.govee.base2light.light.v1.AbsMicFragmentV4
import com.govee.base2light.pact.newdetail.DeviceDetailConfig
import com.govee.base2light.pact.newdetail.config.fuc.ColorPieceConfig
import com.govee.base2light.pact.newdetail.config.fuc.ModeConfig
import com.govee.base2light.pact.newdetail.config.fuc.color.ColorModeControllerConfig
import com.govee.base2light.pact.newdetail.content.mic.MicSpConfig
import com.govee.base2light.pact.newdetail.net.Component
import com.govee.base2light.pact.newdetail.sameMode.SameDetailRepository
import com.govee.base2light.pact.newdetail.sameMode.SameModeInfo
import com.govee.base2light.pact.newdetail.sameMode.config.SameModeDetailConfig
import com.govee.base2light.pact.newdetail.sameMode.config.SameModeDeviceItem
import com.govee.base2light.pact.newdetail.sameMode.config.SameModeDeviceItem.DEVICE_TYPE_EMPTY
import com.govee.base2light.pact.newdetail.sameMode.config.SameModelBean
import com.govee.base2light.pact.newdetail.sameMode.net.EventUpdateDeviceList
import com.govee.base2light.pact.newdetail.sameMode.net.RenameSameModelRequest
import com.govee.ble.BleCons
import com.govee.ble.BleController
import com.govee.ble.group.v1.GroupControllerV1
import com.govee.cache.GlobalCache
import com.govee.cache.KeyConstants
import com.govee.cache.key.SmartCacheKey
import com.govee.mvvm.base.viewmodel.BaseViewModel
import com.govee.mvvm.ext.request
import com.govee.ui.R
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JsonUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

open class SameModeDetailVM : BaseViewModel() {
    private val repository = SameDetailRepository()

    @SuppressLint("StaticFieldLeak")
    var rootView: ViewGroup? = null

    //设备列表更新，
    var deviceListRefresh = MutableLiveData<Boolean>()

    //连接状态发生变化
    var deviceConnectStatus = MutableLiveData<Boolean>()

    //设备连接类型
    var deviceConnectType = SameModeDeviceItem.DEVICE_TYPE_BLE
    var groupDetailsModel = GroupDetailsModel()

    var presetId = 0
    var groupId = 0
    var sameModeInfo = SameModeInfo()

    //对应的同型号的设备列表
    protected var deviceList: MutableList<SameModeInfo> = ArrayList()

    val needAssembleCmdType: Boolean
        get() {
            return SameModeDetailConfig.getModelConfig(sameModeInfo)?.needAssembleCmdType == true
        }

    /**展示的模式列表*/
    val showingModeList = CopyOnWriteArrayList<Int>()

    /**当前选中模式*/
    var curChooseMode: Int = Component.sub_component_type_mode_color
        private set

    /**当前选中模式变化*/
    val curChooseModeChange: MutableLiveData<Boolean?> = MutableLiveData()

    //弹窗提示进入详情页只弹一次，若更新了设备列表则从新弹
    //是否显示分段亮度的弹窗提示（同时存在支持和不支持分段亮度的sku）
    var showPartBrightnessHint = false

    //是否存在不同类型的相对亮度指令，不支持的不发送
    protected var hasDiffPartBrightnessDevice = false

    //是否显示mic拾音的弹窗提示（同时存在支持和不支持手机拾音的sku）
    var showMicModeHint = false

    //是否存在不同mic指令
    protected var hasDifferMicCmd = false

    //是否显示音乐模式的弹窗提示（同时存在支持和不支持新音乐模式的sku）
    var showMusicModeHint = false

    //是否存在不支持的diy 弹窗提示
    var showDiyModeHint = false
    var lastConnectionType = 0

    //是否初始化默认模式
    var initDefMode = false

    //进入同型号页面，是否toast提示背灯不支持色温
    var isToastColorTemNotice = false

    //是否显示颜色分段的弹窗提示（同时存在支持和不支持颜色分段的sku）
    var showColorPartHint = false

    protected val connectChangedListener: ConnectChangedListener = object : ConnectChangedListener {
        override fun onConnectChanged(
            connectType: Int,
            onlineRealtimeMap: ConcurrentHashMap<String, Boolean>
        ) {
            if (connectType != deviceConnectType || groupDetailsModel.noDevices()) {
                return
            }
            refreshDeviceListStatus(onlineRealtimeMap)
        }
    }

    open fun refreshDeviceListStatus(onlineRealtimeMap: ConcurrentHashMap<String, Boolean>) {
        if (deviceConnectType == SameModeDeviceItem.DEVICE_TYPE_BLE) {
            val bleConnectMap = GroupControllerV1.getInstance.deviceConnectStatus
            groupDetailsModel.devices.forEach {
                if (!BleController.getInstance().isBlueToothOpen) {
                    it.connectStatus = BleCons.connect_status_fail
                } else {
                    it.connectStatus = bleConnectMap[it.bleAddress] ?: BleCons.connect_status_ing
                }
            }
        } else {
            groupDetailsModel.devices.forEach {
                it.connectStatus =
                    if (onlineRealtimeMap[it.skuDeviceKey] == true) BleCons.connect_status_suc else BleCons.connect_status_fail
            }
        }
        deviceConnectStatus.postValue(true)
    }

    init {
        registerEvent(true)
    }

    fun getGroupDetails(
        gId: Int, presetId: Int,
        type: Int, name: String?,
        sucCallBack: (data: GroupDetailsModel) -> Unit,
        failCallBack: (status: Int?) -> Unit
    ) {
        var status = 0
        this.presetId = presetId
        this.groupId = gId
        request({
            val request = repository.getGroupDetails(gId, presetId, type, name)
            status = request.status
            request
        }, success = {
            groupDetailsModel = it
            val key =
                SmartCacheKey.appGlobal(KeyConstants.KEY_SAME_MODE_CONNECTION_TYPE + "_$groupId")
                    .contactAccount()
            val connectionType = GlobalCache.build().getInt(key, 0)
            SafeLog.i(TAG, "get last connectionType -> $connectionType groupId: $groupId")
            deviceListChanged(lastType = connectionType)
            sucCallBack(it)
        }, error = {
            toast(
                if (it.isNetworkBroken()) BaseApplication.getContext()
                    .getString(R.string.network_anomaly) else it.message ?: ""
            )
            failCallBack(status)
        })
    }

    fun deleteGroup(
        gId: Int, presetId: Int,
        callBack: (suc: Boolean) -> Unit
    ) {
        request({
            repository.deleteGroup(gId, presetId)
        }, success = {
            callBack(true)
        }, error = {
            toast(
                if (it.isNetworkBroken()) BaseApplication.getContext()
                    .getString(R.string.network_anomaly) else it.message ?: ""
            )
            callBack(false)
        })
    }

    fun renameGroupControl(
        gId: Int, request: RenameSameModelRequest,
        callBack: (suc: Boolean) -> Unit
    ) {
        request({
            repository.renameGroupControl(gId, request)
        }, success = {
            groupDetailsModel.name = request.name
            callBack(true)
        }, error = {
            toast(
                if (it.isNetworkBroken()) BaseApplication.getContext()
                    .getString(R.string.network_anomaly) else it.message ?: ""
            )
            callBack(false)
        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventUpdateDeviceList(event: EventUpdateDeviceList) {
        if (event.groupId != groupDetailsModel.gId) return
        groupDetailsModel.devices = event.devices
        deviceListChanged(lastConnectionType, refresh = true)
    }

    /**
     * @param connection 0 默认  1 ble  2 wifi
     */
    fun deviceListChanged(connection: Int = 0, lastType: Int = 0, refresh: Boolean = false) {
        //确定当前连接类型
        //是否存在不是同一类型的sku（当sku OTA升级之后存在该情况）
        val groupModelSupport = FactorOpM.getInstance.isGroupModelSupport(groupDetailsModel)
        showingModeList.clear()
        deviceList.clear()
        hasDiffPartBrightnessDevice = false
        val hadDevices = groupDetailsModel.hadDevices()
        var sameModelConfig: SameModelBean? = null
        if (!hadDevices) {
            deviceConnectType = DEVICE_TYPE_EMPTY
        } else if (groupModelSupport) {
            //承载是否支持分段亮度、新旧音乐模式、新旧mic等信息
            var deviceModel: DeviceModel = deepCopy(groupDetailsModel.devices[0])
            //最终用来生成各模式的设备信息
            //场景支持类型最多的设备
            var sceneMoreIndex = -1
            //支持渐变的设备
            var gradualIndex = -1
            //支持新音乐模式的设备
            var newMusicIndex = -1
            //支持Mic模式的设备
            var supportMicIndex = -1
            //支持分段亮度的设备
            var partBrightnessIndex = -1

            var finalDeviceModel: DeviceModel =
                JsonUtil.deepCopy(groupDetailsModel.devices[0]) as DeviceModel

            deviceModel.maxVersionSoft = deviceModel.versionSoft
            var wifiFactor = FactorOpM.getInstance.isWifiFactor(deviceModel)

            var supportWifi = true
            groupDetailsModel.devices.forEach {
                //是否是单iot群控
                val isWifi = it.isWifiDevice
                supportWifi = supportWifi && isWifi
            }
            //少于=6台就用蓝牙控制，否则就是wifi控制
            if (groupDetailsModel.devices.size > 6 || (connection == 2 && supportWifi) || (lastType == 2 && supportWifi)) {
                wifiFactor = true
                lastConnectionType = 2
            } else {
                lastConnectionType = 0
            }
            deviceConnectType =
                if (wifiFactor) SameModeDeviceItem.DEVICE_TYPE_IOT else SameModeDeviceItem.DEVICE_TYPE_BLE

            /*由子类提供判断功能版本的方法*/
            sameModelConfig = SameModeDetailConfig.getModelConfig(SameModeInfo(deviceModel))
            val standardModel =
                sameModelConfig?.modelGenerateFuc?.invoke(deepCopy(groupDetailsModel.devices))
            val factorVersion = sameModelConfig?.factorVersion

            if (standardModel != null) {
                sameModeInfo = if (connection > 0 && !refresh) sameModeInfo else standardModel
                sameModeInfo.factorVersion = factorVersion ?: 0
                showDiyModeHint = sameModeInfo.showDiyModeHint
                showMusicModeHint = standardModel.showMusicModeHint
                showPartBrightnessHint = standardModel.showPartBrightnessHint
                isToastColorTemNotice = standardModel.isToastColorTemNotice
                showColorPartHint = standardModel.showColorPartHint
            } else {
                /*计算分段亮度支持逻辑和同步最新的goodsType*/
                var micCmdStatus = 0
                var lastOpScenesSet: IntArray? = intArrayOf()
                groupDetailsModel.devices.forEachIndexed { index, item ->
                    checkDeviceGoodsType(item)
                    val deviceNewInfo = SameModeInfo(item)
                    deviceList.add(deviceNewInfo)
                    val scenesConfig = DeviceDetailConfig.getSceneEffect(deviceNewInfo)
                    val opScenesSet = scenesConfig?.opScenesSet
                    if (lastOpScenesSet == null || lastOpScenesSet.isEmpty() == true) {
                        lastOpScenesSet = opScenesSet
                        sceneMoreIndex = index
                    } else if ((opScenesSet?.size ?: 0) > lastOpScenesSet.size) {
                        lastOpScenesSet = opScenesSet
                        sceneMoreIndex = index
                    }
                    //要求用段数最多时，ic数也需要赋值成最大的
                    if (sameModelConfig != null && sameModelConfig.isSegmentNumBassOnMax()) {
                        if (item.goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_OUTDOOR_LAMP && item.segmentCount == 0) {
                            deviceModel.segmentCount = item.ic / 4
                        }
                        if (item.goodsType == GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT && item.segmentCount == 0) {
                            item.segmentCount = item.ic / 3
                        }
                        if (item.goodsType == GoodsType.GOODS_TYPE_VALUE_H608a && item.segmentCount == 0) {
                            val num = if (item.ic % 5 == 0) item.ic / 5 else item.ic / 5 + 1
                            item.segmentCount = num.coerceAtLeast(1)
                        }
                        deviceModel.ic = deviceModel.ic.coerceAtLeast(item.ic)
                        deviceModel.segmentCount =
                            deviceModel.segmentCount.coerceAtLeast(item.segmentCount)
                    } else if (sameModelConfig != null && sameModelConfig.isIcNumBassOnMax()) {
                        deviceModel.ic = deviceModel.ic.coerceAtLeast(item.ic)
                        if (item.goodsType == GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2) {
                            deviceModel.ic_sub_1 =
                                deviceModel.ic_sub_1.coerceAtLeast(item.ic_sub_1)
                            deviceModel.ic_sub_2 =
                                deviceModel.ic_sub_2.coerceAtLeast(item.ic_sub_2)
                        }
                    } else {
                        if (GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT == deviceModel.goodsType) {
                            deviceModel = item
                        } else {
                            /*ic数不同，取最少的*/
                            deviceModel.ic = deviceModel.ic.coerceAtMost(item.ic)
                        }
                    }

                    /*新出的sku默认支持分段亮度*/
                    if (!OldDreamColorSkuArray.contains(item.sku)) {
                        partBrightnessIndex = index
                        deviceNewInfo.supportPartBrightness = true
                        deviceModel.hasSupportPartBrightness = true
                    } else {
                        if (Constant.supportSubModeColor4PartBrightness(
                                item.sku,
                                item.versionSoft,
                                item.versionHard
                            ) || Constant.isBKProtocol(
                                item.sku,
                                item.goodsType,
                                item.pactType,
                                item.pactCode
                            )
                        ) {
                            deviceNewInfo.supportPartBrightness = true
                            deviceModel.hasSupportPartBrightness = true
                        } else {
                            SafeLog.i("DengFei_debug") { "不支持分段亮度 ${item.getName()} " }
                            deviceNewInfo.supportPartBrightness = false
                            deviceModel.hasNotSupportPartBrightness = true
                        }
                    }
                    val topActivity = BaseApplication.getBaseApplication().topActivity
                    if (gradualIndex == -1) {
                        rootView?.run {
                            (ColorModeControllerConfig.configNormalFucSupport(sameModeInfo)
                                ?: DeviceDetailConfig.getColorPartViewDelegate(
                                    topActivity as AppCompatActivity,
                                    this,
                                    sameModeInfo
                                ) {
                                    ColorPieceConfig.configColorPiece(sameModeInfo)
                                }?.normalFucSupport())?.run {
                                if (this[0]) {
                                    SafeLog.i("DengFei_debug") { "normalFucSupport() ${item.name}" }
                                    gradualIndex = index
                                }
                            }
                        }
                    }

                    val maxVersion = BleUtil.parseVersion(deviceModel.maxVersionSoft)
                    val versionInt = BleUtil.parseVersion(item.versionSoft)

                    if (deviceModel.goodsType == GoodsType.GOODS_TYPE_VALUE_H61F6) {
                        val flagVersion = BleUtil.parseVersion("1.00.06")
                        if (versionInt >= flagVersion) {
                            deviceModel.hasSupportNewMusic = true
                        } else {
                            deviceModel.hasNotSupportNewMusic = true
                        }
                    } else {
                        val musicConfig = DeviceDetailConfig.getMusicSupportEffect(
                            topActivity as AppCompatActivity,
                            deviceNewInfo
                        )
                        if (musicConfig != null) {
                            newMusicIndex = index
                            deviceModel.hasSupportNewMusic = true
                        } else {
                            deviceModel.hasNotSupportNewMusic = true
                        }
                    }
                    //支持手机拾音
                    val micType =
                        MicSpConfig.getInstance(item.sku, item.device)
                            .getPickUpMode(SameModeInfo(item))
                    if (micType > 0) {
                        if (micCmdStatus == 0) {
                            micCmdStatus = micType
                        } else if (micCmdStatus != micType) {
                            this.hasDifferMicCmd = true
                        }
                        supportMicIndex = index
                        deviceModel.hasSupportMicByPhone = true
                    } else {
                        deviceModel.hasNotSupportMicByPhone = true
                    }

                    //找出设备中最大的版本号
                    if (versionInt > maxVersion) {
                        deviceModel.maxVersionSoft = item.versionSoft
                    }
                }
                showPartBrightnessHint =
                    deviceModel.hasSupportPartBrightness && deviceModel.hasNotSupportPartBrightness
                hasDiffPartBrightnessDevice = showPartBrightnessHint

                showMusicModeHint =
                    deviceModel.hasSupportNewMusic && deviceModel.hasNotSupportNewMusic
                showMicModeHint =
                    deviceModel.hasSupportMicByPhone && deviceModel.hasNotSupportMicByPhone && !wifiFactor

                AbsMicFragmentV4.setBleGroupSupportMicByPhone(deviceModel.hasSupportMicByPhone && !wifiFactor)
                if (sceneMoreIndex >= 0) {
                    finalDeviceModel =
                        JsonUtil.deepCopy(groupDetailsModel.devices[sceneMoreIndex]) as DeviceModel
                } else if (gradualIndex >= 0) {
                    finalDeviceModel =
                        JsonUtil.deepCopy(groupDetailsModel.devices[gradualIndex]) as DeviceModel
                } else if (newMusicIndex >= 0) {
                    finalDeviceModel =
                        JsonUtil.deepCopy(groupDetailsModel.devices[newMusicIndex]) as DeviceModel
                } else if (partBrightnessIndex >= 0) {
                    finalDeviceModel =
                        JsonUtil.deepCopy(groupDetailsModel.devices[partBrightnessIndex]) as DeviceModel
                } else if (supportMicIndex >= 0) {
                    finalDeviceModel =
                        JsonUtil.deepCopy(groupDetailsModel.devices[supportMicIndex]) as DeviceModel
                }
                finalDeviceModel.ic = deviceModel.ic
                finalDeviceModel.ic_sub_1 = deviceModel.ic_sub_1
                finalDeviceModel.ic_sub_2 = deviceModel.ic_sub_2
                finalDeviceModel.segmentCount = deviceModel.segmentCount
                finalDeviceModel.maxVersionSoft = deviceModel.maxVersionSoft

                sameModeInfo =
                    if (connection > 0 && !refresh) sameModeInfo else SameModeInfo(finalDeviceModel).apply {
                    this.factorVersion = factorVersion ?: 0
                }
                SafeLog.i("DengFei_debug") {
                    "deviceListChanged() set sameModeInfo = ${
                        JsonUtil.toJson(
                            sameModeInfo
                        )
                    }"
                }
            }

        } else {
            deviceConnectType = SameModeDeviceItem.DEVICE_TYPE_NO_SUPPORT
        }
        getModeList()
        if (deviceConnectType == SameModeDeviceItem.DEVICE_TYPE_IOT || deviceConnectType == SameModeDeviceItem.DEVICE_TYPE_BLE) {
            notifyDeviceListUpdate(groupDetailsModel.devices, deviceConnectType)
        } else {
            notifyDeviceListUpdate(ArrayList(), DEVICE_TYPE_EMPTY)
        }
        sameModelConfig?.let { config ->
            config.dfMode?.let { mode ->
                if (!initDefMode) {
                    curChooseMode = mode
                    initDefMode = true
                }
            }
        }
        if (showingModeList.isNotEmpty() && !showingModeList.contains(curChooseMode)) {
            curChooseMode = showingModeList.first()
        }
        if (connection == 0 || refresh) {
            deviceListRefresh.value = true
        }
        refreshDeviceListStatus(ConcurrentHashMap<String, Boolean>())
    }

    private fun checkDeviceGoodsType(item: DeviceModel) {
        if (item.goodsType == 0) {
            when (item.sku) {
                "H6102" -> {
                    val checkSupportNewDreamColorFuc =
                        OldDreamColorUtil.checkSupportNewDreamColorFuc(
                            item.sku,
                            item.versionSoft
                        )
                    //支持新详情页功能
                    if (checkSupportNewDreamColorFuc) {
                        item.goodsType =
                            GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1
                    }
                }

                "H6125", "H6126" -> {
                    val checkSupportNewDreamColorFuc =
                        OldDreamColorUtil.checkSupportNewDreamColorFuc(
                            item.sku,
                            item.versionSoft
                        )
                    SafeLog.i(TAG) { "itemClickOp() checkSupportNewDreamColorFuc = $checkSupportNewDreamColorFuc" }
                    if (checkSupportNewDreamColorFuc) {
                        item.goodsType = GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1
                    }
                }

                "H6127", "H6116", "H6107", "H6161" -> {
                    val checkSupportNewDreamColorFuc =
                        OldDreamColorUtil.checkSupportNewDreamColorFuc(
                            item.sku,
                            item.versionSoft
                        )
                    SafeLog.i(TAG) { "itemClickOp() checkSupportNewDreamColorFuc = $checkSupportNewDreamColorFuc" }
                    if (checkSupportNewDreamColorFuc) {
                        item.goodsType = GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1
                    }
                }

                "H6163", "H6117" -> {
                    val checkSupportNewDreamColorFuc4BleWifi =
                        OldDreamColorUtil.checkSupportNewDreamColorFuc4BleWifi(
                            item.sku,
                            item.versionHard
                        )
                    if (checkSupportNewDreamColorFuc4BleWifi) {
                        item.goodsType =
                            GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1
                    }
                }

                "H6110", "H614B", "H614E", "H6159" -> {
                    val checkSupportOldRgbBk4BleWifi =
                        OldRgbBkUtil.isOldRgbBk4BleWifi(
                            item.sku,
                            item.pactType, item.pactCode
                        )
                    if (checkSupportOldRgbBk4BleWifi) {
                        item.goodsType = GoodsType.GOODS_TYPE_VALUE_BK_BLE_WIFI_RGB
                    }
                }

                "H6113", "H6114", "H6138", "H6139" -> {
                    val checkSupportOldRgbBk =
                        OldRgbBkUtil.isOldRgbBk(
                            item.sku,
                            item.pactType, item.pactCode
                        )
                    if (checkSupportOldRgbBk) {
                        item.goodsType = GoodsType.GOODS_TYPE_VALUE_BK_BLE_RGB
                    }
                }
            }
        }
        if (item.goodsType == GoodsType.GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1) {
            val rgbicBk = OldRgbicBkUtil.isRgbicBk4Ble(item.sku, item.pactType, item.pactCode)
            if (rgbicBk) {
                item.goodsType = GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1
            }
        }
        //后面补在下面这个方法
        SkuToGoodsTypeUtil.skuToGoodsType(item)
    }

    open fun notifyDeviceListUpdate(
        devices: MutableList<DeviceModel>,
        deviceConnectType: Int,
    ) {
    }

    fun getModeList(): MutableList<Int> {
        /**展示的模式列表*/
        val retModeList = CopyOnWriteArrayList<Int>()
        if (showingModeList.isEmpty()) {
            val temList = mutableListOf<Int>().apply {
                add(Component.sub_component_type_mode_color)
                add(Component.sub_component_type_mode_scene)
                add(Component.sub_component_type_mode_music)
                add(Component.sub_component_type_mode_diy)
            }
            showingModeList.addAll(
                SameModeDetailConfig.getModelConfig(sameModeInfo)?.supportModelList
                    ?: temList
            )
            val supportDiyNewEdit =
                DiyNewConfig.isSupportDiyNewEdit(sameModeInfo.goodsType, sameModeInfo.sku)
            if (supportDiyNewEdit) {
                if (!showingModeList.contains(Component.sub_component_type_mode_share)) {
                    showingModeList.add(Component.sub_component_type_mode_share)
                }
            }
        }
        val sortModeList = mutableListOf<Int>().apply {
            add(Component.sub_component_type_mode_video)
            add(Component.sub_component_type_mode_operate)
            add(Component.sub_component_type_mode_color)
            add(Component.sub_component_type_mode_scene)
            add(Component.sub_component_type_mode_illumination)
            add(Component.sub_component_type_mode_music)
            add(Component.sub_component_type_mode_share)
            add(Component.sub_component_type_mode_diy)
            add(Component.sub_component_type_mode_tuYa)
            add(Component.sub_component_type_mode_ai)
            add(Component.sub_component_type_mode_ai2)
        }
        //重新排序
        sortModeList.forEach { sort ->
            showingModeList.find {
                it == sort
            }?.run {
                retModeList.add(this)
            }
        }
        showingModeList.clear()
        showingModeList.addAll(retModeList)
        return retModeList
    }

    /**
     * 变更选中的模式
     */
    fun changeChooseMode(chooseNewMode: Int) {
        SafeLog.i(TAG) { "changeChooseMode() chooseNewMode = $chooseNewMode" }
        /**统计模式点击次数*/
        ModeConfig.configAnalyticModeClick(sameModeInfo.sku, chooseNewMode)
        if (chooseNewMode != curChooseMode) {
            this.curChooseMode = chooseNewMode
            this.curChooseModeChange.postValue(true)
        }
    }

    companion object {
        fun isOldSameDetail(devices: List<DeviceModel>?): Boolean {

            //无设备跳转新详情页
            if (devices.isNullOrEmpty()) {
                return false
            }
            devices.forEach { deviceModel ->
                /*由子类提供判断功能版本的方法*/
                SameModeDetailConfig.getModelConfig(SameModeInfo(deviceModel))
                    ?: return true
            }
            return false
        }
    }

    override fun onCleared() {
        super.onCleared()
        rootView = null
    }
}

