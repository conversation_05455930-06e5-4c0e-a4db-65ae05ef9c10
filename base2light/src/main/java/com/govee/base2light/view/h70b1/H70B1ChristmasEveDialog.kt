package com.govee.base2light.view.h70b1

import android.content.Context
import com.govee.base2home.color.IColorChoose
import com.govee.base2home.color.PaletteDialogNew
import com.govee.base2home.color.getIPalette
import com.govee.base2home.util.ClickUtil
import com.govee.base2home.util.UIUtil
import com.govee.base2light.ble.music.AbsNewMusicEffect
import com.govee.base2light.ble.music.h70b1.H70B1ChristmasEveMusic
import com.govee.base2light.light.v1.AbsNewMusicFragment.SubMusicMode
import com.govee.base2light.view.Dialog4ColorRecommendV1
import com.govee.base2light.view.h6057.AbsDialog3
import com.govee.ui.R
import com.ihoment.base2app.util.ResUtil

/**
 * @package：com.govee.base2light.view.h70b1
 * @time：2023/9/15 09:47
 * @author：zhangchenxiang
 * @des：70B1圣诞夜音乐效果
 */
class H70B1ChristmasEveDialog(
    context: Context,
    sku: String,
    device: String,
    sensitivity: Int,
    icNum: Int,
    musicMode: SubMusicMode,
    sensitivityChangeListener: SensitivityChangeListener
) : AbsDialog3<H70B1ChristmasEveMusic>(
    context,
    sku,
    device,
    sensitivity,
    icNum,
    musicMode,
    sensitivityChangeListener
) {


    companion object {
        @JvmStatic
        fun showDialog(
            context: Context,
            sku: String,
            device: String,
            sensitivity: Int,
            icNum: Int,
            musicMode: SubMusicMode,
            sensitivityChangeListener: SensitivityChangeListener
        ) {
            H70B1ChristmasEveDialog(
                context,
                sku,
                device,
                sensitivity,
                icNum,
                musicMode,
                sensitivityChangeListener
            ).show()
        }
    }

    init {
        showColorView()
        hideDirection()
        hideSwitch()
        tvBgColorLabel?.apply {
            text = ResUtil.getString(R.string.showing_sub_diy_graffiti_base_color_label)
            setOnClickListener {
                if (ClickUtil.getInstance.clickQuick()) return@setOnClickListener
                val bgColor: Int = newEffect.bgColor
                showPaletteDialog(bgColor) { newColors: IntArray ->
                    newEffect.bgColor = newColors[0]
                    bgColorUi()
                }
            }
        }
    }

    private fun showPaletteDialog(curColor: Int, chooseColor: (IntArray) -> Unit) {
        val iColorChoose = object : IColorChoose {
            override fun isSupportChooseMultiColors(): Boolean {
                return false
            }

            override fun chooseColor(newColors: IntArray) {
                chooseColor.invoke(newColors)
            }

            override fun chooseColorRealTime(color: Int, isFirstDown: Boolean) {
            }
        }
        val iPalette = getIPalette(
            sku, null, true, iColorChoose,
            null, null, true
        )
        PaletteDialogNew.createDialog(context, iPalette, curColor).show()
    }

    override fun makeNewEffect(
        sku: String,
        device: String,
        musicCode: Int,
        icNum: Int
    ): H70B1ChristmasEveMusic {
        var music = AbsNewMusicEffect.read4NewMultiMusic(
            sku, device, musicCode,
            H70B1ChristmasEveMusic::class.java
        )
        if (AbsNewMusicEffect.isNewMusicEffectInValid(music)) {
            music = H70B1ChristmasEveMusic()
            music.makeDefParams(icNum)
            saveNewMultiMusic(music)
        }
        return music!!
    }

    override fun getLabelRes(): Int {
        return R.string.b2light_music_param_dream_color
    }

    override fun getIconRes(): Int {
        return R.mipmap.new_light_icon_color_same
    }

    override fun getLayout(): Int {
        return com.govee.base2light.R.layout.b2light_dialog_edit_multi_music_h70b1
    }

    override fun paramsUi() {
        bgColorUi()
    }

    private fun bgColorUi() {
        val baseColor: Int = newEffect.bgColor
        UIUtil.setColorPiece(ivChooseColorIcon, baseColor)
    }

    override fun onClickBtnSmartMoreColorLabel() {
        if (ClickUtil.getInstance.clickQuick()) return
        val label1Str = ResUtil.getString(R.string.showing_sub_diy_graffiti_base_color_label)
        val label2Str = ResUtil.getString(R.string.b2light_music_shizixing_color_label)
        Dialog4ColorRecommendV1.showDialog4DiyMode(
            context,
            1, label1Str,
            8, label2Str
        ) { colors1: IntArray, colors2: IntArray? ->
            newEffect.bgColor = colors1[0]
            bgColorUi()
            changeColors(colors2)
        }
    }
}
