package com.govee.base2light.view.h70b1;

import android.content.Context;
import android.widget.TextView;

import com.govee.base2home.color.PaletteDialogNew;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.h70b1.H70b1MusicQuicksand;
import com.govee.base2light.light.v1.AbsNewMusicFragment;
import com.govee.base2light.view.AbsMultiMusicEditDialogV1;
import com.govee.ui.R;
import com.govee.ui.dialog.ItemChooseDialog;

import androidx.annotation.NonNull;
import butterknife.BindView;

/**
 * Create by chenshun on 2022/9/9
 * 流沙
 */
public class QuickSandDialog extends AbsMultiMusicEditDialogV1<H70b1MusicQuicksand> {

    @BindView(com.govee.base2light.R2.id.color_fuc_label)
    TextView color_fuc_label;

    protected QuickSandDialog(Context context, String sku, String device, int sensitivity, int icNum, AbsNewMusicFragment.SubMusicMode musicMode, SensitivityChangeListener sensitivityChangeListener) {
        super(context, sku, device, sensitivity, icNum, musicMode, sensitivityChangeListener);
        color_fuc_label.setText(R.string.color_fuc_label);
    }

    /**
     * 展示弹窗
     *
     * @param context
     * @param sku
     * @param device
     * @param sensitivity
     * @param icNum
     * @param musicMode
     * @param sensitivityChangeListener
     */
    public static void showDialog(Context context, String sku, String device, int sensitivity, int icNum, AbsNewMusicFragment.SubMusicMode musicMode, SensitivityChangeListener sensitivityChangeListener) {
        new QuickSandDialog(context, sku, device, sensitivity, icNum, musicMode, sensitivityChangeListener).show();
    }

    @Override
    protected int getLayout() {
        return com.govee.base2light.R.layout.b2light_dialog_edit_multi_music_shizixing_h70b1;
    }

    @Override
    protected void paramsUi() {
    }

    @Override
    public void hide() {
        ItemChooseDialog.hideDialog();
        PaletteDialogNew.Companion.hideDialog();
        super.hide();
    }

    @NonNull
    @Override
    protected H70b1MusicQuicksand makeNewEffect(String sku, String device, int musicCode, int icNum) {
        H70b1MusicQuicksand music = AbsNewMusicEffect.read4NewMultiMusic(sku, device, musicCode, H70b1MusicQuicksand.class);
        if (AbsNewMusicEffect.isNewMusicEffectInValid(music)) {
            music = new H70b1MusicQuicksand();
            music.makeDefParams(icNum);
            saveNewMultiMusic(music);
        }
        return music;
    }
}