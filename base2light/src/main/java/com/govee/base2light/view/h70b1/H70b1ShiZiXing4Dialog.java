package com.govee.base2light.view.h70b1;

import android.content.Context;

import com.govee.base2home.color.PaletteDialogNew;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.h70b1.H70B1ShiZiXingMusic;
import com.govee.base2light.light.v1.AbsNewMusicFragment;
import com.govee.base2light.view.AbsMultiMusicEditDialogV1;
import com.govee.ui.dialog.ItemChooseDialog;

import androidx.annotation.NonNull;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2022/3/15
 * H610B-十字星音乐弹窗
 */
public class H70b1ShiZiXing4Dialog extends AbsMultiMusicEditDialogV1<H70B1ShiZiXingMusic> {

    protected H70b1ShiZiXing4Dialog(Context context,
                                    String sku,
                                    String device,
                                    int sensitivity,
                                    int icNum,
                                    AbsNewMusicFragment.SubMusicMode musicMode,
                                    SensitivityChangeListener sensitivityChangeListener
    ) {
        super(context, sku, device, sensitivity, icNum, musicMode, sensitivityChangeListener);
    }

    public static void showDialog(Context context,
                                  String sku,
                                  String device,
                                  int sensitivity,
                                  int icNum,
                                  AbsNewMusicFragment.SubMusicMode musicMode,
                                  SensitivityChangeListener sensitivityChangeListener
    ) {
        new H70b1ShiZiXing4Dialog(context, sku, device, sensitivity, icNum, musicMode, sensitivityChangeListener).setEventKey(EVENT_KEY_DEF).show();
    }

    @Override
    protected int getLayout() {
        return com.govee.base2light.R.layout.b2light_dialog_edit_multi_music_shizixing_h70b1;
    }

    @Override
    protected void paramsUi() {
    }

    @Override
    public void hide() {
        ItemChooseDialog.hideDialog();
        PaletteDialogNew.Companion.hideDialog();
        super.hide();
    }

    @NonNull
    @Override
    protected H70B1ShiZiXingMusic makeNewEffect(String sku, String device, int musicCode, int icNum) {
        H70B1ShiZiXingMusic music = AbsNewMusicEffect.read4NewMultiMusic(sku, device, musicCode, H70B1ShiZiXingMusic.class);
        if (AbsNewMusicEffect.isNewMusicEffectInValid(music)) {
            music = new H70B1ShiZiXingMusic();
            music.makeDefParams(icNum);
            saveNewMultiMusic(music);
        }
        return music;
    }
}