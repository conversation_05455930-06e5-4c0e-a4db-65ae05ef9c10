package com.govee.base2light.view.h70b1;

import android.content.Context;
import android.widget.TextView;

import com.govee.base2home.color.IColorChoose;
import com.govee.base2home.color.IPalette;
import com.govee.base2home.color.PaletteDialogNew;
import com.govee.base2home.color.PaletteInterfaceKt;
import com.govee.base2home.util.ClickUtil;
import com.govee.base2home.util.UIUtil;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.h70b1.H70b1MusicPinPu;
import com.govee.base2light.light.v1.AbsNewMusicFragment;
import com.govee.base2light.view.Dialog2Energy4H610B;
import com.govee.base2light.view.Dialog4ColorRecommendV1;
import com.govee.base2light.view.h6057.AbsDialog3;
import com.govee.ui.R;
import com.ihoment.base2app.util.ResUtil;

import java.util.List;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2022/5/18
 * 频谱
 */
public class H70b1PinPuDialog extends AbsDialog3<H70b1MusicPinPu> {
    protected H70b1PinPuDialog(Context context, String sku, String device, int sensitivity, int icNum, AbsNewMusicFragment.SubMusicMode musicMode, SensitivityChangeListener sensitivityChangeListener, @NonNull List<Dialog2Energy4H610B.DirectionAdapter.Direction> supportDirections) {
        super(context, sku, device, sensitivity, icNum, musicMode, sensitivityChangeListener, supportDirections);
        showColorView();
        TextView textView = getTvBgColorLabel();
        if (textView != null) {
            textView.setOnClickListener(v -> {
                if (ClickUtil.getInstance.clickQuick()) return;
                int bgColor = newEffect.getBallColor();
                IColorChoose iColorChoose = new IColorChoose() {
                    @Override
                    public boolean isSupportChooseMultiColors() {
                        return false;
                    }

                    @Override
                    public void chooseColor(@NonNull int[] newColors) {
                        newEffect.setBallColor(newColors[0]);
                        bgColorUi();
                    }

                    @Override
                    public void chooseColorRealTime(int color, boolean isFirstDown) {

                    }
                };
                IPalette iPalette = PaletteInterfaceKt.getIPalette(sku, null, false, iColorChoose, null, null);
                PaletteDialogNew.Companion.createDialog(context, iPalette, bgColor, true).show();
            });
        }

    }


    /**
     * 展示弹窗
     *
     * @param context
     * @param sku
     * @param device
     * @param sensitivity
     * @param icNum
     * @param musicMode
     * @param sensitivityChangeListener
     */
    public static void showDialog(Context context, String sku, String device, int sensitivity, int icNum, AbsNewMusicFragment.SubMusicMode musicMode, SensitivityChangeListener sensitivityChangeListener, @NonNull List<Dialog2Energy4H610B.DirectionAdapter.Direction> supportDirections) {
        new H70b1PinPuDialog(context, sku, device, sensitivity, icNum, musicMode, sensitivityChangeListener, supportDirections).show();
    }

    @NonNull
    @Override
    protected H70b1MusicPinPu makeNewEffect(String sku, String device, int musicCode, int icNum) {
        H70b1MusicPinPu music = AbsNewMusicEffect.read4NewMultiMusic(sku, device, musicCode, H70b1MusicPinPu.class);
        if (AbsNewMusicEffect.isNewMusicEffectInValid(music)) {
            music = new H70b1MusicPinPu();
            music.makeDefParams(icNum);
            saveNewMultiMusic(music);
        }
        return music;
    }

    @Override
    protected int getLabelRes() {
        return R.string.b2light_music_param_dream_color;
    }

    @Override
    protected int getIconRes() {
        return R.mipmap.new_light_icon_color_same;
    }

    @Override
    protected void paramsUi() {
        super.paramsUi();
        bgColorUi();
    }

    private void bgColorUi() {
        int baseColor = newEffect.getBallColor();
        UIUtil.setColorPiece(getIvChooseColorIcon(), baseColor);
    }

    @Override
    public void onClickBtnSmartMoreColorLabel() {
        if (ClickUtil.getInstance.clickQuick()) return;
        String label1Str = ResUtil.getString(R.string.showing_sub_diy_graffiti_base_color_label);
        String label2Str = ResUtil.getString(R.string.b2light_music_shizixing_color_label);
        Dialog4ColorRecommendV1.showDialog4DiyMode(context,
                1, label1Str,
                8, label2Str,
                (colors1, colors2) -> {
                    newEffect.setBallColor(colors1[0]);
                    bgColorUi();
                    changeColors(colors2);
                });
    }

    @Override
    protected int getLayout() {
        return com.govee.base2light.R.layout.b2light_dialog_edit_multi_music_h70b1;
    }
}
