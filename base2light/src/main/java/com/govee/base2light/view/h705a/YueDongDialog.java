package com.govee.base2light.view.h705a;

import android.content.Context;
import android.text.TextUtils;
import android.widget.ImageView;

import com.govee.base2home.color.IColorChoose;
import com.govee.base2home.color.IPalette;
import com.govee.base2home.color.PaletteDialogNew;
import com.govee.base2home.color.PaletteInterfaceKt;
import com.govee.base2home.util.ClickUtil;
import com.govee.base2home.util.UIUtil;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.h705a.YueDongMusic4H705a;
import com.govee.base2light.light.v1.AbsNewMusicFragment;
import com.govee.base2light.view.AbsMultiMusicEditDialogV1;
import com.govee.ui.component.LinearProgressSeekBarV2;
import com.govee.ui.dialog.ItemChooseDialog;

import androidx.annotation.NonNull;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * Create by xieyingwu on 2022/3/15
 * H610B-跃动音乐弹窗
 */
public class YueDongDialog extends AbsMultiMusicEditDialogV1<YueDongMusic4H705a> {
    @BindView(com.govee.base2light.R2.id.iv_choose_color_icon)
    ImageView ivChooseColorIcon;
    @BindView(com.govee.base2light.R2.id.brightness_seek_bar)
    LinearProgressSeekBarV2 brightnessSeekBar;

    protected YueDongDialog(Context context, String sku, String device, int sensitivity, int icNum, AbsNewMusicFragment.SubMusicMode musicMode, SensitivityChangeListener sensitivityChangeListener
    ) {
        super(context, sku, device, sensitivity, icNum, musicMode, sensitivityChangeListener);
    }

    public static void showDialog(Context context, String sku, String device, int sensitivity, int icNum, AbsNewMusicFragment.SubMusicMode musicMode, SensitivityChangeListener sensitivityChangeListener
    ) {
        new YueDongDialog(context, sku, device, sensitivity, icNum, musicMode, sensitivityChangeListener)
                .setEventKey(EVENT_KEY_DEF)
                .show();
    }

    @Override
    protected int getLayout() {
        return com.govee.base2light.R.layout.b2light_dialog_edit_multi_music_yuedong_h705a;
    }

    @Override
    protected void paramsUi() {
        bgColorUi();
        brightnessUi();
    }


    private void brightnessUi() {
        int[] percentValueRange = newEffect.getPercentValueRange();
        if (brightnessSeekBar != null) {
            brightnessSeekBar.setMax(percentValueRange[0], percentValueRange[1]);
            brightnessSeekBar.setProgress(newEffect.getBrightness() - 1);
            brightnessSeekBar.setListener(progress -> {
                int realValue = progress + percentValueRange[0];
                newEffect.setBrightness(realValue);
            });
        }
    }

    private void bgColorUi() {
        int baseColor = newEffect.getBgColor();
        UIUtil.setColorPiece(ivChooseColorIcon, baseColor);
    }


    @OnClick(com.govee.base2light.R2.id.tv_bg_color_label)
    public void onClickBgColorChange() {
        if (ClickUtil.getInstance.clickQuick()) return;
        int bgColor = newEffect.getBgColor();
        IColorChoose iColorChoose = new IColorChoose() {
            @Override
            public boolean isSupportChooseMultiColors() {
                return false;
            }

            @Override
            public void chooseColor(@NonNull int[] newColors) {
                newEffect.setBgColor(newColors[0]);
                bgColorUi();
            }

            @Override
            public void chooseColorRealTime(int color, boolean isFirstDown) {

            }
        };
        showPaletteDialog(bgColor, iColorChoose);
    }

    private void showPaletteDialog(int curColor, IColorChoose iColorChoose) {
        IPalette iPalette = PaletteInterfaceKt.getIPalette(sku, null, true, iColorChoose, null, null, true);
        PaletteDialogNew.Companion.createDialog(context, iPalette, curColor, true).show();
    }

    @Override
    public void hide() {
        ItemChooseDialog.hideDialog();
        PaletteDialogNew.Companion.hideDialog();
        super.hide();
    }

    @NonNull
    @Override
    protected YueDongMusic4H705a makeNewEffect(String sku, String device, int musicCode, int icNum) {
        YueDongMusic4H705a music = AbsNewMusicEffect.read4NewMultiMusic(sku, device, musicCode, YueDongMusic4H705a.class);
        if (AbsNewMusicEffect.isNewMusicEffectInValid(music)) {
            music = new YueDongMusic4H705a();
            music.makeDefParams(icNum);
            if ("H706A".equals(sku) || "H706B".equals(sku) || "H706C".equals(sku)) {    //h706a相对亮度默认15
                music.setBrightness(15);
            }
            if (TextUtils.equals("H7037", sku) || TextUtils.equals("H7038", sku) || TextUtils.equals("H7039", sku)) {    //h7037,7038,7039相对亮度默认25
                music.setBrightness(25);
            }
            saveNewMultiMusic(music);
        }
        return music;
    }
}