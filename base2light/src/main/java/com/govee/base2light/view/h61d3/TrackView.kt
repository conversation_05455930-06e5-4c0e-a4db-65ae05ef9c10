package com.govee.base2light.view.h61d3

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.Path
import android.graphics.PathMeasure
import android.graphics.Point
import android.graphics.RectF
import android.graphics.drawable.ColorDrawable
import android.util.AttributeSet
import android.util.Log
import android.view.GestureDetector
import android.view.MotionEvent
import android.view.View
import com.govee.base2home.util.DisplayUtils.dp2px
import com.govee.base2kt.utils.ColorUtils
import com.govee.base2light.ac.diy.v1.h61d3.ViewDiyGraffitiH61D3
import com.govee.ui.R
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil
import java.util.LinkedList
import java.util.Stack
import kotlin.math.abs
import kotlin.math.min
import kotlin.math.sqrt

class TrackView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    //原始点的paint
    private val originPointPaint = Paint().apply {
        style = Paint.Style.FILL
        isAntiAlias = true
        strokeWidth = 2F
    }

    //原始点的paint
    private val clusterPointPaint = Paint().apply {
        style = Paint.Style.FILL
        isAntiAlias = true
        strokeWidth = 1F
    }

    //分隔线的paint
    private val splitPathPaint = Paint().apply {
        style = Paint.Style.STROKE
        isAntiAlias = true
    }

    //线条的paint
    private val pathPaint = Paint().apply {
        style = Paint.Style.STROKE
        isAntiAlias = true
    }

    //已选择线段外围的paint
    private val selectedOuterPathPaint = Paint().apply {
        style = Paint.Style.STROKE
        isAntiAlias = true
    }

    //已选择线段内围的paint
    private val selectedInnerPathPaint = Paint().apply {
        style = Paint.Style.STROKE
        isAntiAlias = true
    }
    private val originList = mutableListOf<Point>()
    private val sortList = mutableListOf<Point>()
    private val pathMeasure = PathMeasure()
    private val totalPath = Path()
    private var pathRectF = RectF()
    private val pathList = mutableListOf<Path>()
    private val splitPathList = mutableListOf<Path>()
    private var segmentCount = 14
    private var splitCount = 13
    private var splitLength = dp2px(3F)
    private var slidePath = Path()
    private var lastPointX = 0F
    private var lastPointY = 0F
    private var lastX = 0F
    private var lastY = 0F
    private var oldDist = 0.0
    private var moveDist = 0.0
    private var downX1 = 0F
    private var downX2 = 0F
    private var downY1 = 0F
    private var downY2 = 0F
    private var isZoom = false
    private var curMode = MODE_NOT_TOUCH
    private var actionDownTime = 0L
    private val visitedPoints = mutableSetOf<Point>()
    private val linkTrackUndoStack = Stack<List<Point>>()
    private val linkTrackRedoStack = Stack<List<Point>>()
    private val graffitiUndoStack = LinkedList<Map<Int, Int>>()
    private val graffitiRedoStack = LinkedList<Map<Int, Int>>()
    private val selectedSegmentList = mutableListOf<Int>()          //已经选择的分段集合
    private val selectedSegmentColorList =
        mutableListOf<Int>()     //已经设置过的分段颜色集合，集合大小等于分段数，没有设置过颜色的位置颜色值为0
    private var currentSegmentColor = 0                            //当前选择的分段颜色
    private var underColor = 0                                     //底色
    private var powerSourceIndex = -1                               //电源位置
    private val sourceBitmap by lazy {
        ResUtil.getBitmapWithTheme(R.mipmap.h61d3_icon_zaoxing_dianyuan)
    }
    private val sourcePressBitmap by lazy {
        ResUtil.getBitmapWithTheme(R.mipmap.h61d3_icon_zaoxing_dianyuan_press)
    }
    private val centerPointBitmap by lazy {
        ResUtil.getBitmapWithTheme(R.mipmap.h61d3_icon_center)
    }
    private var ratio = 1.0
    private var offsetX = 0.0
    private var offsetY = 0.0
    private var centerPointX = 0
    private var centerPointY = 0
    private val gestureDetector =
        GestureDetector(context, object : GestureDetector.SimpleOnGestureListener() {
            override fun onDoubleTap(e: MotionEvent): Boolean {
                setScale(1F)
                return super.onDoubleTap(e)
            }
        })
    private var showSourceBitmap = false

    //选择电源的监听
    var powerSourceSelectListener: ((isSelect: Boolean) -> Unit)? = null

    //连线的回退、撤销监听
    var linkTrackUndoRedoListener: ((canUndo: Boolean, canRedo: Boolean) -> Unit)? = null

    //涂鸦的回退、撤销监听
    var graffitiUndoRedoListener: ((canUndo: Boolean, canRedo: Boolean) -> Unit)? = null
    var graffitiChangeListener: (() -> Unit)? = null
    var selectChangeListener: ((allSelect: Boolean) -> Unit)? = null
    var drawLineListener: (() -> Unit)? = null      //不要在此listener里面做耗时操作！
    var isPolyLineLink = true
        private set

    companion object {
        const val TAG = "TrackView"
        const val VALID_MIN_DISTANCE = 35   //有效的最小距离
        const val MAX_STEP = 10          //最多可撤销的步数
        const val SCALE_MAX = 5F
        const val SCALE_MIN = 1F
        const val CIRCLE_RADIUS = 20F
        const val MODE_NOT_TOUCH = 1    //不允许触碰模式
        const val MODE_LINK_TRACK = 2   //连线模式
        const val MODE_LINK_DONE = 3    //连线完成，选择电源点的位置
        const val MODE_GRAFFITI = 4     //涂鸦模式
        const val MODE_COLOR = 5        //颜色模式
        const val MODE_SELECT_POINT = 6 //点击选点模式，只允许点击
    }

    private var originPointColor = Color.parseColor("#FFD8D8D8")
    private var originPointRadius = dp2px(2F)
    private var clusterPointColor = Color.parseColor("#FFD8D8D8")
    private var clusterPointRadius = dp2px(5F)
    private var splitPathColor = Color.parseColor("#FFC3C3C3")
    private var splitPathStrokeWidth = dp2px(7.5F)
    private var pathDefaultColor = Color.parseColor("#FFE8E8E8")
    private var pathCompleteColor = Color.parseColor("#00ACE7")
    private var pathStrokeWidth = dp2px(7.5F)
    private var selectedOuterPathColor = Color.parseColor("#FFBEBEBE")
    private var selectedOuterPathStrokeWidth = dp2px(11F)
    private var selectedInnerPathColor = Color.parseColor("#00ACE7")
    private var selectedInnerPathStrokeWidth = dp2px(7.5F)
    private var pathStrokeOuterColor = ResUtil.getColor(R.color.ui_line_style_15_stroke_color)
    private var backgroundColor: Int? = null

    init {
        val ta = context.obtainStyledAttributes(attrs, com.govee.base2light.R.styleable.TrackView)
        originPointColor = ta.getColor(
            com.govee.base2light.R.styleable.TrackView_trackViewOriginPointColor,
            originPointColor
        )
        originPointRadius = ta.getDimension(
            com.govee.base2light.R.styleable.TrackView_trackViewOriginPointRadius,
            originPointRadius
        )
        clusterPointColor = ta.getColor(
            com.govee.base2light.R.styleable.TrackView_trackViewClusterPointColor,
            clusterPointColor
        )
        clusterPointRadius = ta.getDimension(
            com.govee.base2light.R.styleable.TrackView_trackViewClusterPointRadius,
            clusterPointRadius
        )
        splitPathColor = ta.getColor(
            com.govee.base2light.R.styleable.TrackView_trackViewSplitPathColor,
            splitPathColor
        )
        splitPathStrokeWidth = ta.getDimension(
            com.govee.base2light.R.styleable.TrackView_trackViewSplitPathStrokeWidth,
            splitPathStrokeWidth
        )
        splitLength = ta.getDimension(
            com.govee.base2light.R.styleable.TrackView_trackViewSplitPathLength,
            splitLength
        )
        pathDefaultColor = ta.getColor(
            com.govee.base2light.R.styleable.TrackView_trackViewPathDefaultColor,
            pathDefaultColor
        )
        pathCompleteColor = ta.getColor(
            com.govee.base2light.R.styleable.TrackView_trackViewPathCompleteColor,
            pathCompleteColor
        )
        pathStrokeWidth = ta.getDimension(
            com.govee.base2light.R.styleable.TrackView_trackViewPathStrokeWidth,
            pathStrokeWidth
        )
        selectedOuterPathColor = ta.getColor(
            com.govee.base2light.R.styleable.TrackView_trackViewSelectedOuterPathColor,
            selectedOuterPathColor
        )
        selectedOuterPathStrokeWidth = ta.getDimension(
            com.govee.base2light.R.styleable.TrackView_trackViewSelectedOuterPathStrokeWidth,
            selectedOuterPathStrokeWidth
        )
        selectedInnerPathColor = ta.getColor(
            com.govee.base2light.R.styleable.TrackView_trackViewSelectedInnerPathColor,
            selectedInnerPathColor
        )
        selectedInnerPathStrokeWidth = ta.getDimension(
            com.govee.base2light.R.styleable.TrackView_trackViewSelectedInnerPathStrokeWidth,
            selectedInnerPathStrokeWidth
        )
        ta.recycle()
        //初始化集合
        initSegmentColor()

        originPointPaint.apply { color = originPointColor }
        clusterPointPaint.apply { color = clusterPointColor }
        splitPathPaint.apply {
            color = splitPathColor
            strokeWidth = splitPathStrokeWidth
        }
        pathPaint.apply {
            color = pathDefaultColor
            strokeWidth = pathStrokeWidth
        }
        selectedOuterPathPaint.apply {
            color = selectedOuterPathColor
            strokeWidth = selectedOuterPathStrokeWidth
        }
        selectedInnerPathPaint.apply {
            color = selectedInnerPathColor
            strokeWidth = selectedInnerPathStrokeWidth
        }
        if (background is ColorDrawable) {
            backgroundColor = (background as ColorDrawable).color
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        when (curMode) {
            MODE_NOT_TOUCH -> {
                drawNotTouchModeStyle(canvas)
            }

            MODE_LINK_TRACK -> {
                drawLineListener?.invoke()
                drawLinkTrackModeStyle(canvas)
            }

            MODE_LINK_DONE -> {
                drawLinkDoneModeStyle(canvas)
            }

            MODE_GRAFFITI -> {
                drawGraffitiModeStyle(canvas)
            }

            MODE_COLOR -> {
                drawColorModeStyle(canvas)
            }

            MODE_SELECT_POINT -> {
                drawSelectPointModeStyle(canvas)
            }
        }
    }

    private fun drawNotTouchModeStyle(canvas: Canvas) {
        if (sortList.isEmpty()) return
        pathPaint.color = pathDefaultColor
        pathPaint.strokeCap = Paint.Cap.ROUND

        //绘制分段的路径
        for (i in pathList.indices) {
            canvas.drawPath(pathList[i], pathPaint)
        }

        for (i in splitPathList.indices) {
            canvas.drawPath(splitPathList[i], splitPathPaint)
        }

        if (showSourceBitmap) {
            val bmpWidth = sourceBitmap?.width ?: 0
            val bmpHeight = sourceBitmap?.height ?: 0
            sourcePressBitmap?.let {
                canvas.drawBitmap(
                    it,
                    sortList[0].x - bmpWidth / 2f,
                    sortList[0].y - bmpHeight / 2f,
                    clusterPointPaint
                )
            }
        }
    }

    private fun drawLinkTrackModeStyle(canvas: Canvas) {
        //绘制原始点
        if (originList.isEmpty()) return
        for (point in originList) {
            canvas.drawCircle(
                point.x.toFloat(),
                point.y.toFloat(),
                originPointRadius,
                originPointPaint
            )
        }
        //绘制聚类的点（稀疏点）
//        if (sortList.isEmpty()) return
//        for (point in sortList) {
//            canvas.drawCircle(point.x.toFloat(), point.y.toFloat(), clusterPointRadius, clusterPointPaint)
//        }
        pathPaint.strokeCap = Paint.Cap.ROUND
        pathPaint.color = pathCompleteColor
        canvas.drawPath(slidePath, pathPaint)
    }

    private fun drawLinkDoneModeStyle(canvas: Canvas) {
        pathPaint.color = pathCompleteColor
        pathPaint.strokeCap = Paint.Cap.ROUND
        canvas.drawPath(totalPath, pathPaint)
        if (sortList.isEmpty()) return
        //这里还要画两个图标，标出电源位置
        val bmpWidth = sourceBitmap?.width ?: 0
        val bmpHeight = sourceBitmap?.height ?: 0
        when (powerSourceIndex) {
            0 -> {
                sourcePressBitmap?.let {
                    canvas.drawBitmap(
                        it,
                        sortList[0].x - bmpWidth / 2f,
                        sortList[0].y - bmpHeight / 2f,
                        clusterPointPaint
                    )
                }
                sourceBitmap?.let {
                    canvas.drawBitmap(
                        it,
                        sortList.last().x - bmpWidth / 2f,
                        sortList.last().y - bmpHeight / 2f,
                        clusterPointPaint
                    )
                }
            }

            sortList.lastIndex -> {
                sourceBitmap?.let {
                    canvas.drawBitmap(
                        it,
                        sortList[0].x - bmpWidth / 2f,
                        sortList[0].y - bmpHeight / 2f,
                        clusterPointPaint
                    )
                }
                sourcePressBitmap?.let {
                    canvas.drawBitmap(
                        it,
                        sortList.last().x - bmpWidth / 2f,
                        sortList.last().y - bmpHeight / 2f,
                        clusterPointPaint
                    )
                }
            }

            else -> {
                sourceBitmap?.let {
                    canvas.drawBitmap(
                        it,
                        sortList[0].x - bmpWidth / 2f,
                        sortList[0].y - bmpHeight / 2f,
                        clusterPointPaint
                    )
                    canvas.drawBitmap(
                        it,
                        sortList.last().x - bmpWidth / 2f,
                        sortList.last().y - bmpHeight / 2f,
                        clusterPointPaint
                    )
                }
            }
        }
    }

    private fun drawGraffitiModeStyle(canvas: Canvas) {
        if (sortList.isEmpty()) return

        pathPaint.color = pathDefaultColor
        pathPaint.strokeCap = Paint.Cap.ROUND
        //绘制分段的路径
        for (i in pathList.indices) {
            if (i == 1 || i == pathList.size - 2) {
                continue
            }
            pathPaint.strokeCap =
                if (i == 0 || i == pathList.size - 1) Paint.Cap.ROUND else Paint.Cap.BUTT
            val segColor = selectedSegmentColorList[i]
            val segShowColor = ColorUtils.changeBlackColor(segColor)
            if (ColorUtils.isNearWhiteColor(segShowColor)) {
                pathPaint.strokeWidth = pathStrokeWidth + 2
                pathPaint.color = pathStrokeOuterColor
                canvas.drawPath(pathList[i], pathPaint)
            }
            pathPaint.color = ColorUtils.changeBlackColor(selectedSegmentColorList[i])
            pathPaint.strokeCap =
                if (i == 0 || i == pathList.size - 1) Paint.Cap.ROUND else Paint.Cap.BUTT
            pathPaint.strokeWidth = pathStrokeWidth
            canvas.drawPath(pathList[i], pathPaint)
        }

        //第二段和倒数第二段需要在最后面画，不然会看到圆角
        pathPaint.strokeCap = Paint.Cap.BUTT
        var segColor = selectedSegmentColorList[1]
        var segShowColor = ColorUtils.changeBlackColor(segColor)
        if (ColorUtils.isNearWhiteColor(segShowColor)) {
            pathPaint.strokeWidth = pathStrokeWidth + 2
            pathPaint.color = pathStrokeOuterColor
            canvas.drawPath(pathList[1], pathPaint)
        }
        pathPaint.color = segShowColor
        pathPaint.strokeWidth = pathStrokeWidth
        canvas.drawPath(pathList[1], pathPaint)

        val element = pathList.size - 2
        pathPaint.strokeCap = Paint.Cap.BUTT
        segColor = selectedSegmentColorList[element]
        segShowColor = ColorUtils.changeBlackColor(segColor)
        if (ColorUtils.isNearWhiteColor(segShowColor)) {
            pathPaint.strokeWidth = pathStrokeWidth + 2
            pathPaint.color = pathStrokeOuterColor
            canvas.drawPath(pathList[element], pathPaint)
        }
        pathPaint.color = segShowColor
        pathPaint.strokeWidth = pathStrokeWidth
        canvas.drawPath(pathList[element], pathPaint)

        //最后画分割线
        for (i in splitPathList.indices) {
            canvas.drawPath(splitPathList[i], splitPathPaint)
        }

        if (showSourceBitmap) {
            val bmpWidth = sourceBitmap?.width ?: 0
            val bmpHeight = sourceBitmap?.height ?: 0
            sourcePressBitmap?.let {
                canvas.drawBitmap(
                    it,
                    sortList[0].x - bmpWidth / 2f,
                    sortList[0].y - bmpHeight / 2f,
                    clusterPointPaint
                )
            }

        }
    }

    private fun drawColorModeStyle(canvas: Canvas) {
        if (sortList.isEmpty()) return

        for (segmentIndex in selectedSegmentList) {
            if (segmentIndex == 0 || segmentIndex == pathList.size - 1) {
                selectedOuterPathPaint.strokeCap = Paint.Cap.ROUND
                selectedInnerPathPaint.strokeCap = Paint.Cap.ROUND
            } else {
                selectedOuterPathPaint.strokeCap = Paint.Cap.BUTT
                selectedInnerPathPaint.strokeCap = Paint.Cap.BUTT
            }
            canvas.drawPath(pathList[segmentIndex], selectedOuterPathPaint)
            val segColor = selectedSegmentColorList[segmentIndex]
            selectedInnerPathPaint.color = ColorUtils.changeBlackColor(segColor)
            canvas.drawPath(pathList[segmentIndex], selectedInnerPathPaint)
        }

        //绘制分段的路径
        pathPaint.color = pathDefaultColor
        pathPaint.strokeCap = Paint.Cap.ROUND
        for (i in pathList.indices) {
            if (!selectedSegmentList.contains(i)) {
                //如果没有设置背景颜色则不画这条线
                backgroundColor?.let { bgColor ->
                    if (i == 1 || i == pathList.size - 2) {
                        selectedOuterPathPaint.color = bgColor
                        selectedOuterPathPaint.strokeCap = Paint.Cap.BUTT
                        //画这条线的目的是为了盖住首尾的圆角
                        canvas.drawPath(pathList[i], selectedOuterPathPaint)
                    }
                }
                pathPaint.strokeCap =
                    if (i == 0 || i == pathList.size - 1) Paint.Cap.ROUND else Paint.Cap.BUTT
                val segColor = selectedSegmentColorList[i]
                val segShowColor = ColorUtils.changeBlackColor(segColor)
                if (ColorUtils.isNearWhiteColor(segShowColor)) {
                    pathPaint.strokeWidth = pathStrokeWidth + 2
                    pathPaint.color = pathStrokeOuterColor
                    canvas.drawPath(pathList[i], pathPaint)
                }
                pathPaint.color = segShowColor
                pathPaint.strokeWidth = pathStrokeWidth
                canvas.drawPath(pathList[i], pathPaint)
            }
        }

        //需要盖一层在上面，不然会看到圆角
        selectedOuterPathPaint.color = selectedOuterPathColor
        if (selectedSegmentList.contains(1)) {
            selectedOuterPathPaint.strokeCap = Paint.Cap.BUTT
            canvas.drawPath(pathList[1], selectedOuterPathPaint)
        }
        selectedInnerPathPaint.strokeCap = Paint.Cap.BUTT
        var segColor = selectedSegmentColorList[1]
        selectedInnerPathPaint.color = ColorUtils.changeBlackColor(segColor)
        canvas.drawPath(pathList[1], selectedInnerPathPaint)

        val element = pathList.size - 2
        if (selectedSegmentList.contains(element)) {
            selectedOuterPathPaint.strokeCap = Paint.Cap.BUTT
            canvas.drawPath(pathList[element], selectedOuterPathPaint)
        }
        selectedInnerPathPaint.strokeCap = Paint.Cap.BUTT
        segColor = selectedSegmentColorList[element]
        selectedInnerPathPaint.color = ColorUtils.changeBlackColor(segColor)
        canvas.drawPath(pathList[element], selectedInnerPathPaint)

        //最后画分割线
        for (i in splitPathList.indices) {
            canvas.drawPath(splitPathList[i], splitPathPaint)
        }

        if (showSourceBitmap) {
            val bmpWidth = sourceBitmap?.width ?: 0
            val bmpHeight = sourceBitmap?.height ?: 0
            sourcePressBitmap?.let {
                canvas.drawBitmap(
                    it,
                    sortList[0].x - bmpWidth / 2f,
                    sortList[0].y - bmpHeight / 2f,
                    clusterPointPaint
                )
            }
        }
    }

    private fun drawSelectPointModeStyle(canvas: Canvas) {
        if (sortList.isEmpty()) return
        pathPaint.color = pathDefaultColor
        pathPaint.strokeCap = Paint.Cap.ROUND

        //绘制分段的路径
        for (i in pathList.indices) {
            canvas.drawPath(pathList[i], pathPaint)
        }

        for (i in splitPathList.indices) {
            canvas.drawPath(splitPathList[i], splitPathPaint)
        }
        val bmpWidth = centerPointBitmap?.width ?: 0
        val bmpHeight = centerPointBitmap?.height ?: 0
        centerPointBitmap?.let {
            canvas.drawBitmap(
                it,
                centerPointX - bmpWidth / 2f,
                centerPointY - bmpHeight / 2f,
                clusterPointPaint
            )
        }

        if (showSourceBitmap) {
            val bmpWidth1 = sourceBitmap?.width ?: 0
            val bmpHeight1 = sourceBitmap?.height ?: 0
            sourcePressBitmap?.let {
                canvas.drawBitmap(
                    it,
                    sortList[0].x - bmpWidth1 / 2f,
                    sortList[0].y - bmpHeight1 / 2f,
                    clusterPointPaint
                )
            }
        }

    }

    /**
     * 生成path并测量path
     */
    private fun pathGenerateAndMeasure() {
        if (sortList.isEmpty() || width == 0 || height == 0) return
        totalPath.reset()
        totalPath.moveTo(sortList[0].x.toFloat(), sortList[0].y.toFloat())

        for (i in 1 until sortList.size - 1) {
            val xMid = (sortList[i].x + sortList[i + 1].x) / 2
            val yMid = (sortList[i].y + sortList[i + 1].y) / 2
            totalPath.quadTo(
                sortList[i].x.toFloat(), sortList[i].y.toFloat(), xMid.toFloat(),
                yMid.toFloat()
            )
        }
        totalPath.lineTo(sortList.last().x.toFloat(), sortList.last().y.toFloat())
        val bounds = RectF()
        totalPath.computeBounds(bounds, true)
        pathRectF = bounds
        pathMeasure.setPath(totalPath, false)
        val length = pathMeasure.length

        //获取每一段的Path及其RectF，RectF用来判断点击或滑动的坐标是否在Path上
        val segmentLength = (pathMeasure.length - splitCount * splitLength) / segmentCount
        pathList.clear()
        splitPathList.clear()
        var start = 0F
        var end = 0F
        for (i in 0 until segmentCount) {
            val path = Path()
            start = i * segmentLength + i * splitLength
            end = start + segmentLength
            pathMeasure.getSegment(start, end, path, true)
            if (i != segmentCount - 1) {
                val splitPath = Path()
                start = end
                end = start + splitLength
                pathMeasure.getSegment(start, end, splitPath, true)
                splitPathList.add(splitPath)
            }
            path.computeBounds(bounds, true)
            pathList.add(path)
        }
        Log.d(TAG, "path length: $length, path count: ${pathList.size}")
    }

    override fun onLayout(changed: Boolean, left: Int, top: Int, right: Int, bottom: Int) {
        super.onLayout(changed, left, top, right, bottom)
        adjustAllPointsAndPathGenerate()
        pathGenerateAndMeasure()
    }

    /**
     * 设置是否显示电源图标
     */
    fun setSourceBitmapShow(show: Boolean) {
        showSourceBitmap = show
    }

    fun setPolyLineAndColor(polyLine: Boolean, color: Int) {
        isPolyLineLink = polyLine
        originPointPaint.color = color
    }

    /**
     * 设置中心点坐标
     * x、y都为-1时中心点显示为View的中心点
     */
    fun setCenterPoint(x: Int, y: Int) {
        centerPointX = if (x == -1) {
            width / 2
        } else {
            (x * ratio + offsetX).toInt()
        }
        centerPointY = if (y == -1) {
            height / 2
        } else {
            (y * ratio + offsetY).toInt()
        }
    }

    fun getCenterPoint(): Pair<Int, Int> {
        var px = ((centerPointX - offsetX) / ratio).toInt()
        var py = ((centerPointY - offsetY) / ratio).toInt()
        px = if (px < 0) 0 else px
        py = if (py < 0) 0 else py
        return Pair(px, py)
    }

    /**
     * 添加原始点
     */
    fun addOriginDotList(list: List<Point>) {
        originList.clear()
        originList.addAll(list)
    }

    /**
     * 添加已排序的点
     */
    fun addSortDotList(list: List<Point>, confirmStartPoint: Boolean = false) {
        if (confirmStartPoint) {
            powerSourceIndex = 0
        }
        sortList.clear()
        sortList.addAll(list)
    }


    fun adjustAllPointsAndPathGenerate() {
        if ((originList.isEmpty() && sortList.isEmpty()) || width == 0 || height == 0) return // 避免空列表引发异常

        var minX = Int.MAX_VALUE
        var minY = Int.MAX_VALUE
        var maxX = 0
        var maxY = 0

        // 计算坐标范围
        originList.forEach {
            if (it.x < minX) minX = it.x
            if (it.y < minY) minY = it.y
            if (it.x > maxX) maxX = it.x
            if (it.y > maxY) maxY = it.y
        }

        sortList.forEach {
            if (it.x < minX) minX = it.x
            if (it.y < minY) minY = it.y
            if (it.x > maxX) maxX = it.x
            if (it.y > maxY) maxY = it.y
        }

        // 计算缩放比例
        val widthRatio = if (maxX - minX != 0) width.toFloat() / (maxX - minX) else 1.0f
        val heightRatio = if (maxY - minY != 0) height.toFloat() / (maxY - minY) else 1.0f
        //这里减0.15是为了避免边缘被割掉一部分
        ratio = min(widthRatio, heightRatio) - 0.15

        // 缩放和居中坐标
        offsetX = (width - (maxX + minX) * ratio) / 2
        offsetY = (height - (maxY + minY) * ratio) / 2

        originList.forEach {
            it.x = (it.x * ratio + offsetX).toInt()
            it.y = (it.y * ratio + offsetY).toInt()
        }

        sortList.forEach {
            it.x = (it.x * ratio + offsetX).toInt()
            it.y = (it.y * ratio + offsetY).toInt()
        }

        pathGenerateAndMeasure()
    }

    /**
     * 手动连线排好序的点
     */
    fun getSlideLinkPointList() = visitedPoints

    /**
     * 自动排序的点
     */
    fun getSortList() = sortList

    /**
     * 获取ic的点的坐标
     */
    fun getIcPointList(pointCount: Int): List<Point> {
        val icPointList = mutableListOf<Point>()
        val segmentPathMeasure = PathMeasure()
        var start = 0F
        var end = 0F
        pathGenerateAndMeasure()
        pathMeasure.setPath(totalPath, false)
        val length = pathMeasure.length / pointCount
        for (i in 0 until pointCount) {
            val path = Path()
            start = i * length
            end = start + length
            pathMeasure.getSegment(start, end, path, true)
            val pos = FloatArray(2)
            segmentPathMeasure.setPath(path, false)
            segmentPathMeasure.getPosTan(pathMeasure.length / 2, pos, null)
            icPointList.add(Point(pos[0].toInt(), pos[1].toInt()))
        }
        return icPointList
    }

    /**
     * 回退连好的轨迹
     */
    fun undoTrack() {
        if (curMode != MODE_LINK_TRACK) return

        if (linkTrackUndoStack.isEmpty()) {
            visitedPoints.clear()
            slidePath.reset()
            linkTrackUndoRedoListener?.invoke(canUndoTrack(), canRedoTrack())
            invalidate()
            return
        }
        val list = linkTrackUndoStack.pop()
        linkTrackRedoStack.push(list)
        visitedPoints.clear()
        //再次校验是否为空
        if (linkTrackUndoStack.isEmpty()) {
            slidePath.reset()
            linkTrackUndoRedoListener?.invoke(canUndoTrack(), canRedoTrack())
            invalidate()
            return
        }
        visitedPoints.addAll(linkTrackUndoStack.peek())
        slidePath.reset()
        val first = visitedPoints.first()
        slidePath.moveTo(first.x.toFloat(), first.y.toFloat())
        lastPointX = first.x.toFloat()
        lastPointY = first.y.toFloat()
        for (i in 1 until visitedPoints.size) {
            val p = visitedPoints.elementAt(i)
            slidePath.quadTo(
                lastPointX,
                lastPointY,
                (p.x + lastPointX) / 2,
                (p.y + lastPointY) / 2
            )
            lastPointX = p.x.toFloat()
            lastPointY = p.y.toFloat()
        }
        invalidate()
        linkTrackUndoRedoListener?.invoke(canUndoTrack(), canRedoTrack())
    }

    /**
     * 撤销回退连好的轨迹
     */
    fun redoTrack() {
        if (curMode != MODE_LINK_TRACK || linkTrackRedoStack.isEmpty()) return
        val list = linkTrackRedoStack.pop()
        visitedPoints.clear()
        visitedPoints.addAll(list)
        linkTrackUndoStack.push(list)
        slidePath.reset()
        val first = visitedPoints.first()
        slidePath.moveTo(first.x.toFloat(), first.y.toFloat())
        lastPointX = first.x.toFloat()
        lastPointY = first.y.toFloat()
        for (i in 1 until visitedPoints.size) {
            val p = visitedPoints.elementAt(i)
            slidePath.quadTo(
                lastPointX,
                lastPointY,
                (p.x + lastPointX) / 2,
                (p.y + lastPointY) / 2
            )
            lastPointX = p.x.toFloat()
            lastPointY = p.y.toFloat()
        }
        invalidate()
        linkTrackUndoRedoListener?.invoke(canUndoTrack(), canRedoTrack())
    }

    /**
     * 是否可以回退和撤销回退
     */
    fun canUndoTrack() = linkTrackUndoStack.isNotEmpty()

    fun canRedoTrack() = linkTrackRedoStack.isNotEmpty()


    /**
     * 回退涂鸦的步骤
     */
    fun undoGraffiti() {
        SafeLog.i(
            TAG,
            "undoGraffiti, undoStack: ${graffitiUndoStack.size}, redoStack: ${graffitiRedoStack.size}"
        )
        if (curMode != MODE_GRAFFITI || (graffitiUndoStack.size <= 1)) return

        checkGraffitiRedoStep()
        graffitiRedoStack.add(graffitiUndoStack.removeLast())
        val map = graffitiUndoStack.last()
        initSegmentColor()
        for (info in map) {
            selectedSegmentColorList[info.key] = info.value
        }
        graffitiUndoRedoListener?.invoke(canUndoGraffiti(), canRedoGraffiti())
        invalidate()
    }

    /**
     * 撤销回退涂鸦的步骤
     */
    fun redoGraffiti() {
        SafeLog.i(
            TAG,
            "redoGraffiti, undoStack: ${graffitiUndoStack.size}, redoStack: ${graffitiRedoStack.size}"
        )
        if (curMode != MODE_GRAFFITI || graffitiRedoStack.isEmpty()) return
        val map = graffitiRedoStack.removeLast()
        checkGraffitiUndoStep()
        graffitiUndoStack.add(map)
        initSegmentColor()
        for (info in map) {
            selectedSegmentColorList[info.key] = info.value
        }
        graffitiUndoRedoListener?.invoke(canUndoGraffiti(), canRedoGraffiti())
        invalidate()
    }

    fun canUndoGraffiti() = graffitiUndoStack.size > 1

    fun canRedoGraffiti() = graffitiRedoStack.isNotEmpty()

    fun hasSetColor(): Boolean {
        var setColor = false
        selectedSegmentColorList.forEach {
            if (it != underColor) {
                setColor = true
                return@forEach
            }
        }
        return setColor
    }

    fun canClear(): Boolean {
        var canClear = false
        for (i in selectedSegmentColorList.indices) {
            if (selectedSegmentColorList[i] != underColor) {
                canClear = true
                break
            }
        }
        return canClear
    }

    /**
     * 设置使用模式
     */
    fun setMode(mode: Int) {
        curMode = mode
        invalidate()
    }

    /**
     * 在一开始调用
     * 设置使用模式、分段数量、底色
     */
    fun setModeSegmentAndUnderColor(mode: Int, count: Int, underColor: Int = 0) {
        if (count < 3) throw IllegalArgumentException("segmentCount必须大于等于3")
        curMode = mode
        segmentCount = count
        splitCount = count - 1
        this.underColor = underColor
        initSegmentColor()
        pathGenerateAndMeasure()
        invalidate()
    }

    /**
     * 设置分段数
     */
    fun setSegmentCount(count: Int) {
        var sCount = count
        if (count < 3) {
            sCount = 14
        }
        segmentCount = sCount
        splitCount = sCount - 1
        initSegmentColor()
        pathGenerateAndMeasure()
    }

    /**
     * 设置分段全选或全不选
     */
    fun setAllSegmentSelect(select: Boolean) {
        selectedSegmentList.clear()
        if (select) {
            for (i in 0 until segmentCount) {
                selectedSegmentList.add(i)
            }
        }
        invalidate()
    }

    /**
     * 反选
     */
    fun reverseSelect() {
        val reverseList = mutableListOf<Int>()
        for (i in 0 until segmentCount) {
            if (!selectedSegmentList.contains(i)) {
                reverseList.add(i)
            }
        }
        selectedSegmentList.clear()
        selectedSegmentList.addAll(reverseList)
        invalidate()
    }

    fun isAllSegmentSelected() = selectedSegmentList.size == segmentCount

    fun getSelectedSegment() = selectedSegmentList
    fun getBulbSelectStatus(): BooleanArray {
        val checks = BooleanArray(segmentCount)
        for (i in 0 until segmentCount) {
            checks[i] = selectedSegmentList.contains(i)
        }
        return checks
    }

    /**
     * ===========================================
     * 以下三个方法仅在涂鸦模式下使用 MODE_GRAFFITI
     * ===========================================
     */

    /**
     * 给已选择的分段设置颜色
     * 也就是先设置颜色，再设置分段
     * 如果设置后不需要点击时设置颜色了，color传0就行
     * 和setOptionClearSegmentColor()方法对应，避免参数传错
     * 所以多加一个方法
     */
    fun setSegmentColor(color: Int) {
        currentSegmentColor = color
    }

    /**
     * 清除颜色
     * 涂鸦模式下调用此方法后，滑动或点击会清除设置的线条的颜色
     */
    fun setOptionSegmentColorClear() {
        currentSegmentColor = 0
    }

    /**
     * 设置底色
     */
    fun setUnderColor(argbColor: Int) {
        //先判断list是否已初始化，如果没初始化则先初始化
        if (selectedSegmentColorList.size < segmentCount) {
            initSegmentColor()
        }
        for (i in 0 until segmentCount) {
            val c = selectedSegmentColorList[i]
            if (c == underColor || c == 0) {
                selectedSegmentColorList[i] = argbColor
            }
        }
        this.underColor = argbColor
        invalidate()
    }

    fun setUnderColor(rgbColor: Int, brightness: Int) {
        val rgb = ColorUtils.getRgb(checkColor(rgbColor))
        val noColor = ColorUtils.isNoColor(rgbColor)
        val percent =
            if (noColor) 100 else 255 * 100.coerceAtMost(10.coerceAtLeast(brightness)) / 100
        val underColor = Color.argb(percent, rgb[0], rgb[1], rgb[2])
        SafeLog.i(ViewDiyGraffitiH61D3.TAG, "handle setTrackViewBgColor: $underColor")
        setUnderColor(underColor)
    }

    private fun checkColor(color: Int): Int {
        return if (color == 0 || color == ColorUtils.toColor(1, 1, 1)) {
            ResUtil.getColor(R.color.ui_color_block_style_10_3)
        } else color
    }

    /**
     * ===================================
     * 此方法仅在颜色模式下调用 MODE_COLOR
     * ===================================
     *
     * 给已选择的分段设置颜色
     * 也就是先选择分段，再设置颜色
     */
    fun setSelectedSegmentColor(color: Int) {
        SafeLog.i(TAG, "setSelectedSegmentColor: $color")
        for (segmentIndex in selectedSegmentList) {
            selectedSegmentColorList[segmentIndex] = color
        }
        invalidate()
    }

    fun updateAllSegmentColor(colors: IntArray) {
        SafeLog.i(TAG, "updateAllSegmentColor: ${colors.toList()}")
        val size = min(colors.size, segmentCount)
        for (i in 0 until size) {
            selectedSegmentColorList[i] = colors[i]
        }
        invalidate()
    }

    fun getSegmentColorList() = selectedSegmentColorList

    fun getSegmentColorMap(): LinkedHashMap<Int, MutableList<Int>> {
        val map = LinkedHashMap<Int, MutableList<Int>>()
        selectedSegmentColorList.forEachIndexed { index, color ->
            if (color != underColor) {
                val colorList = map[color]
                if (colorList != null) {
                    colorList.add(index)
                } else {
                    val newList = mutableListOf<Int>()
                    newList.add(index)
                    map[color] = newList
                }
            }
        }
        return map
    }

    fun setSelectedSegmentColorMap(colorMap: LinkedHashMap<Int, MutableList<Int>>) {
        colorMap.forEach {
            val color = it.key
            it.value.forEach { colorIndex ->
                if (selectedSegmentList.size > colorIndex) {
                    selectedSegmentColorList[colorIndex] = color
                }
            }
        }
        changeOptionGraffitiStack()
        invalidate()
    }

    /**
     * 清理所有的点和路径
     */
    fun clearAll() {
        originList.clear()
        sortList.clear()
        initSegmentColor()
        pathList.clear()
        slidePath.reset()
        visitedPoints.clear()
        selectedSegmentList.clear()
        selectedSegmentList.clear()
        selectedSegmentColorList.clear()
        totalPath.reset()
        splitPathList.clear()
        currentSegmentColor = 0
        powerSourceIndex = -1
        underColor = 0
        invalidate()
    }

    private fun initSegmentColor() {
        selectedSegmentColorList.clear()
        for (i in 0 until segmentCount) {
            selectedSegmentColorList.add(underColor)
        }
    }

    /**
     * 只清除画好的线和路径
     * 不清理点
     */
    fun clearDrawTrack() {
        pathList.clear()
        slidePath.reset()
        visitedPoints.clear()
        linkTrackUndoStack.clear()
        linkTrackRedoStack.clear()
        linkTrackUndoRedoListener?.invoke(canUndoTrack(), canRedoTrack())
        invalidate()
    }

    /**
     * 清除操作栈、所有的涂鸦颜色
     */
    fun clearAllGraffiti() {
        graffitiUndoStack.clear()
        graffitiRedoStack.clear()
        graffitiUndoRedoListener?.invoke(canUndoTrack(), canRedoTrack())
        initSegmentColor()
//        changeOptionGraffitiStack()
        invalidate()
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (curMode == MODE_NOT_TOUCH) {
            return super.onTouchEvent(event)
        }
        if (curMode != MODE_LINK_TRACK && curMode != MODE_SELECT_POINT) {
            gestureDetector.onTouchEvent(event)
        }
        when (event.action and MotionEvent.ACTION_MASK) {
            MotionEvent.ACTION_DOWN -> {
                lastX = event.x
                lastY = event.y
                isZoom = false
                actionDownTime = System.currentTimeMillis()
                parent.requestDisallowInterceptTouchEvent(true)
                if (curMode == MODE_SELECT_POINT) {
                    checkSelectPoint(event)
                }
                return true
            }

            MotionEvent.ACTION_POINTER_DOWN -> {
                isZoom = event.pointerCount >= 2
                downX1 = event.getX(0)
                downX2 = event.getX(1)
                downY1 = event.getY(0)
                downY2 = event.getY(1)
                oldDist = moveDistance(event)
                return false
            }

            MotionEvent.ACTION_MOVE -> {
                if (curMode == MODE_SELECT_POINT) {
                    checkSelectPoint(event)
                    return true
                }
                if (curMode == MODE_GRAFFITI && event.pointerCount == 1 && !isZoom) {
                    //涂鸦模式且不是缩放和双指滑动状态
                    checkSelectSegment(event)
                } else {
                    if (event.pointerCount == 2) {
                        isZoom = true
                        parent.requestDisallowInterceptTouchEvent(true)
                        //两根手指则进行缩放和移动
                        setScaleAndPivot(event)
                    } else if (!isZoom && event.pointerCount == 1 && curMode == MODE_LINK_TRACK) {
                        // 只有在手动连线时才在滑动的时候检查是否经过某个点
                        parent.requestDisallowInterceptTouchEvent(true)
                        checkClickOrSlidePointGroup(event)
                    } else if (!isZoom && event.pointerCount == 1 && curMode == MODE_COLOR) {
                        parent.requestDisallowInterceptTouchEvent(false)
                        return false
                    }
                }
                return true
            }

            MotionEvent.ACTION_UP -> {
                isZoom = false
                //满足这个条件则视为点击
                if (System.currentTimeMillis() - actionDownTime < 100 && event.x - lastX < 10 && event.y - lastY < 10) {
                    when (curMode) {
                        MODE_LINK_TRACK -> {
                            //手动连线模式，则连接点
                            checkClickOrSlidePointGroup(event)
                        }

                        MODE_GRAFFITI, MODE_COLOR -> {
                            //涂鸦和颜色模式，则选中触摸到的分段
                            checkSelectSegment(event)
                        }

                        MODE_LINK_DONE -> {
                            checkSelectPowerSource(event)
                        }

                        MODE_SELECT_POINT -> {
                            checkSelectPoint(event)
                        }
                    }
                }
                //如果是手动连线模式或涂鸦模式，则需要判断是否需要更新撤销回退栈
                if (curMode == MODE_LINK_TRACK) {
                    changeOptionLinkTrackStack()
                } else if (curMode == MODE_GRAFFITI) {
                    changeOptionGraffitiStack(true)
                }
                parent.requestDisallowInterceptTouchEvent(false)
                return true
            }
        }

        return super.onTouchEvent(event)
    }

    private fun findSlidePoint(x: Int, y: Int): Point? {
        var closestPoint: Point? = null

        for (point in sortList) {
            val distance = distance(x, y, point.x, point.y)
            if (distance <= CIRCLE_RADIUS && !visitedPoints.contains(point)) {
                closestPoint = point
                break
            }
        }

        return closestPoint
    }


    private fun checkClickOrSlidePoint(event: MotionEvent) {
        val slidePoint = findSlidePoint(event.x.toInt(), event.y.toInt())
        if (slidePoint != null) {
            if (slidePath.isEmpty) {
                slidePath.moveTo(slidePoint.x.toFloat(), slidePoint.y.toFloat())
                visitedPoints.add(slidePoint)
                lastPointX = slidePoint.x.toFloat()
                lastPointY = slidePoint.y.toFloat()
            } else if (!visitedPoints.contains(slidePoint)) {
                slidePath.quadTo(
                    lastPointX,
                    lastPointY,
                    (slidePoint.x + lastPointX) / 2,
                    (slidePoint.y + lastPointY) / 2
                )
                visitedPoints.add(slidePoint)
                lastPointX = slidePoint.x.toFloat()
                lastPointY = slidePoint.y.toFloat()
            }
            invalidate()
        }
    }

    private fun checkClickOrSlidePointGroup(event: MotionEvent) {
        if (!isPolyLineLink) {
            slideAnywhere(event)
            return
        }
        val slidePoint = findSlideOriginPoint(event.x.toInt(), event.y.toInt()) ?: return
        if (slidePath.isEmpty) {
            slidePath.moveTo(slidePoint.x.toFloat(), slidePoint.y.toFloat())
            visitedPoints.add(slidePoint)
//            addNearPoint(slidePoint)
            lastPointX = slidePoint.x.toFloat()
            lastPointY = slidePoint.y.toFloat()
        } else if (!visitedPoints.contains(slidePoint)) {
            slidePath.quadTo(
                lastPointX,
                lastPointY,
                (slidePoint.x + lastPointX) / 2,
                (slidePoint.y + lastPointY) / 2
            )
            visitedPoints.add(slidePoint)
//            addNearPoint(slidePoint)
            lastPointX = slidePoint.x.toFloat()
            lastPointY = slidePoint.y.toFloat()
        }

        invalidate()
    }

    private fun findSlideOriginPoint(x: Int, y: Int): Point? {
        var nearstPoint: Point? = null
        var nearstDistance = Float.MAX_VALUE

        for (point in originList) {
            if (visitedPoints.contains(point)) {
                continue
            }
            val distance = distance(x, y, point.x, point.y)
            val distance2 = distance(lastPointX.toInt(), lastPointY.toInt(), point.x, point.y)
            val totalDistance = distance + distance2
            if (distance <= CIRCLE_RADIUS) {
                if (nearstDistance > totalDistance) {
                    nearstPoint = point
                    nearstDistance = totalDistance
                }
            }
        }

        return nearstPoint
    }

//    fun slideDone() {
//        val list = ClusterUtil.splitSortedAndCluster(visitedPoints.toMutableList(), 80)
//        addSortDotList(list)
//        pathGenerateAndMeasure()
//    }

    fun slideAnywhere(event: MotionEvent) {
        if (slidePath.isEmpty) {
            lastPointX = event.x
            lastPointY = event.y
            slidePath.moveTo(lastPointX, lastPointY)
            visitedPoints.add(Point(lastPointX.toInt(), lastPointY.toInt()))
        } else {
            when (event.actionMasked) {
                MotionEvent.ACTION_MOVE -> {
                    val dx = abs(event.x - lastPointX)
                    val dy = abs(event.y - lastPointY)
                    if (dx >= VALID_MIN_DISTANCE || dy >= VALID_MIN_DISTANCE) {
                        slideQuadToPath(event)
                    }
                }

                else -> {
                    slideQuadToPath(event)
                }
            }
        }
    }

    private fun slideQuadToPath(event: MotionEvent) {
        slidePath.quadTo(
            lastPointX,
            lastPointY,
            (event.x + lastPointX) / 2,
            (event.y + lastPointY) / 2
        )
        lastPointX = event.x
        lastPointY = event.y
        visitedPoints.add(Point(lastPointX.toInt(), lastPointY.toInt()))
        invalidate()
    }


    /**
     * 检查是否选中了某个分段
     */
    private fun checkSelectSegment(event: MotionEvent) {
        if (pathList.isEmpty()) return
        val x = event.x
        val y = event.y
        val pos = FloatArray(2)
        var minDistance = Float.MAX_VALUE
        var clickPathIndex = -1
        val pointSize = 200 / pathList.size
        for (pathInfo in pathList.withIndex()) {
            pathMeasure.setPath(pathInfo.value, false)
            val length = pathMeasure.length / 20
            var min = Float.MAX_VALUE
            for (i in 0..pointSize) {
                pathMeasure.getPosTan(length * i, pos, null)
                val distance = distance(pos[0].toInt(), pos[1].toInt(), x.toInt(), y.toInt())
                if (distance < min) {
                    min = distance
                }
            }
            if (min < minDistance) {
                minDistance = min
                clickPathIndex = pathInfo.index
            }
        }
        //如果最小距离大于设定的最小距离，则认为是无效点击
        if (minDistance > VALID_MIN_DISTANCE) {
            return
        }
        if (curMode == MODE_COLOR) {
            //如果已经包含了这一段，则移除，否则添加
            if (selectedSegmentList.contains(clickPathIndex)) {
                selectedSegmentList.remove(clickPathIndex)
            } else if (clickPathIndex != -1) {
                selectedSegmentList.add(clickPathIndex)
            }
        } else if (curMode == MODE_GRAFFITI) {
            selectedSegmentColorList[clickPathIndex] = if (currentSegmentColor != 0) {
                currentSegmentColor
            } else {
                underColor
            }
        }
        selectChangeListener?.invoke(selectedSegmentList.size == segmentCount)
        invalidate()
    }

    /**
     * 检查电源位置
     */
    private fun checkSelectPowerSource(event: MotionEvent) {
        if (sortList.isEmpty()) return
        val distanceFirst =
            distance(event.x.toInt(), event.y.toInt(), sortList.first().x, sortList.first().y)
        val distanceLast =
            distance(event.x.toInt(), event.y.toInt(), sortList.last().x, sortList.last().y)
        if (distanceFirst == distanceLast) {
            return
        }
        if (distanceFirst < distanceLast && distanceFirst < dp2px(21)) {
            powerSourceIndex = 0
        } else if (distanceLast < distanceFirst && distanceLast < dp2px(21)) {
            sortList.reverse()
            powerSourceIndex = 0
        }
        powerSourceSelectListener?.invoke(powerSourceIndex != -1)
        invalidate()
    }

    private fun checkSelectPoint(event: MotionEvent) {
        centerPointX = event.x.toInt()
        centerPointY = event.y.toInt()
        centerPointX = if (centerPointX < 0) {
            0
        } else if (centerPointX > width) {
            width
        } else centerPointX
        centerPointY = if (centerPointY < 0) {
            0
        } else if (centerPointY > height) {
            height
        } else centerPointY
        invalidate()
    }

    private fun changeOptionLinkTrackStack() {
        if (visitedPoints.size > 0 && (linkTrackUndoStack.isEmpty() || linkTrackUndoStack.peek().size != visitedPoints.size)) {
            val list = mutableListOf<Point>()
            list.addAll(visitedPoints)
            linkTrackUndoStack.push(list)
            linkTrackRedoStack.clear()
            linkTrackUndoRedoListener?.invoke(canUndoTrack(), canRedoTrack())
        }
    }

    private fun changeOptionGraffitiStack(isTouchUp: Boolean = false) {
        val map = mutableMapOf<Int, Int>()
        selectedSegmentColorList.forEachIndexed { index, color ->
            if (color != 0 && color != underColor) {
                map[index] = color
            }
        }
        if (graffitiUndoStack.isEmpty()) {
            graffitiUndoStack.add(map)
            graffitiRedoStack.clear()
            if (isTouchUp) {
                graffitiChangeListener?.invoke()
            }
        } else {
            val peekMap = graffitiUndoStack.last()
            if (peekMap.size != map.size) {
                checkGraffitiUndoStep()
                graffitiUndoStack.add(map)
                graffitiRedoStack.clear()
                if (isTouchUp) {
                    graffitiChangeListener?.invoke()
                }
            } else {
                //判断两个map是否相同，如果相同则不入栈
                var isSame = true
                for (info in peekMap) {
                    if (map[info.key] != info.value) {
                        isSame = false
                        break
                    }
                }
                if (!isSame) {
                    checkGraffitiUndoStep()
                    graffitiUndoStack.add(map)
                    graffitiRedoStack.clear()
                    if (isTouchUp) {
                        graffitiChangeListener?.invoke()
                    }
                }
            }
        }
        graffitiUndoRedoListener?.invoke(canUndoGraffiti(), canRedoGraffiti())
    }

    private fun checkGraffitiUndoStep() {
        if (graffitiUndoStack.size > MAX_STEP) {
            graffitiUndoStack.remove()
        }
    }

    private fun checkGraffitiRedoStep() {
        if (graffitiRedoStack.size >= MAX_STEP) {
            graffitiRedoStack.remove()
        }
    }

    /**
     * 缩放和平移
     */
    private fun setScaleAndPivot(event: MotionEvent) {
        val x1 = event.getX(0)
        val x2 = event.getX(1)
        val y1 = event.getY(0)
        val y2 = event.getY(1)
        val changeX1 = (x1 - downX1).toDouble()
        val changeX2 = (x2 - downX2).toDouble()
        val changeY1 = (y1 - downY1).toDouble()
        val changeY2 = (y2 - downY2).toDouble()
        if (scaleX > 1) { //滑动
            val lessX = (changeX1 / 2 + changeX2 / 2).toFloat()
            val lessY = (changeY1 / 2 + changeY2 / 2).toFloat()
            setSelfPivot(-lessX, -lessY)
        }
        moveDist = moveDistance(event)
        val space = moveDist - oldDist
        val scale = (scaleX + space / width).toFloat()
        setScale(scale)
        isZoom = scale == 1.0F
    }

    /**
     * 触摸使用的移动事件
     */
    private fun setSelfPivot(lessX: Float, lessY: Float) {
        var setPivotX = pivotX + lessX
        var setPivotY = pivotY + lessY
        if (setPivotX < 0) {
            setPivotX = 0F
        } else if (setPivotX > width) {
            setPivotX = width.toFloat()
        }
        if (setPivotY < 0) {
            setPivotY = 0F
        } else if (setPivotY > width) {
            setPivotY = width.toFloat()
        }
        setPivot(setPivotX, setPivotY)
    }


    /**
     * 平移画面，当画面的宽或高大于屏幕宽高时，调用此方法进行平移
     */
    private fun setPivot(x: Float, y: Float) {
        pivotX = x
        pivotY = y
    }


    /**
     * 设置放大缩小
     */
    private fun setScale(scale: Float) {
        val validScale = if (scale > SCALE_MAX) {
            SCALE_MAX
        } else if (scale < SCALE_MIN) {
            SCALE_MIN
        } else {
            scale
        }
        scaleX = validScale
        scaleY = validScale
    }

    /**
     * 双指移动的距离
     */
    private fun moveDistance(event: MotionEvent): Double {
        return if (event.pointerCount == 2) {
            val x = event.getX(0) - event.getX(1)
            val y = event.getY(0) - event.getY(1)
            sqrt((x * x + y * y).toDouble())
        } else 0.0
    }

    private fun distance(x1: Int, y1: Int, x2: Int, y2: Int): Float {
        return sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1).toDouble()).toFloat()
    }

    fun hadSelect() = selectedSegmentList.size != 0
}