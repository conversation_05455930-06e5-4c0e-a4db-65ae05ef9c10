package com.govee.base2light.view.h70b1

import android.content.Context
import com.govee.base2home.color.PaletteDialogNew
import com.govee.base2light.ble.music.AbsNewMusicEffect
import com.govee.base2light.ble.music.h70b1.H70B1FanKuaiMusic
import com.govee.base2light.light.v1.AbsNewMusicFragment.SubMusicMode
import com.govee.base2light.view.AbsMultiMusicEditDialogV1
import com.govee.ui.dialog.ItemChooseDialog

/**
 * @package：com.govee.base2light.view.h70b1
 * @time：2023/9/14 18:37
 * @author：zhangchenxiang
 * @des：翻块音乐模式dialog
 */
class H70B1FanKuai4Dialog(
    context: Context,
    sku: String,
    device: String,
    sensitivity: Int,
    icNum: Int,
    musicMode: SubMusicMode,
    listener: SensitivityChangeListener
) : AbsMultiMusicEditDialogV1<H70B1FanKuaiMusic>(
    context,
    sku,
    device,
    sensitivity,
    icNum,
    musicMode,
    listener
) {
    override fun getLayout(): Int {
        return com.govee.base2light.R.layout.b2light_dialog_edit_multi_music_fankuai_h70b1
    }

    override fun hide() {
        ItemChooseDialog.hideDialog()
        PaletteDialogNew.hideDialog()
        super.hide()
    }

    override fun paramsUi() {

    }

    override fun makeNewEffect(
        sku: String?,
        device: String?,
        musicCode: Int,
        icNum: Int
    ): H70B1FanKuaiMusic {
        var music = AbsNewMusicEffect.read4NewMultiMusic(
            sku, device, musicCode,
            H70B1FanKuaiMusic::class.java
        )
        if (AbsNewMusicEffect.isNewMusicEffectInValid(music)) {
            music = H70B1FanKuaiMusic()
            music.makeDefParams(icNum)
            saveNewMultiMusic(music)
        }
        return music!!
    }

    companion object {
        @JvmStatic
        fun showDialog(
            context: Context,
            sku: String,
            device: String,
            sensitivity: Int,
            icNum: Int,
            musicMode: SubMusicMode,
            sensitivityChangeListener: SensitivityChangeListener
        ) {
            H70B1FanKuai4Dialog(
                context,
                sku,
                device,
                sensitivity,
                icNum,
                musicMode,
                sensitivityChangeListener
            ).setEventKey(
                EVENT_KEY_DEF
            ).show()
        }
    }
}