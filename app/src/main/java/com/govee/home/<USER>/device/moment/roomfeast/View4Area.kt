package com.govee.home.main.device.moment.roomfeast

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Matrix
import android.graphics.Paint
import android.graphics.Point
import android.graphics.RectF
import android.util.AttributeSet
import android.util.Log
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.View
import com.govee.base2light.pact.feast.FeastDetailResponse
import com.govee.base2light.pact.feast.SubDevice
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil
import kotlin.math.max
import kotlin.math.min

/**
 *Create by hey on 2024/7/30
 *$分配区域画布自定义view
 * 从服务器拿的坐标左上角是0,0 发给设备左下角是0，0
 */
class View4Area @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {
    private val TAG = "View4Area"

    //操作接口
    var iArea: IArea? = null

    //背景色
    private val bgColor = ResUtil.getColor(com.govee.ui.R.color.ui_color_block_style_65_color)
    private val bgPaint = Paint().apply {
        color = bgColor
        style = Paint.Style.FILL
    }
    private val bgStokeColor =
        ResUtil.getColor(com.govee.ui.R.color.ui_color_block_style_65_stroke_color)
    private val bgStrokePaint = Paint().apply {
        color = bgStokeColor
        strokeWidth = dpToPx(0.5f)
        style = Paint.Style.STROKE
    }

    //画布色
    private val canvasColor = ResUtil.getColor(com.govee.ui.R.color.ui_color_block_style_66_color)
    private val canvasPaint = Paint().apply {
        color = canvasColor
        style = Paint.Style.FILL
    }
    private val canvasStokeColor =
        ResUtil.getColor(com.govee.ui.R.color.ui_color_block_style_66_stroke_color)
    private val canvasStrokePaint = Paint().apply {
        color = canvasStokeColor
        strokeWidth = dpToPx(0.5f)
        style = Paint.Style.STROKE
    }

    //绘制画布的矩形
    private val canvasRectF = RectF()

    private val midLineH = ResUtil.getBitmap(com.govee.ui.R.mipmap.area_pics_center_line_heng)
    private val midLineHRectF = RectF()
    private val midLineV = ResUtil.getBitmap(com.govee.ui.R.mipmap.area_pics_center_line_shu)
    private val midLineVRectF = RectF()

    //单位长度 分成120份 0-119
    private var singleLen = dpToPx(351f) / 120f

    private val singleRectF = RectF() //单位矩形 每个线性设备由多个单位矩形拼成
    private var singleLightBitmap: Bitmap? = null //线的单元图
    private var planarBitmap: Bitmap? = null //面状设备图
    private var iceBitmap: Bitmap? = null //冰帘设备图
    private var pointBitmap: Bitmap? = null //点状设备图
    private var device3dBitmap: Bitmap? = null //3d设备图
    private var lightingBitmap: Bitmap? = null //闪电图标
    private val lightingRectF = RectF() //电源位置

    //缩放矩阵
    private val canvasMatrix = Matrix()

    // 缩放逆矩阵
    private val inverseMatrix = Matrix()

    //缩放系数
    private var scaleFactor = 1.5f
        set(value) {
            field = value.coerceIn(MIN_SCALE_FACTOR, MAX_SCALE_FACTOR)
        }

    private val scaleDetector = ScaleGestureDetector(context, ScaleListener())
    private var isScaling = false //是否双指缩放
    private var isTouching = false //是否单指触摸


    //当前操作的设备
    private var curOpDevice: SubDevice? = null
        set(value) {
            field = value
            iArea?.curOpDevice(value)
        }

    //滑动过程处理原始坐标
    private val scaleRect = RectF()

    //画布上的所有设备 下标0是主设备
    private var deviceList = mutableListOf<SubDevice>()

    //画布上所有设备的坐标 缩放为1
    private var devicePosList = mutableListOf<RectF>() //记的都是水平方向的坐标 旋转过的灯坐标不变

    //画布上所有设备缩放后的坐标
    private var deviceScalePosList = mutableListOf<RectF>() //记的都是水平方向的坐标 旋转过的灯坐标不变

    //发给嵌入式的设备坐标
    private val send2DevicePosList = mutableListOf<List<Point>>() //每个设备两个点

    //选中框
    private val choosePaint = Paint().apply {
        style = Paint.Style.STROKE
        color = ResUtil.getColor(com.govee.ui.R.color.ui_rect_style_2_stroke)
        strokeWidth = dpToPx(1f)
    }
    private val chooseRectF = RectF()

    private val textPaint = Paint().apply {
        color = ResUtil.getColor(com.govee.ui.R.color.font_style_57_3_textColor)
        textSize = dpToPx(12f)
        textAlign = Paint.Align.CENTER
    }

    private val connectRectF = RectF() //连接图标
    private var connectFailedBitmap: Bitmap? = null //连接失败
    private var connectSuccessBitmap: Bitmap? = null //连接成功

    //处理旋转后的坐标
    private val rotateRect = RectF()

    //旋转矩阵
    private val rotateMatrix = Matrix()

    private var translateX = 0f
    private var translateY = 0f

    private var lastTouchX = 0f
    private var lastTouchY = 0f

    private var isDragging = true //是否正在拖动画布


    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        // 绘制固定背景
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), bgPaint)
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), bgStrokePaint)
        //缩放画布的坐标
        canvasRectF.left = 0f
        canvasRectF.top = 0f
        canvasRectF.right = width.toFloat()
        canvasRectF.bottom = height.toFloat()
        canvasMatrix.mapRect(canvasRectF)
        //绘制画布
        canvas.drawRect(
            canvasRectF, canvasPaint
        )
        canvas.drawRect(
            canvasRectF, canvasStrokePaint
        )
        //中间线
        drawCenterLine(canvas)
        //画设备
        deviceList.forEachIndexed { index, subDevice ->
            drawDevice(canvas, subDevice, index)
        }
    }

    /**
     * 画中间线
     */
    private fun drawCenterLine(canvas: Canvas) {
        if (curOpDevice == null || curOpIndex == -1) return
        val rectF = devicePosList[curOpIndex]
        val x = (rectF.centerX() * 120 / width).toInt()
        val y = (rectF.centerY() * 120 / height).toInt()
        if (y == 60 && midLineH != null) {
            midLineHRectF.left = canvasRectF.left
            midLineHRectF.top = canvasRectF.centerY() - dpToPx(7f / 2) * scaleFactor
            midLineHRectF.right = canvasRectF.right
            midLineHRectF.bottom = canvasRectF.centerY() + dpToPx(7f / 2) * scaleFactor
            canvas.drawBitmap(midLineH, null, midLineHRectF, null)
        }
        if (x == 60 && midLineV != null) {
            midLineVRectF.left = canvasRectF.centerX() - dpToPx(7f / 2) * scaleFactor
            midLineVRectF.top = canvasRectF.top
            midLineVRectF.right = canvasRectF.centerX() + dpToPx(7f / 2) * scaleFactor
            midLineVRectF.bottom = canvasRectF.bottom
            canvas.drawBitmap(midLineV, null, midLineVRectF, null)
        }
    }

    /**
     * 绘制单个设备
     * 用的坐标是不旋转和翻转的坐标 旋转和翻转通过转画布实现
     */
    private fun drawDevice(canvas: Canvas, device: SubDevice, index: Int) {
        val deviceRectF = deviceScalePosList[index]
        val degree = device.subDevice.positionInfo.rotate.toFloat()
        val reverse = device.subDevice.positionInfo.reverse
        canvas.save()
        //旋转画布
        canvas.rotate(degree, deviceRectF.centerX(), deviceRectF.centerY())
        if (reverse == 1) {
            //翻转画布
            canvas.scale(-1f, 1f, deviceRectF.centerX(), 0f)
        }
        //缩放后的单位长度
        val scaleSingleLen = scaleFactor * singleLen
        //是否是线性灯
        val isLine = device.subDevice.isLineDevice
        if (lightingBitmap == null) {
            lightingBitmap =
                ResUtil.getBitmap(com.govee.ui.R.mipmap.h61d3_icon_zaoxing_dianyuan_press)
        }
        val bitmap = getDeviceBitmap(device)
        //画设备
        bitmap?.let {
            if (!isLine) {
                //拼接灯，别的直接画
                if (device.subDevice.isPlanarDevice || device.subDevice.isIceDevice) {
                    val num = device.subDevice.num
                    //每一面的宽度
                    val singleWith = deviceRectF.width() / num
                    singleRectF.top = deviceRectF.top
                    singleRectF.bottom = deviceRectF.bottom
                    var left = deviceRectF.left
                    for (i in 0 until num) {
                        singleRectF.left = left
                        singleRectF.right = left + singleWith
                        left = singleRectF.right
                        canvas.drawBitmap(it, null, singleRectF, null)
                    }
                    //画电源图标
                    if (device.subDevice.isPlanarDevice) {
                        val lightingSize = (10.5 * singleWith / 82).toFloat()
                        lightingRectF.left = deviceRectF.left - lightingSize / 2
                        lightingRectF.top = deviceRectF.bottom - lightingSize
                        lightingRectF.right = deviceRectF.left + lightingSize / 2
                        lightingRectF.bottom = deviceRectF.bottom
                        lightingBitmap?.let { lighting ->
                            canvas.drawBitmap(lighting, null, lightingRectF, null)
                        }
                    } else if (device.subDevice.isIceDevice) {
                        val lightingSize = (10.5 * singleWith / 50).toFloat()
                        lightingRectF.left = deviceRectF.left - lightingSize
                        lightingRectF.top = deviceRectF.top
                        lightingRectF.right = deviceRectF.left
                        lightingRectF.bottom = lightingRectF.top + lightingSize
                        lightingBitmap?.let { lighting ->
                            canvas.drawBitmap(lighting, null, lightingRectF, null)
                        }
                    }
                } else if (device.subDevice.isSymmetricIceDevice) {
                    val num = device.subDevice.num
                    //每一面的宽度（一面包括左右的对称两面）
                    val singleWith = deviceRectF.width() / num
                    val lightingSize = singleWith / 8
                    singleRectF.top = deviceRectF.top
                    singleRectF.bottom = deviceRectF.bottom
                    var left = deviceRectF.left
                    for (i in 0 until num) {
                        //矩形平分为2分 先画左边一面
                        singleRectF.left = left
                        singleRectF.right = left + singleWith / 2
                        canvas.drawBitmap(it, null, singleRectF, null)
                        val id = canvas.save()
                        //翻转画布 画另外一面
                        canvas.scale(-1f, 1f, deviceRectF.centerX(), 0f)
                        canvas.drawBitmap(it, null, singleRectF, null)
                        canvas.restoreToCount(id)
                        left = singleRectF.right + singleWith / 2
                    }
                    //画电源图标
                    lightingRectF.left = deviceRectF.centerX() - lightingSize / 2
                    lightingRectF.top = deviceRectF.top
                    lightingRectF.right = deviceRectF.centerX() + lightingSize / 2
                    lightingRectF.bottom = deviceRectF.top + lightingSize
                    lightingBitmap?.let { lighting ->
                        canvas.drawBitmap(lighting, null, lightingRectF, null)
                    }
                } else {
                    //其他直接画
                    canvas.drawBitmap(it, null, deviceRectF, null)
                }
                //画电源
//                val draw = setLightRectFPos(device, deviceRectF)
//                if (draw) {
//                    lightingBitmap?.let { lighting ->
//                        canvas.drawBitmap(lighting, null, lightingRectF, null)
//                    }
//                }
                if (device == curOpDevice) {
                    //画选框
                    setChooseRectFPos()
                    canvas.drawRect(chooseRectF, choosePaint)
                }
            } else {
                //线用拼的方式
                val scale = scaleSingleLen / 17
                val singleH = 15 * scale //单个ic高度
                //画ic
                singleRectF.top = deviceRectF.centerY() - singleH / 2
                singleRectF.bottom = deviceRectF.centerY() + singleH / 2
                var left = deviceRectF.left
                //每个设备由多个单元矩形图拼出来
                for (i in 0 until device.subDevice.num) {
                    singleRectF.left = left
                    val right = singleRectF.left + scaleSingleLen
                    singleRectF.right = right
                    canvas.drawBitmap(it, null, singleRectF, null)
                    left = right
                }
                //画电源图标
                val lightingSize = 10.5 * scale
                lightingRectF.top = deviceRectF.centerY() - lightingSize.toFloat() / 2
                lightingRectF.bottom = deviceRectF.centerY() + lightingSize.toFloat() / 2
                lightingRectF.right = deviceRectF.left
                lightingRectF.left = lightingRectF.right - lightingSize.toFloat()
                lightingBitmap?.let { lighting ->
                    canvas.drawBitmap(lighting, null, lightingRectF, null)
                }
                if (device == curOpDevice) {
                    //画选框
                    chooseRectF.left = lightingRectF.left
                    chooseRectF.right = deviceRectF.right
                    chooseRectF.top = deviceRectF.top
                    chooseRectF.bottom = deviceRectF.bottom
                    canvas.drawRect(chooseRectF, choosePaint)
                }
            }
        }
        canvas.restore()
        //画设备名和连接状态
        if (device == curOpDevice) {
            drawNameConnectStatus(canvas, index)
        }
    }

    /**
     * 画设备名和连接状态
     */
    private fun drawNameConnectStatus(canvas: Canvas, deviceIndex: Int) {
        val device = deviceList[deviceIndex]
        val devicePos = devicePosList[deviceIndex]
        canvas.save()
        canvas.concat(canvasMatrix)
        rotateMatrix.setRotate(
            device.subDevice.positionInfo.rotate.toFloat(), devicePos.centerX(), devicePos.centerY()
        )
        rotateRect.left = devicePos.left
        rotateRect.right = devicePos.right
        rotateRect.top = devicePos.top
        rotateRect.bottom = devicePos.bottom
        rotateMatrix.mapRect(rotateRect)
        //画文本
        var text = device.subDevice.deviceName
        if (text.length > 10) {
            text = "${text.substring(0, 10)}..."
        }
        val fontMetrics = textPaint.fontMetrics
        val distance = (fontMetrics.bottom - fontMetrics.top) / 2 - fontMetrics.bottom
        val margin = dpToPx(2 + 16.5f / 2)
        val centerY = rotateRect.bottom + margin + distance // inReal(textH / 2) + distance,
        canvas.drawText(text, rotateRect.centerX(), centerY, textPaint)
        //画图标
        val textWith = textPaint.measureText(text)
        val size = dpToPx(14f)
        connectRectF.left = rotateRect.centerX() + textWith / 2
        connectRectF.top = rotateRect.bottom + dpToPx(3f)
        connectRectF.right = connectRectF.left + size
        connectRectF.bottom = connectRectF.top + size
        if (device.status == 2) {
            if (connectSuccessBitmap == null) {
                connectSuccessBitmap =
                    ResUtil.getBitmap(com.govee.ui.R.mipmap.new_home_icon_bluetooth_connect)
            }
            canvas.drawBitmap(connectSuccessBitmap!!, null, connectRectF, null)
        } else {
            if (connectFailedBitmap == null) {
                connectFailedBitmap =
                    ResUtil.getBitmap(com.govee.ui.R.mipmap.new_control_light_icon_mini_loose)
            }
            canvas.drawBitmap(connectFailedBitmap!!, null, connectRectF, null)
        }
        canvas.restore()
    }


    /**
     * 设置除线性灯外的灯选框位置
     */
    private fun setChooseRectFPos() {
        if (curOpDevice == null || curOpIndex == -1) return
        val rectF = devicePosList[curOpIndex]
        if (curOpDevice!!.subDevice.isPlanarDevice) {
            val marginLeft = dpToPx(6f)
            val marginRight = dpToPx(0.5f)
            chooseRectF.left = rectF.left - marginLeft
            chooseRectF.top = rectF.top
            chooseRectF.right = rectF.right + marginRight
            chooseRectF.bottom = rectF.bottom
        } else if (curOpDevice!!.subDevice.isIceDevice) {
            val marginLeft = dpToPx(11.5f)
            val margin = dpToPx(1f)
            chooseRectF.left = rectF.left - marginLeft
            chooseRectF.top = rectF.top - margin
            chooseRectF.right = rectF.right + margin
            chooseRectF.bottom = rectF.bottom + margin
        } else {
            chooseRectF.left = rectF.left
            chooseRectF.top = rectF.top
            chooseRectF.right = rectF.right
            chooseRectF.bottom = rectF.bottom
        }
        canvasMatrix.mapRect(chooseRectF)
    }

    private fun getDeviceBitmap(device: SubDevice): Bitmap? {
        val res = if (device.subDevice.isLineDevice) {
            if (singleLightBitmap == null) {
                singleLightBitmap = ResUtil.getBitmap(com.govee.ui.R.mipmap.area_pics_device_xian)
            }
            singleLightBitmap
        } else if (device.subDevice.isPlanarDevice) {
            if (planarBitmap == null) {
                planarBitmap =
                    ResUtil.getBitmap(com.govee.ui.R.mipmap.area_pics_device_mian_chuangliandeng)
            }
            planarBitmap
        } else if (device.subDevice.is3DDevice) {
            if (device3dBitmap == null) {
                device3dBitmap = ResUtil.getBitmap(com.govee.ui.R.mipmap.area_pics_device_liti)
            }
            device3dBitmap
        } else if (device.subDevice.isIceDevice || device.subDevice.isSymmetricIceDevice) {
            if (iceBitmap == null) {
                iceBitmap =
                    ResUtil.getBitmap(com.govee.ui.R.mipmap.area_pics_device_xian_bingliandeng)
            }
            iceBitmap
        } else if (device.subDevice.isPointDevice) {
            if (pointBitmap == null) {
                pointBitmap = ResUtil.getBitmap(com.govee.ui.R.mipmap.area_pics_device_dian)
            }
            pointBitmap
        } else {
            null
        }
        return res
    }

    override fun onTouchEvent(event: MotionEvent): Boolean {
        parent.requestDisallowInterceptTouchEvent(true)
        SafeLog.i(TAG) { "onTouchEvent() event.action = ${event.action} isScaling = $isScaling" }
        scaleDetector.onTouchEvent(event)
        if ((event.action == MotionEvent.ACTION_UP || event.action == MotionEvent.ACTION_CANCEL) && isScaling) {
            isScaling = false
            return true
        }
        if (isScaling) return true
        handleOneFingerEvent(event)
        return true
    }


    private var distanceLeft = 0f
    private var distanceRight = 0f
    private var distanceTop = 0f
    private var distanceBottom = 0f
    private var curOpIndex = -1
    private val downRectF = RectF() //设备缩放且旋转后的坐标

    //设备的旋转矩阵 用来求设备旋转后的坐标
    private val deviceRotateMatrix = Matrix()

    private var hadChooseDevice = false

    /**
     * 处理单手指事件
     */
    private fun handleOneFingerEvent(event: MotionEvent): Boolean {
        SafeLog.i(TAG) { "handleOneFingerEvent() e.type = ${event.action} e.x = ${event.x} e.y = ${event.y}" }
        when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                Log.i(TAG, "handleOneFingerEvent() ACTION_DOWN")
                isTouching = true
                lastTouchX = event.x
                lastTouchY = event.y
//                deviceScalePosList.forEachIndexed { index, rectF ->
                for (index in deviceScalePosList.size - 1 downTo 0) {
                    val rectF = deviceScalePosList[index]
                    downRectF.left = rectF.left
                    downRectF.top = rectF.top
                    downRectF.right = rectF.right
                    downRectF.bottom = rectF.bottom
                    val rotate = deviceList[index].subDevice.positionInfo.rotate
                    var hadRotate = false
                    if ((rotate / 90) % 2 != 0) {
                        //设备旋转过的情况 获取设备旋转后的坐标来判断有没有选中设备
                        deviceRotateMatrix.reset()
                        deviceRotateMatrix.setRotate(
                            rotate.toFloat(), rectF.centerX(), rectF.centerY()
                        )
                        deviceRotateMatrix.mapRect(downRectF)
                        hadRotate = true
                    }
                    //线性灯的高度太小了，很难选中，这里做一下判定区域放大
                    var enlargeLineDevice = deviceList[index].subDevice.isLineDevice
                    if (enlargeLineDevice) {
                        enlargeLineDevice = if (hadRotate) {
                            //把水平方向范围放大到和竖直方向一样
                            var factor = (max(downRectF.top, downRectF.bottom) - min(
                                downRectF.top, downRectF.bottom
                            )) / 2
                            //太长也不太合适 暂时限制30
                            factor = min(dpToPx(30f), factor)
                            downRectF.top <= event.y && downRectF.bottom >= event.y && downRectF.centerX() - factor <= event.x && downRectF.centerX() + factor >= event.x
                        } else {
                            //把竖直方向范围放大到和水平方向一样
                            var factor = (max(downRectF.right, downRectF.left) - min(
                                downRectF.right, downRectF.left
                            )) / 2
                            factor = min(dpToPx(30f), factor)
                            downRectF.left <= event.x && downRectF.right >= event.x && downRectF.centerY() - factor <= event.y && downRectF.centerY() + factor >= event.y
                        }
                    }
                    //找到操作的设备
                    if (enlargeLineDevice || downRectF.left <= event.x && downRectF.right >= event.x && downRectF.top <= event.y && downRectF.bottom >= event.y) {
                        curOpIndex = index
                        curOpDevice = deviceList[index]
                        distanceLeft = event.x - downRectF.left
                        distanceRight = downRectF.right - event.x
                        distanceTop = event.y - downRectF.top
                        distanceBottom = downRectF.bottom - event.y
                        isDragging = false
                        invalidate()
                        return true
                    }
                }
                //没找到操作的设备 去掉上次选中的设备
                curOpIndex = -1
                curOpDevice = null
                isDragging = scaleFactor > 1
            }

            MotionEvent.ACTION_MOVE -> {
                if (isScaling || !isTouching) return true
                Log.i(TAG, "handleOneFingerEvent() ACTION_MOVE curOpIndex = $curOpIndex ")
                if (isDragging) {
                    val dx = event.x - lastTouchX
                    val dy = event.y - lastTouchY
                    translateX += dx
                    translateY += dy
                    lastTouchX = event.x
                    lastTouchY = event.y
                    constrainCanvas()
                    return true
                }

                if (curOpIndex != -1) {
                    checkMoveDevicePosition(event.x, event.y)
                    setOriginalRectPosition(curOpIndex)
                }
            }

            MotionEvent.ACTION_UP -> {
                Log.i(TAG, "handleOneFingerEvent() ACTION_UP")
                isTouching = false
                distanceLeft = 0f
                distanceRight = 0f
                distanceTop = 0f
                distanceBottom = 0f
                hadChooseDevice = false
                isDragging = false
                applyNewPosition()
            }

            MotionEvent.ACTION_CANCEL -> {
                Log.i(TAG, "handleOneFingerEvent() ACTION_CANCEL")
                isTouching = false
                distanceLeft = 0f
                distanceRight = 0f
                distanceTop = 0f
                distanceBottom = 0f
                hadChooseDevice = false
                isDragging = false
                applyNewPosition()
            }
        }
        invalidate()
        return true
    }

    //设备的旋转逆矩阵 根据旋转后的坐标求出旋转前的坐标
    private val deviceRotateInvertMatrix = Matrix()

    /**
     * 校验手指移动设备过程位置参数
     */
    private fun checkMoveDevicePosition(x: Float, y: Float) {
        curOpDevice ?: return
        val deviceScaleRectF = deviceScalePosList[curOpIndex]
        val isLineDevice = curOpDevice!!.subDevice.isLineDevice
        val isPointDevice = curOpDevice!!.subDevice.isPointDevice
        val rotate = curOpDevice!!.subDevice.positionInfo.rotate
        val hadRotate = (rotate / 90) % 2 != 0
        SafeLog.i(TAG) { "checkMoveDevicePosition() sku = ${curOpDevice!!.subDevice.sku} hadRotate = $hadRotate" }
        val with = deviceScaleRectF.width()
        val height = deviceScaleRectF.height()
        //产品修改了需求 从1/3改到了中心点在画布内
        val criticalLen = with / 2 //水平能超出画布外的最大长度
        var checkX = x
        var checkY = y
        //线状灯
        if (isLineDevice) {
            //旋转成了竖直方向
            if (hadRotate) {
                //约束旋转后的矩阵坐标
                //水平方向 矩形中心点得在画布内
                checkX = max(checkX, canvasRectF.left - height / 2 + distanceLeft)
                checkX = min(checkX, canvasRectF.right + height / 2 - distanceRight)
                downRectF.left = checkX - distanceLeft
                downRectF.right = checkX + distanceRight
                //竖直方向 上下最多超过画布1/3
                checkY = max(checkY, canvasRectF.top - criticalLen + distanceTop)
                checkY = min(checkY, canvasRectF.bottom + criticalLen - distanceBottom)
                downRectF.top = checkY - distanceTop
                downRectF.bottom = checkY + distanceBottom

                //根据旋转后的矩阵坐标 算出旋转前的矩阵坐标
                deviceRotateInvertMatrix.reset()
                deviceRotateInvertMatrix.setRotate(
                    -rotate.toFloat(), downRectF.centerX(), downRectF.centerY()
                )
                deviceRotateInvertMatrix.mapRect(downRectF)
                deviceScalePosList[curOpIndex].left = downRectF.left
                deviceScalePosList[curOpIndex].top = downRectF.top
                deviceScalePosList[curOpIndex].right = downRectF.right
                deviceScalePosList[curOpIndex].bottom = downRectF.bottom
            } else {
                //水平方向 左右最多超出画布1/3
                checkX = max(checkX, canvasRectF.left - criticalLen + distanceLeft)
                checkX = min(checkX, canvasRectF.right + criticalLen - distanceRight)
                deviceScalePosList[curOpIndex].left = checkX - distanceLeft
                deviceScalePosList[curOpIndex].right = checkX + distanceRight
                //竖直方向上 矩形中心点得在画布内
                checkY = max(checkY, canvasRectF.top - height / 2 + distanceTop)
                checkY = min(checkY, canvasRectF.bottom + height / 2 - distanceBottom)
                deviceScalePosList[curOpIndex].top = checkY - distanceTop
                deviceScalePosList[curOpIndex].bottom = checkY + distanceBottom
            }
        } else if (isPointDevice) {
            val criticalW = with / 2
            val criticalH = height / 2
            //点状灯 左右最多超出1/2 确保中心点一定在画布上
            checkX = max(checkX, canvasRectF.left - criticalW + distanceLeft)
            checkX = min(checkX, canvasRectF.right + criticalW - distanceRight)
            deviceScalePosList[curOpIndex].left = checkX - distanceLeft
            deviceScalePosList[curOpIndex].right = checkX + distanceRight
            //竖直方向上 矩形中心点得在画布内
            checkY = max(checkY, canvasRectF.top - criticalH + distanceTop)
            checkY = min(checkY, canvasRectF.bottom + criticalH - distanceBottom)
            deviceScalePosList[curOpIndex].top = checkY - distanceTop
            deviceScalePosList[curOpIndex].bottom = checkY + distanceBottom
        } else {
            //面状灯或者3d灯
            //水平方向 左右最多超出画布1/2
            checkX = max(checkX, canvasRectF.left - criticalLen + distanceLeft)
            checkX = min(checkX, canvasRectF.right + criticalLen - distanceRight)
            deviceScalePosList[curOpIndex].left = checkX - distanceLeft
            deviceScalePosList[curOpIndex].right = checkX + distanceRight
            //竖直方向上 上下最多超出画布1/2
            val yCriticalLen = height / 2
            checkY = max(checkY, canvasRectF.top - yCriticalLen + distanceTop)
            checkY = min(checkY, canvasRectF.bottom + yCriticalLen - distanceBottom)
            deviceScalePosList[curOpIndex].top = checkY - distanceTop
            deviceScalePosList[curOpIndex].bottom = checkY + distanceBottom
        }
    }

    /**
     * 根据缩放坐标计算原始坐标
     */
    private fun setOriginalRectPosition(index: Int) {
        val originalScaleRect = deviceScalePosList[index]
        scaleRect.left = originalScaleRect.left
        scaleRect.top = originalScaleRect.top
        scaleRect.right = originalScaleRect.right
        scaleRect.bottom = originalScaleRect.bottom

        // 使用逆矩阵对进行变换
        inverseMatrix.mapRect(scaleRect)

        //赋值给原始坐标
        val originalRect = devicePosList[index]
        originalRect.left = scaleRect.left
        originalRect.top = scaleRect.top
        originalRect.right = scaleRect.right
        originalRect.bottom = scaleRect.bottom
    }

    //处理缩放手势
    private inner class ScaleListener : ScaleGestureDetector.SimpleOnScaleGestureListener() {
        override fun onScaleBegin(detector: ScaleGestureDetector): Boolean {
            SafeLog.i(TAG) { "onScaleBegin() " }
            isScaling = true
            return true
        }

        override fun onScale(detector: ScaleGestureDetector): Boolean {
            SafeLog.i(TAG) { "onScale() " }
            return handlerScaleEvent(detector)
        }

        override fun onScaleEnd(detector: ScaleGestureDetector) {
            SafeLog.i(TAG) { "onScaleEnd() " }
//            isScaling = false
        }
    }

    /**
     * 处理缩放手势
     */
    private fun handlerScaleEvent(detector: ScaleGestureDetector): Boolean {
        scaleFactor *= detector.scaleFactor
        scaleFactor = scaleFactor.coerceIn(MIN_SCALE_FACTOR, MAX_SCALE_FACTOR)
        Log.i(TAG, "handlerScaleEvent() scaleFactor = $scaleFactor")
        constrainCanvas()
        return true
    }

    private fun dpToPx(dp: Float): Float {
        return dp * context.resources.displayMetrics.density + 0.5f
    }


    private fun constrainCanvas() {
        //缩放矩阵
        canvasMatrix.reset()
        canvasMatrix.postScale(scaleFactor, scaleFactor, width / 2f, height / 2f)
        //偏移矩阵
        if (scaleFactor <= 1) {
            translateX = 0f
            translateY = 0f
        } else {
            // 计算允许的最大和最小平移值
            canvasRectF.left = 0f
            canvasRectF.top = 0f
            canvasRectF.right = width.toFloat()
            canvasRectF.bottom = height.toFloat()
            canvasMatrix.mapRect(canvasRectF)
            val maxTranslateX = -canvasRectF.left
            val minTranslateX = -maxTranslateX
            val maxTranslateY = -canvasRectF.top
            val minTranslateY = -maxTranslateY
            translateX = min(translateX, maxTranslateX)
            translateX = max(translateX, minTranslateX)
            translateY = min(translateY, maxTranslateY)
            translateY = max(translateY, minTranslateY)
            SafeLog.i(TAG) { "constrainCanvas() maxTranslateX = $maxTranslateX minTranslateX = $minTranslateX maxTranslateY = $maxTranslateY minTranslateY = $minTranslateY translateX = $translateX translateY = $translateY" }
        }
        canvasMatrix.postTranslate(translateX, translateY)
        //计算反向矩阵
        inverseMatrix.reset()
        canvasMatrix.invert(inverseMatrix)
        //所有设备经过矩阵运算后的坐标
        deviceScalePosList.forEachIndexed { index, rectF ->
            rectF.left = devicePosList[index].left
            rectF.top = devicePosList[index].top
            rectF.right = devicePosList[index].right
            rectF.bottom = devicePosList[index].bottom
            canvasMatrix.mapRect(rectF)
        }
        invalidate()
    }

//    /**
//     * 点击左右翻转
//     */
//    fun clickFlip() {
//        curOpDevice ?: return
//        val curReverse = curOpDevice!!.subDevice.positionInfo.reverse
//        curOpDevice!!.subDevice.positionInfo.reverse = !curReverse
//        invalidate()
//    }
//
//    /**
//     * 点击旋转90度
//     */
//    fun clickRotate() {
//        curOpDevice ?: return
//        val curDegree = curOpDevice!!.subDevice.positionInfo.rotate
//        val nextDegree = if (curDegree == 360) 90 else curDegree + 90
//        curOpDevice!!.subDevice.positionInfo.rotate = nextDegree
//        invalidate()
//    }


    /**
     * 添加设备
     */
    fun addDevice(subDevice: SubDevice) {
        deviceList.add(subDevice)
        addNewDevicePos(subDevice)
        curOpDevice = subDevice
        curOpIndex = deviceList.size - 1
        invalidate()
    }

    /**
     * 移除设备
     */
    fun removeDevice() {
        SafeLog.i(TAG) { "removeDevice() " }
        curOpDevice ?: return
        if (deviceList.size <= 1) return //主设备不可移除
        if (!deviceList.contains(curOpDevice)) {
            SafeLog.i(TAG) { "removeDevice() curOpDevice不在设备列表里" }
            return
        }
        val pos = deviceList.indexOf(curOpDevice)
        deviceList.removeAt(pos)
        devicePosList.removeAt(pos)
        deviceScalePosList.removeAt(pos)
        send2DevicePosList.removeAt(pos)
        curOpDevice = null
        curOpIndex = -1
        invalidate()
    }


    /**
     * 设置所有设备
     */
    fun setAllDevice(subDevices: List<SubDevice>) {
        curOpDevice = null
        curOpIndex = -1
        deviceList.clear()
        devicePosList.clear()
        deviceScalePosList.clear()
        send2DevicePosList.clear()
        deviceList.addAll(subDevices)
        subDevices.forEach {
            addNewDevicePos(it)
        }
        constrainCanvas()
    }

    /**
     * 初始化缩放比例 计算偏移位置
     */
    fun initScale() {
        constrainCanvas()
        translateX = canvasRectF.width() / 2
        translateY = canvasRectF.height() / 2
    }

    /**
     * 添加新设备后 计算设备位置坐标并保存
     */
    private fun addNewDevicePos(device: SubDevice) {
        val rectF = RectF()
        val positionInfo = device.subDevice.positionInfo
        //x y是在第几个格子，乘singleLen算出设备所在矩形的中心点坐标
        val x = positionInfo.positionX * singleLen
        val y = positionInfo.positionY * singleLen
        //ic比例 一个ic宽在ui图上是17dp 实际在坐标上是singleLen
        if (device.subDevice.isLineDevice) {
            //线状灯根据ic缩放
            val singleIcScale = singleLen / 17f
            val rectWidth = singleLen * device.subDevice.num//单位长度* 长度
            val rectHeight = 72 * singleIcScale
            rectF.left = (x - rectWidth / 2f)
            rectF.top = y - rectHeight / 2
            rectF.right = (x + rectWidth / 2f)
            rectF.bottom = y + rectHeight / 2
        } else {
            val num = device.subDevice.num
            var picW = 0
            var picH = 0
            if (device.subDevice.is3DDevice) {
                //立体
                picW = 60 //ui上图的宽是60
                picH = 72 //ui上图的宽是60
                if (picW != 0 && picH != 0) {
                    rectF.left = x - dpToPx(picW / 2f)
                    rectF.top = y - dpToPx(picH / 2f)
                    rectF.right = x + dpToPx(picW / 2f)
                    rectF.bottom = y + dpToPx(picH / 2f)
                }
            } else if (device.subDevice.isPlanarDevice) {
                //窗帘 20*26ic 横向拼接
                val singleRectWidth = singleLen * device.subDevice.planarDeviceSingleWidth
                val singleRectHeight = singleLen * device.subDevice.height
                val allWith = singleRectWidth * num
                rectF.left = (x - allWith / 2f)
                rectF.top = y - singleRectHeight / 2
                rectF.right = (x + allWith / 2f)
                rectF.bottom = y + singleRectHeight / 2
//                picW = 82 * num //ui上图的宽是82
//                picH = 72 //ui上图的宽是72
            } else if (device.subDevice.isPointDevice) {
                //点状
                picW = 32 //ui上图的宽是82
                picH = 22 //ui上图的宽是72
                if (picW != 0 && picH != 0) {
                    rectF.left = x - dpToPx(picW / 2f)
                    rectF.top = y - dpToPx(picH / 2f)
                    rectF.right = x + dpToPx(picW / 2f)
                    rectF.bottom = y + dpToPx(picH / 2f)
                }
            } else if (device.subDevice.isIceDevice) {
                //冰帘 3+5+7+5(水平有4个ic)
                val singleRectWidth = singleLen * 4
                val singleRectHeight = singleLen * 7
                val allWith = singleRectWidth * num
                rectF.left = (x - allWith / 2f)
                rectF.top = y - singleRectHeight / 2
                rectF.right = (x + allWith / 2f)
                rectF.bottom = y + singleRectHeight / 2
//                picW = 50 * num //ui上图的宽是50
//                picH = 72 //ui上图的宽是72
            } else if (device.subDevice.isSymmetricIceDevice) {
                //对称的冰帘灯 一边是3+5+7+5(水平有4个ic)
                val singleRectWidth = singleLen * 4 * 2 //一个单位有两边 所以x2
                val singleRectHeight = singleLen * 7
                val allWith = singleRectWidth * num
                rectF.left = (x - allWith / 2f)
                rectF.top = y - singleRectHeight / 2
                rectF.right = (x + allWith / 2f)
                rectF.bottom = y + singleRectHeight / 2
            }
        }
        devicePosList.add(rectF)
        //设备在缩放画布上的坐标
        val scaleRectF = RectF(rectF)
        canvasMatrix.mapRect(scaleRectF)
        deviceScalePosList.add(scaleRectF)
        //发给嵌入式的坐标
        val devicePoint = mutableListOf<Point>()
        //每个设备两个点
        devicePoint.add(Point())
        devicePoint.add(Point())
        send2DevicePosList.add(devicePoint)

    }

    /**
     * 修改位置后发送
     */
    private fun applyNewPosition() {
        if (curOpDevice != null && curOpIndex != -1) {
            val rectF = devicePosList[curOpIndex]
            val x = (rectF.centerX() * 120 / width).toInt()
            val y = (rectF.centerY() * 120 / height).toInt()
            SafeLog.i(TAG) { "applyNewPosition() curDevice = ${curOpDevice!!.subDevice.sku} x = $x y = $y" }
            val curPosition =
                curOpDevice!!.subDevice.positionInfo ?: FeastDetailResponse.Data.PositionInfo()
            curPosition.positionX = x
            curPosition.positionY = y
            curOpDevice!!.subDevice.positionInfo = curPosition
            iArea?.apply()
        }
    }

    fun resetScale() {
        scaleFactor = 1.5f
        constrainCanvas()
    }

    companion object {
        private const val MIN_SCALE_FACTOR = 0.5f //最小值
        private const val MAX_SCALE_FACTOR = 3.0f //最大值
    }
}

interface IArea {

    /**
     * 当前操作的设备
     */
    fun curOpDevice(subDevice: SubDevice?)

    /**
     * 应用当前效果
     */
    fun apply()
}