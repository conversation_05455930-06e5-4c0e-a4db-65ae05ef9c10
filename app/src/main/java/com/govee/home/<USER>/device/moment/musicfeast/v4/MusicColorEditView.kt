package com.govee.home.main.device.moment.musicfeast.v4

import android.content.Context
import android.graphics.Rect
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.GradientDrawable
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.BaseMultiItemQuickAdapter
import com.chad.library.adapter.base.entity.MultiItemEntity
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.google.android.flexbox.FlexDirection
import com.google.android.flexbox.FlexWrap
import com.google.android.flexbox.FlexboxLayoutManager
import com.google.android.flexbox.JustifyContent
import com.govee.base2home.color.IColorChoose
import com.govee.base2home.color.PaletteDialogNew
import com.govee.base2home.color.getIPalette
import com.govee.base2home.custom.DragSortRecyclerView
import com.govee.base2kt.ext.dp4Int
import com.govee.base2kt.ext.invisible
import com.govee.base2kt.ext.loadCircleImage
import com.govee.base2kt.ext.onItemChildClickTrigger
import com.govee.base2kt.ext.visible
import com.govee.base2kt.utils.ColorUtils
import com.govee.base2kt.utils.ColorUtils.Companion.toColor
import com.govee.home.databinding.AppViewMusicNewColorEditBinding
import com.govee.ui.R
import com.ihoment.base2app.skinv2.SkinM
import com.ihoment.base2app.util.ResUtil

/**
 *     author  : sinrow
 *     time    : 2024/5/10
 *     version : 1.0.0
 *     desc    :
 */
class MusicColorEditView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs) {
    private val mDataBinding: AppViewMusicNewColorEditBinding? = DataBindingUtil.inflate(
        LayoutInflater.from(context),
        com.govee.home.R.layout.app_view_music_new_color_edit,
        this,
        true
    )

    private val colorModels by lazy { (mutableListOf<ColorModel>()) }
    private val adapter by lazy { DiyNewColorAdapter() }
    private val tempColor = mutableListOf<Int>()// 用于排序颜色时，判断是否有变更


    var sku: String = ""
    var showTransparent = false
    var showSmartColor = true
    var maxColorNum = 0
    var colorChangeListener: (() -> Unit)? = null
    var needColorApplyBtn = false
    var onApplyListener: () -> Unit = {}

    init {
        initViews()
    }

    private fun initViews() {
        mDataBinding?.run {

            recyclerColor.adapter = adapter
            adapter.setNewInstance(colorModels)
            adapter.setList(colorModels)

            recyclerColor.setQuickAdapter(adapter)
            recyclerColor.isEnableDrag = true
            recyclerColor.setItemType(1)
            recyclerColor.isNestedScrollingEnabled = false
            recyclerColor.isFocusable = false
            recyclerColor.isFocusableInTouchMode = false
            recyclerColor.layoutManager = FlexboxLayoutManager(context).apply {
                flexWrap = FlexWrap.WRAP
                flexDirection = FlexDirection.ROW
                justifyContent = JustifyContent.FLEX_START
            }
            recyclerColor.getItemDecoration(20.dp4Int, 10.dp4Int)

            recyclerColor.setOnDragListener(object : DragSortRecyclerView.OnDragListener {
                override fun onStart() {
                    tempColor.clear()
                    for (colorModel in colorModels) {
                        tempColor.add(colorModel.color)
                    }
                }

                override fun onStop() {
                    if (!recyclerColor.isComputingLayout) {
                        changeColor()
                    }
                }
            })

            adapter.onItemChildClickTrigger { _, view, position ->
                val colorModel = colorModels[position]
                when (view.id) {
                    com.govee.base2light.R.id.ivDelete -> {
                        toDeleteColor(position)
                    }

                    com.govee.base2light.R.id.ivColor -> {
                        val add = colorModel.isAdd()
                        if (add) {
                            toAddNewColor(position)
                        } else {
                            toEditColor(position)
                        }
                    }

                    com.govee.base2light.R.id.tv_apply -> {
                        removeApplyBtn()
                        onApplyListener.invoke()
                    }
                }
            }
        }
    }

    fun RecyclerView.getItemDecoration(
        marginHorizon: Int,
        marginVertical: Int
    ): RecyclerView.ItemDecoration {
        return object : RecyclerView.ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
            ) {
                outRect.left = marginHorizon / 2
                outRect.right = marginHorizon / 2
                outRect.top = marginVertical / 2
                outRect.bottom = marginVertical / 2
            }
        }.also {
            addItemDecoration(it)
        }
    }

    private fun changeColor() {
        if (!isChangeColor(colorModels)) return
        colorChangeListener?.invoke()
        adapter.notifyDataSetChanged()
    }

    /**
     * 顺序是否变更了
     */
    private fun isChangeColor(colorModels: MutableList<ColorModel>): Boolean {
        if (tempColor.size != colorModels.size) return true
        colorModels.forEachIndexed { index, colorModel ->
            if (tempColor[index] != colorModel.color) {
                return true
            }
        }
        return false
    }

    fun removeApplyBtn() {
        if (colorModels.contains(colorApply)) {
            colorModels.remove(colorApply)
            adapter.notifyItemChanged(colorModels.size - 1)
        }
    }


    private fun toEditColor(pos: Int) {
        val size: Int = colorModels.size
        if (pos < 0 || pos >= size) return
        val colorModel: ColorModel = colorModels[pos]
        if (colorModel.isShowingColor()) {
            val iColorChoose = object : IColorChoose {
                override fun isSupportChooseMultiColors(): Boolean {
                    return false
                }

                override fun chooseColor(vararg newColors: Int) {
                    colorModel.color = newColors[0]
                    if (!colorModels.contains(colorApply)) {
                        colorModels.add(colorApply)
                        adapter.notifyItemChanged(colorModels.size - 1)
                    }
                    adapter.notifyItemChanged(pos)
                    notifyValueChange()
                }

                override fun chooseColorRealTime(color: Int, isFirstDown: Boolean) {
                    //空
                }

            }
            val iPalette = getIPalette(
                sku = sku, isSupportNoColor = showTransparent, iColorChoose = iColorChoose,
                iColorStripe = null, iColorTemp = null, needShowMyColor = true
            )
            PaletteDialogNew.createDialog(context, iPalette, colorModel.color).show()
        }
    }

    private val colorApply = ColorModel.makeApply()

    private fun toDeleteColor(pos: Int) {
        val size: Int = colorModels.size
        if (pos < 0 || pos >= size) return
        colorModels.remove(colorApply)
        colorModels.removeAt(pos)
        if (colorModels.isEmpty()) {
            colorModels.add(ColorModel.makeAdd())
        } else {
            val sizeNow: Int = colorModels.size
            val lastColorModel: ColorModel = colorModels[sizeNow - 1]
            if (lastColorModel.isShowingColor() && adapter.needAddColorShowing(sizeNow)) {
                colorModels.add(ColorModel.makeAdd())
            }
        }
        colorModels.add(colorApply)
        adapter.notifyDataSetChanged()
        val curColors: IntArray = getColors()
        if (curColors != null) {
            mDataBinding?.recyclerColor?.setEnableDragNum(curColors.size)
        }
        notifyValueChange()
    }

    fun getColors(): IntArray {
        return colorModels.filter { it.itemType == ColorModel.type_color }.map { it.color }
            .toIntArray()
    }

    private fun notifyValueChange() {
        colorChangeListener?.invoke()
    }

    private fun toAddNewColor(pos: Int) {
        val size: Int = colorModels.size
        if (pos < 0 || pos >= size) return
        val colorModel: ColorModel = colorModels[pos]
        if (colorModel.isShowingColor()) return
        val iColorChoose = object : IColorChoose {
            override fun isSupportChooseMultiColors(): Boolean {
                return true
            }

            override fun chooseColor(vararg newColors: Int) {
                colorModel.toShowingColor(newColors[0])
                colorModels.remove(colorApply)
                val length = newColors.size
                var curSize = colorModels.size
                if (length > 1) {
                    for (i in 1 until length) {
                        val newColor = newColors[i]
                        if (adapter.needAddColorShowing(curSize)) {
                            colorModels.add(ColorModel.makeColor(newColor))
                        }
                        curSize++
                    }
                }
//            if (curSize >= 2) {
//                checkColorDragHint()
//            }
                if (adapter.needAddColorShowing(curSize)) {
                    colorModels.add(ColorModel.makeAdd())
                }
                colorModels.add(colorApply)
                adapter.notifyDataSetChanged()
                val curColors: IntArray = getColors()
                if (curColors != null) {
                    mDataBinding?.recyclerColor?.setEnableDragNum(curColors.size)
                }
                notifyValueChange()
            }

            override fun chooseColorRealTime(color: Int, isFirstDown: Boolean) {
                //空
            }

        }
        val iPalette =
            getIPalette(sku, null, showTransparent, iColorChoose, null, null, true)
        PaletteDialogNew.createDialog(context, iPalette, colorModel.color).show()
    }

    fun updateColor(
        minColorNum: Int,
        maxColorNum: Int,
        colors: MutableList<Int>?,
        notify: Boolean
    ) {
        this.maxColorNum = maxColorNum
        /*更新颜色数量限制*/
        adapter.setColorRange(minColorNum, maxColorNum)
        colorModels.clear()
        var needAddColorModel = true
        if (!colors.isNullOrEmpty()) {
            for (color in colors) {
                //不支持无色时，剔除无色
                if (!showTransparent && color == toColor(1, 1, 1)) continue
                val colorModel = ColorModel.makeColor(color)
                colorModels.add(colorModel)
                /*若已达到最大支持颜色数值，则不再添加*/if (colorModels.size == maxColorNum) break
            }
            needAddColorModel = colorModels.size < maxColorNum
        }
        if (needAddColorModel) {
            colorModels.add(ColorModel.makeAdd())
        }
        colorModels.add(colorApply)
        adapter.notifyDataSetChanged()
        val curColors = getColors()
        if (curColors != null) {
            mDataBinding?.recyclerColor?.setEnableDragNum(curColors.size)
        }
        if (notify) {
            notifyValueChange()
        }
    }

    class DiyNewColorAdapter :
        BaseMultiItemQuickAdapter<ColorModel, BaseViewHolder>() {

        init {
            addChildClickViewIds(
                com.govee.base2light.R.id.ivColor,
                com.govee.base2light.R.id.ivDelete,
                com.govee.base2light.R.id.tv_apply
            )
            addItemType(
                ColorModel.type_color,
                com.govee.base2light.R.layout.b2light_item_diy_new_item_color
            )
            addItemType(
                ColorModel.type_add,
                com.govee.base2light.R.layout.b2light_item_diy_new_item_color
            )
            addItemType(
                ColorModel.type_apply,
                com.govee.home.R.layout.app_item_music_color_new_item_apply_btn
            )
        }

        private var min = 0
        private var max = 8

        fun setColorRange(min: Int, max: Int) {
            this.min = min
            this.max = max
        }

        fun needAddColorShowing(allSize: Int): Boolean {
            return max > allSize
        }

        override fun convert(holder: BaseViewHolder, item: ColorModel) {
            when (item.itemType) {
                ColorModel.type_color, ColorModel.type_add -> {
                    val ivColor = holder.getView<ImageView>(com.govee.base2light.R.id.ivColor)
                    val ivDelete = holder.getView<ImageView>(com.govee.base2light.R.id.ivDelete)
                    ResUtil.setImageResource(ivDelete, R.mipmap.shengyan_btn_delete)
                    val isAdd = item.itemType == ColorModel.type_add
                    if (isAdd) {
                        ivColor.setImageDrawable(ResUtil.getDrawable(item.color))
                    } else {
                        ivColor.loadCircleImage(
                            context, if (ColorUtils.isNoColor(item.color)) {
                                ResUtil.getDrawable(R.mipmap.color_icon_un)
                                    ?: ColorDrawable(item.color)
                            } else {
                                if (ColorUtils.isNearWhiteColor(item.color) && SkinM.isLightMode()) {
                                    ResUtil.getDrawable(R.drawable.b2light_circle_solid_border_gray) as GradientDrawable
                                } else {
                                    ColorDrawable(item.color)
                                }
                            }
                        )

                    }
                    if (!noDelete() && !isAdd) {
                        ivDelete.visible()
                    } else {
                        ivDelete.invisible()
                    }
                }

                ColorModel.type_apply -> {

                }
            }

        }

        private fun noDelete(): Boolean {
            return if (min > 0) {
                data.count { it.itemType != ColorModel.type_add && it.itemType != ColorModel.type_apply } <= min /*最少支持颜色数量内都不支持删除*/
            } else false
            /*默认支持删除*/
        }
    }

    class ColorModel(
        override var itemType: Int,
        var color: Int
    ) : MultiItemEntity {
        fun isAdd(): Boolean {
            return itemType == type_add
        }

        fun isShowingColor(): Boolean {
            return itemType == type_color
        }

        fun toShowingColor(color: Int) {
            itemType = type_color
            this.color = color
        }

        companion object {

            const val type_color = 1
            const val type_add = 2
            const val type_apply = 3
            fun makeColor(color: Int): ColorModel {
                return ColorModel(type_color, color)
            }

            fun makeAdd(): ColorModel {
                return ColorModel(type_add, R.mipmap.h70b3_btn_color_add)
            }

            fun makeApply(): ColorModel {
                return ColorModel(type_apply, 0)
            }
        }
    }
}