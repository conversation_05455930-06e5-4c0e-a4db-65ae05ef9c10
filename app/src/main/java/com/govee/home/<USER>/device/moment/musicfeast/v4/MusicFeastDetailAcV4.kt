package com.govee.home.main.device.moment.musicfeast.v4

import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.core.widget.NestedScrollView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.listener.OnItemDragListener
import com.govee.base2home.BaseRPNetActivity
import com.govee.base2home.BindingAdapter
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.community.feedback.FbAcJump
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.theme.ThemeM
import com.govee.base2home.update.download.CheckVersion
import com.govee.base2home.util.CaughtRunnable
import com.govee.base2home.util.StrUtil
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.dp4Int
import com.govee.base2kt.ext.gone
import com.govee.base2kt.ext.visible
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2kt.ext.visibleOrInvisible
import com.govee.base2light.ac.update.OtaUpdateAcV3
import com.govee.base2light.ac.update.OtaUpdateAcV4
import com.govee.base2light.kt.ble.Compose4DefWrite4Multi.Companion.makeWriteController4MultiV2
import com.govee.base2light.pact.BleInfo
import com.govee.base2light.pact.feast.FeastDetailResponse.Data.CenterDevice
import com.govee.base2light.pact.feast.SubDevice
import com.govee.base2light.pact.feast.feastsnapshot.SnapshotListResp
import com.govee.ble.BleController
import com.govee.eco.helper.EcoHelper
import com.govee.home.databinding.AppAcMusicFeastDetailV4Binding
import com.govee.home.feast.AcFeastList
import com.govee.home.feast.EcoFeastFragment
import com.govee.home.main.device.moment.Constant
import com.govee.home.main.device.moment.moviefeast.ChooseSubDeviceAc
import com.govee.home.main.device.moment.musicfeast.aimusic.AIMusicVM.Companion.EVENT_KEY
import com.govee.home.main.device.moment.musicfeast.MusicFeastHelper
import com.govee.home.main.device.moment.musicfeast.Vm4MusicFeastDetailAcShare
import com.govee.home.main.device.moment.musicfeast.devicesetting.Ac4FeastDeviceSetting
import com.govee.home.main.device.moment.musicfeast.event.Event2SetSubDevice
import com.govee.home.main.device.moment.musicfeast.h1167.VMBleBoxDetail
import com.govee.home.main.device.moment.musicfeast.h1168.DeviceInfo
import com.govee.home.main.device.moment.musicfeast.v2.FeastMaker
import com.govee.home.main.device.moment.musicfeast.v2.FeastMusicMode
import com.govee.home.main.device.moment.musicfeast.v2.IFeastApplyListener
import com.govee.home.main.device.moment.musicfeast.v2.MusicFeastM
import com.govee.home.main.device.moment.musicfeast.v2.SubDeviceAdapter
import com.govee.home.main.device.moment.musicfeast.v2.ViewModel4Feast
import com.govee.home.main.device.moment.snapshot.DelegateSnapshot
import com.govee.home.main.device.moment.snapshot.EventApplySnapshotResult
import com.govee.home.main.device.moment.snapshot.FeastType
import com.govee.home.main.device.moment.snapshot.IApplyListener
import com.govee.home.main.device.moment.snapshot.MusicFeastV4SnapshotDelegate
import com.govee.home.main.device.moment.snapshot.SnapshotRequireViewModel
import com.govee.mvvm.network.NetworkUtil
import com.govee.ui.Cons
import com.govee.ui.R
import com.govee.ui.component.ConnectUIV1
import com.govee.ui.component.ConnectUIV1.EventClickConnectV1
import com.govee.ui.dialog.BleUpdateHintDialog
import com.govee.ui.dialog.ConfirmDialog
import com.govee.ui.dialog.HintDialog1
import com.govee.ui.dialog.NameEditDialog
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.skinv2.SkinM.isLightMode
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.StatusBarUtil
import com.liveeventbus.LiveEventBus
import com.zhy.android.percent.support.PercentRelativeLayout
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Create by hey on 2021/9/29
 * $ 音乐盛宴详情页
 * 代码重写了一版，自测完还没经过测试验证 从v2拷贝的 UI换新
 */
@Suppress("OVERRIDE_DEPRECATION")
class MusicFeastDetailAcV4 : BaseRPNetActivity(), IApplyListener {
    private var binding: AppAcMusicFeastDetailV4Binding? = null
    private val viewModel4Feast by lazy { ViewModelProvider(this)[ViewModel4Feast::class.java] }
    private lateinit var connectUI: ConnectUIV1 //蓝牙连接ui
    private lateinit var adapter: SubDeviceAdapter //子设备适配器

    private var gId: Int = 0
    private var into4Add: Boolean = false
    private var need2ConnectBle: Boolean = true
    private var musicSupportAi: Boolean = true

    private val vm4BoxDetail by lazy { ViewModelProvider(this)[VMBleBoxDetail::class.java] }
    private val snapshotDelegate by lazy { MusicFeastV4SnapshotDelegate(viewModel4Feast, this) }
    private var snapshot: DelegateSnapshot? = null

    companion object {
        @JvmStatic
        fun jump2MusicFeastDetailAcV4(context: Context?, bundle: Bundle?) {
            JumpUtil.jumpWithBundle(context, MusicFeastDetailAcV4::class.java, bundle)
        }
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.home.R.id.ac_container
    }

    override fun getLayout(): Int {
        return com.govee.home.R.layout.app_ac_music_feast_detail_v4
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        /*适配挖孔屏*/
        adaptationInsetTop(com.govee.home.R.id.top_flag, AppUtil.getScreenWidth() * 61 / 750)
        /*解析参数*/
        parseParams(intent)
        initBinding()
        initConnectUI()
        initSubDeviceRecycleView()
        initMusicUI()
        initViewModel()

        //创建监听
        EcoHelper.observerFirstShowCardCreate(this)
    }

    private fun initSnapshot(centerDevice: CenterDevice) {
        binding?.musicUi?.getSnapshotContainer().let {
            val requireViewModel =
                SnapshotRequireViewModel(FeastType.MUSIC, centerDevice, snapshotDelegate)
            snapshot =
                DelegateSnapshot(this, it as ViewGroup, requireViewModel).apply {
                    bind()
                }
        }
    }

    private fun updateSnapshotUI(open: Boolean, centerDevice: CenterDevice) {
        if (open) {
            if (snapshot == null) {
                initSnapshot(centerDevice)
            }
            snapshot?.visible()
        } else {
            snapshot?.invisible()
        }
    }


    private fun initViewModel() {
        /*注册回调监听*/
        viewModel4Feast.run {
            /*请求盛宴接口结果*/
            requestFeastResult.observe(this@MusicFeastDetailAcV4) {
                when (it) {
                    ViewModel4Feast.REQUEST_RESULT_SUC -> {
                        binding?.run {
                            netErrorContainer.beHide()
                        }
                        SafeLog.i(TAG) { "requestFeastResult() REQUEST_RESULT_SUC-接口请求成功-连接主设备" }
                        viewModel4Feast.connectBle(
                            this@MusicFeastDetailAcV4, into4Add, need2ConnectBle
                        )
                        snapshotDelegate.feastId = viewModel4Feast.feast.value?.feastId ?: 0
                        val centerDevice = viewModel4Feast.feast.value?.centerDevice
                        snapshotDelegate.centerDevice = centerDevice
                        initOtaInfo()
                        initShareFeastVM()
                        StatusBarUtil.statusBarTranslucentMode(this@MusicFeastDetailAcV4)
                        setTitleViewVisible(centerDevice)
                    }

                    ViewModel4Feast.REQUEST_RESULT_FAIL -> {
                        binding?.run {
                            connectUI.hide()
                            netErrorContainer.beFail()
                            hideTitleView()
                        }
                    }

                    ViewModel4Feast.REQUEST_RESULT_EFFECTS_EMPTY -> {
                        binding?.run {
                            connectUI.hide()
                            netErrorContainer.beFail()
                            netErrorContainer.setFailDesText(ResUtil.getString(R.string.app_feast_music_effect_list_empty_hint))
                        }
                    }

                    ViewModel4Feast.REQUEST_RESULT_NO_FOUND -> {
                        binding?.run {
                            connectUI.hide()
                            netErrorContainer.showEmptyUi4FixSize(
                                R.mipmap.new_h5_pics_post_no,
                                R.string.app_content_no_found
                            )
                            hideTitleView()
                        }
                    }

                    ViewModel4Feast.REQUEST_RESULT_ING -> {
                        binding?.run {
                            connectUI.hide()
                            netErrorContainer.beLoading()
                        }
                    }
                }
                showBackBtStyleByErrorStatus(it != ViewModel4Feast.REQUEST_RESULT_SUC)
            }
            /*盛宴信息*/
            feast.observeForever {
                /*更新ai曲风动作关联*/
                FeastMaker.makeQuFengActionMap(it.aiActions, it.aiActionColors)
                val bleConnected = it.bleConnected()
                LogInfra.Log.e(TAG, "initViewModel() bleConnected = $bleConnected")
                binding?.run {
                    /*盛宴名称*/
                    tvFeastName.text = it.feastName
                    /*盛宴开关*/
                    val feastInOpen = it.feastOpen && bleConnected
                    ivSwitch.isSelected = feastInOpen
                    ivSwitch.isEnabled = bleConnected
                    //头图
                    ResUtil.setImageResource(
                        ivTopBg,
                        if (feastInOpen)
                            R.mipmap.control_pics_top_music_feast_on
                        else R.mipmap.control_pics_top_music_feast_off
                    )
                    /*主设备信息*/
                    it.centerDevice?.run {
                        updateSnapshotUI(feastInOpen, this)
                        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H6020 && deviceSplicingStatus == 1) {
                            ivDeviceIcon.setImageResource(R.mipmap.add_list_type_device_6020_zuhe)
                        } else {
                            BindingAdapter.setSkuImageResource(
                                ivDeviceIcon, this.goodsType, this.sku, ""
                            )
                        }
                        tvDeviceName.text = this.deviceName
                    }
                    /*蓝牙图标和连接文案*/
                    BindingAdapter.updateImageViewBleStatus(ivConnectState, bleConnected)
                    BindingAdapter.updateTextViewBleStatus(tvConnectState, bleConnected)
                    /*电量*/
                    batteryStatus.visibleByBoolean(bleConnected && it.showBatteryIcon())
                    batteryStatus.setImageDrawable(ResUtil.getDrawable(it.batteryResId))
                    /*已连接模块*/
                    subDeviceFucContainer.visibleByBoolean(bleConnected)
                    /*音乐ui*/
                    flMusicUi.visibleByBoolean(feastInOpen)
                    /*连接Ui*/
                    when (it.bleStatus) {
                        ViewModel4Feast.BLE_STATUS_DEF, ViewModel4Feast.BLE_STATUS_CONNECTING -> {
                            connectUI.show()
                            connectUI.updateConnectUi(Cons.connect_ui_v1_status_ing)
                        }

                        ViewModel4Feast.BLE_STATUS_CONNECTED -> {
                            connectUI.hide()
                            vm4BoxDetail.readSoftInfo(viewModel4Feast.feast.value?.centerDevice?.goodsType)
                        }

                        ViewModel4Feast.BLE_STATUS_CONNECT_FAIL -> {
                            connectUI.show()
                            connectUI.updateConnectUi(Cons.connect_ui_v1_status_fail)
                        }
                    }
                    /*关闭文案*/
                    val feastInClose = bleConnected && !it.feastOpen
                    tvClose.visibleByBoolean(feastInClose)
                    //子设备列表
                    subDeviceListUi()
                }
                hideLoading()
            }
            /*子设备列表*/
            subDevices.observe(this@MusicFeastDetailAcV4) {
                val notEmpty = it.isNotEmpty()
                SafeLog.i(TAG) { "initViewModel() subDevices notEmpty = $notEmpty" }
                binding?.run {
                    adapter.setList(it)
                    recyclerDevice.visibleByBoolean(notEmpty)
                    ivEditSubDevice.visibleByBoolean(notEmpty)
                    deviceEmptyGroup.visibleByBoolean(it.isEmpty())
                    val empty = viewModel4Feast.subDevices.value?.isEmpty() != false
                    (tvClose.layoutParams as ConstraintLayout.LayoutParams).topToBottom =
                        if (empty) com.govee.home.R.id.bg4DeviceEmpty else com.govee.home.R.id.bgDevice
                    subDeviceListUi()
                }
                hideLoading()
            }
            /*亮度更新*/
            feastBrightness.observe(this@MusicFeastDetailAcV4) {
                SafeLog.i(TAG) { "initViewModel() feastBrightness-->" }
                binding?.run {
                    musicUi.brightnessUi(it)
                }
                hideLoading()
            }
            musicEffectList.observe(this@MusicFeastDetailAcV4) {
                SafeLog.i(TAG) { "initViewModel() get 光影秀、嘉年华数据" }
                binding?.musicUi?.initMusicModeList(it.first, it.second)
            }
            /*音乐模式更新*/
            feastMusicMode.observe(this@MusicFeastDetailAcV4) {
                SafeLog.i(TAG) { "initViewModel() feastMusicMode-->" }
                binding?.run {
                    musicUi.musicModeUi(it)
                }
                hideLoading()
            }
            /*删除盛宴接口结果*/
            request4FeastDeleteResult.observe(this@MusicFeastDetailAcV4) {
                when (it) {
                    ViewModel4Feast.REQUEST_RESULT_SUC -> {
                        hideLoading()
                        toast(R.string.b2light_set_switch_success)
                        LiveEventBus.get<Boolean>(AcFeastList.feast_data_change).post(true)
                        doOnDestroy()
                    }

                    ViewModel4Feast.REQUEST_RESULT_FAIL -> {
                        hideLoading()
                        toast(R.string.base2home_diy_crop_img_retry)
                    }

                    ViewModel4Feast.REQUEST_RESULT_ING -> {
                        showLoading()
                    }
                }
            }
            /*编辑盛宴名称接口结果*/
            request4FeastNameResult.observe(this@MusicFeastDetailAcV4) {
                when (it) {
                    ViewModel4Feast.REQUEST_RESULT_SUC -> {
                        NameEditDialog.hideDialog()
                        hideLoading()
                        LiveEventBus.get<Boolean>(EcoFeastFragment.feast_data_change).post(true)
                    }

                    ViewModel4Feast.REQUEST_RESULT_ING -> {
                        showLoading()
                    }

                    ViewModel4Feast.REQUEST_RESULT_FAIL -> {
                        hideLoading()
                    }
                }
            }
        }
        /*关联viewModel*/
        binding?.run {
            musicUi.registerViewModel(viewModel4Feast)
        }
        /*请求盛宴信息*/
        val mark = if (musicSupportAi) Constant.music_feast_ai else Constant.music_feast
        viewModel4Feast.requestFeast(this, gId, mark)
        /**关闭盛宴详情页*/
        LiveEventBus.get<Boolean>(Constant.EVENT_CLOSE_FEAST_DETAIL_PAGE)
            .observe(this) {
                SafeLog.i(TAG) { "关闭盛宴详情页()" }
                doOnDestroy()
            }
    }

    private fun hideTitleView() {
        binding?.run {
            ivHelp.visibleOrInvisible(false)
            helpBg.visibleOrInvisible(false)
            delete.visibleOrInvisible(false)
            deleteBg.visibleOrInvisible(false)
            setting.visibleOrInvisible(false)
            settingBg.visibleByBoolean(false)
        }
    }

    private fun setTitleViewVisible(centerDevice: CenterDevice?) {
        binding?.run {
            ivHelp.visibleOrInvisible(true)
            helpBg.visibleOrInvisible(true)
            if (GoodsType.GOODS_TYPE_VALUE_BLE_AI_MUSIC_BOX != centerDevice?.goodsType) {
                delete.visibleOrInvisible(true)
                deleteBg.visibleOrInvisible(true)
                setting.visibleOrInvisible(false)
                settingBg.visibleByBoolean(false)
            } else {
                delete.visibleOrInvisible(false)
                deleteBg.visibleOrInvisible(false)
                setting.visibleOrInvisible(true)
                settingBg.visibleByBoolean(true)
            }
        }
    }

    private fun initShareFeastVM() {
        val centerDevice = viewModel4Feast.feast.value?.centerDevice
        Vm4MusicFeastDetailAcShare.createShareVm4NewDetail(
            centerDevice?.sku.orEmpty(),
            centerDevice?.device.orEmpty(),
            this@MusicFeastDetailAcV4,
            true
        ).apply {
            this?.initViewModel4Feast(viewModel4Feast)
        }
    }

    private fun subDeviceListUi() {
        binding?.run {
            val bleConnected = viewModel4Feast.feast.value?.bleConnected() == true
            val feastOpen = viewModel4Feast.feast.value?.feastOpen == true
            val empty = viewModel4Feast.subDevices.value?.isEmpty() != false
            SafeLog.i(TAG) { "subDeviceListUi() bleConnected = $bleConnected ;feastOpen = $feastOpen ; empty = $empty" }
            adapter.setEnable(bleConnected && feastOpen)
        }
    }

    private fun parseParams(intent: Intent?) {
        intent?.run {
            gId = getIntExtra(Constant.intent_key_ac_gId, 0)
            into4Add = intent.getIntExtra(
                Constant.intent_key_jump_type, Constant.jump_4_list
            ) == Constant.jump_4_add
            need2ConnectBle = intent.getBooleanExtra(Constant.intent_key_need_2_connect, true)
            musicSupportAi = intent.getBooleanExtra(Constant.intent_key_music_support_ai, true)
            LogInfra.Log.i(
                TAG,
                "parseParams() gId = $gId ; into4Add = $into4Add ; need2ConnectBle = $need2ConnectBle ; musicSupportAi = $musicSupportAi"
            )
        }
    }

    override fun bindView(layoutId: Int): Boolean {
        binding = DataBindingUtil.setContentView(this, layoutId)
        return true
    }

    /**
     * 页面控件操作
     */
    private fun initBinding() {
        //TopBar适配
        val margin = getAdaptationInsetTop(AppUtil.getScreenWidth() * 61 / 750)
        if (margin > 0) {
            val params = binding!!.scrollFlag.layoutParams
            params.height += margin
            binding!!.scrollFlag.layoutParams = params
        }

        /*设置控件响应事件*/
        binding?.run {
            /*网络请求*/
            netErrorContainer.setListener {
                val mark = if (musicSupportAi) Constant.music_feast_ai else Constant.music_feast
                viewModel4Feast.requestFeast(
                    this@MusicFeastDetailAcV4, gId, mark
                )
            }
            /*滚动不透明度*/
            scrollView.setOnScrollChangeListener { _: NestedScrollView?, _: Int, scrollY: Int, _: Int, _: Int ->
                updateUIAlpha(scrollY)
            }
            /*返回键*/
            back.clickDelay {
                doOnDestroy()
            }
            ivHelp.clickDelay {
                FbAcJump.jumpFbAc(this@MusicFeastDetailAcV4, true)
            }
            /*删除键*/
            delete.clickDelay {
                ConfirmDialog.createConfirmDialog(
                    this@MusicFeastDetailAcV4,
                    ResUtil.getString(R.string.moviefeast_delete_hint),
                    ResUtil.getString(R.string.cancel),
                    ResUtil.getString(R.string.confirm)
                ) {
                    val bleStatus = viewModel4Feast.feast.value?.bleStatus
                    when (bleStatus) {
                        ViewModel4Feast.BLE_STATUS_CONNECTED -> {
                            if (!NetworkUtil.isNetworkAvailable(this@MusicFeastDetailAcV4)) {
                                toast(R.string.net_fail_check_retry_hint)
                                return@createConfirmDialog
                            }
                            //蓝牙连接成功，先删除主设备再从网络删除
                            SafeLog.d(TAG) { "删除盛宴--蓝牙连接成功，先删除主设备" }
                            viewModel4Feast.deleteFeast()
                        }

                        ViewModel4Feast.BLE_STATUS_CONNECT_FAIL -> {
                            SafeLog.d(TAG) { "删除盛宴--主设备连接失败" }
                            if (viewModel4Feast.subDevices.value.isNullOrEmpty()) {
                                if (!NetworkUtil.isNetworkAvailable(this@MusicFeastDetailAcV4)) {
                                    toast(R.string.net_fail_check_retry_hint)
                                    return@createConfirmDialog
                                }
                                LoadingDialog.createDialog(this@MusicFeastDetailAcV4, 60 * 1000L)
                                    .show()
                                SafeLog.d(TAG) { "删除盛宴--主设备连接失败,没有子设备，直接从服务器删除" }
                                viewModel4Feast.deleteFeastFromServer()
                            } else {
                                MusicFeastHelper.showForceUnbindDialog(
                                    this@MusicFeastDetailAcV4,
                                    viewModel4Feast.feast.value?.centerDevice?.deviceName.orEmpty(),
                                    viewModel4Feast.feast.value?.feastName.orEmpty(),
                                    viewModel4Feast.feast.value?.gId ?: 0,
                                    viewModel4Feast.feast.value?.centerDevice?.goodsType ?: 0
                                )
                            }
                        }

                        ViewModel4Feast.BLE_STATUS_CONNECTING -> {
                            toast(R.string.moviefeast_unconnected_hint)
                        }

                        else -> {
                        }
                    }
                }.show()
            }
            /*编辑盛宴名称*/
            ivEditFeastName.clickDelay {
                val sensorNameHintDes = ResUtil.getStringFormat(
                    R.string.setting_device_name_hint, 22.toString()
                )
                val feastName = viewModel4Feast.feast.value?.feastName ?: ""
                NameEditDialog.createNameEditDialog(
                    this@MusicFeastDetailAcV4,
                    ResUtil.getString(R.string.scenes_class_feast),
                    feastName,
                    ResUtil.getString(R.string.cancel),
                    ResUtil.getString(R.string.done)
                ) { _: NameEditDialog?, newName: String ->
                    if (!StrUtil.isInputCheck(newName.trim { it <= ' ' }, 22)) {
                        toast(R.string.invalid_input)
                        return@createNameEditDialog
                    }
                    viewModel4Feast.editFeastName(this@MusicFeastDetailAcV4, newName, gId)
                }.setTextLimit(22).setTextHint(sensorNameHintDes).show()
            }
            /*提示文案弹窗*/
            tvHint.clickDelay {
                HintDialog1.createHintDialog1(
                    this@MusicFeastDetailAcV4,
                    ResUtil.getString(R.string.moviefeast_connect_hint),
                    ResUtil.getString(R.string.hint_done_got_it)
                ).show()
            }
            /*开关盛宴*/
            ivSwitch.clickDelay {

                viewModel4Feast.feast.value?.run {
                    AnalyticsRecorder.getInstance().recordTimes(
                        EventKey.use_count,
                        ParamKey.click_1163_ai_music_switch,
                        if (this.feastOpen) ParamFixedValue.ai_music_switch_off else ParamFixedValue.ai_music_switch_on
                    )
                    if (!this.feastOpen && viewModel4Feast.subDevices.value?.isEmpty() == true) {
                        AnalyticsRecorder.getInstance()
                            .recordUseCount(
                                ParamKey.turn_on_feast_no_sub_device,
                                ParamFixedValue.feast_music
                            )
                    }
                    showLoading()
                    MusicFeastM.operateSwitch(!this.feastOpen, object : MusicFeastM.OpOverListener {
                        override fun overResult(suc: Boolean) {
                            hideLoading()
                        }
                    })
                }
            }
            /*添加新的子设备*/
            imgAddNew.clickDelay {
                viewModel4Feast.feast.value?.run {
                    var maxSubDeviceNum =
                        Constant.maxSubDeviceNum(centerDevice?.goodsType ?: 0, protocolVersion)
                    centerDevice?.maxSubDevice?.let {
                        if (it > 0) maxSubDeviceNum = it
                    }
                    LogInfra.Log.i(TAG, "imgAddNew() maxSubDeviceNum = $maxSubDeviceNum")
                    val mark = if (musicSupportAi) Constant.music_feast_ai else Constant.music_feast
                    ChooseSubDeviceAc.jump2ChooseSubDeviceAc(
                        this@MusicFeastDetailAcV4,
                        centerDevice?.sku,
                        centerDevice?.bleAddress,
                        gId,
                        ArrayList(),
                        mark,
                        protocolVersion,
                        maxSubDeviceNum
                    )
                }
            }
            /*编辑子设备*/
            ivEditSubDevice.clickDelay {
                val subDeviceArrayList = ArrayList<SubDevice>()
                viewModel4Feast.subDevices.value?.run {
                    subDeviceArrayList.addAll(this)
                }
                viewModel4Feast.feast.value?.run {
                    var maxSubDeviceNum =
                        Constant.maxSubDeviceNum(centerDevice?.goodsType ?: 0, protocolVersion)
                    centerDevice?.maxSubDevice?.let {
                        if (it > 0) maxSubDeviceNum = it
                    }
                    LogInfra.Log.i(TAG, "ivEditSubDevice() maxSubDeviceNum = $maxSubDeviceNum")
                    val mark = if (musicSupportAi) Constant.music_feast_ai else Constant.music_feast
                    ChooseSubDeviceAc.jump2ChooseSubDeviceAc(
                        this@MusicFeastDetailAcV4,
                        centerDevice?.sku,
                        centerDevice?.bleAddress,
                        gId,
                        subDeviceArrayList,
                        mark,
                        protocolVersion,
                        maxSubDeviceNum
                    )
                }
            }
        }

        LiveEventBus.get<Boolean>(EVENT_KEY).observe(this) {
            binding?.run {
                if (it && adapter.data.isNotEmpty()) {
                    vSpecialSpace.visible()
                    vSpecialSpace.post {
                        scrollView.scrollTo(0, 500.dp4Int)
                    }
                } else {
                    vSpecialSpace.gone()
                }
            }
        }
    }

    /**
     * 根据滚动范围刷新头部ui
     *
     * @param scrollY
     */
    private fun updateUIAlpha(scrollY: Int) {
        binding?.run {
            val alpha = calculateAlpha(scrollY)
            topFlag.post(object : CaughtRunnable() {
                override fun runSafe() {
                    topBg.alpha = alpha
                }
            })
            topBg.post(object : CaughtRunnable() {
                override fun runSafe() {
                    topBg.alpha = alpha
                }
            })
            back.post(object : CaughtRunnable() {
                override fun runSafe() {
                    back.setImageDrawable(ResUtil.getDrawable(if (alpha < 0.5f) R.mipmap.new_home_icon_top_corner_back else R.mipmap.new_sensor_setting_icon_arrow_left))
                }
            })
            delete.post(object : CaughtRunnable() {
                override fun runSafe() {
                    delete.setImageDrawable(ResUtil.getDrawable(if (alpha < 0.5f) R.mipmap.new_testingclub_btn_delate_big else R.mipmap.new_testingclub_btn_delate_big_black))
                }
            })
            backBg.post(object : CaughtRunnable() {
                override fun runSafe() {
                    backBg.alpha = alpha
                }
            })
            deleteBg.post(object : CaughtRunnable() {
                override fun runSafe() {
                    deleteBg.alpha = alpha
                }
            })
            setting.post(object : CaughtRunnable() {
                override fun runSafe() {
                    ResUtil.setImageResource(
                        setting,
                        if (alpha < 0.5f) R.mipmap.new_home_icon_top_corner_setting else com.govee.h1162.R.mipmap.new_control_light_1168_icon_mini_shezhi_gree
                    )
                }
            })
            settingBg.post(object : CaughtRunnable() {
                override fun runSafe() {
                    settingBg.alpha = alpha
                }
            })
            ivHelp.post(object : CaughtRunnable() {
                override fun runSafe() {
                    ivHelp.setImageDrawable(ResUtil.getDrawable(if (alpha < 0.5f) R.mipmap.new_dreamview_icon_feedback else R.mipmap.new_dreamview_icon_feedback_black))
                }
            })
            helpBg.post(object : CaughtRunnable() {
                override fun runSafe() {
                    helpBg.alpha = alpha
                }
            })
            if (calculateAlpha(scrollY) < 0.5f) {
                StatusBarUtil.statusBarTranslucentMode(this@MusicFeastDetailAcV4)
            } else {
                if (isLightMode()) {
                    StatusBarUtil.statusBarLightMode(this@MusicFeastDetailAcV4)
                } else {
                    StatusBarUtil.statusBarTranslucentMode(this@MusicFeastDetailAcV4)
                }
            }
        }
    }

    /**在加载中和异常状态时将back按钮显示对应样式颜色*/
    private fun showBackBtStyleByErrorStatus(errorStatus: Boolean) {
        binding?.back?.setImageDrawable(ResUtil.getDrawable(if (!errorStatus) R.mipmap.new_home_icon_top_corner_back else R.mipmap.new_sensor_setting_icon_arrow_left))
        binding?.backBg?.alpha = if (errorStatus) {
            1f
        } else {
            0f
        }
        binding?.topBg?.visibleByBoolean(!errorStatus)
    }

    /**
     * 计算alpha
     *
     * @param scrollY
     * @return
     */
    private fun calculateAlpha(scrollY: Int): Float {
        val maxH = AppUtil.getScreenWidth() * 306 / 750
        return if (scrollY > maxH) {
            1.0f
        } else {
            scrollY * 1.0f / maxH
        }
    }

    /**
     * 蓝牙连接ui
     */
    private fun initConnectUI() {
        /*添加布局-connect*/
        val failDes: CharSequence =
            ResUtil.getString(R.string.moviefeast_no_connect_hint)
        val connectStr = ResUtil.getString(R.string.b2light_reconnect)
        connectUI = ConnectUIV1(this, failDes, connectStr, null, false, null, 441.5f)
        val fucViewConnect = connectUI.fucView
        /*构建基础ui*/
        val lp = PercentRelativeLayout.LayoutParams(
            connectUI.width, connectUI.height
        )
        binding?.acContainer?.addView(fucViewConnect, lp)
        //点击重连按钮
        connectUI.setConnectV1ClickListener {
            val is2Connect = it == EventClickConnectV1.click_type_2connect
            if (is2Connect) {
                if (!BleController.getInstance().isBlueToothOpen) {
                    toast(R.string.main_operation_fail_ble_not_open)
                    return@setConnectV1ClickListener
                }
                viewModel4Feast.retryConnectBle()
            }
        }
        connectUI.hide()
    }

    private fun initMusicUI() {
        binding?.run {
            musicUi.updateCategories(musicSupportAi)
            musicUi.applyListener = object : IFeastApplyListener {
                override fun changeBrightness(brightness: Int, index: Int) {
                    SafeLog.i(TAG) { "changeBrightness() brightness = $brightness ; index = $index" }
                    showLoading()
                    MusicFeastM.operateBrightness(
                        brightness,
                        index,
                        object : MusicFeastM.OpOverListener {
                            override fun overResult(suc: Boolean) {
                                hideLoading()
                            }
                        })
                }

                override fun changeSensitivity(sensitivity: Int) {
                    SafeLog.i(TAG) { "changeSensitivity() sensitivity = $sensitivity" }
                    showLoading()
                    MusicFeastM.operateSensitivity(sensitivity)
                }

                override fun switchBrightnessUnite() {
                    showLoading()
                    viewModel4Feast.feastBrightness.value?.run {
                        val open = !this.brightnessUnite
                        AnalyticsRecorder.getInstance().recordTimes(
                            EventKey.use_count,
                            ParamKey.click_uniform_brightness,
                            if (open) ParamFixedValue.off_2_on else ParamFixedValue.on_2_off
                        )
                        SafeLog.i(TAG) { "switchBrightnessUnite() open = $open" }
                        MusicFeastM.operateBrightnessUnite(
                            open,
                            object : MusicFeastM.OpOverListener {
                                override fun overResult(suc: Boolean) {
                                    hideLoading()
                                }
                            })
                    }
                }

                override fun applyQuFeng(musicCode: Int) {
                    SafeLog.i(TAG) { "applyQuFeng() musicCode = $musicCode" }
                    AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.click_1163_model)
                    AnalyticsRecorder.getInstance().recordTimes(
                        EventKey.use_count, ParamKey.click_1163_model_ai, musicCode.toString()
                    )
                    showLoading()
                    MusicFeastM.operateMusicMode(FeastMusicMode.music_type_qufeng, musicCode)
                }

                override fun applyGuangYingXiu(musicCode: Int) {
                    SafeLog.i(TAG) { "applyGuangYingXiu() musicCode = $musicCode" }
                    AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.click_1163_model)
                    AnalyticsRecorder.getInstance().recordTimes(
                        EventKey.use_count,
                        ParamKey.click_1163_model_light_show,
                        musicCode.toString()
                    )
                    showLoading()
                    MusicFeastM.operateMusicMode(FeastMusicMode.music_type_guangyingxiu, musicCode)
                }

                override fun applyJiaNianHua(musicCode: Int) {
                    SafeLog.i(TAG) { "applyJiaNianHua() musicCode = $musicCode" }
                    AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.click_1163_model)
                    AnalyticsRecorder.getInstance().recordTimes(
                        EventKey.use_count, ParamKey.click_1163_model_carnival, musicCode.toString()
                    )
                    showLoading()
                    MusicFeastM.operateMusicMode(FeastMusicMode.music_type_jianianhua, musicCode)
                }

                override fun applyColors(colors: MutableList<Int>) {
                    SafeLog.i(TAG) { "applyColors() colors.size = ${colors.size}" }
                    showLoading()
                    MusicFeastM.operateColor(colors)
                }

                override fun changeSecondEffect(dynamic: Boolean) {
                    SafeLog.i(TAG) { "changeSecondEffect() dynamic = $dynamic" }
                    showLoading()
                    MusicFeastM.operateDynamic(dynamic)
                }

                override fun changeAction(subAction: Int) {
                    SafeLog.i(TAG) { "changeAction() subAction = $subAction" }
                    showLoading()
                    MusicFeastM.operationSubAction(subAction)
                }

                override fun applyLastMusicMode(musicType: Int, musicCode: Int, dynamic: Boolean) {
                    SafeLog.i(TAG) { "applyLastMusicMode() musicType = $musicType, musicCode = $musicCode, dynamic = $dynamic" }
                    /*统计*/
                    AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.click_1163_model)
                    val paramKey = when (musicType) {
                        FeastMusicMode.music_type_qufeng -> {
                            ParamKey.click_1163_model_ai
                        }

                        FeastMusicMode.music_type_jianianhua -> {
                            ParamKey.click_1163_model_carnival
                        }

                        else -> {
                            ParamKey.click_1163_model_light_show
                        }
                    }
                    AnalyticsRecorder.getInstance()
                        .recordTimes(EventKey.use_count, paramKey, musicCode.toString())
                    showLoading()
                    MusicFeastM.operateApplyLastMusicMode(musicType, musicCode, dynamic)
                }

                override fun noApplyChangeLastMusicMode(
                    musicType: Int,
                    musicCode: Int
                ) {
                    /*是否直接切换*/
                    viewModel4Feast.run {
                        feastMusicMode.value?.copy()?.run {
                            this.change2NewMusicType(musicType, musicCode)
                            viewModel4Feast.feastMusicMode.value = this
                        }
                    }
                }
            }
        }
    }

    /**
     * 设置子设备的recyclerview
     */
    private fun initSubDeviceRecycleView() {
        adapter = SubDeviceAdapter(viewModel4Feast.subDevices.value)
        adapter.onItemClickListener = object : SubDeviceAdapter.OnItemClickListener {
            override fun onClickOp(device: SubDevice, pos: Int) {
                val status = device.status
                if (status == SubDevice.status_no) return
                val op = if (status == SubDevice.status_disconnect) 1 else 0
                showLoading()
                MusicFeastM.operateSubDeviceConnectStatus(
                    op == 1,
                    pos,
                    object : MusicFeastM.OpOverListener {
                        override fun overResult(suc: Boolean) {
                            hideLoading()
                        }
                    })
            }

            override fun onClickFlag(device: SubDevice, pos: Int) {
                HintDialog1.createHintDialog1(
                    this@MusicFeastDetailAcV4,
                    ResUtil.getString(R.string.moviefeast_version_low),
                    ResUtil.getString(R.string.hint_done_got_it)
                ).show()
            }
        }
        var dragStartPos = 0
        adapter.draggableModule.setOnItemDragListener(object : OnItemDragListener {
            override fun onItemDragStart(viewHolder: RecyclerView.ViewHolder?, pos: Int) {
                viewHolder?.itemView?.isSelected = true
                dragStartPos = pos
                SafeLog.i(TAG) { "onItemDragStart: dragStartPos = $dragStartPos" }
            }

            override fun onItemDragMoving(
                source: RecyclerView.ViewHolder?,
                from: Int,
                target: RecyclerView.ViewHolder?,
                to: Int
            ) {
            }

            override fun onItemDragEnd(viewHolder: RecyclerView.ViewHolder?, pos: Int) {
                viewHolder?.itemView?.isSelected = false
                SafeLog.i(TAG) { "onItemDragEnd: dragEndPos = $pos" }
                if (pos != dragStartPos) {
                    showLoading()
                    val subDeviceList = mutableListOf<SubDevice>().apply {
                        addAll(adapter.data)
                    }
                    MusicFeastM.setSubDevice(subDeviceList, object : MusicFeastM.OpOverListener {
                        override fun overResult(suc: Boolean) {
                            hideLoading()
                        }
                    })
                }
            }
        })

        binding?.run {
            val screenWidth = AppUtil.getScreenWidth()
            val glm = GridLayoutManager(this@MusicFeastDetailAcV4, 4)
            recyclerDevice.layoutManager = glm
            recyclerDevice.addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
                ) {
                    super.getItemOffsets(outRect, view, parent, state)
                    val pos = parent.getChildAdapterPosition(view)
                    var left = 0
                    if (pos % 4 == 1) left = 3
                    if (pos % 4 == 2) left = 6
                    if (pos % 4 == 3) left = 9
                    outRect.left = screenWidth * left / 375
                    outRect.bottom = screenWidth * 16 / 375
                }
            })
            recyclerDevice.adapter = adapter
            recyclerDevice.setHasFixedSize(true)
            recyclerDevice.isNestedScrollingEnabled = false
        }
    }

    @Suppress("DEPRECATION")
    override fun onBackPressed() {
        super.onBackPressed()
        doOnDestroy()
    }

    private fun showLoading() {
        LoadingDialog.createDialog(
            this,
            com.ihoment.base2app.R.style.DialogDim,
            (60 * 1000).toLong()
        ).setEventKey(TAG)
            .show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(TAG)
    }

    private fun doOnDestroy() {
        val destroy = connectUI.isDestroy
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "doOnDestroy() destroy = $destroy")
        }
        if (!destroy) {
            connectUI.onDestroy()
        }
        MusicFeastM.onDestroy()
        finish()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent2SetSubDevice(event: Event2SetSubDevice) {
        LogInfra.Log.i(TAG, "onEvent2SetSubDevice()")
        MusicFeastM.setSubDevice(event.subDeviceList)
    }

    private fun initOtaInfo() {
        val centerDevice = viewModel4Feast.feast.value?.centerDevice
        if (GoodsType.GOODS_TYPE_VALUE_BLE_AI_MUSIC_BOX != centerDevice?.goodsType) {
            return
        }
        binding?.run {
            /*设置页*/
            setting.clickDelay {
                Ac4FeastDeviceSetting.jump2Ac(
                    this@MusicFeastDetailAcV4,
                    BleInfo(
                        centerDevice.sku,
                        centerDevice.goodsType,
                        centerDevice.device,
                        "",
                        centerDevice.deviceName,
                        "",
                        centerDevice.bleAddress
                    ).apply {
                        versionSoft = vm4BoxDetail.deviceInfo?.versionSoft
                        versionHard = vm4BoxDetail.deviceInfo?.versionHard
                        checkVersion = vm4BoxDetail.checkVersion
                        aiVersion = vm4BoxDetail.aiVersion
                    },
                    viewModel4Feast.feast.value?.gId ?: 0,
                    viewModel4Feast.feast.value?.batteryLevel ?: 1
                )
            }
        }
        DeviceInfo().apply {

            sku = centerDevice.sku ?: ""
            device = centerDevice.device ?: ""
            deviceName = centerDevice.deviceName ?: ""
            bleAddress = centerDevice.bleAddress ?: ""
            goodsType = centerDevice.goodsType
            versionSoft = centerDevice.versionSoft ?: ""
            versionHard = centerDevice.versionHard ?: ""
            vm4BoxDetail.init(this)
        }

        vm4BoxDetail.gId = gId
        vm4BoxDetail.feastNm = viewModel4Feast.feast.value?.feastName ?: ""
        vm4BoxDetail.versionUpdate.observe(this@MusicFeastDetailAcV4) { b ->
            binding?.versionFlag?.visibleByBoolean(b)
        }
        vm4BoxDetail.versionUpdateDialogShowing.observe(this@MusicFeastDetailAcV4) { b ->
            val show = b ?: return@observe
            updateDialogShow(show)
        }
    }

    private fun updateDialogShow(show: Boolean) {
        SafeLog.i(TAG) { "updateDialogShow() show = $show" }
        if (show) {
            vm4BoxDetail.versionUpdateDialogShowing.value = null
            vm4BoxDetail.deviceInfo?.run {
                BleUpdateHintDialog.showDialog(
                    this@MusicFeastDetailAcV4, this.sku,
                    { toUpdateAc() }, BleUpdateHintDialog::class.simpleName
                )
            }
        } else {
            BleUpdateHintDialog.hideDialog(BleUpdateHintDialog::class.simpleName)
        }
    }

    private fun toUpdateAc() {
        vm4BoxDetail.deviceInfo?.run {
            val checkVersion = vm4BoxDetail.checkVersion
            val aiVersion = vm4BoxDetail.aiVersion
            val defSkuRes = ThemeM.getDefSkuRes(this.sku)
            if (viewModel4Feast.feast.value?.batteryLevel!! <= 2) {
                toast(R.string.low_battery_no_support_ota)
                return
            }
            if (aiVersion != null) {

                OtaUpdateAcV4.jump2OtaUpdateAcV4(
                    this@MusicFeastDetailAcV4,
                    sku,
                    deviceName,
                    versionSoft,
                    defSkuRes,
                    aiVersion
                )
            } else {
                OtaUpdateAcV3.jump2OtaUpdateAcV3(
                    this@MusicFeastDetailAcV4,
                    sku,
                    deviceName,
                    versionSoft,
                    defSkuRes,
                    checkVersion ?: CheckVersion()
                )
            }
        }
    }

    override fun applySnapshot(snapshot: SnapshotListResp.SnapshotItem) {
        val sendBleCmd = snapshotDelegate.makeApplySnapshotCmds(
            snapshot, viewModel4Feast.subDevices.value ?: mutableListOf()
        )
        if (sendBleCmd.isEmpty()) {
            EventApplySnapshotResult.sendEvent(false)
            return
        }
        val multiCompose = makeWriteController4MultiV2(sendBleCmd.toMutableList()) { res: Boolean ->
            if (res) {
                MusicFeastM.sendReadMsg4Snapshot()
            }
            EventApplySnapshotResult.sendEvent(res)
        }
        MusicFeastM.applySnapshot(multiCompose)
    }
}