package com.govee.home.main.device.moment.musicfeast.v4

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.activity.ComponentActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.databinding.DataBindingUtil
import com.govee.base2home.Constant
import com.govee.base2home.account.LoginActivity
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.sku.ColorsM
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.gone
import com.govee.base2kt.ext.setViewsClick
import com.govee.base2kt.ext.setViewsVisible
import com.govee.base2kt.utils.ColorUtils
import com.govee.base2kt.utils.ColorUtils.Companion.toWhite
import com.govee.base2light.ac.diy.PhotoChooseAcV2
import com.govee.base2light.ac.diy.v1.Cons
import com.govee.base2light.ac.diyNew.bean.DiySelectColorInfo
import com.govee.base2light.ac.diyNew.config.params.DiyColorParams
import com.govee.base2light.view.Dialog4ColorRecommend
import com.govee.base2light.view.Dialog4ColorRecommendNew
import com.govee.home.account.config.AccountConfig
import com.govee.home.databinding.AppViewMusicNewColorBinding
import com.govee.ui.R
import com.govee.ui.dialog.ConfirmDialogV21
import com.govee.ui.dialog.ConfirmDialogV3
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.ToastUtil
import kotlin.Int
import kotlin.Int as Int1


/**
 *     author  : sinrow
 *     time    : 2024/5/9
 *     version : 1.0.0
 *     desc    :
 */
class MusicColorComponentView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null
) : ConstraintLayout(context, attrs), IActivityResultListener {

    companion object {
        private val TAG = "DiyNewColorComponentView"
        const val REQUEST_CODE_SMART_PICK_COLOR = 9998
    }

    private val mDataBinding: AppViewMusicNewColorBinding? = DataBindingUtil.inflate(
        LayoutInflater.from(context),
        com.govee.home.R.layout.app_view_music_new_color,
        this,
        true
    )
    private var params: DiyColorParams = DiyColorParams()
    private var sku: String = ""
    private var mainColorLimitSize = 8
    private var otherColorLimitSize = 8
    var requestCode = REQUEST_CODE_SMART_PICK_COLOR//智能取色

    var otherValueChangeCallback: ((IntArray) -> Unit)? = null
    var mainValueChangeCallback: ((IntArray) -> Unit)? = null

    var allColorValueChangeCallback: ((IntArray, IntArray) -> Unit)? = null


    fun initParams(activity: Activity, params: DiyColorParams, sku: String) {
        this.params = params
        this.sku = sku
        // 智能取色
        // 推荐配色
        mDataBinding?.run {

            ivHint.clickDelay {
                clickHint()
            }

            setViewsClick(ivRecommendColor, tvRecommendColor) {
                clickRecommendColor()
            }

            setViewsClick(ivSmartColor, tvSmartColor) {
                clickSmartColor()
            }

            setViewsClick(tvSaveMyColor, ivSaveMyColorLabel) {
                clickSaveMyColor()
            }

            diyColorViewMain.sku = sku
            diyColorViewOther.sku = sku
            diyColorViewMain.colorChangeListener = {
                params.isChangeMainColor = true
                diyColorViewMain.getColors()?.let {
                    mainValueChangeCallback?.invoke(it)
                }
            }
            diyColorViewOther.colorChangeListener = {
                if (params.useBgColor) {
                    params.isChangeBgColor = true
                } else {
                    params.isChangeOtherColor = true
                }
                diyColorViewOther.getColors()?.let {
                    otherValueChangeCallback?.invoke(it)
                }
            }

        }
        updateColor(params)
    }

    fun setNeedApplyBtn(onApply: () -> Unit) {
        mDataBinding?.run {
            diyColorViewMain.needColorApplyBtn = true
            diyColorViewMain.onApplyListener = onApply
        }
    }

    fun hideChooseColorFromImage() {
        mDataBinding?.run {
            tvSmartColor.gone()
            ivSmartColor.gone()
            ivSaveMyColorLabel.gone()
            tvSaveMyColor.gone()
            ivHint.gone()
        }
    }

    private fun clickSaveMyColor() {
        SafeLog.i(TAG) { "clickSaveMyColor() " }

        if (!AccountConfig.read().isHadToken) {
            ConfirmDialogV3.showConfirmDialog(
                context, ResUtil.getString(R.string.login_first_label),
                ResUtil.getString(R.string.cancel), ResUtil.getString(R.string.to_login_now),
                object : ConfirmDialogV3.DoneListener {
                    override fun doDone() {
                        LoginActivity.jump2LoginAc(context, "", false)
                    }

                    override fun doCancel() {}
                })
            return
        }

        val colors: IntArray = getColors()
        if (colors.isEmpty()) {
            ToastUtil.getInstance().toast(R.string.app_set_color_first)
            return
        }
        val saveColors: MutableList<Int> = ArrayList()
        for (color in colors) {
            var colorTemp = color
            if (Constant.colorTemperatureArray.contains(color)) {
                colorTemp = toWhite()
            }
            if (!saveColors.contains(colorTemp) && !ColorUtils.isNoColor(colorTemp)) {
                saveColors.add(colorTemp)
            }
        }
        if (saveColors.size == 0) return
        if (!TextUtils.isEmpty(sku)) {
            AnalyticsRecorder.getInstance()
                .recordTimes(
                    EventKey.use_count,
                    ParamKey.my_color_save,
                    ParamFixedValue.saveMyColor(sku, ParamFixedValue.diy)
                )
        }
        ColorsM.getInstance.saveColors(context, saveColors, null)
    }

    private fun getColors(): IntArray {
        return mutableListOf<Int>().apply {
            params.getOtherColorParams()?.let {
                SafeLog.i(TAG) { "getColors() 其他颜色有 ${it.second}" }
                addAll(it.second)
            }
            params.getMainColorParams().let {
                SafeLog.i(TAG) { "getColors() 主颜色有 ${it.second}" }
                addAll(it.second)
            }
        }.toIntArray()
    }

    private fun clickRecommendColor() {
        SafeLog.i(TAG) { "clickRecommendColor() " }
        AnalyticsRecorder.getInstance()
            .recordUseCount(ParamKey.new_diy, ParamFixedValue.click_color_recommend)
        val otherColorParams = params.getOtherColorParams()
        val mainColorParams = params.getMainColorParams()
        val first = mainColorParams.first
        val max = first[1]
        otherColorParams?.let {
            val maxOther = it.first[1]
            Dialog4ColorRecommendNew.showDialog(
                context as ComponentActivity,
                maxOther,
                max,
                sku,
                params.getMulTitles(),
                Dialog4ColorRecommendNew.op_from_type_musicMode
            ) { color1, color2 ->
                params.setOtherColors(color1.toIntArray())
                params.setMainColors(color2)
                updateColor(params)
                allColorValueChangeCallback?.invoke(color1.toIntArray(), color2.toIntArray())
            }
        } ?: run {
            //使用旧的配色推荐
            Dialog4ColorRecommend.showDialog4MusicMode(
                context,
                max
            ) { colors: IntArray ->
                // 刷新主色
                params.setMainColors(colors)
                updateColor(params)
                mainValueChangeCallback?.invoke(colors)
            }

            /* Dialog4ColorRecommendNew.showDialog(context as ComponentActivity, max, sku) {
                 // 刷新主色
                 params.setMainColors(it)
                 updateColor(params)
                 mainValueChangeCallback?.invoke(it.toIntArray())
             }*/
        }
    }

    fun updateColor(params: DiyColorParams) {
        this.params = params
        mDataBinding?.run {
            val mainColorParams = params.getMainColorParams()
            val min = mainColorParams.first[0]
            val max = mainColorParams.first[1]
            val second = mainColorParams.second
            diyColorViewMain.showTransparent = mainColorParams.third
            diyColorViewMain.updateColor(min, max, second, false)
            mainColorLimitSize = max

            val otherColorParams = params.getOtherColorParams()?.let {
                val minOther = it.first[0]
                val maxOther = it.first[1]
                val secondOther = it.second
                diyColorViewOther.showTransparent = it.third
                diyColorViewOther.updateColor(minOther, maxOther, secondOther, false)
                otherColorLimitSize = maxOther
            }
            tvColorGroupMain.text = params.getMainColorTitle()
            tvColorGroupOther.text = params.getOtherColorTitle()
            setViewsVisible(
                otherColorParams != null,
                tvColorGroupOther,
                tvColorGroupMain,
                diyColorViewOther
            )
        }
    }

    fun hideApplyBtn() {
        mDataBinding?.diyColorViewMain?.removeApplyBtn()
    }


    private fun clickHint() {
        val confirmDialogV21 = ConfirmDialogV21(context)
        confirmDialogV21.setTitle(R.string.app_color_desc)
            .setContent(R.string.b2light_dialog_color_hint)
            .setTagContent1(R.string.b2light_color_disk, R.string.b2light_dialog_diy_color_hint1)
            .setTagContent2(
                R.string.recommend_color_groups_label,
                R.string.b2light_dialog_diy_color_hint2
            )
            .setTagContent3(
                R.string.b2light_choose_color_hint,
                R.string.b2light_dialog_diy_color_hint3
            )
            .show()
    }

    private fun clickSmartColor() {
        SafeLog.i(TAG) { "clickSmartColor() " }
        AnalyticsRecorder.getInstance()
            .recordUseCount(ParamKey.new_diy, ParamFixedValue.click_Intelligent_color_selection)
        val mainColorParams = params.getMainColorParams()
        val otherColorParams = params.getOtherColorParams()

        val max = mainColorParams.first[1]

        val info = DiySelectColorInfo
            .builder(requestCode, sku, max)
            .setBottomListName(params.getMainColorTitle())
            .setTopListName(params.getOtherColorTitle())
            .setTopListSupportColorNum(otherColorParams?.first?.get(1) ?: 0)
            .build()

        PhotoChooseAcV2.jump2PhotoChooseAc4Result(context as Activity, info)
    }

    private fun isBlackColor(color: Int1): Boolean {
        return color == 0 || color == ResUtil.getColor(R.color.black) || color == ResUtil.getColor(R.color.black_block)
    }

    fun recycler() {
        mDataBinding?.run {

        }
    }


    override fun onActivityResult(requestCode: Int1, resultCode: Int1, data: Intent?): Boolean {
        if (this.requestCode != requestCode) {
            return false
        }
        if (resultCode == Activity.RESULT_OK) {
            if (data != null) {
                val topColors = data.getIntArrayExtra(Cons.intent_ac_new_diy_pick_color_top_array)
                val bottomColors =
                    data.getIntArrayExtra(Cons.intent_ac_new_diy_pick_color_bottom_array)
                val hasTopColor = topColors != null && topColors.isNotEmpty()
                val hasBottomColor = bottomColors != null && bottomColors.isNotEmpty()
                if (hasTopColor) {
                    // 更新其他颜色
                    val handlerColors =
                        handlerColors(
                            limitSize = otherColorLimitSize,
                            oldColors = mDataBinding?.diyColorViewOther?.getColors(),
                            newColors = topColors
                        )
                    handlerColors?.let {
                        params.setOtherColors(it)
                    }
                }
                if (hasBottomColor) {
                    // 更新主颜色
                    val handlerColors =
                        handlerColors(
                            limitSize = mainColorLimitSize,
                            oldColors = mDataBinding?.diyColorViewMain?.getColors(),
                            newColors = bottomColors
                        )
                    handlerColors?.let {
                        params.setMainColors(it)
                    }
                }
                if (hasTopColor || hasBottomColor) {
                    updateColor(params)
                }

                val mainColors = mDataBinding?.diyColorViewMain?.getColors()
                val otherColors = mDataBinding?.diyColorViewOther?.getColors()

                if (hasTopColor && hasBottomColor) {
                    // 两个都变更
                    if (mainColors != null && otherColors != null) {
                        params.isChangeMainColor = true
                        if (params.useBgColor) {
                            params.isChangeBgColor = true
                        } else {
                            params.isChangeOtherColor = true
                        }
                        allColorValueChangeCallback?.invoke(mainColors, otherColors)
                    }
                } else if (hasTopColor) {
                    otherColors?.let {
                        params.isChangeMainColor = true
                        otherValueChangeCallback?.invoke(it)
                    }
                } else if (hasBottomColor) {
                    mainColors?.let {
                        if (params.useBgColor) {
                            params.isChangeBgColor = true
                        } else {
                            params.isChangeOtherColor = true
                        }
                        mainValueChangeCallback?.invoke(it)
                    }
                }
            }
        }
        return true
    }

    private fun handlerColors(
        limitSize: Int,
        oldColors: IntArray?,
        newColors: IntArray?
    ): IntArray? {
        if (oldColors == null || newColors == null) return null
        val maxSize = oldColors.size + newColors.size
        return if (maxSize <= limitSize) {
            // 两个之和
            mutableListOf<Int1>().apply {
                oldColors.forEach {
                    add(it)
                }
                newColors.forEach {
                    add(it)
                }
            }.toIntArray()
        } else {
            val list = mutableListOf<Int1>()
            val hasOldSize = limitSize - newColors.size
            oldColors.copyOf(hasOldSize).forEach {
                list.add(it)
            }
            newColors.forEach {
                list.add(it)
            }
            list.toIntArray()
        }
    }

}

interface IActivityResultListener {
    fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?): Boolean
}
