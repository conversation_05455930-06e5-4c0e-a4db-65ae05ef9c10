package com.govee.home.main.device.moment.musicfeast.ble

import android.bluetooth.BluetoothDevice
import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.TextUtils
import androidx.lifecycle.DefaultLifecycleObserver
import androidx.lifecycle.LifecycleOwner
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.pact.GoodsType
import com.govee.base2kt.ext.eventBusRegisterOp
import com.govee.base2light.ac.diy.DiyM
import com.govee.base2light.ac.diy.IMusicFeastOp
import com.govee.base2light.ac.update.UpdateResultEvent
import com.govee.base2light.ble.AbsBle
import com.govee.base2light.ble.EventDeviceStatus
import com.govee.base2light.ble.controller.AbsSingleController
import com.govee.base2light.ble.controller.EventFeastHeart
import com.govee.base2light.ble.controller.EventMovieFeastOnOffNotify
import com.govee.base2light.ble.controller.EventSwitch
import com.govee.base2light.ble.controller.EventSwitch4Feast
import com.govee.base2light.ble.controller.HeartControllerWithBattery
import com.govee.base2light.ble.controller.IComposeController
import com.govee.base2light.ble.controller.SwitchController
import com.govee.base2light.ble.controller.SwitchControllerV2
import com.govee.base2light.feast.Event2ConnectInDetail
import com.govee.base2light.pact.feast.FeastDetailResponse
import com.govee.base2light.pact.feast.SubDevice
import com.govee.base2light.util.BleBvCacheFunCall
import com.govee.ble.BleController
import com.govee.ble.event.BTStatusEvent
import com.govee.ble.event.EventBleBroadcastListenerTrigger
import com.govee.ble.event.EventBleConnect
import com.govee.ble.event.ScanEvent
import com.govee.ble.scan.ScanManager
import com.govee.ble.scan.ScanParams
import com.govee.db.memory.ShortMemoryMgr.bleBroadVersionCache
import com.govee.h1162.ble.DeviceNumController
import com.govee.h1162.ble.EventDeviceNum
import com.govee.home.main.device.moment.Constant
import com.govee.home.main.device.moment.feastcontroller.DeviceResetController
import com.govee.home.main.device.moment.feastcontroller.FeastBrightnessController
import com.govee.home.main.device.moment.feastcontroller.FeastBrightnessUniteController
import com.govee.home.main.device.moment.feastcontroller.MultiDeviceControllerV2
import com.govee.home.main.device.moment.feastcontroller.MusicModeControllerV2
import com.govee.home.main.device.moment.feastcontroller.MusicSubDeviceEffectController
import com.govee.home.main.device.moment.feastcontroller.SubDeviceClearController
import com.govee.home.main.device.moment.feastcontroller.SubDeviceConnectController
import com.govee.home.main.device.moment.feastevent.EventFeastBrightnessUnite
import com.govee.home.main.device.moment.moviefeast.Area4Device
import com.govee.home.main.device.moment.moviefeast.ble.controller.MovieClearSubDeviceController
import com.govee.home.main.device.moment.moviefeast.ble.controller.MovieSubDeviceController
import com.govee.home.main.device.moment.moviefeast.ble.event.Event2ClearSubDevice
import com.govee.home.main.device.moment.moviefeast.ble.event.EventMovieClearSubDevice
import com.govee.home.main.device.moment.moviefeast.ble.event.EventMovieSubDeviceConnect
import com.govee.home.main.device.moment.musicfeast.EffectColorConfig
import com.govee.home.main.device.moment.musicfeast.Info
import com.govee.home.main.device.moment.musicfeast.bean.MusicMode
import com.govee.home.main.device.moment.musicfeast.controller.MultiDeviceController
import com.govee.home.main.device.moment.musicfeast.controller.MultiMusicController
import com.govee.home.main.device.moment.musicfeast.controller.MusicBrightnessController
import com.govee.home.main.device.moment.musicfeast.controller.MusicModeController
import com.govee.home.main.device.moment.musicfeast.event.Event2SetSubDevice
import com.govee.home.main.device.moment.musicfeast.event.Event2SetSubDeviceResult
import com.govee.home.main.device.moment.musicfeast.event.EventMusicBrightness
import com.govee.home.main.device.moment.musicfeast.event.EventMusicMode
import com.govee.home.main.device.moment.musicfeast.event.EventSetDevice
import com.govee.home.main.device.moment.musicfeast.event.EventSetMultiMusic
import com.govee.home.main.device.moment.musicfeast.event.EventSubDeviceMusicModeEffect
import com.govee.home.main.device.moment.net.IMomentNet
import com.govee.home.main.device.moment.net.request.FeastRegionRequest
import com.govee.home.main.device.moment.net.response.FeastRegionResponse
import com.govee.home.main.device.moment.ui.BrightnessDialog
import com.govee.ui.R
import com.ihoment.base2app.Cache
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.ToastUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Create by hey on 2021/10/8
 * $ 音乐盛宴蓝牙通信模块
 * 多个ac需要用到这个类去操作，此处优化成单例会更好，避免多个ac用eventbus通信
 * 连接流程 ：读信息——读模式——设置子设备——写多包颜色——写模式——读子设备连接状态——读亮度——结束（旧的1162先写模式后写多包颜色）
 * 设置子设备流程：设置子设备——写多包颜色——写模式——读子设备连接状态——读亮度——结束（旧的1162先写模式后写多包颜色）
 */
class Ble(
    val bleInfo: Info,
    val centerDevice: FeastDetailResponse.Data.CenterDevice,
    var bleConnectResult: BleConnectResult?,
    val gId: Int,
    val protocolVersion: Int
) : DefaultLifecycleObserver {
    companion object {
        private const val TAG = "Ble"
        private const val what_bt_open_retry_connect_ble = 200000
        private const val what_scan_device_overtime = 100002
        private const val delay_scan_device_over_time_mills = 15 * 1000L
        private const val what_connect_ble_overtime = 100000
        private const val delay_connect_ble_over_time_mills = 15 * 1000L
        private const val what_retry_connect_device = 100001
        private const val delay_time_retry_connect_device_mills = 500L
        private const val what_start_scan = 100003
    }

    private val mainHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            val what = msg.what
            doWhat(what)
        }
    }

    private var absBle: AbsBle? = null
    private val subDeviceList: MutableList<SubDevice> = ArrayList<SubDevice>()

    private val transactions = Transactions()
    private var iMusicFeastOp: IMusicFeastOp? = null

    private var modeController: AbsSingleController? = null

    var deleteFeast: Boolean = false //标记此次清子设备是删除盛宴
    private var deviceReset = false //设备恢复了出厂设置
    private var inSetSubDevice = false //正在设置子设备
    private var connectFinish = false //连接流程结束

    private var readMsg4Snapshot = false //应用快照后读

    private fun doHandler(what: Int, delayMills: Long) {
        mainHandler.removeMessages(what)
        mainHandler.sendEmptyMessageDelayed(what, delayMills)
    }

    init {
        tryFindBleOp()
    }

    private fun doWhat(what: Int) {
        SafeLog.i(TAG) { "doWhat() what = $what" }
        if (what == what_start_scan) {
            ScanManager.getInstance().stopFoundDevice()
            val params = ScanParams().also {
                it.updateBleAddressInclude(mutableListOf<String>().apply {
                    add(centerDevice.bleAddress)
                })
            }
            ScanManager.getInstance().startFoundDevice(params)
            return
        }
        when (what) {
            what_connect_ble_overtime -> {
                doBleConnectOvertime()
            }

            what_retry_connect_device -> {
                toConnectBle()
            }

            what_scan_device_overtime -> {
                doScanOvertime()
            }

            what_bt_open_retry_connect_ble -> {
                tryReconnected()
            }
        }
    }

    private fun tryFindBleOp() {
        SafeLog.i(TAG) { "tryFindBleOp() " }
        // TODO: @谢鹦鹉 盛宴入口，此处仅需要蓝牙通信器（心跳，主动上报解析（子设备连接状态）），是否还需要单独子类构建 后续得考虑优化
        val iMusicFeastOpList = DiyM.getInstance.musicFeastOp
        val device = centerDevice.toAbsDevice()
        for (musicFeastOp in iMusicFeastOpList) {
            if (musicFeastOp.support(device, 0)) {
                iMusicFeastOp = musicFeastOp
                break
            }
        }
        iMusicFeastOp?.let { op ->
            absBle = op.ble?.also {
                /**仅在存在蓝牙通信器时才需要注册EventBus*/
                eventBusRegisterOp(true)
                it.registerEvent(true, <EMAIL>)
                /**无论是否从详情页进入，都需要先扫描再连接*/
                startScan()
            } ?: run {
                bleConnectResult?.bleConnectFail()
                null
            }

        } ?: run {
            SafeLog.e(TAG) { "tryFindBleOp() 没有找到对应主设备的子类IMusicFeastOp的实现！" }
            bleConnectResult?.bleConnectFail()
            return
        }
    }

    private fun startScan() {
        SafeLog.i(TAG) { "startScan() " }
        /**关闭外部扫描*/
        EventBleBroadcastListenerTrigger.sendEventBleBroadcastListenerTrigger(false)
        doHandler(what_scan_device_overtime, delay_scan_device_over_time_mills)
        toScanDevice()
    }

    private fun doScanOvertime() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "doScanOvertime()")
        }
        ScanManager.getInstance().stopFoundDevice()
        /*连接超时，断开蓝牙连接*/
        bluetoothClose()
    }

    /**
     * 扫描设备
     */
    private fun toScanDevice() {
        bleConnectResult?.bleConnecting()
        ScanManager.getInstance().stopFoundDevice()
        val blueToothOpen = BleController.getInstance().isBlueToothOpen
        SafeLog.i(TAG) { "toScanDevice() blueToothOpen = $blueToothOpen" }
        if (blueToothOpen) {
            doHandler(what_start_scan, 500L)
        } else {
            bluetoothClose()
        }
    }

    private fun doBleConnectOvertime() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "doBleConnectOvertime()")
        }
        /*连接超时，断开蓝牙连接*/
        bluetoothClose()
    }

    /**
     * 连接成功开始读信息
     */
    private fun toReadMsg() {
        val ble = absBle ?: return
        SafeLog.i(TAG) { "toReadMsg() " }
        connectFinish = false
        val controllers = getReadControllers()
        SafeLog.i(TAG) { "toReadMsg() controllers.size = ${controllers.size}" }
        ble.startController(*controllers)
    }

    fun tryReconnected() {
        BleController.getInstance().toBtClose()
        /*先扫描设备-再连接*/
        startScan()
    }

    /**
     * 连接连接结果返回
     */
    private fun bleConnectCallback() {
        mainHandler.removeMessages(what_connect_ble_overtime)
    }

    fun startConnect(device: BluetoothDevice? = null) {
        mainHandler.removeMessages(what_connect_ble_overtime)
        mainHandler.sendEmptyMessageDelayed(
            what_connect_ble_overtime,
            delay_connect_ble_over_time_mills.toLong()
        )
        if (device != null) {
            toConnectBle(device)
        } else {
            toConnectBle()
        }
    }

    private fun toConnectBle(bt: BluetoothDevice) {
        val ble = absBle ?: return
        SafeLog.i(TAG) { "toConnectBle() " }
        ble.registerEvent(true, this.javaClass.getName())
        bleConnectResult?.bleConnecting()
        val connectBle = ble.connectBle(bt, true)
        if (!connectBle) {
            bluetoothClose()
        }
    }

    /**
     * 连接蓝牙
     */
    private fun toConnectBle() {
        val ble = absBle ?: return
        SafeLog.i(TAG) { "toConnectBle() " }
        bleConnectResult?.bleConnecting()
        val connectBle = ble.connectBle(centerDevice.bleAddress, true)
        if (!connectBle) {
            bluetoothClose()
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onBTStatusEvent(event: BTStatusEvent) {
        val btOpen = event.isBtOpen
        if (!btOpen) {
            bluetoothClose()
        }
    }

    /**
     * 存储蓝牙是否加密字段
     */
    private fun cacheBleEncryption(event: ScanEvent, address: String) {
        if (bleBroadVersionCache.queryEncryptionSupportInMemory(address)) {
            return
        }
        val scanRecord = event.scanRecord ?: return
        if (scanRecord.size > 10) {
            GoodsType.parseBleBroadcastPactInfo(
                centerDevice.goodsType,
                scanRecord,
                BleBvCacheFunCall(address)
            )
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onScanEvent(event: ScanEvent) {
        absBle ?: return
        val inScanning = mainHandler.hasMessages(what_scan_device_overtime)
        SafeLog.i(TAG) { "onScanEvent() inScanning = $inScanning" }
        if (!inScanning) return
        val device = event.device
        val address = device.getAddress()
        SafeLog.i(TAG) { "onScanEvent() address = $address" }
        if (TextUtils.isEmpty(address)) return
        val isSameDevice = isSameDevice(address)
        SafeLog.i(TAG) { "onScanEvent() isSameDevice = $isSameDevice" }
        if (!isSameDevice) return
        SafeLog.i(TAG) { "onScanEvent() 扫描到指定设备-尝试连接" }
        cacheBleEncryption(event, address)
        /**停止扫描*/
        ScanManager.getInstance().stopFoundDevice()
        mainHandler.removeMessages(what_scan_device_overtime)
        startConnect(device)
    }

    private fun isSameDevice(address: String?): Boolean {
        return !TextUtils.isEmpty(address) && address == centerDevice.bleAddress
    }

    /**
     * 定义蓝牙关闭处理
     */
    private fun bluetoothClose() {
        bleConnectResult?.bleConnectFail()
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEventBleConnect(event: EventBleConnect) {
        absBle ?: return
        val bleConnectResult = bleConnectResult ?: return
        val connectSuc = event.connectSuc()
        SafeLog.i(TAG) { "onEventBleConnect() connectSuc = $connectSuc" }
        if (connectSuc) {
            /*连接成功*/
            bleConnectCallback()
            toReadMsg()
        } else {
            val inConnectDeviceOverTime = mainHandler.hasMessages(what_connect_ble_overtime)
            if (inConnectDeviceOverTime) {
                /*连接失败，重新尝试连接*/
                doHandler(what_retry_connect_device, delay_time_retry_connect_device_mills)
                bleConnectResult.bleConnecting()
            } else {
                bleConnectCallback()
                bleConnectResult.bleConnectFail()
            }
        }
    }

    /**
     * @return 连接过程需要获取的信息
     */
    private fun getReadControllers(): Array<AbsSingleController> {
        SafeLog.i(TAG) { "getReadControllers() protocolVersion = $protocolVersion ;  intoFromAdd = ${bleInfo.intoFromAdd}" }
        var controllers: Array<AbsSingleController>
        //迭代前后协议不同 需要的信息也不同
        if (protocolVersion == 1) {
            //迭代后
            if (bleInfo.intoFromAdd) {
                /*从添加流程进来打开盛宴和亮度统一开关*/
                controllers = arrayOf<AbsSingleController>(
                    SwitchControllerV2(true),  //从添加进来的话打开盛宴
                    DeviceResetController(),
                    FeastBrightnessUniteController(true),
                    MusicModeControllerV2(),
                )
            } else {
                controllers = arrayOf<AbsSingleController>(
                    SwitchControllerV2(),
                    DeviceResetController(),
                    FeastBrightnessUniteController(),
                    MusicModeControllerV2(),
                )
            }
        } else {
            controllers = if (bleInfo.intoFromAdd) {
                /*从添加流程进来或重连，打开盛宴*/
                arrayOf<AbsSingleController>(
                    SwitchController(true),  //从添加进来的话打开盛宴
                    DeviceNumController(),
                    MusicModeController(),
                )
            } else {
                arrayOf<AbsSingleController>(
                    SwitchController(),
                    DeviceNumController(),
                    MusicModeController(),
                )
            }
        }
        return controllers
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSwitch(event: EventSwitch) {
        absBle?.controllerEvent(event) ?: return
        SafeLog.i(TAG) { "onEventSwitch() " }
        //只有迭代前的1162用这个开关事件
        if (protocolVersion != 0) return
        val result = event.isResult
        val write = event.isWrite
        SafeLog.i(TAG) { "onEventSwitch() write = $write ; result = $result" }
        if (result) {
            bleInfo.open.value = event.isOpen
        }
        if (write && connectFinish) {
            bleConnectResult?.updateUI()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSwitchV2(event: EventSwitch4Feast) {
        absBle?.controllerEvent(event) ?: return
        SafeLog.i(TAG) { "onEventSwitchV2() " }
        val result = event.isResult
        val write = event.isWrite
        SafeLog.i(TAG) { "onEventSwitchV2() write = $write ; result = $result" }
        if (result) {
            val open = event.isOpen
            SafeLog.i(TAG) { "onEventSwitchV2() open = $open" }
            bleInfo.open.value = open
        }
        if (write && connectFinish) {
            bleConnectResult?.updateUI()
        }
    }

    /**
     * 操作开关
     *
     * @param open
     * @param protocolVersion 协议版本
     */
    fun operateSwitch(open: Boolean, protocolVersion: Int) {
        val ble = absBle ?: return
        val controller = if (protocolVersion == 0) {
            SwitchController(open)
        } else {
            SwitchControllerV2(open)
        }
        AnalyticsRecorder.getInstance().recordUseCount(
            ParamKey.feast_music,
            ParamFixedValue.send_cmd_in_detail
        )
        ble.startController(controller)
    }

    /**
     * 操作亮度统一开关
     */
    fun operateBrightnessUniteOpen(open: Boolean) {
        val ble = absBle ?: return
        val controller = FeastBrightnessUniteController(open)
        ble.startController(controller)
        AnalyticsRecorder.getInstance().recordUseCount(
            ParamKey.feast_music,
            ParamFixedValue.send_cmd_in_detail
        )
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventBrightnessUnit(event: EventFeastBrightnessUnite) {
        try {
            SafeLog.i(TAG) { "onEventBrightnessUnit() " }
            val result = event.isResult
            SafeLog.i(TAG) { "onEventBrightnessUnit() result = $result" }
            if (!result) return
            val open = event.isOpen
            SafeLog.i(TAG) { "onEventBrightnessUnit() open = $open" }
            bleInfo.brightnessUniteOpen = open
        } finally {
            if (event.isWrite && connectFinish) {
                bleConnectResult?.updateUI()
            }
            absBle?.controllerEvent(event)
        }
    }

    /**
     * 操作灵敏度
     */
    fun operateSensitivity(sensitivity: Int) {
        val ble = absBle ?: return
        val musicMode: MusicMode = bleInfo.musicCode.value ?: return
        musicMode.sensitivity = sensitivity
        val controller = if (protocolVersion == 0) {
            MusicModeController(musicMode)
        } else {
            MusicModeControllerV2(musicMode)
        }
        AnalyticsRecorder.getInstance().recordUseCount(
            ParamKey.feast_music,
            ParamFixedValue.send_cmd_in_detail
        )
        ble.startController(controller)
    }

    /**
     * 操作动感柔和
     */
    fun operateDynamic(isDynamic: Boolean, subDeviceMusicEffect: ByteArray?) {
        val ble = absBle ?: return
        val musicMode: MusicMode = bleInfo.musicCode.value ?: return
        musicMode.isDynamic = isDynamic
        musicMode.subDeviceMusicEffect = subDeviceMusicEffect
        var controller: AbsSingleController?
        if (protocolVersion == 0) {
            controller = MusicModeController(musicMode)
            ble.startController(controller)
        } else {
            controller = MusicModeControllerV2(musicMode)
            this.modeController = controller
            sendSubDeviceMusicEffect()
        }
        AnalyticsRecorder.getInstance().recordUseCount(
            ParamKey.feast_music,
            ParamFixedValue.send_cmd_in_detail
        )
    }

    /**
     * 操作模式
     */
    fun operateMusicMode(
        modeCode: Byte,
        subDeviceMusicEffect: ByteArray?,
        isDynamic: Boolean
    ) {
        val ble = absBle ?: return
        val musicMode: MusicMode = bleInfo.musicCode.value ?: return
        musicMode.modeCode = modeCode
        musicMode.subDeviceMusicEffect = subDeviceMusicEffect
        musicMode.isDynamic = isDynamic
        val controller = if (protocolVersion == 0) {
            MusicModeController(musicMode)
        } else {
            MusicModeControllerV2(musicMode)
        }
        if (protocolVersion == 0) {
            ble.startController(controller)
        } else {
            this.modeController = controller
            val rgbSet = EffectColorConfig.read().getColor(gId)
            operateColor(rgbSet)
        }
        AnalyticsRecorder.getInstance().recordUseCount(
            ParamKey.feast_music,
            ParamFixedValue.send_cmd_in_detail
        )
    }

    /**旧的先写模式后写颜色 新的先写颜色后写模式*/
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventMusicMode(event: EventMusicMode) {
        absBle?.controllerEvent(event) ?: return
        SafeLog.i(TAG) { "onEventMusicMode() " }
        val result = event.isResult
        SafeLog.i(TAG) { "onEventMusicMode() connectFinish = $connectFinish ; result = $result" }
        if (!result) {
            if (!connectFinish) {
                bleConnectResult?.bleConnectFail()
            }
            readMsg4Snapshot = false
            return
        }
        val mode = event.mode
        bleInfo.musicCode.value = mode
        val write = event.isWrite
        SafeLog.i(TAG) { "onEventMusicMode() write = $write" }
        if (write) {
            SafeLog.i(TAG) { "onEventMusicMode() connectFinish = $connectFinish" }
            if (connectFinish) {
                /**设置子设备过程中设置模式*/
                if (inSetSubDevice) {
                    if (protocolVersion == 0) {
                        toApplyColor(EffectColorConfig.read().getColor(gId))
                    } else {
                        readSubDeviceConnectStatus()
                    }
                } else {
                    /**切模式*/
                    if (protocolVersion == 0) {
                        toApplyColor(EffectColorConfig.read().getColor(gId))
                    } else {
                        bleConnectResult?.updateEffect()
                    }
                }
            } else {
                if (protocolVersion == 0) {
                    toApplyColor(EffectColorConfig.read().getColor(gId))
                } else {
                    readSubDeviceConnectStatus()
                }
            }
        } else if (readMsg4Snapshot) {
            //读完什么都不做
            readMsg4Snapshot = false
        } else {
            toSetSubDevice(bleInfo.subDeviceList)
        }
    }

    /**
     * 设置音乐模式子设备效果
     */
    private fun sendSubDeviceMusicEffect() {
        absBle?.let { ble ->
            (modeController as? MusicModeControllerV2)?.musicMode?.subDeviceMusicEffect?.let { subDeviceEffect ->
                ble.startController(MusicSubDeviceEffectController(subDeviceEffect))
            }
        }
    }

    /**
     * @param event 设置子设备音乐模式效果
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSetSubDeviceMusicEffect(event: EventSubDeviceMusicModeEffect) {
        SafeLog.i(TAG) { "onEventSetSubDeviceMusicEffect() " }
        val ble = absBle?.also { it.controllerEvent(event) } ?: return
        val result = event.isResult
        SafeLog.i(TAG) { "onEventSetSubDeviceMusicEffect() result = $result" }
        if (!result) return
        SafeLog.i(TAG) { "onEventSetSubDeviceMusicEffect() 设置子设备音乐模式效果成功" }
        if (modeController != null) {
            ble.startController(modeController)
            modeController = null
        }
    }

    /**
     * 应用颜色，后续发模式包
     */
    fun operateColor(rgbSet: IntArray) {
        val musicMode = bleInfo.musicCode.value ?: return
        SafeLog.i(TAG) { "operateColor() " }
        /**新版发颜色 + 子设备模式包 + 模式 旧1162只发颜色*/
        if (protocolVersion == 1) {
            modeController = MusicModeControllerV2(musicMode)
        }
        toApplyColor(rgbSet)
    }

    /**
     * 应用颜色
     */
    fun toApplyColor(rgbSet: IntArray) {
        val ble = absBle ?: return
        SafeLog.i(TAG) { "toApplyColor() rgbSet.length = ${rgbSet.size}" }
        val multiMusicController = MultiMusicController(rgbSet)
        ble.sendMultipleControllerV1(multiMusicController)
        AnalyticsRecorder.getInstance().recordUseCount(
            ParamKey.feast_music,
            ParamFixedValue.send_cmd_in_detail
        )
    }

    /**
     * 设置多包颜色结果
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSetMultiMusicColorResult(event: EventSetMultiMusic) {
        absBle?.onMultipleControllerOk(event) ?: return
        val result = event.isResult
        SafeLog.i(TAG) { "onEventSetMultiMusicColorResult() result = $result ; connectFinish = $connectFinish" }
        if (!result) {
            if (!connectFinish) {
                SafeLog.i(TAG) { "onEventSetMultiMusicColorResult() 连接过程中设置多包颜色失败" }
                bleConnectResult?.bleConnectFail()
            }
            return
        }
        EffectColorConfig.read().putColor(gId, event.rgbSet)
        SafeLog.i(TAG) { "onEventSetMultiMusicColorResult() protocolVersion = $protocolVersion" }
        if (connectFinish) {
            if (protocolVersion == 0) {
                if (inSetSubDevice) {
                    readSubDeviceConnectStatus()
                } else {
                    bleConnectResult?.updateUI()
                }
            } else {
                if (modeController != null) {
                    sendSubDeviceMusicEffect()
                }
            }
            return
        }
        /**旧的设置完颜色开始读子设备连接状态*/
        if (protocolVersion == 0) {
            SafeLog.i(TAG) { "onEventSetMultiMusicColorResult() 连接过程中设置多包颜色成功，现在去读子设备连接状态" }
            readSubDeviceConnectStatus()
            return
        }
        if (modeController != null) {
            SafeLog.i(TAG) { "onEventSetMultiMusicColorResult() 连接过程中设置多包颜色成功，现在去设模式" }
            sendSubDeviceMusicEffect()
        }
    }

    /**
     * 清除所有子设备
     */
    fun toClearSubDevice() {
        val ble = absBle ?: return
        SafeLog.i(TAG) { "toClearSubDevice() " }
        val controller = if (protocolVersion == 0) {
            MovieClearSubDeviceController()
        } else {
            SubDeviceClearController()
        }
        ble.startController(controller)
        AnalyticsRecorder.getInstance().recordUseCount(
            ParamKey.feast_music,
            ParamFixedValue.send_cmd_in_detail
        )
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventMovieClearSubDevice(event: EventMovieClearSubDevice) {
        try {
            val result = event.isResult
            SafeLog.i(TAG) { "onEventMovieClearSubDevice() connectFinish = $connectFinish ; result = $result" }
            if (connectFinish) {
                /**后续操作通知子设备设置页面处理，这里不做处理*/
                Event2ClearSubDevice.sendEvent2ClearSubDevice(result)
                if (deleteFeast) {
                    deleteFeast = false
                    bleConnectResult?.feastDelete(result)
                }
                return
            }
            SafeLog.i(TAG) { "onEventMovieClearSubDevice() 连接过程清除子设备成功 此时去设置多包颜色和模式" }
            val rgbSet = EffectColorConfig.read().getColor(gId)
            /**生成一包模式包*/
            operateColor(rgbSet)
        } finally {
            absBle?.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent2SetSubDevice(event: Event2SetSubDevice) {
        SafeLog.i(TAG) { "onEvent2SetSubDevice() " }
        if (event.subDeviceList.isEmpty()) {
            toClearSubDevice()
        } else {
            toSetSubDevice(event.subDeviceList)
        }
    }

    /**
     * 设置子设备
     *
     * @param subDeviceList
     */
    fun toSetSubDevice(subDeviceList: MutableList<SubDevice>) {
        val ble = absBle ?: return
        SafeLog.i(TAG) { "toSetSubDevice() " }
        this.subDeviceList.clear()
        this.subDeviceList.addAll(subDeviceList)
        if (subDeviceList.isEmpty()) {
            toClearSubDevice()
        } else {
            val multiDeviceController = if (protocolVersion == 0) {
                MultiDeviceController(subDeviceList)
            } else {
                MultiDeviceControllerV2(subDeviceList)
            }
            ble.sendMultipleControllerV1(multiDeviceController)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSetDevice(event: EventSetDevice) {
        absBle?.onMultipleControllerOk(event) ?: return
        val result = event.isResult
        SafeLog.i(TAG) { "onEventSetDevice() result = $result" }
        if (!result) {
            SafeLog.i(TAG) { "onEventSetDevice() 连接过程设置子设备失败" }
            Event2SetSubDeviceResult.sendEvent2SetSubDeviceFail()
            return
        }
        SafeLog.i(TAG) { "onEventSetDevice() connectFinish = $connectFinish" }
        if (connectFinish) {
            updateSubDevice4Net()
            return
        }
        SafeLog.i(TAG) { "onEventSetDevice() 连接过程设置子设备成功，现在去设置多包颜色" }
        val subDeviceEffectByte = ByteArray(14)
        val musicCode = bleInfo.musicCode.value ?: return
        val effectByteList =
            Constant.getSubDeviceMusicEffect(bleInfo.subDeviceList, musicCode.modeCode.toInt(), 0)
        for ((index, effect) in effectByteList.withIndex()) {
            subDeviceEffectByte[index] = effect
        }
        val isDynamic = Constant.getDynamicByte(
            bleInfo.subDeviceList,
            musicCode.modeCode.toInt(),
            musicCode.isDynamic
        )
        SafeLog.i(TAG) { "onEventSetDevice() isDynamic = $isDynamic" }
        operateMusicMode(musicCode.modeCode, subDeviceEffectByte, isDynamic)
    }

    private fun updateSubDevice4Net() {
        SafeLog.i(TAG) { "updateSubDevice4Net() " }
        val area4Devices = Constant.changeSubDevice2AreaData(subDeviceList)

        /**蓝牙下设置成功，再同步到服务器上*/
        val regionRequest = FeastRegionRequest(transactions.createTransaction(), area4Devices, gId)
        Cache.get<IMomentNet>(IMomentNet::class.java)
            ?.setFeastArea(regionRequest)
            ?.enqueue(IHCallBack<FeastRegionResponse>(regionRequest))
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponseFeastRegion(response: FeastRegionResponse?) {
        if (!transactions.isMyTransaction(response)) return
        SafeLog.i(TAG) { "onResponseFeastRegion() " }
        bleInfo.subDeviceList.clear()
        bleInfo.subDeviceList.addAll(subDeviceList)
        bleInfo.subDeviceEmpty.value = bleInfo.subDeviceList.isEmpty()

        val mainDeviceBrightnessModel = bleInfo.brightnessModelList[0]
        bleInfo.brightnessModelList.clear()
        bleInfo.brightnessModelList.add(mainDeviceBrightnessModel)
        for (subDevice in subDeviceList) {
            val brightnessModel = BrightnessDialog.BrightnessModel(
                subDevice.subDevice.goodsType,
                subDevice.subDevice.sku,
                null,
                subDevice.subDevice.deviceName,
                100
            )
            bleInfo.brightnessModelList.add(brightnessModel)
        }
        /**读到设备恢复出厂,清除服务器数据即可，发模式包由连接过程后续执行*/
        if (deviceReset) {
            deviceReset = false
            return
        }

        val musicMode = bleInfo.musicCode.value ?: return
        val effectCode = musicMode.modeCode
        val subDeviceEffectByte = ByteArray(14)
        val effectByteList = Constant.getSubDeviceMusicEffect(subDeviceList, effectCode.toInt(), 0)
        for ((index, effect) in effectByteList.withIndex()) {
            subDeviceEffectByte[index] = effect
        }
        inSetSubDevice = true
        operateMusicMode(musicMode.modeCode, subDeviceEffectByte, musicMode.isDynamic)
    }

    /**
     * 读设备是否恢复出厂设置 若恢复出厂设置需要向服务器请求删掉所有子设备信息
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventDeviceNum(event: EventDeviceNum) {
        try {
            val result = event.isResult
            SafeLog.i(TAG) { "onEventDeviceNum() result = $result" }
            if (!result) return
            val deviceNum = event.deviceNum
            SafeLog.i(TAG) { "onEventDeviceNum() deviceNum = $deviceNum" }
            if (deviceNum <= 0) {
                bleInfo.subDeviceList.clear()
                bleInfo.subDeviceEmpty.value = true
                deviceReset = true
                /**向服务器请求删掉数据*/
                val regionRequest = FeastRegionRequest(
                    transactions.createTransaction(),
                    mutableListOf<Area4Device>(),
                    gId
                )
                Cache.get<IMomentNet>(IMomentNet::class.java)?.setFeastArea(regionRequest)
                    ?.enqueue(IHCallBack<FeastRegionResponse>(regionRequest))
            }
        } finally {
            absBle?.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onError(response: ErrorResponse) {
        if (!transactions.isMyTransaction(response)) return
        SafeLog.i(TAG) { "onError() " }
        ToastUtil.getInstance()
            .toast(if (response.isNetworkBroken) ResUtil.getString(R.string.network_anomaly) else response.message)
        Event2SetSubDeviceResult.sendEvent2SetSubDeviceFail()
    }

    /**
     * 主动上报子设备连接状态
     *
     * @param eventDeviceStatus
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventDeviceStatus(eventDeviceStatus: EventDeviceStatus) {
        val status = eventDeviceStatus.status
        SafeLog.i(TAG) { "onEventDeviceStatus() ${status.contentToString()}" }
        for ((index, subDevice) in bleInfo.subDeviceList.withIndex()) {
            subDevice.status = status[index].toInt()
        }
        bleConnectResult?.updateUI()
    }

    /**
     * 操作子设备连接和断开
     */
    fun operateSubDevice(connect: Boolean, pos: Int) {
        val ble = absBle ?: return
        val controller = if (protocolVersion == 0) {
            MovieSubDeviceController(if (connect) 1 else 0, pos)
        } else {
            SubDeviceConnectController(if (connect) 1 else 0, pos)
        }
        ble.startController(controller)
        AnalyticsRecorder.getInstance().recordUseCount(
            ParamKey.feast_music,
            ParamFixedValue.send_cmd_in_detail
        )
    }

    /**
     * 读子设备连接状态
     */
    fun readSubDeviceConnectStatus() {
        val ble = absBle ?: return
        val controller = if (protocolVersion == 0) {
            MovieSubDeviceController()
        } else {
            SubDeviceConnectController()
        }
        ble.startController(controller)
    }

    /**
     * 子设备连接状态
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onSubDeviceConnectEvent(event: EventMovieSubDeviceConnect) {
        val ble = absBle?.also { it.controllerEvent(event) } ?: return
        val result = event.isResult
        if (!result) return
        val write = event.isWrite
        SafeLog.i(TAG) { "onSubDeviceConnectEvent() write = $write" }
        if (write) {
            val connect = event.connect
            val pos = event.pos
            SafeLog.i(TAG) { "onSubDeviceConnectEvent() pos ' $pos ; connect = $connect" }
            bleInfo.subDeviceList.getOrNull(pos)?.let { subDevice ->
                subDevice.status = if (connect == 0) 0 else 1
                bleConnectResult?.updateUI()
            }
            return
        }

        val status = event.status
        for ((index, subDevice) in bleInfo.subDeviceList.withIndex()) {
            subDevice.status = status[index].toInt()
        }
        /**读完再去读一包亮度(不区分新旧版本，统一去读)*/
        ble.startController(if (protocolVersion == 0) MusicBrightnessController() else FeastBrightnessController())
    }

    /**
     * 操作亮度
     */
    fun operateBrightness(brightness: Int, index: Int) {
        val ble = absBle ?: return
        val controller = if (protocolVersion == 0) {
            MusicBrightnessController(brightness, index)
        } else {
            FeastBrightnessController(brightness, index)
        }
        AnalyticsRecorder.getInstance().recordUseCount(
            ParamKey.feast_music,
            ParamFixedValue.send_cmd_in_detail
        )
        ble.startController(controller)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventMusicBrightness(event: EventMusicBrightness) {
        absBle?.controllerEvent(event) ?: return
        val result = event.isResult
        SafeLog.i(TAG) { "onEventMusicBrightness() result = $result" }
        if (!result) return
        val write = event.isWrite
        SafeLog.i(TAG) { "onEventMusicBrightness() " }
        if (write) {
            val brightness = event.brightness
            val index = event.index
            SafeLog.i(TAG) { "onEventMusicBrightness() index = $index ; brightness = $brightness" }
            bleInfo.brightnessModelList.getOrNull(index)?.brightness = brightness
            bleConnectResult?.updateUI()
            return
        }
        val brightnessArray = event.brightnessArray
        for ((index, mode) in bleInfo.brightnessModelList.withIndex()) {
            mode.brightness = brightnessArray[index]
        }
        if (inSetSubDevice) {
            inSetSubDevice = false
            Event2SetSubDeviceResult.sendEvent2SetSubDeviceSuccess(subDeviceList)
            return
        }
        SafeLog.i(TAG) { "onEventMusicBrightness() 连接过程结束，连接成功" }
        connectFinish = true
        bleConnectResult?.bleConnectSuccess()
    }

    /**
     * @param event 1162的心跳包 同步开关状态和电量
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventHeart(event: HeartControllerWithBattery.EventHeart) {
        val result = event.isResult
        SafeLog.i(TAG) { "onEventHeart() result = $result" }
        if (!result) return
        val resourceId = Constant.getResourceIdByBattery(event.batteryLevelWithCharge)
        if (bleInfo.batteryRes.value != resourceId) {
            bleInfo.batteryRes.value = resourceId
        }
        val open = event.isOpen
        if (bleInfo.isFeastOpen() != open) {
            bleInfo.open.value = open
            bleConnectResult?.updateUI()
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onUpdateResultEvent(event: UpdateResultEvent) {
        val result = event.isResult
        SafeLog.i(TAG) { "onUpdateResultEvent() result = $result" }
        if (!result) return
        /**ota升级完成,断开蓝牙，重新进行扫描连接*/
        startScan()
    }

    /**
     * @param event 别的灯具心跳包 同步开关状态
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventMovieHeart(event: EventFeastHeart) {
        val result = event.isResult
        SafeLog.i(TAG) { "onEventMovieHeart() result = $result" }
        if (!result) return
        val feastOpen = event.isFeastOpen
        SafeLog.i(TAG) { "onEventMovieHeart() feastOpen = $feastOpen" }
        if (feastOpen != bleInfo.isFeastOpen()) {
            /**开关发生变化*/
            bleInfo.open.value = feastOpen
            bleConnectResult?.updateUI()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventMusicFeastOnOffNotify(event: EventMovieFeastOnOffNotify) {
        val open = event.isOpen
        SafeLog.i(TAG) { "onEventMusicFeastOnOffNotify() open = $open" }
        if (bleInfo.isFeastOpen() != open) {
            bleInfo.open.value = open
            bleConnectResult?.updateUI()
        }
    }

    override fun onDestroy(owner: LifecycleOwner) {
        eventBusRegisterOp(false)
        ScanManager.getInstance().stopFoundDevice()
        BleController.getInstance().toBtClose()
        absBle?.let {
            it.stopHeart()
            it.registerEvent(false, <EMAIL>)
            it.release()
        }
        iMusicFeastOp = null
        bleConnectResult = null
        mainHandler.removeCallbacksAndMessages(null)
        Event2ConnectInDetail.sendEvent2ConnectInDetail()
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onBtStatusEvent(event: BTStatusEvent) {
        if (event.isBtOpen) {
            SafeLog.i(TAG) { "onBtStatusEvent() 蓝牙重新连接成功!" }
            mainHandler.removeMessages(what_bt_open_retry_connect_ble)
            mainHandler.sendEmptyMessageDelayed(what_bt_open_retry_connect_ble, 1000L)
        }
    }

    interface BleConnectResult {
        fun bleConnecting()

        fun bleConnectSuccess()

        fun bleConnectFail()

        fun updateUI()

        fun updateEffect() //和updateUI的拆出来是因为别的操作刷ui会刷新效果的分组选中

        fun feastDelete(result: Boolean)
    }

    /**
     * 应用快照
     */
    fun applySnapshot(controller: IComposeController?) {
        absBle?.sendComposeController(controller)
    }

    /**
     * 应用完快照用读所有数据
     */
    fun sendReadMsg4Snapshot() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "sendReadMsg4Snapshot()")
        }
        val controllers = getReadControllers4Snapshot()
        val list = ArrayList(listOf(*controllers))
        readMsg4Snapshot = true
        absBle?.startController(*list.toTypedArray<AbsSingleController>())
    }

    private fun getReadControllers4Snapshot(): Array<AbsSingleController> {
        SafeLog.i(TAG) { "getReadControllers4Snapshot() protocolVersion = $protocolVersion" }
        val controllers: Array<AbsSingleController> =
            //迭代前后协议不同 需要的信息也不同
            if (protocolVersion == 1) {
                //迭代后
                arrayOf(
                    FeastBrightnessUniteController(),
                    MusicModeControllerV2(),
                    FeastBrightnessController()
                )
            } else {
                arrayOf(
                    MusicModeController(),
                    MusicBrightnessController()
                )
            }
        return controllers
    }
}
