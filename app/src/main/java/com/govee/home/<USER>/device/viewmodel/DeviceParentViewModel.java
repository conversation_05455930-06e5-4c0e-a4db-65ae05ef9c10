package com.govee.home.main.device.viewmodel;

import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.text.TextUtils;

import com.govee.base2home.BaseViewModel;
import com.govee.base2home.Constant;
import com.govee.base2home.account.AccountEvent;
import com.govee.base2home.account.config.ClientConfig;
import com.govee.base2home.account.config.PushTokenConfig;
import com.govee.base2home.account.net.IAccountNet;
import com.govee.base2home.account.net.SetClientRequest;
import com.govee.base2home.account.net.SetClientResponse;
import com.govee.base2home.account.net.SetPushTokenRequest;
import com.govee.base2home.account.net.SetPushTokenResponse;
import com.govee.base2home.client.AppInfoConfig;
import com.govee.base2home.community.msg.BannerConfig;
import com.govee.base2home.config.DeviceRoomOrderConfig;
import com.govee.base2home.config.OrderConfig;
import com.govee.base2home.device.BindDeviceM;
import com.govee.base2home.device.DeviceNewVersionConfig;
import com.govee.base2home.device.EventBindNextDevice;
import com.govee.base2home.device.IDeviceNet;
import com.govee.base2home.device.OfflineDevicePointsReportConfig;
import com.govee.base2home.device.net.DeviceBindRequest;
import com.govee.base2home.device.net.DeviceBindResponse;
import com.govee.base2home.device.net.DeviceListResponse;
import com.govee.base2home.device.net.DeviceVersionInfoResponse;
import com.govee.base2home.device.net.FirstAddDeviceTopicRequest;
import com.govee.base2home.device.net.FirstAddDeviceTopicResponse;
import com.govee.base2home.event.AddDeviceEvent;
import com.govee.base2home.h5.WebActivity;
import com.govee.base2home.iot.Iot;
import com.govee.base2home.iot.share.ShareController;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.AbsGroup;
import com.govee.base2home.main.AccountDeviceM;
import com.govee.base2home.main.DeviceAccountSetting;
import com.govee.base2home.main.DeviceListConfig;
import com.govee.base2home.main.EventAbsDeviceChange;
import com.govee.base2home.main.OfflineDeviceListConfig;
import com.govee.base2home.main.about.AgreeShowEvent;
import com.govee.base2home.main.about.AgreementM;
import com.govee.base2home.main.ble.BleBroadcastController;
import com.govee.base2home.main.choose.DeviceFilter;
import com.govee.base2home.main.gw.Config4Gateway;
import com.govee.base2home.main.gw.GwInfo;
import com.govee.base2home.main.gw.GwOnlineM;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.push.EventDeviceListFresh;
import com.govee.base2home.push.net.IPushNet;
import com.govee.base2home.push.net.SyncPushSkusRequest;
import com.govee.base2home.push.net.SyncPushSkusResponse;
import com.govee.base2home.qa.Qa;
import com.govee.base2home.qa.QaManager;
import com.govee.base2home.qa.QaReadEvent;
import com.govee.base2home.qa.QaSkipEvent;
import com.govee.base2home.settings.NewConfigManager;
import com.govee.base2home.util.UuidV1;
import com.govee.base2home.vip.VipM;
import com.govee.base2light.ac.diyNew.manager.DiyManagerVM;
import com.govee.bind.SafeBindMgr;
import com.govee.device.DeviceInfoManager;
import com.govee.device.ExtDeviceInfoManager;
import com.govee.gateway.gw.net.GwListRequest;
import com.govee.gateway.gw.net.GwListResponseV2;
import com.govee.gateway.gw.net.GwSupportRequest;
import com.govee.gateway.gw.net.GwSupportResponse;
import com.govee.gateway.gw.net.IGwNet;
import com.govee.home.IhApplication;
import com.govee.home.account.config.AccountConfig;
import com.govee.home.main.device.Config4TabDevice;
import com.govee.home.main.device.ConfigSyncPushSku;
import com.govee.home.main.device.DeviceListExitEditEvent;
import com.govee.home.main.device.DeviceListExitEditResultEvent;
import com.govee.home.main.device.Event4ManualRequestDeviceList;
import com.govee.home.main.device.Event4TokenInvalid;
import com.govee.home.main.device.EventAdHide;
import com.govee.home.main.device.EventUnbindDevice;
import com.govee.home.main.device.Fragment4DeviceOp;
import com.govee.home.main.device.GroupData;
import com.govee.home.main.device.ViewModel4DeviceAll;
import com.govee.kt.net.DeviceListRequestService;
import com.govee.kt.net.IDeviceListRequestService;
import com.govee.kt.ui.device.CardStyle;
import com.govee.kt.ui.device.DevicesOp;
import com.govee.kt.ui.device.util.DeviceListEditManager;
import com.govee.kt.ui.device.util.DeviceListEditSharedKey;
import com.govee.mvvm.network.AppException;
import com.govee.ui.R;
import com.govee.widget.manager.WidgetHelper;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.Cache;
import com.ihoment.base2app.cookie.RefreshDeviceListEvent;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;
import com.ihoment.base2app.infra.SharedPreManager;
import com.ihoment.base2app.network.BaseRequest;
import com.ihoment.base2app.network.ErrorResponse;
import com.ihoment.base2app.network.Network;
import com.ihoment.base2app.util.CaughtRunnable;
import com.ihoment.base2app.util.DeviceUtil;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.ResUtil;
import com.ihoment.base2app.util.ThreadPoolUtil;
import com.liveeventbus.LiveEventBus;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.WorkerThread;
import androidx.lifecycle.MutableLiveData;


public class DeviceParentViewModel extends BaseViewModel {
    private static final String TAG = "DeviceParentViewModelKt";

    private static final int WHAT_HIDE_LOADING = 10000;

    public static final String LIVE_EVENT_BUS_NOTIFY_LIGHT_EFFECT_DATA = "LIVE_EVENT_BUS_NOTIFY_LIGHT_EFFECT_DATA";

    public MutableLiveData<List<GroupData>> groupDataList = new MutableLiveData<>();
    public MutableLiveData<Boolean> requestResult = new MutableLiveData<>();
    public MutableLiveData<Hint> hint = new MutableLiveData<>();
    public MutableLiveData<Boolean> deviceDataLoading = new MutableLiveData<>();
    public MutableLiveData<Boolean> versionRefresh = new MutableLiveData<>();
    public MutableLiveData<Long> feastLastUpdateTime = new MutableLiveData<>();


    private boolean dataLoaded;/*标志位-数据是否加载过*/
    private final Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            doWhat(msg.what);
        }
    };

    private void doWhat(int what) {
        if (what == WHAT_HIDE_LOADING) {
            deviceDataLoading.setValue(false);
        }
    }

    public static class Hint {
        public boolean hadPushDevice;
        public boolean hadBleDevice;
        public boolean needBroadcastDataDevice;

        public Hint(boolean hadPushDevice, boolean hadBleDevice, boolean needBroadcastDataDevice) {
            this.hadPushDevice = hadPushDevice;
            this.hadBleDevice = hadBleDevice;
            this.needBroadcastDataDevice = needBroadcastDataDevice;
        }
    }

    private DeviceParentViewModel() {
        registerEventBus();
    }

    private static class Builder {
        private static final DeviceParentViewModel instance = new DeviceParentViewModel();
    }

    public static DeviceParentViewModel getInstance = Builder.instance;

    public void firstDeviceListRequest() {
        boolean hadToken = AccountConfig.read().isHadToken();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "firstDeviceListRequest() hadToken = " + hadToken);
        }
        ThreadPoolUtil.getThreadPool().execute(new CaughtRunnable() {
            @Override
            protected void runSafe() {
                /*若当前未登录-则展示本地离线设备*/
                if (!hadToken) {
                    localDevices();
                    return;
                }
                bindOfflineDevices();
            }
        });
    }

    private void toBindDevices(AbsDevice absDevice, HashSet<String> offlineDevicePointsReportSet) {
        SafeLog.Companion.i(TAG, () -> "toBindDevices() 绑定支持默认绑定接口的设备 sku = " + absDevice.getSku() + " ; goodsType = " + absDevice.getGoodsType());
        int pointsAdded = 0;
        if (offlineDevicePointsReportSet != null) {
            String offlineDevicePointsReportKey = UuidV1.getOfflineDevicePointsReportKey(absDevice);
            if (offlineDevicePointsReportSet.contains(offlineDevicePointsReportKey)) {
                pointsAdded = Constant.points_added_value;
            }
        }
        DeviceBindRequest request = new DeviceBindRequest(transactions.createTransaction(), absDevice, pointsAdded);
        Cache.get(IDeviceNet.class).bindDevice(request).enqueue(new Network.IHCallBack<>(request));
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onDeviceBindResponse(DeviceBindResponse response) {
        if (!transactions.isMyTransaction(response)) return;
        DeviceBindRequest deviceBindRequest = response.getRequest();
        String sku = deviceBindRequest.sku;
        String device = deviceBindRequest.device;
        OfflineDeviceListConfig offlineDeviceListConfig = OfflineDeviceListConfig.read();
        /*移除已绑定的设备*/
        offlineDeviceListConfig.removeBoundDevice(sku, device);
        /*绑定离线设备*/
        bindOfflineDevices();
    }


    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void addDeviceEvent(AddDeviceEvent event) {
        boolean firstPostGuide = SharedPreManager.getInstance().getBoolean(Fragment4DeviceOp.KEY_ADD_FIRST_DEVICE_POST_GUIDE_SHOW, false);
        if (!firstPostGuide) {
            if (event.getSku() == null || event.getDevice() == null) return;
            FirstAddDeviceTopicRequest request = new FirstAddDeviceTopicRequest(event.getSku(), event.getDevice(), transactions.createTransaction());
            Cache.get(IDeviceNet.class).getFirstAddDeviceTopic(request.getSku()).enqueue(new Network.IHCallBack<>(request));
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onFirstAddDeviceTopiResponse(FirstAddDeviceTopicResponse response) {
        if (!transactions.isMyTransaction(response)) return;
        if (null != response.getData()) {
            SafeLog.i(TAG, "response.getData()====" + response.getData().getTopicId());
            //-1，不存在相关话题，return
            if (response.getData().getTopicId() == -1) return;
            FirstAddDeviceTopicRequest firstAddDeviceTopicRequest = response.getRequest();
            JSONObject object = new JSONObject();
            try {
                object.put("sku", firstAddDeviceTopicRequest.getSku());
                object.put("device", firstAddDeviceTopicRequest.getDevice());
                object.put("topicId", response.getData().getTopicId());
                SharedPreManager.getInstance().saveString(Fragment4DeviceOp.KEY_ADD_FIRST_DEVICE_POST_GUIDE_DATA, object.toString());
            } catch (JSONException e) {
                throw new RuntimeException(e);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventBindNextDevice(EventBindNextDevice event) {
        boolean isError = event.isError;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBindNextDevice() isError = " + isError);
        }
        if (isError) {
            /*绑定失败；直接拉取设备列表*/
            requestServiceDeviceList("绑定失败；直接拉取设备列表");
            return;
        }
        /*绑定离线设备*/
        bindOfflineDevices();
    }

    @WorkerThread
    private void bindOfflineDevices() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "bindOfflineDevices()");
        }
        List<AbsDevice> devices = OfflineDeviceListConfig.read().getDevices();
        if (devices == null || devices.isEmpty()) {
            /*显示本地数据*/
            DeviceListResponse deviceListResponse = DeviceRoomOrderConfig.read().getResponse();
            if (deviceListResponse != null && !deviceListResponse.isNoDevices()) {
                postDeviceListResponse(deviceListResponse);
            }
            /*无本地添加的设备，直接获取列表*/
            requestServiceDeviceList("无本地添加的设备，直接获取列表");
        } else {
            for (AbsDevice device : devices) {
                SafeLog.d(TAG, () -> "要绑定的离线设备信息：" + device.getSku());
            }
            // 记录已上报的离线设备信息
            HashSet<String> offlineDevicePointsReportSet = OfflineDevicePointsReportConfig.read().offlineDevicePointsReportSet;
            if (LogInfra.openLog()) {
                for (String key : offlineDevicePointsReportSet) {
                    SafeLog.Companion.i(TAG, () -> "bindOfflineDevices() 记录已上报的离线设备信息 key = " + key);

                }
            }
            AbsDevice absDevice = devices.get(devices.size() - 1);
            boolean supportLibBindDevice = BindDeviceM.getInstance.supportLibBindDevice(absDevice, offlineDevicePointsReportSet);
            SafeLog.Companion.i(TAG, () -> "bindOfflineDevices() sku  =  " + absDevice.getSku() + ",supportLibBindDevice = " + supportLibBindDevice);
            if (!supportLibBindDevice) {
                /*不支持子lib绑定，采用默认绑定*/
                toBindDevices(absDevice, offlineDevicePointsReportSet);
            }

        }
    }

    /**
     * 手动下拉请求设备列表
     */
    public void manualRequestDeviceList() {
        LogInfra.Log.i(TAG, "manualRequestDeviceList()");
        requestServiceDeviceList("手动下拉请求设备列表", true);
        Event4ManualRequestDeviceList.sendEvent();
        if (!BleBroadcastController.getInstance().isStartScan()) {
            SafeLog.d("BleBroadcastController", () -> "未开启蓝牙扫描！");
        }
    }

    private void requestServiceDeviceList(String flag) {
        requestServiceDeviceList(flag, false);
    }


    /**
     * 请求服务器设备列表数据
     */
    private void requestServiceDeviceList(String flag, boolean manualRequest) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "requestServiceDeviceList() manualRequest = " + manualRequest);
        }
        DeviceListRequestService.INSTANCE.requestDeviceList(flag,
                new IDeviceListRequestService() {
                    @Override
                    public void onSuccess(DeviceListResponse response) {
                        if (response != null) {
                            updateDevicesListResponse(response);

                        }
                    }

                    @Override
                    public void onFail(@NonNull AppException appException) {
                        DeviceListResponse deviceListResponse = DeviceRoomOrderConfig.read().getResponse();
                        transformGroupData(deviceListResponse);
                        requestResult.postValue(false);
                        dataLoaded = true;
                        deviceDataLoadingChange(false);
                    }
                }

        );
        /*是否支持网关*/
        GwSupportRequest gwSupportRequest = new GwSupportRequest(transactions.createTransaction());
        Cache.get(IGwNet.class).getGwSupport().enqueue(new Network.IHCallBack<>(gwSupportRequest));
        /*获取Gateway 列表，扫描添加页，需要过滤已添加的带蓝牙的网关*/
        GwListRequest gwListRequest = new GwListRequest(transactions.createTransaction());
        Cache.get(IGwNet.class).getGwListV2().enqueue(new Network.IHCallBack<>(gwListRequest));
        //拉设备列表的同时通知拉灯效数据
        LiveEventBus.get(LIVE_EVENT_BUS_NOTIFY_LIGHT_EFFECT_DATA).post(true);
    }

    @WorkerThread
    private void localDevices() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "localDevices()");
        }
        List<AbsDevice> devices = noLoginDevice();
        /*上报离线设备添加积分*/
        VipM.getInstance.reportOfflineDevicePoints(devices);
    }

    private List<AbsDevice> noLoginDevice() {
        SafeLog.i(TAG, () -> "noLoginDevice() ");
        List<AbsDevice> devices = OfflineDeviceListConfig.read().getDevices();
        List<AbsGroup> defGroups = AbsGroup.getDefGroups();
        DeviceListResponse response = new DeviceListResponse();
        response.devices = devices;
        response.groups = defGroups;
        postDeviceListResponse(response);
        return devices;
    }

    /**
     * 通知list响应事件
     */
    private void postDeviceListResponse(@NonNull DeviceListResponse response) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "postDeviceListResponse()");
        }
        /*标记是本地数据*/
        response.isLocal = true;
        EventBus.getDefault().post(response);
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onDevicesListResponse(DeviceListResponse response) {
        updateDevicesListResponse(response);
    }

    /**
     * 更新设备列表数据
     *
     * @param response      设备列表data
     */
    private void updateDevicesListResponse(DeviceListResponse response) {
        boolean isLocal = response.isLocal;

        List<AbsDevice> devices = response.devices;
        // 把后台返回的设备信息存储进数据库
        DeviceInfoManager.INSTANCE.saveDeviceInfoByNetwork(devices);
        // 仅当本地数据未登录和服务器数据需要 同步设备列表顺序
        if (isLocal) {
            if (!AccountConfig.read().isHadToken()) {
                SafeLog.Companion.i(TAG, () -> "updateDevicesListResponse() 本地数据未登录 ");
                OrderConfig.config().syncListOrder(devices, new ArrayList<>());
            }
        } else {
            /*同步服务器列表顺序*/
            SafeLog.Companion.i(TAG, () -> "updateDevicesListResponse() 同步服务器列表顺序 ");
            OrderConfig.config().syncListOrder(devices, response.sort == null ? new ArrayList<>() : response.sort);
        }
        /*记录当前账户支持的设备信息*/
        DeviceListConfig.read().initDeviceList(devices);
        IhApplication.getIhApplication().beDevicesList();
        DeviceRoomOrderConfig.read().saveDeviceListResponse(response);
        DeviceAccountSetting accountSetting = response.getAccountSetting();
        SafeLog.Companion.i(TAG, () -> "updateDevicesListResponse()  isLocal = " + isLocal + " isEditState = " + DeviceListEditManager.INSTANCE.isEditState());
        if (accountSetting != null) {
            SafeLog.Companion.i(TAG, () -> "updateDevicesListResponse()  cardStyle =  " + accountSetting.getStyle());
            if (!isLocal && CardStyle.Companion.fromStyle(accountSetting.getStyle()) != null && !DeviceListEditManager.INSTANCE.isEditState()) {
                DeviceListEditSharedKey.INSTANCE.setCardStyle(accountSetting.getStyle());
                DeviceListEditManager.INSTANCE.setCardStyle(accountSetting.getStyle());
            }
        }
        /*
         * 1. 上报设备信息给服务器
         * 2. 同步gidConfirm到内存（共用遍历）
         * */
        syncPushSkusAndGidConfirm(devices);
        /*同步本地分享设备信息*/
        ShareController.INSTANCE.syncShareDevices(devices);
        /*更新小组件设备列表*/
        WidgetHelper.getInstance().checkDeviceList(devices);
        transformGroupData(response);
        findFirstAddDeviceName(devices, null);
        dataLoaded = true;
        requestResult.postValue(true);

        SafeLog.i(TAG, () -> "onDevicesListResponse hasFeastMainDevice");
        feastLastUpdateTime.postValue(response.feastLastUpdateTime);
    }

    private void findFirstAddDeviceName(List<AbsDevice> deviceList, List<GwInfo> gwInfoList) {
        boolean firstPostGuide = SharedPreManager.getInstance().getBoolean(Fragment4DeviceOp.KEY_ADD_FIRST_DEVICE_POST_GUIDE_SHOW, false);
        if (firstPostGuide) return;
        String data = SharedPreManager.getInstance().getString(Fragment4DeviceOp.KEY_ADD_FIRST_DEVICE_POST_GUIDE_DATA, "");
        if (data.isEmpty()) return;
        try {
            JSONObject jsonObject = new JSONObject(data);
            String sku = jsonObject.getString("sku");
            String device = jsonObject.getString("device");
            if (null != deviceList && !deviceList.isEmpty()) {
                for (AbsDevice absDevice : deviceList) {
                    if (absDevice.getSku().equals(sku) && absDevice.getDevice().equals(device)) {
                        LiveEventBus.get(Fragment4DeviceOp.LIVE_EVEN_BUS_KEY_FIRST_DEVICE_POST_GUIDE).post(absDevice.getDeviceName());
                    }
                }
            }
            if (null != gwInfoList && !gwInfoList.isEmpty()) {
                for (GwInfo gwInfo : gwInfoList) {
                    if (gwInfo.sku.equals(sku) && gwInfo.device.equals(device)) {
                        LiveEventBus.get(Fragment4DeviceOp.LIVE_EVEN_BUS_KEY_FIRST_DEVICE_POST_GUIDE).post(gwInfo.deviceName);
                    }
                }
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onDeviceVersionInfoResponse(DeviceVersionInfoResponse response) {
        if (!transactions.isMyTransaction(response)) return;
        List<DeviceVersionInfoResponse.NewVersionInfo> data = response.getData();
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "DeviceVersionInfoResponse:" + (data != null ? data.size() : 0));
        }
        if (data != null) {
            DeviceNewVersionConfig.read().setVersionDataList(DeviceNewVersionConfig.transformList(data));
        }
        versionRefresh.postValue(true);
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onGwListV2Response(GwListResponseV2 response) {
        if (!transactions.isMyTransaction(response)) return;
        List<GwInfo> data = response.data.gateways;
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "onGwListResponse:" + (data != null ? data.size() : 0));
        }
        /*同步更新网关信息的已添加和已移除*/
        if (data == null || data.isEmpty()) {
            Config4Gateway.getConfig().updateKeys(new ArrayList<>());
        } else {
            List<String> keys = new ArrayList<>();
            for (GwInfo gwInfo : data) {
                int goodsType = gwInfo.goodsType;
                keys.add(goodsType == GoodsType.GOODES_TYPE_NO_SUPPORT ? gwInfo.sku : String.valueOf(goodsType));
            }
            Config4Gateway.getConfig().updateKeys(keys);
        }
        findFirstAddDeviceName(null, data);
        DeviceFilter.getInstance.updateGwBleAddress(data);
        DeviceFilter.getInstance.setGwInfoList(data);
        GwOnlineM.realIotGwSync(data);
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventUnBindDevice(EventUnbindDevice event) {
        String sku = event.getSku();
        String unbindDevice = event.getDevice();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventUnBindDevice() sku = " + sku + " ; unbindDevice = " + unbindDevice);
        }
        int[] orders = event.getOrders();
        /*从账户设备列表中移除解绑的sku*/
        if (orders != null && orders.length > 0) {
            DeviceListConfig.read().removeMainWithSub(sku, unbindDevice, orders);
        } else {
            DeviceListConfig.read().remove(sku, unbindDevice);
        }
        AccountDeviceM.getInstance.checkUnbindSku(sku);
        OfflineDeviceListConfig.read().removeBoundDevice(sku, unbindDevice);
        /*删除本地设备列表存储*/
        DeviceRoomOrderConfig.read().removeDevice(sku, unbindDevice);
        // 删除设备信息数据库缓存
        ExtDeviceInfoManager.INSTANCE.deleteExtDeviceInfo(sku, unbindDevice);
        /*获取所在的gId*/
        List<GroupData> value = groupDataList.getValue();
        if (value == null) {
            SafeLog.i(TAG, () -> "onEventUnBindDevice() empty!");
            handler.post(() -> groupDataList.setValue(new ArrayList<>()));
            return;
        }
        List<GroupData> newValue = new ArrayList<>(value);
        for (GroupData groupData : newValue) {
            groupData.removeDevices(sku, unbindDevice, orders);
            boolean hadDeviceList = groupData.hadDeviceList();
            SafeLog.i(TAG, () -> "onEventUnBindDevice() id = " + groupData.getGroupId() + " ; hadDeviceList = " + hadDeviceList);
        }
        handler.post(() -> groupDataList.setValue(newValue));
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onLogoutResultEvent(AccountEvent.LogoutResultEvent event) {
        if (!event.isResult()) return;
        LogInfra.Log.i(TAG, "onLogoutResultEvent()");
        beLogout();
    }

    @WorkerThread
    private void beLogout() {
        // 退出登录后把设备列表
        /*更改上次房间分组选中默认ALL*/
        Config4TabDevice.readConfig().saveChooseGroupId(-1);
        noLoginDevice();
        /*清除广告*/
        LogInfra.Log.e("AdBanner", "beLogout: 清除广告");
        NewConfigManager.getInstance().onDestroy();
        BannerConfig.readConfig().clearAds();
        EventAdHide.sendEventAdHide();
        GwOnlineM.logout();
        DevicesOp.INSTANCE.deviceTransform(new ArrayList<>(), false);
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEvent4TokenInvalid(Event4TokenInvalid event) {
        LogInfra.Log.i(TAG, "onEvent4TokenInvalid()");
        beLogout();
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onLoginResultEvent(AccountEvent.LoginResultEvent event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onLoginResultEvent() result = " + result);
        }
        if (result) {
            /*如果当前处于编辑态，登录成功保存当前编辑数据退出编辑态*/
            SafeLog.Companion.i(TAG, () -> "onLoginResultEvent() isEditState = " + (DeviceListEditManager.INSTANCE.isEditState()));
            DeviceInfoManager.INSTANCE.syncOffLineDeviceList();
            if (DeviceListEditManager.INSTANCE.isEditState()) {
                LiveEventBus.get(ViewModel4DeviceAll.LIVE_EVEN_BUS_KEY_DEVICE_LIST_EXIT_EDIT_STATE).post(new DeviceListExitEditEvent(true, false, false));
            } else {
                handlerLoginIn();
            }
        }
    }


    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onLoginResultEvent(DeviceListExitEditResultEvent event) {
        SafeLog.Companion.i(TAG, () -> "onLoginResultEvent() isInitiative = " + event.isInitiative());
        if (!event.isInitiative()) {
            handlerLoginIn();

        }
    }


    private void handlerLoginIn() {
        /*尝试登录成功-即立马连接iot*/
        Iot.getInstance.toConnectIot();
        LogInfra.Log.i(TAG, "onLoginResultEvent()--登录成功-立马去尝试连接IOT");
        dataLoaded = false;
        NewConfigManager.getInstance().onDestroy();
        BannerConfig.readConfig().clearAds();
        //本地DIY依赖本地sku列表进行的数据存储。需要在OfflineDeviceListConfig清空前进行本地DIY同步
        DiyManagerVM.Companion.syncLocalDiy2Service();
        bindOfflineDevices();
        checkClientInfoReport();
    }


    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onGwSupportResponse(GwSupportResponse response) {
        if (!transactions.isMyTransaction(response)) return;
        boolean online = response.isOnline();
        boolean needShow = response.needShow();
        SafeLog.i(TAG, () -> "onGwSupportResponse() online = " + online + " ; needShow = " + needShow);
        GwOnlineM.gwSync(needShow, online);
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventDeviceListFresh(EventDeviceListFresh event) {
        boolean hadToken = AccountConfig.read().isHadToken();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDeviceListFresh() hadToken = " + hadToken);
        }
        if (!hadToken) return;
        requestServiceDeviceList("EventDeviceListFresh");
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onRefreshDeviceList(RefreshDeviceListEvent event) {
        boolean hadToken = AccountConfig.read().isHadToken();
        if (!hadToken) return;
        SafeLog.d("SaveDeviceInfoInterceptor", () -> "bind device success-------->send RefreshDeviceListEvent");
        requestServiceDeviceList("RefreshDeviceListEvent");
    }

    /**
     * 用于更新本地设备信息-确保切换房间tab后数据获取最新
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventAbsDeviceChange(EventAbsDeviceChange event) {
        String settingStr = event.settingStr;
        if (TextUtils.isEmpty(settingStr)) return;
        String sku = event.sku;
        String device = event.device;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventAbsDeviceChange() sku = " + sku + " ; device = " + device);
        }
        boolean changeDevice = DeviceRoomOrderConfig.read().changeDevice(sku, device, settingStr);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventAbsDeviceChange() changeDevice = " + changeDevice);
        }
        if (!changeDevice) return;
        /*若配置信息发生变更-通知设备列表刷新*/
        transformGroupData(DeviceRoomOrderConfig.read().getResponse());
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onSyncPushSkuResponse(SyncPushSkusResponse response) {
        if (!transactions.isMyTransaction(response)) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onSyncPushSkuResponse()");
        }
        AppInfoConfig.read().appInfoChange(BaseApplication.getContext(), true);
        SyncPushSkusRequest request = response.getRequest();
        ConfigSyncPushSku.read().save(request.pushToken, request.devices);
    }

    @Override
    protected void onErrorResponse(ErrorResponse response) {
        BaseRequest request = response.request;
        if (request instanceof DeviceBindRequest) {
            /*若绑定失败，则直接拉取列表数据*/
            requestServiceDeviceList("若绑定失败，则直接拉取列表数据");
        }
    }

    /**
     * 将设备列表转到每个房间组下
     */
    private synchronized void transformGroupData(DeviceListResponse response) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "transformGroupData()");
        }
        List<GroupData> groupList = new ArrayList<>();
        if (response == null) response = new DeviceListResponse();
        List<AbsDevice> devices = OrderConfig.config().sortByAllDeviceSort(response.devices);
        if (devices == null) devices = new ArrayList<>();
        GroupData allGroup = new GroupData(-1, ResUtil.getString(R.string.device_all), devices);
        groupList.add(allGroup);
        List<AbsGroup> groups = response.groups;
        HashMap<Integer, AbsGroup> groupMap = new HashMap<>();
        List<Integer> groupIdList = new ArrayList<>();
        if (groups != null && !groups.isEmpty()) {
            for (AbsGroup group : groups) {
                int groupId = group.getGroupId();
                groupIdList.add(groupId);
                groupMap.put(groupId, group);
            }
        }
        if (!groupMap.isEmpty()) {
            HashMap<Integer, List<AbsDevice>> groupDevices = new HashMap<>();
            for (AbsDevice device : devices) {
                int groupId = device.getGroupId();
                List<AbsDevice> list = groupDevices.get(groupId);
                if (list == null) {
                    list = new ArrayList<>();
                    list.add(device);
                    groupDevices.put(groupId, list);
                } else {
                    list.add(device);
                }
            }
            for (Integer groupId : groupIdList) {
                AbsGroup absGroup = groupMap.remove(groupId);
                if (absGroup == null) continue;
                List<AbsDevice> list = groupDevices.remove(groupId);
                if (list == null) list = new ArrayList<>();
                OrderConfig.config().sortByLocalRoomSortMap(groupId, list);
                GroupData tempData = new GroupData(absGroup.getGroupId(), absGroup.getGroupName(), list);
                groupList.add(tempData);
            }
        }
        groupDataList.postValue(groupList);
    }

    /**
     * 获取指定组的设备数据
     */
    public GroupData getGroupData(int gId) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getGroupData() gId = " + gId);
        }
        List<GroupData> value = groupDataList.getValue();
        if (value == null) return null;
        for (GroupData groupData : value) {
            if (groupData.getGroupId() == gId) return groupData;
        }
        return null;
    }

    /**
     * 判断是否有设备
     */
    public boolean noDevices() {
        List<GroupData> value = groupDataList.getValue();
        if (value == null || value.isEmpty()) return true;
        GroupData groupData = value.get(0);
        List<AbsDevice> groupList = groupData.getGroupList();
        return groupList == null || groupList.isEmpty();
    }

    /**
     * 获取hint提示信息
     */
    public Hint getHint() {
        return hint.getValue();
    }

    /**
     * 更改房间设备所属关系
     */
    public void changeDeviceBelongRoom(int groupId, String groupName, List<String> inRoomDevices) {
        GroupData allGroup = getGroupData(-1);
        if (allGroup == null) return;
        /*先把之前所属在该分组下的设备-标识成不支持分组*/
        allGroup.changeOneGroup(groupId, inRoomDevices);
        /*所有设备信息*/
        List<AbsDevice> groupList = allGroup.getGroupList();
        DeviceRoomOrderConfig roomOrderConfig = DeviceRoomOrderConfig.read();
        DeviceListResponse deviceListResponse = roomOrderConfig.getResponse();
        deviceListResponse.changeGroupName(groupId, groupName);
        deviceListResponse.devices = groupList;
        roomOrderConfig.writeDef();
        transformGroupData(deviceListResponse);
    }

    /**
     * 加入新的房间
     */
    public void addRoom(GroupData groupData) {
        List<GroupData> value = groupDataList.getValue();
        if (value == null) return;
        DeviceRoomOrderConfig read = DeviceRoomOrderConfig.read();
        DeviceListResponse deviceListResponse = read.getResponse();
        List<AbsGroup> groups = deviceListResponse.groups;
        if (groups == null) {
            groups = new ArrayList<>();
        }
        groups.add(new AbsGroup(groupData.getGroupId(), groupData.getGroupName()));
        deviceListResponse.groups = groups;
        read.writeDef();
        value.add(groupData);
        groupDataList.postValue(value);
    }

    /**
     * 获取当前所有的组信息
     */
    @NonNull
    public List<GroupData> getAllGroupData() {
        List<GroupData> value = groupDataList.getValue();
        if (value == null) return new ArrayList<>();
        return value;
    }

    /**
     * 设置数据加载变更状态
     */
    public void deviceDataLoadingChange(boolean loading) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "deviceDataLoadingChange() loading = " + loading + " ; dataLoaded = " + dataLoaded);
        }
        /*若是取消加载状态-需要确保数据已至少加载成功过一次*/
        if (!loading) {
            if (!dataLoaded) {
                loading = true;
            }
        }
        if (loading) {
            handler.removeMessages(WHAT_HIDE_LOADING);
            deviceDataLoading.postValue(true);
        } else {
            /*若是关闭loading-则延迟200ms-*/
            handler.removeMessages(WHAT_HIDE_LOADING);
            handler.sendEmptyMessageDelayed(WHAT_HIDE_LOADING, 350L);
        }
    }

    /**
     * 功能描述：更新分组排序
     **/
    public synchronized void changeGroupOrder(int[] groupIds, int[] deleteGroupIds) {
        GroupData allGroup = getGroupData(-1);
        if (allGroup == null) return;
        allGroup.removeDeleteGroup(deleteGroupIds);
        /*所有设备信息*/
        List<AbsDevice> groupList = allGroup.getGroupList();
        DeviceRoomOrderConfig roomOrderConfig = DeviceRoomOrderConfig.read();
        DeviceListResponse deviceListResponse = roomOrderConfig.getResponse();
        deviceListResponse.changeGroupOrder(groupIds, deleteGroupIds);
        deviceListResponse.devices = groupList;
        roomOrderConfig.writeDef();
        transformGroupData(deviceListResponse);
    }

    /**
     * 更改某个分组的设备排序
     * @param  isUpdateGroupDataList 是否更新groupDataList
     */
    public void changeGroupDeviceOrder(boolean isUpdateGroupDataList, int groupId, List<String> deviceKeys) {
        /*更新内存设备排序*/
        GroupData groupData = getGroupData(groupId);
        if (groupData != null) {
            groupData.order(deviceKeys);
        }
        /*更新本地排序*/
        OrderConfig.config().changeOrderByDrag(groupId, deviceKeys);
        List<GroupData> groupDataListValue = groupDataList.getValue();
        int size = groupDataListValue != null ? groupDataListValue.size() : 0;
        SafeLog.Companion.i(TAG, () -> "changeGroupDeviceOrder() groupId = " + groupId + ";isUpdateGroupDataList = " + isUpdateGroupDataList + ";size = " + size + ";deviceKeys = " + JsonUtil.toJson(deviceKeys));
        if (isUpdateGroupDataList) {
            handler.post(() -> groupDataList.setValue(groupDataListValue));
        }
    }

    /**
     * 同步设备信息给服务器
     */
    private void syncPushSkusAndGidConfirm(List<AbsDevice> absDevices) {
        List<SyncPushSkusRequest.DeviceSku> deviceSkuList = new ArrayList<>();
        if (absDevices != null && !absDevices.isEmpty()) {
            for (AbsDevice absDevice : absDevices) {
                SafeBindMgr.INSTANCE.saveGidConfirm(absDevice.getSku(), absDevice.getDevice(), absDevice.getGidConfirmed());
                deviceSkuList.add(new SyncPushSkusRequest.DeviceSku(absDevice.getSku(), absDevice.getDevice(), absDevice.getDeviceName(), absDevice.getVersionSoft(), absDevice.getVersionHard()));
            }
        }
        /*判断pushToken是否上报*/
        PushTokenConfig read = PushTokenConfig.read();
        boolean suc = read.isSuc();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "syncPushSkus() pushToken 是否上报 = " + suc);
        }
        String pushToken = read.getPushToken();
        boolean syncPushSku = !suc;
        if (!syncPushSku) {
            boolean appInfoChange = AppInfoConfig.read().appInfoChange(BaseApplication.getContext(), false);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "syncPushSkus() app信息是否改变 = " + appInfoChange);
            }
            syncPushSku = appInfoChange;
        }
        if (!syncPushSku) {
            boolean compare = ConfigSyncPushSku.read().compare(pushToken, deviceSkuList);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "syncPushSkus() sku信息是否发生改变= " + !compare);
            }
            syncPushSku = !compare;
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "syncPushSkus() 是否需要同步sku信息 = " + syncPushSku);
        }
        if (syncPushSku) {
            SyncPushSkusRequest request = new SyncPushSkusRequest(transactions.createTransaction(), pushToken, deviceSkuList);
            Cache.get(IPushNet.class).syncPushSkus(request).enqueue(new Network.IHCallBack<>(request));
        }
    }

    public void checkClientInfoReport() {
        boolean hadLogin = AccountConfig.read().isHadToken();
        SafeLog.Companion.i(TAG, () -> "checkClientInfoReport() hadLogin = " + hadLogin);
        if (!hadLogin) return;
        boolean sucClient = ClientConfig.read().isSuc();
        SafeLog.i(TAG, () -> "checkClientInfoReport() sucClient = " + sucClient);
        if (!sucClient) {
            /*上传client信息*/
            SetClientRequest request = new SetClientRequest(transactions.createTransaction());
            Cache.get(IAccountNet.class).setClient(request).enqueue(new Network.IHCallBack<>(request));
        }
        PushTokenConfig pushTokenConfig = PushTokenConfig.read();
        boolean sucPushToken = pushTokenConfig.isSuc();
        String pushToken = pushTokenConfig.getPushToken();
        SafeLog.Companion.i(TAG, () -> "checkClientInfoReport() sucPushToken = " + sucPushToken);
        SafeLog.Companion.i(TAG, () -> "checkClientInfoReport() pushToken = " + pushToken);
        if (!sucPushToken && !TextUtils.isEmpty(pushToken)) {
            /*上传pushToken信息*/
            SetPushTokenRequest request = new SetPushTokenRequest(transactions.createTransaction(), pushToken, BaseApplication.getPushType());
            Cache.get(IAccountNet.class).setPushToken(request).enqueue(new Network.IHCallBack<>(request));
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onSetClientResponse(SetClientResponse response) {
        LogInfra.Log.i(TAG, "onSetClientResponse()");
        ClientConfig.read().setSuc(true);
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onSetPushTokenResponse(SetPushTokenResponse response) {
        LogInfra.Log.i(TAG, "onSetPushTokenResponse()");
        PushTokenConfig.read().setSuc(true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventAgreeShow(AgreeShowEvent event) {
        AgreementM.getInstance().showAgreementDialog();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onQaSkipEvent(QaSkipEvent event) {
        Context context = BaseApplication.getBaseApplication().getTopActivity();
        if (context == null) return;
        String deviceKey = event.getDeviceKey();
        Qa qa = QaManager.getInstance.getSkipQa(deviceKey);
        if (qa != null) {
            String url = qa.getUrl();
            String title = qa.getTitle();
            if (!TextUtils.isEmpty(url)) {
                String preStr = url.contains("?") ? "&" : "?";
                url = url + preStr + "client=" + DeviceUtil.getDeviceUuid(context) + "&qaId=" + qa.getQaId();
            }
            WebActivity.jump2WebAc(context, title, url);
            /*标记qa已读*/
            QaManager.getInstance.readQa(qa.getQaId());
            /*通知qa已读*/
            QaReadEvent.sendQaReadEvent(qa.getQaId());
        }
    }


}
