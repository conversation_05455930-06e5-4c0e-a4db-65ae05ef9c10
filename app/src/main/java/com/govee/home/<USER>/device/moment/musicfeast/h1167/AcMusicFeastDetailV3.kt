package com.govee.home.main.device.moment.musicfeast.h1167

import android.content.Context
import android.content.Intent
import android.graphics.Rect
import android.os.Bundle
import android.view.View
import android.view.ViewGroup
import androidx.core.widget.NestedScrollView
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.chad.library.adapter.base.listener.OnItemDragListener
import com.govee.base2home.BaseRPNetActivity
import com.govee.base2home.BindingAdapter
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.community.feedback.FbAcJump
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.theme.ThemeM
import com.govee.base2home.util.CaughtRunnable
import com.govee.base2home.util.StrUtil
import com.govee.base2home.util.recordUseCnt
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.dp4Int
import com.govee.base2kt.ext.gone
import com.govee.base2kt.ext.visible
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2kt.ext.visibleOrInvisible
import com.govee.base2light.ac.update.OtaUpdateAcV4
import com.govee.base2light.kt.ble.Compose4DefWrite4Multi.Companion.makeWriteController4MultiV2
import com.govee.base2light.pact.BleInfo
import com.govee.base2light.pact.feast.FeastDetailResponse.Data.CenterDevice
import com.govee.base2light.pact.feast.SubDevice
import com.govee.base2light.pact.feast.feastsnapshot.SnapshotListResp
import com.govee.ble.BleController
import com.govee.eco.helper.EcoHelper
import com.govee.home.databinding.AppAcMusicFeastDetailV3NewBinding
import com.govee.home.feast.AcFeastList
import com.govee.home.feast.EcoFeastFragment
import com.govee.home.main.device.moment.Constant
import com.govee.home.main.device.moment.moviefeast.ChooseSubDeviceAc
import com.govee.home.main.device.moment.musicfeast.Vm4MusicFeastDetailAcShare
import com.govee.home.main.device.moment.musicfeast.aimusic.AIMusicVM.Companion.EVENT_KEY
import com.govee.home.main.device.moment.musicfeast.devicesetting.Ac4FeastDeviceSetting
import com.govee.home.main.device.moment.musicfeast.event.Event2SetSubDevice
import com.govee.home.main.device.moment.musicfeast.h1168.DeviceInfo
import com.govee.home.main.device.moment.musicfeast.v2.FeastMaker
import com.govee.home.main.device.moment.musicfeast.v2.FeastMusicMode
import com.govee.home.main.device.moment.musicfeast.v2.MusicFeastM
import com.govee.home.main.device.moment.musicfeast.v2.SubDeviceAdapter
import com.govee.home.main.device.moment.musicfeast.v2.ViewModel4Feast
import com.govee.home.main.device.moment.net.response.MusicFeastSceneItemResponse
import com.govee.home.main.device.moment.scenesfeast.VoicePlayM
import com.govee.home.main.device.moment.scenesfeast.bean.DetailData
import com.govee.home.main.device.moment.scenesfeast.vm.ScenesVM
import com.govee.home.main.device.moment.snapshot.DelegateSnapshot
import com.govee.home.main.device.moment.snapshot.EventApplySnapshotResult
import com.govee.home.main.device.moment.snapshot.FeastType
import com.govee.home.main.device.moment.snapshot.IApplyListener
import com.govee.home.main.device.moment.snapshot.MusicFeastV3SnapshotDelegate
import com.govee.home.main.device.moment.snapshot.SnapshotRequireViewModel
import com.govee.ui.Cons
import com.govee.ui.R
import com.govee.ui.component.ConnectUIV1
import com.govee.ui.component.ConnectUIV1.EventClickConnectV1
import com.govee.ui.dialog.BleUpdateHintDialog
import com.govee.ui.dialog.HintDialog1
import com.govee.ui.dialog.NameEditDialog
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.skinv2.SkinM.isLightMode
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.StatusBarUtil
import com.liveeventbus.LiveEventBus
import com.zhy.android.percent.support.PercentRelativeLayout
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Create by DengFei on 2023/6/1
 * 音乐盛宴详情页
 */
@Suppress("OVERRIDE_DEPRECATION")
class AcMusicFeastDetailV3 : BaseRPNetActivity(), IApplyListener {
    private var binding: AppAcMusicFeastDetailV3NewBinding? = null
    private val viewModel4Feast by lazy { ViewModelProvider(this)[VModel4MusicFeast1167::class.java] }
    private val vm4Ota by lazy { ViewModelProvider(this)[VMBleBoxDetail::class.java] }
    private val voicePlayM by lazy { VoicePlayM(this) }
    private val scenesVM by lazy { ViewModelProvider(this)[ScenesVM::class.java] }

    private lateinit var connectUI: ConnectUIV1 //蓝牙连接ui
    private lateinit var adapter: SubDeviceAdapter //子设备适配器

    private var gId: Int = 0
    private var into4Add: Boolean = false
    private var need2ConnectBle: Boolean = true
    private var musicSupportAi: Boolean = true

    private val snapshotDelegate by lazy { MusicFeastV3SnapshotDelegate(viewModel4Feast, this) }
    private var snapshot: DelegateSnapshot? = null

    companion object {
        @JvmStatic
        fun jump2MusicFeastDetailAc(context: Context?, bundle: Bundle?) {
            JumpUtil.jumpWithBundle(context, AcMusicFeastDetailV3::class.java, bundle)
        }
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.home.R.id.ac_container
    }

    override fun getLayout(): Int {
        return com.govee.home.R.layout.app_ac_music_feast_detail_v3_new
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        /*适配挖孔屏*/
        adaptationInsetTop(com.govee.home.R.id.top_flag, AppUtil.getScreenWidth() * 61 / 750)
        /*解析参数*/
        parseParams(intent)
        initBinding()
        initConnectUI()
        initSubDeviceRecycleView()
        initViewModel()
        voicePlayM.checkSoundDialog()
        recordUseCnt(ParamKey.feast_music, ParamFixedValue.detail)


        //创建监听
        EcoHelper.observerFirstShowCardCreate(this)
    }

    private fun initSnapshot(centerDevice: CenterDevice) {
        binding?.musicUi?.getSnapshotContainer().let {
            val requireViewModel =
                SnapshotRequireViewModel(FeastType.MUSIC, centerDevice, snapshotDelegate)
            snapshot =
                DelegateSnapshot(this, it as ViewGroup, requireViewModel).apply {
                    bind()
                }
        }
    }

    private fun updateSnapshotUI(open: Boolean, centerDevice: CenterDevice) {
        if (open) {
            if (snapshot == null) {
                initSnapshot(centerDevice)
            }
            snapshot?.visible()
        } else {
            snapshot?.invisible()
        }
    }


    private fun initViewModel() {
        /*注册回调监听*/
        viewModel4Feast.run {
            /*请求盛宴接口结果*/
            requestFeastResult.observe(this@AcMusicFeastDetailV3) {
                when (it) {
                    ViewModel4Feast.REQUEST_RESULT_SUC -> {
                        binding?.run {
                            netErrorContainer.beHide()
                            setTitleViewVisible()
                        }
                        SafeLog.i(TAG) { "requestFeastResult() REQUEST_RESULT_SUC-接口请求成功-连接主设备" }
                        viewModel4Feast.connectBle(
                            this@AcMusicFeastDetailV3, into4Add, need2ConnectBle, viewModel4Feast
                        )
                        initOtaInfo()
                        initShareFeastVM()
                        snapshotDelegate.feastId = viewModel4Feast.feast.value?.feastId ?: 0
                        snapshotDelegate.centerDevice = viewModel4Feast.feast.value?.centerDevice
                        StatusBarUtil.statusBarTranslucentMode(this@AcMusicFeastDetailV3)
                    }

                    ViewModel4Feast.REQUEST_RESULT_FAIL -> {
                        binding?.run {
                            connectUI.hide()
                            netErrorContainer.beFail()
                            hideTitleView()
                        }
                    }

                    ViewModel4Feast.REQUEST_RESULT_NO_FOUND -> {
                        binding?.run {
                            connectUI.hide()
                            netErrorContainer.showEmptyUi4FixSize(
                                R.mipmap.new_h5_pics_post_no,
                                R.string.app_content_no_found
                            )
                            hideTitleView()
                        }
                    }

                    ViewModel4Feast.REQUEST_RESULT_ING -> {
                        binding?.run {
                            connectUI.hide()
                            netErrorContainer.beLoading()
                        }
                    }
                }
                showBackBtStyleByErrorStatus(it != ViewModel4Feast.REQUEST_RESULT_SUC)
            }
            /*盛宴信息*/
            feast.observeForever {
                /*更新ai曲风动作关联*/
                FeastMaker.makeQuFengActionMap(it.aiActions, it.aiActionColors)
                val bleConnected = it.bleConnected()
                LogInfra.Log.e(TAG, "initViewModel() bleConnected = $bleConnected")
                binding?.run {
                    /*盛宴名称*/
                    tvFeastName.text = it.feastName

                    /*盛宴开关*/
                    val feastInOpen = it.feastOpen && bleConnected
                    ivSwitch.isSelected = feastInOpen
                    ivSwitch.isEnabled = bleConnected
                    //头图
                    ResUtil.setImageResource(
                        ivTopBg,
                        if (feastInOpen)
                            R.mipmap.control_pics_top_music_feast_on
                        else R.mipmap.control_pics_top_music_feast_off
                    )
                    scenesVM.feastId = it.feastId
                    scenesVM.isMusicFeast = true
                    /*主设备信息*/
                    it.centerDevice?.run {
                        updateSnapshotUI(feastInOpen, this)
                        BindingAdapter.setSkuImageResource(
                            ivDeviceIcon, this.goodsType, this.sku, ""
                        )
                        tvDeviceName.text = this.deviceName
                    }
                    /*蓝牙图标和连接文案*/
                    BindingAdapter.updateImageViewBleStatus(ivConnectState, bleConnected)
                    BindingAdapter.updateTextViewBleStatus(tvConnectState, bleConnected)
                    /*电量*/
                    batteryStatus.visibleByBoolean(bleConnected && it.showBatteryIcon())
                    batteryStatus.setImageDrawable(ResUtil.getDrawable(it.batteryResId))
                    /*已连接模块*/
                    subDeviceFucContainer.visibleByBoolean(bleConnected)
                    /*音乐ui*/
                    musicUi.visibleByBoolean(feastInOpen)
                    val empty = viewModel4Feast.subDevices.value?.isEmpty() ?: true
//                    emptyBottomFlag.visibleByBoolean(feastInOpen && empty)
                    /*连接Ui*/
                    when (it.bleStatus) {
                        ViewModel4Feast.BLE_STATUS_DEF, ViewModel4Feast.BLE_STATUS_CONNECTING -> {
                            connectUI.show()
                            connectUI.updateConnectUi(Cons.connect_ui_v1_status_ing)
                        }

                        ViewModel4Feast.BLE_STATUS_CONNECTED -> {
                            connectUI.hide()
                            vm4Ota.readSoftInfo(viewModel4Feast.feast.value?.centerDevice?.goodsType)
                        }

                        ViewModel4Feast.BLE_STATUS_CONNECT_FAIL -> {
                            connectUI.show()
                            connectUI.updateConnectUi(Cons.connect_ui_v1_status_fail)
                        }
                    }
                    /*关闭文案*/
                    val feastInClose = bleConnected && !it.feastOpen
                    tvClose.visibleByBoolean(feastInClose)
//                    placeHolder4CloseHint.visibleByBoolean(feastInClose)
                    //子设备列表
                    subDeviceListUi()
                }
                hideLoading()
            }
            feastMusicMode.observe(this@AcMusicFeastDetailV3) {
                SafeLog.i(MusicUIV4.TAG) {
                    "loadLocal feastMusicMode--> musicType = ${
                        JsonUtil.toJson(
                            it
                        )
                    } "
                }
                it.saveLocal(if (it.musicType == 4) MusicUIV4.mode_key_scene_h1167 else MusicUIV4.mode_key_music_h1167)
                binding?.musicUi?.checkUi()
                hideLoading()
            }
            /*子设备列表*/
            subDevices.observe(this@AcMusicFeastDetailV3) {
                val notEmpty = it.isNotEmpty()
                SafeLog.i(TAG) { "initViewModel() subDevices notEmpty = $notEmpty" }
                binding?.run {
                    adapter.setList(it)
                    group4DeviceNoEmpty.visibleByBoolean(notEmpty)
                    ivEditSubDevice.visibleByBoolean(notEmpty)
                    deviceEmptyGroup.visibleByBoolean(it.isEmpty())
                    subDeviceListUi()
                }
                hideLoading()
            }
            /*亮度更新*/
            feastBrightness.observe(this@AcMusicFeastDetailV3) {
                SafeLog.i(TAG) { "initViewModel() feastBrightness-->" }
                binding?.run {
                    musicUi.brightnessUi(it)
                }
                hideLoading()
            }

            scenesVM.soundListener = object : ScenesVM.SoundListener {
                override fun playMusic(scenes: DetailData.TwoScene) {
                    voicePlayM.playBgSound(scenes)
                }

                override fun reshowSoundDialog() {
                    voicePlayM.reshowDialog()
                }
            }

            /*删除盛宴接口结果*/
            request4FeastDeleteResult.observe(this@AcMusicFeastDetailV3) {
                when (it) {
                    ViewModel4Feast.REQUEST_RESULT_SUC -> {
                        hideLoading()
                        toast(R.string.b2light_set_switch_success)
                        LiveEventBus.get<Boolean>(AcFeastList.feast_data_change).post(true)
                        doOnDestroy()
                    }

                    ViewModel4Feast.REQUEST_RESULT_FAIL -> {
                        hideLoading()
                    }

                    ViewModel4Feast.REQUEST_RESULT_ING -> {
                        showLoading()
                    }
                }
            }
            /*编辑盛宴名称接口结果*/
            request4FeastNameResult.observe(this@AcMusicFeastDetailV3) {
                when (it) {
                    ViewModel4Feast.REQUEST_RESULT_SUC -> {
                        NameEditDialog.hideDialog()
                        hideLoading()
                        LiveEventBus.get<Boolean>(EcoFeastFragment.feast_data_change).post(true)
                    }

                    ViewModel4Feast.REQUEST_RESULT_ING -> {
                        showLoading()
                    }

                    ViewModel4Feast.REQUEST_RESULT_FAIL -> {
                        hideLoading()
                    }
                }
            }
        }
        /*关联viewModel*/
        binding?.run {
            musicUi.registerViewModel(viewModel4Feast)
        }
        /*请求盛宴信息*/
        val mark = Constant.music_feast_1167
        viewModel4Feast.requestFeast(this, gId, mark)
        viewModel4Feast.requestFeastSceneCategory(this)
        /**关闭盛宴详情页*/
        LiveEventBus.get<Boolean>(Constant.EVENT_CLOSE_FEAST_DETAIL_PAGE)
            .observe(this) {
                SafeLog.i(TAG) { "关闭盛宴详情页()" }
                doOnDestroy()
            }
    }

    private fun hideTitleView() {
        binding?.run {
            ivHelp.visibleOrInvisible(false)
            helpBg.visibleOrInvisible(false)
            setting.visibleOrInvisible(false)
            settingBg.visibleOrInvisible(false)
        }
    }

    private fun setTitleViewVisible() {
        binding?.run {
            ivHelp.visibleOrInvisible(true)
            helpBg.visibleOrInvisible(true)
            setting.visibleOrInvisible(true)
            settingBg.visibleOrInvisible(true)
        }
    }

    private fun initShareFeastVM() {
        val centerDevice = viewModel4Feast.feast.value?.centerDevice
        SafeLog.i(TAG) { "initShareFeastVM() sku = ${centerDevice?.sku.orEmpty()} ;device = ${centerDevice?.device.orEmpty()}" }
        Vm4MusicFeastDetailAcShare.createShareVm4NewDetail(
            centerDevice?.sku.orEmpty(),
            centerDevice?.device.orEmpty(),
            this@AcMusicFeastDetailV3,
            true
        ).apply {
            this?.initViewModel4Feast(viewModel4Feast)
        }
    }

    private fun subDeviceListUi() {
        binding?.run {
            val bleConnected = viewModel4Feast.feast.value?.bleConnected() ?: false
            val feastOpen = viewModel4Feast.feast.value?.feastOpen ?: false
            val empty = viewModel4Feast.subDevices.value?.isEmpty() ?: true
            SafeLog.i(TAG) { "subDeviceListUi() bleConnected = $bleConnected ;feastOpen = $feastOpen ; empty = $empty" }
            adapter.setEnable(bleConnected && feastOpen)
        }
    }

    private fun initOtaInfo() {
        DeviceInfo().apply {
            val centerDevice = viewModel4Feast.feast.value?.centerDevice
            sku = centerDevice?.sku ?: ""
            device = centerDevice?.device ?: ""
            deviceName = centerDevice?.deviceName ?: ""
            bleAddress = centerDevice?.bleAddress ?: ""
            goodsType = centerDevice?.goodsType ?: 0
            versionSoft = centerDevice?.versionSoft ?: ""
            versionHard = centerDevice?.versionHard ?: ""
            vm4Ota.init(this)
        }


        vm4Ota.gId = gId
        vm4Ota.feastNm = viewModel4Feast.feast.value?.feastName ?: ""
        vm4Ota.versionUpdate.observe(this@AcMusicFeastDetailV3) { b ->
            binding?.versionFlag?.visibleByBoolean(b)
        }
        vm4Ota.versionUpdateDialogShowing.observe(this@AcMusicFeastDetailV3) { b ->
            val show = b ?: return@observe
            updateDialogShow(show)
        }
    }

    private fun updateDialogShow(show: Boolean) {
        SafeLog.i(TAG) { "updateDialogShow() show = $show" }
        if (show) {
            vm4Ota.versionUpdateDialogShowing.value = null
            vm4Ota.deviceInfo?.run {
                BleUpdateHintDialog.showDialog(
                    this@AcMusicFeastDetailV3, this.sku,
                    { toUpdateAc() }, BleUpdateHintDialog::class.simpleName
                )
            }
        } else {
            BleUpdateHintDialog.hideDialog(BleUpdateHintDialog::class.simpleName)
        }
    }

    private fun toUpdateAc() {
        vm4Ota.deviceInfo?.run {
            if (viewModel4Feast.feast.value?.batteryLevel!! <= 2) {
                toast(R.string.low_battery_no_support_ota)
                return
            }
            val checkVersion = vm4Ota.aiVersion ?: return
            val defSkuRes = ThemeM.getDefSkuRes(this.sku)
            OtaUpdateAcV4.jump2OtaUpdateAcV4(
                this@AcMusicFeastDetailV3,
                this.sku,
                this.deviceName,
                this.versionSoft,
                defSkuRes,
                checkVersion
            )
        }
    }

    private fun parseParams(intent: Intent?) {
        intent?.run {
            gId = getIntExtra(Constant.intent_key_ac_gId, 0)
            into4Add = intent.getIntExtra(
                Constant.intent_key_jump_type, Constant.jump_4_list
            ) == Constant.jump_4_add
            need2ConnectBle = intent.getBooleanExtra(Constant.intent_key_need_2_connect, true)
            musicSupportAi = intent.getBooleanExtra(Constant.intent_key_music_support_ai, true)
            LogInfra.Log.i(
                TAG,
                "parseParams() gId = $gId ; into4Add = $into4Add ; need2ConnectBle = $need2ConnectBle ; musicSupportAi = $musicSupportAi"
            )
        }
    }

    override fun bindView(layoutId: Int): Boolean {
        binding = DataBindingUtil.setContentView(this, layoutId)
        return true
    }

    /**
     * 页面控件操作
     */
    private fun initBinding() {

        //TopBar适配
        val margin = getAdaptationInsetTop(AppUtil.getScreenWidth() * 61 / 750)
        if (margin > 0) {
            val params = binding!!.scrollFlag.layoutParams
            params.height += margin
            binding!!.scrollFlag.layoutParams = params
        }

        /*设置控件响应事件*/
        binding?.run {
            /*网络请求*/
            netErrorContainer.setListener {
                val mark = Constant.music_feast_1167
                viewModel4Feast.requestFeast(
                    this@AcMusicFeastDetailV3, gId, mark
                )
            }
            /*滚动不透明度*/
            scrollView.setOnScrollChangeListener { _: NestedScrollView?, _: Int, scrollY: Int, _: Int, _: Int ->
                updateUIAlpha(scrollY)
            }
            /*返回键*/
            back.clickDelay {
                doOnDestroy()
            }
            helpBg.clickDelay {
                FbAcJump.jumpFbAc(this@AcMusicFeastDetailV3, true)
            }
            /*设置页*/
            setting.clickDelay {
                val centerDevice = viewModel4Feast.feast.value?.centerDevice
                Ac4FeastDeviceSetting.jump2Ac(
                    this@AcMusicFeastDetailV3,
                    BleInfo(
                        centerDevice?.sku,
                        centerDevice?.goodsType ?: 0,
                        centerDevice?.device,
                        "",
                        centerDevice?.deviceName,
                        "",
                        centerDevice?.bleAddress
                    ).apply {
                        versionSoft = vm4Ota.deviceInfo?.versionSoft
                        versionHard = vm4Ota.deviceInfo?.versionHard
                        checkVersion = vm4Ota.checkVersion
                        aiVersion = vm4Ota.aiVersion
                    },
                    viewModel4Feast.feast.value?.gId ?: 0,
                    viewModel4Feast.feast.value?.batteryLevel ?: 1
                )
            }
            /*编辑盛宴名称*/
            ivEditFeastName.clickDelay {
                val sensorNameHintDes = ResUtil.getStringFormat(
                    R.string.setting_device_name_hint, 22.toString()
                )
                val feastName = viewModel4Feast.feast.value?.feastName ?: ""
                NameEditDialog.createNameEditDialog(
                    this@AcMusicFeastDetailV3,
                    ResUtil.getString(R.string.scenes_class_feast),
                    feastName,
                    ResUtil.getString(R.string.cancel),
                    ResUtil.getString(R.string.done)
                ) { _: NameEditDialog?, newName: String ->
                    if (!StrUtil.isInputCheck(newName.trim { it <= ' ' }, 22)) {
                        toast(R.string.invalid_input)
                        return@createNameEditDialog
                    }
                    viewModel4Feast.editFeastName(this@AcMusicFeastDetailV3, newName, gId)
                }.setTextLimit(22).setTextHint(sensorNameHintDes).show()
            }
            /*提示文案弹窗*/
            tvHint.clickDelay {
                HintDialog1.createHintDialog1(
                    this@AcMusicFeastDetailV3,
                    ResUtil.getString(R.string.moviefeast_connect_hint),
                    ResUtil.getString(R.string.hint_done_got_it)
                ).show()
            }
            /*开关盛宴*/
            ivSwitch.clickDelay {
                viewModel4Feast.feast.value?.run {
                    AnalyticsRecorder.getInstance().recordTimes(
                        EventKey.use_count,
                        ParamKey.click_1163_ai_music_switch,
                        if (this.feastOpen) ParamFixedValue.ai_music_switch_off else ParamFixedValue.ai_music_switch_on
                    )
                    if (!this.feastOpen && viewModel4Feast.subDevices.value?.isEmpty() == true) {
                        AnalyticsRecorder.getInstance()
                            .recordUseCount(
                                ParamKey.turn_on_feast_no_sub_device,
                                ParamFixedValue.feast_music
                            )
                    }
                    showLoading()
                    MusicFeastM.operateSwitch(!this.feastOpen, object : MusicFeastM.OpOverListener {
                        override fun overResult(suc: Boolean) {
                            hideLoading()
                            if (!feastOpen) {
                                musicUi.openMusicFeast()
                            }
                        }
                    })
                    recordUseCnt(ParamKey.feast_music, ParamFixedValue.send_cmd_in_detail)
                }
            }
            /*添加新的子设备*/
            imgAddNew.clickDelay {
                viewModel4Feast.feast.value?.run {
                    var maxSubDeviceNum =
                        Constant.maxSubDeviceNum(centerDevice?.goodsType ?: 0, protocolVersion)
                    centerDevice?.maxSubDevice?.let {
                        if (it > 0) maxSubDeviceNum = it
                    }
                    LogInfra.Log.i(TAG, "imgAddNew() maxSubDeviceNum = $maxSubDeviceNum")
                    val mark = Constant.music_feast_1167
                    ChooseSubDeviceAc.jump2ChooseSubDeviceAc(
                        this@AcMusicFeastDetailV3,
                        centerDevice?.sku,
                        centerDevice?.bleAddress,
                        gId,
                        ArrayList(),
                        mark,
                        protocolVersion,
                        maxSubDeviceNum
                    )
                }
            }
            /*编辑子设备*/
            ivEditSubDevice.clickDelay {
                val subDeviceArrayList = ArrayList<SubDevice>()
                viewModel4Feast.subDevices.value?.run {
                    subDeviceArrayList.addAll(this)
                }
                viewModel4Feast.feast.value?.run {
                    var maxSubDeviceNum =
                        Constant.maxSubDeviceNum(centerDevice?.goodsType ?: 0, protocolVersion)
                    centerDevice?.maxSubDevice?.let {
                        if (it > 0) maxSubDeviceNum = it
                    }
                    LogInfra.Log.i(TAG, "ivEditSubDevice() maxSubDeviceNum = $maxSubDeviceNum")
                    val mark = Constant.music_feast_1167
                    ChooseSubDeviceAc.jump2ChooseSubDeviceAc(
                        this@AcMusicFeastDetailV3,
                        centerDevice?.sku,
                        centerDevice?.bleAddress,
                        gId,
                        subDeviceArrayList,
                        mark,
                        protocolVersion,
                        maxSubDeviceNum
                    )
                }
            }
        }

        LiveEventBus.get<Boolean>(EVENT_KEY).observe(this) {
            binding?.run {
                if (it && adapter.data.isNotEmpty()) {
                    vSpecialSpace.visible()
                    vSpecialSpace.post {
                        scrollView.scrollTo(0, 500.dp4Int)
                    }
                } else {
                    vSpecialSpace.gone()
                }
            }
        }
    }

    /**
     * 根据滚动范围刷新头部ui
     *
     * @param scrollY
     */
    private fun updateUIAlpha(scrollY: Int) {
        binding?.run {
            val alpha = calculateAlpha(scrollY)
            setting.post(object : CaughtRunnable() {
                override fun runSafe() {
                    ResUtil.setImageResource(
                        setting,
                        if (alpha < 0.5f) R.mipmap.new_home_icon_top_corner_setting else com.govee.h1162.R.mipmap.new_control_light_1168_icon_mini_shezhi_gree
                    )
                }
            })
            settingBg.post(object : CaughtRunnable() {
                override fun runSafe() {
                    settingBg.alpha = alpha
                }
            })
            topBg.post(object : CaughtRunnable() {
                override fun runSafe() {
                    topBg.alpha = alpha
                }
            })
            back.post(object : CaughtRunnable() {
                override fun runSafe() {
                    back.setImageDrawable(ResUtil.getDrawable(if (alpha < 0.5f) R.mipmap.new_home_icon_top_corner_back else R.mipmap.new_sensor_setting_icon_arrow_left))
                }
            })

            backBg.post(object : CaughtRunnable() {
                override fun runSafe() {
                    backBg.alpha = alpha
                }
            })
            ivHelp.post(object : CaughtRunnable() {
                override fun runSafe() {
                    ivHelp.setImageDrawable(ResUtil.getDrawable(if (alpha < 0.5f) R.mipmap.new_dreamview_icon_feedback else R.mipmap.new_dreamview_icon_feedback_black))
                }
            })
            helpBg.post(object : CaughtRunnable() {
                override fun runSafe() {
                    helpBg.alpha = alpha
                }
            })

            if (calculateAlpha(scrollY) < 0.5f) {
                StatusBarUtil.statusBarTranslucentMode(this@AcMusicFeastDetailV3)
            } else {
                if (isLightMode()) {
                    StatusBarUtil.statusBarLightMode(this@AcMusicFeastDetailV3)
                } else {
                    StatusBarUtil.statusBarTranslucentMode(this@AcMusicFeastDetailV3)
                }
            }
        }
    }

    /**在加载中和异常状态时将back按钮显示对应样式颜色*/
    private fun showBackBtStyleByErrorStatus(errorStatus: Boolean) {
        binding?.back?.setImageDrawable(ResUtil.getDrawable(if (!errorStatus) R.mipmap.new_home_icon_top_corner_back else R.mipmap.new_sensor_setting_icon_arrow_left))
        binding?.backBg?.alpha = if (errorStatus) {
            1f
        } else {
            0f
        }
        binding?.topBg?.visibleByBoolean(!errorStatus)
    }

    /**
     * 计算alpha
     *
     * @param scrollY
     * @return
     */
    private fun calculateAlpha(scrollY: Int): Float {
        val maxH = AppUtil.getScreenWidth() * 306 / 750
        return if (scrollY > maxH) {
            1.0f
        } else {
            scrollY * 1.0f / maxH
        }
    }

    /**
     * 蓝牙连接ui
     */
    private fun initConnectUI() {
        /*添加布局-connect*/
        val failDes: CharSequence =
            ResUtil.getString(R.string.moviefeast_no_connect_hint)
        val connectStr = ResUtil.getString(R.string.b2light_reconnect)
        connectUI = ConnectUIV1(this, failDes, connectStr, null, false, null, 441.5f)
        val fucViewConnect = connectUI.fucView
        /*构建基础ui*/
        val lp = PercentRelativeLayout.LayoutParams(
            connectUI.width, connectUI.height
        )
        binding?.acContainer?.addView(fucViewConnect, lp)
        //点击重连按钮
        connectUI.setConnectV1ClickListener {
            val is2Connect = it == EventClickConnectV1.click_type_2connect
            if (is2Connect) {
                if (!BleController.getInstance().isBlueToothOpen) {
                    toast(R.string.main_operation_fail_ble_not_open)
                    return@setConnectV1ClickListener
                }
                viewModel4Feast.retryConnectBle()
            }
        }
        connectUI.hide()
    }

    /**
     * 设置子设备的recyclerview
     */
    private fun initSubDeviceRecycleView() {
        adapter = SubDeviceAdapter(viewModel4Feast.subDevices.value)
        adapter.onItemClickListener = object : SubDeviceAdapter.OnItemClickListener {
            override fun onClickOp(device: SubDevice, pos: Int) {
                val status = device.status
                if (status == SubDevice.status_no) return
                val op = if (status == SubDevice.status_disconnect) 1 else 0
                showLoading()
                MusicFeastM.operateSubDeviceConnectStatus(
                    op == 1,
                    pos,
                    object : MusicFeastM.OpOverListener {
                        override fun overResult(suc: Boolean) {
                            hideLoading()
                        }
                    })
            }

            override fun onClickFlag(device: SubDevice, pos: Int) {
                HintDialog1.createHintDialog1(
                    this@AcMusicFeastDetailV3,
                    ResUtil.getString(R.string.moviefeast_version_low),
                    ResUtil.getString(R.string.hint_done_got_it)
                ).show()
            }
        }
        var dragStartPos = 0
        adapter.draggableModule.setOnItemDragListener(object : OnItemDragListener {
            override fun onItemDragStart(viewHolder: RecyclerView.ViewHolder?, pos: Int) {
                viewHolder?.itemView?.isSelected = true
                dragStartPos = pos
                SafeLog.i(TAG) { "onItemDragStart: dragStartPos = $dragStartPos" }
            }

            override fun onItemDragMoving(
                source: RecyclerView.ViewHolder?,
                from: Int,
                target: RecyclerView.ViewHolder?,
                to: Int
            ) {
            }

            override fun onItemDragEnd(viewHolder: RecyclerView.ViewHolder?, pos: Int) {
                viewHolder?.itemView?.isSelected = false
                SafeLog.i(TAG) { "onItemDragEnd: dragEndPos = $pos" }
                if (pos != dragStartPos) {
                    showLoading()
                    val subDeviceList = mutableListOf<SubDevice>().apply {
                        addAll(adapter.data)
                    }
                    MusicFeastM.setSubDevice(subDeviceList, object : MusicFeastM.OpOverListener {
                        override fun overResult(suc: Boolean) {
                            hideLoading()
                        }
                    })
                }
            }
        })

        binding?.run {
            val screenWidth = AppUtil.getScreenWidth()
            val glm = GridLayoutManager(this@AcMusicFeastDetailV3, 4)
            recyclerDevice.layoutManager = glm
            recyclerDevice.addItemDecoration(object : RecyclerView.ItemDecoration() {
                override fun getItemOffsets(
                    outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State
                ) {
                    super.getItemOffsets(outRect, view, parent, state)
                    val pos = parent.getChildAdapterPosition(view)
                    var left = 0
                    if (pos % 4 == 1) left = 3
                    if (pos % 4 == 2) left = 6
                    if (pos % 4 == 3) left = 9
                    outRect.left = screenWidth * left / 375
                    outRect.bottom = screenWidth * 16 / 375
                }
            })
            recyclerDevice.adapter = adapter
            recyclerDevice.setHasFixedSize(true)
            recyclerDevice.isNestedScrollingEnabled = false
        }
    }

    override fun onBackPressed() {
        doOnDestroy()
    }

    private fun showLoading() {
        LoadingDialog.createDialog(
            this,
            com.ihoment.base2app.R.style.DialogDim,
            (60 * 1000).toLong()
        ).setEventKey(TAG)
            .show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(TAG)
    }

    private fun doOnDestroy() {
        val destroy = connectUI.isDestroy
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "doOnDestroy() destroy = $destroy")
        }
        if (!destroy) {
            connectUI.onDestroy()
        }
        voicePlayM.onDestroy()
        snapshot?.unbind()
        finish()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventClickConnectV1(event: EventClickConnectV1) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventClickConnectV1()")
        }
        val is2Connect = event.clickType == EventClickConnectV1.click_type_2connect
        if (is2Connect) {
            if (!BleController.getInstance().isBlueToothOpen) {
                toast(R.string.main_operation_fail_ble_not_open)
                return
            }
            MusicFeastM.retryConnect()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent2SetSubDevice(event: Event2SetSubDevice) {
        LogInfra.Log.i(TAG, "onEvent2SetSubDevice()")
        MusicFeastM.setSubDevice(event.subDeviceList)
    }

    override fun applySnapshot(snapshot: SnapshotListResp.SnapshotItem) {
        val cmdDetail = snapshot.cmdDetail
        val selectScene = cmdDetail.lightMusicType == FeastMusicMode.music_type_scene
        //当前是场景 需要根据子设备请求场景数据
        if (selectScene) {
            viewModel4Feast.requestFeastSceneItem(this, cmdDetail.twoSceneId) { value ->
                applySnapshotCmds(snapshot, value)
            }
        } else {
            applySnapshotCmds(snapshot)
        }
    }

    /**
     * 应用快照
     */
    private fun applySnapshotCmds(
        snapshot: SnapshotListResp.SnapshotItem,
        addressList2StrMap: HashMap<MusicFeastSceneItemResponse.FeastSceneItem, ArrayList<String>>? = null //发送场景需要的从服务器请求的数据
    ) {
        val sendBleCmd = snapshotDelegate.makeApplySnapshotCmds(
            snapshot, viewModel4Feast.subDevices.value ?: mutableListOf(), addressList2StrMap
        ).toMutableList()
        if (sendBleCmd.isEmpty()) {
            EventApplySnapshotResult.sendEvent(false)
            return
        }
        val multiCompose = makeWriteController4MultiV2(sendBleCmd) { res: Boolean ->
            if (res) {
                MusicFeastM.sendReadMsg4Snapshot()
            }
            EventApplySnapshotResult.sendEvent(res)
        }
        MusicFeastM.applySnapshot(multiCompose)
    }
}