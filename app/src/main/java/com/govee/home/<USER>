package com.govee.home;

import android.app.Activity;
import android.content.ComponentCallbacks2;
import android.content.Context;
import android.os.Bundle;
import android.os.Looper;
import android.text.TextUtils;
import android.util.SparseArray;

import com.alibaba.android.arouter.launcher.ARouter;
import com.google.android.play.core.splitcompat.SplitCompat;
import com.govee.base2home.BuildConfig;
import com.govee.base2home.KtExpand;
import com.govee.base2home.ac.ActivityMgr;
import com.govee.base2home.account.AccountEvent;
import com.govee.base2home.account.config.PushTokenConfig;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.analytics.expose.AnalyticsServiceImpl;
import com.govee.base2home.broadcast.event.GpsChangeEvent;
import com.govee.base2home.broadcast.event.NetChangeEvent;
import com.govee.base2home.broadcast.event.ScreenStatusChangeEvent;
import com.govee.base2home.config.OrderConfig;
import com.govee.base2home.main.tab.EventTabDefault;
import com.govee.base2home.pfd.PfdModuleInit;
import com.govee.base2home.push.PushM;
import com.govee.base2home.service.AnalyticsService;
import com.govee.base2home.service.AnalyticsServiceManager;
import com.govee.base2home.storage.StorageManagerService;
import com.govee.base2light.ac.club.ClubM;
import com.govee.base2light.ac.diy.OldDiyInfoParser;
import com.govee.ble.event.BTStatusEvent;
import com.govee.db.dao.AccountDatabase;
import com.govee.db.memory.ShortMemoryMgr;
import com.govee.device.DeviceInfoManager;
import com.govee.home.main.MainTabActivity;
import com.govee.home.main.device.Event4TokenInvalid;
import com.govee.home.util.AcLifeCycleImp;
import com.govee.home.util.LogPrinter;
import com.govee.lifecycle.api.ApplicationLifecycleManager;
import com.govee.push.NotifyManager;
import com.govee.push.event.TokenEvent;
import com.govee.utils.GlobalStateUtils;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.IApplication;
import com.ihoment.base2app.broadcast.Event4DynamicBroadcastReceiver;
import com.ihoment.base2app.config.RunMode;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;
import com.ihoment.base2app.infra.SharedPreManager;
import com.ihoment.base2app.network.ErrorResponse;
import com.ihoment.base2app.ui.event.EventActivityResult;
import com.ihoment.base2app.ui.event.EventStartActivityForResult;
import com.ihoment.base2app.util.AppActivityLifecycleImp;
import com.ihoment.base2app.util.AppLifecycleCheckUtil;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.CaughtRunnable;
import com.ihoment.base2app.util.DeviceUtil;
import com.ihoment.base2app.util.JumpUtil;
import com.ihoment.base2app.util.ToastUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;
import java.util.Locale;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Created by xieyingwu on 2018/4/13
 * IhApplication
 */
public class IhApplication extends BaseApplication {
    private static final String TAG = "IhApplication";
    private final SparseArray<Locale> supportApkLanguageArray = new SparseArray<>();
    //各个页面监听是否app有到达过桌面
    public static boolean needRefreshVideoList = false;//发现我的视频
    public static boolean needRefreshPostList = false;//发现我的帖子

    /**
     * 后台任务线程池
     */
    private static final ExecutorService BACKGROUND_EXECUTOR = Executors.newCachedThreadPool(r -> {
        Thread thread = new Thread(r, "AutoCleanCache-Thread");
        thread.setDaemon(true);
        return thread;
    });

    private final BtRunnable btRunnable = new BtRunnable();

    public static IhApplication getIhApplication() {
        return (IhApplication) context;
    }


    @Override
    protected void beforeInit() {
        boolean registered = EventBus.getDefault().isRegistered(this);
        if (!registered) {
            EventBus.getDefault().register(this);
        }
    }

    /**
     * 是否是正式环境
     */
    private boolean isNotQaMode() {
        return !RunMode.isQaMode();
    }

    @Override
    public void onCreate() {
        super.onCreate();
        // 初始化设备信息数据库
        DeviceInfoManager.INSTANCE.init(this);
        /*注册非公共部分的Ac生命周期监听操作*/
        registerActivityLifecycleCallbacks(new AcLifeCycleImp());
        PushM.getInstance().addPushM(new HomePush());
        if (com.govee.home.BuildConfig.DEBUG) {
            ARouter.openDebug();
        }
        ARouter.init(IhApplication.this); // 尽可能早，推荐在Application中初始化
        initAppLifecycle();
        /*动态模块打包成APK的初始化*/
        Boolean buildWhole = BuildConfig.buildWhole;
        LogInfra.Log.i(TAG, "onCreate() buildWhole = " + buildWhole);
        if (buildWhole) {
            PfdModuleInit.INSTANCE.pfdModuleInit4Apk();
        } else {
            /*若是动态模块构建方式-则需要在Application构建时进行动态模块已下载模块的初始化*/
            PfdModuleInit.INSTANCE.checkModuleInit(this, "Application.onCreate()");
        }
        /*检测旧存储的diy信息*/
        OldDiyInfoParser.getInstance.checkOldDiyInfo();
        //初始化数据提前从io读取操作
        ShortMemoryMgr.INSTANCE.initDelay();
        //检查自动清理缓存
        checkAutoCleanCache();
        KtExpand.INSTANCE.setAcContainerIdErrorListener(acName -> {
            AnalyticsRecorder.getInstance().recordBug(ParamFixedValue.id_error_ + acName);
            return null;
        });
        /*测试环境下*/
        if (isNotQaMode()) {
            LogInfra.Log.e(TAG, "加入ANR耗时监听记录");
            Looper.getMainLooper().setMessageLogging(new LogPrinter());
            int screenWidth = AppUtil.getScreenWidth();
            int screenHeight = AppUtil.getScreenHeight();
            float dpi = AppUtil.getDpi();
            LogInfra.Log.e(TAG, "screenWidth = " + screenWidth + " ; screenHeight = " + screenHeight + " ; dpi = " + dpi);
            String localCountryCode = AppUtil.getLocalCountryCode();
            LogInfra.Log.e(TAG, "localCountryCode = " + localCountryCode);
            LogInfra.Log.e(TAG, "BRAND = " + DeviceUtil.getDeviceBrand());
            LogInfra.Log.e(TAG, "MODEL = " + DeviceUtil.getDeviceModel());
            LogInfra.Log.e(TAG, "PRODUCT = " + DeviceUtil.getDeviceProduct());
            String localLanguage = AppUtil.getLocalLanguage();
            LogInfra.Log.e(TAG, "localLanguage = " + localLanguage);
            int statusBarHeight = AppUtil.getStatusBarHeight(context);
            LogInfra.Log.e(TAG, "statusBarHeight = " + statusBarHeight);


            /*支持动态更改apk的语言*/
            supportApkLanguageArray.append(0, Locale.ENGLISH);
            supportApkLanguageArray.append(1, Locale.CHINA);
            supportApkLanguageArray.append(2, Locale.FRANCE);
            supportApkLanguageArray.append(3, Locale.GERMAN);
            SharedPreManager instance = SharedPreManager.getInstance();
            boolean apkDynamicLanguageOpen = instance.getBoolean("apk_dynamic_language_open", false);
            int apkDynamicLanguage = instance.getInt("apk_dynamic_language", defApkLanguage);
            LogInfra.Log.e(TAG, "apkDynamicLanguageOpen = " + apkDynamicLanguageOpen + " ; apkDynamicLanguage = " + apkDynamicLanguage);
            if (apkDynamicLanguageOpen) {
                dynamicApkLanguage(this, apkDynamicLanguage);
            }
            //ARouter日志配置
            ARouter.openLog();     // 打印日志
            ARouter.openDebug();   // 开启调试模式(如果在InstantRun模式下运行，必须开启调试模式！线上版本需要关闭,否则有安全风险)
        }
        AnalyticsServiceManager.INSTANCE.register(AnalyticsService.class, new AnalyticsServiceImpl());
    }

    private void initAppLifecycle() {
        try {
            ApplicationLifecycleManager.init();
            SafeLog.d("AppLifecycle", () -> "------->>>create");
            ApplicationLifecycleManager.create();
            AppLifecycleCheckUtil.checkAppLifecycle(ApplicationLifecycleManager.getApplicationList());
        } catch (Exception e) {
            LogInfra.Log.e(TAG, "onCreate()", e);
            try {
                AnalyticsRecorder.getInstance().recordBug("error = " + e.getClass().getSimpleName());
            } catch (Exception ex) {
                LogInfra.Log.e(TAG, "onCreate()-AnalyticsRecorder", e);
            }
        }
    }

    @Override
    public SparseArray<Locale> getSupportLanguages() {
        return supportApkLanguageArray;
    }

    /**
     * 非正式包支持动态更改apk语言
     *
     * @return boolean
     */
    @Override
    public boolean supportDynamicApkLanguage() {
        return isNotQaMode() && defApkLanguage != -1 && SharedPreManager.getInstance().getBoolean("apk_dynamic_language_open", false);
    }

    @Override
    protected String getLogTag() {
        return Constant.DEFAULT_TAG;
    }

    @Override
    protected String getDefaultSpName() {
        return Constant.DEFAULT_SP_NAME;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSessionInvalidEvent(ErrorResponse.SessionInvalidEvent event) {
        /*统计退出登录状态的异常*/
        String errorMsg = event.errorMsg;
        if (!TextUtils.isEmpty(errorMsg)) {
            ToastUtil.getInstance().toast(errorMsg);
        }
        int errorType = event.errorType;
        boolean isService401 = errorType == ErrorResponse.SessionInvalidEvent.error_type_service_401;
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, isService401 ? ParamKey.error_401 : ParamKey.language_change, ParamFixedValue.times);
        /*登录的token失效，需要重新登录*/
        LogInfra.Log.i(TAG, "onSessionInvalidEvent()");
        beLogout();
        Bundle bundle = new Bundle();
        bundle.putBoolean(com.govee.base2home.Constant.intent_main_tab_only_4_login_401, true);
        JumpUtil.jumpActivityNewTask(BaseApplication.getContext(), MainTabActivity.class, bundle);
        /*考虑TabActivity重建逻辑，事件注册可能发生在事件触发之前，通知Tab更改为Default*/
        handler.postDelayed(new CaughtRunnable() {
            @Override
            protected void runSafe() {
                EventTabDefault.sendEventTabDefault();
            }
        }, 1000);
        Event4TokenInvalid.sendEvent();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onTokenEvent(TokenEvent event) {
        /*存储推送token*/
        String token = event.getToken();
        PushTokenConfig pushTokenConfig = PushTokenConfig.read();
        String pushToken = pushTokenConfig.getPushToken();
        if (!TextUtils.isEmpty(token) && !token.equals(pushToken)) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "onTokenEvent() token change; previous token = " + pushToken + " \n current token = " + token);
            }
            /*pushToken发生更改，需要重新存储*/
            pushTokenConfig.setPushToken(token);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventAcLifeCycleRegister(AppActivityLifecycleImp.EventAcLifeCycleRegister event) {
        boolean register = event.register;
        String acClassName = event.acClassName;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventAcLifeCycleRegister() register = " + register + " ; acClassName = " + acClassName);
        }
        if (appActivityLifecycleImp != null)
            appActivityLifecycleImp.setNotifyAcLifeCycle(register, acClassName);
    }

    @Subscribe(threadMode = ThreadMode.ASYNC)
    public void onEvent4DynamicBroadcastReceiver(Event4DynamicBroadcastReceiver event) {
        int type = event.getType();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEvent4DynamicBroadcastReceiver() type = " + type);
        }
        if (type == Event4DynamicBroadcastReceiver.TYPE_BT) {
            boolean btOpen = event.btOpen;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEvent4DynamicBroadcastReceiver() btOpen = " + btOpen);
            }
            /*蓝牙状态广播-因Manifest中静态注册了，可能会导致2个注册事件接收-考虑时差性-需要延时发送状态指令*/
            btRunnable.setBtOpen(btOpen);
            handler.removeCallbacks(btRunnable);
            handler.postDelayed(btRunnable, 100);
            GlobalStateUtils.INSTANCE.setBtSwitchIsOpen(btOpen);
            return;
        }
        if (type == Event4DynamicBroadcastReceiver.TYPE_GPS) {
            boolean gpsOpen = event.gpsOpen;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEvent4DynamicBroadcastReceiver() gpsOpen = " + gpsOpen);
            }
            GpsChangeEvent.sendGpsChangeEvent(gpsOpen);
            return;
        }

        if (type == Event4DynamicBroadcastReceiver.TYPE_SCREEN) {
            int screenStatus = event.screenStatus;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEvent4DynamicBroadcastReceiver() screenStatus = " + screenStatus);
            }
            ScreenStatusChangeEvent.sendScreenStatusChangeEvent(ScreenStatusChangeEvent.Status.values()[screenStatus]);
            return;
        }

        if (type == Event4DynamicBroadcastReceiver.TYPE_NETWORK) {
            NetChangeEvent.sendNetChangeEvent();
        }
    }

    @Override
    public void onTrimMemory(int level) {
        super.onTrimMemory(level);
        onCustomTrimMemory(level);
        LogInfra.Log.i(TAG, "onTrimMemory() level = " + level);
        if (level == ComponentCallbacks2.TRIM_MEMORY_UI_HIDDEN) {
            LogInfra.Log.e(TAG, "进入后台");
        }
    }

    /**
     * @param level TRIM_MEMORY_RUNNING_MODERATE: 表示应用程序正常运行，并且不会被杀掉。 但是目前手机的内存已经有点低了，系统可能会开始根据LRU缓存规则来去杀死进程了。
     *              <p>
     *              TRIM_MEMORY_RUNNING_LOW     : 表示应用程序正常运行，并且不会被杀掉。但是目前手机的内存已经非常低了 ，
     *              我们应该去释放掉一些不必要的资源以提升系统的性能，同时这也会直接影响到我们应用程序的性能。
     *              <p>
     *              TRIM_MEMORY_RUNNING_CRITICAL :   表示应用程序仍然正常运行，但是系统已经根据LRU缓存规则杀掉了大部分缓存的进程了
     *              。这个时候我们应当尽可能地去释放任何不必要的资源，不然的话系统可能会继续杀掉所有缓存中的进程，
     *              并且开始杀掉一些本来应当保持运行的进程，比如说后台运行的服务。以上是当我们的应用程序正在运行时的回调，
     *              那么如果我们的程序目前是被缓存的，则会收到以下几种类型的回调：
     *              <p>
     *              TRIM_MEMORY_BACKGROUND    表示手机目前内存已经很低了，系统准备开始根据LRU缓存来清理进程。这个时候我们的程序在
     *              LRU缓存列表的最近位置，是不太可能被清理掉的，但这时去释放掉一些比较容易恢复的资源能够让手机的内存变得比较充足，
     *              从而让我们的程序更长时间地保留在缓存当中，这样当用户返回我们的程序时会感觉非常顺畅，而不是经历了一次重新启动的过程。
     *              <p>
     *              TRIM_MEMORY_MODERATE    表示手机目前内存已经很低了，并且我们的程序处于LRU缓存列表的中间位置，如果手机内存还得不到进一步释放的话，
     *              那么我们的程序就有被系统杀掉的风险了。
     *              <p>
     *              TRIM_MEMORY_COMPLETE    表示手机目前内存已经很低了，并且我们的程序处于LRU缓存列表的最边缘位置，
     *              系统会最优先考虑杀掉我们的应用程序，在这个时候应当尽可能地把一切可以释放的东西都进行释放。
     */
    public void onCustomTrimMemory(int level) {
        SafeLog.Companion.v(TAG, () -> "onCustomTrimMemory level:" + level);
        switch (level) {
            case TRIM_MEMORY_RUNNING_LOW:
                SafeLog.Companion.e(TAG, () -> "app低内存警告，需要手动释放一些内存 level:" + level);
                ShortMemoryMgr.INSTANCE.clearCacheAll();
                break;
            case TRIM_MEMORY_RUNNING_MODERATE:
                SafeLog.Companion.e(TAG, () -> "app低内存，可能触发LruCache回收");
                break;
        }
    }

    @Override
    public void appClose() {
        LogInfra.Log.i(TAG, "appClose()");
        ApplicationLifecycleManager.close();
        ClubM.getInstance.onClear();
        super.appClose();
    }

    @Override
    public void app2Foreground() {
        super.app2Foreground();
        LogInfra.Log.i(TAG, "app2Foreground()");
        ApplicationLifecycleManager.app2Foreground();
        /*App进入前台时，尝试进行动态模块的检测*/
        handler.postDelayed(() -> PfdModuleInit.INSTANCE.checkModuleInit(this, "Application.app2Foreground()"), 2000L);
    }

    @Override
    public void app2Background() {
        super.app2Background();
        LogInfra.Log.i(TAG, "app2Background()");
        ApplicationLifecycleManager.app2Background();
        needRefreshVideoList = true;
        needRefreshPostList = true;
        deskBackZone = true;
    }

    public void beDevicesList() {
        ApplicationLifecycleManager.onDevicesList();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onLogoutResultEvent(AccountEvent.LogoutResultEvent event) {
        boolean result = event.isResult();
        if (result) {
            beLogout();
        }
    }

    private void beLogout() {
        //关闭数据库
        AccountDatabase.closeDatabase();
        // 清除排序记录
        OrderConfig.config().clearRecord();
        NotifyManager.getInstance().closeAllNotify(getContext());
        ApplicationLifecycleManager.onLogout();
        /*清除testing club记录*/
        ClubM.getInstance.onClear();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventStartActivityForResult(EventStartActivityForResult event) {
        try {
            ActivityMgr.getInstance().getCurrentActivity().startActivityForResult(event.intent, event.requestCode);
        } catch (Exception e) {
            e.printStackTrace();
            EventBus.getDefault().post(new EventActivityResult(event.requestCode, Activity.RESULT_CANCELED, event.intent));
        }
    }

    /**
     * 用于执行bt的开关状态通知
     */
    private static class BtRunnable extends CaughtRunnable {
        private boolean btOpen;

        private void setBtOpen(boolean btOpen) {
            this.btOpen = btOpen;
        }

        @Override
        protected void runSafe() {
            BTStatusEvent.sendBTStatusEvent(btOpen);
        }
    }

    @Override
    public void addDynamicModuleInit(List<IApplication> applications) {
        if (applications == null || applications.isEmpty()) return;
        LogInfra.Log.i(TAG, "addDynamicModuleInit()");
        for (IApplication application : applications) {
            application.create();
            // 添加动态模块applicationImpl
            ApplicationLifecycleManager.registerApplicationLifecycleCallbacks(application);
        }
    }

    /**
     * 检查并执行自动清理缓存
     * 使用线程池执行后台任务，避免频繁创建线程
     */
    private void checkAutoCleanCache() {
        LogInfra.Log.i(TAG, "开始检查自动清理缓存");

        BACKGROUND_EXECUTOR.execute(() -> {
            try {
                LogInfra.Log.i(TAG, "执行自动清理缓存检查");
                kotlinx.coroutines.BuildersKt.runBlocking(
                        kotlinx.coroutines.Dispatchers.getIO(),
                        (scope, continuation) -> StorageManagerService.INSTANCE.checkAndExecuteAutoClean(continuation)
                );
                LogInfra.Log.i(TAG, "自动清理缓存检查完成");
            } catch (Exception e) {
                LogInfra.Log.e(TAG, "自动清理缓存执行失败: " + e.getMessage(), e);
                // 记录分析错误
                try {
                    AnalyticsRecorder.getInstance().recordBug("AutoCleanCache_Error_" + e.getClass().getSimpleName());
                } catch (Exception analyticsError) {
                    LogInfra.Log.e(TAG, "记录自动清理缓存错误失败", analyticsError);
                }
            }
        });
    }

    @Override
    protected void attachBaseContext(Context base) {
        super.attachBaseContext(base);
        SplitCompat.install(base);
    }
}