package com.govee.home.main.device.moment.musicfeast.v4

import android.app.Activity
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.asFlow
import androidx.lifecycle.map
import androidx.recyclerview.widget.GridLayoutManager
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.isVisible
import com.govee.base2kt.ext.visible
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2light.ac.diyNew.config.params.DiyColorParams
import com.govee.base2light.light.v1.ShowHintConfig
import com.govee.base2light.pact.feast.MusicFeastEffect
import com.govee.base2light.pact.feast.SubDevice
import com.govee.home.databinding.AppLayoutMusicFeastUiV4Binding
import com.govee.home.main.device.moment.musicfeast.EffectColorConfig
import com.govee.home.main.device.moment.musicfeast.aimusic.AIMusicVM
import com.govee.home.main.device.moment.musicfeast.aimusic.AiRecommendMode
import com.govee.home.main.device.moment.musicfeast.v2.Category
import com.govee.home.main.device.moment.musicfeast.v2.EffectDetailAdapter
import com.govee.home.main.device.moment.musicfeast.v2.FeastBrightness
import com.govee.home.main.device.moment.musicfeast.v2.FeastMaker
import com.govee.home.main.device.moment.musicfeast.v2.FeastMusicMode
import com.govee.home.main.device.moment.musicfeast.v2.IFeastApplyListener
import com.govee.home.main.device.moment.musicfeast.v2.LocalLastMusicEffect
import com.govee.home.main.device.moment.musicfeast.v2.MusicFeastM.gId
import com.govee.home.main.device.moment.musicfeast.v2.QuFengDetailAdapter
import com.govee.home.main.device.moment.musicfeast.v2.ViewModel4Feast
import com.govee.home.main.device.moment.ui.BrightnessDialog
import com.govee.mvvm.launchRepeatOnLifecycle
import com.govee.ui.R
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.ToastUtil
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map

/**
 *Create by xieyingwu on 2022/8/1
 * 音乐ui控件-V3版本
 * <p>1.曲风</p>
 * <p>2.光影秀</p>
 * <p>3.嘉年华</p>
 */
class MusicUIV4 : ConstraintLayout {
    companion object {
        const val TAG = "MusicUIV4"
    }

    private var dataBinding: AppLayoutMusicFeastUiV4Binding? = null

    private val categories = mutableListOf<Category>()

    private var adapter4QuFeng: QuFengDetailAdapter? = null

    private var adapter4GuangYingXiu: EffectDetailAdapter? = null
    private val layoutManager4GuangYingXiu by lazy {
        GridLayoutManager(context, 4)
    }
    private var adapter4JiaNianHua: EffectDetailAdapter? = null
    private val layoutManager4JiaNianHua by lazy {
        GridLayoutManager(context, 4)
    }

    /*盛宴数据*/
    private var viewModel4Feast: ViewModel4Feast? = null

    var applyListener: IFeastApplyListener? = null
    private var curSelectedMusicCode = 0

    private lateinit var firstColorSet: IntArray
    private var colorParams = DiyColorParams.simple(
        colorRange = intArrayOf(1, 8),
        mainColorSupportNoColor = false
    )

    private val aiMusicVM by (context as AppCompatActivity).viewModels<AIMusicVM>()

    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    init {
        /*构建布局*/
        initLayout()
        /*初始化RV-详情列表(光影秀/嘉年华)*/
        initRV4Detail()
        /*初始化RV-详情列表(曲风)*/
        initRv4QuFengDetail()
        /*初始化底部ui*/
        initBottomUi()
    }

    private fun initBottomUi() {
        dataBinding?.run {
            /*提示文案*/
            tvHint.clickDelay {
                tvHint.visibility = GONE
                recordHintNotShow()
            }
            val secondItemTabs =
                listOf(
                    ViewTabLayout.Tab(
                        ResUtil.getString(R.string.music_sub_model_power_label),
                        true
                    ),
                    ViewTabLayout.Tab(ResUtil.getString(R.string.music_sub_model_soft_label), false)
                )
            /*二级选项*/
            secondItemView.initTabs(
                tabs = secondItemTabs,
                onSelected = {
                    if (!subDeviceHadConnected()) {
                        ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                    } else {
                        applyListener?.changeSecondEffect(it == 0)
                    }
                })
            /*action二级选项*/
            val actionSecondTabs =
                listOf(
                    ViewTabLayout.Tab(ResUtil.getString(R.string.action_sub_a), true),
                    ViewTabLayout.Tab(ResUtil.getString(R.string.action_sub_b), false)
                )
            actionSecondItemView.initTabs(tabs = actionSecondTabs, onSelected = {
                if (!subDeviceHadConnected()) {
                    ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                } else {
                    viewModel4Feast?.feastMusicMode?.value?.run {
                        val action =
                            FeastMaker.quFengAction(this.musicType, this.musicCode, it == 0)
                        applyListener?.changeAction(action)
                    }
                }
            })

            diyColorView.setNeedApplyBtn {
                if (!subDeviceHadConnected()) {
                    ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                } else {
                    colorParams.getMainColorParams().second.run {
                        applyListener?.applyColors(this)
                    }
                }
            }
            diyColorView.hideChooseColorFromImage()
            diyColorView.mainValueChangeCallback = {
                colorParams.setMainColors(it)
            }
            diyColorView.initParams(context as Activity, colorParams, viewModel4Feast?.feast?.value?.centerDevice?.sku ?: "")

        }
    }

    private fun recordHintNotShow() {
        viewModel4Feast?.run {
            feastMusicMode.value?.run {
                SafeLog.i(TAG) { "recordHintNotShow() musicType = $musicType ; musicCode = $musicCode" }
                ShowHintConfig.read().recordNotShow4H1163(musicType, musicCode)
            }
        }
    }

    private fun initRv4QuFengDetail() {
        adapter4QuFeng = QuFengDetailAdapter().apply {
            /*设置数据源*/
            val effects = FeastMaker.makeAiQuFengEffects()
            setList(effects)
            setOnItemClickListener { _, _, pos ->
                if (!subDeviceHadConnected()) {
                    ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                    return@setOnItemClickListener
                }
                val musicCode = effects[pos].musicCode
                toApplyQuFeng(musicCode)
            }
        }
        dataBinding?.run {
            rv4QuFengDetailList.adapter = adapter4QuFeng
            rv4QuFengDetailList.layoutManager = GridLayoutManager(context, 4)
        }
    }

    private fun toApplyQuFeng(musicCode: Int) {
        SafeLog.i(TAG) { "toApplyQuFeng() musicCode = $musicCode" }
        applyListener?.applyQuFeng(musicCode)
    }

    /**
     * 子设备是否有已连接上的
     * @param isHandleDealHint 是否需要做限制处理
     */
    private fun subDeviceHadConnected(isHandleDealHint: Boolean = true): Boolean {
        SafeLog.i(TAG) { "subDeviceHadConnected  viewModel4Feast?.feast?.value = ${viewModel4Feast?.feast?.value}" }
        if (isHandleDealHint) {
            viewModel4Feast?.feast?.value?.let {
                if (it.hintSubDeviceState()) {
                    return true
                }
            }
        }
        return viewModel4Feast?.subDevices?.value?.run {
            if (this.isEmpty()) return false
            for (subDevice in this) {
                if (subDevice.status == SubDevice.status_connected) return true
            }
            return false
        } ?: false
    }

    private fun initRV4Detail() {
        adapter4GuangYingXiu = EffectDetailAdapter().apply {
/*            val effects = FeastMaker.makeLightShowEffects(isDark = false)
            *//*设置数据源*//*
            setList(effects)*/
            /*设置点击事件*/
            setOnItemClickListener { _, _, pos ->
                val musicCode = data[pos].effectCode
                /*统计应用次数*/
                AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.click_1163_model)
                AnalyticsRecorder.getInstance().recordTimes(
                    EventKey.use_count,
                    ParamKey.click_1163_model_light_show,
                    musicCode.toString()
                )
                /*为了兼容之前出现的异常场景（设备通用问题，之前主设备 没有连接子设备应用"频谱"灯效时设备会出现异常现象，所以加此判断，后续 sku 保持统一即可，可以咨询测试@余宇锋）*/
                if (!subDeviceHadConnected(!FeastMaker.isPinPuMusic(musicCode))) {
                    ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                    return@setOnItemClickListener
                }
                toApplyGuangYingXiu(musicCode)
            }
        }
        adapter4JiaNianHua = EffectDetailAdapter().apply {
            /*val effects = FeastMaker.makeCarnivalEffects(isDark = false)
            *//*设置数据源*//*
            setList(effects)*/
            /*设置点击事件*/
            setOnItemClickListener { _, _, pos ->
                val musicCode = data[pos].effectCode
                /*统计应用次数*/
                AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.click_1163_model)
                AnalyticsRecorder.getInstance().recordTimes(
                    EventKey.use_count,
                    ParamKey.click_1163_model_carnival,
                    musicCode.toString()
                )
                if (!subDeviceHadConnected()) {
                    ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                    return@setOnItemClickListener
                }
                toApplyJiaNianHua(musicCode)
            }
        }
    }

    //初始化光影秀、嘉年华数据
    fun initMusicModeList(
        lightShowEffects: List<MusicFeastEffect>,
        carnivalEffects: List<MusicFeastEffect>
    ) {
        adapter4GuangYingXiu?.setList(lightShowEffects)
        adapter4JiaNianHua?.setList(carnivalEffects)
    }

    private fun initObserver() {
        val activity = context as AppCompatActivity
        viewModel4Feast?.run {
            feastDetailData.observe(activity) {
                aiMusicVM.initData(data = it)
                //这里肯定有centerDevice
                dataBinding?.diyColorView?.initParams(context as Activity, colorParams, it.centerDevice?.sku ?: "")
            }
        }

        val vm = viewModel4Feast
        if (vm == null) {
            SafeLog.e(TAG) { "initObserver() vm 初始话错误" }
            return
        }


        val connectStatusFlow = vm.feast.map { it.bleConnected() && it.feastOpen }.asFlow()
        val aiApplyStatusFlow = aiMusicVM.uiStateFlow.map { it.aiStatus }
        val openFlow = aiMusicVM.uiStateFlow.map { it.funcOpen }
        val combineFlow =
            combine(connectStatusFlow, aiApplyStatusFlow, openFlow) { connected, status, open ->
                Triple(connected, status, open)
            }.distinctUntilChanged()
        activity.launchRepeatOnLifecycle(Lifecycle.State.STARTED) {
            combineFlow.collect { (connected, status, open) ->
                //蓝牙连接+应用中状态调用应用效果
                val needApply = connected && open && (status is AIMusicVM.AIStatus.Apply)
                if (needApply) {
                    applyByAiRecommend(status.aiRecommendMode)
                }
            }
        }
        activity.launchRepeatOnLifecycle(Lifecycle.State.STARTED) {
            aiMusicVM.uiStateFlow.map { it.funcOpen }.distinctUntilChanged().collect { funcOpen ->
                dataBinding?.run {
                    clMusicChange.visibleByBoolean(!funcOpen)
                    diyColorView.visibleByBoolean(!funcOpen)
                    vDivider.visibleByBoolean(!funcOpen)
                }
            }
        }
    }

    //更新来自AI推荐的模式
    private fun applyByAiRecommend(bean: AiRecommendMode) {
        SafeLog.i(TAG) { "applyByAiRecommend() $bean" }
        colorParams.setMainColors(bean.getRGBColor().toIntArray())
        dataBinding?.diyColorView?.updateColor(colorParams)
        dataBinding?.diyColorView?.hideApplyBtn()

        viewModel4Feast?.feastMusicMode?.value?.apply {
            EffectColorConfig.read()
                .putColor4H1163(
                    gId,
                    bean.musicType,
                    bean.musicCode,
                    subAction,
                    bean.getRGBColor().toIntArray()
                )
        }
        if (bean.musicType == FeastMusicMode.music_type_guangyingxiu) {
            toApplyGuangYingXiu(bean.musicCode)
            return
        }

        if (bean.musicType == FeastMusicMode.music_type_jianianhua) {
            toApplyJiaNianHua(bean.musicCode)
            return
        }
    }

    private fun toApplyJiaNianHua(musicCode: Int) {
        SafeLog.i(TAG) { "toApplyJiaNianHua() musicCode = $musicCode" }
        applyListener?.applyJiaNianHua(musicCode)
    }

    private fun toApplyGuangYingXiu(musicCode: Int) {
        SafeLog.i(TAG) { "toApplyGuangYingXiu() musicCode = $musicCode" }
        applyListener?.applyGuangYingXiu(musicCode)
    }

    /**
     * 更新支持的分类
     */
    fun updateCategories(musicSupportAi: Boolean) {
        SafeLog.i(TAG) { "updateCategories() musicSupportAi = $musicSupportAi" }
        /*初始化RV-分类*/
        initRV4Category(musicSupportAi)
    }

    private fun initRV4Category(musicSupportAi: Boolean) {
        categories.addAll(
            Category.makeCategories(musicSupportAi).filter { !it.categoryName.isNullOrEmpty() })
        dataBinding?.run {
            rv4Category.initTabs(
                tabs = categories
                    .mapIndexed { index, category ->
                        ViewTabLayout.Tab(category.categoryName ?: "")
                    },
                onSelected = {
                    if (!subDeviceHadConnected()) {
                        ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                    } else {
                        changeCategory(it)
                    }
                })
        }
    }

    private fun changeCategory(pos: Int) {
        val pos4RealCategory = categories[pos].pos4Real
        /*同一个分类-无需切换效果*/
        val musicType = when (pos4RealCategory) {
            Category.CATEGORY_POS_QUFENG -> FeastMusicMode.music_type_qufeng
            Category.CATEGORY_POS_GUANGYINGXIU -> FeastMusicMode.music_type_guangyingxiu
            else -> FeastMusicMode.music_type_jianianhua
        }
        SafeLog.i(TAG) { "changeCategory() musicType = $musicType" }
        viewModel4Feast?.feast?.value?.run {
            val lastModeMusicEffect =
                LocalLastMusicEffect.read().lastModeMusicEffect(this.gId, musicType)
            if (noApplyChangeCategory()) {
                applyListener?.noApplyChangeLastMusicMode(
                    musicType,
                    curSelectedMusicCode
                )
                return
            }
            applyListener?.applyLastMusicMode(
                musicType,
                lastModeMusicEffect.musicCode,
                lastModeMusicEffect.dynamic
            )
        }
    }


    private fun chooseCategory(pos: Int): Boolean {
        SafeLog.i(TAG) { "chooseCategory() pos = $pos" }
        val category = categories[pos]
        val pos4Category = category.pos4Real
        val notifyCategoryChoose = dataBinding?.rv4Category?.chooseTab(pos) ?: false
        SafeLog.i(TAG) { "chooseCategory() pos4Category = $pos4Category; notifyCategoryChoose = $notifyCategoryChoose" }
        if (notifyCategoryChoose) {
            ui4Category(pos4Category)
        }
        return notifyCategoryChoose
    }

    private fun initLayout() {
        dataBinding = DataBindingUtil.inflate(
            LayoutInflater.from(context),
            com.govee.home.R.layout.app_layout_music_feast_ui_v4,
            this,
            true
        )
        dataBinding?.run {
            ivIcon4BrightnessUniteSwitchSelected = false
            /*亮度条*/
            seekBar4Brightness.run {
                setProgressRange(intArrayOf(1, 100))
                setStartIconId(-1)
                setEndIconId(R.mipmap.slider_btn_brightness_02)
                setProgressBgColor(R.color.ui_seekbar_style_29)
                canClickListener = {
                    subDeviceHadConnected().also {
                        if (!it) {
                            ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                        }
                    }
                }
                clickListener = { progress: Int, up: Boolean ->
                    if (up) {
                        toApplyBrightness(progress)
                    }
                }
            }

            /*灵敏度条*/
            seekBar4Sensitivity.run {
                setProgressRange(intArrayOf(1, 100))
                setStartIconId(-1)
                setEndIconId(R.mipmap.slider_btn_sensitivity)
                setProgressColor(R.color.ui_seekbar_style_30_1)
                setProgressBgColor(R.color.ui_seekbar_style_30)
                canClickListener = {
                    subDeviceHadConnected().also {
                        if (!it) {
                            ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                        }
                    }
                }
                clickListener = { progress: Int, up: Boolean ->
                    if (up) {
                        toApplySensitivity(progress)
                    } else {
                        if (!subDeviceHadConnected()) {
                            ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                        }
                    }
                }
            }

            /*亮度开关*/
            ivIcon4BrightnessUniteSwitch.clickDelay {
                if (subDeviceHadConnected()) {
                    applyListener?.switchBrightnessUnite()
                } else {
                    ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                }
            }
            /*亮度单独控制*/
            ivIcon4BrightnessArrow.clickDelay {
                val withMainDevice =
                    viewModel4Feast?.feast?.value?.brightnessWithMainDevice() ?: false
                SafeLog.i(TAG) { "ivIcon4BrightnessArrow--> withMainDevice = $withMainDevice" }
                if (withMainDevice) {
                    op4BrightnessWithMainDevice()
                    return@clickDelay
                }
                if (!subDeviceHadConnected()) {
                    ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                    return@clickDelay
                }
                viewModel4Feast?.feastBrightness?.value?.run {
                    viewModel4Feast?.subDevices?.value?.let {
                        if (it.isEmpty()) return@clickDelay
                        val brightnessModelList = mutableListOf<BrightnessDialog.BrightnessModel>()
                        val minLen = subDeviceBrightnessSet.size.coerceAtMost(it.size)
                        var index = 0
                        for (subDevice in it) {
                            brightnessModelList.add(
                                BrightnessDialog.BrightnessModel(
                                    subDevice.subDevice.goodsType,
                                    subDevice.subDevice.sku,
                                    subDevice.subDevice.spec,
                                    subDevice.subDevice.deviceName,
                                    subDeviceBrightnessSet[index]
                                )
                            )
                            index++
                            if (index >= minLen) break
                        }
                        BrightnessDialog(
                            context,
                            brightnessModelList,
                            object : BrightnessDialog.BrightnessListener {
                                override fun brightnessResult(brightness: Int, index: Int) {
                                    applyListener?.changeBrightness(brightness, index + 1)
                                }
                            }).show()
                    }
                }
            }
            /*ai曲风自动*/
            ivIcon4AiSwitch.clickDelay {
                if (!subDeviceHadConnected()) {
                    ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
                    return@clickDelay
                }
                if (ivIcon4AiSwitch.isSelected) {
                    AnalyticsRecorder.getInstance().recordTimes(
                        EventKey.use_count,
                        ParamKey.click_1163_ai_music_switch,
                        ParamFixedValue.on_2_off
                    )
                    viewModel4Feast?.feast?.value?.run {
                        val lastMusicCode =
                            LocalLastMusicEffect.read().lastAiMusicCode(gId)
                        SafeLog.i(TAG) { "ivIcon4AiSwitch--> clickDelay() lastMusicCode = $lastMusicCode" }
                        applyListener?.applyQuFeng(lastMusicCode)
                    }
                } else {
                    AnalyticsRecorder.getInstance().recordTimes(
                        EventKey.use_count,
                        ParamKey.click_1163_ai_music_switch,
                        ParamFixedValue.off_2_on
                    )
                    applyListener?.applyQuFeng(FeastMaker.qu_feng_music_code_auto)
                }
            }
        }
    }

    private fun op4BrightnessWithMainDevice() {
        if (!subDeviceHadConnected()) {
            ToastUtil.getInstance().toast(R.string.pickupbox_connect_device_first)
            return
        }
        viewModel4Feast?.feastBrightness?.value?.run {
            val brightnessModelList = mutableListOf<BrightnessDialog.BrightnessModel>()
            /*主设备亮度*/
            viewModel4Feast?.feast?.value?.centerDevice?.let { centerDevice ->
                brightnessModelList.add(
                    BrightnessDialog.BrightnessModel(
                        centerDevice.goodsType,
                        centerDevice.sku,
                        "",
                        centerDevice.deviceName,
                        this.brightness
                    )
                )
            }
            /*子设备亮度*/
            viewModel4Feast?.subDevices?.value?.let {
                val minLen = subDeviceBrightnessSet.size.coerceAtMost(it.size)
                var index = 0
                for (subDevice in it) {
                    brightnessModelList.add(
                        BrightnessDialog.BrightnessModel(
                            subDevice.subDevice.goodsType,
                            subDevice.subDevice.sku,
                            subDevice.subDevice.spec,
                            subDevice.subDevice.deviceName,
                            subDeviceBrightnessSet[index]
                        )
                    )
                    index++
                    if (index >= minLen) break
                }
            }
            BrightnessDialog(
                context,
                brightnessModelList,
                object : BrightnessDialog.BrightnessListener {
                    override fun brightnessResult(brightness: Int, index: Int) {
                        applyListener?.changeBrightness(brightness, index)
                    }
                }).show()
        }
    }

    private fun toApplySensitivity(sensitivity: Int) {
        SafeLog.i(TAG) { "toApplySensitivity() sensitivity = $sensitivity" }
        applyListener?.changeSensitivity(sensitivity)
    }

    private fun toApplyBrightness(brightness: Int) {
        SafeLog.i(TAG) { "toApplyBrightness() brightness = $brightness" }
        applyListener?.changeBrightness(brightness, 0)
    }


    private fun ui4Category(categoryPos: Int) {
        SafeLog.i(TAG) { "ui4Category() categoryPos = $categoryPos" }
        when (categoryPos) {
            Category.CATEGORY_POS_QUFENG -> {
                dataBinding?.run {
                    rv4DetailList.visibility = GONE
                    group4AiAuto.visibility = VISIBLE
                }
            }

            Category.CATEGORY_POS_JIANIANHUA -> {
                dataBinding?.run {
                    rv4DetailList.visibility = VISIBLE

                    group4AiAuto.visibility = GONE
                    group4AiAutoOpen.visibility = GONE
                    group4AiAutoClose.visibility = GONE

                    rv4DetailList.layoutManager = layoutManager4JiaNianHua
                    rv4DetailList.adapter = adapter4JiaNianHua
                    vDivider.visible()
                }
            }

            Category.CATEGORY_POS_GUANGYINGXIU -> {
                dataBinding?.run {
                    rv4DetailList.visibility = VISIBLE

                    group4AiAuto.visibility = GONE
                    group4AiAutoOpen.visibility = GONE
                    group4AiAutoClose.visibility = GONE

                    rv4DetailList.layoutManager = layoutManager4GuangYingXiu
                    rv4DetailList.adapter = adapter4GuangYingXiu
                    vDivider.visible()
                }
            }
        }
    }

    private fun ui4QuFeng(aiAutoOpen: Boolean) {
        SafeLog.i(TAG) { "ui4QuFeng() aiAutoOpen = $aiAutoOpen" }
        dataBinding?.run {
            ivIcon4AiSwitch.isSelected = aiAutoOpen
            group4AiAutoOpen.visibility = if (aiAutoOpen) VISIBLE else GONE
            group4AiAutoClose.visibility = if (aiAutoOpen) GONE else VISIBLE
            vDivider.visibleByBoolean(!aiAutoOpen)
        }
    }

    /**
     * 注册viewModel
     */
    fun registerViewModel(viewModel: ViewModel4Feast?) {
        this.viewModel4Feast = viewModel
        initObserver()
    }

    private fun categoryPos(musicType: Int): Int {
        for ((index, category) in categories.withIndex()) {
            val pos4Real = category.pos4Real
            when (musicType) {
                FeastMusicMode.music_type_qufeng -> {
                    if (pos4Real == Category.CATEGORY_POS_QUFENG) return index
                }

                FeastMusicMode.music_type_guangyingxiu -> {
                    if (pos4Real == Category.CATEGORY_POS_GUANGYINGXIU) return index
                }

                FeastMusicMode.music_type_jianianhua -> {
                    if (pos4Real == Category.CATEGORY_POS_JIANIANHUA) return index
                }
            }
        }
        return 0
    }

    /**
     * 音乐模式UI
     */
    fun musicModeUi(mode: FeastMusicMode) {
        /*存储当前效果到本地*/
        viewModel4Feast?.feast?.value?.run {
            LocalLastMusicEffect.read().saveLastModeMusicEffect(this.gId, mode)
        }
        /*灵敏度*/
        sensitivityUi(mode.sensitivity)
        /*分类*/
        val musicType = mode.musicType
        val musicCode = mode.musicCode
        this.curSelectedMusicCode = musicCode
        val categoryPos = categoryPos(musicType)
        SafeLog.i(TAG) { "categoryUi() musicCode = $musicCode" }
        SafeLog.i(TAG) { "categoryUi() musicType = $musicType ; categoryPos = $categoryPos" }
        val chooseCategory = chooseCategory(categoryPos)
        SafeLog.i(TAG) { "categoryUi() chooseCategory = $chooseCategory" }
        when (musicType) {
            FeastMusicMode.music_type_qufeng -> {
                if (musicCode == FeastMaker.qu_feng_music_code_auto) {
                    ui4QuFeng(true)
                } else {
                    ui4QuFeng(false)
                    adapter4QuFeng?.updateSelectMusicCode(musicCode)
                }
            }

            FeastMusicMode.music_type_jianianhua -> {
                adapter4JiaNianHua?.updateSelecMusicCode(musicCode)
                dataBinding?.run {
                    rv4DetailList.layoutManager = layoutManager4JiaNianHua
                    rv4DetailList.adapter = adapter4JiaNianHua
                }
            }

            else -> {
                adapter4GuangYingXiu?.updateSelecMusicCode(musicCode)
                dataBinding?.run {
                    rv4DetailList.layoutManager = layoutManager4GuangYingXiu
                    rv4DetailList.adapter = adapter4GuangYingXiu
                }
            }
        }
        ui4BottomView(mode)
        aiMusicVM.toggleApplyResult()
    }

    private fun ui4BottomView(mode: FeastMusicMode) {
        val musicType = mode.musicType
        val musicCode = mode.musicCode
        SafeLog.i(TAG) { "ui4BottomView() musicType = $musicType ; musicCode = $musicCode" }
        dataBinding?.run {
            /*提示文案*/
            var showHint =
                FeastMaker.supportEffectBySubDeviceOrderHint(musicType, musicCode)
            if (showHint) {
                showHint = !ShowHintConfig.read().isNotShowHint4H1163(musicType, musicCode)
            }
            SafeLog.i(TAG) { "ui4BottomView() showHint = $showHint" }
            tvHint.visibleByBoolean(showHint)
            /*二级选项*/
            var supportSecondItem = FeastMaker.supportSecondItem(musicType, musicCode)
            viewModel4Feast?.feast?.value?.let {
                if (it.noApplyChangeCategory()) {
                    if (viewModel4Feast?.curMusicType?.value != musicType) {
                        supportSecondItem = false
                    }
                }
            }
            SafeLog.i(TAG) { "ui4BottomView() supportSecondItem = $supportSecondItem ; isDynamic = ${mode.isDynamic}" }
            secondItemView.visibleByBoolean(supportSecondItem)
            if (secondItemView.isVisible()) {
                secondItemView.chooseTab(if (mode.isDynamic) 0 else 1)
            }
            /*嘉年华的提示文案*/
            tvHint4JiaNianHua.visibleByBoolean(musicType == FeastMusicMode.music_type_jianianhua)
            /*动作选项*/
            val supportActionSecondItem = FeastMaker.supportActionSecondItem(musicType, musicCode)
            SafeLog.i(TAG) { "ui4BottomView() supportActionSecondItem = $supportActionSecondItem" }
            actionSecondItemView.visibleByBoolean(supportActionSecondItem)
            if (actionSecondItemView.isVisible()) {
                val quFengActionMatchLeft =
                    FeastMaker.quFengActionMatchLeft(musicCode, mode.subAction)
                actionSecondItemView.chooseTab(if (quFengActionMatchLeft) 0 else 1)
            }
            /*颜色选项*/
            val musicColorVisible = FeastMaker.supportColorConfig(
                musicType,
                musicCode
            ) && !aiMusicVM.uiStateFlow.value.funcOpen
            SafeLog.i(TAG) { "ui4BottomView() musicColorVisible = $musicColorVisible" }

            diyColorView.visibleByBoolean(musicColorVisible)
            if (diyColorView.isVisible()) {
                val minColorNum =
                    FeastMaker.quFengActionMinColorNum(musicType, mode.subAction)
                SafeLog.i(TAG) { "ui4BottomView() minColorNum = $minColorNum" }
                colorParams.simpleColorRange = intArrayOf(minColorNum, 8)

                val gId = viewModel4Feast?.feast?.value?.gId ?: 0
                if (!this@MusicUIV4::firstColorSet.isInitialized) {
                    //只读取一个音乐场景颜色，切换场景颜色不变
                    firstColorSet = EffectColorConfig.read()
                        .getColor4H1163(gId, musicType, musicCode, mode.subAction)
                    colorParams.setMainColors(firstColorSet)
                    diyColorView.updateColor(colorParams)
                    diyColorView.hideApplyBtn()
                }
            }
        }
    }

    private fun sensitivityUi(sensitivity: Int) {
        SafeLog.i(TAG) { "sensitivityUi() sensitivity = $sensitivity" }
        dataBinding?.run {
            seekBar4Sensitivity.setProgress(0.coerceAtLeast(sensitivity))
        }
    }

    fun brightnessUi(feastBrightness: FeastBrightness) {
        val brightnessUnite = feastBrightness.brightnessUnite
        SafeLog.i(TAG) { "brightnessUi() brightnessUniteOpen = $brightnessUnite" }
        dataBinding?.run {
            ivIcon4BrightnessUniteSwitchSelected = brightnessUnite
            ivIcon4BrightnessUniteSwitch.isSelected = brightnessUnite
            if (brightnessUnite) {
                SafeLog.i(TAG) { "brightnessUi() $feastBrightness.brightness" }
                seekBar4Brightness.setProgress(0.coerceAtLeast(feastBrightness.brightness))
            }
        }
    }

    fun getSnapshotContainer() = dataBinding?.snapshotContainer
}