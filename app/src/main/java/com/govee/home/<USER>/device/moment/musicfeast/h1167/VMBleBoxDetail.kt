package com.govee.home.main.device.moment.musicfeast.h1167

import androidx.lifecycle.viewModelScope
import com.govee.base2home.iot.protype.v2.IotMsgV2
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.update.event.VersionUIEvent
import com.govee.base2light.ble.AbsBle
import com.govee.base2light.ble.controller.AbsComposeControllerEvent
import com.govee.base2light.ble.controller.IComposeControllerResultListener
import com.govee.base2light.kt.comm.AbsViewMode4OpWithOtaCheck
import com.govee.ble.BleController
import com.govee.h1162.ble.Ble
import com.govee.h1162.h1168.Event4DeleteFeast
import com.govee.h1162.h1168.Event4DeleteFeastRes
import com.govee.home.feast.AcFeastList
import com.govee.home.main.device.moment.musicfeast.h1167.controller.Compose4ReadVersionInfo
import com.govee.home.main.device.moment.musicfeast.h1168.DeviceInfo
import com.govee.home.main.device.moment.musicfeast.h1168.musicFeastApiServer
import com.govee.home.main.device.moment.musicfeast.v2.ble.ComposeController4DeleteMainDevice
import com.govee.mvvm.ext.requestInGlobalScope
import com.govee.ui.ac.EventInfoUpdate
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import com.kk.taurus.playerbase.utils.NetworkUtils
import com.liveeventbus.LiveEventBus
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

class VMBleBoxDetail : AbsViewMode4OpWithOtaCheck<DeviceInfo>() {
    //是否已经读取过软硬件版本信息并检查OTA
    var checkOta = false
    fun init(deviceInfo: DeviceInfo) {
        this.deviceInfo = deviceInfo
        ble(true)
        registerEventBus(true)
    }

    override val ble: AbsBle
        get() = Ble.getInstance

    override fun connectBleSuc() {
    }

    fun readSoftInfo(goodsType: Int?) {
        if (!GoodsType.isMusicBoxProduct(goodsType ?: 0)) {
            return
        }
        viewModelScope.launch {
            if (checkOta) {
                changeBleStatus(connect_status_suc)
                return@launch
            }
            //延时3S让heart先跑（读取到电量），否则快速点击OTA弹窗会报低电量
            checkOta = true
            delay(2000)
            for (i in 0 until 15) {
                delay(1000)
                if (isComposeBleCommInIdle()) {
                    break
                }
            }

            afterBleConnectSuc4ReadInfo(
                Compose4ReadVersionInfo(
                    otaCheckType() != ota_check_normal, {
                        deviceInfo?.updateDspVersion(it.dspVersion)
                        compareUpdateBleVersion(it.versionSoft, it.versionHard)
                        changeBleStatus(connect_status_suc)
                        checkOta()
                    }, {
                        SafeLog.i(TAG) { "request device all info resultFail" }
                        changeBleStatus(connect_status_fail)
                    })
            )
        }
    }


    /**
     * @param event 删除设备事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventDeviceDelete(event: Event4DeleteFeast) {
        if (event.directDeleteFromServer) {
            deleteFeastFromServer()
        } else {
            if (isConnectSuc()) {
                deleteFeast()
            } else {
                Event4DeleteFeastRes.send(false)
            }
        }
    }

    /**
     * 删除音乐盛宴
     */
    fun deleteFeast() {
        /*删除盛宴需要先通知设备，再调用接口*/
        if (NetworkUtils.isNetConnected(BaseApplication.getContext())) {
            ComposeController4DeleteMainDevice(
                object : IComposeControllerResultListener {
                    override fun onResult(event: AbsComposeControllerEvent) {
                        val result = event.result
                        SafeLog.i(TAG) { "onResult()--ComposeController4DeleteMainDevice result = $result" }
                        if (result) {
                            deleteFeastFromServer()
                            return
                        }
                        /*操作失败*/
                        Event4DeleteFeastRes.send(false)
                    }
                }).apply { doCommController(this) }
        } else {
            Event4DeleteFeastRes.send(false)
        }
    }

    /**
     * 从服务器删除盛宴
     */
    fun deleteFeastFromServer() {
        requestInGlobalScope(
            {
                musicFeastApiServer.deleteFeast(
                    feastNm, gId
                )
            }, success = {
                Event4DeleteFeastRes.send(true)
                LiveEventBus.get<Boolean>(AcFeastList.feast_data_change)
                    .post(true)
            },
            error = {
                Event4DeleteFeastRes.send(false)
            })
    }

    override fun supportIotComm(): Boolean {
        return false
    }

    override fun parseIotMsg4Online(msgV2: IotMsgV2, jsonStr: String?) {
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onCheckVersion(event: VersionUIEvent) {
        if (!event.isSameDevice(deviceInfo?.sku, deviceInfo?.device)) return
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onCheckVersion()")
        }
        if (!BleController.getInstance().isConnected) {
            if (LogInfra.openLog()) {
                LogInfra.Log.w(TAG, "蓝牙未连接,清空升级信息")
            }
            return
        }
        deviceInfo?.run {
            EventInfoUpdate.sendEventInfoUpdate(
                sku, device, deviceName, versionHard, versionSoft,
                event.hardVersion, dspVersion, event.aiVersion
            )
        }
    }

    override fun otaCheckType(): Int {
        val goodsType = deviceInfo?.goodsType
        return if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_AI_MUSIC_BOX
            || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_MUSIC_BOX_1167
            || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_MUSIC_BOX_1168
        ) {
            ota_check_ai_with_dsp
        } else {
            ota_check_normal
        }
    }
}
