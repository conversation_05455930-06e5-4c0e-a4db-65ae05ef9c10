package com.govee.community.video.comment

import android.annotation.SuppressLint
import android.content.Context
import android.text.Editable
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.WindowManager
import android.widget.RelativeLayout
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import androidx.recyclerview.widget.DefaultItemAnimator
import androidx.recyclerview.widget.LinearLayoutManager
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.community.CommunityManager
import com.govee.base2home.community.post.ReportDialog
import com.govee.base2home.config.Base2homeConfig
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.databinding.DialogComment4VideoBinding
import com.govee.base2home.databinding.DialogComment4VideoHeaderBinding
import com.govee.base2home.main.user.SavvyUserManager
import com.govee.base2home.translate.TranslateBean
import com.govee.base2home.translate.TranslateUtil.checkShow
import com.govee.base2home.translate.TranslateUtil.getDefaultTranslate
import com.govee.base2home.util.CaughtRunnable
import com.govee.base2home.util.ClickUtil
import com.govee.base2home.util.EditTextWatcherImp
import com.govee.base2home.util.ImeHEvent
import com.govee.base2home.util.InputUtil
import com.govee.base2home.util.StrUtil
import com.govee.base2home.util.TimeFormatM
import com.govee.base2light.following.net.NetConstant
import com.govee.base2light.util.ObjectUtils
import com.govee.bean.community.AtUser
import com.govee.bean.community.CommunityVideo
import com.govee.community.manager.AccountManager.checkSUClick
import com.govee.community.video.comment.Comment.MoreComment.Companion.transitionComment2SubComment
import com.govee.mvvm.network.NetworkUtil
import com.govee.ui.R
import com.govee.ui.dialog.ConfirmDialog
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.dialog.BaseEventDialog
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.NetUtil
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.ToastUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.max
import kotlin.math.min

/**
 * 视频详情页，评论dialog$
 */
class VideoCommentDialog(context: Context, video: CommunityVideo, reply: Int, showIme: Boolean, listener: CommentListener?) :
    BaseEventDialog(context) {
    private var binding: DialogComment4VideoBinding? = null
    private val videoId: Int
    private val reply: Int
    private val des: String?
    private val transactions = Transactions()
    private var adapter: Adapter? = null
    private var atUser: AtUser? = null
    private val showIme: Boolean

    //是否可以点击头像进行跳转
    private var canClickHeader = true
    private var listener: CommentListener?
    private var commentOpListener: CommentOpListener? = null
    private var llm: LinearLayoutManager? = null
    private val llm2: LinearLayoutManager? = null
    private var isMoreCommentUI = false //二级评论页面
    private var moreCommentMain: Comment? = null //二级评论的主评论
    private var needUpdateCommentList = false //二级评论返回一级评论是否刷新界面（删除评论，点赞，发评论）
    private var replyCommentId = -1 //-1默认一级评论
    private val contentMap = HashMap<Int, String>() //储存草稿,key是回复的commentId，-1表示一级评论
    private var curUIType = UI_TYPE_DATA
    private var allCommentNum = 0
    private var lastCommentId = -1
    private var lastSecondCommentId = -1
    private var headerBinding: DialogComment4VideoHeaderBinding? = null
    private var scrollToComment = false
    private var onlyShowContent = false
    private var commentViewModel: CommentViewModel? = null

    init {
        ignoreBackPressed()
        changeDialogOutside(false)
        this.videoId = video.videoId
        this.des = video.des
        this.reply = reply
        this.showIme = showIme
        this.listener = listener
        fullScreen()
        initCommentListView(video)
        /*限制字符串长度*/
        StrUtil.setInputMax(binding?.commentEdit, 1000)
        initListener()
    }

    fun setScrollToComment(scrollToComment: Boolean): VideoCommentDialog {
        this.scrollToComment = scrollToComment
        return this
    }

    fun setCanIntoDetail(canIntoDetail: Boolean) {
        adapter?.setCanIntoDetail(canIntoDetail)
    }

    private fun initCommentListView(video: CommunityVideo) {
        /*一级评论*/
        adapter = Adapter(this)
        adapter?.run {
            setListener(createListener())
            setNewInstance(ArrayList())
            //工坊音乐创作不显示评论
            if (video.isWorkshopVideo()) {
                onlyShowContent = true
            }
            binding?.run {
                list.setHasFixedSize(true)
                list.adapter = adapter
                list.clearAnimation()
                val itemAnimator = list.itemAnimator
                if (itemAnimator is DefaultItemAnimator) {
                    itemAnimator.supportsChangeAnimations = false
                }
                llm = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
                list.layoutManager = llm
            }
            initHeaderView(video)
            headerWithEmptyEnable = true
            headerBinding?.root?.let { addHeaderView(it) }
            setEmptyView(com.govee.base2home.R.layout.dialog_comment_4_video_empty)
        }
    }

    private fun initListener() {
        binding?.run {
            /*初始化ui*/
            netErrorContainer.setListener {
                updateUi(UI_TYPE_LOADING)
                if (isMoreCommentUI) {
                    getMoreComments(-1)
                } else {
                    getRelatedComments(-1)
                }
            }
            listenerBackPressed {
                if (isMoreCommentUI && curUIType != UI_TYPE_LOADING) {
                    onClickBtnBack()
                }
            }

            commentEdit.addTextChangedListener(object : EditTextWatcherImp() {
                override fun afterTextChanged(s: Editable) {
                    checkSendBtnEnable()
                }
            })
            commentEdit.setListener { atUser = null }
            commentEdit.setCanDeletePrefix(false) //不可删除前缀

            /*下拉刷新*/
            swipe1.setOnRefreshListener {
                isMoreCommentUI = false
                getRelatedComments(-1)
            }
            swipe1.setDisableContentWhenLoading(true)
            swipe1.setDisableContentWhenRefresh(true)
            /*上拉加载更多*/
            swipe1.setOnLoadMoreListener {
                if (adapter?.data?.isEmpty() == true) {
                    swipe1.finishLoadMore()
                    return@setOnLoadMoreListener
                }
                isMoreCommentUI = false
                getRelatedComments(lastCommentId)
            }
            btnClose.setOnClickListener(View.OnClickListener {
                if (ClickUtil.getInstance.clickQuick()) return@OnClickListener
                hide()
            })
            btnReturn.setOnClickListener { onClickBtnBack() }
            viewShowInput.setOnClickListener { onClickShowEditText() }
            viewHideInput.setOnClickListener(View.OnClickListener {
                if (ClickUtil.getInstance.clickQuick()) return@OnClickListener
                hideInputMethod()
            })
            container.setOnClickListener { onClickContainer() }
            btnCommentSend.setOnClickListener { onClickBtnSendComment() }
        }
    }

    private var isTransLate: Boolean = false

    private fun setTranslate(item: CommunityVideo, haveContent: Boolean) {
        val translateVis: Int
        headerBinding?.run {
            if (!item.isVideoOwn() && checkShow()) {
                ivTranslate.visibility = View.VISIBLE
                translateVis = View.VISIBLE
                if (null != item.translateList && item.translateList?.isNotEmpty() == true) {
                    ivTranslate.setImageDrawable(ResUtil.getDrawable(R.mipmap.community_icon_fanyi_press))
                    isTransLate = true
                    if (haveContent) {
                        val transText = item.translateList!![0].text
                        des.text = transText ?: ""
                    }
                } else {
                    ivTranslate.setImageDrawable(ResUtil.getDrawable(R.mipmap.community_icon_fanyi))
                    isTransLate = false
                }
            } else {
                ivTranslate.visibility = View.INVISIBLE
                translateVis = View.INVISIBLE
            }
            ivTranslate.visibility = translateVis
            ivTranslate.setOnClickListener {
                if (ClickUtil.getInstance.clickQuick()) return@setOnClickListener
                AnalyticsRecorder.getInstance().recordUseCount(ParamKey.diy_video_detail, ParamFixedValue.click_translate_content)
                if (!NetUtil.isNetworkAvailable(BaseApplication.getContext())) {
                    ToastUtil.getInstance().toast(NetConstant.MSG_NET_ERROR)
                    return@setOnClickListener
                }
                if (!SavvyUserManager.getInstance.checkSUClick(context, true)) {
                    return@setOnClickListener
                }
                if (isTransLate) {
                    des.text = item.des
                    isTransLate = false
                    ivTranslate.setImageDrawable(ResUtil.getDrawable(R.mipmap.community_icon_fanyi_press))
                    return@setOnClickListener
                }
                val text = ArrayList<String>()
                text.add(item.des ?: "")
                showLoading((60 * 1000).toLong())
                getDefaultTranslate(text, "", 11, { list: ArrayList<TranslateBean> ->
                    hideLoading()
                    if (list.isNotEmpty()) {
                        item.translateList = list
                        val transText = list[0].text
                        des.text = transText ?: ""
                        isTransLate = true
                        ivTranslate.setImageDrawable(ResUtil.getDrawable(R.mipmap.community_icon_fanyi_press))
                    }
                }, { errorMsg: String? ->
                    hideLoading()
                    ToastUtil.getInstance().toast(errorMsg)
                })
            }
        }
    }

    /*adapter接口回调*/
    private fun createListener(): CommentClickListener {
        return object : CommentClickListener {
            override fun comment4SomeOne(comment: Comment) {
                val isSavvyUser = SavvyUserManager.getInstance.checkSUClick(context)
                if (isSavvyUser) {
                    /*弹出输入法*/
                    replyCommentId = comment.id
                    isMoreCommentUI = comment.isMoreComment
                    showIme(if (comment.isMoreComment) 2 else 1, comment.toAtUser())
                }
            }

            override fun commentLike(comment: Comment) {
                if (!NetworkUtil.isNetworkAvailable(context)) {
                    toast(R.string.network_anomaly)
                    return
                }
                if (!SavvyUserManager.getInstance.checkSUClick(context, false)) return
                showLoading(3 * 1000L)
                isMoreCommentUI = comment.isMoreComment
                if (commentOpListener != null) {
                    commentOpListener?.onLikeComment(comment, videoId)
                } else {
                    likeComment(context, videoId, comment.id, comment.is2Like())
                }
            }

            override fun commentDelete(comment: Comment) {
                val contentStr = ResUtil.getString(R.string.video_introduce_delete_content)
                val cancelStr = ResUtil.getString(R.string.cancel)
                val doneStr = ResUtil.getString(R.string.confirm)
                ConfirmDialog.showConfirmDialog(context, contentStr, cancelStr, doneStr) {
                    if (!NetworkUtil.isNetworkAvailable(context)) {
                        toast(R.string.network_anomaly)
                        return@showConfirmDialog
                    }
                    showLoading()
                    isMoreCommentUI = comment.isMoreComment
                    if (commentOpListener != null) {
                        commentOpListener?.onDeleteComment(comment, videoId, if (comment.isMoreComment) 2 else 1)
                    } else {
                        deleteComment(comment.id, videoId, if (comment.isMoreComment) 2 else 1) //1-主评论，2-二级评论
                    }
                }
            }

            override fun hideIme() {
                LogInfra.Log.i(MY_TAG, "hideIme()")
                hideInputMethod()
            }

            override fun commentMore(comment: Comment) {
                if (!NetworkUtil.isNetworkAvailable(context)) {
                    toast(R.string.network_anomaly)
                    return
                }
                if (!isMoreCommentUI) {
                    change2MoreCommonUI(comment)
                }
            }

            override fun headerClick(comment: Comment) {
                if (!canClickHeader) return
                Base2homeConfig.getConfig().jump2HomePageAc(context, comment.user?.identity, comment.user?.identityType ?: 0)
            }

            override fun commentReport(comment: Comment) {
                ReportDialog.createDialog(context, comment.id, ReportDialog.TYPE_VIDEO_COMMENT).show()
            }

            override fun commentTranslate(comment: Comment) {
                isMoreCommentUI = comment.isMoreComment
                AnalyticsRecorder.getInstance().recordUseCount(ParamKey.diy_video_detail, ParamFixedValue.click_translate_comment)
                getTranslate(comment)
            }
        }
    }

    fun likeComment(context: Context?, videoId: Int, commentId: Int, like: Boolean) {
        if (!checkSUClick(context, false)) return
        val request = RequestLikeComment(transactions.createTransaction(), videoId, commentId, like)
        commentViewModel?.likeComment(request, result = {
            likeComments(request)
        }, error = { response ->
            commonError(response)
        })
    }

    private fun VideoCommentDialog.commonError(response: ErrorResponse?) {
        var msg = response?.message
        if (TextUtils.isEmpty(msg)) {
            msg = ResUtil.getString(R.string.network_anomaly)
        }
        toast(msg, true)
        hideLoading()
    }

    private fun likeComments(request: RequestLikeComment) {
        val commentId = request.commentId
        val like = request.is2Like()
        hideLoading()

        if (!isMoreCommentUI) {
            adapter?.data?.forEachIndexed { i, curComment ->
                if (curComment?.id == commentId) {
                    curComment.changeLiked(like)
                    binding?.list?.findViewHolderForAdapterPosition(i + 1)?.let { viewHolder ->
                        (viewHolder as? BaseViewHolder)?.let {
                            adapter?.convert(it, curComment)
                        }
                    }
                    return
                }
            }
        } else {
            moreCommentMain?.moreComment?.subComments?.forEachIndexed { _, curComment ->
                if (curComment?.id == commentId) {
                    curComment.changeLiked(like)
                    moreCommentMain?.id?.let { commentId ->
                        getIndex(commentId)?.takeIf { it != -1 }?.let { index ->
                            adapter?.notifyItemChanged(index + 1)
                        }
                    }
                    return
                }
            }
        }
    }

    /**
     * 删除评论
     * @param commentId commentId
     * @param videoId   videoId
     */
    fun deleteComment(commentId: Int, videoId: Int, mainComment: Int) {
        val request =
            RequestDeleteComment(transactions.createTransaction(), videoId, commentId, mainComment)
        commentViewModel?.deleteComment(request, result = {
            deleteComment(commentId)
        }, error = { response ->
            commonError(response)
        })
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun deleteComment(commentId: Int) {
        hideLoading()
        if (!isMoreCommentUI) {
            adapter?.data?.firstOrNull { it?.id == commentId }?.let { curComment ->
                adapter?.remove(curComment)
                val subCommentNum = curComment.moreComment?.subCommentNum ?: 0
                setAllCommentNum(allCommentNum - subCommentNum - 1)
                updateUi(if (adapter?.data.isNullOrEmpty()) UI_TYPE_EMPTY else UI_TYPE_DATA)
            }
        } else {
            moreCommentMain?.moreComment?.let { moreComment ->
                moreComment.subCommentNum = max(0, moreComment.subCommentNum - 1)
                binding?.commentNumSec?.text = CommentUtil.getReplyNumStr(moreComment.subCommentNum)

                moreComment.subComments?.removeAll { it?.id == commentId }
                if ((moreCommentMain?.subCommentLoadSize
                        ?: 0) < (moreCommentMain?.moreComment?.subCommentNum ?: 0)
                ) {
                    //异常逻辑调用接口
                    moreCommentMain?.lastCommentId?.let {
                        getMoreComments(it, true)
                    }

                } else {
                    moreCommentMain?.id?.let { id ->
                        getIndex(id)?.takeIf { it != -1 }?.let { index ->
                            adapter?.notifyItemChanged(index + 1)
                        }
                    }
                    setAllCommentNum(allCommentNum - 1)
                }
            }
            isMoreCommentUI = false
            moreCommentMain = null
        }
    }

    private fun initHeaderView(video: CommunityVideo) {
        headerBinding =
            DataBindingUtil.inflate(LayoutInflater.from(context), com.govee.base2home.R.layout.dialog_comment_4_video_header, null, false)
        headerBinding?.run {
            des.text = video.des
            time.text = TimeFormatM.getInstance().formatTimeToHMYMD(video.createTime)
            if (TextUtils.isEmpty(video.des)) {
                ivTranslate.visibility = View.INVISIBLE
                return
            }
            setTranslate(video, !TextUtils.isEmpty(video.des))
        }
    }

    private fun getTranslate(comment: Comment) {
        if (!NetUtil.isNetworkAvailable(BaseApplication.getContext())) {
            ToastUtil.getInstance().toast(NetConstant.MSG_NET_ERROR)
            return
        }
        if (!SavvyUserManager.getInstance.checkSUClick(context, true)) {
            return
        }
        if (comment.isTranslate) {
            val textList = ArrayList<String>()
            textList.add(comment.content ?: "")
            showLoading()
            getDefaultTranslate(textList, "", 11, { list1: ArrayList<TranslateBean> ->
                if (list1.isNotEmpty()) {
                    if (!isViewOk()) {
                        isMoreCommentUI = false
                        return@getDefaultTranslate
                    }
                    hideLoading()
                    translateForChange(comment.id, true, list1)
                }
                isMoreCommentUI = false
            }, { errorMsg: String? ->
                hideLoading()
                isMoreCommentUI = false
                ToastUtil.getInstance().toast(errorMsg)
            })
        } else {
            translateForChange(comment.id, false, ArrayList())
        }
    }

    private fun translateForChange(commentId: Int, isTranslate: Boolean, textList: ArrayList<TranslateBean>) {
        if (!isMoreCommentUI) {
            /*一级界面*/
            for (i in adapter!!.data.indices) {
                val curComment = adapter!!.getItem(i)
                if (curComment!!.id == commentId) {
                    curComment.isTranslate = isTranslate
                    curComment.translateList = textList
                    //+1是因为添加了header需要占位
                    val viewHolder =
                        binding!!.list.findViewHolderForAdapterPosition(i + 1) as BaseViewHolder?
                            ?: break
                    adapter!!.convert(viewHolder, curComment) //局部刷新
                    break
                }
            }
        } else {
            /*二级评论界面*/
            for (i in 0 until ObjectUtils.getSize(moreCommentMain!!.moreComment!!.subComments)) {
                val curComment =
                    moreCommentMain!!.moreComment!!.subComments!![i]!!
                if (curComment.id == commentId) {
                    curComment.isTranslate = isTranslate
                    curComment.translateList = textList
                    val index = getIndex(moreCommentMain!!.id)
                    if (index != null && index != -1) {
                        //头部占位1
                        adapter?.notifyItemChanged(index + 1)
                    }
                    break
                }
            }
            isMoreCommentUI = false
        }
    }

    override fun dialogOnShow() {
        super.dialogOnShow()
        if (onlyShowContent) {
            refreshView()
        } else {
            /*加载评论信息*/
            getRelatedComments(-1)
        }
    }

    private fun refreshView() {
        binding?.run {
            adapter!!.removeEmptyView()
            swipe1.setEnableRefresh(false)
            swipe1.setEnableLoadMore(false)
            headerBinding?.commentNum?.visibility = View.GONE
            headerBinding?.title?.visibility = View.GONE
            headerBinding?.splitLine?.visibility = View.GONE
            commentEditContainer.visibility = View.GONE
            netErrorContainer.beHide()
            containerList.post(object : CaughtRunnable() {
                override fun runSafe() {
                    val layoutParams =
                        containerList.layoutParams as RelativeLayout.LayoutParams
                    layoutParams.topMargin = AppUtil.dp4WidthDef(430f)
                    containerList.layoutParams = layoutParams
                }
            })
        }
    }


    /*弹出键盘并显示@XX*/
    private fun showIme(level: Int, atUser: AtUser?) { //1 回复主评论 2 回复二级评论
        this.atUser = atUser
        if (level == 1) {
            showInputMethod(ResUtil.getString(R.string.app_community_comment_reply), "")
        } else if (level == 2) {
            showInputMethod(ResUtil.getString(R.string.comment_edit_hint), if (atUser != null) "@" + atUser.nickName + " " else "")
        }
    }

    /*二级评论界面*/
    fun change2MoreCommonUI(comment: Comment) {
        moreCommentMain = comment.clone()
        moreCommentMain!!.moreComment = comment.moreComment
        replyCommentId = moreCommentMain!!.id
        /*隐藏输入法*/
        hideInputMethod()
        val count = comment.subCommentLoadSize - comment.moreComment!!.subCommentNum
        if (count >= LOAD_PAGE_COUNT || comment.subCommentLoadSize == comment.moreComment!!.subCommentNum) {
            updateCurItemCount(comment, false)
            updateSubComment(comment)
            return
        }
        getMoreComments(comment.lastCommentId)
    }

    fun updateSubComment(comment: Comment) {
        val index = getIndex(comment.id)
        if (index != null && index != -1) {
            //头部占位1
            adapter?.notifyItemChanged(index + 1)
        }
        lastSecondCommentId = comment.lastCommentId
    }

    fun generateComment(comment: Comment) {
        moreCommentMain = comment.clone()
        moreCommentMain?.moreComment = (comment.moreComment)
        replyCommentId = moreCommentMain!!.id
    }

    private fun getIndex(commentId: Int): Int? {
        return adapter?.data?.indexOfFirst { it?.id == commentId }?.takeIf { it != -1 }
    }

    private fun changeUI(isMore: Boolean) {
        if (isViewOk()) {
            headerBinding?.root?.visibility =
                if (!isMore) View.VISIBLE else View.INVISIBLE
            binding?.btnClose?.visibility =
                if (!isMore) View.VISIBLE else View.INVISIBLE
            binding?.list?.visibility =
                if (!isMore) View.VISIBLE else View.INVISIBLE
            binding?.rlTopCommonSec?.visibility =
                if (isMore) View.VISIBLE else View.INVISIBLE
        }
    }

    private fun updateUi(uiType: Int) {
        if (!isViewOk()) return
        curUIType = uiType
        binding?.run {
            when (uiType) {
                UI_TYPE_LOADING -> {
                    netErrorContainer.beHide()
                    list.visibility = View.GONE
                    if (headerBinding != null) {
                        headerBinding?.commentNum?.text = ""
                    }
                    commentEditContainer.visibility = View.GONE
                }

                UI_TYPE_FAIL -> {
                    netErrorContainer.beFail()
                    list.visibility = View.GONE
                    if (headerBinding != null) {
                        headerBinding?.commentNum?.text = ""
                    }
                    commentEditContainer.visibility = View.GONE
                }

                UI_TYPE_DATA -> {
                    netErrorContainer.beHide()
                    changeUI(isMoreCommentUI)
                    if (headerBinding != null) {
                        headerBinding?.commentNum?.text = if (0 == allCommentNum) "" else "$allCommentNum"
                    }
                    commentEditContainer.visibility = View.VISIBLE
                }

                UI_TYPE_EMPTY -> {
                    netErrorContainer.beHide()
                    headerBinding?.commentNum?.text = ""
                    commentEditContainer.visibility = View.VISIBLE
                }
            }
        }
    }

    fun onClickBtnBack() {
        isMoreCommentUI = false
        changeUI(false)
        moreCommentMain = null
        replyCommentId = -1
        /*隐藏输入法*/
        hideInputMethod()
        /*检查是否需要更新UI*/
        if (needUpdateCommentList) {
            needUpdateCommentList = false
            updateUi(UI_TYPE_LOADING)
            /*加载评论信息*/
            getRelatedComments(lastCommentId)
        } else {
            updateUi(UI_TYPE_DATA)
        }
    }

    private fun onClickShowEditText() {
        if (ClickUtil.getInstance.clickQuick()) return
        if (CommunityManager.getInstance().showPostGuideResult(BaseApplication.getContext())) return
        if (!SavvyUserManager.getInstance.checkSUClick(context)) return
        if (isMoreCommentUI) {
            showInputMethod(ResUtil.getString(R.string.app_community_comment_reply), "")
        } else {
            showInputMethod(ResUtil.getString(R.string.comment_edit_hint), "")
        }
    }

    private fun onClickContainer() {
        if (ClickUtil.getInstance.clickQuick()) return
        LogInfra.Log.i(MY_TAG, "onClickContainer()")
        /*隐藏输入法*/
        hideInputMethod()
    }

    private fun onClickBtnSendComment() {
        if (ClickUtil.getInstance.clickQuick()) return
        val content = binding?.commentEdit?.editStr ?: " "
        if (TextUtils.isEmpty(content)) {
            binding?.btnCommentSend?.isEnabled = false
            return
        }
        if (SavvyUserManager.getInstance.checkSUClick(context)) {
            showLoading()
            if (!isMoreCommentUI) {
                if (replyCommentId == -1) {
                    /*一级评论*/
                    val requestPublishComment =
                        RequestPublishComment(transactions.createTransaction(), videoId, content)
                    commentViewModel?.publishComment(requestPublishComment, result = { response ->
                        publishComments(response)
                    }, error = { response ->
                        commonError(response)
                    })
                } else {
                    /*二级评论*/
                    val requestPublishComment =
                        RequestPublishMoreComment(transactions.createTransaction(), videoId, replyCommentId, content, 0, null, replyCommentId)
                    commentViewModel?.publishMoreComment(requestPublishComment, result = {
                        it?.let { it1 -> responsePublishMoreComment(it1) }
                    }, error = { response ->
                        commonError(response)
                    })
                }
            } else {
                /*二级评论 & 回复二级评论*/
                val isAtUser = if (replyCommentId != moreCommentMain!!.id) 1 else 0
                val requestPublishComment =
                    RequestPublishMoreComment(transactions.createTransaction(), videoId, moreCommentMain!!.id, content, isAtUser, (if (isAtUser == 1) atUser else null)!!, replyCommentId)
                commentViewModel?.publishMoreComment(requestPublishComment, result = {
                    it?.let { it1 -> responsePublishMoreComment(it1) }
                }, error = { response ->
                    commonError(response)
                })
                isMoreCommentUI = false
            }
        }
    }

    private fun publishComments(response: ResponsePublishComment?) {
        hideLoading()
        /*隐藏键盘*/
        hideInputMethod()
        LogInfra.Log.i(MY_TAG, "onResponsePublishComment()")
        //发布的这是第一条评论
        if (lastCommentId == -1) {
            lastCommentId = response?.id ?: -1
        }
        setAllCommentNum(allCommentNum + 1)
        if (response != null) {
            adapter?.addData(0, response.convert2Comment())
            updateUi(UI_TYPE_DATA)
            contentMap[-1] = "" //清空输入缓存
        }
        atUser = null
        /*滚动到评论顶部*/
        llm?.scrollToPosition(1)
    }

    /*弹出输入键盘*/
    private fun showInputMethod(hint: String, prefix: String) {
        binding?.run {
            commentEdit.isFocusable = true
            commentEdit.requestFocus()
            val content = contentMap[replyCommentId]
            LogInfra.Log.d(MY_TAG, "showInput id = " + replyCommentId + "text = " + content)
            commentEdit.post {
                commentEdit.hint = hint
                commentEdit.setTextWithPrefix(content ?: "", prefix)
            }
            viewShowInput.visibility = View.GONE
            btnCommentSend.visibility = View.VISIBLE
            viewHideInput.visibility = View.VISIBLE
            InputUtil.showInputMethod(commentEdit)
        }
    }

    /*隐藏输入键盘*/
    private fun hideInputMethod() {
        /*每收起才缓存*/
        binding?.run {
            if (btnCommentSend.visibility != View.GONE) {
                val content = commentEdit.editStr
                contentMap[replyCommentId] = content ?: ""
                LogInfra.Log.d(MY_TAG, " hideInputMethod()  commentId = " + replyCommentId + "text = " + content)
            }

            InputUtil.hideInputMethod(commentEdit)
            //收起输入框后，就变成一级评论。
            replyCommentId = -1 //isMoreCommentUI ? moreCommentMain.id : -1;
            isMoreCommentUI = false
            atUser = null
            commentEdit.setHint(R.string.comment_edit_hint)
            commentEdit.clearText()
            commentEdit.clearFocus()
            viewShowInput.visibility = View.VISIBLE
            btnCommentSend.visibility = View.GONE
            viewHideInput.visibility = View.GONE
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onImeHEvent(event: ImeHEvent) {
        if (!isViewOk()) return
        val imeShow = event.isImeShow
        val imeH = event.imeH
        val specialH = event.specialH
        val h = if (imeShow) {
            (imeH + max(specialH.toDouble(), 0.0)).toInt()
        } else {
            0
        }
        LogInfra.Log.i(MY_TAG, "onImeHEvent() imeShow = $imeShow ; imeH = $imeH ; specialH = $specialH ; final h = $h")
        val lp4Bottom = binding?.bottom?.layoutParams
        if (lp4Bottom != null) {
            if (lp4Bottom.height != h) {
                lp4Bottom.height = h
                binding?.bottom?.layoutParams = lp4Bottom
            }
        }
    }

    private fun checkSendBtnEnable() {
        if (isViewOk()) {
            val editStr = binding?.commentEdit?.editStr
            LogInfra.Log.i(MY_TAG, "checkSendBtnEnable() editStr.len = " + editStr?.length)
            binding?.btnCommentSend?.isEnabled = !TextUtils.isEmpty(editStr)
        }
    }

    /*更新评论总数*/
    private fun setAllCommentNum(num: Int) {
        allCommentNum = num
        if (headerBinding != null) {
            headerBinding?.commentNum?.text = if (0 == allCommentNum) "" else "$allCommentNum"
        }
        listener?.onCommentsChange(allCommentNum)
    }

    /*获取最新评论*/
    private fun getRelatedComments(lastId: Int) {
        lastCommentId = lastId
        val requestRelatedComment =
            RequestRelatedComment(transactions.createTransaction(), videoId, reply, lastId, REQUEST_LIMIT)
        commentViewModel?.getRelatedComment(requestRelatedComment.videoId, requestRelatedComment.hasRedDot, requestRelatedComment.lastId.toLong(), REQUEST_LIMIT.toLong(), result = { response ->
            relatedComments(response)
        }, error = { response ->
            hideLoading()
            var msg = response?.message
            if (TextUtils.isEmpty(msg)) {
                msg = ResUtil.getString(R.string.network_anomaly)
            }
            if (binding?.swipe1?.isRefreshing == true) {
                binding?.swipe1?.finishRefresh(false)
            } else {
                toast(msg, true)
            }
            binding?.swipe1?.finishLoadMore()
            if (adapter == null || adapter?.data.isNullOrEmpty()) {
                updateUi(UI_TYPE_FAIL)
            } else {
                updateUi(UI_TYPE_DATA)
            }
        })
    }

    private fun relatedComments(response: ResponseRelatedComment?) {
        isMoreCommentUI = false
        val data = response?.comments ?: emptyList()
        val lastId = response?.request?.lastId ?: -1
        setAllCommentNum(response?.count ?: 0)
        // 处理数据加载
        updateCurItemCount(data)
        when (lastId) {
            -1 -> { // 下拉刷新
                adapter?.setList(data)
                binding?.swipe1?.finishRefresh()
            }

            else -> { // 加载更多
                adapter?.addData(data)
                binding?.swipe1?.finishLoadMore()
                if (data.isEmpty()) toast(R.string.app_video_at_last)
            }
        }
        // 更新最后评论ID
        data.lastOrNull()?.id?.let { lastCommentId = it } ?: run { lastCommentId = -1 }
        // 滚动到指定评论位置
        if (scrollToComment) {
            llm?.scrollToPositionWithOffset(1, AppUtil.dp4WidthDef(30f))
            scrollToComment = false
        }
        LogInfra.Log.i(MY_TAG, "lastCommentId = $lastCommentId data.size() = ${data.size}")
        // UI更新
        updateUi(UI_TYPE_DATA)
        if (showIme) showIme(1, null)
        // 通知已读
        listener?.onRead(allCommentNum)
    }

    //更新单条二级评论数量
    private fun updateCurItemCount(data: Comment?, isFirst: Boolean) {
        if (data == null) return
        val moreComment = data.moreComment ?: return
        val increaseSize = ObjectUtils.getSize(moreComment.subComments) - moreComment.curItemCount
        if (isFirst) {
            moreComment.curItemCount = 0
        }
        moreComment.curItemCount += min(increaseSize, LOAD_PAGE_COUNT)
    }

    //更新二级评论数量
    private fun updateCurItemCount(data: List<Comment?>) {
        if (data.isEmpty()) return
        for (comment in data) {
            updateCurItemCount(comment, true)
        }
    }

    /*获取评论的二级评论*/
    private fun getMoreComments(lastId: Int, isDeleteElicit: Boolean = false) {
        showLoading()
        lastSecondCommentId = lastId
        val moreCommentId = moreCommentMain?.id ?: 0
        val requestMoreComment =
            RequestMoreComment(transactions.createTransaction(), videoId, moreCommentId, lastSecondCommentId, LOAD_PAGE_COUNT)
        requestMoreComment.isDeleteElicit = isDeleteElicit
        commentViewModel?.getMoreComment(requestMoreComment, result = {
            it?.let { it1 -> moreComments(it1) }
        }, error = {
            toast(ResUtil.getString(R.string.network_anomaly), true)
            updateUi(UI_TYPE_DATA)
            hideLoading()
        })
    }

    private fun updateData(commentId: Int, subComment: List<Comment>, isFirst: Boolean): Int {
        return adapter?.data?.indexOfFirst { it?.id == commentId }?.takeIf { it != -1 }?.also { index ->
            adapter?.data?.get(index)?.let { comment ->
                val subComments = comment2subComment(subComment)
                when {
                    isFirst -> comment.moreComment?.subComments = subComments
                    else -> mergeData(comment.moreComment?.subComments, subComments)
                }
            }
        } ?: (adapter?.data?.size ?: 0)
    }

    private fun mergeData(data1: MutableList<Comment.MoreComment.SubComment?>?, data2: List<Comment.MoreComment.SubComment?>) {
        // 使用空安全检查和集合操作替代显式循环
        if (data1.isNullOrEmpty() || data2.isEmpty()) return
        // 创建现有 ID 集合
        val existingIds = data1.mapNotNull { it?.id }.toSet()
        // 添加非重复项
        data1.addAll(data2.filter {
            it?.id != null && it.id !in existingIds
        })
    }

    private fun comment2subComment(subComment: List<Comment>): MutableList<Comment.MoreComment.SubComment?> {
        val subCommentList: MutableList<Comment.MoreComment.SubComment?> = ArrayList()
        if (subComment.isEmpty()) return subCommentList
        for (comment in subComment) {
            val temp = Comment.MoreComment.SubComment()
            temp.id = comment.id
            temp.content = comment.content
            temp.designatedUser = comment.designatedUser
            temp.user = comment.user
            temp.own = comment.own
            temp.likeTimes = comment.likeTimes
            temp.createTime = comment.createTime
            temp.liked = comment.liked
            subCommentList.add(temp)
        }
        return subCommentList
    }


    private fun moreComments(response: ResponseMoreComment) {
        // 安全处理数据
        val data = response.comments?.map {
            it.apply { isMoreComment = true }
        } ?: emptyList()
        // 获取请求参数
        val request = response.request
        val commentId = request?.commentId ?: -1
        val isFirstPage = lastSecondCommentId == -1
        // 更新数据
        val index = updateData(commentId, data, isFirstPage)
        // 获取目标评论
        val comment = adapter?.data?.getOrNull(index)
        // 处理非删除引发的情况
        if (request?.isDeleteElicit != true) {
            comment?.let { updateCurItemCount(it, isFirstPage) }
            comment?.moreComment?.let { moreComment ->
                val remainingCount = moreComment.subCommentNum - moreComment.curItemCount
                if (data.size / LOAD_PAGE_COUNT == 0 && remainingCount / LOAD_PAGE_COUNT == 0) {
                    moreComment.subCommentNum = moreComment.curItemCount
                }
            }
        }
        // 更新UI
        adapter?.notifyItemChanged(index + 1)
        // 处理删除引起的情况
        if (request?.isDeleteElicit == true) {
            setAllCommentNum(allCommentNum - 1)
        }
        // 更新最后评论ID
        comment?.lastCommentId?.let { lastSecondCommentId = it }
        hideLoading()
    }


    private fun responsePublishMoreComment(response: ResponsePublishMoreComment) {
        hideLoading()
        hideInputMethod()
        LogInfra.Log.i(MY_TAG, "onResponsePublishMoreComment() resetReplyId = $replyCommentId")

        updateUi(UI_TYPE_DATA)
        response.request?.replyCommentId?.let { contentMap.remove(it) }
        response.let { nonNullResponse ->
            if (isMoreCommentUI) {
                moreCommentMain?.moreComment?.let { moreComment ->
                    if (moreComment.subCommentNum == moreComment.curItemCount) {
                        moreComment.curItemCount++
                    }
                    moreComment.subCommentNum++
                    nonNullResponse.isMoreComment = true

                    llm2?.scrollToPosition(0)
                    binding?.commentNumSec?.text = CommentUtil.getReplyNumStr(moreComment.subCommentNum)
                }
            } else {
                val replyId = nonNullResponse.request?.mainCommentId
                replyId?.let { id ->
                    adapter?.data?.indexOfFirst { it?.id == id }?.takeIf { it != -1 }?.let { index ->
                        adapter?.getItem(index)?.let { curComment ->
                            val moreComment = curComment.moreComment ?: Comment.MoreComment().also {
                                curComment.moreComment = it
                            }
                            val expandAll = moreComment.subCommentNum == moreComment.curItemCount
                            val isCurPage = moreComment.curItemCount % LOAD_PAGE_COUNT != 0
                            moreComment.subCommentNum++
                            val subComment = transitionComment2SubComment(nonNullResponse)
                            if (moreComment.subComments.isNullOrEmpty()) {
                                moreComment.subComments = mutableListOf(subComment)
                                updateCurItemCount(curComment, true)
                            } else {
                                moreComment.subComments?.add(0, subComment)
                                if ((moreComment.subComments?.size ?: 0) <= LOAD_PAGE_COUNT) {
                                    updateCurItemCount(curComment, false)
                                }
                            }
                            if (expandAll && isCurPage) {
                                moreComment.curItemCount = moreComment.subCommentNum
                            }
                            adapter?.notifyItemChanged(index + 1)
                        }
                    }
                }
            }
            setAllCommentNum(allCommentNum + 1)
            atUser = null
        }
    }


    override fun getLayout(): Int {
        return com.govee.base2home.R.layout.dialog_comment_4_video
    }

    override fun getDialogStyle(): Int {
        return com.ihoment.base2app.R.style.DialogInputMethodsBottom
    }

    override fun getWidth(): Int {
        return WindowManager.LayoutParams.MATCH_PARENT
    }

    override fun getHeight(): Int {
        return WindowManager.LayoutParams.MATCH_PARENT
    }

    override fun hide() {
        super.hide()
        hideInputMethod()
        unbinderButterKnife()
        hideLoading()
        listener = null
        contentMap.clear()
        adapter?.setListener(null)
        ConfirmDialog.hideDialog()
    }

    override fun dialogOnDismiss() {
        super.dialogOnDismiss()
        listener = null
    }

    override fun useDataBinding(): ViewDataBinding? {
        binding = DataBindingUtil.inflate(LayoutInflater.from(context), layout, null, false)
        return binding
    }

    private fun showLoading(delayCloseTimeMills: Long = 60 * 1000L) {
        LoadingDialog.createDialog(context, com.ihoment.base2app.R.style.DialogDim, delayCloseTimeMills).setEventKey("VideoCommentDialog").show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog("VideoCommentDialog")
    }

    companion object {
        private const val MY_TAG = "VideoCommentDialog"
        private const val UI_TYPE_LOADING = 1
        private const val UI_TYPE_DATA = 2
        private const val UI_TYPE_FAIL = 3
        private const val UI_TYPE_EMPTY = 4
        private const val REQUEST_LIMIT = 20
        const val LOAD_PAGE_COUNT: Int = 5
        fun createDialog(context: Context, video: CommunityVideo, reply: Int, showIme: Boolean, commentViewModel: CommentViewModel, listener: CommentListener?): VideoCommentDialog {
            return createDialog(context, video, reply, showIme, true, commentViewModel, listener)
        }

        fun createDialog(context: Context, video: CommunityVideo, reply: Int, showIme: Boolean, canIntoDetail: Boolean, commentViewModel: CommentViewModel, listener: CommentListener?): VideoCommentDialog {
            val videoCommentDialog =
                VideoCommentDialog(context, video, reply, showIme, listener).apply {
                    this.commentViewModel = commentViewModel
                }
            videoCommentDialog.setCanIntoDetail(canIntoDetail)
            return videoCommentDialog
        }
    }
}