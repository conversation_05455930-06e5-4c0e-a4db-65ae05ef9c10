package com.govee.community.biz.message.fragment

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import androidx.recyclerview.widget.LinearLayoutManager
import com.govee.base2home.Constant
import com.govee.base2home.community.notification.EventReadNewMessage
import com.govee.base2home.community.notification.MessageModel
import com.govee.base2home.community.notification.multiitem.BaseMessageModel
import com.govee.base2home.community.notification.multiitem.MultiMessageAdapter
import com.govee.base2home.community.post.Img
import com.govee.base2home.community.post.PostReviewAc
import com.govee.base2home.community.user.UserInfoAc
import com.govee.base2home.community.user.UserInfoM
import com.govee.base2home.config.Base2homeConfig
import com.govee.base2home.constant.PathBaseHome
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.h5.WebActivity
import com.govee.base2home.safe.Vm4Safe
import com.govee.base2home.util.SchemeUtils
import com.govee.base2home.vip.ConvertRecordAc
import com.govee.base2home.vip.CreditsRecordAc
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2kt.utils.closeAnimator
import com.govee.cache.GlobalCache
import com.govee.cache.KeyConstants
import com.govee.cache.key.SmartCacheKey
import com.govee.community.R
import com.govee.community.biz.homepage.viewmodel.ShareViewModel
import com.govee.community.biz.message.bean.ResponseMyMessage
import com.govee.community.biz.message.viewmodel.MessageViewModel
import com.govee.community.databinding.CommunityFragmentMessageListBinding
import com.govee.community.video.CommunityVideoImgAc
import com.govee.home.account.config.AccountConfig
import com.govee.mvvm.BaseFragment
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.NetUtil
import com.ihoment.base2app.util.ResUtil

class MessageListFragment : BaseFragment<MessageViewModel, CommunityFragmentMessageListBinding>() {

    companion object {
        private const val REQUEST_TYPE = "request_type"
        fun newInstance(requestType: Int): MessageListFragment {
            val fragment = MessageListFragment().apply {
                this.requestType = requestType
            }
            val bundle = Bundle()
            bundle.putInt(REQUEST_TYPE, requestType)
            fragment.setArguments(bundle)
            return fragment
        }
    }

    private val mAdapter by lazy { MultiMessageAdapter() }
    private var requestType = MessageViewModel.REQUEST_TYPE_ALL
    private var clickType = 0

    /**
     * 共享的vm
     */
    @Suppress("MemberVisibilityCanBePrivate")
    val shareVM: ShareViewModel by lazy {
        ShareViewModel.createShareVm4HomePage(
            sku = "community_sku_message", device = "community_device_message",
            ac = requireActivity()
        )
    }

    override fun initView(savedInstanceState: Bundle?) {
        initTab()
        initAdapter()
        initListener()
    }

    private fun initTab() {
        mDatabind.clActivityTab.visibleByBoolean(requestType == MessageViewModel.REQUEST_TYPE_INTERACT)
    }

    private fun setRedDot(model: BaseMessageModel? = null) {
        val messageVM = shareVM.opVm as? MessageViewModel
        messageVM?.redDotMessage?.value?.run {
            if (model != null) {
                checkUnReadNumByClick(this, model)
            } else {
                checkUnReadNum(this)
            }
            mDatabind.view4Hint.visibleByBoolean(interactMsgCommentCount > 0)
            mDatabind.view4Hint1.visibleByBoolean(interactMsgNewFollowCount > 0)
            mDatabind.view4Hint2.visibleByBoolean(interactMsgLikeCount > 0)
        }
    }

    private fun checkUnReadNum(msg: ResponseMyMessage) {
        msg.messages?.forEach { message ->
            val newMessageType = message.interactMsgSubType
            val key = SmartCacheKey.appGlobal(KeyConstants.KEY_MY_MESSAGE_READ + message.msgId)
            val read = GlobalCache.build().getBoolean(key) ?: false
            if (read) {
                minusRedDotCount(newMessageType, msg)
            }
        }
    }

    private fun checkUnReadNumByClick(msg: ResponseMyMessage, message: BaseMessageModel) {
        val newMessageType = message.interactMsgSubType
        minusRedDotCount(newMessageType, msg)
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initListener() {
        (shareVM.opVm as? MessageViewModel)?.myMessage?.observe(this) {
            operateData(it)
        }

        (shareVM.opVm as? MessageViewModel)?.messageType?.observe(this) {
            if (it == requestType) {
                mAdapter.data.clear()
                mAdapter.notifyDataSetChanged()
                requestData(-1)
            }
        }
        (shareVM.opVm as? MessageViewModel)?.deleteMessage?.observe(this) {
            LoadingDialog.hideDialog()
            if (it) {
                resetRedPoint()
                setRedDot()
                mAdapter.deleteComplete()
                /*删除成功取消编辑状态*/
                if (mAdapter.data.isEmpty()) {
                    requestData(-1)
                }
            }
            /*监听已读消息, 通知对应item*/
            (shareVM.opVm as? MessageViewModel)?.readMessage?.observe(this) { msg ->
                (mAdapter.data.findLast { (it as BaseMessageModel).msgId == msg.msgId } as? BaseMessageModel)?.read =
                    1
                val position = findPos(msg.msgId)
                if (position >= 0) {
                    mAdapter.notifyItemChanged(position)
                }
            }

            mAdapter.setOnItemChildClickListener { adapter, view, position ->
                if (mAdapter.getItemViewType(position) != MultiMessageAdapter.message_type_activity) {
                    return@setOnItemChildClickListener
                }
                when (view.id) {
                    com.govee.base2home.R.id.iv_cover -> {
                        val ac = (mAdapter.getItemWithPosition<MessageModel.ActivityMsg>(position))
                            ?: return@setOnItemChildClickListener
                        if (TextUtils.isEmpty(ac.imgUrl)) {
                            return@setOnItemChildClickListener
                        }
                        CommunityVideoImgAc.jump2DetailAc(context, 0, mutableListOf(Img().apply {
                            url = ac.imgUrl
                        }))
                    }
                }
            }

            /*item点击事件，编辑状态已拦截点击事件*/
            mAdapter.setOnItemClickListener { _, _, position ->
                /*根据viewType判断 */
                when (mAdapter.getItemViewType(position)) {
                    MultiMessageAdapter.message_type_community -> {
                        /*社区，评论消息*/
                        val community =
                            mAdapter.getItemWithPosition<MessageModel.CommunityMsg>(position)
                                ?: return@setOnItemClickListener
                        if (community.type in arrayOf(4, 5, 10, 11, 12)) {
                            /*视频*/
                            if (community.video == null) {
                                toast(com.govee.ui.R.string.b2light_video_no_exist)
                            } else {
                                Base2homeConfig.getConfig()
                                    .jump2PreviewVideoAc(activity, community.video!!.videoId)
                            }
                        } else if (community.type in arrayOf(1, 2, 3, 6, 7, 8, 9)) {
                            /*帖子*/
                            PostReviewAc.jump2PostReviewAcWithCommentID(
                                activity,
                                community.post,
                                0,
                                community.commentId,
                                community.subCommentId
                            )
                        }
                    }

                    MultiMessageAdapter.message_type_vote -> {
                        /*投票消息*/
                        val vote = mAdapter.getItemWithPosition<MessageModel.VoteMsg>(position)
                        PostReviewAc.jump2PostReviewAc(activity, vote?.post, 0)
                    }

                    MultiMessageAdapter.message_type_point -> {
                        /*积分提醒消息*/
                        val point = mAdapter.getItemWithPosition<MessageModel.PointMsg>(position)
                        if (point?.url.isNullOrEmpty()) {
                            CreditsRecordAc.jump2CreditsRecordAc(activity)
                        } else {
                            WebActivity.jump2WebAc(activity, "", point?.url.orEmpty())
                        }
                    }

                    MultiMessageAdapter.message_type_activity -> {
                        /*活动消息*/
                        /*点击后跳转到对应配置的H5页面。未配置跳转链接的不显示跳转按钮；
                        活动已结束的（即push已过期的）点击toast提示“活动已结束”。
                        （后续配置跳转H5的push要求要有结束时间）。能跳转的显示跳转按钮，不支持的不显示。*/
                        val ac =
                            (mAdapter.getItemWithPosition<MessageModel.ActivityMsg>(position))
                                ?: return@setOnItemClickListener

                        /*有帖子id跳帖子详情*/
                        if (ac.postId != 0) {
                            PostReviewAc.jump2PostReviewAc4AfterSendPost(activity, ac.postId)
                        } else if (ac.skipWay != 0 && ac.endTime != 0L && System.currentTimeMillis() > ac.endTime) {
                            toast(com.govee.ui.R.string.app_message_notification_activity_endtime)
                        } else {
                            when (ac.skipWay) {
                                1 -> {
                                    /*跳转内部webView*/
                                    WebActivity.jump2WebAc(
                                        activity,
                                        ac.pushTitle ?: "",
                                        ac.activityUrlStr ?: ""
                                    )
                                }

                                2 -> {
                                    /*跳转外部webView*/
                                    Vm4Safe.jumpToBrowser(activity, ac.activityUrlStr ?: "")
                                }
                            }

                        }
                    }

                    MultiMessageAdapter.message_type_common -> {
                        /*非push消息*/
                        val common =
                            mAdapter.getItemWithPosition<MessageModel.CommonMsg>(position)
                                ?: return@setOnItemClickListener
                        /*跳转可不配置。不配置跳转则不显示跳转图标，跳转失效则a点击toast提示“活动已结束”*/
                        if (System.currentTimeMillis() > common.endTime) {
                            toast(com.govee.ui.R.string.app_message_notification_activity_endtime)
                        } else {
                            if (LogInfra.openLog()) {
                                LogInfra.Log.d(
                                    TAG,
                                    "message_type_common skipWay = ${common.skipWay} url = ${common.skipUrl}"
                                )
                            }
                            when (common.skipWay) {
                                1 -> {
                                    /*跳转内部webView*/
                                    WebActivity.jump2WebAc(
                                        activity,
                                        common.title ?: "",
                                        common.skipUrl ?: ""
                                    )
                                }

                                2 -> {
                                    /*跳转外部webView*/
                                    Vm4Safe.jumpToBrowser(activity, common.skipUrl)
                                }
                            }
                        }
                    }

                    MultiMessageAdapter.message_type_follow -> {
                        /*关注消息 跳转到关注列表*/
                        val userName = UserInfoM.getInstance.userInfo?.nickName
                        Base2homeConfig.getConfig()
                            .jump2MyFollowingListAc(activity, true, 1, userName)
                    }

                    MultiMessageAdapter.message_type_medal -> {
                        /*勋章消息 跳转到勋章列表*/
                        val account = AccountConfig.read()
                        val url = Constant.getH5MedalListUrl(
                            "${account.accountId}", if (account.isHadToken) 2 else 1
                        )
                        WebActivity.jump2WebAc(
                            activity,
                            ResUtil.getString(com.govee.ui.R.string.homepage_badge),
                            url
                        )
                    }

                    MultiMessageAdapter.message_type_shopping -> {
                        /*购物消息 跳转到兑换纪录列表*/
                        /*积分兑换预售code 跳转复购模块的《兑换纪录》页*/
                        /*无需积分领取预售code，跳转商品购买链接*/
                        mAdapter.getItemWithPosition<MessageModel.ShoppingMsg>(position)
                            ?.let { shopping ->
                                if (shopping.tag.equals("1")) {
                                    ConvertRecordAc.jump2conCovRecordAc(activity, false, true)
                                } else if (shopping.tag.equals("2")) {
                                    Vm4Safe.jumpToBrowser(activity, shopping.shoppingUrl ?: "")
                                }
                            }
                    }

                    MultiMessageAdapter.message_type_cover -> {
                        /*封面消息 无交互*/
                    }

                    MultiMessageAdapter.message_type_avatar -> {
                        /*头像审核消息 跳转到个人信息页*/
                        JumpUtil.jump(activity, UserInfoAc::class.java)
                    }

                    MultiMessageAdapter.message_type_circle -> {
                        val circleMsg =
                            mAdapter.getItemWithPosition<MessageModel.CircleMsg>(position)

                        circleMsg?.let {

                            Base2homeConfig.getConfig()
                                .jump2CirclePage(activity, it.circleId, it.circleType)
                        }
                    }

                    MultiMessageAdapter.message_type_collection -> {
                        val collectionMsg =
                            mAdapter.getItemWithPosition<MessageModel.CollectionMsg>(position)
                        collectionMsg?.post?.let {
                            PostReviewAc.jump2PostReviewAc(activity, it, 0)
                        }
                    }

                    MultiMessageAdapter.message_type_share_device -> {
                        val deviceShareMsg =
                            mAdapter.getItemWithPosition<MessageModel.DeviceShareMsg>(position)
                        deviceShareMsg?.run {

                            if (!canSkip()) {
                                return@run
                            }
                            if (type == 2) {
                                SchemeUtils.jump(
                                    activity,
                                    PathBaseHome.URL_SHARE_DEVICE_LIST,
                                    Bundle().apply {
                                        putInt("intent_key_pager_pos", 1)
                                    })
                            } else {
                                SchemeUtils.jump(
                                    activity,
                                    PathBaseHome.URL_SHARE_MSG_DETAIL,
                                    Bundle().apply {
                                        putInt("intent_ket_mst_id", sharedInfoId ?: 0)
                                    })
                            }
                        }
                    }

                    MultiMessageAdapter.message_type_co_projects -> {
                        val coProjectsMsg =
                            mAdapter.getItemWithPosition<MessageModel.CoProjectsMsg>(position)
                        coProjectsMsg?.h5Url?.let {
                            WebActivity.jump2WebAc(activity, "", it)
                        }
                    }

                    MultiMessageAdapter.message_type_mall_qa -> {
                        val mallQaMsg =
                            mAdapter.getItemWithPosition<MessageModel.MallQaMsg>(position)
                        mallQaMsg?.let {
                            val skipUrl = it.skipUrl
                            if (!TextUtils.isEmpty(skipUrl)) {
                                WebActivity.jump2WebAc(activity, "", skipUrl)
                            }
                        }
                    }
                }
                (mAdapter.data[position] as BaseMessageModel).read = 1
                val key =
                    SmartCacheKey.appGlobal(KeyConstants.KEY_MY_MESSAGE_READ + (mAdapter.data[position] as BaseMessageModel).msgId)
                GlobalCache.build().set(key, true)
                (shareVM.opVm as? MessageViewModel)?.readMessage?.postValue((mAdapter.data[position] as BaseMessageModel))
                mAdapter.notifyItemChanged(position)
                setRedDot((mAdapter.data[position] as BaseMessageModel))
            }
            /*选中item回调*/
            mAdapter.selectedCallback = { selected: Boolean ->
                /*设置item已选中，删除按钮可点击*/
                (shareVM.opVm as? MessageViewModel)?.selected?.postValue(selected)
            }

            /*全选回调*/
            mAdapter.selectedAll = { selected: Boolean ->
                /*设置item已选中，删除按钮可点击*/
                (shareVM.opVm as? MessageViewModel)?.selectedAll?.postValue(selected)
            }

            mDatabind.tvComment.clickDelay {
                if (mDatabind.tvComment.isSelected) {
                    return@clickDelay
                }
                requestData(-1, clickType = MessageViewModel.REQUEST_TYPE_INTERACT_COMMENT)
            }

            mDatabind.tvFollow.clickDelay {
                if (mDatabind.tvFollow.isSelected) {
                    return@clickDelay
                }
                requestData(-1, clickType = MessageViewModel.REQUEST_TYPE_INTERACT_FOLLOW)
            }

            mDatabind.tvLike.clickDelay {
                if (mDatabind.tvLike.isSelected) {
                    return@clickDelay
                }
                requestData(-1, clickType = MessageViewModel.REQUEST_TYPE_INTERACT_LIKE_COLLECTION)
            }

        }

    }

    fun resetRedPoint() {
        val messageVM = shareVM.opVm as? MessageViewModel
        messageVM?.redDotMessage?.value?.run {
            messages?.forEach { message ->
                if (messageVM.deleteIds.value.contains(message.msgId)) {
                    when {
                        message.interactMsgSubType == MessageViewModel.REQUEST_TYPE_INTERACT_COMMENT && interactMsgCommentCount > 0 -> {
                            --interactMsgCommentCount
                        }

                        message.interactMsgSubType == MessageViewModel.REQUEST_TYPE_INTERACT_FOLLOW && interactMsgNewFollowCount > 0 -> {
                            --interactMsgNewFollowCount
                        }

                        message.interactMsgSubType == MessageViewModel.REQUEST_TYPE_INTERACT_LIKE_COLLECTION && interactMsgLikeCount > 0 -> {
                            --interactMsgLikeCount
                        }
                    }
                }
            }
        }
    }


    fun findPos(msgId: Int): Int {
        val size: Int = mAdapter.data.size
        for (i in 0 until size) {
            if ((mAdapter.data[i] as? BaseMessageModel)?.msgId == msgId) {
                return i
            }
        }
        return -1
    }

    /*批量删除消息*/
    fun deleteMessages() {
        val ids = mAdapter.getSelectIds()
        if (ids.isEmpty()) return

        (shareVM.opVm as? MessageViewModel)?.deleteMessages(ids)
        LoadingDialog.createDialog(context).show()
    }

    private fun operateData(pair: Triple<Boolean, Boolean, ResponseMyMessage?>) {
        if (pair.first) {
            val isInteract = pair.third?.requestType in arrayOf(
                MessageViewModel.REQUEST_TYPE_INTERACT_COMMENT,
                MessageViewModel.REQUEST_TYPE_INTERACT_FOLLOW,
                MessageViewModel.REQUEST_TYPE_INTERACT_LIKE_COLLECTION
            )
            val isInteract2 = requestType != MessageViewModel.REQUEST_TYPE_INTERACT
            if (requestType != pair.third?.requestType && !isInteract && !isInteract2) {
                return
            }

            EventReadNewMessage.sendEventReadNewMessage()
            val loadMode = mAdapter.getLastMessageTime() != (-1).toLong()
            val modelList = pair.third?.messages ?: arrayListOf()
            val isRefresh = pair.second

            val convertList = mAdapter.getConvertData(modelList)
            setRedDot()
            if (isRefresh) {
                mDatabind.refreshRecyclerView.recycle.scrollToPosition(0)
                mAdapter.setList(convertList)
            } else {
                mAdapter.addData(convertList)
            }

            mDatabind.refreshRecyclerView.loadDataFinish(
                true,
                mAdapter.data.isNotEmpty(),
                loadMode && modelList.isEmpty()
            )
        } else {
            showUiFail()
        }
    }

    private fun initAdapter() {
        mDatabind.refreshRecyclerView.apply {
            getGoveeRefreshFooter()?.setFreshFailResId(com.govee.ui.R.string.network_anomaly)
            recycle.closeAnimator()
            val manager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            initAdapter(mAdapter, manager, loadMoreCallBack = {
                requestData(clickType = <EMAIL>)
            })
            retryData {
                requestData(clickType = <EMAIL>)
            }
        }
    }


    private fun requestData(lastTime: Long = 0, clickType: Int = 0) {
        <EMAIL> = clickType
        val realType =
            if (clickType > 0) clickType else if (requestType == MessageViewModel.REQUEST_TYPE_INTERACT) getInteractType() else requestType
        setCurrentBtn(realType)
        if (!NetUtil.isNetworkAvailable(context)) {
            showUiFail(clickType)
            return
        }
        setRedDot()
        showUILoading()
        (shareVM.opVm as? MessageViewModel)?.fetchMessages(
            realType,
            if (lastTime != 0L) lastTime else mAdapter.getLastMessageTime()
        )
    }

    private fun setCurrentBtn(realType: Int) {
        mDatabind.tvComment.isSelected = false
        mDatabind.tvFollow.isSelected = false
        mDatabind.tvLike.isSelected = false
        when (realType) {
            MessageViewModel.REQUEST_TYPE_INTERACT_COMMENT -> {
                mDatabind.tvComment.isSelected = true
            }

            MessageViewModel.REQUEST_TYPE_INTERACT_FOLLOW -> {
                mDatabind.tvFollow.isSelected = true
            }

            MessageViewModel.REQUEST_TYPE_INTERACT_LIKE_COLLECTION -> {
                mDatabind.tvLike.isSelected = true
            }
        }
    }

    private fun getInteractType(): Int {
        // 1. 安全获取 ViewModel 实例
        val messageVM = shareVM.opVm as? MessageViewModel
            ?: return MessageViewModel.REQUEST_TYPE_INTERACT_COMMENT
        // 2. 安全获取红点数据
        val redDotMessage = messageVM.redDotMessage.value
            ?: return MessageViewModel.REQUEST_TYPE_INTERACT_COMMENT
        // 3. 按优先级检查计数并直接返回结果
        return when {
            redDotMessage.interactMsgCommentCount > 0 -> {
                MessageViewModel.REQUEST_TYPE_INTERACT_COMMENT
            }

            redDotMessage.interactMsgNewFollowCount > 0 -> {
                MessageViewModel.REQUEST_TYPE_INTERACT_FOLLOW
            }

            redDotMessage.interactMsgLikeCount > 0 -> {
                MessageViewModel.REQUEST_TYPE_INTERACT_LIKE_COLLECTION
            }

            else -> {
                MessageViewModel.REQUEST_TYPE_INTERACT_COMMENT

            }
        }
    }

    private fun showUiFail(clickType: Int = 0) {
        mDatabind.refreshRecyclerView.loadDataFinish(
            false,
            if (clickType == 0) mAdapter.data.isNotEmpty() else false,
            if (clickType == 0) mAdapter.data.isNotEmpty() else false
        )
    }

    private fun showUILoading() {
        mDatabind.refreshRecyclerView.showLoadingUI()
    }

    override fun layoutId(): Int = R.layout.community_fragment_message_list
    fun editMessage() {
        mAdapter.setEditAble(true)
    }

    fun cancelDeleteMessage() {
        mAdapter.setEditAble(false)
    }

    fun selectAll(select: Boolean) {
        mAdapter.setSelectAll(select)
    }
}

private fun minusRedDotCount(
    newMessageType: Int,
    msg: ResponseMyMessage
) {
    when {
        newMessageType == MessageViewModel.REQUEST_TYPE_INTERACT_COMMENT && msg.interactMsgCommentCount > 0 -> {
            --msg.interactMsgCommentCount
        }

        newMessageType == MessageViewModel.REQUEST_TYPE_INTERACT_FOLLOW && msg.interactMsgNewFollowCount > 0 -> {
            --msg.interactMsgNewFollowCount
        }

        newMessageType == MessageViewModel.REQUEST_TYPE_INTERACT_LIKE_COLLECTION && msg.interactMsgLikeCount > 0 -> {
            --msg.interactMsgLikeCount
        }
    }
}