package com.govee.community.compose.clubchoose

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyListState
import androidx.compose.foundation.lazy.itemsIndexed
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.input.nestedscroll.NestedScrollConnection
import androidx.compose.ui.input.nestedscroll.NestedScrollSource
import androidx.compose.ui.input.nestedscroll.nestedScroll
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.govee.base2home.compose.ImageWithBitmapRes
import com.govee.base2home.compose.color
import com.govee.base2home.compose.colorResource
import com.govee.base2home.compose.theme.Type
import com.govee.ui.R
import com.ihoment.base2app.infra.SafeLog
import kotlin.math.abs

/**
 * @author: hujie
 * @date: 2025/3/24
 * @description: 圈子话题列表选择
 */


@Composable
fun ClubTopics(modifier: Modifier = Modifier, viewModel: ClubChooseVM = viewModel()) {
    val uiState by viewModel.uiStateFlow.collectAsState()
    val curClub =
        uiState.clubList.find { it.circleId == uiState.selectedClubId } ?: uiState.clubList[0]
    val context = LocalContext.current
    Row(modifier = modifier.fillMaxSize()) {
        ClubList(
            modifier = Modifier.width(120.dp),
            selectedClub = curClub,
            clubs = uiState.clubList,
            fromAskPage = uiState.fromAskPage,
            onSelected = {
                viewModel.selectClub(it)
            },
            isFirstEnter = uiState.isFirstEnter
        )
        TopicList(
            modifier = Modifier
                .padding(horizontal = 12.dp)
                .fillMaxWidth(1f),
            selectedTopicId = uiState.selectedTopicId,
            curClub = curClub,
            inSearch = uiState.inSearch,
            onNotAddTopic = {
                viewModel.onNotAddTopic(context)
            },
            topics = uiState.topicList,
            topicLoadingState = uiState.topicState,
            onSelected = {
                viewModel.selectTopic(context = context, topic = it)
            },
            onLoadMore = {
                viewModel.loadTopics(loadMore = true)
            }
        )
    }
}


@Composable
fun ClubList(
    modifier: Modifier = Modifier,
    selectedClub: Models.Circle,
    clubs: List<Models.Circle>,
    onSelected: (Models.Circle) -> Unit,
    fromAskPage: Boolean,
    isFirstEnter: Boolean = false
) {
    val lazyColumnState = rememberLazyListState()
    LaunchedEffect(Unit) {
        if (isFirstEnter) {
            val index = clubs.indexOf(selectedClub)
            if (index > -1) {
                lazyColumnState.animateScrollToItem(index)
            }
        }
    }
    LazyColumn(
        modifier = modifier,
        state = lazyColumnState,
        verticalArrangement = Arrangement.spacedBy(0.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {

        itemsIndexed(
            items = clubs,
            key = { _, item -> item.circleId }) { _, item ->
            val selected = item.circleId == selectedClub.circleId
            ClubItem(
                modifier = Modifier.fillMaxWidth(),
                item = item,
                selected = selected,
                onSelected = onSelected,
                fromAskPage = fromAskPage
            )
        }
    }
}

@Composable
fun ClubItem(
    modifier: Modifier = Modifier,
    item: Models.Circle,
    selected: Boolean,
    fromAskPage: Boolean = false,
    onSelected: (Models.Circle) -> Unit,
) {
    val interactionSource = remember { MutableInteractionSource() }
    val bgColor = colorResource(
        if (selected)
            R.color.ui_bg_color_style_2
        else
            R.color.ui_bg_color_style_14
    )
    var style = if (selected) {
        Type.font_style_24_3()
    } else {
        Type.font_style_65_1()
    }

    //提问贴不允许选择通用圈子
    if (fromAskPage && item.circleId == -1) {
        style = style.copy(color = style.color.copy(alpha = 0.3f))
    }

    if (item.circleId == -1) {
        item.circleName = stringResource(R.string.community_post_common_topic)
    }

    Box(
        modifier = modifier
            .height(70.dp)
            .background(bgColor)
            .padding(horizontal = 10.dp, vertical = 7.dp)
            .clickable(interactionSource = interactionSource, indication = null) {
                if (fromAskPage && item.circleId == -1) {
                    SafeLog.i("TAG") { "ClubItem() " }
                } else {
                    onSelected.invoke(item)
                }
            },
        contentAlignment = Alignment.Center
    ) {
        Text(
            text = item.circleName ?: "",
            style = style,
            maxLines = 3,
            overflow = TextOverflow.Ellipsis,
            textAlign = TextAlign.Center,
        )
    }
}

@Composable
fun TopicList(
    modifier: Modifier = Modifier,
    inSearch: Boolean = false,
    onNotAddTopic: () -> Unit = {},
    selectedTopicId: Int,
    topics: List<Models.Topic>,
    curClub: Models.Circle?,
    onSelected: (Models.Topic) -> Unit,
    topicLoadingState: ClubChooseVM.LoadingState,
    onLoadMore: () -> Unit,
    searchInput: String = ""
) {
    val lazyColumnState = rememberLazyListState()
    val coroutineScope = rememberCoroutineScope()
    // 创建 NestedScrollConnection 监听滚动
    val nestedScrollConnection = remember {
        object : NestedScrollConnection {
            override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                // 上滑时触发（available.y < 0）
                if (available.y < 0) {
                    val isAtBottom = isAtBottom(lazyColumnState)
                    SafeLog.i("hujie") { "上滑动作：$available, isAtBootm: $isAtBottom" }
                    val canOnLoadMore =
                        abs(available.y) > 10 && isAtBottom && topicLoadingState != ClubChooseVM.LoadingState.Loading
                    if (canOnLoadMore) {
                        onLoadMore.invoke()
                    }
                }
                return Offset.Zero // 不消费事件，继续传递给子组件
            }

            override fun onPostScroll(
                consumed: Offset,
                available: Offset,
                source: NestedScrollSource
            ): Offset {
                // 下拉时触发（available.y > 0）
                if (available.y > 0) {
                    SafeLog.i("hujie") { "下拉动作：$available" }
                }
                return Offset.Zero
            }
        }
    }

    LaunchedEffect(curClub) {
        if (topics.isEmpty()) {
            onLoadMore.invoke()
        }
    }
    Box(
        modifier = modifier
            .fillMaxSize()
            .nestedScroll(nestedScrollConnection)
    ) {
        if (topicLoadingState == ClubChooseVM.LoadingState.Error && topics.isEmpty()) {
            ErrorContent(modifier = Modifier.fillMaxSize()) {
                onLoadMore.invoke()
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize(),
                state = lazyColumnState,
                verticalArrangement = Arrangement.spacedBy(0.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                if (!inSearch && curClub?.circleId != -1) {
                    item {
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(70.dp),
                            horizontalArrangement = Arrangement.Start,
                            verticalAlignment = Alignment.CenterVertically,
                        ) {
                            Box(
                                modifier = Modifier
                                    .clip(RoundedCornerShape(50))
                                    .background(colorResource(R.color.ui_btn_style_113_2_color))
                                    .clickable {
                                        onNotAddTopic.invoke()
                                    }
                                    .padding(horizontal = 17.5.dp, vertical = 7.5.dp),
                            ) {
                                Text(
                                    modifier = Modifier.align(Alignment.CenterStart),
                                    text = stringResource(R.string.community_do_not_add_topic),
                                    style = Type.font_style_btn_39()
                                )
                            }
                        }
                    }
                }

                itemsIndexed(
                    items = topics,
                    key = { _, item -> item.topicId }) { index, item ->
                    val selected = selectedTopicId == item.topicId
                    if (inSearch) {
                        SearchTopicItem(
                            modifier = Modifier.fillMaxWidth(),
                            item = item,
                            selected = selected,
                            searchInput = searchInput,
                            onSelected = onSelected
                        )
                    } else {
                        TopicItem(
                            modifier = Modifier.fillMaxWidth(),
                            item = item,
                            selected = selected,
                            onSelected = onSelected
                        )
                    }

                    if (index < topics.size - 1) {
                        HorizontalDivider(
                            color = colorResource(R.color.ui_split_line_style_6),
                            thickness = 0.5.dp
                        )
                    }
                }
            }
        }

        if (topicLoadingState == ClubChooseVM.LoadingState.Loading) {
            LoadingContent(modifier = Modifier.align(Alignment.BottomCenter))
        }
    }

}

// 判断是否滚动到底部
private fun isAtBottom(state: LazyListState): Boolean {
    val layoutInfo = state.layoutInfo
    return layoutInfo.visibleItemsInfo.lastOrNull()?.index == layoutInfo.totalItemsCount - 1
}

@Composable
fun TopicItem(
    modifier: Modifier = Modifier,
    item: Models.Topic,
    selected: Boolean,
    onSelected: (Models.Topic) -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    Row(
        modifier = modifier
            .fillMaxWidth()
            .sizeIn(minHeight = 68.dp)
            .padding(vertical = 6.5.dp)
            .clickable(interactionSource = interactionSource, indication = null) {
                onSelected.invoke(item)
            },
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            modifier = Modifier.weight(1f),
            text = "# " + item.topicName,
            style = Type.font_style_34(),
            textAlign = TextAlign.Start,
            maxLines = 3,
            overflow = TextOverflow.Ellipsis
        )

        Spacer(modifier = Modifier.width(5.dp))
        if (item.newTopic) {
            ImageWithBitmapRes(
                modifier = Modifier.size(27.5.dp),
                bitmapRes = R.mipmap.new_icon_community_huati_new,
                contentDescription = null,
            )
            Spacer(modifier = Modifier.width(3.5.dp))
        } else if (item.hotTopic) {
            ImageWithBitmapRes(
                modifier = Modifier.size(13.dp),
                bitmapRes = R.mipmap.new_icon_community_huati_hot,
                contentDescription = null,
            )
            Spacer(modifier = Modifier.width(18.dp))
        } else {
            Spacer(modifier = Modifier.width(20.dp))
            Spacer(modifier = Modifier.width(18.dp))
        }

        if (selected) {
            ImageWithBitmapRes(
                modifier = Modifier.size(20.dp),
                bitmapRes = R.mipmap.public_popup_icon_choose,
            )
        } else {
            Spacer(modifier = Modifier.width(20.dp))
        }
    }
}

@Composable
fun SearchTopicItem(
    modifier: Modifier = Modifier,
    item: Models.Topic,
    selected: Boolean,
    searchInput: String,
    onSelected: (Models.Topic) -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    Row(
        modifier = modifier
            .fillMaxWidth()
            .sizeIn(minHeight = 53.dp)
            .padding(vertical = 15.dp)
            .clickable(interactionSource = interactionSource, indication = null) {
                onSelected.invoke(item)
            },
        verticalAlignment = Alignment.CenterVertically
    ) {

        ImageWithBitmapRes(
            modifier = Modifier.size(35.dp),
            bitmapRes = R.mipmap.search_icon,
            contentDescription = null,
        )
        Spacer(modifier = Modifier.width(5.dp))

        val annotatedTopicName = buildAnnotatedString {
            append("#")
            item.topicName?.run {
                val index = indexOf(searchInput, 0, true)
                if (index >= 0) {
                    withStyle(style = SpanStyle(color = R.color.font_style_95_6_textColor.color)) {
                        append(substring(index, index + searchInput.length))
                    }
                }
                append(item.topicName.replace(searchInput, "", true))
            }
        }

        Text(
            modifier = Modifier.weight(1f),
            text = annotatedTopicName,
            style = Type.font_style_29(),
            textAlign = TextAlign.Start,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
        Spacer(modifier = Modifier.width(5.dp))
        if (item.newTopic) {
            ImageWithBitmapRes(
                modifier = Modifier.size(27.5.dp),
                bitmapRes = R.mipmap.new_icon_community_huati_new,
                contentDescription = null,
            )
        } else if (item.hotTopic) {
            ImageWithBitmapRes(
                modifier = Modifier.size(13.dp),
                bitmapRes = R.mipmap.new_icon_community_huati_hot,
                contentDescription = null,
            )
        }
    }
}

@Composable
fun SimpleNestedScrollDemo() {
    val listState = rememberLazyListState()

    // 创建 NestedScrollConnection 监听滚动
    val nestedScrollConnection = remember {
        object : NestedScrollConnection {
            override fun onPreScroll(available: Offset, source: NestedScrollSource): Offset {
                // 上滑时触发（available.y < 0）
                if (available.y < 0) {
                    println("上滑动作：$available")
                }
                return Offset.Zero // 不消费事件，继续传递给子组件
            }

            override fun onPostScroll(
                consumed: Offset,
                available: Offset,
                source: NestedScrollSource
            ): Offset {
                // 下拉时触发（available.y > 0）
                if (available.y > 0) {
                    println("下拉动作：$available")
                }
                return Offset.Zero
            }
        }
    }

    // 应用嵌套滚动修饰符
    LazyColumn(
        modifier = Modifier.nestedScroll(nestedScrollConnection),
        state = listState
    ) {
        items(100) { index ->
            Text("Item $index", modifier = Modifier.padding(16.dp))
        }
    }
}
