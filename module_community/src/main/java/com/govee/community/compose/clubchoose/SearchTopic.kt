package com.govee.community.compose.clubchoose

import android.app.Activity
import android.view.ViewTreeObserver
import android.view.Window
import androidx.compose.animation.AnimatedVisibility
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.IntrinsicSize
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.heightIn
import androidx.compose.foundation.layout.ime
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.sizeIn
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.DropdownMenuItem
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.ExposedDropdownMenuBox
import androidx.compose.material3.HorizontalDivider
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.State
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.rotate
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalConfiguration
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.platform.LocalView
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.TextRange
import androidx.compose.ui.text.input.TextFieldValue
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.window.Dialog
import androidx.compose.ui.window.DialogProperties
import androidx.compose.ui.window.DialogWindowProvider
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.lifecycle.viewmodel.compose.viewModel
import com.govee.base2home.compose.AsyncImage
import com.govee.base2home.compose.CancelBtn
import com.govee.base2home.compose.ConfirmBtn
import com.govee.base2home.compose.ConfirmDrawableButton
import com.govee.base2home.compose.ImageWithBitmapRes
import com.govee.base2home.compose.color
import com.govee.base2home.compose.components.BaseEditText
import com.govee.base2home.compose.components.MultiEditText
import com.govee.base2home.compose.theme.Type
import com.govee.ui.R
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.skinv2.SkinM
import com.ihoment.base2app.util.ResUtil

/**
 * @author: hujie
 * @date: 2025/3/24
 * @description: 搜索话题
 */

@Composable
fun SearchTopic(
    modifier: Modifier = Modifier,
    viewModel: ClubChooseVM = viewModel()
) {
    val uiState by viewModel.uiStateFlow.collectAsState()
    val context = LocalContext.current
    Box(modifier = modifier.fillMaxSize(), contentAlignment = Alignment.Center) {
        when (uiState.searchState) {
            ClubChooseVM.LoadingState.LoadSuccess, ClubChooseVM.LoadingState.NoMore -> {
                TopicList(
                    modifier = Modifier
                        .padding(horizontal = 12.dp)
                        .fillMaxSize(),
                    selectedTopicId = uiState.selectedTopicId,
                    inSearch = true,
                    topics = uiState.topicList,
                    curClub = null,
                    topicLoadingState = uiState.topicState,
                    onSelected = { topic ->
                        viewModel.selectTopic(context, topic)
                    },
                    onLoadMore = {
                        viewModel.searchTopics(uiState.searchInput, true)
                    },
                    onNotAddTopic = {
                        viewModel.onNotAddTopic(context)
                    },
                    searchInput = uiState.searchInput
                )
            }

            ClubChooseVM.LoadingState.Error -> {
                ErrorContent {
                    viewModel.retryData()
                }
            }

            ClubChooseVM.LoadingState.Empty -> {
                SearchEmptyContent(canCreateTopic = uiState.canCreateTopic) {
                    viewModel.showCreateTopicDialog(true)
                }

                CreateTopicDialog(
                    input = uiState.searchInput,
                    showDialog = uiState.isShowCreateTopicDialog,
                    topicCheckStatus = uiState.topicCheckStatus,
                    circles = uiState.clubList.filter { it.isFellow() },
                    onDismiss = {
                        viewModel.showCreateTopicDialog(false)
                    },
                    onConfirm = { circle, topic, valid ->
                        if (uiState.topicCheckStatus != null && valid) {
                            viewModel.showCreateTopicDialog(false)
                            viewModel.chooseCircleTopic(
                                type = Models.TYPE_CREATE_TOPIC,
                                circle,
                                topic
                            )
                        } else {
                            viewModel.checkTopicStatus(
                                circleId = circle.circleId,
                                topicName = topic.topicName ?: "",
                                topicDes = topic.topicDes
                            )
                        }
                    },
                    onTopicNameChange = {
                        viewModel.resetTopicCheckStatus(isTopicNameChange = true)
                    },
                    onTopicDesChange = {
                        viewModel.resetTopicCheckStatus(isTopicNameChange = false)
                    },
                )
            }

            ClubChooseVM.LoadingState.UnInit -> {
                //空
            }

            ClubChooseVM.LoadingState.Loading -> {
                LoadingContent()
            }
        }
    }
}


@Composable
fun SearchEmptyContent(
    modifier: Modifier = Modifier,
    canCreateTopic: Boolean = false,
    onClick: () -> Unit
) {
    Column(
        modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        ImageWithBitmapRes(
            bitmapRes = R.mipmap.new_search_pics_search_no,
            contentDescription = null,
            modifier = Modifier
                .padding(top = 82.dp)
                .size(162.dp, 113.dp)
        )
        Spacer(modifier = Modifier.height(9.5.dp))
        Text(
            text = stringResource(R.string.community_search_topic_empty),
            style = Type.font_style_131_2()
        )
        if (canCreateTopic) {
            ConfirmDrawableButton(
                modifier = Modifier
                    .padding(top = 50.dp)
                    .width(185.dp)
                    .height(65.dp),
                text = stringResource(R.string.app_shortcut_manager_addable_create),
                onClick = onClick
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CreateTopicDialog(
    showDialog: Boolean,
    input: String,
    inputDesc: String = "",
    circles: List<Models.Circle>,
    selectCircle: Models.Circle? = null,
    topicCheckStatus: Models.TopicStatus? = null,
    edit: Boolean = false,
    onDismiss: () -> Unit,
    onConfirm: (Models.Circle, Models.Topic, Boolean) -> Unit,
    onTopicDesChange: () -> Unit,
    onTopicNameChange: () -> Unit,
) {
    val focusRequester = remember { FocusRequester() }
    var selectedCircle by remember(showDialog) { mutableStateOf(selectCircle) }
    var expanded by remember { mutableStateOf(false) }
    val keyboardOpen by rememberKeyboardState()
    var availableHeight = 535.dp
    if (keyboardOpen) {
        val keyboardHeightDp = WindowInsets.ime.asPaddingValues()
        val screenHeight = LocalConfiguration.current.screenHeightDp.dp
        val statusBarHeight = WindowInsets.systemBars.asPaddingValues()
        val systemBarHeight = WindowInsets.navigationBars.asPaddingValues()
        availableHeight =
            screenHeight - statusBarHeight.calculateTopPadding() - keyboardHeightDp.calculateBottomPadding() - systemBarHeight.calculateBottomPadding()
        SafeLog.i("CreateTopicDialog") { "CreateTopicDialog() screenHeight: $screenHeight, keyboardHeightDp: $keyboardHeightDp, statusBarHeight: $statusBarHeight, systemBarHeight: $systemBarHeight" }
    } else {
        availableHeight = 585.dp
    }
    val dialogTitle =
        if (edit) stringResource(R.string.community_topipc_dialog_edit_title) else stringResource(R.string.community_create_new_topic)
    val dialogDesc =
        if (edit) stringResource(R.string.community_topipc_dialog_edit_desc) else stringResource(R.string.community_topipc_dialog_create_desc)

    val oldInput = input
    val oldInputDesc = inputDesc
    val oldCircle = selectCircle
    if (showDialog) {
        Dialog(
            onDismissRequest = {},
            properties = DialogProperties(usePlatformDefaultWidth = false)
        ) {
            var topicName by remember(input) {
                mutableStateOf(
                    TextFieldValue(
                        text = input,
                        selection = TextRange(input.length)
                    )
                )
            }
            var topicDes by remember(input) {
                mutableStateOf(
                    TextFieldValue(
                        text = inputDesc,
                        selection = TextRange(inputDesc.length)
                    )
                )
            }

            val dialogWindow = getDialogWindow()
            val activityWindow = getActivityWindow()
            val dimAmount = if (SkinM.isLightMode()) 0.3f else 0.7f
            LaunchedEffect(Unit) {
                if (dialogWindow != null) {
                    dialogWindow.attributes = dialogWindow.attributes?.apply {
                        this.dimAmount = dimAmount
                    }
                }
                if (activityWindow != null) {
                    activityWindow.attributes = activityWindow.attributes?.apply {
                        this.dimAmount = dimAmount
                    }
                }
            }
            val interactionSource = remember { MutableInteractionSource() }
            val scrollState = rememberScrollState()
            val keyboardController = if (keyboardOpen) {
                LocalSoftwareKeyboardController.current
            } else {
                null
            }
            if (expanded) {
                keyboardController?.hide()
            }

            Box(
                modifier = Modifier
                    .width(335.dp)
                    .heightIn(max = if (keyboardOpen) 500.dp else 585.dp)
                    .height(IntrinsicSize.Min)
                    .clip(RoundedCornerShape(10.dp))
                    .background(R.color.ui_bg_color_style_2.color)
            ) {
                Column(
                    modifier = Modifier
                        .padding(start = 27.dp, end = 27.dp, top = 38.dp)
                ) {
                    //创建新话题
                    Text(
                        text = dialogTitle,
                        style = Type.font_style_32_1(),
                        modifier = Modifier
                            .align(Alignment.CenterHorizontally)
                    )

                    Column(
                        modifier = Modifier
                            .weight(1f)
                            .verticalScroll(scrollState)
                    ) {
                        //创建成功提示
                        Text(
                            modifier = Modifier.padding(top = 12.dp),
                            text = dialogDesc,
                            style = Type.font_style_29()
                        )

                        //输入话题名称
                        BaseEditText(
                            modifier = Modifier
                                .padding(top = 12.dp)
                                .fillMaxWidth()
                                .height(45.dp)
                                .clip(RoundedCornerShape(10.dp))
                                .background(R.color.ui_bg_color_style_6.color)
                                .padding(start = 10.dp, end = 5.dp),
                            requestFocus = true,
                            input = topicName,
                            textStyle = Type.font_style_29(),
                            hintTextStyle = Type.font_style_131_2(),
                            hintText = stringResource(R.string.community_please_input_topic_name),
                            rightIcon = {
                                AnimatedVisibility(topicName.text.isNotBlank()) {
                                    ImageWithBitmapRes(
                                        modifier = Modifier
                                            .padding(start = 12.dp)
                                            .size(27.dp)
                                            .clickable(
                                                indication = null,
                                                interactionSource = interactionSource
                                            ) {
                                                topicName = TextFieldValue("")
                                                onTopicNameChange.invoke()
                                            },
                                        bitmapRes = R.mipmap.new_btn_password_close,
                                        contentDescription = ""
                                    )
                                }
                            },
                            onInputChange = {
                                topicName = it
                                onTopicNameChange.invoke()
                            },
                            onlyText = true
                        )

                        //0正常, 1话题存在, 2存在敏感词
                        topicCheckStatus?.topicState?.run {
                            val hint = if (1 == this) {
                                //话题已存在?
                                if (edit) {
                                    if (oldInput == topicName.text) {
                                        ""
                                    } else {
                                        stringResource(R.string.community_topic_exisit)
                                    }
                                } else {
                                    stringResource(R.string.community_topic_exisit)
                                }
                            } else {
                                //存在敏感词
                                stringResource(R.string.community_topic_sensitive)
                            }
                            AnimatedVisibility(this != 0) {
                                Text(
                                    modifier = Modifier.padding(top = 6.dp),
                                    style = Type.font_style_27_3(),
                                    text = hint,
                                )
                            }
                        }

                        //输入话题描述
                        MultiEditText(
                            modifier = Modifier
                                .padding(top = 12.dp)
                                .fillMaxWidth()
                                .height(155.dp)
                                .clip(RoundedCornerShape(10.dp))
                                .background(R.color.ui_bg_color_style_6.color)
                                .padding(top = 13.dp, bottom = 10.dp, start = 13.dp, end = 13.dp),
                            maxLines = 6,
                            inputLimit = 300,
                            input = topicDes,
                            textStyle = Type.font_style_29().copy(lineHeight = 20.sp),
                            hintTextStyle = Type.font_style_131_2(),
                            hintText = stringResource(R.string.community_please_input_topic_des),
                            onInputChange = {
                                topicDes = it
                                onTopicDesChange.invoke()
                            }
                        )

                        AnimatedVisibility(topicCheckStatus?.desState == 1) {
                            //存在敏感词
                            Text(
                                modifier = Modifier.padding(top = 5.dp),
                                style = Type.font_style_27_3(),
                                text = stringResource(R.string.community_topic_des_sensitive),
                            )
                        }

                        //选择圈子
                        Text(
                            modifier = Modifier.padding(top = 12.dp),
                            style = Type.font_style_29(),
                            text = stringResource(R.string.community_please_choose_circle),
                        )

                        // 圈子下拉选择
                        ExposedDropdownMenuBox(
                            modifier = Modifier
                                .padding(top = 12.dp),
                            expanded = expanded,
                            onExpandedChange = {
                                expanded = !expanded
                            }
                        ) {
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(45.dp)
                                    .clip(RoundedCornerShape(10.dp))
                                    .background(colorResource(R.color.ui_bg_color_style_6))
                                    .focusRequester(focusRequester)
                                    .menuAnchor(),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                AsyncImage(
                                    modifier = Modifier
                                        .padding(start = 12.5.dp)
                                        .size(30.dp)
                                        .clip(RoundedCornerShape(4.dp)),
                                    contentScale = ContentScale.Crop,
                                    contentDescription = "",
                                    model = selectedCircle?.circleIcon ?: ""
                                )
                                Text(
                                    modifier = Modifier
                                        .weight(1f)
                                        .padding(start = 8.dp),
                                    text = selectedCircle?.circleName ?: "",
                                    style = Type.font_style_29(),
                                    maxLines = 1,
                                    overflow = TextOverflow.Ellipsis
                                )
                                val rotate by animateFloatAsState(if (expanded) 0f else 180f)
                                ImageWithBitmapRes(
                                    modifier = Modifier
                                        .padding(end = 12.5.dp)
                                        .size(18.dp)
                                        .rotate(rotate),
                                    bitmapRes = R.mipmap.search_icon_filtro_up,
                                    contentDescription = null
                                )
                            }

                            ExposedDropdownMenu(
                                modifier = Modifier,
                                expanded = expanded,
                                onDismissRequest = { expanded = false },
                                shape = RoundedCornerShape(10.dp),
                                containerColor = R.color.ui_bg_color_style_2.color
                            ) {
                                Column(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .sizeIn(maxHeight = 210.dp)
                                        .verticalScroll(rememberScrollState())
                                ) {
                                    val showCircles = circles.ifEmpty {
                                        listOf(
                                            Models.Circle(
                                                -1,
                                                ResUtil.getString(R.string.community_not_yet_join_any_circle),
                                                type = -1,
                                                topics = null,
                                                circleIcon = null
                                            )
                                        )
                                    }
                                    showCircles.forEachIndexed { index, circle ->
                                        DropdownMenuItem(
                                            text = {
                                                CircleItem4Search(
                                                    circle = circle,
                                                    selected = selectedCircle?.circleId == circle.circleId
                                                )
                                            },
                                            onClick = {
                                                if (circle.circleId != -1) {
                                                    selectedCircle = circle
                                                }
                                                expanded = false
                                            },
                                            contentPadding = PaddingValues(
                                                horizontal = 12.5.dp,
                                                vertical = 7.dp
                                            )
                                        )
                                        val showBottomLine = index < circles.size - 1
                                        if (showBottomLine) {
                                            HorizontalDivider(
                                                modifier = Modifier.padding(horizontal = 12.5.dp),
                                                color = colorResource(R.color.ui_split_line_style_5),
                                                thickness = 0.5.dp,
                                            )
                                        }
                                    }
                                }
                            }
                        }
                    }

                    Row(
                        horizontalArrangement = Arrangement.End,
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(top = 30.dp)
                    ) {
                        CancelBtn(
                            modifier = Modifier
                                .weight(1f),
                            text = stringResource(R.string.cancel),
                            onClick = {
                                onDismiss.invoke()
                            }
                        )
                        Spacer(Modifier.width(16.dp))
                        val valid = if (edit) {
                            topicCheckStatus != null &&
                                (topicCheckStatus.topicState == 0
                                    || (oldInput == topicName.text && topicCheckStatus.topicState == 1))//编辑模式下话题已存在是还要判断话题名称是否改变
                                && topicCheckStatus.desState == 0
                        } else {
                            topicCheckStatus != null &&
                                topicCheckStatus.topicState == 0 &&
                                topicCheckStatus.desState == 0
                        }


                        val changed = if (edit) {
                            oldInput != topicName.text || oldInputDesc != topicDes.text || oldCircle?.circleId != selectedCircle?.circleId
                        } else {
                            true
                        }
                        val enabled =
                            selectedCircle != null &&
                                topicName.text.isNotBlank() &&
                                topicDes.text.isNotBlank() &&
                                changed

                        if (valid) {
                            selectedCircle?.run {
                                val topic = Models.Topic(topicId = -1, topicName = topicName.text)
                                topic.topicDes = topicDes.text
                                onConfirm(this, topic, valid)
                            }
                        }

                        ConfirmBtn(
                            modifier = Modifier
                                .weight(1f),
                            text = stringResource(R.string.yes),
                            enabled = enabled,//正常话题才可点击
                            onClick = {
                                val topic = Models.Topic(topicId = -1, topicName = topicName.text)
                                topic.topicDes = topicDes.text
                                selectedCircle?.let { onConfirm(it, topic, false) }
                            },
                        )
                    }
                }
            }
        }
    }
}

//下拉圈子选择Item
@Composable
fun CircleItem4Search(
    modifier: Modifier = Modifier,
    circle: Models.Circle,
    selected: Boolean = false
) {
    val style = if (selected) {
        Type.font_style_41_8()
    } else {
        Type.font_style_29()
    }
    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
            .fillMaxWidth()
    ) {
        AsyncImage(
            modifier = Modifier
                .size(30.dp)
                .clip(RoundedCornerShape(4.dp)),
            contentScale = ContentScale.Crop,
            contentDescription = "",
            model = circle.circleIcon ?: ""
        )
        Spacer(Modifier.width(8.dp))
        Text(
            text = circle.circleName ?: "",
            style = style,
            maxLines = 1,
            overflow = TextOverflow.Ellipsis
        )
    }

}

@Composable
private fun getDialogWindow(): Window? = (LocalView.current.parent as? DialogWindowProvider)?.window

@Composable
private fun getActivityWindow(): Window? = (LocalView.current.context as? Activity)?.window


@Composable
fun rememberKeyboardState(): State<Boolean> {
    val isOpen = remember { mutableStateOf(false) }
    val view = LocalView.current

    DisposableEffect(Unit) {
        val listener = ViewTreeObserver.OnGlobalLayoutListener {
            val insets = ViewCompat.getRootWindowInsets(view)
            val imeVisible = insets?.isVisible(WindowInsetsCompat.Type.ime()) ?: false
            isOpen.value = imeVisible
        }

        view.viewTreeObserver.addOnGlobalLayoutListener(listener)
        onDispose {
            view.viewTreeObserver.removeOnGlobalLayoutListener(listener)
        }
    }
    return isOpen
}