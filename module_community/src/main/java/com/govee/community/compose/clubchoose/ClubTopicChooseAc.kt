package com.govee.community.compose.clubchoose

import android.os.Bundle
import androidx.activity.compose.BackHandler
import androidx.activity.viewModels
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.focusable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.asPaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.systemBars
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.viewmodel.compose.viewModel
import com.govee.base2home.compose.BaseComposeActivity
import com.govee.base2home.compose.ImageWithBitmapRes
import com.govee.base2home.compose.colorResource
import com.govee.base2home.compose.components.GoveeCircleLoading
import com.govee.base2home.compose.components.NetErrorView
import com.govee.base2home.compose.components.SearchEditText
import com.govee.base2home.compose.theme.Type
import com.govee.base2home.h5.EventAddCircleTopic
import com.govee.base2home.vip.VipM
import com.govee.kt.hideLoading
import com.govee.kt.showLoading
import com.govee.mvvm.launchRepeatOnLifecycle
import com.govee.ui.R

/**
 * @author: hujie
 * @date: 2025/3/24
 * @description: 圈子话题选择
 */

class ClubChooseAc : BaseComposeActivity() {
    private val viewModel by viewModels<ClubChooseVM>()

    companion object {
        const val TAG = "ClubChooseAc"
    }

    @Composable
    override fun SetContentUI() {
        ClubChoose(viewModel = viewModel) {
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        initEventObserver()
        val circleId = intent.getIntExtra(EventAddCircleTopic.KEY_CIRCLE_ID, -1)
        val topicId = intent.getIntExtra(EventAddCircleTopic.KEY_TOPIC_ID, -1)
        val topicName = intent.getStringExtra(EventAddCircleTopic.KEY_TOPIC_NAME) ?: ""
        val fromNormalPage = intent.getBooleanExtra(EventAddCircleTopic.KEY_FROM_NORMAL_PAGE, true)
        viewModel.initData(
            circleId = circleId,
            topicId = topicId,
            topicName = topicName,
            fromAskPage = !fromNormalPage
        )
        VipM.getInstance.getVipCenterInfo()//更新su数据
        super.onCreate(savedInstanceState)
    }

    private fun initEventObserver() {
        launchRepeatOnLifecycle(Lifecycle.State.STARTED) {
            viewModel.eventFlow.collect {
                when (it) {
                    is ClubChooseVM.Event.DialogLoading -> {
                        showLoading()
                    }

                    is ClubChooseVM.Event.HideDialogLoading -> {
                        hideLoading()
                    }

                    is ClubChooseVM.Event.FinishAc -> {
                        finish()
                    }
                }
            }
        }
    }

}

@Composable
fun ClubChoose(
    modifier: Modifier = Modifier,
    viewModel: ClubChooseVM = viewModel(),
    onClickBack: () -> Unit
) {
    val uiState by viewModel.uiStateFlow.collectAsState()
    val focusRequester = remember { FocusRequester() }
    val keyboardController = LocalSoftwareKeyboardController.current
    fun backHandler() {
        if (uiState.inSearch) {
            focusRequester.requestFocus()
            keyboardController?.hide()
            viewModel.exitSearch()
        } else {
            onClickBack.invoke()
        }
    }
    Scaffold(
        modifier = modifier.fillMaxSize(),
        topBar = {
            ToolBar(
                modifier = Modifier.padding(top = 10.5.dp, bottom = 12.dp),
                searchInput = uiState.searchInput,
                focus = uiState.inSearch,
                onSearchChange = {
                    viewModel.startSearch(it)
                },
                onClickBack = {
                    backHandler()
                },
                onFocusChange = { focusOn ->
                    if (focusOn) {
                        viewModel.startSearch(uiState.searchInput)
                    } else {
                        viewModel.exitSearch()
                    }
                }
            )
        }) { paddingValues ->
        Box(
            modifier = modifier
                .fillMaxSize()
                .background(colorResource(R.color.ui_bg_color_style_2))
                .focusRequester(focusRequester)
                .focusable(true)
                .padding(paddingValues),
            contentAlignment = Alignment.Center
        ) {
            when (uiState.loadState) {
                ClubChooseVM.LoadingState.UnInit -> {
                    LaunchedEffect(Unit) {
                        viewModel.loadCircles()
                    }
                }

                ClubChooseVM.LoadingState.Loading -> {
                    LoadingContent(modifier = Modifier.align(Alignment.Center))
                }

                ClubChooseVM.LoadingState.Empty -> {
                    EmptyContent(modifier = Modifier.align(Alignment.Center))
                }

                ClubChooseVM.LoadingState.LoadSuccess -> {
                    SuccessContent(
                        modifier = Modifier.align(Alignment.Center),
                        inSearch = uiState.inSearch
                    )
                }

                ClubChooseVM.LoadingState.Error -> {
                    ErrorContent(modifier = Modifier.align(Alignment.Center)) {
                        viewModel.retryData()
                    }
                }

                else -> {

                }
            }
            BackHandler {
                backHandler()
            }
        }
    }
}

@Composable
fun LoadingContent(modifier: Modifier = Modifier) {
    GoveeCircleLoading(modifier = modifier)
}

@Composable
fun SuccessContent(modifier: Modifier = Modifier, inSearch: Boolean) {
    Box(modifier = modifier) {
        if (inSearch) {
            SearchTopic()
        } else {
            ClubTopics()
        }
    }
}

@Composable
fun EmptyContent(modifier: Modifier) {
    Text(
        modifier = modifier,
        text = stringResource(R.string.app_empty_data),
        style = Type.font_style_75_2()
    )
}

@Composable
fun ErrorContent(modifier: Modifier = Modifier, onRetry: () -> Unit) {
    NetErrorView(modifier = modifier.fillMaxSize()) {
        onRetry.invoke()
    }
}


@Composable
fun ToolBar(
    modifier: Modifier = Modifier,
    searchInput: String,
    onSearchChange: (String) -> Unit,
    onClickBack: () -> Unit,
    onFocusChange: (Boolean) -> Unit,
    focus: Boolean
) {
    val paddingValues = WindowInsets.systemBars.asPaddingValues()
    val canCreateTopic = VipM.getInstance.level >= 3
    Row(
        modifier = modifier
            .padding(top = paddingValues.calculateTopPadding())
            .fillMaxWidth()
            .padding(start = 12.dp, end = 12.dp)

    ) {
        Box(
            modifier = Modifier
                .size(34.dp)
                .clickable(
                    interactionSource = remember { MutableInteractionSource() },
                    indication = null
                ) {
                    onClickBack()
                }
        ) {
            ImageWithBitmapRes(
                bitmapRes = R.mipmap.new_btn_bg_top_corner,
                contentDescription = null,
                contentScale = ContentScale.Inside,
                modifier = Modifier.fillMaxSize()
            )

            ImageWithBitmapRes(
                bitmapRes = R.mipmap.new_sensor_setting_icon_arrow_left,
                contentDescription = null,
                contentScale = ContentScale.Inside,
                modifier = Modifier.fillMaxSize()
            )
        }
        Spacer(modifier = Modifier.width(12.dp))
        SearchEditText(
            modifier = Modifier.weight(1f),
            input = searchInput,
            hintText = if (canCreateTopic) stringResource(R.string.community_topic_search_and_create_topic) else stringResource(R.string.community_topic_search_topic),
            requestFocus = focus,
            onInputChange = { input ->
                onSearchChange.invoke(input)
            },
            onFocusChange = onFocusChange,
            onlyText = true,
            leftIcon = {
                ImageWithBitmapRes(
                    modifier = Modifier.size(35.dp),
                    bitmapRes = R.mipmap.search_icon_topic_gery
                )
            }
        )
    }
}