package com.govee.community.compose.clubchoose

import android.content.Context
import androidx.lifecycle.viewModelScope
import com.govee.base2home.h5.EventAddCircleTopic
import com.govee.base2home.vip.EventVipSimpleInfo
import com.govee.base2home.vip.VipM
import com.govee.bean.community.CheckTopicStatus
import com.govee.community.bean.RequestTopicEdit
import com.govee.community.network.communityApiService
import com.govee.community.viewmodel.CommonPostViewModel
import com.govee.mvvm.ext.request
import com.govee.ui.R
import com.govee.ui.dialog.ConfirmDialog
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.debounce
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * @author: hujie
 * @date: 2025/3/24
 * @description:
 */
class ClubChooseVM : CommonPostViewModel() {
    private val _eventFlow = MutableSharedFlow<Event>()
    val eventFlow = _eventFlow.asSharedFlow()
    private val _uiStateFlow = MutableStateFlow(
        UiState(
            loadState = LoadingState.UnInit,
            searchState = LoadingState.UnInit,
            topicState = LoadingState.UnInit,
            inSearch = false,
            selectedClubId = -1,
            selectedTopicId = -1
        )
    )
    val uiStateFlow = _uiStateFlow.asStateFlow()

    private val circleTopic = mutableMapOf<Int, MutableList<Models.Topic>>()

    private var initSelectedClubId = -1
    private var lastTopicName = ""


    init {
        viewModelScope.launch {
            //搜索
            uiStateFlow.map { Pair(it.inSearch, it.searchInput) }.debounce(500)
                .distinctUntilChanged().collect {
                    if (it.first && it.second.isNotBlank()) {
                        searchTopics(it.second)
                    }
                }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventVipCenterInfo(response: EventVipSimpleInfo?) {
        val canCreateTopic = VipM.getInstance.level >= 3
        //不到Lv3无法创建话题
        SafeLog.i(TAG) { "onEventVipCenterInfo() ${VipM.getInstance.level}" }
        _uiStateFlow.update { it.copy(canCreateTopic = canCreateTopic) }
    }

    fun initData(
        circleId: Int,
        topicId: Int,
        topicName: String,
        fromAskPage: Boolean
    ) {
        SafeLog.i(TAG) { "initData() circleId: $circleId, topicId: $topicId, $topicName" }
        initSelectedClubId = circleId
        lastTopicName = topicName
        _uiStateFlow.update {
            it.copy(
                selectedClubId = circleId,
                selectedTopicId = topicId,
                fromAskPage = fromAskPage
            )
        }
        viewModelScope.launch {
            VipM.getInstance.checkVip()
        }
    }

    /**
     * 选择圈子
     */
    fun selectClub(club: Models.Circle) {
        SafeLog.i(TAG) { "selectClub() $club" }
        val topics = circleTopic[club.circleId]
        _uiStateFlow.update { it.copy(selectedClubId = club.circleId) }
        _uiStateFlow.update {
            it.copy(
                topicState = LoadingState.LoadSuccess,
                topicList = topics ?: emptyList(),
                isFirstEnter = false
            )
        }
    }

    /**
     * 选择话题
     */
    fun selectTopic(
        context: Context,
        topic: Models.Topic? = null,
        fromCreate: Boolean = false
    ) {
        val circle =
            _uiStateFlow.value.clubList.firstOrNull {
                it.circleId == (topic?.circleId ?: _uiStateFlow.value.selectedClubId)
            }
        SafeLog.i(TAG) { "selectTopic() $topic" }
        val onConfirm: () -> Unit = {
            _uiStateFlow.update { it.copy(selectedTopicId = topic?.topicId ?: -1) }
            var type = Models.TYPE_CIRCLE_TOPIC
            if ((circle?.circleId ?: -1) == -1) {
                type = Models.TYPE_ONLY_TOPIC
            }
            if (topic == null) {
                type = Models.TYPE_ONLY_CIRCLE
            }
            if (fromCreate) {
                type = Models.TYPE_CREATE_TOPIC
            }
            chooseCircleTopic(
                type = type,
                circle = circle,
                topic = topic
            )
        }

        val lastSelectedTopicId = _uiStateFlow.value.selectedTopicId
        val lastSelectedClubId = initSelectedClubId
        if (lastSelectedClubId == -1 && lastSelectedTopicId == -1) {
            SafeLog.i(TAG) { "selectTopic() 没选过" }
            onConfirm.invoke()
            return
        }

        if (lastTopicName.isNotEmpty() && lastSelectedTopicId == -1) {
            //新增进来的
            SafeLog.i(TAG) { "selectTopic() jump from create" }
            showConfirmDialog(context = context, onConfirm = onConfirm, onlyCircle = topic == null)
            return
        }

        if ((circle?.circleId ?: -1) != lastSelectedClubId || (topic?.topicId
                ?: -1) != lastSelectedTopicId
        ) {
            showConfirmDialog(context = context, onConfirm = onConfirm, onlyCircle = topic == null)
        } else {
            onConfirm.invoke()
        }
    }

    private fun showConfirmDialog(
        context: Context,
        onConfirm: () -> Unit,
        onlyCircle: Boolean = false,
    ) {
        ConfirmDialog.showConfirmDialog(
            context,
            ResUtil.getString(if (onlyCircle) R.string.community_change_circle_hint else R.string.community_change_circle_topic_hint),
            ResUtil.getString(R.string.cancel),
            ResUtil.getString(R.string.confirm),
        ) {
            onConfirm.invoke()
        }
    }

    /**
     * 进入搜索
     */
    fun startSearch(input: String) {
        val changed = input != _uiStateFlow.value.searchInput
        _uiStateFlow.update {
            it.copy(
                searchInput = input,
                inSearch = true,
                searchState = if (changed) LoadingState.UnInit else it.searchState,
                topicList = if (changed) emptyList() else it.topicList,
                isFirstEnter = false
            )
        }
    }

    /**
     * 退出搜索
     */
    fun exitSearch() {
        val topics = circleTopic[_uiStateFlow.value.selectedClubId] ?: emptyList()
        _uiStateFlow.update {
            it.copy(
                inSearch = false,
                searchState = LoadingState.UnInit,
                searchInput = "",
                topicList = topics
            )
        }
    }

    /**
     * 显示创建话题弹窗
     */
    fun showCreateTopicDialog(show: Boolean) {
        _uiStateFlow.update { it.copy(isShowCreateTopicDialog = show, topicCheckStatus = null) }
    }


    /**
     * 显示编辑话题弹窗
     */
    fun showEditTopicDialog(show: Boolean, input: String, inputDesc: String) {
        _uiStateFlow.update {
            it.copy(
                isShowCreateTopicDialog = show,
                searchInput = input,
                searchInputDesc = inputDesc,
                topicCheckStatus = null
            )
        }
    }

    /**
     * 选取圈子话题，回到H5
     * @param type type: 1选择非圈子话题，2只选择圈子，3圈子+话题，4新增话题
     * @param topic 话题描述，创建话题时需要
     */
    fun chooseCircleTopic(
        type: Int,
        circle: Models.Circle?,
        topic: Models.Topic? = null,
    ) {
        SafeLog.i(TAG) { "chooseCircleTopic() " }
        //通知H5
        EventAddCircleTopic.send(
            type = type,
            circleId = circle?.circleId ?: -1,
            topicId = topic?.topicId ?: -1,
            topicName = topic?.topicName ?: "",
            topicDes = topic?.topicDes ?: ""
        )
        //退出
        sendEvent(Event.FinishAc)
    }

    /**
     * 加载所有圈子，并且加载入口圈子20条话题
     *
     */
    fun loadCircles() {
        val circleId = _uiStateFlow.value.selectedClubId
        circleTopic.clear()
        _uiStateFlow.update {
            it.copy(
                loadState = LoadingState.Loading,
                topicList = emptyList(),
                clubList = emptyList()
            )
        }
        request({
            communityApiService.fetchPostCircleList()
        }, success = { resp ->
            if (resp.circles.isEmpty()) {
                _uiStateFlow.update { it.copy(loadState = LoadingState.Empty) }
            } else {
                val fromAskPage = _uiStateFlow.value.fromAskPage
                var curClub =
                    resp.circles.firstOrNull { it.circleId == circleId } ?: resp.circles[0]
                if (fromAskPage && curClub.circleId == -1) {
                    curClub = resp.circles.firstOrNull { it.circleId != -1 } ?: resp.circles[0]
                }
                _uiStateFlow.update {
                    it.copy(
                        loadState = LoadingState.LoadSuccess,
                        topicState = if (curClub.topics.isNullOrEmpty()) {
                            LoadingState.Empty
                        } else {
                            LoadingState.LoadSuccess
                        },
                        selectedClubId = curClub.circleId,
                        clubList = resp.circles,
                        topicList = curClub.topics ?: emptyList(),
                        isFirstEnter = true
                    )
                }
            }
        }, error = { exception ->
            toast(exception.errorMsg)
            _uiStateFlow.update { it.copy(loadState = LoadingState.Error) }
        })
    }


    /**
     * 某个圈子话题，分页查询
     *
     */
    fun loadTopics(loadMore: Boolean = false) {
        if (_uiStateFlow.value.topicState == LoadingState.Loading) {
            SafeLog.i(TAG) { "loadTopics()  正在刷新 返回" }
            return
        }
        val circleId = _uiStateFlow.value.selectedClubId
        _uiStateFlow.update { it.copy(topicState = LoadingState.Loading) }
        val topics = circleTopic[circleId] ?: mutableListOf()
        var createTime = if (topics.isEmpty()) {
            -1
        } else {
            topics.last().createTime
        } ?: -1
        if (!loadMore) {
            createTime = -1
        }
        request({
            communityApiService.fetchPostTopicList(circleId = circleId, createTime = createTime)
        }, success = { resp ->
            circleTopic[circleId] = topics
            val beforeSize = topics.size
            val afterSize = (topics + resp.topics).distinctBy { it.topicId }.size
            if (afterSize == beforeSize) {
                //没有更多数据了
                _uiStateFlow.update { it.copy(topicState = LoadingState.NoMore) }
                toast(ResUtil.getString(R.string.app_video_at_last))
            } else {
                topics.addAll(resp.topics)
                topics.distinctBy { it.topicId }
                _uiStateFlow.update {
                    it.copy(
                        topicState = LoadingState.LoadSuccess,
                        topicList = topics.toMutableList()
                    )
                }
            }
        }, error = { resp ->
            toast(resp.errorMsg)
            _uiStateFlow.update { it.copy(topicState = LoadingState.Error) }
        })
    }

    /**
     * 搜索话题
     *
     */
    fun searchTopics(key: String, loadMore: Boolean = false) {
        if (!loadMore) {
            _uiStateFlow.update { it.copy(searchState = LoadingState.Loading) }
        } else {
            _uiStateFlow.update { it.copy(topicState = LoadingState.Loading) }
        }

        val topics = _uiStateFlow.value.topicList.toMutableList()
        var createTime = if (topics.isEmpty()) {
            -1
        } else {
            topics.last().createTime
        } ?: -1

        var discussionNum = if (topics.isEmpty()) {
            -1
        } else {
            topics.last().discussionNum
        }

        if (!loadMore) {
            createTime = -1
            discussionNum = -1
            topics.clear()
        }

        request({
            communityApiService.fetchPostTopicList(
                circleId = null,
                topicName = key,
                createTime = createTime,
                discussionNum = discussionNum
            )
        }, success = { resp ->
            val beforeSize = topics.size
            val afterSize = (topics + resp.topics).distinctBy { it.topicId }.size
            if (afterSize == beforeSize) {
                //没有更多数据了
                if (loadMore) {
                    _uiStateFlow.update { it.copy(topicState = LoadingState.NoMore) }
                    toast(ResUtil.getString(R.string.app_video_at_last))
                } else {
                    _uiStateFlow.update { it.copy(searchState = LoadingState.Empty) }
                }
            } else {
                topics.addAll(resp.topics)
                topics.distinctBy { it.topicId }
                if (loadMore) {
                    _uiStateFlow.update {
                        it.copy(
                            topicList = topics.toMutableList(),
                            topicState = LoadingState.LoadSuccess
                        )
                    }
                } else {
                    _uiStateFlow.update {
                        it.copy(
                            topicList = topics.toMutableList(),
                            topicState = LoadingState.LoadSuccess,
                            searchState = LoadingState.LoadSuccess
                        )
                    }
                }
            }
        }, error = { resp ->
            toast(resp.errorMsg)
            _uiStateFlow.update {
                it.copy(
                    topicState = LoadingState.Error,
                    searchState = LoadingState.LoadSuccess
                )
            }
        })
    }

    /**
     * 检查话题状态
     * @param circleId
     * @param topicName
     */
    fun checkTopicStatus(circleId: Int, topicName: String, topicDes: String) {
        sendEvent(Event.DialogLoading)
        _uiStateFlow.update { it.copy(topicCheckStatus = null) }
        request({
            communityApiService.checkTopicStatus(
                CheckTopicStatus(topicName, topicDes, circleId)
            )
        }, success = { resp ->
            _uiStateFlow.update { it.copy(topicCheckStatus = resp) }
            sendEvent(Event.HideDialogLoading)
        }, error = { resp ->
            sendEvent(Event.HideDialogLoading)
            toast(resp.errorMsg)
        })
    }

    fun topicEdit(topicId: Int, topicName: String, topicDes: String, circleId: Int) {
        request({
            communityApiService.editTopic(RequestTopicEdit(topicId, topicName, topicDes, circleId))
        }, success = {
            _uiStateFlow.update { it.copy(editSuc = true) }
        }, error = { resp ->
            toast(resp.errorMsg)
        })
    }

    //重试
    fun retryData() {
        SafeLog.i(TAG) { "retryData() " }
        if (_uiStateFlow.value.inSearch) {
            searchTopics(_uiStateFlow.value.searchInput)
        } else {
            loadCircles()
        }
    }

    fun onNotAddTopic(context: Context) {
        SafeLog.i(TAG) { "onNotAddTopic() " }
        selectTopic(context)
    }

    fun sendEvent(event: Event) {
        viewModelScope.launch {
            _eventFlow.emit(event)
        }
    }

    fun resetTopicCheckStatus(isTopicNameChange: Boolean) {
        var topicCheckStatus = _uiStateFlow.value.topicCheckStatus ?: return
        //重置
        topicCheckStatus = if (isTopicNameChange) {
            topicCheckStatus.copy(topicState = null)
        } else {
            topicCheckStatus.copy(desState = null)
        }
        _uiStateFlow.update { it.copy(topicCheckStatus = topicCheckStatus) }
    }


    enum class LoadingState {
        UnInit,
        Loading,
        Error,
        Empty,
        LoadSuccess,
        NoMore,
    }

    data class UiState(
        val loadState: LoadingState,
        val searchState: LoadingState,
        val topicState: LoadingState,
        val inSearch: Boolean,
        val fromAskPage: Boolean = false,
        val searchInput: String = "",
        val searchInputDesc: String = "",
        val isShowCreateTopicDialog: Boolean = false,
        val selectedClubId: Int,
        val selectedTopicId: Int,
        val topicList: List<Models.Topic> = emptyList(),
        val clubList: List<Models.Circle> = emptyList(),
        val topicCheckStatus: Models.TopicStatus? = null,
        val canCreateTopic: Boolean = false,
        val isFirstEnter: Boolean = false,
        val editSuc: Boolean = false,
    )

    sealed class Event {
        data object DialogLoading : Event()
        data object HideDialogLoading : Event()
        data object FinishAc : Event()
    }
}