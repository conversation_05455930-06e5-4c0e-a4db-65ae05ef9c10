package com.govee.community.network

import com.govee.base2home.community.CommunityApi.Companion.DELETE_VIDEOS
import com.govee.base2home.community.CommunityApi.Companion.ENVOY_ENTRANCES
import com.govee.base2home.community.CommunityApi.Companion.HOMEPAGE_SHARE_DIY_VIDEOS
import com.govee.base2home.community.CommunityApi.Companion.HOMEPAGE_SHARE_VIDEOS
import com.govee.base2home.community.CommunityApi.Companion.HOMEPAGE_SHARE_VIDEOS_OTHER
import com.govee.base2home.community.CommunityApi.Companion.HOMEPAGE_VIDEO_LIST
import com.govee.base2home.community.CommunityApi.Companion.POST_DIY_VIDEOS_PREVIEW
import com.govee.base2home.community.CommunityApi.Companion.SCROLL_VIDEO_POSTS
import com.govee.base2home.community.CommunityApi.Companion.VIDEOS_VIEW_SHARES
import com.govee.base2home.community.CommunityApi.Companion.VIDEOS_VIEW_TIMES
import com.govee.base2home.community.UrlConstant
import com.govee.base2home.community.bean.CommunityGuideLinesBean
import com.govee.base2home.community.bean.CommunityNewUser
import com.govee.base2home.community.bean.LikeUserResult
import com.govee.bean.community.AssociationBean
import com.govee.bean.community.CheckTopicStatus
import com.govee.bean.community.CircleDetail4Roadmap
import com.govee.bean.community.CircleDetails
import com.govee.bean.community.CircleInviteAnswer
import com.govee.bean.community.CommunityCircleAll
import com.govee.bean.community.CommunityCircleMore
import com.govee.bean.community.CommunityDiscovery
import com.govee.bean.community.CommunityPostings
import com.govee.bean.community.CommunitySearchPostings
import com.govee.bean.community.CommunityVideo
import com.govee.bean.community.Data4SearchAll
import com.govee.bean.community.FollowUserData
import com.govee.bean.community.FollowUserRecommendData
import com.govee.bean.community.FollowingUser
import com.govee.bean.community.FriendDynamicPost
import com.govee.bean.community.RedDotsData
import com.govee.bean.community.Request4JoinCircle
import com.govee.bean.community.Request4RoadmapLike
import com.govee.bean.community.RoadmapList
import com.govee.bean.community.SaveMyCircleRequest
import com.govee.bean.community.Topic4SearchList
import com.govee.bean.community.TopicDetails
import com.govee.bean.community.TopicList
import com.govee.bean.community.User4SearchList
import com.govee.bean.community.VideoLikeState
import com.govee.bean.community.WorkShop
import com.govee.community.bean.MemberListBean
import com.govee.community.bean.RequestTopicComplaint
import com.govee.community.bean.RequestTopicEdit
import com.govee.community.biz.follow.bean.FavorsData
import com.govee.community.biz.follow.bean.FollowPictureData
import com.govee.community.biz.homepage.bean.HomePageEnvoy
import com.govee.community.biz.homepage.bean.RequestFeaturedColorAdd
import com.govee.community.biz.homepage.bean.RequestFeaturedDiyAdd
import com.govee.community.biz.homepage.bean.RequestFeaturedDiyCollections
import com.govee.community.biz.homepage.bean.RequestFeaturedDiyEdit
import com.govee.community.biz.homepage.bean.RequestFeaturedDiyLikes
import com.govee.community.biz.homepage.bean.RequestFollowingUser
import com.govee.community.biz.homepage.bean.RequestHomepageDevicesStatus
import com.govee.community.biz.homepage.bean.RequestHomepageUserScreens
import com.govee.community.biz.homepage.bean.RequestReplaceCoverUrl
import com.govee.community.biz.homepage.bean.ResponseAboutBean
import com.govee.community.biz.homepage.bean.ResponseFeaturedColors
import com.govee.community.biz.homepage.bean.ResponseFeaturedDiyAdd
import com.govee.community.biz.homepage.bean.ResponseFeaturedDiyCollections
import com.govee.community.biz.homepage.bean.ResponseFeaturedDiyDelete
import com.govee.community.biz.homepage.bean.ResponseFeaturedDiyEdit
import com.govee.community.biz.homepage.bean.ResponseFeaturedDiyLikes
import com.govee.community.biz.homepage.bean.ResponseFeaturedDiyList
import com.govee.community.biz.homepage.bean.ResponseFeaturedOwnDiyList
import com.govee.community.biz.homepage.bean.ResponseFollowerList
import com.govee.community.biz.homepage.bean.ResponseHomepageDiyVideo
import com.govee.community.biz.homepage.bean.ResponseHomepageUserScreens
import com.govee.community.biz.homepage.bean.ResponseMusicDiyVideo
import com.govee.community.biz.message.bean.RequestReadMessage
import com.govee.community.biz.message.bean.ResponseMyMessage
import com.govee.community.biz.official.bean.OfficialApplicationRsp
import com.govee.community.biz.official.bean.OfficialCOAllRsp
import com.govee.community.biz.official.bean.OfficialDetail
import com.govee.community.biz.official.bean.OfficialDetailActivity
import com.govee.community.biz.official.bean.OfficialFirstBirdRsp
import com.govee.community.biz.official.bean.OfficialOperation
import com.govee.community.biz.official.bean.OfficialOperationFloat
import com.govee.community.biz.official.bean.OfficialOperationFloatRsp
import com.govee.community.biz.official.bean.OfficialOperationRsp
import com.govee.community.biz.official.bean.OfficialQaRsp
import com.govee.community.biz.official.bean.OfficialTrialQaItemRsp
import com.govee.community.biz.search.bean.CirCleSearchHomeBean
import com.govee.community.biz.search.bean.CommunitySearchHomeBean
import com.govee.community.compose.clubchoose.Models
import com.govee.community.video.CommunityVideoSearch
import com.govee.community.video.ProductResponse
import com.govee.community.video.RequestVideoPlayTimes
import com.govee.community.video.ScrollVideoBean
import com.govee.community.viewmodel.CircleTabViewModel
import com.govee.mvvm.network.BaseStatusResponse
import com.ihoment.base2app.networkV2.NetWork
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Query

/**
 *     author  : sinrow
 *     time    : 2025/3/3
 *     version : 1.0.0
 *     desc    : 社区服务器接口类 API 服务，统一在底下补充
 */

val communityApiService by lazy {
    NetWork.getService(ICommunityApiService::class.java)
}

interface ICommunityApiService {

    companion object {
        /** 发现首页 */
        const val URL_DISCOVERY_HOME = "/bff-app/v1/community/discovery-home"

        const val URL_DISCOVERY_HOME_PAGE = "/bff-app/v1/community/discovery-postings"/*发现首页分页加载*/

        /**帖子基础类 --- star*/
        const val URL_POST_VIDEO_LIKE = "bi/rest/v2/evals/likes"/*视频点赞*/
        const val URL_POST_LIKE = "/bi/rest/v1/postings/spot"/*帖子点赞*/
        const val URL_POST_VIEWS = "bi/rest/v1/postings/views"/*帖子浏览量*/
        const val URL_COMMUNITY_GUIDE_LINES = "/bff-app/v1/community/discovery-guides"// 社区指南
        const val URL_COMMUNITY_NEW_USER =
            "/bff-app/v1/community/new-users"// 是否为新用户；注册时间小于30天 & 未发过帖子
        /**帖子基础类 --- end*/

        /**圈子api -- star*/
        const val URL_CIRCLE_INVITE_ANSWER = "/bff-app/v1/community/circle/invite/answer"/*邀请回答*/
        const val URL_CIRCLE_MESSAGE = "/bff-app/v1/community/circle/message"/*关注圈子&推荐圈子列表*/
        const val URL_CIRCLE_POSTINGS = "/bff-app/v1/community/circle/circle-postings"/*圈子帖子列表*/
        const val URL_CIRCLE_HOME = "/bff-app/v1/community/circle/home"/*圈子首页*/
        const val URL_CIRCLE_FOLLOW = "/appco/v1/community/circles"/*关注圈子*/
        const val URL_CIRCLE_ROADMAP_DETAIL =
            "/bff-app/v1/community/circle/details/roadmap"/*Roadmap圈子详情*/
        const val URL_CIRCLE_DETAILS = "/bff-app/v1/community/circle/details"/*圈子详情页*/
        const val URL_CIRCLE_ROADMAPS_PAGE =
            "/appco/v1/community/circles/roadmaps/page"/*roadmaps列表*/
        const val URL_CIRCLE_ROADMAPS_LIKES = "/app/v1/roadmaps/likes"
        const val URL_TOPIC_DETAILS = "/bff-app/v1/community/circle/topic/info"/*话题详情页*/
        const val URL_TOPIC_LIST = "/bff-app/v1/community/circle/topics"/*话题列表页*/
        const val URL_CIRCLE_MESSAGE_SORT = "/bff-app/v1/community/circle/message/sort"/*关注圈子排序列表*/
        const val URL_CIRCLE_TOPIC_COMPLAINT =
            "/bff-app/v1/community/circle/topic/complaint"/*投诉话题*/
        const val URL_TOPIC_POSTINGS = "/bff-app/v1/community/circle/portal/postings"/*话题帖子列表*/
        const val URL_COMMUNITY_CIRCLE_USERS =
            "/appco/v1/community/circles/users" /* 社区-圈子-查看加入圈子成员 */

        /**圈子api -- end*/
        //圈子发帖中的话题列表
        const val URL_POST_TOPIC_LIST = "/bff-app/v1/community/posting/detail/circle-topic/page"

        //获取所有圈子和某个圈子第一个列表
        const val URL_POST_CIRCLE_LIST = "/bff-app/v1/community/posting/detail/circle-topics"

        //查询话题是否可以创建
        const val URL_POST_CHECK_TOPIC_STATUS = "/bff-app/v1/community/posting/detail/topics"

        const val URL_USER_FOLLOW = "/appco/v1/users/subscription"/*用户关注*/

        /**官方主页api -- star*/
        const val URL_OFFICIAL_DETAILS = "/bff-app/v1/community/official/details"
        const val URL_OFFICIAL_ACTIVITIES = "/bff-app/v1/community/official/activities"
        const val URL_OFFICIAL_POSTINGS = "/bff-app/v1/community/official/postings"
        const val URL_OFFICIAL_CO_ALL = "/bff-app/v1/community/official/co-operations/space"
        const val URL_OFFICIAL_CO_OPERATIONS = "/bff-app/v1/community/official/co-operations"
        const val URL_OFFICIAL_CO_BIRD = "/bff-app/v1/community/official/activities/crowdfunds"
        const val URL_OFFICIAL_CO_TRIAL = "/bff-app/v1/community/official/activities/trial"
        const val URL_OFFICIAL_CO_QA = "/bff-app/v1/community/official/activities/qas"
        const val URL_OFFICIAL_CO_FLOAT_SET = "/bff-app/v1/co-operation/suspensions"
        const val URL_OFFICIAL_CO_FLOAT_GET = "/bff-app/v1/co-operation/suspension/states"

        //查询单个item接口定义
        const val URL_OFFICIAL_CO_PROJECT_ITEM = "bff-app/v1/co-operation/project/details"
        const val URL_OFFICIAL_QA_TRIAL_ITEM =
            "/bff-app/v1/community/user-profile/activities/qa-trial/details"
        /**官方主页api -- end*/

        /**个人主页api -- star*/
        const val URL_USER_CO_ALL =
            "/bff-app/v1/community/user-profile/co-operations/space"//个人主页-共创空间
        const val URL_USER_CO_OPERATIONS =
            "/bff-app/v1/community/user-profile/co-operations"//个人主页-共创项目
        const val URL_USER_CO_BIRD =
            "/bff-app/v1/community/user-profile/activities/crowdfunds"//个人主页-共创新品早鸟
        const val URL_USER_CO_TRIAL =
            "/bff-app/v1/community/user-profile/activities/trial"//个人主页-共创新品试用
        const val URL_USER_CO_QA = "/bff-app/v1/community/user-profile/activities/qas"//个人主页-共创问卷调研
        /**个人主页api -- end*/

        /**关注tab api -- star*/
        const val FOLLOWING_USER_PAGE_POSTS: String =
            "/bff-app/v1/community/space/friend/postings"//关注--帖子
        const val FETCH_SUBSCRIPTION_FEATUREDS =
            "/bff-app/v1/users/subscription/featureds"// 关注-精选列表
        const val FOLLOWING_USER_PAGE = "/appco/v2/users/subscription"//关注用户列表
        const val MY_ZONE_RED_DOTS: String = "/bff-app/v1/community/space/red-dots"//红点数据
        const val FOLLOWING_PICTURE: String = "/appco/v1/users/subscription/pictures"//图片列表
        const val FOLLOWING_REFER_USERS: String =
            "/bff-app/v1/community/subscription/refer-users"//用户推荐
        /**关注tab api -- end*/

        /**社区搜索api -- star*/
        const val URL_COMMUNITY_SEARCH_HOMEPAGE = "/bff-app/v1/community/search-home"
        const val URL_CIRCLE_SEARCH_HOMEPAGE = "/bff-app/v1/community/circle/search-home"
        const val URL_KEYWORD_SEARCH_ALL = "/bff-app/v1/community/portal/search/all"/*搜索全部*/
        const val URL_KEYWORD_SEARCH_POST = "/bff-app/v1/community/portal/posting/search"/*帖子搜索*/
        const val URL_KEYWORD_SEARCH_USER = "/bff-app/v1/community/portal/search/user"/*搜索用户*/
        const val URL_KEYWORD_SEARCH_TOPIC = "/bff-app/v1/community/portal/search/topic"/*搜索话题*/
        const val URL_KEYWORD_ASSOCIATION = "/bff-app/v1/community/portal/search/association"/*联想*/
        const val URL_KEYWORD_SEARCH_VIDEO = "/bff-app/v1/community/portal/search/video"//搜索视频
        /**社区搜索api -- end*/

        /**消息 api -- start*/
        //
        const val URL_MESSAGE_READ = "/bff-app/v1/user/messages/read"

        /**消息 api -- end*/
        /*话题可编辑状态*/
        const val URL_TOPIC_EDIT_STATE = "/bff-app/v1/community/official/topic/edit-state"

        /*话题编辑*/
        const val URL_TOPIC_EDIT = "/bff-app/v1/community/official/topic"
    }

    /**----------------------------基础类相关接口⬇️----------------------------*/

    @POST(URL_POST_VIDEO_LIKE)
    suspend fun restVideoLikeState(@Body videoLikeState: VideoLikeState): BaseStatusResponse<*>

    @GET(URL_POST_LIKE)
    suspend fun likePost(
        @Query("postId") postId: Long,
        @Query("client") client: String?,
        @Query("type") type: Int
    ): BaseStatusResponse<LikeUserResult>

    @POST(URL_POST_VIEWS)
    suspend fun recordPostViewTimes(@Query("postId") postId: Long): BaseStatusResponse<*>

    /**----------------------------发现模块相关接口⬇️----------------------------*/

    @GET(URL_DISCOVERY_HOME)
    suspend fun fetchCommunityDiscovery(
        @Query("postType") postType: Int,//帖子类型 0全部 1帖子 2仅视频 3问答帖子
        @Query("sortType") sortType: Int,//排序类型 0默认、1最新发布、2最新回复、3本周热度
        @Query("pageSize") pageSize: Int //页数
    ): BaseStatusResponse<CommunityDiscovery>


    @GET(URL_DISCOVERY_HOME_PAGE)
    suspend fun fetchCommunityDiscoveryPage(
        @Query("postType") postType: Int,//帖子类型 0全部 1帖子 2仅视频 3问答帖子
        @Query("sortType") sortType: Int,//排序类型 0默认、1最新发布、2最新回复、3本周热度
        @Query("pageSize") pageSize: Int, //页数
        @Query("createTime") createTime: Long?,
        @Query("replyTime") replyTime: Long?,
        @Query("postScore") postScore: Long,
        @Query("evalScore") evalScore: Long,
        @Query("questionScore") questionScore: Long,
        @Query("postId") postId: Long,
        @Query("evalId") evalId: Long,
        @Query("questionId") questionId: Long,
    ): BaseStatusResponse<CommunityPostings>


    @GET(URL_COMMUNITY_GUIDE_LINES)
    suspend fun getGuideLines(): BaseStatusResponse<CommunityGuideLinesBean>

    @GET(URL_COMMUNITY_NEW_USER)
    suspend fun getUserMsg(): BaseStatusResponse<CommunityNewUser>

    /**----------------------------个人主页相关接口⬇️----------------------------*/
    @GET(HOMEPAGE_VIDEO_LIST)
    suspend fun getHomepageVideos(
        @Query("identity") identity: String, @Query("identityType") identityType: Int,
        @Query("limit") limit: Int, @Query("videoId") videoId: Int,
        @Query("sortType") sortType: Int
    ): BaseStatusResponse<ResponseHomepageDiyVideo?>

    @GET(HOMEPAGE_SHARE_DIY_VIDEOS)
    suspend fun getHomepageDiyVideos(
        @Query("lastVideoId") lastVideoId: Long,
        @Query("limit") limit: Int,
        @Query("identity") identity: String,
        @Query("identityType") identityType: Int,
        @Query("sortType") sortType: Int
    ): BaseStatusResponse<ResponseHomepageDiyVideo?>

    /**
     * 关注用户列表_效果分享
     */
    @GET("/appco/v1/users/subscription/videos")
    suspend fun getFollowEffectShare(
        @Query("videoId") videoId: Long,
        @Query("limit") limit: Int,
        @Query("identity") identity: String,
        @Query("identityType") identityType: Int,
        @Query("sortType") sortType: Int
    ): BaseStatusResponse<List<CommunityVideo>?>

    @GET(HOMEPAGE_SHARE_VIDEOS_OTHER)
    suspend fun getHomepageMusicVideosOthers(
        @Query("lastId") lastId: Int,
        @Query("limit") limit: Int,
        @Query("createType") createType: Int,
        @Query("identity") identity: String,
        @Query("identityType") identityType: Int,
        @Query("market") site: String,
        @Query("sortType") sortType: Int,
    ): BaseStatusResponse<ResponseMusicDiyVideo?>

    @GET(HOMEPAGE_SHARE_VIDEOS)
    suspend fun getHomepageMusicVideos(
        @Query("lastId") lastId: Int,
        @Query("limit") limit: Int,
        @Query("createType") createType: Int, //工坊为0，音乐创作为1
        @Query("sortType") sortType: Int,
        @Query("market") market: String?
    ): BaseStatusResponse<ResponseMusicDiyVideo?>

    @GET(POST_DIY_VIDEOS_PREVIEW)
    suspend fun getVideoPreview(@Query("videoId") videoId: Int): BaseStatusResponse<CommunityVideo?>

//    @POST("/bff-app/v1/workshop/share-diys/complaints")
//    fun complaintWorkShop(
//        @Body
//        request: Request4ComplaintWorkShop
//    ): Call<Response4ComplaintWorkShop>

    @GET("/bff-app/v1/workshop/share-diy/videos")
    suspend fun getPreviewWorkshopVideoInfo(
        @Query("shareDiyId") shareDiyId: Int,
        @Query("market") market: String
    ): BaseStatusResponse<WorkShop?>


    @GET(SCROLL_VIDEO_POSTS)
    suspend fun scrollVideoPosts(
        @Query("sortType") sortType: Int,
        @Query("videoId") videoId: Int,
        @Query("scrollType") scrollType: Int = 0,
        @Query("limit") limit: Int = 20,
        @Query("hotScore") hotScore: Int,
        @Query("replyTime") replyTime: Long
    ): BaseStatusResponse<ScrollVideoBean>

    @GET("/bff-app/v1/music-create/share/plays")
    suspend fun playMusicVideos(
        @Query("shareId") shareId: Int
    ): BaseStatusResponse<*>

    @GET("/bff-app/v1/mall/listing-url")
    suspend fun getNewRelatedProduct(
        @Query("sku") sku: String,
        @Query("market") market: String
    ): BaseStatusResponse<ProductResponse>

    //删除音乐共创视频
    @DELETE("/bff-app/v1/music-create/effect/shares")
    fun deleteSharedMusicCreate(
        @Query("musicShareId")
        shareDiyId: Int
    ): BaseStatusResponse<*>

    //删除工坊视频
    @DELETE("/bff-app/v1/workshop/diys/share")
    fun deleteSharedWorkShop(
        @Query("shareDiyId")
        shareDiyId: Int
    ): BaseStatusResponse<*>

    //个人主页共创接口，服务器业务区分，故跟官方主页共创接口分离
    @GET(URL_USER_CO_ALL)
    suspend fun getUserCOAll(
        @Query("startTime") startTime: Long,
        @Query("endTime") endTime: Long,
        @Query("pageSize") pageSize: Int,
        @Query("identity") identity: String,
        @Query("identityType") identityType: Int,
        @Query("market") market: String
    ): BaseStatusResponse<OfficialCOAllRsp>

    @GET(URL_USER_CO_OPERATIONS)
    suspend fun getUserOperations(
        @Query("startTime") startTime: Long,
        @Query("endTime") endTime: Long,
        @Query("pageSize") pageSize: Int,
        @Query("identity") identity: String,
        @Query("identityType") identityType: Int
    ): BaseStatusResponse<OfficialOperationRsp>

    @GET(URL_USER_CO_BIRD)
    suspend fun getUserFirstBirds(
        @Query("startTime") startTime: Long,
        @Query("endTime") endTime: Long,
        @Query("pageSize") pageSize: Int,
        @Query("identity") identity: String,
        @Query("identityType") identityType: Int,
        @Query("market") market: String
    ): BaseStatusResponse<OfficialFirstBirdRsp>

    @GET(URL_USER_CO_TRIAL)
    suspend fun getUserApplications(
        @Query("startTime") startTime: Long,
        @Query("endTime") endTime: Long,
        @Query("pageSize") pageSize: Int,
        @Query("identity") identity: String,
        @Query("identityType") identityType: Int
    ): BaseStatusResponse<OfficialApplicationRsp>

    @GET(URL_USER_CO_QA)
    suspend fun getUserQAs(
        @Query("startTime") startTime: Long,
        @Query("endTime") endTime: Long,
        @Query("pageSize") pageSize: Int,
        @Query("identity") identity: String,
        @Query("identityType") identityType: Int
    ): BaseStatusResponse<OfficialQaRsp>

    /**----------------------------官方主页相关接口⬇️----------------------------*/
    @GET(URL_OFFICIAL_DETAILS)
    suspend fun getOfficialDetails(
        @Query("identity") identity: String,
        @Query("identityType") identityType: Int
    ): BaseStatusResponse<OfficialDetail>

    @GET(URL_OFFICIAL_ACTIVITIES)
    suspend fun getOfficialActivities(
        @Query("type") type: Int,
        @Query("startTime") startTime: Long,
        @Query("pageSize") pageSize: Int,
        @Query("endTime") endTime: Long
    ): BaseStatusResponse<OfficialDetailActivity>

    @GET(URL_OFFICIAL_CO_ALL)
    suspend fun getOfficialCOAll(
        @Query("startTime") startTime: Long,
        @Query("endTime") endTime: Long,
        @Query("pageSize") pageSize: Int,
        @Query("market") market: String
    ): BaseStatusResponse<OfficialCOAllRsp>

    @GET(URL_OFFICIAL_POSTINGS)
    suspend fun getOfficialPostings(
        @Query("postId") postId: Long,
        @Query("pageSize") pageSize: Int,
    ): BaseStatusResponse<CommunityPostings>

    @GET(URL_OFFICIAL_CO_OPERATIONS)
    suspend fun getOfficialOperations(
        @Query("startTime") startTime: Long,
        @Query("endTime") endTime: Long,
        @Query("pageSize") pageSize: Int
    ): BaseStatusResponse<OfficialOperationRsp>

    @GET(URL_OFFICIAL_CO_BIRD)
    suspend fun getOfficialFirstBirds(
        @Query("startTime") startTime: Long,
        @Query("endTime") endTime: Long,
        @Query("pageSize") pageSize: Int,
        @Query("market") market: String
    ): BaseStatusResponse<OfficialFirstBirdRsp>

    @GET(URL_OFFICIAL_CO_TRIAL)
    suspend fun getOfficialApplications(
        @Query("startTime") startTime: Long,
        @Query("endTime") endTime: Long,
        @Query("pageSize") pageSize: Int
    ): BaseStatusResponse<OfficialApplicationRsp>

    @GET(URL_OFFICIAL_CO_QA)
    suspend fun getOfficialQAs(
        @Query("startTime") startTime: Long,
        @Query("endTime") endTime: Long,
        @Query("pageSize") pageSize: Int
    ): BaseStatusResponse<OfficialQaRsp>

    @GET(URL_OFFICIAL_CO_FLOAT_GET)
    suspend fun getOfficialFloatState(): BaseStatusResponse<OfficialOperationFloatRsp>

    @PUT(URL_OFFICIAL_CO_FLOAT_SET)
    suspend fun setOfficialFloatState(@Body officialOperationFloat: OfficialOperationFloat): BaseStatusResponse<*>

    @GET(URL_OFFICIAL_CO_PROJECT_ITEM)
    suspend fun getCoProjectItem(
        @Query("basicInfoId") basicInfoId: Int,
    ): BaseStatusResponse<OfficialOperation>

    @GET(URL_OFFICIAL_QA_TRIAL_ITEM)
    suspend fun getTrialQaItem(
        @Query("id") id: Int,
        @Query("type") type: Int
    ): BaseStatusResponse<OfficialTrialQaItemRsp>


    /**----------------------------官方主页相关接口⬆️----------------------------*/

    /**----------------------------社区搜索相关接口⬇️----------------------------*/
    @GET(URL_COMMUNITY_SEARCH_HOMEPAGE)
    suspend fun getCommunitySearchHomePage(): BaseStatusResponse<CommunitySearchHomeBean>

    @GET(URL_CIRCLE_SEARCH_HOMEPAGE)
    suspend fun getCircleSearchHomePage(
        @Query("circleId") circleId: Int
    ): BaseStatusResponse<CirCleSearchHomeBean>

    @GET(URL_KEYWORD_SEARCH_ALL)
    suspend fun keywordSearchAll(
        @Query("searchStr") searchStr: String,
        @Query("lastPostId") lastPostId: Int,
        @Query("sorts") sorts: MutableList<String>,
        @Query("sort") sort: Int,
        @Query("time") time: Int,
        @Query("source") source: Int,
        @Query("type") type: Int,
        @Query("circleId") circleId: Int?
    ): BaseStatusResponse<Data4SearchAll>

    @GET(URL_KEYWORD_SEARCH_POST)
    suspend fun keywordSearchPost(
        @Query("searchStr") searchStr: String,
        @Query("lastPostId") lastPostId: Long,
        @Query("sorts") sorts: MutableList<String>,
        @Query("sort") sort: Int,
        @Query("time") time: Int,
        @Query("source") source: Int,
        @Query("type") type: Int,
        @Query("circleId") circleId: Int?
    ): BaseStatusResponse<CommunitySearchPostings>

    @GET(URL_KEYWORD_SEARCH_USER)
    suspend fun keywordSearchUsers(
        @Query("searchStr") searchStr: String,
        @Query("lastUserId") lastUserId: Int,
        @Query("sorts") sorts: MutableList<String>,
        @Query("sort") sort: Int,
        @Query("time") time: Int,
        @Query("source") source: Int,
        @Query("type") type: Int,
        @Query("circleId") circleId: Int?
    ): BaseStatusResponse<User4SearchList>

    @GET(URL_KEYWORD_SEARCH_TOPIC)
    suspend fun keywordSearchTopics(
        @Query("searchStr") searchStr: String,
        @Query("lastTopicId") lastUserId: Int,
        @Query("sorts") sorts: MutableList<String>,
        @Query("sort") sort: Int,
        @Query("time") time: Int,
        @Query("source") source: Int,
        @Query("type") type: Int,
        @Query("circleId") circleId: Int?
    ): BaseStatusResponse<Topic4SearchList>

    @GET(URL_KEYWORD_SEARCH_VIDEO)
    suspend fun keywordSearchVideo(
        @Query("searchStr") searchStr: String?,
        @Query("lastVideoId") lastVideoId: Int,
        @Query("sorts") sorts: MutableList<String>,
        @Query("sort") sort: Int,
        @Query("time") time: Int,
        @Query("source") source: Int,
        @Query("type") type: Int,
    ): BaseStatusResponse<CommunityVideoSearch?>

    @GET(URL_KEYWORD_ASSOCIATION)
    suspend fun keywordAssociate(
        @Query("searchStr") searchStr: String
    ): BaseStatusResponse<AssociationBean>
    /**----------------------------社区搜索相关接口⬆️----------------------------*/

    /**
     * 关于自己、他人
     */
    @GET("/bff-app/v1/community/user-profile/info")
    suspend fun getAboutInfo(@Query("identity") identity: String): BaseStatusResponse<ResponseAboutBean?>

    // 获取精选颜色列表
    @GET("/bff-app/v1/featured/colors")
    suspend fun fetchColorList(@Query("identity") identity: String): BaseStatusResponse<ResponseFeaturedColors>

    //添加自己的精选颜色
    @PUT("/bff-app/v1/featured/colors")
    suspend fun saveColor(@Body request: RequestFeaturedColorAdd): BaseStatusResponse<Any>

    //获取精选 diy 列表
    @GET("/bff-app/v1/featured/dies")
    suspend fun fetchFeaturedDiyList(
        @Query("identity") identity: String,
        @Query("favorId") favorId: Int,
        @Query("limit") limit: Int
    ): BaseStatusResponse<ResponseFeaturedDiyList?>

    //获取当前用户自己的 diy 列表
    @GET("/bff-app/v1/own/dies")
    suspend fun fetchOwnDiyList(
        @Query("lastDiyId") lastDiyId: Int,
        @Query("limit") limit: Int
    ): BaseStatusResponse<ResponseFeaturedOwnDiyList>

    // 删除精选 diy
    @DELETE("/bff-app/v1/featured/dies")
    suspend fun deleteDiy(@Query("favorId") favorId: Int): BaseStatusResponse<ResponseFeaturedDiyDelete?>

    // 编辑精选 diy
    @PUT("/bff-app/v1/featured/dies")
    suspend fun editDiy(@Body request: RequestFeaturedDiyEdit): BaseStatusResponse<ResponseFeaturedDiyEdit?>

    // 添加精选 diy
    @POST("/bff-app/v1/featured/dies")
    suspend fun addDiy(@Body request: RequestFeaturedDiyAdd): BaseStatusResponse<ResponseFeaturedDiyAdd>


    @PUT(UrlConstant.url_user_profile_device_status)
    suspend fun setHomepageDeviceStatus(@Body devicesStatus: RequestHomepageDevicesStatus): BaseStatusResponse<Any>

    //屏蔽，
    @PUT(UrlConstant.url_user_screens)
    suspend fun setHomepageUserScreens(@Body userScreens: RequestHomepageUserScreens): BaseStatusResponse<ResponseHomepageUserScreens?>


    //上传用户
    @PUT(UrlConstant.replace_cover)
    suspend fun uploadHomepageCover(@Body request: RequestReplaceCoverUrl?): BaseStatusResponse<Any?>

    //关注用户
    @POST(UrlConstant.following_user)
    suspend fun followingUser(@Body requestFollowingUser: RequestFollowingUser?): BaseStatusResponse<Any?>

    /**
     * 获取关注我的，我关注的列表
     */
    @GET(UrlConstant.GET_FOLLOWER_LIST)
    suspend fun getFollowerList(
        @Query("identity") identity: String?,
        @Query("identityType") identityType: Int,
        @Query("followedId") followedId: Int,
        @Query("userId") userId: Int,
        @Query("updateTime") updateTime: Long,
        @Query("type") type: Int,
        @Query("limit") limit: Int
    ): BaseStatusResponse<ResponseFollowerList?>

    /**
     *
     */
    @POST("/bff-app/v1/featured/dies/likes")
    suspend fun uploadDiyLikes(@Body request: RequestFeaturedDiyLikes): BaseStatusResponse<ResponseFeaturedDiyLikes?>

    @POST("/bff-app/v1/featured/dies/collections")
    suspend fun uploadDiyCollections(
        @Body
        request: RequestFeaturedDiyCollections
    ): BaseStatusResponse<ResponseFeaturedDiyCollections?>

    //删除其他视频
    @DELETE(DELETE_VIDEOS)
    suspend fun deleteVideo(
        @Query("videoId")
        videoId: Int
    ): BaseStatusResponse<*>

    @POST(VIDEOS_VIEW_TIMES)
    suspend fun recordVideoViewTimes(@Body request: RequestVideoPlayTimes): BaseStatusResponse<*>

    @POST(VIDEOS_VIEW_SHARES)
    suspend fun recordVideoShareTimes(@Body request: RequestVideoPlayTimes): BaseStatusResponse<*>

    //大使入口
    @GET(ENVOY_ENTRANCES)
    suspend fun getHomePageEnvoy(@Query("identity") identity: String): BaseStatusResponse<HomePageEnvoy?>

    /**----------------------------圈子相关接口⬇️----------------------------*/
    @GET(URL_CIRCLE_INVITE_ANSWER)
    suspend fun fetchCircleInviteAnswer(
        @Query("pageSize") pageSize: Int,
        @Query("questionId") questionId: Int,
        @Query("circleId") circleId: Int
    ): BaseStatusResponse<CircleInviteAnswer>

    @GET(URL_CIRCLE_POSTINGS)
    suspend fun fetchCirclePostings(
        @Query("circleId") circleId: Int?,//圈子id(-1查所有)
        @Query("topicId") topicId: Int?,//话题id(-1查所有)
        @Query("sortType") sortType: Int,//0默认、1最新发布、2最新回复、3本周热度
        @Query("postType") postType: Int,/**/
        @Query("postId") postId: Long?,
        @Query("postScore") postScore: Long,
        @Query("evalScore") evalScore: Long,
        @Query("questionScore") questionScore: Long,
        @Query("evalId") evalId: Long,
        @Query("questionId") questionId: Long,
        @Query("createTime") createTime: Long?,/*传列表最后一个创建时间, 用来做按最新发布排序*/
        @Query("replyTime") replyTime: Long?,/*传列表最后一个回复时间, 用来做按最新回复排序*/
        @Query("pageSize") pageSize: Int = CircleTabViewModel.PAGE_SIZE,/*每页多少数据*/
    ): BaseStatusResponse<CommunityPostings>

    @GET(URL_CIRCLE_HOME)
    suspend fun fetchCommunityCircleHome(
        @Query("pageSize") pageSize: Int,
        @Query("postType") postType: Int,
        @Query("sortType") topicId: Int,
        @Query("circleId") circleId: Int
    ): BaseStatusResponse<CommunityCircleAll>

    @GET(URL_TOPIC_LIST)
    suspend fun fetchCircleTopicList(
        @Query("pageSize") pageSize: Int,
        @Query("topicId") topicId: Int,
        @Query("userTopicId") userTopicId: Int,
        @Query("circleId") circleId: Int
    ): BaseStatusResponse<TopicList>

    @GET(URL_CIRCLE_MESSAGE)
    suspend fun fetchCommunityCirclesMore(): BaseStatusResponse<CommunityCircleMore>

    @POST(URL_CIRCLE_FOLLOW)
    suspend fun joinCircle(@Body request: Request4JoinCircle): BaseStatusResponse<Any?>

    @PUT(URL_CIRCLE_FOLLOW)
    suspend fun exitCircle(@Body request: Request4JoinCircle): BaseStatusResponse<Any?>

    @GET(URL_CIRCLE_ROADMAP_DETAIL)
    suspend fun getRoadmapCircleDetail(
        @Query("circleId") circleId: Int,
        @Query("postType") postType: Int,
        @Query("sortType") sortType: Int,
        @Query("pageSize") pageSize: Int = CircleTabViewModel.PAGE_SIZE
    ): BaseStatusResponse<CircleDetail4Roadmap>

    @GET(URL_CIRCLE_DETAILS)
    suspend fun fetchCircleDetails(
        @Query("circleId") circleId: Int,
        @Query("postType") postType: Int,
        @Query("sortType") sortType: Int,
        @Query("pageSize") pageSize: Int = CircleTabViewModel.PAGE_SIZE
    ): BaseStatusResponse<CircleDetails>

//    @GET(url_circle_postings)
//    suspend fun fetchCirclePosting(
//        @Query("sortType") sortType: Int,
//        @Query("postId") postId: Long,
//        @Query("videoId") videoId: Long,
//        @Query("circleId") circleId: Int,
//        @Query("pageNum") pageNum: Int,
//        @Query("postingType") postingType: Int = 1,
//        @Query("limit") limit: Int = 20,
//        @Query("topicId") topicId: Int = -1
//    ): BaseStatusResponse<CommunityPostings>

    @GET(URL_CIRCLE_ROADMAPS_PAGE)
    suspend fun getRoadmapList(
        @Query("circleId") circleId: Int,
        @Query("sort") sort: Int,
        @Query("type") type: Int,
        @Query("limit") limit: Int = CircleTabViewModel.PAGE_SIZE
    ): BaseStatusResponse<RoadmapList>

    @POST(URL_CIRCLE_ROADMAPS_LIKES)
    suspend fun roadmapLikeOp(@Body reuqest: Request4RoadmapLike): BaseStatusResponse<Any?>

    @GET(URL_TOPIC_DETAILS)
    suspend fun fetchTopicDetails(
        @Query("topicId") topicId: Int,
        @Query("lastPostId") lastPostId: Int,
        @Query("pageSize") pageSize: Int,
        @Query("sortType") sortType: Int,
        @Query("postType") postType: Int,
    ): BaseStatusResponse<TopicDetails?>

    @PUT(URL_CIRCLE_MESSAGE_SORT)
    suspend fun saveMyCircles(@Body circleRequest: SaveMyCircleRequest): BaseStatusResponse<Any>


    @POST(URL_CIRCLE_TOPIC_COMPLAINT)
    suspend fun complaintTopic(@Body request: RequestTopicComplaint): BaseStatusResponse<Any?>

    @GET(URL_TOPIC_POSTINGS)
    suspend fun fetchTopicPostings(
        @Query("circleId") circleId: Int?,//圈子id(-1查所有)
        @Query("topicId") topicId: Int?,//话题id(-1查所有)
        @Query("postId") postId: Long?,
        @Query("sortType") sortType: Int,//0默认、1最新发布、2最新回复、3本周热度
        @Query("postType") postType: Int,/**/
        @Query("postScore") postScore: Long,
        @Query("evalScore") evalScore: Long,
        @Query("questionScore") questionScore: Long,
        @Query("evalId") evalId: Long,
        @Query("questionId") questionId: Long,
        @Query("createTime") createTime: Long?,/*传列表最后一个创建时间, 用来做按最新发布排序*/
        @Query("replyTime") replyTime: Long?,/*传列表最后一个回复时间, 用来做按最新回复排序*/
        @Query("pageSize") limit: Int = CircleTabViewModel.PAGE_SIZE,/*每页多少数据*/
    ): BaseStatusResponse<CommunityPostings>

    /**
     * 查看圈子成员列表
     *
     * @param circleId 圈子 ID
     * @param limit
     * @param userId
     * @return
     */
    @GET(URL_COMMUNITY_CIRCLE_USERS)
    suspend fun fetchCircleUsers(
        @Query("circleId") circleId: Int,
        @Query("userId") userId: Int,
        @Query("sort") sort: Int,
        @Query("limit") limit: Int = CircleTabViewModel.PAGE_SIZE,/*每页多少数据*/
    ): BaseStatusResponse<MemberListBean>

    /**----------------------------话题相关接口⬇️----------------------------*/
    //所有圈子，附带某个圈子话题
    @GET(URL_POST_CIRCLE_LIST)
    suspend fun fetchPostCircleList(
        @Query("circleId") circleId: Int = -1,
        @Query("pageSize") pageSize: Int = 100,
    ): BaseStatusResponse<Models.Circles>

    @GET(URL_POST_TOPIC_LIST)
    suspend fun fetchPostTopicList(
        @Query("circleId") circleId: Int? = -1,
        @Query("pageSize") pageSize: Int = 20,
        @Query("createTime") createTime: Long = -1,
        @Query("topicName") topicName: String? = null,//搜索用,
        @Query("discussionNum") discussionNum: Int? = -1,
    ): BaseStatusResponse<Models.Topics>

    @POST(URL_POST_CHECK_TOPIC_STATUS)
    suspend fun checkTopicStatus(
        @Body status: CheckTopicStatus
    ): BaseStatusResponse<Models.TopicStatus>


    /**----------------------------消息相关接口⬇️----------------------------*/

    /**
     * 0-全部消息 1-互动消息 2-系统消息 3-活动消息 4-积分消息  5-商城消息
     */
    @GET("/appco/v1/profile/messages")
    suspend fun fetchMessages(
        @Query("messagesType") messagesType: Int = 0,
        @Query("limit") limit: Int = 0,
        @Query("lastTime") lastTime: Long = -1,
    ): BaseStatusResponse<ResponseMyMessage>


    @DELETE(UrlConstant.my_message)
    suspend fun deleteMessages(@Query("msgIds") msgIds: List<Int>): BaseStatusResponse<Any?>

    @PUT(URL_MESSAGE_READ)
    suspend fun readMessages(@Body messagesType: RequestReadMessage): BaseStatusResponse<Any?>

    /**----------------------------账户相关接口⬇️----------------------------*/


    /**----------------------------其他相关接口⬇️----------------------------*/
    @POST(URL_USER_FOLLOW)
    suspend fun followingUser(@Body followingUser: FollowingUser): BaseStatusResponse<*>

    /**----------------------------关注tab相关接口⬇️----------------------------*/
    @GET(FOLLOWING_USER_PAGE_POSTS)
    suspend fun fetchDynamicPostList(
        @Query("postId") postId: Int,
        @Query("videoId") videoId: Int,
        @Query("limit") limit: Int
    ): BaseStatusResponse<FriendDynamicPost>

    @GET(FETCH_SUBSCRIPTION_FEATUREDS)
    suspend fun fetchSubscriptionFeaturedList(
        @Query("lastTime") lastTime: Long,
        @Query("limit") limit: Int
    ): BaseStatusResponse<FavorsData>

    @GET(FOLLOWING_USER_PAGE)
    suspend fun fetchMyFollowing(
        @Query("identity") identity: String?,
        @Query("identityType") identityType: Int
    ): BaseStatusResponse<FollowUserData>

    @GET(MY_ZONE_RED_DOTS)
    suspend fun getRedDotsData(): BaseStatusResponse<RedDotsData>

    @GET(FOLLOWING_PICTURE)
    suspend fun getFollowingPictures(
        @Query("limit") limit: Int,
        @Query("identity") identity: String?,
        @Query("identityType") identityType: Int,
        @Query("pictureId") pictureId: Int
    ): BaseStatusResponse<FollowPictureData>

    @GET(FOLLOWING_REFER_USERS)
    suspend fun fetchReferUsers(): BaseStatusResponse<FollowUserRecommendData>

    @GET(URL_TOPIC_EDIT_STATE)
    suspend fun fetchEditState(
        @Query("topicId") topicId: Int = -1,
    ): BaseStatusResponse<Any>


    @PUT(URL_TOPIC_EDIT)
    suspend fun editTopic(@Body request: RequestTopicEdit): BaseStatusResponse<Any?>
}