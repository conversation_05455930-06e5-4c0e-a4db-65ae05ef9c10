<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/clActivityTab"
            android:layout_width="wrap_content"
            android:layout_height="30dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/tvComment"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:layout_marginStart="12dp"
                android:background="@drawable/component_btn_style_40"
                android:gravity="center"
                android:text="@string/community_pinglun"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:textSize="@dimen/font_style_40_textSize"
                android:textColor="@color/component_btn_style_40_text_color"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.govee.base2home.custom.View4Hint
                android:id="@+id/view4Hint"
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:layout_margin="2dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/tvComment"
                app:layout_constraintTop_toTopOf="@id/tvComment"
                app:v4hint_color="@color/ui_point_style_2"
                app:v4hint_radius="3dp"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvFollow"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:layout_marginStart="10dp"
                android:background="@drawable/component_btn_style_40"
                android:gravity="center"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:text="@string/community_new_follow"
                android:textSize="@dimen/font_style_40_textSize"
                android:textColor="@color/component_btn_style_40_text_color"
                app:layout_constraintStart_toEndOf="@+id/tvComment"
                app:layout_constraintTop_toTopOf="parent" />

            <com.govee.base2home.custom.View4Hint
                android:id="@+id/view4Hint1"
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:layout_margin="2dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/tvFollow"
                app:layout_constraintTop_toTopOf="@id/tvFollow"
                app:v4hint_color="@color/ui_point_style_2"
                app:v4hint_radius="3dp"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/tvLike"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:layout_marginStart="10dp"
                android:background="@drawable/component_btn_style_40"
                android:gravity="center"
                android:paddingStart="10dp"
                android:paddingEnd="10dp"
                android:text="@string/community_message_type_activity_like_collection"
                android:textSize="@dimen/font_style_40_textSize"
                android:textColor="@color/component_btn_style_40_text_color"
                app:layout_constraintStart_toEndOf="@+id/tvFollow"
                app:layout_constraintTop_toTopOf="parent" />

            <com.govee.base2home.custom.View4Hint
                android:id="@+id/view4Hint2"
                android:layout_width="6dp"
                android:layout_height="6dp"
                android:layout_margin="2dp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@id/tvLike"
                app:layout_constraintTop_toTopOf="@id/tvLike"
                app:v4hint_color="@color/ui_point_style_2"
                app:v4hint_radius="3dp"
                tools:visibility="visible" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.govee.ui.component.refresh.view.RefreshRecyclerView
            android:id="@+id/refreshRecyclerView"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/clActivityTab"
            app:srvEmptyImg="@mipmap/new_testingclub_pics_no_xiaoxi"
            app:srvEmptyText="@string/app_message_empty"
            app:srvEnableRefresh="false" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>