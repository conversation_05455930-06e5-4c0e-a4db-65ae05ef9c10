<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >

    <com.govee.ui.component.refresh.view.GoveeSmartRefreshLayout
        android:id="@+id/refreshLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/ui_bg_color_style_2"
        app:srlEnableLoadMore="false"
        >

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/acContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            >

            <TextView
                android:id="@+id/topFlag"
                android:layout_width="match_parent"
                android:layout_height="1px"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                />

            <View
                android:id="@+id/vTopBg"
                android:layout_width="match_parent"
                android:layout_height="200dp"
                android:background="@color/ui_bg_color_style_9"
                app:layout_constraintTop_toTopOf="parent"
                />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="34dp"
                android:layout_marginTop="30.5dp"
                android:ellipsize="end"
                android:gravity="center"
                android:maxWidth="178dp"
                android:maxLines="1"
                android:textColor="@color/font_style_1_textColor"
                android:textSize="@dimen/font_style_1_textSize"
                android:visibility="invisible"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/topFlag"
                />

            <ImageView
                android:id="@+id/ivBack"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_alignTop="@+id/tvTitle"
                android:layout_alignBottom="@+id/tvTitle"
                android:layout_marginStart="12dp"
                android:background="@drawable/component_bg_public_top_corner"
                android:contentDescription="@null"
                android:padding="5dp"
                android:src="@mipmap/new_home_icon_top_corner_back"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tvTitle"
                />

            <ImageView
                android:id="@+id/ivReport"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_alignBottom="@+id/tvTitle"
                android:layout_marginEnd="12dp"
                android:background="@mipmap/new_my_icon_more"
                android:contentDescription="@null"
                android:padding="5dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/ivBack"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivBack"
                tools:visibility="visible" />

            <ImageView
                android:id="@+id/ivEdit"
                android:layout_width="34dp"
                android:layout_height="34dp"
                android:layout_alignBottom="@+id/tvTitle"
                android:layout_marginEnd="12dp"
                android:background="@mipmap/topic_icon_edit"
                android:contentDescription="@null"
                android:padding="5dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/ivBack"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/ivBack"
                tools:visibility="visible"
                />

            <androidx.coordinatorlayout.widget.CoordinatorLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="10dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle"
                >

                <com.google.android.material.appbar.AppBarLayout
                    android:id="@+id/appBarLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:background="@color/ui_bg_color_style_9"
                    android:orientation="vertical"
                    app:elevation="0dp"
                    >

                    <TextView
                        android:id="@+id/tvTopicTitle"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="15.5dp"
                        android:gravity="center_vertical"
                        android:lineSpacingExtra="5dp"
                        android:minHeight="27dp"
                        android:textColor="@color/font_style_108_textColor"
                        android:textSize="@dimen/font_style_108_textSize"
                        app:layout_scrollFlags="scroll"
                        tools:text="阿里斯顿发啦开的减肥啦开始的法律考试地方"
                        />

                    <TextView
                        android:id="@+id/tvParticipateNum"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="15.5dp"
                        android:layout_marginTop="9dp"
                        android:drawablePadding="4dp"
                        android:textColor="@color/font_style_220_1_textColor"
                        android:textSize="@dimen/font_style_220_1_textSize"
                        android:visibility="gone"
                        app:drawableStartCompat="@mipmap/new_club_icon_people"
                        app:layout_scrollFlags="scroll"
                        tools:text="25264 people 参与"
                        tools:visibility="visible"
                        />

                    <androidx.appcompat.widget.LinearLayoutCompat
                        android:id="@+id/llTopicCreatorContainer"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4.5dp"
                        android:gravity="center_vertical"
                        android:visibility="gone"
                        app:layout_scrollFlags="scroll"
                        tools:visibility="visible"
                        >

                        <TextView
                            android:id="@+id/tvTopicCreatorTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="15.5dp"
                            android:drawablePadding="4dp"
                            android:text="@string/community_topic_creator"
                            android:textColor="@color/font_style_121_textColor"
                            android:textSize="@dimen/font_style_121_textSize"
                            android:visibility="visible"
                            app:layout_scrollFlags="scroll"
                            />

                        <com.govee.base2home.custom.ShapeImageView
                            android:id="@+id/ivTopicCreator"
                            android:layout_width="20dp"
                            android:layout_height="20dp"
                            android:layout_marginStart="5dp"
                            android:contentDescription="@null"
                            app:shapeIv_lb_radius="10dp"
                            app:shapeIv_lt_radius="10dp"
                            app:shapeIv_no_broke="true"
                            app:shapeIv_rb_radius="10dp"
                            app:shapeIv_rt_radius="10dp"
                            app:shapeIv_type="round"
                            tools:visibility="visible"
                            />

                        <TextView
                            android:id="@+id/tvTopicCreator"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="5dp"
                            android:layout_marginEnd="16dp"
                            android:drawablePadding="4dp"
                            android:ellipsize="end"
                            android:lines="1"
                            android:maxLines="1"
                            android:textColor="@color/font_style_121_textColor"
                            android:textSize="@dimen/font_style_121_textSize"
                            android:visibility="visible"
                            app:layout_scrollFlags="scroll"
                            tools:text="话题创建人话题创建人话题创建人话题创建人话题创建人话题创建人话题创建人话题创建人话题创建人话题创建人话题创建人"
                            />
                    </androidx.appcompat.widget.LinearLayoutCompat>

                    <TextView
                        android:id="@+id/tvCircleTag"
                        android:layout_width="wrap_content"
                        android:layout_height="22dp"
                        android:layout_marginStart="15.5dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/component_flag_style_72"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:lines="1"
                        android:maxLines="1"
                        android:paddingEnd="7.5dp"
                        android:textColor="@color/ui_flag_style_72_text_color"
                        android:textSize="@dimen/ui_flag_style_72_textSize"
                        android:visibility="gone"
                        app:drawableStartCompat="@mipmap/club_icon_club_white"
                        app:layout_constrainedWidth="true"
                        app:layout_constraintHorizontal_bias="0"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toBottomOf="@+id/tvContent"
                        app:layout_scrollFlags="scroll"
                        tools:text="dshfoshfsdshfoshfoshfoshfsdsh"
                        tools:visibility="visible"
                        />

                    <TextView
                        android:id="@+id/tvTopicContent"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="15.5dp"
                        android:layout_marginTop="12dp"
                        android:ellipsize="end"
                        android:gravity="top"
                        android:maxLines="2"
                        android:textColor="@color/font_style_230_3_textColor"
                        android:textSize="@dimen/font_style_230_3_textSize"
                        app:layout_scrollFlags="scroll"
                        tools:text="阿里斯顿发啦开始的减肥试地方阿斯顿发送到发送到发送到发"
                        />

                    <ImageView
                        android:id="@+id/ivContentMore"
                        android:layout_width="19dp"
                        android:layout_height="19dp"
                        android:layout_gravity="center_horizontal"
                        android:layout_marginTop="4dp"
                        android:contentDescription="@null"
                        android:scaleType="centerInside"
                        android:src="@mipmap/new_huati_icon_arrow_shrink"
                        android:visibility="gone"
                        app:layout_scrollFlags="scroll"
                        />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="48dp"
                        android:layout_marginTop="10dp"
                        android:background="@color/ui_bg_color_style_2"
                        android:minHeight="48dp"
                        android:tag="sticky"
                        app:layout_scrollFlags="exitUntilCollapsed|enterAlways|enterAlwaysCollapsed"
                        >

                        <TextView
                            android:id="@+id/tvPostType"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_centerVertical="true"
                            android:layout_marginStart="16dp"
                            android:text="@string/b2light_post_all"
                            android:textColor="@color/font_style_24_1_textColor"
                            android:textSize="@dimen/font_style_24_1_textSize"
                            android:textStyle="bold"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:ignore="RelativeOverlap"
                            />

                        <TextView
                            android:id="@+id/tvSortType"
                            android:layout_width="199dp"
                            android:layout_height="wrap_content"
                            android:layout_alignParentEnd="true"
                            android:layout_centerVertical="true"
                            android:layout_marginEnd="16dp"
                            android:drawablePadding="2.5dp"
                            android:gravity="end"
                            android:text="@string/app_default_sort"
                            android:textColor="@color/font_style_62_textColor"
                            android:textSize="@dimen/font_style_62_textSize"
                            app:drawableEndCompat="@mipmap/new_club_icon_arrow_black"
                            app:layout_constraintBottom_toBottomOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            />

                        <View
                            android:layout_width="match_parent"
                            android:layout_height="1dp"
                            android:background="@color/ui_split_line_style_1_1"
                            app:layout_constraintBottom_toBottomOf="parent"
                            />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                </com.google.android.material.appbar.AppBarLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    app:layout_behavior="@string/appbar_scrolling_view_behavior"
                    >

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/postRecyclerView"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/ui_bg_color_style_2"
                        app:srvEmptyImg="@mipmap/new_testingclub_pics_no_huati"
                        app:srvEmptyText="@string/app_topic_no_post"
                        app:srvEnableLoadMore="true"
                        app:srvEnableRefresh="false"
                        />

                    <com.govee.ui.component.View4NetStatus
                        android:id="@+id/emptyContainerRecycle"
                        android:layout_width="match_parent"
                        android:layout_height="match_parent"
                        android:background="@color/ui_bg_color_style_39"
                        android:paddingBottom="80dp"
                        android:visibility="gone"
                        />
                </androidx.constraintlayout.widget.ConstraintLayout>


            </androidx.coordinatorlayout.widget.CoordinatorLayout>

            <ImageView
                android:id="@+id/ivPost"
                android:layout_width="55dp"
                android:layout_height="55dp"
                android:layout_marginEnd="5dp"
                android:layout_marginBottom="90dp"
                android:contentDescription="@null"
                android:src="@drawable/component_btn_community_post"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                tools:visibility="visible"
                />

            <androidx.compose.ui.platform.ComposeView
                android:id="@+id/composeContainer"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
            <com.govee.ui.component.View4NetStatus
                android:id="@+id/netErrorContainer"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_marginTop="10dp"
                android:background="@color/ui_bg_color_style_2"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvTitle"
                app:net4status_had_empty="false"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.govee.ui.component.refresh.view.GoveeSmartRefreshLayout>
</layout>