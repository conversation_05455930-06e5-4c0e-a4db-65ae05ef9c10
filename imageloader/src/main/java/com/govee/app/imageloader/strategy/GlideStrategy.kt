package com.govee.app.imageloader.strategy

import android.app.ActivityManager
import android.content.Context
import android.graphics.Bitmap
import android.graphics.drawable.Drawable
import android.net.Uri
import android.widget.ImageView
import com.bumptech.glide.Glide
import com.bumptech.glide.Priority
import com.bumptech.glide.RequestBuilder
import com.bumptech.glide.RequestManager
import com.bumptech.glide.load.DataSource
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.load.engine.cache.InternalCacheDiskCacheFactory
import com.bumptech.glide.load.resource.bitmap.GranularRoundedCorners
import com.bumptech.glide.load.resource.bitmap.RoundedCorners
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.RequestOptions
import com.bumptech.glide.request.target.Target
import com.govee.app.imageloader.IImageLoader
import com.govee.app.imageloader.config.GImageParams
import com.govee.app.imageloader.config.ImageCacheType
import com.govee.app.imageloader.listener.ImageDownloadListener
import com.govee.app.imageloader.listener.LoadBitmapLister
import com.govee.app.imageloader.listener.LoadListener
import com.govee.app.imageloader.strategy.glide.GlideUtil
import com.govee.app.imageloader.utils.FileUtils
import com.govee.app.imageloader.utils.GImageLog
import kotlinx.coroutines.DelicateCoroutinesApi
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.launch
import java.io.File


/**
 *     author  : sinrow
 *     time    : 2024/8/29
 *     version : 1.0.0
 *     desc    : Glide 加载实现类
 */
internal class GlideStrategy : IImageLoader {

    private var defaultImageCacheType: ImageCacheType = ImageCacheType.NONE// 默认实现全资源缓存策略
    private var placeholder: Int = -1// 占位符
    private var isMemoryCache: Boolean = false // 是否跳过内存缓存

    //private val defaultParams: GImageParams by lazy { makeDefaultGImageParams() }
    private var gImageParams: GImageParams? = null

    override fun init(context: Context?) {
        GImageLog.v("init()")
    }

    override fun configParameter(gImageParams: GImageParams?): IImageLoader {
        this.gImageParams = gImageParams
        return this
    }

    override fun loadImage(
        context: Context?,
        url: String?,
        imageView: ImageView?,
        listener: LoadListener?
    ) {
        isContextOk(context) ?: return
        if (context == null || imageView == null) {
            GImageLog.v("loadImage() context = $context imageView = $imageView")
            return
        }
        Glide.with(context)
            .load(url)
            .handleCommRequestBuilder(context)
            .addListener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>,
                    isFirstResource: Boolean
                ): Boolean {
                    return listener?.loadFail(e?.message ?: "") ?: false
                }

                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    return listener?.loadSuc(resource) ?: false
                }
            }).into(imageView)
    }

    override fun preLoadImage(context: Context?, url: String?) {
        isContextOk(context) ?: return
        if (context == null) return
        val load = Glide.with(context).load(url)
        convertParams()?.let { load.apply(it) }
        load.preload()
    }

    override fun loadImageAssets(context: Context?, assets: String?, imageView: ImageView?) {
        isContextOk(context) ?: return
        if (context == null || imageView == null) return
        Glide.with(context).checkResourceType().load("file:///android_asset/".plus(assets))
            .handleCommRequestBuilder(context).into(imageView)
    }

    override fun loadImage(context: Context?, drawable: Drawable?, imageView: ImageView?) {
        isContextOk(context) ?: return
        if (context == null || imageView == null) return
        Glide.with(context).checkResourceType().load(drawable).handleCommRequestBuilder(context).into(imageView)
    }

    override fun loadImage(context: Context?, bitmap: Bitmap?, imageView: ImageView?) {
        isContextOk(context) ?: return
        if (context == null || imageView == null) return
        Glide.with(context).checkResourceType().load(bitmap).handleCommRequestBuilder(context).into(imageView)
    }

    override fun loadImage(context: Context?, resources: Int, imageView: ImageView?) {
        isContextOk(context) ?: return
        if (context == null || imageView == null) return
        Glide.with(context).checkResourceType().load(resources).handleCommRequestBuilder(context).into(imageView)
    }

    override fun loadImage(context: Context?, file: File?, imageView: ImageView?) {
        isContextOk(context) ?: return
        if (context == null || imageView == null) {
            return
        }
        Glide.with(context).checkResourceType().load(file).handleCommRequestBuilder(context).into(imageView)
    }

    override fun loadImage(context: Context?, uri: Uri?, imageView: ImageView?) {
        isContextOk(context) ?: return
        if (context == null || imageView == null) return
        Glide.with(context).checkResourceType().load(uri).handleCommRequestBuilder(context).into(imageView)
    }

    override fun loadImage(context: Context?, byteArray: ByteArray?, imageView: ImageView?) {
        isContextOk(context) ?: return
        if (context == null || imageView == null) return
        Glide.with(context).checkResourceType().load(byteArray).handleCommRequestBuilder(context).into(imageView)
    }

    override fun loadImageBitmap(context: Context?, model: Any?, listener: LoadBitmapLister?) {
        if (context == null) return
        Glide.with(context)
            .load(model)
            .handleCommRequestBuilder()
            .addListener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>,
                    isFirstResource: Boolean
                ): Boolean {
                    listener?.onLoadFailed(null)
                    return false
                }

                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    listener?.onResourceReady(resource)
                    return false
                }
            })
            .submit()
    }

    override fun asyncDownloadImage(
        context: Context?, url: String?, listener: ImageDownloadListener?
    ) {
        isContextOk(context) ?: return
        context ?: return
        Glide.with(context).downloadOnly().load(url).addListener(object : RequestListener<File> {
            override fun onLoadFailed(
                e: GlideException?,
                model: Any?,
                target: Target<File>,
                isFirstResource: Boolean
            ): Boolean {
                listener?.downloadFail(e?.message ?: "")
                return false
            }

            override fun onResourceReady(
                resource: File,
                model: Any,
                target: Target<File>?,
                dataSource: DataSource,
                isFirstResource: Boolean
            ): Boolean {
                return listener?.downloadSuc(resource) ?: false
            }
        }).preload()
    }

    override fun setMemoryCache(isMemoryCache: Boolean) {
        this.isMemoryCache = isMemoryCache
    }

    override fun setDefaultCacheType(imageCacheType: ImageCacheType) {
        this.defaultImageCacheType = imageCacheType
    }

    override fun setDefaultPlaceholder(placeholder: Int) {
        this.placeholder = placeholder
    }

    @OptIn(DelicateCoroutinesApi::class)
    override fun clearImageDiskCache(context: Context?) {
        //清空磁盘缓存，要求在后台线程中执行
        isContextOk(context) ?: return
        if (context != null) {
            GlobalScope.launch(Dispatchers.IO) {
                Glide.get(context).clearDiskCache()
            }
        }
    }

    @OptIn(DelicateCoroutinesApi::class)
    override fun clearImageMemoryCache(context: Context?) {
        //清空内存缓存，要求在主线程中执行
        isContextOk(context) ?: return
        if (context != null) {
            GlobalScope.launch(Dispatchers.Main) {
                Glide.get(context).clearMemory()
            }
        }
    }

    override fun getDiskCacheSize(context: Context?): Long {
        isContextOk(context) ?: return 0
        context ?: return 0
        try {
            return FileUtils.getFolderSize(File(context.cacheDir.toString() + "/" + InternalCacheDiskCacheFactory.DEFAULT_DISK_CACHE_DIR))
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return 0
    }

    override fun trimMemory(context: Context?, level: Int) {
        isContextOk(context) ?: return
        context?.let {
            Glide.get(it).trimMemory(level)
        }
    }

    override fun clear() {
        gImageParams = null
    }

    override fun loadImageWithHeaders(
        context: Context?,
        url: String?,
        imageView: ImageView?,
        cookieStr: String,
        headers: HashMap<String, String>,
        listener: LoadListener?
    ) {
        isContextOk(context) ?: return
        if (context == null || imageView == null) return
        Glide.with(context)
            .load(GlideUtil.getUrlWithHeaders(url, cookieStr, headers))
            .handleCommRequestBuilder(context)
            .listener(object : RequestListener<Drawable> {
                override fun onLoadFailed(
                    e: GlideException?,
                    model: Any?,
                    target: Target<Drawable>,
                    isFirstResource: Boolean
                ): Boolean {
                    return listener?.loadFail(e?.message ?: "load error,check code") ?: false
                }

                override fun onResourceReady(
                    resource: Drawable,
                    model: Any,
                    target: Target<Drawable>?,
                    dataSource: DataSource,
                    isFirstResource: Boolean
                ): Boolean {
                    return listener?.loadSuc(resource) ?: false
                }

            })
            .into(imageView)
    }

    private fun makeDefaultGImageParams(): GImageParams {
        return GImageParams.build().skipMemoryCache(false).placeholder(placeholder)// 默认加载失败图片
    }

    private fun transformCacheStrategy(type: ImageCacheType = this.defaultImageCacheType): DiskCacheStrategy {
        return when (type) {
            ImageCacheType.ALL -> DiskCacheStrategy.ALL
            ImageCacheType.RESOURCE -> DiskCacheStrategy.RESOURCE
            ImageCacheType.DATA -> DiskCacheStrategy.DATA
            ImageCacheType.AUTOMATIC -> DiskCacheStrategy.AUTOMATIC
            ImageCacheType.NONE -> DiskCacheStrategy.NONE
        }
    }

    private fun convertParams(): RequestOptions? {
        val params = gImageParams ?: return null
        var requestOptions = RequestOptions()
        if (params.roundingRadius > 0) {
            val roundedCorners = RoundedCorners(params.roundingRadius)
            requestOptions = RequestOptions.bitmapTransform(roundedCorners)
        }
        params.roundCorners?.let {
            requestOptions.transform(GranularRoundedCorners(it.left, it.top, it.right, it.bottom))
        }
        if (params.isCenterCrop) {
            requestOptions = requestOptions.centerCrop()
        }
        if (params.isCenterInside) {
            requestOptions = requestOptions.centerInside()
        }
        if (params.isCircleCrop) {
            requestOptions = requestOptions.circleCrop()
        }
        if (params.isCenterCropTransform) {
            requestOptions = requestOptions.centerCrop()
        }
        if (params.isFitCenter) {
            requestOptions = requestOptions.fitCenter()
        }
        if (params.placeholder != -1) {
            requestOptions = requestOptions.placeholder(params.placeholder)
        }
        if (params.placeholderDrawable != null) {
            requestOptions = requestOptions.placeholder(params.placeholderDrawable)
        }
        params.customSize?.let {
            if (it.x > 0 && it.y > 0) {
                requestOptions = requestOptions.override(it.x, it.y)
            }
        }


        gImageParams = null
        return requestOptions
            .diskCacheStrategy(
                transformCacheStrategy(
                    params.imageCacheType ?: this.defaultImageCacheType
                )
            )
            .timeout(params.timeout)
            .skipMemoryCache(params.skipCache)
    }

    private fun getMemoryCacheInfo(context: Context?): Pair<Long, Long>? {
        isContextOk(context) ?: return null
        if (context == null) return null
        val activityManager = context.getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val memoryInfo = ActivityManager.MemoryInfo()
        activityManager.getMemoryInfo(memoryInfo)

        // 获取可用内存和总内存
        val availableMemory = memoryInfo.availMem // 可用内存（字节）
        val totalMemory = memoryInfo.totalMem // 总内存（字节）

        return Pair(availableMemory, totalMemory)
    }

    private fun <T : Any> RequestBuilder<T>.handleCommRequestBuilder(context: Context? = null): RequestBuilder<T> {
        // 转换参数并应用

        getMemoryCacheInfo(context)?.let {
//            val availableMemoryInMB = it.first / (1024 * 1024) // 转换为 MB
//            val totalMemoryInMB = it.second / (1024 * 1024) // 转换为 MB

//            GImageLog.d(TAG) { "可用内存: $availableMemoryInMB MB" }
//            GImageLog.d(TAG) { "总内存: $totalMemoryInMB MB" }
        }
        convertParams()?.let { this.apply(it) }

        // 检查缩略图参数并应用
        gImageParams?.thumbnail?.let { thumbnailValue ->
            if (thumbnailValue in 0f..1f) {
                return this.thumbnail(thumbnailValue)
            }
        }

        gImageParams?.priority?.let { imagePriority ->
            try {
                this.priority(Priority.valueOf(imagePriority.name))
            } catch (e: IllegalArgumentException) {
                // 处理无效的枚举值
                GImageLog.d(TAG) { "Invalid ImagePriority value: ${imagePriority.name}" }
            }
        }
        return this.addListener(object : RequestListener<T> {
            override fun onLoadFailed(
                e: GlideException?,
                model: Any?,
                target: Target<T>,
                isFirstResource: Boolean
            ): Boolean {
                return false
            }

            override fun onResourceReady(
                resource: T,
                model: Any,
                target: Target<T>?,
                dataSource: DataSource,
                isFirstResource: Boolean
            ): Boolean {
//                when (dataSource) {
//                    DataSource.MEMORY_CACHE -> GImageLog.d(TAG) { "Loaded from memory cache" }
//                    DataSource.DATA_DISK_CACHE -> GImageLog.d(TAG) { "Loaded from disk cache" }
//                    DataSource.REMOTE -> GImageLog.d(TAG) { "Loaded from network" }
//                    else -> GImageLog.d(TAG) { "Loaded from unknown source" }
//                }
                return false
            }
        })
    }

    private fun RequestManager.checkResourceType(): RequestBuilder<out Any> {
        gImageParams?.let {
            if (it.isBitmap) {
                return asBitmap()
            }
        }
        return asDrawable()
    }
}