apply from: "../module_build.gradle"
apply plugin: 'org.jetbrains.kotlin.android'
android {
    namespace 'com.govee.ctlchannel'
    compileSdk COMPILE_SDK_VERSION
    defaultConfig {
        if(!isModule) {
            applicationId "com.govee.ctlchannel"
            minSdkVersion MIN_SDK_VERSION
            targetSdkVersion TARGET_SDK_VERSION
        }else{
            minSdkVersion MIN_SDK_VERSION
            targetSdkVersion TARGET_SDK_VERSION
        }
    }
    buildTypes {
        release {
            isDefault = RELEASE_BUILD_VARIANT_IS_DEFAULT
            minifyEnabled false
            zipAlignEnabled false  //压缩优化
            consumerProguardFiles 'proguard-rules.pro'
        }
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }
    lint {
        abortOnError false
    }

}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    api project(':base2light')
    moduleCompileOnly(':GoveeHomeCore:lib_encryption')
    implementation project(':pact:base2newth')
    if(!isModule){
        implementation project(':base2home')
        implementation project(':pact:base_h71xx')
    }
}

tasks.register('clearJar', Delete) {
    delete 'libs/CtllerChannel.jar' //sdk是你自己jar包的名字，随便命名
}
tasks.register('makeJar', Jar) {
    archiveBaseName.set('sdk')  //指定生成的jar名
    //从哪里打包class文件，这个是你module中生成的class文件，如果没有classes文件夹，不妨先运行下你的应用
    from('build/intermediates/classes/debug/com/****/*****')
    //打包到jar后的目录结构,这里建议直接用包名
    into('com/*****/******')
    exclude('test/', 'BuildConfig.class', 'R.class')//去掉不需要打包的目录和文件
    exclude { it.name.startsWith('R') }//去掉R开头的文件
}
makeJar.dependsOn(clearJar, build)