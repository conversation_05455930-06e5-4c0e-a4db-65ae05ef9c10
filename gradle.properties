#请勿修改，用于关联当前gradle文件环境
gradleEnv=config
# Project-wide Gradle settings.
# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.
# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html
# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
#org.gradle.jvmargs=-Xmx2048mp/
org.gradle.daemon=true
#org.gradle.jvmargs=-Xms4096m -Xmx9192m -XX:+UseParallelGC
#org.gradle.jvmargs=-Xms4g -Xmx10g -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#org.gradle.jvmargs=-Xms2g -Xmx8g -XX:MaxPermSize=2g
org.gradle.jvmargs=-Xms1024m -Xmx30g -XX:+UseParallelGC -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8 \
--add-exports=jdk.compiler/com.sun.tools.javac.tree=ALL-UNNAMED \
--add-exports=jdk.compiler/com.sun.tools.javac.code=ALL-UNNAMED \
--add-exports=jdk.compiler/com.sun.tools.javac.util=ALL-UNNAMED \
--add-exports=java.base/sun.nio.ch=ALL-UNNAMED \
--add-opens=java.base/java.lang=ALL-UNNAMED \
--add-opens=java.base/java.lang.reflect=ALL-UNNAMED \
--add-opens=java.base/java.io=ALL-UNNAMED \
--add-exports=jdk.unsupported/sun.misc=ALL-UNNAMED
#org.gradle.jvmargs=-Xmx1024M
# When configured, Gradle will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
android.useAndroidX=true
android.enableJetifier=true
org.gradle.configureondemand=true
#kotlin 增量编译
kotlin.incremental=true
kotlin.incremental.java=true
kotlin.incremental.js=true
#kotlin 编译缓存
kotlin.caching.enabled=true
#kotlin 并行编译
kotlin.parallel.tasks.in.project=true
# 优化kapt
# 并行运行kapt1.2.60版本以上支持
kapt.use.worker.api=true
# 增量编译 kapt1.3.30版本以上支持
kapt.incremental.apt=true
# kapt avoiding 如果用kapt依赖的内容没有变化，会完全重用编译内容，省掉#`app:kaptGenerateStubsDebugKotlin`的时间
kapt.include.compile.classpath=false
#获取编译预警信息的详细信息
#android.debug.obsoleteApi=true
org.gradle.parallel=false
android.injected.testOnly=false
#org.gradle.caching=true
#配置是否需要过滤无需正式环境App引入的lib<p>true:需要过滤；false:无需过滤</p>
noQaLibFilter=false
#是否在非正式环境进行leakcanary的内存泄漏检测
inNoQaLibFilter4LeakCanary=false
#android.disableAutomaticComponentCreation=true
isPlugin=flase
# ????native??
runWithNative=true
android.nonTransitiveRClass=true
#配置是否依赖子项目源码进行编译
subLibDependenciesSource=false
## 快照仓库地址
#SNAPSHOT_REPOSITORY_URL=http://10.162.93.86:8082/repository/maven-snapshots/
## release仓库地址
#RELEASE_REPOSITORY_URL=http://10.162.93.86:8082/repository/maven-releases/
#MAVEN_USERNAME=admin
#MAVEM_PASSWORD=govee123456
# @BindView requires a constant resource ID
android.nonFinalResIds=false
org.gradle.unsafe.configuration-cache-problems=warn
#停用AndroidGradle8以上的R8的fullMode优化
#android.enableR8.fullMode=false
