package com.govee.stringlightv2.adjust.v2;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.govee.base2home.pact.support.Info4BleWifi;
import com.govee.base2home.pact.support.OldRgbBkUtil;
import com.govee.base2light.wlanControl.WlanControlOperator;

import com.govee.stringlightv2.add.NewBKWifiChooseAc;
import com.govee.stringlightv2.pact.Support;
import com.govee.ui.ac.AbsWifiBleSettingAcV1;
import com.govee.ui.component.WlanControlView;
import com.ihoment.base2app.util.JumpUtil;

/**
 * Create by xieyingwu on 2019-12-13
 * 设置Ac$
 */
public class SettingAcV1 extends AbsWifiBleSettingAcV1 {
    private Info4BleWifi info4BleWifi;

    /**
     * 跳转到设置页
     *
     * @param context
     * @param info
     * @param wifiInputLimitSet
     */
    public static void jump2SettingAc(Context context, @NonNull Info4BleWifi info, @NonNull int[] wifiInputLimitSet) {
        boolean supportDeviceLock = Support.supportDeviceLock(info.goodsType, info.sku);
        Bundle jumpBundle = makeAcBundle(supportDeviceLock, info.sku, info.device, info.deviceName, 22, info.wifiMac, info.bleAddress, wifiInputLimitSet[0], wifiInputLimitSet[1], info.versionHard);
        if (jumpBundle == null) return;
        jumpBundle.putParcelable(OldRgbBkUtil.intent_ac_key_info4BleWifi, info);
        JumpUtil.jump(context, SettingAcV1.class, jumpBundle);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        info4BleWifi = intent.getParcelableExtra(OldRgbBkUtil.intent_ac_key_info4BleWifi);
        checkSupportWlanControl();
    }

    @Override
    protected void jump2WifiSettingAc() {
        NewBKWifiChooseAc.jump2WifiChooseAcByConfig(this, info4BleWifi, new int[]{wifiInputLimitSsid, wifiInputLimitPassword});
    }

    private void checkSupportWlanControl() {
        WlanControlView wlanControlView = findViewById(com.govee.base2home.R.id.wlan_control);
        if (wlanControlView != null) {
            wlanControlView.checkSupport(new WlanControlOperator(info4BleWifi.goodsType, sku, device, info4BleWifi.topic));
        }
    }
}