package com.govee.stringlightv2.adjust.v1;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;

import com.govee.ble.BleController;
import com.govee.ble.event.BTStatusEvent;
import com.govee.ble.event.EventBleConnect;
import com.govee.stringlightv2.ble.EventChangeLimit;
import com.govee.stringlightv2.ble.EventLimit;
import com.govee.ui.ac.AbsLimitSettingAc4BleAlexa;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JumpUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by xie<PERSON><PERSON> on 2021/7/3
 * 支持限流+ble下alexa设置页$
 */
public class LimitSettingAc4BleAlexa extends AbsLimitSettingAc4BleAlexa {
    private static final String intent_ac_limit_open = "intent_ac_limit_open";
    private static final int what_retry_connect_ble = 100;
    private boolean limitOpen;

    private Handler handler = new Handler(Looper.getMainLooper()) {
        @Override
        public void handleMessage(@NonNull Message msg) {
            super.handleMessage(msg);
            int what = msg.what;
            doWhat(what);
        }
    };

    private void doWhat(int what) {
        if (what == what_retry_connect_ble) {
            if (!BleController.getInstance().isConnected()) {
                EventRetryConnectBle.sendEventRetryConnectBle();
            }
        }
    }

    /**
     * 跳转到限流设置界面
     *
     * @param ac
     * @param sku
     * @param device
     * @param deviceName
     * @param limitOpen
     */
    public static void jump2LimitSettingAc(Activity ac, boolean supportDeviceLock, String sku, String device, String deviceName, String hardVersion, boolean limitOpen) {
        Bundle bundle = makeAcBundle(supportDeviceLock, sku, device, deviceName, 22, hardVersion);
        bundle.putBoolean(intent_ac_limit_open, limitOpen);
        JumpUtil.jumpWithBundle(ac, LimitSettingAc4BleAlexa.class, bundle);
    }

    private boolean tryConnectBle;

    @Override
    protected void parserOtherParams(Intent intent) {
        limitOpen = intent.getBooleanExtra(intent_ac_limit_open, false);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        /*刷新ui*/
        updateUi();
    }

    private void updateUi() {
        boolean enable = BleController.getInstance().isConnected();
        updateLimitEnable(enable, limitOpen);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onBTStatusEvent(BTStatusEvent event) {
        boolean btOpen = event.isBtOpen();
        if (!btOpen) {
            updateLimitEnable(false, limitOpen);
        }
        hideLoading();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onBleConnectEvent(EventBleConnect event) {
        boolean connectSuc = event.connectSuc();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onBleConnectEvent() connectSuc = " + connectSuc);
        }
        if (connectSuc) {
            updateLimitEnable(true, limitOpen);
        } else {
            updateLimitEnable(false, limitOpen);
            /*若需要尝试重新连接蓝牙，则延时2s后请求重新连接*/
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onBleConnectEvent() tryConnectBle = " + tryConnectBle);
            }
            if (tryConnectBle) {
                tryConnectBle = false;
                handler.sendEmptyMessageDelayed(what_retry_connect_ble, 2000);
            }
        }
        hideLoading();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onLimitControllerEvent(EventLimit event) {
        if (event.isResult()) {
            limitOpen = event.openLimit;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onLimitControllerEvent() write = " + event.isWrite());
            }
            if (event.isWrite()) {
                updateLimitEnable(false, limitOpen);
            } else {
                updateUi();
            }
        }
        hideLoading();
    }

    @Override
    protected void toChangeLimitSwitch() {
        /*若用户手动操作限流开关，则标记设备断开后尝试一次连接蓝牙*/
        tryConnectBle = true;
        showLoading();
        EventChangeLimit.sendEventChangeLimit(!limitOpen);
    }
}