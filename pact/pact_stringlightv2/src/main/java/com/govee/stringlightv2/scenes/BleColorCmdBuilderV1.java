package com.govee.stringlightv2.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.ColorModel;
import com.govee.stringlightv2.pact.Comm;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * ble-颜色控制$
 */
public class BleColorCmdBuilderV1 extends AbsBleCmdBuilderV1<ColorModel> {
    @Override
    public IBleCmd createCmd(ColorModel colorModel) {
        return () -> Comm.makeColorController4BleComm(colorModel.sku, colorModel.color).getValue();
    }
}