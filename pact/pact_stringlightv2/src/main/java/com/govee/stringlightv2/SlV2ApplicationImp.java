package com.govee.stringlightv2;

import com.govee.base2home.main.choose.BleProcessorManager;
import com.govee.base2home.scenes.CmdBuilderManager;
import com.govee.base2home.sku.ModelMaker;
import com.govee.lifecycle.annotation.AppLifecycle;
import com.govee.stringlightv2.add.BleBroadcastProcessor;
import com.govee.stringlightv2.add.v1.BleBroadcastProcessorV1;
import com.govee.stringlightv2.adjust.DetailConfig;
import com.govee.stringlightv2.pact.Register4Item;
import com.govee.stringlightv2.pact.SubMaker;
import com.govee.stringlightv2.pact.Support;
import com.govee.stringlightv2.scenes.BleBrightnessBuilderV1;
import com.govee.stringlightv2.scenes.BleColorCmdBuilderV1;
import com.govee.stringlightv2.scenes.BleColorTemCmdBuilderV1;
import com.govee.stringlightv2.scenes.BleHeartCmdBuilderV1;
import com.govee.stringlightv2.scenes.BleSwitchCmdBuilderV1;
import com.govee.stringlightv2.scenes.IotBrightnessCmdBuilderV1;
import com.govee.stringlightv2.scenes.IotColorCmdBuilderV1;
import com.govee.stringlightv2.scenes.IotColorTemCmdBuilderV1;
import com.govee.stringlightv2.scenes.IotSwitchCmdBuilderV1;
import com.ihoment.base2app.IApplication;

/**
 * Create by xieyingwu on 2019-12-10
 * application实现$
 */
@AppLifecycle
public class SlV2ApplicationImp implements IApplication {
    @Override
    public void create() {
        /*item的构建*/
        Register4Item.INSTANCE.register();
        ModelMaker.getInstance().addMaker(new SubMaker());
        /*发现设备后操作逻辑*/
        BleProcessorManager.getInstance().addProcessorFirst(new BleBroadcastProcessor());
        BleProcessorManager.getInstance().addProcessorFirst(new BleBroadcastProcessorV1());
        /*添加默认的支持产品类型sku*/
        Support.addSupportPact();
        /*scenes场景支持*/
        CmdBuilderManager.getInstance().registerBleCmdBuilder(
                new BleBrightnessBuilderV1(),
                new BleColorCmdBuilderV1(),
                new BleColorTemCmdBuilderV1(),
                new BleHeartCmdBuilderV1(),
                new BleSwitchCmdBuilderV1()
        );
        CmdBuilderManager.getInstance().registerCmdBuilder(
                new IotBrightnessCmdBuilderV1(),
                new IotColorCmdBuilderV1(),
                new IotColorTemCmdBuilderV1(),
                new IotSwitchCmdBuilderV1()
        );
        DetailConfig.INSTANCE.addConfig();
    }
}