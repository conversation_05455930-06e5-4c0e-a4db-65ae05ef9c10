package com.govee.stringlightv2.scenes;

import com.govee.base2home.pact.support.NewRgb2TLUtil;
import com.govee.base2home.pact.support.OldRgbBkUtil;
import com.govee.base2home.scenes.builder.BaseCmdModel;
import com.govee.base2home.scenes.builder.BleCmdBuilder;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.stringlightv2.ble.BleComm;
import com.govee.stringlightv2.pact.Support;

import java.util.UUID;

/**
 * Create by xieyingwu on 2019-12-18
 * ble-抽象cmd构造器v1版本$
 */
public abstract class AbsBleCmdBuilderV1<T extends BaseCmdModel> extends BleCmdBuilder<T> {
    @Override
    public boolean needCheckDeviceModel() {
        return true;
    }

    @Override
    public boolean checkSupport(DeviceModel model) {
        if (NewRgb2TLUtil.isNewRgb2TL(model.getSku(), model.pactType, model.pactCode)) return false;
        if (NewRgb2TLUtil.isNewRgb2TL4BleIot(model.getSku(), model.pactType, model.pactCode))
            return false;
        String key = model.getKey();
        int goodsType = model.getGoodsType();
        /*goodsType>0表明是新配置的pactType项目,因H6118是已配置的goodType，因此需要处理goodsType>0时的旧sku判断-仅蓝牙需要支持*/
        if (goodsType > 0) {
            /*支持goodsType*/
            return checkSupportKeys(key) || OldRgbBkUtil.isOldRgbBk(model.getSku(), model.pactType, model.pactCode) || OldRgbBkUtil.isOldRgbBk4BleWifi(model.getSku(), model.pactType, model.pactCode);
        }
        /*判断是否是升级幻彩设备，则采用新项目进行支持*/
        return OldRgbBkUtil.isOldRgbBk(model.getSku(), model.pactType, model.pactCode) || OldRgbBkUtil.isOldRgbBk4BleWifi(model.getSku(), model.pactType, model.pactCode);
    }

    @Override
    public String[] getSupportKeys() {
        return Support.supportBleV1GoodsTypeSet;
    }

    @Override
    public UUID getServiceUUID(String key) {
        return BleComm.serviceUuid;
    }

    @Override
    public UUID getCharacteristicUUID(String key) {
        return BleComm.characteristicUuid;
    }
}