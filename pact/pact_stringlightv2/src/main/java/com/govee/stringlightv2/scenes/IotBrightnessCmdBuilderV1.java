package com.govee.stringlightv2.scenes;

import com.govee.base2home.scenes.BaseCmd;
import com.govee.base2home.scenes.ICmd;
import com.govee.base2home.scenes.builder.model.BrightnessModel;
import com.govee.stringlightv2.pact.Comm;

/**
 * 亮度
 */
public class IotBrightnessCmdBuilderV1 extends AbsIotCmdBuilderV1<BrightnessModel> {
    @Override
    public ICmd createCmd(BrightnessModel brightnessModel) {
        return new BaseCmd() {
            @Override
            public String getIotCmd() {
                return makeCmdStr(Comm.makeBrightnessCmd4IotComm(brightnessModel.model, brightnessModel.brightness));
            }
        };
    }
}