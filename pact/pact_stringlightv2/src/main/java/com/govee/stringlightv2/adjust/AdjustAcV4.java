package com.govee.stringlightv2.adjust;

import android.content.Intent;

import com.govee.base2home.pact.support.Info4BleWifi;
import com.govee.base2home.pact.support.OldRgbBkUtil;
import com.govee.base2light.ble.scenes.ScenesM;
import com.govee.base2light.pact.AbsPactAdjustAc4BleIot;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.IFrame;
import com.govee.base2light.pact.IFrameResult;
import com.govee.stringlightv2.adjust.v2.FrameV4;
import com.govee.stringlightv2.pact.Support;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/7/27
 * 控制页ble+iot-BK$
 * OldRgbBkUtil.jump2RgbBKBleWifiAdjustAc方法跳转至该详情页
 */
public class AdjustAcV4 extends AbsPactAdjustAc4BleIot {
    @NonNull
    @Override
    protected IFrame makeFrame(IFrameResult iFrameResult, BleIotInfo bleIotInfo) {
        return new FrameV4(iFrameResult, bleIotInfo);
    }

    @NonNull
    @Override
    protected BleIotInfo makeInfoFromIntent(Intent intent) {
        Info4BleWifi info4BleWifi = intent.getParcelableExtra(OldRgbBkUtil.intent_ac_key_info4BleWifi);
        if (info4BleWifi != null) {
            BleIotInfo bleIotInfo = new BleIotInfo(info4BleWifi.sku, info4BleWifi.goodsType, info4BleWifi.device, "", info4BleWifi.deviceName, info4BleWifi.bleName, info4BleWifi.bleAddress, info4BleWifi.wifiMac, info4BleWifi.topic, info4BleWifi.versionHard);
            bleIotInfo.versionSoft = info4BleWifi.versionSoft;
            bleIotInfo.wifiHardVersion = info4BleWifi.wifiVersionHard;
            bleIotInfo.wifiSoftVersion = info4BleWifi.wifiVersionSoft;
            return bleIotInfo;
        }
        throw new IllegalArgumentException("intent_ac_key_info4BleWifi is null");
    }

    @Override
    protected int[] getDefHeaderRes() {
        return Support.getDefHeaderRes(info.goodsType, info.sku);
    }

    @Override
    protected String getTag() {
        return "AdjustAcV4-stringLightV2";
    }

    @Override
    protected int getScenesEffectVersion() {
        if (Support.newScenesVersion(info.versionSoft, info.pactCode, info.sku) >= 1) {
            return ScenesM.version_scenes_effect_v1;
        }
        return ScenesM.version_scenes_effect_v0;
    }

    @Override
    protected boolean supportDeviceLock() {
        return Support.supportDeviceLock(info.goodsType, info.sku);
    }

    @Override
    protected int[] supportScenesOp() {
        if (info == null) return null;
        return Support.getSupportScenesOpSet(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard);
    }

    @Override
    protected boolean supportInAcDestoryAnalyticMode() {
        return false;
    }
}