package com.govee.stringlightv2.adjust.v2;

import com.govee.base2home.pact.support.Info4BleWifi;
import com.govee.base2light.pact.AbsFrameBleIot;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.IUi;
import com.govee.base2light.pact.IUiResult4BleIot;
import com.govee.base2light.pact.ble.AbsBlePact;
import com.govee.base2light.pact.ble.IPactResult4Ble;
import com.govee.base2light.pact.iot.AbsIotPact;
import com.govee.base2light.pact.iot.IPactResult4Iot;
import com.govee.stringlightv2.pact.Support;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xie<PERSON>wu on 2019-12-11
 * v2版的框架实现-ble+IOT-BK$
 */
public class FrameV4 extends AbsFrameBleIot {
    public FrameV4(com.govee.base2light.pact.IFrameResult frameResult, BleIotInfo info) {
        super(frameResult, info);
    }

    @Override
    protected List<IUi> getSupportUiList(IUiResult4BleIot iUiResult4BleIot, BleIotInfo bleIotInfo) {
        List<IUi> uiList = new ArrayList<>();
        uiList.add(new UiV6(iUiResult4BleIot, bleIotInfo));
        return uiList;
    }

    @Override
    protected AbsBlePact makeBlePact(IPactResult4Ble iPactResult4Ble) {
        return new BlePactV4(iPactResult4Ble);
    }

    @Override
    protected AbsIotPact makeIotPact(IPactResult4Iot iPactResult4Iot) {
        return new IotPactV6(iPactResult4Iot);
    }

    @Override
    public void toSettingAc(@NonNull AppCompatActivity ac) {
        int[] wifiLimit = Support.getWifiLimit();
        Info4BleWifi info4BleWifi = new Info4BleWifi();
        info4BleWifi.sku = info.sku;
        info4BleWifi.device = info.device;
        info4BleWifi.topic = info.topic;
        info4BleWifi.wifiMac = info.wifiMac;
        info4BleWifi.bleName = info.bleName;
        info4BleWifi.deviceName = info.deviceName;
        info4BleWifi.bleAddress = info.bleAddress;
        info4BleWifi.goodsType = info.goodsType;
        info4BleWifi.versionSoft = info.versionSoft;
        info4BleWifi.versionHard = info.versionHard;
        SettingAcV1.jump2SettingAc(ac, info4BleWifi, wifiLimit);
    }
}