package com.govee.stringlightv2;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-10
 * 常量定义$
 */
public final class Cons {
    private Cons() {
    }

    public static final String intent_ac_ext_info = "intent_ac_ext_info";

    public static final String intent_ac_key_from_type = "intent_ac_key_from_type";
    public static final int from_type_main_item = 1;
    public static final int from_type_add = 2;

    public static final int new_music_version_old_v0 = 0;/*0x0e音乐模式-第一版*/
    public static final int new_music_version_old_v1 = 1;/*0x0e音乐模式-第二版本-扩展支持多包音乐模式效果*/

    public static final int new_music_version_new_v0 = 0;/*0x13音乐模式-第一版*/
    public static final int new_music_version_new_v1 = 1;/*0x13音乐模式-第二版-扩展支持多包音乐模式效果*/

    public static final int multi_new_music_version_noSupport = -1;/*不支持多包音乐模式*/
    public static final int multi_new_music_version_v0 = 0;/*旧协议支持-0x0e-多包音乐模式效果*/
    public static final int multi_new_music_version_v1 = 1;/*新协议支持-0x13-多包音乐模式效果*/


}