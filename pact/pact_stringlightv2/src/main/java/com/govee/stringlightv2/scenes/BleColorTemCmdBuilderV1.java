package com.govee.stringlightv2.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.ColorTempModel;
import com.govee.stringlightv2.pact.Comm;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * ble-色温控制$
 */
public class BleColorTemCmdBuilderV1 extends AbsBleCmdBuilderV1<ColorTempModel> {
    @Override
    public IBleCmd createCmd(ColorTempModel colorTempModel) {
        return () -> Comm.makeColorTemComtroller4BleComm(colorTempModel.sku, colorTempModel.temColor, colorTempModel.colorTemInKelvin).getValue();
    }
}