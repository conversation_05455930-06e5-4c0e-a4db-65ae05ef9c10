package com.govee.stringlightv2.adjust.ui;

import androidx.annotation.NonNull;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.music.OldMusicEffect;
import com.govee.base2light.light.v1.AbsNewMusicFragment;
import com.govee.stringlightv2.ble.BleProtocol;
import com.govee.stringlightv2.ble.Mode;
import com.govee.stringlightv2.ble.SubModeMusicMultiV2;
import com.govee.stringlightv2.pact.Support;
import com.ihoment.base2app.infra.SafeLog;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

/**
 * Create by xieyingwu on 2021/6/7
 * 新音乐模式-V1版本-适配新的sku$
 */
public class MusicFragmentMultiNewV1 extends AbsNewMusicFragment {
    private SubModeMusicMultiV2 subModeMusicV2 = new SubModeMusicMultiV2();
    private List<SubMusicMode> subMusicModeList;

    @Override
    protected void init() {
        super.init();
        /*防止fragment视图还未加载就已传递了模式信息；因此在init完成时也需要刷新ui*/
        updateUi();
    }

    @Override
    protected int getCurSubMusicCode() {
        return subModeMusicV2.getMusicCode();
    }

    @Override
    protected void oldMusicParamsChange(OldMusicEffect oldMusicEffect) {
        SubModeMusicMultiV2 copy = subModeMusicV2.copy();
        copy.oldMusicEffectChange(oldMusicEffect);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
    }

    @Override
    protected int getSensitivity() {
        return subModeMusicV2.getSensitivity();
    }

    @Override
    protected void showOldSubMusicEditDialog(SubMusicMode musicMode) {
        showOldMusicEditV1(musicMode, getSensitivity(), subModeMusicV2.isAutoColor(), subModeMusicV2.getRgb(), subModeMusicV2.isDynamic());
    }

    @NonNull
    @Override
    protected List<SubMusicMode> getSupportMusicModes() {
        if (subMusicModeList == null) {
            subMusicModeList = makeRgbSubMusicModesV1();
        }
        return subMusicModeList;
    }

    @Override
    protected void onSensitivityChange(int sensitivity) {
        SubModeMusicMultiV2 copy = subModeMusicV2.copy();
        copy.setSensitivity(sensitivity);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeMusicMultiV2) {
            boolean checkAnalytic4SubModeUse = ((SubModeMusicMultiV2) subMode).checkAnalytic4SubModeUse(getSku());
            this.subModeMusicV2 = (SubModeMusicMultiV2) subMode;
            boolean newMusicCode = Support.isNewMusicCode(subModeMusicV2.getMusicCode());
            if (!newMusicCode) {
                /*旧音乐模式-需要存储到OldMusicEffect*/
                saveOldMusic(subModeMusicV2.getSensitivity(), subModeMusicV2.isAutoColor(), subModeMusicV2.isDynamic(), subModeMusicV2.getRgb());
            }
            updateUi();
            if (checkAnalytic4SubModeUse) {
                SafeLog.i(TAG, () -> "updateSubMode() analyticSubModeDetail");
                SubMusicMode subMusicMode = querySubMusicModeByMusicCode(getCurSubMusicCode());
                if (subMusicMode != null) {
                    analyticSubModeDetail(ParamFixedValue.mode_use_music_, subMusicMode.getAnalyticSubModeStr());
                }
            }
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

    private void updateUi() {
        if (!isViewInflateOk()) return;
        /*灵敏度*/
        sensitivityUi(subModeMusicV2.getSensitivity());
        /*选中的子音乐模式*/
        subMusicUi();
    }
}