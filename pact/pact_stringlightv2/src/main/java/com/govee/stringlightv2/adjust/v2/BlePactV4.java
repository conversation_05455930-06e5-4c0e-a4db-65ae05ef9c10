package com.govee.stringlightv2.adjust.v2;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.Protocol;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.pact.ble.AbsBlePact;
import com.govee.base2light.pact.ble.IPactResult4Ble;
import com.govee.stringlightv2.ble.Ble;
import com.govee.stringlightv2.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import java.util.List;

import androidx.annotation.NonNull;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2019-12-11
 * v2版本的蓝牙协议$
 */
class BlePactV4 extends AbsBlePact {
    private static final String TAG = "BlePactV4";

    BlePactV4(IPactResult4Ble iPactResult) {
        super(iPactResult);
    }

    @Override
    protected String getTag() {
        return "BlePactV4";
    }

    @NonNull
    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected boolean isSupportProtocol(Protocol protocol) {
        if (protocol == null) return false;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "isSupportProtocol() pactType = " + protocol.pactType + " ; pactCode = " + protocol.pactCode);
        }
        List<Protocol> supportV2Protocols = Support.supportV7Protocols;
        for (Protocol v2Protocol : supportV2Protocols) {
            if (v2Protocol.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        return false;
    }

    @Override
    protected Protocol parseBleBroadcastProtocol(int goodsType, byte[] scanRecord) {
        return GoodsType.parseBleBroadcastPactInfo(goodsType, scanRecord);
    }
}