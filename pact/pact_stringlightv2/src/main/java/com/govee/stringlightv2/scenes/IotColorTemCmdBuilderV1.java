package com.govee.stringlightv2.scenes;

import com.govee.base2home.scenes.BaseCmd;
import com.govee.base2home.scenes.ICmd;
import com.govee.base2home.scenes.builder.model.ColorTempModel;
import com.govee.stringlightv2.pact.Comm;

/**
 * 色温
 */
public class IotColorTemCmdBuilderV1 extends AbsIotCmdBuilderV1<ColorTempModel> {
    @Override
    public ICmd createCmd(ColorTempModel colorTempModel) {
        return new BaseCmd() {
            @Override
            public String getIotCmd() {
                return makeCmdStr(Comm.makeColorTemCmd4IotComm(colorTempModel.sku, colorTempModel.goodsType, colorTempModel.pactType, colorTempModel.pactCode, colorTempModel.temColor, colorTempModel.colorTemInKelvin));
            }
        };
    }
}