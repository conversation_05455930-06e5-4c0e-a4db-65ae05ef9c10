package com.govee.stringlightv2.adjust.v2;

import com.govee.base2home.pact.Protocol;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.IUiResult4BleIot;
import com.govee.stringlightv2.pact.Support;

import java.util.List;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 1/28/21
 * $
 */
class UiV6 extends UiV3 {
    public UiV6(IUiResult4BleIot uiResult, BleIotInfo info) {
        super(uiResult, info);
    }

    @Override
    public boolean isSupportProtocol(int pactType, int pactCode) {
        List<Protocol> supportV2Protocols = Support.supportV7Protocols;
        for (Protocol protocol : supportV2Protocols) {
            if (protocol.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }
}