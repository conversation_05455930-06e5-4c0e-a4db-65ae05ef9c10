package com.govee.stringlightv2.scenes;

import com.govee.base2home.pact.support.NewRgb2TLUtil;
import com.govee.base2home.pact.support.OldRgbBkUtil;
import com.govee.base2home.scenes.builder.BaseCmdModel;
import com.govee.base2home.scenes.builder.CmdBuilder;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.stringlightv2.pact.Support;

/**
 * Create by xie<PERSON><PERSON> on 2019-12-18
 * iot-抽象cmd构造器v1版本$
 */
public abstract class AbsIotCmdBuilderV1<T extends BaseCmdModel> extends CmdBuilder<T> {
    @Override
    public boolean needCheckDeviceModel() {
        return true;
    }

    @Override
    public boolean checkSupport(DeviceModel model) {
        if (NewRgb2TLUtil.isNewRgb2TL4BleIot(model.getSku(), model.pactType, model.pactCode))
            return false;
        String key = model.getKey();
        if (model.getGoodsType() > 0) {
            /*支持goodsType*/
            return checkSupportKeys(key);
        }
        /*判断是否是升级幻彩设备，则采用新项目进行支持*/
        return OldRgbBkUtil.isOldRgbBk4BleWifi(model.getSku(), model.pactType, model.pactCode);
    }

    @Override
    public String[] getSupportKeys() {
        return Support.supportIotV1GoodsTypeSet;
    }
}