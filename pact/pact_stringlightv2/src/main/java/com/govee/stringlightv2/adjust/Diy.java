package com.govee.stringlightv2.adjust;

import com.govee.base2light.ac.diy.DiyM;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v1.EffectCodes;
import com.govee.base2light.ac.diy.v2.DiyOpM;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by x<PERSON>ying<PERSON> on 2019-04-22
 * diy效果信息
 */
public class Diy {
    private Diy() {
    }

    private static DiySupportV1 diyV0;
    private static DiySupportV1 diyV1;

    /**
     * 获取对应的DIY版本
     *
     * @param diyVersion
     * @return
     */
    public static DiySupportV1 getDiy(int diyVersion) {
        if (diyVersion == 1) return getDiyV1();
        return getDiyV0();
    }

    private static DiySupportV1 getDiyV0() {
        if (diyV0 != null) return diyV0;
        diyV0 = makeDiyV0();
        return diyV0;
    }

    private static DiySupportV1 getDiyV1() {
        if (diyV1 != null) return diyV1;
        diyV1 = makeDiyV1();
        return diyV1;
    }

    private static DiySupportV1 makeDiyV0() {
        DiySupportV1 diySupportV1 = new DiySupportV1();
        DiySupportV1.Effect effectFade = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_fade,
                null
        );
        DiySupportV1.Effect effectJump = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jump,
                null
        );
        DiySupportV1.Effect effectTwinkling = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_twinkling,
                null
        );
        DiySupportV1.Effect effectMix = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_mix[0],
                DiyM.EffectCode.diy_effect_code_mix[1]
        );

        /*效果*/
        ArrayList<DiySupportV1.Effect> effects = new ArrayList<>();
        effects.add(effectFade);
        effects.add(effectJump);
        effects.add(effectTwinkling);
        effects.add(effectMix);
        diySupportV1.effects = effects;
        /*混合支持效果*/
        ArrayList<DiySupportV1.Effect> mixEffects = new ArrayList<>();
        mixEffects.add(effectFade);
        mixEffects.add(effectJump);
        mixEffects.add(effectTwinkling);
        diySupportV1.mixEffects = mixEffects;
        diySupportV1.mix4EffectsNum = 4;
        /*颜色数量支持*/
        ArrayList<DiySupportV1.Effect4Color> effect4Colors = new ArrayList<>();
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 8));
        diySupportV1.effect4Colors = effect4Colors;
        /*速度调节*/
        ArrayList<DiySupportV1.Effect4Speed> effect4Speeds = new ArrayList<>();
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 100));
        diySupportV1.effect4Speeds = effect4Speeds;
        /*效果code码*/
        diySupportV1.effectCodes = new EffectCodes(DiyM.getInstance.getFlagCodes(effects), DiyM.getInstance.getFlagCodes(mixEffects));
        return diySupportV1;
    }

    private static DiySupportV1 makeDiyV1() {
        DiySupportV1 diySupportV1 = new DiySupportV1();
        DiySupportV1.Effect effectFade = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_fade,
                null
        );
        DiySupportV1.Effect effectJump = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jump,
                null
        );
        DiySupportV1.Effect effectTwinkling = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_twinkling,
                null
        );
        DiySupportV1.Effect effectMix = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_mix[0],
                DiyM.EffectCode.diy_effect_code_mix[1]
        );

        /*效果*/
        ArrayList<DiySupportV1.Effect> effects = new ArrayList<>();
        effects.add(effectFade);
        effects.add(effectJump);
        effects.add(effectTwinkling);
        effects.add(effectMix);
        diySupportV1.effects = effects;
        /*混合支持效果*/
        ArrayList<DiySupportV1.Effect> mixEffects = new ArrayList<>();
        mixEffects.add(effectFade);
        mixEffects.add(effectJump);
        mixEffects.add(effectTwinkling);
        diySupportV1.mixEffects = mixEffects;
        diySupportV1.mix4EffectsNum = 4;
        /*颜色数量支持*/
        ArrayList<DiySupportV1.Effect4Color> effect4Colors = new ArrayList<>();
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 8));
        diySupportV1.effect4Colors = effect4Colors;
        /*速度调节*/
        ArrayList<DiySupportV1.Effect4Speed> effect4Speeds = new ArrayList<>();
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 100));
        diySupportV1.effect4Speeds = effect4Speeds;
        /*效果code码+支持rgb的抽象*/
        List<Integer> flagCodes = DiyM.getInstance.getFlagCodes(effects, DiyM.EffectCode.diy_effect_code_rgb_from_studio);
        diySupportV1.effectCodes = new EffectCodes(flagCodes, DiyM.getInstance.getFlagCodes(mixEffects));
        return diySupportV1;
    }

    public static String getLastDiyApplyKey(int diyVersion, int goodsType, int icNum, String sku, int diyCode) {
        return DiyOpM.getInstance.getCurChooseDiyValueKey(sku, goodsType, icNum, getDiy(diyVersion), diyCode);
    }

}