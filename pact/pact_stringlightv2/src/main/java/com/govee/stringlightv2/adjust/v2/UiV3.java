package com.govee.stringlightv2.adjust.v2;

import com.govee.ui.R;
import android.app.Activity;
import android.bluetooth.BluetoothDevice;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.custom.LoadingDialog;
import com.govee.base2home.event.EventBleStatus;
import com.govee.base2home.guide.GuideDialog;
import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.ota.OtaType;
import com.govee.base2home.pact.Command4PtReal;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.Constant;
import com.govee.base2light.ac.adjust.EventSleepUpdate;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.EventDiyApply;
import com.govee.base2light.ac.diy.EventDiyApplyResult;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v1.EventDiyEffectOp;
import com.govee.base2light.ac.diy.v2.DiyOpM;
import com.govee.base2light.ac.diy.v2.DiyValue;
import com.govee.base2light.ac.diy.v2.LastDiyConfig;
import com.govee.base2light.ac.diy.v3.AcDiyGroup;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyGroup;
import com.govee.base2light.ac.diy.v3.DiyShortcutManger;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ac.diy.v3.Event2AcDiyGroup;
import com.govee.base2light.ac.diy.v3.EventDiyApply4InModeShowing;
import com.govee.base2light.ac.diy.v3.EventDiyModeShowingChange;
import com.govee.base2light.ac.diy.v3.Util4Diy;
import com.govee.base2light.ac.effect.EffectAc;
import com.govee.base2light.ac.effect.EffectEvent;
import com.govee.base2light.ac.effect.EventEffectSquareOpResult;
import com.govee.base2light.ac.effect.EventScenesEffect;
import com.govee.base2light.ac.timer.NewShowTimerAcV1;
import com.govee.base2light.ac.timer.NewSleepSetEvent;
import com.govee.base2light.ac.timer.NewTimerSetEventV1;
import com.govee.base2light.ac.timer.NewWakeupSetEvent;
import com.govee.base2light.ac.timer.SleepFailEvent;
import com.govee.base2light.ac.timer.SleepSucEvent;
import com.govee.base2light.ac.timer.Timer;
import com.govee.base2light.ac.timer.TimerResultEvent;
import com.govee.base2light.ac.timer.UpdateTimeEvent;
import com.govee.base2light.ac.timer.WakeupFailEvent;
import com.govee.base2light.ac.timer.WakeupSucEvent;
import com.govee.base2light.ac.update.OtaUpdateAcV1;
import com.govee.base2light.ac.update.OtaUpdateAcV2;
import com.govee.base2light.ac.update.OtaUpdateAcV3;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.controller.MultipleDiyControllerV1;
import com.govee.base2light.ble.controller.NewTimerV1;
import com.govee.base2light.ble.controller.NewTimerV1Controller;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.ble.controller.SleepController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.controller.SyncTimeController;
import com.govee.base2light.ble.controller.SyncTimeInfo;
import com.govee.base2light.ble.controller.WakeUpController;
import com.govee.base2light.ble.mic.controller.EventSwitchMicPickUpType;
import com.govee.base2light.ble.mic.controller.MicSetRgbController;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.EventNewMusicOpResult;
import com.govee.base2light.ble.music.EventSetMultiMusicEffect;
import com.govee.base2light.ble.ota.v2.EventOtaPrepareOp;
import com.govee.base2light.ble.ota.v2.OtaFlagV2;
import com.govee.base2light.ble.scenes.Category;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.ble.scenes.EditScenesAc;
import com.govee.base2light.ble.scenes.EventChangeScenes;
import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.iot.ResultBrightness;
import com.govee.base2light.iot.ResultColorWc;
import com.govee.base2light.light.EventServiceScenesFresh;
import com.govee.base2light.light.IScenes;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.light.v1.RealtimeColorChangeEvent;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.EventSceneCheck4BleIot;
import com.govee.base2light.pact.EventSceneCheck4BleIotV1;
import com.govee.base2light.pact.IUi;
import com.govee.base2light.pact.IUiResult4BleIot;
import com.govee.base2light.pact.ble.IBleOpResult;
import com.govee.base2light.pact.iot.IIotOpResultV1;
import com.govee.base2light.ui.mode.ChangeModeEvent;
import com.govee.base2light.util.Util4ColorRealtime;
import com.govee.base2light.util.UtilFlag;
import com.govee.ble.BleController;
import com.govee.home.account.config.AccountConfig;

import com.govee.stringlightv2.adjust.Diy;
import com.govee.stringlightv2.adjust.ui.ModeUiV2;
import com.govee.stringlightv2.ble.Mode;
import com.govee.stringlightv2.ble.ModeController;
import com.govee.stringlightv2.ble.OtaPrepareController;
import com.govee.stringlightv2.ble.ParamsSubMode4NewMusicV2;
import com.govee.stringlightv2.ble.SubModeColor;
import com.govee.stringlightv2.ble.SubModeColor4Ww;
import com.govee.stringlightv2.ble.SubModeColorOldV0;
import com.govee.stringlightv2.ble.SubModeMusic;
import com.govee.stringlightv2.ble.SubModeMusicMultiV2;
import com.govee.stringlightv2.ble.SubModeMusicV2;
import com.govee.stringlightv2.ble.SubModeNewDiy;
import com.govee.stringlightv2.ble.SubModeScenes;
import com.govee.stringlightv2.iot.Cmd;
import com.govee.stringlightv2.iot.CmdBrightness;
import com.govee.stringlightv2.iot.CmdColorWc;
import com.govee.stringlightv2.iot.CmdPtReal;
import com.govee.stringlightv2.iot.CmdStatus;
import com.govee.stringlightv2.iot.CmdStatusV1;
import com.govee.stringlightv2.iot.CmdTurn;
import com.govee.stringlightv2.pact.Support;
import com.govee.ui.Cons;
import com.govee.ui.component.BrightnessUI;
import com.govee.ui.component.EffectUI;
import com.govee.ui.component.NewTimerUI;
import com.govee.ui.dialog.HintDialog1;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.CaughtRunnable;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.ResUtil;
import com.ihoment.base2app.util.ToastUtil;
import com.zhy.android.percent.support.PercentRelativeLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2020/7/27
 * ui$
 */
class UiV3 implements IUi {
    private static final String TAG = "UiV3";
    private static final int ui_type_def = -1;
    private static final int ui_type_normal = 1;
    private static final int ui_type_fail = 2;

    private static final int max_retry_connect_device_times = 2;

    private final IUiResult4BleIot uiResult;
    private final BleIotInfo info;
    private final ExtV2 ext = new ExtV2();

    private final BleOpV3 bleOp;
    private final IotOpV3 iotOp;

    private Activity ac;
    private boolean layoutSuc;/*布局是否已初始化*/
    private boolean destroy;
    private int uiTypeBle = ui_type_def;
    private int uiTypeIot = ui_type_def;
    private int diyCode = -1;
    private int diyTemplateCode = -1;

    private NewTimerUI timerUI;
    private BrightnessUI brightnessUI;
    private EffectUI effectUI;
    private AbsMode4UIV1 modeUI;
    private final List<DiyGroup> curDiyGroups = new ArrayList<>();
    private final Handler handler = new Handler(Looper.getMainLooper());
    //是否是切换拾音方式
    private boolean isSwitchMicPickUpType;

    private int retryConnectDeviceTimes;/*尝试连接设备次数*/

    public UiV3(IUiResult4BleIot uiResult, BleIotInfo info) {
        this.uiResult = uiResult;
        this.info = info;
        bleOp = new BleOpV3(info, ext);
        iotOp = new IotOpV3();
    }

    private final IBleOpResult bleOpResult = new IBleOpResult() {
        @Override
        public void onOffChange() {
            showLoading();
        }

        @Override
        public void infoOver() {
            uiTypeBle = ui_type_normal;
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
        }

        @Override
        public void noConnect() {
            uiTypeBle = ui_type_fail;
            /*蓝牙断开，需要检测iot是否可用*/
            bleUnable2CheckIot();
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
        }

        @Override
        public void bleWrite(byte proCommandType, boolean result) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "bleWrite() proCommandType = " + proCommandType + " ; result = " + result);
            }
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(result ? EventEffectSquareOpResult.result_op_suc : EventEffectSquareOpResult.result_op_fail);
        }
    };

    private void bleUnable2CheckIot() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "bleUnable2CheckIot() uiTypeIot = " + uiTypeIot + " ; retryConnectDeviceTimes = " + retryConnectDeviceTimes);
        }
        if (uiTypeIot != ui_type_normal) {
            if (retryConnectDeviceTimes < max_retry_connect_device_times) {
                retryConnectDeviceTimes++;
                if (iotOp.isOpCommEnable()) {
                    iotOp.readCmd(new CmdStatus());
                } else {
                    uiTypeIot = ui_type_def;
                    iotOp.beOpComm(info.sku, info.device, info.topic);
                }
            }
        }
    }

    private final IIotOpResultV1 iotOpResult = new IIotOpResultV1() {
        @Override
        public void cmdWriteSuc4Pt(AbsCmd absCmd, String cmdJsonStr) {
            if (uiTypeBle == ui_type_normal) return;/*蓝牙通信可用，忽略iot通信信息*/
            if (absCmd instanceof CmdPtReal) {
                parseWritePt((CmdPtReal) absCmd);
            }
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
        }

        @Override
        public void noConnectIot() {
            uiTypeIot = ui_type_fail;
            /*iot断开，检测蓝牙是否可用*/
            iotUnable2CheckBle();
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
        }

        @Override
        public void cmdWriteFail(boolean overtime, AbsCmd absCmd) {
            String cmd = absCmd.getCmd();
            if (Cmd.ptReal.equals(cmd)) {
                CmdPtReal cmdPt = (CmdPtReal) absCmd;
                iotOpPtFail(cmdPt);
            }
            if (overtime) {
                uiTypeIot = ui_type_fail;
                /*iot断开，检测蓝牙是否可用*/
                iotUnable2CheckBle();
            }
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
        }

        @Override
        public void cmdWriteSuc(AbsCmd absCmd) {
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
        }

        @Override
        public void cmdRead(String cmd, String cmdJsonStr) {
            if (uiTypeBle == ui_type_normal) return;/*蓝牙通信可用，忽略iot通信信息*/
            if (Cmd.brightness.equals(cmd)) {
                /*解析亮度*/
                ResultBrightness resultBrightness = JsonUtil.fromJson(cmdJsonStr, ResultBrightness.class);
                if (resultBrightness != null) {
                    ext.brightness = resultBrightness.getBrightness();
                }
            } else if (Cmd.colorwc.equals(cmd)) {
                /*解析冷暖色*/
                ResultColorWc resultColorWc = JsonUtil.fromJson(cmdJsonStr, ResultColorWc.class);
                if (resultColorWc != null) {
                    boolean rgbWw = Support.isRgbWw(info.sku);
                    ISubMode subMode;
                    if (rgbWw) {
                        subMode = SubModeColor4Ww.makeSubModeColor4Ww(resultColorWc);
                    } else {
                        boolean colorTem = resultColorWc.isColorTem();
                        subMode = colorTem ? SubModeColor.beColorTem(resultColorWc.getColorTemInKelvin()) : SubModeColor.beRgb(resultColorWc.getColorRgb());
                    }
                    Mode mode = new Mode();
                    mode.subMode = subMode;
                    info.mode = mode;
                }
            }
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
        }

        @Override
        public void cmdOnline(String softVersion, String cmdJsonStr,String wifiSoftVersion) {
            if (!TextUtils.isEmpty(wifiSoftVersion)){
                ext.wifiSoftVersion=wifiSoftVersion;
            }
            CmdStatusV1 statusV1 = CmdStatusV1.parseJson(softVersion, cmdJsonStr);
            uiTypeIot = statusV1 == null ? ui_type_fail : ui_type_normal;
            if (uiTypeBle == ui_type_normal) return;/*蓝牙通信可用，忽略iot通信信息*/
            if (statusV1 == null) {
                /*iot断开，检测蓝牙是否可用*/
                iotUnable2CheckBle();
            } else {
                info.open = statusV1.on;
                ext.brightness = statusV1.brightness;

                info.versionSoft = statusV1.softVersion;
                Mode mode = statusV1.mode;
                /*检测音乐模式版本*/
                checkMusicModeVersion(mode);
                info.mode = mode;

                ext.timer1 = statusV1.timer1;
                ext.timer2 = statusV1.timer2;
                ext.timer3 = statusV1.timer3;
                ext.timer4 = statusV1.timer4;
                ext.wakeUpInfo = statusV1.wakeUpInfo;
                ext.sleepInfo = statusV1.sleepInfo;
                /*通知事件*/
                TimerResultEvent.sendTimerResultEvent(false, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
                SleepSucEvent.sendSleepSucEvent(false, ext.sleepInfo);
                WakeupSucEvent.sendWakeUpSucEvent(false, ext.wakeUpInfo);
                EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
            }
            checkUi();
        }
    };

    private void checkMusicModeVersion(Mode mode) {
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof ParamsSubMode4NewMusicV2) {
            int newMusicModeVersion4Ble = Support.getBk4MultiMusicVersion(info.versionSoft, info.versionHard);
            mode.subMode = ((ParamsSubMode4NewMusicV2) subMode).toSupportSubMode(newMusicModeVersion4Ble);
        }
    }

    private void iotOpPtFail(CmdPtReal cmdPt) {
        Byte opCommandByte = cmdPt.getOpCommandByte();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "iotOpPtFail() opCommandByte = " + opCommandByte);
        }
        if (opCommandByte == null) return;
        if (BleProtocolConstants.SINGLE_MODE == opCommandByte) {
            /*模式操作失败*/
            byte[] opCommandBytes = cmdPt.getOpCommandBytes();
            if (opCommandBytes != null && opCommandBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(opCommandBytes);
                Mode mode = new Mode();
                mode.parse(valid17Bytes);
                ISubMode subMode = mode.subMode;
                if (subMode instanceof SubModeNewDiy) {
                    int diyCode = ((SubModeNewDiy) subMode).getDiyCode();
                    EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
                } else if (subMode instanceof SubModeScenes && diyCode != -1 && diyTemplateCode != -1) {
                    int effect = ((SubModeScenes) subMode).getEffect();
                    if (effect == diyTemplateCode) {
                        EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
                    }
                }
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
        } else if (BleProtocolConstants.SINGLE_WAKEUP == opCommandByte) {
            /*唤醒操作失败*/
            WakeupFailEvent.sendWakeupFailEvent(true);
        } else if (BleProtocolConstants.SINGLE_SLEEP == opCommandByte) {
            /*睡眠操作失败*/
            SleepFailEvent.sendSleepFailEvent(true);
        } else if (BleProtocolConstants.SINGLE_NEW_TIME_V1 == opCommandByte) {
            /*定时操作失败*/
            TimerResultEvent.sendTimerResultEventFail(true);
        }
    }

    private void parseWritePt(CmdPtReal cmdPtReal) {
        Byte opCommandByte = cmdPtReal.getOpCommandByte();
        if (opCommandByte == null) return;
        byte[] opCommandBytes = cmdPtReal.getOpCommandBytes();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parseWritePt() opCommandBytes = " + BleUtil.bytesToHexString(opCommandBytes));
        }
        if (opCommandBytes == null || opCommandBytes.length != 20) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "parseWritePt() ble协议不完整");
            }
            return;
        }
        if (BleProtocolConstants.SINGLE_MODE == opCommandByte) {
            Mode mode = Mode.parseMode(opCommandBytes);
            checkMusicModeVersion(mode);
            info.mode = mode;
            ISubMode subMode = info.mode.subMode;
            if (subMode instanceof SubModeNewDiy) {
                EventDiyApplyResult.sendEventDiyApplyResult(true, ((SubModeNewDiy) subMode).getDiyCode());
            } else if (subMode instanceof SubModeMusicMultiV2) {
                EventNewMusicOpResult.sendEventNewMusicOpResult(true, ((SubModeMusicMultiV2) subMode).getMusicCode());
            } else if (subMode instanceof SubModeScenes && diyCode != -1 && diyTemplateCode != -1) {
                int effect = ((SubModeScenes) subMode).getEffect();
                if (effect == diyTemplateCode) {
                    /*表明当前是diy模版效果应用成功*/
                    mode.subMode = new SubModeNewDiy(diyCode);
                    EventDiyApplyResult.sendEventDiyApplyResult(true, diyCode);
                }
            }
        } else if (BleProtocolConstants.SINGLE_WAKEUP == opCommandByte) {
            /*唤醒操作成功*/
            WakeUpInfo wakeUpInfo = WakeUpController.parse2WakeUp(opCommandBytes);
            if (wakeUpInfo != null) {
                ext.wakeUpInfo = wakeUpInfo;
                WakeupSucEvent.sendWakeUpSucEvent(true, ext.wakeUpInfo);
            }
        } else if (BleProtocolConstants.SINGLE_SLEEP == opCommandByte) {
            /*睡眠操作成功*/
            SleepInfo sleepInfo = SleepController.parseSleep(opCommandBytes);
            if (sleepInfo != null) {
                ext.sleepInfo = sleepInfo;
                SleepSucEvent.sendSleepSucEvent(true, ext.sleepInfo);
            }
        } else if (BleProtocolConstants.SINGLE_NEW_TIME_V1 == opCommandByte) {
            /*定时操作成功*/
            int group = NewTimerV1Controller.parseGroup(opCommandBytes);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "parseWritePt() group = " + group);
            }
            if (group == BleUtil.getUnsignedByte((byte) 0xFF)) {
                /*解析全部定时*/
                List<Timer> timers = NewTimerV1Controller.parseAllTimer(opCommandBytes);
                if (timers != null && !timers.isEmpty()) {
                    int index = 0;
                    for (Timer timer : timers) {
                        if (index == 0) {
                            ext.timer1 = timer;
                        } else if (index == 1) {
                            ext.timer2 = timer;
                        } else if (index == 2) {
                            ext.timer3 = timer;
                        } else if (index == 3) {
                            ext.timer4 = timer;
                        }
                        index++;
                    }
                }
            } else {
                Timer timer = NewTimerV1Controller.parseTimer(opCommandBytes);
                if (timer != null) {
                    if (group == 0) {
                        ext.timer1 = timer;
                    } else if (group == 1) {
                        ext.timer2 = timer;
                    } else if (group == 2) {
                        ext.timer3 = timer;
                    } else if (group == 3) {
                        ext.timer4 = timer;
                    }
                }
            }
            TimerResultEvent.sendTimerResultEvent(true, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
        }
    }

    private void iotUnable2CheckBle() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "iotUnable2CheckBle() uiTypeBle = " + uiTypeBle + " ; retryConnectDeviceTimes = " + retryConnectDeviceTimes);
        }
        if (uiTypeBle != ui_type_normal) {
            if (retryConnectDeviceTimes < max_retry_connect_device_times) {
                retryConnectDeviceTimes++;
                boolean opCommEnable = bleOp.isOpCommEnable();
                if (!opCommEnable) {
                    uiTypeBle = ui_type_def;
                    bleOp.beOpComm(getBluetoothDevice());
                }
            }

        }
    }

    private void registerEvent(boolean register) {
        if (register) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this);
            }
        } else {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        }
    }

    @Override
    public boolean isSupportProtocol(int pactType, int pactCode) {
        List<Protocol> supportV2Protocols = Support.supportV3Protocols;
        for (Protocol protocol : supportV2Protocols) {
            if (protocol.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    @Override
    public void destroy() {
        if (!destroy) {
            destroy = true;
            layoutSuc = false;
            hideLoading();
            registerEvent(false);
            bleOp.destroy();
            iotOp.destroy();
            ac = null;
            if (timerUI != null) timerUI.onDestroy();
            if (brightnessUI != null) brightnessUI.onDestroy();
            if (modeUI != null) modeUI.onDestroy();
            if (effectUI != null) effectUI.onDestroy();
            hideDiyGuide();
        }
    }

    @Override
    public void onOffChange() {
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            showLoading();
            int delay = 0;
            if (modeUI.isMicMode()) {
                delay = 100;
            }
            handler.postDelayed(new CaughtRunnable() {
                @Override
                protected void runSafe() {
                    SwitchController switchController = new SwitchController(!info.open);
                    bleOp.executeOp(switchController);
                }
            }, delay);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            CmdTurn cmd = new CmdTurn(!info.open);
            iotOp.writeCmd(cmd);
        }
    }

    @Override
    public void toUpdateAc(@NonNull AppCompatActivity ac) {
        if (info.canUpdate() && bleOp.isOpCommEnable()) {
            int defSkuRes = ThemeM.getDefSkuRes(info.sku, info.spec);
            boolean bkOta = OtaType.isBKOtaV1(info.versionHard);
            boolean frkOtaV1 = OtaType.isFRKOtaV1(info.versionHard);
            if (bkOta) {
                OtaUpdateAcV2.jump2OtaUpdateAcV2(ac, info.sku, info.deviceName, info.versionSoft, defSkuRes, info.checkVersion);
            } else if (frkOtaV1) {
                OtaUpdateAcV3.jump2OtaUpdateAcV3(ac, info.sku, info.deviceName, info.versionSoft, defSkuRes, info.checkVersion);
            } else {
                OtaUpdateAcV1.jump2OtaUpdateAcV1(ac, info.sku, info.deviceName, info.versionSoft, defSkuRes, info.checkVersion);
            }
        }
    }

    @Override
    public void layout(@NonNull AppCompatActivity ac, @NonNull PercentRelativeLayout contentParent, int headerId) {
        /*注册事件*/
        registerEvent(true);
        checkMicMode();
        this.ac = ac;
        if (!layoutSuc) {
            layoutSuc = true;
            /*初始化ui组件*/
            int[] ids = new int[]{100, 101, 102, 103, 104, 105, 106, 107, 108};

            effectUI = new EffectUI(ac);
            View fucViewEffect = effectUI.getFucView();
            fucViewEffect.setId(ids[4]);
            addViewMargin(contentParent, fucViewEffect, headerId, effectUI.getWidth(), effectUI.getHeight(), AppUtil.getScreenWidth() * 16 / 750);

            /*添加布局-定时*/
            timerUI = new NewTimerUI(ac);
            View fucViewTimer = timerUI.getFucView();
            fucViewTimer.setId(ids[0]);
            addViewMargin(contentParent, fucViewTimer, fucViewEffect.getId(), timerUI.getWidth(), timerUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
            /*添加布局-亮度*/
            brightnessUI = new BrightnessUI(ac, 100, 1, false);
            View fucViewBrightness = brightnessUI.getFucView();
            fucViewBrightness.setId(ids[1]);
            addViewMargin(contentParent, fucViewBrightness, fucViewTimer.getId(), brightnessUI.getWidth(), brightnessUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
            /*添加布局-mode*/
            modeUI = new ModeUiV2(ac, info.sku, info.device, info.goodsType);
            View fucViewMode = modeUI.getFucView();
            fucViewMode.setId(ids[3]);
            addViewMargin(contentParent, fucViewMode, fucViewBrightness.getId(), modeUI.getWidth(), modeUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
        }
        bleOp.setOpResult(bleOpResult);
        iotOp.setOpResult(iotOpResult);
        /*开始进行op通信*/
        startOpComm();
        /*通知刷新DIY模式*/
        EventDiyModeShowingChange.sendEventDiyModeShowingChange();
    }

    private void startOpComm() {
        /*ble通信处理器*/
        boolean opCommEnableBle = bleOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "startOpComm() opCommEnableBle = " + opCommEnableBle);
        }
        if (!opCommEnableBle) {
            uiTypeBle = ui_type_def;
            bleOp.beOpComm(getBluetoothDevice());
        }
        /*iot通信处理器*/
        boolean opCommEnableIot = iotOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "startOpComm() opCommEnableIot = " + opCommEnableIot);
        }
        if (!opCommEnableIot) {
            uiTypeIot = ui_type_def;
            iotOp.beOpComm(info.sku, info.device, info.topic);
        }
        /*刷新ui*/
        checkUi();
    }

    private BluetoothDevice getBluetoothDevice() {
        BluetoothDevice bluetoothDevice = null;
        if (uiResult != null) bluetoothDevice = uiResult.getCurBluetoothDevice();
        if (bluetoothDevice == null) {
            bluetoothDevice = BleController.getInstance().getBluetoothDeviceByAddress(info.bleAddress);
        }
        return bluetoothDevice;
    }

    @Override
    public void uiLost() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "uiLost() layoutSuc = " + layoutSuc + " ; uiTypeIot = " + uiTypeIot + " ; uiTypeBle = " + uiTypeBle);
        }
        if (layoutSuc && (uiTypeIot == ui_type_normal || uiTypeBle == ui_type_normal)) {
            uiTypeBle = ui_type_fail;
            uiTypeIot = ui_type_fail;
            checkUi(false);
        }
        hideDiyGuide();
    }

    private void checkUi() {
        checkUi(true);
    }

    private void checkUi(boolean reportResult) {
        if (destroy) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkUi() uiTypeBle = " + uiTypeBle + " ; uiTypeIot = " + uiTypeIot);
        }
        if (ui_type_normal == uiTypeBle || ui_type_normal == uiTypeIot) {
            /*重置连接次数*/
            retryConnectDeviceTimes = 0;
            /*通信成功*/
            hideLoading();
            timerUI.show();
            info.headerOpType = ui_type_normal == uiTypeBle ? Cons.op_type_ble : Cons.op_type_iot;
            boolean open = info.open;
            effectUI.show();
            if (open) {
                brightnessUI.show();
                brightnessUI.updateBrightness(checkBrightnessEnable(), ext.brightness);
                if (isSwitchMicPickUpType) {
                    AbsMicFragmentV4.saveMicModeByPhone(info.sku, info.device);
                }
                checkMusicModeVersion();
                checkColorMode();
                checkDiyStudio();
                checkDiyMode(info.mode);
                checkMicMode();
                checkDiyGuide();
                analytic4ModeUse(info.mode);
                modeUI.show();
                modeUI.setMode(info.mode);
            } else {
                hideDiyGuide();
                brightnessUI.hide();
                modeUI.hide();
            }
            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_suc);
        } else if (ui_type_fail == uiTypeBle && ui_type_fail == uiTypeIot) {
            /*重置连接次数*/
            retryConnectDeviceTimes = 0;
            /*通信失败*/
            hideLoading();
            timerUI.hide();
            brightnessUI.hide();
            effectUI.hide();
            modeUI.hide();
            hideLoading();
            /*断开连接-重置通信Op*/
            iotOp.destroy();
            bleOp.destroy();
            hideDiyGuide();
            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_fail);
        } else {
            hideLoading();
            /*通信中*/
            timerUI.hide();
            brightnessUI.hide();
            effectUI.hide();
            modeUI.hide();
            hideDiyGuide();
            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_ing);
        }
        isSwitchMicPickUpType = false;
    }

    /*标志位-是否统计了mode的使用;进去一次仅统计一次*/
    private boolean hadAnalyticModeUse = false;

    private void analytic4ModeUse(AbsMode mode) {
        if (hadAnalyticModeUse) return;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode == null) return;
        hadAnalyticModeUse = true;
        subMode.setAnalyticType(ISubMode.ANALYTIC_TYPE_MODE_USE_DETAIL);
    }

    private void checkMusicModeVersion() {
        if (modeUI instanceof ModeUiV2) {
            int bk4MultiMusicVersion = Support.getBk4MultiMusicVersion(info.versionSoft, info.versionHard);
            int musicVersion = bk4MultiMusicVersion == com.govee.stringlightv2.Cons.new_music_version_new_v1 ? 1 : 0;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicModeVersion() bk4MultiMusicVersion = " + bk4MultiMusicVersion + " ; musicVersion = " + musicVersion);
            }
            ((ModeUiV2) modeUI).checkMusicModeVersion4Bk(bk4MultiMusicVersion);
        }
    }

    private void checkColorMode() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        boolean rgbWw = Support.isRgbWw(info.sku);
        if (rgbWw) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeColor) {
                mode.subMode = SubModeColor4Ww.makeSubModeColor4Ww((SubModeColor) subMode);
            }
        }
    }

    private void checkDiyStudio() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenes) {
            int scenesCode = ((SubModeScenes) subMode).getEffect();
            boolean scenes4DiyStudio = ScenesOp.isScenes4DiyStudio(scenesCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkDiyStudio() scenesCode = " + scenesCode + " ; scenes4DiyStudio = " + scenes4DiyStudio);
            }
            if (scenes4DiyStudio) {
                /*来自于Studio的DIY-切换成DIY模式*/
                int diyCode = DiyOpM.getInstance.getDiyCode4DiyTemplate(info.sku, scenesCode);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "checkDiyStudio() diyCode = " + diyCode);
                }
                mode.subMode = new SubModeNewDiy(diyCode);
            }
        }
    }

    /**
     * 场景模式下-日出日落不支持亮度调节
     *
     * @return
     */
    private boolean checkBrightnessEnable() {
        AbsMode mode = info.mode;
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeScenes) {
                int effect = ((SubModeScenes) subMode).getEffect();
                return Support.checkScenesModeBrightnessEnable(info.goodsType, info.pactType, info.pactCode, info.sku, effect, true);
            }
        }
        /*其他模式都支持调节亮度*/
        return true;
    }

    private int getIotOpType() {
        return getOpType(uiTypeIot);
    }

    private int getBleOpType() {
        return getOpType(uiTypeBle);
    }

    private int getOpType(int step) {
        if (step == ui_type_fail) return IUiResult4BleIot.op_type_fail;
        if (step == ui_type_normal) return IUiResult4BleIot.op_type_suc;
        return IUiResult4BleIot.op_type_ing;
    }

    private void showLoading() {
        LoadingDialog.createDialog(ac, com.ihoment.base2app.R.style.DialogDim, 60 * 1000).setEventKey(TAG).show();
    }

    private void hideLoading() {
        LoadingDialog.hideDialog(TAG);
    }

    private void toast(@StringRes int strRes) {
        ToastUtil.getInstance().toast(strRes);
    }

    private void addViewMargin(PercentRelativeLayout contentParent, View subView, int belowId, int width, int height, int marginTop) {
        PercentRelativeLayout.LayoutParams lp = getLP(width, height);
        lp.addRule(RelativeLayout.BELOW, belowId);
        lp.topMargin = marginTop;
        lp.bottomMargin = 0;
        contentParent.addView(subView, lp);
    }

    private PercentRelativeLayout.LayoutParams getLP(int width, int height) {
        return new PercentRelativeLayout.LayoutParams(width, height);
    }

    private void checkMicMode() {
        String versionSoft = info.versionSoft;
        Support.isSupportMicByPhone(info.sku, info.device, versionSoft, info.goodsType);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onClickNewTimer(NewTimerUI.NewTimerClickEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onClickNewTimer()");
        }
        NewShowTimerAcV1.jump2NewShowTimerAcV1(ac, info, ext.wakeUpInfo, ext.sleepInfo, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onBrightnessEndEvent(BrightnessUI.EventBrightnessClickEvent event) {
        int brightness = event.brightness;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onBrightnessEndEvent() brightness = " + brightness);
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            showLoading();
            BrightnessController controller = new BrightnessController(brightness);
            bleOp.executeOp(controller);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            CmdBrightness cmdBrightness = new CmdBrightness(brightness);
            iotOp.writeCmd(cmdBrightness);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewTimerSetEventV1(NewTimerSetEventV1 event) {
        int group = event.getGroup();
        NewTimerV1 info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewTimerSetEventV1() group = " + group);
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            info.check();
            NewTimerV1Controller controller = new NewTimerV1Controller(group, NewTimerV1.toTimer(info));
            bleOp.executeOp(controller);
        } else if (iotOp.isOpCommEnable()) {
            info.check();
            NewTimerV1Controller controller = new NewTimerV1Controller(group, NewTimerV1.toTimer(info));
            CmdPtReal cmdPt = new CmdPtReal(controller);
            iotOp.writeCmd(cmdPt);
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            TimerResultEvent.sendTimerResultEventFail(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewWakeupSetEvent(NewWakeupSetEvent event) {
        WakeUpInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewWakeupSetEvent() info = " + info.toString());
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            info.check();
            WakeUpController wakeupModeController = new WakeUpController(info.enable, info.endBri, info.wakeHour, info.wakeMin, info.repeat, info.wakeTime);
            bleOp.executeOp(wakeupModeController);
        } else if (iotOp.isOpCommEnable()) {
            info.check();
            WakeUpController wakeupModeController = new WakeUpController(info.enable, info.endBri, info.wakeHour, info.wakeMin, info.repeat, info.wakeTime);
            CmdPtReal cmdPt = new CmdPtReal(wakeupModeController);
            iotOp.writeCmd(cmdPt);
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            WakeupFailEvent.sendWakeupFailEvent(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNewSleepSetEvent(NewSleepSetEvent event) {
        SleepInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewSleepSetEvent() info = " + info.toString());
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            /*若当前是开启睡眠模式，但设备未开启，则提示用户打开灯*/
            if (event.isOpen() && !this.info.open) {
                toast(R.string.b2light_please_open_the_light);
                SleepFailEvent.sendSleepFailEvent(false);
            } else {
                info.check();
                SleepController sleepModeController = new SleepController(info.enable, info.startBri, info.closeTime, info.closeTime);
                bleOp.executeOp(sleepModeController);
            }
        } else if (iotOp.isOpCommEnable()) {
            /*若当前是开启睡眠模式，但设备未开启，则提示用户打开灯*/
            if (event.isOpen() && !this.info.open) {
                toast(R.string.b2light_please_open_the_light);
                SleepFailEvent.sendSleepFailEvent(false);
            } else {
                info.check();
                SleepController sleepModeController = new SleepController(info.enable, info.startBri, info.closeTime, info.closeTime);
                CmdPtReal cmdPt = new CmdPtReal(sleepModeController);
                iotOp.writeCmd(cmdPt);
            }
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            SleepFailEvent.sendSleepFailEvent(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onModeV1Event(Mode mode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onModeV1Event()");
        }
        changeMode(mode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeModeEvent(ChangeModeEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent()");
        }
        checkMicByColorMode(event);
        ISubMode subMode = event.getSubMode();
        boolean change2NewMultiMusicCode = change2NewMultiMusicMode(subMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent() change2NewMultiMusicCode = " + change2NewMultiMusicCode);
        }
        if (change2NewMultiMusicCode) return;
        boolean changeDiyMode = changeDiyMode(subMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent() changeDiyMode = " + changeDiyMode);
        }
        if (changeDiyMode) return;
        ISubMode newSubMode;
        if (Support.newScenesVersion(info.versionSoft, info.pactCode, info.sku) >= 1) {
            newSubMode = Support.checkScenesModeEffect(subMode, info.sku, 1);
        } else {
            newSubMode = checkScenesModeEffect(subMode);
        }
        if (newSubMode != null) {
            subMode = newSubMode;
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        changeMode(mode);
        /*统计场景使用次数*/
        if (subMode instanceof SubModeScenes) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, ParamFixedValue.times);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, "scene_" + ((SubModeScenes) subMode).getEffect());
        } else if (subMode instanceof SubModeColor || subMode instanceof SubModeColorOldV0 || subMode instanceof SubModeColor4Ww) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.color_mode, ParamFixedValue.times);
        } else if (subMode instanceof SubModeMusic || subMode instanceof SubModeMusicV2) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
        }
    }

    private boolean change2NewMultiMusicMode(ISubMode subMode) {
        if (subMode instanceof SubModeMusicMultiV2) {
            int sensitivity = ((SubModeMusicMultiV2) subMode).getSensitivity();
            int musicCode = ((SubModeMusicMultiV2) subMode).getMusicCode();
            boolean newMusicCode = Support.isNewMusicCode(musicCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "change2NewMultiMusicMode() newMusicCode = " + newMusicCode);
            }
            if (newMusicCode) {
                /*若当前是新的音乐模式-则需要先发送新的多包参数*/
                boolean setLocalNewMusicMode = AbsNewMusicEffect.setLocalNewMusicMode(info.sku, info.device, musicCode, sensitivity);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "change2NewMultiMusicMode() setLocalNewMusicMode = " + setLocalNewMusicMode);
                }
                AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
                return true;
            }
        }
        return false;
    }

    /**
     * 离开 通过颜色模式实现的手机拾音模式之前，从新设置灯带的颜色为纯色
     */
    private void checkMicByColorMode(ChangeModeEvent event) {
        if (!bleOp.isOpCommEnable()) {
            return;
        }
        int color = event.getColor();
        if (color != 0) {
            boolean rgbWw = Support.isRgbWw(info.sku);
            Mode mode = new Mode();
            mode.subMode = rgbWw ? SubModeColor4Ww.beRgb(color) : SubModeColor.beRgb(color);
            mode.subMode.saveLocal();
            ISubMode subMode = event.getSubMode();
            if (subMode instanceof SubModeColor || subMode instanceof SubModeColor4Ww) {
                event.setSubMode(mode.subMode);
            }
            ModeController modeController = new ModeController(mode);
            bleOp.executeExtOp(modeController);
        }
    }

    private boolean changeDiyMode(ISubMode subMode) {
        if (subMode instanceof SubModeNewDiy) {
            boolean hadToken = AccountConfig.read().isHadToken();
            String diyValueKey = ((SubModeNewDiy) subMode).getDiyValueKey();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "changeDiyMode() diyValueKey = " + diyValueKey + " ; hadToken = " + hadToken);
            }
            DiyValue diyValue = DiyShortcutManger.queryLastApplyDiy(info.sku, info.goodsType, getDiySupport(), info.ic, diyValueKey, true);
            Util4Diy.toApplyDiy(diyValue);
            return true;
        }
        return false;
    }

    private ISubMode checkScenesModeEffect(ISubMode subMode) {
        if (subMode instanceof SubModeScenes) {
            int effect = ((SubModeScenes) subMode).getEffect();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkScenesMode() effect = " + effect);
            }
            int newScenesVersion = Support.newScenesVersion(info.versionSoft, info.pactCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkScenesMode() newScenesVersion = " + newScenesVersion);
            }
            IScenes iScenes = Support.getScenes(newScenesVersion);
            if (!iScenes.supportEffects().contains(effect)) {
                /*表明当前effect不在支持的效果内，需要设置默认效果*/
                SubModeScenes subModeScenes = new SubModeScenes();
                subModeScenes.setEffect(iScenes.scenesEffectSet()[0]);
                return subModeScenes;
            }
        }
        return null;
    }

    private void changeMode(Mode mode) {
        int newScenesVersion = Support.newScenesVersion(info.versionSoft, info.pactCode);
        if (bleOp.isOpCommEnable()) {
            showLoading();
            AbsMultipleControllerV14Scenes newScenesMode = Support.is2NewScenesMode(info.sku, mode, newScenesVersion);
            if (newScenesMode != null) {
                bleOp.executeMultiOpV1(newScenesMode);
            } else {
                ModeController controller = new ModeController(mode);
                bleOp.executeOp(controller);
            }
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            /*颜色模式*/
            ISubMode subMode = mode.subMode;
            boolean rgbWw = Support.isRgbWw(info.sku);
            if (rgbWw && subMode instanceof SubModeColor) {
                subMode = SubModeColor4Ww.makeSubModeColor4Ww((SubModeColor) subMode);
            }
            if (subMode instanceof SubModeColor) {
                AbsCmd cmd = ((SubModeColor) subMode).beCmdColorWc();
                iotOp.writeCmd(cmd);
            } else if (subMode instanceof SubModeColor4Ww) {
                AbsCmd cmd = ((SubModeColor4Ww) subMode).beCmdColorWc();
                iotOp.writeCmd(cmd);
            } else {
                /*其他模式；透传模式信息即可*/
                AbsMultipleControllerV14Scenes newScenesMode = Support.is2NewScenesMode(info.sku, mode, newScenesVersion);
                if (newScenesMode != null) {
                    CmdPtReal newScenesCmdPt = CmdPtReal.getNewScenesCmdPtReal(newScenesMode);
                    if (newScenesCmdPt == null) return;
                    iotOp.writeCmd(newScenesCmdPt);
                } else {
                    ModeController modeController = new ModeController(mode);
                    CmdPtReal cmdPt = new CmdPtReal(modeController);
                    iotOp.writeCmd(cmdPt);
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeScenes(Category.Scene scene) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeScenes()");
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            int sceneType = scene.sceneType;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onChangeScenes() sceneType = " + sceneType + " ; scene.sceneCode = " + scene.sceneCode);
            }
            if (sceneType == ScenesOp.scene_type_static) {
                /*静态场景效果*/
                SubModeScenes subModeScenes = new SubModeScenes();
                subModeScenes.setEffect(scene.sceneCode);
                Mode mode = new Mode();
                mode.subMode = subModeScenes;
                ModeController controller = new ModeController(mode);
                bleOp.executeOp(controller);
                return;
            }
            if (sceneType == ScenesOp.scene_type_rgb) {
                /*rgb效果*/
                AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgb(scene.sceneCode, scene.sceneEffectStr);
                if (controllerV14Scenes != null) {
                    bleOp.executeMultiOpV1(controllerV14Scenes);
                } else {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.e(TAG, "onChangeScenes() rgb指令解析出错!");
                    }
                    hideLoading();
                }
            }
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            int sceneType = scene.sceneType;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onChangeScenes() sceneType = " + sceneType + " ; scene.sceneCode = " + scene.sceneCode);
            }
            if (sceneType == ScenesOp.scene_type_static) {
                SubModeScenes subModeScenes = new SubModeScenes();
                subModeScenes.setEffect(scene.sceneCode);
                Mode mode = new Mode();
                mode.subMode = subModeScenes;
                ModeController controller = new ModeController(mode);
                CmdPtReal cmdPtReal = new CmdPtReal(controller);
                iotOp.writeCmd(cmdPtReal);
                return;
            }
            if (sceneType == ScenesOp.scene_type_rgb) {
                /*rgb效果*/
                AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgb(scene.sceneCode, scene.sceneEffectStr);
                if (controllerV14Scenes != null) {
                    /*多包场景效果-采用效果包+模式包组合方式*/
                    CmdPtReal cmdPtReal = CmdPtReal.getNewScenesCmdPtReal(controllerV14Scenes);
                    if (cmdPtReal != null) {
                        iotOp.writeCmd(cmdPtReal);
                    }
                } else {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.e(TAG, "onChangeScenes() rgb指令解析出错!");
                    }
                    hideLoading();
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onUpdateTimeEvent(UpdateTimeEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onUpdateTimeEvent()");
        }
        readTimer();
    }

    private void readTimer() {
        /*重新读取定时信息;蓝牙协议优先*/
        if (bleOp.isOpCommEnable()) {
            SyncTimeInfo info = SyncTimeInfo.getCurrentTime();
            AbsSingleController[] controllers = new AbsSingleController[]{
                    new SyncTimeController(info.hour, info.minute, info.second, info.week),
                    new NewTimerV1Controller(0xFF),
                    new WakeUpController(),
                    new SleepController(),
            };
            bleOp.executeOp(controllers);
        } else if (iotOp.isOpCommEnable()) {
            iotOp.readCmd(new CmdStatus());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSleepUpdate(EventSleepUpdate event) {
        SleepInfo sleepInfo = event.getInfo();
        sleepInfo.check();
        ext.sleepInfo = sleepInfo;
        readTimer();
    }

    /**
     * diy效果发生改变操作
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyEffectOp(EventDiyEffectOp event) {
        int opType = event.opType;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyEffectOp() opType = " + opType);
        }
        checkDiyMode(info.mode);
    }

    /**
     * 注册diy的输出
     *
     * @param event event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApply(EventDiyApply event) {
        boolean applyDiyV0 = applyDiyV0(event.getDiyProtocol());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApply() applyDiyV0 = " + applyDiyV0);
        }
        if (!applyDiyV0) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyStudio(DiyStudio diyStudio) {
        boolean applyDiyV4 = applyDiyV4(diyStudio);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyStudio() applyDiyV4 = " + applyDiyV4);
        }
        if (!applyDiyV4) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyAi(DiyAi diyAi) {
        boolean applyDiyV5 = applyDiyV5(diyAi);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyAi() applyDiyV5 = " + applyDiyV5);
        }
        if (!applyDiyV5) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEffectClickEvent(EffectUI.EffectClickEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEffectClickEvent()");
        }

        boolean supportScenesLib = Support.newScenesVersion(info.versionSoft, info.pactCode, info.sku) >= 1;
        int[] protocolSet = null;
        if (supportScenesLib) {
            protocolSet = Support.getSupportScenesOpSet(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard);
        }
        EffectAc.jump2EffectAc(ac, info.goodsType, info.sku, info.device, protocolSet, true, true, supportScenesLib, info.versionSoft, info.versionHard);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventColorEffect(EffectData.ColorEffect colorEffect) {
        if (bleOp.isOpCommEnable() || iotOp.isOpCommEnable()) {
            boolean parseResult = checkColor(colorEffect.colorSet);
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(parseResult ? EventEffectSquareOpResult.result_op_support : EventEffectSquareOpResult.result_op_no_support);
        } else {
            /*当前设备未连接*/
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_unable);
        }
    }

    private boolean checkColor(int[] colors) {
        if (colors == null || colors.length == 0) return false;
        int size = colors.length;
        boolean singleColor = colors.length == 1;
        if (!singleColor) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "颜色数量不支持:" + size);
            }
            return false;
        }
        boolean rgbWw = Support.isRgbWw(info.sku);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkColor() rgbWw = " + rgbWw);
        }
        int rgb = colors[0];
        if (bleOp.isOpCommEnable()) {
            Mode mode = new Mode();
            mode.subMode = rgbWw ? SubModeColor4Ww.beRgb(rgb) : SubModeColor.beRgb(rgb);
            ModeController controller = new ModeController(mode);
            bleOp.executeOp(controller);
        } else {
            CmdColorWc cmdColor = CmdColorWc.makeCmdColorWc4Color(rgb);
            iotOp.writeCmd(cmdColor);
        }
        return true;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventOtaPrepareOp(EventOtaPrepareOp event) {
        boolean opCommEnable = bleOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventOtaPrepareOp() opCommEnable = " + opCommEnable);
        }
        if (opCommEnable) {
            OtaPrepareController otaPrepareController = new OtaPrepareController();
            bleOp.executeOp(otaPrepareController);
        } else {
            OtaFlagV2.getInstance.onOtaPrepare(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventServiceSceneFresh(EventServiceScenesFresh event) {
        String sku = event.sku;
        String skuCur = info.sku;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventServiceSceneFresh() sku = " + sku + " ; skuCur = " + skuCur + " ; uiTypeBle = " + uiTypeBle + " ; uiTypeIot = " + uiTypeIot);
        }
        if (!TextUtils.isEmpty(sku) && sku.equals(skuCur) && (uiTypeBle == ui_type_normal || uiTypeIot == ui_type_normal)) {
            /*检查是否有服务器配置场景*/
            if (Support.newScenesVersion(info.versionSoft, info.pactCode, info.sku) >= 1) {
                EventSceneCheck4BleIotV1.sendEventSceneCheck4BleIotV1(info);
            } else {
                EventSceneCheck4BleIot.sendEventSceneCheck4BleIot(info, uiTypeBle == ui_type_normal, ext.wifiSoftVersion, ext.wifiHardVersion);
            }
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventChangeScenes(EventChangeScenes event) {
        String sku = event.sku;
        String skuCur = info.sku;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventChangeScenes() sku = " + sku + " ; skuCur = " + skuCur);
        }
        if (!TextUtils.isEmpty(sku) && sku.equals(skuCur) && (uiTypeBle == ui_type_normal || uiTypeIot == ui_type_normal)) {
            /*检查是否有服务器配置场景*/
            if (Support.newScenesVersion(info.versionSoft, info.pactCode, info.sku) >= 1) {
                EventSceneCheck4BleIotV1.sendEventSceneCheck4BleIotV1(info);
            } else {
                EventSceneCheck4BleIot.sendEventSceneCheck4BleIot(info, uiTypeBle == ui_type_normal, ext.wifiSoftVersion, ext.wifiHardVersion);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventScenesEffect(EventScenesEffect effect) {
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "onEventScenesEffect");
        }
        CategoryV1.SceneV1 sceneV1 = effect.sceneV1;
        int pos = effect.pos;
        if (bleOp.isOpCommEnable() || iotOp.isOpCommEnable()) {
            boolean result = false;
            int status = EventEffectSquareOpResult.status_iot_no_support;
            if (bleOp.isOpCommEnable()) {
                result = scenesEffect4Ble(sceneV1, pos);
                status = EventEffectSquareOpResult.status_ble_no_support;
            } else if (iotOp.isOpCommEnable()) {
                result = scenesEffect4Iot(sceneV1, pos);
                status = EventEffectSquareOpResult.status_iot_no_support;
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(result ? EventEffectSquareOpResult.result_op_support : EventEffectSquareOpResult.result_op_no_support, status);
        } else {
            /*当前设备未连接*/
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_unable);
        }
    }

    private boolean scenesEffect4Ble(CategoryV1.SceneV1 sceneV1, int pos) {
        int cmdVersion = sceneV1.getCmdVersion(info.sku, pos);
        int sceneType = sceneV1.getSceneType(pos, info.sku);
        int sceneCode = sceneV1.getSceneCode(pos, info.sku);
        if (!Support.checkSceneVersion(sceneType, cmdVersion, info.goodsType, info.sku, info.versionSoft, info.versionHard)) {
            return false;
        }
        String sceneEffectStr = sceneV1.getScenesParam(info.sku, pos);
        if (sceneType == ScenesOp.scene_type_static) {
            /*静态场景效果*/
            SubModeScenes subModeScenes = new SubModeScenes();
            subModeScenes.setEffect(sceneCode);
            Mode mode = new Mode();
            mode.subMode = subModeScenes;
            ModeController controller = new ModeController(mode);
            ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeOp(controller));
            return true;
        }
        if (sceneType == ScenesOp.scene_type_rgb) {
            /*rgb效果*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgb(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeMultiOpV1(controllerV14Scenes));
            } else {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "onChangeScenes() rgb指令解析出错!");
                }
                return false;
            }
            return true;
        }
        if (sceneType == ScenesOp.scene_type_rgbic) {
            /*rgbic效果*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgbic(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeMultiOpV1(controllerV14Scenes));
            } else {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "onChangeScenes() rgbic指令解析出错!");
                }
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * iot 发送配置场景
     *
     * @param sceneV1 sceneV1
     * @param pos     pos
     * @return boolean
     */
    private boolean scenesEffect4Iot(CategoryV1.SceneV1 sceneV1, int pos) {
        int cmdVersion = sceneV1.getCmdVersion(info.sku, pos);
        int sceneType = sceneV1.getSceneType(pos, info.sku);
        int sceneCode = sceneV1.getSceneCode(pos, info.sku);
        if (!Support.checkSceneVersion(sceneType, cmdVersion, info.goodsType, info.sku, info.versionSoft, info.versionHard)) {
            return false;
        }
        String sceneEffectStr = sceneV1.getScenesParam(info.sku, pos);
        if (sceneType == ScenesOp.scene_type_static) {
            SubModeScenes subModeScenes = new SubModeScenes();
            subModeScenes.setEffect(sceneCode);
            Mode mode = new Mode();
            mode.subMode = subModeScenes;
            ModeController controller = new ModeController(mode);
            CmdPtReal cmdPt = new CmdPtReal(controller);
            ScenesOp.toSetScenes(ac, sceneV1, pos, () -> iotOp.writeCmd(cmdPt));
            return true;
        }
        if (sceneType == ScenesOp.scene_type_rgb) {
            /*rgb效果*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgb(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                /*多包场景效果-采用效果包+模式包组合方式*/
                CmdPtReal cmdPtReal = CmdPtReal.getNewScenesCmdPtReal(controllerV14Scenes);
                if (cmdPtReal != null) {
                    ScenesOp.toSetScenes(ac, sceneV1, pos, () -> iotOp.writeCmd(cmdPtReal));
                    return true;
                }
            }
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "scenesEffect4Iot() rgb指令解析出错!");
            }
            return false;
        }
        if (sceneType == ScenesOp.scene_type_rgbic) {
            /*rgbic效果*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgbic(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                /*多包场景效果-采用效果包+模式包组合方式*/
                CmdPtReal cmdPtReal = CmdPtReal.getNewScenesCmdPtReal(controllerV14Scenes);
                if (cmdPtReal != null) {
                    ScenesOp.toSetScenes(ac, sceneV1, pos, () -> iotOp.writeCmd(cmdPtReal));
                    return true;
                }
            }
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "scenesEffect4Iot() rgbic指令解析出错!");
            }
            return false;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSwitchMicPickUpType(EventSwitchMicPickUpType event) {
        isSwitchMicPickUpType = true;
        modeUI.switchMicPickUpMode(event.isMicMode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onMicSetRgbController(MicSetRgbController controller) {
        if (bleOp.isOpCommEnable()) {
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_updating)) return;/*升级中，忽略mic发送逻辑*/
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_rebooting))
                return;/*重启过程中，忽略mic发送逻辑*/
            if (controller.isInitMode) {
                EventBleStatus.sendEvent(EventBleStatus.BleStatus.read_info_over);
            }
            boolean rgbWw = Support.isRgbWw(info.sku);
            if (controller.sendByColorMode) {
                Mode mode = new Mode();
                int rgb = ColorUtils.toColor(controller.data[0], controller.data[1], controller.data[2]);
                mode.subMode = rgbWw ? SubModeColor4Ww.beRgb(rgb) : SubModeColor.beRgb(rgb);
                ModeController modeController = new ModeController(mode);
                bleOp.executeExtOp(modeController);
            } else {
                bleOp.executeExtOp(controller);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onRealtimeColorChangeEvent(RealtimeColorChangeEvent event) {
        if (bleOp.isOpCommEnable()) {
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_updating)) return;/*升级中，忽略mic发送逻辑*/
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_rebooting))
                return;/*重启过程中，忽略mic发送逻辑*/
            if (!Util4ColorRealtime.supportColorRealtime(info.sku, info.versionSoft, info.versionHard))
                return;
            AbsMicFragmentV4.SupportMicStatus micStatus = Support.getMicStatus(info.sku, info.versionSoft, info.goodsType);
            int controllerType = Util4ColorRealtime.getControllerType(micStatus, event.rgbicPartChoose);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onRealtimeColorChangeEvent() controllerType = " + controllerType);
            }
            if (controllerType == Util4ColorRealtime.CONTROLLER_TYPE_NO_SUPPORT) return;
            ISubMode subMode = event.subMode;
            int color = event.color;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onRealtimeColorChangeEvent() subMode = " + subMode + " ; color = " + color);
            }
            AbsSingleController controller;
            if (controllerType == Util4ColorRealtime.CONTROLLER_TYPE_COLOR) {
                Mode mode = new Mode();
                mode.subMode = subMode;
                controller = new ModeController(mode);
            } else {
                controller = new MicSetRgbController(ColorUtils.getRgbBytes(color));
            }
            bleOp.executeExtOp(controller);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventEditScenes(EffectEvent.EventEditScenes event) {
        String sku = event.sku;
        int categoryId = event.categoryId;
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "onEventEditScenes:" + sku + "---categoryId:" + categoryId);
        }
        if (!info.sku.equals(sku)) return;
        EditScenesAc.jump2EditScenesAc(ac, info.sku, info.device, info.goodsType, categoryId, true, true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeScenes(CategoryV1.SceneV1 scene) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeScenes()");
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            if (!scenesEffect4Ble(scene, 0)) {
                HintDialog1.createHintDialog1(ac, ResUtil.getString(R.string.b2light_scenes_ble_not_support), ResUtil.getString(R.string.hint_done_got_it)).show();
                hideLoading();
            }
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            if (!scenesEffect4Iot(scene, 0)) {
                HintDialog1.createHintDialog1(ac, ResUtil.getString(R.string.b2light_scenes_ble_not_support), ResUtil.getString(R.string.hint_done_got_it)).show();
                hideLoading();
            }
        }
    }

    /*新DIY交互补充代码逻辑*/

    private DiySupportV1 getDiySupport() {
        return Diy.getDiy(1);
    }

    private void checkDiyModeInfo(SubModeNewDiy diy) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyModeInfo()");
        }
        if (diy == null) return;
        /*刷新在模式中展示的DIY列表*/
        diy.setDiyGroups(curDiyGroups);
        /*获取当前选中的diyValueKey*/
        int diyCode = diy.getDiyCode();
        String lastDiyApplyKey = Diy.getLastDiyApplyKey(1, info.goodsType, info.ic, info.sku, diyCode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyMode() lastDiyApplyKey = " + lastDiyApplyKey);
        }
        diy.setDiyValueKey(lastDiyApplyKey);
    }

    private void checkDiyMode(AbsMode mode) {
        if (mode == null) return;
        ISubMode subMode = mode.subMode;

        if (subMode instanceof SubModeNewDiy) {
            checkDiyModeInfo((SubModeNewDiy) subMode);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEvent2AcDiyGroup(Event2AcDiyGroup event) {
        int icNum = 0;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEvent2AcDiyGroup icNum = " + icNum);
        }
        AcDiyGroup.jump2DiyGroupAc(ac, info.sku, info.goodsType, getDiySupport(), icNum, true);
    }

    private DiyValue applyingDiyValue;

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApplyInModeShowing(EventDiyApply4InModeShowing event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing()");
        }
        DiyProtocol diyProtocol = event.getDiyProtocol();
        if (diyProtocol != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV0 = applyDiyV0(diyProtocol);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV0 = " + applyDiyV0);
            }
            if (applyDiyV0) {
                showLoading();
            }
            return;
        }

        DiyStudio diyStudio = event.getDiyStudio();
        if (diyStudio != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV4 = applyDiyV4(diyStudio);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV4 = " + applyDiyV4);
            }
            if (applyDiyV4) {
                showLoading();
            }
            return;
        }

        DiyAi diyAi = event.getDiyAi();
        if (diyAi != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV5 = applyDiyV5(diyAi);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV5 = " + applyDiyV5);
            }
            if (applyDiyV5) {
                showLoading();
            }
        }
    }

    private boolean applyDiyV0(@NonNull DiyProtocol diyProtocol) {
        if (bleOp.isOpCommEnable()) {
            MultipleDiyControllerV1 multipleDiyController = new MultipleDiyControllerV1(diyProtocol);
            bleOp.executeMultiOpV1(multipleDiyController);
            return true;
        } else if (iotOp.isOpCommEnable()) {
            CmdPtReal cmdPt = CmdPtReal.getDiyCmdPt(diyProtocol);
            iotOp.writeCmd(cmdPt);
            return true;
        }
        return false;
    }

    private boolean applyDiyV5(@NonNull DiyAi diyAi) {
        Command4PtReal ptReal = diyAi.command4PtReal;
        if (bleOp.isOpCommEnable()) {
            bleOp.executeMultiple4PtReal(PtRealController.makePtRealController(ptReal.opCommands, diyAi.diyCode, diyAi.scenesCode));
            return true;
        }
        if (iotOp.isOpCommEnable()) {
            this.diyCode = diyAi.diyCode;
            this.diyTemplateCode = diyAi.scenesCode;
            CmdPtReal cmdPtReal = CmdPtReal.makeCmdPt(diyAi.command4PtReal.getCommands4IotPtReal());
            iotOp.writeCmd(cmdPtReal);
            return true;
        }
        return false;
    }

    private boolean applyDiyV4(@NonNull DiyStudio diyStudio) {
        if (bleOp.isOpCommEnable()) {
            AbsMultipleControllerV14DiyTemplate multi4Scenes = diyStudio.toMulti4Scenes();
            if (multi4Scenes != null) {
                bleOp.executeMultiOpV1(multi4Scenes);
                return true;
            }
        } else if (iotOp.isOpCommEnable()) {
            AbsMultipleControllerV14DiyTemplate multi4Scenes = diyStudio.toMulti4Scenes();
            if (multi4Scenes != null) {
                this.diyCode = diyStudio.diyCode;
                this.diyTemplateCode = diyStudio.scenesCode;
                CmdPtReal cmdPtReal = CmdPtReal.getNewScenes4StudioCmdPtReal(multi4Scenes);
                iotOp.writeCmd(cmdPtReal);
                return true;
            }
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN, priority = 100)
    public void onEventDiyApplyResult(EventDiyApplyResult event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyResult() result = " + result + " ; diyCode = " + diyCode);
        }
        if (applyingDiyValue != null && applyingDiyValue.diyCode == diyCode) {
            if (result) {
                String diyValueKey = applyingDiyValue.getDiyValueKey();
                /*记录上次应用的DIY*/
                LastDiyConfig.read().saveLastDiyValueKey(info.sku, diyValueKey, diyCode, applyingDiyValue.effectCode);
            } else {
                toast(R.string.b2light_diy_apply_fail);
                hideLoading();
            }
        }
        applyingDiyValue = null;
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventDiyModeShowingChange(EventDiyModeShowingChange event) {
        List<DiyGroup> diyShortcuts = DiyShortcutManger.getDiyShortcuts(info.sku, info.goodsType, info.ic, getDiySupport());
        curDiyGroups.clear();
        if (diyShortcuts != null && !diyShortcuts.isEmpty()) {
            curDiyGroups.addAll(diyShortcuts);
        }
        handler.post(new CaughtRunnable() {
            @Override
            protected void runSafe() {
                checkUi();
            }
        });
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyModeShowingChange() curDiyGroups.size = " + curDiyGroups.size());
        }
    }

    /*DIY引导逻辑*/

    private void hideDiyGuide() {
        if (hadShowGuide) {
            hadShowGuide = false;
            GuideDialog.hideDialog(TAG);
        }
    }

    private boolean hadDiyGuide = false;
    private boolean hadShowGuide;

    private void checkDiyGuide() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyGuide() hadDiyGuide = " + hadDiyGuide);
        }
        if (hadDiyGuide) return;
        if (modeUI != null) {
            hadDiyGuide = true;
            hadShowGuide = true;
            Util4Diy.checkShowDiyGuide4ModeNum(ac, TAG, modeUI.getModeNum());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSetMultiMusicEffect(EventSetMultiMusicEffect event) {
        int sensitivity = event.sensitivity;
        AbsNewMusicEffect newMusicEffect = event.newMusicEffect;
        int musicCode = newMusicEffect.getMusicCode();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNewMusicEffect() sensitivity = " + sensitivity + " ; newMusicEffect.musicCode = " + musicCode);
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            MultipleController4Music multipleController4Music = new MultipleController4Music(sensitivity, newMusicEffect);
            bleOp.executeMultiOpV2(multipleController4Music);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            MultipleController4Music multipleController4Music = new MultipleController4Music(sensitivity, newMusicEffect);
            CmdPtReal cmdPt = CmdPtReal.getMultiNewMusicModeV2(multipleController4Music);
            iotOp.writeCmd(cmdPt);
        }
    }

}
