<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.govee.stringlightv2"
    tools:ignore="DiscouragedApi"
    >
    <application>
        <activity
            android:name=".add.DeviceNameAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.WifiChooseAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.AdjustAcV4"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.DeviceNameAc4BKBle"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.DeviceNameAc4BKBleWifi"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.NewBKWifiChooseAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.v2.PairAcV1"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.v2.SettingAcV1"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.v1.PairAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.v1.LimitSettingAc4BleAlexa"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
    </application>
</manifest>
