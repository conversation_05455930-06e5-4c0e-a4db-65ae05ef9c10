apply plugin: 'com.android.library'
apply plugin: 'com.jakewharton.butterknife'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

/**
 * <p>该lib针对的sku类型-RGB灯带
 * 功能列表：亮度+定时+diy+模式+开关
 * 模式-颜色模式(仅rgb);音乐模式(设备识音);场景模式(7大场景)
 * diy-淡入淡出+跳跃+闪烁+混合；颜色个数8
 */
android {
    namespace 'com.govee.stringlightv2'
    compileSdk COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                        AROUTER_MODULE_NAME: project.getName(),
                        MODULE_NAME        : project.getName()
                ]
            }
        }
        //ARouter配置-->用于kotlin编写页面
        kapt {
            //ARouter配置
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
    }

    buildTypes {
        release {
            isDefault = RELEASE_BUILD_VARIANT_IS_DEFAULT
            minifyEnabled false
            consumerProguardFiles = ['proguard-rules.pro']
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            isDefault = !RELEASE_BUILD_VARIANT_IS_DEFAULT
            minifyEnabled false
            consumerProguardFiles = ['proguard-rules.pro']
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    /*资源前缀*/
    resourcePrefix "slv2_"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    /*base2light*/
    implementation project(':base2light')
    /*butterknife注解实现*/
    kapt 'com.jakewharton:butterknife-compiler:' + BUTTERKNIFE_VERSION
    /*ARouter相关配置*/
    //noinspection DependencyNotationArgument
    implementation rootProject.ext.sdk.arouter_api
    //noinspection DependencyNotationArgument
    kapt rootProject.ext.sdk.arouter_compiler
    // AppLifecycle
    implementation 'com.govee.lifecycle:lifecycle-api:' + lifecycle_api_version
    kapt 'com.govee.lifecycle:lifecycle-compiler:' + lifecycle_compiler_version
}
