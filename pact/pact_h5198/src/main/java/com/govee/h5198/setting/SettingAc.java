package com.govee.h5198.setting;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.annotation.Nullable;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.config.Base2homeConfig;
import com.govee.base2home.iot.share.ShareController;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.main.OfflineDeviceListConfig;
import com.govee.base2home.temUnit.TemperatureUnitType;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2home.update.download.CheckVersion;
import com.govee.base2home.update.event.VersionUIEvent;
import com.govee.base2light.ac.update.UpdateResultEvent;
import com.govee.base2newth.bbq.ConsV1;
import com.govee.base2newth.bbq.ble.controller.EventHardVersion;
import com.govee.base2newth.bbq.ble.controller.EventSoftVersion;
import com.govee.base2newth.bbq.ble.controller.EventWifiHardVersion;
import com.govee.base2newth.bbq.ble.controller.EventWifiMac;
import com.govee.base2newth.bbq.ble.controller.EventWifiSoftVersion;
import com.govee.base2newth.bbq.config.BarbecuerAlarmSwitchConfig;
import com.govee.base2newth.bbq.config.BbqPreWarningTemConfig;
import com.govee.base2newth.bbq.setting.AbsBbqSettingAcV2;
import com.govee.base2newth.other.SyncTemUnitUtil;
import com.govee.ble.BleController;
import com.govee.db.table.ShareDeviceTable;
import com.govee.h5198.add.WifiChooseAc;
import com.govee.h5198.ble.ThBle;
import com.govee.h5198.ble.controller.ControllerPreWarn;
import com.govee.h5198.ble.controller.ControllerSwitchBuzzer;
import com.govee.h5198.ble.controller.ControllerTempUnit;
import com.govee.h5198.ble.event.EventPreWarn;
import com.govee.h5198.ble.event.EventSendCmd;
import com.govee.h5198.ble.event.EventSwitchBuzzer;
import com.govee.h5198.ble.event.EventTempUnit;
import com.govee.h5198.iot.IotOpV1;
import com.govee.h5198.model.BarbecuerModel;
import com.govee.h5198.model.DeviceSettings;
import com.govee.h5198.pact.Support;
import com.govee.h5198.ui.BbqMultiDetailActivity;
import com.govee.h5198.ui.BbqMultiUpdateAc;
import com.govee.h5198.ui.ConnectType;
import com.govee.h5198.ui.EventConnectType;
import com.govee.h5198.ui.event.ConnectDeviceEvent;
import com.govee.h5198.ui.event.EventIotConnectStatus;
import com.govee.home.account.config.AccountConfig;
import com.govee.shared.CommSharedSettingsAc;
import com.govee.ui.R;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.JumpUtil;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Create by DengFei on 2020/12/27
 * 设置界面Ac$
 */
public class SettingAc extends AbsBbqSettingAcV2 {

    //固件版本。
    private String versionHard;
    private String bleName;
    private String bleAddress;
    private CheckVersion checkVersion;
    private TemperatureUnitType unitType = TemperatureUnitType.Fahrenheit;
    private boolean isBuzzerOpen;
    private BarbecuerModel model;

    /**
     * 跳转到设置界面
     */
    public static void jump2SettingAc(Activity ac, BarbecuerModel deviceInfo,
                                      int unitTypeOrdinal, boolean isBuzzerOpen, CheckVersion checkVersion) {
        Bundle bundle = makeAcBundle(false, deviceInfo.getSku(), deviceInfo.getDevice(), deviceInfo.getBleName(), deviceInfo.getVersionHard(), 22);
        bundle.putInt(intent_ac_key_unit_type, unitTypeOrdinal);
        bundle.putBoolean(intent_ac_key_buzzer, isBuzzerOpen);
        bundle.putString(ConsV1.intent_ac_key_device_info, JsonUtil.toJson(deviceInfo));
        bundle.putSerializable(intent_ac_key_checkVersion, checkVersion);
        ShareDeviceTable shareDev = ShareController.INSTANCE.queryIsShareDevice(deviceInfo.getSku(), deviceInfo.getDevice());
        if (shareDev != null) {
            CommSharedSettingsAc.jump2ActivitySimple(ac, deviceInfo.getSku(),
                    deviceInfo.getDevice(), JsonUtil.toJson((new SharedSettingVM.ExtJson(unitTypeOrdinal)))
                    , new SharedSettingVM());
            return;
        }
        JumpUtil.jump(ac, SettingAc.class, bundle);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        showBuzzerView();
        /*更新预警ui*/
        BarbecuerAlarmSwitchConfig alarmSwitchConfig = BarbecuerAlarmSwitchConfig.read();
        updateWarmSwitch(alarmSwitchConfig.noticOpen(getKey()), alarmSwitchConfig.flackerOpen(getKey()));
        /*更新版本ui*/
        updateVersion(!TextUtils.isEmpty(versionSoft) && BleController.getInstance().isConnected(), versionSoft, hadNewVersion() && BleController.getInstance().isConnected(), versionHard);
        preWarn.setEnable(isConnected());
        /*更新温度单位ui*/
        updateUnitUi(isConnected(), unitType == TemperatureUnitType.Fahrenheit);
        updateBuzzerSwitch(isConnected(), isBuzzerOpen);
    }

    private boolean isConnected() {
        boolean isConnected = BleController.getInstance().isConnected();
        if (!isConnected) {
            IotOpV1 iotOp = BbqMultiDetailActivity.iotOp;
            if (iotOp != null && iotOp.isOpCommEnable()) {
                isConnected = true;
            }
        }
        return isConnected;
    }

    @Override
    protected void onResume() {
        super.onResume();
        closeBleUpdateHint();
        // 连接蓝牙
        ConnectDeviceEvent.sendConnectDeviceEvent();
    }

    @Override
    protected boolean showPreWarnView() {
        return Support.supportPreWarn(versionSoft);
    }

    @Override
    protected void parserExtParams(Intent intent) {
        model = JsonUtil.fromJson(intent.getStringExtra(ConsV1.intent_ac_key_device_info), BarbecuerModel.class);
        if (model == null) {
            return;
        }
        wifiMac = model.getWifiMac();
        bleAddress = model.getAddress();
        deviceName = model.getDeviceName();
        versionHard = model.getVersionHard();
        versionSoft = model.getVersionSoft();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "versionSoft " + versionSoft);
        }
        bleName = model.getBleName();

        isBuzzerOpen = intent.getBooleanExtra(intent_ac_key_buzzer, true);
        unitType = TemperatureUnitType.values()[intent.getIntExtra(intent_ac_key_unit_type, 0)];
        checkVersion = (CheckVersion) intent.getSerializableExtra(intent_ac_key_checkVersion);
    }

    private String getKey() {
        return sku + "_" + device;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventConnectType(EventConnectType event) {
        updateConnectUI(event.type);
    }

    private void updateConnectUI(ConnectType connectType) {
        switch (connectType) {
            case bleDisable:
            case disconnect:
            case oneWay:
                /*更新版本ui*/
                updateVersion(true, versionSoft, false, versionHard);
            case iot: {
                preWarn.setEnable(isConnected());
                /*更新温度单位ui*/
                updateUnitUi(isConnected(), unitType == TemperatureUnitType.Fahrenheit);
                updateBuzzerSwitch(isConnected(), isBuzzerOpen);
                break;
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventWifiMac(EventWifiMac event) {
        boolean result = event.isResult();
        if (result) {
            model.setWifiMac(event.getMac());
            wifiMac = event.getMac();
            updateVersion(!TextUtils.isEmpty(versionSoft), versionSoft, hadNewVersion(), versionHard);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventIotConnectStatus(EventIotConnectStatus event) {
        updateConnectUI(ConnectType.iot);
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventWifiHardVersion(EventWifiHardVersion event) {
        boolean result = event.isResult();
        if (result) {
            model.setWifiHardVersion(event.wifiHardVersion);
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventWifiSoftVersion(EventWifiSoftVersion event) {
        boolean result = event.isResult();
        if (result) {
            model.setWifiSoftVersion(event.wifiSoftVersion);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN) //发指令获取固件版本
    public void onEventSoftVersion(EventSoftVersion event) {
        boolean result = event.isResult();
        if (result) {
            versionSoft = event.softVersion;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "EventSoftVersion " + versionSoft);
            }
            preWarn.setVisibility(showPreWarnView() ? View.VISIBLE : View.GONE);
            //更新version的ui
            updateVersion(!TextUtils.isEmpty(versionSoft), versionSoft, hadNewVersion(), versionHard);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventHardVersion(EventHardVersion event) {
        boolean result = event.isResult();
        if (result) {
            versionHard = event.hardVersion;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onCheckVersion(VersionUIEvent event) {
        if (!event.isSameDevice(sku, device)) return;
        this.checkVersion = event.hardVersion;
        /*更新version的ui*/
        updateVersion(!TextUtils.isEmpty(versionSoft), versionSoft, hadNewVersion(), versionHard);
    }

    @Override
    protected void toSaveDeviceName(String newDeviceName) {
        if (AccountConfig.read().isHadToken()) {
            super.toSaveDeviceName(newDeviceName);
        } else {
            AbsDevice devices = OfflineDeviceListConfig.read().getDevices(sku, device);
            if (devices == null) {
                return;
            }
            devices.setDeviceName(newDeviceName);
            DeviceExtMode deviceExt = devices.getDeviceExt();
            String deviceSettings = deviceExt.getDeviceSettings();
            DeviceSettings settings = JsonUtil.fromJson(deviceSettings, DeviceSettings.class);
            if (settings == null) {
                return;
            }
            settings.deviceName = newDeviceName;
            deviceExt.setDeviceSettings(JsonUtil.toJson(settings));
            /*存储到本地*/
            OfflineDeviceListConfig.read().addOfflineDevice(devices);
            this.deviceName = newDeviceName;
            deviceNameChangeSuc();
        }
    }

    @Override
    protected void onVoiceSwitch() {
        BarbecuerAlarmSwitchConfig alarmSwitchConfig = BarbecuerAlarmSwitchConfig.read();
        boolean noticeOpen = alarmSwitchConfig.noticOpen(getKey());
        boolean flackerOpen = alarmSwitchConfig.flackerOpen(getKey());
        updateWarmSwitch(!noticeOpen, flackerOpen);
        alarmSwitchConfig.noticSwitch(getKey(), !noticeOpen);
    }

    @Override
    protected void onBuzzerSwitch() {
        // 设置蜂鸣器开关
        EventSendCmd.sendEvent(new ControllerSwitchBuzzer(!isBuzzerOpen));
    }

    @Override
    protected void onVibrationSwitch() {
        BarbecuerAlarmSwitchConfig alarmSwitchConfig = BarbecuerAlarmSwitchConfig.read();
        boolean noticeOpen = alarmSwitchConfig.noticOpen(getKey());
        boolean flackerOpen = alarmSwitchConfig.flackerOpen(getKey());
        updateWarmSwitch(noticeOpen, !flackerOpen);
        alarmSwitchConfig.flackerSwitch(getKey(), !flackerOpen);
    }

    @Override
    protected void toChangeUnit() {
        TemperatureUnitType unit = unitType == TemperatureUnitType.Celsius ?
                TemperatureUnitType.Fahrenheit : TemperatureUnitType.Celsius;
        // 设置温度单位
        EventSendCmd.sendEvent(new ControllerTempUnit(unit));
        SyncTemUnitUtil.INSTANCE.syncTemUnit(transactions.createTransaction(), sku, unit == TemperatureUnitType.Fahrenheit);
    }

    @Override
    protected void setPreWarn(boolean turnOn, int tem) {
        // 设置提前预警
        EventSendCmd.sendEvent(new ControllerPreWarn(turnOn, tem));
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventPreWarn(EventPreWarn event) {
        boolean result = event.isResult();

        if (result) {
            BbqPreWarningTemConfig.read().setPreWarnTemSwitch(getKey(), event.isPreWarn ? 1 : 0);
            BbqPreWarningTemConfig.read().setPreWarnTem(getKey(), (int) event.preWarnTem);
            preWarn.setSkuDevice(sku, device);
            preWarn.setEnable(isConnected());
            if (event.isWrite()) {
                toast(ResUtil.getString(R.string.bbq_presettem_successful));
            }
        } else {
            if (event.isWrite()) {
                toast(ResUtil.getString(R.string.bbq_presettem_failed));
            }
        }
        hideLoading();
    }

    @Override
    protected void toUpdateAc() {
        if (!BleController.getInstance().isConnected()) {
            toast(R.string.bluetooth_disconnect);
            return;
        }
        if (hadNewVersion()) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkVersion.getCurVersionSoft() =" + checkVersion.getCurVersionSoft());
            }
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "versionSoft =" + versionSoft);
            }
            if (TextUtils.isEmpty(checkVersion.getCurVersionSoft())) {
                checkVersion.setCurVersionSoft(versionSoft);
            }
            BbqMultiUpdateAc.jump2OtaUpdateAcV3(this, sku, deviceName, versionSoft, ThemeM.getDefSkuRes(sku), checkVersion);
        }
    }

    @Override
    protected boolean hadNewVersion() {
        return checkVersion != null && checkVersion.isNeedUpdate();
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onUpdateSucEvent(UpdateResultEvent event) {
        if (event.isResult()) {
            this.versionSoft = null;
            this.checkVersion = null;
            TextUtils.isEmpty(null);
            updateVersion(false, versionSoft, hadNewVersion(), versionHard);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventTemperatureUnit(EventTempUnit event) {
        boolean result = event.isResult();
        hideLoading();
        //设置成功
        if (event.isWrite()) {
            if (result) {
                toast(ResUtil.getString(R.string.bbq_presettem_successful));
            } else {
                toast(ResUtil.getString(R.string.bbq_presettem_failed));
            }
        }
        if (result) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventTemperatureUnit()");
            }
            unitType = event.unitType;
            /*更新温度单位ui*/
            updateUnitUi(isConnected(), unitType == TemperatureUnitType.Fahrenheit);
        }
    }

    private void deleteLocal(String error) {
        hideLoading();
        toast(error);
        // 断开连接
        BleController.getInstance().toBtClose();
        // 删除本地离线设备记录
        OfflineDeviceListConfig.read().removeBoundDevice(sku, device);

        // 跳转到主页
        Bundle bundle = new Bundle();
        bundle.putString(com.govee.base2home.Constant.intent_ac_key_unbind, device);
        bundle.putString(com.govee.base2home.Constant.intent_ac_key_sku, sku);
        /*统计删除设备*/
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.delete_device, sku);
        JumpUtil.jumpWithBundle(this, Base2homeConfig.getConfig().getMainAcClass(), true, bundle);
    }

    protected void jump2WifiSettingAc() {
        WifiChooseAc.jump2wifiChooseAcByChangeWifi(this,
                sku,
                device,
                deviceName,
                bleName,
                bleAddress,
                versionHard,
                versionSoft
        );
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventSwitchBuzzer(EventSwitchBuzzer event) {
        boolean result = event.isResult();
        hideLoading();
        //设置成功
        if (event.isWrite()) {
            if (result) {
                toast(ResUtil.getString(R.string.bbq_presettem_successful));
            } else {
                toast(ResUtil.getString(R.string.bbq_presettem_failed));
            }
        }
        if (result) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventSwitchBuzzer()" + event.isBuzzerOn);
            }
            isBuzzerOpen = event.isBuzzerOn;
            updateBuzzerSwitch(isConnected(), isBuzzerOpen);
        }
        ThBle.getInstance().controllerEvent(event);
    }

    @Override
    public void onUnbindSuc(String sku, String device, String msg) {
        deleteLocal(msg);
    }
}
