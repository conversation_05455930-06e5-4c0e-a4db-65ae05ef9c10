package com.govee.pact_h60a0.pact;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.Pact;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2light.DeviceNameConfig;
import com.govee.base2light.ac.diy.DiyM;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.ScenesRgb;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.ble.scenes.IScenesEffect4Statics;
import com.govee.base2light.ble.scenes.ScenesM;
import com.govee.base2light.config.DynamicDomainConfig;
import com.govee.base2light.kt.comm.Info4BleIotDevice;
import com.govee.base2light.light.ScenesHint;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.pact_h60a0.ble.Mode;
import com.govee.pact_h60a0.ble.v1.SubModeScenesV1;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Create by linshicong on 2020/7/12
 */
public final class Support {
    public static final int goodsTypeH60A0 = GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A0;
    public static final int goodsTypeH60A1 = GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A1;
    public static final int goodsTypeH60A4 = GoodsType.GOODS_TYPE_VALUE_H60A4;
    public static final int goodsTypeH60A6 = GoodsType.GOODS_TYPE_VALUE_H60A6;
    public static final int goodsTypeH60C1 = GoodsType.GOODS_TYPE_VALUE_H60C1;


    private Support() {
    }

    public static final String H60A0 = "H60A0";
    public static final String H60A1 = "H60A1";
    public static final String H80A1 = "H80A1";//H60A1的线下款
    public static final String H60A4 = "H60A4";
    public static final String H80A4 = "H80A4";//H60A4的线下款
    public static final String H60A6 = "H60A6";

    public static List<Protocol> supportProtocolsV1 = new ArrayList<>();/*H60A0*/
    public static List<Protocol> supportProtocolsV2 = new ArrayList<>();/*H60A1*/
    public static List<Protocol> supportH60A4 = new ArrayList<>();/*H60A4*/
    public static List<Protocol> supportH60A6 = new ArrayList<>();/*H60A6*/

    /**
     * 设备列表的goodsType集合
     */
    public static final int[] deviceItemGoodsTypes = {
            goodsTypeH60A0,
            goodsTypeH60A1,
            goodsTypeH60A4,
            goodsTypeH60A6,
            goodsTypeH60C1
    };
    /**
     * 节律支持的goodsType
     */
    public static final int[] rhythmGoodsTypes = {
            goodsTypeH60A0,
            goodsTypeH60A1,
            goodsTypeH60A4,
            goodsTypeH60A6
    };

    /**
     * 支持ble的goodsType集合
     */
    public static String[] supportBleGoodsTypeSet = new String[]{
            String.valueOf(goodsTypeH60A0),
            String.valueOf(goodsTypeH60A1),
            String.valueOf(goodsTypeH60A4),
            String.valueOf(goodsTypeH60A6)
    };

    /**
     * 支持iot的goodsType集合
     */
    public static String[] supportIotGoodsTypeSet = new String[]{
            String.valueOf(goodsTypeH60A0),
            String.valueOf(goodsTypeH60A1),
            String.valueOf(goodsTypeH60A4),
            String.valueOf(goodsTypeH60A6)
    };

    public static void addSupportPact() {
        Pact pact = Pact.getInstance;

        /*H60A0*/
        Protocol protocolV1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_BLE_WIFI_H60A0, GoodsType.PACT_CODE_BLE_WIFI_H60A0);
        supportProtocolsV1.add(protocolV1);
        pact.addProtocol(goodsTypeH60A0, protocolV1);
        /*初始化支持的默认sku图*/
        ThemeM.getInstance.addDefSkuRes(H60A0, com.govee.pact_h60a0.R.mipmap.add_list_type_device_60a0);

        /*H60A1*/
        Protocol protocolH60A1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_BLE_WIFI_H60A1, GoodsType.PACT_CODE_BLE_WIFI_H60A1);
        supportProtocolsV2.add(protocolH60A1);
        pact.addProtocol(goodsTypeH60A1, protocolH60A1);
        Protocol protocolH60A1_V2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_BLE_WIFI_H60A1_V2, GoodsType.PACT_CODE_BLE_WIFI_H60A1);
        supportProtocolsV2.add(protocolH60A1_V2);
        pact.addProtocol(goodsTypeH60A1, protocolH60A1_V2);
        /*初始化支持的默认sku图*/
        ThemeM.getInstance.addDefSkuRes(H60A1, com.govee.pact_h60a0.R.mipmap.add_list_type_device_60a0);
        ThemeM.getInstance.addDefSkuRes(H80A1, com.govee.pact_h60a0.R.mipmap.add_list_type_device_60a0);

        /*H60A4*/
        Protocol protocolH60A4 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_H60A4, GoodsType.PACT_CODE_VALUE_H60A4);
        supportH60A4.add(protocolH60A4);
        pact.addProtocol(goodsTypeH60A4, protocolH60A4);
        /*初始化支持的默认sku图*/
        ThemeM.getInstance.addDefSkuRes(H60A4, com.govee.pact_h60a0.R.mipmap.add_list_type_device_60a4);
        ThemeM.getInstance.addDefSkuRes(H80A4, com.govee.pact_h60a0.R.mipmap.add_list_type_device_60a4);

        /*H60A6*/
        Protocol protocolH60A6 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_H60A6, GoodsType.PACT_CODE_VALUE_H60A6);
        supportH60A6.add(protocolH60A6);
        pact.addProtocol(goodsTypeH60A6, protocolH60A6);
        Protocol protocolV2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_H60A6, GoodsType.PACT_CODE_VALUE_H60A6_2);
        supportH60A6.add(protocolV2);
        pact.addProtocol(goodsTypeH60A6, protocolV2);
        Protocol protocolV3 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_H60A6, GoodsType.PACT_CODE_VALUE_H60A6_3);
        supportH60A6.add(protocolV3);
        pact.addProtocol(goodsTypeH60A6, protocolV3);
        /*初始化支持的默认sku图*/
        ThemeM.getInstance.addDefSkuRes(H60A6, com.govee.pact_h60a0.R.mipmap.add_list_type_device_60a6);

        /*注册diyOp*/
        DiyM.getInstance.addDiyOp(EffectOp4BleIot.getInstance);
    }

    /**
     * H60A0
     */
    public static boolean supportPactV1(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == goodsTypeH60A0 && !supportProtocolsV1.isEmpty()) {
            for (Protocol pro : supportProtocolsV1) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    /**
     * H60A1
     */
    public static boolean supportPactV2(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == goodsTypeH60A1 && !supportProtocolsV2.isEmpty()) {
            for (Protocol pro : supportProtocolsV2) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    public static boolean supportPactH60A4(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == goodsTypeH60A4 && !supportH60A4.isEmpty()) {
            for (Protocol pro : supportH60A4) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    public static boolean supportPactH60A6(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == goodsTypeH60A6 && !supportH60A6.isEmpty()) {
            for (Protocol pro : supportH60A6) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    public static boolean supportPactH60C1(int goodsType, Protocol protocol) {
        return isGoodsTypeH60C1(goodsType) &&
                protocol.isSameProtocol(GoodsType.PACT_TYPE_VALUE_H60C1, GoodsType.PACT_CODE_VALUE_H60C1);
    }

    /**
     * 获取wifi输入限制
     *
     * @return [2];[0]ssidInputLimit;[1]passwordInputLimit
     */
    public static int[] getWifiInputLimit() {
        return new int[]{32, 64};
    }

    public static int[] getDefHeaderRes(int goodsType) {
        if (isGoodsTypeH60A0(goodsType) || isGoodsTypeH60A1(goodsType)) {
            return new int[]{com.govee.pact_h60a0.R.mipmap.h60a0_light_title_on, com.govee.pact_h60a0.R.mipmap.h60a0_light_title_on_1, com.govee.pact_h60a0.R.mipmap.h60a0_light_title_on_2, com.govee.pact_h60a0.R.mipmap.h60a0_light_title_off};
        }
        return null;
    }

    /**
     * 检测给到wifi设备的当前服务器域名版本值
     *
     * @return int
     */
    public static int check2WifiDeviceRunModeVersion() {
        return 1;/*新sku默认使用v1*/
    }

    public static int getColorPieceSize(int goodsType) {
        if (goodsType == Support.goodsTypeH60A0 || goodsType == Support.goodsTypeH60A1) {
            return 14;
        } else if (goodsType == Support.goodsTypeH60A4) {
            return 11;
        } else if (isGoodsTypeH60C1(goodsType)) {
            return 3;
        }
        return 1;
    }

    public static int getColorPieceSize(int goodsType, int pactType, int pactCode) {
        if (supportPartColor4H60A6(goodsType, pactType, pactCode)) {
            return 13;
        }
        return getColorPieceSize(goodsType);
    }

    public static int supportRandomColorSize(int goodsType, int pactType, int pactCode) {
        if (goodsType == Support.goodsTypeH60A0) {
            return 13;
        } else if (goodsType == Support.goodsTypeH60A1) {
            return 14;
        } else if (goodsType == Support.goodsTypeH60A4) {
            return 11;
        }
        if (supportPartColor4H60A6(goodsType, pactType, pactCode)) {
            return 13;
        }
        return 1;
    }

    public static void isSupportMicByPhone(String sku, String device) {
        AbsMicFragmentV4.SupportMicStatus support = AbsMicFragmentV4.SupportMicStatus.support_new_order;
        AbsMicFragmentV4.saveSupportMicModeByPhone(sku, device, support);
    }

    /**
     * 获取场景提示信息
     * @return null表明当前不支持hint提示
     */
    public static ScenesHint getScenesHint(int effect) {
        checkHintMap();
        return scenesHintMap.get(effect);
    }

    private synchronized static void checkHintMap() {
        if (scenesHintMap.isEmpty()) {
            /*日出提示*/
            ScenesHint scenesHint4gm = ScenesHint.makeHint4gm(IScenesEffect4Statics.value_sub_mode_scenes_gm);
            scenesHintMap.put(scenesHint4gm.effect, scenesHint4gm);
            /*日落提示*/
            ScenesHint scenesHint4sunset = ScenesHint.makeHint4sunset(IScenesEffect4Statics.value_sub_mode_scenes_sunset);
            scenesHintMap.put(scenesHint4sunset.effect, scenesHint4sunset);
            /*警报提示*/
            ScenesHint scenesHint4alarm = ScenesHint.makeHint4alarm(ScenesRgb.effect_alarm, ScenesOp.scene_type_rgb);
            scenesHintMap.put(scenesHint4alarm.effect, scenesHint4alarm);
        }
    }

    private static final HashMap<Integer, ScenesHint> scenesHintMap = new HashMap<>();

    public static AbsMultipleControllerV14Scenes is2NewScenesModeV1(String sku, Mode mode) {
        if (mode == null) return null;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenesV1) {
            int effect = ((SubModeScenesV1) subMode).getEffect();
            return isMultiScenesV1(sku, effect);
        }
        return null;
    }

    public static AbsMultipleControllerV14Scenes isMultiScenesV1(String sku, int effect) {
        /*先检查服务器场景配置*/
        CategoryV1.SceneV1 scene = ScenesM.getInstance.getSceneV1(sku, effect);

        if (scene != null) {
            return ScenesOp.parseSceneV1(sku, scene, ScenesOp.scene_type_rgb, ScenesOp.scene_type_op_rgbic_v0, ScenesOp.scene_type_op_rgbic_v1);
        }
        return null;
    }

    /**
     * 色温范围-[2]-[0]:minKelvin;[1]:maxKelvin;
     */
    public static int[] getColorTemRange() {
        return new int[]{2200, 6500};
    }

    public static int[] getColorTemRange(int goodsType) {
        if (Support.isGoodsTypeH60A4(goodsType) || Support.isGoodsTypeH60A6(goodsType) || Support.isGoodsTypeH60C1(goodsType)) {
            return new int[]{2700, 6500};
        }
        return new int[]{2200, 6500};
    }

    public static String getSkuDefName(String sku, String defDeviceName) {
        if (H60A0.equalsIgnoreCase(sku) || H60A1.equalsIgnoreCase(sku)) {
            return DeviceNameConfig.h60A0_name;
        }
        return defDeviceName;
    }

    public static int[] supportScenesOpSet(int goodsType) {
        if (isGoodsTypeH60A0(goodsType) || isGoodsTypeH60A1(goodsType) || isGoodsTypeH60A4(goodsType)) {
            return new int[]{
                    ScenesOp.scene_type_op_static,
                    ScenesOp.scene_type_op_rgb,
                    ScenesOp.scene_type_op_rgbic_v0,
                    ScenesOp.scene_type_op_rgbic_v1
            };
        } else if (isGoodsTypeH60A6(goodsType) || isGoodsTypeH60C1(goodsType)) {
            return new int[]{
                    ScenesOp.scene_type_op_static,
                    ScenesOp.scene_type_op_rgb,
                    ScenesOp.scene_type_op_rgbic_v0,
                    ScenesOp.scene_type_op_rgbic_v1,
                    ScenesOp.scene_type_op_diy};
        }
        return new int[]{};
    }

    public static boolean isGoodsTypeH60A0(int goodsType) {
        return goodsType == goodsTypeH60A0;
    }

    public static boolean isGoodsTypeH60A1(int goodsType) {
        return goodsType == goodsTypeH60A1;
    }

    public static boolean isGoodsTypeH60A4(int goodsType) {
        return goodsType == goodsTypeH60A4;
    }

    public static boolean isGoodsTypeH60A6(int goodsType) {
        return goodsType == goodsTypeH60A6;
    }

    public static boolean isGoodsTypeH60C1(int goodsType) {
        return goodsType == goodsTypeH60C1;
    }

    public static boolean supportPartColor4H60A6(int goodsType, int pactType, int pactCode) {
        boolean condition1 = goodsType == goodsTypeH60A6;
        boolean condition2 = pactType == GoodsType.PACT_TYPE_VALUE_H60A6 && pactCode >= GoodsType.PACT_CODE_VALUE_H60A6_2;
        return condition1 && condition2;
    }

    public static int getDiyVersion(int goodsType) {
        if (isGoodsTypeH60A4(goodsType)) return 2;
        if (isGoodsTypeH60A6(goodsType)) return 3;
        return 1;
    }

    /**
     * 是否支持Ai OTA
     */
    public static boolean supportAiOta(int goodsType) {
        //60A0不支持AI
        return !isGoodsTypeH60A0(goodsType);
    }

    /**
     * 支持动态域名
     */
    public static void setDynamicApi() {
        DynamicDomainConfig.getInstance().setSupportHttps(H60A0);
    }

    /**
     * 是否是新的音乐模式code
     */
    public static boolean isNewMusicCode(int musicCode) {
        return AbsNewMusicEffect.newMusicCode4RgbicList.contains(musicCode) || AbsNewMusicEffect.newMusicCode4RgbList.contains(musicCode);
    }

    public static int getOneGroupColorSize(int goodsType) {
        return 4;
    }

    public static boolean supportPartTem(Info4BleIotDevice info) {
        return info.getPactType() == GoodsType.PACT_TYPE_VALUE_H60A6 && info.getPactCode() >= GoodsType.PACT_CODE_VALUE_H60A6_3;
    }

    public static boolean supportPartTem(int pactType, int pactCode) {
        return pactType == GoodsType.PACT_TYPE_VALUE_H60A6 && pactCode >= GoodsType.PACT_CODE_VALUE_H60A6_3;
    }

    public static boolean supportAllColor4H60A6(int goodsType, int pactType, int pactCode) {
        boolean condition1 = goodsType == goodsTypeH60A6;
        boolean condition2 = pactType == GoodsType.PACT_TYPE_VALUE_H60A6 && pactCode == GoodsType.PACT_CODE_VALUE_H60A6;
        return condition1 && condition2;
    }

    public static boolean partColor4EqualH60A6(int goodsType, int pactType, int pactCode) {
        boolean condition1 = goodsType == goodsTypeH60A6;
        boolean condition2 = pactType == GoodsType.PACT_TYPE_VALUE_H60A6 && pactCode == GoodsType.PACT_CODE_VALUE_H60A6_2;
        return condition1 && condition2;
    }
}