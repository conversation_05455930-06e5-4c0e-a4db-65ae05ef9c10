package com.govee.base_h71xx.sku_base.chart.viewmodel

import com.govee.base2newth.ThUtil
import com.govee.base2newth.data.AbsDownloadOp
import com.govee.base_h71xx.sku_base.adjust.component.ServiceChartDataConfig
import com.govee.base_h71xx.sku_base.chart.H71xxChartCons
import com.govee.db.dao.AccountDatabase
import com.govee.db.table.H71xxModeChartTable
import com.govee.home.account.config.AccountConfig
import com.ihoment.base2app.infra.SafeLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch

/**
 * Create by yu on 2023/9/21
 */
class H71xxChartDownloadOp(
    sku: String,
    device: String,
    val goodsType: Int,
    private var validMinTimeMill: Long,
    private var needFilterHumZero: Boolean,

    ) :
    AbsDownloadOp(sku, device) {
    private val chartDao =
        AccountDatabase.getInstance(AccountConfig.read().accountId).getH71xxModeChartDao()


    var map = hashMapOf<Long, Int>()

    override fun insertServiceData(
        sku: String,
        device: String,
        thSet: Array<String>?,
        minTime: Long,
    ): Boolean {

        if (thSet == null || thSet.isEmpty()) {
            return false
        }
        if (ServiceChartDataConfig.isAirPurifierDevice(goodsType)) {
            return insertWithAirPurifier(sku, device, thSet, minTime)
        }
        if (ServiceChartDataConfig.isDataMergeGoodsType(goodsType)) {
            SafeLog.i("DownloadOp", "-----insertServiceData merge-----")
            return insertWithMergeSku(sku, device, thSet, minTime)
        }
        SafeLog.i("DownloadOp", "-----insertServiceData not merge-----")
        val list = mutableListOf<H71xxModeChartTable>()
        //普通温度计，数据包含：温度、湿度、上报时间点
        if (thSet[0].split(",".toRegex()).toTypedArray().size == 3) {
            for (thDataStr in thSet) {
                val str = thDataStr.split(",".toRegex()).toTypedArray()
                if (str.size == 3) {
                    val tem = ThUtil.parseTem(str[0])
                    val hum = ThUtil.parseHum(str[1])
                    var timeMills = ThUtil.parseTime(str[2])
                    //时间做整分钟处理
                    timeMills -= timeMills % 60000
                    var validThData = ThUtil.isValidThData(tem, hum, timeMills)
                    /*过滤异常数据*/
                    if (needFilterHumZero && hum <= H71xxModeChartTable.HUM_LOW_INVALID || hum >= H71xxModeChartTable.HUM_HEIGHT_INVALID) {
                        validThData = false
                    } else if (H71xxChartCons.isNeedFilterTempInvalid(sku) && (tem >= H71xxModeChartTable.TEM_HEIGHT_INVALID || tem <= H71xxModeChartTable.TEM_LOW_INVALID)) {
                        validThData = false
                    }
                    if (validThData && timeMills >= minTime) {
                        val mode = if (map.contains(timeMills)) map[timeMills] else -1
                        list.add(
                            H71xxModeChartTable(
                                timeMills,
                                sku,
                                device,
                                H71xxModeChartTable.FROM_TYPE_CLOUD,
                                tem = tem,
                                hum = hum,
                                mode = mode ?: -1
                            )
                        )
                    }
                }
            }


        }

        //带pm2.5的温度计，数据包含：温度、湿度、pm2.5、上报时间点
        else if (thSet[0].split(",".toRegex()).toTypedArray().size == 4) {
            for (thDataStr in thSet) {
                val str = thDataStr.split(",".toRegex()).toTypedArray()
                if (str.size == 4) {
                    val tem = ThUtil.parseTem(str[0])
                    val hum = ThUtil.parseHum(str[1])
                    val pm25 = ThUtil.parseHum(str[2])
                    var timeMills = ThUtil.parseTime(str[3])
                    //时间做整分钟处理
                    timeMills -= timeMills % 60000
                    val validThData = ThUtil.isValidThpData(tem, hum, pm25, timeMills)
                    if (validThData && timeMills >= minTime) {
                        val mode = if (map.contains(timeMills)) map[timeMills] else -1

                        list.add(
                            H71xxModeChartTable(
                                timeMills,
                                sku,
                                device,
                                H71xxModeChartTable.FROM_TYPE_CLOUD,
                                tem = tem,
                                hum = hum,
                                pm25 = pm25, mode = mode ?: -1
                            )
                        )
                    }
                }
            }

        }
        if (list.isNotEmpty()) {
            CoroutineScope(Dispatchers.IO).launch {
                chartDao.saveChartDataList(list)
            }
        }
        return true
    }

    /**
     * 空气净化器数据入库
     */
    private fun insertWithAirPurifier(sku: String, device: String, pm25Set: Array<String>, minTime: Long): Boolean {
        val defaultTem = 9000
        val defaultHum = 5100
        val list = mutableListOf<H71xxModeChartTable>()
        for (pm25DataStr in pm25Set) {
            val str = pm25DataStr.split(",".toRegex()).toTypedArray()
            if (str[1] == "65535") {
                continue
            }
            if (str.size == 3 && str[1].toInt() >= 0) {
                val modeGear = str[0].toInt()
                val pm25 = str[1].toInt()
                var timeMills = ThUtil.parseTime(str[2])
                //时间做整分钟处理
                timeMills -= timeMills % 60000
                if (timeMills >= minTime) {
                    list.add(
                        H71xxModeChartTable(
                            timeMills,
                            sku,
                            device,
                            H71xxModeChartTable.FROM_TYPE_CLOUD,
                            pm25 = pm25,
                            mode = modeGear,
                            tem = defaultTem,
                            hum = defaultHum
                        )
                    )
                }
            }
        }
        if (list.isNotEmpty()) {
            CoroutineScope(Dispatchers.IO).launch {
                SafeLog.i("DownloadOp", "pm2.5 有效数据 size=${list.size}")
                chartDao.saveChartDataList(list)
            }
        }
        return true
    }

    private fun insertWithMergeSku(sku: String, device: String, thSet: Array<String>, minTime: Long): Boolean {
        SafeLog.i("DownloadOp", "thSet size=${thSet.size}")
        val list = mutableListOf<H71xxModeChartTable>()
        //普通温度计，数据包含：温度、湿度、上报时间点
        for (thDataStr in thSet) {
            val str = thDataStr.split(",".toRegex()).toTypedArray()
            if (str.size == 4) {
                val modeGear = str[0].toInt()
                val tem = ThUtil.parseTem(str[1])
                val hum = ThUtil.parseHum(str[2])
                var timeMills = ThUtil.parseTime(str[3])
                //时间做整分钟处理
                timeMills -= timeMills % 60000
                var validThData = ThUtil.isValidThData(tem, hum, timeMills)
                /*过滤异常数据*/
                if (needFilterHumZero && hum <= H71xxModeChartTable.HUM_LOW_INVALID || hum >= H71xxModeChartTable.HUM_HEIGHT_INVALID) {
                    validThData = false
                } else if (H71xxChartCons.isNeedFilterTempInvalid(sku) && (tem >= H71xxModeChartTable.TEM_HEIGHT_INVALID || tem <= H71xxModeChartTable.TEM_LOW_INVALID)) {
                    validThData = false
                }
                if (tem == H71xxModeChartTable.BIND_DEVICE_OFFLINE && hum ==  H71xxModeChartTable.BIND_DEVICE_OFFLINE) {// 不过滤7fff
                    validThData = true
                }
                if (validThData && timeMills >= minTime) {
                    list.add(
                        H71xxModeChartTable(
                            timeMills,
                            sku,
                            device,
                            H71xxModeChartTable.FROM_TYPE_CLOUD,
                            tem = tem,
                            hum = hum,
                            mode = modeGear
                        )
                    )
                }
            }
        }

        if (list.isNotEmpty()) {
            CoroutineScope(Dispatchers.IO).launch {
                chartDao.saveChartDataList(list)
            }
        }
        return true
    }


    override fun getValidMinTimeMills(): Long {
        return validMinTimeMill
    }

    override fun onDestroy() {
        super.onDestroy()
    }
}