package com.govee.base_h71xx.sku_base.chart.viewmodel

import android.os.Handler
import android.os.Looper
import android.os.Message
import androidx.lifecycle.MutableLiveData
import com.govee.base2home.iot.share.ShareController
import com.govee.base2home.update.downloadv2.DownloadManager
import com.govee.base2home.update.downloadv2.DownloadState
import com.govee.base2newth.data.DataConfig
import com.govee.base2newth.data.IServiceDataOp
import com.govee.base2newth.data.IServiceDataOpResult
import com.govee.base_h71xx.network.ChartTaskLinkInfo.Companion.STATE_COMPLETE
import com.govee.base_h71xx.network.ChartTaskLinkInfo.Companion.STATE_NO_DATA
import com.govee.base_h71xx.network.ChartTaskLinkInfo.Companion.STATE_OVERTIME
import com.govee.base_h71xx.network.ChartTaskLinkInfo.Companion.STATE_PROCESS
import com.govee.base_h71xx.network.ChartTaskReq
import com.govee.base_h71xx.network.TaskTimeRange
import com.govee.base_h71xx.network.chartApiService
import com.govee.db.dao.AccountDatabase
import com.govee.db.table.H71xxModeChartTable
import com.govee.home.account.config.AccountConfig
import com.govee.mvvm.ext.executeResponse
import com.govee.mvvm.network.AppException
import com.govee.mvvm.network.BaseResponse
import com.govee.mvvm.network.ExceptionHandle
import com.govee.mvvm.state.ResultState
import com.govee.mvvm.state.paresException
import com.govee.mvvm.state.paresResult
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.infra.SafeLog
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Deferred
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.cancel
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import kotlin.coroutines.CoroutineContext
import kotlin.math.max

class ServiceDataOpH71xxNew(
    val sku: String,
    val device: String,
    private val h71xxChartViewModel: H71xxChartViewModel,
    private val minTimeMillis: Long
) : IServiceDataOp {
    private val TAG = this.javaClass.simpleName
    private var opResult: IServiceDataOpResult? = null
    private var isServicing = false
    private var loadDataScope: CoroutineScope? = null
    private var retryTimes = 0
    private val taskLinkList = mutableListOf<String>()  //待下载的任务链接
    private val processTaskIdSet = mutableSetOf<Int>()  //还需要再次调服务端接口拉取的接口
    private var serviceDataClearTimeMillis = 0L
    private var isSharedDev: Boolean? = null
    private val chartDao =
        AccountDatabase.getInstance(AccountConfig.read().accountId).getH71xxModeChartDao()

    companion object {
        const val MAX_LINK_RETRY_TIMES = 60 / 5           //重试次数
        const val MSG_RETRY_LOAD_DOWNLOAD_LINK = 1
        const val MSG_LOAD_DOWNLOAD_LINK_DONE = 2
    }

    private val handler = object: Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            when (msg.what) {
                MSG_RETRY_LOAD_DOWNLOAD_LINK -> {
                    retryTimes++
                    if (retryTimes < MAX_LINK_RETRY_TIMES && processTaskIdSet.isNotEmpty()) {
                        loadChartDownloadLink(processTaskIdSet.toList())
                    } else {
                        processTaskIdSet.clear()
                        if (taskLinkList.isEmpty()) {
                            loadServiceDataOver()
                        } else {
                            downloadFile(taskLinkList)
                        }
                    }
                }
                MSG_LOAD_DOWNLOAD_LINK_DONE -> {
                    if (taskLinkList.isEmpty()) {
                        loadServiceDataOver()
                    } else {
                        downloadFile(taskLinkList)
                    }
                }
            }
        }
    }

    init {
        loadDataScope = CoroutineScope(Dispatchers.IO)
    }

    private fun loadChartData() {
        processTaskIdSet.clear()
        taskLinkList.clear()
        var lastEndTime = DataConfig.read().getLastDataClearTime(sku, device)
        SafeLog.i(TAG, "chart loadChartData, sp里的开始时间：${lastEndTime}")
        if (lastEndTime <= 0L) {
            lastEndTime = minTimeMillis
        }
        val endTime = System.currentTimeMillis()
        SafeLog.i(
            TAG,
            "chart loadChartData, 开始时间和结束时间：${lastEndTime}, $endTime"
        )
        if (isSharedDev == null) {
            isSharedDev = ShareController.isSharedDevice(sku, device)
        }
        val isShared = if (isSharedDev.isTrue()) 1 else 0
        val info = ChartTaskReq(
            sku,
            device,
            isShared,
            TaskTimeRange(lastEndTime, endTime)
        )
        taskLinkList.clear()
        processTaskIdSet.clear()
        loadDataScope?.request({
            chartApiService.generateChartDownloadTask(info)
        }, success = { res ->
            SafeLog.i(TAG, "chart loadServiceChartDataTask(), success: $res")
            if (res != null && !res.taskIds.isNullOrEmpty()) {
                serviceDataClearTimeMillis = res.dataClearTime
                DataConfig.read()
                    .updateLastDataClearTime(sku, device, res.dataClearTime)
                loadChartDownloadLink(res.taskIds)
            } else {
                loadServiceDataOver()
            }
        }, error = { err ->
            loadServiceDataOver()
            SafeLog.i(TAG, "chart loadServiceChartDataTask(), error: ${err.errorMsg}")
        })
    }

    /**
     * 根据taskId获取下载链接
     * clearTime 毫秒
     */
    private fun loadChartDownloadLink(taskIds: List<Int>) {
        loadDataScope?.launch(Dispatchers.IO) {
            request({
                chartApiService.getChartDataLinks(taskIds)
            }, success = { info ->
                SafeLog.i(TAG, "chart loadChartDownloadLink() success: $info")
                //下载
                info?.let { linkInfoList ->
                    for (link in linkInfoList) {
                        when (link.state) {
                            STATE_PROCESS -> {
                                //链接任务还在处理，加入集合进行下一次的重新
                                processTaskIdSet.add(link.taskId)
                            }
                            STATE_COMPLETE -> {
                                //链接任务处理完成，将taskId从待处理的集合中移除
                                //并加入下载链接到链接集合中
                                processTaskIdSet.remove(link.taskId)
                                link.links?.let {
                                    if (it.isNotEmpty()) {
                                        taskLinkList.addAll(it)
                                    }
                                }
                            }
                            STATE_NO_DATA, STATE_OVERTIME -> {
                                //没有数据或超时，将taskId从待处理的集合中移除
                                processTaskIdSet.remove(link.taskId)
                            }
                        }
                    }
                }
                if (processTaskIdSet.isNotEmpty()) {
                    //还有待处理的taskId，那么继续重试拉取下载链接
                    SafeLog.i(TAG, "taskId还没有处理完成：${processTaskIdSet}")
                    handler.removeMessages(MSG_RETRY_LOAD_DOWNLOAD_LINK)
                    handler.sendEmptyMessageDelayed(MSG_RETRY_LOAD_DOWNLOAD_LINK, 15000L)
                } else {
                    //所有的taskId任务都已经完成，那么就开始下载文件等下一步操作
                    SafeLog.i(TAG, "taskId全部处理完成，开始下载链接，链接数量：${taskLinkList.size}")
                    handler.removeMessages(MSG_LOAD_DOWNLOAD_LINK_DONE)
                    handler.sendEmptyMessage(MSG_LOAD_DOWNLOAD_LINK_DONE)
                }
            }, error = { err ->
                loadServiceDataOver()
                SafeLog.i(TAG, "chart loadChartDownloadLink() error: ${err.errorMsg}")
            })
        }
    }

    /**
     * 下载文件
     */
    private fun downloadFile(links: List<String>?) {
        if (links.isNullOrEmpty()) return
        loadDataScope?.launch(Dispatchers.IO) {
            val list = mutableListOf<Deferred<Unit>>()
            for (link in links) {
                val job = async {
                    val curTime = System.currentTimeMillis()
                    val fileName = "$curTime.txt"
                    val file = File(BaseApplication.getBaseApplication().cacheDir, "$curTime")
                    DownloadManager.download(link, file).collect {
                        when (it) {
                            is DownloadState.InProgress -> {
                                SafeLog.i(
                                    TAG,
                                    "chart downloadFile: $fileName, progress: ${it.progress}"
                                )
                            }

                            is DownloadState.Success -> {
                                SafeLog.i(TAG, "chart downloadFile: $fileName, success")
                                parseFile(file.absolutePath)
                                SafeLog.i(TAG, "chart downloadFile, parseFile done")
                            }

                            is DownloadState.Error -> {
                                SafeLog.i(TAG, "chart downloadFile: $fileName, error")
                            }
                        }
                    }
                }
                list.add(job)
            }
            list.awaitAll()
            SafeLog.i(TAG, "chart downFile: All done!")
            loadServiceDataOver()
        }
    }

    /**
     * 解析文件内容
     */
    private suspend fun parseFile(filePath: String) {
        SafeLog.i(TAG, "chart parseFile()")
        try {
            val lastMinTime = max(minTimeMillis, serviceDataClearTimeMillis)
            val file = File(filePath)
            file.bufferedReader().useLines { lines ->
                lines.forEach { line ->
                    val result = mutableListOf<H71xxModeChartTable>()
                    val data = line.replace(";", "|").split("|")
                    SafeLog.i(TAG, line)
                    data.forEach { item ->
                        val numbers = item.split(",")
                        //只有时间戳大于清除时间的才是有效的数据
                        //小于清除时间的是被清除的数据，不应该再保存到本地
                        if (numbers[3].toLong() >= lastMinTime) {
                            val modeVal = numbers[0].toInt()
                            val temVal = numbers[1].toInt()
                            val humVal = numbers[2].toInt()
                            var timestamp = numbers[3].toLong()
                            //时间做整分钟处理
                            timestamp -= timestamp % 60000
                            result.add(
                                H71xxModeChartTable(
                                    timestamp = timestamp,
                                    sku = sku,
                                    device = device,
                                    dataFrom = H71xxModeChartTable.FROM_TYPE_CLOUD,
                                    tem = temVal,
                                    hum = humVal,
                                    mode = modeVal
                                )
                            )
                        }
                    }
                    chartDao.saveChartDataList(result)
                    SafeLog.i(TAG, "chart parseFile() insert done, result size: ${result.size}")
                }
            }
            file.delete()
            SafeLog.i(TAG, "chart parseFile() complete")
        } catch (e: Exception) {
            SafeLog.i(TAG, "chart parseFile(), 文件解析出错：${e.message}")
        }
    }

    private fun loadServiceDataOver() {
        SafeLog.i(TAG, "loadServiceDataOver, 服务端数据加载完成, $opResult")
        isServicing = false
        opResult?.loadOver(true)
    }

    override fun getKey(): String {
        return "${sku}_${device}"
    }

    override fun loadServiceData4Compare(timeRange4Compare: Pair<Long, Long>) {}

    override fun getLoadEndTime(): Long {
        return 0L
    }

    override fun setLoadFromType(loadFromType: Int) {}

    override fun loadServiceData() {
        if (!AccountConfig.read().isHadToken) {
            loadServiceDataOver()
            return
        }
        isServicing = true
        loadChartData()
    }

    override fun destroy() {
        loadDataScope?.cancel()
        handler.removeCallbacksAndMessages(null)
    }

    override fun uploadData() {
        if (!AccountConfig.read().isHadToken) return
        //分享设备不能上传数据
        ShareController.queryIfSharedCall(sku, device) { isShared ->
            SafeLog.d(TAG) { "uploadData 是否分享设备，isShared:$isShared" }
            if (!isShared) {
                SafeLog.w(TAG) { "-----uploadData  " }
                h71xxChartViewModel.updateDataNew(sku, device) { result ->

                }
            }
        }
    }

    override fun uploadDataWithSubDevice() {

    }

    override fun inServicing(): Boolean {
        return isServicing
    }

    override fun setOpResult(opResult: IServiceDataOpResult?) {
        this.opResult = opResult
    }

    @JvmOverloads //Java调用也不用写默认值
    fun <T> CoroutineScope.request(
        block: suspend () -> BaseResponse<T>,
        resultState: MutableLiveData<ResultState<T>>? = null,
        success: ((T) -> Unit)? = null,
        error: ((AppException) -> Unit)? = null,
        isShowDialog: Boolean = false,
        loadingMessage: String = "",
        successMsg: ((msg: String) -> Unit)? = null,
        sucDispatcher: CoroutineContext = Dispatchers.Main,
        errorDispatcher: CoroutineContext = Dispatchers.Main
    ): Job {
        //如果需要弹窗 通知Activity/fragment弹窗
        return launch(Dispatchers.IO) {
            runCatching {
                if (isShowDialog) {
                    resultState?.postValue(ResultState.onAppLoading(loadingMessage))
                }
                withContext(Dispatchers.IO) {
                    //请求体
                    block()
                }
            }.onSuccess {
                //请求码回调
                runCatching {
                    //校验请求结果码是否正确，不正确会抛出异常走下面的onFailure
                    executeResponse(it) { t ->
                        //不走异常回调，需在这里解析，在上面若有异常resultState会postValue两次
                        resultState?.paresResult(it)
                        if (success != null) {
                            withContext(sucDispatcher) {
                                success(t)
                            }
                        }
                        successMsg?.let { msg ->
                            withContext(sucDispatcher) {
                                msg.invoke(it.getRespMsg())
                            }
                        }
                    }
                }.onFailure { e ->
                    //打印错误消息
                    SafeLog.e(TAG, e)
                    //失败回调
                    resultState?.paresException(e)
                    withContext(errorDispatcher) {
                        error?.invoke(ExceptionHandle.handleException(e))
                    }
                }
            }.onFailure {
                //网络请求异常 关闭弹窗
                //打印错误消息
                SafeLog.e(TAG, it)
                //失败回调
                resultState?.paresException(it)
                withContext(errorDispatcher) {
                    error?.invoke(ExceptionHandle.handleException(it))
                }
            }
        }
    }
}