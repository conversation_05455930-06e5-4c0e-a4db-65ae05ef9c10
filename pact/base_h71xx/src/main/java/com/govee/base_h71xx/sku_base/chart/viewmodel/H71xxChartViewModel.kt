package com.govee.base_h71xx.sku_base.chart.viewmodel

import android.text.TextUtils
import androidx.lifecycle.viewModelScope
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.net.ModeData
import com.govee.base2newth.net.RequestUploadData
import com.govee.base2newth.net.RequestUploadModeInfo
import com.govee.base_h71xx.network.ChartUploadData
import com.govee.base_h71xx.network.chartApiService
import com.govee.db.dao.AccountDatabase
import com.govee.db.table.H71xxModeChartTable
import com.govee.home.account.config.AccountConfig
import com.govee.mvvm.GeneralViewModel
import com.govee.mvvm.ext.executeResponse
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.StreamUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.launch
import java.io.ByteArrayOutputStream

/**
 * Create by yu on 2023/9/20
 */
class H71xxChartViewModel : GeneralViewModel<H71xxChartRepository>() {
    private val chartDao = AccountDatabase.getInstance(AccountConfig.read().accountId)
            .getH71xxModeChartDao()

    fun updateData(sku: String, device: String, callBack: (Boolean) -> Unit) {
        viewModelScope.launch(Dispatchers.IO) {
            kotlin.runCatching {
                val temHums = chartDao
                    .getFormDevice(
                        sku,
                        device,
                        H71xxModeChartTable.FROM_TYPE_DEVICE,
                        ThConsV1.th_data_5_day_num
                    )
                if (temHums.isEmpty()) return@launch
                val size = temHums.size
                var index = 0
                /*使用流的方式转换发送的字符串*/
                val baos = ByteArrayOutputStream()
                /*构建本地未上报数据请求, 记录待上传的任务*/
                val modeDataList = mutableListOf<ModeData>()
                var lastModeGear: Int? = null
                for (temHum in temHums) {
                    baos.write(temHum.tem.toString().toByteArray())
                    baos.write(",".toByteArray())
                    baos.write(temHum.hum.toString().toByteArray())
                    baos.write(",".toByteArray())
                    baos.write(temHum.timestamp.toString().toByteArray())
                    index++
                    if (index != size) {
                        baos.write("|".toByteArray())
                    }
                    if (lastModeGear != temHum.mode) {
                        lastModeGear = temHum.mode
                        modeDataList.add(ModeData(temHum.mode, temHum.timestamp))
                    }
                }
                val thDataFromDevice: String = baos.toString()
                StreamUtil.closeStream(baos)
                if (TextUtils.isEmpty(thDataFromDevice)) return@launch
                val requestUploadData =
                    RequestUploadData(
                        Transactions().createTransaction(),
                        sku,
                        device,
                        thDataFromDevice
                    )
                val requestUploadModeInfo =
                    RequestUploadModeInfo(
                        Transactions().createTransaction(),
                        sku,
                        device,
                        modeDataList
                    )
                /* ️注意：后台针对模式重复数据上传会做过滤，但是温湿度计不会针对重复数据过滤，所以这边先上传模式成功再上传温湿度计数据（本地仅一个数据来源），成功回调再更改本地数据库数据来源 */
                val resultMode = repository.uploadModeRecordData(requestUploadModeInfo)
                executeResponse(resultMode) {

                    val resultTemHum = repository.uploadData(requestUploadData)
                    executeResponse(resultTemHum) {
                        if (isActive) {
                            temHums.forEach {
                                AccountDatabase.getInstance(AccountConfig.read().accountId)
                                    .getH71xxModeChartDao().updateForm(
                                        sku, device, it.timestamp,
                                        H71xxModeChartTable.FROM_TYPE_UPLOAD
                                    )
                            }
                            callBack.invoke(true)
                        }


                    }
                }
            }.onFailure {
                callBack.invoke(false)
            }


        }

    }

    fun updateDataMergeMode(sku: String, device: String, callBack: (Boolean) -> Unit) {
        viewModelScope.launch(Dispatchers.IO) {
            kotlin.runCatching {
                val temHums = chartDao.getFormDevice(
                    sku,
                    device,
                    H71xxModeChartTable.FROM_TYPE_DEVICE,
                    ThConsV1.th_data_5_day_num
                )
                if (temHums.isEmpty()) return@launch
                val size = temHums.size
                var index = 0
                /*使用流的方式转换发送的字符串*/
                val baos = ByteArrayOutputStream()
                var thDataFromDevice: String? = null
                /*构建本地未上报数据请求, 记录待上传的任务*/
                for (temHum in temHums) {
                    baos.write(temHum.mode.toString().toByteArray())
                    baos.write(",".toByteArray())
                    baos.write(temHum.tem.toString().toByteArray())
                    baos.write(",".toByteArray())
                    baos.write(temHum.hum.toString().toByteArray())
                    baos.write(",".toByteArray())
                    baos.write(temHum.timestamp.toString().toByteArray())
                    index++
                    if (index != size) {
                        baos.write("|".toByteArray())
                    }
                }
                thDataFromDevice = baos.toString()
                StreamUtil.closeStream(baos)
                if (TextUtils.isEmpty(thDataFromDevice)) return@launch
                val data = ChartUploadData(sku, device, thDataFromDevice)
                val result = chartApiService.uploadChartData(data)
                executeResponse(result) {
                    if (isActive) {
                        temHums.forEach {
                            chartDao.updateForm(
                                sku, device, it.timestamp,
                                H71xxModeChartTable.FROM_TYPE_UPLOAD
                            )
                        }
                        callBack.invoke(true)
                    }
                }
            }.onFailure {
                callBack.invoke(false)
            }
        }
    }

    fun updateDataNew(sku: String, device: String, callBack: (Boolean) -> Unit) {
        viewModelScope.launch(Dispatchers.IO) {
            kotlin.runCatching {
                val temHums = chartDao.getFormDevice(
                    sku,
                    device,
                    H71xxModeChartTable.FROM_TYPE_DEVICE,
                    ThConsV1.th_data_5_day_num
                )
                SafeLog.i(TAG, "----------h71xxChart, size: ${temHums.size}")
                if (temHums.isEmpty()) return@launch
                val size = temHums.size
                var index = 0
                /*使用流的方式转换发送的字符串*/
                val baos = ByteArrayOutputStream()
                var thDataFromDevice: String? = null
                /*构建本地未上报数据请求, 记录待上传的任务*/
                for (temHum in temHums) {
                    baos.write(temHum.mode.toString().toByteArray())
                    baos.write(",".toByteArray())
                    baos.write(temHum.tem.toString().toByteArray())
                    baos.write(",".toByteArray())
                    baos.write(temHum.hum.toString().toByteArray())
                    baos.write(",".toByteArray())
                    baos.write(temHum.timestamp.toString().toByteArray())
                    index++
                    if (index != size) {
                        baos.write("|".toByteArray())
                    }
                }
                thDataFromDevice = baos.toString()
                StreamUtil.closeStream(baos)
                if (TextUtils.isEmpty(thDataFromDevice)) return@launch
                val data = ChartUploadData(sku, device, thDataFromDevice)
                val result = chartApiService.uploadChartData(data)
                executeResponse(result) {
                    SafeLog.i(TAG, "-------h71xx result: ${result}")
                    if (isActive) {
                        temHums.forEach {
                            chartDao.updateForm(
                                sku, device, it.timestamp,
                                H71xxModeChartTable.FROM_TYPE_UPLOAD
                            )
                        }
                        callBack.invoke(true)
                    }
                }
            }.onFailure {
                callBack.invoke(false)
            }
        }
    }

    fun updatePm25Data(sku: String, device: String, callBack: (Boolean) -> Unit){
        SafeLog.e(TAG){"--------->>>updatePm25Data"}
        viewModelScope.launch(Dispatchers.IO) {
            kotlin.runCatching {
                val temHums = chartDao.getFormDevice(
                    sku,
                    device,
                    H71xxModeChartTable.FROM_TYPE_DEVICE,
                    ThConsV1.th_data_5_day_num
                )
                SafeLog.i(TAG, "----------h71xxChart, size: ${temHums.size}")
                if (temHums.isEmpty()) return@launch
                val size = temHums.size
                var index = 0
                /*使用流的方式转换发送的字符串*/
                val baos = ByteArrayOutputStream()
                var thDataFromDevice: String? = null
                /*构建本地未上报数据请求, 记录待上传的任务*/
                for (temHum in temHums) {
                    baos.write(temHum.mode.toString().toByteArray())
                    baos.write(",".toByteArray())
                    baos.write(temHum.pm25.toString().toByteArray())
                    baos.write(",".toByteArray())
                    baos.write(temHum.timestamp.toString().toByteArray())
                    index++
                    if (index != size) {
                        baos.write("|".toByteArray())
                    }
                }
                thDataFromDevice = baos.toString()
                StreamUtil.closeStream(baos)
                if (TextUtils.isEmpty(thDataFromDevice)) return@launch
                val data = ChartUploadData(sku, device, thDataFromDevice)
                val result = chartApiService.uploadChartData(data)
                executeResponse(result) {
                    SafeLog.i(TAG, "-------h71xx result: ${result}")
                    if (isActive) {
                        temHums.forEach {
                            chartDao.updateForm(
                                sku, device, it.timestamp,
                                H71xxModeChartTable.FROM_TYPE_UPLOAD
                            )
                        }
                        callBack.invoke(true)
                    }
                }
            }.onFailure {
                callBack.invoke(false)
            }
        }
    }
}