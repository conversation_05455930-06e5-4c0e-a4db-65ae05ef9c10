package com.govee.h6092;


import com.govee.base2home.main.choose.BleProcessorManager;
import com.govee.base2home.scenes.CmdBuilderManager;
import com.govee.base2home.sku.ModelMaker;
import com.govee.base2light.config.DynamicDomainConfig;
import com.govee.h6092.add.AddDeviceM;
import com.govee.h6092.add.BleBroadcastProcessor;
import com.govee.h6092.newDetail.h6092.H6092DetailConfig;
import com.govee.h6092.newDetail.h6093.H6093DetailConfig;
import com.govee.h6092.newDetail.h6094.H6094DiyConfig;
import com.govee.h6092.newDetail.h6095.H6095DetailConfig;
import com.govee.h6092.newDetail.h6095.diy.H6095DiyConfig;
import com.govee.h6092.newDetail.h609d.H609dDetailConfig;
import com.govee.h6092.newDetail.h609d.diy.H609dDiyConfig;
import com.govee.h6092.newDetail.h7070.H7070DetailConfig;
import com.govee.h6092.newDetail.h7071.H7071DetailConfig;
import com.govee.h6092.pact.OnlineMusicConfig;
import com.govee.h6092.pact.OnlineMusicConfigH6093;
import com.govee.h6092.pact.OnlineMusicConfigH6094;
import com.govee.h6092.pact.OnlineMusicConfigH6095;
import com.govee.h6092.pact.OnlineMusicConfigH609d;
import com.govee.h6092.pact.Register4Item;
import com.govee.h6092.pact.SubMaker;
import com.govee.h6092.pact.Support;
import com.govee.h6092.scenes.BleBrightnessBuilderV1;
import com.govee.h6092.scenes.BleHeartCmdBuilderV1;
import com.govee.h6092.scenes.BleSwitchCmdBuilderV1;
import com.govee.h6092.scenes.IotBrightnessCmdBuilderV1;
import com.govee.h6092.scenes.IotSwitchCmdBuilderV1;
import com.govee.lifecycle.annotation.AppLifecycle;
import com.govee.onlinesound.OnlineMusicM;
import com.ihoment.base2app.IApplication;

/**
 * Create by hey on 2020/11/24
 * $
 */
@AppLifecycle
public class H6092ApplicationImp implements IApplication {
    @Override
    public void create() {
        AddDeviceM.addDeviceH609D();
        AddDeviceM.addDeviceH7073();
        /*item的构建*/
        Register4Item.INSTANCE.register();
        ModelMaker.getInstance().addMaker(new SubMaker());
        /*发现设备后操作逻辑*/
        BleProcessorManager.getInstance().addProcessor(new BleBroadcastProcessor());

        /*添加默认的支持产品类型sku*/
        Support.addSupportPact();

        /*scenes场景支持*/
        CmdBuilderManager.getInstance().registerBleCmdBuilder(
                new BleBrightnessBuilderV1(),
                new BleHeartCmdBuilderV1(),
                new BleSwitchCmdBuilderV1()
        );
        CmdBuilderManager.getInstance().registerCmdBuilder(
                new IotBrightnessCmdBuilderV1(),
                new IotSwitchCmdBuilderV1()
        );

        OnlineMusicM.INSTANCE.setOnlineMusicConfig(new OnlineMusicConfig());
        OnlineMusicM.INSTANCE.setOnlineMusicConfig(new OnlineMusicConfigH6093());
        OnlineMusicM.INSTANCE.setOnlineMusicConfig(new OnlineMusicConfigH6095());
        OnlineMusicM.INSTANCE.setOnlineMusicConfig(new OnlineMusicConfigH6094());
        OnlineMusicM.INSTANCE.setOnlineMusicConfig(new OnlineMusicConfigH609d());

        /*支持https配置动态域名*/
        DynamicDomainConfig.getInstance().setSupportHttps(Support.H6092);
        DynamicDomainConfig.getInstance().setSupportHttps(Support.H6093);

        //Matter配置
        MatterHelper.INSTANCE.init();
        //H7070新版详情页配置
        H7070DetailConfig.INSTANCE.addConfig();
        H7070DetailConfig.INSTANCE.configDiyEdit();

        //H6092新版详情页配置
        H6092DetailConfig.INSTANCE.addConfig();
        H6092DetailConfig.INSTANCE.configDiyEdit();

        //H6093新版详情页配置
        H6093DetailConfig.INSTANCE.addConfig();
        H6093DetailConfig.INSTANCE.configDiyEdit();

        //H6095新版详情页配置
        H6095DetailConfig.INSTANCE.addConfig();
        H6095DiyConfig.INSTANCE.configDiyEdit();
        H6094DiyConfig.INSTANCE.configDiyEdit();

        //H609D新版详情页配置
        H609dDetailConfig.addConfig();
        H609dDiyConfig.build();

        //H7071新版详情页配置
        H7071DetailConfig.INSTANCE.addConfig();
        H7071DetailConfig.INSTANCE.configDiyEdit();
    }
}