apply plugin: 'com.android.library'
apply plugin: 'com.jakewharton.butterknife'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

android {
    namespace 'com.govee.chassislightv1'
    compileSdk COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                        MODULE_NAME: project.getName()
                ]
            }
        }
    }

    buildTypes {
        release {
            isDefault = RELEASE_BUILD_VARIANT_IS_DEFAULT
            minifyEnabled false
            consumerProguardFiles = ['proguard-rules.pro']
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            isDefault = !RELEASE_BUILD_VARIANT_IS_DEFAULT
            minifyEnabled false
            consumerProguardFiles = ['proguard-rules.pro']
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }

    /*资源前缀*/
    resourcePrefix "chlv1_"

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    /*base2light*/
    implementation project(':base2light')
    /*butterknife注解实现*/
    kapt 'com.jakewharton:butterknife-compiler:' + BUTTERKNIFE_VERSION
    // AppLifecycle
    implementation 'com.govee.lifecycle:lifecycle-api:' + lifecycle_api_version
    kapt 'com.govee.lifecycle:lifecycle-compiler:' + lifecycle_compiler_version
}
