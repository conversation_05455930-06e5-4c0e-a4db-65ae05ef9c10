<?xml version="1.0" encoding="utf-8"?>
<com.zhy.android.percent.support.PercentRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/ac_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/ui_bg_color_style_1">

    <TextView
        android:id="@+id/top_flag"
        android:layout_width="match_parent"
        android:layout_height="0dp" />

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/btn_back"
        android:layout_alignBottom="@+id/btn_back"
        android:gravity="center"
        android:text="@string/h5072_ads_title"
        android:textColor="@color/font_style_105_textColor"
        android:textSize="@dimen/font_style_105_textSize" />

    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_below="@+id/top_flag"
        android:background="@drawable/component_btn_style_23"
        android:contentDescription="@null"
        android:padding="5dp"
        android:src="@mipmap/new_sensor_setting_icon_arrow_left"
        app:layout_heightPercent="9.0667%w"
        app:layout_marginLeftPercent="3.2%w"
        app:layout_marginStartPercent="3.2%w"
        app:layout_marginTopPercent="8.1333%w"
        app:layout_widthPercent="9.0667%w" />

    <com.govee.base2home.custom.NestedScrollViewV1
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/btn_back"
        app:layout_marginTopPercent="7.6%w">

        <com.zhy.android.percent.support.PercentRelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/device_info_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_alignParentTop="true"
                android:gravity="center_vertical"
                android:lines="1"
                android:text="@string/device_info_label"
                android:textColor="@color/font_style_70_1_textColor"
                android:textSize="@dimen/font_style_70_1_textSize"
                app:layout_minHeightPercent="5.3333%w"
                app:layout_paddingLeftPercent="6.1333%w"
                app:layout_paddingRightPercent="6.1333%w" />

            <com.zhy.android.percent.support.PercentRelativeLayout
                android:id="@+id/device_name_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/device_info_label"
                android:layout_centerHorizontal="true"
                android:background="@drawable/component_bg_style_4"
                app:layout_marginTopPercent="2.6667%w"
                app:layout_widthPercent="93.0667%w">

                <TextView
                    android:id="@+id/device_name_label"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/device_info_label"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:text="@string/device_name_hint"
                    android:textColor="@color/font_style_70_2_textColor"
                    android:textSize="@dimen/font_style_70_2_textSize"
                    app:layout_marginTopPercent="4.0115%w"
                    app:layout_minHeightPercent="5.7307%w"
                    app:layout_paddingLeftPercent="4.2980%w"
                    app:layout_paddingRightPercent="4.2980%w" />

                <com.govee.base2home.custom.ClearEditText
                    android:id="@+id/device_name_edit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/device_name_label"
                    android:layout_toStartOf="@+id/btn_device_name_cancel"
                    android:gravity="center_vertical"
                    android:inputType="text"
                    android:lines="1"
                    android:textColor="@color/font_style_70_3_textColor"
                    android:textColorHint="@color/font_style_28_4_textColor"
                    android:textSize="@dimen/font_style_70_3_textSize"
                    app:cet_clear_drawable="@drawable/component_btn_close"
                    app:cet_clear_drawable_height="27dp"
                    app:cet_clear_drawable_width="27dp"
                    app:cet_input_limit="22"
                    app:layout_marginBottomPercent="4.0115%w"
                    app:layout_marginEndPercent="2.8653%w"
                    app:layout_marginRightPercent="2.8653%w"
                    app:layout_marginTopPercent="1.4327%w"
                    app:layout_minHeightPercent="7.7364%w"
                    app:layout_paddingLeftPercent="4.2980%w" />

                <TextView
                    android:id="@+id/btn_device_name_done"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_alignParentEnd="true"
                    android:gravity="center"
                    android:text="@string/done"
                    android:textColor="@color/component_text_color_70_7_8"
                    android:textSize="@dimen/font_style_70_7_textSize"
                    app:layout_heightPercent="11.4613%w"
                    app:layout_marginEndPercent="2.8653%w"
                    app:layout_marginRightPercent="2.8653%w"
                    app:layout_marginTopPercent="9.3123%w"
                    app:layout_minWidthPercent="14.3266%w" />

                <TextView
                    android:id="@+id/btn_device_name_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_alignTop="@+id/btn_device_name_done"
                    android:layout_toStartOf="@+id/btn_device_name_done"
                    android:gravity="center"
                    android:text="@string/cancel"
                    android:textColor="@color/component_text_color_70_7_8"
                    android:textSize="@dimen/font_style_70_7_textSize"
                    app:layout_heightPercent="11.4613%w"
                    app:layout_marginEndPercent="2.8653%w"
                    app:layout_marginRightPercent="2.8653%w"
                    app:layout_minWidthPercent="14.3266%w" />
            </com.zhy.android.percent.support.PercentRelativeLayout>

            <TextView
                android:id="@+id/other_info_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/device_name_container"
                android:gravity="center_vertical"
                android:lines="1"
                android:text="@string/bbq_preset_others"
                android:textColor="@color/font_style_70_1_textColor"
                android:textSize="@dimen/font_style_70_1_textSize"
                app:layout_marginBottomPercent="2.6667%w"
                app:layout_marginTopPercent="4.8%w"
                app:layout_minHeightPercent="5.3333%w"
                app:layout_paddingLeftPercent="6.1333%w"
                app:layout_paddingRightPercent="6.1333%w" />

            <com.govee.ui.component.DeviceLockView
                android:id="@+id/device_lock"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/other_info_label"
                app:layout_marginBottomPercent="4.8%w" />

            <com.zhy.android.percent.support.PercentRelativeLayout
                android:id="@+id/rlCheckLocation"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/device_lock"
                android:visibility="visible">

                <TextView
                    android:id="@+id/bg_guide"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_alignBottom="@+id/device_base_info_container"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    android:background="@drawable/component_bg_style_4"
                    app:layout_widthPercent="93.0667%w" />

                <com.zhy.android.percent.support.PercentLinearLayout
                    android:id="@+id/on_off_memory"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    app:layout_paddingLeftPercent="5.3333%w"
                    app:layout_paddingRightPercent="6.1333%w">

                    <ImageView
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_sensor_setting_icon_power_off"
                        app:layout_heightPercent="16%w"
                        app:layout_widthPercent="13.3333%w" />

                    <TextView
                        android:id="@+id/bulb_setting_label"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_toEndOf="@+id/on_off_switch"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="@string/b2light_power_off_memory"
                        android:textColor="@color/font_style_70_4_textColor"
                        android:textSize="@dimen/font_style_70_4_textSize"
                        app:layout_minHeightPercent="8%w"
                        app:layout_paddingLeftPercent="1.8667%w" />

                    <TextView
                        android:id="@+id/tvMemoryContent"
                        android:layout_width="wrap_content"
                        android:layout_height="match_parent"
                        android:layout_gravity="center_horizontal"
                        android:gravity="center_vertical"
                        android:textColor="@color/font_style_21_4_textColor"
                        android:textSize="@dimen/font_style_21_2_textSize"
                        app:layout_marginRightPercent="2.5333%w" />

                    <ImageView
                        android:id="@+id/on_off_switch"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_update_list_arrow"
                        app:layout_heightPercent="8%w"
                        app:layout_widthPercent="8%w" />

                </com.zhy.android.percent.support.PercentLinearLayout>

                <include
                    android:id="@+id/line_soft_version"
                    layout="@layout/component_dividing_line"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/on_off_memory"
                    android:layout_centerHorizontal="true"
                    app:layout_widthPercent="93.0667%w" />

                <com.govee.ui.component.DeviceBaseInfoViewV1NoRoundBg
                    android:id="@+id/device_base_info_container"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/line_soft_version" />

            </com.zhy.android.percent.support.PercentRelativeLayout>


            <TextView
                android:id="@+id/btn_delete"
                style="@style/compoent_btn_style_1"
                android:layout_below="@+id/rlCheckLocation"
                android:layout_centerHorizontal="true"
                android:background="@drawable/component_btn_style_6"
                android:text="@string/text_delete_device"
                android:textColor="@color/ui_btn_style_6_1_text_color"
                android:textSize="@dimen/ui_btn_style_6_1_text_size"
                app:layout_marginBottomPercent="1.0667%w"
                app:layout_marginTopPercent="8%w"
                app:layout_minWidthPercent="54.1333%w"
                app:layout_paddingLeftPercent="19.6%w"
                app:layout_paddingRightPercent="19.6%w" />
        </com.zhy.android.percent.support.PercentRelativeLayout>
    </com.govee.base2home.custom.NestedScrollViewV1>


</com.zhy.android.percent.support.PercentRelativeLayout>