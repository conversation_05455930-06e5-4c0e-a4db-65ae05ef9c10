package com.govee.chassislightv1.ble;

import com.govee.base2light.ble.controller.AbsMode;
import com.govee.chassislightv1.adjust.h7092.color.SubModeColorH7092;

/**
 * Create by hey on 2021/3/9
 * $ 判断
 */
public class Mode4ColorStripV2 extends AbsMode {
    public boolean isLastController;

    @Override
    protected void parseSubMode(byte subModeType, byte[] subModeValidBytes) {
        if (subModeType == BleProtocol.sub_mode_mic) {
            subMode = new SubModeMic();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_scenes) {
            subMode = new SubModeScenes();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_diy) {
            subMode = new SubModeNewDiy();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_color_v2) {
            subMode = new SubModeColorH7092();
            subMode.parse(subModeValidBytes);
        } else {
            /*默认都按照颜色模式解析*/
            subMode = new SubModeColor();
            subMode.parse(subModeValidBytes);
        }
    }
}