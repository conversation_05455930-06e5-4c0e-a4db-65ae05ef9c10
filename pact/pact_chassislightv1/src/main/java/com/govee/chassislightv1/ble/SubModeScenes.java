package com.govee.chassislightv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.ISubModeScenes;
import com.govee.base2light.light.ModeStr;
import com.ihoment.base2app.infra.StorageInfra;

import androidx.annotation.NonNull;

/**
 * Create by linshicong on 2020/3/18
 */
public class SubModeScenes extends AbsSubMode4Analytic implements ISubModeScenes {
    private int effect = BleUtil.getUnsignedByte(BleProtocol.value_sub_mode_scenes_movie);/*默认效果-电影*/

    public static int goodsType;//为了解决h6184与h7092共用同一个scenes场景缓存bug

    @Override
    public void loadLocal() {
        SubModeScenes subModeScenes = StorageInfra.get(goodsType + "", SubModeScenes.class);
        if (subModeScenes == null) return;
        this.effect = subModeScenes.effect;
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(goodsType + "", this);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_scenes;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr = ModeStr.getScenesSubModeStr(effect);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_scenes, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        effect = BleUtil.getSignedShort(validBytes[1], validBytes[0]);
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] effectBytes = BleUtil.getSignedBytesFor2(effect, false);
        return new byte[]{subModeCommandType(), effectBytes[0], effectBytes[1]};
    }

    public int getEffect() {
        return effect;
    }

    public void setEffect(int effect) {
        this.effect = effect;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_scenes;
    }
}