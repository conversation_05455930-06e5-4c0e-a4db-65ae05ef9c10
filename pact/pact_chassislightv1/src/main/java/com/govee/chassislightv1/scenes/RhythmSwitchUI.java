package com.govee.chassislightv1.scenes;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.ble.controller.AbsController;
import com.govee.base2light.rhythm.ui.AbsRhythmSwitchUI;
import com.govee.chassislightv1.pact.Comm;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by linshicong on 11/30/20
 */
public class RhythmSwitchUI extends AbsRhythmSwitchUI {
    public RhythmSwitchUI(AppCompatActivity ac, DeviceModel deviceModel, OnSelectChangeListener onSelectChangeListener) {
        super(ac, deviceModel, onSelectChangeListener);
    }

    @Nullable
    @Override
    protected AbsCmd makeRuleCmd() {
        return null;
    }

    @Nullable
    @Override
    protected AbsController makeBleCmd() {
        return Comm.makeSwitchController4BleComm(open == 1);
    }
}