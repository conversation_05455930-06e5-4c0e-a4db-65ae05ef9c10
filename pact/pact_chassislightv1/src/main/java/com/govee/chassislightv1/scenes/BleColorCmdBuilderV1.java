package com.govee.chassislightv1.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.ColorModel;
import com.govee.chassislightv1.pact.Comm;

/**
 * Create by linshicong on 2020/3/18
 */
public class BleColorCmdBuilderV1 extends AbsBleCmdBuilderV1<ColorModel> {
    @Override
    public IBleCmd createCmd(ColorModel colorModel) {
        return () -> Comm.makeColorController4BleComm(colorModel.goodsType, colorModel.pactType, colorModel.pactCode, colorModel.model.versionSoft, colorModel.model.versionHard, colorModel.color).getValue();
    }
}