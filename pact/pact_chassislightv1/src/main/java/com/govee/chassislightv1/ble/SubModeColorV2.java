package com.govee.chassislightv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.chassislightv1.ConsV1;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


/**
 * Create by hey on 2021/2/4
 * $ 支持分段亮度
 */
public class SubModeColorV2 implements ISubMode {
    public int rgb = 0;
    public int ctRgb;

    public boolean[] ctlLight = new boolean[7];
    public int[] rgbSet;

    public int gradual = 1;/*默认开启渐变*/

    public int[] brightnessSet;
    public int brightness;

    public int kelvin;
    public int opType;

    @Override
    public void loadLocal() {
    }

    @Override
    public void saveLocal() {
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr;
        if (kelvin != 0) {
            subModeStr = ParamFixedValue.other;
        } else {
            subModeStr = UtilColor.colorName(rgb);
        }
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_color, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        this.gradual = BleUtil.getUnsignedByte(validBytes[0]);
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] bytes = new byte[17];
        bytes[0] = subModeCommandType();
        if (this.brightness == 0 && this.brightnessSet == null) {
            bytes[1] = (byte) 0x01;
            int[] rgb = ColorUtils.getRgb(this.rgb);
            bytes[2] = (byte) rgb[0];
            bytes[3] = (byte) rgb[1];
            bytes[4] = (byte) rgb[2];
            if (this.kelvin != 0) {
                byte[] signedBytesFor2 = BleUtil.getSignedBytesFor2(kelvin, true);
                bytes[5] = signedBytesFor2[0];
                bytes[6] = signedBytesFor2[1];
                int[] ctRgb = ColorUtils.getRgb(this.ctRgb);
                bytes[7] = (byte) ctRgb[0];
                bytes[8] = (byte) ctRgb[1];
                bytes[9] = (byte) ctRgb[2];
            }
            bytes[10] = (byte) 0x00;
            int temp = 1;
            for (int i = 0; i < ConsV1.bulb_num_max_v2; i++) {
                if (ctlLight[i]) {
                    bytes[10] = (byte) (bytes[10] | temp);
                }
                temp = temp << 1;
            }
            bytes[11] = (byte) 0x00;
        } else if (this.brightnessSet != null) {
            bytes[1] = (byte) 0x03;
            for (int i = 0; i < brightnessSet.length; i++) {
                bytes[i + 2] = (byte) brightnessSet[i];
            }
        } else {
            bytes[1] = (byte) 0x02;
            bytes[2] = (byte) this.brightness;
            bytes[3] = (byte) 0x00;
            int temp = 1;
            for (int i = 0; i < ConsV1.bulb_num_max_v2; i++) {
                if (ctlLight[i]) {
                    bytes[3] = (byte) (bytes[3] | temp);
                }
                temp = temp << 1;
            }
            bytes[4] = (byte) 0x00;
        }
        return bytes;
    }

    public static AbsSingleController[] makeSubModeColor(Colors colorStrip) {
        int[] colors = colorStrip.colorSet;
        if (colors.length != 7) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        boolean hasBrightness = colorStrip.brightnessSet != null && colorStrip.brightnessSet.length == 7;
        AbsSingleController[] modeControllers = new AbsSingleController[hasBrightness ? hashMap.size() + 1 : hashMap.size()];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[7];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hasBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hasBrightness) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.brightnessSet = colorStrip.brightnessSet;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i] = modeController;
        }
        return modeControllers;
    }

    public static AbsSingleController[] makeSubModeColor(int[] colors, int[] brightnessArray, boolean isFade, boolean hadFadeController) {
        if (colors == null || colors.length != 7) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        boolean hadBrightness = brightnessArray != null && brightnessArray.length == 7;
        int fadeSize = hadFadeController ? 1 : 0;
        AbsSingleController[] modeControllers = new AbsSingleController[hadBrightness ? hashMap.size() + fadeSize + 1 : hashMap.size() + fadeSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[7];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hadBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadBrightness) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.brightnessSet = brightnessArray;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            modeControllers[i] = new Gradual4BleWifiController(isFade);
        }
        return modeControllers;
    }

    public static SubModeColorV2 makeSubModeColor(int color) {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        subModeColor.rgb = color;
        subModeColor.kelvin = 0;
        subModeColor.ctRgb = 0;
        int length = subModeColor.ctlLight.length;
        int[] rgbSet = new int[length];
        for (int i = 0; i < length; i++) {
            subModeColor.ctlLight[i] = true;
            rgbSet[i] = color;
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }

}