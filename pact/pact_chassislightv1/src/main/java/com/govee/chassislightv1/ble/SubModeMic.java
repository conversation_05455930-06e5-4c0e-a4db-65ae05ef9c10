package com.govee.chassislightv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.ISubMode;
import com.ihoment.base2app.infra.StorageInfra;

/**
 * Create by lins<PERSON>ong on 2020/3/19
 */
public class SubModeMic implements ISubMode {
    private boolean autoColor = false;/*是否自动颜色*/
    private int rgb = 0xFFFF0000;/*颜色-默认红色*/

    public void setRgb(int rgb) {
        this.rgb = rgb;
    }

    public int getRgb() {
        return rgb;
    }

    @Override
    public void loadLocal() {
        SubModeMic subModeMic = StorageInfra.get(SubModeMic.class);
        if (subModeMic == null) return;
        this.autoColor = subModeMic.autoColor;
        this.rgb = subModeMic.rgb;
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(this);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_mic;
    }

    @Override
    public String getAnalyticModeName() {
        return ParamFixedValue.mode_music;
    }

    @Override
    public void parse(byte[] validBytes) {
        autoColor = BleUtil.getUnsignedByte(validBytes[1]) == 0;
        if (!autoColor) {
            int r = BleUtil.getUnsignedByte(validBytes[2]);
            int g = BleUtil.getUnsignedByte(validBytes[3]);
            int b = BleUtil.getUnsignedByte(validBytes[4]);
            rgb = ColorUtils.toColor(r, g, b);
        }
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] bytes = new byte[6];
        bytes[0] = subModeCommandType();
        bytes[1] = BleProtocol.value_sub_mode_mic_set_mode;
        bytes[2] = (byte) (autoColor ? 0 : 1);
        if (!autoColor) {
            int[] rgb = ColorUtils.getRgb(this.rgb);
            bytes[3] = (byte) rgb[0];
            bytes[4] = (byte) rgb[1];
            bytes[5] = (byte) rgb[2];
        }
        return bytes;
    }

    public SubModeMic copy() {
        SubModeMic subModeMic = new SubModeMic();
        subModeMic.autoColor = autoColor;
        subModeMic.rgb = rgb;
        return subModeMic;
    }
}