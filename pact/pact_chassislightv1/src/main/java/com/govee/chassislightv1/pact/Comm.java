package com.govee.chassislightv1.pact;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.sku.Colors;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.comm.MultipleBleBytes;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.HeartController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultiDiyGraffitiController;
import com.govee.base2light.ble.controller.MultipleDiyController;
import com.govee.base2light.ble.controller.MultipleDiyControllerV1;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.util.NumUtil;
import com.govee.chassislightv1.adjust.h7092.color.SubModeColorH7092;
import com.govee.chassislightv1.ble.Mode;
import com.govee.chassislightv1.ble.ModeController;
import com.govee.chassislightv1.ble.SubModeColor;
import com.govee.chassislightv1.ble.SubModeColorV2;
import com.govee.chassislightv1.ble.SubModeNewDiy;
import com.govee.chassislightv1.ble.SubModeScenes;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by xieyingwu on 2021/10/21
 * 指令生成类$
 */
public final class Comm {
    private Comm() {
    }

    /**
     * 生成亮度指令-ble
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param brightness
     * @return
     */
    public static AbsSingleController makeBrightnessController4BleComm(int goodsType, int pactType, int pactCode, int brightness) {
        boolean bk = Support.isBk(goodsType, pactType, pactCode);
        int newBrightness;
        if (bk) {
            newBrightness = brightness;
        } else {
            newBrightness = NumUtil.calculateProgress(254, 20, brightness);
        }
        return new BrightnessController(newBrightness);
    }

    /**
     * 生成颜色指令-ble
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param versionHard
     * @param color
     * @return
     */
    public static AbsSingleController makeColorController4BleComm(int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, int color) {
        boolean supportSubModeColor4PartBrightness = Support.supportSubModeColor4PartBrightness(goodsType, pactType, pactCode, versionSoft, versionHard);
        ISubMode subMode;
        if (supportSubModeColor4PartBrightness) {
            if (goodsType == GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092) {
                subMode = SubModeColorH7092.makeSubModeColor(color);
            } else {
                subMode = SubModeColorV2.makeSubModeColor(color);
            }
        } else {
            subMode = SubModeColor.makeSubModeColorByColor(color);
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        return new ModeController(mode);
    }
    /**
     * 生成颜色指令-ble
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param versionHard
     * @param colors
     * @return
     */
    public static AbsSingleController[] makeColorStripController4BleComm(int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, Colors colors) {
        boolean supportSubModeColor4PartBrightness = Support.supportSubModeColor4PartBrightness(goodsType, pactType, pactCode, versionSoft, versionHard);
        if (supportSubModeColor4PartBrightness) {
            if (goodsType == GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092) {
                return SubModeColorH7092.makeSubModeColor(colors, 8);
            }
            return SubModeColorV2.makeSubModeColor(colors);
        }
        return null;
    }

    /**
     * 生成色温指令-ble
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param versionHard
     * @param temColor
     * @return
     */
    public static AbsSingleController makeColorTemController4BleComm(int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, int temColor) {
        boolean supportSubModeColor4PartBrightness = Support.supportSubModeColor4PartBrightness(goodsType, pactType, pactCode, versionSoft, versionHard);
        ISubMode subMode;
        if (supportSubModeColor4PartBrightness) {
            if (goodsType == GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092) {
                subMode = SubModeColorH7092.makeSubModeColor(temColor);
            } else {
                subMode = SubModeColorV2.makeSubModeColor(temColor);
            }
        } else {
            subMode = SubModeColor.makeSubModeColorByColorTem(temColor);
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        return new ModeController(mode);
    }

    /**
     * 生成心跳指令-ble
     *
     * @return
     */
    public static AbsSingleController makeHeartController4BleComm() {
        return new HeartController();
    }

    /**
     * 生成开关指令-ble
     *
     * @param open
     * @return
     */
    public static AbsSingleController makeSwitchController4BleComm(boolean open) {
        return new SwitchController(open);
    }

    /**
     * 生成场景蓝牙指令集合-ble
     *
     * @param sku
     * @param device
     * @param versionSoft
     * @param sceneCode
     * @return
     */
    public static List<String> makeScene4BleComm(String sku,String device, String versionSoft, int sceneCode, int goodsType) {
        List<byte[]> bytes = new ArrayList<>();
        int newVersion = Support.getScenesModeVersion(versionSoft, goodsType);
        AbsMultipleControllerV14Scenes multiScene = Support.isMultiScene(sku, device, sceneCode, newVersion);
        if (multiScene != null) {
            List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(multiScene);
            bytes.addAll(multipleWriteBytes);
        }
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.setEffect(sceneCode);
        Mode mode = new Mode();
        mode.subMode = subModeScenes;
        ModeController modeController = new ModeController(mode);
        bytes.add(modeController.getValue());
        return BleUtil.makeBleComm2Str(bytes);
    }

    /**
     * 生成diy指令-iot
     *
     * @param supportPactV1
     * @param diyProtocol
     * @param diyGraffiti4Rgbic
     * @param diyAi
     * @return
     */
    public static List<String> makeDiy4BleComm(boolean supportPactV1, DiyProtocol diyProtocol, DiyGraffitiV2 diyGraffiti4Rgbic, DiyStudio diyStudio, DiyAi diyAi) {
        if (diyProtocol != null) {
            if (supportPactV1) {
                MultipleDiyController multipleDiyController = new MultipleDiyController(diyProtocol);
                List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytes(multipleDiyController);
                SubModeNewDiy subModeNewDiy = new SubModeNewDiy(diyProtocol.getDiyCode());
                Mode mode = new Mode();
                mode.subMode = subModeNewDiy;
                ModeController modeController = new ModeController(mode);
                multipleWriteBytes.add(modeController.getValue());
                return BleUtil.makeBleComm2Str(multipleWriteBytes);
            } else {
                MultipleDiyControllerV1 multipleDiyController = new MultipleDiyControllerV1(diyProtocol);
                List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(multipleDiyController);
                SubModeNewDiy subModeNewDiy = new SubModeNewDiy(diyProtocol.getDiyCode());
                Mode mode = new Mode();
                mode.subMode = subModeNewDiy;
                ModeController modeController = new ModeController(mode);
                multipleWriteBytes.add(modeController.getValue());
                return BleUtil.makeBleComm2Str(multipleWriteBytes);
            }
        }
        if (diyGraffiti4Rgbic != null) {
            MultiDiyGraffitiController controller = new MultiDiyGraffitiController(diyGraffiti4Rgbic.getDiyCode(), diyGraffiti4Rgbic.getEffectBytes());
            List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(controller);
            SubModeNewDiy subModeNewDiy = new SubModeNewDiy(diyGraffiti4Rgbic.getDiyCode());
            Mode mode = new Mode();
            mode.subMode = subModeNewDiy;
            ModeController modeController = new ModeController(mode);
            multipleWriteBytes.add(modeController.getValue());
            return BleUtil.makeBleComm2Str(multipleWriteBytes);
        }
        if (diyStudio != null) {
            AbsMultipleControllerV14DiyTemplate scenes4RgbicV1 = diyStudio.toMulti4Scenes4RgbicV1(1);
            if (scenes4RgbicV1 == null) return null;
            List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(scenes4RgbicV1);
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(diyStudio.scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            multipleWriteBytes.add(modeController.getValue());
            return BleUtil.makeBleComm2Str(multipleWriteBytes);
        }
        if (diyAi != null) {
            return diyAi.command4PtReal.getCommands4IotPtReal();
        }
        return null;
    }

}