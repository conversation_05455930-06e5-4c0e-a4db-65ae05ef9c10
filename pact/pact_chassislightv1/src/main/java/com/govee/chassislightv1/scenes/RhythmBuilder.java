package com.govee.chassislightv1.scenes;

import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.rhythm.AbsRhythmEffectUi;
import com.govee.base2light.rhythm.ui.AbsEffectUI;
import com.govee.chassislightv1.pact.Support;


/**
 * Create by l<PERSON><PERSON><PERSON> on 11/27/20
 * 昼夜节律
 */
public class RhythmBuilder implements AbsRhythmEffectUi {
    @Override
    public AbsEffectUI getEffectUI() {
        return new RhythmEffectUI();
    }

    @Override
    public boolean supportFuc(DeviceModel deviceModel) {
        int goodsType = deviceModel.getGoodsType();
        int[] rhythmGoodsTypes = Support.effect4bleGoodsTypeArray;
        for (int rhythmGoodsType : rhythmGoodsTypes) {
            if (goodsType == rhythmGoodsType) return true;
        }
        return false;
    }
}