package com.govee.chassislightv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.IMusicEffectStatic;
import com.govee.base2light.ble.controller.ISubMode;

/**
 * Create by l<PERSON><PERSON><PERSON> on 2020/3/19
 */
public class SubModeMicV2 implements ISubMode {

    @Override
    public void loadLocal() {
    }

    @Override
    public void saveLocal() {

    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_music_new;
    }

    @Override
    public String getAnalyticModeName() {
        return ParamFixedValue.mode_music;
    }

    @Override
    public void parse(byte[] validBytes) {
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] bytes = new byte[2];
        bytes[0] = subModeCommandType();
        bytes[1] = IMusicEffectStatic.single_value_sub_mic;
        return bytes;
    }
}