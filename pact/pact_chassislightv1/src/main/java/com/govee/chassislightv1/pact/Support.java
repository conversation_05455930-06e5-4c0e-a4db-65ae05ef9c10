package com.govee.chassislightv1.pact;

import com.govee.base2home.pact.BleUtil;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.Pact;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2light.ac.diy.DiyM;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.ble.scenes.ScenesM;
import com.govee.base2light.rhythm.RhythmOpM;
import com.govee.chassislightv1.scenes.RhythmBuilder;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by linshicong on 2020/3/18
 */
public final class Support {
    private static final String TAG = "Support";
    private static final String H6184 = "H6184";
    private static final String H7092 = "H7092";


    private Support() {
    }

    public static List<Protocol> supportProtocolsV1 = new ArrayList<>();
    public static List<Protocol> supportProtocolsV2 = new ArrayList<>();
    public static List<Protocol> supportProtocolsV3 = new ArrayList<>();//h7092

    public static void addSupportPact() {
        Pact pact = Pact.getInstance;
        Protocol protocol = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_CH_LIGHT_BLE_V1_1, GoodsType.PACT_CODE_4_CH_LIGHT_BLE_V1_1);
        supportProtocolsV1.add(protocol);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1, protocol);
        /*BK方案*/
        Protocol protocolV2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_CH_LIGHT_BLE_V1_BK, GoodsType.PACT_CODE_4_CH_LIGHT_BLE_V1_BK);
        supportProtocolsV2.add(protocolV2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1, protocolV2);

        Protocol protocolV3 = GoodsType.beProtocol(GoodsType.PACT_TYPE_CAR_LIGHT_h7092, GoodsType.PACT_CODE_CAR_LIGHT_h7092);
        supportProtocolsV3.add(protocolV3);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092, protocolV3);

        /*初始化支持的默认sku图*/
        ThemeM.getInstance.addDefSkuRes(H6184, com.govee.chassislightv1.R.mipmap.add_list_type_device_6184);
        ThemeM.getInstance.addDefSkuRes(H7092, com.govee.chassislightv1.R.mipmap.add_list_type_device_6184);
        /*注册支持的diyOp*/
        DiyM.getInstance.addDiyOp(EffectOp.getInstance);
        /*昼夜节律*/
        RhythmOpM.getInstance.registerRhythmOp(new RhythmBuilder());

    }

    /**
     * 设备列表的goodsType集合
     */
    public static final int[] deviceItemGoodsTypes = {
            GoodsType.GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1,
            GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092,
    };

    /**
     * 针对灯效op操作的ble的goodsType
     */
    public static final int[] effect4bleGoodsTypeArray = {
            GoodsType.GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1,
            GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092,
    };

    public static boolean supportPactV1(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1) {
            for (Protocol pro : supportProtocolsV1) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    public static boolean supportPactV2(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1) {
            for (Protocol pro : supportProtocolsV2) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092) {//h7092
            for (Protocol pro : supportProtocolsV3) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    public static final String[] supportBleV1GoodsSet = {
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092)
    };

    /**
     * 是否是BK协议
     */
    public static boolean isBk(int goodsType, int pactType, int pactCode) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1) {
            return pactType >= GoodsType.PACT_TYPE_4_CH_LIGHT_BLE_V1_BK &&
                    pactCode >= GoodsType.PACT_CODE_4_CH_LIGHT_BLE_V1_BK;
        }
        if (isGoodsTypeH7092(goodsType)) {
            //h7092不是bk协议  但走bk条件逻辑
            return true;
        }
        return false;
    }

    /**
     * 获取最大的灯带段数
     */
    public static int getBulbStringMaxNum(int goodsType, int pactType, int pactCode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getBulbStringMaxNum() goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode);
        }
        if (isGoodsTypeH7092(goodsType)) {
            return 8;
        }
        return 7;
    }

    private static final String hard_version_v0_new_color_mode = "1.00.01";
    private static final String soft_version_v0_new_color_mode = "1.06.00";

    /**
     * 是否支持带分段亮度控制
     */
    public static boolean supportSubModeColor4PartBrightness(int goodsType, int pactType, int pactCode, String versionSoft, String versionHard) {
        /*支持bk设备默认支持分段亮度*/
        if (isBk(goodsType, pactType, pactCode)) return true;
        if (isGoodsTypeH7092(goodsType)) return true;//h7092 支持分段亮度
        int curVersionHardInt = BleUtil.parseVersion(versionHard);
        int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
        if (curVersionHardInt <= 0 || curVersionSoftInt <= 0) return false;
        int compareVersionHardInt = BleUtil.parseVersion(hard_version_v0_new_color_mode);
        int compareVersionSoftInt = BleUtil.parseVersion(soft_version_v0_new_color_mode);
        return curVersionHardInt >= compareVersionHardInt && curVersionSoftInt >= compareVersionSoftInt;
    }

    public static int getDiyVersion(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getDiyVersion() sku = " + sku + " ; goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode + " ; versionSoft = " + versionSoft + " ; versionHard = " + versionHard);
        }
        if (isGoodsTypeH7092(goodsType)) return 2;
        boolean supportSubModeColor4PartBrightness = supportSubModeColor4PartBrightness(goodsType, pactType, pactCode, versionSoft, versionHard);
        return supportSubModeColor4PartBrightness ? 1 : 0;
    }

    private static final String soft_version_support_service_scenes = "1.07.00";

    /**
     * 是否支持服务器配置场景 - BK下默认都支持，telink下1.06.00支持
     */
    public static boolean supportServiceScenes(String versionSoft, int goodsType) {
        if (isGoodsTypeH7092(goodsType)) {
            return true;
        }
        int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
        if (curVersionSoftInt <= 0) return false;
        int compareVersionSoftInt = BleUtil.parseVersion(soft_version_support_service_scenes);
        return curVersionSoftInt >= compareVersionSoftInt;
    }

    /**
     * 获取支持抽象场景的版本
     */
    public static int getScenesModeVersion(String versionSoft, int goodsType) {
        boolean supportServiceScenes = supportServiceScenes(versionSoft, goodsType);
        if (isGoodsTypeH7092(goodsType)) {
            return 0;
        }
        return supportServiceScenes ? 1 : 0;
    }

    /**
     * 获取支持的场景op操作集合
     */
    public static int[] getSupportScenesOpSet(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard) {
        boolean supportServiceScenes = supportServiceScenes(versionSoft, goodsType);
        if (!supportServiceScenes) return null;
        if (isGoodsTypeH7092(goodsType)) {
            return new int[]{
                    ScenesOp.scene_type_op_static,
                    ScenesOp.scene_type_op_rgb,
                    ScenesOp.scene_type_op_rgbic_v0,
                    ScenesOp.scene_type_op_compose,
            };
        }
        return new int[]{
                ScenesOp.scene_type_op_static,
                ScenesOp.scene_type_op_rgb,
                ScenesOp.scene_type_op_rgbic_v0,
                ScenesOp.scene_type_op_rgbic_v1,
        };
    }

    public static AbsMultipleControllerV14Scenes isMultiScene(String sku,String device, int effect, int newVersion) {
        if (H7092.equals(sku)) {
            return isMultiSceneH7092(sku, device, effect);
        }
        if (newVersion == 1) {
            CategoryV1.SceneV1 scene = ScenesM.getInstance.getSceneV1(sku,device, effect);
            if (scene != null) {
                return ScenesOp.parseSceneV1(sku, scene, ScenesOp.scene_type_rgb, ScenesOp.scene_type_rgbic);
            }
            return ScenesOp.parseEffect(sku, effect, 1, 2);
        }
        return null;
    }

    public static AbsMultipleControllerV14Scenes isMultiSceneH7092(String sku, String device, int effect) {
        CategoryV1.SceneV1 sceneV1 = ScenesM.getInstance.getSceneV1(sku, effect);
        if (sceneV1 != null) {
            return ScenesOp.parseSceneV1(sku, sceneV1, ScenesOp.scene_type_static, ScenesOp.scene_type_rgb, ScenesOp.scene_type_rgbic, ScenesOp.scene_type_compose);
        }
        return ScenesOp.parseEffect(sku, effect, 1, 2);
    }

    /**
     * 是否支持AIDiy
     */
    public static boolean supportAiDiy(String softVersion, int goodsType) {
        //h7092 不支持AiDiy
        if (isGoodsTypeH7092(goodsType)) {
            return false;
        }
        return supportServiceScenes(softVersion, goodsType);
    }

    public static boolean isGoodsTypeH7092(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092;
    }

}
