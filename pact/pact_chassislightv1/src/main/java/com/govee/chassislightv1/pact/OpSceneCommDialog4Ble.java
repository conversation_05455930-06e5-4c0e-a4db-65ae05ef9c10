package com.govee.chassislightv1.pact;

import android.content.Context;

import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultiNewScenes;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.light.AbsOpCommDialog4Ble;
import com.govee.chassislightv1.ble.Ble;
import com.govee.chassislightv1.ble.Mode;
import com.govee.chassislightv1.ble.ModeController;
import com.govee.chassislightv1.ble.SubModeScenes;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by linshicong on 1/28/21
 * op color操作应用$
 */
public class OpSceneCommDialog4Ble extends AbsOpCommDialog4Ble {
    private AbsMultipleControllerV14Scenes controllerV14Scenes;

    protected OpSceneCommDialog4Ble(Context context, String bleAddress, String bleName, @NonNull EffectData.ShareEffect shareEffect) {
        super(context, bleAddress, bleName, EFFECT_TYPE_SCENES, -1);
        int parseVersion = shareEffect.parseVersion;
        String effectStr = shareEffect.effectStr;
        if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgb) {
            /*rgb场景*/
            controllerV14Scenes = ScenesOp.parseShareEffect(ScenesOp.scenes_code_share_rgb, effectStr);
        } else if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v0
                || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v1) {
            /*rgbic场景*/
            controllerV14Scenes = ScenesOp.parseShareEffect(ScenesOp.scenes_code_share_rgbic, effectStr);
        }
    }

    protected OpSceneCommDialog4Ble(Context context, String bleAddress, String bleName, int type, @NonNull PtRealController controller) {
        super(context, bleAddress, bleName, EFFECT_TYPE_DIY, type);
        this.ptRealController = controller;
    }

    @Override
    protected void bleOping() {
        if (controllerV14Scenes != null) {
            getBle().sendMultipleControllerV1(controllerV14Scenes);
            return;
        }
        if (ptRealController != null) {
            getBle().sendMultipleController4PtReal(ptRealController);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleNewScenes(EventMultiNewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleNewScenes() result = " + result + " ; scenesCode = " + scenesCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*场景效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*diy效果传输失败，则通知设置新场景模式失败*/
            getBle().clearControllers();
            updateResult(false);
            hide();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    public static void showDialog(Context context, String bleAddress, String bleName, @NonNull EffectData.ShareEffect shareEffect) {
        new OpSceneCommDialog4Ble(context, bleAddress, bleName, shareEffect).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, int type, @NonNull PtRealController controller) {
        new OpSceneCommDialog4Ble(context, bleAddress, bleName, type, controller).show();
    }
}