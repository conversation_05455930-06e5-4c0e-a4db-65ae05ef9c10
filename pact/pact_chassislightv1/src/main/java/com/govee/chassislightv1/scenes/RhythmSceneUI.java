package com.govee.chassislightv1.scenes;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.light.IScenes4BleRhythm;
import com.govee.base2light.rhythm.ui.AbsRhythmSceneUI;
import com.govee.chassislightv1.pact.Comm;

import java.util.List;

/**
 * Create by lins<PERSON><PERSON> on 11/30/20
 */
public class RhythmSceneUI extends AbsRhythmSceneUI {

    public RhythmSceneUI(AppCompatActivity ac, IScenes4BleRhythm iScenes, DeviceModel deviceModel, OnSelectChangeListener selectChangeListener) {
        super(ac, iScenes, deviceModel, selectChangeListener);
    }

    public RhythmSceneUI(AppCompatActivity ac, DeviceModel deviceModel, OnSelectChangeListener selectChangeListener) {
        super(ac, deviceModel, selectChangeListener);
    }

    @Nullable
    @Override
    protected AbsCmd makeRuleCmd() {
        return null;
    }

    @Nullable
    @Override
    protected List<String> makeBleCmd() {
        return Comm.makeScene4BleComm(deviceModel.getSku(), deviceModel.device, deviceModel.versionSoft, code, deviceModel.getGoodsType());
    }

}