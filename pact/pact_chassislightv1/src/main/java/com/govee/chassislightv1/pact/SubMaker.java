package com.govee.chassislightv1.pact;

import com.govee.base2home.sku.IMaker;
import com.govee.base2home.sku.ISkuItem;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by linshicong on 2020/3/16
 * SubMaker
 */
public class SubMaker implements IMaker {
    private List<ISkuItem> makers = new ArrayList<>();

    public SubMaker() {
        makers.add(new BleSkuItem());
        makers.add(new BleSkuItemH7092());
    }

    @Override
    public List<ISkuItem> getSupportMakers() {
        return makers;
    }
}