package com.govee.chassislightv1.ble;

import com.govee.base2light.ble.comm.AbsBleComm;

import java.util.UUID;

/**
 * Create by linshicong on 2020/3/18
 */
public class BleComm extends AbsBleComm {
    public static final UUID serviceUuid = UUID.fromString("00010203-0405-0607-0809-0a0b0c0d1910");
    public static final UUID characteristicUuid = UUID.fromString("00010203-0405-0607-0809-0a0b0c0d2b11");

    @Override
    public UUID getServiceUuid() {
        return serviceUuid;
    }

    @Override
    public UUID getCharacteristicUuid() {
        return characteristicUuid;
    }
}
