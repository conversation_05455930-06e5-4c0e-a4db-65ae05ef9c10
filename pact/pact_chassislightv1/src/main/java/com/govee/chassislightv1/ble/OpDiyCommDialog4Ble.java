package com.govee.chassislightv1.ble;

import android.content.Context;

import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultipleDiy;
import com.govee.base2light.ble.controller.MultipleDiyController;
import com.govee.base2light.ble.controller.MultipleDiyControllerV1;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.light.AbsOpCommDialog4Ble;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/8/12
 * op diy操作应用$
 */
public class OpDiyCommDialog4Ble extends AbsOpCommDialog4Ble {
    private MultipleDiyController multipleDiyController;
    private MultipleDiyControllerV1 multipleDiyControllerV1;

    protected OpDiyCommDialog4Ble(Context context, String bleAddress, String bleName, @NonNull DiyProtocol diyProtocol, int type, boolean bk) {
        super(context, bleAddress, bleName, EFFECT_TYPE_DIY, type);
        if (bk) {
            multipleDiyControllerV1 = new MultipleDiyControllerV1(diyProtocol);
        } else {
            multipleDiyController = new MultipleDiyController(diyProtocol);
        }
    }

    protected OpDiyCommDialog4Ble(Context context, String bleAddress, String bleName, @NonNull PtRealController ptRealController, int type) {
        super(context, bleAddress, bleName, EFFECT_TYPE_DIY, type);
        this.ptRealController = ptRealController;
    }

    @Override
    protected void bleOping() {
        if (multipleDiyControllerV1 != null) {
            getBle().sendMultipleControllerV1(multipleDiyControllerV1);
            return;
        }
        if (multipleDiyController != null) {
            getBle().sendMultipleController(multipleDiyController);
            return;
        }
        if (ptRealController != null) {
            getBle().sendMultipleController4PtReal(ptRealController);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleDiy(EventMultipleDiy event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiy() result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(event.getDiyCode());
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            getBle().clearControllers();
            updateResult(false);
            hide();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    public static void showDialog(Context context, String bleAddress, String bleName, @NonNull DiyProtocol diyProtocol, int type, boolean bk) {
        new OpDiyCommDialog4Ble(context, bleAddress, bleName, diyProtocol, type, bk).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, @NonNull PtRealController controller, int type) {
        new OpDiyCommDialog4Ble(context, bleAddress, bleName, controller, type).show();
    }
}