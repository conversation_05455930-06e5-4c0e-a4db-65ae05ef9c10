package com.govee.chassislightv1.adjust;

import com.govee.base2light.ac.diy.DiyM;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v1.EffectCodes;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by linshicong on 2020/3/18
 * diy效果信息
 * <p>淡入淡出</p>
 * <p>跳跃</p>
 * <p>闪烁</p>
 * <p>跑马灯</p>
 * <p>音乐</p>
 * <p>流水</p>
 * <p>渐变流水</p>
 * <p>混合</p>
 * <p>有二级分类</p>
 */
public class Diy {

    private Diy() {
    }

    private static DiySupportV1 supportV1;
    private static DiySupportV1 supportV1_1;
    private static DiySupportV1 supportV2;
    private static DiySupportV1 supportV2_1;
    private static DiySupportV1 supportH7092;

    public static DiySupportV1 getDiySupport(int newVersion, boolean supportAiDiy) {
        if (newVersion == 1) return getDiySupportV2(supportAiDiy);
        if (newVersion == 2) return getDiySupportH7092();
        return getDiySupport(supportAiDiy);
    }

    static DiySupportV1 getDiySupportV2(boolean supportAiDiy) {
        if (supportAiDiy) {
            if (supportV2_1 != null) return supportV2_1;
            supportV2_1 = makeDiySupportV2(true);
            return supportV2_1;
        }
        if (supportV2 != null) return supportV2;
        supportV2 = makeDiySupportV2(false);
        return supportV2;
    }

    private static DiySupportV1 makeDiySupportV2(boolean supportAiDiy) {
        DiySupportV1 diySupportV1 = new DiySupportV1();
        DiySupportV1.Effect effectFade = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_fade,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );
        DiySupportV1.Effect effectJump = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jump,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                });
        DiySupportV1.Effect effectTwinkling = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_twinkling,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );
        DiySupportV1.Effect effectMarquee = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_marquee,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_whole,
                        DiyM.EffectSubCode.diy_sub_effect_code_gather,
                        DiyM.EffectSubCode.diy_sub_effect_code_separate,
                }
        );
        DiySupportV1.Effect effectWater = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_water,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );
        DiySupportV1.Effect effectRainbow = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_rainbow,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );
        DiySupportV1.Effect effectChase = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_chase[0],
                DiyM.EffectCode.diy_effect_code_chase[1]
        );
        DiySupportV1.Effect effectMix = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_mix[0],
                DiyM.EffectCode.diy_effect_code_mix[1]
        );

        DiySupportV1.Effect effectGraffiti = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_graffiti_4_rgbic,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle,
                        DiyM.EffectSubCode.diy_sub_effect_code_fade,
                        DiyM.EffectSubCode.diy_sub_effect_code_blinking,
                        DiyM.EffectSubCode.diy_sub_effect_code_breath
                }
        );
        effectGraffiti.setSpecialDiyGraffiti4Rgbic();
        /*效果*/
        ArrayList<DiySupportV1.Effect> effects = new ArrayList<>();
        effects.add(effectGraffiti);
        effects.add(effectFade);
        effects.add(effectJump);
        effects.add(effectTwinkling);
        effects.add(effectMarquee);
        effects.add(effectWater);
        effects.add(effectRainbow);
        effects.add(effectChase);
        effects.add(effectMix);
        diySupportV1.effects = effects;
        /*混合支持效果*/
        ArrayList<DiySupportV1.Effect> mixEffects = new ArrayList<>();
        mixEffects.add(effectFade);
        mixEffects.add(effectJump);
        mixEffects.add(effectTwinkling);
        mixEffects.add(effectMarquee);
        mixEffects.add(effectWater);
        mixEffects.add(effectRainbow);
        diySupportV1.mixEffects = mixEffects;
        diySupportV1.mix4EffectsNum = 4;
        /*颜色数量支持*/
        ArrayList<DiySupportV1.Effect4Color> effect4Colors = new ArrayList<>();
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_water[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 3));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 8));
        diySupportV1.effect4Colors = effect4Colors;
        /*速度调节*/
        ArrayList<DiySupportV1.Effect4Speed> effect4Speeds = new ArrayList<>();
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_water[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_graffiti_4_rgbic[0], 100));
        diySupportV1.effect4Speeds = effect4Speeds;
        /*效果code码*/
        List<Integer> flagCodes;
        if (supportAiDiy) {
            flagCodes = DiyM.getInstance.getFlagCodes(effects,
                    DiyM.EffectCode.diy_effect_code_rgb_from_studio,
                    DiyM.EffectCode.diy_effect_code_rgbic_v0_from_studio,
                    DiyM.EffectCode.diy_effect_code_rgbic_v1_from_studio
            );
        } else {
            flagCodes = DiyM.getInstance.getFlagCodes(effects);
        }
        diySupportV1.effectCodes = new EffectCodes(flagCodes, DiyM.getInstance.getFlagCodes(mixEffects));
        return diySupportV1;
    }

    static DiySupportV1 getDiySupport(boolean supportAiDiy) {
        if (supportAiDiy) {
            if (supportV1_1 != null) return supportV1_1;
            supportV1_1 = makeDiySupport(true);
            return supportV1_1;
        }
        if (supportV1 != null) return supportV1;
        supportV1 = makeDiySupport(false);
        return supportV1;
    }

    private static DiySupportV1 makeDiySupport(boolean supportAiDiy) {
        DiySupportV1 diySupportV1 = new DiySupportV1();
        DiySupportV1.Effect effectFade = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_fade,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );
        DiySupportV1.Effect effectJump = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jump,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                });
        DiySupportV1.Effect effectTwinkling = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_twinkling,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );
        DiySupportV1.Effect effectMarquee = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_marquee,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_whole,
                        DiyM.EffectSubCode.diy_sub_effect_code_gather,
                        DiyM.EffectSubCode.diy_sub_effect_code_separate,
                }
        );
        DiySupportV1.Effect effectWater = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_water,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );
        DiySupportV1.Effect effectRainbow = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_rainbow,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );
        DiySupportV1.Effect effectChase = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_chase[0],
                DiyM.EffectCode.diy_effect_code_chase[1]
        );
        DiySupportV1.Effect effectMix = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_mix[0],
                DiyM.EffectCode.diy_effect_code_mix[1]
        );

        /*效果*/
        ArrayList<DiySupportV1.Effect> effects = new ArrayList<>();
        effects.add(effectFade);
        effects.add(effectJump);
        effects.add(effectTwinkling);
        effects.add(effectMarquee);
        effects.add(effectWater);
        effects.add(effectRainbow);
        effects.add(effectChase);
        effects.add(effectMix);
        diySupportV1.effects = effects;
        /*混合支持效果*/
        ArrayList<DiySupportV1.Effect> mixEffects = new ArrayList<>();
        mixEffects.add(effectFade);
        mixEffects.add(effectJump);
        mixEffects.add(effectTwinkling);
        mixEffects.add(effectMarquee);
        mixEffects.add(effectWater);
        mixEffects.add(effectRainbow);
        diySupportV1.mixEffects = mixEffects;
        diySupportV1.mix4EffectsNum = 4;
        /*颜色数量支持*/
        ArrayList<DiySupportV1.Effect4Color> effect4Colors = new ArrayList<>();
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_water[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 3));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 8));
        diySupportV1.effect4Colors = effect4Colors;
        /*速度调节*/
        ArrayList<DiySupportV1.Effect4Speed> effect4Speeds = new ArrayList<>();
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_water[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 100));
        diySupportV1.effect4Speeds = effect4Speeds;
        /*效果code码*/
        List<Integer> flagCodes;
        if (supportAiDiy) {
            flagCodes = DiyM.getInstance.getFlagCodes(effects,
                    DiyM.EffectCode.diy_effect_code_rgb_from_studio,
                    DiyM.EffectCode.diy_effect_code_rgbic_v0_from_studio,
                    DiyM.EffectCode.diy_effect_code_rgbic_v1_from_studio
            );
        } else {
            flagCodes = DiyM.getInstance.getFlagCodes(effects);
        }
        diySupportV1.effectCodes = new EffectCodes(flagCodes, DiyM.getInstance.getFlagCodes(mixEffects));
        return diySupportV1;
    }

    static DiySupportV1 getDiySupportH7092() {
        if (supportH7092 != null) return supportH7092;
        supportH7092 = makeDiySupportH7092(false);
        return supportH7092;
    }

    private static DiySupportV1 makeDiySupportH7092(boolean supportAiDiy) {
        DiySupportV1 diySupportV1 = new DiySupportV1();
        /*淡入淡出*/
        DiySupportV1.Effect effectFade = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_fade,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part
                }
        );
        /*呼吸*/
        DiySupportV1.Effect effectBreath = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_breath,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );
        /*跳跃*/
        DiySupportV1.Effect effectJump = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jump,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                });
        /*闪烁*/
        DiySupportV1.Effect effectTwinkling = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_twinkling,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );
        /*跑马灯*/
        DiySupportV1.Effect effectMarquee = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_marquee,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_whole,
                        DiyM.EffectSubCode.diy_sub_effect_code_gather,
                        DiyM.EffectSubCode.diy_sub_effect_code_separate,
                }
        );
        /*流水*/
        DiySupportV1.Effect effectWater = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_water,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );
        /*彩虹*/
        DiySupportV1.Effect effectRainbow = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_rainbow,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );

        /*贪吃蛇*/
        DiySupportV1.Effect effectSnake = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_sub_effect_code_snake,
                null
        );
        /*堆积*/
        DiySupportV1.Effect effectStacking = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_accumulation,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );

        /*混合*/
        DiySupportV1.Effect effectMix = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_mix[0],
                DiyM.EffectCode.diy_effect_code_mix[1]
        );
        /*涂鸦*/
        DiySupportV1.Effect effectGraffiti = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_graffiti_4_rgbic,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle,
                        DiyM.EffectSubCode.diy_sub_effect_code_fade,
                        DiyM.EffectSubCode.diy_sub_effect_code_blinking,
                        DiyM.EffectSubCode.diy_sub_effect_code_breath
                }
        );
        effectGraffiti.setSpecialDiyGraffiti4Rgbic();
        /*效果*/
        ArrayList<DiySupportV1.Effect> effects = new ArrayList<>();
        effects.add(effectGraffiti);
        effects.add(effectFade);
        effects.add(effectBreath);
        effects.add(effectJump);
        effects.add(effectTwinkling);
        effects.add(effectMarquee);
        effects.add(effectWater);
        effects.add(effectRainbow);
        effects.add(effectSnake);
        effects.add(effectStacking);
        effects.add(effectMix);
        diySupportV1.effects = effects;
        /*混合支持效果*/
        ArrayList<DiySupportV1.Effect> mixEffects = new ArrayList<>();
        mixEffects.add(effectFade);
        mixEffects.add(effectBreath);
        mixEffects.add(effectJump);
        mixEffects.add(effectTwinkling);
        mixEffects.add(effectMarquee);
        mixEffects.add(effectWater);
        mixEffects.add(effectRainbow);
        mixEffects.add(effectSnake);
        mixEffects.add(effectStacking);
        diySupportV1.mixEffects = mixEffects;
        diySupportV1.mix4EffectsNum = 4;
        /*颜色数量支持*/
        ArrayList<DiySupportV1.Effect4Color> effect4Colors = new ArrayList<>();
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_breath[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_water[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeColorRange4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 8, 2));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_sub_effect_code_snake[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_accumulation[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 8));
        diySupportV1.effect4Colors = effect4Colors;
        /*速度调节*/
        ArrayList<DiySupportV1.Effect4Speed> effect4Speeds = new ArrayList<>();
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_breath[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_water[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_sub_effect_code_snake[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_accumulation[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_graffiti_4_rgbic[0], 100));
        diySupportV1.effect4Speeds = effect4Speeds;
        /*效果code码*/
        List<Integer> flagCodes;
        if (supportAiDiy) {
            flagCodes = DiyM.getInstance.getFlagCodes(effects,
                    DiyM.EffectCode.diy_effect_code_rgb_from_studio,
                    DiyM.EffectCode.diy_effect_code_rgbic_v0_from_studio,
                    DiyM.EffectCode.diy_effect_code_rgbic_v1_from_studio
            );
        } else {
            flagCodes = DiyM.getInstance.getFlagCodes(effects);
        }
        diySupportV1.effectCodes = new EffectCodes(flagCodes, DiyM.getInstance.getFlagCodes(mixEffects));
        return diySupportV1;
    }

}