package com.govee.chassislightv1.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.BrightnessModel;
import com.govee.chassislightv1.pact.Comm;

/**
 * Create by linshicong on 2020/3/18
 */
public class BleBrightnessBuilderV1 extends AbsBleCmdBuilderV1<BrightnessModel> {
    @Override
    public IBleCmd createCmd(BrightnessModel brightnessModel) {
        return () -> Comm.makeBrightnessController4BleComm(brightnessModel.goodsType, brightnessModel.pactType, brightnessModel.pactCode, brightnessModel.brightness).getValue();
    }
}