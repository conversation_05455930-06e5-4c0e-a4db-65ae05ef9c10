package com.govee.chassislightv1.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.ColorTempModel;
import com.govee.chassislightv1.pact.Comm;

/**
 * Create by lins<PERSON>ong on 2020/3/18
 */
public class BleColorTemCmdBuilderV1 extends AbsBleCmdBuilderV1<ColorTempModel> {
    @Override
    public IBleCmd createCmd(ColorTempModel colorTempModel) {
        return () -> Comm.makeColorTemController4BleComm(colorTempModel.goodsType, colorTempModel.pactType, colorTempModel.pactCode, colorTempModel.model.versionSoft, colorTempModel.model.versionHard, colorTempModel.temColor).getValue();
    }
}