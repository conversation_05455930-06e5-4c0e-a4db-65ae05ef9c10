package com.govee.chassislightv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.ISubMode;
import com.ihoment.base2app.infra.StorageInfra;

/**
 * Create by lins<PERSON><PERSON> on 2020/3/18
 */
public class SubModeColor implements ISubMode {
    private int rgb = 0xFFFF0000;/*默认颜色-红色*/
    private boolean ctOpen;
    private int ctRgb;

    @Override
    public void loadLocal() {
        SubModeColor subModeColor = StorageInfra.get(SubModeColor.class);
        if (subModeColor == null) return;
        this.rgb = subModeColor.rgb;
        this.ctOpen = subModeColor.ctOpen;
        this.ctRgb = subModeColor.ctRgb;
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(this);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_color;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr;
        if (ctOpen) {
            subModeStr = ParamFixedValue.other;
        } else {
            subModeStr = UtilColor.colorName(rgb);
        }
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_color, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        int r = BleUtil.getUnsignedByte(validBytes[0]);
        int g = BleUtil.getUnsignedByte(validBytes[1]);
        int b = BleUtil.getUnsignedByte(validBytes[2]);
        rgb = ColorUtils.toColor(r, g, b);
        ctOpen = validBytes[3] != 0;
        int ctR = BleUtil.getUnsignedByte(validBytes[4]);
        int ctG = BleUtil.getUnsignedByte(validBytes[5]);
        int ctB = BleUtil.getUnsignedByte(validBytes[6]);
        ctRgb = ColorUtils.toColor(ctR, ctG, ctB);
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] bytes = new byte[8];
        bytes[0] = subModeCommandType();
        int[] rgb = ColorUtils.getRgb(this.rgb);
        bytes[1] = (byte) rgb[0];
        bytes[2] = (byte) rgb[1];
        bytes[3] = (byte) rgb[2];
        bytes[4] = ctOpen ? (byte) 1 : (byte) 0;
        int[] ctRgb = ColorUtils.getRgb(this.ctRgb);
        bytes[5] = (byte) ctRgb[0];
        bytes[6] = (byte) ctRgb[1];
        bytes[7] = (byte) ctRgb[2];
        return bytes;
    }

    public int getRgb() {
        return rgb;
    }

    public void setRgb(int rgb) {
        this.rgb = rgb;
    }

    public static SubModeColor makeSubModeColorByColor(int color) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.ctOpen = false;
        subModeColor.ctRgb = 0;
        subModeColor.rgb = color;
        return subModeColor;
    }

    public static SubModeColor makeSubModeColorByColorTem(int colorTem) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.ctOpen = true;
        subModeColor.ctRgb = colorTem;
        subModeColor.rgb = 0xFFFFFFFF;
        return subModeColor;
    }
}
