package com.govee.chassislightv1.pact;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/8/13
 * $
 */
class EffectOp extends AbsEffectOp4Ble {
    @Override
    protected int[] supportGoodsType() {
        return Support.effect4bleGoodsTypeArray;
    }

    private EffectOp() {
    }

    private static class Builder {
        private static final EffectOp instance = new EffectOp();
    }

    public static EffectOp getInstance = Builder.instance;
}