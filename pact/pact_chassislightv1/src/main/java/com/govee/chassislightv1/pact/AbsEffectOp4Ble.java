package com.govee.chassislightv1.pact;

import android.content.Context;

import androidx.annotation.NonNull;

import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.pact.Command4PtReal;
import com.govee.base2home.pact.PtRealCommands;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2home.sku.SkuIcM;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.DiyShare;
import com.govee.base2light.ac.diy.IColorOp;
import com.govee.base2light.ac.diy.IDiyOp;
import com.govee.base2light.ac.diy.IRoomOp4Ble;
import com.govee.base2light.ac.diy.IRoomOp4Iot;
import com.govee.base2light.ac.diy.ISceneOp;
import com.govee.base2light.ac.diy.ISmartRoomOp;
import com.govee.base2light.ac.diy.ISwitchAndBrightnessOp;
import com.govee.base2light.ac.diy.local.ShareDiy;
import com.govee.base2light.ac.diy.v1.EffectCodes;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.HeartController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.mic.controller.MicSetRgbController;
import com.govee.base2light.util.NumUtil;
import com.govee.chassislightv1.adjust.Diy;
import com.govee.chassislightv1.adjust.h7092.color.SubModeColorH7092;
import com.govee.chassislightv1.ble.Ble;
import com.govee.chassislightv1.ble.BleComm;
import com.govee.chassislightv1.ble.MicController;
import com.govee.chassislightv1.ble.Mode;
import com.govee.chassislightv1.ble.ModeController;
import com.govee.chassislightv1.ble.OpColorCommDialog4Ble;
import com.govee.chassislightv1.ble.OpColorCommDialog4BleV1;
import com.govee.chassislightv1.ble.OpColorCommDialog4SquareBle;
import com.govee.chassislightv1.ble.OpColorCommDialog4SquareBleV1;
import com.govee.chassislightv1.ble.OpDiyCommDialog4Ble;
import com.govee.chassislightv1.ble.OpDiyCommDialog4BleV1;
import com.govee.chassislightv1.ble.OpDiyCommDialog4SquareBle;
import com.govee.chassislightv1.ble.OpDiyCommDialog4SquareBleV1;
import com.govee.chassislightv1.ble.SubModeColor;
import com.govee.chassislightv1.ble.SubModeColorV2;
import com.govee.chassislightv1.ble.SubModeMic;
import com.govee.chassislightv1.ble.SubModeMicV2;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JsonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Create by xieyingwu on 2020/8/12
 * diy的op操作$
 */
abstract class AbsEffectOp4Ble implements IDiyOp, IColorOp, ISceneOp, ISmartRoomOp, ISwitchAndBrightnessOp {
    private static final String TAG = "AbsEffectOp4Ble";

    @Override
    public String getKey() {
        StringBuilder sb = new StringBuilder();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            sb.append(goodsType).append("_");
        }
        return sb.toString();
    }

    /**
     * 支持的goodsType
     *
     * @return int
     */
    protected abstract int[] supportGoodsType();

    @Override
    public boolean support(@NonNull AbsDevice absDevice, int effectType) {
        int curGoodsType = absDevice.getGoodsType();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) return true;
        }
        return false;
    }

    @Override
    public boolean supportDiyEffect(@NonNull AbsDevice absDevice, int[] effectCodes) {
        int version = Support.getDiyVersion(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        boolean supportAiDiy = Support.supportAiDiy(absDevice.getVersionSoft(), absDevice.getGoodsType());
        EffectCodes effectCodesCur = Diy.getDiySupport(version, supportAiDiy).effectCodes;
        if (effectCodesCur != null) {
            return effectCodesCur.supportDiyEffect(effectCodes);
        }
        return false;
    }

    @Override
    public boolean applyDiyEffect(Context context, @NonNull AbsDevice absDevice, @NonNull DiyShare diyShare, boolean needConnect) {
        return applyDiyEffect(context, absDevice, diyShare.effectStr, diyShare.type, diyShare.effectCodes, needConnect);
    }

    private boolean applyDiyEffect(Context context, @NonNull AbsDevice absDevice, String effectStr, int type, int[] effectCodes, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        DiyProtocol diyProtocol = ShareDiy.parseDiyProtocol(effectStr);
        boolean bk = Support.isBk(absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode());
        if (diyProtocol != null) {
            if (needConnect) {
                OpDiyCommDialog4Ble.showDialog(context, ext.address, ext.bleName, diyProtocol, type, bk);
            } else {
                OpDiyCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, diyProtocol, type, bk);
            }
            return true;
        }

        DiyGraffitiV2 diyGraffiti = ShareDiy.parseDiyGraffiti4Rgbic(effectStr);
        if (diyGraffiti != null) {
            int ic = ext.ic;
            if (ic <= 0) {
                SkuIcM.getInstance().getDefIcNum(absDevice.getSku());
            }
            boolean support = diyGraffiti.checkDiyValue4RgbicGraffiti(ic);
            if (!support) return false;
            if (needConnect) {
                OpDiyCommDialog4BleV1.showDialog(context, ext.address, ext.bleName, diyGraffiti, type);
            } else {
                OpDiyCommDialog4SquareBleV1.showDialog(context, ext.address, ext.bleName, diyGraffiti, type);
            }
            return true;
        }

        /*来自Govee Studio*/
        AbsMultipleControllerV14DiyTemplate shareDiyStudio = ScenesOp.isShareDiyStudio(effectCodes, effectStr);
        if (shareDiyStudio != null) {
            if (needConnect) {
                OpDiyCommDialog4BleV1.showDialog(context, ext.address, ext.bleName, shareDiyStudio, type);
            } else {
                OpDiyCommDialog4SquareBleV1.showDialog(context, ext.address, ext.bleName, shareDiyStudio, type);
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean supportColorEffect(@NonNull AbsDevice absDevice, @NonNull EffectData.ColorEffect colorEffect) {
        if (Support.isGoodsTypeH7092(absDevice.getGoodsType())) {
            return colorEffect.isSingleColor() || colorEffect.colorSet.length == 8;
        }
        return colorEffect.isSingleColor() || colorEffect.colorSet.length == 7;
    }

    @Override
    public boolean applyColorEffect(Context context, @NonNull AbsDevice absDevice, @NonNull EffectData.ColorEffect colorEffect, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        boolean supportBrightness = Support.supportSubModeColor4PartBrightness(absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        if (colorEffect.isSingleColor()) {
            if (needConnect) {
                OpColorCommDialog4Ble.showDialog(context, ext.address, ext.bleName, colorEffect);
            } else {
                OpColorCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, colorEffect, absDevice.getGoodsType());
            }
        } else {
            if (supportBrightness) {
                if (needConnect) {
                    OpColorCommDialog4BleV1.showDialog(context, ext.address, ext.bleName, colorEffect, absDevice.getGoodsType());
                } else {
                    OpColorCommDialog4SquareBleV1.showDialog(context, ext.address, ext.bleName, colorEffect, absDevice.getGoodsType());
                }
            }
            return supportBrightness;
        }
        return true;
    }

    @Override
    public boolean applySwitch(Context context, @NonNull AbsDevice absDevice, boolean on) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        OpColorCommDialog4SquareBleV1.showDialog(context, ext.address, ext.bleName, on);
        return true;
    }

    @Override
    public boolean applySwitch(Context context, @NonNull AbsDevice absDevice, boolean on, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpColorCommDialog4BleV1.showDialog(context, ext.address, ext.bleName, on, absDevice.getGoodsType());
        } else {
            OpColorCommDialog4SquareBleV1.showDialog(context, ext.address, ext.bleName, on);
        }
        return true;
    }

    @Override
    public boolean applyBrightness(Context context, @NonNull AbsDevice absDevice, int brightness4Percent) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        OpColorCommDialog4SquareBleV1.showDialog(context, ext.address, ext.bleName, absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), brightness4Percent);
        return true;
    }

    @Override
    public boolean applyBrightness(Context context, @NonNull AbsDevice absDevice, int brightness4Percent, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpColorCommDialog4BleV1.showDialog(context, ext.address, ext.bleName, absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), brightness4Percent);
        } else {
            OpColorCommDialog4SquareBleV1.showDialog(context, ext.address, ext.bleName, absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), brightness4Percent);
        }
        return true;
    }

    @Override
    public boolean supportSceneEffect(@NonNull AbsDevice absDevice, @NonNull EffectData.ShareEffect shareEffect) {
        int parseVersion = shareEffect.parseVersion;
        if (parseVersion == EffectData.ShareEffect.parser_version_diy) {
            return supportDiyEffect(absDevice, shareEffect.effectCodes);
        }
        boolean support = (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgb
                || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v0
                || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v1
                || (Support.isGoodsTypeH7092(absDevice.getGoodsType()) && parseVersion == EffectData.ShareEffect.parser_version_scenes_compose))//h7092支持组合灯
                && Support.supportServiceScenes(absDevice.getVersionSoft(), absDevice.getGoodsType());
        return shareEffect.isValidEffect() && support;
    }

    @Override
    public boolean applySceneEffect(Context context, @NonNull AbsDevice absDevice, @NonNull EffectData.ShareEffect shareEffect, boolean needConnect) {
        int parseVersion = shareEffect.parseVersion;
        if (parseVersion == EffectData.ShareEffect.parser_version_diy) {
            return applyDiyEffect(context, absDevice, shareEffect.effectStr, DiyShare.type_recommend, shareEffect.effectCodes, needConnect);
        }
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpSceneCommDialog4Ble.showDialog(context, ext.address, ext.bleName, shareEffect);
        } else {
            OpSceneCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, shareEffect);
        }
        return true;
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    public EffectCodes getSupportDiyEffectCode(@NonNull AbsDevice absDevice) {
        int version = Support.getDiyVersion(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        boolean supportAiDiy = Support.supportAiDiy(absDevice.getVersionSoft(), absDevice.getGoodsType());
        return Diy.getDiySupport(version, supportAiDiy).effectCodes;
    }

    @Override
    public int supportRandomColorSize(AbsDevice absDevice) {
        boolean supportBrightness = Support.supportSubModeColor4PartBrightness(absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        if (Support.isGoodsTypeH7092(absDevice.getGoodsType())) return 8;
        if (supportBrightness) return 7;
        return 1;
    }

    @Override
    public boolean applyPtControllers(Context context, @NonNull AbsDevice absDevice, int type, @NonNull Command4PtReal ptReal, boolean needConnect) {
        boolean invalid = ptReal.isInvalid();
        if (invalid) return false;
        boolean supportBleOp = ptReal.supportBleOp();
        List<PtRealCommands> commands = ptReal.opCommands;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyPtControllers() supportBleOp = " + supportBleOp);
        }
        if (!supportBleOp) return false;
        PtRealController ptRealController = PtRealController.makePtRealController(commands, -1, -1);
        if (ptRealController == null) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "applyPtControllers() 透传指令解析失败");
            }
            return false;
        }
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpDiyCommDialog4Ble.showDialog(context, ext.address, ext.bleName, ptRealController, type);
        } else {
            OpDiyCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, ptRealController, type);
        }
        return true;
    }

    @Override
    public int[] supportScenesOpSet(AbsDevice absDevice) {
        return Support.getSupportScenesOpSet(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
    }
    /*smartRoom*/

    @Override
    public boolean supportWifi() {
        return false;
    }

    @Override
    public boolean supportBle() {
        return true;
    }

    @Override
    public int supportColorSize(DeviceModel deviceModel) {
        boolean supportBrightness = Support.supportSubModeColor4PartBrightness(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard);
        if (Support.isGoodsTypeH7092(deviceModel.getGoodsType())) return 8;
        if (supportBrightness) return 7;
        return 1;
    }

    @Override
    public boolean supportDevice(DeviceModel deviceModel) {
        int curGoodsType = deviceModel.getGoodsType();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) return true;
        }
        return false;
    }

    @Override
    public IRoomOp4Iot getRoomOp4Iot() {
        return null;
    }

    @Override
    public IRoomOp4Ble getRoomOp() {
        return new IRoomOp4Ble() {
            @Override
            public UUID getServiceUUID() {
                return BleComm.serviceUuid;
            }

            @Override
            public UUID getCharacteristicUUID() {
                return BleComm.characteristicUuid;
            }

            @Override
            public byte[] heartBytes() {
                return new HeartController().getValue();
            }

            @Override
            public byte[] makeSwitchOpBytes(DeviceModel deviceModel, boolean on) {
                return new SwitchController(on).getValue();
            }

            @Override
            public byte[] makeBrightnessOpBytes(DeviceModel deviceModel, int brightnessPercent) {
                boolean bk = Support.isBk(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                int brightness;
                if (bk) {
                    brightness = brightnessPercent;
                } else {
                    brightness = NumUtil.calculateProgress(254, 20, brightnessPercent);
                }
                if (Support.isGoodsTypeH7092(deviceModel.getGoodsType()))
                    brightness = NumUtil.calculateProgress(100, 0, brightnessPercent);
                return new BrightnessController(brightness).getValue();
            }

            @Override
            public List<byte[]> makeSetColorOpBytes(DeviceModel deviceModel, int[] colors) {
                boolean supportBrightness = Support.supportSubModeColor4PartBrightness(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard);
                if (supportBrightness) {
                    AbsSingleController[] controllers;
                    if (Support.isGoodsTypeH7092(deviceModel.getGoodsType())) {
                        controllers = SubModeColorH7092.makeSubModeColor(colors, null, false, false, 8);
                    } else {
                        controllers = SubModeColorV2.makeSubModeColor(colors, null, false, false);
                    }
                    if (controllers == null) return null;
                    List<byte[]> bytes = new ArrayList<>();
                    for (AbsSingleController controller : controllers) {
                        bytes.add(controller.getValue());
                    }
                    return bytes;
                } else {
                    Mode mode = new Mode();
                    mode.subMode = SubModeColor.makeSubModeColorByColor(colors[0]);
                    ModeController modeController = new ModeController(mode);
                    List<byte[]> bytes = new ArrayList<>();
                    bytes.add(modeController.getValue());
                    return bytes;
                }
            }

            @Override
            public List<byte[]> makeSetColorTemOpBytes(DeviceModel deviceModel, int[] kelvin, int[] temColor) {
                boolean supportBrightness = Support.supportSubModeColor4PartBrightness(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard);
                if (supportBrightness) {
                    AbsSingleController[] controllers;
                    if (Support.isGoodsTypeH7092(deviceModel.getGoodsType())) {
                        controllers = SubModeColorH7092.makeSubModeColor(temColor, null, false, false, 8);
                    } else {
                        controllers = SubModeColorV2.makeSubModeColor(temColor, null, false, false);
                    }
                    if (controllers == null) return null;
                    List<byte[]> bytes = new ArrayList<>();
                    for (AbsSingleController controller : controllers) {
                        bytes.add(controller.getValue());
                    }
                    return bytes;
                } else {
                    Mode mode = new Mode();
                    mode.subMode = SubModeColor.makeSubModeColorByColorTem(temColor[0]);
                    ModeController modeController = new ModeController(mode);
                    List<byte[]> bytes = new ArrayList<>();
                    bytes.add(modeController.getValue());
                    return bytes;
                }
            }

            @Override
            public byte[] makeSetColorOpBytes(DeviceModel deviceModel, int color) {
                return Comm.makeColorController4BleComm(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard, color).getValue();
            }

            @Override
            public byte[] makeSetColorTemOpBytes(DeviceModel deviceModel, int kelvin, int temColor) {
                return Comm.makeColorTemController4BleComm(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard, temColor).getValue();
            }

            @Override
            public int supportMic(DeviceModel deviceModel) {
                boolean bk = Support.isBk(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                if (bk) return support_mic_new;
                return support_mic_self;
            }

            @Override
            public byte[] makeMicBytesByRgb(DeviceModel deviceModel, int rgb) {
                if (Support.isGoodsTypeH7092(deviceModel.getGoodsType())) {
                    byte[] bytes = ColorUtils.getRgbBytes(rgb);
                    return new MicSetRgbController(bytes).getValue();
                }
                return new MicController(true).getValue();
            }

            @Override
            public byte[] makeChange2MicModeBytesBySelf(DeviceModel deviceModel) {
                boolean bk = Support.isBk(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                if (Support.isGoodsTypeH7092(deviceModel.getGoodsType())) {
                    bk = true;
                }
                ISubMode subMode;
                if (bk) {
                    subMode = new SubModeMicV2();
                } else {
                    subMode = new SubModeMic();
                }
                Mode mode = new Mode();
                mode.subMode = subMode;
                return new ModeController(mode).getValue();
            }
        };
    }
    /*smartRoom*/
}