package com.govee.chassislightv1.scenes;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.rhythm.ui.AbsRhythmDiyUI;
import com.govee.chassislightv1.adjust.Diy;
import com.govee.chassislightv1.pact.Comm;
import com.govee.chassislightv1.pact.Support;

import java.util.List;

/**
 * Create by linshicong on 12/28/20
 */
public class RhythmDiyUI extends AbsRhythmDiyUI {
    public RhythmDiyUI(AppCompatActivity ac, DeviceModel deviceModel, OnSelectChangeListener selectChangeListener) {
        super(ac, deviceModel, selectChangeListener);
    }

    @Override
    protected DiySupportV1 getDiySupport() {
        int diyVersion = Support.getDiyVersion(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard);
        boolean supportAiDiy = Support.supportAiDiy(deviceModel.versionSoft, deviceModel.getGoodsType());
        return Diy.getDiySupport(diyVersion, supportAiDiy);
    }

    @Nullable
    @Override
    protected AbsCmd makeRuleCmd() {
        return null;
    }

    @Nullable
    @Override
    protected List<String> makeBleCmd() {
        return Comm.makeDiy4BleComm(Support.supportPactV1(deviceModel.getGoodsType(), GoodsType.beProtocol(deviceModel.pactType, deviceModel.pactCode)),
                getDiyProtocol(), getDiyGraffiti4Rgbic(), getDiyStudio(), getDiyAi());
    }
}