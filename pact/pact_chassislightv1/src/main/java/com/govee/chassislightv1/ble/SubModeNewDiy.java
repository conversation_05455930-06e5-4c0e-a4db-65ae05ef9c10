package com.govee.chassislightv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.ISubMode;
import com.ihoment.base2app.infra.StorageInfra;

/**
 * Create by linshicong on 2020/3/18
 */
public class SubModeNewDiy implements ISubMode {
    private int diyCode;
    private String diyValueKey;

    public SubModeNewDiy() {
    }

    /**
     * 写操作时的构造器
     *
     * @param diyCode diyCode
     */
    public SubModeNewDiy(int diyCode) {
        this.diyCode = diyCode;
    }

    @Override
    public void loadLocal() {
        DiyLocal subModeNewDiy = StorageInfra.get(DiyLocal.class);
        if (subModeNewDiy != null) {
            this.diyCode = subModeNewDiy.diyCode;
            this.diyValueKey = subModeNewDiy.diyValueKey;
        }
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(new DiyLocal(diyCode, diyValueKey));
    }

    @Override
    public String getAnalyticModeName() {
        return ParamFixedValue.mode_diy;
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_new_diy;
    }

    @Override
    public void parse(byte[] validBytes) {
        diyCode = BleUtil.getSignedShort(validBytes[1], validBytes[0]);
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] diyCodeBytes = BleUtil.getSignedBytesFor2(diyCode, false);
        return new byte[]{subModeCommandType(), diyCodeBytes[0], diyCodeBytes[1]};
    }

}