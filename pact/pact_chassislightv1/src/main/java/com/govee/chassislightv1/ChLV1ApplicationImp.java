package com.govee.chassislightv1;

import com.govee.base2home.main.choose.BleProcessorManager;
import com.govee.base2home.scenes.CmdBuilderManager;
import com.govee.base2home.sku.ModelMaker;
import com.govee.chassislightv1.add.BleBroadcastProcessorV1;
import com.govee.chassislightv1.pact.Register4Item;
import com.govee.chassislightv1.pact.SubMaker;
import com.govee.chassislightv1.pact.Support;
import com.govee.chassislightv1.scenes.BleBrightnessBuilderV1;
import com.govee.chassislightv1.scenes.BleColorCmdBuilderV1;
import com.govee.chassislightv1.scenes.BleColorTemCmdBuilderV1;
import com.govee.chassislightv1.scenes.BleHeartCmdBuilderV1;
import com.govee.chassislightv1.scenes.BleSwitchCmdBuilderV1;
import com.govee.lifecycle.annotation.AppLifecycle;
import com.ihoment.base2app.IApplication;

/**
 * Create by lins<PERSON>ong on 2020/3/18
 */
@AppLifecycle
public class ChLV1ApplicationImp implements IApplication {
    @Override
    public void create() {
        /*item的构建*/
        Register4Item.INSTANCE.register();
        ModelMaker.getInstance().addMaker(new SubMaker());
        /*发现设备后操作逻辑*/
        BleProcessorManager.getInstance().addProcessor(new BleBroadcastProcessorV1());
        /*添加默认的支持产品类型sku*/
        Support.addSupportPact();
        /*scenes场景ble支持*/
        CmdBuilderManager.getInstance().registerBleCmdBuilder(
                new BleBrightnessBuilderV1(),
                new BleColorCmdBuilderV1(),
                new BleColorTemCmdBuilderV1(),
                new BleHeartCmdBuilderV1(),
                new BleSwitchCmdBuilderV1()
        );
    }
}