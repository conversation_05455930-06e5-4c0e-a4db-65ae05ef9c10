package com.govee.chassislightv1.ble;

import com.govee.base2light.ble.controller.AbsOnlyWriteSingleController;
import com.govee.base2light.ble.controller.BleProtocolConstants;

/**
 * Create by linshicong on 2020/3/19
 */
public class MicController extends AbsOnlyWriteSingleController {
    private final boolean dynamic;

    public MicController(boolean dynamic) {
        this.dynamic = dynamic;
    }

    @Override
    protected boolean writeResult(boolean writeSuc) {
        /*写失败也不处理*/
        return true;
    }

    @Override
    protected byte[] translateWrite() {
        byte[] values = new byte[3];
        values[0] = BleProtocol.sub_mode_mic;
        values[1] = BleProtocol.value_sub_mode_mic_send_rhythm_point;
        values[2] = dynamic ? (byte) 0x01 : (byte) 0x00;
        return values;
    }

    @Override
    protected void fail() {
        /*发送失败不处理*/
    }

    @Override
    public byte getCommandType() {
        return BleProtocolConstants.SINGLE_MODE;
    }
}