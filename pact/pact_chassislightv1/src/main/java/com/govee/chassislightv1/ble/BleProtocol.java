package com.govee.chassislightv1.ble;

/**
 * Create by lins<PERSON><PERSON> on 2020/3/18
 */
public interface BleProtocol {
    /**
     * 子模式-MIC
     */
    byte sub_mode_mic = (byte) 0x05;

    /**
     * MIC操作类型-设置mic模式
     */
    byte value_sub_mode_mic_set_mode = (byte) 1;

    /**
     * MIC操作类型-发送节奏点
     */
    byte value_sub_mode_mic_send_rhythm_point = (byte) 3;

    /**
     * 子模式-颜色
     */
    byte sub_mode_color = (byte) 0x02;
    /**
     * 子模式-场景
     */
    byte sub_mode_scenes = (byte) 0x04;

    /**
     * 场景效果-电影
     */
    byte value_sub_mode_scenes_movie = (byte) 4;
    /**
     * 场景效果-约会
     */
    byte value_sub_mode_scenes_date = (byte) 5;

    /**
     * 场景效果-浪漫
     */
    byte value_sub_mode_scenes_romantic = (byte) 7;

    /**
     * 场景效果-闪烁
     */
    byte value_sub_mode_scenes_blinking = (byte) 8;

    /**
     * 场景效果-烛光
     */
    byte value_sub_mode_scenes_cl = (byte) 9;

    /**
     * 场景效果-呼吸
     */
    byte value_sub_mode_scenes_breath = (byte) 10;

    /**
     * 场景效果-活力
     */
    byte value_sub_mode_scenes_dynamic = (byte) 16;

    /**
     * 场景效果-雪花
     */
    int value_sub_mode_scenes_snow = (byte) 15;

    /**
     * 场景效果-追逐
     */
    byte value_sub_mode_scenes_chase = (byte) 21;


    /**
     * 场景效果-4彩流水
     */
    byte value_sub_mode_scenes_stream = (byte) 22;
    /**
     * 子模式-新diy
     */
    byte sub_mode_new_diy = (byte) 0x0a;

    /**
     * 多包-diy
     */
    byte value_multiple_ble_diy = (byte) 0x02;

    /**
     * 子模式-新颜色 支持分段亮度
     */
    byte sub_mode_color_v2 = (byte) 0x15;

    /**
     * 单包-开启渐变--针对ble+wifi的设备
     */
    byte SINGLE_GRADUAL_CHANGE_4_BLE = (byte) 0x14;
    /**
     * 新音乐模式
     */
    byte sub_mode_music_new = (byte) 0x13;

    /**
     * 单包-开启渐变--针对ble+wifi的设备
     */
    byte SINGLE_GRADUAL_CHANGE_4_WIFI_BLE = (byte) 0xa3;


}