package com.govee.chassislightv1.ble;

import android.content.Context;

import androidx.annotation.NonNull;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.light.AbsOpCommDialog4BleV2;
import com.govee.chassislightv1.adjust.h7092.color.SubModeColorH7092;
import com.govee.chassislightv1.pact.Comm;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Create by linshicong on 1/28/21
 * op color操作应用$
 */
public class OpColorCommDialog4SquareBleV1 extends AbsOpCommDialog4BleV2 {
    private int size;
    private AbsSingleController[] controllers;

    protected OpColorCommDialog4SquareBleV1(Context context, String bleAddress, String bleName, @NonNull EffectData.ColorEffect colorEffect, int goodsType) {
        super(context, bleAddress, bleName, EFFECT_TYPE_COLOR, -1);
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092) {
            controllers = SubModeColorH7092.makeSubModeColor(colorEffect.colorSet, colorEffect.brightnessSet, colorEffect.isFade(), !colorEffect.noFadeController(), 8);
        } else {
            controllers = SubModeColorV2.makeSubModeColor(colorEffect.colorSet, colorEffect.brightnessSet, colorEffect.isFade(), !colorEffect.noFadeController());
        }
        if (controllers != null && controllers.length > 0) {
            size = controllers.length;
        }
    }

    protected OpColorCommDialog4SquareBleV1(Context context, String bleAddress, String bleName, boolean on) {
        super(context, bleAddress, bleName, EFFECT_TYPE_SWITCH, -1);
        singleController = Comm.makeSwitchController4BleComm(on);
    }

    protected OpColorCommDialog4SquareBleV1(Context context, String bleAddress, String bleName, int goodsType, int pactType, int pactCode, int brightness4Percent) {
        super(context, bleAddress, bleName, EFFECT_TYPE_BRIGHTNESS, -1);
        singleController = Comm.makeBrightnessController4BleComm(goodsType, pactType, pactCode, brightness4Percent);
    }

    @Override
    protected void bleOping() {
        if (controllers != null && controllers.length > 0) {
            getBle().startController(controllers);
            return;
        }
        if (singleController != null) {
            getBle().startController(singleController);
            return;
        }
        updateResult(false);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        size--;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result + "---size:" + size);
        }
        if (!result || size <= 1) {
            updateResult(result);
            hide();
        }
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    public static void showDialog(Context context, String bleAddress, String bleName, @NonNull EffectData.ColorEffect colorEffect, int goodsType) {
        new OpColorCommDialog4SquareBleV1(context, bleAddress, bleName, colorEffect, goodsType).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, boolean on) {
        new OpColorCommDialog4SquareBleV1(context, bleAddress, bleName, on).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, int goodsType, int pactType, int pactCode, int brightness4Percent) {
        new OpColorCommDialog4SquareBleV1(context, bleAddress, bleName, goodsType, pactType, pactCode, brightness4Percent).show();
    }
}