package com.govee.chassislightv1.ble;

import android.content.Context;

import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.light.AbsOpCommDialog4BleV2;
import com.govee.chassislightv1.adjust.h7092.color.SubModeColorH7092;
import com.govee.chassislightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by lins<PERSON>ong on 1/28/21
 * op color操作应用$
 */
public class OpColorCommDialog4SquareBle extends AbsOpCommDialog4BleV2 {
    private final EffectData.ColorEffect colorEffect;
    private final int goodsType;

    protected OpColorCommDialog4SquareBle(Context context, String bleAddress, String bleName, @NonNull EffectData.ColorEffect colorEffect, int goodsType) {
        super(context, bleAddress, bleName, EFFECT_TYPE_COLOR, -1);
        this.colorEffect = colorEffect;
        this.goodsType = goodsType;
    }

    @Override
    protected void bleOping() {
        Mode mode = new Mode();
        if (Support.isGoodsTypeH7092(goodsType)) {
            mode.subMode = SubModeColorH7092.makeSubModeColor(colorEffect.colorSet[0]);
        } else {
            mode.subMode = SubModeColor.makeSubModeColorByColor(colorEffect.colorSet[0]);
        }
        ModeController controller = new ModeController(mode);
        getBle().startController(controller);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    public static void showDialog(Context context, String bleAddress, String bleName, @NonNull EffectData.ColorEffect colorEffect, int goodsType) {
        new OpColorCommDialog4SquareBle(context, bleAddress, bleName, colorEffect, goodsType).show();
    }
}