package com.govee.chassislightv1.scenes;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.ble.controller.AbsController;
import com.govee.base2light.rhythm.ui.AbsRhythmColorUI;
import com.govee.chassislightv1.pact.Comm;
import com.govee.chassislightv1.pact.Support;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by linshicong on 11/30/20
 */
public class RhythmColorUI extends AbsRhythmColorUI {
    public RhythmColorUI(AppCompatActivity ac, DeviceModel deviceModel, OnSelectChangeListener selectChangeListener) {
        super(ac, deviceModel, selectChangeListener);
    }

    @Nullable
    @Override
    protected AbsCmd makeRuleCmd() {
        return null;
    }

    @Nullable
    @Override
    protected AbsController makeBleCmd() {
        return Comm.makeColorController4BleComm(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard, color);
    }

    @Nullable
    @Override
    protected GetColorStripInfoInterface makeColorStripInfo() {
        return new GetColorStripInfoInterface() {
            @Override
            public Integer getSupportColorSize() {
                return Support.getBulbStringMaxNum(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
            }

            @Override
            public AbsController[] getAbsController() {
                return Comm.makeColorStripController4BleComm(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard, colorStrip);
            }

            @Override
            public AbsCmd getAbsCmd() {
                return null;
            }
        };
    }
}