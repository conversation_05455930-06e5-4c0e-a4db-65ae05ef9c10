package com.govee.chassislightv1.scenes;

import androidx.appcompat.app.AppCompatActivity;

import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.rhythm.RhyRule;
import com.govee.base2light.rhythm.ui.AbsEffectUI;
import com.govee.base2light.rhythm.ui.AbsRhythmSceneUI;
import com.govee.chassislightv1.adjust.Diy;
import com.govee.chassislightv1.pact.Scenes4BleRhythm;
import com.govee.chassislightv1.pact.Support;
import com.zhy.android.percent.support.PercentRelativeLayout;

/**
 * Create by linshicong on 12/2/20
 * 昼夜节律显示UI
 */
public class RhythmEffectUI extends AbsEffectUI {

    @Override
    public void layout(AppCompatActivity ac, PercentRelativeLayout parent, DeviceModel deviceModel, RhyRule rhyRule, OnConfirmListener confirmListener) {
        super.layout(ac, parent, deviceModel, rhyRule, confirmListener);
        /*初始化ui组件*/

        /*添加布局-开关*/
        RhythmSwitchUI switchUI = new RhythmSwitchUI(ac, deviceModel, this);
        uiList.add(switchUI);
        /*添加布局-亮度*/
        RhythmBrightnessUI brightnessUI = new RhythmBrightnessUI(ac, deviceModel, this);
        uiList.add(brightnessUI);
        /*添加布局-色温*/
        RhythmColorTemUI colorTemUI = new RhythmColorTemUI(ac, deviceModel, this);
        uiList.add(colorTemUI);
        /*添加布局-颜色*/
        RhythmColorUI colorUI = new RhythmColorUI(ac, deviceModel, this);
        uiList.add(colorUI);
        /*添加布局-模式*/
        RhythmSceneUI modeUI;
        int scenesModeVersion = Support.getScenesModeVersion(deviceModel.versionSoft, deviceModel.getGoodsType());
        boolean goodsTypeH7092 = Support.isGoodsTypeH7092(deviceModel.getGoodsType());
        if (scenesModeVersion == 0) {
            if (goodsTypeH7092) {
                modeUI = new RhythmSceneUI(ac, deviceModel, this);
                modeUI.setServiceSceneVersion(true, AbsRhythmSceneUI.service_scene_version_old);
                int[] opSet = Support.getSupportScenesOpSet(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard);
                int diyVersion = Support.getDiyVersion(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard);
                boolean supportAiDiy = Support.supportAiDiy(deviceModel.versionSoft, deviceModel.getGoodsType());
                modeUI.setSceneFilter4Old(opSet, Diy.getDiySupport(diyVersion, supportAiDiy).effectCodes);
            } else {
                modeUI = new RhythmSceneUI(ac, Scenes4BleRhythm.bleRhythm, deviceModel, this);
                modeUI.setServiceSceneVersion(false, AbsRhythmSceneUI.service_scene_version_not_support);
            }
        } else {
            modeUI = new RhythmSceneUI(ac, deviceModel, this);
            modeUI.setServiceSceneVersion(true, AbsRhythmSceneUI.service_scene_version_new);
            int[] opSet = Support.getSupportScenesOpSet(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard);
            int diyVersion = Support.getDiyVersion(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard);
            boolean supportAiDiy = Support.supportAiDiy(deviceModel.versionSoft, deviceModel.getGoodsType());
            modeUI.setSceneFilter(sceneV1 -> true, opSet, Diy.getDiySupport(diyVersion, supportAiDiy));
        }
        uiList.add(modeUI);

        /*添加布局-DIY*/
        RhythmDiyUI diyUI = new RhythmDiyUI(ac, deviceModel, this);
        uiList.add(diyUI);

        autoAddViewAndApplyRule(parent, rhyRule);

    }
}