package com.govee.chassislightv1.scenes;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.ble.controller.AbsController;
import com.govee.base2light.rhythm.ui.AbsRhythmBrightnessUI;
import com.govee.chassislightv1.pact.Comm;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by linshicong on 11/30/20
 */
public class RhythmBrightnessUI extends AbsRhythmBrightnessUI {

    public RhythmBrightnessUI(AppCompatActivity ac, DeviceModel deviceModel, OnSelectChangeListener selectChangeListener) {
        super(ac, deviceModel, selectChangeListener);
    }

    @Nullable
    @Override
    protected AbsCmd makeRuleCmd() {
        return null;
    }

    @Nullable
    @Override
    protected AbsController makeBleCmd() {
        return Comm.makeBrightnessController4BleComm(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, percent);
    }
}