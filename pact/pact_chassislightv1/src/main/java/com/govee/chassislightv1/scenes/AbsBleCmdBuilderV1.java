package com.govee.chassislightv1.scenes;

import com.govee.base2home.scenes.builder.BaseCmdModel;
import com.govee.base2home.scenes.builder.BleCmdBuilder;
import com.govee.chassislightv1.ble.BleComm;
import com.govee.chassislightv1.pact.Support;

import java.util.UUID;

/**
 * Create by linshicong on 2020/3/18
 * ble-抽象cmd构造器v1版本
 */
public abstract class AbsBleCmdBuilderV1<T extends BaseCmdModel> extends BleCmdBuilder<T> {
    @Override
    public String[] getSupportKeys() {
        return Support.supportBleV1GoodsSet;
    }

    @Override
    public UUID getServiceUUID(String key) {
        return BleComm.serviceUuid;
    }

    @Override
    public UUID getCharacteristicUUID(String key) {
        return BleComm.characteristicUuid;
    }
}