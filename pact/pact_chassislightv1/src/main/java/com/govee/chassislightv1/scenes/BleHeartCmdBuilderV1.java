package com.govee.chassislightv1.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.HeartModel;
import com.govee.chassislightv1.pact.Comm;

/**
 * Create by linshicong on 2020/3/18
 */
public class BleHeartCmdBuilderV1 extends AbsBleCmdBuilderV1<HeartModel> {
    @Override
    public IBleCmd createCmd(HeartModel heartModel) {
        return () -> Comm.makeHeartController4BleComm().getValue();
    }
}