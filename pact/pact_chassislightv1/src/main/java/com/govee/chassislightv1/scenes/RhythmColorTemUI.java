package com.govee.chassislightv1.scenes;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.ble.controller.AbsController;
import com.govee.base2light.rhythm.ui.AbsRhyColorTemUI;
import com.govee.chassislightv1.pact.Comm;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by linshicong on 2021/5/7
 */
public class RhythmColorTemUI extends AbsRhyColorTemUI {
    public RhythmColorTemUI(AppCompatActivity ac, DeviceModel deviceModel, OnSelectChangeListener selectChangeListener) {
        super(ac, deviceModel, selectChangeListener);
    }

    @Nullable
    @Override
    protected AbsCmd makeRuleCmd() {
        return null;
    }

    @Nullable
    @Override
    protected AbsController makeBleCmd() {
        return Comm.makeColorTemController4BleComm(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard, temColor);
    }
}