package com.govee.chassislightv1.add;

import android.app.Activity;

import com.govee.base2home.main.choose.BaseBleDeviceModel;
import com.govee.base2home.main.choose.BaseBleProcessor;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.sku.DefaultDeviceNameUtil;
import com.govee.chassislightv1.pact.Support;

/**
 * Create by lins<PERSON>ong on 2020/3/18
 */
public class BleBroadcastProcessorV1 extends BaseBleProcessor {
    @Override
    public boolean onItemClick(Activity activity, BaseBleDeviceModel model, boolean singleSku) {
        int goodsType = model.goodsType;
        Protocol protocol = model.protocol;
        /*是否支持协议处理;无须登录限定*/
        if (Support.supportPactV1(goodsType, protocol) || Support.supportPactV2(goodsType, protocol)) {
            AddInfo addInfo = new AddInfo();
            addInfo.sku = model.getSku();
            addInfo.goodsType = goodsType;
            addInfo.pactType = protocol.pactType;
            addInfo.pactCode = protocol.pactCode;
            addInfo.deviceName = model.getDeviceName();
            if (Support.isGoodsTypeH7092(goodsType)) {
                addInfo.deviceName = "Car Underglow Light";
            }
            addInfo.deviceName = DefaultDeviceNameUtil.INSTANCE.getSkuDefaultName(model.getSku(), addInfo.deviceName);
            addInfo.bleName = model.getBleName();
            addInfo.bleAddress = model.getDevice().getAddress();
            ConnectDialog.createDialog(activity, model.getDevice(), addInfo).show();
            return true;
        }
        return false;
    }
}