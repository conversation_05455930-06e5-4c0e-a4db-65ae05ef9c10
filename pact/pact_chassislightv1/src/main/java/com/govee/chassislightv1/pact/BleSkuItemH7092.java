package com.govee.chassislightv1.pact;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2light.light.AbsBleSku;

public class BleSkuItemH7092 extends AbsBleSku {
    @Override
    public int getDefIcon() {
        return ThemeM.getDefSkuRes(getSku());
    }

    @Override
    public String getSku() {
        return product == null ? "" : product.sku;
    }

    @Override
    public int getGoodsType() {
        return GoodsType.GOODS_TYPE_VALUE_CAR_LIGHT_h7092;
    }
}