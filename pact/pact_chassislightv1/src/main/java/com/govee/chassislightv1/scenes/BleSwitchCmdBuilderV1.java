package com.govee.chassislightv1.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.SwitchModel;
import com.govee.chassislightv1.pact.Comm;

/**
 * Create by linshicong on 2020/3/18
 */
public class BleSwitchCmdBuilderV1 extends AbsBleCmdBuilderV1<SwitchModel> {
    @Override
    public IBleCmd createCmd(SwitchModel switchModel) {
        return () -> Comm.makeSwitchController4BleComm(switchModel.isOpen).getValue();
    }
}