package com.govee.chassislightv1.pact;

import com.govee.base2light.light.IScenes4BleRhythm;
import com.govee.chassislightv1.ble.BleProtocol;
import com.govee.ui.R;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2021/10/21
 * $
 */
public class Scenes4BleRhythm implements IScenes4BleRhythm {
    private Scenes4BleRhythm() {
    }

    private static class Builder {
        private static final Scenes4BleRhythm instance = new Scenes4BleRhythm();
    }

    public static Scenes4BleRhythm bleRhythm = Builder.instance;

    private static final int[] defRes = {
            R.mipmap.new_light_btn_scenes_movie,
            R.mipmap.new_light_btn_scenes_date,
            R.mipmap.new_light_btn_scenes_romantic,
            R.mipmap.new_light_btn_scenes_blinking,
            R.mipmap.new_light_btn_scenes_candle,
            R.mipmap.new_light_btn_scenes_vivid,
            R.mipmap.new_light_btn_scenes_breath,
            R.mipmap.new_light_btn_scenes_snow,
            R.mipmap.new_light_btn_scenes_chase,
            R.mipmap.new_light_btn_scenes_four_color,
    };
    private static final int[] selectedRes = {
            R.mipmap.new_light_btn_scenes_movie_press,
            R.mipmap.new_light_btn_scenes_date_press,
            R.mipmap.new_light_btn_scenes_romantic_press,
            R.mipmap.new_light_btn_scenes_blinking_press,
            R.mipmap.new_light_btn_scenes_candle_press,
            R.mipmap.new_light_btn_scenes_vivid_press,
            R.mipmap.new_light_btn_scenes_breath_press,
            R.mipmap.new_light_btn_scenes_snow_press,
            R.mipmap.new_light_btn_scenes_chase_press,
            R.mipmap.new_light_btn_scenes_four_color_press,
    };

    private final int[] str = {
            R.string.b2light_scenes_film,
            R.string.b2light_scenes_date,
            R.string.b2light_scenes_romantic,
            R.string.b2light_scenes_blinking,
            R.string.b2light_scenes_cl,
            R.string.b2light_scenes_energetic,
            R.string.b2light_scenes_breath,
            R.string.b2light_scenes_snow,
            R.string.b2light_scenes_chase,
            R.string.b2light_scenes_stream
    };

    private final int[] scenesType = {
            BleProtocol.value_sub_mode_scenes_movie,
            BleProtocol.value_sub_mode_scenes_date,
            BleProtocol.value_sub_mode_scenes_romantic,
            BleProtocol.value_sub_mode_scenes_blinking,
            BleProtocol.value_sub_mode_scenes_cl,
            BleProtocol.value_sub_mode_scenes_dynamic,
            BleProtocol.value_sub_mode_scenes_breath,
            BleProtocol.value_sub_mode_scenes_snow,
            BleProtocol.value_sub_mode_scenes_chase,
            BleProtocol.value_sub_mode_scenes_stream,
    };

    @Override
    public int[] strSet() {
        return str;
    }

    @Override
    public int[] scenesEffectSet() {
        return scenesType;
    }

    @Override
    public int[] defResSet() {
        return defRes;
    }

    @Override
    public int[] selectedResSet() {
        return selectedRes;
    }

}
