apply from: "../../dynamic.gradle"
apply plugin: 'kotlin-kapt'
apply plugin: 'org.jetbrains.kotlin.android'

android {
    namespace 'com.govee.straightfloorlamp'
    resourcePrefix "straightfloorlamp_"
    defaultConfig {
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                        MODULE_NAME: project.getName()
                ]
            }
        }

        //ARouter配置
        kapt {
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
    }

    dataBinding {
        enabled = true
    }
    viewBinding {
        enabled = true
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':base2light')
    // AppLifecycle
    implementation 'com.govee.lifecycle:lifecycle-api:' + lifecycle_api_version
    implementation project(path: ':SerialCombin')
    kapt 'com.govee.lifecycle:lifecycle-compiler:' + lifecycle_compiler_version

    /*ARouter相关配置*/
    //noinspection DependencyNotationArgument
    implementation rootProject.ext.sdk.arouter_api
    //noinspection DependencyNotationArgument
    kapt rootProject.ext.sdk.arouter_compiler

}