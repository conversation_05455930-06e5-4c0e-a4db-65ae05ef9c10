<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="#272727"
    >

        <View
            android:id="@+id/bg"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:background="@color/ui_bg_color_style_80"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            android:id="@+id/bgTopFlag"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:maxHeight="0dp"
            app:layout_constraintTop_toTopOf="parent"
            />

        <ImageView
            android:id="@+id/ivEffect1"
            android:layout_width="74dp"
            android:layout_height="73dp"
            android:layout_marginStart="25dp"
            android:layout_marginTop="20dp"
            android:src="@drawable/component_btn_effect_energic_newdetail"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/bgTopFlag"
            />

        <TextView
            android:id="@+id/tvEffect1"
            android:layout_width="0dp"
            android:layout_height="33dp"
            android:layout_marginTop="87dp"
            android:gravity="center|top"
            android:text="@string/effect_energic_des"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_60_2_textSize"
            app:layout_constraintEnd_toEndOf="@+id/ivEffect1"
            app:layout_constraintStart_toStartOf="@+id/ivEffect1"
            app:layout_constraintTop_toTopOf="@+id/bgTopFlag"
            />

        <ImageView
            android:id="@+id/ivEffect2"
            android:layout_width="74dp"
            android:layout_height="73dp"
            android:layout_marginStart="10dp"
            android:src="@drawable/component_btn_effect_rhythm_newdetail"
            app:layout_constraintStart_toEndOf="@+id/ivEffect1"
            app:layout_constraintTop_toTopOf="@+id/ivEffect1"
            />

        <TextView
            android:id="@+id/tvEffect2"
            android:layout_width="0dp"
            android:layout_height="33dp"
            android:layout_marginTop="87dp"
            android:gravity="center|top"
            android:text="@string/effect_rhythm_des"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_60_2_textSize"
            app:layout_constraintEnd_toEndOf="@+id/ivEffect2"
            app:layout_constraintStart_toStartOf="@+id/ivEffect2"
            app:layout_constraintTop_toTopOf="@+id/bgTopFlag"
            />

        <ImageView
            android:id="@+id/ivEffect3"
            android:layout_width="74dp"
            android:layout_height="73dp"
            android:layout_marginStart="10dp"
            android:src="@drawable/component_btn_effect_jump_newdetail"
            app:layout_constraintStart_toEndOf="@+id/ivEffect2"
            app:layout_constraintTop_toTopOf="@+id/ivEffect2"
            />

        <TextView
            android:id="@+id/tvEffect3"
            android:layout_width="0dp"
            android:layout_height="33dp"
            android:layout_marginTop="87dp"
            android:gravity="center|top"
            android:text="@string/effect_jump_des"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_60_2_textSize"
            app:layout_constraintEnd_toEndOf="@+id/ivEffect3"
            app:layout_constraintStart_toStartOf="@+id/ivEffect3"
            app:layout_constraintTop_toTopOf="@+id/bgTopFlag"
            />

        <ImageView
            android:id="@+id/ivEffect4"
            android:layout_width="74dp"
            android:layout_height="73dp"
            android:layout_marginStart="10dp"
            android:src="@drawable/component_btn_effect_yuedong_newdetail"
            app:layout_constraintStart_toEndOf="@+id/ivEffect3"
            app:layout_constraintTop_toTopOf="@+id/ivEffect3"
            />

        <TextView
            android:id="@+id/tvEffect4"
            android:layout_width="0dp"
            android:layout_height="33dp"
            android:layout_marginTop="87dp"
            android:gravity="center|top"
            android:text="@string/b2light_music_joy"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_60_2_textSize"
            app:layout_constraintEnd_toEndOf="@+id/ivEffect4"
            app:layout_constraintStart_toStartOf="@+id/ivEffect4"
            app:layout_constraintTop_toTopOf="@+id/bgTopFlag"
            />


        <ImageView
            android:id="@+id/ivEffect5"
            android:layout_width="74dp"
            android:layout_height="73dp"
            android:layout_marginTop="44dp"
            android:src="@drawable/component_btn_effect_crush_newdetail"
            app:layout_constraintStart_toStartOf="@+id/ivEffect1"
            app:layout_constraintTop_toBottomOf="@+id/ivEffect1"
            />

        <TextView
            android:id="@+id/tvEffect5"
            android:layout_width="0dp"
            android:layout_height="33dp"
            android:layout_marginTop="111dp"
            android:gravity="center|top"
            android:text="@string/effect_hit_des"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_60_2_textSize"
            app:layout_constraintEnd_toEndOf="@+id/ivEffect5"
            app:layout_constraintStart_toStartOf="@+id/ivEffect5"
            app:layout_constraintTop_toBottomOf="@+id/ivEffect1"
            />


        <ImageView
            android:id="@+id/ivEffect6"
            android:layout_width="74dp"
            android:layout_height="73dp"
            android:layout_marginStart="10dp"
            android:src="@drawable/component_btn_effect_spray_newdetail"
            app:layout_constraintStart_toEndOf="@+id/ivEffect5"
            app:layout_constraintTop_toTopOf="@+id/ivEffect5"
            />

        <TextView
            android:id="@+id/tvEffect6"
            android:layout_width="0dp"
            android:layout_height="33dp"
            android:gravity="center|top"
            android:text="@string/effect_jet_des"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_60_2_textSize"
            app:layout_constraintEnd_toEndOf="@+id/ivEffect6"
            app:layout_constraintStart_toStartOf="@+id/ivEffect6"
            app:layout_constraintTop_toTopOf="@+id/tvEffect5"
            />

        <View
            android:id="@+id/bgSubEffect"
            android:layout_width="335dp"
            android:layout_height="44dp"
            android:layout_marginTop="10dp"
            android:background="@drawable/component_btn_style_171_bg"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvEffect6"
            />


        <TextView
            android:id="@+id/tvDynamic"
            android:layout_width="161.5dp"
            android:layout_height="36dp"
            android:layout_marginStart="4dp"
            android:background="@drawable/component_btn_style_171"
            android:gravity="center"
            android:text="@string/music_sub_model_power_label"
            android:textColor="@color/ui_btn_style_171_text_color"
            android:textSize="@dimen/ui_btn_style_171_text_size"
            app:layout_constraintBottom_toBottomOf="@+id/bgSubEffect"
            app:layout_constraintStart_toStartOf="@+id/bgSubEffect"
            app:layout_constraintTop_toTopOf="@+id/bgSubEffect"
            />

        <TextView
            android:id="@+id/tvSoft"
            android:layout_width="161.5dp"
            android:layout_height="36dp"
            android:layout_marginEnd="4dp"
            android:background="@drawable/component_btn_style_171"
            android:gravity="center"
            android:text="@string/music_sub_model_soft_label"
            android:textColor="@color/ui_btn_style_171_text_color"
            android:textSize="@dimen/ui_btn_style_171_text_size"
            app:layout_constraintBottom_toBottomOf="@+id/bgSubEffect"
            app:layout_constraintEnd_toEndOf="@+id/bgSubEffect"
            app:layout_constraintTop_toTopOf="@+id/bgSubEffect"
            />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/groupSubEffect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="gone"
            app:constraint_referenced_ids="bgSubEffect,tvDynamic,tvSoft"
            />

        <TextView
            android:id="@+id/line"
            android:layout_width="335dp"
            android:layout_height="1dp"
            android:layout_marginTop="20dp"
            android:background="@color/ui_split_line_style_34_color"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/bgSubEffect"
            />

        <ImageView
            android:id="@+id/ivAutoColor"
            android:layout_width="49dp"
            android:layout_height="30dp"
            android:layout_marginTop="20dp"
            android:layout_marginEnd="20dp"
            android:background="@drawable/component_light_switch"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/line"
            />

        <TextView
            android:id="@+id/tvAutoColor"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginStart="30dp"
            android:gravity="center"
            android:text="@string/b2light_music_auto"
            android:textColor="@color/font_style_59_1_textColor"
            android:textSize="@dimen/font_style_59_1_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/ivAutoColor"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/ivAutoColor"
            />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/groupAutoColor"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="ivAutoColor,tvAutoColor"
            />

        <com.govee.base2light.pact.newdetail.view.ViewColor
            android:id="@+id/viewColor"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            app:layout_constraintTop_toBottomOf="@+id/ivAutoColor"
            />

        <androidx.legacy.widget.Space
            android:layout_width="match_parent"
            android:layout_height="80dp"
            app:layout_constraintTop_toBottomOf="@+id/viewColor"
            />


</androidx.constraintlayout.widget.ConstraintLayout>