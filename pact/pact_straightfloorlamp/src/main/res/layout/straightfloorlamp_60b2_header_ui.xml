<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:id="@+id/constraint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        >

        <com.govee.base2light.pact.newdetail.view.CutView
            android:id="@+id/bg"
            android:layout_width="match_parent"
            android:layout_height="213dp"
            app:layout_constraintTop_toTopOf="parent"
            />

        <ImageView
            android:id="@+id/ivPic"
            android:layout_width="match_parent"
            android:layout_height="190dp"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@mipmap/light_pics_60b2_off"
            />

        <!--        <ImageView-->
        <!--            android:id="@+id/iv4HeaderLoadFail"-->
        <!--            android:layout_width="34dp"-->
        <!--            android:layout_height="34dp"-->
        <!--            android:src="@drawable/component_loading_pics_refresh"-->
        <!--            android:visibility="gone"-->
        <!--            app:layout_constraintBottom_toBottomOf="@+id/ivPic"-->
        <!--            app:layout_constraintLeft_toLeftOf="parent"-->
        <!--            app:layout_constraintRight_toRightOf="parent"-->
        <!--            app:layout_constraintTop_toTopOf="@+id/ivPic"-->
        <!--            tools:visibility="visible"-->
        <!--            />-->

        <!--        <ProgressBar-->
        <!--            android:id="@+id/pb4HeaderLoading"-->
        <!--            style="@style/newProgressBarCycleV1"-->
        <!--            android:layout_width="34dp"-->
        <!--            android:layout_height="34dp"-->
        <!--            android:visibility="gone"-->
        <!--            app:layout_constraintBottom_toBottomOf="@+id/ivPic"-->
        <!--            app:layout_constraintLeft_toLeftOf="parent"-->
        <!--            app:layout_constraintRight_toRightOf="parent"-->
        <!--            app:layout_constraintTop_toTopOf="@+id/ivPic"-->
        <!--            tools:visibility="visible"-->
        <!--            />-->

        <ImageView
            android:id="@+id/btnSwitch"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginTop="15dp"
            android:layout_marginEnd="16dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:src="@mipmap/control_btn_device_on"
            />

        <ImageView
            android:id="@+id/btnSwitch1"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginTop="73dp"
            android:layout_marginEnd="119dp"
            app:layout_constraintEnd_toEndOf="@+id/btnSwitch"
            app:layout_constraintTop_toBottomOf="@+id/btnSwitch"
            tools:src="@mipmap/control_btn_device_on_01"
            />

        <ImageView
            android:id="@+id/btnSwitch2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="12dp"
            app:layout_constraintStart_toEndOf="@+id/btnSwitch1"
            app:layout_constraintTop_toTopOf="@+id/btnSwitch1"
            tools:src="@mipmap/control_btn_device_on_02"
            />

        <ImageView
            android:id="@+id/btnSwitch3"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="12dp"
            app:layout_constraintStart_toEndOf="@+id/btnSwitch2"
            app:layout_constraintTop_toTopOf="@+id/btnSwitch1"
            tools:src="@mipmap/control_btn_device_on_03"
            />

        <TextView
            android:id="@+id/tvTimer"
            android:layout_width="wrap_content"
            android:layout_height="32dp"
            android:layout_marginStart="15dp"
            android:layout_marginBottom="3dp"
            android:background="@drawable/component_btn_style_164"
            android:drawablePadding="9dp"
            android:gravity="center_vertical"
            android:paddingStart="9dp"
            android:paddingEnd="12dp"
            android:textColor="@color/ui_btn_style_164_text_color"
            android:textSize="@dimen/ui_btn_style_164_text_size"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            tools:drawableStart="@mipmap/control_icon_timer"
            tools:text="@string/new_timer_label"
            tools:visibility="visible"
            />

        <com.govee.base2light.pact.newdetail.view.ViewBrightness
            android:id="@+id/viewBrightness"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="9.5dp"
            android:layout_marginEnd="9.5dp"
            android:layout_marginBottom="3dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvTimer"
            app:layout_goneMarginStart="13.5dp"
            app:view_brightness_startIconRes="@mipmap/slider_btn_brightness_01"
            app:view_brightness_style="1"
            tools:visibility="visible"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>