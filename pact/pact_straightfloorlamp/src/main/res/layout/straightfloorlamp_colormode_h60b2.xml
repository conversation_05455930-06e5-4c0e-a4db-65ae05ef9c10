<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        >

        <TextView
            android:id="@+id/selectAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="20dp"
            android:text="@string/b2light_select_all"
            android:textColor="@color/font_style_172_4_textColor"
            android:textSize="@dimen/font_style_172_4_textSize"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <com.govee.base2home.custom.ShapeImageView
            android:id="@+id/light1"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight1"
            app:layout_constraintEnd_toEndOf="@+id/ivLight1"
            app:layout_constraintStart_toStartOf="@+id/ivLight1"
            app:layout_constraintTop_toTopOf="@+id/ivLight1"
            app:shapeIv_lb_radius="1000dp"
            app:shapeIv_lt_radius="1000dp"
            app:shapeIv_rb_radius="1000dp"
            app:shapeIv_rt_radius="1000dp"
            app:shapeIv_shape_bg_color_apply="true"
            app:shapeIv_type="round"
            />

        <ImageView
            android:id="@+id/ivLight1"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_marginStart="159.5dp"
            android:layout_marginTop="30dp"
            android:src="@mipmap/h60b2_pics_control_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/selectAll"
            />

        <TextView
            android:id="@+id/tvBrightness1"
            android:layout_width="wrap_content"
            android:layout_height="16.5dp"
            android:layout_marginEnd="11dp"
            android:gravity="center"
            android:textColor="@color/font_style_172_1_textColor"
            android:textSize="@dimen/font_style_172_1_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight1"
            app:layout_constraintEnd_toStartOf="@+id/ivLight1"
            app:layout_constraintTop_toTopOf="@+id/ivLight1"
            />

        <ImageView
            android:id="@+id/ivCheck1"
            android:layout_width="26dp"
            android:layout_height="17dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/component_btn_bulb_select_5"
            android:button="@null"
            android:scaleType="center"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight1"
            app:layout_constraintStart_toEndOf="@+id/ivLight1"
            app:layout_constraintTop_toTopOf="@+id/ivLight1"
            />

        <View
            android:id="@+id/light1Area"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight1"
            app:layout_constraintEnd_toEndOf="@+id/ivCheck1"
            app:layout_constraintStart_toStartOf="@+id/tvBrightness1"
            app:layout_constraintTop_toTopOf="@+id/ivLight1"
            />

        <com.govee.base2home.custom.ShapeImageView
            android:id="@+id/light2"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight2"
            app:layout_constraintEnd_toEndOf="@+id/ivLight2"
            app:layout_constraintStart_toStartOf="@+id/ivLight2"
            app:layout_constraintTop_toTopOf="@+id/ivLight2"
            app:shapeIv_lb_radius="1000dp"
            app:shapeIv_lt_radius="1000dp"
            app:shapeIv_rb_radius="1000dp"
            app:shapeIv_rt_radius="1000dp"
            app:shapeIv_shape_bg_color_apply="true"
            app:shapeIv_type="round"
            />

        <ImageView
            android:id="@+id/ivLight2"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_marginStart="159.5dp"
            android:layout_marginTop="20dp"
            android:src="@mipmap/h60b2_pics_control_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivLight1"
            />

        <TextView
            android:id="@+id/tvBrightness2"
            android:layout_width="wrap_content"
            android:layout_height="16.5dp"
            android:layout_marginEnd="11dp"
            android:gravity="center"
            android:textColor="@color/font_style_172_1_textColor"
            android:textSize="@dimen/font_style_172_1_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight2"
            app:layout_constraintEnd_toStartOf="@+id/ivLight2"
            app:layout_constraintTop_toTopOf="@+id/ivLight2"
            />

        <ImageView
            android:id="@+id/ivCheck2"
            android:layout_width="26dp"
            android:layout_height="17dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/component_btn_bulb_select_5"
            android:button="@null"
            android:scaleType="center"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight2"
            app:layout_constraintStart_toEndOf="@+id/ivLight2"
            app:layout_constraintTop_toTopOf="@+id/ivLight2"
            />


        <View
            android:id="@+id/light2Area"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight2"
            app:layout_constraintEnd_toEndOf="@+id/ivCheck2"
            app:layout_constraintStart_toStartOf="@+id/tvBrightness2"
            app:layout_constraintTop_toTopOf="@+id/ivLight2"
            />

        <com.govee.base2home.custom.ShapeImageView
            android:id="@+id/light3"
            android:layout_width="40dp"
            android:layout_height="40dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight3"
            app:layout_constraintEnd_toEndOf="@+id/ivLight3"
            app:layout_constraintStart_toStartOf="@+id/ivLight3"
            app:layout_constraintTop_toTopOf="@+id/ivLight3"
            app:shapeIv_lb_radius="1000dp"
            app:shapeIv_lt_radius="1000dp"
            app:shapeIv_rb_radius="1000dp"
            app:shapeIv_rt_radius="1000dp"
            app:shapeIv_shape_bg_color_apply="true"
            app:shapeIv_type="round"
            />

        <ImageView
            android:id="@+id/ivLight3"
            android:layout_width="52dp"
            android:layout_height="52dp"
            android:layout_marginStart="159.5dp"
            android:layout_marginTop="20dp"
            android:src="@mipmap/h60b2_pics_control_light"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivLight2"
            />

        <TextView
            android:id="@+id/tvBrightness3"
            android:layout_width="wrap_content"
            android:layout_height="16.5dp"
            android:layout_marginEnd="11dp"
            android:gravity="center"
            android:textColor="@color/font_style_172_1_textColor"
            android:textSize="@dimen/font_style_172_1_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight3"
            app:layout_constraintEnd_toStartOf="@+id/ivLight3"
            app:layout_constraintTop_toTopOf="@+id/ivLight3"
            />

        <ImageView
            android:id="@+id/ivCheck3"
            android:layout_width="26dp"
            android:layout_height="17dp"
            android:layout_marginStart="8dp"
            android:background="@drawable/component_btn_bulb_select_5"
            android:button="@null"
            android:scaleType="center"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight3"
            app:layout_constraintStart_toEndOf="@+id/ivLight3"
            app:layout_constraintTop_toTopOf="@+id/ivLight3"
            />


        <View
            android:id="@+id/light3Area"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivLight3"
            app:layout_constraintEnd_toEndOf="@+id/ivCheck3"
            app:layout_constraintStart_toStartOf="@+id/tvBrightness3"
            app:layout_constraintTop_toTopOf="@+id/ivLight3"
            />

        <ImageView
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginTop="9dp"
            android:src="@mipmap/new_light_segment_icon_power"
            app:layout_constraintEnd_toEndOf="@+id/ivLight3"
            app:layout_constraintStart_toStartOf="@+id/ivLight3"
            app:layout_constraintTop_toBottomOf="@+id/ivLight3"
            />


        <androidx.constraintlayout.widget.Group
            android:id="@+id/group1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tvBrightness1,ivCheck1"
            />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tvBrightness2,ivCheck2"
            />

        <androidx.constraintlayout.widget.Group
            android:id="@+id/group3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:constraint_referenced_ids="tvBrightness3,ivCheck3"
            />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
