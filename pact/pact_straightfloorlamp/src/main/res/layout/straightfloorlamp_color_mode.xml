<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >

        <TextView
            android:id="@+id/selectAll"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="35dp"
            android:text="@string/b2light_select_all"
            android:textColor="@color/font_style_172_4_textColor"
            android:textSize="@dimen/font_style_172_4_textSize"
            android:paddingVertical="10dp"
            android:paddingStart="44dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <View
            android:id="@+id/color1"
            android:layout_width="5dp"
            android:layout_height="21dp"
            android:layout_marginBottom="39dp"
            app:layout_constraintBottom_toBottomOf="@+id/ivPic"
            app:layout_constraintEnd_toEndOf="@+id/ivPic"
            app:layout_constraintStart_toStartOf="@+id/ivPic"
            />

        <View
            android:id="@+id/color2"
            android:layout_width="5dp"
            android:layout_height="21dp"
            app:layout_constraintBottom_toTopOf="@+id/color1"
            app:layout_constraintEnd_toEndOf="@+id/ivPic"
            app:layout_constraintStart_toStartOf="@+id/ivPic"
            />

        <View
            android:id="@+id/color3"
            android:layout_width="5dp"
            android:layout_height="21dp"
            app:layout_constraintBottom_toTopOf="@+id/color2"
            app:layout_constraintEnd_toEndOf="@+id/ivPic"
            app:layout_constraintStart_toStartOf="@+id/ivPic"
            />

        <View
            android:id="@+id/color4"
            android:layout_width="5dp"
            android:layout_height="21dp"
            app:layout_constraintBottom_toTopOf="@+id/color3"
            app:layout_constraintEnd_toEndOf="@+id/ivPic"
            app:layout_constraintStart_toStartOf="@+id/ivPic"
            />

        <View
            android:id="@+id/color5"
            android:layout_width="5dp"
            android:layout_height="21dp"
            app:layout_constraintBottom_toTopOf="@+id/color4"
            app:layout_constraintEnd_toEndOf="@+id/ivPic"
            app:layout_constraintStart_toStartOf="@+id/ivPic"
            />

        <View
            android:id="@+id/color6"
            android:layout_width="5dp"
            android:layout_height="21dp"
            app:layout_constraintBottom_toTopOf="@+id/color5"
            app:layout_constraintEnd_toEndOf="@+id/ivPic"
            app:layout_constraintStart_toStartOf="@+id/ivPic"
            />

        <View
            android:id="@+id/color7"
            android:layout_width="5dp"
            android:layout_height="21dp"
            app:layout_constraintBottom_toTopOf="@+id/color6"
            app:layout_constraintEnd_toEndOf="@+id/ivPic"
            app:layout_constraintStart_toStartOf="@+id/ivPic"
            />

        <View
            android:id="@+id/color8"
            android:layout_width="5dp"
            android:layout_height="21dp"
            app:layout_constraintBottom_toTopOf="@+id/color7"
            app:layout_constraintEnd_toEndOf="@+id/ivPic"
            app:layout_constraintStart_toStartOf="@+id/ivPic"
            />


        <ImageView
            android:id="@+id/ivPic"
            android:layout_width="116dp"
            android:layout_height="210dp"
            android:src="@mipmap/control_color_pics_6072"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            android:layout_marginTop="25dp"
            app:layout_constraintTop_toTopOf="parent"
            />

        <ImageView
            android:id="@+id/checkBtn1"
            android:layout_width="26dp"
            android:layout_height="17dp"
            android:layout_marginBottom="41dp"
            android:background="@drawable/component_btn_bulb_select_5"
            app:layout_constraintBottom_toBottomOf="@+id/ivPic"
            app:layout_constraintEnd_toEndOf="@+id/ivPic"
            />

        <TextView
            android:id="@+id/tvBright1"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_172_2_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/checkBtn1"
            app:layout_constraintStart_toEndOf="@+id/checkBtn1"
            app:layout_constraintTop_toTopOf="@+id/checkBtn1"
            />

        <ImageView
            android:id="@+id/checkBtn3"
            android:layout_width="26dp"
            android:layout_height="17dp"
            android:layout_marginBottom="25dp"
            android:background="@drawable/component_btn_bulb_select_5"
            app:layout_constraintBottom_toTopOf="@+id/checkBtn1"
            app:layout_constraintEnd_toEndOf="@+id/checkBtn1"
            />

        <TextView
            android:id="@+id/tvBright3"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_172_2_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/checkBtn3"
            app:layout_constraintStart_toEndOf="@+id/checkBtn3"
            app:layout_constraintTop_toTopOf="@+id/checkBtn3"
            />


        <ImageView
            android:id="@+id/checkBtn5"
            android:layout_width="26dp"
            android:layout_height="17dp"
            android:layout_marginBottom="25dp"
            android:background="@drawable/component_btn_bulb_select_5"
            app:layout_constraintBottom_toTopOf="@+id/checkBtn3"
            app:layout_constraintEnd_toEndOf="@+id/checkBtn1"
            />

        <TextView
            android:id="@+id/tvBright5"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_172_2_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/checkBtn5"
            app:layout_constraintStart_toEndOf="@+id/checkBtn5"
            app:layout_constraintTop_toTopOf="@+id/checkBtn5"
            />

        <ImageView
            android:id="@+id/checkBtn7"
            android:layout_width="26dp"
            android:layout_height="17dp"
            android:layout_marginBottom="25dp"
            android:background="@drawable/component_btn_bulb_select_5"
            app:layout_constraintBottom_toTopOf="@+id/checkBtn5"
            app:layout_constraintEnd_toEndOf="@+id/checkBtn1"
            />

        <TextView
            android:id="@+id/tvBright7"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_172_2_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/checkBtn7"
            app:layout_constraintStart_toEndOf="@+id/checkBtn7"
            app:layout_constraintTop_toTopOf="@+id/checkBtn7"
            />

        <ImageView
            android:id="@+id/checkBtn2"
            android:layout_width="26dp"
            android:layout_height="17dp"
            android:layout_marginBottom="62dp"
            android:background="@drawable/component_btn_bulb_select_5"
            app:layout_constraintBottom_toBottomOf="@+id/ivPic"
            app:layout_constraintStart_toStartOf="@+id/ivPic"
            />

        <TextView
            android:id="@+id/tvBright2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_172_2_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/checkBtn2"
            app:layout_constraintEnd_toStartOf="@+id/checkBtn2"
            app:layout_constraintTop_toTopOf="@+id/checkBtn2"
            />

        <ImageView
            android:id="@+id/checkBtn4"
            android:layout_width="26dp"
            android:layout_height="17dp"
            android:layout_marginBottom="25dp"
            android:background="@drawable/component_btn_bulb_select_5"
            app:layout_constraintBottom_toTopOf="@+id/checkBtn2"
            app:layout_constraintStart_toStartOf="@+id/checkBtn2"
            />

        <TextView
            android:id="@+id/tvBright4"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_172_2_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/checkBtn4"
            app:layout_constraintEnd_toStartOf="@+id/checkBtn4"
            app:layout_constraintTop_toTopOf="@+id/checkBtn4"
            />

        <ImageView
            android:id="@+id/checkBtn6"
            android:layout_width="26dp"
            android:layout_height="17dp"
            android:layout_marginBottom="25dp"
            android:background="@drawable/component_btn_bulb_select_5"
            app:layout_constraintBottom_toTopOf="@+id/checkBtn4"
            app:layout_constraintStart_toStartOf="@+id/checkBtn4"
            />

        <TextView
            android:id="@+id/tvBright6"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_172_2_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/checkBtn6"
            app:layout_constraintEnd_toStartOf="@+id/checkBtn6"
            app:layout_constraintTop_toTopOf="@+id/checkBtn6"
            />

        <ImageView
            android:id="@+id/checkBtn8"
            android:layout_width="26dp"
            android:layout_height="17dp"
            android:layout_marginBottom="25dp"
            android:background="@drawable/component_btn_bulb_select_5"
            app:layout_constraintBottom_toTopOf="@+id/checkBtn6"
            app:layout_constraintStart_toStartOf="@+id/checkBtn6"
            />

        <TextView
            android:id="@+id/tvBright8"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textColor="@color/font_style_230_1_textColor"
            android:textSize="@dimen/font_style_172_2_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/checkBtn8"
            app:layout_constraintEnd_toStartOf="@+id/checkBtn8"
            app:layout_constraintTop_toTopOf="@+id/checkBtn8"
            />


</androidx.constraintlayout.widget.ConstraintLayout>