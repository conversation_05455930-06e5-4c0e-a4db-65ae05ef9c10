<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ac_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/ui_bg_color_style_1"
        >

        <TextView
            android:id="@+id/top_flag"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="30.5dp"
            android:layout_marginBottom="10.5dp"
            android:minHeight="34dp"
            android:layout_marginHorizontal="93dp"
            android:gravity="center"
            android:text="@string/control_remote_label"
            android:textColor="@color/font_style_105_textColor"
            android:textSize="@dimen/font_style_105_textSize"
            app:layout_constraintTop_toBottomOf="@+id/top_flag"
            />

        <ImageView
            android:id="@+id/back"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="12dp"
            android:background="@drawable/component_btn_style_23"
            android:contentDescription="@null"
            android:padding="5dp"
            android:src="@mipmap/new_sensor_setting_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/title"
            />

        <TextView
            android:id="@+id/skip"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:layout_marginEnd="13dp"
            android:gravity="center_vertical|end"
            android:minWidth="48dp"
            android:maxWidth="78dp"
            android:text="@string/skip"
            android:textColor="@color/FF00ACE7"
            android:textSize="14sp"
            app:layout_constraintBottom_toBottomOf="@+id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/title"
            />

        <TextView
            android:id="@+id/compoent_bg"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@+id/top_flag"
            android:layout_marginTop="75dp"
            android:background="@drawable/component_bg_style_2"
            />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/to_connect_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title"
            >

            <ImageView
                android:id="@+id/icon"
                android:layout_width="304dp"
                android:layout_height="352dp"
                android:layout_centerHorizontal="true"
                android:contentDescription="@null"
                android:src="@mipmap/new_add_list_type_device_6072_1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                />

            <TextView
                android:id="@+id/guide_video"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:minHeight="21dp"
                android:text="@string/guide_video_hint"
                android:textColor="@color/FF00ACE7"
                android:textSize="15sp"
                android:visibility="gone"
                app:layout_constraintTop_toBottomOf="@+id/icon"
                />

            <TextView
                android:id="@+id/hint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="38dp"
                android:gravity="center"
                android:paddingHorizontal="54.5dp"
                android:text="@string/control_remote_hint"
                android:textColor="@color/font_style_49_2_textColor"
                android:textSize="@dimen/font_style_49_2_textSize"
                app:layout_constraintTop_toBottomOf="@+id/icon"
                />

            <TextView
                android:id="@+id/hint2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="17dp"
                android:gravity="center"
                android:paddingHorizontal="54.5dp"
                android:text="@string/control_remote_hint_label"
                android:textColor="@color/font_style_159_1_textColor"
                android:textSize="@dimen/font_style_159_1_textSize"
                app:layout_constraintTop_toBottomOf="@+id/hint"
                />

            <TextView
                android:id="@+id/btn_confirm"
                style="@style/compoent_btn_style_1_new_xml"
                android:layout_width="match_parent"
                android:layout_marginStart="14dp"
                android:layout_marginEnd="14dp"
                android:layout_marginBottom="5dp"
                android:background="@drawable/component_btn_style_3"
                android:text="@string/control_remote_start_connect"
                android:textColor="@color/ui_btn_style_3_1_text_color"
                android:textSize="@dimen/ui_btn_style_3_1_text_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/is_connecting_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title"
            >

            <ImageView
                android:id="@+id/icon_2"
                android:layout_width="304dp"
                android:layout_height="352dp"
                android:contentDescription="@null"
                android:src="@mipmap/new_add_list_type_device_6072_2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                />

            <TextView
                android:id="@+id/hint_2"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="38dp"
                android:gravity="center"
                android:paddingHorizontal="54.5dp"
                android:text="@string/control_remote_connecting_hint_label"
                android:textColor="@color/font_style_49_2_textColor"
                android:textSize="@dimen/font_style_49_2_textSize"
                app:layout_constraintTop_toBottomOf="@+id/icon_2"
                />

            <ProgressBar
                style="@style/ProgressBarCycle"
                android:layout_width="40dp"
                android:layout_height="40dp"
                android:layout_marginBottom="27dp"
                app:layout_constraintBottom_toBottomOf="@+id/bottom_flag"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                />

            <TextView
                android:id="@+id/bottom_flag"
                android:layout_width="match_parent"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/to_disconnect_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            tools:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/title"
            >

            <ImageView
                android:id="@+id/icon_3"
                android:layout_width="304dp"
                android:layout_height="352dp"
                android:contentDescription="@null"
                android:src="@mipmap/new_add_list_type_device_6072_3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                />

            <TextView
                android:id="@+id/guide_video_3"
                android:layout_width="270dp"
                android:layout_height="wrap_content"
                android:gravity="center"
                android:text="@string/control_remote_hint_label"
                android:textColor="@color/font_style_159_1_textColor"
                android:textSize="@dimen/font_style_159_1_textSize"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/icon_3"
                />

            <TextView
                android:id="@+id/hint_3"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="17dp"
                android:gravity="center"
                android:paddingHorizontal="54.5dp"
                android:text="@string/control_remote_connected"
                android:textColor="@color/font_style_46_2_textColor"
                android:textSize="@dimen/font_style_46_2_textSize"
                app:layout_constraintTop_toBottomOf="@+id/guide_video_3"
                />

            <TextView
                android:id="@+id/btn_2disconnect"
                style="@style/compoent_btn_style_1_new_xml"
                android:layout_width="match_parent"
                android:layout_marginStart="14dp"
                android:layout_marginEnd="14dp"
                android:layout_marginBottom="5dp"
                android:background="@drawable/component_btn_style_6"
                android:text="@string/control_remote_disconnect"
                android:textColor="@color/ui_btn_style_3_1_text_color"
                android:textSize="@dimen/ui_btn_style_3_1_text_size"
                app:layout_constraintBottom_toBottomOf="parent"
                />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/read_info_fail_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            tools:visibility="visible"
            >

            <ImageView
                android:id="@+id/icon_fail"
                android:layout_width="72dp"
                android:layout_height="72dp"
                android:layout_marginTop="147.5dp"
                android:contentDescription="@null"
                android:src="@mipmap/new_public_pics_icon_fail"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                />

            <TextView
                android:layout_width="290dp"
                android:layout_height="wrap_content"
                android:layout_marginTop="153dp"
                android:gravity="center"
                android:text="@string/control_remote_read_fail"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/icon_fail"
                />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
