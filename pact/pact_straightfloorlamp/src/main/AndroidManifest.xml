<?xml version="1.0" encoding="utf-8"?>
<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:dist="http://schemas.android.com/apk/distribution"
    package="com.govee.straightfloorlamp"
    tools:ignore="DiscouragedApi"
    >

    <!-- module名称字符最多50个字符 -->
    <dist:module
        dist:instant="false"
        dist:title="@string/pfd_h6072_module"
        >
        <dist:delivery>
            <dist:on-demand />
        </dist:delivery>
        <!-- 无需兼容API20及更低版本设备 -->
        <dist:fusing dist:include="false" />
    </dist:module>
    <uses-permission android:name="android.permission.BLUETOOTH" />

    <application>
        <activity
            android:name=".add.DeviceNameAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.WifiChooseAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.v1.RemoteControlAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.PairAcV1"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.PairAcV2"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.SettingAcH6072"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.AcNewDetail"
            android:configChanges="uiMode"
            android:exported="false"
            android:windowSoftInputMode="adjustPan"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.SettingAcH6076"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".SettingAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
    </application>

</manifest>