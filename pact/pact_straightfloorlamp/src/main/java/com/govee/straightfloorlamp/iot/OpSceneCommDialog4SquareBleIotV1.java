package com.govee.straightfloorlamp.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultiNewScenes;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.straightfloorlamp.ble.Ble;
import com.govee.straightfloorlamp.ble.Mode;
import com.govee.straightfloorlamp.ble.ModeController;
import com.govee.straightfloorlamp.ble.SubModeScenes;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by linshicong on 1/28/21
 * op scene操作应用$
 */
public class OpSceneCommDialog4SquareBleIotV1 extends AbsOpCommDialog4BleIotV2 {
    private AbsCmd absCmd;
    private AbsMultipleControllerV14Scenes controllerV14Scenes;

    protected OpSceneCommDialog4SquareBleIotV1(Context context, String bleAddress, String bleName, String topic, String sku, String device, String effectStr) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_SCENES, -1);
        controllerV14Scenes = ScenesOp.parseShareEffect(ScenesOp.scenes_code_share_rgbic, effectStr);
        absCmd = CmdPtReal.getNewScenesCmdPtReal(controllerV14Scenes);
    }

    protected OpSceneCommDialog4SquareBleIotV1(Context context, String bleAddress, String bleName, String topic, String sku, String device, int type, @NonNull PtRealController controller, List<String> commands, boolean bleOp, boolean wifiOp) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_DIY, type);
        if (bleOp) {
            this.ptRealController = controller;
        }
        if (wifiOp) {
            absCmd = CmdPtReal.makeCmdPt(commands);
        }
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, String effectStr) {
        new OpSceneCommDialog4SquareBleIotV1(context, bleAddress, bleName, topic, sku, device, effectStr).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, int type, @NonNull PtRealController controller, List<String> commands, boolean bleOp, boolean wifiOp) {
        new OpSceneCommDialog4SquareBleIotV1(context, bleAddress, bleName, topic, sku, device, type, controller, commands, bleOp, wifiOp).show();
    }

    @Nullable
    @Override
    protected AbsCmd getOpCmd() {
        return absCmd;
    }

    @Override
    protected void bleOping() {
        if (controllerV14Scenes != null) {
            getBle().sendMultipleControllerV1(controllerV14Scenes);
            return;
        }
        if (ptRealController != null) {
            getBle().sendMultipleController4PtReal(ptRealController);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleNewScenes(EventMultiNewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleNewScenes() result = " + result + " ; scenesCode = " + scenesCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*场景效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.effect = scenesCode;
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*diy效果传输失败，则通知设置新场景模式失败*/
            getBle().clearControllers();
            updateBleResult(false);
            hide();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateBleResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}