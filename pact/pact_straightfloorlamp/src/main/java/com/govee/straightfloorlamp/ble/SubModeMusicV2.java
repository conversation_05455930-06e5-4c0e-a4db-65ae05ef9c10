package com.govee.straightfloorlamp.ble;

import android.text.TextUtils;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.IMusicEffectStatic;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.music.OldMusicEffect;
import com.govee.base2light.light.ModeStr;
import com.govee.base2light.util.NumUtil;
import com.govee.straightfloorlamp.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.StorageInfra;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/7/28
 * 音乐模式$ 6072迭代
 */
public class SubModeMusicV2 extends AbsSubMode4Analytic {
    private static final String TAG = "SubModeMusicV2";
    private static final int min_sensitivity = 0;
    private static final int max_sensitivity = 99;
    private static final byte sub_effect_dynamic = BleProtocol.value_sub_mode_music_rhythm_dynamic;
    private static final byte sub_effect_soft = BleProtocol.value_sub_mode_music_rhythm_soft;

    private boolean isNewMusic = false;

    private int musicCode = BleProtocol.value_sub_mode_music_energy;

    private int sensitivity = max_sensitivity;
    private boolean dynamic = true;
    private int rgb = 0xFFFF0000;

    private boolean isAutoColor = false;

    @Override
    public void loadLocal() {
        SubModeMusicV2 subModeMusicV2 = StorageInfra.get(SubModeMusicV2.class);
        if (subModeMusicV2 == null) return;
        isNewMusic = subModeMusicV2.isNewMusic;
        musicCode = subModeMusicV2.musicCode;
        sensitivity = subModeMusicV2.sensitivity;
        dynamic = subModeMusicV2.dynamic;
        rgb = subModeMusicV2.rgb;
        isAutoColor = subModeMusicV2.isAutoColor;
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(this);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_music;
    }

    @Override
    public String getAnalyticModeName() {
        String subStr = parseSubStr(musicCode);
        if (!TextUtils.isEmpty(subStr)) return ParamFixedValue.mode_music + "_" + subStr;
        return ParamFixedValue.mode_music;
    }

    private String parseSubStr(int musicCode) {
        if (isNewMusic) return IMusicEffectStatic.parseSubStr4New(musicCode);
        String subModeStr;
        if (musicCode == BleProtocol.value_sub_mode_music_rhythm) {
            subModeStr = ModeStr.value_sub_mode_music_rhythm_str;
        } else if (musicCode == BleProtocol.value_sub_mode_music_energy) {
            subModeStr = ModeStr.value_sub_mode_music_energy_str;
        } else if (musicCode == BleProtocol.value_sub_mode_music_jet) {
            subModeStr = ModeStr.value_sub_mode_music_spray_str;
        } else if (musicCode == BleProtocol.value_sub_mode_music_jump) {
            subModeStr = ModeStr.value_sub_mode_music_bounce_str;
        } else if (musicCode == BleProtocol.value_sub_mode_music_joy) {
            subModeStr = ModeStr.value_sub_mode_music_joy_str;
        } else {
            subModeStr = ModeStr.value_sub_mode_music_impact_str;
        }
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_music, subModeStr);
    }

    public static SubModeMusicV2 parseSubModeV1(SubModeMusic subModeMusicV1) {
        SubModeMusicV2 result = new SubModeMusicV2();
        result.isNewMusic = false;
        result.rgb = subModeMusicV1.rgb;
        result.isAutoColor = subModeMusicV1.auto;
        result.musicCode = subModeMusicV1.effect;
        result.sensitivity = subModeMusicV1.sensitivity;
        result.dynamic = subModeMusicV1.isSubDynamicEffect();
        return result;
    }


    @Override
    public void parse(byte[] validBytes) {
        musicCode = BleUtil.getUnsignedByte(validBytes[0]);
        sensitivity = BleUtil.getUnsignedByte(validBytes[1]);
        isNewMusic = Support.isH6072NewMusicCode(musicCode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parse() musicCode = " + musicCode);
        }
        /*旧音乐模式-单包指令包含全部音乐模式参数*/
        if (!isNewMusic) {
            if (musicCode == BleProtocol.value_sub_mode_music_rhythm) {
                /*节奏下有动感柔和*/
                dynamic = BleUtil.getUnsignedByte(validBytes[2]) == sub_effect_dynamic;
                isAutoColor = BleUtil.getUnsignedByte(validBytes[3]) == 0;
                if (!isAutoColor) {
                    int r = BleUtil.getUnsignedByte(validBytes[4]);
                    int g = BleUtil.getUnsignedByte(validBytes[5]);
                    int b = BleUtil.getUnsignedByte(validBytes[6]);
                    rgb = ColorUtils.toColor(r, g, b);
                }
            } else {
                isAutoColor = BleUtil.getUnsignedByte(validBytes[2]) == 0;
                if (!isAutoColor) {
                    int r = BleUtil.getUnsignedByte(validBytes[3]);
                    int g = BleUtil.getUnsignedByte(validBytes[4]);
                    int b = BleUtil.getUnsignedByte(validBytes[5]);
                    rgb = ColorUtils.toColor(r, g, b);
                }
            }
        }
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] valueBytes;
        if (isNewMusic) {
            valueBytes = new byte[3];
            valueBytes[0] = subModeCommandType();
            valueBytes[1] = (byte) musicCode;
            valueBytes[2] = (byte) sensitivity;
        } else {
            if (musicCode == BleProtocol.value_sub_mode_music_rhythm) {
                valueBytes = new byte[8];
                valueBytes[0] = subModeCommandType();
                valueBytes[1] = (byte) musicCode;
                valueBytes[2] = (byte) sensitivity;
                valueBytes[3] = dynamic ? sub_effect_dynamic : sub_effect_soft;
                valueBytes[4] = (byte) (isAutoColor ? 0 : 1);
                if (!isAutoColor) {
                    int[] rgb = ColorUtils.getRgb(this.rgb);
                    valueBytes[5] = (byte) rgb[0];
                    valueBytes[6] = (byte) rgb[1];
                    valueBytes[7] = (byte) rgb[2];
                }
            } else {
                valueBytes = new byte[7];
                valueBytes[0] = subModeCommandType();
                valueBytes[1] = (byte) musicCode;
                valueBytes[2] = (byte) sensitivity;
                valueBytes[3] = (byte) (isAutoColor ? 0 : 1);
                if (!isAutoColor) {
                    int[] rgb = ColorUtils.getRgb(this.rgb);
                    valueBytes[4] = (byte) rgb[0];
                    valueBytes[5] = (byte) rgb[1];
                    valueBytes[6] = (byte) rgb[2];
                }
            }
        }
        return valueBytes;
    }

    public int getMusicCode() {
        return musicCode;
    }

    public void setMusicCode(int musicCode) {
        this.musicCode = musicCode;
        isNewMusic = Support.isH6072NewMusicCode(musicCode);
    }

    public int getSensitivity() {
        return sensitivity;
    }

    public void setSensitivity(int sensitivity) {
        this.sensitivity = NumUtil.checkNum(sensitivity, min_sensitivity, max_sensitivity);
    }

    public int getRgb() {
        return rgb;
    }

    public void setRgb(int rgb) {
        this.rgb = rgb;
    }

    public boolean isDynamic() {
        return dynamic;
    }

    public boolean isAutoColor() {
        return isAutoColor;
    }

    public void setDynamic(boolean dynamic) {
        this.dynamic = dynamic;
    }

    public void oldMusicEffectChange(OldMusicEffect oldMusicEffect) {
        isNewMusic = false;
        musicCode = oldMusicEffect.musicCode;
        dynamic = oldMusicEffect.dynamic;
        isAutoColor = oldMusicEffect.autoColor;
        rgb = oldMusicEffect.rgb;
        setSensitivity(oldMusicEffect.sensitivity);
    }

    public SubModeMusicV2 copy() {
        SubModeMusicV2 subModeMusicV2 = new SubModeMusicV2();
        subModeMusicV2.isNewMusic = isNewMusic;
        subModeMusicV2.musicCode = musicCode;
        subModeMusicV2.sensitivity = sensitivity;
        subModeMusicV2.dynamic = dynamic;
        subModeMusicV2.rgb = rgb;
        subModeMusicV2.isAutoColor = isAutoColor;
        return subModeMusicV2;
    }

    public static SubModeMusicV2 toNewSubModeMusic(int sensitivity, int musicCode) {
        SubModeMusicV2 subModeMusicV2 = new SubModeMusicV2();
        subModeMusicV2.isNewMusic = true;
        subModeMusicV2.musicCode = musicCode;
        subModeMusicV2.setSensitivity(sensitivity);
        return subModeMusicV2;
    }

    public static ISubMode parseSubModeMusic4Write(byte[] subModeValidBytes) {
        SubModeMusicV2 subModeMusic = new SubModeMusicV2();
        subModeMusic.parse(subModeValidBytes);
        return subModeMusic;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_music;
    }
}