package com.govee.straightfloorlamp.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ac.diy.v3.DiyGroup;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.v1.IHighColorDiy;
import com.ihoment.base2app.infra.StorageInfra;

import java.util.List;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2019-04-19
 * diy模式
 */
public class SubModeNewDiy extends AbsSubMode4Analytic implements IHighColorDiy {
    private int diyCode;
    private String diyValueKey;
    private List<DiyGroup> diyGroups;

    public void setDiyGroups(List<DiyGroup> diyGroups) {
        this.diyGroups = diyGroups;
    }

    public List<DiyGroup> getDiyGroups() {
        return diyGroups;
    }

    public void setDiyValueKey(String diyValueKey) {
        this.diyValueKey = diyValueKey;
    }

    public String getDiyValueKey() {
        return diyValueKey;
    }

    public SubModeNewDiy() {
    }

    /**
     * 写操作时的构造器
     *
     * @param diyCode diyCode
     */
    public SubModeNewDiy(int diyCode) {
        this.diyCode = diyCode;
    }

    @Override
    public void loadLocal() {
        DiyLocal subModeNewDiy = StorageInfra.get(DiyLocal.class);
        if (subModeNewDiy != null) {
            this.diyCode = subModeNewDiy.diyCode;
            this.diyValueKey = subModeNewDiy.diyValueKey;
        }
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(new DiyLocal(diyCode, diyValueKey));
    }

    @Override
    public String getAnalyticModeName() {
        return ParamFixedValue.mode_diy;
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_new_diy;
    }

    @Override
    public void parse(byte[] validBytes) {
        diyCode = BleUtil.getSignedShort(validBytes[1], validBytes[0]);
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] diyCodeBytes = BleUtil.getSignedBytesFor2(diyCode, false);
        return new byte[]{subModeCommandType(), diyCodeBytes[0], diyCodeBytes[1]};
    }

    public int getDiyCode() {
        return diyCode;
    }

    /**
     * 设置新diy模式，读写操作一致
     *
     * @param validBytes
     * @return
     */
    public static SubModeNewDiy parseSubModeNewDiy4Write(byte[] validBytes) {
        SubModeNewDiy subModeNewDiy = new SubModeNewDiy();
        subModeNewDiy.parse(validBytes);
        return subModeNewDiy;
    }

    @Override
    public int getDiyCodeNow() {
        return diyCode;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_DIY;
    }
}