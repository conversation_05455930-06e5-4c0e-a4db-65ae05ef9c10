package com.govee.straightfloorlamp.ble;

import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.comm.AbsNotifyParse;

/**
 * Create by hey on 2020/11/28
 * $
 */
public class RemoteControlParse extends AbsNotifyParse {
    @Override
    protected byte getNotifyType() {
        return BleProtocol.REMOTE_CONTROL_PAIR;
    }

    @Override
    protected void parseValue(byte[] value) {
        int pair = BleUtil.getUnsignedByte(value[0]);
        EventPairNotify.sendEventPairNotify(pair);
    }
}