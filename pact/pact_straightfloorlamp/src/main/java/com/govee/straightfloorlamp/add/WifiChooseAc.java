package com.govee.straightfloorlamp.add;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.govee.base2home.config.Base2homeConfig;
import com.govee.base2home.main.tab.EventTabDefault;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.support.OldDreamColorUtil;
import com.govee.base2home.sku.SkuIcM;
import com.govee.base2light.ac.AbsBleWifiChooseActivity;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.kt.comm.DefInfo;
import com.govee.base2light.pact.newdetail.ShareVM;
import com.govee.base2light.pact.newdetail.Vm4NewDetailAcShare;
import com.govee.ble.BleController;
import com.govee.straightfloorlamp.ConsV1;
import com.govee.straightfloorlamp.adjust.AcNewDetail;
import com.govee.straightfloorlamp.adjust.v1.RemoteControlAc;
import com.govee.straightfloorlamp.ble.Ble;
import com.govee.straightfloorlamp.pact.Support;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.util.JumpUtil;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/3/6
 * wifi选择界面$
 */
public class WifiChooseAc extends AbsBleWifiChooseActivity {
    private static final String intent_ac_key_ble_disconnect_close_ac = "intent_ac_key_ble_disconnect_close_ac";
    private static final String intent_ac_key_support_back = "intent_ac_key_support_back";
    private static final String intent_ac_key_support_skip = "intent_ac_key_support_skip";
    private static final String intent_ac_key_wifi_mac = "intent_ac_key_wifi_mac";
    private static final String intent_ac_key_pair = "intent_ac_key_pair";
    private static final String intent_ac_key_addInfo = "intent_ac_key_addInfo";

    private boolean bleDisconnectCloseAc;
    private boolean supportBack;
    private int goodsType;

    private String wifiMac;
    private String secretCode;
    private int hadPair;

    private AddInfo addInfo;

    /**
     * 跳转到wifi选择页面-添加流程
     *
     * @param ac
     * @param addInfo
     */
    public static void jump2wifiChooseAcByAdd(Activity ac, @NonNull AddInfo addInfo) {
        int[] wifiInputLimit = Support.getWifiInputLimit();
        Bundle jumpBundle = getJumpBundle(addInfo.sku, addInfo.device, addInfo.bleName, addInfo.bleAddress, addInfo.deviceName, "", wifiInputLimit[0], wifiInputLimit[1], addInfo.versionHard, addInfo.versionSoft);
        if (jumpBundle == null) return;
        jumpBundle.putBoolean(intent_ac_key_ble_disconnect_close_ac, true);
        jumpBundle.putBoolean(intent_ac_key_support_back, false);
        jumpBundle.putBoolean(intent_ac_key_support_skip, true);
        jumpBundle.putInt(ConsV1.intent_ac_adjust_goodsType, addInfo.goodsType);
        jumpBundle.putString(intent_ac_key_wifi_mac, addInfo.wifiMac);
        jumpBundle.putInt(intent_ac_key_pair, addInfo.hadPair);
        jumpBundle.putParcelable(intent_ac_key_addInfo, addInfo);
        JumpUtil.jumpWithBundle(ac, WifiChooseAc.class, true, jumpBundle);
    }

    /**
     * 跳转到wifi选择界面-修改wifi
     *
     * @param goodsType
     * @param context
     * @param sku
     * @param device
     * @param bleName
     * @param bleAddress
     */
    public static void jump2wifiChooseAcByChangeWifi(Context context, int goodsType, String sku, String device, String deviceName, String bleName, String bleAddress, String versionHard, String versionSoft, String wifiMac, String wifiHardVersion, String wifiSoftVersion) {
        int[] wifiInputLimit = Support.getWifiInputLimit();
        Bundle jumpBundle = getJumpBundle(sku, device, bleName, bleAddress, deviceName, "", wifiInputLimit[0], wifiInputLimit[1], versionHard, versionSoft);
        if (jumpBundle == null) return;
        jumpBundle.putBoolean(intent_ac_key_ble_disconnect_close_ac, false);
        jumpBundle.putBoolean(intent_ac_key_support_back, true);
        jumpBundle.putBoolean(intent_ac_key_support_skip, false);
        jumpBundle.putInt(ConsV1.intent_ac_adjust_goodsType, goodsType);
        jumpBundle.putString(OldDreamColorUtil.intent_ac_adjust_wifiMac, wifiMac);
        jumpBundle.putString(com.govee.base2light.homepage.ConsV1.intent_ac_adjust_versionHard_4_wifi, wifiHardVersion);
        jumpBundle.putString(com.govee.base2light.homepage.ConsV1.intent_ac_adjust_versionSoft_4_wifi, wifiSoftVersion);
        JumpUtil.jump(context, WifiChooseAc.class, jumpBundle);
    }

    @Override
    protected void doCheckPermissionPre() {
        super.doCheckPermissionPre();
        Intent intent = getIntent();
        goodsType = intent.getIntExtra(ConsV1.intent_ac_adjust_goodsType, 0);
        bleDisconnectCloseAc = intent.getBooleanExtra(intent_ac_key_ble_disconnect_close_ac, false);
        supportBack = intent.getBooleanExtra(intent_ac_key_support_back, false);
        boolean supportSkip = intent.getBooleanExtra(intent_ac_key_support_skip, false);
        updateBackAndSkip(supportBack, supportSkip);
        wifiMac = intent.getStringExtra(intent_ac_key_wifi_mac);
        /*密钥*/
        secretCode = intent.getStringExtra(ConsV1.intent_ac_adjust_secretCode);
        hadPair = intent.getIntExtra(intent_ac_key_pair, 0);
        addInfo = intent.getParcelableExtra(intent_ac_key_addInfo);
    }

    private void toJumpAdjustAc() {
        if (BaseApplication.getBaseApplication().isInBackground()) return;/*若应用处于后台，则不进行界面跳转*/
        /*关闭蓝牙*/
        getBle().stopHeart();
        BleController.getInstance().toBtClose();
        /*通知Tab更改为Default*/
        EventTabDefault.sendEventTabDefault();
        /*跳转到详情页*/
        Class<?> mainAcClass = Base2homeConfig.getConfig().getMainAcClass();
//        Bundle bundle = ConsV1.makeAdjustAcBundle(sku, device, "", goodsType, deviceName, bleName, bleAddress, wifiMac, null, versionHard, versionSoft, secretCode);
        DefInfo defInfo = new DefInfo();
        defInfo.setSku(sku);
        defInfo.setGoodsType(goodsType);
        defInfo.setDevice(device);
        defInfo.setBleName(bleName);
        defInfo.setBleAddress(bleAddress);
        defInfo.setDeviceName(deviceName);
        if (secretCode != null)
            defInfo.setSecretCode(secretCode);
        defInfo.setPactType(addInfo.pactType);
        defInfo.setPactCode(addInfo.pactCode);
        if (versionSoft != null)
            defInfo.setVersionSoft(versionSoft);
        if (versionHard != null)
            defInfo.setVersionHard(versionHard);
        defInfo.setTopic("");
        if (wifiMac != null)
            defInfo.setWifiMac(wifiMac);
        if (addInfo.wifiSoftVersion != null)
            defInfo.setWifiSoftVersion(addInfo.wifiSoftVersion);
        if (addInfo.wifiHardVersion != null)
            defInfo.setWifiHardVersion(addInfo.wifiHardVersion);
        defInfo.setIc(SkuIcM.getInstance().getDefIcNum(sku));
        BaseApplication.getBaseApplication().finishOther(mainAcClass);
        AcNewDetail.Companion.jump2NewDetailAc(this, defInfo);
    }

    @Override
    protected boolean bleDisconnectCloseAc() {
        return bleDisconnectCloseAc;
    }

    @Override
    protected void toCloseAc() {
        if (supportBack) { //设置流程
            finish();
        } else {
            //添加流程
            if (hadPair == 0 && addInfo.goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
                //跳转到配对页面
                RemoteControlAc.jump2RemoteControlAc4Add(this, addInfo);
            } else {
                /*跳转到adjustAc*/
                toJumpAdjustAc();
            }
        }
    }

    @Override
    protected int runModeVersion() {
        return Support.check2WifiDeviceRunModeVersion();
    }

    @Override
    protected AbsBle getBle() {
        Vm4NewDetailAcShare shareVm = Vm4NewDetailAcShare.Companion.createShareVm4NewDetail(sku, device, this, false);
        ShareVM opVm = shareVm.getOpVm();
        if (opVm != null) {
            return opVm.queryBleOp();
        }
        return Ble.getInstance;
    }

    @Override
    protected boolean isCanBack() {
        return supportBack;
    }

    @Override
    protected void toSkip() {
        toCloseAc();
    }
}