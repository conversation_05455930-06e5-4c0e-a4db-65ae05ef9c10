package com.govee.straightfloorlamp.pact;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.sku.Colors;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.HeartController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.util.NumUtil;
import com.govee.straightfloorlamp.ble.Mode;
import com.govee.straightfloorlamp.ble.ModeController;
import com.govee.straightfloorlamp.ble.SubModeColor;
import com.govee.straightfloorlamp.ble.SubModeColorV2;
import com.govee.straightfloorlamp.ble.SubModeScenes;
import com.govee.straightfloorlamp.iot.CmdBrightness;
import com.govee.straightfloorlamp.iot.CmdColorWc;
import com.govee.straightfloorlamp.iot.CmdPtReal;
import com.govee.straightfloorlamp.iot.CmdTurn;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by xieyingwu on 2021/10/21
 * 指令生成类$
 */
public final class Comm {
    private Comm() {
    }

    /**
     * 生成亮度指令
     *
     * @param brightness
     * @return
     */
    public static AbsSingleController makeBrightnessController4BleComm(int brightness) {
        int[] brightnessRange = Support.getBrightnessRange();
        int newBrightness;
        if (brightnessRange[2] == 1) {
            newBrightness = NumUtil.calculateProgress(brightnessRange[1], brightnessRange[0], brightness);
        } else {
            newBrightness = brightness;
        }
        return new BrightnessController(newBrightness);
    }

    /**
     * 生成亮度指令-iot
     *
     * @param brightness
     * @return
     */
    public static AbsCmd makeBrightnessCmd4IotComm(int brightness) {
        int[] brightnessRange = Support.getBrightnessRange();
        int newBrightness;
        if (brightnessRange[2] == 1) {
            newBrightness = NumUtil.calculateProgress(brightnessRange[1], brightnessRange[0], brightness);
        } else {
            newBrightness = brightness;
        }
        return new CmdBrightness(newBrightness);
    }

    /**
     * 生成颜色指令-ble
     *
     * @param goodsType
     * @param versionSoft
     * @param versionHard
     * @param color
     * @return
     */
    public static AbsSingleController makeColorController4BleComm(int goodsType, String versionSoft, String versionHard, int color) {
        boolean supportNewColorMode = Support.supportSubModeColor4PartBrightness(goodsType, versionSoft, versionHard);
        Mode mode = new Mode();
        mode.subMode = supportNewColorMode ? SubModeColorV2.makeSubModeColor(color, goodsType) : SubModeColor.makeSubModeColor4Scenes(color);
        return new ModeController(mode);
    }

    /**
     * 生成颜色-色条指令-ble
     *
     * @param goodsType
     * @param versionSoft
     * @param versionHard
     * @param colors
     * @return
     */
    public static AbsSingleController[] makeColorStripController4BleComm(int goodsType, String versionSoft, String versionHard, Colors colors) {
        AbsSingleController[] modeControllers;
        boolean supportPartBrightness = Support.supportSubModeColor4PartBrightness(goodsType, versionSoft, versionHard);
        if (!supportPartBrightness) {
            modeControllers = SubModeColor.makeSubModeColor(colors);
        } else {
            modeControllers = SubModeColorV2.makeSubModeColor(colors, goodsType);
        }
        return modeControllers;
    }

    /**
     * 生成颜色--色条指令-iot
     *
     * @param color
     * @return
     */
    public static AbsCmd makeColorCmd4IotComm(int color) {
        return CmdColorWc.makeCmdColorWc4Color(color);
    }

    /**
     * 生成颜色指令-iot
     *
     * @param colors
     * @return
     */
    public static AbsCmd makeColorStripCmd4IotComm(int goodsType, String versionSoft, String versionHard, Colors colors) {

        AbsSingleController[] modeControllers;
        boolean supportPartBrightness = Support.supportSubModeColor4PartBrightness(goodsType, versionSoft, versionHard);
        if (!supportPartBrightness) {
            modeControllers = SubModeColor.makeSubModeColor(colors);
        } else {
            modeControllers = SubModeColorV2.makeSubModeColor(colors, goodsType);
        }
        if (modeControllers != null) {
            List<byte[]> bytes = new ArrayList<>();
            for (AbsSingleController absSingleController : modeControllers) {
                bytes.add(absSingleController.getValue());
            }
            return new CmdPtReal(bytes);
        }
        return null;
    }

    /**
     * 生成色温指令-ble
     *
     * @param goodsType
     * @param versionSoft
     * @param versionHard
     * @param colorTemInKelvin
     * @return
     */
    public static AbsSingleController makeColorTemController4BleComm(int goodsType, String versionSoft, String versionHard, int colorTemInKelvin) {
        boolean supportNewColorMode = Support.supportSubModeColor4PartBrightness(goodsType, versionSoft, versionHard);
        Mode mode = new Mode();
        mode.subMode = supportNewColorMode ? SubModeColorV2.makeSubModeColorByKelvin(colorTemInKelvin, goodsType) : SubModeColor.makeSubModeColor4ScenesByKelvin(colorTemInKelvin);
        return new ModeController(mode);
    }

    /**
     * 生成色温指令-iot
     *
     * @param colorTemInKelvin
     * @return
     */
    public static AbsCmd makeColorTemCmd4IotComm(int colorTemInKelvin) {
        return CmdColorWc.makeCmdColorWc4Kelvin(colorTemInKelvin);
    }

    /**
     * 生成心跳指令-ble
     *
     * @return
     */
    public static AbsSingleController makeHeartController4BleComm() {
        return new HeartController();
    }

    /**
     * 生成开关指令-ble
     *
     * @param open
     * @return
     */
    public static AbsSingleController makeSwitchController4BleComm(boolean open) {
        return new SwitchController(open);
    }

    /**
     * 生成开关指令-iot
     *
     * @param open
     * @return
     */
    public static AbsCmd makeSwitchCmd4IotComm(boolean open) {
        return new CmdTurn(open);
    }

    /**
     * 生成场景指令-iot
     *
     * @param sku
     * @param device
     * @param sceneCode
     * @return
     */
    public static AbsCmd makeSceneCmd4IotComm(String sku,String device, int sceneCode) {
        Mode mode = new Mode();
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.setEffect(sceneCode);
        mode.subMode = subModeScenes;
        CmdPtReal cmdPtReal;
        AbsMultipleControllerV14Scenes newMultiScenesModeV1 = Support.is2NewScenesMode(sku, device, mode);
        if (newMultiScenesModeV1 != null) {
            cmdPtReal = CmdPtReal.getNewScenesCmdPtReal(newMultiScenesModeV1);
        } else {
            ModeController modeController = new ModeController(mode);
            cmdPtReal = new CmdPtReal(modeController);
        }
        return cmdPtReal;
    }

    /**
     * 生成场景指令-iot
     */
    public static AbsCmd makeSceneCmd4IotComm(String sku, CategoryV1.SceneV1 scene) {
        AbsMultipleControllerV14Scenes newMultiScenesModeV1 = ScenesOp.parseSceneV1(sku, scene, ScenesOp.scene_type_rgb, ScenesOp.scene_type_rgbic);
        if (newMultiScenesModeV1 != null) {
            return CmdPtReal.getNewScenesCmdPtReal(newMultiScenesModeV1);
        }
        return null;
    }

    /**
     * 生成场景蓝牙指令集和-ble
     *
     * @param sku
     * @param sceneCode
     * @return
     */
    public static List<String> makeScene4BleComm(String sku,String device, int sceneCode) {
        AbsCmd cmd = makeSceneCmd4IotComm(sku, device, sceneCode);
        if (cmd == null) return null;
        if (cmd instanceof CmdPtReal) {
            return ((CmdPtReal) cmd).getCommand();
        }
        return null;
    }

    /**
     * 生成diy指令-iot
     *
     * @param diyProtocol
     * @param diyGraffiti4Rgbic
     * @param diyStudio
     * @param diyAi
     * @return
     */
    public static AbsCmd makeDiyCmd4IotComm(DiyProtocol diyProtocol, DiyGraffitiV2 diyGraffiti4Rgbic, DiyStudio diyStudio, DiyAi diyAi) {
        if (diyProtocol != null) {
            return CmdPtReal.getDiyCmdPt(diyProtocol);
        }
        if (diyGraffiti4Rgbic != null) {
            CmdPtReal cmdPtReal = CmdPtReal.getDiyCmdPt4DiyGraffiti(diyGraffiti4Rgbic);
            int commandPacketNum = cmdPtReal.getCommand().size();
            if (commandPacketNum > 19) {
                return null;
            }
            return cmdPtReal;
        }
        if (diyStudio != null) {
            return CmdPtReal.getDiyCmdPt4DiyStudio(diyStudio);
        }
        if (diyAi != null) {
            return CmdPtReal.makeCmdPt(diyAi.command4PtReal.getCommands4IotPtReal());
        }
        return null;
    }

    /**
     * 生成diy蓝牙指令集合-ble
     *
     * @param diyProtocol
     * @param diyGraffiti4Rgbic
     * @param diyStudio
     * @param diyAi
     * @return
     */
    public static List<String> makeDiy4BleComm(DiyProtocol diyProtocol, DiyGraffitiV2 diyGraffiti4Rgbic, DiyStudio diyStudio, DiyAi diyAi) {
        AbsCmd cmd = makeDiyCmd4IotComm(diyProtocol, diyGraffiti4Rgbic, diyStudio, diyAi);
        if (cmd == null) return null;
        if (cmd instanceof CmdPtReal) {
            return ((CmdPtReal) cmd).getCommand();
        }
        return null;
    }
}