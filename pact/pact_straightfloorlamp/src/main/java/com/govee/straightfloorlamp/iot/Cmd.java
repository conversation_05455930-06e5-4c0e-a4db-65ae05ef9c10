package com.govee.straightfloorlamp.iot;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/3/5
 * cmd常量定义$
 */
public final class Cmd {
    private Cmd() {
    }

    public static final String parse_json_key_state = "state";
    public static final String parse_json_op = "op";

    public static String getCmdReadParseKey(String cmd) {
        if (ptReal.equals(cmd)) return parse_json_op;
        return parse_json_key_state;
    }

    public static final String online = "online";
    public static final String turn = "turn";
    public static final String brightness = "brightness";
    public static final String status = "status";
    public static final String colorwc = "colorwc";
    public static final String ptReal = "ptReal";

    public static final String pt = "pt";
 }