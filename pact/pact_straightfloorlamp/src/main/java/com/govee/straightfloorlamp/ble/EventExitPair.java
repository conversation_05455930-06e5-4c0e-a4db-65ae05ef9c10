package com.govee.straightfloorlamp.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by hey on 2020/11/27
 * $
 */
public class EventExitPair extends AbsControllerEvent {

    private EventExitPair(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    public static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventExitPair(false, write, commandType, proType));
    }

    public static void sendSuc(boolean write, byte commandType, byte proType) {
        EventExitPair eventExitPair = new EventExitPair(true, write, commandType, proType);
        EventBus.getDefault().post(eventExitPair);
    }

}