package com.govee.straightfloorlamp.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.ScenesRgbIC;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.ISubModeScenes;
import com.govee.base2light.light.ModeStr;
import com.govee.straightfloorlamp.pact.Support;
import com.ihoment.base2app.infra.StorageInfra;

import androidx.annotation.NonNull;

/**
 * Create by xie<PERSON><PERSON> on 2019-04-19
 * 场景模式
 */
public class SubModeScenes extends AbsSubMode4Analytic implements ISubModeScenes {
    public int effect = ScenesRgbIC.effect_sunrise;/*默认效果日出*/

    @Override
    public void loadLocal() {
        SubModeScenes subModeScenes = StorageInfra.get(SubModeScenes.class);
        if (subModeScenes == null) return;
        this.effect = subModeScenes.effect;
    }

    public SubModeScenes(String sku) {
        switch (sku) {
            case Support.H8072:
            case Support.H6072:
                effect = ScenesRgbIC.effect_sunrise_h6072;
                break;
            case Support.H6076:
                effect = ScenesRgbIC.effect_movie_h6076;
                break;
        }
    }

    public SubModeScenes() {
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(this);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_scenes;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr = ModeStr.getScenesSubModeStr(effect);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_scenes, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        effect = BleUtil.getSignedShort(validBytes[1], validBytes[0]);
    }

    /**
     * 设置场景模式，读写操作一致，解析一致
     *
     * @param validBytes
     * @return
     */
    public static SubModeScenes parseSubModeScenes4Write(byte[] validBytes) {
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.parse(validBytes);
        return subModeScenes;
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] effectBytes = BleUtil.getSignedBytesFor2(effect, false);
        return new byte[]{subModeCommandType(), effectBytes[0], effectBytes[1]};
    }

    public void setEffect(int effect) {
        this.effect = effect;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_scenes;
    }
}