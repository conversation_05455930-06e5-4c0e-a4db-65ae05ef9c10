package com.govee.straightfloorlamp.add;

import android.app.Activity;
import android.bluetooth.BluetoothDevice;
import android.content.Context;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.AbsConnectDialog4Secret;
import com.govee.straightfloorlamp.ble.Ble;

/**
 * Create by hey on 2021/3/4
 * $需要配对的设备 连接弹窗 蓝牙配对
 */
public class ConnectDialogV2 extends AbsConnectDialog4Secret {
    private final AddInfo addInfo;

    private ConnectDialogV2(Context context, BluetoothDevice device, AddInfo addInfo) {
        super(context, device, addInfo.sku);
        this.addInfo = addInfo;
        /*注册*/
        getBle().registerEvent(true, this.getClass().getName());
    }

    public static ConnectDialogV2 createDialog(Context context, BluetoothDevice device, AddInfo addInfo) {
        return new ConnectDialogV2(context, device, addInfo);
    }

    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected void jumpToPairAc() {
        if (addInfo.goodsType == GoodsType.GOODS_TYPE_VALUE_H60B2) {
            PairAcV2.jump2PairAc(context, addInfo, device);
            return;
        }
        PairAcV1.jump2PairAc((Activity) context, addInfo, device);
    }
}