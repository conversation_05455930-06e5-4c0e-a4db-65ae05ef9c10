package com.govee.straightfloorlamp.add;

import android.os.Parcel;
import android.os.Parcelable;

import androidx.annotation.Keep;

import com.govee.base2home.pact.GoodsType;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-10
 * 扩展信息$
 */
@Keep
public class ExtInfo implements Parcelable {
    public String sku;
    public String device;
    public String deviceName;
    public String bleName;
    public String bleAddress;
    public String versionSoft;
    public String versionHard;
    public String spec;
    public String wifiSoftVersion;
    public String wifiHardVersion;
    public String wifiMac;
    public int deviceNameInputLimit;
    public int goodsType;
    public int pactType = -1;
    public int pactCode = -1;
    public int wifiSsidInputLimit;
    public int wifiPasswordInputLimit;
    public String topic;
    public int ic;

    public ExtInfo() {
    }

    protected ExtInfo(Parcel in) {
        sku = in.readString();
        device = in.readString();
        deviceName = in.readString();
        bleName = in.readString();
        bleAddress = in.readString();
        versionSoft = in.readString();
        versionHard = in.readString();
        spec = in.readString();
        wifiSoftVersion = in.readString();
        wifiHardVersion = in.readString();
        wifiMac = in.readString();
        deviceNameInputLimit = in.readInt();
        goodsType = in.readInt();
        pactType = in.readInt();
        pactCode = in.readInt();
        wifiSsidInputLimit = in.readInt();
        wifiPasswordInputLimit = in.readInt();
        topic = in.readString();
        ic = in.readInt();
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(sku);
        dest.writeString(device);
        dest.writeString(deviceName);
        dest.writeString(bleName);
        dest.writeString(bleAddress);
        dest.writeString(versionSoft);
        dest.writeString(versionHard);
        dest.writeString(spec);
        dest.writeString(wifiSoftVersion);
        dest.writeString(wifiHardVersion);
        dest.writeString(wifiMac);
        dest.writeInt(deviceNameInputLimit);
        dest.writeInt(goodsType);
        dest.writeInt(pactType);
        dest.writeInt(pactCode);
        dest.writeInt(wifiSsidInputLimit);
        dest.writeInt(wifiPasswordInputLimit);
        dest.writeString(topic);
        dest.writeInt(ic);
    }

    @Override
    public int describeContents() {
        return 0;
    }

    public static final Creator<ExtInfo> CREATOR = new Creator<ExtInfo>() {
        @Override
        public ExtInfo createFromParcel(Parcel in) {
            return new ExtInfo(in);
        }

        @Override
        public ExtInfo[] newArray(int size) {
            return new ExtInfo[size];
        }
    };

    public boolean hadCheckProtocol() {
        return goodsType != GoodsType.GOODES_TYPE_NO_SUPPORT && pactType != -1 && pactCode != -1;
    }
}