package com.govee.straightfloorlamp.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.ColorModel;
import com.govee.straightfloorlamp.pact.Comm;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * ble-颜色控制$
 */
public class BleColorCmdBuilderV1 extends AbsBleCmdBuilderV1<ColorModel> {
    @Override
    public IBleCmd createCmd(ColorModel colorModel) {
        return () -> Comm.makeColorController4BleComm(colorModel.goodsType, colorModel.model.versionSoft, colorModel.model.versionHard, colorModel.color).getValue();
    }
}