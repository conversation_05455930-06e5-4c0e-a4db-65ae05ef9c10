package com.govee.straightfloorlamp.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.straightfloorlamp.ble.Ble;
import com.govee.straightfloorlamp.ble.Mode;
import com.govee.straightfloorlamp.ble.ModeController;
import com.govee.straightfloorlamp.ble.SubModeColor;
import com.govee.straightfloorlamp.ble.SubModeColorV2;
import com.govee.straightfloorlamp.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by linshicong on 1/28/21
 * op color操作应用$
 */
public class OpColorCommDialog4SquareBleIot extends AbsOpCommDialog4BleIotV2 {
    private final EffectData.ColorEffect colorEffect;
    private final CmdColorWc colorWc;
    private final boolean isSupportBrightness;

    protected OpColorCommDialog4SquareBleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isSupportBrightness) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_COLOR,-1);
        this.colorEffect = colorEffect;
        this.isSupportBrightness = isSupportBrightness;
        colorWc = CmdColorWc.makeCmdColorWc4Color(colorEffect.colorSet[0]);
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isSupportBrightness) {
        new OpColorCommDialog4SquareBleIot(context, bleAddress, bleName, topic, sku, device, colorEffect, isSupportBrightness).show();
    }

    @Override
    protected @androidx.annotation.Nullable
    AbsCmd getOpCmd() {
        return colorWc;
    }

    @Override
    protected void bleOping() {
        ISubMode subModeColor = isSupportBrightness ? SubModeColorV2.makeSubModeColor(colorEffect.colorSet[0], Support.getGoodsTypeByName(this.sku)) : SubModeColor.makeSubModeColor(colorEffect.colorSet[0]);
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        ModeController controller = new ModeController(mode);
        getBle().startController(controller);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateBleResult(result);
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}