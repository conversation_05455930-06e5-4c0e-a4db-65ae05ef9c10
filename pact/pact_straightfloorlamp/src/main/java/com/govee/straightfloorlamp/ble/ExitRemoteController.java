package com.govee.straightfloorlamp.ble;

import com.govee.base2light.ble.controller.AbsSingleController;

/**
 * Create by hey on 2020/11/27
 * $
 */
public class ExitRemoteController extends AbsSingleController {

    public ExitRemoteController() {
        super(true);
    }

    @Override
    protected boolean writeResult(boolean writeSuc) {
        EventExitPair.sendSuc(isWrite(), getCommandType(), getProType());
        return true;
    }

    @Override
    protected byte[] translateWrite() {
        return null;
    }

    @Override
    protected void fail() {
        EventPair.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.EXIT_REMOTE_CONTROL_PAIR;
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        EventExitPair.sendSuc(isWrite(), getCommandType(), getProType());
        return true;
    }
}