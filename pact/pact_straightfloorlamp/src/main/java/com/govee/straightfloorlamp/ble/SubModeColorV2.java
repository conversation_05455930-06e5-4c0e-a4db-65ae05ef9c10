package com.govee.straightfloorlamp.ble;

import androidx.annotation.NonNull;

import com.govee.base2home.Constant;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.colortemp.base.ISubModeColorTem;
import com.govee.straightfloorlamp.pact.Support;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Create by hey on 2021/2/7
 * $ 支持分段亮度的颜色模式 6072迭代 / 6076 动态设置分段数
 */
public class SubModeColorV2 extends AbsSubMode4Analytic implements ISubModeColorTem {
    public static final int op_type_ui_no_fresh = -1;
    public static final int op_type_gradual = 1;
    public int rgb;
    private int sectionNum;

    public int gradual = 1;/*默认开启渐变*/
    public boolean[] ctlLight;
    public int[] rgbSet;

    public int kelvin;
    public int ctRgb;

    public int[] brightnessSet;
    public int brightness;
    public int opType;

    public SubModeColorV2(int goodsType) {
        this.sectionNum = Support.getBulbStringMaxNum(goodsType);
        ctlLight = new boolean[sectionNum];
    }

    @Override
    public void loadLocal() {
    }

    @Override
    public void saveLocal() {
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr = UtilColor.colorName(rgb);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_color, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        this.gradual = BleUtil.getUnsignedByte(validBytes[0]);
        this.kelvin = BleUtil.getSignedShort(validBytes[1], validBytes[2]);
        if (kelvin != 0) {
            int[] kelvinColor = Constant.getTemColorByKelvin(kelvin);
            this.ctRgb = kelvinColor[2];
        }
    }

    public static SubModeColorV2 parseWriteBytesV2(byte[] writeBytes, int goodsType) {
        byte[] validBytes = new byte[writeBytes.length - 1];
        System.arraycopy(writeBytes, 1, validBytes, 0, validBytes.length);
        return parseSubModeColor4WriteV2(validBytes, goodsType);
    }

    public static SubModeColorV2 parseSubModeColor4WriteV2(byte[] validBytes, int goodsType) {
        SubModeColorV2 subModeColor = new SubModeColorV2(goodsType);
        boolean[] group1ValueN;
        boolean[] ctlLight = new boolean[subModeColor.sectionNum];
        int[] brightnessSet = new int[subModeColor.sectionNum];
        if (validBytes[0] == 1) {
            int r = BleUtil.getUnsignedByte(validBytes[1]);
            int g = BleUtil.getUnsignedByte(validBytes[2]);
            int b = BleUtil.getUnsignedByte(validBytes[3]);
            subModeColor.rgb = ColorUtils.toColor(r, g, b);
            subModeColor.kelvin = BleUtil.getSignedShort(validBytes[4], validBytes[5]);
            r = BleUtil.getUnsignedByte(validBytes[6]);
            g = BleUtil.getUnsignedByte(validBytes[7]);
            b = BleUtil.getUnsignedByte(validBytes[8]);
            subModeColor.ctRgb = ColorUtils.toColor(r, g, b);
            /*前N盏灯的选中状态*/
            group1ValueN = BleUtil.parseBytes4BitReverse(validBytes[9]);
            System.arraycopy(group1ValueN, 0, ctlLight, 0, ctlLight.length);
            subModeColor.ctlLight = ctlLight;
        } else if (validBytes[0] == 2) {
            subModeColor.brightness = validBytes[1];
            /*前N盏灯的选中状态*/
            group1ValueN = BleUtil.parseBytes4BitReverse(validBytes[2]);
            System.arraycopy(group1ValueN, 0, ctlLight, 0, ctlLight.length);
            subModeColor.ctlLight = ctlLight;
        } else if (validBytes[0] == 3) {
            for (int i = 0; i < Math.min(brightnessSet.length, validBytes.length - 1); i++) {
                brightnessSet[i] = BleUtil.getUnsignedByte(validBytes[i + 1]);
            }
            subModeColor.brightnessSet = brightnessSet;
        }
        return subModeColor;
    }

    public static SubModeColorV2 parseSubModeColor4Write(byte[] validBytes) {
        int sectionNum = 0;//第一位不算，要减一位
        for (int i = 1; i < validBytes.length; i++) {
            if (validBytes[i] != 0) sectionNum++;
            else break;
        }
        SubModeColorV2 subModeColor = new SubModeColorV2(0);
        subModeColor.sectionNum = sectionNum;
        if (validBytes[0] == 1) {
            subModeColor.kelvin = BleUtil.getSignedShort(validBytes[4], validBytes[5]);
            if (subModeColor.kelvin != 0) {
                int[] kelvinColor = Constant.getTemColorByKelvin(subModeColor.kelvin);
                subModeColor.ctRgb = kelvinColor[2];
            }
            subModeColor.rgb = ColorUtils.toColor(validBytes[1], validBytes[2], validBytes[3]);
        } else if (validBytes[0] == 2) {
            subModeColor.brightness = validBytes[1];
        } else if (validBytes[0] == 3) {
            int[] brightness = new int[sectionNum];
            for (int i = 1; i < sectionNum + 1; i++) {
                brightness[i - 1] = validBytes[i];
            }
            subModeColor.brightnessSet = brightness;
        }
        return subModeColor;
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] bytes = new byte[17];
        bytes[0] = subModeCommandType();
        if (this.brightness == 0 && this.brightnessSet == null) {
            bytes[1] = (byte) 0x01;
            int[] rgb = ColorUtils.getRgb(this.rgb);
            bytes[2] = (byte) rgb[0];
            bytes[3] = (byte) rgb[1];
            bytes[4] = (byte) rgb[2];
            if (this.kelvin != 0) {
                byte[] signedBytesFor2 = BleUtil.getSignedBytesFor2(kelvin, true);
                bytes[5] = signedBytesFor2[0];
                bytes[6] = signedBytesFor2[1];
                int[] ctRgb = ColorUtils.getRgb(this.ctRgb);
                bytes[7] = (byte) ctRgb[0];
                bytes[8] = (byte) ctRgb[1];
                bytes[9] = (byte) ctRgb[2];
            }
            bytes[10] = (byte) 0x00;
            int temp = 1;
            for (int i = 0; i < sectionNum; i++) {
                if (ctlLight[i]) {
                    bytes[10] = (byte) (bytes[10] | temp);
                }
                temp = temp << 1;
            }
            bytes[11] = (byte) 0x00;
        } else if (this.brightnessSet != null) {
            bytes[1] = (byte) 0x03;
            for (int i = 0; i < brightnessSet.length; i++) {
                bytes[i + 2] = (byte) brightnessSet[i];
            }
        } else {
            bytes[1] = (byte) 0x02;
            bytes[2] = (byte) this.brightness;
            bytes[3] = (byte) 0x00;
            int temp = 1;
            for (int i = 0; i < sectionNum; i++) {
                if (ctlLight[i]) {
                    bytes[3] = (byte) (bytes[3] | temp);
                }
                temp = temp << 1;
            }
            bytes[4] = (byte) 0x00;
        }
        return bytes;
    }

    public static boolean isSetMode2Color(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE &&
                    opByte == BleProtocolConstants.SINGLE_MODE &&
                    modeType == BleProtocol.sub_mode_color_v2;
        }
        return false;
    }

    public static SubModeColorV2 parseSubModeColor2New(@NonNull SubModeColor subModeColor, int goodsType) {
        SubModeColorV2 subModeColorV2 = new SubModeColorV2(goodsType);
        subModeColorV2.ctlLight = subModeColor.ctlLight;
        subModeColorV2.rgb = subModeColor.rgb;
        subModeColorV2.ctRgb = subModeColor.ctRgb;
        subModeColorV2.rgbSet = subModeColor.rgbSet;
        return subModeColorV2;
    }

    public static AbsSingleController[] makeSubModeColor(Colors colorStrip, int goodsType) {
        int[] colors = colorStrip.colorSet;
        int sectionMun = Support.getBulbStringMaxNum(goodsType);
        if (colors.length != sectionMun) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }

        boolean hasBrightness = colorStrip.brightnessSet != null && colorStrip.brightnessSet.length == sectionMun;
        AbsSingleController[] modeControllers = new AbsSingleController[hasBrightness ? hashMap.size() + 1 : hashMap.size()];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV2 subModeColor = new SubModeColorV2(goodsType);
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[subModeColor.sectionNum];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hasBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hasBrightness) {
            SubModeColorV2 subModeColor = new SubModeColorV2(goodsType);
            subModeColor.brightnessSet = colorStrip.brightnessSet;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i] = modeController;
        }
        return modeControllers;
    }

    public static AbsSingleController[] makeSubModeColor(int[] colors, int[] brightnessArray, boolean isFade, boolean hadFadeController, int goodsType) {
        if (colors == null || colors.length != Support.getBulbStringMaxNum(goodsType)) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        boolean hadBrightness = brightnessArray != null && brightnessArray.length == Support.getBulbStringMaxNum(goodsType);
        int fadeSize = hadFadeController ? 1 : 0;
        AbsSingleController[] modeControllers = new AbsSingleController[hadBrightness ? hashMap.size() + fadeSize + 1 : hashMap.size() + fadeSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV2 subModeColor = new SubModeColorV2(goodsType);
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[subModeColor.sectionNum];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hadBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadBrightness) {
            SubModeColorV2 subModeColor = new SubModeColorV2(goodsType);
            subModeColor.brightnessSet = brightnessArray;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            modeControllers[i] = new Gradual4BleWifiController(isFade);
        }
        return modeControllers;
    }

    public static boolean isSetMode2Color4ColorEffect(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opByte == BleProtocolConstants.SINGLE_MODE && modeType == BleProtocol.sub_mode_color_v2;
        }
        return false;
    }

    public static void parsePosColor(byte[] setPosColorBytes, @NonNull SubModeColorV2 subModeColor) {
        if (setPosColorBytes == null || setPosColorBytes.length != 20) return;
        if (subModeColor.rgbSet == null) {
            subModeColor.rgbSet = new int[subModeColor.ctlLight.length];
        }
        /*解颜色*/
        if (setPosColorBytes[3] == 0x01) {
            int rgb = ColorUtils.toColor(setPosColorBytes[4], setPosColorBytes[5], setPosColorBytes[6]);
            int kelvin = BleUtil.convertTwoBytesToShort(setPosColorBytes[7], setPosColorBytes[8]);
            int temRgb = ColorUtils.toColor(setPosColorBytes[9], setPosColorBytes[10], setPosColorBytes[11]);
            int rgbValue;
            if (kelvin > 0) {
                rgbValue = temRgb;
            } else {
                rgbValue = rgb;
            }
            /*从低位bit开始描述*/
            boolean[] low8Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[12]);
            for (int i = 0; i < low8Set.length; i++) {
                if (low8Set[i]) {
                    subModeColor.rgbSet[i] = rgbValue;
                }
            }
            /*解亮度*/
        } else if (setPosColorBytes[3] == 0x03) {
            if (subModeColor.brightnessSet == null) {
                subModeColor.brightnessSet = new int[subModeColor.ctlLight.length];
            }
            int[] brightnessArray = new int[subModeColor.ctlLight.length];
            for (int i = 0; i < subModeColor.ctlLight.length; i++) {
                brightnessArray[i] = BleUtil.getUnsignedByte(setPosColorBytes[i + 4]);
            }
            subModeColor.brightnessSet = brightnessArray;
        }
    }

    public static SubModeColorV2 makeSubModeColor4Group(Colors colors, int goodsType) {
        SubModeColorV2 subModeColorV2 = new SubModeColorV2(goodsType);
        subModeColorV2.rgbSet = colors.colorSet;
        subModeColorV2.brightnessSet = colors.brightnessSet;
        return subModeColorV2;
    }

    public static SubModeColorV2 makeSubModeColor(int color, int goodsType) {
        SubModeColorV2 subModeColor = new SubModeColorV2(goodsType);
        subModeColor.rgb = color;
        subModeColor.kelvin = 0;
        subModeColor.ctRgb = 0;
        int length = subModeColor.ctlLight.length;
        int[] rgbSet = new int[length];
        for (int i = 0; i < length; i++) {
            subModeColor.ctlLight[i] = true;
            rgbSet[i] = color;
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }

    public static SubModeColorV2 makeSubModeColor4Kelvin(int colorTemInKelvin, int goodsType) {
        SubModeColorV2 subModeColor = new SubModeColorV2(goodsType);
        int[] temColorByKelvin = Constant.getTemColorByKelvin(colorTemInKelvin);
        if (temColorByKelvin[0] == 1) {
            subModeColor.kelvin = temColorByKelvin[1];
            subModeColor.ctRgb = temColorByKelvin[2];
        }
        return subModeColor;
    }

    public static SubModeColorV2 makeSubModeColor4Wc(int color, int goodsType) {
        SubModeColorV2 subModeColor = new SubModeColorV2(goodsType);
        int length = subModeColor.ctlLight.length;
        int[] rgbSet = new int[length];
        for (int i = 0; i < length; i++) {
            rgbSet[i] = color;
        }
        subModeColor.rgbSet = rgbSet;
        subModeColor.ctRgb = color;
        subModeColor.rgb = color;
        return subModeColor;
    }

    public static SubModeColorV2 makeSubModeColorByKelvin(int kelvin, int goodsType) {
        SubModeColorV2 subModeColor = new SubModeColorV2(goodsType);
        subModeColor.rgb = 0xFFFFFFFF;
        int[] temColor = Constant.getTemColorByKelvin(kelvin);
        if (temColor[0] == 1) {
            subModeColor.kelvin = kelvin;
            subModeColor.ctRgb = temColor[2];
        }
        Arrays.fill(subModeColor.ctlLight, true);
        return subModeColor;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_color;
    }

    @Override
    public int getColorTemKelvin() {
        if (kelvin > 0) return kelvin;
        return getColorTemKelvinByRgbSet(rgbSet);
    }
}