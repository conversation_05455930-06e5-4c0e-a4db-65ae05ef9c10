package com.govee.straightfloorlamp.ble;

import android.bluetooth.BluetoothGattCallback;

import com.govee.base2light.CommEventGattCallback;
import com.govee.base2light.Constant;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.comm.AbsBleComm;
import com.govee.base2light.ble.comm.AbsMultiple4PtRealBleComm;
import com.govee.base2light.ble.comm.AbsMultipleBleComm;
import com.govee.base2light.ble.comm.INotify;
import com.govee.base2light.util.UtilFlag;

/**
 * Create by xieyingwu on 2020/3/17
 * ble控制器
 */
public class Ble extends AbsBle {
    private static class Builder {
        private static final Ble instance = new Ble();
    }

    public static Ble getInstance = Builder.instance;

    @Override
    protected AbsBleComm generateBleComm() {
        return new BleComm();
    }

    @Override
    protected BluetoothGattCallback getGattCallbackImp() {
        return new CommEventGattCallback();
    }

    @Override
    protected AbsMultipleBleComm generateMultipleBleComm() {
        return new BleMultiComm();
    }

    @Override
    protected INotify generateNotify() {
        return new BleNotifyComm();
    }
    @Override
    protected AbsMultiple4PtRealBleComm generateMultiple4PtRealBleComm() {
        return new BlePtRealMultiComm();
    }
    @Override
    protected boolean needSkipHeart() {
        if (UtilFlag.getInstance.getFlag(Constant.key_flag_updating)) return true;/*升级中，忽略心跳逻辑*/
        if (UtilFlag.getInstance.getFlag(Constant.key_flag_rebooting)) return true;/*重启过程中，忽略心跳逻辑*/
        return inMultipleComm();/*多包通信时，忽略心跳逻辑*/
        /*默认执行心跳逻辑*/
    }
}