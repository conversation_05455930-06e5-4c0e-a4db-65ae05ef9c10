package com.govee.straightfloorlamp;

import android.os.Bundle;

/**
 * Create by hey on 2020/11/24
 * 常量定义$
 */
public class ConsV1 {
    private ConsV1() {
    }

    public static final String intent_ac_key_addInfo = "intent_ac_key_addInfo";

    public static final String intent_ac_adjust_sku = "intent_ac_adjust_sku";
    public static final String intent_ac_adjust_spec = "intent_ac_adjust_spec";
    public static final String intent_ac_adjust_device = "intent_ac_adjust_device";
    public static final String intent_ac_adjust_deviceName = "intent_ac_adjust_deviceName";
    public static final String intent_ac_adjust_goodsType = "intent_ac_adjust_goodsType";
    public static final String intent_ac_adjust_bleAddress = "intent_ac_adjust_bleAddress";
    public static final String intent_ac_adjust_bleName = "intent_ac_adjust_bleName";

    public static final String intent_ac_adjust_wifiMac = "intent_ac_adjust_wifiMac";
    public static final String intent_ac_adjust_topic = "intent_ac_adjust_topic";
    public static final String intent_ac_adjust_versionHard = "intent_ac_adjust_versionHard";
    public static final String intent_ac_adjust_versionSoft = "intent_ac_adjust_versionSoft";
    public static final String intent_ac_adjust_ic = "intent_ac_adjust_ic";
    public static final String intent_ac_adjust_secretCode = "intent_ac_adjust_secretCode";

    public static Bundle makeAdjustAcBundle(String sku, String device, String spec, int goodsType, String deviceName, String bleName, String bleAddress, String wifiMac, String topic, String versionHard,String versionSoft, String secretCode) {
        Bundle bundle = new Bundle();
        bundle.putString(intent_ac_adjust_sku, sku);
        bundle.putString(intent_ac_adjust_spec, spec);
        bundle.putString(intent_ac_adjust_device, device);
        bundle.putString(intent_ac_adjust_deviceName, deviceName);
        bundle.putInt(intent_ac_adjust_goodsType, goodsType);
        bundle.putString(intent_ac_adjust_bleAddress, bleAddress);
        bundle.putString(intent_ac_adjust_bleName, bleName);
        bundle.putString(intent_ac_adjust_wifiMac, wifiMac);
        bundle.putString(intent_ac_adjust_topic, topic);
        bundle.putString(intent_ac_adjust_versionHard, versionHard);
        bundle.putString(intent_ac_adjust_versionSoft, versionSoft);
        bundle.putString(intent_ac_adjust_secretCode, secretCode);
        return bundle;
    }
}