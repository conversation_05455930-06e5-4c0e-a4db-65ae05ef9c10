package com.govee.straightfloorlamp.scenes;

import com.govee.base2home.scenes.BaseCmd;
import com.govee.base2home.scenes.ICmd;
import com.govee.base2home.scenes.builder.model.BrightnessModel;
import com.govee.straightfloorlamp.pact.Comm;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * iot-亮度控制$
 */
public class IotBrightnessCmdBuilderV1 extends AbsIotCmdBuilderV1<BrightnessModel> {
    @Override
    public ICmd createCmd(BrightnessModel brightnessModel) {
        return new BaseCmd() {

            @Override
            public String getIotCmd() {
                return makeCmdStr(Comm.makeBrightnessCmd4IotComm(brightnessModel.brightness));
            }
        };
    }
}