package com.govee.straightfloorlamp.ble;

import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.comm.AbsNotifyParse;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020/9/14
 * 亮度-解析$
 */
class BrightnessNotifyParse extends AbsNotifyParse {
    @Override
    protected byte getNotifyType() {
        return BleProtocol.notify_brightness;
    }

    @Override
    protected void parseValue(byte[] value) {
        int brightness = BleUtil.getUnsignedByte(value[0]);
        EventBrightnessNotify.sendEventBrightnessNotify(brightness);
    }
}