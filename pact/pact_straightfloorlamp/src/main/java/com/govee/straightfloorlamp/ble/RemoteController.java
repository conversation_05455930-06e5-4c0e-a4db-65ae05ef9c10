package com.govee.straightfloorlamp.ble;

import com.govee.base2light.ble.controller.AbsSingleController;

/**
 * Create by hey on 2020/11/27
 * $
 */
public class RemoteController extends AbsSingleController {
    private int pair;

    public RemoteController(boolean pairOpen) {
        super(true);
        this.pair = pairOpen ? 1 : 0;
    }

    public RemoteController() {
        super(false);
    }

    @Override
    protected boolean writeResult(boolean writeSuc) {
        EventPair.sendSuc(isWrite(), getCommandType(), getProType(), pair);
        return true;
    }

    @Override
    protected byte[] translateWrite() {
        return new byte[]{(byte) pair};
    }

    @Override
    protected void fail() {
        EventPair.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.REMOTE_CONTROL_PAIR;
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        int pair = validBytes[0];
        EventPair.sendSuc(isWrite(), getCommandType(), getProType(), pair);
        return true;
    }
}