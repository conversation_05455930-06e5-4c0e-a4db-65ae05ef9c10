package com.govee.straightfloorlamp.ble;

import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.SubModeAbsMusic;
import com.govee.straightfloorlamp.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020/7/28
 * 模式解析对象$
 */
public class Mode extends AbsMode {
    private static final String TAG = "Mode";
    public boolean isChange4ColorTem;

    public static ISubMode parseWriteSubMode(boolean isNewMusicMode, byte subModeType, byte[] subModeValidBytes) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parseWriteSubMode() subModeType = " + subModeType + " ; subModeValidBytes = " + BleUtil.bytesToHexString(subModeValidBytes));
        }
        ISubMode subMode;
        if (subModeType == BleProtocol.sub_mode_music) {
            /*区分新音乐模式*/
            if (isNewMusicMode) {
                subMode = SubModeMusicV2.parseSubModeMusic4Write(subModeValidBytes);
            } else {
                subMode = SubModeMusic.parseSubModeMusic4Write(subModeValidBytes);
            }
        } else if (subModeType == BleProtocol.sub_mode_scenes) {
            subMode = SubModeScenes.parseSubModeScenes4Write(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_diy) {
            subMode = SubModeNewDiy.parseSubModeNewDiy4Write(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_color_v2) {
            subMode = SubModeColorV2.parseSubModeColor4Write(subModeValidBytes);
        } else if (subModeType == BleProtocolConstants.sub_mode_abs_music) {
            subMode = SubModeAbsMusic.Companion.parseSubModeMusic4Write(subModeValidBytes);
        } else {
            /*默认都按颜色模式解析*/
            subMode = SubModeColor.parseSubModeColor4Write(subModeValidBytes);
        }
        return subMode;
    }

    @Override
    protected void parseSubMode(byte subModeType, byte[] subModeValidBytes) {
        if (subModeType == BleProtocol.sub_mode_music) {
            /*如何区分新音乐模式*/
            subMode = new SubModeMusic();
            subMode.parse(subModeValidBytes);
            int musicCode = ((SubModeMusic) subMode).effect;
            if (Support.isH6072NewMusicCode(musicCode)) {
                /*新音乐模式*/
                subMode = new SubModeMusicV2();
                subMode.parse(subModeValidBytes);
            }
        } else if (subModeType == BleProtocol.sub_mode_new_diy) {
            subMode = new SubModeNewDiy();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_scenes) {
            subMode = new SubModeScenes();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_color_v2) {
            subMode = new SubModeColorV2(0);
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocolConstants.sub_mode_abs_music) {
            //抽象音乐
            subMode = new SubModeAbsMusic();
            subMode.parse(subModeValidBytes);
        } else {
            /*默认都按照颜色模式解析*/
            subMode = new SubModeColor();
            subMode.parse(subModeValidBytes);
        }
    }

    public void changeMusicMode4MultiNewMusic(int goodsType, int pactType, int pactCode, String versionSoft) {
        int musicVersion = Support.isSupportNewMusicMode(goodsType, pactType, pactCode, versionSoft) ? 2 : 1;
        boolean isMusicV0 = subMode instanceof SubModeMusic;
        boolean isMusicV1 = subMode instanceof SubModeMusicV2;

        if (!isMusicV0 && !isMusicV1) return;
        if (musicVersion == 1) {
            if (!isMusicV0) {
                subMode = new SubModeMusic();
                subMode.loadLocal();
            }
        } else {
            if (!isMusicV1) {
                if (Support.isH6072NewMusicCode(((SubModeMusic) subMode).effect)) {
                    subMode = new SubModeMusicV2();
                    subMode.loadLocal();
                } else {
                    /*旧音乐效果，要去解析成新的音乐模式模型*/
                    subMode = SubModeMusicV2.parseSubModeV1((SubModeMusic) subMode);
                }

            }
        }
    }

    public void changeMusicMode4MultiNewMusic(boolean isNewMusicVersion) {
        int musicVersion = isNewMusicVersion ? 2 : 1;
        boolean isMusicV0 = subMode instanceof SubModeMusic;
        boolean isMusicV1 = subMode instanceof SubModeMusicV2;

        if (!isMusicV0 && !isMusicV1) return;
        if (musicVersion == 1) {
            if (!isMusicV0) {
                subMode = new SubModeMusic();
                subMode.loadLocal();
            }
        } else {
            if (!isMusicV1) {
                if (Support.isH6072NewMusicCode(((SubModeMusic) subMode).effect)) {
                    subMode = new SubModeMusicV2();
                    subMode.loadLocal();
                } else {
                    /*旧音乐效果，要去解析成新的音乐模式模型*/
                    subMode = SubModeMusicV2.parseSubModeV1((SubModeMusic) subMode);
                }

            }
        }
    }
}