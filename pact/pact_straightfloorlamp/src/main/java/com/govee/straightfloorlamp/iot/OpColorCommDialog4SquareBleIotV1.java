package com.govee.straightfloorlamp.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.straightfloorlamp.ble.Ble;
import com.govee.straightfloorlamp.ble.SubModeColor;
import com.govee.straightfloorlamp.ble.SubModeColorV2;
import com.govee.straightfloorlamp.pact.Comm;
import com.govee.straightfloorlamp.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by lins<PERSON>ong on 1/28/21
 * op color操作应用$
 */
public class OpColorCommDialog4SquareBleIotV1 extends AbsOpCommDialog4BleIotV2 {
    private AbsCmd absCmd;
    private AbsSingleController[] modeControllers;
    private int size;

    protected OpColorCommDialog4SquareBleIotV1(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isSupportBrightness) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_COLOR, -1);
        if (isSupportBrightness) {
            modeControllers = SubModeColorV2.makeSubModeColor(colorEffect.colorSet, colorEffect.brightnessSet, colorEffect.isFade(), !colorEffect.noFadeController(), Support.getGoodsTypeByName(sku));
        } else {
            modeControllers = SubModeColor.makeSubModeColor(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
        }
        if (modeControllers == null) return;
        size = modeControllers.length;
        List<byte[]> bytes = new ArrayList<>();
        for (AbsSingleController absSingleController : modeControllers) {
            bytes.add(absSingleController.getValue());
        }
        absCmd = new CmdPtReal(bytes);
    }

    protected OpColorCommDialog4SquareBleIotV1(Context context, String bleAddress, String bleName, String topic, String sku, String device, boolean on) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_SWITCH, -1);
        singleController = Comm.makeSwitchController4BleComm(on);
        absCmd = Comm.makeSwitchCmd4IotComm(on);
    }

    protected OpColorCommDialog4SquareBleIotV1(Context context, String bleAddress, String bleName, String topic, String sku, String device, int brightness4Percent) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_BRIGHTNESS, -1);
        singleController = Comm.makeBrightnessController4BleComm(brightness4Percent);
        absCmd = Comm.makeBrightnessCmd4IotComm(brightness4Percent);
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isSupportBrightness) {
        new OpColorCommDialog4SquareBleIotV1(context, bleAddress, bleName, topic, sku, device, colorEffect, isSupportBrightness).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, boolean on) {
        new OpColorCommDialog4SquareBleIotV1(context, bleAddress, bleName, topic, sku, device, on).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, int brightness4Percent) {
        new OpColorCommDialog4SquareBleIotV1(context, bleAddress, bleName, topic, sku, device, brightness4Percent).show();
    }

    @Nullable
    @Override
    protected AbsCmd getOpCmd() {
        return absCmd;
    }

    @Override
    protected void bleOping() {
        if (modeControllers != null && modeControllers.length > 0) {
            getBle().startController(modeControllers);
            return;
        }
        if (singleController != null) {
            getBle().startController(singleController);
        }

    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        size--;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result + "---size:" + size);
        }
        if (!result || size <= 1) {
            updateBleResult(result);
            hide();
        }
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}