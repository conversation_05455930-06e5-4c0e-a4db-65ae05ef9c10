package com.govee.straightfloorlamp.add;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import com.govee.base2home.ac.ActivityMgr;
import com.govee.base2home.device.IDeviceNet;
import com.govee.base2home.device.net.DeviceBindRequest;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.scenes.SecretKeyConfig;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventHardVersion;
import com.govee.base2light.ble.controller.EventSn;
import com.govee.base2light.ble.controller.EventSoftVersion;
import com.govee.base2light.ble.controller.EventWifiHardVersion;
import com.govee.base2light.ble.controller.EventWifiMac;
import com.govee.base2light.ble.controller.EventWifiSoftVersion;
import com.govee.base2light.ble.controller.HardVersionController;
import com.govee.base2light.ble.controller.SnController;
import com.govee.base2light.ble.controller.SoftVersionController;
import com.govee.base2light.ble.controller.WifiHardVersionController;
import com.govee.base2light.ble.controller.WifiMacController;
import com.govee.base2light.ble.controller.WifiSoftVersionController;
import com.govee.base2light.pact.AbsPairAc4SecretV1;
import com.govee.bind.SafeBindMgr;
import com.govee.bind.bean.ConfirmGidReq;
import com.govee.bind.engine.BindDevServerAddInfoImpl;
import com.govee.bind.engine.ReadDeviceGid4Ble;
import com.govee.db.memory.ShortMemoryMgr;
import com.govee.kt.ui.device.FastConnectConfig;
import com.govee.matter.MatterManager;
import com.govee.matter.pact.MtPairProtocol;
import com.govee.matter.pair.MatterInfo;
import com.govee.straightfloorlamp.ble.Ble;
import com.govee.ui.R;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.Cache;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.network.Network;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.JumpUtil;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2021/6/8
 * 配对流程$
 */
public class PairAcV2 extends AbsPairAc4SecretV1 {
    private static final String intent_ac_key_addInfo = "intent_ac_key_addInfo";
    private AddInfo addInfo;
    private String videoUrl;
    /**
     * 设备matter信息
     */
    private MatterInfo matterPairInfo;

    /**
     * 跳转到配对页面
     *
     * @param ac
     * @param addInfo
     * @param bluetoothDevice
     */
    public static void jump2PairAc(Context ac, @NonNull AddInfo addInfo, BluetoothDevice bluetoothDevice) {
        Bundle bundle = makeAcBundle(addInfo.sku, bluetoothDevice);
        bundle.putParcelable(intent_ac_key_addInfo, addInfo);
        JumpUtil.jump(ac, PairAcV2.class, bundle);
    }

    @Override
    protected void parseParams(Intent intent) {
        super.parseParams(intent);
        addInfo = intent.getParcelableExtra(intent_ac_key_addInfo);
    }

    @Override
    protected void finishAc() {
        finish();
    }

    @Override
    protected void readDeviceInfo() {
        AbsSingleController[] controllers = new AbsSingleController[]{new SoftVersionController(), new HardVersionController(), new WifiSoftVersionController(), new WifiHardVersionController(), new WifiMacController(), new SnController(),};
        getBle().startController(controllers);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSoftVersionEvent(EventSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "softVersion = " + softVersion);
            }
            addInfo.versionSoft = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onHardVersionEvent(EventHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "hardVersion = " + hardVersion);
            }
            addInfo.versionHard = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiSoftVersionEvent(EventWifiSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiSoftVersionEvent() softVersion = " + softVersion);
            }
            addInfo.wifiSoftVersion = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiHardVersionEvent(EventWifiHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiHardVersionEvent() hardVersion = " + hardVersion);
            }
            addInfo.wifiHardVersion = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiMacEvent(EventWifiMac event) {
        if (event.isResult()) {
            String mac = event.getMac();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiMacEvent() mac = " + mac);
            }
            addInfo.wifiMac = mac;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSnEvent(EventSn event) {
        if (event.isResult()) {
            String device = event.getUuid();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "device = " + device);
            }
            addInfo.device = device;
        }
        getBle().controllerEvent(event);
        if (event.isResult()) {
            //获取matter信息
            if (MtPairProtocol.INSTANCE.supportMatter(addInfo.goodsType, addInfo.sku, addInfo.versionSoft, addInfo.versionHard, addInfo.wifiSoftVersion, addInfo.wifiHardVersion)) {
                MatterManager.Companion.instance().getMtInfo4AddOrUp(Ble.getInstance, new MatterManager.OnMtInfo4AddUpListener() {
                    @Override
                    public void getBaseInfo(boolean isSupportMatter, boolean hasPairNet, boolean hasAddedEcology) {
                        if (matterPairInfo == null) {
                            matterPairInfo = new MatterInfo();
                        }
                        matterPairInfo.setSupportMatter(isSupportMatter);
                        matterPairInfo.setHasPairNet(hasPairNet);
                        matterPairInfo.setHasAddedEcology(hasAddedEcology);
                    }

                    @Override
                    public void getGid(@NonNull String gid) {
                        if (matterPairInfo != null) {
                            matterPairInfo.setGid(gid);
                        }
                    }

                    @Override
                    public void getMatterId(@NonNull String matterId) {
                        if (matterPairInfo != null) {
                            matterPairInfo.setMatterId(matterId);
                        }
                    }

                    @Override
                    public void finish() {
                        bindDevice();
                    }
                });
            } else {
                //进行绑定操作
                bindDevice();
            }
        }
    }

    private void bindDevice() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "bindDevice()");
        }
        /*构建absDevice对象，用于绑定设备*/
        DeviceExtMode deviceExt = new DeviceExtMode();
        deviceExt.setLastDeviceData("{}");
        deviceExt.setDeviceSettings(makeDeviceSettingsJsonStr(addInfo.wifiMac));
        AbsDevice absDevice = new AbsDevice(addInfo.device, addInfo.sku, addInfo.versionHard, addInfo.versionSoft, addInfo.deviceName, addInfo.goodsType, deviceExt);
        BindDevServerAddInfoImpl bindImpl = new BindDevServerAddInfoImpl(this, absDevice, PairAcV2.this::makeDeviceSettingsJsonStr, bindResult -> {
            if (bindResult) {
                beBindSuc();
            } else {
                /*绑定失败*/
                exitPairProcessing();
            }
            return null;
        });
        SafeBindMgr.INSTANCE.bindDeviceGidCheck(
                new ReadDeviceGid4Ble(getBle()),
                ConfirmGidReq.createConfirmGidReq(absDevice, addInfo.wifiHardVersion, addInfo.wifiSoftVersion),
                addInfo.wifiMac,
                bindImpl,
                () -> {
                    // 直接绑定
                    bindDevice(absDevice);
                    return null;
                }
        );

    }

    private String makeDeviceSettingsJsonStr(String wifiMacFromGid) {
        if (!TextUtils.isEmpty(wifiMacFromGid)) {
            addInfo.wifiMac = wifiMacFromGid;
        }
        BindExt bindExt = new BindExt(addInfo.pactType, addInfo.pactCode);
        bindExt.deviceName = addInfo.deviceName;
        bindExt.bleName = addInfo.bleName;
        bindExt.address = addInfo.bleAddress;
        bindExt.wifiMac = addInfo.wifiMac;
        bindExt.secretCode = addInfo.secretCode;
        bindExt.wifiSoftVersion = addInfo.wifiSoftVersion;
        bindExt.wifiHardVersion = addInfo.wifiHardVersion;

        FastConnectConfig.checkSupportFastConnectNotNull(addInfo.goodsType, addInfo.versionSoft, addInfo.versionHard, addInfo.sku, support -> {
            bindExt.supportBleBroadV3 = support;
            ShortMemoryMgr.INSTANCE.getBleBroadVersionCache().putBleBroadV3Support(bluetoothDevice.getAddress(), bindExt.supportBleBroadV3);
            return null;
        });

        return JsonUtil.toJson(bindExt);
    }

    private void bindDevice(AbsDevice absDevice) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i("makeModel", "SecretKeyController上传 :" + JsonUtil.toJson(absDevice));
        }
        DeviceBindRequest request = new DeviceBindRequest(transactions.createTransaction(), absDevice);
        //5.6.0-->支持matter的设备新增
        if (matterPairInfo != null && matterPairInfo.hasMatterInfo()) {
            //有gid,matterId传1，其他默认传0
            request.bindVersion = 1;
            request.gid = matterPairInfo.getGid();
            request.matterId = matterPairInfo.getMatterId();
        }
        Cache.get(IDeviceNet.class).bindDevice(request).enqueue(new Network.IHCallBack<>(request));
    }

    @Override
    protected void beBindSuc() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "beBindSuc()");
        }
        boolean inBackground = BaseApplication.getBaseApplication().isInBackground();
        if (inBackground) return;/*应用处于后台，则不跳转*/
        ActivityMgr.getInstance().finishAllExceptMain();
        destroy();
        DeviceNameAc.jump2DeviceNameAcV2(PairAcV2.this, addInfo);
    }

    @Override
    protected void saveSecretKey(String secretCode) {
        /*读操作；获取成功密钥，存储密钥*/
        addInfo.secretCode = secretCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "secretCode = " + addInfo.secretCode);
        }
        String address = bluetoothDevice.getAddress();
        SecretKeyConfig.read().saveSecretKey(address, addInfo.secretCode);
    }

    @Override
    protected int getPairIconRes() {
        return R.mipmap.new_pics_add_6061_anzhuang_yindao;
    }

    @Override
    protected void rebind() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "rebind()");
        }
        bindDevice();
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected String getTipsStr() {
        return ResUtil.getString(R.string.b2light_press_hint);
    }
}
