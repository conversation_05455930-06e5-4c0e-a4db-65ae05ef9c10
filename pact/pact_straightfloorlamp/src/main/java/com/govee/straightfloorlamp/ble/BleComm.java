package com.govee.straightfloorlamp.ble;

import com.govee.base2light.ble.comm.AbsBleComm;
import com.govee.base2light.ble.controller.AbsOnlyReadSingleController;
import com.govee.base2light.ble.controller.HeartControllerV2;

import java.util.UUID;


/**
 * Create by xieyingwu on 2020/3/17
 */
public class BleComm extends AbsBleComm {
    public static final UUID serviceUuid = UUID.fromString("00010203-0405-0607-0809-0a0b0c0d1910");
    public static final UUID characteristicUuid = UUID.fromString("00010203-0405-0607-0809-0a0b0c0d2b11");
    private final HeartControllerV2 heartControllerV2 = new HeartControllerV2();

    @Override
    public UUID getServiceUuid() {
        return serviceUuid;
    }

    @Override
    protected AbsOnlyReadSingleController getHeartController() {
        return heartControllerV2;
    }

    @Override
    public UUID getCharacteristicUuid() {
        return characteristicUuid;
    }
}