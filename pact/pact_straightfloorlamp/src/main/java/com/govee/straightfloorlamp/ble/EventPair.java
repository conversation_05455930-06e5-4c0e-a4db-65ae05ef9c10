package com.govee.straightfloorlamp.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by hey on 2020/11/27
 * $
 */
public class EventPair extends AbsControllerEvent {
    private int value;

    private EventPair(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    public static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventPair(false, write, commandType, proType));
    }

    public static void sendSuc(boolean write, byte commandType, byte proType, int value) {
        EventPair eventPair = new EventPair(true, write, commandType, proType);
        eventPair.value = value;
        EventBus.getDefault().post(eventPair);
    }

    public int getValue() {
        return value;
    }
}