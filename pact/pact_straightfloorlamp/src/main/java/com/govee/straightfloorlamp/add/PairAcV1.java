package com.govee.straightfloorlamp.add;

import android.app.Activity;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import com.govee.base2home.ac.ActivityMgr;
import com.govee.base2home.device.IDeviceNet;
import com.govee.base2home.device.net.DeviceBindRequest;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.main.OfflineDeviceListConfig;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.scenes.SecretKeyConfig;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventHardVersion;
import com.govee.base2light.ble.controller.EventSn;
import com.govee.base2light.ble.controller.EventSoftVersion;
import com.govee.base2light.ble.controller.EventWifiHardVersion;
import com.govee.base2light.ble.controller.EventWifiMac;
import com.govee.base2light.ble.controller.EventWifiSoftVersion;
import com.govee.base2light.ble.controller.HardVersionController;
import com.govee.base2light.ble.controller.SnController;
import com.govee.base2light.ble.controller.SoftVersionController;
import com.govee.base2light.ble.controller.WifiHardVersionController;
import com.govee.base2light.ble.controller.WifiMacController;
import com.govee.base2light.ble.controller.WifiSoftVersionController;
import com.govee.base2light.pact.AbsPairAc4SecretV1;
import com.govee.bind.SafeBindMgr;
import com.govee.bind.bean.ConfirmGidReq;
import com.govee.bind.engine.BindDevServerAddInfoImpl;
import com.govee.bind.engine.ReadDeviceGid4Ble;
import com.govee.db.memory.ShortMemoryMgr;
import com.govee.home.account.config.AccountConfig;
import com.govee.kt.ui.device.FastConnectConfig;
import com.govee.straightfloorlamp.ble.Ble;
import com.govee.straightfloorlamp.pact.Support;
import com.govee.ui.R;
import com.ihoment.base2app.Cache;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.network.Network;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.JumpUtil;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;

/**
 * Create by DengFei on 2020-02-18
 * 配对Ac$
 */
public class PairAcV1 extends AbsPairAc4SecretV1 {
    private static final String intent_ac_key_addInfo = "intent_ac_key_addInfo";
    private AddInfo addInfo;
    protected AbsDevice absDevice;

    @Override
    protected void readDeviceInfo() {
        AbsSingleController[] controllers;
        if (Arrays.asList(Support.supportIotGoodsTypeSet).contains(String.valueOf(addInfo.goodsType))) {
            controllers = new AbsSingleController[]{
                    new SoftVersionController(),
                    new HardVersionController(),
                    new WifiSoftVersionController(),
                    new WifiHardVersionController(),
                    new WifiMacController(),
                    new SnController(),
            };
        } else {
            controllers = new AbsSingleController[]{
                    new SoftVersionController(),
                    new HardVersionController(),
                    new SnController(),
            };
        }
        getBle().startController(controllers);
    }

    public static void jump2PairAc(Activity activity, AddInfo addInfo, BluetoothDevice bluetoothDevice) {
        Bundle bundle = makeAcBundle(addInfo.sku, bluetoothDevice);
        bundle.putParcelable(intent_ac_key_addInfo, addInfo);
        JumpUtil.jump(activity, PairAcV1.class, bundle);
    }

    @Override
    protected void doCheckPermissionPre() {
        Intent intent = getIntent();
        addInfo = intent.getParcelableExtra(intent_ac_key_addInfo);
        if (addInfo != null && !TextUtils.isEmpty(addInfo.bleAddress)) {
            ShortMemoryMgr.INSTANCE.getBleBroadVersionCache().putBleBroadV3Support(addInfo.bleAddress, false);
        }
        super.doCheckPermissionPre();
    }

    @Override
    protected void finishAc() {
        finish();
    }

    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected String getTipsStr() {
        return ResUtil.getString(R.string.b2light_press_hint);
    }

    @Override
    protected int getPairIconRes() {
        if (addInfo != null) {
            if (addInfo.goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
                return R.mipmap.new_add_list_type_device_6076;
            }
        }
        return R.mipmap.new_pics_add_6055_anzhuang_yindao;
    }

    @Override
    protected void rebind() {
        toBindDevice();
    }

    private void toBindDevice() {
        /*构建absDevice对象，用于绑定设备*/
        DeviceExtMode deviceExt = new DeviceExtMode();
        deviceExt.setLastDeviceData("{}");

        if (Arrays.asList(Support.supportIotGoodsTypeSet).contains(String.valueOf(addInfo.goodsType))) {
            deviceExt.setDeviceSettings(makeDeviceSettingsJsonStr4BleIot(addInfo.wifiMac));
        } else {
            deviceExt.setDeviceSettings(makeDeviceSettingsJsonStr4Ble());
            boolean login = AccountConfig.read().isHadToken();
            if (!login) {
                absDevice = new AbsDevice(addInfo.device, addInfo.sku, addInfo.versionHard, addInfo.versionSoft, addInfo.deviceName, addInfo.goodsType, deviceExt);
                OfflineDeviceListConfig.read().addOfflineDevice(absDevice);
                beBindSuc();
                return;
            }
        }
        absDevice = new AbsDevice(addInfo.device, addInfo.sku, addInfo.versionHard, addInfo.versionSoft, addInfo.deviceName, addInfo.goodsType, deviceExt);

        BindDevServerAddInfoImpl bindImpl = new BindDevServerAddInfoImpl(this, absDevice, this::makeDeviceSettingsJsonStr4BleIot, bindResult -> {
            if (bindResult) {
                beBindSuc();
            } else {
                /*绑定失败*/
                exitPairProcessing();
            }
            return null;
        });
        SafeBindMgr.INSTANCE.bindDeviceGidCheck(
                new ReadDeviceGid4Ble(getBle()),
                ConfirmGidReq.createConfirmGidReq(absDevice, addInfo.wifiHardVersion, addInfo.wifiSoftVersion),
                addInfo.wifiMac,
                bindImpl,
                () -> {
                    // 直接绑定
                    bindDevice(absDevice);
                    return null;
                }
        );
    }

    @Override
    protected void saveSecretKey(String secretCode) {
        /*读操作；获取成功密钥，存储密钥*/
        addInfo.secretCode = secretCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "secretCode = " + addInfo.secretCode);
        }
        String address = bluetoothDevice.getAddress();
        SecretKeyConfig.read().saveSecretKey(address, addInfo.secretCode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSoftVersionEvent(EventSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "softVersion = " + softVersion);
            }
            addInfo.versionSoft = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onHardVersionEvent(EventHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "hardVersion = " + hardVersion);
            }
            addInfo.versionHard = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSnEvent(EventSn event) {
        if (event.isResult()) {
            String device = event.getUuid();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "device = " + device);
            }
            addInfo.device = device;
            toBindDevice();
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiSoftVersionEvent(EventWifiSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiSoftVersionEvent() softVersion = " + softVersion);
            }
            addInfo.wifiSoftVersion = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiHardVersionEvent(EventWifiHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiHardVersionEvent() hardVersion = " + hardVersion);
            }
            addInfo.wifiHardVersion = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiMacEvent(EventWifiMac event) {
        if (event.isResult()) {
            String mac = event.getMac();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiMacEvent() mac = " + mac);
            }
            addInfo.wifiMac = mac;
        }
        getBle().controllerEvent(event);
    }

    /*ble*/
    private String makeDeviceSettingsJsonStr4Ble() {
        BindExt bindExt = new BindExt();
        bindExt.pactType = addInfo.pactType;
        bindExt.pactCode = addInfo.pactCode;
        bindExt.deviceName = addInfo.deviceName;
        bindExt.bleName = addInfo.bleName;
        bindExt.address = addInfo.bleAddress;
        bindExt.secretCode = addInfo.secretCode;
        bindExt.device = addInfo.device;
        FastConnectConfig.checkSupportFastConnectNotNull(addInfo.goodsType, addInfo.versionSoft, addInfo.versionHard, addInfo.sku, support -> {
            bindExt.supportBleBroadV3 = support;
            ShortMemoryMgr.INSTANCE.getBleBroadVersionCache().putBleBroadV3Support(bluetoothDevice.getAddress(), bindExt.supportBleBroadV3);
            return null;
        });
        return JsonUtil.toJson(bindExt);
    }

    /*ble,iot*/
    private String makeDeviceSettingsJsonStr4BleIot(String wifiMacFromGid) {
        if (!TextUtils.isEmpty(wifiMacFromGid)) {
            addInfo.wifiMac = wifiMacFromGid;
        }
        BindExt bindExt = new BindExt();
        bindExt.pactType = addInfo.pactType;
        bindExt.pactCode = addInfo.pactCode;
        bindExt.deviceName = addInfo.deviceName;
        bindExt.bleName = addInfo.bleName;
        bindExt.address = addInfo.bleAddress;
        bindExt.secretCode = addInfo.secretCode;
        bindExt.wifiMac = addInfo.wifiMac;
        bindExt.wifiSoftVersion = addInfo.wifiSoftVersion;
        bindExt.wifiHardVersion = addInfo.wifiHardVersion;
        bindExt.device = addInfo.device;
        FastConnectConfig.checkSupportFastConnectNotNull(addInfo.goodsType, addInfo.versionSoft, addInfo.versionHard, addInfo.sku, support -> {
            bindExt.supportBleBroadV3 = support;
            ShortMemoryMgr.INSTANCE.getBleBroadVersionCache().putBleBroadV3Support(bluetoothDevice.getAddress(), bindExt.supportBleBroadV3);
            return null;
        });
        return JsonUtil.toJson(bindExt);
    }

    private void bindDevice(AbsDevice absDevice) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i("makeModel", "SecretKeyController上传 :" + JsonUtil.toJson(absDevice));
        }
        DeviceBindRequest request = new DeviceBindRequest(transactions.createTransaction(), absDevice);
        Cache.get(IDeviceNet.class).bindDevice(request).enqueue(new Network.IHCallBack<>(request));
    }

    protected void beBindSuc() {
        ActivityMgr.getInstance().finishAllExceptMain();
        destroy();
//        ExtInfo bindExt = new ExtInfo();
//        bindExt.pactType = addInfo.pactType;
//        bindExt.pactCode = addInfo.pactCode;
//        bindExt.deviceName = addInfo.deviceName;
//        bindExt.bleName = addInfo.bleName;
//        bindExt.bleAddress = addInfo.bleAddress;
//        bindExt.secretCode = addInfo.secretCode;
//        bindExt.goodsType = addInfo.goodsType;
//        bindExt.sku = addInfo.sku;
//        bindExt.device = addInfo.device;
//        bindExt.wifiHardVersion = addInfo.wifiHardVersion;
//        bindExt.wifiSoftVersion = addInfo.wifiSoftVersion;
//        bindExt.wifiMac = addInfo.wifiMac;
        DeviceNameAc.jump2DeviceNameAcV2(PairAcV1.this, addInfo);
    }

}
