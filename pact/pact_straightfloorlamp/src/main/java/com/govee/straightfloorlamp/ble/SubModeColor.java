package com.govee.straightfloorlamp.ble;

import androidx.annotation.NonNull;

import com.govee.base2home.Constant;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.colortemp.base.ISubModeColorTem;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Create by xieyingwu on 2020/7/28
 * 颜色模式-仅白色下支持设置色温$
 */
public class SubModeColor extends AbsSubMode4Analytic implements ISubModeColorTem {
    public static final int op_type_ui_no_fresh = -1;
    public static final int op_type_gradual = 1;
    public int rgb;

    public int gradual = 1;/*默认开启渐变*/
    public boolean[] ctlLight = new boolean[8];
    public int[] rgbSet;

    public int kelvin;
    public int ctRgb;
    public int opType;

    public static SubModeColor makeSubModeColor4Kelvin(int colorTemInKelvin) {
        SubModeColor subModeColor = new SubModeColor();
        int[] temColorByKelvin = Constant.getTemColorByKelvin(colorTemInKelvin);
        if (temColorByKelvin[0] == 1) {
            subModeColor.kelvin = temColorByKelvin[1];
            subModeColor.ctRgb = temColorByKelvin[2];
            subModeColor.rgb = temColorByKelvin[2];
        }
        return subModeColor;
    }

    @Override
    public void loadLocal() {
    }

    @Override
    public void saveLocal() {
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_color;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr = UtilColor.colorName(rgb);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_color, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        this.gradual = BleUtil.getUnsignedByte(validBytes[0]);
        this.kelvin = BleUtil.getSignedShort(validBytes[1], validBytes[2]);
        if (kelvin != 0) {
            int[] kelvinColor = Constant.getTemColorByKelvin(kelvin);
            this.rgb = kelvinColor[2];
        }
    }

    public static SubModeColor parseSubModeColor4Write(byte[] validBytes) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.kelvin = BleUtil.getSignedShort(validBytes[3], validBytes[4]);
        if (subModeColor.kelvin != 0) {
            int[] kelvinColor = Constant.getTemColorByKelvin(subModeColor.kelvin);
            subModeColor.rgb = kelvinColor[2];
        } else {
            subModeColor.rgb = ColorUtils.toColor(validBytes[0], validBytes[1], validBytes[2]);
        }
        return subModeColor;
    }

    public static SubModeColor parseWriteBytesV2(byte[] writeBytes) {
        byte[] validBytes = new byte[writeBytes.length - 1];
        System.arraycopy(writeBytes, 1, validBytes, 0, validBytes.length);
        return parseSubModeColor4WriteV2(validBytes);
    }

    public static SubModeColor parseSubModeColor4WriteV2(byte[] validBytes) {
        SubModeColor subModeColor = new SubModeColor();
        int r = BleUtil.getUnsignedByte(validBytes[0]);
        int g = BleUtil.getUnsignedByte(validBytes[1]);
        int b = BleUtil.getUnsignedByte(validBytes[2]);
        subModeColor.rgb = ColorUtils.toColor(r, g, b);
        if (r == 0 && g == 0 && b == 0) {
            r = BleUtil.getUnsignedByte(validBytes[5]);
            g = BleUtil.getUnsignedByte(validBytes[6]);
            b = BleUtil.getUnsignedByte(validBytes[7]);
            subModeColor.rgb = ColorUtils.toColor(r, g, b);
        }
        boolean[] ctlLight = new boolean[15];
        /*前8盏灯的选中状态*/
        boolean[] group1Value8 = BleUtil.parseBytes4BitReverse(validBytes[9]);
        System.arraycopy(group1Value8, 0, ctlLight, 0, group1Value8.length);
        subModeColor.ctlLight = ctlLight;
        return subModeColor;
    }

    public static SubModeColor parseWriteBytesV3(byte[] writeBytes) {
        byte[] validBytes = new byte[writeBytes.length - 2];
        System.arraycopy(writeBytes, 2, validBytes, 0, validBytes.length);
        return parseSubModeColor4WriteV3(validBytes);
    }

    public static SubModeColor parseSubModeColor4WriteV3(byte[] validBytes) {
        SubModeColor subModeColor = new SubModeColor();
        int r = BleUtil.getUnsignedByte(validBytes[0]);
        int g = BleUtil.getUnsignedByte(validBytes[1]);
        int b = BleUtil.getUnsignedByte(validBytes[2]);
        subModeColor.rgb = ColorUtils.toColor(r, g, b);
        if (r == 0 && g == 0 && b == 0) {
            r = BleUtil.getUnsignedByte(validBytes[5]);
            g = BleUtil.getUnsignedByte(validBytes[6]);
            b = BleUtil.getUnsignedByte(validBytes[7]);
            subModeColor.rgb = ColorUtils.toColor(r, g, b);
        }
        boolean[] ctlLight = new boolean[8];
        /*前8盏灯的选中状态*/
        boolean[] group1Value8 = BleUtil.parseBytes4BitReverse(validBytes[8]);
        System.arraycopy(group1Value8, 0, ctlLight, 0, group1Value8.length);
        subModeColor.ctlLight = ctlLight;
        return subModeColor;
    }


    @Override
    public byte[] getWriteBytes() {
        byte[] values = new byte[11];
        values[0] = subModeCommandType();
        int[] rgb = ColorUtils.getRgb(this.rgb);
        values[1] = (byte) rgb[0];
        values[2] = (byte) rgb[1];
        values[3] = (byte) rgb[2];
        byte[] signedBytesFor2 = BleUtil.getSignedBytesFor2(kelvin, true);
        values[4] = signedBytesFor2[0];
        values[5] = signedBytesFor2[1];

        int[] temColorByKelvin = Constant.getTemColorByKelvin(kelvin);
        if (temColorByKelvin[0] == 1) {
            ctRgb = temColorByKelvin[2];
        }
        int[] ctRgb = ColorUtils.getRgb(this.ctRgb);
        values[6] = (byte) ctRgb[0];
        values[7] = (byte) ctRgb[1];
        values[8] = (byte) ctRgb[2];
        /*目前只有8段*/
        values[9] = (byte) 0x00;
        values[10] = (byte) 0x00;
        int temp = 1;
        for (int i = 0; i < 8; i++) {
            if (ctlLight[i]) {
                values[10] = (byte) (values[10] | temp);
            }
            temp = temp << 1;
        }
        return values;
    }

    public static SubModeColor makeSubModeColor(int color) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgb = color;
        int length = subModeColor.ctlLight.length;
        int[] rgbSet = new int[length];
        for (int i = 0; i < length; i++) {
            rgbSet[i] = color;
            subModeColor.ctlLight[i] = true;
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }

    public static SubModeColor makeSubModeColor4Group(Colors colors) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgbSet = colors.colorSet;
        return subModeColor;
    }

    public static SubModeColor makeSubModeColor4Wc(int color) {
        SubModeColor subModeColor = new SubModeColor();
        int length = subModeColor.ctlLight.length;
        int[] rgbSet = new int[length];
        for (int i = 0; i < length; i++) {
            rgbSet[i] = color;
        }
        subModeColor.rgbSet = rgbSet;
        subModeColor.rgb = color;
        return subModeColor;
    }

    public static SubModeColor makeSubModeColor4Scenes(int color) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgb = color;
        int length = subModeColor.ctlLight.length;
        for (int i = 0; i < length; i++) {
            subModeColor.ctlLight[i] = true;
        }
        return subModeColor;
    }

    public static SubModeColor makeSubModeColor4ScenesByKelvin(int kelvin) {
        SubModeColor subModeColor = new SubModeColor();
        int[] temColorByKelvin = Constant.getTemColorByKelvin(kelvin);
        if (temColorByKelvin[0] == 1) {
            subModeColor.kelvin = temColorByKelvin[1];
            subModeColor.ctRgb = temColorByKelvin[2];
        }
        int length = subModeColor.ctlLight.length;
        for (int i = 0; i < length; i++) {
            subModeColor.ctlLight[i] = true;
        }
        return subModeColor;
    }

    public static AbsSingleController[] makeSubModeColor(int[] colors, boolean isFade, boolean hadFadeController) {
        if (colors.length != 8) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        int fadeSize = hadFadeController ? 1 : 0;
        AbsSingleController[] modeControllers = new AbsSingleController[hashMap.size() + fadeSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColor subModeColor = new SubModeColor();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[8];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            modeControllers[i] = new Gradual4BleWifiController(isFade);
        }
        return modeControllers;
    }

    public static AbsSingleController[] makeSubModeColor(Colors colorStrip) {
        int[] colors = colorStrip.colorSet;
        if (colors.length != 8) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        AbsSingleController[] modeControllers = new AbsSingleController[hashMap.size()];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColor subModeColor = new SubModeColor();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[8];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        return modeControllers;
    }

    public static boolean isSetMode2Color(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE &&
                    opByte == BleProtocolConstants.SINGLE_MODE &&
                    modeType == BleProtocol.sub_mode_color;
        }
        return false;
    }

    public static boolean isSetMode2Color4ColorEffect(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opByte == BleProtocolConstants.SINGLE_MODE && modeType == BleProtocol.sub_mode_color;
        }
        return false;
    }


    public static void parsePosColor(byte[] setPosColorBytes, @NonNull SubModeColor subModeColor) {
        if (setPosColorBytes == null || setPosColorBytes.length != 20) return;
        if (subModeColor.rgbSet == null) {
            subModeColor.rgbSet = new int[subModeColor.ctlLight.length];
        }
        int rgb = ColorUtils.toColor(setPosColorBytes[3], setPosColorBytes[4], setPosColorBytes[5]);
        /*从低位bit开始描述*/
        boolean[] low8Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[12]);
        for (int i = 0; i < low8Set.length; i++) {
            if (low8Set[i]) {
                subModeColor.rgbSet[i] = rgb;
            }
        }
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_color;
    }

    @Override
    public int getColorTemKelvin() {
        if (kelvin > 0) return kelvin;
        return getColorTemKelvinByRgbSet(rgbSet);
    }
}