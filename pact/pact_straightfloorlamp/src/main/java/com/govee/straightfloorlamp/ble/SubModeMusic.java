package com.govee.straightfloorlamp.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.ModeStr;
import com.ihoment.base2app.infra.StorageInfra;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/7/28
 * 音乐模式$
 */
public class SubModeMusic extends AbsSubMode4Analytic {
    private static final int min_sensitivity = 0;
    private static final int max_sensitivity = 99;

    public int effect = BleProtocol.value_sub_mode_music_energy;/*默认能量*/
    public int sensitivity = max_sensitivity;/*灵敏度*/
    public int subEffect = BleProtocol.value_sub_mode_music_rhythm_dynamic;/*子效果-默认动感*/
    public boolean auto = true;/*颜色自动*/
    public int rgb = 0xFFFF0000;/*颜色-默认红色*/

    public SubModeMusic copy() {
        SubModeMusic subModeMusic = new SubModeMusic();
        subModeMusic.effect = effect;
        subModeMusic.sensitivity = sensitivity;
        subModeMusic.subEffect = subEffect;
        subModeMusic.auto = auto;
        subModeMusic.rgb = rgb;
        return subModeMusic;
    }

    public boolean isSubDynamicEffect() {
        return subEffect == BleProtocol.value_sub_mode_music_rhythm_dynamic;
    }

    public boolean supportSubEffect() {
        return effect == BleProtocol.value_sub_mode_music_rhythm;
    }

    public boolean supportChangeColor() {
        return effect != BleProtocol.value_sub_mode_music_energy;
    }

    @Override
    public void loadLocal() {
        SubModeMusic subModeMusic = StorageInfra.get(SubModeMusic.class);
        if (subModeMusic != null) {
            this.effect = subModeMusic.effect;
            this.sensitivity = subModeMusic.sensitivity;
            this.subEffect = subModeMusic.subEffect;
            this.auto = subModeMusic.auto;
            this.rgb = subModeMusic.rgb;
        }
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(this);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_music;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr;
        if (effect == BleProtocol.value_sub_mode_music_rhythm) {
            subModeStr = ModeStr.value_sub_mode_music_rhythm_str;
        } else if (effect == BleProtocol.value_sub_mode_music_energy) {
            subModeStr = ModeStr.value_sub_mode_music_energy_str;
        } else if (effect == BleProtocol.value_sub_mode_music_jet) {
            subModeStr = ModeStr.value_sub_mode_music_spray_str;
        } else if (effect == BleProtocol.value_sub_mode_music_jump) {
            subModeStr = ModeStr.value_sub_mode_music_bounce_str;
        } else if (effect == BleProtocol.value_sub_mode_music_joy) {
            subModeStr = ModeStr.value_sub_mode_music_joy_str;
        } else {
            subModeStr = ModeStr.value_sub_mode_music_impact_str;
        }
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_music, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        effect = BleUtil.getUnsignedByte(validBytes[0]);
        int sensitivity = BleUtil.getUnsignedByte(validBytes[1]);
        this.sensitivity = Math.max(min_sensitivity, Math.min(sensitivity, max_sensitivity));
        if (effect == BleProtocol.value_sub_mode_music_rhythm) {
            subEffect = BleUtil.getUnsignedByte(validBytes[2]);
            auto = BleUtil.getUnsignedByte(validBytes[3]) == 0;
            if (!auto) {
                int r = BleUtil.getUnsignedByte(validBytes[4]);
                int g = BleUtil.getUnsignedByte(validBytes[5]);
                int b = BleUtil.getUnsignedByte(validBytes[6]);
                rgb = ColorUtils.toColor(r, g, b);
            }
        } else {
            auto = BleUtil.getUnsignedByte(validBytes[2]) == 0;
            if (!auto) {
                int r = BleUtil.getUnsignedByte(validBytes[3]);
                int g = BleUtil.getUnsignedByte(validBytes[4]);
                int b = BleUtil.getUnsignedByte(validBytes[5]);
                rgb = ColorUtils.toColor(r, g, b);
            }
        }
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] bytes;
        if (effect == BleProtocol.value_sub_mode_music_rhythm) {
            bytes = new byte[8];
            bytes[0] = subModeCommandType();
            bytes[1] = (byte) effect;
            bytes[2] = (byte) sensitivity;
            bytes[3] = (byte) subEffect;
            bytes[4] = (byte) (auto ? 0 : 1);
            if (!auto) {
                int[] rgb = ColorUtils.getRgb(this.rgb);
                bytes[5] = (byte) rgb[0];
                bytes[6] = (byte) rgb[1];
                bytes[7] = (byte) rgb[2];
            }
        } else {
            bytes = new byte[7];
            bytes[0] = subModeCommandType();
            bytes[1] = (byte) effect;
            bytes[2] = (byte) sensitivity;
            bytes[3] = (byte) (auto ? 0 : 1);
            if (!auto) {
                int[] rgb = ColorUtils.getRgb(this.rgb);
                bytes[4] = (byte) rgb[0];
                bytes[5] = (byte) rgb[1];
                bytes[6] = (byte) rgb[2];
            }
        }
        return bytes;
    }

    public static ISubMode parseSubModeMusic4Write(byte[] subModeValidBytes) {
        SubModeMusic subModeMusic = new SubModeMusic();
        subModeMusic.parse(subModeValidBytes);
        return subModeMusic;
    }

    public void setDynamic(boolean dynamic) {
        subEffect = dynamic ? BleProtocol.value_sub_mode_music_rhythm_dynamic : BleProtocol.value_sub_mode_music_rhythm_soft;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_music;
    }
}