package com.govee.straightfloorlamp.iot;

import com.govee.base2home.Constant;
import com.govee.base2home.iot.AbsCmd;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.iot.Color;

import androidx.annotation.Keep;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020/7/6
 * cmd=colorwc$
 */
@Keep
public class CmdColorWc extends AbsCmd {
    Color color;/*非冷暖色-色值表示*/
    public int colorTemInKelvin;/*冷暖色-色值表示*/

    public CmdColorWc() {
    }

    public static CmdColorWc makeCmdColorWc4Color(int rgb) {
        CmdColorWc cmdColorWc = new CmdColorWc();
        cmdColorWc.color = new Color(rgb);
        cmdColorWc.colorTemInKelvin = 0;
        return cmdColorWc;
    }

    public static CmdColorWc makeCmdColorWc4Kelvin(int kelvin) {
        CmdColorWc cmdColorWc = new CmdColorWc();
        boolean isColorTem = Constant.getColorTemKelvinSet().contains(kelvin);
        if (isColorTem) {
            cmdColorWc.colorTemInKelvin = kelvin;
        } else {
            cmdColorWc.colorTemInKelvin = 0;
        }
        cmdColorWc.color = new Color(ColorUtils.toWhite());
        return cmdColorWc;
    }

    @Override
    public String getCmd() {
        return Cmd.colorwc;
    }
}