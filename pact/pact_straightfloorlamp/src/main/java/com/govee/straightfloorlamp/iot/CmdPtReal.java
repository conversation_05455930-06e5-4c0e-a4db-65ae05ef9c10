package com.govee.straightfloorlamp.iot;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.util.Encode;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.comm.MultipleBleBytes;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultiDiyGraffitiController;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.controller.MultipleDiyControllerV1;
import com.govee.straightfloorlamp.ble.BleProtocol;
import com.govee.straightfloorlamp.ble.BulbStringColorController;
import com.govee.straightfloorlamp.ble.BulbStringColorControllerV2;
import com.govee.straightfloorlamp.ble.BulbStringColorControllerV3;
import com.govee.straightfloorlamp.ble.Gradual4BleWifiController;
import com.govee.straightfloorlamp.ble.Mode;
import com.govee.straightfloorlamp.ble.ModeController;
import com.govee.straightfloorlamp.ble.SubModeColor;
import com.govee.straightfloorlamp.ble.SubModeColorV2;
import com.govee.straightfloorlamp.ble.SubModeMusicV2;
import com.govee.straightfloorlamp.ble.SubModeNewDiy;
import com.govee.straightfloorlamp.ble.SubModeScenes;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import kotlin.jvm.Transient;

/**
 * Create by xieyingwu on 2020/6/24
 * cmd=ptReal$
 */
@Keep
public class CmdPtReal extends AbsCmd {
    private static final String TAG = "CmdPtReal";
    private final List<String> command = new ArrayList<>();


    public static int OP_VERSION_ABS_MUSIC_MODE = 1;
    public int opVersion;
    @Transient
    public int musicCode = 0;
    @Transient
    public int musicSensitivity = 0;


    protected CmdPtReal() {
    }

    public static CmdPtReal makeCmdPt(List<String> opCommands) {
        CmdPtReal cmdPtReal = new CmdPtReal();
        cmdPtReal.command.addAll(opCommands);
        return cmdPtReal;
    }

    public List<String> getCommand() {
        return command;
    }

    public int commandSize() {
        return command.size();
    }

    @Override
    public String getCmd() {
        return Cmd.ptReal;
    }

    public CmdPtReal(@NonNull AbsSingleController controller) {
        byte[] bytes = controller.getValue();
        String valueStr = Encode.encryptByBase64(bytes);
        command.add(valueStr);
    }

    public CmdPtReal(@NonNull List<byte[]> multipleBytes) {
        for (byte[] multipleByte : multipleBytes) {
            String valueStr = Encode.encryptByBase64(multipleByte);
            command.add(valueStr);
        }
    }

    public CmdPtReal(@NonNull byte[] multipleBytes) {
        String valueStr = Encode.encryptByBase64(multipleBytes);
        command.add(valueStr);
    }

    public Byte getOpCommandByte() {
        if (command.isEmpty()) return null;
        int size = command.size();
        String commandByteStr = command.get(size - 1);
        byte[] bytes = Encode.decryByBase64(commandByteStr);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getOpCommandByte() bytes = " + BleUtil.bytesToHexString(bytes));
        }
        if (bytes != null && bytes.length == 20) {
            return bytes[1];
        }
        return null;
    }

    public byte[] getOpCommandBytes() {
        if (command.isEmpty()) return null;
        int size = command.size();
        String commandByteStr = command.get(size - 1);
        return Encode.decryByBase64(commandByteStr);
    }

    /**
     * 是否是采用ptReal设置分段颜色
     */
    public SubModeColor isPtReal4SetPartColor() {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size >= 2) {
            SubModeColor subModeColor = new SubModeColor();
            /*至少一包设置颜色+一包设置渐变值*/
            for (int i = 0; i < size - 1; i++) {
                String encodeStr = command.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                boolean setMode2Color = SubModeColor.isSetMode2Color4ColorEffect(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColor.parsePosColor(setColor4PosBytes, subModeColor);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "isPtReal4SetPartColor() commandStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
            }
            String encodeStr = command.get(size - 1);
            byte[] gradual20Bytes = Encode.decryByBase64(encodeStr);
            boolean setGradual = Gradual4BleWifiController.isWriteGradualController(gradual20Bytes);
            if (setGradual) {
                subModeColor.gradual = Gradual4BleWifiController.parseGradual4CmdPtReal(gradual20Bytes);
                return subModeColor;
            }
            return null;
        }
        return null;
    }

    /**
     * 是否是采用ptReal设置分段颜色
     */
    public SubModeColorV2 isPtReal4SetPartColorV2(int goodsType) {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size >= 2) {
            SubModeColorV2 subModeColor = new SubModeColorV2(goodsType);
            /*至少一包设置颜色+一包设置渐变值*/
            for (int i = 0; i < size - 1; i++) {
                String encodeStr = command.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                boolean setMode2Color = SubModeColorV2.isSetMode2Color4ColorEffect(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColorV2.parsePosColor(setColor4PosBytes, subModeColor);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "isPtReal4SetPartColor() commandStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
            }
            String encodeStr = command.get(size - 1);
            byte[] gradual20Bytes = Encode.decryByBase64(encodeStr);
            boolean setGradual = Gradual4BleWifiController.isWriteGradualController(gradual20Bytes);
            if (setGradual) {
                subModeColor.gradual = Gradual4BleWifiController.parseGradual4CmdPtReal(gradual20Bytes);
                return subModeColor;
            }
            return null;
        }
        return null;
    }

    public static CmdPtReal getDiyCmdPt(DiyProtocol diyProtocol) {
        MultipleDiyControllerV1 multipleDiyController = new MultipleDiyControllerV1(diyProtocol);
        /*diy效果数据*/
        List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(multipleDiyController);
        Mode mode = new Mode();
        mode.subMode = new SubModeNewDiy(diyProtocol.getDiyCode());
        ModeController modeController = new ModeController(mode);
        /*切换至diy模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytes.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytes);
    }

    public static CmdPtReal getNewScenesCmdPtReal(AbsMultipleControllerV14Scenes multiNewScenesControllerV1) {
        List<byte[]> multipleWriteBytesV1 = MultipleBleBytes.getMultipleWriteBytesV1(multiNewScenesControllerV1);
        if (multipleWriteBytesV1 == null) return null;
        Mode mode = new Mode();
        SubModeScenes subMode = new SubModeScenes();
        subMode.effect = multiNewScenesControllerV1.getScenesCode();
        mode.subMode = subMode;
        ModeController modeController = new ModeController(mode);
        /*切换至场景模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV1.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytesV1);
    }

    /**
     * 是否是由其他模式切换到颜色模式 带亮度
     *
     * @return
     */
    public boolean isOtherMode2ColorModePtWithBrightness() {
        if (command.isEmpty()) return false;
        int size = command.size();
        if (size >= 3) {
            byte[] setSubModeColorBytes = Encode.decryByBase64(command.get(0));
            boolean isSetSubModeColor = SubModeColor.isSetMode2Color(setSubModeColorBytes) || SubModeColorV2.isSetMode2Color(setSubModeColorBytes);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isOtherMode2ColorModePtWithBrightness() isSetSubModeColor = " + isSetSubModeColor + " ; setSubModeColorBytes = " + BleUtil.bytesToHexString(setSubModeColorBytes));
            }
            if (!isSetSubModeColor) return false;
            byte[] getPartColorBytes = Encode.decryByBase64(command.get(1));
            boolean isGetPartColor = BulbStringColorController.isReadBulbStringColorController(getPartColorBytes) || BulbStringColorControllerV2.isReadBulbStringColorController(getPartColorBytes) || BulbStringColorControllerV3.isReadBulbStringColorController(getPartColorBytes);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isOtherMode2ColorModePtWithBrightness() isGetPartColor = " + isGetPartColor + " ; getPartColorBytes = " + BleUtil.bytesToHexString(getPartColorBytes));
            }
            if (!isGetPartColor) return false;
            byte[] getGradualBytes = Encode.decryByBase64(command.get(size - 1));
            boolean isReadGradualController = Gradual4BleWifiController.isReadGradualController(getGradualBytes);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isOtherMode2ColorModePtWithBrightness() isReadGradualController = " + isReadGradualController + " ; getGradualBytes = " + BleUtil.bytesToHexString(getGradualBytes));
            }
            return isReadGradualController;
        }
        return false;
    }

    /**
     * 点击色条应用整段颜色
     */
    public SubModeColor setPartColor4ColorStrip() {
        if (command.isEmpty()) return null;
        int size = command.size();
        SubModeColor subModeColor = new SubModeColor();
        for (int i = 0; i < size; i++) {
            String encodeStr = command.get(i);
            byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
            boolean setMode2Color = SubModeColor.isSetMode2Color4ColorEffect(setColor4PosBytes);
            if (!setMode2Color) return null;
            /*解析对应位置的颜色*/
            SubModeColor.parsePosColor(setColor4PosBytes, subModeColor);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "setPartColor4ColorStrip commandStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
            }
        }
        return subModeColor;
    }

    /**
     * 点击色条应用整段颜色-带亮度
     */
    public SubModeColorV2 setPartColor4ColorStripWithBrightness(int goodsType) {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size >= 2) {
            /*至少一包颜色+一包亮度*/
            SubModeColorV2 subModeColor = new SubModeColorV2(goodsType);
            for (int i = 0; i < size; i++) {
                String encodeStr = command.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                boolean setMode2Color = SubModeColorV2.isSetMode2Color4ColorEffect(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColorV2.parsePosColor(setColor4PosBytes, subModeColor);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "setPartColor4ColorStripWithBrightness commandStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
            }
            return subModeColor;
        }
        return null;
    }

    /**
     * 是否是应用色条
     *
     * @return
     */
    public boolean isSetColorStrip() {
        /*只有一包时直接走pt*/
        if (command.isEmpty() || command.size() == 1) return false;
        for (int i = 0; i < command.size(); i++) {
            String encodeStr = command.get(i);
            byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
            if (setColor4PosBytes == null) return false;
            if (setColor4PosBytes[2] != BleProtocol.sub_mode_color && setColor4PosBytes[2] != BleProtocol.sub_mode_color_v2)
                return false;
        }
        return true;
    }

    public boolean isSetColorStripWithBrightness() {
        if (command.isEmpty()) return false;
        String encodeStr = command.get(0);
        byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
        if (setColor4PosBytes == null) return false;
        return setColor4PosBytes[2] == BleProtocol.sub_mode_color_v2;
    }

    public static CmdPtReal getDiyCmdPt4DiyStudio(@NonNull AbsMultipleControllerV14DiyTemplate multi4Scenes) {
        List<byte[]> multipleWriteBytesV1 = MultipleBleBytes.getMultipleWriteBytesV1(multi4Scenes);
        Mode mode = new Mode();
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.effect = multi4Scenes.getScenesCode();
        mode.subMode = subModeScenes;
        ModeController modeController = new ModeController(mode);
        /*切换至场景模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV1.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytesV1);
    }

    public static CmdPtReal getDiyCmdPt4DiyStudio(@NonNull DiyStudio diyStudio) {
        AbsMultipleControllerV14DiyTemplate multi4Scenes = diyStudio.toMulti4Scenes();
        if (multi4Scenes == null) return null;
        List<byte[]> multipleWriteBytesV1 = MultipleBleBytes.getMultipleWriteBytesV1(multi4Scenes);
        Mode mode = new Mode();
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.effect = diyStudio.scenesCode;
        mode.subMode = subModeScenes;
        ModeController modeController = new ModeController(mode);
        /*切换至场景模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV1.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytesV1);
    }

    public static CmdPtReal getDiyCmdPt4DiyGraffiti(DiyGraffitiV2 diyGraffiti) {
        MultiDiyGraffitiController multiDiyGraffitiController = new MultiDiyGraffitiController(diyGraffiti.getDiyCode(), diyGraffiti.getEffectBytes());
        List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(multiDiyGraffitiController);
        Mode mode = new Mode();
        mode.subMode = new SubModeNewDiy(diyGraffiti.getDiyCode());
        ModeController modeController = new ModeController(mode);
        /*切换至diy模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytes.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytes);
    }

    public static CmdPtReal getMultiNewMusicMode4H6072(@NonNull MultipleController4Music multipleController4Music) {
        List<byte[]> multipleWriteBytesV2 = MultipleBleBytes.getMultipleWriteBytesV2(multipleController4Music);
        Mode mode = new Mode();
        ISubMode subMode = multipleController4Music.subMode;
        if (subMode instanceof SubModeMusicV2) {
            mode.subMode = ((SubModeMusicV2) subMode).copy();
        } else {
            mode.subMode = SubModeMusicV2.toNewSubModeMusic(multipleController4Music.getSensitivity(), multipleController4Music.getMusicCode());
        }
        ModeController modeController = new ModeController(mode);
        /*切换至音乐模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV2.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytesV2);
    }
}