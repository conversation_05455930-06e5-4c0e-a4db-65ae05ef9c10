package com.govee.straightfloorlamp.add;

import android.app.Activity;

import com.govee.base2home.main.choose.BaseBleDeviceModel;
import com.govee.base2home.main.choose.BaseBleProcessor;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.sku.DefaultDeviceNameUtil;
import com.govee.base2light.DeviceNameConfig;
import com.govee.straightfloorlamp.pact.Support;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2019-11-27
 * 蓝牙广播处理器$
 */
public class BleBroadcastProcessor extends BaseBleProcessor {
    @Override
    public boolean onItemClick(Activity activity, BaseBleDeviceModel model, boolean singleSku) {
        int goodsType = model.goodsType;
        Protocol protocol = model.protocol;
        /*是否支持协议处理;需要登录检测*/
        if (Support.supportPactV1(goodsType, protocol) || Support.supportPactV2(goodsType, protocol) || Support.supportPact4H60b2(goodsType, protocol)) {
            boolean checkLogin = checkLogin(activity);
            if (checkLogin) {
                return true;
            }
            AddInfo addInfo = new AddInfo();
            addInfo.sku = model.getSku();
            addInfo.goodsType = goodsType;
            addInfo.pactType = protocol.pactType;
            addInfo.pactCode = protocol.pactCode;
            if (Support.supportPactV1(goodsType, protocol) || Support.supportPactV2(goodsType, protocol)) {
                addInfo.deviceName = DeviceNameConfig.h6072_name;
            } else if (Support.supportPactV3(goodsType, protocol) || Support.supportPactV4(goodsType, protocol)) {
                addInfo.deviceName = DeviceNameConfig.h6076_name;
            }
            addInfo.deviceName = DefaultDeviceNameUtil.INSTANCE.getSkuDefaultName(model.getSku(), addInfo.deviceName);
            addInfo.bleName = model.getBleName();
            addInfo.bleAddress = model.getDevice().getAddress();
            if (goodsType == GoodsType.GOODS_TYPE_VALUE_H60B2) {
                /*支持配对的sku*/
                ConnectDialogV2.createDialog(activity, model.getDevice(), addInfo).show();
                return true;
            }
            //这段代码在合代码时冲突被注释掉了 包括6076和60b2,和乐军沟通6076不加进来 所以上面只加60b2判断
//            if (Arrays.asList(Support.supportPairGoodsTypeSet).contains(String.valueOf(goodsType))) {
//                /*支持配对的sku*/
//                ConnectDialogV2.createDialog(activity, model.getDevice(), addInfo).show();
//                return true;
//            }
            ConnectDialog.createDialog(activity, model.getDevice(), addInfo).show();
            return true;
        }
        return false;
    }
}