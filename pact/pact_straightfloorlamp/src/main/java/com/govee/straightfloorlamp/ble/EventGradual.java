package com.govee.straightfloorlamp.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by lins<PERSON>ong on 2019-09-10
 * 开启渐变
 */
public class EventGradual extends AbsControllerEvent {
    private int value;

    private EventGradual(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventGradual(false, write, commandType, proType));
    }

    static void sendSuc(boolean write, byte commandType, byte proType, int value) {
        EventGradual eventGradual = new EventGradual(true, write, commandType, proType);
        eventGradual.value = value;
        EventBus.getDefault().post(eventGradual);
    }

    public int getValue() {
        return value;
    }
}