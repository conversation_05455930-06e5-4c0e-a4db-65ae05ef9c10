package com.govee.straightfloorlamp.ble;


/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/3/17
 * 蓝牙协议
 */
public interface BleProtocol {
    /**
     * 子模式-music
     */
    byte sub_mode_music = (byte) 0x0f;

    /**
     * music效果-能量
     */
    byte value_sub_mode_music_energy = (byte) 0x00;

    /**
     * music效果-节奏
     */
    byte value_sub_mode_music_rhythm = (byte) 0x04;

    /**
     * music效果-节奏-动感
     */
    byte value_sub_mode_music_rhythm_dynamic = (byte) 0x01;

    /**
     * music效果-节奏-柔和
     */
    byte value_sub_mode_music_rhythm_soft = (byte) 0x00;

    /**
     * music效果-喷射
     */
    byte value_sub_mode_music_jet = (byte) 0x07;

    /**
     * music 效果-弹跳
     */
    byte value_sub_mode_music_jump = (byte) 0x08;
    /**
     * music 效果-悦动
     */
    byte value_sub_mode_music_joy = (byte) 0x02;
    /**
     * music效果-撞击
     */
    byte value_sub_mode_music_impact = (byte) 0x06;

    /**
     * 子模式-场景
     */
    byte sub_mode_scenes = (byte) 0x04;

    /**
     * 子模式-颜色
     */
    byte sub_mode_color = (byte) 0x14;

    /**
     * 子模式-新diy
     */
    byte sub_mode_new_diy = (byte) 0x0a;

    /**
     * 多包-diy-v1版本
     */
    byte value_multiple_ble_diy_v1 = (byte) 0x04;

    /**
     * 上报-亮度
     */
    byte notify_brightness = 0x20;

    /**
     * 单包-开启渐变--针对ble+wifi的设备
     */
    byte SINGLE_GRADUAL_CHANGE_4_WIFI_BLE = (byte) 0xa3;

    /**
     * 读取灯串颜色
     */
    byte MSG_TYPE_READ_BULB_COLOR = (byte) 0xa2;

    /**
     * 遥控器配对
     */
    byte REMOTE_CONTROL_PAIR = (byte) 0xa4;

    /**
     * 退出遥控器配对
     */
    byte EXIT_REMOTE_CONTROL_PAIR = (byte) 0xa6;

    /**
     * 子模式-新颜色 支持分段亮度
     */
    byte sub_mode_color_v2 = (byte) 0x15;

    /**
     * 读取渐变开关
     */
    byte MSG_TYPE_READ_GRADUAL = (byte) 0x05;
}