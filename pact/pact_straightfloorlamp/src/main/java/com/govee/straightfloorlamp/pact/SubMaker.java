package com.govee.straightfloorlamp.pact;

import com.govee.base2home.sku.IMaker;
import com.govee.base2home.sku.ISkuItem;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by hey on 2020/11/24
 * $
 */
public class SubMaker implements IMaker {
    private final List<ISkuItem> makers = new ArrayList<>();

    public SubMaker() {
        makers.add(new H6072Maker());
        makers.add(new H6076Maker());
        makers.add(new H60B2Maker());
    }

    @Override
    public List<ISkuItem> getSupportMakers() {
        return makers;
    }
}