package com.govee.straightfloorlamp.pact.bleiot;

import android.content.Context;
import android.text.TextUtils;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.pact.Command4PtReal;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2home.util.NumberUtil;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.DiyShare;
import com.govee.base2light.ac.diy.IColorOp;
import com.govee.base2light.ac.diy.IDiyOp;
import com.govee.base2light.ac.diy.IMusicFeastOp;
import com.govee.base2light.ac.diy.IRoomOp4Ble;
import com.govee.base2light.ac.diy.IRoomOp4Iot;
import com.govee.base2light.ac.diy.ISceneOp;
import com.govee.base2light.ac.diy.ISmartRoomOp;
import com.govee.base2light.ac.diy.ISwitchAndBrightnessOp;
import com.govee.base2light.ac.diy.local.ShareDiy;
import com.govee.base2light.ac.diy.v1.EffectCodes;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.HeartController;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.ble.controller.SecretKeyController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.util.NumUtil;
import com.govee.straightfloorlamp.adjust.Diy;
import com.govee.straightfloorlamp.ble.Ble;
import com.govee.straightfloorlamp.ble.BleComm;
import com.govee.straightfloorlamp.ble.Mode;
import com.govee.straightfloorlamp.ble.ModeController;
import com.govee.straightfloorlamp.ble.SubModeColor;
import com.govee.straightfloorlamp.ble.SubModeColorV2;
import com.govee.straightfloorlamp.iot.Cmd;
import com.govee.straightfloorlamp.iot.CmdBrightness;
import com.govee.straightfloorlamp.iot.CmdColorWc;
import com.govee.straightfloorlamp.iot.CmdPtReal;
import com.govee.straightfloorlamp.iot.CmdTurn;
import com.govee.straightfloorlamp.iot.OpColorCommDialog4BleIot;
import com.govee.straightfloorlamp.iot.OpColorCommDialog4BleIotV1;
import com.govee.straightfloorlamp.iot.OpColorCommDialog4SquareBleIot;
import com.govee.straightfloorlamp.iot.OpColorCommDialog4SquareBleIotV1;
import com.govee.straightfloorlamp.iot.OpDiyCommDialog4BleIot;
import com.govee.straightfloorlamp.iot.OpDiyCommDialog4BleIotV2;
import com.govee.straightfloorlamp.iot.OpDiyCommDialog4SquareBleIot;
import com.govee.straightfloorlamp.iot.OpDiyCommDialog4SquareBleIotV2;
import com.govee.straightfloorlamp.iot.OpSceneCommDialog4BleIotV1;
import com.govee.straightfloorlamp.iot.OpSceneCommDialog4SquareBleIot;
import com.govee.straightfloorlamp.iot.OpSceneCommDialog4SquareBleIotV1;
import com.govee.straightfloorlamp.pact.BleIotExt;
import com.govee.straightfloorlamp.pact.Comm;
import com.govee.straightfloorlamp.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JsonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/8/12
 * diy的op操作$
 */
abstract class AbsEffectOp4BleIot implements IDiyOp, IColorOp, ISceneOp, ISmartRoomOp, IMusicFeastOp, ISwitchAndBrightnessOp {

    @Override
    public String getKey() {
        StringBuilder sb = new StringBuilder();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            sb.append(goodsType).append("_");
        }
        return sb.toString();
    }

    /**
     * 支持的goodsType
     *
     * @return
     */
    protected abstract int[] supportGoodsType();

    @Override
    public boolean support(@NonNull AbsDevice absDevice, int effectType) {
        int curGoodsType = absDevice.getGoodsType();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) return true;
        }
        return false;
    }

    @Override
    public boolean supportDiyEffect(@NonNull AbsDevice absDevice, int[] effectCodes) {
        int version = Support.getDiyVersion(absDevice.getGoodsType(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        EffectCodes effectCodesCur;
        if (absDevice.getGoodsType() == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2 || absDevice.getGoodsType() == GoodsType.GOODS_TYPE_VALUE_H60B2) {
            effectCodesCur = Diy.getDiySupport(1, true, absDevice.getGoodsType(), absDevice.getVersionSoft(), absDevice.getVersionHard()).effectCodes;
        } else {
            effectCodesCur = Diy.getDiySupport(version, Support.supportNewScenes(absDevice.getGoodsType(), absDevice.getVersionSoft()), absDevice.getGoodsType(), absDevice.getVersionSoft(), absDevice.getVersionHard()).effectCodes;
        }
        if (effectCodesCur != null) {
            return effectCodesCur.supportDiyEffect(effectCodes);
        }
        return false;
    }

    @Override
    public boolean applyDiyEffect(Context context, @NonNull AbsDevice absDevice, @NonNull DiyShare diyShare, boolean needConnect) {
        return applyDiyEffect(context, absDevice, diyShare.effectStr, diyShare.effectCodes, diyShare.type, needConnect);
    }

    private boolean applyDiyEffect(Context context, @NonNull AbsDevice absDevice, String effectStr, int[] effectCodes, int type, boolean needConnect) {
        DiyProtocol diyProtocol = ShareDiy.parseDiyProtocol(effectStr);
        if (diyProtocol != null) {
            DeviceExtMode deviceExt = absDevice.getDeviceExt();
            String extSettingJson = deviceExt.getDeviceSettings();
            BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
            if (ext != null) {
                if (needConnect) {
                    OpDiyCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyProtocol, type);
                } else {
                    OpDiyCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyProtocol, type);
                }
                return true;
            }
        }
        DiyGraffitiV2 diyGraffiti = ShareDiy.parseDiyGraffiti4Rgbic(effectStr);
        if (diyGraffiti != null) {
            DeviceExtMode deviceExt = absDevice.getDeviceExt();
            String extSettingJson = deviceExt.getDeviceSettings();
            BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
            if (ext != null) {
                boolean support = diyGraffiti.checkDiyValue4RgbicGraffiti(ext.ic);
                if (!support) return false;
                if (needConnect) {
                    OpDiyCommDialog4BleIotV2.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyGraffiti, type);
                } else {
                    OpDiyCommDialog4SquareBleIotV2.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyGraffiti, type);
                }
                return true;
            }
        }
        /*来自Govee Studio*/
        AbsMultipleControllerV14DiyTemplate shareDiyStudio = ScenesOp.isShareDiyStudio(effectCodes, effectStr);
        if (shareDiyStudio != null) {
            DeviceExtMode deviceExt = absDevice.getDeviceExt();
            String extSettingJson = deviceExt.getDeviceSettings();
            BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
            if (ext != null) {
                if (needConnect) {
                    OpDiyCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareDiyStudio, type);
                } else {
                    OpDiyCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareDiyStudio, type);
                }
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean supportColorEffect(@NonNull AbsDevice absDevice, @NonNull EffectData.ColorEffect colorEffect) {
        int length = colorEffect.colorSet.length;
        return length == 1 || length == Support.getBulbStringMaxNum(absDevice.getGoodsType());
    }

    @Override
    public boolean applyColorEffect(Context context, @NonNull AbsDevice
            absDevice, @NonNull EffectData.ColorEffect colorEffect, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        boolean isSupportBrightness = Support.supportSubModeColor4PartBrightness(absDevice.getGoodsType(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        if (ext != null) {
            if (colorEffect.isSingleColor()) {
                if (needConnect) {
                    OpColorCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, isSupportBrightness);
                } else {
                    OpColorCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, isSupportBrightness);
                }
            } else {
                if (needConnect) {
                    OpColorCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, isSupportBrightness);
                } else {
                    OpColorCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, isSupportBrightness);
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean applySwitch(Context context, @NonNull AbsDevice absDevice, boolean on) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        OpColorCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), on);
        return true;
    }

    @Override
    public boolean applySwitch(Context context, @NonNull AbsDevice absDevice, boolean on, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpColorCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), on);
        } else {
            OpColorCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), on);
        }
        return true;
    }

    @Override
    public boolean applyBrightness(Context context, @NonNull AbsDevice absDevice, int brightness4Percent) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        OpColorCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), brightness4Percent);
        return true;
    }

    @Override
    public boolean applyBrightness(Context context, @NonNull AbsDevice absDevice, int brightness4Percent, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpColorCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), brightness4Percent);
        } else {
            OpColorCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), brightness4Percent);
        }
        return true;
    }

    @Override
    public boolean supportSceneEffect(@NonNull AbsDevice absDevice, @NonNull EffectData.ShareEffect shareEffect) {
        int parseVersion = shareEffect.parseVersion;
        if (parseVersion == EffectData.ShareEffect.parser_version_diy) {
            return supportDiyEffect(absDevice, shareEffect.effectCodes);
        }
        boolean support = parseVersion == EffectData.ShareEffect.parser_version_scenes_rgb
                || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v0
                || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v1;
        if (LogInfra.openLog()) {
            LogInfra.Log.d(TAG, "supportSceneEffect() shareEffect.isValidEffect() = " + shareEffect.isValidEffect());
        }
        return shareEffect.isValidEffect() && support;
    }

    private static final String TAG = "AbsEffectOp4BleIot";

    @Override
    public boolean applySceneEffect(Context context, @NonNull AbsDevice absDevice, @NonNull EffectData.ShareEffect shareEffect, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        int parseVersion = shareEffect.parseVersion;
        if (parseVersion == EffectData.ShareEffect.parser_version_diy) {
            return applyDiyEffect(context, absDevice, shareEffect.effectStr, shareEffect.effectCodes, DiyShare.type_recommend, needConnect);
        }
        if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgb) {
            if (needConnect) {
                OpSceneCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareEffect.effectStr, true);
            } else {
                OpSceneCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareEffect.effectStr);
            }
            return true;
        }
        if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v0 || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v1) {
            if (!Support.supportNewScenes(absDevice.getGoodsType(), absDevice.getVersionSoft())) {
                return false;
            }
            if (needConnect) {
                OpSceneCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareEffect.effectStr, false);
            } else {
                OpSceneCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareEffect.effectStr);
            }
            return true;
        }
        return false;
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    public EffectCodes getSupportDiyEffectCode(@NonNull AbsDevice absDevice) {
        int version = Support.getDiyVersion(absDevice.getGoodsType(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        if (absDevice.getGoodsType() == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2 || absDevice.getGoodsType() == GoodsType.GOODS_TYPE_VALUE_H60B2) {
            return Diy.getDiySupport(1, true, absDevice.getGoodsType(), absDevice.getVersionSoft(), absDevice.getVersionHard()).effectCodes;
        } else {
            return Diy.getDiySupport(version, Support.supportNewScenes(absDevice.getGoodsType(), absDevice.getVersionSoft()), absDevice.getGoodsType(), absDevice.getVersionSoft(), absDevice.getVersionHard()).effectCodes;
        }
    }

    @Override
    public boolean isWifiLight() {
        return true;
    }

    @Override
    public String getWifiOnlineCmd() {
        return Cmd.online;
    }

    @Override
    public int supportRandomColorSize(AbsDevice absDevice) {
        return Support.getBulbStringMaxNum(absDevice.getGoodsType());
    }

    @Override
    public int[] supportScenesOpSet(AbsDevice absDevice) {
        return Support.supportScenesOpSet(absDevice.getGoodsType(), absDevice.getVersionSoft());
    }

    @Override
    public boolean applyPtControllers(Context context, @NonNull AbsDevice absDevice, int type, @NonNull Command4PtReal ptReal, boolean needConnect) {
        boolean invalid = ptReal.isInvalid();
        if (invalid) return false;
        boolean supportBleOp = ptReal.supportBleOp();
        boolean supportWifiOp = ptReal.supportWifiOp();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyPtControllers() supportBleOp = " + supportBleOp + " ; supportWifiOp = " + supportWifiOp);
        }
        if (!supportBleOp && !supportWifiOp) return false;
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        PtRealController ptRealController = PtRealController.makePtRealController(ptReal.opCommands, -1, -1);
        if (ptRealController == null) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "applyPtControllers() 透传指令解析失败");
            }
            return false;
        }
        List<String> commandList = ptReal.getCommands4IotPtReal();
        if (needConnect) {
            OpSceneCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), type, ptRealController, commandList, supportBleOp, supportWifiOp);
        } else {
            OpSceneCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), type, ptRealController, commandList, supportBleOp, supportWifiOp);
        }

        return true;
    }
    /*smartRoom*/

    @Override
    public boolean supportWifi() {
        return true;
    }

    @Override
    public boolean supportBle() {
        return true;
    }

    @Override
    public int supportColorSize(DeviceModel model) {
        return Support.getBulbStringMaxNum(model.getGoodsType());
    }

    @Override
    public boolean supportDevice(DeviceModel deviceModel) {
        int curGoodsType = deviceModel.getGoodsType();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) return true;
        }
        return false;
    }

    @Override
    public IRoomOp4Ble getRoomOp() {
        return new IRoomOp4Ble() {
            @Override
            public UUID getServiceUUID() {
                return BleComm.serviceUuid;
            }

            @Override
            public UUID getCharacteristicUUID() {
                return BleComm.characteristicUuid;
            }

            @Override
            public byte[] heartBytes() {
                return new HeartController().getValue();
            }

            @Override
            public byte[] makeSwitchOpBytes(DeviceModel deviceModel, boolean on) {
                return new SwitchController(on).getValue();
            }

            @Override
            public byte[] makeBrightnessOpBytes(DeviceModel deviceModel, int brightnessPercent) {
                int[] brightnessRange = Support.getBrightnessRange();
                int brightness;
                if (brightnessRange[2] == 1) {
                    brightness = NumUtil.calculateProgress(brightnessRange[1], brightnessRange[0], brightnessPercent);
                } else {
                    brightness = brightnessPercent;
                }
                return new BrightnessController(brightness).getValue();
            }

            @Override
            public List<byte[]> makeSetColorOpBytes(DeviceModel deviceModel, int[] colors) {
                boolean isSupportBrightness = Support.supportSubModeColor4PartBrightness(deviceModel.getGoodsType(), deviceModel.versionSoft, deviceModel.versionHard);
                AbsSingleController[] modeControllers;
                if (isSupportBrightness) {
                    modeControllers = SubModeColorV2.makeSubModeColor(colors, null, false, false, Support.getGoodsTypeByName(deviceModel.getSku()));
                } else {
                    modeControllers = SubModeColor.makeSubModeColor(colors, false, false);
                }
                if (modeControllers == null) return null;
                List<byte[]> values = new ArrayList<>();
                for (AbsSingleController controller : modeControllers) {
                    values.add(controller.getValue());
                }
                return values;
            }

            @Override
            public List<byte[]> makeSetColorTemOpBytes(DeviceModel deviceModel, int[] kelvin, int[] temColor) {
                if (NumberUtil.isAllSame(kelvin)) {
                    boolean isSupportBrightness = Support.supportSubModeColor4PartBrightness(deviceModel.getGoodsType(), deviceModel.versionSoft, deviceModel.versionHard);
                    Mode mode = new Mode();
                    if (isSupportBrightness) {
                        mode.subMode = SubModeColorV2.makeSubModeColorByKelvin(kelvin[0], deviceModel.getGoodsType());
                    } else {
                        mode.subMode = SubModeColor.makeSubModeColor4Kelvin(kelvin[0]);
                    }
                    ModeController modeController = new ModeController(mode);
                    List<byte[]> bytes = new ArrayList<>();
                    bytes.add(modeController.getValue());
                    return bytes;
                }
                return makeSetColorOpBytes(deviceModel, temColor);
            }

            @Override
            public byte[] makeSetColorOpBytes(DeviceModel deviceModel, int color) {
                return Comm.makeColorController4BleComm(deviceModel.getGoodsType(), deviceModel.versionSoft, deviceModel.versionHard, color).getValue();
            }

            @Override
            public byte[] makeSetColorTemOpBytes(DeviceModel deviceModel, int kelvin, int temColor) {
                return Comm.makeColorTemController4BleComm(deviceModel.getGoodsType(), deviceModel.versionSoft, deviceModel.versionHard, kelvin).getValue();
            }

            @Override
            public int supportMic(DeviceModel deviceModel) {
                AbsMicFragmentV4.SupportMicStatus micStatus = Support.getMicStatus(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.getGoodsType());
                if (AbsMicFragmentV4.SupportMicStatus.support_new_order.equals(micStatus))
                    return support_mic_new;
                if (AbsMicFragmentV4.SupportMicStatus.support_color_order.equals(micStatus))
                    return support_mic_rgb;
                return support_mic_no;
            }

            @Override
            public byte[] makeMicBytesByRgb(DeviceModel deviceModel, int rgb) {
                Mode mode = new Mode();
                boolean isSupportBrightness = Support.supportSubModeColor4PartBrightness(deviceModel.getGoodsType(), deviceModel.versionSoft, deviceModel.versionHard);
                if (isSupportBrightness) {
                    mode.subMode = SubModeColor.makeSubModeColor(rgb);
                } else {
                    mode.subMode = SubModeColorV2.makeSubModeColor(rgb, deviceModel.getGoodsType());
                }
                return new ModeController(mode).getValue();
            }

            @Override
            public byte[] forceSendSecretKeyBytes(DeviceModel deviceModel) {
                if (!Support.H6076.equals(deviceModel.getSku())) return null;
                String secretCode = deviceModel.secretCode;
                if (TextUtils.isEmpty(secretCode)) return null;
                return new SecretKeyController(secretCode).getValue();
            }
        };
    }

    @Override
    public IRoomOp4Iot getRoomOp4Iot() {
        return new IRoomOp4Iot() {
            @Override
            public AbsCmd makeSwitchCmd(DeviceModel deviceModel, boolean on) {
                return new CmdTurn(on);
            }

            @Override
            public AbsCmd makeBrightnessCmd(DeviceModel deviceModel, int brightnessPercent) {
                int[] brightnessRange = Support.getBrightnessRange();
                int brightness;
                if (brightnessRange[2] == 1) {
                    brightness = NumUtil.calculateProgress(brightnessRange[1], brightnessRange[0], brightnessPercent);
                } else {
                    brightness = brightnessPercent;
                }
                return new CmdBrightness(brightness);
            }

            @Override
            public List<AbsCmd> makeColorCmd(DeviceModel deviceModel, int[] colors) {
                if (NumberUtil.isAllSame(colors)) {
                    CmdColorWc cmdColorWc = CmdColorWc.makeCmdColorWc4Color(colors[0]);
                    List<AbsCmd> cmds = new ArrayList<>();
                    cmds.add(cmdColorWc);
                    return cmds;
                }
                boolean isSupportBrightness = Support.supportSubModeColor4PartBrightness(deviceModel.getGoodsType(), deviceModel.versionSoft, deviceModel.versionHard);
                AbsSingleController[] modeControllers;
                if (isSupportBrightness) {
                    modeControllers = SubModeColorV2.makeSubModeColor(colors, null, false, false, Support.getGoodsTypeByName(deviceModel.getSku()));
                } else {
                    modeControllers = SubModeColor.makeSubModeColor(colors, false, false);
                }
                if (modeControllers == null) return null;
                List<byte[]> values = new ArrayList<>();
                for (AbsSingleController controller : modeControllers) {
                    values.add(controller.getValue());
                }
                AbsCmd cmdPtReal = new CmdPtReal(values);
                List<AbsCmd> cmds = new ArrayList<>();
                cmds.add(cmdPtReal);
                return cmds;
            }

            @Override
            public List<AbsCmd> makeColorTemCmd(DeviceModel deviceModel, int[] kelvin, int[] temColors) {
                if (NumberUtil.isAllSame(kelvin)) {
                    CmdColorWc cmdColorWc = CmdColorWc.makeCmdColorWc4Kelvin(kelvin[0]);
                    List<AbsCmd> cmds = new ArrayList<>();
                    cmds.add(cmdColorWc);
                    return cmds;
                }
                return makeColorCmd(deviceModel, temColors);
            }

            @Override
            public String getOnlineCmd() {
                return Cmd.brightness;
            }

            @Override
            public int onlineCmdVersion(DeviceModel deviceModel) {
                return 0;
            }
        };
    }

    /*smartRoom*/
}