package com.govee.straightfloorlamp.ble;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/9/14
 * event亮度-主动上报$
 */
public class EventBrightnessNotify {
    public int brightness;

    private EventBrightnessNotify() {
    }

    public static void sendEventBrightnessNotify(int brightness) {
        EventBrightnessNotify event = new EventBrightnessNotify();
        event.brightness = brightness;
        EventBus.getDefault().post(event);
    }
}