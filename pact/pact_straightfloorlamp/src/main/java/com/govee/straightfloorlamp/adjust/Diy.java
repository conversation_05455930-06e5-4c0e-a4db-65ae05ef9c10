package com.govee.straightfloorlamp.adjust;

import com.govee.base2light.ac.diy.DiyM;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v1.EffectCodes;
import com.govee.base2light.ac.diy.v2.DiyOpM;
import com.govee.straightfloorlamp.pact.Support;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by hey on 2020/11/24
 * $
 */
public class Diy {
    private Diy() {
    }

    private static DiySupportV1 supportV1;
    private static DiySupportV1 supportV2;
    private static DiySupportV1 supportV3;

    public static DiySupportV1 getDiySupport(int newVersion, boolean isSupportNewScenes, int goodsType, String versionSoft, String versionHard) {
        if (newVersion == 0) return getDiySupport(goodsType, versionSoft, versionHard);
        if (newVersion == 1)
            return getDiySupportV2(isSupportNewScenes, goodsType, versionSoft, versionHard);
        return getDiySupport(goodsType, versionSoft, versionHard);
    }

    private static DiySupportV1 getDiySupportV2(boolean isSupportNewScenes, int goodsType, String versionSoft, String versionHard) {
        if (isSupportNewScenes) {
            if (supportV2 != null) return supportV2;
            supportV2 = makeDiySupportV2(true, goodsType, versionSoft, versionHard);
            return supportV2;
        }
        if (supportV3 != null) return supportV3;
        supportV3 = makeDiySupportV2(false, goodsType, versionSoft, versionHard);
        return supportV3;
    }


    private static DiySupportV1 makeDiySupportV2(boolean isSupportNewScenes, int goodsType, String versionSoft, String versionHard) {
        DiySupportV1 diySupportV1 = new DiySupportV1();
        DiySupportV1.Effect effectFade = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_gradual,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                }
        );
        DiySupportV1.Effect effectBreath = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_breath,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle,
                }
        );
        DiySupportV1.Effect effectJump = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jump,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle,
                }
        );
        DiySupportV1.Effect effectAccumulation = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_accumulation,
                null
        );
        DiySupportV1.Effect effectChase = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_chase,
                null
        );
        DiySupportV1.Effect effectRainbow = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_rainbow,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_move_down,
                        DiyM.EffectSubCode.diy_sub_effect_code_move_up,
                }
        );
        DiySupportV1.Effect effectJet = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jet,
                null
        );

        /*混合*/
        DiySupportV1.Effect effectMix = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_mix[0],
                DiyM.EffectCode.diy_effect_code_mix[1]
        );

        DiySupportV1.Effect effectMusic = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_music,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_rhythm,
                        DiyM.EffectSubCode.diy_sub_effect_code_joy,
                        DiyM.EffectSubCode.diy_sub_effect_code_jump,
                        DiyM.EffectSubCode.diy_sub_effect_code_jet,
                        DiyM.EffectSubCode.diy_sub_effect_code_hit,
                }
        );


        DiySupportV1.Effect effectGraffiti = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_graffiti_4_rgbic,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_up,
                        DiyM.EffectSubCode.diy_sub_effect_code_down,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle,
                        DiyM.EffectSubCode.diy_sub_effect_code_fade,
                        DiyM.EffectSubCode.diy_sub_effect_code_blinking,
                        DiyM.EffectSubCode.diy_sub_effect_code_breath
                }
        );
        effectGraffiti.setSpecialDiyGraffiti4Rgbic();
        /*效果*/
        ArrayList<DiySupportV1.Effect> effects = new ArrayList<>();
        effects.add(effectGraffiti);
        effects.add(effectFade);
        effects.add(effectBreath);
        effects.add(effectJump);
        effects.add(effectAccumulation);
        effects.add(effectChase);
        effects.add(effectRainbow);
        effects.add(effectJet);
        effects.add(effectMix);
        effects.add(effectMusic);
        diySupportV1.effects = effects;
        /*混合支持效果*/
        ArrayList<DiySupportV1.Effect> mixEffects = new ArrayList<>();
        mixEffects.add(effectFade);
        mixEffects.add(effectBreath);
        mixEffects.add(effectJump);
        mixEffects.add(effectAccumulation);
        mixEffects.add(effectChase);
        mixEffects.add(effectRainbow);
        mixEffects.add(effectJet);
        diySupportV1.mixEffects = mixEffects;
        diySupportV1.mix4EffectsNum = 4;
        /*颜色数量支持*/
        ArrayList<DiySupportV1.Effect4Color> effect4Colors = new ArrayList<>();
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_breath[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_accumulation[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jet[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_music[0], 8));
        diySupportV1.effect4Colors = effect4Colors;
        /*速度调节*/
        ArrayList<DiySupportV1.Effect4Speed> effect4Speeds = new ArrayList<>();
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_breath[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_accumulation[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jet[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_graffiti_4_rgbic[0], 100));

        diySupportV1.effect4Speeds = effect4Speeds;
        /*灵敏度调节*/
        ArrayList<DiySupportV1.Effect4Sensitivity> effect4Sensitivities = new ArrayList<>();
        effect4Sensitivities.add(DiySupportV1.Effect4Sensitivity.makeMaxSensitivity4Effect(DiyM.EffectCode.diy_effect_code_music[0], 100));
        diySupportV1.effect4Sensitivities = effect4Sensitivities;
        /*效果code码-rgb抽象协议+rgbic抽象协议v0*/
        List<Integer> flagCodes;
        flagCodes = DiyM.getInstance.getFlagCodes(effects,
                DiyM.EffectCode.diy_effect_code_rgb_from_studio,
                DiyM.EffectCode.diy_effect_code_rgbic_v0_from_studio,
                DiyM.EffectCode.diy_effect_code_rgbic_v1_from_studio);
        if (isSupportNewScenes) {
            diySupportV1.effectCodes = new EffectCodes(flagCodes, DiyM.getInstance.getFlagCodes(mixEffects));
        } else {
            diySupportV1.effectCodes = new EffectCodes(DiyM.getInstance.getFlagCodes(effects), DiyM.getInstance.getFlagCodes(mixEffects));
        }
        return diySupportV1;
    }

    private static DiySupportV1 getDiySupport(int goodsType, String versionSoft, String versionHard) {
        if (supportV1 != null) return supportV1;
        supportV1 = makeDiySupport(goodsType, versionSoft, versionHard);
        return supportV1;
    }


    private static DiySupportV1 makeDiySupport(int goodsType, String versionSoft, String versionHard) {
        DiySupportV1 diySupportV1 = new DiySupportV1();
        DiySupportV1.Effect effectFade = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_gradual,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                }
        );
        DiySupportV1.Effect effectBreath = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_breath,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle,
                }
        );
        DiySupportV1.Effect effectJump = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jump,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle,
                }
        );
        DiySupportV1.Effect effectAccumulation = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_accumulation,
                null
        );
        DiySupportV1.Effect effectChase = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_chase,
                null
        );
        DiySupportV1.Effect effectRainbow = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_rainbow,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_move_down,
                        DiyM.EffectSubCode.diy_sub_effect_code_move_up,
                }
        );
        DiySupportV1.Effect effectJet = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jet,
                null
        );

        /*混合*/
        DiySupportV1.Effect effectMix = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_mix[0],
                DiyM.EffectCode.diy_effect_code_mix[1]
        );

        DiySupportV1.Effect effectMusic = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_music,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_rhythm,
                        DiyM.EffectSubCode.diy_sub_effect_code_joy,
                        DiyM.EffectSubCode.diy_sub_effect_code_jump,
                        DiyM.EffectSubCode.diy_sub_effect_code_jet,
                        DiyM.EffectSubCode.diy_sub_effect_code_hit,
                }
        );

        /*效果*/
        ArrayList<DiySupportV1.Effect> effects = new ArrayList<>();
        effects.add(effectFade);
        effects.add(effectBreath);
        effects.add(effectJump);
        effects.add(effectAccumulation);
        effects.add(effectChase);
        effects.add(effectRainbow);
        effects.add(effectJet);
        effects.add(effectMix);
        effects.add(effectMusic);
        diySupportV1.effects = effects;
        /*混合支持效果*/
        ArrayList<DiySupportV1.Effect> mixEffects = new ArrayList<>();
        mixEffects.add(effectFade);
        mixEffects.add(effectBreath);
        mixEffects.add(effectJump);
        mixEffects.add(effectAccumulation);
        mixEffects.add(effectChase);
        mixEffects.add(effectRainbow);
        mixEffects.add(effectJet);
        diySupportV1.mixEffects = mixEffects;
        diySupportV1.mix4EffectsNum = 4;
        /*颜色数量支持*/
        ArrayList<DiySupportV1.Effect4Color> effect4Colors = new ArrayList<>();
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_breath[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_accumulation[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jet[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_music[0], 8));
        diySupportV1.effect4Colors = effect4Colors;
        /*速度调节*/
        ArrayList<DiySupportV1.Effect4Speed> effect4Speeds = new ArrayList<>();
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_breath[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_accumulation[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jet[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 100));

        diySupportV1.effect4Speeds = effect4Speeds;
        /*灵敏度调节*/
        ArrayList<DiySupportV1.Effect4Sensitivity> effect4Sensitivities = new ArrayList<>();
        effect4Sensitivities.add(DiySupportV1.Effect4Sensitivity.makeMaxSensitivity4Effect(DiyM.EffectCode.diy_effect_code_music[0], 100));
        diySupportV1.effect4Sensitivities = effect4Sensitivities;
        /*效果code码*/
        List<Integer> flagCodes = DiyM.getInstance.getFlagCodes(effects,
                DiyM.EffectCode.diy_effect_code_rgbic_v0_from_studio,
                DiyM.EffectCode.diy_effect_code_rgbic_v1_from_studio
        );
        if (Support.supportAiEffect(goodsType, versionSoft, versionHard)) {
            diySupportV1.effectCodes = new EffectCodes(flagCodes, DiyM.getInstance.getFlagCodes(mixEffects));
        } else {
            diySupportV1.effectCodes = new EffectCodes(DiyM.getInstance.getFlagCodes(effects), DiyM.getInstance.getFlagCodes(mixEffects));
        }
        return diySupportV1;
    }

    public static String getLastDiyApplyKey(int version, String sku, int diyCode, boolean isSupportNewVersion, int goodsType, String versionSoft, String versionHard, int icNum) {
        return DiyOpM.getInstance.getCurChooseDiyValueKey(sku, goodsType, icNum, getDiySupport(version, isSupportNewVersion, goodsType, versionSoft, versionHard), diyCode);
    }

}