package com.govee.straightfloorlamp.add;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.text.TextUtils;

import com.govee.base2home.ac.ActivityMgr;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.custom.AbsConnectDialog;
import com.govee.base2home.device.net.DeviceBindResponse;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventHardVersion;
import com.govee.base2light.ble.controller.EventSn;
import com.govee.base2light.ble.controller.EventSoftVersion;
import com.govee.base2light.ble.controller.EventWifiHardVersion;
import com.govee.base2light.ble.controller.EventWifiMac;
import com.govee.base2light.ble.controller.EventWifiSoftVersion;
import com.govee.base2light.ble.controller.HardVersionController;
import com.govee.base2light.ble.controller.SnController;
import com.govee.base2light.ble.controller.SoftVersionController;
import com.govee.base2light.ble.controller.WifiHardVersionController;
import com.govee.base2light.ble.controller.WifiMacController;
import com.govee.base2light.ble.controller.WifiSoftVersionController;
import com.govee.bind.SafeBindMgr;
import com.govee.bind.bean.ConfirmGidReq;
import com.govee.bind.engine.BindDevServerAddInfoImpl;
import com.govee.bind.engine.ReadDeviceGid4Ble;
import com.govee.ble.BleController;
import com.govee.ble.event.EventBleConnect;
import com.govee.straightfloorlamp.ble.Ble;
import com.govee.straightfloorlamp.ble.EventPair;
import com.govee.straightfloorlamp.ble.RemoteController;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.network.ErrorResponse;
import com.ihoment.base2app.util.JsonUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Create by xieyingwu on 2020/3/6
 * 连接弹窗v1$
 */
public class ConnectDialog extends AbsConnectDialog {
    private final AddInfo addInfo;

    protected ConnectDialog(Context context, BluetoothDevice device, AddInfo addInfo) {
        super(context, device);
        this.addInfo = addInfo;
        /*注册*/
        getBle().registerEvent(true, this.getClass().getName());
    }

    public static ConnectDialog createDialog(Context context, BluetoothDevice device, AddInfo addInfo) {
        return new ConnectDialog(context, device, addInfo);
    }

    private AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected void toConnectBle() {
        boolean connectBle = getBle().connectBle(device, true);
        if (!connectBle) {
            bluetoothClose();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBleConnect(EventBleConnect event) {
        boolean connectSuc = event.connectSuc();
        LogInfra.Log.i(TAG, "onEventBleConnect() connectSuc = " + connectSuc);
        if (connectSuc) {
            /*统计蓝牙连接成功*/
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.ble_connect_suc, addInfo.sku);
            /*连接成功*/
            sendMsg();
        } else {
            /*统计蓝牙连接失败*/
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.ble_connect_fail, addInfo.sku);
            uiDisconnect();
        }
    }

    protected void sendMsg() {
        AbsSingleController[] controllers = new AbsSingleController[]{
                new SoftVersionController(),
                new HardVersionController(),
                new WifiSoftVersionController(),
                new WifiHardVersionController(),
                new WifiMacController(),
                new SnController(),
                new RemoteController()
        };
        getBle().startController(controllers);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventPair(EventPair event) {
        boolean result = event.isResult();
        if (result) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "pair result = " + event.getValue());
            }
            addInfo.hadPair = event.getValue();
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSoftVersionEvent(EventSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "softVersion = " + softVersion);
            }
            addInfo.versionSoft = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onHardVersionEvent(EventHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "hardVersion = " + hardVersion);
            }
            addInfo.versionHard = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiSoftVersionEvent(EventWifiSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiSoftVersionEvent() softVersion = " + softVersion);
            }
            addInfo.wifiSoftVersion = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiHardVersionEvent(EventWifiHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiHardVersionEvent() hardVersion = " + hardVersion);
            }
            addInfo.wifiHardVersion = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiMacEvent(EventWifiMac event) {
        if (event.isResult()) {
            String mac = event.getMac();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiMacEvent() mac = " + mac);
            }
            addInfo.wifiMac = mac;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSnEvent(EventSn event) {
        if (event.isResult()) {
            String device = event.getUuid();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "device = " + device);
            }
            addInfo.device = device;
        }
        getBle().controllerEvent(event);

        if (event.isResult()) {
            /*构建absDevice对象，用于绑定设备*/
            DeviceExtMode deviceExt = new DeviceExtMode();
            deviceExt.setLastDeviceData("{}");
            deviceExt.setDeviceSettings(makeDeviceSettingsJsonStr(addInfo.wifiMac));
            absDevice = new AbsDevice(addInfo.device, addInfo.sku, addInfo.versionHard, addInfo.versionSoft, addInfo.deviceName, addInfo.goodsType, deviceExt);
            /*读取完成，绑定设备 - 检测设备锁*/
            checkDeviceLock(addInfo.sku, addInfo.device);
        }
    }

    @Override
    protected void retryCheckDeviceLock() {
        checkDeviceLock(addInfo.sku, addInfo.device);
    }

    @Override
    protected void bindDeviceStep() {
        BindDevServerAddInfoImpl bindImpl = new BindDevServerAddInfoImpl(this.context, absDevice, this::makeDeviceSettingsJsonStr, bindResult -> {
            if (bindResult) {
                afterBindSuc();
            } else {
                /*绑定失败*/
                beBindError(true);
            }
            return null;
        }, false);
        SafeBindMgr.INSTANCE.bindDeviceGidCheck(
                new ReadDeviceGid4Ble(getBle()),
                ConfirmGidReq.createConfirmGidReq(absDevice, addInfo.wifiHardVersion, addInfo.wifiSoftVersion),
                addInfo.wifiMac,
                bindImpl,
                () -> {
                    // 直接绑定
                    bindDevice(absDevice);
                    return null;
                }
        );
    }

    private String makeDeviceSettingsJsonStr(String wifiMacFromGid) {
        if (!TextUtils.isEmpty(wifiMacFromGid)) {
            addInfo.wifiMac = wifiMacFromGid;
        }
        BindExt bindExt = new BindExt(addInfo.pactType, addInfo.pactCode);
        bindExt.deviceName = addInfo.deviceName;
        bindExt.bleName = addInfo.bleName;
        bindExt.address = addInfo.bleAddress;
        bindExt.wifiMac = addInfo.wifiMac;
        bindExt.wifiSoftVersion = addInfo.wifiSoftVersion;
        bindExt.wifiHardVersion = addInfo.wifiHardVersion;
        return JsonUtil.toJson(bindExt);
    }


    @Override
    protected void bluetoothClose() {
        closeBle();
        /*关闭弹窗*/
        hide();
    }

    @Override
    protected void bindError(ErrorResponse response) {
        beBindError(true);
    }

    @Override
    protected void bindSuc(DeviceBindResponse response) {
        afterBindSuc();
    }

    private void afterBindSuc() {
        /*隐藏弹窗*/
        hide();
        /*关闭其他Ac，除了主界面*/
        ActivityMgr.getInstance().finishAllExceptMain();
        /*跳转到名称设置界面*/
        DeviceNameAc.jump2DeviceNameAcV2(context, addInfo);
    }

    @Override
    protected void toDisconnectBle() {
        closeBle();
    }

    private void closeBle() {
        getBle().stopHeart();
        BleController.getInstance().toBtClose();
        getBle().registerEvent(false, this.getClass().getName());
        getBle().release();
    }
}