package com.govee.straightfloorlamp.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.SwitchModel;
import com.govee.straightfloorlamp.pact.Comm;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * ble-开关控制$
 */
public class BleSwitchCmdBuilderV1 extends AbsBleCmdBuilderV1<SwitchModel> {
    @Override
    public IBleCmd createCmd(SwitchModel switchModel) {
        return () -> Comm.makeSwitchController4BleComm(switchModel.isOpen).getValue();
    }
}