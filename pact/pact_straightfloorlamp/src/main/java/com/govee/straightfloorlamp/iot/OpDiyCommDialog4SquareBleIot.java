package com.govee.straightfloorlamp.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsMultipleControllerV1;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.EventDiyTemplate4NewScenes;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultipleDiy;
import com.govee.base2light.ble.controller.MultipleDiyControllerV1;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.straightfloorlamp.ble.Ble;
import com.govee.straightfloorlamp.ble.Mode;
import com.govee.straightfloorlamp.ble.ModeController;
import com.govee.straightfloorlamp.ble.SubModeNewDiy;
import com.govee.straightfloorlamp.ble.SubModeScenes;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2020/8/12
 * op diy操作应用-ble+iot$
 */
public class OpDiyCommDialog4SquareBleIot extends AbsOpCommDialog4BleIotV2 {
    private final AbsMultipleControllerV1 multipleControllerV1;
    private final CmdPtReal cmdPt;

    protected OpDiyCommDialog4SquareBleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull DiyProtocol diyProtocol, int type) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_DIY, type);
        multipleControllerV1 = new MultipleDiyControllerV1(diyProtocol);
        cmdPt = CmdPtReal.getDiyCmdPt(diyProtocol);
    }

    protected OpDiyCommDialog4SquareBleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull AbsMultipleControllerV14DiyTemplate diyTemplate, int type) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_DIY, type);
        multipleControllerV1 = diyTemplate;
        cmdPt = CmdPtReal.getDiyCmdPt4DiyStudio(diyTemplate);
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull DiyProtocol diyProtocol, int type) {
        new OpDiyCommDialog4SquareBleIot(context, bleAddress, bleName, topic, sku, device, diyProtocol, type).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull AbsMultipleControllerV14DiyTemplate diyTemplate, int type) {
        new OpDiyCommDialog4SquareBleIot(context, bleAddress, bleName, topic, sku, device, diyTemplate, type).show();
    }

    @Override
    protected @Nullable
    AbsCmd getOpCmd() {
        return cmdPt;
    }

    @Override
    protected void bleOping() {
        if (multipleControllerV1 != null) {
            getBle().sendMultipleControllerV1(multipleControllerV1);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleDiy(EventMultipleDiy event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiy() result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(event.getDiyCode());
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            getBle().clearControllers();
            updateBleResult(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyTemplate4NewScenes(EventDiyTemplate4NewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        int diyCode = event.diyCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyTemplate4NewScenes() result = " + result + " ; scenesCode = " + scenesCode + " ; diyCode = " + diyCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*diy模版效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*diy效果传输失败，则通知传输失败*/
            getBle().clearControllers();
            updateBleResult(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateBleResult(result);
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}