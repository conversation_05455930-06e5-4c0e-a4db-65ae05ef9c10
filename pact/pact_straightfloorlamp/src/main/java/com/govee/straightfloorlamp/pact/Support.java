package com.govee.straightfloorlamp.pact;

import android.text.TextUtils;

import com.govee.base2home.ota.OtaType;
import com.govee.base2home.pact.BleUtil;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.Pact;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2home.util.NumberUtil;
import com.govee.base2light.ac.diy.DiyM;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.ScenesRgb;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.scenes.Category;
import com.govee.base2light.ble.scenes.ScenesM;
import com.govee.base2light.light.ScenesHint;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.straightfloorlamp.ble.Mode;
import com.govee.straightfloorlamp.ble.SubModeScenes;
import com.govee.straightfloorlamp.pact.bleiot.EffectOp4BleIot;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Create by hey on 2020/11/24
 * $
 */
public final class Support {
    public static final String H6072 = "H6072";
    public static final String H8072 = "H8072";
    public static final String H6076 = "H6076";
    public static final String H60B2 = "H60B2";
    public static final String TAG = "Support";

    public static List<Protocol> supportProtocolsV1 = new ArrayList<>();
    public static List<Protocol> supportProtocolsV2 = new ArrayList<>();
    public static List<Protocol> supportProtocolsV3 = new ArrayList<>();
    public static List<Protocol> supportProtocolsV4 = new ArrayList<>();
    /**
     * 设备列表的goodsType集合
     */
    public static final int[] deviceItemGoodsTypes = {
            GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1,
            GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2,
            GoodsType.GOODS_TYPE_VALUE_H60B2,
    };

    public static void addSupportPact() {
        Pact pact = Pact.getInstance;
        Protocol protocol1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1, GoodsType.PACT_CODE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1_1);
        supportProtocolsV1.add(protocol1);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1, protocol1);
        /*富芮坤方案*/
        Protocol protocol2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1_2, GoodsType.PACT_CODE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1_2);
        supportProtocolsV2.add(protocol2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1, protocol2);
        /*6076*/
        Protocol protocol3 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2, GoodsType.PACT_CODE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2);
        supportProtocolsV3.add(protocol3);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2, protocol3);
        //H6072迭代款
        Protocol protocol4 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V3, GoodsType.PACT_CODE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2);
        supportProtocolsV4.add(protocol4);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2, protocol4);
        /*60b2*/
        Protocol protocol4H60b2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_H60B2, GoodsType.PACT_CODE_H60B2);
        supportProtocols4H60b2.add(protocol4H60b2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H60B2, protocol4H60b2);

        /*初始化支持的默认sku图*/
        ThemeM.getInstance.addDefSkuRes(H6072, com.govee.straightfloorlamp.R.mipmap.add_list_type_device_6072);
        ThemeM.getInstance.addDefSkuRes(H8072, com.govee.straightfloorlamp.R.mipmap.add_list_type_device_6072);
        ThemeM.getInstance.addDefSkuRes(H6076, com.govee.straightfloorlamp.R.mipmap.add_list_type_device_6076);

        /*注册diyOp*/
        DiyM.getInstance.addDiyOp(EffectOp4BleIot.getInstance);
    }

    /**
     * 效果op操作-针对ble+wifi的goodsType
     */
    public static final int[] effectOp4bleWifiGoodsTypes = {
            GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1,
            GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2,
            GoodsType.GOODS_TYPE_VALUE_H60B2,
    };
    /**
     * 节律支持的goodsType
     */
    public static final int[] rhythmGoodsTypes = {
            GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1,
            GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2,
            GoodsType.GOODS_TYPE_VALUE_H60B2,
    };
    public static List<Protocol> supportProtocols4H60b2 = new ArrayList<>();
    /**
     * v1版本的支持蓝牙配对的goodsType集合
     */
    public static String[] supportPairGoodsTypeSet = new String[]{
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H60B2),
    };
    /**
     * 支持ble的goodsType集合
     */
    public static String[] supportBleGoodsTypeSet = new String[]{
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H60B2),
    };

    /**
     * 小组件支持的goodsType
     */
    public static final String[] widget4BleGoodsTypeSet = {
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2),
    };

    /*6072*/
    public static boolean supportPactV1(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1 && !supportProtocolsV1.isEmpty()) {
            for (Protocol pro : supportProtocolsV1) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    /*6072 富芮坤方案*/
    public static boolean supportPactV2(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1 && !supportProtocolsV2.isEmpty()) {
            for (Protocol pro : supportProtocolsV2) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    /*H6076*/
    public static boolean supportPactV3(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2 && !supportProtocolsV3.isEmpty()) {
            for (Protocol pro : supportProtocolsV3) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    public static boolean supportPactV4(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2 && !supportProtocolsV4.isEmpty()) {
            for (Protocol pro : supportProtocolsV4) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    /**
     * 支持iot的goodsType集合
     */
    public static String[] supportIotGoodsTypeSet = new String[]{
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H60B2),
    };

    /*H60b2*/
    public static boolean supportPact4H60b2(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H60B2 && !supportProtocols4H60b2.isEmpty()) {
            for (Protocol pro : supportProtocols4H60b2) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    /**
     * 是否显示设备详情页头部的版本号
     */
    public static boolean isShowVersion(int goodsType) {
        return goodsType != GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2;
    }

    /**
     * 是否支持设备锁
     */
    public static boolean isSupportDeviceLock(int goodsType) {
        return goodsType != GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2;
    }

    /**
     * 获取wifi输入限制
     *
     * @return [2];[0]ssidInputLimit;[1]passwordInputLimit
     */
    public static int[] getWifiInputLimit() {
        return new int[]{32, 64};
    }

    public static int[] getDefHeaderRes(int goodsType) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
            return new int[]{com.govee.straightfloorlamp.R.mipmap.new_light_title_6072_on, com.govee.straightfloorlamp.R.mipmap.new_light_title_6072_off, com.govee.straightfloorlamp.R.mipmap.new_light_title_6072_off};
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
            return new int[]{com.govee.straightfloorlamp.R.mipmap.new_light_title_6076_on, com.govee.straightfloorlamp.R.mipmap.new_light_title_6076_off, com.govee.straightfloorlamp.R.mipmap.new_light_title_6076_off};
        }
        return null;
    }

    /**
     * 亮度值-[3]-[0]:minBrightness;[1]:maxBrightness;[2]:1-需要计算百分比-0不需要计算百分比
     *
     * @return
     */
    public static int[] getBrightnessRange() {
        return new int[]{1, 100, 0};
    }

    /**
     * 检测给到wifi设备的当前服务器域名版本值
     *
     * @return
     */
    public static int check2WifiDeviceRunModeVersion() {
        return 1;/*新sku默认使用v1*/
    }

    private static final HashMap<Integer, ScenesHint> scenesHintMap = new HashMap<>();

    /**
     * 获取场景提示信息
     *
     * @param effect
     * @return null表明当前不支持hint提示
     */
    public static ScenesHint getScenesHint(int effect) {
        checkHintMap();
        return scenesHintMap.get(effect);
    }

    private synchronized static void checkHintMap() {
        if (scenesHintMap.isEmpty()) {
            /*警报提示*/
            ScenesHint scenesHint4alarm = ScenesHint.makeHint4alarm(ScenesRgb.effect_alarm, ScenesOp.scene_type_rgb);
            scenesHintMap.put(scenesHint4alarm.effect, scenesHint4alarm);
        }
    }

    /**
     * 是否是场景多包
     *
     * @param sku    sku
     * @param device
     * @param mode   mode
     * @return AbsMultipleControllerV14Scenes
     */
    public static AbsMultipleControllerV14Scenes is2NewScenesMode(String sku, String device, Mode mode) {
        if (mode == null) return null;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenes) {
            int effect = ((SubModeScenes) subMode).effect;
            Category.Scene scene = ScenesM.getInstance.getScene(sku, device, effect);
            if (scene != null) {
                return ScenesOp.parseScene(scene, ScenesOp.scene_type_rgb, ScenesOp.scene_type_rgbic);
            }
            return ScenesOp.parseEffect(sku, effect, 1, 2);
        }
        return null;
    }

    private static final String hard_version_v0_new_color_mode = "1.00.01";
    private static final String soft_version_v0_new_color_mode = "1.00.11";

    /**
     * 是否支持带分段亮度控制
     *
     * @param versionSoft
     * @param versionHard
     * @return
     */
    public static boolean supportSubModeColor4PartBrightness(int goodsType, String versionSoft, String versionHard) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
            int curVersionHardInt = BleUtil.parseVersion(versionHard);
            int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
            if (curVersionHardInt <= 0 || curVersionSoftInt <= 0) return false;
            int compareVersionHardInt = BleUtil.parseVersion(hard_version_v0_new_color_mode);
            int compareVersionSoftInt = BleUtil.parseVersion(soft_version_v0_new_color_mode);
            return curVersionHardInt >= compareVersionHardInt && curVersionSoftInt >= compareVersionSoftInt;
        }
        /*6076默认支持*/
        return true;
    }


    private static final String soft_version_new_scenes_mode = "1.01.00";

//    /**
//     * 是否支持新场景-10分区
//     *
//     * @param versionSoft
//     * @return
//     */
//    public static boolean supportNewScenes(String versionSoft) {
//        int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
//        if (curVersionSoftInt <= 0) return false;
//        int compareVersionSoftInt = BleUtil.parseVersion(soft_version_new_scenes_mode);
//        return curVersionSoftInt >= compareVersionSoftInt;
//    }

    /**
     * 是否支持新场景-10分区
     *
     * @param versionSoft
     * @return
     */
    public static boolean supportNewScenes(int goodsType, String versionSoft) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) return true;
        int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
        if (curVersionSoftInt <= 0) return false;
        int compareVersionSoftInt = BleUtil.parseVersion(soft_version_new_scenes_mode);
        return curVersionSoftInt >= compareVersionSoftInt;
    }

    /**
     * 获取颜色模式的对应UI版本号
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param versionHard
     * @return
     */
    public static int getSubModeColorVersion(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getSubModeColorVersion() sku = " + sku + " ; goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode + " ; versionSoft = " + versionSoft + " ; versionHard = " + versionHard);
        }
        boolean supportSubModeColor4PartBrightness = supportSubModeColor4PartBrightness(goodsType, versionSoft, versionHard);
        if (supportSubModeColor4PartBrightness) return 2;
        return 0;
    }

    /**
     * @param versionSoft 迭代成富瑞坤
     * @return
     */
    public static boolean isNewVersionByFRK(String versionSoft) {
        int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
        if (curVersionSoftInt <= 0) return false;
        int compareVersionSoftInt = BleUtil.parseVersion(soft_version_FRK);
        return curVersionSoftInt >= compareVersionSoftInt;
    }

    private static final String soft_version_FRK = "2.00.00";

    /**
     * 获取分段颜色模式一包中包含几个颜色
     *
     * @param goodsType
     * @return
     */
    public static int getColorModeOneGroupNum(int goodsType) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
            return 3;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
            return 4;
        }
        return 3;
    }

    /**
     * 获取最大的灯带段数
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return
     */
    public static int getBulbStringMaxNum(int goodsType, int pactType, int pactCode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getBulbStringMaxNum() goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode);
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
            return 8;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
            return 7;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_H60B2) {
            return 3;
        }
        return 8;
    }

    public static int getBulbStringMaxNum(int goodsType) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
            return 8;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
            return 7;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_H60B2) {
            return 3;
        }
        return 8;
    }

    public static int getOneGroupColorSize(int goodsType) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
            return 3;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
            return 4;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_H60B2) {
            return 3;
        }
        return 3;
    }

    public static int getGoodsTypeByName(String name) {
        if (H6072.contains(name) || name.contains(H6072) || H8072.contains(name) || name.contains(H8072)) {
            return GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1;
        } else if (H6076.contains(name) || name.contains(H6076) || "H8076".contains(name) || name.contains("H8076")) {
            return GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2;
        } else if (H60B2.contains(name)) {
            return GoodsType.GOODS_TYPE_VALUE_H60B2;
        }
        return GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1;
    }

    /*diy version 6076与6072相同都支持1*/
    public static int getDiyVersion(int goodsType, String versionSoft, String versionHard) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
            boolean support = supportSubModeColor4PartBrightness(goodsType, versionSoft, versionHard);
            return support ? 1 : 0;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
            return 1;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_H60B2) {
            //todo 待定
            return 1;
        }
        return 0;
    }

    /**
     * 检查场景协议版本
     *
     * @param sceneType
     * @param cmdVersion
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param versionHard
     * @return
     */
    public static boolean checkSceneVersion(int sceneType, int cmdVersion, String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkSceneVersion() sceneType = " + sceneType + " ; cmdVersion = " + cmdVersion + " ; sku = " + sku + " ; goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode + " ; versionSoft = " + versionSoft + " ; versionHard = " + versionHard);
        }
        return cmdVersion == 0;
    }

    public static void isSupportMicByPhone(String sku, String device, String softVersion, int goodsType) {
        AbsMicFragmentV4.SupportMicStatus support = AbsMicFragmentV4.SupportMicStatus.not_support;
        try {
            if (TextUtils.isEmpty(softVersion)) {
                return;
            }
            int curVersion = Integer.parseInt(softVersion.replace(".", ""));
            int flagVersion = 0;
            if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
                flagVersion = 10009;
            }
            if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
                flagVersion = 10000;
            }
            if (flagVersion != 0) {
                if (flagVersion <= curVersion) {
                    support = AbsMicFragmentV4.SupportMicStatus.support_new_order;
                }
            }
            AbsMicFragmentV4.saveSupportMicModeByPhone(sku, device, support);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static AbsMicFragmentV4.SupportMicStatus getMicStatus(String sku, String versionSoft, int goodsType) {
        int curVersion = NumberUtil.parseVersion(versionSoft);
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
            if (curVersion >= NumberUtil.parseVersion("1.00.09"))
                return AbsMicFragmentV4.SupportMicStatus.support_new_order;
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
            if (curVersion >= NumberUtil.parseVersion("1.00.00"))
                return AbsMicFragmentV4.SupportMicStatus.support_new_order;
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H60B2) {
            return AbsMicFragmentV4.SupportMicStatus.support_new_order;
        }
        return AbsMicFragmentV4.SupportMicStatus.not_support;
    }

    public static boolean supportGroupFactorV1(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1;
    }

    public static boolean supportGroupFactorV2(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2;
    }

    /**
     * 支持抽象场景的op集合
     *
     * @return
     */
    public static int[] supportScenesOpSet(int goodsType, String versionSoft) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1 && !supportNewScenes(goodsType, versionSoft)) {
            return new int[]{
                    ScenesOp.scene_type_op_static,
                    ScenesOp.scene_type_op_rgb,
            };
        }
        return new int[]{
                ScenesOp.scene_type_op_static,
                ScenesOp.scene_type_op_rgb,
                ScenesOp.scene_type_op_rgbic_v0,
                ScenesOp.scene_type_op_rgbic_v1,
        };
    }

    /**
     * 是否是新的音乐模式code
     *
     * @param musicCode
     * @return
     */
    public static boolean isH6072NewMusicCode(int musicCode) {
        return AbsNewMusicEffect.newMusicCode4H6072List.contains(musicCode);
    }


    private static final String hard_version_support_ai = "1.00.01";
    private static final String soft_version_support_ai = "1.01.01";

    /**
     * 固件是否支持新音乐模式
     * 72富芮坤 2.02.00 卓胜微 1.03.00
     * 76 1.02.00
     *
     * @return
     */
    public static boolean isSupportNewMusicMode(int goodsType, int pactType, int pactCode, String versionSoft) {
        Protocol protocol = GoodsType.beProtocol(pactType, pactCode);
        int curVersion = NumberUtil.parseVersion(versionSoft);
        if (supportPactV1(goodsType, protocol)) {
            /*6072-卓胜微*/
            return curVersion >= NumberUtil.parseVersion("1.03.00");
        } else if (supportPactV2(goodsType, protocol)) {
            /*6072-富芮坤*/
            return curVersion >= NumberUtil.parseVersion("2.02.00");
        } else if (supportPactV3(goodsType, protocol) || supportPactV4(goodsType, protocol)) {
            /*6076*/
            return curVersion >= NumberUtil.parseVersion("1.02.00");
        }
        return false;
    }

    public static boolean supportAiEffect(int goodsType, String versionSoft, String versionHard) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
            int curVersionHardInt = BleUtil.parseVersion(versionHard);
            int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
            if (curVersionHardInt <= 0 || curVersionSoftInt <= 0) return false;
            int compareVersionHardInt = BleUtil.parseVersion(hard_version_support_ai);
            int compareVersionSoftInt = BleUtil.parseVersion(soft_version_support_ai);
            return curVersionHardInt >= compareVersionHardInt && curVersionSoftInt >= compareVersionSoftInt;
        } else {
            //6076默认全部支持
            return true;
        }
    }

    private static final String soft_version_support_feast_h6072 = "2.03.00";
    private static final String hard_version_support_feast_h6072 = "3.02.00";
    private static final String soft_version_support_feast_h6076 = "1.03.00";
    private static final String hard_version_support_feast_h6076 = "3.01.01";

    /**
     * 是否支持盛宴
     *
     * @param versionSoft
     * @return
     */
    public static boolean supportJump2FeastAc(int goodsType, String versionSoft, String versionHard) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
            int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
            int curVersionHardInt = BleUtil.parseVersion(versionHard);
            if (curVersionSoftInt <= 0 || curVersionHardInt <= 0) return false;
            int compareVersionSoftInt = BleUtil.parseVersion(soft_version_support_feast_h6072);
            int compareVersionHardInt = BleUtil.parseVersion(hard_version_support_feast_h6072);
            return curVersionSoftInt >= compareVersionSoftInt && curVersionHardInt >= compareVersionHardInt;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
            int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
            int curVersionHardInt = BleUtil.parseVersion(versionHard);
            if (curVersionSoftInt <= 0 || curVersionHardInt <= 0) return false;
            int compareVersionSoftInt = BleUtil.parseVersion(soft_version_support_feast_h6076);
            int compareVersionHardInt = BleUtil.parseVersion(hard_version_support_feast_h6076);
            return curVersionSoftInt >= compareVersionSoftInt && curVersionHardInt >= compareVersionHardInt;
        }
        return false;
    }

    private static final String soft_version_support_high_color_h6072 = "1.03.05";

    /**
     * h6072 Tl是否支持高阶颜色模式
     *
     * @param versionSoft
     * @return
     */
    public static boolean supportHighColor4H6072TL(int goodsType, String versionSoft, String versionHard) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1) {
            boolean tl = OtaType.isTelinkOta(versionHard);
            return !tl || BleUtil.parseVersion(versionSoft) >= BleUtil.parseVersion(soft_version_support_high_color_h6072);
        }
        return true;
    }

    public static boolean supportAiLight(int goodsType, String sku) {
        return (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1 && TextUtils.equals(sku, H6072))
                || (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) && TextUtils.equals(sku, H6076);
    }

    public static int[] colorTemRange(int goodsType) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2) {
            return new int[]{2200, 6500};
        }
        return new int[]{2000, 9000};
    }

    /**
     * @param goodsType
     * @return 是否支持断点记忆
     */
    public static boolean supportOnOffMemory(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_H60B2;
    }

    /**
     * @param goodsType
     * @return 是否支持子开关
     */
    public static boolean supportSubOpen(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_H60B2;
    }

    /**
     * 是否是H6076 迭代款
     *
     * @param goodsType
     * @param pactType
     * @return
     */
    public static boolean isH6076Iteration(int goodsType, int pactType) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2 && pactType == GoodsType.PACT_TYPE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V3) {
            return true;
        }
        return false;
    }
}