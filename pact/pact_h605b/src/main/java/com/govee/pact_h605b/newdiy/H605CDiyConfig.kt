package com.govee.pact_h605b.newdiy

import com.govee.base2home.pact.GoodsType
import com.govee.base2kt.utils.ColorUtils
import com.govee.base2light.ac.diyNew.config.DiyNewConfig
import com.govee.base2light.ac.diyNew.config.IDiyNewConfigManager
import com.govee.base2light.ac.diyNew.config.params.DiyColorParams
import com.govee.base2light.ac.diyNew.config.params.DiyDirectionParams
import com.govee.base2light.ac.diyNew.config.params.DiySpeedParams
import com.govee.base2light.ac.diyNew.effect.Diy4MixEffect
import com.govee.base2light.ac.diyNew.effect.Diy4RgbIcGraffiti
import com.govee.base2light.ac.diyNew.effect.Diy4ShareCode0x00
import com.govee.base2light.ac.diyNew.effect.base.IDiyNewEffect
import com.govee.base2light.ac.diyNew.net.DiyEffectGif
import com.govee.base2light.ac.diyNew.parse.DiyProtocolParseShare0x00
import com.govee.base2light.ac.diyNew.parse.IDiyParse
import com.govee.base2light.ac.diyNew.parse.RgbIcGraffitiShare0x08
import com.govee.base2light.ac.diyNew.parse.temp.DiyTemplateParse
import com.govee.base2light.ac.diyNew.views.DiyNewDirection
import com.govee.base2light.kt.comm.Info4BleIotDevice
import com.govee.ui.R
import com.ihoment.base2app.util.ResUtil


/**
 * H605C 新版diy配置
 */
object H605CDiyConfig {

    fun build() {
        /** 步骤一：构建协议解析类 如 DiyProtocolParseShare0x00，需实现 IDiyParse 接口*/
        val parseList = mutableListOf<IDiyParse>()
        parseList.add(DiyProtocolParseShare0x00())
        parseList.add(RgbIcGraffitiShare0x08())
        parseList.add(DiyTemplateParse())

        /** 步骤二：构建 DIY 管理类*/
        val config4Camera = object : IDiyNewConfigManager {
            override fun getBasicEffectList(info4BleIotDevice: Info4BleIotDevice): MutableList<IDiyNewEffect> {
                return mutableListOf<IDiyNewEffect>().apply {
//                    淡入淡出1--全段
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(0, 0, 1),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            1, ResUtil.getString(R.string.b2light_diy_fade).plus(1)
                        )
                    )
//                    淡入淡出2--分段
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(0, 1, 2),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            2, ResUtil.getString(R.string.b2light_diy_fade).plus(2)
                        )
                    )
//                    淡入淡出3--循环
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(0, 2, 3),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            3, ResUtil.getString(R.string.b2light_diy_fade).plus(3)
                        )
                    )
//                    跳跃1--全段
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(1, 0, 4),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            4, ResUtil.getString(R.string.b2light_diy_jumping).plus(1)
                        )
                    )
//                    跳跃2--循环
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(1, 2, 6),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            5, ResUtil.getString(R.string.b2light_diy_jumping).plus(2)
                        )
                    )
//                    闪烁1--全段
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(2, 0, 7),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            6, ResUtil.getString(R.string.b2light_scenes_blinking).plus(1)
                        )
                    )
//                    闪烁2--分段
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(2, 1, 8),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            7, ResUtil.getString(R.string.b2light_scenes_blinking).plus(2)
                        )
                    )
//                    闪烁3--循环
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(2, 2, 9),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            8, ResUtil.getString(R.string.b2light_scenes_blinking).plus(3)
                        )
                    )
//                    跑马灯1--直线
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(3, 3, 10),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            9, ResUtil.getString(R.string.b2light_diy_marquee).plus(1)
                        )
                    )
//                    跑马灯2--聚拢
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(3, 4, 11),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            10, ResUtil.getString(R.string.b2light_diy_marquee).plus(2)
                        )
                    )
//                    跑马灯3--分散
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(3, 5, 12),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            11, ResUtil.getString(R.string.b2light_diy_marquee).plus(3)
                        )
                    )
//                    流水(电源->末端)
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(8, 9, 19),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            gifId = 12,
                            ResUtil.getString(R.string.b2light_diy_effect_stream).plus(1)
                        )
                    )
//                    流水(末端->电源)
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(8, 10, 20),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            gifId = 13,
                            ResUtil.getString(R.string.b2light_diy_effect_stream).plus(2)
                        )
                    )
//                    彩虹(电源->末端)
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(9, 9, 21),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            gifId = 14,
                            ResUtil.getString(R.string.b2light_scenes_stream).plus(1)
                        )
                    )
//                    彩虹(末端->电源)
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(9, 10, 22),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            gifId = 15,
                            ResUtil.getString(R.string.b2light_scenes_stream).plus(2)
                        )
                    )
//                    追逐
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffect(
                            intArrayOf(10, 0, 23),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 3),
                                defColors = intArrayOf(
                                    ColorUtils.toColor(255, 0, 0),
                                    ColorUtils.toColor(0, 255, 0),
                                    ColorUtils.toColor(0, 0, 255)
                                ),
                                mainColorSupportNoColor = true
                            ),
                            speedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            16, ResUtil.getString(R.string.b2light_scenes_chase)
                        )
                    )
//                    音乐1--节奏
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffectNoSpeed(
                            intArrayOf(4, 8, 15),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            17, ResUtil.getString(R.string.b2light_icon_label_for_music).plus(1)
                        )
                    )
//                    音乐2--频谱
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffectNoSpeed(
                            intArrayOf(4, 6, 13),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            18, ResUtil.getString(R.string.b2light_icon_label_for_music).plus(2)
                        )
                    )
//                   音乐3--滚动
                    add(
                        Diy4ShareCode0x00.makeSimpleDiyEffectNoSpeed(
                            intArrayOf(4, 7, 14),
                            colorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                            19, ResUtil.getString(R.string.b2light_icon_label_for_music).plus(3)
                        )
                    )

                    //                    涂鸦
                    add(
                        Diy4RgbIcGraffiti.makeEffect(
                            20, ResUtil.getString(R.string.b2light_scenes_graffiti),
                            intArrayOf(9, 57),
                            intArrayOf(10, 58),
                            intArrayOf(2, 59),
                            intArrayOf(19, 60),
                            intArrayOf(15, 61),
                            intArrayOf(20, 62),
                            directionParams = DiyDirectionParams.makeDirections(
                                mutableListOf<DiyNewDirection>().apply {
                                    add(DiyNewDirection.ssz(9))
                                    add(DiyNewDirection.nsz(10))
                                    add(DiyNewDirection.cycle(2))
                                    add(DiyNewDirection.gradual(19))
                                    add(DiyNewDirection.flash(15))
                                    add(DiyNewDirection.breathe(20))
                                }, 9
                            ),
                            speedParams = DiySpeedParams(intArrayOf(0, 100, 50))
                        )
                    )
                }
            }

            private fun getMusicSpeed(): DiySpeedParams {
                return DiySpeedParams(intArrayOf(1, 100, 50)).apply {
                    title = ResUtil.getString(R.string.b2light_sensitivity_label)
                }
            }

            override fun getMixEffectList(info4BleIotDevice: Info4BleIotDevice): MutableList<IDiyNewEffect> {
                return mutableListOf<IDiyNewEffect>().apply {
                    add(
                        Diy4MixEffect.makeSubEffects(
//                            淡入淡出1--全段
                            DiyEffectGif.make4MixEffect(
                                0,
                                0,
                                1,
                                1,
                                ResUtil.getString(R.string.b2light_diy_fade).plus(1)
                            ),
//                            淡入淡出2--分段
                            DiyEffectGif.make4MixEffect(
                                0,
                                1,
                                2,
                                2,
                                ResUtil.getString(R.string.b2light_diy_fade).plus(2)
                            ),
//                            淡入淡出3--循环
                            DiyEffectGif.make4MixEffect(
                                0,
                                2,
                                3,
                                3,
                                ResUtil.getString(R.string.b2light_diy_fade).plus(3)
                            ),
//                            跳跃1--全段
                            DiyEffectGif.make4MixEffect(
                                1,
                                0,
                                4,
                                4,
                                ResUtil.getString(R.string.b2light_diy_jumping).plus(1)
                            ),
//                            跳跃2--循环
                            DiyEffectGif.make4MixEffect(
                                1,
                                2,
                                6,
                                5,
                                ResUtil.getString(R.string.b2light_diy_jumping).plus(2)
                            ),
//                            闪烁1--全段
                            DiyEffectGif.make4MixEffect(
                                2,
                                0,
                                7,
                                6,
                                ResUtil.getString(R.string.b2light_scenes_blinking).plus(1)
                            ),
//                            闪烁2--分段
                            DiyEffectGif.make4MixEffect(
                                2,
                                1,
                                8,
                                7,
                                ResUtil.getString(R.string.b2light_scenes_blinking).plus(2)
                            ),
//                            闪烁3--循环
                            DiyEffectGif.make4MixEffect(
                                2,
                                2,
                                9,
                                8,
                                ResUtil.getString(R.string.b2light_scenes_blinking).plus(3)
                            ),
//                            跑马灯1--直线
                            DiyEffectGif.make4MixEffect(
                                3,
                                3,
                                10,
                                9,
                                ResUtil.getString(R.string.b2light_diy_marquee).plus(1)
                            ),
//                            跑马灯2--聚拢
                            DiyEffectGif.make4MixEffect(
                                3,
                                4,
                                11,
                                10,
                                ResUtil.getString(R.string.b2light_diy_marquee).plus(2)
                            ),
//                            跑马灯3--分散
                            DiyEffectGif.make4MixEffect(
                                3,
                                5,
                                12,
                                11,
                                ResUtil.getString(R.string.b2light_diy_marquee).plus(3)
                            ),
//                            流水1--电源->末端
                            DiyEffectGif.make4MixEffect(
                                8,
                                9,
                                19,
                                12,
                                ResUtil.getString(R.string.b2light_diy_effect_stream).plus(1)
                            ),
//                            流水2
                            DiyEffectGif.make4MixEffect(
                                8,
                                10,
                                20,
                                13,
                                ResUtil.getString(R.string.b2light_diy_effect_stream).plus(2)
                            ),
//                            彩虹1--电源->末端
                            DiyEffectGif.make4MixEffect(
                                9,
                                9,
                                21,
                                14,
                                ResUtil.getString(R.string.b2light_scenes_stream).plus(1)
                            ),
                            //彩虹2
                            DiyEffectGif.make4MixEffect(
                                9,
                                10,
                                22,
                                15,
                                ResUtil.getString(R.string.b2light_scenes_stream).plus(2)
                            ),
                            diySpeedParams = DiySpeedParams(intArrayOf(1, 100, 50)),
                            diyColorParams = DiyColorParams.simple(
                                intArrayOf(1, 8),
                                mainColorSupportNoColor = true
                            ),
                        )
                    )
                }
            }

        }

        /** 步骤三：添加到全局缓存数据集合*/
        DiyNewConfig.addNewEffectConfig(
            GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT,
            config4Camera
        )
        DiyNewConfig.addDiyEffectParse(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT, parseList)
    }

}
