package com.govee.thnew.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->读写温度告警信息的事件
 */
class Event4TemWarning private constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON>an, commandType: Byte, proType: Byte) : AbsControllerEvent(result, write, commandType, proType, !write) {

    /**
     * 温度告警下限值*100
     */
    var minTem = 0

    /**
     * 温度告警上限值*100
     */
    var maxTem = 0
    var openWarning = false

    /**
     * 延迟告警分钟(若不支持，则默认为0)
     */
    var delayPushTime = 0

    companion object {

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(Event4TemWarning(false, write, commandType, proType))
        }

        fun sendSuc(write: <PERSON>olean, commandType: Byte, proType: Byte, openWarning: Boolean, minTem: Int, maxTem: Int, delayPushTime: Int = 0) {
            val event = Event4TemWarning(true, write, commandType, proType)
            event.openWarning = openWarning
            event.minTem = minTem
            event.maxTem = maxTem
            event.delayPushTime = delayPushTime
            EventBus.getDefault().post(event)
        }
    }
}