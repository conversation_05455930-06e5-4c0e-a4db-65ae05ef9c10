package com.govee.thnew.pact

import android.os.Bundle
import com.alibaba.android.arouter.facade.annotation.Route
import com.govee.base2home.Constant4L5
import com.govee.base2home.constant.PathBaseHome
import com.govee.base2home.main.AbsDevice
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.deviceitem.Ext4Gw
import com.govee.thnew.add.AddInfo
import com.govee.thnew.config.ThConfig4Detail
import com.govee.thnew.pact.h5112.Ext4H5112
import com.govee.thnew.ui.detail.Ac4ThCommonDetail
import com.govee.thnew.ui.detail.Ac4ThCommonDetailNew
import com.govee.thnew.ui.detail.h5112.Ac4H5112Detail
import com.govee.widget.manager.IWidgetJumpService
import com.ihoment.base2app.util.JsonUtil

@Route(path = PathBaseHome.URL_WIDGET_SERVICE_VERSION1)
class WidgetJumpServiceImp : IWidgetJumpService {
    override fun combineBundle(deviceInfo: AbsDevice): Bundle {
        val settingStr = deviceInfo.deviceExt.deviceSettings ?: ""
        val bindExt = JsonUtil.fromJson(settingStr, AddInfo::class.java) ?: AddInfo()
        bindExt.goodsType = deviceInfo.goodsType
        bindExt.sku = deviceInfo.sku
        bindExt.device = deviceInfo.device
        bindExt.bleHardVersion = deviceInfo.versionHard
        bindExt.bleSoftVersion = deviceInfo.versionSoft

        return Bundle().apply {
            putParcelable(ThConsV1.KEY_4_ADD_INFO, bindExt)
            JsonUtil.fromJson(settingStr, Ext4Gw::class.java).gatewayInfo?.let {
                when (deviceInfo.sku) {
                    Constant4L5.H5112 -> {
                        JsonUtil.fromJson(settingStr, Ext4H5112::class.java).sno?.let { sno4H5112 ->
                            it.sno = sno4H5112
                        }
                    }

                    else -> {}
                }
                putParcelable(ThConsV1.KEY_4_GATEWAY_INFO, it)
            }
        }
    }

    override fun supportAcClassName(deviceInfo: AbsDevice): String? {
        return when (deviceInfo.sku) {
            Constant4L5.H5112 -> {
                Ac4H5112Detail::class.java.name
            }

            else -> {
                if (ThConfig4Detail.useNewDetailPage(deviceInfo.goodsType, deviceInfo.sku)) {
                    Ac4ThCommonDetailNew::class.java.name
                } else {
                    Ac4ThCommonDetail::class.java.name
                }
            }
        }
    }
}