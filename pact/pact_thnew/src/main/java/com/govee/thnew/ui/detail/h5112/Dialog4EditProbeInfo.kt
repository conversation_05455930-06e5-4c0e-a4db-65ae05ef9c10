package com.govee.thnew.ui.detail.h5112

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Rect
import android.text.Editable
import android.text.TextWatcher
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import androidx.annotation.Keep
import androidx.databinding.DataBindingUtil
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.alibaba.android.arouter.utils.TextUtils
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.config.DeviceRoomOrderConfig
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.util.InputUtil
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.dp4Int
import com.govee.base2kt.ext.setVisibility
import com.govee.base2newth.net.IThNet
import com.govee.base2newth.net.Request4Setting
import com.govee.base2newth.net.Response4GetSettings
import com.govee.thnew.R
import com.govee.thnew.add.AddInfo
import com.govee.thnew.databinding.ThnewDialog4H5112EditProbeBinding
import com.govee.thnew.databinding.ThnewDialog4H5112PbNameBinding
import com.govee.thnew.databinding.ThnewItem4H5112ProbeIconBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.op.BleOpManager
import com.govee.util.recordUseCount
import com.ihoment.base2app.Cache
import com.ihoment.base2app.dialog.BaseDialog
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.NetUtil
import com.ihoment.base2app.util.ResUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2024/4/7
 * @description 温湿度计=->设备图表对比选择图表类型的弹窗
 */
class Dialog4EditProbeInfo private constructor(context: Context) : BaseDialog(context) {

    companion object {
        fun createDialog(context: Context): Dialog4EditProbeInfo {
            return Dialog4EditProbeInfo(context)
        }

        private val temHumProbeIconList = arrayListOf<ProbeIcon>().apply {
            add(ProbeIcon(1, R.drawable.thnew_selector_4_pb_icon_1, com.govee.ui.R.string.h5112_text_4_pb_icon_1))
            add(ProbeIcon(2, R.drawable.thnew_selector_4_pb_icon_2, com.govee.ui.R.string.h5112_text_4_pb_icon_2))
            add(ProbeIcon(3, R.drawable.thnew_selector_4_pb_icon_6, com.govee.ui.R.string.h5112_text_4_pb_icon_6))
            add(ProbeIcon(4, R.drawable.thnew_selector_4_pb_icon_7, com.govee.ui.R.string.h5112_text_4_pb_icon_7))
            add(ProbeIcon(5, R.drawable.thnew_selector_4_pb_icon_8, com.govee.ui.R.string.h5112_text_4_pb_icon_8))
        }
        private val temHumProbeIconList4Tab = arrayListOf<ProbeIcon>().apply {
            add(ProbeIcon(1, R.mipmap.h5112_icon_freshness_white, com.govee.ui.R.string.h5112_text_4_pb_icon_1))
            add(ProbeIcon(2, R.mipmap.h5112_icon_freezing_white, com.govee.ui.R.string.h5112_text_4_pb_icon_2))
            add(ProbeIcon(3, R.mipmap.h5112_icon_cabinet_white, com.govee.ui.R.string.h5112_text_4_pb_icon_6))
            add(ProbeIcon(4, R.mipmap.h5112_icon_greenhouse_white, com.govee.ui.R.string.h5112_text_4_pb_icon_7))
            add(ProbeIcon(5, R.mipmap.h5112_icon_rv_white, com.govee.ui.R.string.h5112_text_4_pb_icon_8))
        }

        private val temProbeIconList = arrayListOf<ProbeIcon>().apply {
            add(ProbeIcon(1, R.drawable.thnew_selector_4_pb_icon_1, com.govee.ui.R.string.h5112_text_4_pb_icon_1))
            add(ProbeIcon(2, R.drawable.thnew_selector_4_pb_icon_2, com.govee.ui.R.string.h5112_text_4_pb_icon_2))
            add(ProbeIcon(3, R.drawable.thnew_selector_4_pb_icon_3, com.govee.ui.R.string.h5112_text_4_pb_icon_3))
            add(ProbeIcon(4, R.drawable.thnew_selector_4_pb_icon_4, com.govee.ui.R.string.h5112_text_4_pb_icon_4))
            add(ProbeIcon(5, R.drawable.thnew_selector_4_pb_icon_5, com.govee.ui.R.string.h5112_text_4_pb_icon_5))
            add(ProbeIcon(6, R.drawable.thnew_selector_4_pb_icon_6, com.govee.ui.R.string.h5112_text_4_pb_icon_6))
            add(ProbeIcon(7, R.drawable.thnew_selector_4_pb_icon_7, com.govee.ui.R.string.h5112_text_4_pb_icon_7))
            add(ProbeIcon(8, R.drawable.thnew_selector_4_pb_icon_8, com.govee.ui.R.string.h5112_text_4_pb_icon_8))
        }
        private val temProbeIconList4Tab = arrayListOf<ProbeIcon>().apply {
            add(ProbeIcon(1, R.mipmap.h5112_icon_freshness_white, com.govee.ui.R.string.h5112_text_4_pb_icon_1))
            add(ProbeIcon(2, R.mipmap.h5112_icon_freezing_white, com.govee.ui.R.string.h5112_text_4_pb_icon_2))
            add(ProbeIcon(3, R.mipmap.h5112_icon_fish_white, com.govee.ui.R.string.h5112_text_4_pb_icon_3))
            add(ProbeIcon(4, R.mipmap.h5112_icon_bathtub_white, com.govee.ui.R.string.h5112_text_4_pb_icon_4))
            add(ProbeIcon(5, R.mipmap.h5112_icon_swimming_white, com.govee.ui.R.string.h5112_text_4_pb_icon_5))
            add(ProbeIcon(6, R.mipmap.h5112_icon_cabinet_white, com.govee.ui.R.string.h5112_text_4_pb_icon_6))
            add(ProbeIcon(7, R.mipmap.h5112_icon_greenhouse_white, com.govee.ui.R.string.h5112_text_4_pb_icon_7))
            add(ProbeIcon(8, R.mipmap.h5112_icon_rv_white, com.govee.ui.R.string.h5112_text_4_pb_icon_8))
        }

        /**
         * 获取探针图标
         * 备注：tab中仅用白色图标
         */
        fun getPbIcon(probeIndex: Int, pbIconIndex: Int, isUsed4Tab: Boolean = false): Int? {
            val probeIconList = when (probeIndex) {
                Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                    if (isUsed4Tab) {
                        temHumProbeIconList4Tab
                    } else {
                        temHumProbeIconList
                    }
                }

                Constant4L5.PROBE_INDEX_4_TEM -> {
                    if (isUsed4Tab) {
                        temProbeIconList4Tab
                    } else {
                        temProbeIconList
                    }
                }

                else -> {
                    return null
                }
            }
            var iconIndex = pbIconIndex - 1
            if (iconIndex < 0 || iconIndex >= probeIconList.size) {
                iconIndex = 0
            }
            return probeIconList[iconIndex].iconRes
        }
    }

    private lateinit var binding: ThnewDialog4H5112EditProbeBinding
    private var selPbIconCallback: ((selectIcon: ProbeIcon) -> Unit)? = null
    private var editPbNameCallback: ((pbName: String) -> Unit)? = null
    private var selectIcon = temProbeIconList.first()
    private var probeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM

    private val setIconAdapter by lazy {
        Adapter4ProbeIcon()
    }

    private val inputPbNameDialog by lazy {
        Dialog4InputPbName.createDialog(context)
    }

    init {
        changeDialogOutside(false)
        immersionMode()
        updatePosition(0, 0, Gravity.BOTTOM)
        binding.rvIconList4EditProbe.layoutManager = GridLayoutManager(context, 2)
        binding.rvIconList4EditProbe.adapter = setIconAdapter.also {
            it.setOnItemClickListener { _, _, position -> selectIcon(position, true) }
        }
        binding.rvIconList4EditProbe.addItemDecoration(object : ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                outRect.left = 2.5f.dp4Int
                outRect.right = 2.5f.dp4Int
                outRect.bottom = 10.dp4Int
            }
        })
    }

    /**
     * 更新操作显示
     * @param hasGetDeviceInfo first:是否已获取到设备信息，second:first->probeIndex,second->pbIconIndex
     */
    fun updateUi(hasGetDeviceInfo: Boolean) {
        val dataList = when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                temHumProbeIconList
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                temProbeIconList
            }

            else -> {
                return
            }
        }
        binding.tvEditIconText4EditProbe.alpha = if (hasGetDeviceInfo) 1.0f else 0.3f
        binding.rvIconList4EditProbe.alpha = if (hasGetDeviceInfo) 1.0f else 0.3f
        binding.vMask4EditProbe.setVisibility(!hasGetDeviceInfo)
        for (probeIcon in dataList) {
            probeIcon.isSelected = if (hasGetDeviceInfo) selectIcon.index == probeIcon.index else false
        }
        setIconAdapter.setList(dataList)
    }

    /**
     * 选中icon
     */
    @SuppressLint("NotifyDataSetChanged")
    private fun selectIcon(position: Int, isOperated: Boolean) {
        if (position < 0 || position > setIconAdapter.data.lastIndex) {
            return
        }
        if (setIconAdapter.data[position].isSelected) {
            return
        }
        for ((index, probeIcon) in setIconAdapter.data.withIndex()) {
            val isSelected = index == position
            probeIcon.isSelected = isSelected
            if (isSelected) {
                selectIcon = probeIcon
                if (isOperated) {
                    //统计
                    recordSelectPbIcon()
                    //处理选中
                    selPbIconCallback?.invoke(selectIcon)
                }
            }
        }
        setIconAdapter.notifyDataSetChanged()
    }

    /**
     * 统计设置探针图标
     */
    private fun recordSelectPbIcon() {
        when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                when (selectIcon.index) {
                    1 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_fresh_}${Constant4L5.PROBE_INDEX_4_TEM_HUM}")
                    }

                    2 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_freeze_}${Constant4L5.PROBE_INDEX_4_TEM_HUM}")
                    }

                    3 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_cabinet_}${Constant4L5.PROBE_INDEX_4_TEM_HUM}")
                    }

                    4 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_plant_T_}${Constant4L5.PROBE_INDEX_4_TEM_HUM}")
                    }

                    5 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_motor_H_}${Constant4L5.PROBE_INDEX_4_TEM_HUM}")
                    }

                    else -> {}
                }
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                when (selectIcon.index) {
                    1 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_fresh_}${Constant4L5.PROBE_INDEX_4_TEM}")
                    }

                    2 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_freeze_}${Constant4L5.PROBE_INDEX_4_TEM}")
                    }

                    3 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_fish_tank_}${Constant4L5.PROBE_INDEX_4_TEM}")
                    }

                    4 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_bathtub_}${Constant4L5.PROBE_INDEX_4_TEM}")
                    }

                    5 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_pool_}${Constant4L5.PROBE_INDEX_4_TEM}")
                    }

                    6 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_cabinet_}${Constant4L5.PROBE_INDEX_4_TEM}")
                    }

                    7 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_plant_T_}${Constant4L5.PROBE_INDEX_4_TEM}")
                    }

                    8 -> {
                        recordUseCount(Constant4L5.H5112, "${ParamFixedValue.click_set_icon_motor_H_}${Constant4L5.PROBE_INDEX_4_TEM}")
                    }

                    else -> {}
                }
            }

            else -> {}
        }
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getLayoutView(): View {
        binding = ThnewDialog4H5112EditProbeBinding.inflate(LayoutInflater.from(context))
        setListener()
        return binding.root
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth()
    }

    /**
     * 显示弹窗
     */
    fun showDialog(
        probeIndex: Int,
        curSelectIndex: Int,
        probeName: String,
        selPbIconCallback: ((selectIcon: ProbeIcon) -> Unit),
        editPbNameCallback: ((pbName: String) -> Unit),
    ) {
        this.probeIndex = probeIndex
        this.selPbIconCallback = selPbIconCallback
        this.editPbNameCallback = editPbNameCallback
        val hasGetDeviceInfo = (Vm4ThOpManager.instance()?.ld4ConnectStatusChange?.value?.first == BleOpManager.BLE_READ_INFO_FINISH) ||
            Vm4ThOpManager.instance()?.ld4ConnectStatusChange?.value?.second?.first.isTrue()
        updateUi(hasGetDeviceInfo)
        selectIcon(curSelectIndex - 1, false)
        binding.tvPbNameBg4EditProbe.text = probeName
        super.show()
    }

    private fun setListener() {
        binding.ivCloseBtn4EditProbe.clickDelay {
            hide()
        }
        binding.tvPbNameBg4EditProbe.clickDelay {
            inputPbNameDialog.showDialog(probeIndex, binding.tvPbNameBg4EditProbe.text.toString()) {
                binding.tvPbNameBg4EditProbe.text = it
                editPbNameCallback?.invoke(it)
            }
        }
    }

    inner class Adapter4ProbeIcon :
        BaseQuickAdapter<ProbeIcon, BaseViewHolder>(R.layout.thnew_item_4_h5112_probe_icon) {

        override fun convert(holder: BaseViewHolder, item: ProbeIcon) {
            DataBindingUtil.bind<ThnewItem4H5112ProbeIconBinding>(holder.itemView)?.let {
                ResUtil.setImageResource(it.ivIcon4ProbeIcon, item.iconRes)
                it.tvName4ProbeIcon.text = ResUtil.getString(item.nameRes)
                it.clItemContainer4ProbeIcon.isSelected = item.isSelected
                it.ivIcon4ProbeIcon.isSelected = item.isSelected
                it.tvName4ProbeIcon.isSelected = item.isSelected
            }
        }
    }
}

@Keep
data class ProbeIcon(
    var index: Int,
    var iconRes: Int,
    var nameRes: Int,
    var isSelected: Boolean = false
)

/**
 * 输入弹窗
 */
internal class Dialog4InputPbName private constructor(context: Context) : BaseDialog(context) {

    companion object {
        fun createDialog(context: Context): Dialog4InputPbName {
            return Dialog4InputPbName(context)
        }
    }

    private lateinit var binding: ThnewDialog4H5112PbNameBinding
    private var setPbNameCallback: ((newPbName: String) -> Unit)? = null
    private var probeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM
    private val transactions by lazy {
        Transactions()
    }

    /**
     * 显示弹窗
     */
    fun showDialog(probeIndex: Int, curPbName: String, setPbNameCallback: ((newPbName: String) -> Unit)? = null) {
        this.probeIndex = probeIndex
        this.setPbNameCallback = setPbNameCallback
        binding.cetInput4PbName.setText(curPbName)
        super.show()
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getLayoutView(): View {
        binding = ThnewDialog4H5112PbNameBinding.inflate(LayoutInflater.from(context))
        setListener()
        return binding.root
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth() * 335 / 375
    }

    private fun setListener() {
        binding.cetInput4PbName.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

            override fun afterTextChanged(s: Editable?) {
                binding.tvSure4PbName.alpha = if (TextUtils.isEmpty(s)) 0.3f else 1.0f
                binding.tvSure4PbName.isEnabled = !TextUtils.isEmpty(s)
            }
        })
        binding.tvCancel4PbName.clickDelay {
            hide()
        }
        binding.tvSure4PbName.clickDelay {
            Vm4ThOpManager.instance()?.getDeviceInfo()?.let {
                showLoading()
                binding.cetInput4PbName.clearFocus()
                InputUtil.hideInputMethod(binding.cetInput4PbName)
                val newPbName = binding.cetInput4PbName.text.toString()
                val request = Request4Setting(transactions.createTransaction(), it.sku, it.device).apply {
                    when (probeIndex) {
                        Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                            probeName1 = newPbName
                        }

                        Constant4L5.PROBE_INDEX_4_TEM -> {
                            probeName2 = newPbName
                        }

                        else -> {}
                    }
                }
                Cache.get(IThNet::class.java).updateSetting(request).enqueue(IHCallBack(request))
                //统计
                recordUseCount(Constant4L5.H5112, ParamFixedValue.edit_device_name)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponse4UpdatePbName(response: Response4GetSettings) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        hideLoading()
        response.settings?.let { settingsPartValidStr ->
            Vm4ThOpManager.instance()?.getDeviceInfo()?.let { bindExt ->
                //新settings设置接口中修改的数据不会同步到旧的settings设置接口
                //故旧settings接口中获取的settings字符串只取设置的值
                JsonUtil.fromJson(settingsPartValidStr, AddInfo::class.java)?.let {
                    bindExt.probeName1 = it.probeName1
                    bindExt.probeName2 = it.probeName2
                    JsonUtil.parseToHashmap(JsonUtil.toJson(bindExt))?.let { newSettingsMap ->
                        DeviceListConfig.read().getDeviceByKey(bindExt.sku, bindExt.device)?.deviceExt?.deviceSettings?.let { oldSettingsStr ->
                            JsonUtil.parseToHashmap(oldSettingsStr)?.let { oldSettingsMap ->
                                for (key in oldSettingsMap.keys) {
                                    newSettingsMap[key]?.let { newValue ->
                                        oldSettingsMap[key] = newValue
                                    }
                                }
                                DeviceRoomOrderConfig.read().changeDevice(bindExt.sku, bindExt.device, JsonUtil.toJson(oldSettingsMap))
                            }
                        }
                    }
                }
            }
        }
        setPbNameCallback?.invoke(binding.cetInput4PbName.text.toString())
        //隐藏弹窗
        hide()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onErrorResponse(response: ErrorResponse) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        val request = response.request
        when (request) {
            is Request4Setting -> {
                hideLoading()
                //无网络提示
                if (!NetUtil.isNetworkAvailable(context)) {
                    toast(com.govee.ui.R.string.network_anomaly)
                }
                hide()
            }

            else -> {}
        }
    }

    private fun showLoading() {
        LoadingDialog.createDialog(context, com.ihoment.base2app.R.style.DialogDim).setEventKey(this::class.java.name).show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(this::class.java.name)
    }

    override fun hide() {
        super.hide()
        EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
    }
}