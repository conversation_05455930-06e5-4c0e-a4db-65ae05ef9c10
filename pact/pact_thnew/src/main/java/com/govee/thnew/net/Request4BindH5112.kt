package com.govee.thnew.net

import androidx.annotation.Keep
import com.govee.kt.net.Request4DeviceOp
import com.govee.thnew.pact.h5112.Ext4H5112

/**
 * <AUTHOR>
 * @date created on 2025/5/26
 * @description H5044绑定H5112子设备
 */
@Keep
class Request4BindH5112(
    sku: String,
    device: String,
    val devices: MutableList<SubDevice4H5112>
) : Request4DeviceOp(sku, device)

@Keep
data class SubDevice4H5112(val sku: String, val device: String) {
    var settings: Ext4H5112? = null
}