package com.govee.thnew.ui.detail

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.PopupWindow
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.appbar.AppBarLayout
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.temUnit.TemperatureUnitType
import com.govee.base2home.util.IntroUtils
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ac.AbsAc
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.setVisibility
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.ChartVisibleConfig
import com.govee.base2newth.chart.DataGroup
import com.govee.base2newth.chart.IntervalType
import com.govee.base2newth.chart.square.ChartValueType
import com.govee.base2newth.chart.square.newsquare.SquareChartController4New
import com.govee.base2newth.data.ExtV1
import com.govee.base2newth.deviceitem.LastData
import com.govee.base2newth.other.Config4DeviceChartOrder
import com.govee.base2newth.other.Config4LastThValue
import com.govee.base2newth.other.SyncTemUnitUtil
import com.govee.base2newth.ui.adapter.TextListAdapter
import com.govee.base2newth.ui.viewmodel.SquareViewModel
import com.govee.thnew.add.AddInfo
import com.govee.thnew.config.ThConfig4Detail
import com.govee.thnew.config.ThConfig4Setting
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.databinding.ThnewAc4SquareChartNewBinding
import com.govee.thnew.databinding.ThnewSquareContainer4DewPointBinding
import com.govee.thnew.databinding.ThnewSquareContainer4HumBinding
import com.govee.thnew.databinding.ThnewSquareContainer4Pm25Binding
import com.govee.thnew.databinding.ThnewSquareContainer4TemBinding
import com.govee.thnew.databinding.ThnewSquareContainer4VpdBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.setting.Ac4ThCommonSetting
import com.govee.thnew.ui.setting.Ac4ThCommonSettingNew
import com.govee.thnew.update.ota4frk.Ac4UpdateByFrk
import com.govee.thnew.update.ota4v1.Ac4UpdateV1
import com.govee.thnew.update.ota4v2.Ac4UpdateV2
import com.govee.ui.R
import com.govee.ui.dialog.BleUpdateHintDialog
import com.govee.ui.dialog.TimeDialogV13
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import com.tk.mediapicker.utils.DensityUtil

/**
 * <AUTHOR>
 * @date created on 2024/4/01
 * @description 温湿度计-->柱状图表页(新版)
 */
class Ac4ThSquareChartNew : AbsAc<ThnewAc4SquareChartNewBinding>() {

    /**
     * 实时信息
     */
    private var refreshTime = 0L
    private var realTem = 0
    private var realHum = 0
    private var realPm25 = 0

    private val squareChartVm by lazy {
        ViewModelProvider(this)[SquareViewModel::class.java]
    }

    //dataGroup不为null表明处于默认标签下
    private var dataGroup: DataGroup? = null
    private lateinit var chartController: SquareChartController4New

    companion object {
        private const val MENU_TYPE_TIME = 1 //type：时间筛选
        private const val MENU_TYPE_VALUE = 2//type: 值筛选
    }

    /**
     * 图表控件相关
     */
    private val pm25Binding by lazy {
        ThnewSquareContainer4Pm25Binding.inflate(layoutInflater)
    }
    private val temBinding by lazy {
        ThnewSquareContainer4TemBinding.inflate(layoutInflater)
    }
    private val humBinding by lazy {
        ThnewSquareContainer4HumBinding.inflate(layoutInflater)
    }
    private val dpBinding by lazy {
        ThnewSquareContainer4DewPointBinding.inflate(layoutInflater)
    }
    private val vpdBinding by lazy {
        ThnewSquareContainer4VpdBinding.inflate(layoutInflater)
    }

    override fun layoutId(): Int {
        return com.govee.thnew.R.layout.thnew_ac_4_square_chart_new
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.thnew.R.id.ac_container
    }

    override fun adapterInsetViewId(): Int {
        return com.govee.thnew.R.id.top_flag
    }

    override fun statusBarLightMode(): Boolean {
        return false
    }

    /**
     * 该设备自身的实时信息
     */
    private fun getDeviceInfo(): AddInfo? {
        return Vm4ThOpManager.instance()?.getDeviceInfo()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initData()
        initUi()
        initOpClick()
        initObserver()
        loadData(true)
    }

    private var firstTimeToAdjustHeight = true

    override fun onResume() {
        super.onResume()
        if (!firstTimeToAdjustHeight) {
            return
        }
        firstTimeToAdjustHeight = false
        viewBinding.let { vb ->
            vb.root.postDelayed({
                //展示内容未超出一屏高度，则不可滑动
                val realBottom = vb.rlActionBarContainer4Scn.bottom + vb.ablTopContentContainer4Scn.bottom + vb.spBottomPadding4Scn.bottom
                if (realBottom - vb.acContainer.bottom < 0) {
                    val ablLp = vb.thRealInfo4Scn.layoutParams as AppBarLayout.LayoutParams
                    ablLp.scrollFlags = AppBarLayout.LayoutParams.SCROLL_FLAG_NO_SCROLL
                    vb.thRealInfo4Scn.layoutParams = ablLp
                }
            }, 100)
        }
    }

    private fun initData() {
        getDeviceInfo()?.let { bindExt ->
            squareChartVm.apply {
                ext = ExtV1(bindExt.goodsType, bindExt.sku, bindExt.device, bindExt.deviceName, bindExt.address, bindExt.wifiMac, bindExt.temCali, bindExt.humCali)
            }
        }
    }

    private fun initUi() {
        viewBinding.let { vb ->
            getDeviceInfo()?.let { bindExt ->
                vb.tvTitle.text = bindExt.deviceName
                //主+从款sku不显示实时信息
                vb.thRealInfo4Scn.setVisibility(!ThConfig4Support.isMainSubDevice(bindExt.goodsType, bindExt.sku))
                //实时信息展示控件初始化配置
                vb.thRealInfo4Scn.init(bindExt.goodsType, bindExt.sku, bindExt.device)
                updateTemUnit(TemUnitConfig.read().isTemUnitFah(bindExt.sku))
            }
            vb.tvMenuTime.text = squareChartVm.selectMenuTimeStr
            vb.tvMenuValue.text = squareChartVm.selectMenuValueStr
            updateShowTime()
        }
        //初始化图表显示
        initChart()
        refreshDewPView()
        refreshVpdView()
        //图表排序
        changeChartOrder()
    }

    @SuppressLint("RtlHardcoded")
    private fun initOpClick() {
        viewBinding.let { vb ->
            vb.btnBack.clickDelay {
                onBackPressed()
            }
            vb.btnChart.clickDelay {
                finish()
            }
            vb.btnSetting.clickDelay {
                getDeviceInfo()?.let { bindExt ->
                    if (ThConfig4Setting.useNewSettingPage(bindExt.goodsType, bindExt.sku)) {
                        JumpUtil.jump(this, Ac4ThCommonSettingNew::class.java)
                    } else {
                        JumpUtil.jump(this, Ac4ThCommonSetting::class.java)
                    }
                }
            }
            vb.tvMenuTime.clickDelay {
                showMenu(MENU_TYPE_TIME).showAsDropDown(vb.tvMenuTime, -DensityUtil.dp2px(this, 12f), 0, Gravity.LEFT)
            }
            vb.tvMenuValue.clickDelay {
                showMenu(MENU_TYPE_VALUE).showAsDropDown(vb.tvMenuValue, -DensityUtil.dp2px(this, 12f), 0, Gravity.LEFT)
            }
            vb.tvTimeRange.clickDelay {
                showTimeDialog()
            }
        }
        temBinding.temUnitType.clickDelay {
            changeTempUnit()
        }
        dpBinding.ivDpSwitch.clickDelay {
            getDeviceInfo()?.let {
                val selected = !dpBinding.ivDpSwitch.isSelected
                ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_DEW_P, selected)
                refreshDewPView()
            }
        }
        dpBinding.ivDpSwitch1.clickDelay {
            getDeviceInfo()?.let {
                val selected = !dpBinding.ivDpSwitch.isSelected
                ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_DEW_P, selected)
                refreshDewPView()
            }
        }
        dpBinding.ivDewPointIntroIcon.clickDelay {
            getDeviceInfo()?.let {
                IntroUtils.showDewPointIntro(this, it.sku)
            }
        }
        dpBinding.ivDewPointIntroIcon1.clickDelay {
            getDeviceInfo()?.let {
                IntroUtils.showDewPointIntro(this, it.sku)
            }
        }
        vpdBinding.ivVpdSwitch.clickDelay {
            getDeviceInfo()?.let {
                val selected = !vpdBinding.ivVpdSwitch.isSelected
                ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_VPD, selected)
                refreshVpdView()
            }
        }
        vpdBinding.ivVpdSwitch1.clickDelay {
            getDeviceInfo()?.let {
                val selected = !vpdBinding.ivVpdSwitch.isSelected
                ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_VPD, selected)
                refreshVpdView()
            }
        }
        vpdBinding.ivVpdIntroIcon.clickDelay {
            getDeviceInfo()?.let {
                IntroUtils.showVpdIntro(this, it.sku)
            }
        }
        vpdBinding.ivVpdIntroIcon1.clickDelay {
            getDeviceInfo()?.let {
                IntroUtils.showVpdIntro(this, it.sku)
            }
        }
    }

    private fun initObserver() {
        Vm4ThOpManager.instance()?.let { vm ->
            vm.settingOpManager.ld4UpdateDeviceName.observe(this) {
                if (!hasCreated) {
                    return@observe
                }
                getDeviceInfo()?.let { bindExt ->
                    if (it.first == bindExt.getKey()) {
                        viewBinding.tvTitle.text = it.second
                    }
                }
            }
            vm.ld4RealTemHum.observe(this) {
                val thpInfo = it.first
                refreshTime = it.second
                realTem = thpInfo.first
                realHum = thpInfo.second
                realPm25 = thpInfo.third
                //更新实时数据
                viewBinding.thRealInfo4Scn.updateRealInfo(refreshTime, realTem, realHum, realPm25)
            }
            vm.loadThcdManager.ld4ShowChartNew.observe(this) {
                loadData(false)
            }
            vm.ld4UpgradeVersion.observe(this) {
                when (it.first) {
                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_FIRST_REMIND -> {
                        getDeviceInfo()?.let { bindExt ->
                            BleUpdateHintDialog.showDialog(this, bindExt.sku, {
                                toUpgradePage()
                            }, this.javaClass.name)
                        }
                        //再执行红点
                        Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value = Pair(Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND, it.second)
                    }

                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND -> {
                        //显示升级提示红点
                        viewBinding.versionFlag.setVisibility(true)
                    }

                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_FINISH -> {
                        //隐藏升级提示
                        viewBinding.versionFlag.setVisibility(false)
                    }

                    else -> {
                        viewBinding.versionFlag.setVisibility(false)
                    }
                }
            }
            //设置页更新告警和校准时会影响到图表展示
            vm.ld4UpdateDeviceInfo.observe(this) {
                when (it.second) {
                    Vm4ThOpManager.UPDATE_4_WARNING -> {
                        //更新校准等相关信息并刷新图表
                        getDeviceInfo()?.let { bindExt ->
                            squareChartVm.ext = ExtV1(bindExt.goodsType, bindExt.sku, bindExt.device, bindExt.deviceName, bindExt.address, bindExt.wifiMac, bindExt.temCali, bindExt.humCali)
                        }
                        loadData(false)
                    }

                    Vm4ThOpManager.UPDATE_4_CALIBRATION -> {
                        //更新校准等相关信息并刷新图表
                        getDeviceInfo()?.let { bindExt ->
                            squareChartVm.ext = ExtV1(bindExt.goodsType, bindExt.sku, bindExt.device, bindExt.deviceName, bindExt.address, bindExt.wifiMac, bindExt.temCali, bindExt.humCali)
                        }
                        loadData(false)
                        //更新实时数据
                        viewBinding.thRealInfo4Scn.updateRealInfo(refreshTime, realTem, realHum, realPm25)
                    }

                    else -> {}
                }
            }
        }
        squareChartVm.thDataLiveData.observe(this) {
            chartController.updateLookupData(squareChartVm.startTimeStamp, squareChartVm.endTimeStamp, it)
        }
    }

    /**
     * 切换温度单位
     */
    private fun changeTempUnit() {
        getDeviceInfo()?.let {
            val fahOpen = !TemUnitConfig.read().isTemUnitFah(it.sku)
            TemUnitConfig.read().setTemUnit(it.sku, if (fahOpen) TemperatureUnitType.Fahrenheit else TemperatureUnitType.Celsius)
            updateTemUnit(fahOpen)
            //同步温度单位到服务端
            SyncTemUnitUtil.syncTemUnit(Transactions().createTransaction(), it.sku, fahOpen)
            //通知详情页
            Vm4ThOpManager.instance()?.ld4ChangeTemUnit?.value = true
        }
    }

    /**
     * 跳转至升级页面
     */
    private fun toUpgradePage() {
        getDeviceInfo()?.let { bindExt ->
            Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value?.let { upgradePair ->
                upgradePair.second?.run {
                    val upgradeWay = ThConfig4Setting.getUpgradeWay(bindExt.goodsType, bindExt.sku, bindExt.bleHardVersion)
                    SafeLog.i("xiaobing") { "Ac4ThCommonDetail--toUpgradePage-->key=->${bindExt.getKey()},升级方式=->${upgradeWay}" }
                    when (upgradeWay) {
                        ThConfig4Setting.UPGRADE_WAY_4_FRK -> {
                            Ac4UpdateByFrk.jump2OtaUpdate(this@Ac4ThSquareChartNew, bindExt.sku, bindExt.deviceName, this)
                        }

                        ThConfig4Setting.UPGRADE_WAY_4_V1 -> {
                            Ac4UpdateV1.jump2UpdateAc(this@Ac4ThSquareChartNew, bindExt.sku, bindExt.deviceName, this)
                        }

                        ThConfig4Setting.UPGRADE_WAY_4_V2 -> {
                            Ac4UpdateV2.jump2UpdateAc(this@Ac4ThSquareChartNew, bindExt.sku, bindExt.deviceName, this)
                        }

                        else -> {}
                    }
                }
            }
        }
    }

    /**
     * 弹出时间范围筛选弹窗
     */
    private fun showTimeDialog() {
        TimeDialogV13.createDialog(
            this,
            ResUtil.getString(R.string.time_slot_introduction),
            ResUtil.getString(R.string.setting_time_introduction),
            squareChartVm.startTimeStamp,
            squareChartVm.endTimeStamp
        ) { startTime, endTime ->
            //统计
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.select_overview_duration, ParamFixedValue.times);
            //数据处理
            squareChartVm.startTimeStamp = startTime
            squareChartVm.endTimeStamp = endTime
            updateShowTime()
            loadData(true)
        }.setEventKey(TAG).show()
    }

    /**
     *更新展示时间
     */
    private fun updateShowTime() {
        viewBinding.tvTimeRange.text = ResUtil.getStringFormat(
            R.string.time_range,
            TimeFormatM.getInstance().formatTimeToHMYMD(squareChartVm.startTimeStamp),
            TimeFormatM.getInstance().formatTimeToHMYMD(squareChartVm.endTimeStamp)
        )
    }

    /**
     * 时间和间隔筛选条件PopupWindow
     */
    private fun showMenu(type: Int): PopupWindow {
        val lp = window.attributes
        lp.alpha = 0.7f
        window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
        window.attributes = lp
        val view = View.inflate(this, com.govee.base2newth.R.layout.layout_dialog_list_text, null)
        val popWindow = PopupWindow(this)
        popWindow.contentView = view
        val lv: RecyclerView = view.findViewById(com.govee.base2newth.R.id.lv)
        val root: View = view.findViewById(com.govee.base2newth.R.id.root)
        root.setOnClickListener { popWindow.dismiss() }

        popWindow.setBackgroundDrawable(null)
        popWindow.isOutsideTouchable = true
        popWindow.isTouchable = true
        popWindow.isFocusable = true
        val adapter = TextListAdapter(
            com.govee.base2newth.R.layout.item_popup_text,
            if (type == MENU_TYPE_TIME) squareChartVm.menuTimeList.toMutableList() else squareChartVm.menuValueList.toMutableList()
        )
        adapter.selectText = if (type == MENU_TYPE_TIME) squareChartVm.selectMenuTimeStr else squareChartVm.selectMenuValueStr

        popWindow.setOnDismissListener {
            val lp1 = window.attributes
            lp1.alpha = 1f
            window.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            window.attributes = lp1
        }

        adapter.setOnItemClickListener { _, _, position ->
            popWindow.dismiss()
            if (type == MENU_TYPE_TIME) {
                val time = squareChartVm.menuTimeList[position]
                if (time == squareChartVm.selectMenuTimeStr) return@setOnItemClickListener
                squareChartVm.selectMenuTimeStr = time
                viewBinding.tvMenuTime.text = squareChartVm.selectMenuTimeStr
                showLoading()
            } else {
                val value = squareChartVm.menuValueList[position]
                if (value == squareChartVm.selectMenuValueStr) return@setOnItemClickListener
                squareChartVm.selectMenuValueStr = value
                viewBinding.tvMenuValue.text = squareChartVm.selectMenuValueStr
            }
            updateMenuMode()
        }

        lv.layoutManager = LinearLayoutManager(this)
        lv.adapter = adapter

        return popWindow
    }

    private fun initChart() {
        dataGroup = DataGroup.day
        chartController = SquareChartController4New(7)
        chartController.setChartListener(object : SquareChartController4New.ChartListener {
            override fun beScale() {
                dataGroup = null
            }

            override fun timeChange(startTimeStamp: Long, endTimeStamp: Long, intervalType: IntervalType) {}

            override fun invalidateFinish(isLookupNoData: Boolean) {
                val visibility = if (isLookupNoData) View.GONE else View.VISIBLE
                temBinding.temTendencyChart.setEmptyUI(visibility)
                humBinding.humTendencyChart.setEmptyUI(visibility)
                dpBinding.dpTendencyChart.setEmptyUI(visibility)
                vpdBinding.vpdTendencyChart.setEmptyUI(visibility)
                hideLoading()
            }
        })
        temBinding.temTendencyChart.setChart(chartController)
        humBinding.humTendencyChart.setChart(chartController)
        dpBinding.dpTendencyChart.setChart(chartController)
        vpdBinding.vpdTendencyChart.setChart(chartController)
        //设置图标页面无数据状态下的最大最小值
        getDeviceInfo()?.let { bindExt ->
            val defTemRange = ThConfig4Detail.getTemRange(bindExt.goodsType, bindExt.sku, bindExt.bleSoftVersion, bindExt.bleHardVersion)
            temBinding.temTendencyChart.getSquareChart().setDefMaxValueAndMinValue(defTemRange.first * 1.0f, defTemRange.second * 1.0f)
        }
        humBinding.humTendencyChart.getSquareChart().setDefMaxValueAndMinValue(ThConsV1.HUM_MIN_VALUE * 1.0f, ThConsV1.HUM_MAX_VALUE * 1.0f)
        dpBinding.dpTendencyChart.getSquareChart().setDefMaxValueAndMinValue(ThConsV1.DEW_POINT_MIN_VALUE, ThConsV1.DEW_POINT_MAX_VALUE)
        vpdBinding.vpdTendencyChart.getSquareChart().setDefMaxValueAndMinValue(ThConsV1.VPD_MIN_VALUE, ThConsV1.VPD_MAX_VALUE)
        //设置图标页面显示小数点位数
        getDeviceInfo()?.let { bindExt ->
            val pointNumArr = ThConfig4Detail.getChartValueDecimalDigits(bindExt.goodsType, bindExt.sku)
            pm25Binding.pm25TendencyChart.getSquareChart().setPointValue(pointNumArr[0])
            temBinding.temTendencyChart.getSquareChart().setPointValue(pointNumArr[1])
            humBinding.humTendencyChart.getSquareChart().setPointValue(pointNumArr[2])
            dpBinding.dpTendencyChart.getSquareChart().setPointValue(pointNumArr[3])
            vpdBinding.vpdTendencyChart.getSquareChart().setPointValue(pointNumArr[4])
            chartController.intervalType = IntervalType.hour_1_hour
        }
    }

    /**
     * 更新筛选条件
     */
    private fun updateMenuMode() {
        val timeType = when (squareChartVm.selectMenuTimeStr) {
            squareChartVm.menuTimeList[1] -> IntervalType.week_1_day
            squareChartVm.menuTimeList[2] -> IntervalType.year_1_month
            else -> {
                IntervalType.hour_1_hour
            }
        }
        val valueType = when (squareChartVm.selectMenuValueStr) {
            squareChartVm.menuValueList[1] -> ChartValueType.MIN
            squareChartVm.menuValueList[2] -> ChartValueType.AVERAGE
            else -> {
                ChartValueType.MAX
            }
        }
        chartController.setType(timeType, valueType)
    }

    /**
     * 刷新温度按钮状态
     */
    private fun updateTemUnit(fahOpen: Boolean) {
        temBinding.temUnitType.setImageDrawable(ResUtil.getDrawable(if (fahOpen) R.mipmap.new_sensor_setting_switch_fahrenheit else R.mipmap.new_sensor_setting_switch_celsius))
        temBinding.temTendencyChart.getSquareChart().setChangeValueShow(fahOpen, if (fahOpen) getString(R.string.tem_unit_fah) else getString(R.string.tem_unit_cel))
        dpBinding.dpTendencyChart.getSquareChart().setChangeValueShow(fahOpen, if (fahOpen) getString(R.string.tem_unit_fah) else getString(R.string.tem_unit_cel))
    }

    /**
     * 刷新露点
     */
    private fun refreshDewPView() {
        getDeviceInfo()?.let {
            val selected = ChartVisibleConfig.read().getVisible(it.sku, ChartVisibleConfig.CHART_TYPE_DEW_P)
            dpBinding.gpShowContent4Dp.setVisibility(selected)
            dpBinding.gpClose4Dp.setVisibility(!selected)
            dpBinding.ivDpSwitch.isSelected = selected
            dpBinding.ivDpSwitch1.isSelected = false
        }
    }

    /**
     * 刷新VPD
     */
    private fun refreshVpdView() {
        getDeviceInfo()?.let {
            val selected = ChartVisibleConfig.read().getVisible(it.sku, ChartVisibleConfig.CHART_TYPE_VPD)
            vpdBinding.gpShowContent4Vpd.setVisibility(selected)
            vpdBinding.gpClose4Vpd.setVisibility(!selected)
            vpdBinding.ivVpdSwitch.isSelected = selected
            vpdBinding.ivVpdSwitch1.isSelected = false
        }
    }

    private fun loadData(needLoading: Boolean) {
        getDeviceInfo()?.let {
            if (needLoading) {
                showLoading()
            }
            squareChartVm.queryAllData(
                it.sku,
                it.device,
                squareChartVm.startTimeStamp,
                squareChartVm.endTimeStamp
            )
        }
    }

    /**
     * 根据图表排序展示图表
     *
     * @param chartOrder 备注：A->pm2.5，B->温度，C->湿度，D->露点，E->vpd
     */
    private fun changeChartOrder(chartOrder: ArrayList<String>? = null) {
        getDeviceInfo()?.let { bindExt ->
            var usedChartOrder = chartOrder
            if (chartOrder.isNullOrEmpty()) {
                usedChartOrder = Config4DeviceChartOrder.getConfig().getChartOrder(bindExt.sku, bindExt.device) ?: arrayListOf(ThConsV1.PM25, ThConsV1.TEM, ThConsV1.HUM, ThConsV1.DP, ThConsV1.VPD)
            }
            viewBinding.let {
                it.flContent1Container4ThDetail.removeAllViews()
                it.flContent2Container4ThDetail.removeAllViews()
                it.flContent3Container4ThDetail.removeAllViews()
                it.flContent4Container4ThDetail.removeAllViews()
                it.flContent5Container4ThDetail.removeAllViews()
            }
            usedChartOrder?.let {
                fillToContainer(0, usedChartOrder[0])
                fillToContainer(1, usedChartOrder[1])
                fillToContainer(2, usedChartOrder[2])
                fillToContainer(3, usedChartOrder[3])
                fillToContainer(4, usedChartOrder[4])
            }
        }
    }

    private fun fillToContainer(order: Int, type: String) {
        viewBinding.let { vb ->
            //排序填充
            val container = when (order) {
                0 -> vb.flContent1Container4ThDetail
                1 -> vb.flContent2Container4ThDetail
                2 -> vb.flContent3Container4ThDetail
                3 -> vb.flContent4Container4ThDetail
                else -> {
                    vb.flContent5Container4ThDetail
                }
            }
            when (type) {
                ThConsV1.PM25 -> {
                    getDeviceInfo()?.let { bindExt ->
                        container.setVisibility(ThConfig4Support.supportPm25(bindExt.goodsType, bindExt.sku))
                        if (ThConfig4Support.supportPm25(bindExt.goodsType, bindExt.sku)) {
                            container.addView(pm25Binding.root)
                        }
                    }
                }

                ThConsV1.TEM -> {
                    container.setVisibility(true)
                    container.addView(temBinding.root)
                }

                ThConsV1.HUM -> {
                    getDeviceInfo()?.let { bindExt ->
                        container.setVisibility(ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku))
                        if (ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku)) {
                            container.addView(humBinding.root)
                        }
                    }
                }

                ThConsV1.DP -> {
                    getDeviceInfo()?.let { bindExt ->
                        container.setVisibility(ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku))
                        if (ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku)) {
                            container.addView(dpBinding.root)
                        }
                    }
                }

                ThConsV1.VPD -> {
                    getDeviceInfo()?.let { bindExt ->
                        container.setVisibility(ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku))
                        if (ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku)) {
                            container.addView(vpdBinding.root)
                        }
                    }
                }

                else -> {}
            }
            if (vb.flContent1Container4ThDetail == container && container.visibility != View.VISIBLE) {
                val lp: LinearLayoutCompat.LayoutParams = vb.flContent2Container4ThDetail.layoutParams as LinearLayoutCompat.LayoutParams
                lp.topMargin = 0
                vb.flContent2Container4ThDetail.layoutParams = lp
            }
        }
    }

    @SuppressLint("MissingSuperCall")
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        BaseApplication.getBaseApplication().finishAc(Ac4ThCommonDetailNew::class.java)
        finish()
    }

    override fun onPause() {
        getDeviceInfo()?.let {
            //保存最新的实时温湿度值
            Config4LastThValue.getConfig().updateLastTh(it.sku, it.device, getLastData())
        }
        super.onPause()
    }

    private fun getLastData(): LastData {
        val lastData = LastData()
        lastData.tem = realTem
        lastData.hum = realHum
        lastData.pm = realPm25
        lastData.lastTime = refreshTime
        return lastData
    }

    override fun onDestroy() {
        super.onDestroy()
        chartController.destroy()
    }

    private fun showLoading(delayTimeMills: Long = 60 * 1000L) {
        LoadingDialog.createDialog(this, com.ihoment.base2app.R.style.DialogDim, delayTimeMills)
            .setEventKey(TAG).show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(TAG)
    }
}