package com.govee.thnew.ui.setting.h5112

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import com.govee.base2home.Constant
import com.govee.base2home.Constant4L5
import com.govee.base2home.account.LoginActivity
import com.govee.base2home.config.Base2homeConfig
import com.govee.base2home.device.attachSkuShopAccessory
import com.govee.base2home.main.tab.EventTabDefault
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.update.download.CheckVersion
import com.govee.base2home.util.NumberUtil
import com.govee.base2home.util.TemUtil
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.setVisibility
import com.govee.base2kt.ext.toast
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.ThUtil
import com.govee.base2newth.deviceitem.GatewayInfo
import com.govee.base2newth.other.ClearThDataUtils
import com.govee.base2newth.other.Config4LastThValue
import com.govee.base2newth.other.DelayAlarmView
import com.govee.base2newth.other.Dialog4CloseVolumeRemind
import com.govee.base2newth.other.Dialog4LoadAtd
import com.govee.base2newth.other.Event4DeleteThFromGw
import com.govee.base2newth.other.Event4LoadAllData
import com.govee.base2newth.other.LowBatPushView
import com.govee.base2newth.other.VolumeLeveView
import com.govee.ble.BleController
import com.govee.cache.GlobalCache
import com.govee.cache.key.SmartCacheKey
import com.govee.home.account.config.AccountConfig
import com.govee.kt.setting.AbsAc4Setting
import com.govee.kt.ui.view.SettingOp
import com.govee.mvvm.globalLaunch
import com.govee.thnew.add.AddInfo
import com.govee.thnew.config.ThConfig4Detail
import com.govee.thnew.config.ThConfig4Setting
import com.govee.thnew.databinding.ThnewAc4H5112SettingBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.op.BleOpManager
import com.govee.thnew.ui.setting.ThSettingOpItems
import com.govee.thnew.update.ota4frk.Ac4UpdateByFrk
import com.govee.ui.R
import com.govee.ui.component.THCalibrationView
import com.govee.ui.dialog.BleUpdateHintDialog
import com.govee.ui.dialog.ConfirmDialog
import com.govee.ui.dialog.ConfirmDialogV3
import com.govee.ui.dialog.Dialog4ClearThDataSelPeriod
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.CaughtRunnable
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.NetUtil
import com.ihoment.base2app.util.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @date created on 2024/2/22
 * @description 温湿度计-->通用的设置页
 */
class Ac4H5112Setting : AbsAc4Setting<ThnewAc4H5112SettingBinding>() {

    private val fahOpen by lazy {
        getDeviceInfo()?.let {
            TemUnitConfig.read().isTemUnitFah(it.sku)
        } != false
    }

    /**
     * 加载全部数据中的loading弹窗
     */
    private val loadAtdDialog by lazy {
        Dialog4LoadAtd.createDialog(this)
    }

    /**
     * 清理数据时，选择保留时间段
     */
    private val selPeriodDialog by lazy {
        Dialog4ClearThDataSelPeriod.createDialog(this, getDeviceInfo()?.sku ?: "")
    }

    /**
     * 延时告警控件
     */
    private var delayAlarmView4Pb1: DelayAlarmView? = null
    private var delayAlarmView4Pb2: DelayAlarmView? = null

    /**
     * 音量调节控件
     */
    private var volumeLeveView: VolumeLeveView? = null
    private val closeVolumeRemindDialog by lazy {
        Dialog4CloseVolumeRemind.createDialog(this)
    }

    private val calibrationIntroDialog by lazy {
        Dialog4H5112CalibrationIntro.createDialog(this)
    }

    /**
     * 上一次的版本号
     * 备注：升级等情况下，用来判断版本号是否发生变化，若版本号发生变化跟版本号相关联的也须改动
     *      first->bleSv,second->bleHv
     */
    private var lastBleVersion: Pair<String, String>? = null

    private val mHandler by lazy {
        Handler(Looper.getMainLooper())
    }

    override fun initAfterOnCreate() {
        initData()
        initUi()
        initOpClick()
        initObserver()
    }

    override fun onResume() {
        super.onResume()
        //蓝牙打开的情况下，若蓝牙断连则去主动连接一次
        if (BleController.getInstance().isBlueToothOpen && !BleController.getInstance().isConnected) {
            Vm4ThOpManager.instance()?.toConnectBle()
        }
        getDeviceInfo()?.let { bindExt ->
            //分布式网关联动的支持
            //显、隐分布式网关相关功能视图
            val supportRemoteService = Constant4L5.supportRemoteService(bindExt.goodsType, bindExt.sku)
            viewBinding.rsvRemoteService.getBindInfo(
                supportRemoteService,
                bindExt.sku,
                bindExt.device,
                bindExt.deviceName,
                bindExt.bleHardVersion,
                bindExt.bleSoftVersion
            )
            //更新通知权限
            ThConfig4Setting.supportWarnSetting(bindExt.goodsType, bindExt.sku).let {
                if (it == ThConfig4Setting.TH_WARN_TYPE_4_NEW) {
                    viewBinding.twsvWarnSettingNew4H5112Setting.onResume()
                }
            }
        }
    }

    /**
     * 该设备自身的实时信息
     */
    private fun getDeviceInfo(): AddInfo? {
        Vm4ThOpManager.instance()?.getDeviceInfo()?.let {
            return it
        }
        //这种情况直接退回到设备列表页(长时间置于后台，被销毁后，回到前台重建)
        BaseApplication.getBaseApplication().finishOther(Base2homeConfig.getConfig().mainAcClass)
        return null
    }

    /**
     * 该设备绑定的网关的实时信息
     */
    private fun getGateInfo(): GatewayInfo? {
        return Vm4ThOpManager.instance()?.getGateInfo()
    }

    /**
     * 该设备是否已经连接蓝牙的实时状态
     */
    private fun hasBleConnected(): Boolean {
        return Vm4ThOpManager.instance()?.hasBleConnected().isTrue()
    }

    /**
     * 绑定了网关，以网关为准
     */
    private fun isBindGateway(): Boolean {
        return Vm4ThOpManager.instance()?.isBindGateway().isTrue()
    }

    private val items4ShowAllTheTime = arrayListOf<SettingOp>().apply {
        getDeviceInfo()?.let { bindExt ->
            add(SettingOp.makeItemTypeShowing4Sku(bindExt.sku))
            add(
                SettingOp.makeItemTypeShowing4BatteryType(
                    ThConfig4Setting.getBatteryType(
                        bindExt.goodsType,
                        bindExt.sku,
                        bindExt.groupOrder
                    )
                )
            )
        }
    }

    private val items4NeedBle = arrayListOf<SettingOp>().apply {
        getDeviceInfo()?.let { bindExt ->
            //载入全部数据须登录才支持
            if (AccountConfig.read().isHadToken) {
                add(ThSettingOpItems.makeSt4LoadAllData {
                    loadAllThData()
                })
            }
            //删除图表数据
            add(ThSettingOpItems.makeSt4DeleteData("") {
                selPeriodDialog.show(
                    bindExt.goodsType,
                    bindExt.sku,
                    object : Dialog4ClearThDataSelPeriod.OnSelectedListener {
                        override fun onSure(selPeriodType: Int) {
                            if (selPeriodType == 4) {
                                //删除全部数据
                                showDeleteAllDataConfirmDialog()
                            } else {
                                Vm4ThOpManager.instance()?.clearDataByType(selPeriodType)
                            }
                        }
                    })
            })
            add(
                SettingOp.makeItemTypeUpdate4SoftVersion(bindExt.bleSoftVersion, false)
                    .apply {
                        itemClick = {
                            if (it.canUpdate) {
                                toUpgradePage()
                            }
                        }
                    })
            add(SettingOp.makeItemTypeShowing4VersionHard(bindExt.bleHardVersion))
        }
    }

    private fun initData() {
        getDeviceInfo()?.let { bindExt ->
            lastBleVersion = Pair(bindExt.bleSoftVersion, bindExt.bleHardVersion)
        }
        updateGuideInfo()
    }

    private fun initUi() {
        viewBinding.let { vb ->
            vb.vspGuideContainer4H5112Setting.needBg = false
            vb.vspDevice4H5112Setting.needBg = false
            vb.rsvRemoteService.setBackgroundInfo(false, false)
            getDeviceInfo()?.let { bindExt ->
                //设备名
                vb.vdn4Rename4H5112Setting.updateHintLimit(curName = bindExt.deviceName)
                //告警设置
                vb.twsvWarnSettingNew4H5112Setting.initDeviceInfo(bindExt.goodsType, bindExt.sku, bindExt.device)
                ThConfig4Setting.supportVolumeLevel(bindExt.goodsType, bindExt.sku).let { supportVolumeLevel ->
                    if (supportVolumeLevel) {
                        volumeLeveView = VolumeLeveView(this).apply {
                            initDeviceInfo(bindExt.sku, bindExt.device)
                            canOperate(hasBleConnected(), getGateInfo()?.isIotOnLine.isTrue(), true)
                            updateValue(bindExt.muteLevel)
                            bindExt.muteLevel?.let {
                                showCloseVolumeRemind(it)
                            }
                        }
                        vb.twsvWarnSettingNew4H5112Setting.addOtherContent(volumeLeveView!!)
                    }
                }
                vb.twsvWarnSettingNew4H5112Setting.setBackgroundInfo(needBackGround = true, needBottomDivider = false)
                vb.twsvWarnSettingNew4H5112Setting.setVisibility(true)
                //低电量告警
                val supportLowBat = ThConfig4Setting.supportLowBatAlarm(bindExt.goodsType, bindExt.sku)
                supportLowBat.let {
                    if (it.first) {
                        vb.lbpvLowBattery
                            .needIntro(it.third, true)
                            .setSwitch(bindExt.batteryWarning)
                            .setVisibleTopDivider(false)
                            .setVisibleBottomDivider(false)
                            .setVisibility(true)
                    }
                }
                //导航项
                vb.vspGuideContainer4H5112Setting.setItemList(mutableListOf<SettingOp>().apply {
                    add(SettingOp.makeItemType4Guide(sku()))
                })
                //延时告警
                if (ThConfig4Setting.supportDelayAlarm(bindExt.goodsType, bindExt.sku)) {
                    delayAlarmView4Pb1 = DelayAlarmView(this)
                    delayAlarmView4Pb1!!.updateValue(bindExt.delayPushTime)
                    vb.ttarvPb1TemWarning4H5112Setting.addMidLayout(delayAlarmView4Pb1!!)
                }
                //初始化温度告警相关信息(pb1+pb2)
                vb.ttarvPb1TemWarning4H5112Setting.initSubDeviceInfo(bindExt.goodsType, bindExt.sku, bindExt.bleSoftVersion, bindExt.bleHardVersion)
                vb.ttarvPb2TemWarning4H5112Setting.initSubDeviceInfo(bindExt.goodsType, bindExt.sku, bindExt.bleSoftVersion, bindExt.bleHardVersion)
                vb.ttarvPb1TemWarning4H5112Setting.needTitleIcon(true)
                vb.ttarvPb2TemWarning4H5112Setting.needTitleIcon(true)
                if (isBindGateway()) {
                    getGateInfo()?.let { gwInfo ->
                        vb.ttarvPb1TemWarning4H5112Setting.initGwDeviceInfo(0, gwInfo.sku, gwInfo.versionSoft, gwInfo.versionHard)
                        vb.ttarvPb2TemWarning4H5112Setting.initGwDeviceInfo(0, gwInfo.sku, gwInfo.versionSoft, gwInfo.versionHard)
                    }
                }
                vb.ttarvPb1TemWarning4H5112Setting.setBackgroundInfo(false, true)
                vb.ttarvPb2TemWarning4H5112Setting.setBackgroundInfo(false, true)
                val temRangePair = ThConfig4Detail.getTemRange(bindExt.goodsType, bindExt.sku, bindExt.bleSoftVersion, bindExt.bleHardVersion)
                val temLimitRange = if (fahOpen) {
                    Pair(TemUtil.getTemF(temRangePair.first * 1.0f), TemUtil.getTemF(temRangePair.second * 1.0f))
                } else {
                    temRangePair
                }
                vb.ttarvPb1TemWarning4H5112Setting.setTemRange(fahOpen, intArrayOf(temLimitRange.first, temLimitRange.second))
                vb.ttarvPb2TemWarning4H5112Setting.setTemRange(fahOpen, intArrayOf(temLimitRange.first, temLimitRange.second))
                DelayAlarmView(this).let {
                    delayAlarmView4Pb1 = it
                    it.updateValue(bindExt.delayPushTime)
                    vb.ttarvPb1TemWarning4H5112Setting.addMidLayout(it)
                }
                DelayAlarmView(this).let {
                    delayAlarmView4Pb2 = it
                    it.updateValue(bindExt.delayPushTime2 ?: 1)
                    vb.ttarvPb2TemWarning4H5112Setting.addMidLayout(it)
                }
                //初始化湿度告警相关信息
                vb.tharvPb1HumWarning4H5112Setting.setHumRange(intArrayOf(ThConsV1.HUM_MIN_VALUE, ThConsV1.HUM_MAX_VALUE))
                vb.tharvPb1HumWarning4H5112Setting.setBackgroundInfo(false, true)
                vb.tharvPb1HumWarning4H5112Setting.needItemIcon(true)
                //初始显示校准相关信息(pb1+pb2)
                vb.tcvPb1Cali4H5112Setting.setDeviceInfo(bindExt.sku)
                vb.tcvPb2Cali4H5112Setting.setDeviceInfo(bindExt.sku)
                vb.tcvPb1Cali4H5112Setting.setUiInfo(true, false, supportLowBat.first)
                vb.tcvPb2Cali4H5112Setting.setUiInfo(true, false, supportLowBat.first)
                vb.tcvPb1Cali4H5112Setting.noHumInfo(false)
                vb.tcvPb2Cali4H5112Setting.noHumInfo(true)
                ThConfig4Setting.getCalibrationRange(bindExt.goodsType, bindExt.sku).run {
                    val temCaliRange = if (fahOpen) {
                        //不能用摄氏度直接转化，因为要保留小数，而我们的工具转化类都是已取整
                        floatArrayOf(second.first, second.second)
                    } else {
                        floatArrayOf(first.first, first.second)
                    }
                    val humCaliRange = floatArrayOf(third.first, third.second)
                    vb.tcvPb1Cali4H5112Setting.setRange(fahOpen, temCaliRange, humCaliRange)
                    vb.tcvPb2Cali4H5112Setting.setRange(fahOpen, temCaliRange, humCaliRange)
                }
            }
        }
        if (!hasBleConnected()) {
            //ble若未连接则更新一遍相关操作值，若已连接则会在相应的LiveData中更新
            updateDeviceInfo("initUi")
        }
        //初始化显示
        updateViewDisplayByConnection()
        //其他控件相关显示
        otherViewSetting()
    }

    /**
     * 将配件shop的view组件插入到指定view的下方，这里是插入到电池ui-item的下方
     */
    private fun otherViewSetting() {
        getDeviceInfo()?.let { bindExt ->
            attachSkuShopAccessory(
                this::class.java.name,
                bindExt.sku,
                viewBinding.sivShowItem4H5112Setting,
                this
            )
        }
    }

    /**
     * 更新设备信息展示
     */
    private fun updateDeviceInfo(from: String) {
        updateWarnInfo(from)
        updateCalibrationInfo()
        updateOtherInfo()
    }

    private fun initOpClick() {
        viewBinding.let { vb ->
            vb.ivBackBtn4H5112Setting.clickDelay {
                onBackPressed()
            }
            vb.acContainer.clickDelay {
                vb.vdn4Rename4H5112Setting.hideInputMethod()
            }
            vb.clContentContainer4H5112Setting.clickDelay {
                vb.vdn4Rename4H5112Setting.hideInputMethod()
            }
            vb.vdn4Rename4H5112Setting.saveNewDeviceNameCallback = { newName ->
                if (AccountConfig.read().isHadToken && !NetUtil.isNetworkAvailable(this)) {
                    toast(R.string.network_anomaly)
                } else {
                    Vm4ThOpManager.instance()?.settingOpManager?.changeDeviceName(newName)
                }
            }
            //切换低电量开关
            vb.lbpvLowBattery.setOnSwitchListener(object : LowBatPushView.OnSwitchListener {
                override fun onSelected(isOpen: Boolean) {
                    getDeviceInfo()?.let { bindExt ->
                        val supportLowBat = ThConfig4Setting.supportLowBatAlarm(bindExt.goodsType, bindExt.sku)
                        //未登录的情况下，若支持低电量告警,但仅有服务端而无设备本地，则需提示登录
                        if ((!AccountConfig.read().isHadToken) && supportLowBat.first && (!supportLowBat.second)) {
                            ConfirmDialogV3.showConfirmDialog(
                                this@Ac4H5112Setting,
                                R.string.login_first_label,
                                R.string.cancel,
                                R.string.to_login_now
                            ) {
                                LoginActivity.jump2LoginAc(this@Ac4H5112Setting, "", false)
                            }
                            return
                        }
                    }
                    Vm4ThOpManager.instance()?.settingOpManager?.updateLowBatAlarm(isOpen) {
                        if (it) {
                            vb.lbpvLowBattery.setSwitch(isOpen)
                        }
                    }
                }
            })
            //音量档位切换
            volumeLeveView?.setSelectVolumeLevelListener(object :
                VolumeLeveView.OnSelectVolumeListener {
                override fun onSelectVolumeLevel(selectVolumeGear: Int) {
                    Vm4ThOpManager.instance()?.settingOpManager?.setVolumeLevel(selectVolumeGear) {
                        if (it) {
                            volumeLeveView?.updateValue(selectVolumeGear)
                            showCloseVolumeRemind(selectVolumeGear)
                        }
                    }
                }
            })
            //温度报警相关设置
            //探针1
            vb.ttarvPb1TemWarning4H5112Setting.setListener { minAlarmTem: Int, maxAlarmTem: Int, alarmOn: Boolean ->
                val minTem100: Int
                val maxTem100: Int
                if (fahOpen) {
                    minTem100 = NumberUtil.temRangeFah2Cel4Setting(minAlarmTem)
                    maxTem100 = NumberUtil.temRangeFah2Cel4Setting(maxAlarmTem)
                } else {
                    minTem100 = minAlarmTem * 100
                    maxTem100 = maxAlarmTem * 100
                }
                Vm4ThOpManager.instance()?.settingOpManager?.updateTemWarningInfo4H5112(minTem100, maxTem100, alarmOn, Constant4L5.PROBE_INDEX_4_TEM_HUM)
            }
            delayAlarmView4Pb1?.setOperationListener { mins: Int ->
                Vm4ThOpManager.instance()?.settingOpManager?.updateDelayPushTime4H5112(mins, Constant4L5.PROBE_INDEX_4_TEM_HUM)
            }
            vb.tcvPb1Cali4H5112Setting.setIntroClickListener {
                calibrationIntroDialog.show()
            }
            //探针2
            vb.ttarvPb2TemWarning4H5112Setting.setListener { minAlarmTem: Int, maxAlarmTem: Int, alarmOn: Boolean ->
                val minTem100: Int
                val maxTem100: Int
                if (fahOpen) {
                    minTem100 = NumberUtil.temRangeFah2Cel4Setting(minAlarmTem)
                    maxTem100 = NumberUtil.temRangeFah2Cel4Setting(maxAlarmTem)
                } else {
                    minTem100 = minAlarmTem * 100
                    maxTem100 = maxAlarmTem * 100
                }
                Vm4ThOpManager.instance()?.settingOpManager?.updateTemWarningInfo4H5112(minTem100, maxTem100, alarmOn, Constant4L5.PROBE_INDEX_4_TEM)
            }
            delayAlarmView4Pb2?.setOperationListener { mins: Int ->
                Vm4ThOpManager.instance()?.settingOpManager?.updateDelayPushTime4H5112(mins, Constant4L5.PROBE_INDEX_4_TEM)
            }
            vb.tcvPb2Cali4H5112Setting.setIntroClickListener {
                vb.tcvPb1Cali4H5112Setting.isEnabled
                calibrationIntroDialog.show()
            }
            //湿度报警相关设置
            vb.tharvPb1HumWarning4H5112Setting.setListener { minAlarmHum: Int, maxAlarmHum: Int, alarmOn: Boolean ->
                Vm4ThOpManager.instance()?.settingOpManager?.updateHumWarningInfo4H5112(minAlarmHum * 100, maxAlarmHum * 100, alarmOn)
            }
            //校准值相关设置
            //探针1
            vb.tcvPb1Cali4H5112Setting.setListener(object :
                THCalibrationView.THCalibrationListener {
                override fun onTemCali(fahOpen: Boolean, temCali: Float) {
                    val temCali100 = NumberUtil.getValidTemCali(temCali, fahOpen)
                    Vm4ThOpManager.instance()?.settingOpManager?.updateTemCali4H5112(temCali100, Constant4L5.PROBE_INDEX_4_TEM_HUM)
                }

                override fun onHumCali(humCali: Float) {
                    val humCali100 = NumberUtil.toIntValueBy100(humCali)
                    Vm4ThOpManager.instance()?.settingOpManager?.updateHumCali4H5112(humCali100)
                }
            })
            //探针2
            vb.tcvPb2Cali4H5112Setting.setListener(object :
                THCalibrationView.THCalibrationListener {
                override fun onTemCali(fahOpen: Boolean, temCali: Float) {
                    val temCali100 = NumberUtil.getValidTemCali(temCali, fahOpen)
                    Vm4ThOpManager.instance()?.settingOpManager?.updateTemCali4H5112(temCali100, Constant4L5.PROBE_INDEX_4_TEM)
                }

                //探针2无湿度相关设置
                override fun onHumCali(humCali: Float) {}
            })
            //重新连接设备(蓝牙连接)
            vb.tvReconnectBtn4H5112Setting.clickDelay {
                if (!BleController.getInstance().isBlueToothOpen) {
                    toast(ResUtil.getString(R.string.bluetooth_unable_des))
                    return@clickDelay
                }
                Vm4ThOpManager.instance()?.toConnectBle()
            }
            //删除设备
            vb.tvDeleteBtn4H5112Setting.clickDelay {
                //弹出确认弹窗
                ConfirmDialog.showConfirmDialog(
                    this,
                    ResUtil.getString(R.string.dialog_unbind_label),
                    ResUtil.getString(R.string.no),
                    ResUtil.getString(R.string.yes),
                    false
                ) {
                    //执行删除
                    Vm4ThOpManager.instance()?.settingOpManager?.deleteDevice { deleteResult ->
                        getDeviceInfo()?.let {
                            if (deleteResult) {
                                //断开蓝牙
                                BleController.getInstance().toBtClose()
                                //需判断是从网关页面跳转过来还是从设备列表跳转过来，新需求(暂时为H5044的子设备)要求从哪来，回哪去
                                //网关列表页存在,则说明是从网关页面跳转过来的
                                val fromGwListPage = BaseApplication.getBaseApplication().hadActivity(Base2homeConfig.getConfig().gwListAc.name)
                                val hasGw = when (Vm4ThOpManager.instance()?.getGateInfo()?.sku) {
                                    Constant4L5.H5044 -> {
                                        true
                                    }

                                    else -> {
                                        false
                                    }
                                }
                                if (fromGwListPage && hasGw) {
                                    //向外通知事件(关闭详情页、刷新网关子设备列表页)
                                    Event4DeleteThFromGw.sendEvent()
                                    mHandler.postDelayed(object : CaughtRunnable() {
                                        override fun runSafe() {
                                            hideLoading()
                                            onBackPressed()
                                        }
                                    }, 100)
                                } else {
                                    //回退到主界面
                                    EventTabDefault.sendEventTabDefault()
                                    val bundle = Bundle().apply {
                                        putString(Constant.intent_ac_key_sku, it.sku)
                                        putString(Constant.intent_ac_key_unbind, it.device)
                                    }
                                    JumpUtil.jumpWithBundle(
                                        this,
                                        Base2homeConfig.getConfig().mainAcClass,
                                        true,
                                        bundle
                                    )
                                    hideLoading()
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun initObserver() {
        Vm4ThOpManager.instance()?.let { vm ->
            vm.loadingChange.showDialog.observeInActivity(this) {
                showLoading()
            }
            vm.loadingChange.dismissDialog.observeInActivity(this) {
                hideLoading()
            }
            vm.ld4ChangeGateway.observe(this) {
                updateGuideInfo()
                getGateInfo()?.let { gwInfo ->
                    viewBinding.ttarvPb1TemWarning4H5112Setting.initGwDeviceInfo(0, gwInfo.sku, gwInfo.versionSoft, gwInfo.versionHard)
                    viewBinding.ttarvPb2TemWarning4H5112Setting.initGwDeviceInfo(0, gwInfo.sku, gwInfo.versionSoft, gwInfo.versionHard)
                }
                //相关视图刷新
                updateDeviceInfo("after bind gateway...")
                updateViewDisplayByConnection()
            }
            vm.ld4ConnectStatusChange.observe(this) {
                viewBinding.rsvRemoteService.setPureGwOnline(it.second.second)
                updateBleConnectStatus(it.first)
                //相关视图刷新
                updateViewDisplayByConnection()
            }
            vm.ld4UpgradeVersion.observe(this) {
                upgradeInfoChange(it)
            }
            //导航信息展示
            vm.settingOpManager.ld4guides.observe(this) { guides ->
                viewBinding.let { vb ->
                    vb.vspGuideContainer4H5112Setting.changeOp(SettingOp.tag_guide) {
                        this.guides = guides
                        true
                    }
                    vb.vGuideDividerLine4H5112Setting.setVisibility(!guides.isNullOrEmpty())
                    vb.vspGuideContainer4H5112Setting.setVisibility(!guides.isNullOrEmpty())
                }
            }
            //更新设备名
            vm.settingOpManager.ld4UpdateDeviceName.observe(this) {
                getDeviceInfo()?.let { bindExt ->
                    if (bindExt.getKey() == it.first) {
                        viewBinding.vdn4Rename4H5112Setting.updateDeviceName(it.second)
                    }
                }
            }
            //更新实时数据显示
            vm.ld4RealTemHum.observe(this) {
                updateCalibrationInfo(it)
            }
            //相关设置操作的回调更新
            vm.ld4UpdateDeviceInfo.observe(this) { updatePair ->
                if (!hasCreated) {
                    return@observe
                }
                when (updatePair.second) {
                    Vm4ThOpManager.UPDATE_4_WARNING -> {
                        updateWarnInfo("ld4UpdateDeviceInfo")
                    }

                    Vm4ThOpManager.UPDATE_4_DELAY_ALARM -> {
                        getDeviceInfo()?.let { bindExt ->
                            delayAlarmView4Pb1?.updateValue(bindExt.delayPushTime)
                            delayAlarmView4Pb2?.updateValue(bindExt.delayPushTime2 ?: 1)
                        }
                    }

                    Vm4ThOpManager.UPDATE_4_CALIBRATION -> {
                        updateCalibrationInfo()
                    }

                    else -> {}
                }
            }
            //加载全部数据结束
            vm.loadThcdManager.ld4LoadThCdStep.observe(this) {
                getDeviceInfo()?.let { bindExt ->
                    it[Pair(bindExt.sku, bindExt.device)]?.let { thCdInfo ->
                        if (thCdInfo.second.second == Vm4ThOpManager.THCD_4_IS_LOAD_FINISH) {
                            if (thCdInfo.second.first == Vm4ThOpManager.RC_FROM_SETTING_LOAD_ALL) {
                                loadAtdDialog.finish(thCdInfo.second.third?.first.isTrue())
                                mHandler.postDelayed(object : CaughtRunnable() {
                                    override fun runSafe() {
                                        setThDataCacheMemory()
                                        loadAtdDialog.hide()
                                    }
                                }, ClearThDataUtils.REFRESH_TH_WAIT_TIME)
                            } else {
                                setThDataCacheMemory()
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 蓝牙状态变化
     */
    private fun updateBleConnectStatus(bleConnectStatus: Int) {
        when (bleConnectStatus) {
            //蓝牙未打开
            BleOpManager.BLE_UNABLE -> {
                if (!isBindGateway()) {
                    viewBinding.tvBleDisconnectRemind4H5112Setting.setVisibility(true)
                    viewBinding.tvReconnectBtn4H5112Setting.setVisibility(false)
                    viewBinding.pbReconectLoading4H5112Setting.setVisibility(false)
                } else {
                    updateCalibrationInfo()
                }
            }
            //断开
            BleOpManager.BLE_DISCONNECT -> {
                if (!isBindGateway()) {
                    viewBinding.tvBleDisconnectRemind4H5112Setting.setVisibility(true)
                    viewBinding.tvReconnectBtn4H5112Setting.setVisibility(true)
                    viewBinding.pbReconectLoading4H5112Setting.setVisibility(false)
                } else {
                    updateCalibrationInfo()
                }
            }
            //连接中
            BleOpManager.BLE_CONNECTING -> {
                if (!isBindGateway()) {
                    viewBinding.tvBleDisconnectRemind4H5112Setting.visibility = View.INVISIBLE
                    viewBinding.tvReconnectBtn4H5112Setting.visibility = View.INVISIBLE
                    viewBinding.pbReconectLoading4H5112Setting.setVisibility(true)
                }
            }
            //连接成功
            BleOpManager.BLE_CONNECTED_SUC -> {
                viewBinding.tvBleDisconnectRemind4H5112Setting.visibility = View.GONE
                viewBinding.tvReconnectBtn4H5112Setting.visibility = View.GONE
                viewBinding.pbReconectLoading4H5112Setting.setVisibility(false)
                updateDeviceInfo("BLE_CONNECTED_SUC")
            }

            //读取完设备信息
            BleOpManager.BLE_READ_INFO_FINISH -> {
                viewBinding.tvBleDisconnectRemind4H5112Setting.visibility = View.GONE
                viewBinding.tvReconnectBtn4H5112Setting.visibility = View.GONE
                viewBinding.pbReconectLoading4H5112Setting.setVisibility(false)
                updateBleVersionLinkage()
                updateDeviceInfo("BLE_READ_INFO_FINISH")
                //更新版本号显示
                viewBinding.let { vb ->
                    getDeviceInfo()?.let { bindExt ->
                        vb.vspDevice4H5112Setting.changeOp(SettingOp.tag_soft_version) {
                            showingStr = bindExt.bleSoftVersion
                            true
                        }
                        vb.vspDevice4H5112Setting.changeOp(SettingOp.tag_hard_version) {
                            showingStr = bindExt.bleHardVersion
                            true
                        }
                        //赋值版本号
                        lastBleVersion = Pair(bindExt.bleSoftVersion, bindExt.bleHardVersion)
                    }
                }
            }
        }
        volumeLeveView?.canOperate(hasBleConnected(), getGateInfo()?.isIotOnLine.isTrue(), true)
    }

    /**
     * 升级相关变化
     */
    private fun upgradeInfoChange(upgradeInfo: Pair<Int, CheckVersion?>) {
        when (upgradeInfo.first) {
            Vm4ThOpManager.UPGRADE_FIRMWARE_4_FIRST_REMIND -> {
                getDeviceInfo()?.let { bindExt ->
                    BleUpdateHintDialog.showDialog(this, bindExt.sku, {
                        toUpgradePage()
                    }, this.javaClass.name)
                }
                //再执行红点
                Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value =
                    Pair(Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND, upgradeInfo.second)
            }

            Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND -> {
                viewBinding.vspDevice4H5112Setting.changeOp(SettingOp.tag_soft_version) {
                    this.canUpdateCheck(true)
                }
            }

            Vm4ThOpManager.UPGRADE_FIRMWARE_4_FINISH -> {
                //升级成功后，会断开连接，故须重连
                Vm4ThOpManager.instance()?.toConnectBle()
                //隐藏升级提示
                viewBinding.vspDevice4H5112Setting.changeOp(SettingOp.tag_soft_version) {
                    this.canUpdateCheck(false)
                }
            }

            else -> {
                viewBinding.vspDevice4H5112Setting.changeOp(SettingOp.tag_soft_version) {
                    getDeviceInfo()?.let { bindExt ->
                        this.showingStrCheck(bindExt.bleSoftVersion)
                    }
                    this.canUpdateCheck(false)
                }
            }
        }
    }

    /**
     * 若版本号发生变化，更新相关关联业务
     */
    private fun updateBleVersionLinkage() {
        lastBleVersion?.let {
            getDeviceInfo()?.let { bindExt ->
                //判断版本号是否有变动
                if (it.first != bindExt.bleSoftVersion || it.second != bindExt.bleHardVersion) {
                    viewBinding.let { vb ->
                        //远程服务的支持
                        val supportRemoteService = Constant4L5.supportRemoteService(bindExt.goodsType, bindExt.sku)
                        vb.rsvRemoteService.getBindInfo(
                            supportRemoteService,
                            bindExt.sku,
                            bindExt.device,
                            bindExt.deviceName,
                            bindExt.bleHardVersion,
                            bindExt.bleSoftVersion
                        )
                    }
                }
            }
        }
    }

    /**
     * 更新导航信息展示
     */
    private fun updateGuideInfo() {
        //未登录则无须处理
        if (!AccountConfig.read().isHadToken) {
            return
        }
        if (isBindGateway()) {
            Vm4ThOpManager.instance()?.settingOpManager?.queryGuide()
        }
    }

    /**
     * 更新告警相关信息
     */
    private fun updateWarnInfo(from: String) {
        SafeLog.i("xiaobing") { "Ac4H5112Setting--updateWarnInfo-->更新来源=->${from}" }
        getDeviceInfo()?.let { bindExt ->
            viewBinding.let { vb ->
                //更新温度预警
                //探针1
                val temAlarmRange4Pb1 = IntArray(2)
                if (fahOpen) {
                    temAlarmRange4Pb1[0] = ThUtil.temRangeCel2Fah4Showing(bindExt.temMin)
                    temAlarmRange4Pb1[1] = ThUtil.temRangeCel2Fah4Showing(bindExt.temMax)
                } else {
                    temAlarmRange4Pb1[0] = ThUtil.temRangeCel2Cel4Showing(bindExt.temMin)
                    temAlarmRange4Pb1[1] = ThUtil.temRangeCel2Cel4Showing(bindExt.temMax)
                }
                vb.ttarvPb1TemWarning4H5112Setting.updateTemAlarm(temAlarmRange4Pb1, bindExt.temWarning)
                //探针2
                val temAlarmRange4Pb2 = IntArray(2)
                if (fahOpen) {
                    temAlarmRange4Pb2[0] = ThUtil.temRangeCel2Fah4Showing(bindExt.temMin2 ?: 0)
                    temAlarmRange4Pb2[1] = ThUtil.temRangeCel2Fah4Showing(bindExt.temMax2 ?: 0)
                } else {
                    temAlarmRange4Pb2[0] = ThUtil.temRangeCel2Cel4Showing(bindExt.temMin2 ?: 0)
                    temAlarmRange4Pb2[1] = ThUtil.temRangeCel2Cel4Showing(bindExt.temMax2 ?: 0)
                }
                vb.ttarvPb2TemWarning4H5112Setting.updateTemAlarm(temAlarmRange4Pb2, bindExt.temWarning2.isTrue())
                //更新湿度预警
                val humAlarmRange = intArrayOf(
                    NumberUtil.getValueUpward(bindExt.humMin, true),
                    NumberUtil.getValueUpward(bindExt.humMax, false)
                )
                vb.tharvPb1HumWarning4H5112Setting.updateHumAlram(humAlarmRange, bindExt.humWarning)
            }
        }
    }

    /**
     * 更新校准相关信息
     */
    private fun updateCalibrationInfo(realInfo: Pair<Triple<Int, Int, Int>, Long>? = null) {
        getDeviceInfo()?.let { bindExt ->
            val showRealInfo = realInfo
                ?: Vm4ThOpManager.instance()?.ld4RealTemHum?.value
                ?: Config4LastThValue.getConfig().getLastTh(bindExt.sku, bindExt.device)?.let {
                    Pair(Triple(it.tem, it.hum, it.pm), it.lastTime)
                }
            showRealInfo?.let { info ->
                val canOperate = hasBleConnected() || isBindGateway()
                val temCali4Pb1 = if (canOperate) NumberUtil.getValidTemCali(bindExt.temCali, fahOpen) else 0.0f
                val humCali = if (canOperate) NumberUtil.toFloatValueBy100(bindExt.humCali) else 0.0f
                val temCali4Pb2 = if (canOperate) NumberUtil.getValidTemCali(bindExt.temCali2 ?: 0, fahOpen) else 0.0f
                val thInfo = info.first
                val realTem4Pb1 = if (canOperate) NumberUtil.getTemValue(fahOpen, thInfo.first, 0) else 0.0f
                val realHum = if (canOperate) NumberUtil.toFloatValueBy100(thInfo.second) else 0.0f
                val realTem4Pb2 = if (canOperate) NumberUtil.getTemValue(fahOpen, thInfo.third, 0) else 0.0f
                viewBinding.let { vb ->
                    //探针1
                    vb.tcvPb1Cali4H5112Setting.updateCalibration(
                        realTem4Pb1,
                        temCali4Pb1,
                        realHum,
                        humCali,
                        canOperate && ThConsV1.isValidThValue(thInfo.first, thInfo.second)
                    )
                    vb.tcvPb1Cali4H5112Setting.updateUiByBleConnected(hasBleConnected())
                    //探针2
                    vb.tcvPb2Cali4H5112Setting.updateCalibration(
                        realTem4Pb2,
                        temCali4Pb2,
                        60.0f,
                        0.0f,
                        canOperate && ThConsV1.isValidThValue(thInfo.third, 6000)
                    )
                    vb.tcvPb2Cali4H5112Setting.updateUiByBleConnected(hasBleConnected())
                }
            }
        }
    }

    /**
     * 其他信息
     * 如：音量、低电量等
     */
    private fun updateOtherInfo() {
        getDeviceInfo()?.let {
            //音量
            ThConfig4Setting.supportVolumeLevel(it.goodsType, it.sku).let { supportVolumeLevel ->
                if (supportVolumeLevel) {
                    volumeLeveView?.updateValue(it.muteLevel)
                    it.muteLevel?.let { volumeGear ->
                        showCloseVolumeRemind(volumeGear)
                    }
                }
            }
            //低电量报警
            ThConfig4Setting.supportLowBatAlarm(it.goodsType, it.sku).run {
                if (this.first || this.second) {
                    viewBinding.lbpvLowBattery.setSwitch(it.batteryWarning)
                }
            }
        }
    }

    /**
     * 显示关闭设备声音报警的弹窗
     */
    private fun showCloseVolumeRemind(volumeGear: Int) {
        getDeviceInfo()?.let { bindExt ->
            val cacheKey = SmartCacheKey.appGlobal("${AccountConfig.read().accountId}_${bindExt.sku}_${Dialog4CloseVolumeRemind::class.java.name}")
            val hasShowed = GlobalCache.build().getBoolean(cacheKey).isTrue()
            val hasVolume = volumeGear > 0
            if ((!hasShowed) && hasVolume) {
                GlobalCache.build().set(cacheKey, true)
                closeVolumeRemindDialog.showDialog(bindExt.sku)
            }
        }
    }

    /**
     * 根据有无绑定网关、蓝牙连接状态来更新告警、校准等相关控件的显/隐
     */
    private fun updateViewDisplayByConnection() {
        //告警、校准相关控件的显/隐
        val showViews = if (isBindGateway()) {
            //绑定了网关则都可以操作
            true
        } else {
            //蓝牙连接的情况下，才可操作
            hasBleConnected()
        }
        viewBinding.let {
            //探针1
            it.vPb1ProductSettingBg4H5112Setting.setVisibility(showViews)
            it.tvPb1NameText4H5112Setting.setVisibility(showViews)
            it.ttarvPb1TemWarning4H5112Setting.setVisibility(showViews)
            it.tharvPb1HumWarning4H5112Setting.setVisibility(showViews)
            it.tcvPb1Cali4H5112Setting.setVisibility(showViews)
            //探针2
            it.vPb2ProductSettingBg4H5112Setting.setVisibility(showViews)
            it.tvPb2NameText4H5112Setting.setVisibility(showViews)
            it.ttarvPb2TemWarning4H5112Setting.setVisibility(showViews)
            it.tcvPb2Cali4H5112Setting.setVisibility(showViews)
        }
        //item的显/隐
        viewBinding.vspDevice4H5112Setting.setItemList(mutableListOf<SettingOp>().apply {
            addAll(items4ShowAllTheTime)
            //能绑定网关的设备，其自身是不支持wifi功能的，故无wifi相关item
            if (isBindGateway()) {
                addAll(0, items4NeedBle)
            } else {
                //蓝牙连接的情况下，展示须要蓝牙操作的item
                if (hasBleConnected()) {
                    addAll(0, items4NeedBle)
                }
            }
        })
        setThDataCacheMemory()
    }

    /**
     * 拉取全部数据
     */
    private fun loadAllThData() {
        getDeviceInfo()?.let { bindExt ->
            Vm4ThOpManager.instance()?.loadThcdManager?.ld4LoadThCdStep?.value?.get(Pair(bindExt.sku, bindExt.device))?.let {
                if (it.second.second != Vm4ThOpManager.THCD_4_IS_LOAD_FINISH) {
                    toast(ResUtil.getString(R.string.fresh_des_loading))
                    return
                }
            }
            loadAtdDialog.show()
            Event4LoadAllData.sendEvent(Event4LoadAllData.LOAD_TH_DATA_TO_RESET_TIME, false)
        }
    }

    /**
     * 删除全部数据的确认弹窗
     */
    private fun showDeleteAllDataConfirmDialog() {
        ConfirmDialog.showConfirmDialog(
            this,
            ResUtil.getString(R.string.hint_4_delete_device_data),
            ResUtil.getString(R.string.no),
            ResUtil.getString(R.string.yes)
        ) {
            Vm4ThOpManager.instance()?.clearAllData {}
        }
    }

    /**
     * 设置图表数据占用存储大小
     */
    private fun setThDataCacheMemory() {
        getDeviceInfo()?.let { bindExt ->
            globalLaunch(Dispatchers.IO) {
                val cacheMemoryStr = ClearThDataUtils.getThpDataCacheMemory(bindExt.sku, bindExt.device)
                withContext(Dispatchers.Main) {
                    viewBinding.vspDevice4H5112Setting.changeOp(ThSettingOpItems.TAG_4_TH_DELETE_DATA) {
                        desStr = cacheMemoryStr
                        true
                    }
                }
            }
        }
    }

    /**
     * 跳转至升级页面
     */
    private fun toUpgradePage() {
        getDeviceInfo()?.let { bindExt ->
            Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value?.let { upgradePair ->
                upgradePair.second?.run {
                    Ac4UpdateByFrk.jump2OtaUpdate(this@Ac4H5112Setting, bindExt.sku, bindExt.deviceName, this)
                }
            }
        }
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.thnew.R.id.ac_container
    }

    override fun adapterInsetViewId(): Int {
        return com.govee.thnew.R.id.sp_top_adjust_holder
    }

    override fun layoutId(): Int {
        return com.govee.thnew.R.layout.thnew_ac_4_h5112_setting
    }

    override fun sku(): String {
        return getDeviceInfo()?.sku ?: ""
    }

    override fun parseIntent(intent: Intent?) {}

    @Suppress("DEPRECATION")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        viewBinding.twsvWarnSettingNew4H5112Setting.onActivityResult(requestCode, resultCode, data)
    }

    override fun onDestroy() {
        mHandler.removeCallbacksAndMessages(null)
        super.onDestroy()
    }
}