package com.govee.thnew.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2025/5/16
 * @description 温湿度计-->读写探针icon的事件
 * 备注：H5112使用
 */
class Event4ProbeIcon private constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON>olean, commandType: Byte, proType: Byte) : AbsControllerEvent(result, write, commandType, proType, !write) {

    /**
     * 探针1的icon的序号代码
     */
    var pb1IconIndex = 1

    /**
     * 探针2的icon的序号代码
     */
    var pb2IconIndex = 1

    companion object {
        fun sendSuc4Read(write: Boolean, commandType: Byte, proType: Byte, pb1IconIndex: Int, pb2IconIndex: Int) {
            val event = Event4ProbeIcon(true, write, commandType, proType)
            event.pb1IconIndex = pb1IconIndex
            event.pb2IconIndex = pb2IconIndex
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(Event4ProbeIcon(false, write, commandType, proType))
        }
    }
}