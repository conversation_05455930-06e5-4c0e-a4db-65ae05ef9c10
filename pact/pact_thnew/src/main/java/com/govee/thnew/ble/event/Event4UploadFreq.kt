package com.govee.thnew.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->读写设备上传温湿度数据频率的事件
 */
class Event4UploadFreq private constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON>olean, commandType: Byte, proType: Byte) : AbsControllerEvent(result, write, commandType, proType, !write) {

    var minutes = 0

    companion object {
        fun sendFail(write: <PERSON>olean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(Event4UploadFreq(false, write, commandType, proType))
        }

        fun sendSuc4Read(write: Boolean, commandType: Byte, proType: Byte, hour: Int, minute: Int) {
            val event = Event4UploadFreq(true, write, commandType, proType)
            event.minutes = hour * 60 + minute
            EventBus.getDefault().post(event)
        }
    }
}