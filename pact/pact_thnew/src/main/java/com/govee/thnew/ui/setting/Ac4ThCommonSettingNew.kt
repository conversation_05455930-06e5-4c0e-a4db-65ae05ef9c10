package com.govee.thnew.ui.setting

import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.View
import com.govee.base2home.Constant
import com.govee.base2home.Constant4L5
import com.govee.base2home.account.LoginActivity
import com.govee.base2home.config.Base2homeConfig
import com.govee.base2home.device.attachSkuShopAccessory
import com.govee.base2home.main.tab.EventTabDefault
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.update.download.CheckVersion
import com.govee.base2home.util.NumberUtil
import com.govee.base2home.util.TemUtil
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.setVisibility
import com.govee.base2kt.ext.toast
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.ThUtil
import com.govee.base2newth.deviceitem.GatewayInfo
import com.govee.base2newth.other.ClearThDataUtils
import com.govee.base2newth.other.Config4LastThValue
import com.govee.base2newth.other.DelayAlarmView
import com.govee.base2newth.other.DeviceWarningTimeView
import com.govee.base2newth.other.Dialog4CloseVolumeRemind
import com.govee.base2newth.other.Dialog4LoadAtd
import com.govee.base2newth.other.Event4DeleteThFromGw
import com.govee.base2newth.other.Event4LoadAllData
import com.govee.base2newth.other.LowBatPushView
import com.govee.base2newth.other.VolumeLeveView
import com.govee.ble.BleController
import com.govee.cache.GlobalCache
import com.govee.cache.key.SmartCacheKey
import com.govee.home.account.config.AccountConfig
import com.govee.kt.setting.AbsAc4Setting
import com.govee.kt.ui.view.SettingOp
import com.govee.mvvm.globalLaunch
import com.govee.thnew.add.Ac4ThWifiSetting
import com.govee.thnew.add.AddInfo
import com.govee.thnew.config.ThConfig4Detail
import com.govee.thnew.config.ThConfig4Setting
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.databinding.ThnewAc4SettingNewBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.op.BleOpManager
import com.govee.thnew.update.ota4frk.Ac4UpdateByFrk
import com.govee.thnew.update.ota4v1.Ac4UpdateV1
import com.govee.thnew.update.ota4v2.Ac4UpdateV2
import com.govee.ui.R
import com.govee.ui.component.DeviceLockView
import com.govee.ui.component.THCalibrationView
import com.govee.ui.dialog.BleUpdateHintDialog
import com.govee.ui.dialog.ConfirmDialog
import com.govee.ui.dialog.ConfirmDialogV3
import com.govee.ui.dialog.Dialog4ClearThDataSelPeriod
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.CaughtRunnable
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.NetUtil
import com.ihoment.base2app.util.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @date created on 2024/2/22
 * @description 温湿度计-->通用的设置页
 */
class Ac4ThCommonSettingNew : AbsAc4Setting<ThnewAc4SettingNewBinding>() {

    private val fahOpen by lazy {
        getDeviceInfo()?.let {
            TemUnitConfig.read().isTemUnitFah(it.sku)
        } != false
    }

    private var originalBindExt: AddInfo? = null

    /**
     * 加载全部数据中的loading弹窗
     */
    private val loadAtdDialog by lazy {
        Dialog4LoadAtd.createDialog(this)
    }

    /**
     * 清理数据时，选择保留时间段
     */
    private val selPeriodDialog by lazy {
        Dialog4ClearThDataSelPeriod.createDialog(this, getDeviceInfo()?.sku ?: "")
    }

    /**
     * 延时告警控件
     */
    private var delayAlarmView: DelayAlarmView? = null

    /**
     * 音量调节控件
     */
    private var volumeLeveView: VolumeLeveView? = null
    private val closeVolumeRemindDialog by lazy {
        Dialog4CloseVolumeRemind.createDialog(this)
    }

    /**
     * 设备声音告警控件
     */
    private var deviceWarningTimeView: DeviceWarningTimeView? = null

    /**
     * 上一次的版本号
     * 备注：升级等情况下，用来判断版本号是否发生变化，若版本号发生变化跟版本号相关联的也须改动
     *      first->bleSv,second->bleHv
     */
    private var lastBleVersion: Pair<String, String>? = null

    private val mHandler by lazy {
        Handler(Looper.getMainLooper())
    }

    override fun initAfterOnCreate() {
        initData()
        initUi()
        initOpClick()
        initObserver()
    }

    override fun onResume() {
        super.onResume()
        //蓝牙打开的情况下，若蓝牙断连则去主动连接一次
        if (BleController.getInstance().isBlueToothOpen && !BleController.getInstance().isConnected) {
            Vm4ThOpManager.instance()?.toConnectBle()
        }
        getDeviceInfo()?.let { bindExt ->
            //分布式网关联动的支持
            //显、隐分布式网关相关功能视图
            val supportRemoteService = Constant4L5.supportRemoteService(bindExt.goodsType, bindExt.sku)
            viewBinding.rsvRemoteService.getBindInfo(
                supportRemoteService,
                bindExt.sku,
                bindExt.device,
                bindExt.deviceName,
                bindExt.bleHardVersion,
                bindExt.bleSoftVersion
            )
            viewBinding.drvOperatorOld4ThSetting.getBindInfo(
                !supportRemoteService,
                bindExt.sku,
                bindExt.device,
                bindExt.deviceName,
                bindExt.bleHardVersion,
                bindExt.bleSoftVersion
            )
            //更新通知权限
            ThConfig4Setting.supportWarnSetting(bindExt.goodsType, bindExt.sku).let {
                if (it == ThConfig4Setting.TH_WARN_TYPE_4_NEW) {
                    viewBinding.twsvWarnSettingNew4ThSetting.onResume()
                }
            }
        }
    }

    /**
     * 该设备自身的实时信息
     */
    private fun getDeviceInfo(): AddInfo? {
        Vm4ThOpManager.instance()?.getDeviceInfo()?.let {
            return it
        }
        //这种情况直接退回到设备列表页(长时间置于后台，被销毁后，回到前台重建)
        BaseApplication.getBaseApplication().finishOther(Base2homeConfig.getConfig().mainAcClass)
        return null
    }

    /**
     * 该设备绑定的网关的实时信息
     */
    private fun getGateInfo(): GatewayInfo? {
        return Vm4ThOpManager.instance()?.getGateInfo()
    }

    /**
     * 该设备是否已经连接蓝牙的实时状态
     */
    private fun hasBleConnected(): Boolean {
        return Vm4ThOpManager.instance()?.hasBleConnected().isTrue()
    }

    /**
     * 绑定了网关，以网关为准
     */
    private fun isBindGateway(): Boolean {
        return Vm4ThOpManager.instance()?.isBindGateway().isTrue()
    }

    private val items4ShowAllTheTime = arrayListOf<SettingOp>().apply {
        getDeviceInfo()?.let { bindExt ->
            add(SettingOp.makeItemTypeShowing4Sku(bindExt.sku))
            add(
                SettingOp.makeItemTypeShowing4BatteryType(
                    ThConfig4Setting.getBatteryType(
                        bindExt.goodsType,
                        bindExt.sku,
                        bindExt.groupOrder
                    )
                )
            )
        }
    }

    private val items4NeedBle = arrayListOf<SettingOp>().apply {
        getDeviceInfo()?.let { bindExt ->
            val isMainSubDevice = ThConfig4Support.isMainSubDevice(bindExt.goodsType, bindExt.sku)
            //载入全部数据须登录才支持,B5178本身就不支持
            if (!isMainSubDevice && AccountConfig.read().isHadToken) {
                add(ThSettingOpItems.makeSt4LoadAllData {
                    loadAllThData()
                })
            }
            //B5178本身不支持删除部分本地数据
            val supportDeletePartCache = !isMainSubDevice
            add(ThSettingOpItems.makeSt4DeleteData("") {
                if (!supportDeletePartCache) {
                    //删除全部数据
                    showDeleteAllDataConfirmDialog()
                } else {
                    selPeriodDialog.show(
                        bindExt.goodsType,
                        bindExt.sku,
                        object : Dialog4ClearThDataSelPeriod.OnSelectedListener {
                            override fun onSure(selPeriodType: Int) {
                                if (selPeriodType == 4) {
                                    //删除全部数据
                                    showDeleteAllDataConfirmDialog()
                                } else {
                                    Vm4ThOpManager.instance()?.clearDataByType(selPeriodType)
                                }
                            }
                        })
                }
            })
            add(
                SettingOp.makeItemTypeUpdate4SoftVersion(bindExt.bleSoftVersion, false)
                    .apply {
                        itemClick = {
                            if (it.canUpdate) {
                                toUpgradePage()
                            }
                        }
                    })
            add(SettingOp.makeItemTypeShowing4VersionHard(bindExt.bleHardVersion))
        }
    }

    private fun initData() {
        getDeviceInfo()?.let { bindExt ->
            originalBindExt = bindExt.copy()
            lastBleVersion = Pair(bindExt.bleSoftVersion, bindExt.bleHardVersion)
        }
        updateGuideInfo()
    }

    private fun initUi() {
        viewBinding.let { vb ->
            vb.vspGuideContainer4ThSetting.needBg = false
            vb.vspDevice4ThSetting.needBg = false
            vb.drvOperatorOld4ThSetting.setBackgroundInfo(false, false)
            vb.rsvRemoteService.setBackgroundInfo(false, false)
            getDeviceInfo()?.let { bindExt ->
                //设备名
                vb.vdn4Rename4ThSetting.updateHintLimit(curName = bindExt.deviceName)
                //是否支持设备锁
                val supportDeviceLock = ThConfig4Support.supportDeviceLock(bindExt.goodsType, bindExt.sku)
                vb.dlvDeviceSafety4ThSetting.setBackgroundInfo(false, Constant4L5.supportRemoteService(bindExt.goodsType, bindExt.sku))
                vb.dlvDeviceSafety4ThSetting.setVisibility(supportDeviceLock)
                if (supportDeviceLock) {
                    vb.dlvDeviceSafety4ThSetting.needNewIntroText()
                    vb.dlvDeviceSafety4ThSetting.updateSwitch(
                        bindExt.sku,
                        bindExt.device,
                        object : DeviceLockView.CheckListener {
                            override fun showLoading() {
                                <EMAIL>()
                            }

                            override fun hideLoading() {
                                <EMAIL>()
                            }
                        })
                }
                //低电量告警
                val supportLowBat = ThConfig4Setting.supportLowBatAlarm(bindExt.goodsType, bindExt.sku)
                supportLowBat.let {
                    if (it.first) {
                        vb.lbpvLowBattery
                            .needIntro(it.third)
                            .setSwitch(bindExt.batteryWarning)
                            .setVisibleTopDivider(false)
                            .setVisibleBottomDivider(false)
                            .setVisibility(true)
                    }
                }
                //导航项
                vb.vspGuideContainer4ThSetting.setItemList(mutableListOf<SettingOp>().apply {
                    add(SettingOp.makeItemType4Guide(sku()))
                })
                //延时告警
                if (ThConfig4Setting.supportDelayAlarm(bindExt.goodsType, bindExt.sku)) {
                    delayAlarmView = DelayAlarmView(this)
                    delayAlarmView!!.updateValue(bindExt.delayPushTime)
                    vb.ttarvTemWarning4ThSetting.addMidLayout(delayAlarmView!!)
                }
                //初始化温度告警相关信息
                vb.ttarvTemWarning4ThSetting.initSubDeviceInfo(
                    bindExt.goodsType,
                    bindExt.sku,
                    bindExt.bleSoftVersion,
                    bindExt.bleHardVersion
                )
                vb.ttarvTemWarning4ThSetting.needTitleIcon(true)
                if (isBindGateway()) {
                    getGateInfo()?.let { gwInfo ->
                        vb.ttarvTemWarning4ThSetting.initGwDeviceInfo(
                            0,
                            gwInfo.sku,
                            gwInfo.versionSoft,
                            gwInfo.versionHard
                        )
                    }
                }
                vb.ttarvTemWarning4ThSetting.setBackgroundInfo(false, true)
                val temRangePair = ThConfig4Detail.getTemRange(
                    bindExt.goodsType,
                    bindExt.sku,
                    bindExt.bleSoftVersion,
                    bindExt.bleHardVersion
                )
                val temLimitRange = if (fahOpen) {
                    Pair(
                        TemUtil.getTemF(temRangePair.first * 1.0f),
                        TemUtil.getTemF(temRangePair.second * 1.0f)
                    )
                } else {
                    temRangePair
                }
                vb.ttarvTemWarning4ThSetting.setTemRange(fahOpen, intArrayOf(temLimitRange.first, temLimitRange.second))
                //初始化湿度告警相关信息
                vb.tharvHumWarning4ThSetting.setHumRange(intArrayOf(ThConsV1.HUM_MIN_VALUE, ThConsV1.HUM_MAX_VALUE))
                vb.tharvHumWarning4ThSetting.setBackgroundInfo(false, true)
                //告警设置
                ThConfig4Setting.supportWarnSetting(bindExt.goodsType, bindExt.sku).let { supportWarnSetting ->
                    when (supportWarnSetting) {
                        ThConfig4Setting.TH_WARN_TYPE_4_OLD -> {
                            vb.twsvWarnSetting4ThSetting.initDeviceInfo(bindExt.sku, bindExt.device)
                            vb.twsvWarnSetting4ThSetting.setBackgroundInfo(needBackGround = false, needBottomDivider = true)
                            vb.twsvWarnSetting4ThSetting.setVisibility(true)
                        }

                        ThConfig4Setting.TH_WARN_TYPE_4_NEW -> {
                            vb.twsvWarnSettingNew4ThSetting.initDeviceInfo(bindExt.goodsType, bindExt.sku, bindExt.device)
                            ThConfig4Setting.supportVolumeLevel(bindExt.goodsType, bindExt.sku).let { supportVolumeLevel ->
                                if (supportVolumeLevel) {
                                    volumeLeveView = VolumeLeveView(this).apply {
                                        initDeviceInfo(bindExt.sku, bindExt.device)
                                        canOperate(hasBleConnected())
                                        updateValue(bindExt.muteLevel)
                                        bindExt.muteLevel?.let {
                                            showCloseVolumeRemind(it)
                                        }
                                    }
                                    vb.twsvWarnSettingNew4ThSetting.addOtherContent(volumeLeveView!!)
                                }
                            }
                            ThConfig4Setting.supportDeviceWarningTime(bindExt.goodsType, bindExt.sku).let { supportWarningTime ->
                                if (supportWarningTime) {
                                    deviceWarningTimeView = DeviceWarningTimeView(this).apply {
                                        updateSwitch(bindExt.deviceWarning)
                                        updateSeconds(bindExt.deviceWarningSeconds)
                                        changeState(hasBleConnected())
                                    }
                                    vb.twsvWarnSettingNew4ThSetting.addOtherContent(deviceWarningTimeView!!)
                                }
                            }
                            vb.twsvWarnSettingNew4ThSetting.setBackgroundInfo(needBackGround = false, needBottomDivider = true)
                            vb.twsvWarnSettingNew4ThSetting.setVisibility(true)
                        }

                        else -> {}
                    }
                }
                //初始显示校准相关信息
                vb.tcvCali4ThSetting.setDeviceInfo(bindExt.sku)
                val frequencyInfoPair = ThConfig4Support.getThCdUploadFrequencyInfo(bindExt.goodsType, bindExt.sku)
                vb.tcvCali4ThSetting.setUiInfo(true, false, supportLowBat.first || (frequencyInfoPair.first != ThConfig4Support.UNDEFINED))
                vb.tcvCali4ThSetting.noHumInfo(!ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku))
                ThConfig4Setting.getCalibrationRange(bindExt.goodsType, bindExt.sku).run {
                    val temCaliRange = if (fahOpen) {
                        //不能用摄氏度直接转化，因为要保留小数，而我们的工具转化类都是已取整
                        floatArrayOf(second.first, second.second)
                    } else {
                        floatArrayOf(first.first, first.second)
                    }
                    val humCaliRange = floatArrayOf(third.first, third.second)
                    vb.tcvCali4ThSetting.setRange(fahOpen, temCaliRange, humCaliRange)
                }
                //初始化上传频率相关信息
                vb.thivCdUploadFrequency4ThSetting.setBackgroundInfo(false, supportLowBat.first)
                when (frequencyInfoPair.first) {
                    ThConfig4Support.UPLOAD_FREQUENCY_V0 -> {
                        vb.thivCdUploadFrequency4ThSetting.setIntervalStr(frequencyInfoPair.second)
                    }

                    ThConfig4Support.UPLOAD_FREQUENCY_V1 -> {
                        vb.thivCdUploadFrequency4ThSetting.setIntervalStr4V2(frequencyInfoPair.second)
                    }

                    else -> {}
                }
                //主+从款sku的从设备无删除设备按钮，其跟随主设备一起删除
                if (ThConfig4Support.isMainSubDevice(bindExt.goodsType, bindExt.sku)) {
                    vb.tvDeleteBtn4ThSetting.setVisibility(bindExt.groupOrder == 0)
                    vb.spBottomPadding14ThSetting.setVisibility(bindExt.groupOrder > 0)
                }
                ThConfig4Setting.supportComfortSetting(bindExt.goodsType, bindExt.sku).let { supportComfort ->
                    if (supportComfort) {
                        viewBinding.comfortSettingView.initTemRange(fahOpen)
                    }
                }
            }
        }
        if (!hasBleConnected()) {
            //ble若未连接则更新一遍相关操作值，若已连接则会在相应的LiveData中更新
            updateDeviceInfo("initUi")
        }
        //初始化显示
        updateViewDisplayByConnection()
        //其他控件相关显示
        otherViewSetting()
    }

    /**
     * 显示关闭设备声音报警的弹窗
     */
    private fun showCloseVolumeRemind(volumeGear: Int) {
        getDeviceInfo()?.let { bindExt ->
            val cacheKey = SmartCacheKey.appGlobal("${AccountConfig.read().accountId}_${bindExt.sku}_${Dialog4CloseVolumeRemind.Companion::class.java.name}")
            val hasShowed = GlobalCache.build().getBoolean(cacheKey).isTrue()
            val supportVolume = ThConfig4Setting.supportVolumeLevel(bindExt.goodsType, bindExt.sku)
            val hasVolume = volumeGear > 0
            if (supportVolume && (!hasShowed) && hasVolume) {
                GlobalCache.build().set(cacheKey, true)
                closeVolumeRemindDialog.showDialog(bindExt.sku)
            }
        }
    }

    /**
     * 将配件shop的view组件插入到指定view的下方，这里是插入到电池ui-item的下方
     */
    private fun otherViewSetting() {
        getDeviceInfo()?.let { bindExt ->
            attachSkuShopAccessory(
                this::class.java.name,
                bindExt.sku,
                viewBinding.sivShowItem4ThSetting,
                this
            )
        }
    }

    /**
     * 更新设备信息展示
     */
    private fun updateDeviceInfo(from: String) {
        updateWarnInfo(from)
        updateCalibrationInfo()
        updateUploadFrequency()
        updateComfortTemHum()
        updateOtherInfo()
    }

    private fun initOpClick() {
        viewBinding.let { vb ->
            vb.ivBackBtn4ThSetting.clickDelay {
                onBackPressed()
            }
            vb.acContainer.clickDelay {
                vb.vdn4Rename4ThSetting.hideInputMethod()
            }
            vb.clContentContainer4ThSetting.clickDelay {
                vb.vdn4Rename4ThSetting.hideInputMethod()
            }
            vb.vdn4Rename4ThSetting.saveNewDeviceNameCallback = { newName ->
                if (AccountConfig.read().isHadToken && !NetUtil.isNetworkAvailable(this)) {
                    toast(R.string.network_anomaly)
                } else {
                    Vm4ThOpManager.instance()?.settingOpManager?.changeDeviceName(newName)
                }
            }
            //切换低电量开关
            vb.lbpvLowBattery.setOnSwitchListener(object : LowBatPushView.OnSwitchListener {
                override fun onSelected(isOpen: Boolean) {
                    getDeviceInfo()?.let { bindExt ->
                        val supportLowBat = ThConfig4Setting.supportLowBatAlarm(bindExt.goodsType, bindExt.sku)
                        //未登录的情况下，若支持低电量告警,但仅有服务端而无设备本地，则需提示登录
                        if ((!AccountConfig.read().isHadToken) && supportLowBat.first && (!supportLowBat.second)) {
                            ConfirmDialogV3.showConfirmDialog(
                                this@Ac4ThCommonSettingNew,
                                R.string.login_first_label,
                                R.string.cancel,
                                R.string.to_login_now
                            ) {
                                LoginActivity.jump2LoginAc(this@Ac4ThCommonSettingNew, "", false)
                            }
                            return
                        }
                    }
                    Vm4ThOpManager.instance()?.settingOpManager?.updateLowBatAlarm(isOpen) {
                        if (it) {
                            vb.lbpvLowBattery.setSwitch(isOpen)
                        }
                    }
                }
            })
            vb.ttarvTemWarning4ThSetting.setListener { minAlarmTem: Int, maxAlarmTem: Int, alarmOn: Boolean ->
                val minTem100: Int
                val maxTem100: Int
                if (fahOpen) {
                    minTem100 = NumberUtil.temRangeFah2Cel4Setting(minAlarmTem)
                    maxTem100 = NumberUtil.temRangeFah2Cel4Setting(maxAlarmTem)
                } else {
                    minTem100 = minAlarmTem * 100
                    maxTem100 = maxAlarmTem * 100
                }
                getDeviceInfo()?.let { info ->
                    if (ThConfig4Setting.onlyBleBindGateway(info.goodsType, info.sku) && !NetUtil.isNetworkAvailable(this)) {
                        toast(R.string.net_fail_check_retry_hint)
                    } else {
                        Vm4ThOpManager.instance()?.settingOpManager?.updateTemWarningInfo(minTem100, maxTem100, alarmOn)
                    }
                }
            }
            delayAlarmView?.setOperationListener { mins: Int ->
                Vm4ThOpManager.instance()?.settingOpManager?.updateDelayPushTime(mins)
            }
            volumeLeveView?.setSelectVolumeLevelListener(object :
                VolumeLeveView.OnSelectVolumeListener {
                override fun onSelectVolumeLevel(selectVolumeGear: Int) {
                    Vm4ThOpManager.instance()?.settingOpManager?.setVolumeLevel(selectVolumeGear) {
                        if (it) {
                            volumeLeveView?.updateValue(selectVolumeGear)
                            showCloseVolumeRemind(selectVolumeGear)
                        }
                    }
                }
            })
            deviceWarningTimeView?.onTimeSelectedListener = { seconds ->
                Vm4ThOpManager.instance()?.settingOpManager?.setDeviceWarning(true, seconds) {
                    if (it) {
                        deviceWarningTimeView?.updateSeconds(seconds)
                    }
                }
            }
            deviceWarningTimeView?.onChangeSwitchListener = { open, seconds ->
                Vm4ThOpManager.instance()?.settingOpManager?.setDeviceWarning(open, seconds) {
                    if (it) {
                        deviceWarningTimeView?.updateSwitch(open)
                    }
                }
            }
            vb.tharvHumWarning4ThSetting.setListener { minAlarmHum: Int, maxAlarmHum: Int, alarmOn: Boolean ->
                getDeviceInfo()?.let { info ->
                    if (ThConfig4Setting.onlyBleBindGateway(info.goodsType, info.sku) && !NetUtil.isNetworkAvailable(this)) {
                        toast(R.string.net_fail_check_retry_hint)
                    } else {
                        Vm4ThOpManager.instance()?.settingOpManager?.updateHumWarningInfo(minAlarmHum * 100, maxAlarmHum * 100, alarmOn)
                    }
                }
            }
            vb.tcvCali4ThSetting.setListener(object : THCalibrationView.THCalibrationListener {
                override fun onTemCali(fahOpen: Boolean, temCali: Float) {
                    getDeviceInfo()?.let { info ->
                        if (ThConfig4Setting.onlyBleBindGateway(info.goodsType, info.sku) && !NetUtil.isNetworkAvailable(this@Ac4ThCommonSettingNew)) {
                            toast(R.string.net_fail_check_retry_hint)
                        } else {
                            val temCali100 = NumberUtil.getValidTemCali(temCali, fahOpen)
                            Vm4ThOpManager.instance()?.settingOpManager?.updateTemCali(temCali100)
                        }
                    }
                }

                override fun onHumCali(humCali: Float) {
                    getDeviceInfo()?.let { info ->
                        if (ThConfig4Setting.onlyBleBindGateway(info.goodsType, info.sku) && !NetUtil.isNetworkAvailable(this@Ac4ThCommonSettingNew)) {
                            toast(R.string.net_fail_check_retry_hint)
                        } else {
                            val humCali100 = NumberUtil.toIntValueBy100(humCali)
                            Vm4ThOpManager.instance()?.settingOpManager?.updateHumCali(humCali100)
                        }
                    }
                }
            })
            vb.comfortSettingView.onValueChangeListener = { temRange, humRange ->
                Vm4ThOpManager.instance()?.settingOpManager?.setComfortTemHum(temRange, humRange) {
                    if (it) {
                        vb.comfortSettingView.updateValue(temRange, humRange)
                    }
                }
            }
            //更新上传频率
            vb.thivCdUploadFrequency4ThSetting.setListener { minutes: Int ->
                Vm4ThOpManager.instance()?.settingOpManager?.updateThCdUploadFrequency(minutes)
            }
            vb.tvReconnectBtn4ThSetting.clickDelay {
                if (!BleController.getInstance().isBlueToothOpen) {
                    toast(ResUtil.getString(R.string.bluetooth_unable_des))
                    return@clickDelay
                }
                Vm4ThOpManager.instance()?.toConnectBle()
            }
            vb.tvDeleteBtn4ThSetting.clickDelay {
                //弹出确认弹窗
                ConfirmDialog.showConfirmDialog(
                    this,
                    ResUtil.getString(R.string.dialog_unbind_label),
                    ResUtil.getString(R.string.no),
                    ResUtil.getString(R.string.yes),
                    false
                ) {
                    //执行删除
                    Vm4ThOpManager.instance()?.settingOpManager?.deleteDevice { deleteResult ->
                        getDeviceInfo()?.let {
                            if (deleteResult) {
                                //断开蓝牙
                                BleController.getInstance().toBtClose()
                                //需判断是从网关页面跳转过来还是从设备列表跳转过来，新需求(暂时为H5044的子设备)要求从哪来，回哪去
                                //网关列表页存在,则说明是从网关页面跳转过来的
                                val fromGwListPage = BaseApplication.getBaseApplication().hadActivity(Base2homeConfig.getConfig().gwListAc.name)
                                val hasGw = when (Vm4ThOpManager.instance()?.getGateInfo()?.sku) {
                                    Constant4L5.H5044 -> {
                                        true
                                    }

                                    else -> {
                                        false
                                    }
                                }
                                if (fromGwListPage && hasGw) {
                                    //向外通知事件(关闭详情页、刷新网关子设备列表页)
                                    Event4DeleteThFromGw.sendEvent()
                                    mHandler.postDelayed(object : CaughtRunnable() {
                                        override fun runSafe() {
                                            hideLoading()
                                            onBackPressed()
                                        }
                                    }, 100)
                                } else {
                                    //回退到主界面
                                    EventTabDefault.sendEventTabDefault()
                                    val bundle = Bundle().apply {
                                        putString(Constant.intent_ac_key_sku, it.sku)
                                        putString(Constant.intent_ac_key_unbind, it.device)
                                    }
                                    JumpUtil.jumpWithBundle(
                                        this,
                                        Base2homeConfig.getConfig().mainAcClass,
                                        true,
                                        bundle
                                    )
                                    hideLoading()
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    private fun initObserver() {
        Vm4ThOpManager.instance()?.let { vm ->
            vm.loadingChange.showDialog.observeInActivity(this) {
                showLoading()
            }
            vm.loadingChange.dismissDialog.observeInActivity(this) {
                hideLoading()
            }
            vm.ld4ChangeGateway.observe(this) {
                updateGuideInfo()
                getGateInfo()?.let { gwInfo ->
                    viewBinding.ttarvTemWarning4ThSetting.initGwDeviceInfo(
                        0,
                        gwInfo.sku,
                        gwInfo.versionSoft,
                        gwInfo.versionHard
                    )
                }
                //相关视图刷新
                updateDeviceInfo("after bind gateway...")
                updateViewDisplayByConnection()
            }
            vm.ld4ConnectStatusChange.observe(this) {
                SafeLog.i("xiaobing") { "Ac4ThCommonSettingNew--initObserver-->iotOnline->${JsonUtil.toJson(it.second)}" }
                viewBinding.rsvRemoteService.setPureGwOnline(it.second.second)
                updateBleConnectStatus(it.first)
                //相关视图刷新
                updateViewDisplayByConnection()
            }
            vm.ld4UpgradeVersion.observe(this) {
                upgradeInfoChange(it)
            }
            //导航信息展示
            vm.settingOpManager.ld4guides.observe(this) { guides ->
                viewBinding.let { vb ->
                    vb.vspGuideContainer4ThSetting.changeOp(SettingOp.tag_guide) {
                        this.guides = guides
                        true
                    }
                    //间隔线设置
                    vb.dlvDeviceSafety4ThSetting.setBackgroundInfo(false, !guides.isNullOrEmpty())
                    vb.drvOperatorOld4ThSetting.setBackgroundInfo(false, !guides.isNullOrEmpty())
                    vb.rsvRemoteService.setBackgroundInfo(false, !guides.isNullOrEmpty())
                    //更多玩法相关
                    vb.rpvMorePlays4ThSetting.also {
                        it.showLine(!guides.isNullOrEmpty(), false)
                    }.bindData(this, getDeviceInfo()?.sku ?: "") {
                        vb.vspGuideContainer4ThSetting.setVisibility(!guides.isNullOrEmpty() || it)
                    }
                }
            }
            //更新设备名
            vm.settingOpManager.ld4UpdateDeviceName.observe(this) {
                getDeviceInfo()?.let { bindExt ->
                    if (bindExt.getKey() == it.first) {
                        viewBinding.vdn4Rename4ThSetting.updateDeviceName(it.second)
                    }
                }
            }
            //更新实时数据显示
            vm.ld4RealTemHum.observe(this) {
                updateCalibrationInfo(it)
            }
            //相关设置操作的回调更新
            vm.ld4UpdateDeviceInfo.observe(this) { updatePair ->
                if (!hasCreated) {
                    return@observe
                }
                when (updatePair.second) {
                    Vm4ThOpManager.UPDATE_4_WARNING -> {
                        updateWarnInfo("ld4UpdateDeviceInfo")
                    }

                    Vm4ThOpManager.UPDATE_4_DELAY_ALARM -> {
                        getDeviceInfo()?.let { bindExt ->
                            delayAlarmView?.updateValue(bindExt.delayPushTime)
                        }
                    }

                    Vm4ThOpManager.UPDATE_4_CALIBRATION -> {
                        updateCalibrationInfo()
                    }

                    Vm4ThOpManager.UPDATE_4_UPLOAD_FREQUENCY -> {
                        updateUploadFrequency()
                    }

                    Vm4ThOpManager.UPDATE_4_BUZZER_WARNING_CHANGE -> {
                        updateDeviceWarningTime()
                    }

                    else -> {}
                }
            }
            //加载全部数据结束
            vm.loadThcdManager.ld4LoadThCdStep.observe(this) {
                getDeviceInfo()?.let { bindExt ->
                    it[Pair(bindExt.sku, bindExt.device)]?.let { thCdInfo ->
                        if (thCdInfo.second.second == Vm4ThOpManager.THCD_4_IS_LOAD_FINISH) {
                            if (thCdInfo.second.first == Vm4ThOpManager.RC_FROM_SETTING_LOAD_ALL) {
                                loadAtdDialog.finish(thCdInfo.second.third?.first.isTrue())
                                mHandler.postDelayed(object : CaughtRunnable() {
                                    override fun runSafe() {
                                        setThDataCacheMemory()
                                        loadAtdDialog.hide()
                                    }
                                }, ClearThDataUtils.REFRESH_TH_WAIT_TIME)
                            } else {
                                setThDataCacheMemory()
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 蓝牙状态变化
     */
    private fun updateBleConnectStatus(bleConnectStatus: Int) {
        when (bleConnectStatus) {
            //蓝牙未打开
            BleOpManager.BLE_UNABLE -> {
                if (!isBindGateway()) {
                    viewBinding.tvBleDisconnectRemind4ThSetting.setVisibility(true)
                    viewBinding.tvReconnectBtn4ThSetting.setVisibility(false)
                    viewBinding.pbReconectLoading4ThSetting.setVisibility(false)
                } else {
                    updateCalibrationInfo()
                }
            }
            //断开
            BleOpManager.BLE_DISCONNECT -> {
                if (!isBindGateway()) {
                    viewBinding.tvBleDisconnectRemind4ThSetting.setVisibility(true)
                    viewBinding.tvReconnectBtn4ThSetting.setVisibility(true)
                    viewBinding.pbReconectLoading4ThSetting.setVisibility(false)
                } else {
                    updateCalibrationInfo()
                }
            }
            //连接中
            BleOpManager.BLE_CONNECTING -> {
                if (!isBindGateway()) {
                    viewBinding.tvBleDisconnectRemind4ThSetting.visibility = View.INVISIBLE
                    viewBinding.tvReconnectBtn4ThSetting.visibility = View.INVISIBLE
                    viewBinding.pbReconectLoading4ThSetting.setVisibility(true)
                }
            }
            //连接成功
            BleOpManager.BLE_CONNECTED_SUC -> {
                viewBinding.tvBleDisconnectRemind4ThSetting.visibility = View.GONE
                viewBinding.tvReconnectBtn4ThSetting.visibility = View.GONE
                viewBinding.pbReconectLoading4ThSetting.setVisibility(false)
                updateDeviceInfo("BLE_CONNECTED_SUC")
            }

            //读取完设备信息
            BleOpManager.BLE_READ_INFO_FINISH -> {
                viewBinding.tvBleDisconnectRemind4ThSetting.visibility = View.GONE
                viewBinding.tvReconnectBtn4ThSetting.visibility = View.GONE
                viewBinding.pbReconectLoading4ThSetting.setVisibility(false)
                updateBleVersionLinkage()
                updateDeviceInfo("BLE_READ_INFO_FINISH")
                //更新版本号显示
                viewBinding.let { vb ->
                    getDeviceInfo()?.let { bindExt ->
                        vb.vspDevice4ThSetting.changeOp(SettingOp.tag_soft_version) {
                            showingStr = bindExt.bleSoftVersion
                            true
                        }
                        vb.vspDevice4ThSetting.changeOp(SettingOp.tag_hard_version) {
                            showingStr = bindExt.bleHardVersion
                            true
                        }
                        //赋值版本号
                        lastBleVersion = Pair(bindExt.bleSoftVersion, bindExt.bleHardVersion)
                    }
                }
            }
        }
        SafeLog.i(TAG, "updateBleConnectStatus: $bleConnectStatus")
        getDeviceInfo()?.let { bindExt ->
            ThConfig4Setting.supportVolumeLevel(bindExt.goodsType, bindExt.sku).let {
                if (it) {
                    volumeLeveView?.canOperate(hasBleConnected())
                }
            }
            ThConfig4Setting.supportDeviceWarningTime(bindExt.goodsType, bindExt.sku).let {
                if (it) {
                    deviceWarningTimeView?.changeState(hasBleConnected())
                }
            }
        }
    }

    /**
     * 升级相关变化
     */
    private fun upgradeInfoChange(upgradeInfo: Pair<Int, CheckVersion?>) {
        when (upgradeInfo.first) {
            Vm4ThOpManager.UPGRADE_FIRMWARE_4_FIRST_REMIND -> {
                getDeviceInfo()?.let { bindExt ->
                    BleUpdateHintDialog.showDialog(this, bindExt.sku, {
                        toUpgradePage()
                    }, this.javaClass.name)
                }
                //再执行红点
                Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value =
                    Pair(Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND, upgradeInfo.second)
            }

            Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND -> {
                viewBinding.vspDevice4ThSetting.changeOp(SettingOp.tag_soft_version) {
                    this.canUpdateCheck(true)
                }
            }

            Vm4ThOpManager.UPGRADE_FIRMWARE_4_FINISH -> {
                //升级成功后，会断开连接，故须重连
                Vm4ThOpManager.instance()?.toConnectBle()
                //隐藏升级提示
                viewBinding.vspDevice4ThSetting.changeOp(SettingOp.tag_soft_version) {
                    this.canUpdateCheck(false)
                }
            }

            else -> {
                viewBinding.vspDevice4ThSetting.changeOp(SettingOp.tag_soft_version) {
                    getDeviceInfo()?.let { bindExt ->
                        this.showingStrCheck(bindExt.bleSoftVersion)
                    }
                    this.canUpdateCheck(false)
                }
            }
        }
    }

    /**
     * 若版本号发生变化，更新相关关联业务
     */
    private fun updateBleVersionLinkage() {
        lastBleVersion?.let {
            getDeviceInfo()?.let { bindExt ->
                //判断版本号是否有变动
                if (it.first != bindExt.bleSoftVersion || it.second != bindExt.bleHardVersion) {
                    viewBinding.let { vb ->
                        //分布式网关联动的支持
                        //显、隐分布式网关相关功能视图
                        val supportRemoteService = Constant4L5.supportRemoteService(bindExt.goodsType, bindExt.sku)
                        vb.rsvRemoteService.getBindInfo(
                            supportRemoteService,
                            bindExt.sku,
                            bindExt.device,
                            bindExt.deviceName,
                            bindExt.bleHardVersion,
                            bindExt.bleSoftVersion
                        )
                        vb.drvOperatorOld4ThSetting.getBindInfo(
                            !supportRemoteService,
                            bindExt.sku,
                            bindExt.device,
                            bindExt.deviceName,
                            bindExt.bleHardVersion,
                            bindExt.bleSoftVersion
                        )
                        //初始化温度告警相关信息
                        vb.ttarvTemWarning4ThSetting.initSubDeviceInfo(
                            bindExt.goodsType,
                            bindExt.sku,
                            bindExt.bleSoftVersion,
                            bindExt.bleHardVersion
                        )
                        //温度范围
                        val temRangePair = ThConfig4Detail.getTemRange(
                            bindExt.goodsType,
                            bindExt.sku,
                            bindExt.bleSoftVersion,
                            bindExt.bleHardVersion
                        )
                        val temLimitRange = if (fahOpen) {
                            Pair(
                                TemUtil.getTemF(temRangePair.first * 1.0f),
                                TemUtil.getTemF(temRangePair.second * 1.0f)
                            )
                        } else {
                            temRangePair
                        }
                        vb.ttarvTemWarning4ThSetting.setTemRange(
                            fahOpen,
                            intArrayOf(temLimitRange.first, temLimitRange.second)
                        )
                    }
                }
            }
        }
    }

    /**
     * 更新导航信息展示
     */
    private fun updateGuideInfo() {
        //未登录则无须处理
        if (!AccountConfig.read().isHadToken) {
            return
        }
        getDeviceInfo()?.let { bindExt ->
            //Wi-Fi款、绑定网关的蓝牙款以及H5053有导航信息
            val supportGuide = ThConfig4Support.supportWifi(bindExt.goodsType, bindExt.sku)
                    || ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)
                    || isBindGateway()
            if (supportGuide) {
                Vm4ThOpManager.instance()?.settingOpManager?.queryGuide()
            } else {
                //更多玩法相关
                viewBinding.let { vb ->
                    vb.rpvMorePlays4ThSetting.also {
                        it.showLine(showTop = false, showBottom = false)
                    }.bindData(this, bindExt.sku) {
                        vb.vspGuideContainer4ThSetting.setVisibility(it)
                    }
                }
            }
        }
    }

    /**
     * 更新告警相关信息
     */
    private fun updateWarnInfo(from: String) {
        SafeLog.i("xiaobing") { "Ac4ThCommonSettingNew--updateWarnInfo-->更新来源=->${from}" }
        getDeviceInfo()?.let { bindExt ->
            viewBinding.let { vb ->
                //更新温度预警
                val temAlarmRange = IntArray(2)
                if (fahOpen) {
                    temAlarmRange[0] = ThUtil.temRangeCel2Fah4Showing(bindExt.temMin)
                    temAlarmRange[1] = ThUtil.temRangeCel2Fah4Showing(bindExt.temMax)
                } else {
                    temAlarmRange[0] = ThUtil.temRangeCel2Cel4Showing(bindExt.temMin)
                    temAlarmRange[1] = ThUtil.temRangeCel2Cel4Showing(bindExt.temMax)
                }
                vb.ttarvTemWarning4ThSetting.updateTemAlarm(temAlarmRange, bindExt.temWarning)
                //更新湿度预警
                val humAlarmRange = intArrayOf(
                    NumberUtil.getValueUpward(bindExt.humMin, true),
                    NumberUtil.getValueUpward(bindExt.humMax, false)
                )
                vb.tharvHumWarning4ThSetting.updateHumAlram(humAlarmRange, bindExt.humWarning)
            }
        }
    }

    /**
     * 更新校准相关信息
     */
    private fun updateCalibrationInfo(realInfo: Pair<Triple<Int, Int, Int>, Long>? = null) {
        getDeviceInfo()?.let { bindExt ->
            val showRealInfo = realInfo
                ?: Vm4ThOpManager.instance()?.ld4RealTemHum?.value
                ?: Config4LastThValue.getConfig().getLastTh(bindExt.sku, bindExt.device)?.let {
                    Pair(Triple(it.tem, it.hum, it.pm), it.lastTime)
                }
            showRealInfo?.let { info ->
                val canOperate = hasBleConnected() || ThConfig4Support.supportOnly433(
                    bindExt.goodsType,
                    bindExt.sku
                )
                val temCali = if (canOperate) NumberUtil.getValidTemCali(bindExt.temCali, fahOpen) else 0.0f
                val humCali = if (canOperate) NumberUtil.toFloatValueBy100(bindExt.humCali) else 0.0f
                val thInfo = info.first
                val realTem = if (canOperate) NumberUtil.getTemValue(fahOpen, thInfo.first, 0) else 0.0f
                val realHum = if (canOperate) NumberUtil.toFloatValueBy100(thInfo.second) else 0.0f
                viewBinding.let { vb ->
                    vb.tcvCali4ThSetting.updateCalibration(
                        realTem,
                        temCali,
                        realHum,
                        humCali,
                        canOperate && ThConsV1.isValidThValue(thInfo.first, thInfo.second)
                    )
                    vb.tcvCali4ThSetting.alpha = if (canOperate) 1f else 0.4f
                    vb.tcvCali4ThSetting.isEnabled = canOperate
                }
            }
        }
    }

    /**
     * 更新上传频率信息
     */
    private fun updateUploadFrequency() {
        getDeviceInfo()?.let { bindExt ->
            viewBinding.thivCdUploadFrequency4ThSetting.updateChooseInterval(bindExt.uploadRate)
        }
    }

    /**
     * 更新舒适度
     */
    private fun updateComfortTemHum() {
        getDeviceInfo()?.let { bindExt ->
            if (!ThConfig4Setting.supportComfortSetting(bindExt.goodsType, bindExt.sku)) return
            val comfortRange = bindExt.comfortRange()
            viewBinding.comfortSettingView.updateValue(
                comfortRange.first,
                comfortRange.second
            )
        }
    }

    /**
     * 其他信息
     * 如：音量、低电量等
     */
    private fun updateOtherInfo() {
        getDeviceInfo()?.let {
            //音量
            ThConfig4Setting.supportVolumeLevel(it.goodsType, it.sku).let { supportVolumeLevel ->
                if (supportVolumeLevel) {
                    volumeLeveView?.updateValue(it.muteLevel)
                    it.muteLevel?.let { volumeGear ->
                        showCloseVolumeRemind(volumeGear)
                    }
                }
            }
            //低电量报警
            ThConfig4Setting.supportLowBatAlarm(it.goodsType, it.sku).run {
                if (this.first || this.second) {
                    viewBinding.lbpvLowBattery.setSwitch(it.batteryWarning)
                }
            }
            updateDeviceWarningTime()
        }
    }

    /**
     * 设备声音报警
     */
    private fun updateDeviceWarningTime() {
        getDeviceInfo()?.let {
            ThConfig4Setting.supportDeviceWarningTime(it.goodsType, it.sku).let { support ->
                if (support) {
                    deviceWarningTimeView?.updateSeconds(it.deviceWarningSeconds)
                    deviceWarningTimeView?.updateSwitch(it.deviceWarning)
                }
            }
        }
    }

    /**
     * 根据有无绑定网关、蓝牙连接状态来更新告警、校准、上传频率等相关控件的显/隐
     */
    private fun updateViewDisplayByConnection() {
        getDeviceInfo()?.let { bindExt ->
            val isSupportOnly433 = ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)
            val supportHum = ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku)
            val supportComfort =
                ThConfig4Setting.supportComfortSetting(bindExt.goodsType, bindExt.sku)
            //告警、校准相关控件的显/隐
            if (isBindGateway() || isSupportOnly433) {
                //绑定了网关则都可以操作
                viewBinding.let {
                    it.ttarvTemWarning4ThSetting.setVisibility(true)
                    it.tharvHumWarning4ThSetting.setVisibility(supportHum)
                    it.tcvCali4ThSetting.setVisibility(true)
                    it.comfortSettingView.setVisibility(supportComfort)
                    if (isBindGateway()) {
                        //有些设备即使绑定了网关，也只能通过蓝牙连接来设置温度、湿度等的报警
                        ThConfig4Setting.setAlarmByWay(bindExt.goodsType, bindExt.sku).let { wayInfo ->
                            val canOperate = wayInfo.first || (wayInfo.second && hasBleConnected())
                            it.ttarvTemWarning4ThSetting.alpha = if (canOperate) 1f else 0.4f
                            it.ttarvTemWarning4ThSetting.setEnable(canOperate)
                            it.tharvHumWarning4ThSetting.alpha = if (canOperate) 1f else 0.4f
                            it.tharvHumWarning4ThSetting.setEnable(canOperate)
                        }
                        val onlyBleWithGw =
                            ThConfig4Setting.onlyBleBindGateway(bindExt.goodsType, bindExt.sku)
                        if (onlyBleWithGw) {
                            it.ttarvTemWarning4ThSetting.setVisibility(hasBleConnected())
                            it.tharvHumWarning4ThSetting.setVisibility(hasBleConnected() && supportHum)
                            it.tcvCali4ThSetting.setVisibility(hasBleConnected())
                            it.comfortSettingView.setVisibility(supportComfort && hasBleConnected())
                        }
                    }
                }
            } else {
                //蓝牙连接的情况下，才可操作
                val showViews = hasBleConnected()
                viewBinding.let {
                    it.ttarvTemWarning4ThSetting.setVisibility(showViews)
                    it.tharvHumWarning4ThSetting.setVisibility(showViews && supportHum)
                    it.tcvCali4ThSetting.setVisibility(showViews)
                    it.comfortSettingView.visibleByBoolean(showViews && supportComfort)
                }
            }
            //上传频率的显/隐
            val uploadFrequencyPair = ThConfig4Support.getThCdUploadFrequencyInfo(bindExt.goodsType, bindExt.sku)
            val showFrequency = uploadFrequencyPair.first != ThConfig4Support.UNDEFINED && hasBleConnected()
            viewBinding.thivCdUploadFrequency4ThSetting.setVisibility(showFrequency)
            //item的显/隐
            viewBinding.vspDevice4ThSetting.setItemList(mutableListOf<SettingOp>().apply {
                addAll(items4ShowAllTheTime)
                //能绑定网关的设备，其自身是不支持wifi功能的，故无wifi相关item
                if (isBindGateway()) {
                    addAll(0, items4NeedBle)
                } else if (isSupportOnly433) {
                    add(1, ThSettingOpItems.makeSt4DeleteData("") {
                        //删除全部数据
                        showDeleteAllDataConfirmDialog()
                    })
                } else {
                    //蓝牙连接的情况下，展示须要蓝牙操作的item
                    if (hasBleConnected()) {
                        addAll(0, items4NeedBle)
                        //支持Wi-Fi功能的，须加入wifi配置项
                        if (ThConfig4Support.supportWifi(bindExt.goodsType, bindExt.sku)) {
                            add(0, SettingOp.makeItemType4WifiSetting {
                                //点击跳转值Wi-Fi配置页
                                Ac4ThWifiSetting.jump2WifiChooseAc4ChangeWifi(
                                    this@Ac4ThCommonSettingNew,
                                    bindExt
                                )
                            })
                        }
                    }
                }
            })
            setThDataCacheMemory()
            //h5053的删除提示语
            viewBinding.tvRemindText4H5053.setVisibility(isSupportOnly433)
        }
    }

    /**
     * 拉取全部数据
     */
    private fun loadAllThData() {
        getDeviceInfo()?.let { bindExt ->
            Vm4ThOpManager.instance()?.loadThcdManager?.ld4LoadThCdStep?.value?.get(
                Pair(
                    bindExt.sku,
                    bindExt.device
                )
            )?.let {
                if (it.second.second != Vm4ThOpManager.THCD_4_IS_LOAD_FINISH) {
                    toast(ResUtil.getString(R.string.fresh_des_loading))
                    return
                }
            }
            loadAtdDialog.show()
            Event4LoadAllData.sendEvent(Event4LoadAllData.LOAD_TH_DATA_TO_RESET_TIME, false)
        }
    }

    /**
     * 删除全部数据的确认弹窗
     */
    private fun showDeleteAllDataConfirmDialog() {
        ConfirmDialog.showConfirmDialog(
            this,
            ResUtil.getString(R.string.hint_4_delete_device_data),
            ResUtil.getString(R.string.no),
            ResUtil.getString(R.string.yes)
        ) {
            Vm4ThOpManager.instance()?.clearAllData {}
        }
    }

    /**
     * 设置图表数据占用存储大小
     */
    private fun setThDataCacheMemory() {
        getDeviceInfo()?.let { bindExt ->
            globalLaunch(Dispatchers.IO) {
                //H5053/B5178本身就不支持
                val isSupportOnly433 =
                    ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)
                val isMainSubDevice =
                    ThConfig4Support.isMainSubDevice(bindExt.goodsType, bindExt.sku)
                if (!isSupportOnly433 && !isMainSubDevice) {
                    val cacheMemoryStr =
                        ClearThDataUtils.getThDataCacheMemory(bindExt.sku, bindExt.device)
                    withContext(Dispatchers.Main) {
                        viewBinding.vspDevice4ThSetting.changeOp(ThSettingOpItems.TAG_4_TH_DELETE_DATA) {
                            desStr = cacheMemoryStr
                            true
                        }
                    }
                }
            }
        }
    }

    /**
     * 跳转至升级页面
     */
    private fun toUpgradePage() {
        getDeviceInfo()?.let { bindExt ->
            Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value?.let { upgradePair ->
                upgradePair.second?.run {
                    val upgradeWay = ThConfig4Setting.getUpgradeWay(
                        bindExt.goodsType,
                        bindExt.sku,
                        bindExt.bleHardVersion
                    )
                    SafeLog.i("xiaobing") { "Ac4ThCommonSettingNew--toUpgradePage-->key=->${bindExt.getKey()},升级方式=->${upgradeWay}" }
                    when (upgradeWay) {
                        ThConfig4Setting.UPGRADE_WAY_4_FRK -> {
                            Ac4UpdateByFrk.jump2OtaUpdate(
                                this@Ac4ThCommonSettingNew,
                                bindExt.sku,
                                bindExt.deviceName,
                                this
                            )
                        }

                        ThConfig4Setting.UPGRADE_WAY_4_V1 -> {
                            Ac4UpdateV1.jump2UpdateAc(
                                this@Ac4ThCommonSettingNew,
                                bindExt.sku,
                                bindExt.deviceName,
                                this
                            )
                        }

                        ThConfig4Setting.UPGRADE_WAY_4_V2 -> {
                            Ac4UpdateV2.jump2UpdateAc(
                                this@Ac4ThCommonSettingNew,
                                bindExt.sku,
                                bindExt.deviceName,
                                this
                            )
                        }

                        else -> {}
                    }
                }
            }
        }
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.thnew.R.id.ac_container
    }

    override fun adapterInsetViewId(): Int {
        return com.govee.thnew.R.id.sp_top_adjust_holder
    }

    override fun layoutId(): Int {
        return com.govee.thnew.R.layout.thnew_ac_4_setting_new
    }

    override fun sku(): String {
        return getDeviceInfo()?.sku ?: ""
    }

    override fun parseIntent(intent: Intent?) {}

    @Suppress("DEPRECATION")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        getDeviceInfo()?.let { bindExt ->
            ThConfig4Setting.supportWarnSetting(bindExt.goodsType, bindExt.sku).let { supportWarnSetting ->
                when (supportWarnSetting) {
                    ThConfig4Setting.TH_WARN_TYPE_4_OLD -> {
                        viewBinding.twsvWarnSetting4ThSetting.onActivityResult(requestCode, resultCode, data)
                    }

                    ThConfig4Setting.TH_WARN_TYPE_4_NEW -> {
                        viewBinding.twsvWarnSettingNew4ThSetting.onActivityResult(requestCode, resultCode, data)
                    }

                    else -> {}
                }
            }
        }
    }

    override fun onDestroy() {
        getDeviceInfo()?.let { bindExt ->
            //有信息更新，通知刷新首页卡片
            if (bindExt != originalBindExt) {
                ThConsV1.refreshDeviceInfo(bindExt.sku, bindExt.device, bindExt, true)
            }
        }
        mHandler.removeCallbacksAndMessages(null)
        super.onDestroy()
    }
}