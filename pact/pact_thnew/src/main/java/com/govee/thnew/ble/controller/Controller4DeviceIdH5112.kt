package com.govee.thnew.ble.controller

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsOnlyReadSingleController
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.event.Event4DeviceId

/**
 * <AUTHOR>
 * @date created on 2025/5/12
 * @description 温湿度计(H5112)-->从设备读取该设备uuid的Controller
 */
class Controller4DeviceIdH5112 : AbsOnlyReadSingleController() {

    override fun getCommandType(): Byte {
        return BleProtocol.value_device_uuid
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        //H5112的deviceId为16个字节
        val addressBytes = ByteArray(16)
        System.arraycopy(validBytes, 0, addressBytes, 0, addressBytes.size)
        val deviceId = BleUtil.toAddressBytes(addressBytes, false)
        Event4DeviceId.sendSuc(isWrite, commandType, proType, deviceId)
        return true
    }

    override fun fail() {
        Event4DeviceId.sendFail(isWrite, commandType, proType)
    }
}