package com.govee.thnew.push

import com.govee.base2home.Constant4L5
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.util.CaughtRunnable
import com.govee.base2home.util.NumberUtil
import com.govee.base2home.util.StrUtil
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.ThUtil
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.config.WarnRange
import com.govee.push.NotifyType
import com.govee.push.PushData
import com.govee.thnew.add.AddInfo
import com.govee.ui.R
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.ResUtil
import java.util.concurrent.Executors

/**
 * <AUTHOR>
 * @date created on 2024/3/21
 * @description 温湿度计-->本地告警的实现类
 */
class LocalWarningImp private constructor() {

    companion object {
        var getInstance = Builder.instance
    }

    private val es by lazy {
        Executors.newSingleThreadExecutor()
    }

    private object Builder {
        val instance = LocalWarningImp()
    }

    /**
     * 检测告警
     */
    fun checkWarning(sku: String, device: String, tem: Int, supportTem: Boolean, hum: Int, supportHum: Boolean, battery: Int, timeMills: Long) {
        val warningRunnable = WarningRunnable(sku, device, timeMills, tem, supportTem, hum, supportHum, battery)
        es.execute(warningRunnable)
    }

    private class WarningRunnable(
        private val sku: String,
        private val device: String,
        private val timeMills: Long,
        private val tem: Int,
        private val supportTem: Boolean,
        private val hum: Int,
        private val supportHum: Boolean,
        private val battery: Int
    ) : CaughtRunnable() {

        private var isValidThValue = true

        init {
            isValidThValue = ThConsV1.isValidThValue(tem, hum)
        }

        override fun runSafe() {
            val fahOpen = TemUnitConfig.read().isTemUnitFah(sku)
            val warnRange = WarnConfig.read().queryWarningRangeByKey(sku, device) ?: return
            val localWarning = LocalNotifyConfig.read()?.getLocalWarning(sku, device) ?: return
            //电量预警
            var updateLocalNotify = checkBatteryWarning(localWarning, warnRange)
            //温度告警(温湿度需为有效值)
            if (warnRange.temWarning && isValidThValue && supportTem) {
                val warning = checkTemWarning(localWarning, warnRange, fahOpen)
                if (warning) {
                    updateLocalNotify = true
                }
            }
            //湿度(温湿度需为有效值)
            if (warnRange.humWarning && isValidThValue && supportHum) {
                val warning = checkHumWarning(localWarning, warnRange)
                if (warning) {
                    updateLocalNotify = true
                }
            }
            //更新记录
            if (updateLocalNotify) {
                LocalNotifyConfig.read()?.writeDef()
            }
        }

        /**
         * 检测湿度报警
         */
        private fun checkHumWarning(localWarning: LocalWarning, warnRange: WarnRange): Boolean {
            var curHum = hum + warnRange.humCali
            curHum = curHum.coerceAtMost(ThConsV1.HUM_MAX_VALUE_BY_OVER)
            val humWarningType = getHumWarningType(curHum, warnRange.humMin, warnRange.humMax)
            val warning = localWarning.checkHumWarning(humWarningType, timeMills)
            if (warning) {
                SafeLog.i("xiaobing") { "WarningRunnable--checkHumWarning-->humWarningType=>${humWarningType},cu4Hum=>${curHum},warnRange.humMin =>${warnRange.humMin},warnRange.humMax =>${warnRange.humMax}" }
                val titleStr = ResUtil.getString(R.string.th_hum_warning_notification_title)
                val humStr = NumberUtil.getHumBy1Point(curHum).toString() + StrUtil.getHumUnit()
                val type: String
                var contentStr = ""
                if (humWarningType == ThConsV1.TH_WARNING_TYPE_HIGH) {
                    contentStr = when (sku) {
                        Constant4L5.H5111 -> {
                            String.format(ResUtil.getString(R.string.text_4_high_hum_alarm_new), warnRange.deviceName, humStr)
                        }

                        Constant4L5.H5112 -> {
                            String.format(ResUtil.getString(R.string.h5112_text_4_push_high_hum_alarm), warnRange.deviceName, getProbeName(), humStr)
                        }

                        else -> {
                            String.format(ResUtil.getString(R.string.th_hum_high_notify_format), warnRange.deviceName, humStr)
                        }
                    }
                    type = ThConsV1.HumHigh
                } else {
                    contentStr = when (sku) {
                        Constant4L5.H5111 -> {
                            String.format(ResUtil.getString(R.string.text_4_low_hum_alarm_new), warnRange.deviceName, humStr)
                        }

                        Constant4L5.H5112 -> {
                            String.format(ResUtil.getString(R.string.h5112_text_4_push_low_hum_alarm), warnRange.deviceName, getProbeName(), humStr)
                        }

                        else -> {
                            String.format(ResUtil.getString(R.string.th_hum_low_notify_format), warnRange.deviceName, humStr)
                        }
                    }
                    type = ThConsV1.HumLow
                }
                val msg = Msg(titleStr, contentStr, type)
                val data = JsonUtil.toJson(msg)
                val pushData = PushData(warnRange.goodsType, warnRange.sku, warnRange.device, NotifyType.notification, ThConsV1.PUSH_TYPE_WARNING, data)
                PushData.sendPushData(pushData)
            }
            return warning
        }

        private fun getHumWarningType(curHum: Int, humMin: Int, humMax: Int): Int {
            if (curHum < humMin) {
                return ThConsV1.TH_WARNING_TYPE_LOW
            }
            return if (curHum > humMax) {
                ThConsV1.TH_WARNING_TYPE_HIGH
            } else ThConsV1.TH_WARNING_TYPE_NORMAL
        }

        /**
         * 检测温度报警
         */
        private fun checkTemWarning(localWarning: LocalWarning, warnRange: WarnRange, fahOpen: Boolean): Boolean {
            val curTem = tem + warnRange.temCali
            val temWarningType = getTemWarningType(fahOpen, curTem, warnRange.temMin, warnRange.temMax)
            val warning = localWarning.checkTemWarning(temWarningType, timeMills)
            if (warning) {
                SafeLog.i("xiaobing") { "WarningRunnable--checkTemWarning-->temWarningType=>${temWarningType},curTem=>${curTem},warnRange.temMin =>${warnRange.temMin},warnRange.temMax =>${warnRange.temMax},fahOpen=>${fahOpen}" }
                val titleStr = ResUtil.getString(R.string.th_tem_warning_notification_title)
                val temStr = NumberUtil.getTemBy1Point(curTem, fahOpen).toString() + if (fahOpen) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
                var contentStr = ""
                val type: String
                if (temWarningType == ThConsV1.TH_WARNING_TYPE_HIGH) {
                    when (sku) {
                        Constant4L5.H5111 -> {
                            contentStr = String.format(ResUtil.getString(R.string.text_4_high_tem_alarm_new), warnRange.deviceName, temStr)
                        }

                        Constant4L5.H5112 -> {
                            String.format(ResUtil.getString(R.string.h5112_text_4_push_high_tem_alarm), warnRange.deviceName, getProbeName(), temStr)
                        }

                        else -> {
                            contentStr = String.format(ResUtil.getString(R.string.th_tem_high_notify_format), warnRange.deviceName, temStr)
                        }
                    }
                    type = ThConsV1.TemHigh
                } else {
                    when (sku) {
                        Constant4L5.H5111 -> {
                            contentStr = String.format(ResUtil.getString(R.string.text_4_low_tem_alarm_new), warnRange.deviceName, temStr)
                        }

                        Constant4L5.H5112 -> {
                            contentStr = String.format(ResUtil.getString(R.string.h5112_text_4_push_low_tem_alarm), warnRange.deviceName, getProbeName(), temStr)
                        }

                        else -> {
                            contentStr = String.format(ResUtil.getString(R.string.th_tem_low_notify_format), warnRange.deviceName, temStr)
                        }
                    }
                    type = ThConsV1.TemLow
                }
                val msg = Msg(titleStr, contentStr, type)
                val data = JsonUtil.toJson(msg)
                val pushData = PushData(warnRange.goodsType, warnRange.sku, warnRange.device, NotifyType.notification, ThConsV1.PUSH_TYPE_WARNING, data)
                PushData.sendPushData(pushData)
            }
            return warning
        }

        private fun getTemWarningType(fahOpen: Boolean, curTem: Int, temMin: Int, temMax: Int): Int {
            val compareTemMin: Int
            val compareTemMax: Int
            if (fahOpen) {
                //对比华摄度下的预警范围
                compareTemMin = ThUtil.temRange4FahCompare(temMin)
                compareTemMax = ThUtil.temRange4FahCompare(temMax)
            } else {
                //对比摄氏度下的预警范围
                compareTemMin = ThUtil.temRange4CelCompare(temMin)
                compareTemMax = ThUtil.temRange4CelCompare(temMax)
            }
            if (curTem < compareTemMin) {
                return ThConsV1.TH_WARNING_TYPE_LOW
            }
            return if (curTem > compareTemMax) {
                ThConsV1.TH_WARNING_TYPE_HIGH
            } else ThConsV1.TH_WARNING_TYPE_NORMAL
        }

        private fun getProbeName(): String {
            when (sku) {
                Constant4L5.H5112 -> {
                    DeviceListConfig.read().getDeviceByKey(sku, ThConsV1.getRealDeviceId(sku, device))?.deviceExt?.deviceSettings?.let {
                        JsonUtil.fromJson(it, AddInfo::class.java)?.let { bindExt ->
                            if (ThConsV1.isCustomDevice(sku, device)) {
                                return bindExt.probeName2 ?: ""
                            } else {
                                return bindExt.probeName1 ?: ""
                            }
                        }
                    }
                }

                else -> {}
            }
            return ""
        }

        /**
         * 检测电量报警
         * 备注：H5112的两根探针在此处当作了两个设备，但电量不能是视作两个设备，注意合并！！！
         */
        private fun checkBatteryWarning(localWarning: LocalWarning, warnRange: WarnRange): Boolean {
            val batteryWarningType = getBatteryWarningType(battery)
            val warning = localWarning.checkBatteryWarning(batteryWarningType, timeMills)
            var otherElement = true
            when (sku) {
                Constant4L5.H5112 -> {
                    otherElement = if (ThConsV1.isCustomDevice(sku, device)) {
                        LocalNotifyConfig.read()?.getLocalWarning(sku, ThConsV1.getRealDeviceId(sku, device))?.checkBatteryWarning(batteryWarningType, timeMills).isTrue()
                    } else {
                        LocalNotifyConfig.read()?.getLocalWarning(sku, ThConsV1.getH5112Device4Pb2(device))?.checkBatteryWarning(batteryWarningType, timeMills).isTrue()
                    }
                }

                else -> {}
            }
            if (warning && otherElement) {
                SafeLog.i("xiaobing") { "WarningRunnable--checkBatteryWarning-->batteryWarningType = $batteryWarningType ; battery = $battery" }
                val titleStr = ResUtil.getString(R.string.th_battery_warning_notification_title)
                val contentStr = when (sku) {
                    Constant4L5.H5111,
                    Constant4L5.H5112,
                        -> {
                        ResUtil.getStringFormat(R.string.text_4_low_battery_new, warnRange.deviceName)
                    }

                    else -> {
                        String.format(ResUtil.getString(R.string.th_battery_notify_format), warnRange.deviceName, "$battery%")
                    }
                }
                val msg = Msg(titleStr, contentStr, "")
                val data = JsonUtil.toJson(msg)
                val pushData = PushData(warnRange.goodsType, warnRange.sku, warnRange.device, NotifyType.notification, ThConsV1.PUSH_TYPE_WARNING, data)
                PushData.sendPushData(pushData)
            }
            return warning
        }

        private fun getBatteryWarningType(battery: Int): Int {
            if (battery >= 20) {
                return ThConsV1.BATTERY_WARNING_LEVEL_NORMAL
            }
            if (battery >= 15) {
                return ThConsV1.BATTERY_WARNING_LEVEL_20
            }
            if (battery >= 10) {
                return ThConsV1.BATTERY_WARNING_LEVEL_15
            }
            return if (battery >= 5) {
                ThConsV1.BATTERY_WARNING_LEVEL_10
            } else ThConsV1.BATTERY_WARNING_LEVEL_5
        }
    }
}
