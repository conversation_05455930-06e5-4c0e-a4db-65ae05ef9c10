package com.govee.thnew.ble.controller

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsOnlyReadSingleController
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.event.Event4Heart.Companion.sendSuc
import com.govee.thnew.pact.ThBroadcastUtil

/**
 * <AUTHOR>
 * @date created on 2024/3/04
 * @description 温湿度计-->心跳包controller-=>V0
 * 备注：心跳返回数据：2bytes/温度+2bytes/湿度
 *      使用sku:H5179
 */
class Controller4HeartV0 : AbsOnlyReadSingleController() {

    override fun getCommandType(): Byte {
        return BleProtocol.value_tem_hum
    }

    /**
     * @param 2bytes/温度+2bytes/湿度
     */
    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val isInvalidTh = validBytes[0] == ThBroadcastUtil.INVALID_BYTE && validBytes[1] == ThBroadcastUtil.INVALID_BYTE || validBytes[2] == ThBroadcastUtil.INVALID_BYTE && validBytes[3] == ThBroadcastUtil.INVALID_BYTE
        if (!isInvalidTh) {
            //温度
            val tem = BleUtil.getSignedShort(validBytes[1], validBytes[0]).toInt()
            //湿度
            val hum = BleUtil.getSignedInt(byteArrayOf(validBytes[2], validBytes[3]), false)
            sendSuc(isWrite, commandType, proType, tem, hum, 100)
        }
        return true
    }

    override fun fail() {
        //心跳包；失败不处理
    }
}