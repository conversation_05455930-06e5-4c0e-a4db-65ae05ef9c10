package com.govee.thnew.ui.op

import android.text.TextUtils
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.pact.GoodsType
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.data.DataConfig
import com.govee.base2newth.db.DbController
import com.govee.base2newth.db.TemHum
import com.govee.base2newth.net.IThNet
import com.govee.base2newth.net.h5053data.DeviceDataLoadRequest
import com.govee.base2newth.net.h5053data.DeviceDataLoadResponse
import com.govee.base2newth.net.h5053data.THListIndexConfig
import com.govee.base2newth.net.h5053data.Th
import com.govee.base2newth.other.Event4LoadAllData
import com.govee.base2newth.other.Event4LoadAllData.Companion.sendEvent
import com.govee.db.utils.globalLaunch
import com.govee.thnew.ui.Vm4ThOpManager
import com.ihoment.base2app.Cache
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.network.Transactions
import kotlinx.coroutines.Dispatchers
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2024/3/22
 * @description 温湿度计-->H5053获取图表数据的类
 */
internal class LoadThCd4H5053 {

    private var goodsType = GoodsType.GOODES_TYPE_NO_SUPPORT
    private var sku = ""
    private var device = ""
    private var lastValidDataLength = 0L
    private var lastDataTime = 0L
    private var toLoadEndTime = 0L

    private val transactions by lazy {
        Transactions()
    }

    companion object {
        //一次请求的服务器数据最大值；48h数据
        private const val LOAD_NUMBERS_LIMIT = 2 * 24 * 60
    }

    private val thLiConfig by lazy {
        THListIndexConfig.read()
    }

    private lateinit var loadResult: ((hasNewData: Boolean, result: Boolean) -> Unit)

    init {
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
    }

    /**
     * 开始下载图表数据
     */
    fun startToLoadThCd(
        goodsType: Int,
        sku: String,
        device: String,
        loadResult: ((hasNewData: Boolean, result: Boolean) -> Unit)
    ) {
        this.goodsType = goodsType
        this.sku = sku
        this.device = device
        toLoadEndTime = System.currentTimeMillis()
        this.loadResult = loadResult
        globalLaunch(Dispatchers.IO) {
            lastValidDataLength = ThConsV1.getValidDataLength(goodsType, sku, device)
            lastDataTime = ThConsV1.getLastValidTime(goodsType, sku, device)
            loadNext()
        }
    }

    /**
     * 加载下一段
     * 备注：H5053没有加载某一段时间数据的功能，故只要加载过就是加载了全部数据
     */
    private fun loadNext() {
        val index = thLiConfig.getIndex(device)
        //index:数据点下标,limit:一次能拉取的最大数据量
        val request = DeviceDataLoadRequest(
            transactions.createTransaction(),
            device,
            LOAD_NUMBERS_LIMIT,
            index,
            sku
        )
        Cache.get(IThNet::class.java).loadThCd4H5053(request).enqueue(IHCallBack(request))
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onDeviceDataLoadResponse(response: DeviceDataLoadResponse) {
        if (!transactions.isMyTransaction(response)) return
        val request: DeviceDataLoadRequest = response.getRequest()
        val sku: String = request.sku
        val device: String = request.device
        if (TextUtils.isEmpty(sku) || TextUtils.isEmpty(device) || sku != this.sku || device != this.device) return
        //统计加载服务器数据成功次数
        AnalyticsRecorder.getInstance()
            .recordTimes(EventKey.use_count, sku, ParamFixedValue.data_service_suc)
        //处理加载的图表数据
        dealLoadThcd(response)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onErrorResponse(response: ErrorResponse) {
        if (!transactions.isMyTransaction(response)) return
        val request = response.request
        if (request is DeviceDataLoadRequest) {
            SafeLog.i("xiaobing") { "LoadThCd4H5053--onErrorResponse-->图表数据拉取失败..." }
            //加载失败...
            //更新图表数据
            Vm4ThOpManager.instance()?.loadThcdManager?.refreshThChartData("H5053加载温湿度图表数据失败...")
            //统计加载服务器数据失败次数
            AnalyticsRecorder.getInstance()
                .recordTimes(EventKey.use_count, sku, ParamFixedValue.data_service_fail)
            //回调结果
            loadResult.invoke(false, false)
        }
    }

    /**
     * 处理下载的温湿度图表数据
     */
    private fun dealLoadThcd(response: DeviceDataLoadResponse) {
        val thCd: List<Th> = response.datas
        val dataClearTime: Long = response.dataClearTime
        //按照dataClearTime清除本地无效数据
        if (DataConfig.read().getLastDataClearTime(sku, device) < dataClearTime) {
            DbController.clearThCache(sku, device, dataClearTime)
            DataConfig.read().updateLastDataClearTime(sku, device, dataClearTime)
        }
        val index: Long = response.index
        //记录新的Index
        thLiConfig.updateIndex(device, index)
        val size = thCd.size
        val hadNextPage: Boolean = size >= LOAD_NUMBERS_LIMIT
        //插入云端数据到数据库中
        toInsertCloudDataPart(thCd)
        if (hadNextPage) {
            //加载下一页数据
            loadNext()
        } else {
            dataLoadFinish()
        }
    }

    /**
     * 存入本地数据库
     */
    private fun toInsertCloudDataPart(values: List<Th>?) {
        if (values.isNullOrEmpty()) return
        val temHumList: MutableList<TemHum> = ArrayList()
        val size = values.size
        for (i in 0 until size) {
            val th: Th = values[i]
            val temHum = TemHum(th.tem, th.hum, th.time, TemHum.FROM_TYPE_CLOUD)
            temHumList.add(temHum)
        }
        DbController.insertDeviceData(sku, device, temHumList)
    }

    /**
     * run in background thread
     */
    private fun dataLoadFinish() {
        globalLaunch(Dispatchers.IO) {
            //更新图表数据
            Vm4ThOpManager.instance()?.loadThcdManager?.refreshThChartData("H5053加载温湿度图表数据完成...")
            sendEvent(Event4LoadAllData.LOAD_TH_DATA_FINISH, true)
            val validDataLength = ThConsV1.getValidDataLength(goodsType, sku, device)
            val dataTime = ThConsV1.getLastValidTime(goodsType, sku, device)
            //判断是否有新数据加载
            val hasNewData = dataTime > lastDataTime || validDataLength > lastValidDataLength
            //回调结果
            loadResult.invoke(hasNewData, true)
        }
    }

    fun release() {
        EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
        SafeLog.i("xiaobing") { "LoadThCd4H5053--release-->..." }
    }
}