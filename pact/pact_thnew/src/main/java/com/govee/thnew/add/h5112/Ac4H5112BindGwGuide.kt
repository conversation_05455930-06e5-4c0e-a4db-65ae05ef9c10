package com.govee.thnew.add.h5112

import android.annotation.SuppressLint
import android.graphics.Rect
import android.os.Bundle
import android.view.View
import androidx.databinding.DataBindingUtil
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.bean.Gateway
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.main.choose.BaseBleDeviceChooseActivity
import com.govee.base2home.theme.ThemeM
import com.govee.base2kt.ac.AbsAc
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.dp4Int
import com.govee.base2kt.ext.setVisibility
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2kt.utils.IntentUtils
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.bbq.ConsV1
import com.govee.base2newth.deviceitem.Ext4Gw
import com.govee.ble.BleController
import com.govee.ble.event.EventBleBroadcastListenerTrigger
import com.govee.kt.hideLoading
import com.govee.kt.showLoading
import com.govee.kt.ui.device.AbsGatewayModel
import com.govee.mvvm.globalLaunch
import com.govee.thnew.add.AddInfo
import com.govee.thnew.databinding.ThnewAc4H5112BindGatewayGuideBinding
import com.govee.thnew.databinding.ThnewItem4H5112BindGwBinding
import com.govee.thnew.pact.h5112.Ext4H5112
import com.govee.thnew.ui.detail.h5112.Ac4H5112Detail
import com.govee.ui.R
import com.govee.ui.dialog.HintDialog1
import com.govee.util.recordUseCount
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.glide.ImageResConfigManager
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import kotlinx.coroutines.Dispatchers

/**
 * <AUTHOR>
 * @date created on 2024/5/21
 * @description H5112-->被添加后引导去绑定网关的页面
 */
class Ac4H5112BindGwGuide : AbsAc<ThnewAc4H5112BindGatewayGuideBinding>() {

    private var h5044Gws = arrayListOf<Gateway>()
    private lateinit var addInfo: AddInfo
    private val h5044GwListAdapter by lazy {
        Adapter4H5044GwList()
    }
    private var selectGw: Gateway? = null

    private val vm4bindGw by lazy {
        ViewModelProvider(this)[Vm4H5112BindGw::class.java].apply {
            <EMAIL>()
            init(addInfo.sku, addInfo.device)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initData()
        initUi()
        initOpClick()
        initObserver()
    }

    private fun initData() {
        addInfo = IntentUtils.parseParcelable<AddInfo>(intent, ThConsV1.KEY_4_ADD_INFO) ?: AddInfo()
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initUi() {
        ImageResConfigManager.loadImage(viewBinding.ivIcon4H5112BindGw, "h5112_pics_anzhuang_yindao_2")
        viewBinding.rvGwList4H5112BindGw.layoutManager = LinearLayoutManager(this)
        viewBinding.rvGwList4H5112BindGw.adapter = h5044GwListAdapter
        viewBinding.rvGwList4H5112BindGw.addItemDecoration(object : ItemDecoration() {
            override fun getItemOffsets(
                outRect: Rect,
                view: View,
                parent: RecyclerView,
                state: RecyclerView.State
            ) {
                outRect.bottom = 10.dp4Int
            }
        })
        h5044GwListAdapter.setOnItemClickListener { _, _, position ->
            h5044GwListAdapter.data[position].run {
                if (this.isSelected) {
                    return@setOnItemClickListener
                }
                val supportBindH5112 = Constant4L5.supportH51124H5044(this.gatewayModel.device.versionHard, this.gatewayModel.device.versionSoft)
                if (!supportBindH5112) {
                    return@setOnItemClickListener
                }
            }
            for ((index, gwInfo) in h5044GwListAdapter.data.withIndex()) {
                val isSelected = index == position
                if (isSelected) {
                    selectGw = gwInfo
                }
                gwInfo.isSelected = isSelected
            }
            h5044GwListAdapter.notifyDataSetChanged()
        }
    }

    private fun initOpClick() {
        viewBinding.run {
            tvBindBtn4H5112BindGw.clickDelay {
                //执行绑定网关
                if (h5044Gws.isNotEmpty()) {
                    if (selectGw != null) {
                        showLoading()
                        vm4bindGw.bindH5112ToH5044(selectGw!!) {
                            hideLoading()
                            toast(if (it) R.string.temhum_bind_suc else R.string.h512x_bind_failure)
                            if (it) {
                                //跳转至H5112详情页
                                val arguments = Bundle().apply {
                                    putParcelable(ThConsV1.KEY_4_ADD_INFO, addInfo)
                                }
                                DeviceListConfig.read().getDeviceByKey(addInfo.sku, addInfo.device)?.deviceExt?.deviceSettings?.run {
                                    val ext4Other: Ext4Gw? = JsonUtil.fromJson(this, Ext4Gw::class.java)?.apply {
                                        this.gatewayInfo?.isIotOnLine = true
                                    }
                                    JsonUtil.fromJson(this, Ext4H5112::class.java)?.sno?.let { sno ->
                                        ext4Other?.gatewayInfo?.sno = sno
                                    }
                                    ext4Other?.gatewayInfo?.let { gwInfo ->
                                        arguments.putParcelable(ThConsV1.KEY_4_GATEWAY_INFO, gwInfo)
                                    }
                                    JumpUtil.jumpWithBundle(this@Ac4H5112BindGwGuide, Ac4H5112Detail::class.java, arguments)
                                    //关闭本页面
                                    finish()
                                }
                            }
                        }
                        //统计
                        recordUseCount(addInfo.sku, ParamFixedValue.click_bind_gateway)
                    }
                } else {
                    toBleScanAc()
                    //统计
                    recordUseCount(addInfo.sku, ParamFixedValue.click_add_gateway)
                }
            }
            tvNoBindBtn4H5112BindGw.clickDelay {
                //不去绑定网关则直接跳转到详情页
                intent.extras?.let {
                    JumpUtil.jumpWithBundle(this@Ac4H5112BindGwGuide, Ac4H5112Detail::class.java, true, it)
                    //统计
                    recordUseCount(addInfo.sku, ParamFixedValue.click_no_bind_gateway)
                }
            }
        }
    }

    @SuppressLint("NotifyDataSetChanged")
    private fun initObserver() {
        vm4bindGw.ld4H5044GwList.observe(this) {
            hideLoading()
            this.h5044Gws.clear()
            this.h5044Gws.addAll(it)
            refreshBindView(it.isNotEmpty())
        }
        vm4bindGw.ld4AdapterFresh.observe(this) {
            h5044GwListAdapter.notifyDataSetChanged()
        }
        vm4bindGw.ld4AddH5112Full.observe(this) {
            if (it.first) {
                showAddH5112FullRemindBy915()
                return@observe
            }
            if (it.second) {
                showAddH5112FullRemindByTh()
                return@observe
            }
        }
    }

    /**
     * H5044添加温湿度计设备已满
     */
    private fun showAddH5112FullRemindByTh() {
        val remindContent = ResUtil.getString(R.string.h5044_text_4_max_th_add_remind)
        val btnText = ResUtil.getString(R.string.hint_done_got_it)
        HintDialog1.showHintDialog1(this, remindContent, btnText)
        //统计
        recordUseCount(Constant4L5.H5044, ParamFixedValue.th_show_over_limit_dialog)
    }

    /**
     * H5044添加915通讯设备已满
     */
    private fun showAddH5112FullRemindBy915() {
        val remindContent = ResUtil.getString(R.string.h5112_text_4_bind_915_max_remind)
        val btnText = ResUtil.getString(R.string.hint_done_got_it)
        HintDialog1.showHintDialog1(this, remindContent, btnText)
        //统计
        recordUseCount(Constant4L5.H5044, ParamFixedValue.show_over_limit_dialog_915)
    }

    private fun refreshBindView(hasH5044Gw: Boolean) {
        viewBinding.run {
            if (hasH5044Gw) {
                for (gwModel in h5044Gws) {
                    val supportBindH5112 = Constant4L5.supportH51124H5044(gwModel.gatewayModel.device.versionHard, gwModel.gatewayModel.device.versionSoft)
                    if (supportBindH5112) {
                        gwModel.isSelected = true
                        selectGw = gwModel
                        break
                    }
                }
                tvBindBtn4H5112BindGw.setText(R.string.app_bind_to_gw)
                tvBindBtn4H5112BindGw.alpha = if (selectGw != null) 1.0f else 0.3f
                tvBindBtn4H5112BindGw.isEnabled = selectGw != null
                h5044GwListAdapter.setList(h5044Gws)
                rvGwList4H5112BindGw.setVisibility(true)
                tvTips14H5112BindGw.setVisibility(false)
                tvTips24H5112BindGw.setVisibility(true)
            } else {
                tvBindBtn4H5112BindGw.setText(R.string.app_to_add_gw)
                tvBindBtn4H5112BindGw.alpha = 1.0f
                tvBindBtn4H5112BindGw.isEnabled = true
                tvTips14H5112BindGw.setVisibility(true)
                tvTips24H5112BindGw.setVisibility(false)
                rvGwList4H5112BindGw.setVisibility(false)
            }
        }
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.thnew.R.id.ac_container
    }

    override fun adapterInsetViewId(): Int {
        return com.govee.thnew.R.id.sp_top_holder_4_h5112_bind_gw
    }

    override fun layoutId(): Int {
        return com.govee.thnew.R.layout.thnew_ac_4_h5112_bind_gateway_guide
    }

    private fun toBleScanAc() {
        //先断开蓝牙
        BleController.getInstance().disconnectBleAndNotify()
        //进入蓝牙扫描界面；需要关闭主界面蓝牙广播
        EventBleBroadcastListenerTrigger.sendEventBleBroadcastListenerTrigger(false)
        //跳转至设备扫描页
        val bundle = Bundle()
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_NEED_LOCATION, true)
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG, true)
        bundle.putString(BaseBleDeviceChooseActivity.INTENT_KEY_SKU_NAME, Constant4L5.H5044)
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG_HINT, true)
        bundle.putInt(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG_HINT_DES, R.string.h5074_more_device_5074_hint_des)
        bundle.putInt(BaseBleDeviceChooseActivity.INTENT_KEY_DEVICE_FLASHING_RSSI_VALUE, ConsV1.ble_ssid_compare_value)
        JumpUtil.jump(this, BaseBleDeviceChooseActivity::class.java, bundle)
    }

    @SuppressLint("MissingSuperCall")
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        //不允许物理按键返回
    }

    /**
     * 网关列表适配器
     */
    inner class Adapter4H5044GwList : BaseQuickAdapter<Gateway, BaseViewHolder>(com.govee.thnew.R.layout.thnew_item_4_h5112_bind_gw) {

        @SuppressLint("NotifyDataSetChanged")
        override fun convert(holder: BaseViewHolder, item: Gateway) {

            DataBindingUtil.bind<ThnewItem4H5112BindGwBinding>(holder.itemView)?.run {
                item.gatewayModel.let { gwModel ->
                    gwModel.ui.uiCheck(gwModel.bleOnline(), gwModel.iotStatus())
                    gwModel.uiFreshCallback = {
                        if (it.uiFresh()) {
                            globalLaunch(Dispatchers.Main) {
                                notifyDataSetChanged()
                            }
                        }
                    }
                    val supportBindH5112 = Constant4L5.supportH51124H5044(gwModel.device.versionHard, gwModel.device.versionSoft)
                    tvVersionLowRemind4H5044Gw.setVisibility(!supportBindH5112)
                    this.root.alpha = if (supportBindH5112) 1.0f else 0.3f
                    this.root.isEnabled = supportBindH5112
                }
                val device = item.gatewayModel.device
                ThemeM.showSku(ivIcon4H5044Gw, device.sku, ThemeM.getDefSkuRes(device.sku))
                tvName4H5044Gw.text = device.deviceName
                ivBleIcon4H5044Gw.visibleByBoolean(item.bleInRange())
                val status = item.iotStatus()
                ivWifiIcon4H5044Gw.visibleByBoolean(status != AbsGatewayModel.iot_no_visible)
                ivWifiIcon4H5044Gw.setImageDrawable(ResUtil.getDrawable(if (status == AbsGatewayModel.iot_offline) R.mipmap.new_home_icon_wifi_un else R.mipmap.new_home_icon_wifi_04))
                clContentBg4H5044Gw.isSelected = item.isSelected
                ivSelectIcon4H5044Gw.isSelected = item.isSelected
            }
        }
    }
}