package com.govee.thnew.ble.controller

import com.govee.base2home.Constant4L5
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4CloseWarnH5112

/**
 * <AUTHOR>
 * @date created on 2025/7/4
 * @description H5112-->关闭温湿度报警的Controller
 */
class Controller4CloseWarnH5112 : AbsControllerWithCallback {

    private var relieveType = 1
    private var isRelieve = false
    private var closeType = 1
    private var pauseTime = 0//小时

    /**
     * 写操作
     */
    constructor(probeIndex: Int, isTem: <PERSON><PERSON>an, isRelieve: <PERSON>olean, writeCallBack: ((result: Boolean) -> Unit)) : super(writeCallBack) {
        when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                relieveType = if (isTem) {
                    1
                } else {
                    3
                }
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                relieveType = 2
            }

            else -> {}
        }
        this.isRelieve = isRelieve
        if (isRelieve) {
            closeType = 1
            pauseTime = 0
        } else {
            closeType = 2
            pauseTime = 1
        }
    }

    override fun getCommandType(): Byte {
        return BleProtocol.COMMAND_TYPE_4_RELIEVE_WARN
    }

    override fun translateWrite(): ByteArray {
        return byteArrayOf(relieveType.toByte(), closeType.toByte(), pauseTime.toByte())
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        Event4CloseWarnH5112.sendSuc4Write(isWrite, commandType, proType, suc, isRelieve)
        return super.onWriteResult(suc)
    }

    override fun fail() {
        Event4CloseWarnH5112.sendFail(isWrite, commandType, proType)
    }
}