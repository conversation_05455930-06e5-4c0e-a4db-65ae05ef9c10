package com.govee.thnew.pact

import android.bluetooth.BluetoothDevice
import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.annotation.Keep
import com.govee.base2home.Constant4L5
import com.govee.base2home.config.DeviceRoomOrderConfig
import com.govee.base2home.iot.Cmd
import com.govee.base2home.iot.protype.v2.IotMsgV2
import com.govee.base2home.main.AbsDevice
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.pact.Protocol
import com.govee.base2home.qa.QaSkipEvent
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.theme.ThemeM
import com.govee.base2home.util.NumberUtil
import com.govee.base2home.util.StrUtil
import com.govee.base2home.util.TemUtil
import com.govee.base2home.util.TimeFormatM
import com.govee.base2home.util.UIUtil
import com.govee.base2kt.utils.BleUtils
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.ThUtil
import com.govee.base2newth.deviceitem.AbsModel4Th
import com.govee.base2newth.deviceitem.AbsThBleInRange
import com.govee.base2newth.deviceitem.AbsThItem4Ble
import com.govee.base2newth.deviceitem.AbsThItem4BleWithGw
import com.govee.base2newth.deviceitem.AbsThModel4BleWithGw
import com.govee.base2newth.deviceitem.AbsThWifiStatus4GwSubDevice
import com.govee.base2newth.deviceitem.Ext
import com.govee.base2newth.deviceitem.Ext4Gw
import com.govee.base2newth.deviceitem.GwIot4StatusCommand
import com.govee.base2newth.deviceitem.Item3
import com.govee.base2newth.deviceitem.Range
import com.govee.base2newth.deviceitem.ThHolder
import com.govee.base2newth.net.Request4Setting
import com.govee.base2newth.other.DevicePactCheckOp
import com.govee.kt.ui.device.DevicesOp
import com.govee.kt.ui.device.Event4ItemUi
import com.govee.kt.ui.device.GwH5151Op
import com.govee.kt.ui.device.IDeviceItem
import com.govee.kt.ui.device.IItem
import com.govee.kt.ui.device.IMultiItem
import com.govee.kt.ui.device.IRegister4DeviceItem
import com.govee.kt.ui.device.ISubLib
import com.govee.kt.ui.device.Item
import com.govee.kt.ui.device.base.AbsBleInRange
import com.govee.kt.ui.device.base.AbsWifiStatus4GwSubDevice
import com.govee.kt.ui.device.base.AbsWifiStatus4GwSubDevice.Companion.SUB_DEVICE_PARSE_IOT_STATUS_OFFLINE
import com.govee.kt.ui.device.base.AbsWifiStatus4GwSubDevice.Companion.SUB_DEVICE_PARSE_IOT_STATUS_ONLINE
import com.govee.kt.ui.device.base.AbsWifiStatus4GwSubDevice.Companion.SUB_DEVICE_PARSE_IOT_STATUS_UNKNOW
import com.govee.kt.ui.device.base.IGwSubDeviceItemOpImp
import com.govee.kt.ui.device.base.IModel
import com.govee.kt.ui.device.util.DeviceListEditManager
import com.govee.mvvm.ext.request4Scope
import com.govee.mvvm.globalLaunch
import com.govee.thnew.add.AddInfo
import com.govee.thnew.config.ThConfig4Detail
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.net.thNewNetService
import com.govee.thnew.pact.h5112.Dialog4H5112CloseWarn
import com.govee.thnew.pact.h5112.Ext4H5112
import com.govee.thnew.pact.h5112.Holder4H5112
import com.govee.thnew.pact.h5112.Item31
import com.govee.thnew.push.LocalWarningImp
import com.govee.thnew.ui.detail.Ac4ThCommonDetail
import com.govee.thnew.ui.detail.Ac4ThCommonDetailNew
import com.govee.thnew.ui.detail.h5112.Ac4H5112Detail
import com.govee.ui.R
import com.ihoment.base2app.ext.isFalse
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay

object Register4Item : IRegister4DeviceItem {
    override fun register() {
        //注册Holder-注册过一次即可
        DevicesOp.registerDeviceHolder(ThHolder::class.java) {
            ThHolder()
        }
        DevicesOp.registerDeviceHolder(Holder4H5112::class.java) {
            Holder4H5112()
        }
        DevicesOp.register(mutableListOf<ISubLib>().apply {
            add(ItemTransform())
            add(ItemTransform4H5112())
        })
        //注册为类H5151网关的子设备
        GwH5151Op.registerOp(GwSubDeviceItemImp())
    }
}

/**
 * 温湿度计首页卡片的通用Item类
 */
class DeviceItem4ThCommon(device: AbsDevice) : AbsThItem4BleWithGw(device) {

    init {
        device.parsePact()
    }

    override fun makeModel(): IModel = Model4ThCommon(device)

    override fun fahValueSet(): IntArray {
        val temRangePair = ThConfig4Detail.getTemRange(device.goodsType, device.sku, device.versionSoft, device.versionHard)
        return intArrayOf(TemUtil.getTemF(temRangePair.first * 1.0f), TemUtil.getTemF(temRangePair.second * 1.0f))
    }

    override fun celValueSet(): IntArray {
        val temRangePair = ThConfig4Detail.getTemRange(device.goodsType, device.sku, device.versionSoft, device.versionHard)
        return intArrayOf(temRangePair.first, temRangePair.second)
    }

    override fun parseScanRecord(scanRecord: ByteArray, bt: BluetoothDevice): Boolean {
        val model = iModel as? Model4ThCommon ?: return false
        return model.parseScanRecord(scanRecord, bt)
    }

    override fun parseItemUi(device: AbsDevice): IItem {
        return when (device.sku) {
            Constant4L5.H5112 -> {
                Item31().apply {
                    syncDevice(device)
                }
            }

            else -> {
                super.parseItemUi(device)
            }
        }
    }

    override fun checkItemUi4Other(): Boolean? {
        when (device.sku) {
            Constant4L5.H5112 -> {
                if (device.sku != Constant4L5.H5112) {
                    return null
                }
                val model = iModel as? Model4ThCommon ?: return null
                val item = item4Ui as? Item31 ?: return null
                var change = false
                //电池
                if (item.checkBatteryResId(UIUtil.getBatteryRes2(model.ext.battery))) {
                    change = true
                }
                //蓝牙图标
                if (item.checkBleVis(if (model.needBleIcon()) model.btInRange() else false)) {
                    change = true
                }
                //wifi(+信号)图标
                if (item.checkWifiStatus(if (model.needIotIcon()) model.wifiStatus() else Item.WIFI_STATUS_UNDEFINED)) {
                    change = true
                }
                model.getSignalRes()?.let {
                    if (item.signalResId(it)) {
                        change = true
                    }
                }
                //探针1数据有效性
                SafeLog.i(TAG) { "DeviceItem4ThCommon--checkItemUi4Other-->key:${model.device.key},lastData:${JsonUtil.toJson(model.lastData)}" }
                val noThData4Pb1 = model.lastData.noTHData(hadPm = false, isMainSubDev4Sub = false)
                SafeLog.i(TAG) { "DeviceItem4ThCommon--checkItemUi4Other-->noThData4Pb1 = $noThData4Pb1" }
                //刷新探针1温度信息
                val fahValueSet = fahValueSet()
                val celValueSet = celValueSet()
                val fahOpen = TemUnitConfig.read().isTemUnitFah(device.sku, device.device, device.versionHard, device.versionSoft)
                val temMin = if (fahOpen) fahValueSet[0] else celValueSet[0]
                val temMax = if (fahOpen) fahValueSet[1] else celValueSet[1]
                var warnTemMin =
                    if (fahOpen) ThUtil.temRangeCel2Fah4Showing(model.ext.temMin)
                    else ThUtil.temRangeCel2Cel4Showing(model.ext.temMin)
                warnTemMin = NumberUtil.getValidRangeValue(warnTemMin, temMin, temMax)
                var warnTemMax =
                    if (fahOpen) ThUtil.temRangeCel2Fah4Showing(model.ext.temMax)
                    else ThUtil.temRangeCel2Cel4Showing(model.ext.temMax)
                warnTemMax = NumberUtil.getValidRangeValue(warnTemMax, temMin, temMax)
                val warnTemMinStr = warnTemMin.toString() + if (fahOpen) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
                val warnTemMaxStr = warnTemMax.toString() + if (fahOpen) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
                val realTemStr: String
                val isWarningTem: Boolean
                if (noThData4Pb1) {
                    isWarningTem = false
                    realTemStr = ResUtil.getString(R.string.h5072_chart_text_def) + " " + if (fahOpen) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
                } else {
                    val tem = NumberUtil.getTemValue(fahOpen, model.lastData.tem, model.ext.temCali)
                    realTemStr = NumberUtil.getValidFloatByOnePoint(tem).toString() + " " + if (fahOpen) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
                    isWarningTem = (NumberUtil.getValidFloatByOnePoint(tem) < warnTemMin) || (NumberUtil.getValidFloatByOnePoint(tem) > warnTemMax)
                }
                if (item.checkTem(realTemStr, isWarningTem, Range().apply {
                        this.rangeReset(
                            0,
                            0,
                            warnTemMinStr,
                            warnTemMaxStr,
                            0,
                            !noThData4Pb1
                        )
                    })) {
                    change = true
                }
                //刷新探针1湿度信息
                val humValueSet = humValueSet()
                val humMin = humValueSet[0]
                val humMax = humValueSet[1]
                var warnHumMin = NumberUtil.getValueUpward(model.ext.humMin, true)
                warnHumMin = NumberUtil.getValidRangeValue(warnHumMin, humMin, humMax)
                var warnHumMax = NumberUtil.getValueUpward(model.ext.humMax, false)
                warnHumMax = NumberUtil.getValidRangeValue(warnHumMax, humMin, humMax)
                val warnHumMinStr = warnHumMin.toString() + StrUtil.getHumUnit()
                val warnHumMaxStr = warnHumMax.toString() + StrUtil.getHumUnit()
                val realHumStr: String
                val isWarningHum: Boolean
                if (noThData4Pb1) {
                    isWarningHum = false
                    realHumStr = ResUtil.getString(R.string.h5072_chart_text_def) + " " + StrUtil.getHumUnit()
                } else {
                    val hum = NumberUtil.getHumValue(model.lastData.hum, model.ext.humCali)
                    realHumStr = NumberUtil.getValidFloatByOnePoint(hum).toString() + " " + StrUtil.getHumUnit()
                    isWarningHum = (hum < warnHumMin) || (hum > warnHumMax)
                }
                if (item.checkHum(realHumStr, isWarningHum, Range().apply {
                        this.rangeReset(
                            0,
                            0,
                            warnHumMinStr,
                            warnHumMaxStr,
                            0,
                            !noThData4Pb1
                        )
                    })) {
                    change = true
                }
                //探针2数据有效性
                val noThData4Pb2 = (!ThConsV1.isValidThValue(model.lastData.pm, 6000)) || (model.lastData.lastTime <= 0)
                SafeLog.i(TAG) { "DeviceItem4ThCommon--checkItemUi4Other-->noThData4Pb2 = $noThData4Pb2" }
                //刷新探针2温度信息
                val temMin2 = if (fahOpen) fahValueSet[0] else celValueSet[0]
                val temMax2 = if (fahOpen) fahValueSet[1] else celValueSet[1]
                var warnTem2Min =
                    if (fahOpen) ThUtil.temRangeCel2Fah4Showing(model.ext.temMin2 ?: 0)
                    else ThUtil.temRangeCel2Cel4Showing(model.ext.temMin2 ?: 0)
                warnTem2Min = NumberUtil.getValidRangeValue(warnTem2Min, temMin2, temMax2)
                var warnTem2Max =
                    if (fahOpen) ThUtil.temRangeCel2Fah4Showing(model.ext.temMax2 ?: 0)
                    else ThUtil.temRangeCel2Cel4Showing(model.ext.temMax2 ?: 0)
                warnTem2Max = NumberUtil.getValidRangeValue(warnTem2Max, temMin2, temMax2)
                val warnTem2MinStr = warnTem2Min.toString() + if (fahOpen) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
                val warnTem2MaxStr = warnTem2Max.toString() + if (fahOpen) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
                val realTem2Str: String
                val isWarningTem2: Boolean
                if (noThData4Pb2) {
                    isWarningTem2 = false
                    realTem2Str = ResUtil.getString(R.string.h5072_chart_text_def) + " " + if (fahOpen) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
                } else {
                    val tem2 = NumberUtil.getTemValue(fahOpen, model.lastData.pm, model.ext.temCali2 ?: 0)
                    realTem2Str = NumberUtil.getValidFloatByOnePoint(tem2).toString() + " " + if (fahOpen) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
                    isWarningTem2 = (NumberUtil.getValidFloatByOnePoint(tem2) < warnTem2Min) || (NumberUtil.getValidFloatByOnePoint(tem2) > warnTem2Max)
                }
                if (item.checkTem2(realTem2Str, isWarningTem2, Range().apply {
                        this.rangeReset(
                            0,
                            0,
                            warnTem2MinStr,
                            warnTem2MaxStr,
                            0,
                            !noThData4Pb2
                        )
                    })) {
                    change = true
                }
                //时间
                val timeStr = TimeFormatM.getInstance().formatTimeToHMMD(model.lastData.lastTime)
                val timeMills = if (noThData4Pb1 && noThData4Pb2) "" else timeStr
                if (item.checkTimeMillStr(timeMills)) {
                    change = true
                }
                //探针1的icon、名称
                val pb1IconIndex = model.ext.pb1IconIndex ?: 1
                if (item.checkPb1Icon(pb1IconIndex)) {
                    change = true
                }
                val pb1Name = model.ext.probeName1 ?: ""
                if (item.checkPb1Name(pb1Name)) {
                    change = true
                }
                //探针2的icon、名称
                val pb2IconIndex = model.ext.pb2IconIndex ?: 1
                if (item.checkPb2Icon(pb2IconIndex)) {
                    change = true
                }
                val pb2Name = model.ext.probeName2 ?: ""
                if (item.checkPb2Name(pb2Name)) {
                    change = true
                }
                return change
            }

            else -> {
                return null
            }
        }
    }

    override fun otherParamsCheck(item: Item3, model: AbsThModel4BleWithGw): Boolean {
        val realModel4ThCommon = model as? Model4ThCommon ?: return super.otherParamsCheck(item, model)
        if (!ThConfig4Support.supportOnly433(device.goodsType, device.sku)) {
            return false
        }
        //如H5053这样的支持通过433协议与网关通讯的设备的在线文案提示
        var change = false
        if (item.checkOrder(realModel4ThCommon.sub433index.header ?: "")) {
            change = true
        }
        if (item.checkHintStr(
                realModel4ThCommon.hintStr(),
                realModel4ThCommon.hintStrColor()
            )
        ) {
            change = true
        }
        val offline = !realModel4ThCommon.lastData.gwonline
        if (item.checkSpecialOffline(offline)) {
            change = true
        }
        if (item.checkBleVis(null)) {
            change = true
        }
        return change
    }

    override fun onChildViewClick(v: View) {
        super.onChildViewClick(v)
        when (device.sku) {
            Constant4L5.H5112 -> {
                when (v.id) {
                    com.govee.thnew.R.id.iv_qa_icon_4_h5112_card -> {
                        QaSkipEvent.sendQaSkipEvent(device.key)
                    }
                }
            }

            else -> {}
        }
    }

    override fun needBle(): Boolean {
        return !ThConfig4Support.supportOnly433(device.goodsType, device.sku)
    }

    override fun needLocationRp(): Boolean {
        return !ThConfig4Support.supportOnly433(device.goodsType, device.sku)
    }

    override fun useLinkIcon(): Boolean {
        return Constant4L5.isH5151SubSku(device.sku)
    }

    override fun needPush(): Boolean {
        return ThConfig4Support.supportPush(device.goodsType, device.sku)
    }

    override fun checkIotOnline() {
        val model4ThCommon = iModel as? Model4ThCommon ?: return
        model4ThCommon.checkIotOnline()
    }

    override fun stopIotTimer4Status() {
        val model4ThCommon = iModel as? Model4ThCommon ?: return
        model4ThCommon.stopIotTimer4Status()
    }

    override fun quickIotOnlineCheck() {
        val model4ThCommon = iModel as? Model4ThCommon ?: return
        model4ThCommon.quickIotOnlineCheck()
    }
}

/**
 * 433通讯子设备的序号
 */
@Keep
class OrdinalIndex4Sub433 {
    var header: String? = null
}

/**
 * 主+从设备的序号类
 */
@Keep
class Ext4Multi {
    //角色-1主设备;2-从设备
    var groupRole = ThConsV1.GROUP_ROLE_MAIN

    //序号
    var groupOrder = 0
}

/**
 * 温湿度计首页卡片的通用信息实体类
 */
class Model4ThCommon(device: AbsDevice) : AbsThModel4BleWithGw(device) {

    var sub433index: OrdinalIndex4Sub433 = OrdinalIndex4Sub433()
    var ext4Multi: Ext4Multi = Ext4Multi()
    var btOnline: Boolean = false
    private var lastProtocol: Protocol? = null

    override fun syncDevice(newDevice: AbsDevice) {
        super.syncDevice(newDevice)
        device.parsePact()
        if (device.pactType <= 0 || device.pactCode <= 0) {
            lastProtocol?.let {
                device.pactType = it.pactType
                device.pactCode = it.pactCode
            }
        }
        device.deviceExt?.deviceSettings?.run {
            JsonUtil.fromJson(this, OrdinalIndex4Sub433::class.java)?.let {
                sub433index = it
            }

            JsonUtil.fromJson(this, Ext4Multi::class.java)?.let {
                ext4Multi = it
            }
        }
    }

    override fun syncExt(extNew: Ext) {
        super.syncExt(extNew)
        when (device.sku) {
            Constant4L5.H5112 -> {
                if (btInRange()) {
                    //蓝牙广播在线，但本地探针的图标icon与服务端的不一致，表示发生了变化，但是没有更新到服务端，则同步一下
                    val pbIconIndexArr = arrayOf<Int?>(null, null)
                    ext.pb1IconIndex?.let {
                        if (it != extNew.pb1IconIndex) {
                            extNew.pb1IconIndex = it
                            pbIconIndexArr[0] = it
                        }
                    }
                    ext.pb2IconIndex?.let {
                        if (it != extNew.pb2IconIndex) {
                            extNew.pb2IconIndex = it
                            pbIconIndexArr[1] = it
                        }
                    }
                    pbIconIndexArr.forEach {
                        if (it != null) {
                            syncPbIcon(pbIconIndexArr[0], pbIconIndexArr[1])
                            return
                        }
                    }
                }
            }

            else -> {}
        }
    }

    /**
     * 同步探针图标
     * 备注：需同步则会有值，null标识无需同步
     */
    private fun syncPbIcon(pb1IconIndex: Int?, pb2IconIndex: Int?) {
        globalLaunch {
            val request = Request4Setting(Transactions().createTransaction(), device.sku, device.device).apply {
                pb1IconIndex?.let {
                    this.pb1IconIndex = it
                }
                pb2IconIndex?.let {
                    this.pb2IconIndex = it
                }
            }
            request4Scope({ thNewNetService.updateSettings(request) }, success = {
                DeviceListConfig.read().getDeviceByKey(device.sku, device.device)?.let { absDevice ->
                    JsonUtil.parseToHashmap(absDevice.deviceExt?.deviceSettings ?: "")?.let { settingMap ->
                        JsonUtil.parseToHashmap(JsonUtil.toJson(ext))?.let { lastInfoMap ->
                            for (key in settingMap.keys) {
                                lastInfoMap[key]?.let { newValue ->
                                    settingMap[key] = newValue
                                }
                            }
                        }
                        DeviceRoomOrderConfig.read().changeDevice(device.sku, device.device, JsonUtil.toJson(settingMap))
                    }
                }
            })
        }
    }

    /**
     * 信号图标icon
     */
    fun getSignalRes(): Int? {
        when (device.sku) {
            Constant4L5.H5112 -> {
                if (gwOnline().isFalse()) {
                    //不在线图标
                    return UIUtil.getSignalResV1(ThConsV1.INVALID_SIGNAL_VALUE)
                } else if (gwOnline().isTrue()) {
                    return if (isSubOnline) {
                        UIUtil.getSignalResV1(lastData.signal ?: -1)
                    } else {
                        //不在线图标
                        UIUtil.getSignalResV1(ThConsV1.INVALID_SIGNAL_VALUE)
                    }
                }
            }

            else -> {}
        }
        return null
    }

    override fun jump2Adjust(context: Context) {
//        /**注册App版本过低的响应类*/
//        if (!ThConfig4Support.supportPact(device.goodsType, device.sku, Protocol(device.pactType, device.pactCode))) {
//            AppOlderDialog.showAppOlderDialogWithErrorMsg(context, DevicePactCheckOp.appOldReasonMsg(device)) {}
//            AnalyticsRecorder.getInstance().recordUseCount(device.sku, ParamFixedValue.device_into_detail_excetion)
//            return
//        }
        val bindExt = JsonUtil.fromJson(device.deviceExt.deviceSettings ?: "", AddInfo::class.java) ?: AddInfo()
        bindExt.goodsType = device.goodsType
        bindExt.sku = device.sku
        bindExt.device = device.device
        bindExt.bleHardVersion = device.versionHard
        bindExt.bleSoftVersion = device.versionSoft
        val arguments = Bundle().apply {
            putParcelable(ThConsV1.KEY_4_ADD_INFO, bindExt)
        }
        when (bindExt.sku) {
            Constant4L5.H5112 -> {
                ext4Other.gatewayInfo?.let {
                    it.isIotOnLine = isSubOnline
                    JsonUtil.fromJson(device.deviceExt.deviceSettings ?: "", Ext4H5112::class.java)?.sno?.let { sno4H5112 ->
                        it.sno = sno4H5112
                        it.signal = lastData.signal
                    }
                    arguments.putParcelable(ThConsV1.KEY_4_GATEWAY_INFO, it)
                }
                JumpUtil.jumpWithBundle(context, Ac4H5112Detail::class.java, arguments)
            }

            else -> {
                ext4Other.gatewayInfo?.let {
                    it.isIotOnLine = isSubOnline
                    arguments.putParcelable(ThConsV1.KEY_4_GATEWAY_INFO, it)
                }
                if (ThConfig4Detail.useNewDetailPage(bindExt.goodsType, bindExt.sku)) {
                    JumpUtil.jumpWithBundle(context, Ac4ThCommonDetailNew::class.java, arguments)
                } else {
                    JumpUtil.jumpWithBundle(context, Ac4ThCommonDetail::class.java, arguments)
                }
            }
        }
    }

    fun parseScanRecord(scanRecord: ByteArray, bt: BluetoothDevice): Boolean {
        if (ThConfig4Support.supportOnly433(device.goodsType, device.sku)) {
            return false
        }
        DevicePactCheckOp.checkPact4Bc(device, scanRecord, bt.address) {
            it?.let { curProtocol ->
                device.run {
                    pactType = curProtocol.pactType
                    pactCode = curProtocol.pactCode
//                    SafeLog.i("xiaobing") { "DeviceItem4ThCommon--parseScanRecord-->key:${key},${JsonUtil.toJson(Protocol(pactType, pactCode))}" }
                }
                //记录最新的协议
                lastProtocol = curProtocol
            }
        }
        val broadcastType = ThConfig4Support.broadcastType(device.goodsType, device.sku, device.device)
        val broadcast = when (broadcastType) {
            ThConfig4Support.BROADCAST_TYPE_V0 -> {
                ThBroadcastUtil.parseBroadcastV0(scanRecord)
            }

            ThConfig4Support.BROADCAST_TYPE_V1 -> {
                ThBroadcastUtil.parseBroadcastV1(scanRecord)
            }

            ThConfig4Support.BROADCAST_TYPE_V2 -> {
                ThBroadcastUtil.parseBroadcastV2(scanRecord)
            }

            ThConfig4Support.BROADCAST_TYPE_V3 -> {
                ThBroadcastUtil.parseBroadcastV3(scanRecord)
            }

            ThConfig4Support.BROADCAST_TYPE_V4 -> {
                ThBroadcastUtil.parseBroadcastV4(scanRecord)
            }

            ThConfig4Support.BROADCAST_TYPE_V6 -> {
                ThBroadcastUtil.parseBroadcastV6(scanRecord)
            }

            else -> {
                null
            }
        } ?: return false
        when (device.sku) {
            Constant4L5.H5112 -> {
                if ((broadcastType == ThConfig4Support.BROADCAST_TYPE_V6) && (broadcast.size == 9)) {
                    when (broadcast[broadcast.lastIndex - 1]) {
                        Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                            lastData.run {
                                this.lastTime = System.currentTimeMillis()
                                this.tem = broadcast[2]
                                this.hum = broadcast[3]
                            }
                            ext.pb1IconIndex = broadcast.last()
                        }

                        Constant4L5.PROBE_INDEX_4_TEM -> {
                            lastData.run {
                                this.lastTime = System.currentTimeMillis()
                                this.pm = broadcast[2]
                            }
                            ext.pb2IconIndex = broadcast.last()
                        }

                        else -> {
                            return false
                        }
                    }
                }
            }

            else -> {
                lastData.run {
                    this.lastTime = System.currentTimeMillis()
                    this.tem = broadcast[2]
                    this.hum = broadcast[3]
                }
            }
        }
        //限定电量的有效值为0-100
        if (broadcast[4] in 0..100) {
            ext.run {
                val batteryChange = this.battery != broadcast[4]
                this.battery = broadcast[4]
                if (batteryChange) {
                    <EMAIL>(broadcast[4])
                }
            }
        }
        //H5112的报警弹窗
        when (device.sku) {
            Constant4L5.H5112 -> {
                if ((broadcastType == ThConfig4Support.BROADCAST_TYPE_V6) && (broadcast.size == 9)) {
                    //温度是否正处于报警
                    val isTemWarning = broadcast[5] > 0
                    val isTemWarnHigh = broadcast[5] == 2
                    //湿度是否正处于报警
                    val isHumWarning = broadcast[6] > 0
                    val isHumWarnHigh = broadcast[6] == 2
                    if (isTemWarning) {
                        Dialog4H5112CloseWarn.showDialog(device.sku, device.device, broadcast[broadcast.lastIndex - 1], true, isTemWarnHigh, Pair(broadcast[2], broadcast[3]))
                    } else {
                        Dialog4H5112CloseWarn.hideDialog(device.sku, device.device, broadcast[broadcast.lastIndex - 1], true)
                    }
                    if (isHumWarning) {
                        Dialog4H5112CloseWarn.showDialog(device.sku, device.device, broadcast[broadcast.lastIndex - 1], false, isHumWarnHigh, Pair(broadcast[2], broadcast[3]))
                    } else {
                        Dialog4H5112CloseWarn.hideDialog(device.sku, device.device, broadcast[broadcast.lastIndex - 1], false)
                    }
                }
            }

            else -> {}
        }
        //告警推送，iot在线则有服务端推送,否则用本地告警
        if (!isSubOnline && ThConfig4Support.supportLocalPush(device.goodsType, device.sku)) {
            //如果有app提醒开关，需打开提醒开关才能push
            //低电量提醒，如果有低电量开关，也需先打开低电量开关
            var batteryWarning = true
            JsonUtil.fromJson(device.deviceExt.deviceSettings ?: "", Request4Setting::class.java)?.let {
                batteryWarning = !it.batteryWarning.isFalse()
                if (it.normalPushOnOff.isFalse()) {
                    return true
                }
            }
            when (device.sku) {
                Constant4L5.H5112 -> {
                    if ((broadcastType == ThConfig4Support.BROADCAST_TYPE_V6) && (broadcast.size == 9)) {
                        //温度是否正处于报警
                        val isTemWarning = broadcast[5] > 0
                        //湿度是否正处于报警
                        val isHumWarning = broadcast[6] > 0
                        val deviceId = when (broadcast[broadcast.lastIndex - 1]) {
                            Constant4L5.PROBE_INDEX_4_TEM -> {
                                ThConsV1.getH5112Device4Pb2(device.device)
                            }

                            else -> {
                                device.device
                            }
                        }
                        LocalWarningImp.getInstance.checkWarning(
                            device.sku,
                            deviceId,
                            broadcast[2],
                            isTemWarning,
                            broadcast[3],
                            isHumWarning,
                            if (batteryWarning) broadcast[4] else 100,
                            System.currentTimeMillis()
                        )
                    }
                }

                else -> {
                    var supportTem = true
                    val supportHum = ThConfig4Support.supportHum(device.goodsType, device.sku)
                    //有延时告警
                    if (broadcastType == ThConfig4Support.BROADCAST_TYPE_V3 && broadcast.size >= 6) {
                        supportTem = broadcast[5] == 1
                    }
                    LocalWarningImp.getInstance.checkWarning(
                        device.sku,
                        device.device,
                        broadcast[2],
                        supportTem,
                        broadcast[3],
                        supportHum,
                        if (batteryWarning) broadcast[4] else 100,
                        System.currentTimeMillis()
                    )
                }
            }
        }
        return true
    }

    override fun parseIotV2(msg: IotMsgV2, jsonStr: String) {
        val subIndex: Int = ext4Other.gatewayInfo?.index ?: 0
        if (subIndex <= 0) {
            when (device.sku) {
                Constant4L5.H5112 -> {
                    Ext4H5112.parseIot4H5112Sub(device, jsonStr)?.let {
//                        SafeLog.i("xiaobing") { "Model4ThCommon--parseIotV2-->${JsonUtil.toJson(it)}" }
                        isSubOnline = it.online.isTrue()
                        if (it.online.isTrue()) {
                            //信号值只从iot拿去，无需作时间判断
                            lastData.signal = it.signal
                            if ((lastData.lastTime < (it.updateTimeMills ?: 0))) {
                                lastData.lastTime = it.updateTimeMills ?: 0
                                //限定电量的有效值为0-100
                                it.battery?.let { bat ->
                                    if (bat in 1..100) {
                                        val batteryChange = ext.battery != bat
                                        if (batteryChange) {
                                            <EMAIL>(bat)
                                        }
                                        ext.battery = bat
                                    }
                                }
                                lastData.tem = it.tem4Pb1
                                lastData.hum = it.hum4Pb1
                                lastData.pm = it.tem4Pb2
                            }
                        }
                    }
                }

                else -> {}
            }
            return
        }
        when (msg.sku) {
            Constant4L5.H5151 -> {
                parseIot4H5151(subIndex, jsonStr)
            }

            Constant4L5.H5042,
            Constant4L5.H5043,
            Constant4L5.H5044,
                -> {
                parseIot4Common(subIndex, jsonStr)
            }

            else -> {}
        }
    }

    /**
     * H5151网关的iot数据解析
     */
    private fun parseIot4H5151(subIndex: Int, jsonStr: String) {
        val bytes = GwIot4StatusCommand.parse2Commands(jsonStr) ?: return
        if (bytes.isEmpty()) return
        val subNum = BleUtils.getUnsignedByte(bytes[0])
        if (subNum <= 0 || subIndex > subNum) {
            SafeLog.i("xiaobing") { "Model4ThCommon--parseIot4H5151-->subNum = $subNum ; subIndex = $subIndex,子设备数量与下标不对!" }
            return
        }
        //第一个字节为绑定的子设备个数，每个子设备10个字节数据 1byte 在线状态，1 byte电量，4 byte实时温湿度，4 byte 时间戳分钟
        val startIndex = 1 + (subIndex - 1) * 10
        if (startIndex >= bytes.size) {
            SafeLog.i("xiaobing") { "Model4ThCommon--parseIot4H5151-->子设备 ${device.deviceName} 的有效数据位置 不在返回的响应iot数据包中" }
            return
        }
        isSubOnline = BleUtils.getUnsignedByte(bytes[startIndex]) > 0
        //优先用蓝牙广播数据
        //若在线-则需要解析后续的相关信息
        if (startIndex + 10 < bytes.size && isSubOnline && !btInRange()) {
            val validBytes = BleUtils.byteList2ByteArray(bytes.subList(startIndex, startIndex + 10))
            parseSubDevInfo(validBytes)
        }
    }

    /**
     * 其他类H5151网关的iot数据解析
     */
    private fun parseIot4Common(subIndex: Int, jsonStr: String) {
        val validBytes = GwIot4StatusCommand.parse2Commands4Common(subIndex, jsonStr) ?: return
        if (validBytes.isEmpty()) return
        isSubOnline = validBytes[0].toUInt().toInt() == 1
        //优先用蓝牙广播数据
        //解析须先确认子设备在线
        if (isSubOnline && !btInRange()) {
            parseSubDevInfo(validBytes)
        }
    }

    /**
     * 解析子设备的具体信息
     * @param validBytes 10个字节的具体信息数据
     */
    private fun parseSubDevInfo(validBytes: ByteArray) {
        val timeBts = ByteArray(4)
        System.arraycopy(validBytes, 6, timeBts, 0, 4)
        val time = BleUtil.getSignedInt(timeBts)
        //比上一次蓝牙的数据新才更新
        if (lastData.lastTime / 60 / 1000 < time) {
//                SafeLog.i("xiaobing") { "Model4ThCommon--parseIotV2-->${device.key}-->${BleUtil.bytesToHexString(validBytes)}" }
            lastData.lastTime = time * 60 * 1000
            //限定电量的有效值为0-100
            validBytes[1].toInt().let {
                if (it in 0..100) {
                    val batteryChange = ext.battery != it
                    if (batteryChange) {
                        <EMAIL>(it)
                    }
                    ext.battery = it
                }
            }

            val temBytes = ByteArray(2)
            System.arraycopy(validBytes, 2, temBytes, 0, 2)
            lastData.tem = BleUtil.getSignedIntV2(temBytes, true)

            val humBytes = ByteArray(2)
            System.arraycopy(validBytes, 4, humBytes, 0, 2)
            lastData.hum = BleUtil.getSignedIntV2(humBytes, true)
        }
    }

    override fun isBle(): Boolean {
        return !ThConfig4Support.supportOnly433(device.goodsType, device.sku)
    }

    override fun isWifi(): Boolean {
        if (ThConfig4Support.supportWifi(device.goodsType, device.sku)) {
            return true
        }
        return super.isWifi()
    }

    override fun wifiStatus(): Int {
        if (device.sku == Constant4L5.B5178) {
            return Item.WIFI_STATUS_INVISIBLE
        }
        if (ThConfig4Support.supportWifi(device.goodsType, device.sku)) {
            if (isWifi()) return if (lastData.online) Item.WIFI_STATUS_ONLINE else Item.WIFI_STATUS_OFFLINE
            return Item.WIFI_STATUS_INVISIBLE
        }
        return super.wifiStatus()
    }

    /**
     * H5053等设备的状态文案
     */
    fun hintStr(): String {
        if (DeviceListEditManager.isNewStyle()) {
            if (!lastData.gwonline) return ResUtil.getString(R.string.app_device_offLine)
            if (!lastData.online) return ResUtil.getString(R.string.h7171_device_error_label)
        }
        if (!lastData.gwonline) return ResUtil.getString(R.string.h512x_offline_text)
        if (!lastData.online) return ResUtil.getString(R.string.temhum_5053_fault)
        return ""
    }

    fun hintStrColor(): Int {
        if (DeviceListEditManager.isClassicStyle()) {
            return ResUtil.getColor(R.color.font_style_73_5_textColor)
        }
        if (!lastData.gwonline) return ResUtil.getColor(R.color.font_style_77_1_textColor)
        if (!lastData.online) return ResUtil.getColor(R.color.font_style_73_5_textColor)
        return ResUtil.getColor(R.color.font_style_77_1_textColor)
    }
}

/**
 * 主+从款sku的首页卡片Item类
 * 备注：目前仅有B5178
 */
class DeviceItem4MainWithSub(device: AbsDevice) : AbsThItem4Ble(device), IMultiItem {
    //关联的主设备item
    private var mainDeviceItem: DeviceItem4MainWithSub? = null

    //关联的从设备
    private var subDeviceItem: DeviceItem4MainWithSub? = null

    override fun parseScanRecord(scanRecord: ByteArray, bt: BluetoothDevice): Boolean {
        val model4Th = iModel as? Model4ThCommon ?: return false
        val broadcast = ThBroadcastUtil.parseBroadcast4V5(scanRecord) ?: return false
        val groupOrder = model4Th.ext4Multi.groupOrder
        if (groupOrder != broadcast[2]) {
            if (groupOrder == 0) {
                //通知子设备解析
                subDeviceItem?.parseScanRecord(scanRecord, bt)
            } else {
                //通知主设备解析
                mainDeviceItem?.parseScanRecord(scanRecord, bt)
            }
            return false
        }
        val online = if (groupOrder == 0) {
            true
        } else {
            broadcast[6] == 0
        }
        val temHumValid = online && broadcast[4] > 0
        if (groupOrder == 0) {
            //主设备
            model4Th.lastData.run {
                if (temHumValid) {
                    this.lastTime = System.currentTimeMillis()
                    this.tem = broadcast[3]
                    this.hum = broadcast[4]
                }
                this.online = true
            }
            model4Th.ext.run {
                val batteryChange = this.battery != broadcast[5]
                this.battery = broadcast[5]
                if (batteryChange) {
                    model4Th.reportBattery(this.battery)
                }
            }
            //从设备要刷一下
            globalLaunch(Dispatchers.Main) {
                SafeLog.i(TAG) { "parseScanRecord() 延迟刷新从设备UI" }
                delay(200)
                Event4ItemUi.sendEvent(subDeviceItem?.device?.key ?: "", true)
            }
        } else {
            //从设备
            model4Th.lastData.run {
                if (temHumValid) {
                    this.lastTime = System.currentTimeMillis()
                    this.tem = broadcast[3]
                    this.hum = broadcast[4]
                }
                this.online = online
            }
            model4Th.ext.run {
                val batteryChange = this.battery != broadcast[5]
                this.battery = broadcast[5]
                if (batteryChange) {
                    model4Th.reportBattery(this.battery)
                }
            }
            //关联对应的主设备进行蓝牙解析
            mainDeviceItem?.linkMainDevice(bt)
        }
        model4Th.btOnline = online
        //本地告警逻辑处理;设备不在线，则无须执行告警逻辑
        if (online && ThConfig4Support.supportLocalPush(device.goodsType, device.sku)) {
            //如果有app提醒开关，需打开提醒开关才能push
            //低电量提醒，如果有低电量开关，也需先打开低电量开关
            var batteryWarning = true
            JsonUtil.fromJson(device.deviceExt.deviceSettings ?: "", Request4Setting::class.java)?.let {
                batteryWarning = !it.batteryWarning.isFalse()
                if (it.normalPushOnOff.isFalse()) {
                    return true
                }
            }
            LocalWarningImp.getInstance.checkWarning(
                device.sku,
                device.device,
                broadcast[3],
                true,
                broadcast[4],
                true,
                if (batteryWarning) broadcast[5] else 100,
                System.currentTimeMillis()
            )
        }
        return true
    }

    override fun syncDeviceUi() {
        super.syncDeviceUi()
        //更新默认sku图和链接
        val item = item4Ui as? Item3 ?: return
        val model = iModel as? Model4ThCommon ?: return
        item.defSkuRes = ThemeM.getDefSkuRes(device.sku, model.ext4Multi.groupOrder)
        item.skuUrl = ThemeM.getInstance.getSkuResourceV1(device.sku, device.spec, model.ext4Multi.groupOrder)?.skuUrl
    }

    override fun makeModel(): IModel = Model4ThCommon(device)

    override fun otherParamsCheck(item: Item3, model: AbsModel4Th): Boolean {
        val model4Th = model as? Model4ThCommon ?: return false
        var change = false
        val flagStatus = if (model4Th.ext4Multi.groupRole == ThConsV1.GROUP_ROLE_MAIN) Item3.FLAG_STATUS_INNER else Item3.FLAG_STATUS_OUTER
        if (item.checkFlagStatus(flagStatus)) {
            change = true
        }
        //从设备不显示蓝牙图标
        if (model4Th.ext4Multi.groupRole != ThConsV1.GROUP_ROLE_MAIN) {
            if (item.checkBleVis(null)) {
                change = true
            }
            var mainDeviceIsOffline = (mainDeviceItem?.item4Ui as? Item3)?.isOffline()
            val isClassicStyle = DeviceListEditManager.isClassicStyle()
            if (isClassicStyle) {
                //经典模式不管
                mainDeviceIsOffline = null
            }
            val textColor = if (mainDeviceIsOffline.isTrue()) {
                ResUtil.getColor(R.color.font_style_77_1_textColor)
            } else {
                ResUtil.getColor(R.color.font_style_73_5_textColor)
            }
            //子是否有故障或者离线
            if (item.checkHintStr(hintStr(mainDeviceIsOffline, !isClassicStyle), textColor)) {
                change = true
            }

        }
        return change
    }

    override fun linkItem(deviceItem: IDeviceItem) {
        val item = deviceItem as? DeviceItem4MainWithSub ?: return
        if (deviceItem.key4Multi() == this.key4Multi()) {
            if (deviceItem.order() == 0) {
                this.mainDeviceItem = item
            } else if (deviceItem.order() == 1) {
                this.subDeviceItem = item
            } else {
                SafeLog.i("xiaobing") { "DeviceItem4MainWithSub--linkItem-->关联主设备错误；order is error!" }
            }
        } else {
            SafeLog.i("xiaobing") { "DeviceItem4MainWithSub--linkItem-->关联主设备错误；key = ${deviceItem.key4Multi()}" }
        }
    }

    override fun key4Multi(): String {
        val model4Th = iModel as? Model4ThCommon ?: return ""
        return device.sku + "_" + model4Th.ext.address
    }

    override fun order(): Int {
        val model4Th = iModel as? Model4ThCommon ?: return -1
        return model4Th.ext4Multi.groupOrder
    }

    /**
     * B5178的状态文案
     */
    private fun hintStr(mainDeviceIsOffline: Boolean? = null, isNewStyle: Boolean): String {
        val model = iModel as? Model4ThCommon ?: return ""
        if (model.ext4Multi.groupOrder > 0) {
            //从设备
            if (mainDeviceIsOffline.isTrue()) {
                //主设备离线
                return ResUtil.getString(R.string.app_device_offLine)
            }
            if (!model.lastData.online) {
                //主设备在线，子设备通信中断
                return if (isNewStyle) {
                    ResUtil.getString(R.string.h7171_device_error_label)
                } else {
                    ResUtil.getString(R.string.temhum_5053_fault)
                }
            }
        }
        return ""
    }
}

/**
 * 数据对应model、item的转化注册类
 */
internal class ItemTransform : ISubLib {
    override fun transform(absDevice: AbsDevice): IDeviceItem {
        return if (ThConfig4Support.isMainSubDevice(absDevice.goodsType, absDevice.sku)) {
            DeviceItem4MainWithSub(absDevice)
        } else {
            DeviceItem4ThCommon(absDevice)
        }
    }

    override fun keys(): MutableList<String> {
        return mutableListOf<String>().apply {
            val deviceItemGoodsTypes = ThConfig4Support.deviceItemTypes
            for (deviceTypePair in deviceItemGoodsTypes) {
                val goodsType = deviceTypePair.first
                val sku = deviceTypePair.second
                if (goodsType != GoodsType.GOODES_TYPE_NO_SUPPORT) {
                    add(goodsType.toString())
                } else {
                    add(sku)
                }
            }
        }
    }

    override fun itemTypes(): MutableList<Int> {
        return Item.itemTypes3()
    }
}

internal class ItemTransform4H5112 : ISubLib {
    override fun transform(absDevice: AbsDevice): IDeviceItem {
        return DeviceItem4ThCommon(absDevice)
    }

    override fun keys(): MutableList<String> {
        return mutableListOf<String>().apply {
            add(GoodsType.GOODS_TYPE_VALUE_4_H5112.toString())
        }
    }

    override fun itemTypes(): MutableList<Int> {
        return Item.itemTypes31()
    }
}


/**
 * 网关子设备Item的实现类
 */
class GwSubDeviceItemImp : IGwSubDeviceItemOpImp {

    private val gwSubExtMap by lazy {
        hashMapOf<String, Ext4Gw>()
    }

    override fun keys(): MutableList<String> {
        return mutableListOf<String>().apply {
            val deviceItemGoodsTypes = ThConfig4Support.h5151SubDeviceItemTypes
            for (goodsType in deviceItemGoodsTypes) {
                add(goodsType)
            }
        }
    }

    override fun itemClick(context: Context, absDevice: AbsDevice) {
        jump2ThDetailAc(context, absDevice)
    }

    private fun jump2ThDetailAc(context: Context, absDevice: AbsDevice) {
        val bindExt = JsonUtil.fromJson(absDevice.deviceExt.deviceSettings ?: "", AddInfo::class.java)
        bindExt.goodsType = absDevice.goodsType
        bindExt.sku = absDevice.sku
        bindExt.device = absDevice.device
        bindExt.bleHardVersion = absDevice.versionHard
        bindExt.bleSoftVersion = absDevice.versionSoft
        val arguments = Bundle().apply {
            putParcelable(ThConsV1.KEY_4_ADD_INFO, bindExt)
        }
        gwSubExtMap.get(absDevice.key)?.gatewayInfo?.let {
            it.isIotOnLine = GwH5151Op.wifiStatus(absDevice.key) == Item.WIFI_STATUS_ONLINE
            arguments.putParcelable(ThConsV1.KEY_4_GATEWAY_INFO, it)
        }
        when (bindExt.sku) {
            Constant4L5.H5112 -> {
                JumpUtil.jumpWithBundle(context, Ac4H5112Detail::class.java, arguments)
            }

            else -> {
                if (ThConfig4Detail.useNewDetailPage(bindExt.goodsType, bindExt.sku)) {
                    JumpUtil.jumpWithBundle(context, Ac4ThCommonDetailNew::class.java, arguments)
                } else {
                    JumpUtil.jumpWithBundle(context, Ac4ThCommonDetail::class.java, arguments)
                }
            }
        }
    }

    override fun supportBleScan(): Boolean = true

    override fun tryGenerateBleInRangeModel(absDevice: AbsDevice): AbsBleInRange {
        val goodsType = absDevice.goodsType

        return object : AbsThBleInRange(absDevice) {

            override fun goodsTypeOrSku(): String {
                return goodsType.toString()
            }
        }
    }

    override fun tryGenerateWifiStatus4GwSubDevice(absDevice: AbsDevice): AbsWifiStatus4GwSubDevice {
        return object : AbsThWifiStatus4GwSubDevice(absDevice) {

            init {
                JsonUtil.fromJson(absDevice.deviceExt.deviceSettings ?: "", Ext4H5112::class.java)?.let { ext4H5112 ->
                    ext4Gw?.gatewayInfo?.sno = ext4H5112.sno
                }
                ext4Gw?.let {
                    gwSubExtMap[absDevice.key] = it
                }
            }

            override fun parseSubDeviceOnline(msg: IotMsgV2, jsonStr: String): Int {
                if (msg.cmd != Cmd.status) return SUB_DEVICE_PARSE_IOT_STATUS_UNKNOW
                when (absDevice.sku) {
                    Constant4L5.H5112 -> {
                        Ext4H5112.parseIot4H5112Sub(absDevice, jsonStr)?.let { ext4H5112 ->
//                            SafeLog.i("xiaobing") { "GwSubDeviceItemImp--parseSubDeviceOnline-->${JsonUtil.toJson(ext4H5112)}" }
                            gwSubExtMap.get(absDevice.key)?.gatewayInfo?.run {
                                this.signal = ext4H5112.signal
                            }
                            return if (ext4H5112.online.isTrue()) SUB_DEVICE_PARSE_IOT_STATUS_ONLINE else SUB_DEVICE_PARSE_IOT_STATUS_OFFLINE
                        }
                        return SUB_DEVICE_PARSE_IOT_STATUS_UNKNOW
                    }

                    else -> {
                        val subIndex = ext4Gw?.gatewayInfo?.index ?: return SUB_DEVICE_PARSE_IOT_STATUS_UNKNOW
                        return parseOnline(msg.sku, subIndex, jsonStr)
                    }
                }
            }
        }
    }

    private fun parseOnline(gwSku: String, subIndex: Int, jsonStr: String): Int {
        when (gwSku) {
            Constant4L5.H5151 -> {
                val validBytes = GwIot4StatusCommand.parse2Commands(jsonStr) ?: return SUB_DEVICE_PARSE_IOT_STATUS_UNKNOW
                if (validBytes.isEmpty()) return SUB_DEVICE_PARSE_IOT_STATUS_UNKNOW
                val subNum = BleUtils.getUnsignedByte(validBytes[0])
                if (subNum <= 0 || subIndex > subNum) return SUB_DEVICE_PARSE_IOT_STATUS_UNKNOW
                //第一个字节为绑定的子设备个数，每个子设备10个字节数据 1byte 在线状态，1 byte电量，4 byte实时温湿度，4 byte 时间戳分钟
                val startIndex = 1 + (subIndex - 1) * 10
                if (startIndex >= validBytes.size) return SUB_DEVICE_PARSE_IOT_STATUS_UNKNOW
                val isSubIotOnline = BleUtils.getUnsignedByte(validBytes[startIndex]) > 0
                return if (isSubIotOnline) SUB_DEVICE_PARSE_IOT_STATUS_ONLINE else SUB_DEVICE_PARSE_IOT_STATUS_OFFLINE
            }

            Constant4L5.H5042,
            Constant4L5.H5043,
            Constant4L5.H5044,
                -> {
                val validBytes = GwIot4StatusCommand.parse2Commands4Common(subIndex, jsonStr) ?: return SUB_DEVICE_PARSE_IOT_STATUS_UNKNOW
                val isSubIotOnline = validBytes[0].toUInt().toInt() == 1
                return if (isSubIotOnline) SUB_DEVICE_PARSE_IOT_STATUS_ONLINE else SUB_DEVICE_PARSE_IOT_STATUS_OFFLINE
            }

            else -> {
                return SUB_DEVICE_PARSE_IOT_STATUS_UNKNOW
            }
        }
    }
}
