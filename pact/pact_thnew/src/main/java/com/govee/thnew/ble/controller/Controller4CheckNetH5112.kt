package com.govee.thnew.ble.controller

import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback

/**
 * <AUTHOR>
 * @date created on 2025/6/6
 * @description H5112-->联网检测的Controller
 */
class Controller4CheckNetH5112 : AbsControllerWithCallback {

    /**
     * 写操作
     */
    constructor(writeCallBack: ((result: <PERSON><PERSON><PERSON>) -> Unit)) : super(writeCallBack)

    override fun getCommandType(): Byte {
        return BleProtocol.COMMAND_TYPE_4_CHECK_NET
    }

    override fun translateWrite(): ByteArray {
        return byteArrayOf()
    }
}