package com.govee.thnew.pact.h5112

import android.content.Context
import android.text.TextUtils
import android.widget.ImageView
import android.widget.TextView
import androidx.databinding.DataBindingUtil
import androidx.databinding.ViewDataBinding
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.govee.base2home.Constant4L5
import com.govee.base2home.util.StrUtil
import com.govee.base2kt.ext.gone
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.cubeview.visible
import com.govee.kt.loadSkuIcon
import com.govee.kt.ui.device.Adapter4DeviceList
import com.govee.kt.ui.device.CardStyle
import com.govee.kt.ui.device.CommonViewResId
import com.govee.kt.ui.device.IDeviceHolder
import com.govee.kt.ui.device.IDeviceItem
import com.govee.kt.ui.device.Item
import com.govee.kt.ui.device.ItemType
import com.govee.kt.ui.device.SpanSize
import com.govee.kt.ui.device.util.DeviceItemOfflineUtils
import com.govee.kt.ui.device.util.DeviceListEditManager
import com.govee.thnew.databinding.ThnewLayout4H5112CardBinding
import com.govee.thnew.databinding.ThnewLayout4H5112CardNewBinding
import com.govee.thnew.ui.detail.h5112.Dialog4EditProbeInfo
import com.govee.ui.R
import com.ihoment.base2app.util.ResUtil

/**
 * <AUTHOR>
 * @date created on 2025/5/22
 * @description H5112--卡片的视图类
 */
class Holder4H5112 : IDeviceHolder {

    override fun itemTypes(): MutableList<ItemType> {
        return mutableListOf<ItemType>().apply {
            add(
                ItemType(
                    Item.ITEM_TYPE_31_BIG,
                    com.govee.thnew.R.layout.thnew_layout_4_h5112_card,
                    CardStyle.CLASSICS,
                    SpanSize.BIG
                )
            )
            add(
                ItemType(
                    Item.ITEM_TYPE_31_BIG_NEW,
                    com.govee.thnew.R.layout.thnew_layout_4_h5112_card_new,
                    CardStyle.NEWS,
                    SpanSize.BIG
                )
            )
        }
    }

    override fun childClickIds(): MutableList<Int> {
        return mutableListOf<Int>().apply {
            add(com.govee.thnew.R.id.iv_qa_icon_4_h5112_card)
        }
    }

    override fun getCommonViewResId(holder: BaseViewHolder): CommonViewResId {
        return CommonViewResId(
            ivRemoveCardResId = com.govee.thnew.R.id.ivRemove,
            ivChangeCardTypeResId = com.govee.thnew.R.id.ivChangeSize
        )
    }

    override fun covert(context: Context?, adapterDeviceList: Adapter4DeviceList, holder: BaseViewHolder, item: IDeviceItem): Boolean {
        val isEditMode = DeviceListEditManager.isEditState()
        return when (holder.itemViewType) {
            Item.ITEM_TYPE_31_BIG -> {
                parseItemTypeBig(context, holder, item, !isEditMode)
            }

            Item.ITEM_TYPE_31_BIG_NEW -> {
                parseItemTypeBig4New(context, holder, item, !isEditMode)
            }

            else -> {
                false
            }
        }
    }

    private fun parseItemTypeBig(
        context: Context?,
        holder: BaseViewHolder,
        item: IDeviceItem,
        isUiNormal: Boolean
    ): Boolean {
        val item31 = item.item4Ui as? Item31 ?: return false
        val binding = DataBindingUtil.bind<ViewDataBinding>(holder.itemView) ?: return false
        if (binding is ThnewLayout4H5112CardBinding) {
            binding.icShareFlag.tvShareFlag.visibleByBoolean(item31.share == 1)
            item31.run {
                uiPartFresh(
                    context, item,
                    this,
                    binding.ivQaIcon4H5112Card,
                    binding.ivBatteryIcon4H5112Card,
                    binding.ivWifiIcon4H5112Card,
                    binding.ivBtIcon4H5112Card,
                    binding.ivDeviceIcon4H5112Card,
                    binding.tvDeviceName4H5112Card,
                    binding.ivTimeRefreshIcon4H5112Card,
                    binding.tvTimeStr4H5112Card,
                    binding.ivPb1Icon4H5112Card,
                    binding.tvPb1Name4H5112Card,
                    binding.ivPb2Icon4H5112Card,
                    binding.tvPb2Name4H5112Card,
                    true,
                    isUiNormal
                )
                //探针1的温度值
                val pb1TemInfo = getTemInfo(true, item31.isOffline(), this.temStr)
                binding.tvPb1TemValue4H5112Card.text = pb1TemInfo.first
                binding.tvPb1TemUnit4H5112Card.text = pb1TemInfo.second
                binding.tvPb1TemValue4H5112Card.setTextColor(this.getColor(this.temWarning))
                binding.tvPb1TemUnit4H5112Card.setTextColor(this.getColor(this.temWarning))
                ResUtil.setImageResource(binding.ivPb1TemIcon4H5112Card, item31.temIconRes())
                //探针1的湿度值
                val pb1HumValueStr = if (TextUtils.isEmpty(this.humStr)) "" else this.humStr.substring(0, this.humStr.length - 1)
                val pb1HumUnitStr = if (TextUtils.isEmpty(this.humStr)) "" else this.humStr.substring(this.humStr.length - 1, this.humStr.length)
                binding.tvPb1HumValue4H5112Card.text = pb1HumValueStr
                binding.tvPb1HumUnit4H5112Card.text = pb1HumUnitStr
                binding.tvPb1HumValue4H5112Card.setTextColor(this.getColor(this.humWarning))
                binding.tvPb1HumUnit4H5112Card.setTextColor(this.getColor(this.humWarning))
                ResUtil.setImageResource(binding.ivPb1HumIcon4H5112Card, item31.humIconRes())
                //探针2的温度值
                val pb2TemInfo = getTemInfo(true, item31.isOffline(), this.tem2Str)
                binding.tvPb2TemValue4H5112Card.text = pb2TemInfo.first
                binding.tvPb2TemUnit4H5112Card.text = pb2TemInfo.second
                binding.tvPb2TemValue4H5112Card.setTextColor(this.getColor(this.tem2Warning))
                binding.tvPb2TemUnit4H5112Card.setTextColor(this.getColor(this.tem2Warning))
                ResUtil.setImageResource(binding.ivPb2TemIcon4H5112Card, item31.tem2IconRes())
                return true
            }
        }
        return false
    }

    private fun parseItemTypeBig4New(
        context: Context?,
        holder: BaseViewHolder,
        item: IDeviceItem,
        isUiNormal: Boolean
    ): Boolean {
        val item31 = item.item4Ui as? Item31 ?: return false
        val binding = DataBindingUtil.bind<ViewDataBinding>(holder.itemView) ?: return false
        if (binding is ThnewLayout4H5112CardNewBinding) {
            binding.icShareFlag.tvShareFlag.visibleByBoolean(item31.share == 1)
            item31.run {
                uiPartFresh(
                    context, item,
                    this,
                    binding.ivQaIcon4H5112Card,
                    binding.ivBatteryIcon4H5112Card,
                    binding.ivWifiIcon4H5112Card,
                    binding.ivBtIcon4H5112Card,
                    binding.ivDeviceIcon4H5112Card,
                    binding.tvDeviceName4H5112Card,
                    binding.ivTimeRefreshIcon4H5112Card,
                    binding.tvTimeStr4H5112Card,
                    binding.ivPb1Icon4H5112Card,
                    binding.tvPb1Name4H5112Card,
                    binding.ivPb2Icon4H5112Card,
                    binding.tvPb2Name4H5112Card,
                    false,
                    isUiNormal
                )
                //探针1的温度值
                val pb1TemInfo = getTemInfo(false, item31.isOffline(), this.temStr)
                binding.tvPb1TemValue4H5112Card.text = pb1TemInfo.first
                binding.tvPb1TemUnit4H5112Card.text = pb1TemInfo.second
                binding.tvPb1TemValue4H5112Card.setTextColor(this.getColor(this.temWarning && !isOffline()))
                binding.tvPb1TemUnit4H5112Card.setTextColor(this.getColor(this.temWarning && !isOffline()))
                ResUtil.setImageResource(binding.ivPb1TemIcon4H5112Card, item31.temIconResNew())
                //探针1的湿度值
                val pb1HumValueStr = if (isOffline()) {
                    "${ResUtil.getString(R.string.h5072_chart_text_def)} "
                } else {
                    if (TextUtils.isEmpty(this.humStr)) "" else this.humStr.substring(0, this.humStr.length - 1)
                }
                val pb1HumUnitStr = if (TextUtils.isEmpty(this.humStr)) "" else this.humStr.substring(this.humStr.length - 1, this.humStr.length)
                binding.tvPb1HumValue4H5112Card.text = pb1HumValueStr
                binding.tvPb1HumUnit4H5112Card.text = pb1HumUnitStr
                binding.tvPb1HumValue4H5112Card.setTextColor(this.getColor(this.humWarning && !isOffline()))
                binding.tvPb1HumUnit4H5112Card.setTextColor(this.getColor(this.humWarning && !isOffline()))
                ResUtil.setImageResource(binding.ivPb1HumIcon4H5112Card, item31.humIconResNew())
                //探针2的温度值
                val pb2TemInfo = getTemInfo(false, item31.isOffline(), this.tem2Str)
                binding.tvPb2TemValue4H5112Card.text = pb2TemInfo.first
                binding.tvPb2TemUnit4H5112Card.text = pb2TemInfo.second
                binding.tvPb2TemValue4H5112Card.setTextColor(this.getColor(this.tem2Warning && !isOffline()))
                binding.tvPb2TemUnit4H5112Card.setTextColor(this.getColor(this.tem2Warning && !isOffline()))
                ResUtil.setImageResource(binding.ivPb2TemIcon4H5112Card, item31.tem2IconResNew())
                return true
            }
        }
        return false
    }

    /**
     * 获取温度值和其单位
     */
    private fun getTemInfo(isClassStyle: Boolean, isOffline: Boolean, temStr: String): Pair<String, String> {
        if (TextUtils.isEmpty(temStr)) {
            return Pair("", "")
        }
        return if (temStr.endsWith(StrUtil.getTemUnitCel())) {
            val temValueStr = if (!isClassStyle && isOffline) {
                "${ResUtil.getString(R.string.h5072_chart_text_def)} "
            } else {
                temStr.replace(StrUtil.getTemUnitCel(), "", true)
            }
            Pair(temValueStr, StrUtil.getTemUnitCel())
        } else {
            val temValueStr = if (!isClassStyle && isOffline) {
                "${ResUtil.getString(R.string.h5072_chart_text_def)} "
            } else {
                temStr.replace(StrUtil.getTemUnitFah(), "", true)
            }
            Pair(temValueStr, StrUtil.getTemUnitFah())
        }
    }

    private fun uiPartFresh(
        context: Context?,
        item: IDeviceItem,
        item31: Item31,
        ivQa: ImageView,
        ivBattery: ImageView,
        ivWifi: ImageView,
        ivBT: ImageView,
        ivIcon: ImageView,
        tvName: TextView,
        ivTimeIcon: ImageView,
        tvTimeMills: TextView,
        ivPb1Icon: ImageView,
        tvPb1Name: TextView,
        ivPb2Icon: ImageView,
        tvPb2Name: TextView,
        isClassStyle: Boolean,
        isUiNormal: Boolean,
    ) {
        //调查问卷
        ivQa.visibleByBoolean(item.qaVis && isUiNormal)
        //设备名称
        tvName.text = item31.deviceName
        //蓝牙图标
        ivBT.visibleByBoolean(item31.bleVis && isUiNormal)
        //Wi-Fi图标(信号图标)
        val wifiVis = item31.wifiVis()
        ivWifi.visibleByBoolean(wifiVis && isUiNormal)
        if (wifiVis) {
            ivWifi.setImageDrawable(ResUtil.getDrawable(item31.wifiIconRes()))
        }
        //电池图标
        val batteryVis = item31.batteryVis()
        if (isClassStyle) {
            ivBattery.visibleByBoolean(batteryVis && isUiNormal)
        } else {
            ivBattery.visibleByBoolean(batteryVis && (!item31.isOffline()) && isUiNormal)
        }
        val batteryResId = item31.batteryResId
        if (batteryVis && batteryResId != Item.ICON_STATUS_INVISIBLE) {
            ivBattery.setImageDrawable(ResUtil.getDrawable(batteryResId))
        }
        //icon图标
        ivIcon.loadSkuIcon(context, item31.sku, item31.spec, item31.defSkuRes, item31.skuUrl)
        //时间显示
        if (isClassStyle) {
            ivTimeIcon.visible()
            tvTimeMills.visible()
            val timeStrVis = item31.timeStrVis()
            if (timeStrVis) {
                tvTimeMills.text = item31.timeMillStr
            }
        } else {
            if (item31.isOffline()) {
                tvTimeMills.text = ResUtil.getString(R.string.app_device_offLine)
                tvTimeMills.visibleByBoolean(DeviceItemOfflineUtils.offlineOverTime())
                ivWifi.gone()
                ivBT.gone()
                ivTimeIcon.gone()
                tvTimeMills.setTextColor(ResUtil.getColor(R.color.font_style_77_1_textColor))
            } else {
                ivTimeIcon.visible()
                tvTimeMills.visible()
                val timeStrVis = item31.timeStrVis()
                if (timeStrVis) {
                    tvTimeMills.text = item31.timeMillStr
                    tvTimeMills.setTextColor(ResUtil.getColor(R.color.font_style_6_textColor))
                }
            }
        }
        //探针1
        Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM_HUM, item31.pb1IconIndex)?.let {
            ResUtil.setImageResource(ivPb1Icon, it)
        }
        tvPb1Name.text = item31.pb1Name
        //探针2
        Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM, item31.pb2IconIndex)?.let {
            ResUtil.setImageResource(ivPb2Icon, it)
        }
        tvPb2Name.text = item31.pb2Name
    }
}
