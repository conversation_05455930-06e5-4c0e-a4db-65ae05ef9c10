package com.govee.thnew.pact.h5112

import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import androidx.annotation.Keep
import com.govee.base2home.Constant4L5
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.iot.Cmd
import com.govee.base2home.iot.Iot
import com.govee.base2home.iot.IotTransactions
import com.govee.base2home.iot.ResultPt
import com.govee.base2home.iot.getJsonObjectStr
import com.govee.base2home.iot.protype.v2.IotMsgEventV2
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.main.OfflineDeviceListConfig
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.util.NumberUtil
import com.govee.base2home.util.StrUtil
import com.govee.base2kt.ext.clickDelay
import com.govee.base2light.ble.ota.OtaFlag
import com.govee.base2light.ble.ota.v1.OtaFlagV1
import com.govee.base2light.ble.ota.v2.OtaFlagV2
import com.govee.base2light.ble.ota.v3.OtaOpV3
import com.govee.base2light.pact.iot.CmdPtReal
import com.govee.base2newth.BleThProtocol
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.base2newth.deviceitem.Ext4Gw
import com.govee.base2newth.deviceitem.GatewayInfo
import com.govee.ble.BleController
import com.govee.ble.event.EventBleBroadcastListenerTrigger
import com.govee.ble.event.EventBleConnect
import com.govee.home.account.config.AccountConfig
import com.govee.thnew.add.AddInfo
import com.govee.thnew.ble.ThBle
import com.govee.thnew.ble.controller.Controller4CloseWarnH5112
import com.govee.thnew.ble.controller.Controller4PtRealOp
import com.govee.thnew.ble.event.Event4CloseWarnH5112
import com.govee.thnew.databinding.ThnewDialog4H5112CloseWarnBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.dialog.BaseEventDialog
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.CaughtRunnable
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.ResUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2025/07/04
 * @description H5112解除报警弹窗
 */
class Dialog4H5112CloseWarn private constructor(context: Context) :
    BaseEventDialog(context) {

    private lateinit var binding: ThnewDialog4H5112CloseWarnBinding
    private var sku = ""
    private var device = ""
    private var deviceName = ""
    private var gatewayInfo: GatewayInfo? = null
    private var bleAddress = ""
    private var probeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM
    private var mHandler = Handler(Looper.getMainLooper())
    private var isTem = false
    private var isHigh = false
    private var thInfo: Pair<Int, Int>? = null
    private var operationType: Int? = null
    private var operateController: Controller4CloseWarnH5112? = null

    init {
        ignoreBackPressed()
        changeDialogOutside(false)
        immersionMode()
    }

    companion object {
        private const val OPERATION_4_PAUSE = 1
        private const val OPERATION_4_RELIEVE = 2
        private const val OPERATION_OUT_TIME = 30 * 1000L
        private val needShowWarmSet = hashSetOf<WarnInfo>()
        private const val LOADING_EVENT_KEY = "LOADING_EVENT_KEY"

        @SuppressLint("StaticFieldLeak")
        private var closeWarnDialog: Dialog4H5112CloseWarn? = null

        /**
         * 显示弹窗
         */
        @Synchronized
        fun showDialog(sku: String, device: String, probeIndex: Int, isTem: Boolean, isHigh: Boolean, thInfo: Pair<Int, Int>?) {
            val isInOta = OtaOpV3.getInstance().inOta()
                    || ThBle.getInstance.thOta?.inOta().isTrue()
                    || OtaFlag.getInstance().isInOta
                    || OtaFlagV1.getInstance.isInOta
                    || OtaFlagV2.getInstance.isInOta
            //处于iot中或温湿度值异常时，不处理
            if (isInOta || (thInfo == null) || !ThConsV1.isValidThValue(thInfo.first, thInfo.second)) {
                SafeLog.i("xiaobing") { "Dialog4H5112CloseWarn--showDialog-->有无效数据，报警未处理..." }
                return
            }
            if (closeWarnDialog != null) {
                needShowWarmSet.add(WarnInfo(sku, device, probeIndex, isTem, isHigh, thInfo))
                return
            }
            BaseApplication.getBaseApplication().topActivity?.let {
                closeWarnDialog = Dialog4H5112CloseWarn(it).apply {
                    this.sku = sku
                    this.device = device
                    this.probeIndex = probeIndex
                    this.isTem = isTem
                    this.isHigh = isHigh
                    this.thInfo = thInfo
                    val cacheDevice = if (AccountConfig.read().isHadToken) {
                        DeviceListConfig.read().getDeviceByKey(sku, device)
                    } else {
                        OfflineDeviceListConfig.read().getDevices(sku, device)
                    }
                    cacheDevice?.let { absDevice ->
                        this.deviceName = absDevice.deviceName
                        absDevice.deviceExt?.deviceSettings?.let { settingsStr ->
                            JsonUtil.fromJson(settingsStr, Ext4Gw::class.java)?.gatewayInfo?.let { gwInfo ->
                                JsonUtil.fromJson(settingsStr, Ext4H5112::class.java)?.let { ext4H5112 ->
                                    gwInfo.sno = ext4H5112.sno
                                    gatewayInfo = gwInfo
                                }
                            }
                            JsonUtil.fromJson(settingsStr, AddInfo::class.java)?.let { bindExt ->
                                bleAddress = bindExt.address
                            }
                        }
                    }
                }
                closeWarnDialog?.showDialog(probeIndex, isTem)
            }
        }

        /**
         * 隐藏弹窗
         */
        @Synchronized
        fun hideDialog(sku: String, device: String, probeIndex: Int, isTem: Boolean) {
            closeWarnDialog?.let {
                val isSameDevice = (it.sku == sku) && (it.device == device)
                val isSameProbeIndex = it.probeIndex == probeIndex
                val isSameWarnType = it.isTem == isTem
                if (isSameDevice && isSameProbeIndex && isSameWarnType) {
                    SafeLog.i("xiaobing") { "Dialog4H5112CloseWarn--hideDialog-->..." }
                    needShowWarmSet.remove(WarnInfo(sku, device, probeIndex, isTem))
                    it.hide()
                    //置空弹窗
                    closeWarnDialog = null
                    //如果有待报警的需立即展示
                    if (needShowWarmSet.isNotEmpty()) {
                        needShowWarmSet.first().let { warnInfo ->
                            showDialog(warnInfo.sku, warnInfo.device, warnInfo.probeIndex, warnInfo.isTem, warnInfo.isHigh, warnInfo.thInfo)
                        }
                    }
                }
            }
        }
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getLayoutView(): View {
        binding = ThnewDialog4H5112CloseWarnBinding.inflate(LayoutInflater.from(context))
        binding.tvNotUnbindBtn4CloseWarn.clickDelay {
            operationType = OPERATION_4_PAUSE
            sendOperateCommand(false)
        }
        binding.tvSure4CloseWarn.clickDelay {
            operationType = OPERATION_4_RELIEVE
            sendOperateCommand(true)
        }
        return binding.root
    }

    private val operationOverTimeRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                SafeLog.i("xiaobing") { "Dialog4H5112CloseWarn--runSafe-->操作超时..." }
                mHandler.removeCallbacks(this)
                when (operationType) {
                    OPERATION_4_PAUSE -> {
                        toast(com.govee.ui.R.string.app_scense_setting_fail)
                    }

                    OPERATION_4_RELIEVE -> {
                        toast(com.govee.ui.R.string.h5112_text_4_close_warn_fail)
                    }

                    else -> {}
                }
                //关闭本弹窗
                hideDialog(sku, device, probeIndex, isTem)
            }
        }
    }

    private fun isBleConnected(): Boolean {
        val connectedAddress = BleController.getInstance().connectedBleAddress
        val isSameDevice = !TextUtils.isEmpty(bleAddress) && connectedAddress == bleAddress
        return BleController.getInstance().isConnected && isSameDevice
    }

    private fun isBindGateway(): Boolean {
        return gatewayInfo != null && (gatewayInfo?.sno ?: -1) >= 0
    }

    /**
     * 发送操作指令
     */
    private fun sendOperateCommand(isRelieve: Boolean) {
        if (TextUtils.isEmpty(bleAddress)) {
            //移除超时
            mHandler.removeCallbacks(operationOverTimeRunnable)
            mHandler.post(operationOverTimeRunnable)
            return
        }
        showLoading()
        operateController = Controller4CloseWarnH5112(probeIndex, isTem, isRelieve) {}
        //连接了蓝牙则直接蓝牙发送
        if (isBleConnected()) {
            SafeLog.i("xiaobing") { "Dialog4H5112CloseWarn--sendOperateCommand-->蓝牙发送指令..." }
            ThBle.getInstance.startExtControllers(false, operateController)
        } else {
            //绑定了网关，则尝试iot发送
            if (isBindGateway() && Iot.getInstance.isConnected) {
                gatewayInfo?.let { gwInfo ->
                    //尝试iot发送
                    SafeLog.i("xiaobing") { "Dialog4H5112CloseWarn--sendOperateCommand-->尝试iot发送指令..." }
                    val ptRealOpController = Controller4PtRealOp(gwInfo.sno ?: 0, operateController!!)
                    gwInfo.topic?.let { gwTopic ->
                        Iot.getInstance.write(gwTopic, IotTransactions().createTransaction(false), CmdPtReal(ptRealOpController.value))
                    }
                }
            }
            //同时尝试连接蓝牙
            ThBle.getInstance.connectBle(bleAddress)
        }
        //设置总超时
        mHandler.removeCallbacks(operationOverTimeRunnable)
        mHandler.postDelayed(operationOverTimeRunnable, OPERATION_OUT_TIME)
    }

    /**
     * ble、iot均会回调到此处
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4CloseWarnResult(event: Event4CloseWarnH5112) {
        if (operationType == null || operateController == null) {
            return
        }
        operationType = null
        operateController = null
        //移除超时
        mHandler.removeCallbacks(operationOverTimeRunnable)
        //断开蓝牙连接
        //备注：根据是否需要->即在详情页之前的页面展示弹窗，如果是通过蓝牙连接发送的指令，需断开蓝牙并重新打开蓝牙广播接收
        if (Vm4ThOpManager.instance() == null && BleController.getInstance().isConnected) {
            //断开蓝牙连接
            BleController.getInstance().disconnectBleAndNotify()
            //开启蓝牙扫描
            EventBleBroadcastListenerTrigger.sendEvent4immediateStartScan(true)
        }
        //toast提示
        if (event.result) {
            if (event.isRelieve) {
                toast(com.govee.ui.R.string.h5112_text_4_close_warn_suc)
            } else {
                toast(com.govee.ui.R.string.b2light_app_setting_success)
            }
        } else {
            if (event.isRelieve) {
                toast(com.govee.ui.R.string.h5112_text_4_close_warn_fail)
            } else {
                toast(com.govee.ui.R.string.app_scense_setting_fail)
            }
        }
        //关闭本弹窗
        hideDialog(sku, device, probeIndex, isTem)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4BleConnect(event: EventBleConnect) {
        if (operationType == null) {
            return
        }
        if (isBleConnected()) {
            //要做延迟处理(在详情页之后的页面，Vm4ThOpManager中也会对连接做处理)
            mHandler.postDelayed(object : CaughtRunnable() {
                override fun runSafe() {
                    operateController?.let {
                        SafeLog.i("xiaobing") { "Dialog4H5112CloseWarn--runSafe-->尝试蓝牙发送指令..." }
                        ThBle.getInstance.startExtControllers(false, it)
                    }
                }
            }, 1000)
        } else {
            //无需处理蓝牙未连接的情况(有总的超时处理)
        }
    }

    private fun showDialog(probeIndex: Int, isTem: Boolean) {
        if (thInfo == null) {
            return
        }
        SafeLog.i("xiaobing") { "Dialog4H5112CloseWarn--showDialog-->..." }
        var contentText = ""
        when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                var temCali = 0
                var humCali = 0
                WarnConfig.read().queryWarningRangeByKey(sku, device)?.let {
                    temCali = it.temCali
                    humCali = it.humCali
                }
                if (isTem) {
                    val unitText = if (isFahOpen()) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
                    val realTemValue = NumberUtil.getTemValue(isFahOpen(), thInfo!!.first, temCali)
                    val realTemStr = "${NumberUtil.getValidFloatByOnePoint(realTemValue)}${unitText}"
                    contentText = if (isHigh) {
                        ResUtil.getStringFormat(com.govee.ui.R.string.h5112_text_4_close_tem_warn_high, deviceName, getProbeName(), realTemStr)
                    } else {
                        ResUtil.getStringFormat(com.govee.ui.R.string.h5112_text_4_close_tem_warn_low, deviceName, getProbeName(), realTemStr)
                    }
                } else {
                    val unitText = StrUtil.getHumUnit()
                    val realHumValue = NumberUtil.getHumValue(thInfo!!.second, humCali)
                    val realHumStr = "${NumberUtil.getValidFloatByOnePoint(realHumValue)}${unitText}"
                    contentText = if (isHigh) {
                        ResUtil.getStringFormat(com.govee.ui.R.string.h5112_text_4_close_warn_hum_high, deviceName, getProbeName(), realHumStr)
                    } else {
                        ResUtil.getStringFormat(com.govee.ui.R.string.h5112_text_4_close_warn_hum_low, deviceName, getProbeName(), realHumStr)
                    }
                }
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                var temCali = 0
                WarnConfig.read().queryWarningRangeByKey(sku, ThConsV1.getH5112Device4Pb2(device))?.let {
                    temCali = it.temCali
                }
                val unitText = if (isFahOpen()) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
                val realTemValue = NumberUtil.getTemValue(isFahOpen(), thInfo!!.first, temCali)
                val realTemStr = "${NumberUtil.getValidFloatByOnePoint(realTemValue)}${unitText}"
                contentText = if (isHigh) {
                    ResUtil.getStringFormat(com.govee.ui.R.string.h5112_text_4_close_tem_warn_high, deviceName, getProbeName(), realTemStr)
                } else {
                    ResUtil.getStringFormat(com.govee.ui.R.string.h5112_text_4_close_tem_warn_low, deviceName, getProbeName(), realTemStr)
                }
            }

            else -> {
                return
            }
        }
        binding.tvWarnContentText4CloseWarn.text = contentText
        SafeLog.i("xiaobing") { "Dialog4H5112CloseWarn--showDialog-->${probeIndex},${isTem}" }
        show()
    }

    private fun isFahOpen(): Boolean {
        return TemUnitConfig.read().isTemUnitFah(sku)
    }

    private fun getProbeName(): String {
        when (sku) {
            Constant4L5.H5112 -> {
                DeviceListConfig.read().getDeviceByKey(sku, device)?.deviceExt?.deviceSettings?.let {
                    JsonUtil.fromJson(it, AddInfo::class.java)?.let { bindExt ->
                        when (probeIndex) {
                            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                                return bindExt.probeName1 ?: ""
                            }

                            Constant4L5.PROBE_INDEX_4_TEM -> {
                                return bindExt.probeName2 ?: ""
                            }

                            else -> {}
                        }
                    }
                }
            }

            else -> {}
        }
        return ""
    }

    /**
     * iot读写操作的回调事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onIotMsgEventV2(iotEvent: IotMsgEventV2) {
        if (operationType == null || operateController == null) {
            return
        }
        val iotMsg = iotEvent.msgV2
        val isSameDevice = iotMsg.isSameDevice(gatewayInfo?.sku ?: "", gatewayInfo?.device ?: "")
        if (!isSameDevice) {
            return
        }
        getJsonObjectStr(iotEvent.jsonStr, Cmd.parse_json_op)?.let {
            JsonUtil.fromJson(it, ResultPt::class.java)?.run {
                parseIotOpInfo(iotMsg.cmd, this.bytes())
            }
        }
    }

    private fun parseIotOpInfo(cmd: String, iotResponseBytes: List<ByteArray>) {
        when (cmd) {
            Cmd.ptReal,
            Cmd.multiSync -> {
                //处理回复
                val iotRespBys = iotResponseBytes[0]
                SafeLog.i("xiaobing") { "Dialog4H5112CloseWarn--parseIotOpInfo-->cmd:${cmd},${BleUtil.bytesToHexString(iotRespBys)}" }
                val isResultReport = (iotRespBys.size == 20) && (iotRespBys[0] == BleThProtocol.NOTIFY) && (iotRespBys[1] == BleProtocol.COMMAND_TYPE_4_PT_REAL_OP_RESULT_NOTIFY)
                if (isResultReport) {
                    val respDataBys = ByteArray(18)
                    System.arraycopy(iotRespBys, 2, respDataBys, 0, respDataBys.size)
                    //确认为同一个设备
                    if (gatewayInfo?.sno != respDataBys[0].toInt()) return
                    //处理数据
                    operateController?.run {
                        //确认为同一条指令后，回复成功
                        if (this.isSameController(BleThProtocol.SINGLE_WRITE, respDataBys[1])) {
                            val respValues = ByteArray(20).apply {
                                fill(0)
                            }
                            respValues[0] = BleThProtocol.SINGLE_WRITE
                            respValues[1] = respDataBys[1]
                            this.onResult(true, respValues)
                        }
                    }
                }
            }

            else -> {}
        }
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth() * 335 / 375
    }

    override fun hide() {
        hideLoading()
        mHandler.removeCallbacksAndMessages(null)
        super.hide()
        SafeLog.i("xiaobing") { "Dialog4H5112CloseWarn--hide-->..." }
    }

    private fun showLoading() {
        LoadingDialog.createDialog(context, com.ihoment.base2app.R.style.DialogDim).setEventKey(LOADING_EVENT_KEY).show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(LOADING_EVENT_KEY)
    }
}


@Keep
internal data class WarnInfo(
    val sku: String,
    val device: String,
    val probeIndex: Int,
    val isTem: Boolean,
    val isHigh: Boolean = false,
    val thInfo: Pair<Int, Int>? = null
) {
    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is WarnInfo) return false

        if (probeIndex != other.probeIndex) return false
        if (isTem != other.isTem) return false
        if (sku != other.sku) return false
        if (device != other.device) return false

        return true
    }

    override fun hashCode(): Int {
        var result = probeIndex
        result = 31 * result + isTem.hashCode()
        result = 31 * result + sku.hashCode()
        result = 31 * result + device.hashCode()
        return result
    }
}