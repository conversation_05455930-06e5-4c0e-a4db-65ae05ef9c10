package com.govee.thnew.pact

import com.govee.base2home.Constant4L5
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.util.NumberUtil
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.deviceitem.AbsModel4Th
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JsonUtil

/**
 * <AUTHOR>
 * @date created on 2024/2/18
 * @description 温湿度计-->广播解析工具类
 * 备注：可联动的设备有两种广播，其一为正常的广播，另一种为触发联动的触发广播(通过网关给服务器用)。
 *      此处只需解正常广播即可，须先甄别
 */
object ThBroadcastUtil {

    const val INVALID_BYTE = 0xFF.toByte()
    private const val MIN_TEM_4_V0 = -40
    private const val MAX_TEM_4_V0 = 100

    /**
     * 解析温湿度计广播信息
     *
     * @param scanRecord
     * @return int[5]：[0]->无用; [1]->是否低电告警; [2]->tem; [3]->hum; [4]->battery
     * 备注：数据部分格式=>是否低电告警(1bytes)+tem(2bytes)+hum(2bytes)+battery(1byte)
     *      !!!关联sku:H5051、H5052、H5071、5074
     */
    fun parseBroadcastV0(scanRecord: ByteArray): IntArray? {
        val pos = BleUtil.checkBroadcastData(scanRecord)
        if (pos != -1) {
            val validValue = ByteArray(7)
            System.arraycopy(scanRecord, pos, validValue, 0, validValue.size)
            val batteryWarn = validValue[0] == 0x00.toByte()
            var isValid = !(validValue[1] == INVALID_BYTE && validValue[2] == INVALID_BYTE && validValue[3] == INVALID_BYTE && validValue[4] == INVALID_BYTE)
            val tem = BleUtil.getSignedShort(validValue[2], validValue[1]).toInt()
            val hum = BleUtil.getSignedIntV2(byteArrayOf(validValue[3], validValue[4]))
            val battery = BleUtil.getSignedIntV2(byteArrayOf(validValue[5]))
            if (tem == 0 && hum == 0 && battery == 0) {
                isValid = false
            }
            //处理由于上电导致传感器数据读取出错的情况
            if (tem < MIN_TEM_4_V0 * 100 || tem > MAX_TEM_4_V0 * 100) {
                isValid = false
            }
            if (hum < ThConsV1.HUM_MIN_VALUE || hum > ThConsV1.HUM_MAX_VALUE * 100) {
                isValid = false
            }
            if (isValid) {
                return intArrayOf(1, if (batteryWarn) 1 else 0, tem, hum, battery)
            }
        }
        return null
    }

    /**
     * 解析温湿度计广播信息
     *
     * @param scanRecord
     * @return int[5]：[0]->pactType; [1]->pactCode; [2]->tem; [3]->hum; [4]->battery
     * 备注：数据部分格式=>pactType(2bytes)+pactCode(1byte)+tem_hum(3bytes)+battery(1byte)
     *      !!!关联sku:H5100、H5101、H5102、H5103、H5104、H5105、H5110、H5174、H5177、新款H5179
     */
    fun parseBroadcastV1(scanRecord: ByteArray): IntArray? {
        val index = BleUtil.parseThBleValidBytePosV1(scanRecord)
        if (index == -1) {
            return null
        }
        //pactType(2bytes)+pactCode(1byte)+tem_hum(3bytes)+battery(1byte)
        val len = 2 + 1 + 3 + 1
        if (index + len >= scanRecord.size) {
            return null
        }
        val validBytes = ByteArray(len)
        System.arraycopy(scanRecord, index, validBytes, 0, len)
        val pactType = BleUtil.getUnsignedInt(validBytes[0], validBytes[1])
        val pactCode = BleUtil.getUnsignedByte(validBytes[2])
        val thBytes = ByteArray(3)
        System.arraycopy(validBytes, 3, thBytes, 0, thBytes.size)
        val temHum = parseThValueV1(thBytes)
        val battery = BleUtil.getUnsignedByte(validBytes[6])
        return intArrayOf(pactType, pactCode, temHum[0], temHum[1], battery)
    }

    fun parseThValueV1(thByte: ByteArray): IntArray {
        val value = IntArray(2)
        val temOver0 = NumberUtil.toBinaryString(thByte[0], 8).startsWith("0")
        if (!temOver0) {
            //需要将第一个字节的最高位置0
            val thByte0 = (BleUtil.getUnsignedByte(thByte[0]) - 128).toByte()
            thByte[0] = thByte0
        }
        val signedInt = BleUtil.getSignedInt(thByte, true)
        val tem = if (temOver0) signedInt / 1000 else signedInt / -1000
        val hum = signedInt % 1000
        value[0] = tem * 10
        value[1] = hum * 10
        return value
    }

    /**
     * 解析温湿度计广播信息
     *
     * @param scanRecord
     * @return int[5]：[0]->pactType; [1]->pactCode; [2]->tem; [3]->hum; [4]->battery
     * 备注：数据部分格式=>pactType(2bytes)+pactCode(1byte)+tem(2bytes)+hum(2bytes)+battery(1byte)
     *      !!!关联sku:旧款H5179
     */
    fun parseBroadcastV2(scanRecord: ByteArray): IntArray? {
        val index = BleUtil.parseThBleValidBytePos(scanRecord)
        if (index == -1) return null
        //pactType(2bytes)+pactCode(1byte)+tem(2bytes)+hum(2bytes)+battery(1byte)
        val len = 2 + 1 + 2 + 2 + 1
        if (index + len >= scanRecord.size) return null
        val validBytes = ByteArray(len)
        System.arraycopy(scanRecord, index, validBytes, 0, len)
        val pactType = BleUtil.getUnsignedInt(validBytes[0], validBytes[1])
        val pactCode = BleUtil.getUnsignedByte(validBytes[2])
        //判断温湿度数据的有效性
        if (validBytes[3] == INVALID_BYTE && validBytes[4] == INVALID_BYTE && validBytes[5] == INVALID_BYTE && validBytes[6] == INVALID_BYTE) return null
        val tem = BleUtil.getSignedShort(validBytes[4], validBytes[3]).toInt()
        val hum = BleUtil.getSignedShort(validBytes[6], validBytes[5]).toInt()
        val battery = BleUtil.getUnsignedByte(validBytes[7])
        return intArrayOf(pactType, pactCode, tem, hum, battery)
    }

    /**
     * 解析温湿度计广播信息
     *
     * @param scanRecord
     * @return int[6]：[0]->pactType; [1]->pactCode; [2]->tem; [3]->hum; [4]->battery;[5]->alarmType(是否已经执行告警=->与延时告警关联)
     * 备注：数据部分格式=>pactType(2bytes)+pactCode(1byte)+tem_hum(3bytes)+battery(1byte)+alarm(1byte)
     *      !!!关联sku:H5108、H5111
     */
    fun parseBroadcastV3(scanRecord: ByteArray): IntArray? {
        val index = BleUtil.parseThBleValidBytePosV1(scanRecord)
        if (index == -1) {
            return null
        }
        //pactType(2bytes)+pactCode(1byte)+tem_hum(3bytes)+battery(1byte)+alarm(1byte)
        val len = 2 + 1 + 3 + 1 + 1
        if (index + len >= scanRecord.size) {
            return null
        }
        val validBytes = ByteArray(len)
        System.arraycopy(scanRecord, index, validBytes, 0, len)
//        SafeLog.i("xiaobing") { "ThBroadcastUtil--parseBroadcastV3-->${BleUtil.bytesToHexString(validBytes)}" }
        val pactType = BleUtil.getUnsignedInt(validBytes[0], validBytes[1])
        val pactCode = BleUtil.getUnsignedByte(validBytes[2])
        val thBytes = ByteArray(3)
        System.arraycopy(validBytes, 3, thBytes, 0, 3)
        val temHum = BleUtil.parseThValue(thBytes)
        val battery = BleUtil.getUnsignedByte(validBytes[6])
        val alarmType = if (BleUtil.getUnsignedByte(validBytes[7]) > 0) 1 else 0
        return intArrayOf(pactType, pactCode, temHum[0], temHum[1], battery, alarmType)
    }

    /**
     * 解析温湿度计广播信息
     *
     * @param scanRecord
     * @return int[5]：[0]->无用; [1]->无用; [2]->tem; [3]->hum; [4]->battery
     * 备注：数据部分格式=>未知(1bytes)+tem_hum(3bytes)+battery(1byte)
     *      !!!关联sku:H5072/H5075
     */
    fun parseBroadcastV4(scanRecord: ByteArray): IntArray? {
        val pos = BleUtil.checkBroadcastData(scanRecord)
        if (pos != -1) {
            val validValue = ByteArray(7)
            System.arraycopy(scanRecord, pos, validValue, 0, validValue.size)
            var isValid = !(validValue[1] == INVALID_BYTE && validValue[2] == INVALID_BYTE && validValue[3] == INVALID_BYTE)
            val thBytes = ByteArray(3)
            System.arraycopy(validValue, 1, thBytes, 0, thBytes.size)
            val temHum = parseThValueV1(thBytes)
            val tem = temHum[0]
            val hum = temHum[1]
            val battery = BleUtil.getUnsignedByte(validValue[4])
            if (tem == 0 && hum == 0 && battery == 0) {
                isValid = false
            }
            //处理由于上电导致传感器数据读取出错的情况
            if (tem < MIN_TEM_4_V0 * 100 || tem > MAX_TEM_4_V0 * 100) {
                isValid = false
            }
            if (hum < ThConsV1.HUM_MIN_VALUE || hum > ThConsV1.HUM_MAX_VALUE * 100) {
                isValid = false
            }
            if (isValid) {
                return intArrayOf(0, 0, tem, hum, battery)
            }
        }
        return null
    }

    /**
     * 解析温湿度计广播信息
     *
     * @param scanRecord 广播包
     * @return int[8]=->[0]:pactType;[1]pactCode;[2]:order;[3]:tem;[4]:hum;[5]:battery;[6]:online;[7]:timeStampOffset
     * 备注：数据部分格式=>pactType(2bytes)+pactCode(1byte)+order(1byte)+tem_hum(3bytes)+battery_online(1byte)+timeStampOffset(2bytes)
     *      !!!关联sku:B5178
     */
    fun parseBroadcast4V5(scanRecord: ByteArray): IntArray? {
        val index = BleUtil.parseMultiThBleValidBytePos(scanRecord)
        if (index == -1) return null
        //pactType(2bytes)+pactCode(1byte)+order(1byte)+tem_hum(3bytes)+battery_online(1byte)+timeStampOffset(2bytes)
        val len = 2 + 1 + 1 + 3 + 1 + 2
        if (index + len >= scanRecord.size) return null
        val validBytes = ByteArray(len)
        System.arraycopy(scanRecord, index, validBytes, 0, len)
        val pactType = BleUtil.getUnsignedInt(validBytes[0], validBytes[1])
        val pactCode = BleUtil.getUnsignedByte(validBytes[2])
        val order = BleUtil.getUnsignedByte(validBytes[3])
        //判断温湿度数据的有效性
        if (validBytes[4] == INVALID_BYTE && validBytes[5] == INVALID_BYTE && validBytes[6] == INVALID_BYTE) {
            if (order == 0) return null
        }
        val thBytes = ByteArray(3)
        System.arraycopy(validBytes, 4, thBytes, 0, 3)
        val temHum = if (isInvalidThBytes(thBytes)) intArrayOf(-1, -1) else parseThValueV1(thBytes)
        SafeLog.i("xiaobing") { "ThBroadcastUtil--parseBroadcast4V5-->order=->${order},${JsonUtil.toJson(temHum)}" }
        val onlineAndBattery: IntArray = parseBatAndOnline4V5(validBytes[7]) ?: return null
        val timeStampOffset = BleUtil.getUnsignedInt(validBytes[8], validBytes[9])
        return intArrayOf(pactType, pactCode, order, temHum[0], temHum[1], onlineAndBattery[1], onlineAndBattery[0], timeStampOffset)
    }

    /**
     * 有效性判断
     */
    private fun isInvalidThBytes(thBytes: ByteArray): Boolean {
        for (thByte in thBytes) {
            if (thByte != INVALID_BYTE) return false
        }
        return true
    }

    /**
     * 返回在线状态和电量
     * @return 0->在线状态：>0：不在线,0在线
     */
    private fun parseBatAndOnline4V5(value: Byte): IntArray? {
        try {
            val binaryStr = NumberUtil.toBinaryString(value, 8)
            val online = if (binaryStr.startsWith("0")) 0 else 1
            val battertBinaryStr = "0" + binaryStr.substring(1)
            val battery = battertBinaryStr.toInt(2)
            return intArrayOf(online, battery)
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return null
    }


    /**
     * 解析温湿度计广播信息
     *
     * @param scanRecord
     * @return int[8]：[0]->pactType; [1]->pactCode; [2]->tem; [3]->hum; [4]->battery;
     *                [5]->temAlarmType(是否已经执行告警=->与延时告警关联);
     *                [6]->humAlarmType(是否已经执行告警=->与延时告警关联);
     *                [7]->探针序号(1,2);
     *                [8]->对应探针的图标icon序号
     * 备注：数据部分格式=>pactType(2bytes)+pactCode(1byte)+tem_hum(3bytes)+battery(1byte)+
     *                  alarm(1byte)+pbIndex/pbIconIndex(1byte:高2位表示探针序号(1,2),后6位表示对应探针序号(1-8))
     *      !!!关联sku:H5112
     */
    fun parseBroadcastV6(scanRecord: ByteArray): IntArray? {
        val index = BleUtil.parseThBleValidBytePosV1(scanRecord)
        if (index == -1) {
            return null
        }
        //pactType(2bytes)+pactCode(1byte)+tem_hum(3bytes)+battery(1byte)+
        //alarm(1byte)+pbIndex/pbIconIndex(1byte:高2位表示探针序号(1,2),后6位表示对应探针序号(1-8))
        val len = 2 + 1 + 3 + 1 + 1 + 1
        if (index + len >= scanRecord.size) {
            return null
        }
        val validBytes = ByteArray(len)
        System.arraycopy(scanRecord, index, validBytes, 0, len)
        val pactType = BleUtil.getUnsignedInt(validBytes[0], validBytes[1])
        val pactCode = BleUtil.getUnsignedByte(validBytes[2])
        val thBytes = ByteArray(3)
        System.arraycopy(validBytes, 3, thBytes, 0, 3)
        val temHum = if (isInvalidThBytes(thBytes)) intArrayOf(AbsModel4Th.INVALID_INT_VALUE, AbsModel4Th.INVALID_INT_VALUE) else BleUtil.parseThValue(thBytes)
        val battery = BleUtil.getUnsignedByte(validBytes[6])
        //报警标志位，bit 位=0表示对应事件无报警。
        //其中 bit0=1：低于设置温度报警，bit1=1高于设置温度报警，
        //bit2=1：低于设置湿度报警，bit3=1高于设置湿度报警，
        //bit4=1：低于设置NTC温度报警，bit5=1高于设置NTC温度报警
        val warnBinaryStr = BleUtil.hexToBinary(BleUtil.toHex(validBytes[7])).reversed()
        //温湿度探针--温度报警状态(0：无报警，1：低于，2高于，下同)
        val pb1TemWarnType = warnBinaryStr.substring(0, 2).reversed().toInt(2)
        //温湿度探针--湿度报警状态
        val pb1HumWarnType = warnBinaryStr.substring(2, 4).reversed().toInt(2)
        //温度探针--湿度报警值状态
        val pb2TemWarnType = warnBinaryStr.substring(4, 6).reversed().toInt(2)

        var pbIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM
        var pbIconIndex = 1
        val pbBinaryStr = BleUtil.hexToBinary(BleUtil.toHex(validBytes[8]))
        pbIndex = pbBinaryStr.substring(0, 2).toInt(2)
        pbIconIndex = pbBinaryStr.substring(2, 8).toInt(2)
        var temWarnType = 0
        var humHumType = 0
        when (pbIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                temWarnType = pb1TemWarnType
                humHumType = pb1HumWarnType
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                temWarnType = pb2TemWarnType
            }

            else -> {}
        }
//        SafeLog.i("xiaobing") { "ThBroadcastUtil--parseBroadcastV6-->${BleUtil.bytesToHexString(validBytes)}" }
//        SafeLog.i("xiaobing") { "ThBroadcastUtil--parseBroadcastV6-->${JsonUtil.toJson(arrayOf(temHum[0], temHum[1], battery, temWarnType, humHumType, pbIndex))}" }
        return intArrayOf(pactType, pactCode, temHum[0], temHum[1], battery, temWarnType, humHumType, pbIndex, pbIconIndex)
    }
}