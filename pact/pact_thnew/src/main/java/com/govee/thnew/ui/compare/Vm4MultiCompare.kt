package com.govee.thnew.ui.compare

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.pact.GoodsType
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.TimeUtil
import com.govee.base2newth.chart.newchart.ChartController4New
import com.govee.base2newth.db.DbController
import com.govee.base2newth.db.TemHum
import com.govee.base2newth.db.TemHumPm
import com.govee.ble.BleController
import com.govee.mvvm.base.viewmodel.BaseViewModel
import com.govee.mvvm.globalLaunch
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.ui.R
import com.govee.util.recordUseCount
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JsonUtil
import kotlinx.coroutines.Dispatchers


/**
 * <AUTHOR>
 * @date created on 2024/4/8
 * @description 温湿度计=->多设备图表数据比较的ViewModel
 */
class Vm4MultiCompare : BaseViewModel() {

    /**
     * 设备信息
     */
    private var goodsType = GoodsType.GOODES_TYPE_NO_SUPPORT
    private lateinit var sku: String
    private lateinit var device: String

    /**
     * 该设备在本地的最新一条数据的时间点
     */
    var lastDataTime = 0L
        private set

    /**
     * 最小的有效时间
     */
    var minValidTime = 0L
        private set

    /**
     * 数据对比的开始、结束时间
     */
    private var startTime = 0L
    private var endTime = 0L

    /**
     * 缓存数据相关
     */
    private lateinit var cacheDataMap: HashMap<Int, Pair<Triple<Int, String, String>, ArrayList<TemHumPm>>>

    /**
     * 线序对应设备信息的map
     * 备注：key=->线序号，value->first:goodsType,second:first->sku,second->device,third->bleAddress
     */
    private lateinit var lineIndexMap: HashMap<Int, Pair<Int, Triple<String, String, String>>>
    private var lastLineIndex = ThConsV1.LINE_1

    /**
     * 初始化，锁定设备
     */
    fun init(lifecycleOwner: LifecycleOwner, goodsType: Int, sku: String, device: String, bleAddress: String) {
        this.goodsType = goodsType
        this.sku = sku
        this.device = device
        lastDataTime = TimeUtil.calibrationTimeByMinutes(System.currentTimeMillis())
        minValidTime = lastDataTime - ThConsV1.ONE_YEAR_MILLIS * 2
        cacheDataMap = HashMap()
        lineIndexMap = HashMap<Int, Pair<Int, Triple<String, String, String>>>().apply {
            put(lastLineIndex, Pair(goodsType, Triple(sku, device, bleAddress)))
        }
        fastSelectTimePeriod(1)
        Vm4ThOpManager.instance()?.loadThcdManager?.ld4LoadThCdStep?.observe(lifecycleOwner) {
            Vm4ThOpManager.instance()?.loadThcdManager?.currFinishDevice?.let { cld ->
                it[cld]?.let { thcdInfo ->
                    if (thcdInfo.second.second == Vm4ThOpManager.THCD_4_IS_LOAD_FINISH) {
                        customSelectTimePeriod(startTime, endTime, false, "deviceKey->${cld.first}_${cld.second},图表数据加载完毕...")
                        //置空赋值
                        Vm4ThOpManager.instance()?.loadThcdManager?.currFinishDevice = null
                    }
                }
            }
        }
        if (!BleController.getInstance().isBlueToothOpen) {
            toast(R.string.ble_not_open_msg)
        }
    }

    /**
     * 获取数据对比时间段
     */
    fun getTimeRange4Compare(): Pair<Long, Long> {
        return Pair(startTime, endTime)
    }

    /**
     * 选中时间段后的数据处理结果
     */
    val ld4SelectedTimePeriod by lazy {
        MutableLiveData<Triple<Long, Long, HashMap<Int, Pair<Triple<Int, String, String>, ArrayList<TemHumPm>>>>>()
    }

    /**
     * 快速选中对比时间段
     */
    fun fastSelectTimePeriod(position: Int) {
        SafeLog.i("xiaobing") { "Vm4MultiCompare--fastSelectTimePeriod-->...." }
        showLoading()
        globalLaunch(Dispatchers.IO) {
            endTime = lastDataTime
            val timeInterval = when (position) {
                //近1小时
                0 -> {
                    //统计
                    recordUseCount(sku, ParamFixedValue.multi_device_compare_time_1_hour)
                    //赋值
                    ThConsV1.ONE_HOUR_MILLIS
                }
                //近1天
                1 -> {
                    //统计
                    recordUseCount(sku, ParamFixedValue.multi_device_compare_time_1_day)
                    //赋值
                    ThConsV1.ONE_DAY_MILLIS
                }
                //近1周
                2 -> {
                    //统计
                    recordUseCount(sku, ParamFixedValue.multi_device_compare_time_1_week)
                    //赋值
                    ThConsV1.ONE_WEEK_MILLIS
                }
                //近1个月
                3 -> {
                    //统计
                    recordUseCount(sku, ParamFixedValue.multi_device_compare_time_1_month)
                    //赋值
                    ThConsV1.ONE_MONTH_MILLIS
                }
                //近1年
                else -> {
                    //统计
                    recordUseCount(sku, ParamFixedValue.multi_device_compare_time_1_year)
                    //赋值
                    ThConsV1.ONE_YEAR_MILLIS
                }
            }
            startTime = endTime - timeInterval
            var firstValidTime = Long.MAX_VALUE
            for (lineIndex in lineIndexMap.keys) {
                lineIndexMap[lineIndex]?.let { deviceInfo ->
                    val goodsType = deviceInfo.first
                    val sku = deviceInfo.second.first
                    val device = deviceInfo.second.second
                    val fvTime = ThConsV1.getFirstValidTime(goodsType, sku, ThConsV1.getRealDeviceId(sku, device))
                    if (fvTime < firstValidTime) {
                        firstValidTime = fvTime
                    }
                }
            }
            //确认至少有一条曲线有数据
            if (firstValidTime > 0 && firstValidTime != Long.MAX_VALUE) {
                firstValidTime = firstValidTime.coerceAtLeast(startTime)
                for (lineIndex in lineIndexMap.keys) {
                    lineIndexMap[lineIndex]?.let { deviceInfo ->
                        val goodsType = deviceInfo.first
                        val sku = deviceInfo.second.first
                        val device = deviceInfo.second.second
                        val lineData = arrayListOf<TemHumPm>()
                        //时间点已被取整，若传该值去查询可能会遗漏首尾点，故开始时间-30s，解释时间+30s,确保首尾点能被查询到
                        val searchStartTime = startTime - 30 * 1000L
                        val searchEndTime = endTime + 30 * 1000L
                        if (ThConsV1.needUsedThp(goodsType, sku)) {
                            val lineDataThp = DbController.queryAllData4Thp(sku, ThConsV1.getRealDeviceId(sku, device), searchStartTime, searchEndTime)
                            when (sku) {
                                Constant4L5.H5112 -> {
                                    if (ThConsV1.isCustomDevice(sku, device)) {
                                        //探针2
                                        val lineData4Pb2 = arrayListOf<TemHumPm>()
                                        for (thp in lineDataThp) {
                                            var type = thp.from
                                            if (type != TemHum.FROM_TYPE_INVALID && !ThConsV1.isValidThValue(thp.pm25, 6000)) {
                                                type = TemHum.FROM_TYPE_INVALID
                                            }
                                            lineData4Pb2.add(TemHumPm(thp.pm25, 6000, 100, thp.time, type))
                                        }
                                        lineData.addAll(lineData4Pb2)
                                    } else {
                                        //探针1
                                        val lineData4Pb1 = arrayListOf<TemHumPm>()
                                        for (thp in lineDataThp) {
                                            var type = thp.from
                                            if (type != TemHum.FROM_TYPE_INVALID && !ThConsV1.isValidThValue(thp.tem, thp.hum)) {
                                                type = TemHum.FROM_TYPE_INVALID
                                            }
                                            lineData4Pb1.add(TemHumPm(thp.tem, thp.hum, 100, thp.time, type))
                                        }
                                        lineData.addAll(lineData4Pb1)
                                    }
                                    lineDataThp.clear()
                                }

                                else -> {
                                    lineData.addAll(lineDataThp)
                                }
                            }
                            lineDataThp.clear()
                        } else {
                            val lineDataTh = DbController.queryAllData(sku, device, searchStartTime, searchEndTime)
                            //将TemHum对像转换为TemHumPm
                            for (temHum in lineDataTh) {
                                lineData.add(temHum.thp)
                            }
                            //释放资源
                            lineDataTh.clear()
                        }
                        if (lineData.isEmpty() || lineData.first().time > firstValidTime) {
                            lineData.add(0, TemHumPm(0, 0, 0, firstValidTime, TemHumPm.FROM_TYPE_INVALID))
                        }
                        if (lineData.isEmpty() || lineData.last().time < endTime) {
                            lineData.add(TemHumPm(0, 0, 0, endTime, TemHumPm.FROM_TYPE_INVALID))
                        }
                        cacheDataMap[lineIndex] = Pair(Triple(goodsType, sku, device), lineData)
                    }
                }
            } else {
                for (lineIndex in lineIndexMap.keys) {
                    lineIndexMap[lineIndex]?.let { deviceInfo ->
                        cacheDataMap[lineIndex] = Pair(Triple(deviceInfo.first, deviceInfo.second.first, deviceInfo.second.second), arrayListOf())
                    }
                }
            }
            //先展示本地已有数据
            ld4SelectedTimePeriod.postValue(Triple(startTime, endTime, cacheDataMap))
            //根据要对比的时间段载入本地可能缺失的数据
            Vm4ThOpManager.instance()?.loadThcdManager?.loadThcdByMulti(
                startTime,
                endTime,
                lineIndexMap
            )
        }
    }

    /**
     * 自定义选择对比时间段
     */
    fun customSelectTimePeriod(startTime: Long, endTime: Long, needLoadThcd: Boolean, from: String, isFromSelectTime: Boolean = false) {
        SafeLog.i("xiaobing") { "Vm4MultiCompare--customSelectTimePeriod-->from=->${from}" }
        showLoading()
        globalLaunch(Dispatchers.IO) {
            <EMAIL> = startTime
            <EMAIL> = endTime
            var firstValidTime = Long.MAX_VALUE
            for (lineIndex in lineIndexMap.keys) {
                lineIndexMap[lineIndex]?.let { deviceInfo ->
                    val goodsType = deviceInfo.first
                    val sku = deviceInfo.second.first
                    val device = deviceInfo.second.second
                    val fvTime = ThConsV1.getFirstValidTime(goodsType, sku, ThConsV1.getRealDeviceId(sku, device))
                    if (fvTime in 1..<firstValidTime) {
                        firstValidTime = fvTime
                    }
                }
            }
            //确认至少有一条曲线有数据
            if (firstValidTime > 0 && firstValidTime != Long.MAX_VALUE) {
                firstValidTime = firstValidTime.coerceAtLeast(startTime)
                for (lineIndex in lineIndexMap.keys) {
                    lineIndexMap[lineIndex]?.let { deviceInfo ->
                        val goodsType = deviceInfo.first
                        val sku = deviceInfo.second.first
                        val device = deviceInfo.second.second
                        val lineData = arrayListOf<TemHumPm>()
                        //时间点已被取整，若传该值去查询可能会遗漏首尾点，故开始时间-30s，解释时间+30s,确保首尾点能被查询到
                        val searchStartTime = startTime - 30 * 1000L
                        val searchEndTime = endTime + 30 * 1000L
                        if (ThConsV1.needUsedThp(goodsType, sku)) {
                            val lineDataThp = DbController.queryAllData4Thp(sku, ThConsV1.getRealDeviceId(sku, device), searchStartTime, searchEndTime)
                            when (sku) {
                                Constant4L5.H5112 -> {
                                    if (ThConsV1.isCustomDevice(sku, device)) {
                                        //探针2
                                        val lineData4Pb2 = arrayListOf<TemHumPm>()
                                        for (thp in lineDataThp) {
                                            var type = thp.from
                                            if (type != TemHum.FROM_TYPE_INVALID && !ThConsV1.isValidThValue(thp.pm25, 6000)) {
                                                type = TemHum.FROM_TYPE_INVALID
                                            }
                                            lineData4Pb2.add(TemHumPm(thp.pm25, 6000, 100, thp.time, type))
                                        }
                                        lineData.addAll(lineData4Pb2)
                                    } else {
                                        //探针1
                                        val lineData4Pb1 = arrayListOf<TemHumPm>()
                                        for (thp in lineDataThp) {
                                            var type = thp.from
                                            if (type != TemHum.FROM_TYPE_INVALID && !ThConsV1.isValidThValue(thp.tem, thp.hum)) {
                                                type = TemHum.FROM_TYPE_INVALID
                                            }
                                            lineData4Pb1.add(TemHumPm(thp.tem, thp.hum, 100, thp.time, type))
                                        }
                                        lineData.addAll(lineData4Pb1)
                                    }
                                    lineDataThp.clear()
                                }

                                else -> {
                                    lineData.addAll(lineDataThp)
                                }
                            }
                        } else {
                            val lineDataTh = DbController.queryAllData(sku, device, searchStartTime, searchEndTime)
                            //将TemHum对像转换为TemHumPm
                            for (temHum in lineDataTh) {
                                lineData.add(temHum.thp)
                            }
                            //释放资源
                            lineDataTh.clear()
                        }
                        if (lineData.isEmpty() || lineData.first().time > firstValidTime) {
                            lineData.add(0, TemHumPm(0, 0, 0, firstValidTime, TemHumPm.FROM_TYPE_INVALID))
                        }
                        if (lineData.isEmpty() || lineData.last().time < endTime) {
                            lineData.add(TemHumPm(0, 0, 0, endTime, TemHumPm.FROM_TYPE_INVALID))
                        }
                        SafeLog.i("xiaobing") { "Vm4MultiCompare--customSelectTimePeriod-->key=->${JsonUtil.toJson(deviceInfo)},dataSize=->${lineData.size}" }
                        cacheDataMap[lineIndex] = Pair(Triple(goodsType, sku, device), lineData)
                    }
                }
            } else {
                for (lineIndex in lineIndexMap.keys) {
                    lineIndexMap[lineIndex]?.let { deviceInfo ->
                        cacheDataMap[lineIndex] = Pair(Triple(deviceInfo.first, deviceInfo.second.first, deviceInfo.second.second), arrayListOf())
                    }
                }
            }
            //先展示本地已有数据
            ld4SelectedTimePeriod.postValue(Triple(startTime, endTime, cacheDataMap))
            //根据要对比的时间段载入本地可能缺失的数据
            if (needLoadThcd) {
                Vm4ThOpManager.instance()?.loadThcdManager?.loadThcdByMulti(
                    startTime,
                    endTime,
                    lineIndexMap
                )
            }
            //统计
            if (isFromSelectTime) {
                recordUseCount(sku, ParamFixedValue.click_set_time_range)
            }
        }
    }

    /**
     * 添加对比设备
     */
    fun addDeviceToCompare(chartOp4Mc: ChartController4New, goodsType: Int, sku: String, device: String, bleAddress: String) {
        lastLineIndex++
        lineIndexMap[lastLineIndex] = Pair(goodsType, Triple(sku, device, bleAddress))
        chartOp4Mc.updateLineInfo(lineIndexMap)
        //统计
        when (lastLineIndex) {
            ThConsV1.LINE_2 -> {
                recordUseCount(this.sku, ParamFixedValue.user_two_device_compare)
            }

            ThConsV1.LINE_3 -> {
                recordUseCount(this.sku, ParamFixedValue.user_three_device_compare)
            }

            else -> {}
        }
        //先加载展示本地已有数据
        customSelectTimePeriod(startTime, endTime, true, "添加新设备,key=->${sku}_${device}...")
    }

    /**
     * 删除对比设备
     */
    fun deleteDeviceToCompare(chartOp4Mc: ChartController4New, goodsType: Int, sku: String, device: String) {
        showLoading()
        globalLaunch(Dispatchers.IO) {
            for (lineIndex in lineIndexMap.keys) {
                lineIndexMap[lineIndex]?.let { deviceInfo ->
                    if (deviceInfo.second.first == sku && deviceInfo.second.second == device) {
                        //移除相关数据
                        lineIndexMap.remove(lineIndex)
                        cacheDataMap.remove(lineIndex)
                        //曲线数据源须重置(line1始终存在)
                        val newCacheDataMap = HashMap<Int, Pair<Triple<Int, String, String>, ArrayList<TemHumPm>>>()
                        val newlineIndexMap = HashMap<Int, Pair<Int, Triple<String, String, String>>>()
                        if (lineIndexMap.keys.size == 2) {
                            cacheDataMap.remove(ThConsV1.LINE_1)?.let {
                                newCacheDataMap[ThConsV1.LINE_1] = it
                            }
                            lineIndexMap.remove(ThConsV1.LINE_1)?.let {
                                newlineIndexMap[ThConsV1.LINE_1] = it
                            }
                            cacheDataMap[ThConsV1.LINE_2]?.let {
                                newCacheDataMap[ThConsV1.LINE_2] = it
                            } ?: run {
                                cacheDataMap[ThConsV1.LINE_3]?.let {
                                    newCacheDataMap[ThConsV1.LINE_2] = it
                                }
                            }
                            lineIndexMap[ThConsV1.LINE_2]?.let {
                                newlineIndexMap[ThConsV1.LINE_2] = it
                            } ?: run {
                                lineIndexMap[ThConsV1.LINE_3]?.let {
                                    newlineIndexMap[ThConsV1.LINE_2] = it
                                }
                            }
                            lastLineIndex = ThConsV1.LINE_2
                        } else {
                            cacheDataMap.remove(ThConsV1.LINE_1)?.let {
                                newCacheDataMap[ThConsV1.LINE_1] = it
                            }
                            lineIndexMap.remove(ThConsV1.LINE_1)?.let {
                                newlineIndexMap[ThConsV1.LINE_1] = it
                            }
                            lastLineIndex = ThConsV1.LINE_1
                        }
                        //统计
                        when (lastLineIndex) {
                            ThConsV1.LINE_2 -> {
                                recordUseCount(<EMAIL>, ParamFixedValue.user_two_device_compare)
                            }

                            else -> {}
                        }
                        cacheDataMap.clear()
                        cacheDataMap = newCacheDataMap
                        lineIndexMap.clear()
                        lineIndexMap = newlineIndexMap
                        SafeLog.i("xiaobing") { "Vm4MultiCompare--deleteDeviceToCompare-->lineIndexList=->${JsonUtil.toJson(cacheDataMap.keys)}" }
                        //同步数据到其他位置
                        chartOp4Mc.updateLineInfo(lineIndexMap)
                        Vm4ThOpManager.instance()?.loadThcdManager?.removeLoadThcd(sku, device)
                        ld4SelectedTimePeriod.postValue(Triple(startTime, endTime, cacheDataMap))
                        return@globalLaunch
                    }
                }
            }
        }
    }
}
