package com.govee.thnew.ble.controller

import com.govee.base2home.Constant4L5
import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4TemWarningH5112

/**
 * <AUTHOR>
 * @date created on 2025/5/12
 * @description 温湿度计(H5112)-->读写温度告警信息的Controller
 */
class Controller4TemWarningH5112 : AbsControllerWithCallback {
    /**
     * 温度告警下限值*100
     */
    private var minTem = 0

    /**
     * 温度告警上限值*100
     */
    private var maxTem = 0
    private var openWarning = false

    /**
     * 延迟告警分钟(不支持，则默认为0)
     */
    private var delayPushTime = 0

    /**
     * 探针序号(只用于写操作)
     */
    private var probeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     *
     * @param openWarning
     * @param minTem
     * @param maxTem
     */
    constructor(
        openWarning: Boolean,
        minTem: Int,
        maxTem: Int,
        delayPushTime: Int = 0,
        probeIndex: Int,
        writeCallBack: ((result: Boolean) -> Unit)
    ) : super(writeCallBack) {
        this.openWarning = openWarning
        this.minTem = minTem
        this.maxTem = maxTem
        this.delayPushTime = delayPushTime
        this.probeIndex = probeIndex
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_tem_warning
    }

    override fun translateWrite(): ByteArray {
        val openWarningValue = if (openWarning) 1.toByte() else 0.toByte()
        val minHumValues = BleUtil.getSignedBytesFor2(minTem, false)
        val maxHumValues = BleUtil.getSignedBytesFor2(maxTem, false)
        return byteArrayOf(openWarningValue, minHumValues[0], minHumValues[1], maxHumValues[0], maxHumValues[1], delayPushTime.toByte(), probeIndex.toByte())
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val openWarning4Pb1 = validBytes[0].toInt() == 1
        val minTem4Pb1 = BleUtil.convertTwoBytesToShort(validBytes[1], validBytes[2])
        val maxTem4Pb1 = BleUtil.convertTwoBytesToShort(validBytes[3], validBytes[4])
        val delayPushTime4Pb1 = BleUtil.getUnsignedByte(validBytes[5])
        val openWarning4Pb2 = validBytes[6].toInt() == 1
        val minTem4Pb2 = BleUtil.convertTwoBytesToShort(validBytes[7], validBytes[8])
        val maxTem4Pb2 = BleUtil.convertTwoBytesToShort(validBytes[9], validBytes[10])
        val delayPushTime4Pb2 = BleUtil.getUnsignedByte(validBytes[11])
        Event4TemWarningH5112.sendSuc4Read(
            isWrite, commandType, proType,
            openWarning4Pb1, minTem4Pb1, maxTem4Pb1, delayPushTime4Pb1,
            openWarning4Pb2, minTem4Pb2, maxTem4Pb2, delayPushTime4Pb2
        )
        return true
    }

    override fun fail() {
        super.fail()
        Event4TemWarningH5112.sendFail(isWrite, commandType, proType)
    }
}