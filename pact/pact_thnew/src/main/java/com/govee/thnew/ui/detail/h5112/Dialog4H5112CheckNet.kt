package com.govee.thnew.ui.detail.h5112

import android.content.Context
import android.graphics.drawable.AnimationDrawable
import android.graphics.drawable.Drawable
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.view.WindowManager
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.util.ClickUtil
import com.govee.thnew.databinding.ThnewDialog4H5112CheckNetBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.util.recordUseCount
import com.ihoment.base2app.dialog.BaseEventDialog
import com.ihoment.base2app.ext.setVisibility
import com.ihoment.base2app.infra.FullDisplayConfig
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.ResUtil
import com.tk.mediapicker.utils.DensityUtil

/**
 * <AUTHOR>
 * @date created on 2025/6/6
 * @description H5112查找设备弹窗
 */
class Dialog4H5112CheckNet private constructor(context: Context) : BaseEventDialog(context), OnClickListener {

    private lateinit var cnBinding: ThnewDialog4H5112CheckNetBinding

    init {
        fullScreen()
        changeDialogOutside(false)
        immersionMode()
        updatePosition(0, 0, Gravity.BOTTOM)
    }

    companion object {
        private const val STEP_4_TO_CHECK = 1
        private const val STEP_4_CHECK_SUC = 2

        //失败：网关不在线
        private const val STEP_4_CHECK_FAIL_TYPE_1 = 3

        //失败：网关在线，子设备与网关断连
        private const val STEP_4_CHECK_FAIL_TYPE_2 = 4

        fun createDialog(context: Context): Dialog4H5112CheckNet {
            return Dialog4H5112CheckNet(context)
        }
    }

    override fun changeDialogOutside(isCanClick: Boolean) {
        super.changeDialogOutside(isCanClick)
        dialog.setCancelable(isCanClick)
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getLayoutView(): View {
        cnBinding = ThnewDialog4H5112CheckNetBinding.inflate(LayoutInflater.from(context))
        adaptationInsetTop()
        cnBinding.ivCloseIcon4CheckNet.setOnClickListener(this)
        cnBinding.tvRecheckBtn4CheckNet.setOnClickListener(this)
        cnBinding.tvCloseBtn4CheckNet.setOnClickListener(this)
        return cnBinding.root
    }

    /**
     * 显示联网检测的弹窗
     */
    fun showCheckNetDialog() {
        show()
        toCheckNet()
        //统计
        recordUseCount(Constant4L5.H5112, ParamFixedValue.click_check_network)
    }

    /**
     * 开始检测
     */
    private fun toCheckNet() {
        switchCheckUi(STEP_4_TO_CHECK)
        Vm4ThOpManager.instance()?.checkNet4H5112 { isOk4Gw, isOk4Sub ->
            when {
                isOk4Gw && isOk4Sub -> {
                    switchCheckUi(STEP_4_CHECK_SUC)
                }

                isOk4Gw && !isOk4Sub -> {
                    switchCheckUi(STEP_4_CHECK_FAIL_TYPE_2)
                }

                else -> {
                    switchCheckUi(STEP_4_CHECK_FAIL_TYPE_1)
                }
            }

        }
    }

    override fun onClick(v: View?) {
        if (ClickUtil.getInstance.clickQuick()) {
            return
        }
        v?.let {
            when (it) {
                cnBinding.ivCloseIcon4CheckNet,
                cnBinding.tvCloseBtn4CheckNet -> {
                    hide()
                }

                cnBinding.tvRecheckBtn4CheckNet -> {
                    toCheckNet()
                }

                else -> {}
            }
        }
    }

    private fun switchCheckUi(step: Int) {
        when (step) {
            STEP_4_TO_CHECK -> {
                cnBinding.ivLoadingIcon4CheckNet.setImageDrawable(ResUtil.getDrawable(com.govee.ui.R.drawable.anim_4_check_net))
                switchAmin(true)
                cnBinding.tvCheckingRemind4CheckNet.setVisibility(true)
                cnBinding.gpSucViews4CheckNet.setVisibility(false)
                cnBinding.gpErrorViews4CheckNet.setVisibility(false)
                cnBinding.tvCloseBtn4CheckNet.setVisibility(false)
                cnBinding.tvRecheckBtn4CheckNet.setVisibility(false)
            }

            STEP_4_CHECK_SUC -> {
                switchAmin(false)
                cnBinding.ivLoadingIcon4CheckNet.setImageDrawable(ResUtil.getDrawable(com.govee.ui.R.mipmap.h5044_gateway_pics_link_success))
                cnBinding.tvCheckingRemind4CheckNet.setVisibility(false)
                cnBinding.gpSucViews4CheckNet.setVisibility(true)
                cnBinding.gpErrorViews4CheckNet.setVisibility(false)
                cnBinding.tvCloseBtn4CheckNet.setVisibility(true)
                cnBinding.tvRecheckBtn4CheckNet.setVisibility(true)
            }

            STEP_4_CHECK_FAIL_TYPE_1 -> {
                switchAmin(false)
                cnBinding.ivLoadingIcon4CheckNet.setImageDrawable(ResUtil.getDrawable(com.govee.ui.R.mipmap.h5044_gateway_pics_link_fail))
                cnBinding.tvCheckingRemind4CheckNet.setVisibility(false)
                cnBinding.gpSucViews4CheckNet.setVisibility(false)
                cnBinding.gpErrorViews4CheckNet.setVisibility(true)
                cnBinding.tvCloseBtn4CheckNet.setVisibility(false)
                cnBinding.tvRecheckBtn4CheckNet.setVisibility(true)
            }

            STEP_4_CHECK_FAIL_TYPE_2 -> {
                switchAmin(false)
                cnBinding.ivLoadingIcon4CheckNet.setImageDrawable(ResUtil.getDrawable(com.govee.ui.R.mipmap.h5044_gateway_pics_link_fail_02))
                cnBinding.tvCheckingRemind4CheckNet.setVisibility(false)
                cnBinding.gpSucViews4CheckNet.setVisibility(false)
                cnBinding.gpErrorViews4CheckNet.setVisibility(true)
                cnBinding.tvCloseBtn4CheckNet.setVisibility(false)
                cnBinding.tvRecheckBtn4CheckNet.setVisibility(true)
            }

            else -> {}
        }
    }

    private fun switchAmin(start: Boolean) {
        //启动帧动画
        val drawable: Drawable = cnBinding.ivLoadingIcon4CheckNet.drawable
        if (start) {
            if (drawable is AnimationDrawable && !drawable.isRunning) {
                drawable.start()
            }
        } else {
            if (drawable is AnimationDrawable && drawable.isRunning) {
                drawable.stop()
            }
        }
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth()
    }

    override fun getHeight(): Int {
        return WindowManager.LayoutParams.MATCH_PARENT
    }

    /**
     * 适配顶部空档
     */
    private fun adaptationInsetTop() {
        var paddingTop = DensityUtil.dp2px(context, 34.0f + 10.5f)
        paddingTop += FullDisplayConfig.read().insetTop
        val lp = cnBinding.spAdapterPadding4CheckNet.layoutParams
        lp.height = paddingTop
        cnBinding.spAdapterPadding4CheckNet.layoutParams = lp
    }
}
