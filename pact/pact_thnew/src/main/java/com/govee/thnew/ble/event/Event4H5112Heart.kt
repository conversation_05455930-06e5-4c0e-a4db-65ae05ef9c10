package com.govee.thnew.ble.event

import com.govee.base2newth.AbsControllerEvent
import com.govee.base2newth.deviceitem.AbsModel4Th
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2025/5/6
 * @description H5112的心跳包处理事件
 */
class Event4H5112Heart private constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, false) {
    var tem4Pb1 = AbsModel4Th.INVALID_INT_VALUE
    var tem4Pb2 = AbsModel4Th.INVALID_INT_VALUE
    var hum = AbsModel4Th.INVALID_INT_VALUE
    var battery = AbsModel4Th.INVALID_INT_VALUE

    //延时告警是否已经生效
    var pb1TemWarnType = 0
    var pb1HumWarnType = 0
    var pb2TemWarnType = 0

    companion object {
        fun sendSuc(
            write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte,
            tem4Pb1: Int,
            hum: Int,
            tem4Pb2: Int,
            battery: Int,
            pb1TemWarnType: Int,
            pb1HumWarnType: Int,
            pb2TemWarnType: Int
        ) {
            val event = Event4H5112Heart(true, write, commandType, proType)
            event.tem4Pb1 = tem4Pb1
            event.hum = hum
            event.tem4Pb2 = tem4Pb2
            event.battery = battery
            event.pb1TemWarnType = pb1TemWarnType
            event.pb1HumWarnType = pb1HumWarnType
            event.pb2TemWarnType = pb2TemWarnType
            EventBus.getDefault().post(event)
        }
    }
}