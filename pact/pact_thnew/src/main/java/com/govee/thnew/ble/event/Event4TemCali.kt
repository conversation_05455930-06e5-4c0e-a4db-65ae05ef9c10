package com.govee.thnew.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->读写温度校准值的事件
 */
class Event4TemCali private constructor(result: <PERSON><PERSON>an, write: <PERSON>olean, commandType: Byte, proType: Byte) : AbsControllerEvent(result, write, commandType, proType, !write) {
    /**
     * 温度校准值*100
     */
    var temCali = 0

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(Event4TemCali(false, write, commandType, proType))
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, temCali: Int) {
            val event = Event4TemCali(true, write, commandType, proType)
            event.temCali = temCali
            EventBus.getDefault().post(event)
        }
    }
}