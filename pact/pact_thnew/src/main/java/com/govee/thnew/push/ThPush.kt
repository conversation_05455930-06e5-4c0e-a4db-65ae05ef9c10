package com.govee.thnew.push

import android.app.Notification
import android.app.NotificationChannel
import android.app.NotificationManager
import android.content.Context.NOTIFICATION_SERVICE
import android.os.Bundle
import android.text.TextUtils
import com.govee.base2home.Constant4L5
import com.govee.base2home.config.DeviceRoomOrderConfig
import com.govee.base2home.main.AbsDevice
import com.govee.base2home.push.IPush
import com.govee.base2home.push.PushUtil
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.deviceitem.Ext4Gw
import com.govee.base2newth.other.Config4ThAudibleAlarm
import com.govee.kt.GlobalScopeUtils
import com.govee.push.NotificationConfig
import com.govee.push.NotifyManager
import com.govee.push.PushData
import com.govee.push.event.NotificationClickEvent
import com.govee.thnew.add.AddInfo
import com.govee.thnew.config.ThConfig4Setting
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.ui.detail.Ac4ThCommonDetailNew
import com.govee.ui.R
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.ext.isFalse
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.App2ForegroundUtil
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.ResUtil

/**
 * <AUTHOR>
 * @date created on 2024/2/20
 * @description 温湿度计-->通知处理类
 */
class ThPush : IPush {

    init {
        //注册报警通知渠道
        val channelList = arrayListOf<NotificationChannel>()
        for (sku in Constant4L5.supportThSkuList4NewThWarn()) {
            if (Constant4L5.supportImportantLevel(sku).isFalse()) {
                continue
            }
            // Create the NotificationChannel.
            val name = ResUtil.getString(R.string.channel_id_warning_string)
            val importance = NotificationManager.IMPORTANCE_LOW
            val mChannel = NotificationChannel(Constant4L5.getImportanceNotificationChannelId(sku), name, importance)
            mChannel.description = sku
            channelList.add(mChannel)
        }
        // Register the channel with the system. You can't change the importance
        // or other notification behaviors after this.
        BaseApplication.getContext().getSystemService(NOTIFICATION_SERVICE)?.let {
            val notificationManager = it as NotificationManager
            notificationManager.createNotificationChannels(channelList)
        }
    }

    override fun checkPushData(sku: String, goodType: Int, messageType: String): Boolean {
        return ThConfig4Support.supportPush(goodType, sku)
    }

    override fun showPushData(pushData: PushData) {
        val data: String = pushData.data
        SafeLog.e("xiaobing") { "THPush--showPushData-->${JsonUtil.toJson(pushData)}" }
        val msg: Msg = JsonUtil.fromJson(data, Msg::class.java)
        if (msg.isInvalidMsg) return
        var title = msg.contentTitle
        if (TextUtils.isEmpty(title) || !TextUtils.isEmpty(msg.type)) {
            title = msg.type
        }
        val content = msg.message
        val notifyId: Int = PushUtil.getNotifyId()
        val supportImportLevel = Constant4L5.supportImportantLevel(pushData.sku)
        val channelId: String = if (supportImportLevel) Constant4L5.getImportanceNotificationChannelId(pushData.sku) else ThConsV1.CHANNEL_ID
        val channelStr: String = ResUtil.getString(R.string.channel_id_warning_string)
        val iconRes: Int = R.mipmap.new_icon_push
        val config = NotificationConfig(title, content, iconRes, true, false, false, Notification.DEFAULT_ALL, channelId, channelStr)
        when (pushData.sku) {
            Constant4L5.H5111,
            Constant4L5.H5112,
                -> {
                NotifyManager.getInstance().showNotifyByFullData(
                    BaseApplication.getContext(),
                    notifyId,
                    config,
                    pushData::class.java.name,
                    JsonUtil.toJson(pushData)
                )
            }

            else -> {
                NotifyManager.getInstance().showNotify(BaseApplication.getContext(), notifyId, config)
            }
        }

        //新增声音告警
        val supportWarnSetting = ThConfig4Setting.supportWarnSetting(pushData.goodsType, pushData.sku) == ThConfig4Setting.TH_WARN_TYPE_4_OLD
        val isAudibleOpen = Config4ThAudibleAlarm.getConfig().getAudibleStatus(pushData.sku, pushData.device)
        if (supportWarnSetting && isAudibleOpen) {
            when (msg.type) {
                ThConsV1.TemHigh,
                ThConsV1.TemLow,
                ThConsV1.HumHigh,
                ThConsV1.HumLow,
                    -> {
                    msg.contentTitle = ResUtil.getString(R.string.th_text_4_alarm_notification)
                    ThWarnManager.instance.addAudibleAlarm(pushData.sku, pushData.device, msg)
                }

                else -> {}
            }
        }
    }

    override fun checkConsumePush(event: NotificationClickEvent): Boolean {
        val json = event.fullData4Json
        if (json.isNullOrEmpty()) return false
        val pushData = JsonUtil.fromJson(json, PushData::class.java) ?: return false
        when (pushData.sku) {
            Constant4L5.H5111 -> {
                return try2DeviceDetailAc(pushData)
            }

            else -> {}
        }
        return false
    }

    private fun try2DeviceDetailAc(pushInfo: PushData): Boolean {
        App2ForegroundUtil.toForeground(true)
        val isAcExit = Ac4ThCommonDetailNew.isAcExist(pushInfo.sku, pushInfo.device)
        if (isAcExit) {
            //当前页面存在-则无需重新拉起界面
            return false
        }
        GlobalScopeUtils.byFind<AbsDevice?>({
            DeviceRoomOrderConfig.read().findDevice(pushInfo.sku, pushInfo.device)
        }, success = {
            it?.run {
                deviceExt?.deviceSettings?.let { settings ->
                    JsonUtil.fromJson(settings, AddInfo::class.java)?.let { bindExt ->
                        bindExt.goodsType = goodsType
                        bindExt.sku = sku
                        bindExt.device = device
                        bindExt.bleHardVersion = versionHard
                        bindExt.bleSoftVersion = versionSoft
                        val arguments = Bundle().apply {
                            putParcelable(ThConsV1.KEY_4_ADD_INFO, bindExt)
                            JsonUtil.fromJson(settings, Ext4Gw::class.java)?.gatewayInfo?.let { gatewayInfo ->
                                putParcelable(ThConsV1.KEY_4_GATEWAY_INFO, gatewayInfo)
                            }
                        }
                        PushUtil.jumpToPushHomeAc(Ac4ThCommonDetailNew::class.java, arguments, true)
                    }
                }
            }
        })
        return true
    }
}
