package com.govee.thnew.ui.compare

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.View
import android.view.ViewGroup.LayoutParams
import android.widget.PopupWindow
import androidx.activity.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.govee.base2home.Constant
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.reform4dbgw.router.RouterRuler
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ac.AbsAc
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.setVisibility
import com.govee.base2kt.ext.toast
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.DataGroup
import com.govee.base2newth.chart.IntervalType
import com.govee.base2newth.chart.TimeUtil
import com.govee.base2newth.chart.newchart.ChartController4New
import com.govee.base2newth.chart.newchart.ConfigParams
import com.govee.base2newth.chart.newchart.DbDataVm4New
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.db.TemHum
import com.govee.base2newth.db.TemHumPm
import com.govee.base2newth.other.Config4SingleChartType
import com.govee.base2newth.other.SyncTemUnitUtil
import com.govee.thnew.add.AddInfo
import com.govee.thnew.ble.event.ReleaseVMEvent
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.databinding.ThnewAc4SingleDevCompareBinding
import com.govee.thnew.databinding.ThnewPop4SingleCompareIntroBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.ui.R
import com.govee.ui.dialog.TimeDialogV18
import com.govee.util.recordUseCount
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import com.tk.mediapicker.utils.DensityUtil
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2024/4/2
 * @description 温湿度计=->单设备图表数据对比页面
 */
@Route(path = RouterRuler.ROUTER_PATH_4_THCD_SINGLE_COMPARE)
class Ac4ThSingleDevCompare : AbsAc<ThnewAc4SingleDevCompareBinding>() {

    companion object {
        fun jump2SingleDevCompare(context: Context, settingStr: String, probeIndex: Int = -1) {
            val bundle = Bundle()
            bundle.putString(Constant.intent_ac_key_setting_str, settingStr)
            bundle.putInt(Constant4L5.KEY_4_TH_PROBE_INDEX, probeIndex)
            JumpUtil.jump(context, Ac4ThSingleDevCompare::class.java, bundle)
        }
    }

    private lateinit var bindExt: AddInfo
    private var probeIndex = -1

    private val vm4Sc by lazy {
        ViewModelProvider(this)[Vm4SingleCompare::class.java].apply {
            init(this@Ac4ThSingleDevCompare, bindExt.goodsType, bindExt.sku, bindExt.device)
        }
    }

    /**
     * 数据展示的vm
     */
    private val vm4ChartData: DbDataVm4New by viewModels()

    /**
     * 图表展示控制类
     */
    private lateinit var chartOp4ThSc: ChartController4New

    /**
     * 当前选中的图表时间段
     */
    private var selectedDataGroup: DataGroup? = DataGroup.hour

    /**
     * 对比数据的时间段
     */
    private lateinit var mainLineTsPair: Pair<Long, Long>

    /**
     * line1和line2的首尾间隔时长
     */
    private var intervalTime4Lines = 0L

    private val selectChartTypeDialog by lazy {
        Dialog4ThSelectChartType.createDialog(this@Ac4ThSingleDevCompare, bindExt.goodsType, bindExt.sku, bindExt.device, true, probeIndex)
    }

    private val selectedPeriodAdapter by lazy {
        Adapter4FastSelectTime(this, arrayListOf(TimePeriod().apply {
            selectTime = ResUtil.getString(R.string.th_text_4_sc_last_2_hours)
        }, TimePeriod().apply {
            selectTime = ResUtil.getString(R.string.th_text_4_sc_last_2_days)
            selected = true
        }, TimePeriod().apply {
            selectTime = ResUtil.getString(R.string.th_text_4_sc_last_2_weeks)
        }, TimePeriod().apply {
            selectTime = ResUtil.getString(R.string.th_text_4_sc_last_2_mouths)
        }, TimePeriod().apply {
            selectTime = ResUtil.getString(R.string.th_text_4_sc_last_2_years)
        })) {
            vm4Sc.fastSelectTimePeriod(it)
        }
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.thnew.R.id.ac_container
    }

    override fun adapterInsetViewId(): Int {
        return com.govee.thnew.R.id.sp_top_adjust_holder
    }

    override fun layoutId(): Int {
        return com.govee.thnew.R.layout.thnew_ac_4_single_dev_compare
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initData()
        initUi()
        initOpClick()
        initObserver()
        //统计
        recordUseCount(bindExt.sku, ParamFixedValue.click_compare_history_data)
    }

    private fun initData() {
        val settingStr = intent.getStringExtra(Constant.intent_ac_key_setting_str) ?: ""
        bindExt = JsonUtil.fromJson(settingStr, AddInfo::class.java) ?: run {
            toast(R.string.h721214_other_listen)
            finish()
            return
        }
        probeIndex = intent.getIntExtra(Constant4L5.KEY_4_TH_PROBE_INDEX, -1)
        //从其他工程的温湿度计详情页跳转至此，会执行Vm4ThOpManager的初始化
        Vm4ThOpManager.init(bindExt, null, Vm4ThOpManager.INIT_FROM_COMPARE)
    }

    private fun initUi() {
        viewBinding.let { vb ->
            vb.rvTimeRange4SingleCompare.layoutManager = LinearLayoutManager(this, RecyclerView.HORIZONTAL, false)
            vb.rvTimeRange4SingleCompare.adapter = selectedPeriodAdapter
        }
        updateTemUnit(Vm4ThOpManager.instance()?.isFahOpen4Compare() ?: false)
        initChartSet()
    }

    /**
     * 更新图表相关配置
     */
    private fun initChartSet() {
        val warnRange = when (bindExt.sku) {
            Constant4L5.H5112 -> {
                when (probeIndex) {
                    Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                        WarnConfig.read().queryWarningRangeByKey(bindExt.sku, bindExt.device)
                    }

                    Constant4L5.PROBE_INDEX_4_TEM -> {
                        WarnConfig.read().queryWarningRangeByKey(bindExt.sku, ThConsV1.getH5112Device4Pb2(bindExt.device))
                    }

                    else -> {
                        null
                    }
                }
            }

            else -> {
                WarnConfig.read().queryWarningRangeByKey(bindExt.sku, bindExt.device)
            }
        }
        warnRange?.let {
            //配置处理数据的校准值
            vm4ChartData.setCali(it.temCali, it.humCali)
        } ?: run {
            vm4ChartData.setCali(0, 0)
        }
        //图表初始化相关设置
        chartOp4ThSc = ChartController4New(this, ConfigParams().apply {
            //设备信息
            goodsType = bindExt.goodsType
            sku = bindExt.sku
            device = when (bindExt.sku) {
                Constant4L5.H5112 -> {
                    if (probeIndex == Constant4L5.PROBE_INDEX_4_TEM_HUM) {
                        bindExt.device
                    } else {
                        ThConsV1.getH5112Device4Pb2(bindExt.device)
                    }
                }

                else -> {
                    bindExt.device
                }
            }
            bleAddress = bindExt.address
            bleSv = bindExt.bleSoftVersion
            bleHv = bindExt.bleHardVersion
            //初始化配置参数
            isHistoryCompare = true
        }).apply {
            //设置时间段显示切换的监听
            setChartListener(object : ChartController4New.ChartListener {
                override fun beScale() {
                    viewBinding.cspvBottomBarBg4SingleCompare.clearSelect()
                }

                override fun timeChange(startTimeStamp: Long, endTimeStamp: Long, intervalType: IntervalType) {
                    updateTime(startTimeStamp, endTimeStamp, intervalType)
                }

                override fun hasDrawnChart() {
                    hideLoading()
                }
            })
            setPointEndNum(0, 1, 1, 1, 2)
        }
        //图表相关试图初始化
        viewBinding.let { vb ->
            //co2
            vb.co2Container.ttcPm25Chart4ThCompare.setChart(chartOp4ThSc)
            //pm25
            vb.pm25Container.ttcPm25Chart4ThCompare.setChart(chartOp4ThSc)
            //温度
            vb.temContainer.ttcTemChart4ThCompare.setChart(chartOp4ThSc)
            //湿度
            vb.humContainer.ttcHumChart4ThCompare.setChart(chartOp4ThSc)
            //露点
            vb.dpContainer.ttcDpChart4ThCompare.setChart(chartOp4ThSc)
            //VPD
            vb.vpdContainer.ttcVpdChart4ThCompare.setChart(chartOp4ThSc)
        }
        updateShowCharts()
    }

    /**
     * 根据选择的类型显示相应图表
     */
    private fun updateShowCharts() {
        val showCo2 = bindExt.sku == Constant4L5.H5140
        var showPm25 = bindExt.sku == Constant4L5.H5106
        var showTem = true
        val supportHum = when (bindExt.sku) {
            Constant4L5.H5112 -> {
                probeIndex == Constant4L5.PROBE_INDEX_4_TEM_HUM
            }

            else -> {
                ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku)
            }
        }
        var showHum = supportHum
        var showDp = supportHum
        var showVpd = supportHum
        Config4SingleChartType.getConfig().getChartTypes(bindExt.sku, bindExt.device)?.let {
            for (chartType in it) {
                when (chartType.type) {
                    ThConsV1.PM25 -> {
                        showPm25 = showPm25 && chartType.selected
                    }

                    ThConsV1.TEM -> {
                        showTem = showTem && chartType.selected
                    }

                    ThConsV1.HUM -> {
                        showHum = showHum && chartType.selected
                    }

                    ThConsV1.DP -> {
                        showDp = showDp && chartType.selected
                    }

                    ThConsV1.VPD -> {
                        showVpd = showVpd && chartType.selected
                    }

                    else -> {}
                }
            }
        } ?: run {
            //默认只有温度、湿度是展示的
            showPm25 = false
            showDp = false
            showVpd = false
        }
        viewBinding.let { vb ->
            vb.co2Container.root.setVisibility(showCo2)
            vb.pm25Container.root.setVisibility(showPm25)
            vb.temContainer.root.setVisibility(showTem)
            vb.humContainer.root.setVisibility(showHum)
            vb.dpContainer.root.setVisibility(showDp)
            vb.vpdContainer.root.setVisibility(showVpd)
        }
    }

    /**
     * 更新温度单位的显示
     */
    private fun updateTemUnit(fahOpen: Boolean) {
        viewBinding.let { vb ->
            vb.temContainer.ivTemUnitIcon4ThCompare.setImageDrawable(ResUtil.getDrawable(if (fahOpen) R.mipmap.new_sensor_setting_switch_fahrenheit else R.mipmap.new_sensor_setting_switch_celsius))
            vb.temContainer.ttcTemChart4ThCompare.setChangeValueShow(fahOpen, if (fahOpen) getString(R.string.tem_unit_fah) else getString(R.string.tem_unit_cel))
            vb.dpContainer.ttcDpChart4ThCompare.setChangeValueShow(fahOpen, if (fahOpen) getString(R.string.tem_unit_fah) else getString(R.string.tem_unit_cel))
        }
    }

    /**
     * 更新图表的展示时间段
     */
    @SuppressLint("SetTextI18n")
    private fun updateTime(startTs4Line1: Long, endTs4Line1: Long, intervalType: IntervalType) {
        val ts4Lin1 = selectedDataGroup?.let {
            //在默认标签下，需要格式时间起点的一致
            TimeUtil.calibration(startTs4Line1, endTs4Line1, it)
        } ?: longArrayOf(startTs4Line1, endTs4Line1)
        val startTs4Line2 = ts4Lin1[0] - intervalTime4Lines
        val endTs4Line2 = ts4Lin1[1] - intervalTime4Lines
        val formatSt4Line1: String
        val formatEt4Line1: String
        val formatSt4Line2: String
        val formatEt4Line2: String
        if (IntervalType.year_1_month == intervalType) {
            formatSt4Line1 = TimeFormatM.getInstance().formatTimeToYM(ts4Lin1[0])
            formatEt4Line1 = TimeFormatM.getInstance().formatTimeToYM(ts4Lin1[1])
            formatSt4Line2 = TimeFormatM.getInstance().formatTimeToYM(startTs4Line2)
            formatEt4Line2 = TimeFormatM.getInstance().formatTimeToYM(endTs4Line2)
        } else {
            formatSt4Line1 = TimeFormatM.getInstance().formatTimeToHMMD(ts4Lin1[0])
            formatEt4Line1 = TimeFormatM.getInstance().formatTimeToHMMD(ts4Lin1[1])
            formatSt4Line2 = TimeFormatM.getInstance().formatTimeToHMMD(startTs4Line2)
            formatEt4Line2 = TimeFormatM.getInstance().formatTimeToHMMD(endTs4Line2)
        }
        //更新展示时间
        viewBinding.let { vb ->
            //line1的展示时间
            vb.co2Container.tvPm25StartTime14ThCompare.text = formatSt4Line1
            vb.co2Container.tvPm25EndTime14ThCompare.text = formatEt4Line1
            vb.pm25Container.tvPm25StartTime14ThCompare.text = formatSt4Line1
            vb.pm25Container.tvPm25EndTime14ThCompare.text = formatEt4Line1
            vb.temContainer.tvTemStartTime14ThCompare.text = formatSt4Line1
            vb.temContainer.tvTemEndTime14ThCompare.text = formatEt4Line1
            vb.humContainer.tvHumStartTime14ThCompare.text = formatSt4Line1
            vb.humContainer.tvHumEndTime14ThCompare.text = formatEt4Line1
            vb.dpContainer.tvDpStartTime14ThCompare.text = formatSt4Line1
            vb.dpContainer.tvDpEndTime14ThCompare.text = formatEt4Line1
            vb.vpdContainer.tvVpdStartTime14ThCompare.text = formatSt4Line1
            vb.vpdContainer.tvVpdEndTime14ThCompare.text = formatEt4Line1
            //line2的展示时间
            vb.co2Container.tvPm25StartTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatSt4Line2})"
            vb.co2Container.tvPm25EndTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatEt4Line2})"
            vb.pm25Container.tvPm25StartTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatSt4Line2})"
            vb.pm25Container.tvPm25EndTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatEt4Line2})"
            vb.temContainer.tvTemStartTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatSt4Line2})"
            vb.temContainer.tvTemEndTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatEt4Line2})"
            vb.humContainer.tvHumStartTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatSt4Line2})"
            vb.humContainer.tvHumEndTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatEt4Line2})"
            vb.dpContainer.tvDpStartTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatSt4Line2})"
            vb.dpContainer.tvDpEndTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatEt4Line2})"
            vb.vpdContainer.tvVpdStartTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatSt4Line2})"
            vb.vpdContainer.tvVpdEndTime24ThCompare.also { it.setVisibility(true) }.text = "(${formatEt4Line2})"
        }
    }

    private fun updateFresh(showLoading: Boolean) {
        viewBinding.run {
            val lp: LayoutParams = llcLoadingContainer4SingleCompare.layoutParams
            if (showLoading) {
                lp.height = (AppUtil.getScreenWidth() * ThConsV1.FRESH_HEIGHT_PERCENT_SCREEN_WIDTH).toInt()
            } else {
                tvRefreshDes4ThDetail.setText(R.string.fresh_des_loading)
                lp.height = 0
            }
            llcLoadingContainer4SingleCompare.setVisibility(showLoading)
            llcLoadingContainer4SingleCompare.layoutParams = lp
        }
    }

    private fun initOpClick() {
        viewBinding.let { vb ->
            vb.ivBackBtn4SingleCompare.clickDelay {
                onBackPressed()
            }
            vb.ivIntroIcon4SingleCompare.clickDelay {
                getIntroPop().showAsDropDown(vb.ivIntroIcon4SingleCompare)
            }
            vb.tvFilterLabel4SingleCompare.clickDelay {
                //点击筛选
                selectChartTypeDialog.show {
                    updateShowCharts()
                }
                //统计
                recordUseCount(bindExt.sku, ParamFixedValue.click_filter)
            }
            //自定义选择line1的时间段
            vb.vLine1TimeBg4SingleCompare.clickDelay {
                vm4Sc.ld4SelectedTimePeriod.value?.let {
                    TimeDialogV18.createDialog(
                        this, ResUtil.getString(R.string.filter_date_title), true, it.first.first, it.first.second, vm4Sc.minValidTime, vm4Sc.lastDataTime,
                        needHour = true, listener = object : TimeDialogV18.DoneListener {
                            override fun chooseTime(startTime: Long, endTime: Long) {
                                selectedPeriodAdapter.clearSelected()
                                val compareTimeInterval = endTime - startTime
                                val line2EndTime = startTime
                                val line2StartTime = line2EndTime - compareTimeInterval
                                vm4Sc.customSelectTimePeriod(startTime, endTime, line2StartTime, line2EndTime, isChangeLine1 = true, needToLoading = true)
                            }
                        }).setEventKey(TAG).show()
                    //统计
                    recordUseCount(bindExt.sku, ParamFixedValue.click_set_time_range_1)
                }
            }
            //自定义选择line2的时间段
            vb.vLine2TimeBg4SingleCompare.clickDelay {
                vm4Sc.ld4SelectedTimePeriod.value?.let {
                    val compareTimeInterval = it.first.second - it.first.first
                    //line2可选4年
                    TimeDialogV18.createDialog(
                        this, ResUtil.getString(R.string.filter_date_title), true, it.second.first, it.second.second, vm4Sc.minValidTime - ThConsV1.ONE_YEAR_MILLIS * 2, vm4Sc.lastDataTime - ThConsV1.ONE_HOUR_MILLIS,
                        compareTimeInterval = compareTimeInterval, needHour = true, listener = object :
                            TimeDialogV18.DoneListener {
                            override fun chooseTime(startTime: Long, endTime: Long) {
                                selectedPeriodAdapter.clearSelected()
                                vm4Sc.customSelectTimePeriod(it.first.first, it.first.second, startTime, endTime, isChangeLine1 = false, needToLoading = true)
                            }
                        }).setEventKey(TAG).show()
                    //统计
                    recordUseCount(bindExt.sku, ParamFixedValue.click_set_time_range_2)
                }
            }
            //切换温度单位
            vb.temContainer.ivTemUnitIcon4ThCompare.clickDelay {
                val fahOpen = !(Vm4ThOpManager.instance()?.isFahOpen4Compare() ?: false)
                Vm4ThOpManager.instance()?.setTemUnit4Compare(fahOpen)
                updateTemUnit(fahOpen)
                //同步温度单位到服务端
                SyncTemUnitUtil.syncTemUnit(Transactions().createTransaction(), bindExt.sku, fahOpen)
                //通知详情页
                Vm4ThOpManager.instance()?.ld4ChangeTemUnit?.value = true
            }
            //切换图表展示比例
            vb.cspvBottomBarBg4SingleCompare.setSelectListener { type, dataGroup ->
                selectedDataGroup = dataGroup
                if (type != null) {
                    chartOp4ThSc.setIntervalType(type)
                }
            }
            //导出数据
            vb.tvExportDataBtn4SingleCompare.clickDelay {
                chartOp4ThSc.hideAllPointInTime()
                Vm4ThOpManager.instance()?.exportData4Compare(arrayListOf<View>().apply {
                    add(vb.clExportContent14SingleCompare)
                    add(vb.clExportContent24SingleCompare)
                })
            }
        }
    }

    /**
     * 历史数据对比介绍弹窗
     */
    private fun getIntroPop(): PopupWindow {
        val popViewBinding = ThnewPop4SingleCompareIntroBinding.inflate(layoutInflater)
        viewBinding.let { vb ->
            val rightOffset = AppUtil.getScreenWidth() - vb.ivIntroIcon4SingleCompare.right + (vb.ivIntroIcon4SingleCompare.width - DensityUtil.dp2px(this@Ac4ThSingleDevCompare, 8.0f)) / 2
            val rightHoldLp = popViewBinding.spRightOffset4ScPop.layoutParams
            rightHoldLp.width = rightOffset
            popViewBinding.spRightOffset4ScPop.layoutParams = rightHoldLp
        }
        val popWindow = PopupWindow(this)
        popWindow.width = AppUtil.getScreenWidth()
        popWindow.contentView = popViewBinding.root.apply {
            layoutParams = LayoutParams(AppUtil.getScreenWidth(), LayoutParams.WRAP_CONTENT)
        }
        popWindow.setBackgroundDrawable(null)
        popWindow.isOutsideTouchable = true
        popWindow.isTouchable = true
        popWindow.isFocusable = true
        return popWindow
    }

    @SuppressLint("SetTextI18n")
    private fun initObserver() {
        vm4Sc.loadingChange.showDialog.observeInActivity(this) {
            showLoading()
        }
        vm4Sc.loadingChange.dismissDialog.observeInActivity(this) {
            hideLoading()
        }
        vm4Sc.ld4SelectedTimePeriod.observe(this) {
            //赋值
            mainLineTsPair = Pair(it.first.first, it.first.second)
            intervalTime4Lines = it.first.first - it.second.first
            //处理选择时长显示
            val line1StartFormatTime = TimeFormatM.getInstance().formatTimeToHMYMD(it.first.first)
            val line1EndFormatTime = TimeFormatM.getInstance().formatTimeToHMYMD(it.first.second)
            val line2StartFormatTime = TimeFormatM.getInstance().formatTimeToHMYMD(it.second.first)
            val line2EndFormatTime = TimeFormatM.getInstance().formatTimeToHMYMD(it.second.second)
            viewBinding.let { vb ->
                vb.tvLine1TimeValue4SingleCompare.text = "$line1StartFormatTime - $line1EndFormatTime"
                vb.tvLine2TimeValue4SingleCompare.text = "$line2StartFormatTime - $line2EndFormatTime"
            }
            //处理图表数据
            vm4ChartData.dealAllLineThpData(HashMap<Int, Pair<Triple<Int, String, String>, ArrayList<TemHumPm>>>().apply {
                when (bindExt.sku) {
                    Constant4L5.H5112 -> {
                        when (probeIndex) {
                            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                                val line1Data = arrayListOf<TemHumPm>()
                                for (thp in it.first.third) {
                                    var type = thp.from
                                    if (type != TemHum.FROM_TYPE_INVALID && !ThConsV1.isValidThValue(thp.tem, thp.hum)) {
                                        type = TemHum.FROM_TYPE_INVALID
                                    }
                                    line1Data.add(TemHumPm(thp.tem, thp.hum, 100, thp.time, type))
                                }
                                val line2Data = arrayListOf<TemHumPm>()
                                for (thp in it.second.third) {
                                    var type = thp.from
                                    if (type != TemHum.FROM_TYPE_INVALID && !ThConsV1.isValidThValue(thp.tem, thp.hum)) {
                                        type = TemHum.FROM_TYPE_INVALID
                                    }
                                    line2Data.add(TemHumPm(thp.tem, thp.hum, 100, thp.time, type))
                                }
                                put(ThConsV1.LINE_1, Pair(Triple(bindExt.goodsType, bindExt.sku, bindExt.device), line1Data))
                                put(ThConsV1.LINE_2, Pair(Triple(bindExt.goodsType, bindExt.sku, bindExt.device), line2Data))
                            }

                            Constant4L5.PROBE_INDEX_4_TEM -> {
                                val line1Data = arrayListOf<TemHumPm>()
                                for (thp in it.first.third) {
                                    var type = thp.from
                                    if (type != TemHum.FROM_TYPE_INVALID && !ThConsV1.isValidThValue(thp.pm25, 6000)) {
                                        type = TemHum.FROM_TYPE_INVALID
                                    }
                                    line1Data.add(TemHumPm(thp.pm25, 6000, 100, thp.time, type))
                                }
                                val line2Data = arrayListOf<TemHumPm>()
                                for (thp in it.second.third) {
                                    var type = thp.from
                                    if (type != TemHum.FROM_TYPE_INVALID && !ThConsV1.isValidThValue(thp.pm25, 6000)) {
                                        type = TemHum.FROM_TYPE_INVALID
                                    }
                                    line2Data.add(TemHumPm(thp.pm25, 6000, 100, thp.time, type))
                                }
                                put(ThConsV1.LINE_1, Pair(Triple(bindExt.goodsType, bindExt.sku, ThConsV1.getH5112Device4Pb2(bindExt.device)), line1Data))
                                put(ThConsV1.LINE_2, Pair(Triple(bindExt.goodsType, bindExt.sku, ThConsV1.getH5112Device4Pb2(bindExt.device)), line2Data))
                            }

                            else -> {}
                        }
                    }

                    else -> {
                        put(ThConsV1.LINE_1, Pair(Triple(bindExt.goodsType, bindExt.sku, bindExt.device), it.first.third))
                        put(ThConsV1.LINE_2, Pair(Triple(bindExt.goodsType, bindExt.sku, bindExt.device), it.second.third))
                    }
                }
            })
        }
        //给vm4Data加入生命周期监听
        lifecycle.addObserver(vm4ChartData)
        vm4ChartData.thpDataLiveData.observe(this) {
            chartOp4ThSc.updateRealData(it, mainLineTsPair, intervalTime4Lines) {
                val curCompareTimeDis = mainLineTsPair.second - mainLineTsPair.first
                //根据对比时间段选择默认选中的展示时间段
                when {
                    curCompareTimeDis < ThConsV1.ONE_DAY_MILLIS -> {
                        viewBinding.cspvBottomBarBg4SingleCompare.updateDataGroupChoose(DataGroup.hour)
                    }

                    curCompareTimeDis < ThConsV1.ONE_WEEK_MILLIS -> {
                        viewBinding.cspvBottomBarBg4SingleCompare.updateDataGroupChoose(DataGroup.day)
                    }

                    curCompareTimeDis < ThConsV1.ONE_MONTH_MILLIS -> {
                        viewBinding.cspvBottomBarBg4SingleCompare.updateDataGroupChoose(DataGroup.week)
                    }

                    curCompareTimeDis < ThConsV1.ONE_YEAR_MILLIS -> {
                        viewBinding.cspvBottomBarBg4SingleCompare.updateDataGroupChoose(DataGroup.month)
                    }

                    curCompareTimeDis <= 2 * ThConsV1.ONE_YEAR_MILLIS -> {
                        viewBinding.cspvBottomBarBg4SingleCompare.updateDataGroupChoose(DataGroup.year)
                    }
                }
            }
        }
        Vm4ThOpManager.instance()?.let { vm ->
            vm.loadThcdManager.ld4BleLoadThCdProgress.observe(this) { progressInfo ->
                if (!hasCreated) {
                    return@observe
                }
                if (bindExt.sku == progressInfo.first.first && bindExt.device == progressInfo.first.second) {
                    when (progressInfo.second.first) {
                        Vm4ThOpManager.RC_FROM_DETAIL,
                        Vm4ThOpManager.RC_FROM_COMPARE_DATA,
                            -> {
                            var percentStr = "${progressInfo.second.second}%"
                            percentStr = String.format(getString(R.string.h5072_fresh_des_syncing), percentStr)
                            viewBinding.tvRefreshDes4ThDetail.text = percentStr
                        }

                        else -> {}
                    }
                }
            }
            vm.loadThcdManager.ld4LoadThCdStep.observe(this) {
                it[Pair(bindExt.sku, bindExt.device)]?.let { thcdInfo ->
                    when (thcdInfo.second.first) {
                        Vm4ThOpManager.RC_FROM_DETAIL,
                        Vm4ThOpManager.RC_FROM_COMPARE_DATA,
                            -> {
                            when (thcdInfo.second.second) {
                                Vm4ThOpManager.THCD_4_WAIT_TO_LOAD,
                                Vm4ThOpManager.THCD_4_IS_LOAD_FROM_SERVICE,
                                Vm4ThOpManager.THCD_4_IS_LOAD_FROM_DEVICE,
                                    -> {
                                    updateFresh(true)
                                }

                                Vm4ThOpManager.THCD_4_IS_LOAD_FINISH,
                                    -> {
                                    updateFresh(false)
                                }

                                else -> {}
                            }
                        }

                        else -> {}
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        Vm4ThOpManager.instance()?.release(Vm4ThOpManager.INIT_FROM_COMPARE)
        Vm4ThOpManager.instance()?.loadThcdManager?.clearCompareDevices()
        EventBus.getDefault().post(ReleaseVMEvent())
        super.onDestroy()
    }

    private fun showLoading(delayTimeMills: Long = 60 * 1000L) {
        LoadingDialog.createDialog(this, com.ihoment.base2app.R.style.DialogDim, delayTimeMills)
            .setEventKey(TAG).show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(TAG)
    }
}