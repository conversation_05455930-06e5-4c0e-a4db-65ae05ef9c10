package com.govee.thnew.ui.detail

import android.app.Activity
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2newth.net.DataExportRequest
import com.govee.base2newth.net.DataExportResponse
import com.govee.base2newth.net.IThNet
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.ui.ac.AbsExportDataAc
import com.ihoment.base2app.Cache
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.util.JumpUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2024/2/26
 * @description 温湿度计-->导出数据的通用页面
 */
class Ac4ThExportData : AbsExportDataAc() {

    companion object {
        private val intervalMinutes = arrayOf(
            1, 15, 30, 60, 120,
            240, 360, 480, 720
        )

        /**
         * 跳转到数据导出界面
         */
        fun jump2ExportData(ac: Activity) {
            Vm4ThOpManager.instance()?.getDeviceInfo()?.let {
                JumpUtil.jumpWithBundle(ac, Ac4ThExportData::class.java, makeAcBundle(it.sku, it.device))
            }
        }

        /**
         * 跳转到数据导出界面
         */
        fun jump2ExportData4H5112(ac: Activity, probeIndex: Int) {
            Vm4ThOpManager.instance()?.getDeviceInfo()?.let {
                JumpUtil.jumpWithBundle(ac, Ac4ThExportData::class.java, makeAcBundle(it.sku, it.device).apply {
                    putInt(Constant4L5.KEY_4_TH_PROBE_INDEX, probeIndex)
                })
            }
        }
    }

    override fun getIntervalMintues(): Array<Int> {
        return intervalMinutes
    }

    override fun applyExportData(emails: List<String>, interval: Int, startTimeMills: Long, endTimeMills: Long, chooseOther: Boolean) {
        showExportDialog()
        val fahOpen = TemUnitConfig.read().isTemUnitFah(sku)
        val request = DataExportRequest(
            transactions.createTransaction(),
            emails, interval, startTimeMills, endTimeMills, device, sku, fahOpen, chooseOther
        )
        when (sku) {
            Constant4L5.H5112 -> {
                val probeIndex = intent.getIntExtra(Constant4L5.KEY_4_TH_PROBE_INDEX, Constant4L5.PROBE_INDEX_4_TEM_HUM)
                request.probeIndex = probeIndex
            }

            else -> {}
        }
        Cache.get(IThNet::class.java).dataExport(request).enqueue(IHCallBack(request))
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onDataExportResponse(response: DataExportResponse) {
        if (!transactions.isMyTransaction(response)) return
        val request = response.getRequest()
        if (request.isExportAll) {
            //埋点统计导出所有数据
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.export_all_data, ParamFixedValue.success)
        }
        toast(response.message)
        hideExportDialog()
    }

    override fun onErrorResponse(response: ErrorResponse) {
        super.onErrorResponse(response)
        hideExportDialog()
    }

    override fun supportTimerExport(): Boolean {
        Vm4ThOpManager.instance()?.let {
            it.getDeviceInfo().let { bindExt ->
                //wifi款都支持定时导出
                if (ThConfig4Support.supportWifi(bindExt.goodsType, bindExt.sku)) {
                    return true
                } else {
                    if (bindExt.sku == Constant4L5.H5053) {
                        //H5053依赖网关支持定时导出
                        return true
                    } else {
                        return it.isBindGateway()
                    }
                }
            }
        }
        return false
    }
}