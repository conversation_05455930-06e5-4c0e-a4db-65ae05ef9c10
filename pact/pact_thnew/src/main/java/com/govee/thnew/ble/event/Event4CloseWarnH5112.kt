package com.govee.thnew.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2025/7/4
 * @description  H5112-->关闭温湿度报警的Event
 */
class Event4CloseWarnH5112 private constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON>an, commandType: Byte, proType: Byte) : AbsControllerEvent(result, write, commandType, proType, !write) {
    var isRelieve = false
    var result = false

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(Event4CloseWarnH5112(false, write, commandType, proType))
        }

        fun sendSuc4Write(write: Boolean, commandType: Byte, proType: Byte, result: Boolean, isRelieve: Boolean) {
            val event = Event4CloseWarnH5112(true, write, commandType, proType)
            event.result = result
            event.isRelieve = isRelieve
            EventBus.getDefault().post(event)
        }
    }
}