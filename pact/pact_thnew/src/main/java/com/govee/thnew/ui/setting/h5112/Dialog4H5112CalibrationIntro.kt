package com.govee.thnew.ui.setting.h5112

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import com.govee.thnew.databinding.ThnewDialog4H5112CalibrationIntroBinding
import com.ihoment.base2app.dialog.BaseEventDialog
import com.ihoment.base2app.util.AppUtil

/**
 * <AUTHOR>
 * @date created on 2025/07/01
 * @description H5112温湿度校准的功能提示弹窗
 */
class Dialog4H5112CalibrationIntro private constructor(context: Context) :
    BaseEventDialog(context) {

    private lateinit var binding: ThnewDialog4H5112CalibrationIntroBinding

    init {
        ignoreBackPressed()
        changeDialogOutside(false)
        immersionMode()
    }

    companion object {
        fun createDialog(context: Context): Dialog4H5112CalibrationIntro {
            return Dialog4H5112CalibrationIntro(context)
        }
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getLayoutView(): View {
        binding = ThnewDialog4H5112CalibrationIntroBinding.inflate(LayoutInflater.from(context))
        binding.tvCloseBtn4H5112CaliIntro.setOnClickListener {
            hide()
        }
        return binding.root
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth() * 335 / 375
    }
}