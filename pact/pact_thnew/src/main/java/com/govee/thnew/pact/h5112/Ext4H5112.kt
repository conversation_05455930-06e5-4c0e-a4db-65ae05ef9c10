package com.govee.thnew.pact.h5112

import androidx.annotation.Keep
import com.govee.base2home.Constant4L5
import com.govee.base2home.iot.Cmd
import com.govee.base2home.iot.ResultPt
import com.govee.base2home.main.AbsDevice
import com.govee.base2home.pact.BleUtil
import com.govee.base2kt.utils.BleUtils
import com.govee.base2light.ac.AbsIotManagerV1
import com.govee.base2light.ble.controller.BleProtocolConstants
import com.govee.base2newth.deviceitem.AbsModel4Th
import com.govee.mvvm.globalLaunch
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.util.JsonUtil
import kotlinx.coroutines.Dispatchers

/**
 * <AUTHOR>
 * @date created on 2025/6/4
 * @description H5112的额外参数对象
 */
@Keep
class Ext4H5112 private constructor() {

    companion object {

        const val COMMAND_TYPE_4_DEVICE_INFO = 0x04.toByte()
        const val COMMAND_TYPE_4_DEVICE_INFO_NOTIFY = 0x34.toByte()
        const val DEVICE_TYPE = 0x03

        /**
         * 从iot消息中获取子设备信息
         */
        @Synchronized
        fun parseIot4H5112Sub(absDevice: AbsDevice, jsonStr: String): Ext4H5112? {
            val ext4H5112 = JsonUtil.fromJson(absDevice.deviceExt?.deviceSettings ?: "", Ext4H5112::class.java)
            ext4H5112.sno ?: return null
            val opJsonStr = AbsIotManagerV1.getJsonObjectStr(jsonStr, Cmd.parse_json_op) ?: return null
            val resultPt = JsonUtil.fromJson(opJsonStr, ResultPt::class.java) ?: return null
            val bytes = resultPt.bytes()
            if (bytes.isEmpty()) return null
            for (infoBys in bytes) {
                if (infoBys[0] == BleProtocolConstants.SINGLE_READ || infoBys[0] == BleProtocolConstants.NOTIFY) {
                    when (infoBys[1]) {
                        COMMAND_TYPE_4_DEVICE_INFO,
                        COMMAND_TYPE_4_DEVICE_INFO_NOTIFY,
                            -> {
                            //确认子设备为H5112
                            if (BleUtils.getUnsignedByte(infoBys[3]) == DEVICE_TYPE) {
                                val validBytes = ByteArray(infoBys.size - 3)
                                System.arraycopy(infoBys, 2, validBytes, 0, validBytes.size)
                                parseSubInfo(validBytes).let {
                                    if (ext4H5112?.sno == it.sno) {
                                        //报警弹窗
                                        it.status?.let { warnInfo ->
                                            //存在子设备离线，但网关还是上报报警的情况。故显示报警弹窗的前提是设备在线
                                            if (it.online.isTrue()) {
                                                globalLaunch(Dispatchers.Main) {
                                                    if (warnInfo.first.first) {
                                                        Dialog4H5112CloseWarn.showDialog(absDevice.sku, absDevice.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, true, warnInfo.first.second, Pair(it.tem4Pb1, it.hum4Pb1))
                                                    } else {
                                                        Dialog4H5112CloseWarn.hideDialog(absDevice.sku, absDevice.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, true)
                                                    }
                                                    if (warnInfo.second.first) {
                                                        Dialog4H5112CloseWarn.showDialog(absDevice.sku, absDevice.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, false, warnInfo.second.second, Pair(it.tem4Pb1, it.hum4Pb1))
                                                    } else {
                                                        Dialog4H5112CloseWarn.hideDialog(absDevice.sku, absDevice.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, false)
                                                    }
                                                    if (warnInfo.third.first) {
                                                        Dialog4H5112CloseWarn.showDialog(absDevice.sku, absDevice.device, Constant4L5.PROBE_INDEX_4_TEM, true, warnInfo.third.second, Pair(it.tem4Pb2, 6000))
                                                    } else {
                                                        Dialog4H5112CloseWarn.hideDialog(absDevice.sku, absDevice.device, Constant4L5.PROBE_INDEX_4_TEM, true)
                                                    }
                                                }
                                            }
                                        }
                                        return it
                                    }
                                }
                            }
                        }

                        else -> {}
                    }
                }
            }
            return null
        }

        /**
         * 解析子设备信息(17bytes有效字节)
         */
        @Synchronized
        fun parseSubInfo(validBytes: ByteArray): Ext4H5112 {
            var index = 0
            val sno = BleUtil.getUnsignedByte(validBytes[index++])
            val deviceType = BleUtil.getUnsignedByte(validBytes[index++])
            val onLineStatus = BleUtil.getUnsignedByte(validBytes[index++])
            val batteryHex = BleUtil.toHex(validBytes[index++])
            val batBinaryStr = BleUtil.hexToBinary(batteryHex)
            val isLowBattery = batBinaryStr.substring(0, 1).toInt() == 1
            val battery = batBinaryStr.substring(1).toInt(2)
            //versionSoft-->H5112不用
            index++
            //versionHard-->H5112不用
            index++
            val signal = validBytes[index++].toInt()
            val timeBys = ByteArray(4)
            System.arraycopy(validBytes, index, timeBys, 0, timeBys.size)
            val timeMills = BleUtil.getSignedInt(timeBys) * 1000
            index += 4
            val infoBys = ByteArray(4)
            System.arraycopy(validBytes, index, infoBys, 0, infoBys.size)
            val infoValue = BleUtil.getSignedInt(infoBys, false)
            index += 4
            val warnStatusHex = BleUtil.toHex(validBytes[index])
            val warnStatusBinaryStr = BleUtil.hexToBinary(warnStatusHex).reversed()
            //温度探针--温度报警状态(0：无报警，1：低于，2高于，下同)
            val pb2TemWarnType = warnStatusBinaryStr.substring(0, 2).reversed().toInt(2)
            val isPb2TemWarn = pb2TemWarnType > 0
            val isPb2TemWarnHigh = pb2TemWarnType == 2
            //温湿度探针--温度报警状态
            val pb1TemWarnType = warnStatusBinaryStr.substring(2, 4).reversed().toInt(2)
            val isPb1TemWarn = pb1TemWarnType > 0
            val isPb1TemWarnHigh = pb1TemWarnType == 2
            //温湿度探针--湿度报警值状态
            val pb1HumWarnType = warnStatusBinaryStr.substring(4, 6).reversed().toInt(2)
            val isPb1HumWarn = pb1HumWarnType > 0
            val isPb1HumWarnHigh = pb1HumWarnType == 2

            return Ext4H5112().apply {
                this.sno = sno
                this.online = onLineStatus == 0
                this.battery = battery
                this.signal = signal
                this.battery = battery
                this.updateTimeMills = timeMills
                unpackTh(infoValue).let {
                    it.first?.let { tem2 ->
                        this.tem4Pb2 = (tem2 * 100).toInt()
                    }
                    it.second?.let { tem1 ->
                        this.tem4Pb1 = (tem1 * 100).toInt()
                    }
                    it.third?.let { hum ->
                        this.hum4Pb1 = (hum * 100).toInt()
                    }
                }
                //网关断电后上报，只有在线状态是准确的，这种下不处理报警
                if ((timeMills > 0)) {
                    this.status = Triple(Pair(isPb1TemWarn, isPb1TemWarnHigh), Pair(isPb1HumWarn, isPb1HumWarnHigh), Pair(isPb2TemWarn, isPb2TemWarnHigh))
                }
            }
        }

        /**
         * 范围映射：
         *  温度原始值 T （单位 °C）映射到整数 t = (T + 40.0) * 10，t∈[0,1650]，需 11 位
         *  湿度原始值 H （单位 %RH）映射到整数 h = H * 10，h∈[0,1000]，需 10 位
         *
         * 位域分配（从低位到高位）：
         *  bit  0-10   : 温度1 t1  温度探针温度 (11 位)
         *  bit 11-21   : 温度2 t2  温湿度探针温度 (11 位)
         *  bit 22-31   : 湿度  h  温湿度探针湿度 (10 位)
         */
        private const val TEMP_SCALE = 10.0f
        private const val TEMP_OFFSET = 40.0f

        private fun unpackTh(packed: Int): Triple<Float?, Float?, Float?> {
            val t1 = packed and 0x7FF
            val t2 = (packed shr 11) and 0x7FF
            val h = (packed shr 22) and 0x3FF

            val temp2 = if (t1 == 0x7FF) null else ((t1 / TEMP_SCALE) - TEMP_OFFSET)
            val temp1 = if (t2 == 0x7FF) null else ((t2 / TEMP_SCALE) - TEMP_OFFSET)
            val hum = if (h == 0x3FF) null else (h / TEMP_SCALE)

            return Triple(temp2, temp1, hum)
        }
    }

    constructor(sno: Int) : this() {
        this.sno = sno
    }

    var sno: Int? = null

    var battery: Int? = null

    /**
     * 子设备在线状态
     */
    var online: Boolean? = null

    /**
     * 信号强度
     */
    var signal: Int? = null

    /**
     * 更新时间
     */
    var updateTimeMills: Long? = null

    /**
     * 探针1的实时温度
     */
    var tem4Pb1 = AbsModel4Th.INVALID_INT_VALUE

    /**
     * 探针1的实时湿度
     */
    var hum4Pb1 = AbsModel4Th.INVALID_INT_VALUE

    /**
     * 探针2的实时温度
     */
    var tem4Pb2 = AbsModel4Th.INVALID_INT_VALUE

    /**
     * 报警状态
     * 备注：first->探针1温度报警，second->探针1湿度报警，third->探针2温度报警
     */
    var status: Triple<Pair<Boolean, Boolean>, Pair<Boolean, Boolean>, Pair<Boolean, Boolean>>? = null
}