package com.govee.thnew.ui.detail.h5112

import android.os.Bundle
import android.view.MotionEvent
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.viewpager2.widget.ViewPager2
import com.govee.base2home.Constant4L5
import com.govee.base2home.config.Base2homeConfig
import com.govee.base2home.kt.ac.AbsBTRp4EnforceAcHint
import com.govee.base2home.main.hint.HintLabel
import com.govee.base2home.ui.showguide.ShowGuideView.Companion.addShowGuide
import com.govee.base2home.util.UIUtil
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.setVisibility
import com.govee.base2kt.ext.toast
import com.govee.base2kt.utils.IntentUtils
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.deviceitem.GatewayInfo
import com.govee.base2newth.other.Event4DeleteThFromGw
import com.govee.thnew.R
import com.govee.thnew.add.AddInfo
import com.govee.thnew.databinding.ThnewAc4H5112DetailBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.op.BleOpManager
import com.govee.thnew.ui.setting.h5112.Ac4H5112Setting
import com.govee.thnew.update.ota4frk.Ac4UpdateByFrk
import com.govee.ui.component.NotifyHintView
import com.govee.ui.dialog.BleUpdateHintDialog
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import java.util.concurrent.ConcurrentHashMap

/**
 * <AUTHOR>
 * @date created on 2025/4/28
 * @description H5112的详情页
 */
class Ac4H5112Detail : AbsBTRp4EnforceAcHint<ThnewAc4H5112DetailBinding>() {

    private var selectedProbeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM
    private val setProbeIconDialog by lazy {
        Dialog4EditProbeInfo.createDialog(this)
    }
    private val checkNetDialog by lazy {
        Dialog4H5112CheckNet.createDialog(this)
    }

    private var originalBindExt: AddInfo? = null

    override fun doPreCheck(savedInstanceState: Bundle?) {
        initData()
        initUi()
        initOpClick()
        initObserver()
        //开始加载数据
        updateFresh(true)
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
    }

    /**
     * 该设备自身的实时信息
     */
    private fun getDeviceInfo(): AddInfo? {
        Vm4ThOpManager.instance()?.getDeviceInfo()?.let {
            return it
        }
        //这种情况直接退回到设备列表页(长时间置于后台，被销毁后，回到前台重建)
        BaseApplication.getBaseApplication().finishOther(Base2homeConfig.getConfig().mainAcClass)
        return null
    }

    private fun initData() {
        val bindExt = IntentUtils.parseParcelable<AddInfo>(intent, ThConsV1.KEY_4_ADD_INFO) ?: run {
            toast(com.govee.ui.R.string.h721214_other_listen)
            finish()
            return
        }
        val gatewayInfo = IntentUtils.parseParcelable<GatewayInfo>(intent, ThConsV1.KEY_4_GATEWAY_INFO)
        Vm4ThOpManager.init(bindExt, gatewayInfo, Vm4ThOpManager.INIT_FROM_NEW_DETAIL)
        originalBindExt = bindExt.copy()
    }

    private fun initUi() {
        getDeviceInfo()?.let { bindExt ->
            viewBinding.let { vb ->
                //复购按钮
                addShowGuide(this, vb.ivGuideHolder4H5112Detail, bindExt.sku, bindExt.device)
                //设备名称
                vb.tvTextTitle4H5112Detail.text = bindExt.deviceName
                //联网检测提示
                vb.clCheckNetContainer4H5112Detail.setVisibility(Vm4ThOpManager.instance()?.isBindGateway().isTrue())
                //探针信息
                vb.tvName14H5112Probe.text = bindExt.probeName1 ?: ResUtil.getString(com.govee.ui.R.string.h5112_text_4_tem_hum_probe)
                vb.tvName24H5112Probe.text = bindExt.probeName2 ?: ResUtil.getString(com.govee.ui.R.string.h5112_text_4_tem_probe)
                Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM_HUM, getDeviceInfo()?.pb1IconIndex ?: 1, true)?.let { iconRes ->
                    ResUtil.setImageResource(viewBinding.ivIcon14H5112Probe, iconRes)
                    viewBinding.ivIcon14H5112Probe.setVisibility(true)
                }
                Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM, getDeviceInfo()?.pb2IconIndex ?: 1, true)?.let { iconRes ->
                    ResUtil.setImageResource(viewBinding.ivIcon24H5112Probe, iconRes)
                    viewBinding.ivIcon24H5112Probe.setVisibility(true)
                }
            }
        }
        setConnectStatus()
        //探针内容
        val probeFragmentList = arrayListOf<Pair<Int, Fragment>>().apply {
            add(Pair(Constant4L5.PROBE_INDEX_4_TEM_HUM, Fragment4H5112Detail.newInstance(Constant4L5.PROBE_INDEX_4_TEM_HUM)))
            add(Pair(Constant4L5.PROBE_INDEX_4_TEM, Fragment4H5112Detail.newInstance(Constant4L5.PROBE_INDEX_4_TEM)))
        }
        viewBinding.vpContentContainer4H5112Detail.adapter = ViewPagerAdapter(probeFragmentList, supportFragmentManager, this.lifecycle)
        viewBinding.vpContentContainer4H5112Detail.offscreenPageLimit = probeFragmentList.size
        selectTab(0)
    }

    private fun initOpClick() {
        viewBinding.run {
            ivBackBtn4H5112Detail.clickDelay {
                onBackPressed()
            }
            //跳转到柱状图表页
            ivChartBtn4H5112Detail.clickDelay {
                getDeviceInfo()?.let { bindExt ->
                    JumpUtil.jump(this@Ac4H5112Detail, Ac4H5112SquareChart::class.java, Bundle().apply {
                        this.putParcelable(ThConsV1.KEY_4_ADD_INFO, bindExt)
                        this.putInt(Constant4L5.KEY_4_TH_PROBE_INDEX, selectedProbeIndex)
                    })
                }
            }
            //跳转到设置页
            ivSettingBtn4H5112Detail.clickDelay {
                JumpUtil.jump(this@Ac4H5112Detail, Ac4H5112Setting::class.java)
            }
            clCheckNetContainer4H5112Detail.clickDelay {
                checkNetDialog.showCheckNetDialog()
            }
            //Tab+ViewPage2联动
            clTab14H5112Detail.clickDelay {
                selectTab(0)
                vpContentContainer4H5112Detail.setCurrentItem(0, true)
            }
            clTab24H5112Detail.clickDelay {
                selectTab(1)
                vpContentContainer4H5112Detail.setCurrentItem(1, true)
            }
            vpContentContainer4H5112Detail.registerOnPageChangeCallback(object :
                ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    selectTab(position)
                    selectedProbeIndex = if (position == 0) {
                        Constant4L5.PROBE_INDEX_4_TEM_HUM
                    } else {
                        Constant4L5.PROBE_INDEX_4_TEM
                    }
                }
            })
            //编辑探针信息
            ivEditIcon14H5112Probe.clickDelay {
                setProbeIconDialog.showDialog(
                    selectedProbeIndex,
                    getDeviceInfo()?.pb1IconIndex ?: 1,
                    tvName14H5112Probe.text.toString(),
                    selPbIconCallback = { selectedProbeIcon ->
                        Vm4ThOpManager.instance()?.setPbIconIndex(Constant4L5.PROBE_INDEX_4_TEM_HUM, selectedProbeIcon.index) {
                            if (it) {
                                Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM_HUM, selectedProbeIcon.index, true)?.run {
                                    ResUtil.setImageResource(ivIcon14H5112Probe, this)
                                }
                            }
                        }
                    },
                    editPbNameCallback = {
                        tvName14H5112Probe.text = it
                    })
            }
            ivEditIcon24H5112Probe.clickDelay {
                setProbeIconDialog.showDialog(
                    selectedProbeIndex,
                    getDeviceInfo()?.pb2IconIndex ?: 1,
                    tvName24H5112Probe.text.toString(),
                    selPbIconCallback = { selectedProbeIcon ->
                        Vm4ThOpManager.instance()?.setPbIconIndex(Constant4L5.PROBE_INDEX_4_TEM, selectedProbeIcon.index) {
                            if (it) {
                                Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM, selectedProbeIcon.index, true)?.run {
                                    ResUtil.setImageResource(ivIcon24H5112Probe, this)
                                }
                            }
                        }
                    },
                    editPbNameCallback = {
                        tvName24H5112Probe.text = it
                    })
            }
        }
    }

    /**
     * 选中tab
     */
    private fun selectTab(position: Int) {
        viewBinding.run {
            clTab14H5112Detail.alpha = 0.2f
            clTab24H5112Detail.alpha = 0.2f
            ivEditIcon14H5112Probe.setVisibility(false)
            ivEditIcon24H5112Probe.setVisibility(false)
            when (position) {
                0 -> {
                    clTab14H5112Detail.alpha = 1f
                    ivEditIcon14H5112Probe.setVisibility(true)
                }

                1 -> {
                    clTab24H5112Detail.alpha = 1f
                    ivEditIcon24H5112Probe.setVisibility(true)
                }

                else -> {
                    return
                }
            }
        }
    }

    private fun initObserver() {
        Vm4ThOpManager.instance()?.let { vm ->
            vm.loadingChange.showDialog.observeInActivity(this) {
                showLoading()
            }
            vm.loadingChange.dismissDialog.observeInActivity(this) {
                hideLoading()
            }
            vm.ld4ConnectStatusChange.observe(this) {
                //状态相关更新
                viewBinding.nhvBleEnable4H5112Detail.let { v4BleEnable ->
                    if (it.first == BleOpManager.BLE_UNABLE) {
                        v4BleEnable.updateHint(HintLabel(ResUtil.getString(com.govee.ui.R.string.h5072_bluetooth_unable_detail_main_des), NotifyHintView.hint_type_notification_showing))
                    } else {
                        v4BleEnable.hide()
                    }
                }
                setConnectStatus()
                //联网检测提示(在设置页绑定网关后，需显示出来)
                viewBinding.clCheckNetContainer4H5112Detail.setVisibility(Vm4ThOpManager.instance()?.isBindGateway().isTrue())
                //探针icon相关
                val hasGetDeviceInfo = (it.first == BleOpManager.BLE_READ_INFO_FINISH) || it.second.first
                if (hasGetDeviceInfo) {
                    Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM_HUM, getDeviceInfo()?.pb1IconIndex ?: 1, true)?.let { iconRes ->
                        ResUtil.setImageResource(viewBinding.ivIcon14H5112Probe, iconRes)
                        viewBinding.ivIcon14H5112Probe.setVisibility(true)
                    }
                    Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM, getDeviceInfo()?.pb2IconIndex ?: 1, true)?.let { iconRes ->
                        ResUtil.setImageResource(viewBinding.ivIcon24H5112Probe, iconRes)
                        viewBinding.ivIcon24H5112Probe.setVisibility(true)
                    }
                }
                setProbeIconDialog.updateUi(hasGetDeviceInfo)
            }
            vm.settingOpManager.ld4UpdateDeviceName.observe(this) {
                if (!hasCreated) {
                    return@observe
                }
                getDeviceInfo()?.let { bindExt ->
                    if (it.first == bindExt.getKey()) {
                        viewBinding.tvTextTitle4H5112Detail.text = it.second
                    }
                }
            }
            vm.ld4UpgradeVersion.observe(this) {
                when (it.first) {
                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_FIRST_REMIND -> {
                        getDeviceInfo()?.let { bindExt ->
                            BleUpdateHintDialog.showDialog(this, bindExt.sku, {
                                toUpgradePage()
                            }, this.javaClass.name)
                        }
                        //再执行红点
                        Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value = Pair(Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND, it.second)
                    }

                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND -> {
                        //隐藏升级提示
                        viewBinding.vVersionFlag4H5112Detail.setVisibility(true)
                    }

                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_FINISH -> {
                        //升级成功后，会断开连接，故须重连
                        Vm4ThOpManager.instance()?.toConnectBle()
                        //隐藏升级提示
                        viewBinding.vVersionFlag4H5112Detail.setVisibility(false)
                    }

                    else -> {
                        viewBinding.vVersionFlag4H5112Detail.setVisibility(false)
                    }
                }
            }
            vm.loadThcdManager.ld4BleLoadThCdProgress.observe(this) {
                getDeviceInfo()?.let { bindExt ->
                    if (bindExt.sku == it.first.first && bindExt.device == it.first.second) {
                        when (it.second.first) {
                            Vm4ThOpManager.RC_FROM_DETAIL,
                            Vm4ThOpManager.RC_FROM_COMPARE_DATA,
                                -> {
                                updateSyncDes(it.second.second)
                            }

                            else -> {}
                        }
                    }
                }
            }
            vm.loadThcdManager.ld4LoadThCdStep.observe(this) {
                getDeviceInfo()?.let { bindExt ->
                    it[Pair(bindExt.sku, bindExt.device)]?.let { thcdInfo ->
                        when (thcdInfo.second.first) {
                            Vm4ThOpManager.RC_FROM_DETAIL,
                            Vm4ThOpManager.RC_FROM_COMPARE_DATA,
                                -> {
                                if (thcdInfo.second.second == Vm4ThOpManager.THCD_4_IS_LOAD_FINISH) {
                                    updateFresh(false)
                                }
                            }

                            else -> {}
                        }
                    }
                }
            }
        }
    }

    /**
     * 设置连接状态
     */
    private fun setConnectStatus() {
        Vm4ThOpManager.instance()?.let { vm4Th ->
            viewBinding.let { vb ->
                if (vm4Th.isBindGateway() && vm4Th.getGateInfo()?.isIotOnLine.isTrue()) {
                    val signal = vm4Th.getGateInfo()?.signal ?: -1
                    ResUtil.setImageResource(vb.ivConnectStatusIcon4H5112Detail, UIUtil.getSignalResV2(signal))
                    vb.tvConnectStatusText4H5112Detail.text = ResUtil.getString(com.govee.ui.R.string.text_4_connect_gateway)
                    vb.tvConnectStatusText4H5112Detail.isSelected = false
                } else if (vm4Th.hasBleConnected()) {
                    ResUtil.setImageResource(vb.ivConnectStatusIcon4H5112Detail, com.govee.ui.R.mipmap.new_control_icon_bluetooch_white)
                    vb.tvConnectStatusText4H5112Detail.text = ResUtil.getString(com.govee.ui.R.string.text_4_connect_ble)
                    vb.tvConnectStatusText4H5112Detail.isSelected = false
                } else {
                    ResUtil.setImageResource(vb.ivConnectStatusIcon4H5112Detail, com.govee.ui.R.mipmap.new_control_icon_loose_red)
                    vb.tvConnectStatusText4H5112Detail.text = ResUtil.getString(com.govee.ui.R.string.text_4_disconnect)
                    vb.tvConnectStatusText4H5112Detail.isSelected = true
                }
            }
        }
        viewBinding.gpStatusContainer4H5112Detail.setVisibility(true)
    }

    /**
     * 跳转至升级页面
     */
    private fun toUpgradePage() {
        getDeviceInfo()?.let { bindExt ->
            Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value?.let { upgradePair ->
                upgradePair.second?.run {
                    Ac4UpdateByFrk.jump2OtaUpdate(this@Ac4H5112Detail, bindExt.sku, bindExt.deviceName, this)
                }
            }
        }
    }

    /**
     * 柱状图表页面切换按钮是否显示
     */
    fun showChartBtn(show: Boolean) {
        viewBinding.ivChartBtn4H5112Detail.setVisibility(show)
    }

    //下拉刷新相关--------------------------------------start--------------------------------------
    private var needShowFresh = false
    private var lastStartMoveY = 0
    private var lastDownY = 0
    private var fullFreshShow = false
    var subPageScrollYMap = ConcurrentHashMap<Int, Int>()

    /**
     * appBarLayout的实时滑动距离
     */
    @Volatile
    var ablVerticalOffsetMap = ConcurrentHashMap<Int, Int>()

    /**
     * 更新下拉刷新时的进度文案
     */
    private fun updateSyncDes(percent: Int) {
        var percentStr = "$percent%"
        percentStr = String.format(getString(com.govee.ui.R.string.h5072_fresh_des_syncing), percentStr)
        viewBinding.tvRefreshDes4H5112Detail.text = percentStr
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (fullFreshShow) return super.dispatchTouchEvent(ev)
        val action = ev.action
        if (MotionEvent.ACTION_DOWN == action) {
            lastDownY = ev.rawY.toInt()
        } else if (MotionEvent.ACTION_MOVE == action) {
            val scrollY: Int = subPageScrollYMap[selectedProbeIndex] ?: 0
            val rawY = ev.rawY.toInt()
            //向下滑动，才需要做逻辑处理
            val pullDown: Boolean = rawY - lastDownY > ThConsV1.VERTICAL_DIR_PULL_DOWN
            val ablRecover = (ablVerticalOffsetMap[selectedProbeIndex] ?: 0) == 0
            if (scrollY == 0 && !needShowFresh && pullDown && ablRecover) {
                lastStartMoveY = rawY
                needShowFresh = true
            }
            if (needShowFresh) {
                updateFreshHeight(rawY)
                return true
            }
        } else if ((MotionEvent.ACTION_UP == action || MotionEvent.ACTION_CANCEL == action) && needShowFresh) {
            checkFreshHeight()
            needShowFresh = false
            lastDownY = 0
        }
        return super.dispatchTouchEvent(ev)
    }

    private fun updateFreshHeight(rawY: Int) {
        viewBinding.run {
            var moreDis: Int = rawY - lastStartMoveY
            moreDis = 0.coerceAtLeast(moreDis)
            llcRefreshContainer4H5112Detail.layoutParams?.let {
                it.height = moreDis
                llcRefreshContainer4H5112Detail.layoutParams = it
            }
        }
    }

    private fun checkFreshHeight() {
        viewBinding.run {
            val maxHeight: Int = (AppUtil.getScreenWidth() * ThConsV1.FRESH_HEIGHT_PERCENT_SCREEN_WIDTH).toInt()
            val lp: ViewGroup.LayoutParams = llcRefreshContainer4H5112Detail.layoutParams
            val height = lp.height
            //展示/隐藏
            updateFresh(height >= maxHeight)
        }
    }

    private fun updateFresh(fullFreshShow: Boolean) {
        viewBinding.let { vb ->
            this.fullFreshShow = fullFreshShow
            vb.pbRefreshCycle4H5112Detail.setVisibility(true)
            val lp: ViewGroup.LayoutParams = vb.llcRefreshContainer4H5112Detail.layoutParams
            if (fullFreshShow) {
                lp.height = (AppUtil.getScreenWidth() * ThConsV1.FRESH_HEIGHT_PERCENT_SCREEN_WIDTH).toInt()
            } else {
                lp.height = 0
            }
            vb.llcRefreshContainer4H5112Detail.layoutParams = lp
            if (fullFreshShow) {
                //开始加载数据:先服务端，再同步设备端的
                Vm4ThOpManager.instance()?.loadThcdManager?.toLoadThcdBySingle(this)
            } else {
                vb.tvRefreshDes4H5112Detail.setText(com.govee.ui.R.string.fresh_des_loading)
            }
        }
    }

    @Subscribe
    fun onEvent4DeleteThFromGw(event: Event4DeleteThFromGw) {
        onBackPressed()
    }

    override fun onBtPerGrantedOver() {}

    override fun getAcContentRootViewId(): Int {
        return R.id.ac_container
    }

    override fun adapterInsetViewId(): Int {
        return R.id.sp_top_adjust_holder
    }

    override fun layoutId(): Int {
        return R.layout.thnew_ac_4_h5112_detail
    }

    override fun statusBarLightMode(): Boolean {
        return false
    }


    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        getDeviceInfo()?.let { bindExt ->
            //有信息更新，通知刷新首页卡片
            if (bindExt != originalBindExt) {
                ThConsV1.refreshDeviceInfo(bindExt.sku, bindExt.device, bindExt, true)
            }
        }
        super.onBackPressed()
    }

    override fun onDestroy() {
        EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
        Vm4ThOpManager.instance()?.release(Vm4ThOpManager.INIT_FROM_NEW_DETAIL)
        super.onDestroy()
    }
}