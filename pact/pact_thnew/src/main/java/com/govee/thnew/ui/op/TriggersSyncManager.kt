package com.govee.thnew.ui.op

import android.os.Handler
import android.os.Looper
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.reform4dbgw.net.IThDbgwNet
import com.govee.base2home.reform4dbgw.net.request.Request4BdType
import com.govee.base2home.reform4dbgw.net.request.RequestThTriggers
import com.govee.base2home.reform4dbgw.net.response.ResponseThTriggers
import com.govee.base2home.util.CaughtRunnable
import com.govee.base2newth.form4Dbgw.ControllerBdType
import com.govee.base2newth.form4Dbgw.DbgwThLinkageBleOpNew
import com.govee.base2newth.form4Dbgw.EventBdType
import com.govee.base2newth.form4Dbgw.EventMultiTriggers
import com.govee.thnew.ble.ThBle
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.Cache
import com.ihoment.base2app.infra.SafeLog.Companion.i
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.network.Transactions
import com.kk.taurus.playerbase.utils.NetworkUtils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2024/3/14
 * @description 温湿度计-->作为分布式网关子设备须同步bdType、triggers
 */
internal class TriggersSyncManager {

    companion object {
        private const val SEND_COMM_TIME_OUT = (5 * 1000).toLong()
        private const val GET_TRIGGERS_TIME_OUT = (30 * 1000).toLong()
    }

    private var sku = ""
    private var device = ""
    private val transactions = Transactions()
    private val mHandler by lazy {
        Handler(Looper.getMainLooper())
    }
    private lateinit var finishCallback: (() -> Unit)

    init {
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
    }

    /**
     * 分布式网关子设备在其详情页须执行的相关通讯
     * 1.从设备端获取bdType后同步给服务端
     * 2.从服务端获取联动的全部triggers同步个设备
     */
    fun toComm4DbgwSub(sku: String, device: String, needSendTriggers: Boolean, finishCallback: (() -> Unit)) {
        this.finishCallback = finishCallback
        ThBle.getInstance.startExtControllers(false, ControllerBdType(sku, device, needSendTriggers))
        //设置超时
        mHandler.postDelayed(sendCommFinishRunnable, SEND_COMM_TIME_OUT)
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEventBdType(event: EventBdType) {
        val result = event.isResult
        if (result && event.bdType >= 0) {
            i("xiaobing") { "ThDbgwSubCommManager--onEventBdType-->读取到byType=->${event.bdType}" }
            //将属性上报服务端
            saveBdType(event.sku, event.device, event.bdType)
            if (event.needSendTriggers) {
                sendAllTriggers(event.sku, event.device)
            } else {
                //结束
                mHandler.removeCallbacks(sendCommFinishRunnable)
                mHandler.postDelayed(sendCommFinishRunnable, 0)
            }
        } else {
            //结束
            mHandler.removeCallbacks(sendCommFinishRunnable)
            mHandler.postDelayed(sendCommFinishRunnable, 0)
        }
        //下一包读取
        ThBle.getInstance.controllerEvent(event)
    }

    private fun saveBdType(sku: String, device: String, bdType: Int) {
        val request = Request4BdType(
            transactions.createTransaction(),
            sku,
            device,
            bdType
        )
        Cache.get(IThDbgwNet::class.java).saveBdType(request)?.enqueue(IHCallBack(request))
    }

    /**
     * 设备详情页下发所有该设备触发的联动的触发动作
     *
     * @param sku
     * @param device
     */
    private fun sendAllTriggers(sku: String, device: String) {
        //无网络则终止
        if (!NetworkUtils.isNetConnected(BaseApplication.getBaseApplication())) {
            mHandler.removeCallbacks(sendCommFinishRunnable)
            mHandler.postDelayed(sendCommFinishRunnable, 0)
            return
        }
        val request = RequestThTriggers(transactions.createTransaction())
        Cache.get(IThDbgwNet::class.java).getThTriggers(sku, device).enqueue(IHCallBack(request))
        //重置超时
        mHandler.removeCallbacks(sendCommFinishRunnable)
        mHandler.postDelayed(sendCommFinishRunnable, GET_TRIGGERS_TIME_OUT)
    }

    /**
     * 发送指令结束（成功或失败）
     */
    private val sendCommFinishRunnable: CaughtRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            EventBus.getDefault().takeIf { it.isRegistered(this@TriggersSyncManager) }?.unregister(this@TriggersSyncManager)
            //释放Handler资源
            mHandler.removeCallbacksAndMessages(null)
            //结束的回调
            finishCallback.invoke()
        }
    }

    /**
     * 指令总包数
     */
    @Volatile
    private var totalCommsNum = 0

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onResponseAllTriggers(response: ResponseThTriggers) {
        if (response.data != null && response.request is RequestThTriggers) {
            var triggers = response.data!!.triggers
            if (triggers == null) {
                triggers = ArrayList()
            }
            val allTriggersComms = DbgwThLinkageBleOpNew.getTriggerComms(sku, triggers)
            for (allTriggersComm in allTriggersComms) {
                i("xiaobing") { "ThDbgwSubCommManager--onResponseAllTriggers-->要下发的指令内容为-->" + BleUtil.bytesToHexString(allTriggersComm!!.commBytes) }
            }
            totalCommsNum = allTriggersComms.size
            //开始下发trigger
            ThBle.getInstance.startExtControllers(false, *allTriggersComms)
            //设置超时
            mHandler.removeCallbacks(sendCommFinishRunnable)
            mHandler.postDelayed(sendCommFinishRunnable, SEND_COMM_TIME_OUT)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponseError(response: ErrorResponse) {
        if (response.request is RequestThTriggers) {
            mHandler.postDelayed(sendCommFinishRunnable, 0)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventMultiTriggers(event: EventMultiTriggers) {
        i("xiaobing") { "ThDbgwSubCommManager--onEventMultiTriggers-->收到下发trigger的回复，包序号-->" + event.packIndex }
        if (event.packIndex + 1 == totalCommsNum) {
            i("xiaobing") { "ThDbgwSubCommManager--onEventMultiTriggers-->trigger下发完成" }
            //移除超时
            mHandler.removeCallbacks(sendCommFinishRunnable)
            mHandler.postDelayed(sendCommFinishRunnable, 0)
        }
        //下一包读取
        ThBle.getInstance.controllerEvent(event)
    }
}