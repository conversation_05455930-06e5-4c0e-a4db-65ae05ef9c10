package com.govee.thnew.ble.controller

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4UploadFreq

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->读写设备上传温湿度数据频率的Controller
 */
class Controller4UploadFreq : AbsControllerWithCallback {
    private var hour = 0
    private var minute = 0

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     *
     * @param hour
     * @param minute
     */
    constructor(hour: Int, minute: Int, writeCallBack: ((result: Boolean) -> Unit)) : super(writeCallBack) {
        this.hour = hour
        this.minute = minute
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_upload_frequency
    }

    override fun translateWrite(): ByteArray {
        val hourValue = hour.toByte()
        val minuteValue = minute.toByte()
        return byteArrayOf(hourValue, minuteValue)
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val hour = BleUtil.getUnsignedByte(validBytes[0])
        val minute = BleUtil.getUnsignedByte(validBytes[1])
        Event4UploadFreq.sendSuc4Read(isWrite, commandType, proType, hour, minute)
        return true
    }

    override fun fail() {
        super.fail()
        Event4UploadFreq.sendFail(isWrite, commandType, proType)
    }
}