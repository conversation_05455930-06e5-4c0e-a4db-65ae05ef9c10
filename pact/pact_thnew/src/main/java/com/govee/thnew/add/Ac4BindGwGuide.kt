package com.govee.thnew.add

import android.annotation.SuppressLint
import android.os.Bundle
import android.text.TextUtils
import android.view.View
import android.view.View.OnClickListener
import androidx.databinding.DataBindingUtil
import com.govee.base2home.AbsNetActivity
import com.govee.base2home.Constant4L5
import com.govee.base2home.config.Base2homeConfig
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.device.IDeviceNet
import com.govee.base2home.device.net.GwListRequest
import com.govee.base2home.device.net.GwListResponse
import com.govee.base2home.main.choose.BaseBleDeviceChooseActivity
import com.govee.base2home.main.gw.GwInfo
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.reform4dbgw.CommonConstant
import com.govee.base2home.reform4dbgw.router.RouterRuler
import com.govee.base2home.util.ClickUtil
import com.govee.base2home.videoplay.CommonVideoActivity
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.setVisibility
import com.govee.base2kt.utils.IntentUtils
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.bbq.ConsV1
import com.govee.ble.BleController
import com.govee.ble.event.EventBleBroadcastListenerTrigger
import com.govee.home.account.config.AccountConfig
import com.govee.thnew.config.ThConfig4Detail
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.databinding.ThnewAc4BindGatewayGuideBinding
import com.govee.thnew.ui.detail.Ac4ThCommonDetail
import com.govee.thnew.ui.detail.Ac4ThCommonDetailNew
import com.govee.ui.R
import com.ihoment.base2app.Cache
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2024/3/21
 * @description 温湿度计-->被添加后引导去绑定网关的页面
 */
class Ac4BindGwGuide : AbsNetActivity(), OnClickListener {

    private var hasH5151Gw = false
    private lateinit var addInfo: AddInfo
    private var mBind: ThnewAc4BindGatewayGuideBinding? = null

    override fun bindView(layoutId: Int): Boolean {
        mBind = DataBindingUtil.setContentView(this, layoutId)
        return true
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        addInfo = IntentUtils.parseParcelable<AddInfo>(intent, ThConsV1.KEY_4_ADD_INFO) ?: AddInfo()
        mBind = DataBindingUtil.bind(containerView)
        loadGwData()
        //适配insetTop
        adaptationInsetTop(com.govee.thnew.R.id.top_flag, AppUtil.getScreenWidth() * 61 / 750)
        //设置监听
        setListener()
    }

    private fun initGwInfo(gwList: List<GwInfo>?) {
        if (null == gwList) {
            refreshBindView(false)
            return
        }
        for (item in gwList) {
            if (GoodsType.GOODS_TYPE_VALUE_TH_BLE_IOT_GATEWAY == item.goodsType) {
                hasH5151Gw = true
                break
            }
        }
        refreshBindView(hasH5151Gw)
    }

    private fun refreshBindView(hasH5151Gw: Boolean) {
        mBind?.let {
            if (hasH5151Gw) {
                if (TextUtils.isEmpty(ThConfig4Support.guideVideo4BindGw(addInfo.goodsType, addInfo.sku))) {
                    it.tvBindGw.setText(R.string.app_bind_to_gw)
                    it.tvLable.setText(R.string.th_gateway_use_guide_tip1)
                    it.iconTips.setImageDrawable(ResUtil.getDrawable(R.mipmap.new_pics_add_5043_anzhuang_wancheng_1))
                    it.tipsLable.visibility = View.VISIBLE
                    it.tvTips2.visibility = View.VISIBLE
                } else {
                    it.iconTips.setImageDrawable(ResUtil.getDrawable(R.mipmap.new_pics_add_5043_anzhuang_wancheng_1))
                    it.tvGwVideoPage.setVisibility(true)
                    it.tvBindGw.setText(R.string.thnew_h5111_to_bind_gw)
                    it.tvLable.setText(R.string.th_h5100_use_guide_tip2)
                }
            } else {
                it.iconTips.setImageDrawable(ResUtil.getDrawable(ThConfig4Support.getBindGwGuideImgRes(addInfo.goodsType, addInfo.sku)))
                it.tvBindGw.setText(R.string.app_to_add_gw)
                it.tvLable.setText(R.string.th_h5100_use_guide_tip2)
            }
        }
    }

    private fun loadGwData() {
        if (!AccountConfig.read().isHadToken) {
            refreshBindView(false)
            return
        }
        showLoading()
        val gwListRequest = GwListRequest(transactions.createTransaction())
        Cache.get(IDeviceNet::class.java).gwList.enqueue(IHCallBack(gwListRequest))
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onGwListResponse(response: GwListResponse) {
        if (!transactions.isMyTransaction(response)) return
        initGwInfo(response.data)
        hideLoading()
    }

    override fun onErrorResponse(response: ErrorResponse) {
        refreshBindView(false)
        hideLoading()
    }

    private fun setListener() {
        mBind?.tvNoBind?.setOnClickListener(this)
        mBind?.tvBindGw?.setOnClickListener(this)
        mBind?.tvGwVideoPage?.clickDelay {
            CommonVideoActivity.jump2CommonVideoActivity(this, ThConfig4Support.guideVideo4BindGw(addInfo.goodsType, addInfo.sku))
        }
    }

    override fun onClick(v: View?) {
        v?.let { view ->
            mBind?.let { binding ->
                if (ClickUtil.getInstance.clickQuick()) {
                    return
                }
                when (view) {
                    binding.tvNoBind -> {
                        //不去绑定网关则直接跳转到详情页
                        intent.extras?.let {
                            if (ThConfig4Detail.useNewDetailPage(addInfo.goodsType, addInfo.sku)) {
                                JumpUtil.jumpWithBundle(this@Ac4BindGwGuide, Ac4ThCommonDetailNew::class.java, true, it)
                            } else {
                                JumpUtil.jumpWithBundle(this@Ac4BindGwGuide, Ac4ThCommonDetail::class.java, true, it)
                            }
                        }
                    }

                    binding.tvBindGw -> {
                        //先断开蓝牙
                        BleController.getInstance().disconnectBleAndNotify()
                        //引导去绑定网关或添加网关
                        if (hasH5151Gw) {
                            //去绑定H5151网关
                            JumpUtil.jumpWithBundle(this@Ac4BindGwGuide, Base2homeConfig.getConfig().gwListAc, true, Bundle())
                            if (TextUtils.isEmpty(ThConfig4Support.guideVideo4BindGw(addInfo.goodsType, addInfo.sku))) {
                                JumpUtil.jumpWithBundle(this@Ac4BindGwGuide, Base2homeConfig.getConfig().gwListAc, true, Bundle())
                            } else {
                                //跳转至--远程查看页面(绑定网关页面)
                                val args = Bundle()
                                args.putString(CommonConstant.COMMON_KEY_SKU, addInfo.sku)
                                args.putString(CommonConstant.COMMON_KEY_DEVICE, addInfo.device)
                                args.putString(CommonConstant.SUB_DEVICE_BLE_SOFT_VERSION, addInfo.bleSoftVersion)
                                args.putString(CommonConstant.SUB_DEVICE_BLE_HARD_VERSION, addInfo.bleHardVersion)
                                args.putBoolean(CommonConstant.KEY_4_NEED_SKIP_TO_DETAIL_PAGE, true)
                                RouterRuler.instance.startToRemoteLook(this, args)
                                //关闭本页面
                                finish()
                            }
                        } else {
                            //去添加H5151网关
                            toBleScanAc()
                        }
                    }

                    else -> {}
                }
            }
        }
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.thnew.R.id.ac_container
    }

    override fun getLayout(): Int {
        return com.govee.thnew.R.layout.thnew_ac_4_bind_gateway_guide
    }

    private fun toBleScanAc() {
        //进入蓝牙扫描界面；需要关闭主界面蓝牙广播
        EventBleBroadcastListenerTrigger.sendEventBleBroadcastListenerTrigger(false)
        //跳转至设备扫描页
        val bundle = Bundle()
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_NEED_LOCATION, true)
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG, true)
        bundle.putString(BaseBleDeviceChooseActivity.INTENT_KEY_SKU_NAME, Constant4L5.H5151)
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG_HINT, true)
        bundle.putInt(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG_HINT_DES, R.string.h5074_more_device_5074_hint_des)
        bundle.putInt(BaseBleDeviceChooseActivity.INTENT_KEY_DEVICE_FLASHING_RSSI_VALUE, ConsV1.ble_ssid_compare_value)
        JumpUtil.jump(this, BaseBleDeviceChooseActivity::class.java, bundle)
    }

    @SuppressLint("MissingSuperCall")
    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        //不允许物理按键返回
    }

    private fun showLoading(delayTimeMills: Long = 60 * 1000L) {
        LoadingDialog.createDialog(this, com.ihoment.base2app.R.style.DialogDim, delayTimeMills).setEventKey(TAG).show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(TAG)
    }
}