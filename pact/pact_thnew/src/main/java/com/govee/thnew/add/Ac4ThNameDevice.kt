package com.govee.thnew.add

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.text.TextUtils
import android.widget.FrameLayout
import com.govee.base2home.Constant4L5
import com.govee.base2home.config.Base2homeConfig
import com.govee.base2home.device.AbsDeviceNameAcV2
import com.govee.base2home.main.OfflineDeviceListConfig
import com.govee.base2home.main.tab.EventTabDefault
import com.govee.base2home.push.EventDeviceListFresh
import com.govee.base2home.reform4dbgw.view.ThDbgwLinkageSupport.linkageSupport
import com.govee.base2kt.ext.setVisibility
import com.govee.base2kt.utils.IntentUtils
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.net.IThNet
import com.govee.base2newth.net.RequestUploadFrequency
import com.govee.base2newth.net.ResponseUploadFrequency
import com.govee.ble.BleController
import com.govee.db.utils.globalLaunch
import com.govee.home.account.config.AccountConfig
import com.govee.thnew.add.h5112.Ac4H5112BindGwGuide
import com.govee.thnew.ble.ThBle
import com.govee.thnew.ble.controller.Controller4UploadFreq
import com.govee.thnew.config.ThConfig4Detail
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.databinding.ThnewH5100MarkNumViewBinding
import com.govee.thnew.ui.detail.Ac4ThCommonDetail
import com.govee.thnew.ui.detail.Ac4ThCommonDetailNew
import com.govee.thnew.ui.op.TriggersSyncManager
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.Cache
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import kotlinx.coroutines.Dispatchers
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2024/2/21
 * @description 温湿度计-->设备命名页
 */
class Ac4ThNameDevice : AbsDeviceNameAcV2() {

    private lateinit var addInfo: AddInfo

    /**
     * 上传数据频率是否已经成功同步到服务端
     */
    private var hasSyncUploadFreq = false

    companion object {
        /**
         * 跳转到设备命名页
         *
         * @param context
         * @param addInfo
         */
        fun jump2ThNameDevice(context: Context, addInfo: AddInfo) {
            val bundle = Bundle()
            bundle.putParcelable(ThConsV1.KEY_4_ADD_INFO, addInfo)
            JumpUtil.jump(context, Ac4ThNameDevice::class.java, bundle)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setNeedUploadRate(ThConfig4Support.getThCdUploadFrequencyInfo(addInfo.goodsType, addInfo.sku).first == ThConfig4Support.UPLOAD_FREQUENCY_V0)
        updateInterval(addInfo.uploadRate)
        //H5100/H5110有贴标签提示
        if (ThConfig4Support.hasFlagRemind(addInfo.goodsType, addInfo.sku)) {
            findViewById<FrameLayout>(com.govee.base2home.R.id.fl_other_content_container)?.let {
                it.addView(ThnewH5100MarkNumViewBinding.inflate(layoutInflater).root)
                it.setVisibility(true)
            }
        }
        //对支持分布式网关的温湿度计设备获取bdType并上传服务端
        if (linkageSupport(addInfo.sku, addInfo.bleHardVersion, addInfo.bleSoftVersion)) {
            var triggersSyncManager: TriggersSyncManager? = TriggersSyncManager()
            triggersSyncManager?.toComm4DbgwSub(addInfo.sku, addInfo.device, false) {
                //置空资源
                triggersSyncManager = null
            }
        }
    }

    private fun updateInterval(uploadRate: Int) {
        if (needUploadRate) {
            addInfo.uploadRate = uploadRate
            intervalViewV2.updateChooseInterval(uploadRate)
        }
    }

    override fun initParams(intent: Intent) {
        addInfo = IntentUtils.parseParcelable<AddInfo>(getIntent(), ThConsV1.KEY_4_ADD_INFO) ?: AddInfo()
        //更新告警信息
        //主+从款sku的告警信息在添加成功后即已更新
        if (!ThConfig4Support.isMainSubDevice(addInfo.goodsType, addInfo.sku)) {
            WarnConfig.read().updateWarningRange(addInfo.warnRange, false)
        }
    }

    override fun chooseIntervalMinutes(minutes: Int) {
        addInfo.uploadRate = minutes
        sendFrequencyController(minutes)
    }

    private fun sendFrequencyController(minutes: Int) {
        val hour = minutes / 60
        val minute = minutes % 60
        val controllerUploadFrequency = Controller4UploadFreq(hour, minute) {
            globalLaunch(Dispatchers.Main) {
                if (it) {
                    SafeLog.i("xiaobing") { "Ac4ThNameDevice--sendFrequencyController-->${minutes}" }
                    updateInterval(minutes)
                    toSyncUploadFreqToService()
                }
            }
        }
        ThBle.getInstance.startControllers(controllerUploadFrequency)
    }

    override fun getIntervalMinutes(): IntArray {
        return ThConfig4Support.getThCdUploadFrequencyInfo(addInfo.goodsType, addInfo.sku).second
    }

    override fun toSaveDeviceName(deviceName: String?) {
        if (TextUtils.isEmpty(deviceName)) {
            return
        }
        if (!AccountConfig.read().isHadToken) {
            //未登录；修改成功同步到本地
            showLoading()
            OfflineDeviceListConfig.read().getDevices(addInfo.sku, addInfo.device)?.let { absDevice ->
                absDevice.deviceName = deviceName!!
                JsonUtil.fromJson(absDevice.deviceExt?.deviceSettings ?: "", AddInfo::class.java)?.let {
                    it.deviceName = deviceName
                    absDevice.deviceExt?.deviceSettings = JsonUtil.toJson(it)
                    OfflineDeviceListConfig.read().addOfflineDevice(absDevice)
                    window.decorView.postDelayed({
                        hideLoading()
                        onSaveDeviceNameSuc(deviceName)
                    }, 1000)
                }
            }
        } else {
            super.toSaveDeviceName(deviceName)
            if (needUploadRate && !hasSyncUploadFreq) {
                toSyncUploadFreqToService()
            }
        }
    }

    override fun doSkip() {
        jumpToNextPage()
    }

    override fun onSaveDeviceNameSuc(newDeviceName: String) {
        addInfo.deviceName = newDeviceName
        jumpToNextPage()
    }

    override fun getDevice(): String {
        return addInfo.device
    }

    override fun getSku(): String {
        return addInfo.sku
    }

    override fun getDeviceName(): String {
        return addInfo.deviceName
    }

    private fun toSyncUploadFreqToService() {
        //未登录无须同步至服务端
        if (!AccountConfig.read().isHadToken) {
            return
        }
        //修改标识位
        hasSyncUploadFreq = false
        //同步到服务端
        val request4UploadFreq = RequestUploadFrequency(transactions.createTransaction(), addInfo.sku, addInfo.device, addInfo.uploadRate)
        Cache.get(IThNet::class.java).updateDeviceUploadFrequency(request4UploadFreq).enqueue(IHCallBack(request4UploadFreq))
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4SyncUploadFreqToService(response: ResponseUploadFrequency) {
        if (!transactions.isMyTransaction(response)) return
        hasSyncUploadFreq = true
    }

    /**
     * 跳转下级页面
     * 备注：该跳转跟蓝牙连接与否无关
     */
    private fun jumpToNextPage() {
        //处于后台则不跳转ac
        if (BaseApplication.getBaseApplication().isInBackground) return
        //刷新设备列表
        EventDeviceListFresh.sendEventDeviceListFresh(true)
        //连接蓝牙的情况下，支持wifi则跳转到wifi配置页面
        if (ThConfig4Support.supportWifi(addInfo.goodsType, addInfo.sku) && BleController.getInstance().isConnected) {
            //跳转到wifi设置界面
            Ac4ThWifiSetting.jump2WifiChooseAc4Add(this, addInfo)
        } else {
            ThBle.getInstance.inStep4AddDevice(false)
            //说明是从网关添加列表跳转过来的(部分纯蓝牙设备可以从其支持的网关页面添加)
            if (BaseApplication.getBaseApplication().hadActivity(Constant4L5.H5151GwAddSubClassName)) {
                //断开连接
                BleController.getInstance().disconnectBleAndNotify()
                //关闭本页面
                finish()
            } else {
                //通知Tab更改为Default
                EventTabDefault.sendEventTabDefault()
                //页面传参
                val bundle = Bundle().apply {
                    putParcelable(ThConsV1.KEY_4_ADD_INFO, addInfo)
                }
                val mainClass = Base2homeConfig.getConfig().mainAcClass
                if (ThConfig4Support.hasBindGwGuide(addInfo.goodsType, addInfo.sku)) {
                    when (addInfo.sku) {
                        Constant4L5.H5112 -> {
                            //跳转至引导绑定网关页面
                            BaseApplication.getBaseApplication().finishOtherAndStartNewAc(mainClass, Ac4H5112BindGwGuide::class.java, bundle)
                        }

                        else -> {
                            //跳转至引导绑定网关页面
                            BaseApplication.getBaseApplication().finishOtherAndStartNewAc(mainClass, Ac4BindGwGuide::class.java, bundle)
                        }
                    }
                } else {
                    //跳转到详情页
                    if (ThConfig4Detail.useNewDetailPage(addInfo.goodsType, addInfo.sku)) {
                        BaseApplication.getBaseApplication().finishOtherAndStartNewAc(mainClass, Ac4ThCommonDetailNew::class.java, bundle)
                    } else {
                        BaseApplication.getBaseApplication().finishOtherAndStartNewAc(mainClass, Ac4ThCommonDetail::class.java, bundle)
                    }
                }
            }
        }
    }
}