package com.govee.thnew.ui.compare

import android.annotation.SuppressLint
import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.activity.viewModels
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.alibaba.android.arouter.facade.annotation.Route
import com.govee.base2home.Constant
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.common.BaseRvAdapter
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.reform4dbgw.router.RouterRuler
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ac.AbsAc
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.isVisible
import com.govee.base2kt.ext.setVisibility
import com.govee.base2kt.ext.toast
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.DataGroup
import com.govee.base2newth.chart.IntervalType
import com.govee.base2newth.chart.TimeUtil
import com.govee.base2newth.chart.newchart.ChartController4New
import com.govee.base2newth.chart.newchart.ConfigParams
import com.govee.base2newth.chart.newchart.DbDataVm4New
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.other.Config4MultiChartType
import com.govee.base2newth.other.SyncTemUnitUtil
import com.govee.thnew.add.AddInfo
import com.govee.thnew.ble.event.ReleaseVMEvent
import com.govee.thnew.config.ThConfig4Compare
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.databinding.ThnewAc4MultiDevCompareBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.ui.R
import com.govee.ui.dialog.TimeDialogV18
import com.govee.util.recordUseCount
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2024/4/2
 * @description 温湿度计=->多设备图表数据对比页面
 */
@Route(path = RouterRuler.ROUTER_PATH_4_THCD_MULTI_COMPARE)
class Ac4ThMultiDevCompare : AbsAc<ThnewAc4MultiDevCompareBinding>() {

    companion object {
        fun jump2MultiDevCompare(context: Context, deviceInfoStr: String, probeIndex: Int = -1) {
            val bundle = Bundle()
            bundle.putString(Constant.intent_ac_key_setting_str, deviceInfoStr)
            bundle.putInt(Constant4L5.KEY_4_TH_PROBE_INDEX, probeIndex)
            JumpUtil.jump(context, Ac4ThMultiDevCompare::class.java, bundle)
        }
    }

    private val vm4Mc by lazy {
        ViewModelProvider(this)[Vm4MultiCompare::class.java].apply {
            init(this@Ac4ThMultiDevCompare, bindExt.goodsType, bindExt.sku, getUsedDeviceId(), bindExt.address)
        }
    }
    private lateinit var bindExt: AddInfo
    private var probeIndex = -1

    /**
     * 数据展示的vm
     */
    private val vm4ChartData: DbDataVm4New by viewModels()

    /**
     * 图表展示控制类
     */
    private lateinit var chartOp4ThMc: ChartController4New

    /**
     * 当前选中的图表时间段
     */
    private var selectedDataGroup: DataGroup? = DataGroup.hour

    /**
     * 对比数据的时间段
     */
    private lateinit var mainLineTsPair: Pair<Long, Long>

    /**
     * 选择图表类型的弹窗
     */
    private val selectChartTypeDialog by lazy {
        Dialog4ThSelectChartType.createDialog(this@Ac4ThMultiDevCompare, bindExt.goodsType, bindExt.sku, bindExt.device, false, probeIndex)
    }

    /**
     * 已添加的对比设备列表适配器
     */
    private val compareDeviceAdapter by lazy {
        Adapter4ComparedDevice(this@Ac4ThMultiDevCompare, arrayListOf<ChartDevice>().apply {
            add(ChartDevice().apply {
                this.goodsType = bindExt.goodsType
                this.sku = bindExt.sku
                this.device = getUsedDeviceId()
                this.deviceName = getUsedDeviceName()
                this.bleAddress = bindExt.address
                lineIndex = ThConsV1.LINE_1
                loadDataStatus = ChartDevice.HAS_DATA
            })
            add(ChartDevice().apply {
                lineIndex = ThConsV1.LINE_UNDEFINED
            })
        })
    }

    /**
     * 选择添加对比数据的设备列表的弹窗
     */
    private val addComparedDeviceDialog by lazy {
        Dialog4AddComparedDevice.createDialog(this@Ac4ThMultiDevCompare)
    }

    /**
     * 快速选择时间段的时间段列表适配器
     */
    private val selectedPeriodAdapter by lazy {
        Adapter4FastSelectTime(this, arrayListOf(TimePeriod().apply {
            selectTime = ResUtil.getString(R.string.th_text_4_mc_last_1_hours)
        }, TimePeriod().apply {
            selectTime = ResUtil.getString(R.string.th_text_4_mc_last_1_days)
            selected = true
        }, TimePeriod().apply {
            selectTime = ResUtil.getString(R.string.th_text_4_mc_last_1_weeks)
        }, TimePeriod().apply {
            selectTime = ResUtil.getString(R.string.th_text_4_mc_last_1_mouths)
        }, TimePeriod().apply {
            selectTime = ResUtil.getString(R.string.th_text_4_mc_last_1_years)
        })) {
            vm4Mc.fastSelectTimePeriod(it)
        }
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.thnew.R.id.ac_container
    }

    override fun adapterInsetViewId(): Int {
        return com.govee.thnew.R.id.sp_top_adjust_holder
    }

    override fun layoutId(): Int {
        return com.govee.thnew.R.layout.thnew_ac_4_multi_dev_compare
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initData()
        initUi()
        initOpClick()
        initObserver()
        //统计
        recordUseCount(bindExt.sku, ParamFixedValue.click_multi_data_compare)
    }

    private fun initData() {
        val settingStr = intent.getStringExtra(Constant.intent_ac_key_setting_str) ?: ""
        bindExt = JsonUtil.fromJson(settingStr, AddInfo::class.java) ?: run {
            toast(R.string.h721214_other_listen)
            finish()
            return
        }
        probeIndex = intent.getIntExtra(Constant4L5.KEY_4_TH_PROBE_INDEX, -1)
        SafeLog.i("xiaobing") { "Ac4ThMultiDevCompare--initData-->probeIndex:${probeIndex}" }
        //从其他工程的温湿度计详情页跳转至此，会执行Vm4ThOpManager的初始化
        Vm4ThOpManager.init(bindExt, null, Vm4ThOpManager.INIT_FROM_COMPARE)
    }

    private fun initUi() {
        viewBinding.let { vb ->
            //选择的设备列表适配
            vb.rvDeviceItem4MultiCompare.layoutManager = LinearLayoutManager(this, RecyclerView.HORIZONTAL, false)
            vb.rvDeviceItem4MultiCompare.adapter = compareDeviceAdapter
            //快速选择的时间段列表适配
            vb.rvTimeRange4MultiCompare.layoutManager = LinearLayoutManager(this, RecyclerView.HORIZONTAL, false)
            vb.rvTimeRange4MultiCompare.adapter = selectedPeriodAdapter
        }
        updateTemUnit(Vm4ThOpManager.instance()?.isFahOpen4Compare() ?: false)
        initChartSet()
    }

    /**
     * 获取自定义使用的deviceId
     * 备注：用于区分H5112的探针1和探针2的数据
     */
    private fun getUsedDeviceId(): String {
        return when (bindExt.sku) {
            Constant4L5.H5112 -> {
                if (probeIndex == Constant4L5.PROBE_INDEX_4_TEM_HUM) {
                    bindExt.device
                } else {
                    ThConsV1.getH5112Device4Pb2(bindExt.device)
                }
            }

            else -> {
                bindExt.device
            }
        }
    }

    /**
     * 获取自定义使用的deviceName
     * 备注：用于区分H5112的探针1和探针2的数据
     */
    private fun getUsedDeviceName(): String {
        return when (bindExt.sku) {
            Constant4L5.H5112 -> {
                when (probeIndex) {
                    Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                        "${bindExt.deviceName}-${bindExt.probeName1}"
                    }

                    Constant4L5.PROBE_INDEX_4_TEM -> {
                        "${bindExt.deviceName}-${bindExt.probeName2}"
                    }

                    else -> {
                        bindExt.deviceName
                    }
                }
            }

            else -> {
                bindExt.deviceName
            }
        }
    }

    /**
     * 更新图表相关配置
     */
    private fun initChartSet() {
        WarnConfig.read().queryWarningRangeByKey(bindExt.sku, getUsedDeviceId())?.let {
            //配置处理数据的校准值
            vm4ChartData.setCali(it.temCali, it.humCali)
        } ?: run {
            vm4ChartData.setCali(0, 0)
        }
        //图表初始化相关设置
        chartOp4ThMc = ChartController4New(this, ConfigParams().apply {
            //设备信息
            goodsType = bindExt.goodsType
            sku = bindExt.sku
            device = getUsedDeviceId()
            bleAddress = bindExt.address
            bleSv = bindExt.bleSoftVersion
            bleHv = bindExt.bleHardVersion
        }).apply {
            //设置时间段显示切换的监听
            setChartListener(object : ChartController4New.ChartListener {
                override fun beScale() {
                    viewBinding.cspvBottomBarBg4MultiCompare.clearSelect()
                }

                override fun timeChange(startTimeStamp: Long, endTimeStamp: Long, intervalType: IntervalType) {
                    updateTime(startTimeStamp, endTimeStamp, intervalType)
                }

                override fun hasDrawnChart() {
                    hideLoading()
                }
            })
            setPointEndNum(0, 1, 1, 1, 2)
        }
        //图表相关试图初始化
        viewBinding.let { vb ->
            //co2
            vb.co2Container.ttcPm25Chart4ThCompare.setChart(chartOp4ThMc)
            //pm25
            vb.pm25Container.ttcPm25Chart4ThCompare.setChart(chartOp4ThMc)
            //温度
            vb.temContainer.ttcTemChart4ThCompare.setChart(chartOp4ThMc)
            //湿度
            vb.humContainer.ttcHumChart4ThCompare.setChart(chartOp4ThMc)
            //露点
            vb.dpContainer.ttcDpChart4ThCompare.setChart(chartOp4ThMc)
            //VPD
            vb.vpdContainer.ttcVpdChart4ThCompare.setChart(chartOp4ThMc)
        }
        updateShowCharts()
    }

    /**
     * 根据选择的类型显示相应图表
     */
    private fun updateShowCharts() {
        var showCo2 = bindExt.sku == Constant4L5.H5140
        var showPm25 = bindExt.sku == Constant4L5.H5106
        var showTem = true
        val supportHum = when (bindExt.sku) {
            Constant4L5.H5112 -> {
                probeIndex == Constant4L5.PROBE_INDEX_4_TEM_HUM
            }

            else -> {
                ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku)
            }
        }
        var showHum = supportHum
        var showDp = supportHum
        var showVpd = supportHum
        Config4MultiChartType.getConfig().getChartTypes(bindExt.sku, bindExt.device)?.let {
            for (chartType in it) {
                when (chartType.type) {

                    ThConsV1.CO2 -> {
                        showCo2 = showCo2 && chartType.selected
                    }

                    ThConsV1.PM25 -> {
                        showPm25 = showPm25 && chartType.selected
                    }

                    ThConsV1.TEM -> {
                        showTem = showTem && chartType.selected
                    }

                    ThConsV1.HUM -> {
                        showHum = showHum && chartType.selected
                    }

                    ThConsV1.DP -> {
                        showDp = showDp && chartType.selected
                    }

                    ThConsV1.VPD -> {
                        showVpd = showVpd && chartType.selected
                    }

                    else -> {}
                }
            }
        } ?: run {
            //默认只有温度、湿度是展示的
            showCo2 = false
            showPm25 = false
            showDp = false
            showVpd = false
        }
        viewBinding.let { vb ->
            vb.co2Container.root.setVisibility(showCo2)
            vb.pm25Container.root.setVisibility(showPm25)
            vb.temContainer.root.setVisibility(showTem)
            vb.humContainer.root.setVisibility(showHum)
            vb.dpContainer.root.setVisibility(showDp)
            vb.vpdContainer.root.setVisibility(showVpd)
        }
    }

    /**
     * 更新温度单位的显示
     */
    private fun updateTemUnit(fahOpen: Boolean) {
        viewBinding.let { vb ->
            vb.temContainer.ivTemUnitIcon4ThCompare.setImageDrawable(ResUtil.getDrawable(if (fahOpen) R.mipmap.new_sensor_setting_switch_fahrenheit else R.mipmap.new_sensor_setting_switch_celsius))
            vb.temContainer.ttcTemChart4ThCompare.setChangeValueShow(fahOpen, if (fahOpen) getString(R.string.tem_unit_fah) else getString(R.string.tem_unit_cel))
            vb.dpContainer.ttcDpChart4ThCompare.setChangeValueShow(fahOpen, if (fahOpen) getString(R.string.tem_unit_fah) else getString(R.string.tem_unit_cel))
        }
    }

    /**
     * 更新图表的展示时间段
     */
    private fun updateTime(startTimeStamp: Long, endTimeStamp: Long, intervalType: IntervalType) {
        val calibration = selectedDataGroup?.let {
            //在默认标签下，需要格式时间起点的一致
            TimeUtil.calibration(startTimeStamp, endTimeStamp, it)
        } ?: longArrayOf(startTimeStamp, endTimeStamp)
        val formatStartTime1: String
        val formatEndTime1: String
        if (IntervalType.year_1_month == intervalType) {
            formatStartTime1 = TimeFormatM.getInstance().formatTimeToYM(calibration[0])
            formatEndTime1 = TimeFormatM.getInstance().formatTimeToYM(calibration[1])
        } else {
            formatStartTime1 = TimeFormatM.getInstance().formatTimeToHMMD(calibration[0])
            formatEndTime1 = TimeFormatM.getInstance().formatTimeToHMMD(calibration[1])
        }
        //更新展示时间
        viewBinding.let { vb ->
            vb.co2Container.tvPm25StartTime14ThCompare.text = formatStartTime1
            vb.co2Container.tvPm25EndTime14ThCompare.text = formatEndTime1
            vb.pm25Container.tvPm25StartTime14ThCompare.text = formatStartTime1
            vb.pm25Container.tvPm25EndTime14ThCompare.text = formatEndTime1
            vb.temContainer.tvTemStartTime14ThCompare.text = formatStartTime1
            vb.temContainer.tvTemEndTime14ThCompare.text = formatEndTime1
            vb.humContainer.tvHumStartTime14ThCompare.text = formatStartTime1
            vb.humContainer.tvHumEndTime14ThCompare.text = formatEndTime1
            vb.dpContainer.tvDpStartTime14ThCompare.text = formatStartTime1
            vb.dpContainer.tvDpEndTime14ThCompare.text = formatEndTime1
            vb.vpdContainer.tvVpdStartTime14ThCompare.text = formatStartTime1
            vb.vpdContainer.tvVpdEndTime14ThCompare.text = formatEndTime1
        }
    }

    private fun initOpClick() {
        viewBinding.let { vb ->
            vb.ivBackBtn4MultiCompare.clickDelay {
                onBackPressed()
            }
            vb.tvFilterLabel4MultiCompare.clickDelay {
                //点击筛选
                selectChartTypeDialog.show {
                    updateShowCharts()
                }
                //统计
                recordUseCount(bindExt.sku, ParamFixedValue.click_filter)
            }
            //自定义对比时间段
            vb.vTimeRangeBg4MultiCompare.clickDelay {
                vm4Mc.ld4SelectedTimePeriod.value?.let {
                    TimeDialogV18.createDialog(this, ResUtil.getString(R.string.filter_date_title), true, it.first, it.second, vm4Mc.minValidTime, vm4Mc.lastDataTime, needHour = true, listener = object : TimeDialogV18.DoneListener {
                        override fun chooseTime(startTime: Long, endTime: Long) {
                            selectedPeriodAdapter.clearSelected()
                            vm4Mc.customSelectTimePeriod(startTime, endTime, true, "自定义对比时间段...", true)
                        }
                    }).setEventKey(TAG).show()
                }
            }
            //切换温度单位
            vb.temContainer.ivTemUnitIcon4ThCompare.clickDelay {
                val fahOpen = !(Vm4ThOpManager.instance()?.isFahOpen4Compare() ?: false)
                Vm4ThOpManager.instance()?.setTemUnit4Compare(fahOpen)
                updateTemUnit(fahOpen)
                //同步温度单位到服务端
                SyncTemUnitUtil.syncTemUnit(Transactions().createTransaction(), bindExt.sku, fahOpen)
                //通知详情页
                Vm4ThOpManager.instance()?.ld4ChangeTemUnit?.value = true
            }
            //切换图表展示比例
            vb.cspvBottomBarBg4MultiCompare.setSelectListener { type, dataGroup ->
                selectedDataGroup = dataGroup
                if (type != null) {
                    chartOp4ThMc.setIntervalType(type)
                }
            }
            //导出数据
            vb.tvExportDataBtn4MultiCompare.clickDelay {
                chartOp4ThMc.hideAllPointInTime()
                Vm4ThOpManager.instance()?.exportData4Compare(arrayListOf<View>().apply {
                    add(vb.rvDeviceItem4MultiCompare)
                    add(vb.clExportContent14MultiCompare)
                    add(vb.clExportContent24MultiCompare)
                })
            }
        }
        //增、减要对比的设备
        compareDeviceAdapter.setOnRvViewInItemClickListener(
            object : BaseRvAdapter.OnRvViewInItemClickListener {
                override fun onViewInItemClick(v: View, position: Int) {
                    val comparedChartDevices = compareDeviceAdapter.getChartDevice()
                    if (comparedChartDevices.lastIndex < position) {
                        return
                    }
                    comparedChartDevices[position].let {
                        when (v.id) {
                            //添加对比设备、重新加载
                            com.govee.thnew.R.id.v_item_bg_4_compared_device -> {
                                if (it.lineIndex != ThConsV1.LINE_UNDEFINED) {
                                    //点击数据缺失，重新加载
                                    if (it.loadDataStatus == ChartDevice.NO_DATA) {
                                        Vm4ThOpManager.instance()?.loadThcdManager?.toLoadThcdBySingle(
                                            null,
                                            Vm4ThOpManager.RC_FROM_COMPARE_DATA,
                                            it.goodsType,
                                            it.sku,
                                            ThConsV1.getRealDeviceId(it.sku, it.device),
                                            it.bleAddress,
                                            vm4Mc.getTimeRange4Compare(),
                                            true
                                        )
                                    }
                                    return
                                }
                                addComparedDeviceDialog.show(comparedChartDevices) { selectedDevice ->
                                    comparedChartDevices.add(comparedChartDevices.lastIndex, selectedDevice)
                                    //若对比设备达到上限则不可再添加,须移除添加按钮item
                                    if (comparedChartDevices.size > ThConfig4Compare.MAX_COMPARE_DEVICE_NUM) {
                                        comparedChartDevices.removeAt(comparedChartDevices.lastIndex)
                                    }
                                    compareDeviceAdapter.updateList(comparedChartDevices)
                                    //数据处理
                                    vm4Mc.addDeviceToCompare(chartOp4ThMc, selectedDevice.goodsType, selectedDevice.sku, selectedDevice.device, selectedDevice.bleAddress)
                                }
                            }

                            //删除当前对比设备
                            com.govee.thnew.R.id.iv_delete_btn_4_compared_device -> {
                                comparedChartDevices.remove(it)
                                if (it.lineIndex == ThConsV1.LINE_2) {
                                    comparedChartDevices.apply {
                                        for (chartDevice in this) {
                                            if (chartDevice.lineIndex == ThConsV1.LINE_3) {
                                                chartDevice.lineIndex = ThConsV1.LINE_2
                                                break
                                            }
                                        }
                                    }
                                }
                                //添加按钮未添加，则将添加按钮重新添加进去
                                var hasAddBtn = false
                                for (selectedDevice in comparedChartDevices) {
                                    if (selectedDevice.lineIndex == ThConsV1.LINE_UNDEFINED) {
                                        hasAddBtn = true
                                        break
                                    }
                                }
                                if (!hasAddBtn) {
                                    comparedChartDevices.add(ChartDevice().apply {
                                        lineIndex = ThConsV1.LINE_UNDEFINED
                                    })
                                }
                                //刷新列表
                                compareDeviceAdapter.updateList(comparedChartDevices)
                                //数据处理
                                vm4Mc.deleteDeviceToCompare(chartOp4ThMc, it.goodsType, it.sku, it.device)
                            }

                            else -> {}
                        }
                    }
                }
            },
            com.govee.thnew.R.id.v_item_bg_4_compared_device,
            com.govee.thnew.R.id.iv_delete_btn_4_compared_device
        )
    }

    @SuppressLint("SetTextI18n")
    private fun initObserver() {
        vm4Mc.loadingChange.showDialog.observeInActivity(this) {
            showLoading()
        }
        vm4Mc.loadingChange.dismissDialog.observeInActivity(this) {
            hideLoading()
        }
        vm4Mc.ld4SelectedTimePeriod.observe(this) {
            mainLineTsPair = Pair(it.first, it.second)
            val startFormatTime = TimeFormatM.getInstance().formatTimeToHMYMD(it.first)
            val endFormatTime = TimeFormatM.getInstance().formatTimeToHMYMD(it.second)
            viewBinding.tvTimeValue4MultiCompare.text = "$startFormatTime - $endFormatTime"
            //处理图表数据
            vm4ChartData.dealAllLineThpData(it.third)
        }
        //给vm4Data加入生命周期监听
        lifecycle.addObserver(vm4ChartData)
        vm4ChartData.thpDataLiveData.observe(this) {
            chartOp4ThMc.updateRealData(it, mainLineTsPair, 0) {
                val curCompareTimeDis = mainLineTsPair.second - mainLineTsPair.first
                //根据对比时间段选择默认选中的展示时间段
                when {
                    curCompareTimeDis < ThConsV1.ONE_DAY_MILLIS -> {
                        viewBinding.cspvBottomBarBg4MultiCompare.updateDataGroupChoose(DataGroup.hour)
                    }

                    curCompareTimeDis < ThConsV1.ONE_WEEK_MILLIS -> {
                        viewBinding.cspvBottomBarBg4MultiCompare.updateDataGroupChoose(DataGroup.day)
                    }

                    curCompareTimeDis < ThConsV1.ONE_MONTH_MILLIS -> {
                        viewBinding.cspvBottomBarBg4MultiCompare.updateDataGroupChoose(DataGroup.week)
                    }

                    curCompareTimeDis < ThConsV1.ONE_YEAR_MILLIS -> {
                        viewBinding.cspvBottomBarBg4MultiCompare.updateDataGroupChoose(DataGroup.month)
                    }

                    curCompareTimeDis <= 2 * ThConsV1.ONE_YEAR_MILLIS -> {
                        viewBinding.cspvBottomBarBg4MultiCompare.updateDataGroupChoose(DataGroup.year)
                    }
                }
            }
        }
        Vm4ThOpManager.instance()?.loadThcdManager?.ld4LoadThCdStep?.observe(this) {
            var hasLoadedAll = true
            for (key in it.keys) {
                it[key]?.let { thcdInfo ->
                    when (thcdInfo.second.second) {
                        Vm4ThOpManager.THCD_4_WAIT_TO_LOAD,
                        Vm4ThOpManager.THCD_4_IS_LOAD_FROM_SERVICE,
                        Vm4ThOpManager.THCD_4_IS_LOAD_FROM_DEVICE,
                            -> {
                            viewBinding.let { vb ->
                                hasLoadedAll = false
                                if (!vb.tvLoadingRemind4MultiCompare.isVisible()) {
                                    vb.tvLoadingRemind4MultiCompare.setVisibility(true)
                                    return@observe
                                }
                            }
                        }

                        else -> {

                        }
                    }
                }
            }
            if (hasLoadedAll) {
                viewBinding.let { vb ->
                    if (vb.tvLoadingRemind4MultiCompare.isVisible()) {
                        vb.tvLoadingRemind4MultiCompare.setVisibility(false)
                        return@observe
                    }
                }
            }
        }
    }

    override fun onDestroy() {
        Vm4ThOpManager.instance()?.loadThcdManager?.clearLoadThcdDevices()
        Vm4ThOpManager.instance()?.release(Vm4ThOpManager.INIT_FROM_COMPARE)
        EventBus.getDefault().post(ReleaseVMEvent())
        super.onDestroy()
    }

    private fun showLoading(delayTimeMills: Long = 60 * 1000L) {
        LoadingDialog.createDialog(this, com.ihoment.base2app.R.style.DialogDim, delayTimeMills)
            .setEventKey(TAG).show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(TAG)
    }
}