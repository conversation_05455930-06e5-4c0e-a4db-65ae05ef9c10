package com.govee.thnew.ui.op

import com.govee.base2home.Constant4L5
import com.govee.base2home.iot.Iot
import com.govee.base2home.iot.IotTransactions
import com.govee.base2light.pact.iot.CmdPtReal
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.net.CommonSettings
import com.govee.base2newth.net.smw.Req4SetSettingsNewV1
import com.govee.base2newth.net.smw.smwNetService
import com.govee.home.account.config.AccountConfig
import com.govee.mvvm.ext.request
import com.govee.mvvm.globalLaunch
import com.govee.thnew.add.AddInfo
import com.govee.thnew.ble.controller.Controller4DeleteSubH5112
import com.govee.thnew.ble.controller.Controller4HumCali
import com.govee.thnew.ble.controller.Controller4HumWarning
import com.govee.thnew.ble.controller.Controller4TemCaliH5112
import com.govee.thnew.ble.controller.Controller4TemWarningH5112
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.Vm4ThOpManager.Companion.UPDATE_4_CALIBRATION
import com.govee.thnew.ui.Vm4ThOpManager.Companion.UPDATE_4_DELAY_ALARM
import com.govee.thnew.ui.Vm4ThOpManager.Companion.UPDATE_4_WARNING
import kotlinx.coroutines.Dispatchers

/**
 * <AUTHOR>
 * @date created on 2025/6/6
 * @description H5112的属性设置类
 */
internal class SettingOp4H5112(private val bindExt: AddInfo) {

    /**
     * 更新温度告警信息
     */
    fun updateTemWarningInfo(minAlarmTem: Int, maxAlarmTem: Int, alarmOn: Boolean, probeIndex: Int) {
        Vm4ThOpManager.instance()?.showLoading()
        var delayPushTime = 1
        when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                delayPushTime = bindExt.delayPushTime
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                delayPushTime = bindExt.delayPushTime2 ?: 1
            }

            else -> {}
        }
        Vm4ThOpManager.instance()?.sendCommand(
            Controller4TemWarningH5112(
                alarmOn,
                minAlarmTem,
                maxAlarmTem,
                delayPushTime,
                probeIndex
            ) {
                globalLaunch(Dispatchers.IO) {
                    if (it) {
                        when (probeIndex) {
                            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                                //赋新值
                                bindExt.temMin = minAlarmTem
                                bindExt.temMax = maxAlarmTem
                                bindExt.temWarning = alarmOn
                                //更新告警信息缓存
                                WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                            }

                            Constant4L5.PROBE_INDEX_4_TEM -> {
                                //赋新值
                                bindExt.temMin2 = minAlarmTem
                                bindExt.temMax2 = maxAlarmTem
                                bindExt.temWarning2 = alarmOn
                                //更新告警信息缓存
                                WarnConfig.read().updateWarningRange(bindExt.warnRange4Pb2, true)
                            }

                            else -> {}
                        }
                    }
                    Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.postValue(Pair(it, UPDATE_4_WARNING))
                    //若已登录则尝试同步到服务端
                    if (AccountConfig.read().isHadToken) {
                        updateTemWarning2Service(minAlarmTem, maxAlarmTem, alarmOn, probeIndex)
                    } else {
                        Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                    }
                    Vm4ThOpManager.instance()?.closeLoading()
                }
            })
    }

    /**
     * 将温度告警信息同步到服务端
     */
    private fun updateTemWarning2Service(temMin: Int, temMax: Int, temWarning: Boolean, probeIndex: Int) {
        var temCali = 0
        var delayPushTime = 1
        when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                temCali = bindExt.temCali
                delayPushTime = bindExt.delayPushTime
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                temCali = bindExt.temCali2 ?: 0
                delayPushTime = bindExt.delayPushTime2 ?: 1
            }

            else -> {}
        }
        updateTemInfo2Service(temMin, temMax, temWarning, temCali, delayPushTime, probeIndex)
    }

    /**
     * 更新温度校准值
     */
    fun updateTemCali(temCali: Int, probeIndex: Int) {
        Vm4ThOpManager.instance()?.showLoading()
        Vm4ThOpManager.instance()?.sendCommand(Controller4TemCaliH5112(temCali, probeIndex) {
            globalLaunch(Dispatchers.IO) {
                if (it) {
                    when (probeIndex) {
                        Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                            bindExt.temCali = temCali
                            //更新告警信息缓存
                            WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                        }

                        Constant4L5.PROBE_INDEX_4_TEM -> {
                            bindExt.temCali2 = temCali
                            //更新告警信息缓存
                            WarnConfig.read().updateWarningRange(bindExt.warnRange4Pb2, true)
                        }

                        else -> {}
                    }
                }
                Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.postValue(Pair(it, UPDATE_4_CALIBRATION))
                //若已登录则尝试同步到服务端
                if (AccountConfig.read().isHadToken) {
                    updateTemCali2Service(temCali, probeIndex)
                } else {
                    Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                }
                Vm4ThOpManager.instance()?.closeLoading()
            }
        })
    }

    /**
     * 将温度校准信息同步到服务端
     */
    private fun updateTemCali2Service(temCali: Int, probeIndex: Int) {
        var temMin = 0
        var temMax = 3000
        var temWarning = false
        var delayPushTime = 1
        when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                temMin = bindExt.temMin
                temMax = bindExt.temMax
                temWarning = bindExt.temWarning
                delayPushTime = bindExt.delayPushTime
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                temMin = bindExt.temMin2 ?: 0
                temMax = bindExt.temMax2 ?: 3000
                temWarning = bindExt.temWarning2 ?: false
                delayPushTime = bindExt.delayPushTime2 ?: 1
            }

            else -> {}
        }
        updateTemInfo2Service(temMin, temMax, temWarning, temCali, delayPushTime, probeIndex)
    }

    /**
     * 更新温度延时告警时间
     */
    fun updateDelayPushTime(delayPushTime: Int, probeIndex: Int) {
        Vm4ThOpManager.instance()?.showLoading()
        var temMin = 0
        var temMax = 3000
        var temWarning = false
        when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                temMin = bindExt.temMin
                temMax = bindExt.temMax
                temWarning = bindExt.temWarning
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                temMin = bindExt.temMin2 ?: 0
                temMax = bindExt.temMax2 ?: 3000
                temWarning = bindExt.temWarning2 ?: false
            }

            else -> {}
        }
        Vm4ThOpManager.instance()?.sendCommand(
            Controller4TemWarningH5112(
                temWarning,
                temMin,
                temMax,
                delayPushTime,
                probeIndex
            ) {
                globalLaunch(Dispatchers.IO) {
                    if (it) {
                        when (probeIndex) {
                            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                                bindExt.delayPushTime = delayPushTime
                            }

                            Constant4L5.PROBE_INDEX_4_TEM -> {
                                bindExt.delayPushTime2 = delayPushTime
                            }

                            else -> {}
                        }
                    }
                    Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.postValue(Pair(it, UPDATE_4_DELAY_ALARM))
                    //若已登录则尝试同步到服务端
                    if (AccountConfig.read().isHadToken) {
                        updateDelayPushTime2Service(delayPushTime, probeIndex)
                    } else {
                        Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                    }
                    Vm4ThOpManager.instance()?.closeLoading()
                }
            })
    }

    /**
     * 将温度延时告警时间同步给服务端
     */
    private fun updateDelayPushTime2Service(delayPushTime: Int, probeIndex: Int) {
        var temMin = 0
        var temMax = 3000
        var temWarning = false
        var temCali = 1
        when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                temMin = bindExt.temMin
                temMax = bindExt.temMax
                temWarning = bindExt.temWarning
                temCali = bindExt.temCali
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                temMin = bindExt.temMin2 ?: 0
                temMax = bindExt.temMax2 ?: 3000
                temWarning = bindExt.temWarning2 ?: false
                temCali = bindExt.temCali2 ?: 0
            }

            else -> {}
        }
        updateTemInfo2Service(temMin, temMax, temWarning, temCali, delayPushTime, probeIndex)
    }

    /**
     * 将温度相关变更信息同步到服务端
     */
    private fun updateTemInfo2Service(temMin: Int, temMax: Int, temWarning: Boolean, temCali: Int, delayPushTime: Int, probeIndex: Int) {
        Vm4ThOpManager.instance()?.request({
            smwNetService.updateSetting4Callback(Req4SetSettingsNewV1().apply {
                sku = bindExt.sku
                device = bindExt.device
                settings = CommonSettings().apply {
                    when (probeIndex) {
                        Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                            this.temMin = temMin
                            this.temMax = temMax
                            this.temWarning = temWarning
                            this.temCali = temCali
                            this.delayPushTime = delayPushTime
                        }

                        Constant4L5.PROBE_INDEX_4_TEM -> {
                            this.temMin2 = temMin
                            this.temMax2 = temMax
                            this.temWarning2 = temWarning
                            this.temCali2 = temCali
                            this.delayPushTime2 = delayPushTime
                        }

                        else -> {}
                    }
                }
            })
        })
    }

    /**
     * 更新设湿度告警信息
     */
    fun updateHumWarning(humMin: Int, humMax: Int, humWarning: Boolean) {
        Vm4ThOpManager.instance()?.showLoading()
        Vm4ThOpManager.instance()?.sendCommand(Controller4HumWarning(humWarning, humMin, humMax) {
            globalLaunch(Dispatchers.IO) {
                if (it) {
                    //赋新值
                    bindExt.humMin = humMin
                    bindExt.humMax = humMax
                    bindExt.humWarning = humWarning
                    //更新告警信息缓存
                    WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                }
                Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.postValue(Pair(it, UPDATE_4_WARNING))
                //若已登录则尝试同步到服务端
                if (AccountConfig.read().isHadToken) {
                    updateHumWarning2Service(humMin, humMax, humWarning)
                } else {
                    Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                }
                Vm4ThOpManager.instance()?.closeLoading()
            }
        })
    }

    /**
     * 将湿度告警信息同步给服务端
     */
    private fun updateHumWarning2Service(humMin: Int, humMax: Int, humWarning: Boolean) {
        updateHumInfo2Service(humMin, humMax, humWarning, bindExt.humCali)
    }

    /**
     * 更新湿度校准信息
     */
    fun updateHumCali(humCali: Int) {
        Vm4ThOpManager.instance()?.showLoading()
        Vm4ThOpManager.instance()?.sendCommand(Controller4HumCali(humCali) {
            globalLaunch(Dispatchers.IO) {
                if (it) {
                    bindExt.humCali = humCali
                    //更新告警信息缓存
                    WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                }
                Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.postValue(Pair(it, UPDATE_4_CALIBRATION))
                //若已登录则尝试同步到服务端
                if (AccountConfig.read().isHadToken) {
                    updateHumCali2Service(humCali)
                } else {
                    Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                }
                Vm4ThOpManager.instance()?.closeLoading()
            }
        })
    }

    /**
     * 将湿度校准信息同步给服务端
     */
    private fun updateHumCali2Service(humCali: Int) {
        updateHumInfo2Service(bindExt.humMin, bindExt.humMax, bindExt.humWarning, humCali)
    }

    /**
     * 将湿度变更信息同步到服务端
     */
    private fun updateHumInfo2Service(humMin: Int, humMax: Int, humWarning: Boolean, humCali: Int) {
        Vm4ThOpManager.instance()?.request({
            smwNetService.updateSetting4Callback(Req4SetSettingsNewV1().apply {
                sku = bindExt.sku
                device = bindExt.device
                settings = CommonSettings().apply {
                    this.humMin = humMin
                    this.humMax = humMax
                    this.humWarning = humWarning
                    this.humCali = humCali
                }
            })
        })
    }

    /**
     * 网关解绑子设备
     * 备注:H5112删除时，向其绑定的网关(H5044)发送解绑指令
     */
    fun unbindSubDev2Gw() {
        //H5112如果绑了网关，删除时要向设备发送解绑指令(无需处理结果，在网关详情页还会做同步操作)
        Vm4ThOpManager.instance()?.getGateInfo()?.let { gwInfo ->
            gwInfo.sno?.let { sno ->
                if (sno >= 0 && Iot.getInstance.isConnected) {
                    gwInfo.topic?.let { gwTopic ->
                        val ptRealCmd = CmdPtReal(Controller4DeleteSubH5112(sno).value)
                        Iot.getInstance.write(gwTopic, IotTransactions().createTransaction(false), ptRealCmd)
                    }
                }
            }
        }
    }
}