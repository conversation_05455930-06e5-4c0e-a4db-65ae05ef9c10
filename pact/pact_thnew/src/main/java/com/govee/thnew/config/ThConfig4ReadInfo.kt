package com.govee.thnew.config

import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import com.govee.base2home.Constant4L5
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2newth.AbsSingleController
import com.govee.base2newth.bbq.ble.controller.EventSecretKey
import com.govee.base2newth.bbq.config.SecretKeyConfig
import com.govee.base2newth.net.CommonSettings
import com.govee.base2newth.net.smw.Req4SetSettingsNewV1
import com.govee.base2newth.net.smw.smwNetService
import com.govee.db.utils.globalLaunch
import com.govee.thnew.add.AddInfo
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.ThBle
import com.govee.thnew.ble.controller.Controller4Battery
import com.govee.thnew.ble.controller.Controller4BleHv
import com.govee.thnew.ble.controller.Controller4BleSv
import com.govee.thnew.ble.controller.Controller4ChangeOrder2Comm
import com.govee.thnew.ble.controller.Controller4ComfortTemHum
import com.govee.thnew.ble.controller.Controller4DeviceId
import com.govee.thnew.ble.controller.Controller4DeviceIdH5112
import com.govee.thnew.ble.controller.Controller4DeviceWarningTime
import com.govee.thnew.ble.controller.Controller4HumCali
import com.govee.thnew.ble.controller.Controller4HumWarning
import com.govee.thnew.ble.controller.Controller4ProbeIcon
import com.govee.thnew.ble.controller.Controller4SecretKeyV0
import com.govee.thnew.ble.controller.Controller4SyncTime
import com.govee.thnew.ble.controller.Controller4TemCali
import com.govee.thnew.ble.controller.Controller4TemCaliH5112
import com.govee.thnew.ble.controller.Controller4TemWarning
import com.govee.thnew.ble.controller.Controller4TemWarningH5112
import com.govee.thnew.ble.controller.Controller4UploadFreq
import com.govee.thnew.ble.controller.Controller4Volume
import com.govee.thnew.ble.controller.Controller4WifiHv
import com.govee.thnew.ble.controller.Controller4WifiMac
import com.govee.thnew.ble.controller.Controller4WifiSv
import com.govee.thnew.ble.event.Event4Battery
import com.govee.thnew.ble.event.Event4BleHv
import com.govee.thnew.ble.event.Event4BleSv
import com.govee.thnew.ble.event.Event4ComfortTemHum
import com.govee.thnew.ble.event.Event4DeviceId
import com.govee.thnew.ble.event.Event4DeviceWarningTime
import com.govee.thnew.ble.event.Event4HumCali
import com.govee.thnew.ble.event.Event4HumWarning
import com.govee.thnew.ble.event.Event4ProbeIcon
import com.govee.thnew.ble.event.Event4SyncTime
import com.govee.thnew.ble.event.Event4TemCali
import com.govee.thnew.ble.event.Event4TemCaliH5112
import com.govee.thnew.ble.event.Event4TemWarning
import com.govee.thnew.ble.event.Event4TemWarningH5112
import com.govee.thnew.ble.event.Event4UploadFreq
import com.govee.thnew.ble.event.Event4Volume
import com.govee.thnew.ble.event.Event4WifiHv
import com.govee.thnew.ble.event.Event4WifiMac
import com.govee.thnew.ble.event.Event4WifiSv
import com.govee.thnew.ui.Vm4ThOpManager
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->添加设备时，从设备端读取设备信息的配置类
 * 备注:不同温湿度，其添加时须要从设备端读取的信息可能有差别，须对要读取的信息进行配置
 */
@Suppress("TYPE_INTERSECTION_AS_REIFIED_WARNING")
class ThConfig4ReadInfo {

    companion object {
        //读取数据的总超时时长
        private const val READ_INFO_OVER_TIME = 15 * 1000L
        private const val GV5179 = "GV5179"

        private const val RELOAD_NECESSARY_TIME = 1
    }

    init {
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
    }

    private val mHandler by lazy {
        Handler(Looper.getMainLooper())
    }

    /**
     * 读取结束的回调函数
     */
    private var readFinishCallback: (() -> Unit)? = null

    /**
     * 设备的属性信息对象
     */
    private lateinit var addInfo: AddInfo

    /**
     * 是否为添加设备时读取设备信息的场景
     */
    private var fromAddDevice = false

    /**
     * 类H5151网关的温湿度计子设备是否已经绑定了网关
     * 备注:用于同步服务端数据到本地(H5112无需此操作,反而需要始终以设备端数据为准，即如果不一致，要同步设备端数据到云端)
     */
    private var hasBindGw = false

    private val commonControllers by lazy {
        arrayListOf(
            Controller4BleHv(),
            Controller4BleSv(),
            Controller4TemWarning(),
            Controller4TemCali(),
            Controller4Battery(),
        )
    }
    private val commonControllersExt by lazy {
        arrayListOf(
            Controller4DeviceId(),
            Controller4SyncTime(),
        )
    }

    private val humControllers by lazy {
        arrayListOf(
            Controller4HumWarning(),
            Controller4HumCali(),
        )
    }

    private val wifiVsControllers by lazy {
        arrayListOf(
            Controller4WifiSv(),
            Controller4WifiHv(),
        )
    }

    /**
     * 根据温湿度计设备类型，获取其须要读取信息的Controllers
     * 备注：!!!须要根据不同sku进行配置,为方便区分sku故用sku名来判断
     * @param fromAddDevice 是否为添加流程读取信息，这个过程在配对时如果须要校验密钥，则会校验一次密钥，故无需重复校验
     * @param order 如B5178这样的有主从设备的，order用于标识主从，0为主设备，>0为从设备序号
     */
    private fun getReadControllers(fromAddDevice: Boolean, order: Int): Array<AbsSingleController> {
        val readControllers = arrayListOf<AbsSingleController>().apply {
            addAll(commonControllers)
        }
        var cacheSecretKey = addInfo.secretCode
        if (TextUtils.isEmpty(cacheSecretKey)) {
            cacheSecretKey = SecretKeyConfig.read().getSecretKey(addInfo.address) ?: ""
        }
        val isPairByV0 = ThConfig4Support.supportPair(addInfo.goodsType, addInfo.sku) == ThConfig4Support.PAIR_WAY_V0
        val needCheckSecretKey = !TextUtils.isEmpty(cacheSecretKey) && isPairByV0 && !fromAddDevice
        when (addInfo.sku) {
            //旧方案的部分设备必须先校验密钥，才能进行后续读写操作，否则10s左右设备会主动断连
            Constant4L5.H5052,
            Constant4L5.H5072,
                -> {
                if (needCheckSecretKey) {
                    readControllers.add(0, Controller4SecretKeyV0(cacheSecretKey))
                }
                readControllers.addAll(if (needCheckSecretKey) 3 else 2, humControllers)
                readControllers.addAll(if (needCheckSecretKey) 3 else 2, commonControllersExt)
            }

            Constant4L5.H5051,
            Constant4L5.H5071,
                -> {
                if (needCheckSecretKey) {
                    readControllers.add(0, Controller4SecretKeyV0(cacheSecretKey))
                }
                readControllers.add(if (needCheckSecretKey) 3 else 2, Controller4UploadFreq())
                readControllers.addAll(if (needCheckSecretKey) 3 else 2, wifiVsControllers)
                readControllers.addAll(if (needCheckSecretKey) 3 else 2, humControllers)
                readControllers.addAll(if (needCheckSecretKey) 3 else 2, commonControllersExt)
            }

            Constant4L5.H5074,
            Constant4L5.H5075,
            Constant4L5.H5100,
            Constant4L5.H5101,
            Constant4L5.H5102,
            Constant4L5.H5104,
            Constant4L5.H5105,
            Constant4L5.H5110,
            Constant4L5.H5174,
            Constant4L5.H5177,
                -> {
                readControllers.addAll(2, humControllers)
                readControllers.addAll(2, commonControllersExt)
            }

            Constant4L5.H5171 -> {
                readControllers.add(2, Controller4ComfortTemHum())
                readControllers.addAll(2, humControllers)
                readControllers.addAll(2, commonControllersExt)
            }

            Constant4L5.H5103 -> {
                readControllers.add(2, Controller4UploadFreq())
                readControllers.addAll(2, wifiVsControllers)
                readControllers.addAll(2, humControllers)
                readControllers.addAll(2, commonControllersExt)
            }

            Constant4L5.H5179 -> {
                readControllers.add(2, Controller4UploadFreq())
                readControllers.addAll(2, wifiVsControllers)
                if (addInfo.bleName.startsWith(GV5179) || addInfo.bleHardVersion == Constant4L5.H5179_BLE_HV_NEW) {
                    //新款H5179支持gid安全，gid安全须读取wifiMac
                    readControllers.add(2, Controller4WifiMac())
                }
                readControllers.addAll(2, humControllers)
                readControllers.addAll(2, commonControllersExt)
            }

            Constant4L5.H5108 -> {
                readControllers.addAll(2, commonControllersExt)
            }

            Constant4L5.H5111,
                -> {
                readControllers.add(2, Controller4Volume())
                readControllers.addAll(2, commonControllersExt)
            }

            Constant4L5.H5112,
                -> {
                readControllers.clear()
                readControllers.apply {
                    add(Controller4BleHv())
                    add(Controller4BleSv())
                    add(Controller4ProbeIcon())
                    add(Controller4TemWarningH5112())
                    add(Controller4TemCaliH5112())
                    addAll(humControllers)
                    add(Controller4DeviceIdH5112())
                    add(Controller4Volume())
                    add(Controller4SyncTime())
                    add(Controller4Battery())
                }
            }

            Constant4L5.B5178,
                -> {
                readControllers.addAll(2, humControllers)
                if (order == 0) {
                    readControllers.addAll(2, commonControllersExt)
                } else {
                    //从设备无须读取deviceId(事实上主从设备的deviceId是一样的，但为了区分，从设备的deviceId会再最后加上“_{order}”)、同步时间
                }
            }

            else -> {}
        }
        return readControllers.toTypedArray()
    }

    /**
     * 读取设备信息
     * @param hasBindGw 纯蓝牙设备是否绑定了类H5151网关(H5151/H5042/H5043/H5044)，绑定网关后告警信息以服务端的为准,故此时读取了告警相关信息也无须进行赋值
     * 备注：H5112在绑定网关(H5044)时是当作915通讯的设备看待，故它的数据展示始终以它设备自身的为准
     */
    fun readDeviceInfo(addInfo: AddInfo, fromAddDevice: Boolean = false, hasBindGw: Boolean = false, order: Int = 0, readFinish: () -> Unit) {
        SafeLog.i("xiaobing") { "ThConfig4ReadInfo--readDeviceInfo-->开始读/写设备相关信息..." }
        this.addInfo = addInfo
        this.fromAddDevice = fromAddDevice
        this.hasBindGw = hasBindGw && (addInfo.sku != Constant4L5.H5112)
        this.readFinishCallback = readFinish
        //开始读/写设备相关信息
        //主+从款设备须先设置通讯通道
        if (ThConfig4Support.isMainSubDevice(addInfo.goodsType, addInfo.sku)) {
            ThBle.getInstance.startControllers(Controller4ChangeOrder2Comm(order) {
                ThBle.getInstance.startControllers(*getReadControllers(fromAddDevice, order))
            })
        } else {
            ThBle.getInstance.startControllers(*getReadControllers(fromAddDevice, order))
        }
        //设置总超时
        mHandler.postDelayed({
            //超时,数据读取结束
            readFinishCallback?.invoke()
        }, READ_INFO_OVER_TIME)
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4CheckSecretKeyV0(event: EventSecretKey) {
        if (event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4CheckSecretKeyV0-->密钥写入成功..." }
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4BleHv(event: Event4BleHv) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4BleHv-->${event.hardVersion}" }
            addInfo.bleHardVersion = event.hardVersion
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4BleSv(event: Event4BleSv) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4BleSv-->${event.softVersion}" }
            addInfo.bleSoftVersion = event.softVersion
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4ProbeIcon(event: Event4ProbeIcon) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4ProbeIcon-->${JsonUtil.toJson(arrayOf(event.pb1IconIndex, event.pb2IconIndex))}" }
            addInfo.pb1IconIndex = event.pb1IconIndex
            addInfo.pb2IconIndex = event.pb2IconIndex
            //将探针默认名称也加上
            addInfo.probeName1 = ResUtil.getString(com.govee.ui.R.string.h5112_text_4_tem_hum_probe)
            addInfo.probeName2 = ResUtil.getString(com.govee.ui.R.string.h5112_text_4_tem_probe)
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4WifiHv(event: Event4WifiHv) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4WifiHv-->${event.wifiHardVersion}" }
            addInfo.wifiHardVersion = event.wifiHardVersion
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4WifiSv(event: Event4WifiSv) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4WifiSv-->${event.wifiSoftVersion}" }
            addInfo.wifiSoftVersion = event.wifiSoftVersion
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4WifiMac(event: Event4WifiMac) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4WifiMac-->${event.wifiMac}" }
            addInfo.wifiMac = event.wifiMac
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4UploadFreq(event: Event4UploadFreq) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4UploadFreq-->${event.minutes}" }
            addInfo.uploadRate = event.minutes
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4TemWarning(event: Event4TemWarning) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4TemWarning-->${JsonUtil.toJson(arrayOf(event.openWarning, event.minTem, event.maxTem, event.delayPushTime))}" }
            addInfo.temMin = event.minTem
            addInfo.temMax = event.maxTem
            addInfo.temWarning = event.openWarning
            addInfo.delayPushTime = event.delayPushTime
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4TemWarningH5112(event: Event4TemWarningH5112) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4TemWarningH5112-->${JsonUtil.toJson(event)}" }
            addInfo.temMin = event.minTem4Pb1
            addInfo.temMax = event.maxTem4Pb1
            addInfo.temWarning = event.openWarning4Pb1
            addInfo.delayPushTime = event.delayPushTime4Pb1
            addInfo.temMin2 = event.minTem4Pb2
            addInfo.temMax2 = event.maxTem4Pb2
            addInfo.temWarning2 = event.openWarning4Pb2
            addInfo.delayPushTime2 = event.delayPushTime4Pb2
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4TemCali(event: Event4TemCali) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4TemCali-->${event.temCali}" }
            addInfo.temCali = event.temCali
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4TemCaliH5112(event: Event4TemCaliH5112) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4TemCaliH5112-->${JsonUtil.toJson(event)}" }
            addInfo.temCali = event.temCali4Pb1
            addInfo.temCali2 = event.temCali4Pb2
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4HumWarning(event: Event4HumWarning) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4HumWarning-->${JsonUtil.toJson(arrayOf(event.openWarning, event.minHum, event.maxHum))}" }
            addInfo.humMin = event.minHum
            addInfo.humMax = event.maxHum
            addInfo.humWarning = event.openWarning
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4HumCali(event: Event4HumCali) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4HumCali-->${event.humCali}" }
            addInfo.humCali = event.humCali
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4DeviceId(event: Event4DeviceId) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4DeviceId-->${event.device}" }
            addInfo.device = event.device
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4SyncTime(event: Event4SyncTime) {
        if (event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4SyncTime-->完成时间同步" }
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4Volume(event: Event4Volume) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4Volume-->${event.volumeLevel}" }
            addInfo.muteLevel = event.volumeLevel
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4DeviceWarningTime(event: Event4DeviceWarningTime) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4DeviceWarningTime-->${event.open}, ${event.seconds}" }
            addInfo.deviceWarning = event.open
            addInfo.deviceWarningSeconds = event.seconds
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4ComfortTemHum(event: Event4ComfortTemHum) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4ComfortTemHum-->${JsonUtil.toJson(event.temRange)}, ${JsonUtil.toJson(event.humRange)}" }
            addInfo.comfortTemMin = event.temRange.firstOrNull()
            addInfo.comfortTemMax = event.temRange.lastOrNull()
            addInfo.comfortHumMin = event.humRange.firstOrNull()
            addInfo.comfortHumMax = event.humRange.lastOrNull()
            ThBle.getInstance.controllerEvent(event)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4Battery(event: Event4Battery) {
        if (!event.isWrite && event.isResult) {
            SafeLog.i("xiaobing") { "ThConfig4ReadInfo--onEvent4Battery-->${event.battery}" }
            addInfo.battery = event.battery
            ThBle.getInstance.controllerEvent(event)
            //数据读取完毕
            readFinishOperation()
        }
    }

    private val syncControllers by lazy {
        ConcurrentLinkedQueue<AbsControllerWithCallback>()
    }

    /**
     * 重新载入必要参数的次数
     */
    private var reLoadTime = 0

    /**
     * 数据读取完毕
     */
    private fun readFinishOperation() {
        globalLaunch(Dispatchers.IO) {
            //添加设备时，一些信息是必传的，故未读到的情况下尝试重新读取一定次数，以尽量规避这种情况的出现
            if (fromAddDevice && (reLoadTime < 3)) {
                reLoadTime++
                delay(300)
                val reLoadControllers = arrayListOf<AbsSingleController>()
                if (TextUtils.isEmpty(addInfo.bleHardVersion)) {
                    reLoadControllers.add(Controller4BleHv())
                }
                if (TextUtils.isEmpty(addInfo.bleSoftVersion)) {
                    reLoadControllers.add(Controller4BleSv())
                }
                if (TextUtils.isEmpty(addInfo.device)) {
                    when (addInfo.sku) {
                        Constant4L5.H5112 -> {
                            reLoadControllers.add(Controller4DeviceIdH5112())
                        }

                        else -> {
                            reLoadControllers.add(Controller4DeviceId())
                        }
                    }
                }
                if (reLoadControllers.isNotEmpty()) {
                    SafeLog.e("xiaobing") { "ThConfig4ReadInfo--readFinishOperation-->添加信息时有信息需要重新读取..." }
                    reLoadControllers.forEach { controller ->
                        SafeLog.e("xiaobing") { "ThConfig4ReadInfo--readFinishOperation-->重读指令：${controller::class.java.simpleName}" }
                    }
                    reLoadControllers.add(Controller4Battery())
                    ThBle.getInstance.startControllers(*reLoadControllers.toTypedArray())
                    return@globalLaunch
                }
            }
            if (hasBindGw) {
                //此时的告警信息以服务端的为准，如果设备端与服务端不一致可以尝试将服务端的同步给设备
                DeviceListConfig.read().getDeviceByKey(addInfo.sku, addInfo.device)?.deviceExt?.deviceSettings?.let { serviceSettingsStr ->
                    JsonUtil.fromJson(serviceSettingsStr, AddInfo::class.java)?.let { bindExt4Service ->
                        val isSameTemWarning = addInfo.temMin == bindExt4Service.temMin
                            && addInfo.temMax == bindExt4Service.temMax
                            && addInfo.temWarning == bindExt4Service.temWarning
                            && addInfo.delayPushTime == bindExt4Service.delayPushTime
                        val isSameHumWarning = addInfo.humMin == bindExt4Service.humMin
                            && addInfo.humMax == bindExt4Service.humMax
                            && addInfo.humWarning == bindExt4Service.humWarning
                        if (!isSameTemWarning) {
                            val supportSingleAlarm = Constant4L5.supportSingleAlarm(
                                addInfo.goodsType,
                                addInfo.sku,
                                addInfo.bleSoftVersion,
                                addInfo.bleHardVersion
                            )
                            syncControllers.add(
                                Controller4TemWarning(addInfo.temWarning, addInfo.temMin, addInfo.temMax, addInfo.delayPushTime, supportSingleAlarm) { result ->
                                    SafeLog.i("xiaobing") { "ThConfig4ReadInfo--readFinishOperation-->服务端的温度告警信息与设备端不一致,向设备端同步=->${result}" }
                                    syncWarnInfo()
                                })
                        }
                        if (!isSameHumWarning) {
                            syncControllers.add(Controller4HumWarning(addInfo.humWarning, addInfo.humMin, addInfo.humMax) { result ->
                                SafeLog.i("xiaobing") { "ThConfig4ReadInfo--readFinishOperation-->服务端的湿度告警信息与设备端不一致,向设备端同步=->${result}" }
                                syncWarnInfo()
                            })
                        }
                        syncWarnInfo()
                    } ?: syncWarnInfo()
                } ?: syncWarnInfo()
            } else {
                when (addInfo.sku) {
                    Constant4L5.H5112 -> {
                        //服务端的数据和设备端的不一致，以设备端的为准，需更新服务端数据
                        DeviceListConfig.read().getDeviceByKey(addInfo.sku, addInfo.device)?.deviceExt?.deviceSettings?.let { serviceSettingsStr ->
                            JsonUtil.fromJson(serviceSettingsStr, AddInfo::class.java)?.let { bindExt4Service ->
                                val settings = CommonSettings()
                                var needSync2Service = false
                                if (bindExt4Service.temWarning != addInfo.temWarning) {
                                    settings.temWarning = addInfo.temWarning
                                    needSync2Service = true
                                }
                                if (bindExt4Service.temMin != addInfo.temMin) {
                                    settings.temMin = addInfo.temMin
                                    needSync2Service = true
                                }
                                if (bindExt4Service.temMax != addInfo.temMax) {
                                    settings.temMax = addInfo.temMax
                                    needSync2Service = true
                                }
                                if (bindExt4Service.temCali != addInfo.temCali) {
                                    settings.temCali = addInfo.temCali
                                    needSync2Service = true
                                }
                                if (bindExt4Service.humWarning != addInfo.humWarning) {
                                    settings.humWarning = addInfo.humWarning
                                    needSync2Service = true
                                }
                                if (bindExt4Service.humMin != addInfo.humMin) {
                                    settings.humMin = addInfo.humMin
                                    needSync2Service = true
                                }
                                if (bindExt4Service.humMax != addInfo.humMax) {
                                    settings.humMax = addInfo.humMax
                                    needSync2Service = true
                                }
                                if (bindExt4Service.humCali != addInfo.humCali) {
                                    settings.humCali = addInfo.humCali
                                    needSync2Service = true
                                }
                                if (bindExt4Service.temWarning2 != addInfo.temWarning2) {
                                    settings.temWarning2 = addInfo.temWarning2
                                    needSync2Service = true
                                }
                                if (bindExt4Service.temMin2 != addInfo.temMin2) {
                                    settings.temMin2 = addInfo.temMin2
                                    needSync2Service = true
                                }
                                if (bindExt4Service.temMax2 != addInfo.temMax2) {
                                    settings.temMax2 = addInfo.temMax2
                                    needSync2Service = true
                                }
                                if (bindExt4Service.temCali2 != addInfo.temCali2) {
                                    settings.temCali2 = addInfo.temCali2
                                    needSync2Service = true
                                }
                                if (needSync2Service) {
                                    SafeLog.i("xiaobing") { "ThConfig4ReadInfo--readFinishOperation-->有数据需要同步给服务端，${JsonUtil.toJson(settings)}" }
                                    smwNetService.updateSetting4Callback(Req4SetSettingsNewV1().apply {
                                        this.sku = addInfo.sku
                                        this.device = addInfo.device
                                        this.settings = settings
                                    })
                                }
                            }
                        }
                    }

                    else -> {}
                }
                withContext(Dispatchers.Main) {
                    readFinishCallback?.invoke()
                }
            }
        }
    }

    /**
     * 同步告警信息
     */
    private fun syncWarnInfo() {
        syncControllers.poll()?.let {
            Vm4ThOpManager.instance()?.sendCommand(it)
        } ?: globalLaunch(Dispatchers.Main) {
            readFinishCallback?.invoke()
        }
    }

    fun release() {
        mHandler.removeCallbacksAndMessages(null)
        if (readFinishCallback != null) {
            readFinishCallback = null
        }
        EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
        SafeLog.i("xiaobing") { "ThConfig4ReadInfo--release-->释放资源" }
    }
}