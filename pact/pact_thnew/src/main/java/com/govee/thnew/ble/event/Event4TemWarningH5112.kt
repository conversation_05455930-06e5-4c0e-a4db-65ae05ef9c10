package com.govee.thnew.ble.event

import com.govee.base2home.Constant4L5
import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2025/5/12
 * @description 温湿度计（H5112）-->读写温度告警信息的事件
 */
class Event4TemWarningH5112 private constructor(result: <PERSON><PERSON><PERSON>, write: Boolean, commandType: Byte, proType: Byte) : AbsControllerEvent(result, write, commandType, proType, !write) {

    /**
     * 温度告警下限值*100--探针1
     */
    var minTem4Pb1 = 0

    /**
     * 温度告警上限值*100--探针1
     */
    var maxTem4Pb1 = 0
    var openWarning4Pb1 = false

    /**
     * 延迟告警分钟--探针1
     */
    var delayPushTime4Pb1 = 0

    /**
     * 温度告警下限值*100--探针2
     */
    var minTem4Pb2 = 0

    /**
     * 温度告警上限值*100--探针2
     */
    var maxTem4Pb2 = 0
    var openWarning4Pb2 = false

    /**
     * 延迟告警分钟--探针2
     */
    var delayPushTime4Pb2 = 0

    /**
     * 探针序号(只用于写操作)
     */
    var probeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(Event4TemWarningH5112(false, write, commandType, proType))
        }

        fun sendSuc4Read(
            write: Boolean, commandType: Byte, proType: Byte,
            openWarning4Pb1: Boolean, minTem4Pb1: Int, maxTem4Pb1: Int, delayPushTime4Pb1: Int = 0,
            openWarning4Pb2: Boolean, minTem4Pb2: Int, maxTem4Pb2: Int, delayPushTime4Pb2: Int = 0,
        ) {
            val event = Event4TemWarningH5112(true, write, commandType, proType)
            event.openWarning4Pb1 = openWarning4Pb1
            event.minTem4Pb1 = minTem4Pb1
            event.maxTem4Pb1 = maxTem4Pb1
            event.delayPushTime4Pb1 = delayPushTime4Pb1
            event.openWarning4Pb2 = openWarning4Pb2
            event.minTem4Pb2 = minTem4Pb2
            event.maxTem4Pb2 = maxTem4Pb2
            event.delayPushTime4Pb2 = delayPushTime4Pb2
            EventBus.getDefault().post(event)
        }
    }
}