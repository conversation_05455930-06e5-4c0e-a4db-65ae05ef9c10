package com.govee.thnew.ble.event

import com.govee.base2newth.AbsControllerEvent
import com.govee.base2newth.deviceitem.AbsModel4Th
import org.greenrobot.eventbus.EventBus


/**
 * <AUTHOR>
 * @date created on 2024/2/18
 * @description 温湿度计的心跳包处理事件
 */
class Event4Heart private constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) : AbsControllerEvent(result, write, commandType, proType, false) {
    var tem = AbsModel4Th.INVALID_INT_VALUE
    var hum = AbsModel4Th.INVALID_INT_VALUE
    var battery = AbsModel4Th.INVALID_INT_VALUE
    var pm25 = AbsModel4Th.INVALID_INT_VALUE

    //延时告警是否已经生效(H5108/H5111)
    var isTemAlarming = false

    companion object {
        fun sendSuc(write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte, tem: Int, hum: Int, battery: Int, pm25: Int = 100, isTemAlarming: Boolean = false) {
            val event = Event4Heart(true, write, commandType, proType)
            event.tem = tem
            event.hum = hum
            event.battery = battery
            event.pm25 = pm25
            event.isTemAlarming = isTemAlarming
            EventBus.getDefault().post(event)
        }
    }
}