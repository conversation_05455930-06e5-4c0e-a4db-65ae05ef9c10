package com.govee.thnew.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * @description 温湿度计-->温、湿度舒适度设置的回调事件
 * 备注：H5171使用
 */
class Event4ComfortTemHum private constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, !write) {
    var temRange = intArrayOf()
    var humRange = intArrayOf()

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(Event4ComfortTemHum(false, write, commandType, proType))
        }

        fun sendSuc(suc: Boolean, write: Boolean, commandType: Byte, proType: Byte, temRange: IntArray, humRange: IntArray) {
            val event = Event4ComfortTemHum(suc, write, commandType, proType)
            event.temRange = temRange
            event.humRange = humRange
            EventBus.getDefault().post(event)
        }
    }
}