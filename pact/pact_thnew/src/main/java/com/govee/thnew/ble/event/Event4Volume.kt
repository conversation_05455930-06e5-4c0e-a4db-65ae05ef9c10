package com.govee.thnew.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2024/12/27
 * @description 温湿度计-->从设备读写音量档位的事件
 */
class Event4Volume private constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON>olean, commandType: Byte, proType: Byte) : AbsControllerEvent(result, write, commandType, proType, !write) {
    var volumeLevel = 3

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(Event4Volume(false, write, commandType, proType))
        }

        fun sendSuc4Read(write: Boolean, commandType: Byte, proType: Byte, volumeLevel: Int) {
            val event = Event4Volume(true, write, commandType, proType)
            event.volumeLevel = volumeLevel
            EventBus.getDefault().post(event)
        }
    }
}