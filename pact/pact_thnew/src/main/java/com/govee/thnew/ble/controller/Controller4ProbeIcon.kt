package com.govee.thnew.ble.controller

import com.govee.base2home.Constant4L5
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4ProbeIcon

/**
 * <AUTHOR>
 * @date created on 2024/6/11
 * @description 温湿度计-->读写温度单位的Controller
 * 备注：H5112使用
 */
class Controller4ProbeIcon : AbsControllerWithCallback {

    /**
     * 探针序号
     */
    private var probeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM

    /**
     * 探针1的icon的序号代码
     */
    private var pb1IconIndex = 1

    /**
     * 探针2的icon的序号代码
     */
    private var pb2IconIndex = 1

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     *
     * @param probeIndex 探针序号
     * @param probeIconIndex 探针icon序号代码
     */
    constructor(probeIndex: Int, probeIconIndex: Int, writeCallBack: ((result: Boolean) -> Unit)) : super(writeCallBack) {
        this.probeIndex = probeIndex
        when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                this.pb1IconIndex = probeIconIndex
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                this.pb2IconIndex = probeIconIndex
            }

            else -> {}
        }
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_4_probe_icon
    }

    override fun translateWrite(): ByteArray {
        val pbIconIndex = when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                pb1IconIndex
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                pb2IconIndex
            }

            else -> {
                return byteArrayOf()
            }
        }
        return byteArrayOf(probeIndex.toByte(), pbIconIndex.toByte())
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val pb1IconIndex = validBytes[0].toInt()
        val pb2IconIndex = validBytes[1].toInt()
        Event4ProbeIcon.sendSuc4Read(isWrite, commandType, proType, pb1IconIndex, pb2IconIndex)
        return true
    }

    override fun fail() {
        super.fail()
        Event4ProbeIcon.sendFail(isWrite, commandType, proType)
    }
}