package com.govee.thnew.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * @description 温湿度计-->设置设备声音报警开关/时长的回调事件
 * 备注：H5171使用
 */
class Event4DeviceWarningTime private constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON>an, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, !write) {
    var open = false
    var seconds = 10

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(Event4DeviceWarningTime(false, write, commandType, proType))
        }

        fun sendSuc(suc: <PERSON>olean, write: Boolean, commandType: Byte, proType: Byte, open: Boolean, seconds: Int) {
            val event = Event4DeviceWarningTime(suc, write, commandType, proType)
            event.open = open
            event.seconds = seconds
            EventBus.getDefault().post(event)
        }
    }
}