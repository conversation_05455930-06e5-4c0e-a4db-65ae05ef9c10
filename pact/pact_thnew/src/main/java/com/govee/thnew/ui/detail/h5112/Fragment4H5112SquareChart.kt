package com.govee.thnew.ui.detail.h5112

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import android.widget.PopupWindow
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.temUnit.TemperatureUnitType
import com.govee.base2home.util.IntroUtils
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.setVisibility
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.ChartVisibleConfig
import com.govee.base2newth.chart.DataGroup
import com.govee.base2newth.chart.IntervalType
import com.govee.base2newth.chart.square.ChartValueType
import com.govee.base2newth.chart.square.newsquare.SquareChartController4New
import com.govee.base2newth.data.ExtV1
import com.govee.base2newth.deviceitem.LastData
import com.govee.base2newth.other.Config4DeviceChartOrder
import com.govee.base2newth.other.Config4LastThValue
import com.govee.base2newth.other.SyncTemUnitUtil
import com.govee.base2newth.ui.adapter.TextListAdapter
import com.govee.base2newth.ui.viewmodel.SquareViewModel
import com.govee.mvvm.BaseFragment
import com.govee.mvvm.base.viewmodel.BaseViewModel
import com.govee.thnew.R
import com.govee.thnew.ThNewConstant
import com.govee.thnew.add.AddInfo
import com.govee.thnew.config.ThConfig4Detail
import com.govee.thnew.databinding.ThnewFragment4H5112SquareChartBinding
import com.govee.thnew.databinding.ThnewSquareContainer4DewPointBinding
import com.govee.thnew.databinding.ThnewSquareContainer4HumBinding
import com.govee.thnew.databinding.ThnewSquareContainer4TemBinding
import com.govee.thnew.databinding.ThnewSquareContainer4VpdBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.ui.dialog.TimeDialogV13
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.ResUtil
import com.tk.mediapicker.utils.DensityUtil
import java.util.concurrent.ConcurrentHashMap

/**
 * <AUTHOR>
 * @date created on 2025/05/06
 * @description H5112柱状图表页的Fragment
 */
class Fragment4H5112SquareChart :
    BaseFragment<BaseViewModel, ThnewFragment4H5112SquareChartBinding>() {

    companion object {
        fun newInstance(pbIndex: Int): Fragment4H5112SquareChart {
            val args = Bundle().apply {
                putInt(Constant4L5.KEY_4_TH_PROBE_INDEX, pbIndex)
            }
            val fragment = Fragment4H5112SquareChart()
            fragment.arguments = args
            return fragment
        }

        private const val MENU_TYPE_TIME = 1 //type：时间筛选
        private const val MENU_TYPE_VALUE = 2//type: 值筛选
    }

    private var probeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM
    private val refreshFlagMap = ConcurrentHashMap<String, Boolean>()

    /**
     * 实时信息
     */
    private var refreshTime = 0L
    private var realTem4Pb1 = 0
    private var realHum = 0
    private var realTem4Pb2 = 0

    private val squareChartVm by lazy {
        ViewModelProvider(this)[SquareViewModel::class.java]
    }

    //dataGroup不为null表明处于默认标签下
    private var dataGroup: DataGroup? = null
    private lateinit var chartController: SquareChartController4New

    /**
     * 图表控件相关
     */
    private val temBinding by lazy {
        ThnewSquareContainer4TemBinding.inflate(layoutInflater)
    }
    private val humBinding by lazy {
        ThnewSquareContainer4HumBinding.inflate(layoutInflater)
    }
    private val dpBinding by lazy {
        ThnewSquareContainer4DewPointBinding.inflate(layoutInflater)
    }
    private val vpdBinding by lazy {
        ThnewSquareContainer4VpdBinding.inflate(layoutInflater)
    }

    /**
     * 该设备自身的实时信息
     */
    private fun getDeviceInfo(): AddInfo? {
        return Vm4ThOpManager.instance()?.getDeviceInfo()
    }

    override fun initView(savedInstanceState: Bundle?) {
        probeIndex = arguments?.getInt(Constant4L5.KEY_4_TH_PROBE_INDEX, Constant4L5.PROBE_INDEX_4_TEM_HUM) ?: Constant4L5.PROBE_INDEX_4_TEM_HUM
        getDeviceInfo()?.let { bindExt ->
            squareChartVm.apply {
                ext = ExtV1(bindExt.goodsType, bindExt.sku, bindExt.device, bindExt.deviceName, bindExt.address, bindExt.wifiMac, bindExt.temCali, bindExt.humCali).apply {
                    bindExt.temCali2
                }
            }
        }
        initUi()
        initOpClick()
        initObserver()
        loadData(true)
    }

    private fun initUi() {
        mDatabind.let { vb ->
            getDeviceInfo()?.let { bindExt ->
                //实时信息展示控件初始化配置
                vb.thRealInfo4H5112Sc.init(bindExt.goodsType, bindExt.sku, bindExt.device, probeIndex)
                updateTemUnit(TemUnitConfig.read().isTemUnitFah(bindExt.sku))
            }
            vb.tvMenuTime.text = squareChartVm.selectMenuTimeStr
            vb.tvMenuValue.text = squareChartVm.selectMenuValueStr
            updateShowTime()
        }
        getDeviceInfo()?.let {
            updateTemUnit(TemUnitConfig.read().isTemUnitFah(it.sku))
        }
        //初始化图表显示
        initChart()
        refreshDewPView()
        refreshVpdView()
        //图表排序
        changeChartOrder()
    }

    @SuppressLint("RtlHardcoded")
    private fun initOpClick() {
        mDatabind.let { vb ->
            vb.tvMenuTime.clickDelay {
                this.activity?.let {
                    showMenu(MENU_TYPE_TIME)?.showAsDropDown(vb.tvMenuTime, -DensityUtil.dp2px(it, 12f), 0, Gravity.LEFT)
                }
            }
            vb.tvMenuValue.clickDelay {
                this.activity?.let {
                    showMenu(MENU_TYPE_VALUE)?.showAsDropDown(vb.tvMenuValue, -DensityUtil.dp2px(it, 12f), 0, Gravity.LEFT)
                }
            }
            vb.tvTimeRange.clickDelay {
                showTimeDialog()
            }
        }
        temBinding.temUnitType.clickDelay {
            changeTempUnit()
        }
        dpBinding.ivDpSwitch.clickDelay {
            getDeviceInfo()?.let {
                val selected = !dpBinding.ivDpSwitch.isSelected
                ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_DEW_P, selected)
                refreshDewPView()
            }
        }
        dpBinding.ivDpSwitch1.clickDelay {
            getDeviceInfo()?.let {
                val selected = !dpBinding.ivDpSwitch.isSelected
                ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_DEW_P, selected)
                refreshDewPView()
            }
        }
        dpBinding.ivDewPointIntroIcon.clickDelay {
            getDeviceInfo()?.let { bindExt ->
                this.activity?.let {
                    IntroUtils.showDewPointIntro(it, bindExt.sku)
                }
            }
        }
        dpBinding.ivDewPointIntroIcon1.clickDelay {
            getDeviceInfo()?.let { bindExt ->
                this.activity?.let {
                    IntroUtils.showDewPointIntro(it, bindExt.sku)
                }
            }
        }
        vpdBinding.ivVpdSwitch.clickDelay {
            getDeviceInfo()?.let {
                val selected = !vpdBinding.ivVpdSwitch.isSelected
                ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_VPD, selected)
                refreshVpdView()
            }
        }
        vpdBinding.ivVpdSwitch1.clickDelay {
            getDeviceInfo()?.let {
                val selected = !vpdBinding.ivVpdSwitch.isSelected
                ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_VPD, selected)
                refreshVpdView()
            }
        }
        vpdBinding.ivVpdIntroIcon.clickDelay {
            getDeviceInfo()?.let { bindExt ->
                this.activity?.let {
                    IntroUtils.showVpdIntro(it, bindExt.sku)
                }
            }
        }
        vpdBinding.ivVpdIntroIcon1.clickDelay {
            getDeviceInfo()?.let { bindExt ->
                this.activity?.let {
                    IntroUtils.showVpdIntro(it, bindExt.sku)
                }
            }
        }
    }

    private fun initObserver() {
        Vm4ThOpManager.instance()?.let { vm ->
            vm.ld4RealTemHum.observe(this) {
                if (isResumed) {
                    refreshRealInfo(it)
                } else {
                    refreshFlagMap[ThNewConstant.REFRESH_FLAG_4_REAL_INFO] = true
                }
            }
            vm.ld4ChangeTemUnit.observe(this) {
                if (it) {
                    getDeviceInfo()?.let { bindExt ->
                        val fahOpen = TemUnitConfig.read().isTemUnitFah(bindExt.sku)
                        updateTemUnit(fahOpen)
                    }
                }
            }
            vm.loadThcdManager.ld4ShowChartNew.observe(this) {
                if (isResumed) {
                    loadData(false)
                } else {
                    refreshFlagMap[ThNewConstant.REFRESH_FLAG_4_CHART_INFO] = true
                }
            }
            //设置页更新告警和校准时会影响到图表展示
            vm.ld4UpdateDeviceInfo.observe(this) {
                when (it.second) {
                    Vm4ThOpManager.UPDATE_4_CALIBRATION,
                        -> {
                        //更新校准等相关信息并刷新图表
                        getDeviceInfo()?.let { bindExt ->
                            squareChartVm.ext = ExtV1(bindExt.goodsType, bindExt.sku, bindExt.device, bindExt.deviceName, bindExt.address, bindExt.wifiMac, bindExt.temCali, bindExt.humCali).apply {
                                temCali2 = bindExt.temCali2 ?: 0
                            }
                        }
                        loadData(false)
                    }

                    else -> {}
                }
            }
        }
        squareChartVm.thDataLiveData.observe(this) {
            chartController.updateLookupData(squareChartVm.startTimeStamp, squareChartVm.endTimeStamp, it)
        }
    }

    /**
     * 刷新事实信息
     */
    private fun refreshRealInfo(realInfo: Pair<Triple<Int, Int, Int>, Long>) {
        val thpInfo = realInfo.first
        refreshTime = realInfo.second
        realTem4Pb1 = thpInfo.first
        realHum = thpInfo.second
        realTem4Pb2 = thpInfo.third
        when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                //更新实时数据
                mDatabind.thRealInfo4H5112Sc.updateRealInfo(refreshTime, realTem4Pb1, realHum, 100)
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                //更新实时数据
                mDatabind.thRealInfo4H5112Sc.updateRealInfo(refreshTime, realTem4Pb2, 6000, 100)
            }

            else -> {}
        }
    }

    /**
     * 切换温度单位
     */
    private fun changeTempUnit() {
        getDeviceInfo()?.let {
            val fahOpen = !TemUnitConfig.read().isTemUnitFah(it.sku)
            TemUnitConfig.read().setTemUnit(it.sku, if (fahOpen) TemperatureUnitType.Fahrenheit else TemperatureUnitType.Celsius)
            //同步温度单位到服务端
            SyncTemUnitUtil.syncTemUnit(Transactions().createTransaction(), it.sku, fahOpen)
            //通知详情页
            Vm4ThOpManager.instance()?.ld4ChangeTemUnit?.value = true
        }
    }

    /**
     * 弹出时间范围筛选弹窗
     */
    private fun showTimeDialog() {
        TimeDialogV13.createDialog(
            this.context,
            ResUtil.getString(com.govee.ui.R.string.time_slot_introduction),
            ResUtil.getString(com.govee.ui.R.string.setting_time_introduction),
            squareChartVm.startTimeStamp,
            squareChartVm.endTimeStamp
        ) { startTime, endTime ->
            //统计
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.select_overview_duration, ParamFixedValue.times);
            //数据处理
            squareChartVm.startTimeStamp = startTime
            squareChartVm.endTimeStamp = endTime
            updateShowTime()
            loadData(true)
        }.setEventKey(TAG).show()
    }

    /**
     *更新展示时间
     */
    private fun updateShowTime() {
        mDatabind.tvTimeRange.text = ResUtil.getStringFormat(
            com.govee.ui.R.string.time_range,
            TimeFormatM.getInstance().formatTimeToHMYMD(squareChartVm.startTimeStamp),
            TimeFormatM.getInstance().formatTimeToHMYMD(squareChartVm.endTimeStamp)
        )
    }

    /**
     * 时间和间隔筛选条件PopupWindow
     */
    private fun showMenu(type: Int): PopupWindow? {
        activity?.run {
            val lp = window.attributes
            lp.alpha = 0.7f
            window.addFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
            window.attributes = lp
            val view = View.inflate(this, com.govee.base2newth.R.layout.layout_dialog_list_text, null)
            val popWindow = PopupWindow(this)
            popWindow.contentView = view
            val lv: RecyclerView = view.findViewById(com.govee.base2newth.R.id.lv)
            val root: View = view.findViewById(com.govee.base2newth.R.id.root)
            root.setOnClickListener { popWindow.dismiss() }

            popWindow.setBackgroundDrawable(null)
            popWindow.isOutsideTouchable = true
            popWindow.isTouchable = true
            popWindow.isFocusable = true
            val adapter = TextListAdapter(
                com.govee.base2newth.R.layout.item_popup_text,
                if (type == MENU_TYPE_TIME) squareChartVm.menuTimeList.toMutableList() else squareChartVm.menuValueList.toMutableList()
            )
            adapter.selectText = if (type == MENU_TYPE_TIME) squareChartVm.selectMenuTimeStr else squareChartVm.selectMenuValueStr

            popWindow.setOnDismissListener {
                val lp1 = window.attributes
                lp1.alpha = 1f
                window.clearFlags(WindowManager.LayoutParams.FLAG_DIM_BEHIND)
                window.attributes = lp1
            }

            adapter.setOnItemClickListener { _, _, position ->
                popWindow.dismiss()
                if (type == MENU_TYPE_TIME) {
                    val time = squareChartVm.menuTimeList[position]
                    if (time == squareChartVm.selectMenuTimeStr) return@setOnItemClickListener
                    squareChartVm.selectMenuTimeStr = time
                    mDatabind.tvMenuTime.text = squareChartVm.selectMenuTimeStr
                    showLoading()
                } else {
                    val value = squareChartVm.menuValueList[position]
                    if (value == squareChartVm.selectMenuValueStr) return@setOnItemClickListener
                    squareChartVm.selectMenuValueStr = value
                    mDatabind.tvMenuValue.text = squareChartVm.selectMenuValueStr
                }
                updateMenuMode()
            }

            lv.layoutManager = LinearLayoutManager(this)
            lv.adapter = adapter

            return popWindow
        }
        return null
    }

    private fun initChart() {
        dataGroup = DataGroup.day
        chartController = SquareChartController4New(7)
        chartController.setChartListener(object : SquareChartController4New.ChartListener {
            override fun beScale() {
                dataGroup = null
            }

            override fun timeChange(startTimeStamp: Long, endTimeStamp: Long, intervalType: IntervalType) {}

            override fun invalidateFinish(isLookupNoData: Boolean) {
                val visibility = if (isLookupNoData) View.GONE else View.VISIBLE
                temBinding.temTendencyChart.setEmptyUI(visibility)
                humBinding.humTendencyChart.setEmptyUI(visibility)
                dpBinding.dpTendencyChart.setEmptyUI(visibility)
                vpdBinding.vpdTendencyChart.setEmptyUI(visibility)
                dismissLoading()
            }
        })
        temBinding.temTendencyChart.setChart(chartController)
        humBinding.humTendencyChart.setChart(chartController)
        dpBinding.dpTendencyChart.setChart(chartController)
        vpdBinding.vpdTendencyChart.setChart(chartController)
        //设置图标页面无数据状态下的最大最小值
        getDeviceInfo()?.let { bindExt ->
            val defTemRange = ThConfig4Detail.getTemRange(bindExt.goodsType, bindExt.sku, bindExt.bleSoftVersion, bindExt.bleHardVersion)
            temBinding.temTendencyChart.getSquareChart().setDefMaxValueAndMinValue(defTemRange.first * 1.0f, defTemRange.second * 1.0f)
        }
        humBinding.humTendencyChart.getSquareChart().setDefMaxValueAndMinValue(ThConsV1.HUM_MIN_VALUE * 1.0f, ThConsV1.HUM_MAX_VALUE * 1.0f)
        dpBinding.dpTendencyChart.getSquareChart().setDefMaxValueAndMinValue(ThConsV1.DEW_POINT_MIN_VALUE, ThConsV1.DEW_POINT_MAX_VALUE)
        vpdBinding.vpdTendencyChart.getSquareChart().setDefMaxValueAndMinValue(ThConsV1.VPD_MIN_VALUE, ThConsV1.VPD_MAX_VALUE)
        //设置图标页面显示小数点位数
        getDeviceInfo()?.let { bindExt ->
            val pointNumArr = ThConfig4Detail.getChartValueDecimalDigits(bindExt.goodsType, bindExt.sku)
            temBinding.temTendencyChart.getSquareChart().setPointValue(pointNumArr[1])
            humBinding.humTendencyChart.getSquareChart().setPointValue(pointNumArr[2])
            dpBinding.dpTendencyChart.getSquareChart().setPointValue(pointNumArr[3])
            vpdBinding.vpdTendencyChart.getSquareChart().setPointValue(pointNumArr[4])
            chartController.intervalType = IntervalType.hour_1_hour
        }
    }

    /**
     * 更新筛选条件
     */
    private fun updateMenuMode() {
        val timeType = when (squareChartVm.selectMenuTimeStr) {
            squareChartVm.menuTimeList[1] -> IntervalType.week_1_day
            squareChartVm.menuTimeList[2] -> IntervalType.year_1_month
            else -> {
                IntervalType.hour_1_hour
            }
        }
        val valueType = when (squareChartVm.selectMenuValueStr) {
            squareChartVm.menuValueList[1] -> ChartValueType.MIN
            squareChartVm.menuValueList[2] -> ChartValueType.AVERAGE
            else -> {
                ChartValueType.MAX
            }
        }
        chartController.setType(timeType, valueType)
    }

    /**
     * 刷新温度按钮状态
     */
    private fun updateTemUnit(fahOpen: Boolean) {
        temBinding.temUnitType.setImageDrawable(ResUtil.getDrawable(if (fahOpen) com.govee.ui.R.mipmap.new_sensor_setting_switch_fahrenheit else com.govee.ui.R.mipmap.new_sensor_setting_switch_celsius))
        temBinding.temTendencyChart.getSquareChart().setChangeValueShow(fahOpen, if (fahOpen) getString(com.govee.ui.R.string.tem_unit_fah) else getString(com.govee.ui.R.string.tem_unit_cel))
        dpBinding.dpTendencyChart.getSquareChart().setChangeValueShow(fahOpen, if (fahOpen) getString(com.govee.ui.R.string.tem_unit_fah) else getString(com.govee.ui.R.string.tem_unit_cel))
    }

    /**
     * 刷新露点
     */
    private fun refreshDewPView() {
        getDeviceInfo()?.let {
            val selected = ChartVisibleConfig.read().getVisible(it.sku, ChartVisibleConfig.CHART_TYPE_DEW_P)
            dpBinding.gpShowContent4Dp.setVisibility(selected)
            dpBinding.gpClose4Dp.setVisibility(!selected)
            dpBinding.ivDpSwitch.isSelected = selected
            dpBinding.ivDpSwitch1.isSelected = false
        }
    }

    /**
     * 刷新VPD
     */
    private fun refreshVpdView() {
        getDeviceInfo()?.let {
            val selected = ChartVisibleConfig.read().getVisible(it.sku, ChartVisibleConfig.CHART_TYPE_VPD)
            vpdBinding.gpShowContent4Vpd.setVisibility(selected)
            vpdBinding.gpClose4Vpd.setVisibility(!selected)
            vpdBinding.ivVpdSwitch.isSelected = selected
            vpdBinding.ivVpdSwitch1.isSelected = false
        }
    }

    private fun loadData(needLoading: Boolean) {
        getDeviceInfo()?.let {
            if (needLoading) {
                showLoading()
            }
            squareChartVm.queryAllData4H5112(
                it.sku,
                it.device,
                squareChartVm.startTimeStamp,
                squareChartVm.endTimeStamp,
                probeIndex
            )
        }
    }

    /**
     * 根据图表排序展示图表
     *
     * @param chartOrder 备注：A->pm2.5，B->温度，C->湿度，D->露点，E->vpd
     */
    private fun changeChartOrder(chartOrder: ArrayList<String>? = null) {
        getDeviceInfo()?.let { bindExt ->
            var usedChartOrder = chartOrder
            if (chartOrder.isNullOrEmpty()) {
                usedChartOrder = Config4DeviceChartOrder.getConfig().getChartOrder(bindExt.sku, bindExt.device) ?: arrayListOf(ThConsV1.PM25, ThConsV1.TEM, ThConsV1.HUM, ThConsV1.DP, ThConsV1.VPD)
            }
            mDatabind.let {
                it.flContent1Container4H5112Sc.removeAllViews()
                it.flContent2Container4H5112Sc.removeAllViews()
                it.flContent3Container4H5112Sc.removeAllViews()
                it.flContent4Container4H5112Sc.removeAllViews()
                it.flContent5Container4H5112Sc.removeAllViews()
            }
            usedChartOrder?.let {
                fillToContainer(0, usedChartOrder[0])
                fillToContainer(1, usedChartOrder[1])
                fillToContainer(2, usedChartOrder[2])
                fillToContainer(3, usedChartOrder[3])
                fillToContainer(4, usedChartOrder[4])
            }
        }
    }

    private fun fillToContainer(order: Int, type: String) {
        mDatabind.let { vb ->
            //排序填充
            val container = when (order) {
                0 -> vb.flContent1Container4H5112Sc
                1 -> vb.flContent2Container4H5112Sc
                2 -> vb.flContent3Container4H5112Sc
                3 -> vb.flContent4Container4H5112Sc
                else -> {
                    vb.flContent5Container4H5112Sc
                }
            }
            val supportHum = probeIndex == Constant4L5.PROBE_INDEX_4_TEM_HUM
            when (type) {
                ThConsV1.TEM -> {
                    container.setVisibility(true)
                    container.addView(temBinding.root)
                }

                ThConsV1.HUM -> {
                    container.setVisibility(supportHum)
                    if (supportHum) {
                        container.addView(humBinding.root)
                    }
                }

                ThConsV1.DP -> {
                    container.setVisibility(supportHum)
                    if (supportHum) {
                        container.addView(dpBinding.root)
                    }
                }

                ThConsV1.VPD -> {
                    container.setVisibility(supportHum)
                    if (supportHum) {
                        container.addView(vpdBinding.root)
                    }
                }

                else -> {}
            }
            if (vb.flContent1Container4H5112Sc == container && container.visibility != View.VISIBLE) {
                val lp: LinearLayoutCompat.LayoutParams = vb.flContent2Container4H5112Sc.layoutParams as LinearLayoutCompat.LayoutParams
                lp.topMargin = 0
                vb.flContent2Container4H5112Sc.layoutParams = lp
            }
        }
    }

    override fun onPause() {
        getDeviceInfo()?.let {
            //保存最新的实时温湿度值
            Config4LastThValue.getConfig().updateLastTh(it.sku, it.device, getLastData())
        }
        super.onPause()
    }

    override fun onResume() {
        super.onResume()
        SafeLog.i("xiaobing") { "Fragment4H5112SquareChart--onResume-->${probeIndex}" }
        Vm4ThOpManager.instance()?.let { vm ->
            if (refreshFlagMap[ThNewConstant.REFRESH_FLAG_4_REAL_INFO].isTrue()) {
                refreshFlagMap.remove(ThNewConstant.REFRESH_FLAG_4_REAL_INFO)
                vm.ld4RealTemHum.value?.let {
                    refreshRealInfo(it)
                }
            }
            if (refreshFlagMap[ThNewConstant.REFRESH_FLAG_4_CHART_INFO].isTrue()) {
                refreshFlagMap.remove(ThNewConstant.REFRESH_FLAG_4_CHART_INFO)
                vm.loadThcdManager.ld4ShowChartNew.value?.let {
                    loadData(false)
                }
            }
        }
    }

    private fun getLastData(): LastData {
        val lastData = LastData()
        lastData.tem = realTem4Pb1
        lastData.hum = realHum
        lastData.pm = realTem4Pb2
        lastData.lastTime = refreshTime
        return lastData
    }

    override fun onDestroy() {
        super.onDestroy()
        chartController.destroy()
    }

    override fun layoutId(): Int {
        return R.layout.thnew_fragment_4_h5112_square_chart
    }
}