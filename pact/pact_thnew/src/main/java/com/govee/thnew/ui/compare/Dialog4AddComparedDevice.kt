package com.govee.thnew.ui.compare

import android.annotation.SuppressLint
import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.govee.base2home.common.BaseRvAdapter
import com.govee.base2home.main.AbsDevice
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.util.ClickUtil
import com.govee.base2newth.ThConsV1
import com.govee.thnew.add.AddInfo
import com.govee.thnew.config.ThConfig4Compare
import com.govee.thnew.databinding.ThnewDialog4SelectCompareDeviceBinding
import com.ihoment.base2app.dialog.BaseDialog
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.JsonUtil

/**
 * <AUTHOR>
 * @date created on 2024/4/8
 * @description 温湿度计=->选择添加对比数据的设备列表弹窗
 */
class Dialog4AddComparedDevice private constructor(context: Context) : BaseDialog(context), View.OnClickListener {

    companion object {
        fun createDialog(context: Context): Dialog4AddComparedDevice {
            return Dialog4AddComparedDevice(context)
        }
    }

    private lateinit var binding: ThnewDialog4SelectCompareDeviceBinding
    private var hasSelDeviceNum = 1
    private lateinit var selectedCallback: ((selectedDevice: ChartDevice) -> Unit)

    private val selectChartDeviceAdapter by lazy {
        Adapter4CanComparedDevice(context, arrayListOf())
    }

    init {
        changeDialogOutside(false)
        immersionMode()
        updatePosition(0, 0, Gravity.BOTTOM)
        binding.rvList4SelCompareDevice.layoutManager = LinearLayoutManager(context)
        binding.rvList4SelCompareDevice.adapter = selectChartDeviceAdapter.also {
            it.setOnRvItemClickListener(object : BaseRvAdapter.OnRvItemClickListener {
                @SuppressLint("NotifyDataSetChanged")
                override fun onItemClick(v: View, position: Int) {
                    val canSelectedDevices = selectChartDeviceAdapter.getChartData()
                    if (canSelectedDevices.lastIndex < position) {
                        return
                    }
                    //点击item
                    canSelectedDevices[position].let {
                        selectedCallback.invoke(ChartDevice().apply {
                            goodsType = it.goodsType
                            sku = it.sku
                            device = it.device
                            deviceName = it.deviceName
                            bleAddress = JsonUtil.fromJson(it.deviceExt?.deviceSettings ?: "", AddInfo::class.java)?.address ?: ""
                            lineIndex = hasSelDeviceNum + 1
                            loadDataStatus = ChartDevice.HAS_DATA
                        })
                        //隐藏弹窗
                        hide()
                    }
                }
            })
        }
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getLayoutView(): View {
        binding = ThnewDialog4SelectCompareDeviceBinding.inflate(LayoutInflater.from(context))
        binding.ivCancelBtn4SelCompareDevice.setOnClickListener(this)
        return binding.root
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth()
    }

    /**
     * 显示弹窗
     */
    fun show(curSelectedDevices: ArrayList<ChartDevice>, selectedCallback: ((selectedDevice: ChartDevice) -> Unit)) {
        this.hasSelDeviceNum = curSelectedDevices.size - 1
        this.selectedCallback = selectedCallback
        DeviceListConfig.read().allDeviceList?.let { allDevices ->
            val canAddDevices = arrayListOf<AbsDevice>()
            first@
            for (absDevice in allDevices) {
                if (!ThConfig4Compare.supportComparedTh.contains(absDevice.sku)) {
                    continue@first
                }
                ThConsV1.getCustomDevice(absDevice).let {
                    second@
                    for (cusDevice in it) {
                        third@
                        for (selectedChartDevice in curSelectedDevices) {
                            if (cusDevice.key == selectedChartDevice.key) {
                                continue@second
                            }
                        }
                        canAddDevices.add(cusDevice)
                    }
                }
            }
            //更新列表
            selectChartDeviceAdapter.updateList(canAddDevices)
            super.show()
        }
    }

    override fun onClick(v: View) {
        if (ClickUtil.getInstance.clickQuick()) {
            return
        }
        when (v) {
            binding.ivCancelBtn4SelCompareDevice -> {
                hide()
            }

            else -> {}
        }
    }
}