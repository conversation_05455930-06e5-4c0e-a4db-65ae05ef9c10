package com.govee.thnew.ble.controller

import com.govee.base2home.Constant4L5
import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4TemCaliH5112

/**
 * <AUTHOR>
 * @date created on 2025/5/12
 * @description 温湿度计(H5112)-->读写温度校准值的Controller
 */
class Controller4TemCaliH5112 : AbsControllerWithCallback {
    /**
     * 温度校准值*100
     */
    private var temCali = 0

    /**
     * 探针序号(只用于写操作)
     */
    private var probeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     *
     * @param temCali
     */
    constructor(temCali: Int, probeIndex: Int, writeCallBack: ((result: Boolean) -> Unit)) : super(writeCallBack) {
        this.temCali = temCali
        this.probeIndex = probeIndex
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_tem_cali
    }

    override fun translateWrite(): ByteArray {
        val caliTemBytes = BleUtil.getSignedBytesFor2(temCali, false)
        return byteArrayOf(caliTemBytes[0], caliTemBytes[1], probeIndex.toByte())
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val temCali4Pb1 = BleUtil.getSignedShort(validBytes[1], validBytes[0]).toInt()
        val temCali4Pb2 = BleUtil.getSignedShort(validBytes[3], validBytes[2]).toInt()
        Event4TemCaliH5112.sendSuc(isWrite, commandType, proType, temCali4Pb1, temCali4Pb2)
        return true
    }

    override fun fail() {
        super.fail()
        Event4TemCaliH5112.sendFail(isWrite, commandType, proType)
    }
}