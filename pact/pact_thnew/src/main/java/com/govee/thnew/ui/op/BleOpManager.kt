package com.govee.thnew.ui.op

import android.os.Handler
import android.os.Looper
import com.govee.base2home.Constant4L5
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.util.CaughtRunnable
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.data.THMemoryUtil
import com.govee.ble.BleController
import com.govee.ble.event.BTStatusEvent
import com.govee.ble.event.EventBleConnect
import com.govee.thnew.ble.ThBle
import com.govee.thnew.config.ThConfig4ReadInfo
import com.govee.thnew.ui.Vm4ThOpManager
import com.ihoment.base2app.infra.SafeLog
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2024/3/23
 * @description 温湿度计-->蓝牙操作管理类
 */
class BleOpManager {

    companion object {
        //蓝牙连接标识常量
        const val BLE_UNABLE = 0
        const val BLE_CONNECTING = 1
        const val BLE_CONNECTED_SUC = 2
        const val BLE_DISCONNECT = 3
        const val BLE_READ_INFO_FINISH = 4

        //蓝牙连接超时时间
        private const val BLE_CONNECT_OVER_TIME = 30 * 1000L
    }

    init {
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
    }

    private var goodsType = GoodsType.GOODES_TYPE_NO_SUPPORT
    private var sku = ""
    private var device = ""
    private var bleAddress = ""

    var needReadInfo = false
        private set

    @Volatile
    var curBleStatus = BLE_DISCONNECT
        private set

    private var statusCallBack: ((status: Int, hasWarnInfoChanged: Boolean) -> Unit)? = null

    private val mHandler by lazy {
        Handler(Looper.getMainLooper())
    }

    /**
     * 初始化方法
     */
    fun start(goodsType: Int, sku: String, device: String, bleAddress: String, needReadInfo: Boolean, statusCallBack: ((status: Int, hasWarnInfoChanged: Boolean) -> Unit)) {
        this.goodsType = goodsType
        this.sku = sku
        this.device = device
        this.bleAddress = bleAddress
        this.needReadInfo = needReadInfo
        this.statusCallBack = statusCallBack
        //蓝牙操作类注册、连接蓝牙
        ThBle.getInstance.inStep4Details(true)
        if (BleController.getInstance().isBlueToothOpen) {
            if (BleController.getInstance().isConnected && BleController.getInstance().connectedBleAddress == bleAddress) {
                //蓝牙已连接
                curBleStatus = BLE_CONNECTED_SUC
                this.statusCallBack?.invoke(BLE_CONNECTED_SUC, false)
                //蓝牙连接成功读取设备信息
                operationAfterBleConnSuc()
            } else {
                toConnectBle(needReadInfo)
            }
        } else {
            //手机蓝牙未打开
            curBleStatus = BLE_UNABLE
            this.statusCallBack?.invoke(BLE_UNABLE, false)
        }
    }

    /**
     * 连接蓝牙
     * 备注：主设备自用
     */
    fun toConnectBle(needReadInfo: Boolean) {
        SafeLog.i("xiaobing") { "BleOpManager--toConnectBle-->连接蓝牙,key=->${sku}_${device},address=->${bleAddress}" }
        this.needReadInfo = needReadInfo
        //连接操作
        ThBle.getInstance.thHeartComm.setDeviceInfo(goodsType, sku)
        ThBle.getInstance.connectBle(bleAddress)
        THMemoryUtil.getInstance().curDeviceBleAddress = bleAddress
        curBleStatus = BLE_CONNECTING
        statusCallBack?.invoke(BLE_CONNECTING, false)
        //设置连接超时
        mHandler.postDelayed(bleConnectOvRunnable, BLE_CONNECT_OVER_TIME)
    }

    /**
     * 蓝牙连接超时的Runnable
     */
    private val bleConnectOvRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                BleController.getInstance().disconnectBleAndNotify()
                //移除自身
                mHandler.removeCallbacks(this)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onBTStatusEvent(event: BTStatusEvent) {
        if (!event.isBtOpen) {
            //移除连接超时
            mHandler.removeCallbacks(bleConnectOvRunnable)
            //处理连接状态
            curBleStatus = BLE_UNABLE
            statusCallBack?.invoke(BLE_UNABLE, false)
        } else {
            //打开蓝牙后主动去连接
            toConnectBle(needReadInfo)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventBleConnect(event: EventBleConnect) {
        if (!BleController.getInstance().isBlueToothOpen || bleAddress != event.address) {
            return
        }
        //移除连接超时
        mHandler.removeCallbacks(bleConnectOvRunnable)
        //处理连接状态
        //须确保是从本管理类发起的连接，否则不做处理
        if (event.connectSuc() && event.address == BleController.getInstance().connectedBleAddress) {
            if (curBleStatus == BLE_CONNECTING) {
                //蓝牙连接成功
                //更新蓝牙连接状态
                curBleStatus = BLE_CONNECTED_SUC
                statusCallBack?.invoke(BLE_CONNECTED_SUC, false)
                //蓝牙连接成功读取设备信息
                operationAfterBleConnSuc()
            }
        } else {
            when (curBleStatus) {
                BLE_CONNECTING, BLE_CONNECTED_SUC, BLE_READ_INFO_FINISH -> {
                    //蓝牙连接断开
                    curBleStatus = BLE_DISCONNECT
                    statusCallBack?.invoke(BLE_DISCONNECT, false)
                }

                else -> {}
            }
        }
    }

    /**
     * 蓝牙连接成功读取设备信息
     */
    private fun operationAfterBleConnSuc() {
        if (needReadInfo) {
            //作短暂延时，确保Vm4ThOpManager已初始化
            mHandler.postDelayed({
                Vm4ThOpManager.instance()?.let { vm4Th ->
                    //读取设备信息
                    vm4Th.getDeviceInfo().let { bindExt ->
                        var op4ReadInfo: ThConfig4ReadInfo? = ThConfig4ReadInfo()
                        op4ReadInfo?.readDeviceInfo(bindExt, hasBindGw = vm4Th.isBindGateway(), order = bindExt.groupOrder) {
                            //如果告警信息有变化,须更新
                            //sku缓存的WarnRange可能为空(如卸载重装时)，注意equals的非空判断
                            var hasWarnInfoChanged = false
                            when (bindExt.sku) {
                                Constant4L5.H5112 -> {
                                    val hasWarnInfoChanged4Pb1 = bindExt.warnRange != WarnConfig.read().queryWarningRangeByKey(bindExt.sku, bindExt.device)
                                    if (hasWarnInfoChanged4Pb1) {
                                        //更新warnInfo
                                        WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                                    }
                                    val hasWarnInfoChanged4Pb2 = bindExt.warnRange4Pb2 != WarnConfig.read().queryWarningRangeByKey(bindExt.sku, ThConsV1.getH5112Device4Pb2(bindExt.device))
                                    if (hasWarnInfoChanged4Pb2) {
                                        //更新warnInfo
                                        WarnConfig.read().updateWarningRange(bindExt.warnRange4Pb2, true)
                                    }
                                }

                                else -> {
                                    hasWarnInfoChanged = bindExt.warnRange != WarnConfig.read().queryWarningRangeByKey(bindExt.sku, bindExt.device)
                                    if (hasWarnInfoChanged) {
                                        //更新warnInfo
                                        WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                                    }
                                }
                            }

                            curBleStatus = BLE_READ_INFO_FINISH
                            statusCallBack?.invoke(BLE_READ_INFO_FINISH, hasWarnInfoChanged)
                            //释放资源
                            op4ReadInfo?.release()
                            op4ReadInfo = null
                        }
                    }
                }
            }, 500)
        } else {
            curBleStatus = BLE_READ_INFO_FINISH
            statusCallBack?.invoke(BLE_READ_INFO_FINISH, false)
        }
    }

    /**
     * 重置相关变量
     */
    fun clear() {
        goodsType = GoodsType.GOODES_TYPE_NO_SUPPORT
        sku = ""
        device = ""
        bleAddress = ""
        needReadInfo = false
        curBleStatus = BLE_DISCONNECT
        statusCallBack = null
    }

    /**
     * 释放相关资源
     */
    fun release() {
        clear()
        mHandler.removeCallbacksAndMessages(null)
        ThBle.getInstance.inStep4Details(false)
        EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
    }
}