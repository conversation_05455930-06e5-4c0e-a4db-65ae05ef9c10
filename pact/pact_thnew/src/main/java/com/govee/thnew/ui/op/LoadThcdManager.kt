package com.govee.thnew.ui.op

import android.content.Context
import android.os.Handler
import android.text.TextUtils
import androidx.lifecycle.MutableLiveData
import com.govee.base2home.util.CaughtRunnable
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.data.DataConfig
import com.govee.base2newth.data.IServiceDataOp
import com.govee.base2newth.data.IServiceDataOpCallback
import com.govee.base2newth.data.ServiceDataOp
import com.govee.base2newth.data.ServiceDataOp4Thp
import com.govee.base2newth.data.THMemoryUtil
import com.govee.base2newth.data.TaskLinkConfig
import com.govee.base2newth.db.DbController
import com.govee.base2newth.db.TemHum
import com.govee.base2newth.db.TemHumPm
import com.govee.base2newth.deviceitem.LastData
import com.govee.base2newth.net.IThNet
import com.govee.base2newth.net.RequestUpdateLatestData
import com.govee.base2newth.other.Config4LastThValue
import com.govee.base2newth.other.Config4LthdInfoKey.Companion.getConfig
import com.govee.ble.BleController
import com.govee.home.account.config.AccountConfig
import com.govee.mvvm.globalLaunch
import com.govee.mvvm.network.NetworkUtil
import com.govee.thnew.add.AddInfo
import com.govee.thnew.ble.ThBle
import com.govee.thnew.ble.event.Event4BleLoadThcdProgress
import com.govee.thnew.ble.event.Event4BleLoadThcdResult
import com.govee.thnew.config.ThConfig4Detail
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.Vm4ThOpManager.Companion.INIT_FROM_NEW_DETAIL
import com.govee.thnew.ui.Vm4ThOpManager.Companion.INIT_FROM_OLD_DETAIL
import com.govee.thnew.ui.Vm4ThOpManager.Companion.RC_FROM_COMPARE_DATA
import com.govee.thnew.ui.Vm4ThOpManager.Companion.RC_FROM_DETAIL
import com.govee.thnew.ui.Vm4ThOpManager.Companion.RC_FROM_SETTING_LOAD_ALL
import com.govee.thnew.ui.Vm4ThOpManager.Companion.THCD_4_IS_LOAD_FINISH
import com.govee.thnew.ui.Vm4ThOpManager.Companion.THCD_4_IS_LOAD_FROM_DEVICE
import com.govee.thnew.ui.Vm4ThOpManager.Companion.THCD_4_IS_LOAD_FROM_SERVICE
import com.govee.thnew.ui.Vm4ThOpManager.Companion.THCD_4_WAIT_TO_LOAD
import com.govee.ui.R
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.Cache
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.infra.SafeLog.Companion.e
import com.ihoment.base2app.infra.SafeLog.Companion.i
import com.ihoment.base2app.infra.SafeLog.Companion.v
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.network.Transactions
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.concurrent.ConcurrentHashMap

/**
 * <AUTHOR>
 * @date created on 2024/12/10
 * @description 温湿度计载入图表数据的管理类
 * 备注：详情页，数据对比页载入图表数据
 */
class LoadThcdManager(
    private val bindExt: AddInfo,
    private val mHandler: Handler,
    private val initFromType: Int,
    private var bleOpManager: BleOpManager? = null
) {

    init {
        //注册EvenBus
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
    }

    /**
     * 从服务端加载图表数据的操作管理类
     */
    var serviceThCdOp: IServiceDataOp? = null

    /**
     * 是否须要通过ble拉取设备端缓存的图表数据
     */
    @Volatile
    var loadBleThCdIsNecessary = false

    /**
     * 载入服务端图表数据的结果
     */
    var loadServiceThCdResult = false

    /**
     * 标记上一次拉取完后的数据信息,用于拉取完数据后的toast提示
     */
    private var lastValidDataLength = 0L
    private var lastValidDataTime = 0L

    /**
     * 首次拉取蓝牙数据后，是否因数据有缺失而需要重新拉取缺失数据
     */
    private var needReloadBleThcd = true

    /**
     * 是否处于因数据缺失时而重拉数据的状态中
     */
    private var isReloadingThcd = false

    /**
     * 更新图表的LiveData-->旧详情页使用
     */
    val ld4ShowChart by lazy {
        MutableLiveData<List<TemHum>>()
    }

    /**
     * 更新图表的LiveData-->新详情页使用
     */
    val ld4ShowChartNew by lazy {
        MutableLiveData<HashMap<Int, ArrayList<TemHumPm>>>()
    }

    /**
     * 正在加载或等待加载图表数据的设备集
     * 备注：key(pair):   first->sku,second->device;
     *      value(pair): first(pair)=-> first->goodsType,second->bleAddress;
     *                   second(triple)=-> first->处理图表数据的来源,second->加载的步骤,third->加载结束后的信息(使用前提是加载步骤为：LOAD_TH_DATA_FINISH；first->加载结果，second->本地是否有该设备数据)
     */
    val loadThcdInfoMap by lazy {
        ConcurrentHashMap<Pair<String, String>, Pair<Pair<Int, String>, Triple<Int, Int, Pair<Boolean, Boolean?>?>>>()
    }

    /**
     * 图表数据加载过程(service+ble)步骤的LiveData
     * 备注：key(pair)   =->first->sku,second->device;
     *      value(pair) =->first(pair)=-> first->goodsType,second->bleAddress;
     *                     second=(triple)-> first->处理图表数据的来源,second->加载的步骤,third(pair)->加载结束后的信息(使用前提是加载步骤为：LOAD_TH_DATA_FINISH；first->加载结果，second->本地是否有该设备数据)
     */
    val ld4LoadThCdStep by lazy {
        MutableLiveData<ConcurrentHashMap<Pair<String, String>, Pair<Pair<Int, String>, Triple<Int, Int, Pair<Boolean, Boolean?>?>>>>()
    }

    /**
     * 数据对比选中的对比时间段
     */
    private var timeRange4Compare: Pair<Long, Long>? = null

    /**
     * 缓存多设备数据对比时已加载过的设备
     * 备注：存储deviceKey(sku_device)
     */
    val hasLoadedDevices4Compare by lazy {
        hashSetOf<String>()
    }

    /**
     * 供外部更新蓝牙操作类
     */
    fun setBleManager(bleOpManager: BleOpManager?) {
        this.bleOpManager = bleOpManager
    }

    /**
     * 获取设备key
     */
    private fun getKey(sku: String = bindExt.sku, device: String = bindExt.device): String {
        return "${sku}_${device}"
    }

    @Subscribe(threadMode = ThreadMode.MAIN, sticky = true)
    fun onEvent4ServiceDataOp(serviceDataOp: IServiceDataOp?) {
        i("xiaobing") { "LoadThcdManager--onEvent4ServiceDataOp-->接收从旧工程传递过来的服务端数据加载管理类，共用此类..." }
        serviceDataOp?.let {
            serviceThCdOp = it
        }
    }

    /**
     * 对比数据时，获取真实的加载时间段
     * 备注：确保加载设备的全部数据(主+从款设备(B5178)、H5053无选时间段加载功能，故总是加载全部数据)
     */
    private fun getRealLoadTr4Compare(
        goodsType: Int,
        sku: String,
        device: String,
        timeRange4Compare: Pair<Long, Long>,
        from: String
    ): Pair<Long, Long>? {
        var firstValidDataTime = ThConsV1.getFirstValidTime(goodsType, sku, device)
        //可能未加载过数据
        if (firstValidDataTime <= 0) {
            v("xiaobing") { "LoadThcdManager--getRealLoadTr4Compare-->deviceKey=->${sku}_${device},from->${from},设备还未加载过数据,准备载入全部数据..." }
            return Pair(ThConsV1.LOAD_ALL_TIME, System.currentTimeMillis())
        }
        var lastValidDataTime = ThConsV1.getLastValidTime(goodsType, sku, device)
        val temMinuteMills = 10 * 60 * 1000L
        if (ThConfig4Support.isMainSubDevice(goodsType, sku)) {
            v("xiaobing") { "LoadThcdManager--getRealLoadTr4Compare-->deviceKey=->${sku}_${device},from->${from},主+从款设备已加载了前面的全部数据..." }
            return Pair(lastValidDataTime - temMinuteMills, System.currentTimeMillis())
        } else {
            DataConfig.read().getHasLoadedTr(sku, device)?.let {
                if (it.first < firstValidDataTime) {
                    firstValidDataTime = it.first
                }
                if (it.second > lastValidDataTime) {
                    lastValidDataTime = it.second
                }
            }
            val startTime4Compare = timeRange4Compare.first
            val endTime4Compare = timeRange4Compare.second
            val clearDataTime = DataConfig.read().getLastDataClearTime(sku, device)
            val realStartTime = clearDataTime.coerceAtLeast(startTime4Compare)
            return if (realStartTime >= firstValidDataTime && endTime4Compare <= lastValidDataTime) {
                //数据已加载到本地，无须再去从服务端拉取
                e("xiaobing") { "LoadThcdManager--getRealLoadTr4Compare-->deviceKey=>${sku}_${device},from->${from},全部数据已加载到本地，无须再去拉取..." }
                null
            } else if (realStartTime < firstValidDataTime) {
                //本地有部分数据，还需加载头部缺少部分
                //备注：头部有数据未拉取，则拉取全部
                v("xiaobing") { "LoadThcdManager--getRealLoadTr4Compare-->deviceKey=->${sku}_${device},from->${from},本地有部分数据，须载入全部数据..." }
                Pair(ThConsV1.LOAD_ALL_TIME, System.currentTimeMillis())
            } else {
                //本地有部分数据，还需加载尾部缺少部分
                v("xiaobing") { "LoadThcdManager--getRealLoadTr4Compare-->deviceKey=->${sku}_${device},from->${from},本地有部分数据，只须载入尾部缺少部分..." }
                Pair(lastValidDataTime - temMinuteMills, System.currentTimeMillis())
            }
        }
    }

    /**
     * 服务端图表数据加载回调
     */
    private val loadServiceDataCallback by lazy {
        object : IServiceDataOpCallback {
            override fun loadOver(sku: String, device: String, allOver: Boolean) {
                globalLaunch(Dispatchers.Main) {
                    loadThcdInfoMap[Pair(sku, device)]?.let { deviceInfo ->
                        if (deviceInfo.second.second == THCD_4_IS_LOAD_FROM_SERVICE) {
                            //修改相关标识位
                            loadServiceThCdResult = allOver
                            val hasNetWork = NetworkUtil.isNetworkAvailable(BaseApplication.getContext())
                            if (AccountConfig.read().isHadToken && !hasNetWork) {
                                //无网络提示
                                toast(R.string.network_anomaly)
                            }
                            //设置页=->加载全部数据时，如果服务端数据加载失败，则不再加载设备端数据
                            //根据是否须要从设备端同步温湿度图表数据，进行后续操作
                            val needLoadDeviceThcd = if (deviceInfo.second.first == RC_FROM_SETTING_LOAD_ALL) {
                                ThConfig4Detail.needLoadDeviceThChartData(deviceInfo.first.first, sku) && allOver
                            } else {
                                ThConfig4Detail.needLoadDeviceThChartData(deviceInfo.first.first, sku)
                            }
                            if (needLoadDeviceThcd) {
                                i("xiaobing") { "LoadThcdManager--loadOver-->needLoadDeviceThcd..." }
                                Vm4ThOpManager.instance()?.getBleOpManager()
                                //开始通过ble从设备端获取数据
                                when (bleOpManager?.curBleStatus) {
                                    BleOpManager.BLE_CONNECTING,
                                    BleOpManager.BLE_CONNECTED_SUC,
                                        -> {
                                        //蓝牙正在连接中或连接后正在载入其他数据，须等待直至其他必要数据加载完毕后，再从设备同步图表数据
                                        loadBleThCdIsNecessary = true
                                    }
                                    //蓝牙已连接，则通过ble同步设备端的温湿度图表数据
                                    BleOpManager.BLE_READ_INFO_FINISH -> {
                                        mHandler.postDelayed(bleLoadThCdAfterServiceRunnable, 0)
                                    }

                                    BleOpManager.BLE_DISCONNECT -> {
                                        loadBleThCdIsNecessary = true
                                        val isOwner = getKey() == getKey(sku, device)
                                        if (isOwner) {
                                            Vm4ThOpManager.instance()?.toConnectOriginDevice(bleOpManager)
                                        } else {
                                            Vm4ThOpManager.instance()?.toConnectBleByLoadThcd(deviceInfo.first.first, sku, device, deviceInfo.first.second)
                                        }
                                    }

                                    BleOpManager.BLE_UNABLE -> {
                                        mHandler.postDelayed({
                                            loadThCdFinish(sku, device, allOver, null)
                                        }, 1000L)
                                    }

                                    else -> {}
                                }
                            } else {
                                //加载完毕(无须进行蓝牙加载，则默认其为成功)
                                loadThCdFinish(sku, device, allOver, true)
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 载入多个设备图表数据
     */
    fun loadThcdByMulti(
        startTime: Long,
        endTime: Long,
        lineIndexMap: HashMap<Int, Pair<Int, Triple<String, String, String>>>
    ) {
        for (deviceInfo in lineIndexMap.values) {
            val goodsType = deviceInfo.first
            val sku = deviceInfo.second.first
            val device = ThConsV1.getRealDeviceId(sku, deviceInfo.second.second)
            val bleAddress = deviceInfo.second.third
            loadThcdInfoMap[Pair(sku, device)]?.let {
                //已加载的设备更正其加载数据来源
                if (it.second.first != RC_FROM_COMPARE_DATA) {
                    loadThcdInfoMap[Pair(sku, device)] = Pair(it.first, Triple(RC_FROM_COMPARE_DATA, it.second.second, it.second.third))
                }
            }
            toLoadThcdBySingle(
                null,
                RC_FROM_COMPARE_DATA,
                goodsType,
                sku,
                device,
                bleAddress,
                Pair(startTime, endTime)
            )
        }
    }

    /**
     * 开始加载单个温湿度计的图表数据
     * 备注:先拉取服务端的，再同步设备端的
     */
    @Synchronized
    fun toLoadThcdBySingle(
        context: Context? = null,
        from: Int = RC_FROM_DETAIL,
        goodsType: Int = bindExt.goodsType,
        sku: String = bindExt.sku,
        device: String = bindExt.device,
        bleAddress: String = bindExt.address,
        timeRange4Compare: Pair<Long, Long>? = null,
        reload4NoData: Boolean = false
    ) {
        //数据来源可能由：RC_FROM_DETAIL=->RC_FROM_COMPARE_DATA
        val deviceKeyPair = Pair(sku, device)
        loadThcdInfoMap[deviceKeyPair]?.let {
            if (it.second.first != from) {
                loadThcdInfoMap[deviceKeyPair] = Pair(it.first, Triple(from, it.second.second, it.second.third))
            }
        }
        //数据对比时，已经加载过的无须重复加载
        if (from == RC_FROM_COMPARE_DATA && hasLoadedDevices4Compare.contains(getKey(sku, device)) && !reload4NoData) {
            e("xiaobing") { "LoadThcdManager--toLoadThcdBySingle-->deviceKey=>${sku}_${device},from->${from},该设备已加载过..." }
            loadThcdInfoMap[deviceKeyPair]?.let {
                val hasData = hasData4Compare(goodsType, sku, device, timeRange4Compare)
                loadThcdInfoMap[deviceKeyPair] = Pair(it.first, Triple(RC_FROM_COMPARE_DATA, THCD_4_IS_LOAD_FINISH, Pair(true, hasData)))
                ld4LoadThCdStep.postValue(loadThcdInfoMap)
                return
            } ?: run {
                hasLoadedDevices4Compare.remove(getKey(sku, device))
            }
        }
        //构建数据库表
        DbController.getInstance.createDbTable(sku, device)
        //赋值对比时间段
        this.timeRange4Compare = timeRange4Compare
        //1.从旧工程跳转过来：serviceThcdOp不为空，但loadThcdInfoMap为空
        if (loadThcdInfoMap.isEmpty()) {
            serviceThCdOp?.let { thcdOp ->
                //从旧工程的详情页跳转过来
                if (thcdOp.key == getKey()) {
                    if (thcdOp is ServiceDataOp) {
                        thcdOp.setOpCallback(loadServiceDataCallback)
                    } else if (thcdOp is ServiceDataOp4Thp) {
                        thcdOp.setOpCallback(loadServiceDataCallback)
                    }
                    //加载状态
                    val loadType = if (thcdOp.inServicing()) {
                        THCD_4_IS_LOAD_FROM_SERVICE
                    } else {
                        THCD_4_WAIT_TO_LOAD
                    }
                    v("xiaobing") { "LoadThcdManager--toLoadThcdBySingle-->key->${getKey()},从旧工程跳转过来,loadType:${loadType},..." }
                    loadThcdInfoMap[deviceKeyPair] = Pair(Pair(goodsType, bleAddress), Triple(RC_FROM_COMPARE_DATA, loadType, null))
                }
            }
        } else {
            //2.新增对比设备
            loadThcdInfoMap[deviceKeyPair] ?: run {
                v("xiaobing") { "LoadThcdManager--toLoadThcdBySingle-->key->${deviceKeyPair.first}_${deviceKeyPair.second},添加新设备对比..." }
                loadThcdInfoMap[deviceKeyPair] = Pair(Pair(goodsType, bleAddress), Triple(RC_FROM_COMPARE_DATA, THCD_4_WAIT_TO_LOAD, null))
            }
        }
        //已经处于加载的设备可能须要额外处理
        loadThcdInfoMap[deviceKeyPair]?.let {
            when (it.second.second) {
                THCD_4_IS_LOAD_FROM_SERVICE,
                THCD_4_IS_LOAD_FROM_DEVICE,
                    -> {
                    e("xiaobing") { "LoadThcdManager--toLoadThcdBySingle-->deviceKey->${sku}_${device},from->${it.second.first},该设备数据正在加载中..." }
                    //正在加载的设备不做处理，除非已经进入了数据对比页面，但此时弹出了时间选择弹窗-->则直接加载全部数据
                    if (it.second.first == RC_FROM_COMPARE_DATA) {
                        ld4LoadThCdStep.postValue(loadThcdInfoMap)
                        serviceThCdOp?.setLoadFromType(IServiceDataOp.LOAD_FROM_COMPARE)
                    }
                    return
                }

                THCD_4_IS_LOAD_FINISH -> {
                    //更新加载状态
                    timeRange4Compare?.let { tr ->
                        getRealLoadTr4Compare(goodsType, sku, device, tr, "toLoadThcdBySingle-->loadStatus->THCD_4_IS_LOAD_FINISH") ?: run {
                            //无须再处理的前提是：对比时间段本地有数据
                            if (hasData4Compare(goodsType, sku, device, tr).isTrue()) {
                                v("xiaobing") { "LoadThcdManager--toLoadThcdBySingle-->对比时间段的数据已被加载，无须再加载..." }
                                return
                            }
                        }
                    }
                    v("xiaobing") { "LoadThcdManager--toLoadThcdBySingle-->已加载过，还需加载数据..." }
                    loadThcdInfoMap[deviceKeyPair] =
                        Pair(Pair(goodsType, bleAddress), Triple(from, THCD_4_WAIT_TO_LOAD, null))
                }

                else -> {}
            }
        }
        //若存在正在加载图表数据的设备，则等待该设备加载完后再进行加载
        for (deviceKey in loadThcdInfoMap.keys) {
            loadThcdInfoMap[deviceKey]?.let { deviceInfo ->
                when (deviceInfo.second.second) {
                    THCD_4_IS_LOAD_FROM_SERVICE,
                    THCD_4_IS_LOAD_FROM_DEVICE
                        -> {
                        e("xiaobing") { "LoadThcdManager--toLoadThcdBySingle-->deviceKey->${deviceKey.first}_${deviceKey.second},正在加载数据中,已将 ${sku}_${device} 加入集合，等待加载..." }
                        ld4LoadThCdStep.postValue(loadThcdInfoMap)
                        return
                    }

                    else -> {}
                }
            }
        }
        toLoadThcdFromService(context, from, goodsType, sku, device, bleAddress, "toLoadThcdBySingle")
    }

    /**
     * 开始从服务端加载图表数据
     */
    private fun toLoadThcdFromService(
        context: Context? = null,
        from: Int, goodsType: Int,
        sku: String,
        device: String,
        bleAddress: String,
        invokeFrom: String
    ) {
        //根据设备类型按各自方式开始加载服务端的图表数据
        if (ThConfig4Support.supportOnly433(goodsType, sku)) {
            //更新标记;进入数据读取中
            THMemoryUtil.getInstance().isTrendChartReading = true
            var loadThcd4H5053: LoadThCd4H5053? = LoadThCd4H5053()
            //更新加载状态
            loadThcdInfoMap[Pair(sku, device)] = Pair(Pair(goodsType, bleAddress), Triple(from, THCD_4_IS_LOAD_FROM_SERVICE, null))
            ld4LoadThCdStep.postValue(loadThcdInfoMap)
            loadThcd4H5053?.startToLoadThCd(goodsType, sku, device) { hasNewData, result ->
                loadThcdInfoMap[Pair(sku, device)]?.let {
                    if (it.second.second == THCD_4_IS_LOAD_FROM_SERVICE) {
                        val hasNetWork = NetworkUtil.isNetworkAvailable(BaseApplication.getContext())
                        if (AccountConfig.read().isHadToken && !hasNetWork) {
                            //无网络提示
                            toast(R.string.network_anomaly)
                        }
                        //对比数据时加载过全部数据就不再加载(H5053只要加载过数据就是加载全部数据)
                        hasLoadedDevices4Compare.add(getKey(sku, device))
                        //回调结果
                        ld4LoadThCdStep.postValue(loadThcdInfoMap.apply {
                            currFinishDevice = Pair(sku, device)
                            val hasData = hasData4Compare(goodsType, sku, device, timeRange4Compare)
                            put(Pair(sku, device), Pair(it.first, Triple(from, THCD_4_IS_LOAD_FINISH, Pair(result, hasData))))
                        })
                        //更新最后一条数据
                        updateLatestThData(sku, device)
                        //更新标记;数据加载结束
                        THMemoryUtil.getInstance().isTrendChartReading = false
                        //若存在等待加载的设备，则进行一个设备的数据加载
                        toLoadNextDeviceThcd()
                        //切换设备加载图表数据无须相关提示语
                        val isOwner = getKey(sku, device) == getKey() && it.second.first == RC_FROM_DETAIL
                        if (isOwner) {
                            if (hasNewData) {
                                toast(R.string.temhum_data_sync_suc)
                            } else {
                                toast(R.string.h5072_data_sync_suc_no_new)
                            }
                        }
                    }
                }
                //释放资源
                loadThcd4H5053?.release()
                loadThcd4H5053 = null
            }
        } else {
            if (serviceThCdOp == null) {
                //构建服务器请求图表数据管理类
                serviceThCdOp = createServiceOp(context, goodsType, sku, device)
            } else {
                if (serviceThCdOp!!.key != getKey(sku, device)) {
                    //图表数据对比=->切换设备拉取图表数据
                    serviceThCdOp = createServiceOp(context, goodsType, sku, device)
                }
            }
            if (serviceThCdOp?.inServicing() == true) {
                e("xiaobing") { "LoadThcdManager--toLoadThcdFromService-->正在加载服务端图表数据..." }
                return
            }
            globalLaunch(Dispatchers.IO) {
                //更新加载状态
                loadThcdInfoMap[Pair(sku, device)] = Pair(Pair(goodsType, bleAddress), Triple(from, THCD_4_IS_LOAD_FROM_SERVICE, null))
                ld4LoadThCdStep.postValue(loadThcdInfoMap)
                v("xiaobing") { "LoadThcdManager--toLoadThcdFromService-->开始加载服务端图表数据，key=>${getKey(sku, device)},from->${invokeFrom}..." }
                //更新标记;进入数据读取中
                THMemoryUtil.getInstance().isTrendChartReading = true
                //被加载设备不是已连接的设备，则须断开蓝牙连接
                if (BleController.getInstance().isConnected &&
                    BleController.getInstance().connectedBleAddress != bleAddress &&
                    !TextUtils.isEmpty(bleAddress)
                ) {
                    //断开之前的连接
                    e("xiaobing") { "LoadThcdManager--toLoadThcdFromService-->准备蓝牙加载图表数据，断开之前的连接,oldBleAddress->${BleController.getInstance().connectedBleAddress}" }
                    BleController.getInstance().disconnectBleAndNotify()
                    bleOpManager?.clear()
                }
                mHandler.postDelayed({
                    if (bleOpManager?.curBleStatus == BleOpManager.BLE_DISCONNECT) {
                        //尝试连接所加载设备的蓝牙,为后续通过ble加载设备端图表数据做好准备
                        val isOwner = getKey() == getKey(sku, device)
                        if (isOwner) {
                            Vm4ThOpManager.instance()?.toConnectOriginDevice(bleOpManager)
                        } else {
                            Vm4ThOpManager.instance()?.toConnectBleByLoadThcd(goodsType, sku, device, bleAddress)
                        }
                    }
                }, 200)
                //开始加载服务端的温湿度图表数据
                timeRange4Compare?.let {
                    getRealLoadTr4Compare(goodsType, sku, device, it, "toLoadThcdFromService=->开始载入服务端数据")?.let { realLoadTimeRange ->
                        //加载全部数据时，更新加载数据起始时间点
                        if (realLoadTimeRange.first <= 0) {
                            TaskLinkConfig.read(sku, device).lastLoadDataTimeMills = ThConsV1.LOAD_ALL_TIME
                            getConfig().saveSelPeriodInfo(sku, device, Pair(true, ThConsV1.LOAD_ALL_TIME))
                        }
                        serviceThCdOp?.loadServiceData4Compare(realLoadTimeRange)
                    } ?: run {
                        //已有全部数据，则直接结束
                        loadThCdFinish(sku, device, true, null)
                    }
                } ?: run {
                    //记录加载数据前已有的数据信息
                    lastValidDataLength = ThConsV1.getValidDataLength(goodsType, sku, device)
                    lastValidDataTime = ThConsV1.getLastValidTime(goodsType, sku, device)
                    if (from == RC_FROM_COMPARE_DATA) {
                        serviceThCdOp?.setLoadFromType(IServiceDataOp.LOAD_FROM_COMPARE)
                    }
                    serviceThCdOp?.loadServiceData()
                }
            }
        }
    }

    /**
     * 创建载入服务端图表数据的操作类
     */
    private fun createServiceOp(
        context: Context?,
        goodsType: Int,
        sku: String,
        device: String
    ): IServiceDataOp {
        serviceThCdOp?.destroy()
        serviceThCdOp = null
        return if (ThConsV1.needUsedThp(goodsType, sku)) {
            ServiceDataOp4Thp(context, sku, device, ThConfig4Detail.getMinValidTime(goodsType, sku)).apply {
                //服务端拉取图表数据的结果回调
                setOpCallback(loadServiceDataCallback)
            }
        } else {
            ServiceDataOp(context, sku, device, ThConfig4Detail.getMinValidTime(goodsType, sku)).apply {
                //服务端拉取图表数据的结果回调
                setOpCallback(loadServiceDataCallback)
            }
        }
    }

    /**
     * 加载的时段内，本地是否已有数据
     */
    private fun hasData4Compare(
        goodsType: Int,
        sku: String,
        device: String,
        tr4Compare: Pair<Long, Long>?
    ): Boolean? {
        return tr4Compare?.let {
            val dataSize = if (ThConsV1.needUsedThp(goodsType, sku)) {
                DbController.queryAllData4Thp(sku, device, it.first, it.second).size
            } else {
                DbController.queryAllData(sku, device, it.first, it.second).size
            }
            dataSize >= 2
        }
    }

    /**
     * 删除加载设备时同步删除加载设备数据集
     */
    fun removeLoadThcd(sku: String, device: String) {
        loadThcdInfoMap.remove(Pair(sku, device))
        //如果正在加载数据须销毁serviceDataOp
        serviceThCdOp?.let {
            if (it.key == getKey(sku, device)) {
                it.destroy()
                serviceThCdOp = null
            }
        }
        //如果蓝牙处于连接须断开
        when (bleOpManager?.curBleStatus) {
            BleOpManager.BLE_CONNECTED_SUC,
            BleOpManager.BLE_READ_INFO_FINISH,
                -> {
                BleController.getInstance().disconnectBleAndNotify()
            }

            else -> {}
        }
        hasLoadedDevices4Compare.remove(getKey(sku, device))
        ld4LoadThCdStep.postValue(loadThcdInfoMap)
    }

    /**
     * 退出数据对比页面(多设备+历史数据)时清除缓存的加载设备
     */
    fun clearCompareDevices() {
        hasLoadedDevices4Compare.clear()
    }

    /**
     * 等待通过ble加载设备端图表数据
     */
    private val bleLoadThCdAfterServiceRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                mHandler.removeCallbacks(this)
                for (deviceKey in loadThcdInfoMap.keys) {
                    loadThcdInfoMap[deviceKey]?.let {
                        if (it.second.second == THCD_4_IS_LOAD_FROM_SERVICE) {
                            if (getKey() == getKey(deviceKey.first, deviceKey.second)) {
                                val loadDevInfoFinish = Vm4ThOpManager.instance()?.loadDevInfoFinish ?: false
                                val loadBdTypeFinish = Vm4ThOpManager.instance()?.loadBdTypeFinish ?: false
                                val syncTriggersFinish = Vm4ThOpManager.instance()?.syncTriggersFinish ?: false
                                i("xiaobing") { "LoadThcdManager--runSafe-->loadDevInfoFinish->${loadDevInfoFinish},loadBdTypeFinish->${loadBdTypeFinish},syncTriggersFinish->${syncTriggersFinish}" }
                                val readInfoOver = !(bleOpManager?.needReadInfo ?: false) || (loadDevInfoFinish && loadBdTypeFinish && syncTriggersFinish)
                                if (readInfoOver) {
                                    toLoadThCdFromDeviceByBle(it.first.first, deviceKey.first, deviceKey.second)
                                } else {
                                    mHandler.postDelayed(this, 1000)
                                }
                            } else {
                                toLoadThCdFromDeviceByBle(it.first.first, deviceKey.first, deviceKey.second)
                            }
                            return
                        }
                    }
                }
            }
        }
    }

    /**
     * 开始通过ble载入设备端缓存的温湿度图表数据
     */
    fun toLoadThCdFromDeviceByBle(goodsType: Int, sku: String, device: String) {
        globalLaunch(Dispatchers.IO) {
            loadThcdInfoMap[Pair(sku, device)]?.let {
                if (it.second.second == THCD_4_IS_LOAD_FROM_SERVICE) {
                    loadThcdByBle(goodsType, sku, device, it.first, it.second.first, false)
                }
            }
        }
    }

    /**
     * 开始通过ble载入设备端缓存的温湿度图表数据-->具体实现
     * 备注:调用前，先确保蓝牙已连接
     * @return 是否需要继续往后执行代码
     */
    private fun loadThcdByBle(
        goodsType: Int,
        sku: String,
        device: String,
        partInfo: Pair<Int, String>,
        from: Int,
        isReload: Boolean
    ): Boolean {
        isReloadingThcd = isReload
        //须判断蓝牙是否仍连接，因为在等待加载的过程中可能断连
        if (bleOpManager?.curBleStatus != BleOpManager.BLE_CONNECTED_SUC &&
            bleOpManager?.curBleStatus != BleOpManager.BLE_READ_INFO_FINISH
        ) {
            if (!isReload) {
                loadThCdFinish(sku, device, loadServiceThCdResult, false)
            }
            return true
        }
        //开始通过ble拉取设备端数据，进程和结果通过事件返回到该类中
        val maxTimeRange = ThConfig4Detail.getDeviceThDataRange(goodsType, sku)
        //更新加载状态
        loadThcdInfoMap[Pair(sku, device)] = Pair(partInfo, Triple(from, THCD_4_IS_LOAD_FROM_DEVICE, null))
        ld4LoadThCdStep.postValue(loadThcdInfoMap)
        if (ThConfig4Support.isMainSubDevice(goodsType, sku)) {
            val mainDevice: String
            val subDevice: String
            if (bindExt.groupOrder == 0) {
                mainDevice = device
                subDevice = "${mainDevice}_1"
            } else {
                mainDevice = if (device.contains("_")) device.split("_")[0] else device
                subDevice = device
            }
            //主设备的删除数据时间点
            val lastClearTime4Main: Long = DataConfig.read().getLastDataClearTime(sku, mainDevice)
            //从设备的删除数据时间点
            val lastClearTime4Sub: Long = DataConfig.read().getLastDataClearTime(sku, subDevice)
            i("xiaobing") { "LoadThcdManager--loadThcdByBle-->mainDevice=->${mainDevice},subDevice=>${subDevice}" }
            //开始通过ble加载数据
            val dataTimeSet4Main = DbController.queryDataTimeSet(sku, mainDevice, lastClearTime4Main, maxTimeRange)
            val dataTimeSet4Sub = DbController.queryDataTimeSet(sku, subDevice, lastClearTime4Sub, maxTimeRange)
            if (!isReload) {
                ThBle.getInstance.thChartDataManager.startDataOp(
                    goodsType,
                    sku,
                    mainDevice,
                    mainDevice == device,
                    dataTimeSet4Main,
                    dataTimeSet4Sub
                )
            } else {
                if (dataTimeSet4Main.hasMissingDataInMid() || dataTimeSet4Sub.hasMissingDataInMid()) {
                    e("xiaobing") { "LoadThcdManager--loadThcdByBle-->有数据丢失，重新加载一遍..." }
                    ThBle.getInstance.thChartDataManager.startDataOp(
                        goodsType,
                        sku,
                        mainDevice,
                        mainDevice == device,
                        dataTimeSet4Main,
                        dataTimeSet4Sub
                    )
                    return false
                }
                return true
            }
        } else {
            //开始通过ble加载数据
            val lastDataClearTime = DataConfig.read().getLastDataClearTime(sku, device)
            val dataTimeSet = if (ThConsV1.needUsedThp(goodsType, sku)) {
                DbController.queryDataTimeSet4Thp(sku, device, lastDataClearTime, maxTimeRange)
            } else {
                DbController.queryDataTimeSet(sku, device, lastDataClearTime, maxTimeRange)
            }
            if (!isReload) {
                ThBle.getInstance.thChartDataManager.startDataOp(goodsType, sku, device, dataTimeSet)
            } else {
                if (dataTimeSet.hasMissingDataInMid()) {
                    e("xiaobing") { "LoadThcdManager--loadThcdByBle-->有数据丢失，重新加载一遍..." }
                    ThBle.getInstance.thChartDataManager.startDataOp(goodsType, sku, device, dataTimeSet)
                    return false
                }
                return true
            }
        }
        return false
    }

    /**
     * 更新图表显示
     * @param isHoldShow 是否为占位显示(详情页如果数据较多，直接展示较慢，为营造快速展示的效果先展示少量数据)
     */
    @Synchronized
    fun refreshThChartData(from: String, isHoldShow: Boolean = false) {
        globalLaunch(Dispatchers.IO) {
            val searchStartTime = if (isHoldShow) {
                val lastValidTime = DbController.queryLastValidTime(bindExt.sku, bindExt.device)
                if (lastValidTime > 0) {
                    lastValidTime - ThConsV1.ONE_WEEK_MILLIS
                } else {
                    System.currentTimeMillis() - ThConsV1.ONE_WEEK_MILLIS
                }
            } else {
                ThConfig4Detail.getMinValidTime(bindExt.goodsType, bindExt.sku) - 30 * 1000L
            }
            val searchEndTime = System.currentTimeMillis() + ThConsV1.flag_time_mills_one_month + 30 * 1000L
            i("xiaobing") { "LoadThcdManager--refreshThChartData-->from=->${from}" }
            //通知图表刷新
            when (initFromType) {
                INIT_FROM_OLD_DETAIL -> {
                    val queryAllData = DbController.queryAllData(bindExt.sku, bindExt.device, searchStartTime, searchEndTime) ?: ArrayList<TemHum>()
                    ld4ShowChart.postValue(queryAllData)
                }

                INIT_FROM_NEW_DETAIL -> {
                    val lineData = arrayListOf<TemHumPm>()
                    //时间点已被取整，若传该值去查询可能会遗漏首尾点，故开始时间-30s，解释时间+30s,确保首尾点能被查询到
                    if (ThConsV1.needUsedThp(bindExt.goodsType, bindExt.sku)) {
                        lineData.addAll(DbController.queryAllData4Thp(bindExt.sku, bindExt.device, searchStartTime, searchEndTime))
                    } else {
                        val lineDataTh = DbController.queryAllData(bindExt.sku, bindExt.device, searchStartTime, searchEndTime)
                        //将TemHum对像转换为TemHumPm
                        for (temHum in lineDataTh) {
                            lineData.add(temHum.thp)
                        }
                        //释放资源
                        lineDataTh.clear()
                    }
                    ld4ShowChartNew.postValue(HashMap<Int, ArrayList<TemHumPm>>().apply {
                        put(ThConsV1.LINE_1, lineData)
                    })
                }

                else -> {}
            }
            mayUpdateRealInfoShow()
        }
    }

    /**
     * 存在更新了服务端数据，但本地显示更新滞后的情况
     */
    private fun mayUpdateRealInfoShow() {
        globalLaunch(Dispatchers.IO) {
            val lastValidThp = if (ThConsV1.needUsedThp(bindExt.goodsType, bindExt.sku)) {
                DbController.queryLastData4Thp(bindExt.sku, bindExt.device)
            } else {
                DbController.queryLastData(bindExt.sku, bindExt.device)?.thp
            }
            lastValidThp?.let { thp ->
                (Config4LastThValue.getConfig().getLastTh(bindExt.sku, bindExt.device)
                    ?: LastData()).apply {
                    if (thp.time > lastTime && thp.time <= System.currentTimeMillis()) {
                        tem = thp.tem
                        hum = thp.hum
                        pm = thp.pm25
                        lastTime = thp.time
                        Vm4ThOpManager.instance()?.ld4RealTemHum?.postValue(Pair(Triple(tem, hum, pm), lastTime))
                    }
                }
            }
        }
    }

    /**
     * 同步设备端数据的进度百分比的LiveData
     * 备注：first:first->sku,second->device;second:first->拉取数据的来源，second->蓝牙拉取进度
     */
    val ld4BleLoadThCdProgress by lazy {
        MutableLiveData<Pair<Pair<String, String>, Pair<Int, Int>>>()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4LoadBleThCdProgress(event: Event4BleLoadThcdProgress) {
        //进度值无效、重拉数据的进度无需更新
        if (event.progress < 0 || isReloadingThcd) {
            return
        }
        var all = event.all
        all = 1.coerceAtLeast(all)
        var progress = event.progress
        progress = 0.coerceAtLeast(progress)
        progress = progress.coerceAtMost(all)
        //更新进度
        val percent = (progress * 100) / all
        val deviceKey = Pair(event.sku, event.device)
        loadThcdInfoMap[deviceKey]?.let {
            ld4BleLoadThCdProgress.value = Pair(deviceKey, Pair(it.second.first, percent))
        }
    }

    /**
     * 通过蓝牙同步设备端图表数据结束
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4LoadBleThCdFinish(event: Event4BleLoadThcdResult) {
        loadThcdInfoMap[Pair(event.sku, event.device)]?.let {
            if (it.second.second == THCD_4_IS_LOAD_FROM_DEVICE) {
                i("xiaobing") { "LoadThcdManager--onEvent4LoadBleThCdFinish-->${event.readOver}" }
                //数据读取完成；则触发上传逻辑
                if (getKey(event.sku, event.device) == serviceThCdOp?.key) {
                    serviceThCdOp?.uploadData()
                }
                if (event.readOver) {
                    ld4BleLoadThCdProgress.value = Pair(Pair(event.sku, event.device), Pair(it.second.first, 100))
                }
                //加载图表数据结束
                mHandler.postDelayed({
                    loadThCdFinish(event.sku, event.device, loadServiceThCdResult, event.readOver)
                }, 1000)
            }
        }
    }

    /**
     * 记录当前正在加载的设备
     */
    @Volatile
    var currFinishDevice: Pair<String, String>? = null

    /**
     * 整个加载图表数据的过程结束
     * @param bleLoadSuc 为null时无须提示
     * 备注：H5053加载结束后，有其自生的回调，不调用此方法
     */
    fun loadThCdFinish(
        sku: String,
        device: String,
        serviceLoadResult: Boolean,
        bleLoadSuc: Boolean?
    ) {
        loadThcdInfoMap[Pair(sku, device)]?.let {
            globalLaunch(Dispatchers.IO) {
                val goodsType = it.first.first
                if (it.second.second == THCD_4_IS_LOAD_FROM_DEVICE || it.second.second == THCD_4_IS_LOAD_FROM_SERVICE) {
                    i("xiaobing") { "LoadThcdManager--loadThCdFinish-->deviceKey=->${sku}_${device},拉取图表数据结束...." }
                    if (needReloadBleThcd && bleLoadSuc.isTrue()) {
                        needReloadBleThcd = false
                        val doContinue = loadThcdByBle(goodsType, sku, device, it.first, it.second.first, true)
                        if (!doContinue) {
                            return@globalLaunch
                        }
                    }
                    //缓存已加载数据的时间段
                    var loadResult = false
                    if (serviceLoadResult) {
                        //wifi款的设备如果只拉取了服务端的数据，但已拉到最新的数据也视为成功
                        val lastValidTime = ThConsV1.getLastValidTime(goodsType, sku, device)
                        val loadEndTime = serviceThCdOp?.loadEndTime ?: lastValidTime
                        if (!bleLoadSuc.isTrue()) {
                            val timeStep = ThConfig4Support.getTimeStep(goodsType, sku)
                            if (loadEndTime >= lastValidTime && (loadEndTime - lastValidTime < timeStep * (60 * 1000L) * 2)) {
                                saveLoadTime4AllSuc(goodsType, sku, device, loadEndTime)
                                loadResult = true
                            } else {
                                saveLoadTime4AllSuc(goodsType, sku, device, lastValidTime)
                            }
                        } else {
                            saveLoadTime4AllSuc(goodsType, sku, device, loadEndTime)
                            loadResult = true
                        }
                    }
                    //对比数据时加载过全部数据就不再加载(前提是本地数据库有数据存在)
                    //备注：主+从款设备无选时段加载功能，故只要加载过即可
                    val isFromCompare = it.second.first == RC_FROM_COMPARE_DATA
                    if (isFromCompare && (getConfig().getSelPeriodStartTime(sku, device) == ThConsV1.LOAD_ALL_TIME || ThConfig4Support.isMainSubDevice(goodsType, sku))) {
                        hasLoadedDevices4Compare.add(getKey(sku, device))
                    }
                    //结果回调
                    ld4LoadThCdStep.postValue(loadThcdInfoMap.apply {
                        currFinishDevice = Pair(sku, device)
                        //加载全部数据时，绑定了网关的蓝牙温湿度计，只需加载服务端数据成功即视为成功
                        val isLoadAll4Self = (it.second.first == RC_FROM_SETTING_LOAD_ALL) && (getKey() == getKey(sku, device)) && Vm4ThOpManager.instance()?.isBindGateway() ?: false
                        if (isLoadAll4Self) {
                            put(
                                Pair(sku, device),
                                Pair(
                                    it.first,
                                    Triple(
                                        it.second.first,
                                        THCD_4_IS_LOAD_FINISH,
                                        Pair(serviceLoadResult, hasData4Compare(goodsType, sku, device, timeRange4Compare))
                                    )
                                )
                            )
                        } else {
                            put(
                                Pair(sku, device),
                                Pair(
                                    it.first,
                                    Triple(
                                        it.second.first,
                                        THCD_4_IS_LOAD_FINISH,
                                        Pair(loadResult, hasData4Compare(goodsType, sku, device, timeRange4Compare))
                                    )
                                )
                            )
                        }
                    })
                    //更新最后一条数据
                    updateLatestThData(sku, device)
                    //更新标记;数据读取结束
                    THMemoryUtil.getInstance().isTrendChartReading = false
                    isReloadingThcd = false
                    //若存在等待加载的设备，则进行下一个设备的数据加载
                    toLoadNextDeviceThcd()
                    //切换设备加载图表数据无须相关提示语
                    val isOwner = getKey(sku, device) == getKey()
                    if (isOwner) {
                        //更新图表
                        refreshThChartData("图表数据加载完毕，更新图表...")
                        if (it.second.first != RC_FROM_DETAIL) {
                            return@globalLaunch
                        }
                    } else {
                        return@globalLaunch
                    }
                    //提示处理
                    //设置页处理图表数据无需刷新提示语
                    if (it.second.first != RC_FROM_DETAIL) {
                        return@globalLaunch
                    }
                    //服务端数据加载失败、蓝牙关闭等情况无需刷新提示语
                    if (!serviceLoadResult || bleLoadSuc == null) {
                        //未登录的时候，有提示
                        if (!AccountConfig.read().isHadToken) {
                            withContext(Dispatchers.Main) {
                                toast(R.string.temhum_data_sync_suc_no_new)
                            }
                        }
                        return@globalLaunch
                    }
                    if (!bleLoadSuc) {
                        withContext(Dispatchers.Main) {
                            toast(R.string.h5072_read_ble_data_fail)
                        }
                        return@globalLaunch
                    }
                    val curValidDataLength = ThConsV1.getValidDataLength(bindExt.goodsType, bindExt.sku, bindExt.device)
                    val curValidDataTime = ThConsV1.getLastValidTime(bindExt.goodsType, bindExt.sku, bindExt.device)
                    val loadFinishRemindRes = if (curValidDataLength > lastValidDataLength || curValidDataTime > lastValidDataLength) {
                        R.string.hint_fresh_done
                    } else {
                        R.string.temhum_data_sync_suc_no_new
                    }
                    withContext(Dispatchers.Main) {
                        toast(loadFinishRemindRes)
                    }
                }
            }
        }
    }

    /**
     * 更新最后一条数据
     */
    private fun updateLatestThData(sku: String, device: String) {
        var latestData = ""
        if (ThConsV1.needUsedThp(bindExt.goodsType, bindExt.sku)) {
            DbController.queryLastData4Thp(bindExt.sku, bindExt.device)?.let {
                latestData = "${it.tem},${it.hum},${it.pm25},${it.time}"
            }
        } else {
            DbController.queryLastData(bindExt.sku, bindExt.device)?.let {
                latestData = "${it.tem},${it.hum},${it.time}"
            }
        }
        if (TextUtils.isEmpty(latestData)) {
            return
        }
        val requestUpdateLatestData = RequestUpdateLatestData(Transactions().createTransaction(), sku, device, latestData)
        Cache.get(IThNet::class.java).updateLatesData(requestUpdateLatestData).enqueue(IHCallBack(requestUpdateLatestData))
    }

    /**
     * service+ble都加载成功=->保存加载时间范围，用来判断某段时间范围内数据是否已加载
     */
    private fun saveLoadTime4AllSuc(goodsType: Int, sku: String, device: String, endTime: Long) {
        //主+从款设备无选时段加载功能，故无须此操作
        if (!ThConfig4Support.isMainSubDevice(goodsType, sku)) {
            val hasLoadedSt = getConfig().getSelPeriodStartTime(sku, device)
            //service+ble加载都成功则保存整段时间
            DataConfig.read().updateHasLoadedTr(sku, device, Pair(hasLoadedSt, endTime))
        }
    }

    /**
     * 加载下一个温湿度计设备的图表数据
     */
    private fun toLoadNextDeviceThcd() {
        for (deviceKey in loadThcdInfoMap.keys) {
            loadThcdInfoMap[deviceKey]?.let {
                if (it.second.second == THCD_4_WAIT_TO_LOAD) {
                    toLoadThcdFromService(
                        null,
                        RC_FROM_COMPARE_DATA,
                        it.first.first,
                        deviceKey.first,
                        deviceKey.second,
                        it.first.second,
                        "toLoadNextDeviceThcd"
                    )
                    return
                }
            }
        }
        //重置加载开始时间值
        timeRange4Compare = null
        //断开连接
        if (BleController.getInstance().isConnected &&
            BleController.getInstance().connectedBleAddress != bindExt.address &&
            !TextUtils.isEmpty(bindExt.address)
        ) {
            //断开之前的连接
            e("xiaobing") { "LoadThcdManager--toLoadNextDeviceThcd-->加载结束，断开之前的连接,oldBleAddress=->${BleController.getInstance().connectedBleAddress}" }
            BleController.getInstance().disconnectBleAndNotify()
            bleOpManager?.clear()
        }
    }

    /**
     *  蓝牙回连原始主设备(若须要蓝牙连接的话)
     */
    private fun reconnect4OriginDevice() {
        if (BleController.getInstance().isConnected &&
            BleController.getInstance().connectedBleAddress != bindExt.address &&
            !TextUtils.isEmpty(bindExt.address)
        ) {
            //断开之前的连接
            e("xiaobing") { "LoadThcdManager--reconnect4OriginDevice-->重连原设备前断开之前的连接,oldBleAddress=->${BleController.getInstance().connectedBleAddress}" }
            BleController.getInstance().disconnectBleAndNotify()
            bleOpManager?.clear()
        }
        if (ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)) {
            return
        }
        mHandler.postDelayed({
            if (!BleController.getInstance().isConnected) {
                Vm4ThOpManager.instance()?.toConnectOriginDevice(bleOpManager)
            }
        }, 100)
    }

    /**
     * 退出多设备数据对比时-->清空除主设备外加载图表数据的设备集
     */
    fun clearLoadThcdDevices() {
        for (deviceKey in loadThcdInfoMap.keys) {
            loadThcdInfoMap[deviceKey]?.let {
                if (!(bindExt.sku == deviceKey.first && bindExt.device == deviceKey.second)) {
                    loadThcdInfoMap.remove(deviceKey)
                    serviceThCdOp?.let { thcdOp ->
                        if (thcdOp.key == getKey(deviceKey.first, deviceKey.second)) {
                            thcdOp.destroy()
                        }
                    }
                }
            }
        }
        clearCompareDevices()
        //蓝牙回连原始主设备
        reconnect4OriginDevice()
    }

    fun release() {
        //注销EventBus
        EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
    }
}