package com.govee.thnew.ble

import com.govee.base2newth.AbsMultiBleComm
import com.govee.base2newth.ThConsV1
import java.util.UUID

/**
 * <AUTHOR>
 * @date created on 2024/3/06
 * @description 温湿度计的蓝牙通讯-->多包通讯处理器
 * 备注:配网使用
 */
class ThBleMultiComm : AbsMultiBleComm() {

    companion object {
        private const val serviceUuid = "494e5445-4c4c-495f-524f-434b535f4857"
        private const val characteristicUuid = "494e5445-4c4c-495f-524f-434b535f2011"
    }

    override fun serviceUUID(): UUID {
        return UUID.fromString(serviceUuid)
    }

    override fun characteristicUUID(): UUID {
        return UUID.fromString(characteristicUuid)
    }

    override fun parsePriority(): Int {
        return ThConsV1.comm_parse_priority_th_comm
    }
}