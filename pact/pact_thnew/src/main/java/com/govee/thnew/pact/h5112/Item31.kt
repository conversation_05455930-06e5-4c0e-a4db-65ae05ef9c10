package com.govee.thnew.pact.h5112

import com.govee.base2home.main.AbsDevice
import com.govee.base2home.theme.ThemeM
import com.govee.base2newth.deviceitem.Range
import com.govee.kt.ui.device.CardStyle
import com.govee.kt.ui.device.IBaseItemInfo
import com.govee.kt.ui.device.Item
import com.govee.kt.ui.device.ItemTypeStyle
import com.govee.kt.ui.device.SpanSize
import com.govee.ui.R
import com.ihoment.base2app.ext.isFalse
import com.ihoment.base2app.util.ResUtil

/**
 * <AUTHOR>
 * @date created on 2025/5/22
 * @description H5112--卡片的item类
 */
open class Item31 : IBaseItemInfo() {

    var bleVis: Boolean = false
    private var wifiStatus: Int = Item.WIFI_STATUS_INVISIBLE
    var batteryResId: Int = Item.ICON_STATUS_INVISIBLE
    private var signalResId: Int = Item.ICON_STATUS_INVISIBLE

    var timeMillStr: String = ""

    /**
     * 探针1的温度相关
     */
    var temStr: String = ""
    var temWarning: Boolean = false
    private var temRange: Range = Range()

    /**
     * 探针1的湿度相关
     */
    var humStr: String = ""
    var humWarning: Boolean = false
    private var humRange: Range = Range()

    /**
     * 探针2的温度相关
     */
    var tem2Str: String = ""
    var tem2Warning: Boolean = false
    private var tem2Range: Range = Range()

    var pb1Name = ""
    var pb1IconIndex = 1
    var pb2Name = ""
    var pb2IconIndex = 1

    /*默认sku的Icon图*/
    var defSkuRes: Int = R.mipmap.new_add_list_type_device_defualt_none

    /*sku的服务器链接*/
    var skuUrl: String? = ""

    override fun syncDevice(device: AbsDevice) {
        copyFromAbsDevice(device)
        defSkuRes = ThemeM.getDefSkuRes(sku)
        skuUrl = ThemeM.getInstance.getSkuResource(sku, spec)?.skuUrl
    }

    override fun itemType(bigItem: Boolean, style: Int): ItemTypeStyle {
        return if (style == CardStyle.CLASSICS.style) {
            ItemTypeStyle(
                Item.ITEM_TYPE_31_BIG,
                SpanSize.BIG
            )
        } else {
            ItemTypeStyle(
                Item.ITEM_TYPE_31_BIG_NEW,
                SpanSize.BIG
            )
        }
    }

    fun temIconRes(): Int {
        return if (temWarning) R.mipmap.new_widget_ios_icon_temp_red else R.mipmap.new_widget_ios_icon_temp
    }

    fun temIconResNew(): Int {
        return if (temWarning && !isOffline()) R.mipmap.new_widget_ios_icon_temp_red else R.mipmap.new_widget_ios_icon_temp
    }

    fun humIconRes(): Int {
        return if (humWarning) R.mipmap.new_widget_ios_icon_humi_red else R.mipmap.new_widget_ios_icon_humi
    }

    fun humIconResNew(): Int {
        return if (humWarning && !isOffline()) R.mipmap.new_widget_ios_icon_humi_red else R.mipmap.new_widget_ios_icon_humi
    }

    fun tem2IconRes(): Int {
        return if (tem2Warning) R.mipmap.new_widget_ios_icon_temp_red else R.mipmap.new_widget_ios_icon_temp
    }

    fun tem2IconResNew(): Int {
        return if (tem2Warning && !isOffline()) R.mipmap.new_widget_ios_icon_temp_red else R.mipmap.new_widget_ios_icon_temp
    }

    fun wifiVis(): Boolean {
        return wifiStatus != Item.WIFI_STATUS_INVISIBLE && wifiStatus != Item.WIFI_STATUS_UNDEFINED
    }

    fun wifiIconRes(): Int {
        return signalResId
    }

    fun batteryVis(): Boolean {
        return batteryResId != Item.ICON_STATUS_INVISIBLE
    }

    fun timeStrVis(): Boolean {
        return timeMillStr.isNotEmpty()
    }

    fun getColor(isWarning: Boolean): Int {
        return ResUtil.getColor(if (isWarning) R.color.font_style_8_2_textColor else R.color.font_style_137_3_textColor)
    }

    fun signalResId(signalResId: Int): Boolean {
        if (this.signalResId == signalResId) return false
        this.signalResId = signalResId
        return true
    }

    fun checkBatteryResId(batteryResId: Int): Boolean {
        if (this.batteryResId == batteryResId) return false
        this.batteryResId = batteryResId
        return true
    }

    fun checkBleVis(bleVis: Boolean): Boolean {
        if (this.bleVis == bleVis) return false
        this.bleVis = bleVis
        return true
    }

    fun checkWifiStatus(wifiStatus: Int): Boolean {
        if (this.wifiStatus == wifiStatus) return false
        this.wifiStatus = wifiStatus
        return true
    }

    fun checkTimeMillStr(timeMillStr: String): Boolean {
        if (this.timeMillStr == timeMillStr) return false
        this.timeMillStr = timeMillStr
        return true
    }

    fun checkPb1Icon(pb1IconIndex: Int): Boolean {
        if (this.pb1IconIndex == pb1IconIndex) return false
        this.pb1IconIndex = pb1IconIndex
        return true
    }

    fun checkPb1Name(pb1Name: String): Boolean {
        if (this.pb1Name == pb1Name) return false
        this.pb1Name = pb1Name
        return true
    }

    fun checkPb2Icon(pb2IconIndex: Int): Boolean {
        if (this.pb2IconIndex == pb2IconIndex) return false
        this.pb2IconIndex = pb2IconIndex
        return true
    }

    fun checkPb2Name(pb2Name: String): Boolean {
        if (this.pb2Name == pb2Name) return false
        this.pb2Name = pb2Name
        return true
    }

    fun checkTem(temStr: String, temWarning: Boolean, temRange: Range): Boolean {
        var result = false
        if (this.temStr != temStr) {
            this.temStr = temStr
            result = true
        }
        if (this.temWarning != temWarning) {
            this.temWarning = temWarning
            result = true
        }
        if (this.temRange.compared(temRange)) {
            result = true
        }
        return result
    }

    fun checkHum(humStr: String, humWarning: Boolean, humRange: Range): Boolean {
        var result = false
        if (this.humStr != humStr) {
            this.humStr = humStr
            result = true
        }
        if (this.humWarning != humWarning) {
            this.humWarning = humWarning
            result = true
        }
        if (this.humRange.compared(humRange)) {
            result = true
        }
        return result
    }

    fun checkTem2(tem2Str: String, tem2Warning: Boolean, tem2Range: Range): Boolean {
        var result = false
        if (this.tem2Str != tem2Str) {
            this.tem2Str = tem2Str
            result = true
        }
        if (this.tem2Warning != tem2Warning) {
            this.tem2Warning = tem2Warning
            result = true
        }
        if (this.tem2Range.compared(tem2Range)) {
            result = true
        }
        return result
    }

    fun isOffline(): Boolean {
        //wifi 和 蓝牙明确显示，两者离线才离线
        return bleVis.isFalse() && (wifiStatus != Item.WIFI_STATUS_ONLINE)
    }
}