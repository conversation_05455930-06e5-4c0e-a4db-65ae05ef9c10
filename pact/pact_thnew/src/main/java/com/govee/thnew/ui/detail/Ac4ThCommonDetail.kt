package com.govee.thnew.ui.detail

import android.os.Bundle
import android.text.TextUtils
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.activity.viewModels
import androidx.appcompat.widget.LinearLayoutCompat
import com.alibaba.android.arouter.facade.annotation.Route
import com.google.android.material.appbar.AppBarLayout
import com.govee.base2home.Constant
import com.govee.base2home.config.Base2homeConfig
import com.govee.base2home.kt.ac.AbsBTRp4EnforceAcHint
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.main.hint.HintLabel
import com.govee.base2home.reform4dbgw.router.RouterRuler
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.temUnit.TemperatureUnitType
import com.govee.base2home.ui.showguide.ShowGuideView.Companion.addShowGuide
import com.govee.base2home.util.CaughtRunnable
import com.govee.base2home.util.IntroUtils.showDewPointIntro
import com.govee.base2home.util.IntroUtils.showVpdIntro
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.setVisibility
import com.govee.base2kt.ext.toast
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2kt.utils.IntentUtils
import com.govee.base2light.light.EventBleTimeMillsUpdate
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.ChartController
import com.govee.base2newth.chart.ChartVisibleConfig
import com.govee.base2newth.chart.DataGroup
import com.govee.base2newth.chart.IntervalType
import com.govee.base2newth.chart.TimeUtil
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.db.DbData4ThVm
import com.govee.base2newth.deviceitem.Ext4Gw
import com.govee.base2newth.deviceitem.GatewayInfo
import com.govee.base2newth.deviceitem.LastData
import com.govee.base2newth.other.Config4DeviceChartOrder
import com.govee.base2newth.other.Config4LastThValue.Companion.getConfig
import com.govee.base2newth.other.Event4DeleteThFromGw
import com.govee.base2newth.other.SyncTemUnitUtil.syncTemUnit
import com.govee.ble.BleController
import com.govee.home.account.config.AccountConfig
import com.govee.thnew.add.AddInfo
import com.govee.thnew.config.ThConfig4Detail
import com.govee.thnew.config.ThConfig4Setting
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.databinding.ThnewAc4DetailOldBinding
import com.govee.thnew.databinding.ThnewChartOld4DewPointBinding
import com.govee.thnew.databinding.ThnewChartOld4HumBinding
import com.govee.thnew.databinding.ThnewChartOld4Pm25Binding
import com.govee.thnew.databinding.ThnewChartOld4TemBinding
import com.govee.thnew.databinding.ThnewChartOld4VpdBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.compare.Ac4ThMultiDevCompare
import com.govee.thnew.ui.compare.Ac4ThSingleDevCompare
import com.govee.thnew.ui.op.BleOpManager
import com.govee.thnew.ui.setting.Ac4ThCommonSetting
import com.govee.thnew.ui.setting.Ac4ThCommonSettingNew
import com.govee.thnew.update.ota4frk.Ac4UpdateByFrk
import com.govee.thnew.update.ota4v1.Ac4UpdateV1
import com.govee.thnew.update.ota4v2.Ac4UpdateV2
import com.govee.ui.R
import com.govee.ui.component.NotifyHintView
import com.govee.ui.dialog.BleUpdateHintDialog
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.ext.isFalse
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe

/**
 * <AUTHOR>
 * @date created on 2024/2/22
 * @description 温湿度计-->通用的详情页
 */
@Route(path = RouterRuler.ROUTER_PATH_4_TH_DETAIL_PAGE_OLD)
class Ac4ThCommonDetail : AbsBTRp4EnforceAcHint<ThnewAc4DetailOldBinding>() {

    /**
     * 实时信息
     */
    private var refreshTime = 0L
    private var realTem = 0
    private var realHum = 0
    private var realPm25 = 0

    /**
     * 数据展示的vm
     */
    private val vm4ChartData: DbData4ThVm by viewModels()

    /**
     * 图表展示控制类
     */
    private lateinit var chartOp4Th: ChartController

    /**
     * 当前选中的图表时间段
     */
    private var selectedDataGroup: DataGroup? = DataGroup.hour

    /**
     * appBarLayout的实时滑动距离
     */
    @Volatile
    private var ablVerticalOffset = 0

    /**
     * 图表控件相关
     */
    private val pm25Binding by lazy {
        ThnewChartOld4Pm25Binding.inflate(layoutInflater)
    }
    private val temBinding by lazy {
        ThnewChartOld4TemBinding.inflate(layoutInflater)
    }
    private val humBinding by lazy {
        ThnewChartOld4HumBinding.inflate(layoutInflater)
    }
    private val dpBinding by lazy {
        ThnewChartOld4DewPointBinding.inflate(layoutInflater)
    }
    private val vpdBinding by lazy {
        ThnewChartOld4VpdBinding.inflate(layoutInflater)
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.thnew.R.id.ac_container
    }

    override fun adapterInsetViewId(): Int {
        return com.govee.thnew.R.id.sp_top_adjust_holder
    }

    override fun layoutId(): Int {
        return com.govee.thnew.R.layout.thnew_ac_4_detail_old
    }

    override fun statusBarLightMode(): Boolean {
        return false
    }

    /**
     * 该设备自身的实时信息
     */
    private fun getDeviceInfo(): AddInfo? {
        Vm4ThOpManager.instance()?.getDeviceInfo()?.let {
            return it
        }
        //这种情况直接退回到设备列表页(长时间置于后台，被销毁后，回到前台重建)
        BaseApplication.getBaseApplication().finishOther(Base2homeConfig.getConfig().mainAcClass)
        return null
    }

    override fun doPreCheck(savedInstanceState: Bundle?) {
        initData()
        initUi()
        initOpClick()
        initObserver()
        //开始加载数据
        updateFresh(true)
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
    }

    private fun initData() {
        var bindExt: AddInfo?
        var gatewayInfo: GatewayInfo?
        bindExt = IntentUtils.parseParcelable<AddInfo>(intent, ThConsV1.KEY_4_ADD_INFO)
        gatewayInfo = IntentUtils.parseParcelable<GatewayInfo>(intent, ThConsV1.KEY_4_GATEWAY_INFO)
        if (bindExt == null) {
            val sku = intent.getStringExtra(Constant.intent_ac_key_sku)
            val device = intent.getStringExtra(Constant.intent_ac_key_device)
            if (!TextUtils.isEmpty(sku) && !TextUtils.isEmpty(device)) {
                DeviceListConfig.read().getDeviceByKey(sku, device)?.let { absDevice ->
                    absDevice.deviceExt?.deviceSettings?.let { settingsStr ->
                        JsonUtil.fromJson(settingsStr, AddInfo::class.java)?.let { ext ->
                            bindExt = ext
                            bindExt.goodsType = absDevice.goodsType
                            bindExt.sku = absDevice.sku
                            bindExt.device = absDevice.device
                            bindExt.bleHardVersion = absDevice.versionHard
                            bindExt.bleSoftVersion = absDevice.versionSoft
                        }
                        JsonUtil.fromJson(settingsStr, Ext4Gw::class.java)?.gatewayInfo?.let { gwInfo ->
                            gatewayInfo = gwInfo
                        }
                    }
                }
            }
        }
        bindExt?.let {
            Vm4ThOpManager.init(it, gatewayInfo, Vm4ThOpManager.INIT_FROM_OLD_DETAIL)
        } ?: run {
            toast(R.string.h721214_other_listen)
            finish()
        }
    }

    private fun initUi() {
        getDeviceInfo()?.let { bindExt ->
            viewBinding.let { vb ->
                //复购按钮
                addShowGuide(this, vb.ivGuideHolder4ThDetail, bindExt.sku, bindExt.device)
                //主+从款sku不显示实时信息
                vb.thRealInfo4ThDetail.setVisibility(!ThConfig4Support.isMainSubDevice(bindExt.goodsType, bindExt.sku))
                //设备名称
                vb.tvTextTitle4ThDetail.text = bindExt.deviceName
                //实时信息展示控件初始化配置
                vb.thRealInfo4ThDetail.init(bindExt.sku, bindExt.device)
            }
            updateTemUnit(TemUnitConfig.read().isTemUnitFah(bindExt.sku))
        }
        setConnectStatus()
        //初始化图表相关配置
        initChartSet()
        //图表排序
        changeChartOrder()
        //展示本地已有温湿度图表数据
        Vm4ThOpManager.instance()?.loadThcdManager?.refreshThChartData(
            "先展示本地已有温湿度图表数据...",
            true
        )
        //只有登录才有导出数据、数据对比等操作
        viewBinding.tvExportData4ThDetail.setVisibility(AccountConfig.read().isHadToken)
        if (!AccountConfig.read().isHadToken) {
            viewBinding.clExportContainer4ThDetail.background = null
            viewBinding.clExportContainer4ThDetail.isEnabled = false
        }
        viewBinding.tvHistoryCompare4ThDetail.setVisibility(AccountConfig.read().isHadToken)
        viewBinding.tvMultiDeviceCompare4ThDetail.setVisibility(AccountConfig.read().isHadToken)
    }

    private var firstTimeToAdjustHeight = true
    override fun onResume() {
        super.onResume()
        //vpd图表展示
        refreshVpdView()
        //露点图表展示
        refreshDewPView()
        if (!firstTimeToAdjustHeight) {
            return
        }
        firstTimeToAdjustHeight = false
        viewBinding.let { vb ->
            vb.root.postDelayed({
                //展示内容未超出一屏高度，则不可滑动
                val realBottom = vb.rlTopContentContainer4ThDetail.bottom + vb.ablTopContentContainer4ThDetail.bottom + vb.spBottomPadding4ThDetail.bottom
                if (realBottom - vb.acContainer.bottom < 0) {
                    val ablLp = vb.thRealInfo4ThDetail.layoutParams as AppBarLayout.LayoutParams
                    ablLp.scrollFlags = AppBarLayout.LayoutParams.SCROLL_FLAG_NO_SCROLL
                    vb.thRealInfo4ThDetail.layoutParams = ablLp
                }
            }, 100)
        }
    }

    /**
     * 设置连接状态
     */
    private fun setConnectStatus() {
        getDeviceInfo()?.let { bindExt ->
            val needConnectStatus = ThConfig4Detail.needConnectStatus(bindExt.goodsType, bindExt.sku)
            if (needConnectStatus) {
                Vm4ThOpManager.instance()?.let { vm4Th ->
                    viewBinding.let { vb ->
                        if (vm4Th.isBindGateway() && vm4Th.getGateInfo()?.isIotOnLine.isTrue()) {
                            ResUtil.setImageResource(vb.ivConnectStatusIcon4ThDetail, R.mipmap.new_control_icon_wifi_white)
                            vb.tvConnectStatusText4ThDetail.text = ResUtil.getString(R.string.text_4_connect_gateway)
                            vb.tvConnectStatusText4ThDetail.isSelected = false
                        } else if (vm4Th.hasBleConnected()) {
                            ResUtil.setImageResource(vb.ivConnectStatusIcon4ThDetail, R.mipmap.new_control_icon_bluetooch_white)
                            vb.tvConnectStatusText4ThDetail.text = ResUtil.getString(R.string.text_4_connect_ble)
                            vb.tvConnectStatusText4ThDetail.isSelected = false
                        } else {
                            ResUtil.setImageResource(vb.ivConnectStatusIcon4ThDetail, R.mipmap.new_control_icon_loose_red)
                            vb.tvConnectStatusText4ThDetail.text = ResUtil.getString(R.string.text_4_disconnect)
                            vb.tvConnectStatusText4ThDetail.isSelected = true
                        }
                    }
                }
            }
            viewBinding.gpStatusContainer4ThDetail.setVisibility(needConnectStatus)
        }
    }

    /**
     * 更新图表相关配置
     */
    private fun initChartSet() {
        //图表初始化相关设置
        chartOp4Th = ChartController(this).apply {
            setChartListener(object : ChartController.ChartListener {
                override fun beScale() {
                    viewBinding.spvChartSelectPeriod4ThDetail.clearSelect()
                }

                override fun timeChange(startTimeStamp: Long, endTimeStamp: Long, intervalType: IntervalType) {
                    updateTime(startTimeStamp, endTimeStamp, intervalType)
                }
            })
        }
        //图表相关试图初始化
        getDeviceInfo()?.let { bindExt ->
            val pointNumArr = ThConfig4Detail.getChartValueDecimalDigits(bindExt.goodsType, bindExt.sku)
            //pm25
            pm25Binding.ttcPm25Chart4ThDetail.setChart(chartOp4Th)
            pm25Binding.ttcPm25Chart4ThDetail.setPointValue(pointNumArr[0])
            //温度
            temBinding.ttcTemChart4ThDetail.setDeviceInfo(bindExt.goodsType, bindExt.sku, bindExt.bleSoftVersion, bindExt.bleHardVersion)
            temBinding.ttcTemChart4ThDetail.setChart(chartOp4Th)
            temBinding.ttcTemChart4ThDetail.setPointValue(pointNumArr[1])
            //湿度
            humBinding.ttcHumChart4ThDetail.setChart(chartOp4Th)
            humBinding.ttcHumChart4ThDetail.setPointValue(pointNumArr[2])
            //露点
            dpBinding.ttcDpChart4ThDetail.setChart(chartOp4Th)
            dpBinding.ttcDpChart4ThDetail.setPointValue(pointNumArr[3])
            //VPD
            vpdBinding.ttcVpdChart4ThDetail.setChart(chartOp4Th)
            vpdBinding.ttcVpdChart4ThDetail.setPointValue(pointNumArr[4])
        }
        //告警配置
        updateChartWarnSet()
    }

    private fun initOpClick() {
        viewBinding.let { vb ->
            vb.ivBackBtn4ThDetail.clickDelay {
                onBackPressed()
            }
            //跳转到柱状图表页
            vb.ivChartBtn4ThDetail.clickDelay {
                getDeviceInfo()?.let { bindExt ->
                    if (ThConfig4Detail.useNewDetailPage(bindExt.goodsType, bindExt.sku)) {
                        JumpUtil.jump(this, Ac4ThSquareChartNew::class.java, Bundle().apply {
                            this.putParcelable(ThConsV1.KEY_4_ADD_INFO, bindExt)
                        })
                    } else {
                        Ac4ThSquareChart.jumpToSquareChart(this, bindExt, false)
                    }
                }
            }
            //跳转到设置页
            vb.ivSettingBtn4ThDetail.clickDelay {
                getDeviceInfo()?.let { bindExt ->
                    if (ThConfig4Setting.useNewSettingPage(bindExt.goodsType, bindExt.sku)) {
                        JumpUtil.jump(this, Ac4ThCommonSettingNew::class.java)
                    } else {
                        JumpUtil.jump(this, Ac4ThCommonSetting::class.java)
                    }
                }
            }
            //切换图表时间段相关
            getDeviceInfo()?.let { bindExt ->
                vb.spvChartSelectPeriod4ThDetail.setSelectListener(bindExt.sku) { type, dataGroup ->
                    if (type != null) {
                        chartOp4Th.intervalType = type
                    }
                    selectedDataGroup = dataGroup
                }
            }

            vb.thstvContainer4ThDetail.setOnSwitchTimeListener { isToNext: Boolean ->
                chartOp4Th.setOneStage(isToNext)
            }
            //强升级提示栏点击
            vb.llLowVersionRemind4ThDetail.clickDelay {
                toUpgradePage()
            }
            //导出数据
            vb.clExportContainer4ThDetail.clickDelay {
                Ac4ThExportData.jump2ExportData(this)
            }
            //单设备数据对比
            vb.tvHistoryCompare4ThDetail.clickDelay {
                getDeviceInfo()?.let { bindExt ->
                    Ac4ThSingleDevCompare.jump2SingleDevCompare(this@Ac4ThCommonDetail, JsonUtil.toJson(bindExt))
                }
            }
            //多设备数据对比
            vb.tvMultiDeviceCompare4ThDetail.clickDelay {
                getDeviceInfo()?.let { bindExt ->
                    Ac4ThMultiDevCompare.jump2MultiDevCompare(this@Ac4ThCommonDetail, JsonUtil.toJson(bindExt))
                }
            }
            vb.ablTopContentContainer4ThDetail.addOnOffsetChangedListener { _, verticalOffset -> ablVerticalOffset = verticalOffset }
        }
        //显/隐露点、vpd相关
        dpBinding.ivDpSwitch4ThDetail.clickDelay {
            getDeviceInfo()?.let {
                val selected: Boolean = !dpBinding.ivDpSwitch4ThDetail.isSelected
                ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_DEW_P, selected)
                refreshDewPView()
            }
        }
        dpBinding.ivDpIntroIcon4ThDetail.clickDelay {
            getDeviceInfo()?.let {
                showDewPointIntro(this, it.sku)
            }
        }
        dpBinding.ivDpIntroIcon14ThDetail.clickDelay {
            getDeviceInfo()?.let {
                showDewPointIntro(this, it.sku)
            }
        }
        vpdBinding.ivVpdSwitch4ThDetail.clickDelay {
            getDeviceInfo()?.let {
                val selected: Boolean = !vpdBinding.ivVpdSwitch4ThDetail.isSelected
                ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_VPD, selected)
                refreshVpdView()
            }
        }
        vpdBinding.ivVpdIntroIcon4ThDetail.clickDelay {
            getDeviceInfo()?.let {
                showVpdIntro(this, it.sku)
            }
        }
        vpdBinding.ivVpdIntroIcon14ThDetail.clickDelay {
            getDeviceInfo()?.let {
                showVpdIntro(this, it.sku)
            }
        }
        //切换温度单位
        temBinding.ivTemUnitIcon4ThDetail.clickDelay {
            getDeviceInfo()?.let { bindExt ->
                val fahOpen = !TemUnitConfig.read().isTemUnitFah(bindExt.sku)
                TemUnitConfig.read().setTemUnit(bindExt.sku, if (fahOpen) TemperatureUnitType.Fahrenheit else TemperatureUnitType.Celsius)
                updateTemUnit(fahOpen)
                //同步温度单位到服务端
                syncTemUnit(Transactions().createTransaction(), bindExt.sku, fahOpen)
            }
        }
    }

    private fun initObserver() {
        Vm4ThOpManager.instance()?.let { vm ->
            vm.settingOpManager.ld4UpdateDeviceName.observe(this) {
                if (!hasCreated) {
                    return@observe
                }
                getDeviceInfo()?.let { bindExt ->
                    if (it.first == bindExt.getKey()) {
                        viewBinding.tvTextTitle4ThDetail.text = it.second
                    }
                }
            }
            vm.ld4ConnectStatusChange.observe(this) {
                viewBinding.nhvBleEnable4ThDetail.let { v4BleEnable ->
                    if (it.first == BleOpManager.BLE_UNABLE) {
                        v4BleEnable.updateHint(HintLabel(ResUtil.getString(R.string.h5072_bluetooth_unable_detail_main_des), NotifyHintView.hint_type_notification_showing))
                    } else {
                        v4BleEnable.hide()
                    }
                }
                setConnectStatus()
            }
            vm.ld4RealTemHum.observe(this) {
                val thpInfo = it.first
                refreshTime = it.second
                realTem = thpInfo.first
                realHum = thpInfo.second
                realPm25 = thpInfo.third
                //更新实时数据
                viewBinding.thRealInfo4ThDetail.updateRealInfo(refreshTime, realTem, realHum, realPm25)
            }
            vm.ld4ChangeTemUnit.observe(this) {
                if (it) {
                    getDeviceInfo()?.let { bindExt ->
                        val fahOpen = TemUnitConfig.read().isTemUnitFah(bindExt.sku)
                        updateTemUnit(fahOpen)
                    }
                }
            }
            vm.loadThcdManager.ld4ShowChart.observe(this) {
                if (it.isEmpty()) {
                    chartOp4Th.updateRealData(it)
                    updateSyncTime(-1)
                } else {
                    vm4ChartData.setThData(it, true)
                }
            }
            vm.loadThcdManager.ld4BleLoadThCdProgress.observe(this) {
                getDeviceInfo()?.let { bindExt ->
                    if (bindExt.sku == it.first.first && bindExt.device == it.first.second) {
                        when (it.second.first) {
                            Vm4ThOpManager.RC_FROM_DETAIL,
                            Vm4ThOpManager.RC_FROM_COMPARE_DATA,
                                -> {
                                updateSyncDes(it.second.second)
                            }

                            else -> {}
                        }
                    }
                }
            }
            vm.loadThcdManager.ld4LoadThCdStep.observe(this) {
                getDeviceInfo()?.let { bindExt ->
                    it[Pair(bindExt.sku, bindExt.device)]?.let { thcdInfo ->
                        when (thcdInfo.second.first) {
                            Vm4ThOpManager.RC_FROM_DETAIL,
                            Vm4ThOpManager.RC_FROM_COMPARE_DATA,
                                -> {
                                if (thcdInfo.second.second == Vm4ThOpManager.THCD_4_IS_LOAD_FINISH) {
                                    if (ThConfig4Support.isMainSubDevice(bindExt.goodsType, bindExt.sku)) {
                                        //加载子设备失败，须提示子设备靠近主设备--2s
                                        if (BleController.getInstance().isBlueToothOpen && bindExt.groupOrder > 0 && thcdInfo.second.third?.first.isFalse()) {
                                            viewBinding.let { vb ->
                                                vb.pbRefreshCycle4ThDetail.setVisibility(false)
                                                vb.tvRefreshDes4ThDetail.text = ResUtil.getString(R.string.th_multi_ble_close_main_device_hint)
                                            }
                                            window.decorView.postDelayed(object : CaughtRunnable() {
                                                override fun runSafe() {
                                                    updateFresh(false)
                                                }
                                            }, 2000L)
                                        } else {
                                            updateFresh(false)
                                        }
                                    } else {
                                        updateFresh(false)
                                    }
                                }
                            }

                            else -> {}
                        }
                    }
                }
            }
            vm.ld4UpgradeVersion.observe(this) {
                when (it.first) {
                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_FIRST_REMIND -> {
                        getDeviceInfo()?.let { bindExt ->
                            BleUpdateHintDialog.showDialog(this, bindExt.sku, {
                                toUpgradePage()
                            }, this.javaClass.name)
                        }
                        //再执行升级提示红点逻辑
                        Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value = Pair(Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND, it.second)
                    }

                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND -> {
                        //显示升级提示红点
                        it.second?.let { vsInfo ->
                            upgradeRemindSetting(showFlag = true, forceRemind = ThConsV1.needUpdateFirmwareVersion(vsInfo.sku, vsInfo.curVersionSoft))
                        }
                    }

                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_FINISH -> {
                        //升级成功后，会断开连接，故须重连
                        Vm4ThOpManager.instance()?.toConnectBle()
                        //隐藏升级提示
                        upgradeRemindSetting(showFlag = false, forceRemind = false)
                    }

                    else -> {
                        upgradeRemindSetting(showFlag = false, forceRemind = false)
                    }
                }
            }
            //设置页更新告警和校准时会影响到图表展示
            vm.ld4UpdateDeviceInfo.observe(this) {
                when (it.second) {
                    Vm4ThOpManager.UPDATE_4_WARNING -> {
                        updateChartWarnSet()
                    }

                    Vm4ThOpManager.UPDATE_4_CALIBRATION -> {
                        updateChartWarnSet()
                        //更新实时数据
                        viewBinding.thRealInfo4ThDetail.updateRealInfo(refreshTime, realTem, realHum, realPm25)
                    }

                    else -> {}
                }
            }
        }
        //给vm4Data加入生命周期监听
        lifecycle.addObserver(vm4ChartData)
        vm4ChartData.thDataLiveData.observe(this) {
            val notEmpty = it.isNotEmpty()
            if (notEmpty) {
                chartOp4Th.updateRealData(it)
                updateSyncTime(it.last().time)
            }
            viewBinding.ivChartBtn4ThDetail.visibleByBoolean(notEmpty)
        }
    }

    /**
     * 升级提示设置
     */
    private fun upgradeRemindSetting(showFlag: Boolean, forceRemind: Boolean) {
        viewBinding.let { vb ->
            vb.vVersionFlag4ThDetail.setVisibility(showFlag)
            vb.llLowVersionRemind4ThDetail.setVisibility(forceRemind)
        }
    }

    //下拉刷新相关--------------------------------------start--------------------------------------
    private var needShowFresh = false
    private var lastStartMoveY = 0
    private var lastDownY = 0
    private var fullFreshShow = false

    /**
     * 更新下拉刷新时的进度文案
     */
    private fun updateSyncDes(percent: Int) {
        var percentStr = "$percent%"
        percentStr = String.format(getString(R.string.h5072_fresh_des_syncing), percentStr)
        viewBinding.tvRefreshDes4ThDetail.text = percentStr
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (fullFreshShow) return super.dispatchTouchEvent(ev)
        val action = ev.action
        if (MotionEvent.ACTION_DOWN == action) {
            lastDownY = ev.rawY.toInt()
        } else if (MotionEvent.ACTION_MOVE == action) {
            val scrollY: Int = viewBinding.nsvScrollContainer4ThDetail.scrollY
            val rawY = ev.rawY.toInt()
            //向下滑动，才需要做逻辑处理
            val pullDown: Boolean = rawY - lastDownY > ThConsV1.VERTICAL_DIR_PULL_DOWN
            val ablRecover = ablVerticalOffset == 0
            if (scrollY == 0 && !needShowFresh && pullDown && ablRecover) {
                lastStartMoveY = rawY
                needShowFresh = true
            }
            if (needShowFresh) {
                updateFreshHeight(rawY)
                return true
            }
        } else if ((MotionEvent.ACTION_UP == action || MotionEvent.ACTION_CANCEL == action) && needShowFresh) {
            checkFreshHeight()
            needShowFresh = false
            lastDownY = 0
        }
        return super.dispatchTouchEvent(ev)
    }

    private fun updateFreshHeight(rawY: Int) {
        viewBinding.run {
            var moreDis: Int = rawY - lastStartMoveY
            moreDis = 0.coerceAtLeast(moreDis)
            llcRefreshContainer4ThDetail.layoutParams?.let {
                it.height = moreDis
                llcRefreshContainer4ThDetail.layoutParams = it
            }
        }
    }

    private fun checkFreshHeight() {
        viewBinding.run {
            val maxHeight: Int = (AppUtil.getScreenWidth() * ThConsV1.FRESH_HEIGHT_PERCENT_SCREEN_WIDTH).toInt()
            val lp: ViewGroup.LayoutParams = llcRefreshContainer4ThDetail.layoutParams
            val height = lp.height
            //展示/隐藏
            updateFresh(height >= maxHeight)
        }
    }

    private fun updateFresh(fullFreshShow: Boolean) {
        viewBinding.let { vb ->
            <EMAIL> = fullFreshShow
            vb.pbRefreshCycle4ThDetail.setVisibility(true)
            val lp: ViewGroup.LayoutParams = vb.llcRefreshContainer4ThDetail.layoutParams
            if (fullFreshShow) {
                lp.height = (AppUtil.getScreenWidth() * ThConsV1.FRESH_HEIGHT_PERCENT_SCREEN_WIDTH).toInt()
            } else {
                lp.height = 0
            }
            vb.llcRefreshContainer4ThDetail.layoutParams = lp
            if (fullFreshShow) {
                //开始加载数据:先服务端，再同步设备端的
                Vm4ThOpManager.instance()?.loadThcdManager?.toLoadThcdBySingle(this@Ac4ThCommonDetail)
            } else {
                vb.tvRefreshDes4ThDetail.setText(R.string.fresh_des_loading)
            }
        }
    }
    //下拉刷新相关--------------------------------------end--------------------------------------

    /**
     * 更新图表的展示时间段
     */
    private fun updateTime(startTimeStamp: Long, endTimeStamp: Long, intervalType: IntervalType) {
        val calibration = selectedDataGroup?.let {
            //在默认标签下，需要格式时间起点的一致
            TimeUtil.calibration(startTimeStamp, endTimeStamp, it)
        } ?: longArrayOf(startTimeStamp, endTimeStamp)
        val strByMDHMStart: String
        val strByMDHMEnd: String
        if (IntervalType.year_1_month == intervalType) {
            strByMDHMStart = TimeFormatM.getInstance().formatTimeToYM(calibration[0])
            strByMDHMEnd = TimeFormatM.getInstance().formatTimeToYM(calibration[1])
        } else {
            strByMDHMStart = TimeFormatM.getInstance().formatTimeToHMMD(calibration[0])
            strByMDHMEnd = TimeFormatM.getInstance().formatTimeToHMMD(calibration[1])
        }
        //更新展示时间
        pm25Binding.tvPm25StartTime4ThDetail.text = strByMDHMStart
        pm25Binding.tvPm25EndTime4ThDetail.text = strByMDHMEnd
        temBinding.tvTemStartTime4ThDetail.text = strByMDHMStart
        temBinding.tvTemEndTime4ThDetail.text = strByMDHMEnd
        humBinding.tvHumStartTime4ThDetail.text = strByMDHMStart
        humBinding.tvHumEndTime4ThDetail.text = strByMDHMEnd
        dpBinding.tvDpStartTime4ThDetail.text = strByMDHMStart
        dpBinding.tvDpEndTime4ThDetail.text = strByMDHMEnd
        vpdBinding.tvVpdStartTime4ThDetail.text = strByMDHMStart
        vpdBinding.tvVpdEndTime4ThDetail.text = strByMDHMEnd
    }

    /**
     * 更新最后的数据同步时间点
     */
    private fun updateSyncTime(syncTime: Long) {
        getDeviceInfo()?.let {
            val syncTimeStr = if (syncTime <= 0) {
                ""
            } else {
                String.format(ResUtil.getString(R.string.h5072_last_sync_time), TimeFormatM.getInstance().formatTimeToHMYMD(syncTime))
            }
            viewBinding.let { vb ->
                vb.tvSyncTime4ThDetail.text = syncTimeStr
                vb.tvSyncTime4ThDetail.setVisibility(!TextUtils.isEmpty(syncTimeStr))
            }
        }
    }

    /**
     * 更新图表告警配置
     */
    private fun updateChartWarnSet() {
        getDeviceInfo()?.let {
            val warnRange = WarnConfig.read().queryWarningRangeByKey(it.sku, it.device)
            if (warnRange != null) {
                temBinding.ttcTemChart4ThDetail.updateWarn(warnRange.temMin, warnRange.temMax)
                humBinding.ttcHumChart4ThDetail.updateWarn(warnRange.humMin, warnRange.humMax)
                temBinding.ttcTemChart4ThDetail.updateCali(warnRange.temCali)
                humBinding.ttcHumChart4ThDetail.updateCali(warnRange.humCali)
                //配置处理数据的校准值
                vm4ChartData.setCali(warnRange.temCali, warnRange.humCali)
            } else {
                val defTemWarnRange = ThConfig4Detail.getTemRange(it.goodsType, it.sku, it.bleSoftVersion, it.bleHardVersion)
                temBinding.ttcTemChart4ThDetail.updateWarn(defTemWarnRange.first * 100, defTemWarnRange.second * 100)
                humBinding.ttcHumChart4ThDetail.updateWarn(ThConsV1.HUM_MIN_VALUE * 100, ThConsV1.HUM_MAX_VALUE * 100)
                temBinding.ttcTemChart4ThDetail.updateCali(0)
                humBinding.ttcHumChart4ThDetail.updateCali(0)
                //配置处理数据的校准值
                vm4ChartData.setCali(0, 0)
            }
        }
    }

    /**
     * 更新温度单位的显示
     */
    private fun updateTemUnit(fahOpen: Boolean) {
        temBinding.ivTemUnitIcon4ThDetail.setImageDrawable(ResUtil.getDrawable(if (fahOpen) R.mipmap.new_sensor_setting_switch_fahrenheit else R.mipmap.new_sensor_setting_switch_celsius))
        temBinding.ttcTemChart4ThDetail.setChangeValueShow(fahOpen, if (fahOpen) getString(R.string.tem_unit_fah) else getString(R.string.tem_unit_cel))
        dpBinding.ttcDpChart4ThDetail.setChangeValueShow(fahOpen, if (fahOpen) getString(R.string.tem_unit_fah) else getString(R.string.tem_unit_cel))
    }

    /**
     * 刷新vpd的图表显示
     */
    private fun refreshVpdView() {
        getDeviceInfo()?.let { bindExt ->
            val selected = ChartVisibleConfig.read().getVisible(bindExt.sku, ChartVisibleConfig.CHART_TYPE_VPD)
            val visibility = if (selected) View.VISIBLE else View.GONE
            vpdBinding.tvVpdLabel4ThDetail.visibility = visibility
            vpdBinding.ivVpdSwitch4ThDetail.isSelected = selected
            vpdBinding.ttcVpdChart4ThDetail.visibility = visibility
            vpdBinding.tvVpdStartTime4ThDetail.visibility = visibility
            vpdBinding.tvVpdEndTime4ThDetail.visibility = visibility
            vpdBinding.ivVpdIcon4ThDetail.visibility = if (!selected) View.VISIBLE else View.GONE
            vpdBinding.tvVpdLabelHolder4ThDetail.visibility = if (!selected) View.VISIBLE else View.GONE
            vpdBinding.ivVpdIntroIcon4ThDetail.visibility = if (selected) View.VISIBLE else View.GONE
            vpdBinding.ivVpdIntroIcon14ThDetail.visibility = if (!selected) View.VISIBLE else View.GONE
        }
    }

    /**
     * 刷新露点图表的显示
     */
    private fun refreshDewPView() {
        getDeviceInfo()?.let { bindExt ->
            val selected = ChartVisibleConfig.read().getVisible(bindExt.sku, ChartVisibleConfig.CHART_TYPE_DEW_P)
            val visibility = if (selected) View.VISIBLE else View.GONE
            dpBinding.tvDpLabel4ThDetail.visibility = visibility
            dpBinding.ivDpSwitch4ThDetail.isSelected = selected
            dpBinding.ttcDpChart4ThDetail.visibility = visibility
            dpBinding.tvDpStartTime4ThDetail.visibility = visibility
            dpBinding.tvDpEndTime4ThDetail.visibility = visibility
            dpBinding.ivDpIcon4ThDetail.visibility = if (!selected) View.VISIBLE else View.GONE
            dpBinding.tvDpLabelHolder4ThDetail.visibility = if (!selected) View.VISIBLE else View.GONE
            dpBinding.ivDpIntroIcon4ThDetail.visibility = if (selected) View.VISIBLE else View.GONE
            dpBinding.ivDpIntroIcon14ThDetail.visibility = if (!selected) View.VISIBLE else View.GONE
        }
    }

    /**
     * 跳转至升级页面
     */
    private fun toUpgradePage() {
        getDeviceInfo()?.let { bindExt ->
            Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value?.let { upgradePair ->
                upgradePair.second?.run {
                    val upgradeWay = ThConfig4Setting.getUpgradeWay(bindExt.goodsType, bindExt.sku, bindExt.bleHardVersion)
                    SafeLog.i("xiaobing") { "Ac4ThCommonDetail--toUpgradePage-->key=->${bindExt.getKey()},升级方式=->${upgradeWay}" }
                    when (upgradeWay) {
                        ThConfig4Setting.UPGRADE_WAY_4_FRK -> {
                            Ac4UpdateByFrk.jump2OtaUpdate(this@Ac4ThCommonDetail, bindExt.sku, bindExt.deviceName, this)
                        }

                        ThConfig4Setting.UPGRADE_WAY_4_V1 -> {
                            Ac4UpdateV1.jump2UpdateAc(this@Ac4ThCommonDetail, bindExt.sku, bindExt.deviceName, this)
                        }

                        ThConfig4Setting.UPGRADE_WAY_4_V2 -> {
                            Ac4UpdateV2.jump2UpdateAc(this@Ac4ThCommonDetail, bindExt.sku, bindExt.deviceName, this)
                        }

                        else -> {}
                    }
                }
            }
        }
    }

    /**
     * 根据图表排序展示图表
     *
     * @param chartOrder 备注：A->pm2.5，B->温度，C->湿度，D->露点，E->vpd
     */
    private fun changeChartOrder(chartOrder: ArrayList<String>? = null) {
        getDeviceInfo()?.let { bindExt ->
            var usedChartOrder = chartOrder
            if (chartOrder.isNullOrEmpty()) {
                usedChartOrder = Config4DeviceChartOrder.getConfig().getChartOrder(bindExt.sku, bindExt.device) ?: arrayListOf(ThConsV1.PM25, ThConsV1.TEM, ThConsV1.HUM, ThConsV1.DP, ThConsV1.VPD)
            }
            viewBinding.let {
                it.flContent1Container4ThDetail.removeAllViews()
                it.flContent2Container4ThDetail.removeAllViews()
                it.flContent3Container4ThDetail.removeAllViews()
                it.flContent4Container4ThDetail.removeAllViews()
                it.flContent5Container4ThDetail.removeAllViews()
            }
            usedChartOrder.let {
                fillToContainer(0, usedChartOrder[0])
                fillToContainer(1, usedChartOrder[1])
                fillToContainer(2, usedChartOrder[2])
                fillToContainer(3, usedChartOrder[3])
                fillToContainer(4, usedChartOrder[4])
            }
        }
    }

    private fun fillToContainer(order: Int, type: String) {
        viewBinding.let { vb ->
            //排序填充
            val container = when (order) {
                0 -> vb.flContent1Container4ThDetail
                1 -> vb.flContent2Container4ThDetail
                2 -> vb.flContent3Container4ThDetail
                3 -> vb.flContent4Container4ThDetail
                else -> {
                    vb.flContent5Container4ThDetail
                }
            }
            when (type) {
                ThConsV1.PM25 -> {
                    getDeviceInfo()?.let { bindExt ->
                        container.setVisibility(ThConfig4Support.supportPm25(bindExt.goodsType, bindExt.sku))
                        if (ThConfig4Support.supportPm25(bindExt.goodsType, bindExt.sku)) {
                            container.addView(pm25Binding.root)
                        }
                    }
                }

                ThConsV1.TEM -> {
                    container.setVisibility(true)
                    container.addView(temBinding.root)
                }

                ThConsV1.HUM -> {
                    getDeviceInfo()?.let { bindExt ->
                        container.setVisibility(ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku))
                        if (ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku)) {
                            container.addView(humBinding.root)
                        }
                    }
                }

                ThConsV1.DP -> {
                    getDeviceInfo()?.let { bindExt ->
                        container.setVisibility(ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku))
                        if (ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku)) {
                            container.addView(dpBinding.root)
                        }
                    }
                }

                ThConsV1.VPD -> {
                    getDeviceInfo()?.let { bindExt ->
                        container.setVisibility(ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku))
                        if (ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku)) {
                            container.addView(vpdBinding.root)
                        }
                    }
                }

                else -> {}
            }
            if (vb.flContent1Container4ThDetail == container && container.visibility != View.VISIBLE) {
                val lp: LinearLayoutCompat.LayoutParams = vb.flContent2Container4ThDetail.layoutParams as LinearLayoutCompat.LayoutParams
                lp.topMargin = 0
                vb.flContent2Container4ThDetail.layoutParams = lp
            }
        }
    }

    override fun onPause() {
        getDeviceInfo()?.let {
            //保存最新的实时温湿度值
            getConfig().updateLastTh(it.sku, it.device, getLastData())
        }
        super.onPause()
    }

    private fun getLastData(): LastData {
        val lastData = LastData()
        lastData.tem = realTem
        lastData.hum = realHum
        lastData.pm = realPm25
        lastData.lastTime = refreshTime
        return lastData
    }

    override fun onBtPerGrantedOver() {}

    @Subscribe
    fun onEvent4DeleteThFromGw(event: Event4DeleteThFromGw) {
        onBackPressed()
    }

    override fun onDestroy() {
        EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
        Vm4ThOpManager.instance()?.release(Vm4ThOpManager.INIT_FROM_OLD_DETAIL)
        super.onDestroy()
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        Vm4ThOpManager.instance()?.run {
            if (hasBleConnected().isTrue()) {
                val info = getDeviceInfo()
                EventBleTimeMillsUpdate.sendEventBleTimeMillsUpdate(info.sku, info.device)
            }
        }
        super.onBackPressed()
    }
}