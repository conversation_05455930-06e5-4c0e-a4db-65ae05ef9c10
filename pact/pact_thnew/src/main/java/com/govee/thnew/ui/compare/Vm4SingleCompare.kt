package com.govee.thnew.ui.compare

import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.util.TimeFormatM
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.TimeUtil
import com.govee.base2newth.db.DbController
import com.govee.base2newth.db.TemHumPm
import com.govee.ble.BleController
import com.govee.mvvm.base.viewmodel.BaseViewModel
import com.govee.mvvm.globalLaunch
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.ui.R
import com.govee.util.recordUseCount
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.infra.SafeLog
import kotlinx.coroutines.Dispatchers

/**
 * <AUTHOR>
 * @date created on 2024/4/3
 * @description 温湿度计=->单设备图表数据比较的ViewModel
 */
class Vm4SingleCompare : BaseViewModel() {

    /**
     * 设备信息
     */
    private var goodsType = GoodsType.GOODES_TYPE_NO_SUPPORT
    private lateinit var sku: String
    private lateinit var device: String

    /**
     * 两端曲线的起、止时间点
     */
    private var line1StartTime = 0L
    private var line1EndTime = 0L
    private var line2StartTime = 0L
    private var line2EndTime = 0L

    /**
     * 该设备在本地的最新一条数据的时间点
     */
    var lastDataTime = 0L
        private set

    /**
     * 最小的有效时间
     */
    var minValidTime = 0L
        private set

    /**
     * 初始化，锁定设备
     */
    fun init(lifecycleOwner: LifecycleOwner, goodsType: Int, sku: String, device: String) {
        this.goodsType = goodsType
        this.sku = sku
        this.device = device
        //获取最新一条数据的时间点
        lastDataTime = TimeUtil.calibrationTimeByMinutes(System.currentTimeMillis())
        minValidTime = lastDataTime - ThConsV1.ONE_YEAR_MILLIS * 2
        fastSelectTimePeriod(1)
        Vm4ThOpManager.instance()?.loadThcdManager?.ld4LoadThCdStep?.observe(lifecycleOwner) {
            Vm4ThOpManager.instance()?.loadThcdManager?.currFinishDevice?.let { cld ->
                it[cld]?.let { thcdInfo ->
                    if (thcdInfo.second.second == Vm4ThOpManager.THCD_4_IS_LOAD_FINISH) {
                        customSelectTimePeriod(line1StartTime, line1EndTime, line2StartTime, line2EndTime, false)
                        //置空赋值
                        Vm4ThOpManager.instance()?.loadThcdManager?.currFinishDevice = null
                    }
                }
            }
        }
        if (!BleController.getInstance().isBlueToothOpen) {
            toast(R.string.ble_not_open_msg)
        }
    }

    /**
     * 选中时间段后的数据处理结果
     * 备注：first=->line1(内容元组：first:开始时间，second:结束时间，third:图表数据)，second=->line2
     */
    val ld4SelectedTimePeriod by lazy {
        MutableLiveData<Pair<Triple<Long, Long, ArrayList<TemHumPm>>, Triple<Long, Long, ArrayList<TemHumPm>>>>()
    }

    /**
     * 快速选中对比时间段
     */
    fun fastSelectTimePeriod(position: Int) {
        showLoading()
        globalLaunch(Dispatchers.IO) {
            line1EndTime = lastDataTime
            SafeLog.i("xiaobing") { "Vm4SingleCompare--fastSelectTimePeriod-->endTime=->${TimeFormatM.getInstance().formatTimeToHMSYMD(lastDataTime)}" }
            val timeInterval = when (position) {
                //近2小时
                0 -> {
                    //统计
                    recordUseCount(sku, ParamFixedValue.compare_history_time_2_hours)
                    //赋值
                    ThConsV1.ONE_HOUR_MILLIS
                }
                //近2天
                1 -> {
                    //统计
                    recordUseCount(sku, ParamFixedValue.compare_history_time_2_days)
                    //赋值
                    ThConsV1.ONE_DAY_MILLIS
                }
                //近2周
                2 -> {
                    //统计
                    recordUseCount(sku, ParamFixedValue.compare_history_time_2_weeks)
                    //赋值
                    ThConsV1.ONE_WEEK_MILLIS
                }
                //近2月
                3 -> {
                    //统计
                    recordUseCount(sku, ParamFixedValue.compare_history_time_2_months)
                    //赋值
                    ThConsV1.ONE_MONTH_MILLIS
                }
                //近2年
                else -> {
                    //统计
                    recordUseCount(sku, ParamFixedValue.compare_history_time_2_years)
                    //赋值
                    ThConsV1.ONE_YEAR_MILLIS
                }
            }
            line1StartTime = line1EndTime - timeInterval
            line2EndTime = line1StartTime
            line2StartTime = line2EndTime - timeInterval
            val line1Data = arrayListOf<TemHumPm>()
            val line2Data = arrayListOf<TemHumPm>()
            //时间点已被取整，若传该值去查询可能会遗漏首尾点，故开始时间-30s，解释时间+30s,确保首尾点能被查询到
            val halfMinute = 30 * 1000L
            val searchStartTimeLin1 = line1StartTime - halfMinute
            val searchEndTimeLin1 = line1EndTime + halfMinute
            val searchStartTimeLin2 = line2StartTime - halfMinute
            val searchEndTimeLin2 = line1StartTime + halfMinute
            if (ThConsV1.needUsedThp(goodsType, sku)) {
                line1Data.addAll(DbController.queryAllData4Thp(sku, device, searchStartTimeLin1, searchEndTimeLin1))
                line2Data.addAll(DbController.queryAllData4Thp(sku, device, searchStartTimeLin2, searchEndTimeLin2))
            } else {
                val line1DataTh = DbController.queryAllData(sku, device, searchStartTimeLin1, searchEndTimeLin1)
                val line2DataTh = DbController.queryAllData(sku, device, searchStartTimeLin2, searchEndTimeLin2)
                //将TemHum对像转换为TemHumPm
                for (temHum in line1DataTh) {
                    line1Data.add(temHum.thp)
                }
                for (temHum in line2DataTh) {
                    line2Data.add(temHum.thp)
                }
                //释放资源
                line1DataTh.clear()
                line2DataTh.clear()
            }
            val timeInterval4Lines = line1StartTime - line2StartTime
            var fvt4Line1 = if (line1Data.isEmpty()) {
                if (line2Data.isEmpty()) {
                    0
                } else {
                    line2Data.first().time + timeInterval4Lines
                }
            } else {
                if (line2Data.isEmpty()) {
                    line1Data.first().time
                } else {
                    (line1Data.first().time).coerceAtMost(line2Data.first().time + timeInterval4Lines)
                }
            }
            fvt4Line1 = fvt4Line1.coerceAtLeast(line1StartTime)
            val fvt4Line2 = fvt4Line1 - timeInterval4Lines
            //至少一条曲线有数据
            if (fvt4Line1 > 0) {
                if (line1Data.isEmpty() || line1Data.first().time > fvt4Line1) {
                    line1Data.add(0, TemHumPm(0, 0, 0, fvt4Line1, TemHumPm.FROM_TYPE_INVALID))
                }
                if (line1Data.isEmpty() || line1Data.last().time < line1EndTime) {
                    line1Data.add(TemHumPm(0, 0, 0, line1EndTime, TemHumPm.FROM_TYPE_INVALID))
                }
                if (line2Data.isEmpty() || line2Data.first().time > fvt4Line2) {
                    line2Data.add(0, TemHumPm(0, 0, 0, fvt4Line2, TemHumPm.FROM_TYPE_INVALID))
                }
                if (line2Data.isEmpty() || line2Data.last().time < line2EndTime) {
                    line2Data.add(TemHumPm(0, 0, 0, line2EndTime, TemHumPm.FROM_TYPE_INVALID))
                }
            }
            ld4SelectedTimePeriod.postValue(Pair(Triple(line1StartTime, line1EndTime, line1Data), Triple(line2StartTime, line2EndTime, line2Data)))
            Vm4ThOpManager.instance()?.loadThcdManager?.toLoadThcdBySingle(
                null,
                Vm4ThOpManager.RC_FROM_COMPARE_DATA,
                timeRange4Compare = Pair(line2StartTime, line1EndTime)
            )
        }
    }

    /**
     * 自定义选择对比时间段
     */
    fun customSelectTimePeriod(line1StartTime: Long, line1EndTime: Long, line2StartTime: Long, line2EndTime: Long, isChangeLine1: Boolean, needToLoading: Boolean = false) {
        SafeLog.i("xiaobing") { "Vm4SingleCompare--customSelectTimePeriod-->..." }
        showLoading()
        globalLaunch(Dispatchers.IO) {
            this@Vm4SingleCompare.line1StartTime = line1StartTime
            this@Vm4SingleCompare.line1EndTime = line1EndTime
            this@Vm4SingleCompare.line2StartTime = line2StartTime
            this@Vm4SingleCompare.line2EndTime = line2EndTime
            val timeInterval = line1EndTime - line1StartTime
            val line2UsedEndTime = if (isChangeLine1) {
                line1StartTime
            } else {
                line2EndTime
            }
            val line2UsedStartTime = if (isChangeLine1) {
                line2UsedEndTime - timeInterval
            } else {
                line2StartTime
            }
            val line1Data = arrayListOf<TemHumPm>()
            val line2Data = arrayListOf<TemHumPm>()
            //时间点已被取整，若传该值去查询可能会遗漏首尾点，故开始时间-30s，结束时间+30s,确保首尾点能被查询到
            val halfMinute = 30 * 1000L
            val searchStartTimeLine1 = line1StartTime - halfMinute
            val searchEndTimeLine1 = line1EndTime + halfMinute
            val searchStartTimeLine2 = line2UsedStartTime - halfMinute
            val searchEndTimeLine2 = line2UsedEndTime + halfMinute
            if (ThConsV1.needUsedThp(goodsType, sku)) {
                line1Data.addAll(DbController.queryAllData4Thp(sku, device, searchStartTimeLine1, searchEndTimeLine1))
                line2Data.addAll(DbController.queryAllData4Thp(sku, device, searchStartTimeLine2, searchEndTimeLine2))
            } else {
                val line1DataTh = DbController.queryAllData(sku, device, searchStartTimeLine1, searchEndTimeLine1)
                val line2DataTh = DbController.queryAllData(sku, device, searchStartTimeLine2, searchEndTimeLine2)
                //将TemHum对像转换为TemHumPm
                for (temHum in line1DataTh) {
                    line1Data.add(temHum.thp)
                }
                for (temHum in line2DataTh) {
                    line2Data.add(temHum.thp)
                }
                //释放资源
                line1DataTh.clear()
                line2DataTh.clear()
            }
            val timeInterval4Lines = line1StartTime - line2StartTime
            var fvt4Line1 = if (line1Data.isEmpty()) {
                if (line2Data.isEmpty()) {
                    0
                } else {
                    line2Data.first().time + timeInterval4Lines
                }
            } else {
                if (line2Data.isEmpty()) {
                    line1Data.first().time
                } else {
                    (line1Data.first().time).coerceAtMost(line2Data.first().time + timeInterval4Lines)
                }
            }
            fvt4Line1 = fvt4Line1.coerceAtLeast(line1StartTime)
            val fvt4Line2 = fvt4Line1 - timeInterval4Lines
            SafeLog.i("xiaobing") { "Vm4SingleCompare--customSelectTimePeriod-->fvt4Line1=->${TimeFormatM.getInstance().formatTimeToHMYMD(fvt4Line1)},fvt4Line2=->${TimeFormatM.getInstance().formatTimeToHMYMD(fvt4Line2)}" }
            //至少一条曲线有数据
            if (fvt4Line1 > 0) {
                if (line1Data.isEmpty() || line1Data.first().time > fvt4Line1) {
                    line1Data.add(0, TemHumPm(0, 0, 0, fvt4Line1, TemHumPm.FROM_TYPE_INVALID))
                }
                if (line1Data.isEmpty() || line1Data.last().time < line1EndTime) {
                    line1Data.add(TemHumPm(0, 0, 0, line1EndTime, TemHumPm.FROM_TYPE_INVALID))
                }
                if (line2Data.isEmpty() || line2Data.first().time > fvt4Line2) {
                    line2Data.add(0, TemHumPm(0, 0, 0, fvt4Line2, TemHumPm.FROM_TYPE_INVALID))
                }
                if (line2Data.isEmpty() || line2Data.last().time < line2UsedEndTime) {
                    line2Data.add(TemHumPm(0, 0, 0, line2UsedEndTime, TemHumPm.FROM_TYPE_INVALID))
                }
            }
            ld4SelectedTimePeriod.postValue(Pair(Triple(line1StartTime, line1EndTime, line1Data), Triple(line2UsedStartTime, line2UsedEndTime, line2Data)))
            if (needToLoading) {
                Vm4ThOpManager.instance()?.loadThcdManager?.toLoadThcdBySingle(
                    null,
                    Vm4ThOpManager.RC_FROM_COMPARE_DATA,
                    timeRange4Compare = Pair(line2UsedStartTime, line1EndTime)
                )
            }
        }
    }
}
