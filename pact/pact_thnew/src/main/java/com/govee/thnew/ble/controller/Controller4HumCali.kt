package com.govee.thnew.ble.controller

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4HumCali

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->读写湿度校准值的Controller
 */
class Controller4HumCali : AbsControllerWithCallback {

    /**
     * 湿度校准值*100
     */
    private var humCali = 0

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     *
     * @param humCali
     */
    constructor(humCali: Int, writeCallBack: ((result: Boolean) -> Unit)) : super(writeCallBack) {
        this.humCali = checkHumCali(humCali)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_hum_cali
    }

    private fun checkHumCali(humCali: Int): Int {
        var lastHumCali = humCali
        val minHumCali = (ThConsV1.HUM_MIN_CALI * 100).toInt()
        val maxHumCali = (ThConsV1.HUM_MAX_CALI * 100).toInt()
        lastHumCali = lastHumCali.coerceAtLeast(minHumCali)
        lastHumCali = lastHumCali.coerceAtMost(maxHumCali)
        return lastHumCali
    }

    override fun translateWrite(): ByteArray {
        return BleUtil.getSignedBytesFor2(humCali, false)
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        var humCali = BleUtil.convertTwoBytesToShort(validBytes[0], validBytes[1])
        humCali = checkHumCali(humCali)
        Event4HumCali.sendSuc(isWrite, commandType, proType, humCali)
        return true
    }

    override fun fail() {
        super.fail()
        Event4HumCali.sendFail(isWrite, commandType, proType)
    }
}