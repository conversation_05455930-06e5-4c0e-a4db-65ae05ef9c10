package com.govee.thnew.ble.controller

import com.govee.base2newth.AbsSingleController
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback

/**
 * <AUTHOR>
 * @date created on 2025/6/5
 * @description H5112通过网关操作的透传指令
 */
class Controller4PtRealOp : AbsSingleController {

    private var sno = 0
    lateinit var originalController: AbsControllerWithCallback

    private constructor() : super(true)

    /**
     * 写操作
     * @param sno 子设备序号
     * @param originalController 透传指令
     */
    constructor(sno: Int, originalController: AbsControllerWithCallback) : super(true) {
        this.sno = sno
        this.originalController = originalController
    }

    override fun getCommandType(): Byte {
        return BleProtocol.COMMAND_TYPE_4_PT_REAL_OP
    }

    override fun parseValidBytes(validBytes: ByteArray?): Boolean {
        return false
    }

    override fun translateWrite(): ByteArray {
        val originalValues = ByteArray(16)
        System.arraycopy(originalController.value, 1, originalValues, 0, originalValues.size)
        return byteArrayOf(sno.toByte()).plus(originalValues)
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        return suc
    }

    override fun fail() {}
}