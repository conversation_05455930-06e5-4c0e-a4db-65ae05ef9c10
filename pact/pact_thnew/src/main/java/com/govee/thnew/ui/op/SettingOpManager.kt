package com.govee.thnew.ui.op

import android.os.Handler
import androidx.lifecycle.MutableLiveData
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.device.Guide
import com.govee.base2home.main.OfflineDeviceListConfig
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.net.CommonSettings
import com.govee.base2newth.net.Request4Setting
import com.govee.base2newth.net.smw.Req4SetSettingsNewV1
import com.govee.base2newth.net.smw.smwNetService
import com.govee.home.account.config.AccountConfig
import com.govee.kt.net.Request4ChangeDeviceName
import com.govee.kt.net.Request4DeleteDevice
import com.govee.kt.net.netService4Base
import com.govee.kt.setting.event.Event4DeviceNameChange
import com.govee.mvvm.ext.request
import com.govee.mvvm.globalLaunch
import com.govee.thnew.add.AddInfo
import com.govee.thnew.ble.controller.Controller4ComfortTemHum
import com.govee.thnew.ble.controller.Controller4DeviceWarningTime
import com.govee.thnew.ble.controller.Controller4HumCali
import com.govee.thnew.ble.controller.Controller4HumWarning
import com.govee.thnew.ble.controller.Controller4TemCali
import com.govee.thnew.ble.controller.Controller4TemWarning
import com.govee.thnew.ble.controller.Controller4UploadFreq
import com.govee.thnew.ble.controller.Controller4Volume
import com.govee.thnew.config.ThConfig4Setting
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.net.thNewNetService
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.Vm4ThOpManager.Companion.UPDATE_4_CALIBRATION
import com.govee.thnew.ui.Vm4ThOpManager.Companion.UPDATE_4_DELAY_ALARM
import com.govee.thnew.ui.Vm4ThOpManager.Companion.UPDATE_4_UPLOAD_FREQUENCY
import com.govee.thnew.ui.Vm4ThOpManager.Companion.UPDATE_4_WARNING
import com.govee.ui.R
import com.govee.util.CountryAndLangUtil
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.infra.SafeLog.Companion.i
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @date created on 2025/5/7
 * @description 温湿度计--设置页操作管理类
 */
class SettingOpManager(private val bindExt: AddInfo, private val mHandler: Handler, private val transactions: Transactions) {

    private var skuGuideInfo: MutableList<Guide>? = null

    /**
     * 引导的LiveData
     */
    val ld4guides: MutableLiveData<MutableList<Guide>?> = MutableLiveData()

    /**
     * 查询引导信息
     */
    fun queryGuide() {
        ld4guides.postValue(skuGuideInfo)
        Vm4ThOpManager.instance()?.request({
            netService4Base.getGuides(
                bindExt.sku,
                bindExt.bleSoftVersion,
                bindExt.bleHardVersion,
                bindExt.wifiSoftVersion,
                bindExt.wifiHardVersion,
                bindExt.device
            )
        }, success = {
            // 如果是俄罗斯地区，展示yandex图标
            val userGuide = it.userGuide
            val isRussia = CountryAndLangUtil.isRussia()
            if (!isRussia && !userGuide.isNullOrEmpty()) {
                userGuide.removeIf { guide -> guide.guideType == Guide.GUIDE_TYPE_YANDEX }
            }
            skuGuideInfo = userGuide
            ld4guides.postValue(userGuide)
        }, error = {
            i("xiaobing") { "SettingOpManager--queryGuide-->获取引导信息出错..." }
        })
    }

    /**
     * 更新设备名称的LiveData
     * 备注：first->key(sku_device),second->newName
     */
    val ld4UpdateDeviceName by lazy {
        MutableLiveData<Pair<String, String>>()
    }

    /**
     * 更改设备名称
     */
    fun changeDeviceName(newDeviceName: String) {
        //未登录则本地保存
        if (!AccountConfig.read().isHadToken) {
            Vm4ThOpManager.instance()?.showLoading()
            //更新缓存
            bindExt.deviceName = newDeviceName
            //更新本地数据
            OfflineDeviceListConfig.read().getDevices(bindExt.sku, bindExt.device)?.let { absDevice ->
                JsonUtil.parseToHashmap(absDevice.deviceExt.deviceSettings)?.let { settingMap ->
                    //更新本地数据
                    settingMap["deviceName"] = newDeviceName
                    absDevice.deviceName = newDeviceName
                    absDevice.deviceExt.deviceSettings = JsonUtil.toJson(settingMap)
                    OfflineDeviceListConfig.read().addOfflineDevice(absDevice)
                }
                mHandler.postDelayed({
                    Vm4ThOpManager.instance()?.closeLoading()
                    //通知设备名变更
                    Event4DeviceNameChange.sendEvent(bindExt.sku, bindExt.deviceName, newDeviceName)
                    ld4UpdateDeviceName.value = Pair(bindExt.getKey(), newDeviceName)
                }, 1000)
            }
        } else {
            updateDeviceName(bindExt.sku, bindExt.device, newDeviceName) {
                if (it) {
                    bindExt.deviceName = newDeviceName
                    //通知设备名变更
                    Event4DeviceNameChange.sendEvent(bindExt.sku, bindExt.deviceName, newDeviceName)
                    ld4UpdateDeviceName.value = Pair(bindExt.getKey(), newDeviceName)
                }
            }
        }
    }

    private fun updateDeviceName(sku: String, device: String, newName: String, callback: ((suc: Boolean) -> Unit)?) {
        Vm4ThOpManager.instance()?.showLoading()
        Vm4ThOpManager.instance()?.request({
            netService4Base.changeDeviceName(Request4ChangeDeviceName(sku, device, newName))
        }, success = {
            Vm4ThOpManager.instance()?.closeLoading()
            callback?.invoke(true)
        }, error = {
            Vm4ThOpManager.instance()?.closeLoading()
            toast(it.errorMsg)
            callback?.invoke(false)
        })
    }

    /**
     * 更新温度告警信息
     * 备注：这个要区分场景：1:可绑网关但未绑网关的纯蓝牙设备(或wifi+蓝牙款)-->同步给设备成功后，即视为成功；然后再同步给服务端，不管结果
     *                    2:可绑网关且已绑定网关的纯蓝牙设备-->同步给设备，同时同步给服务端，以服务端的结果为准
     *                    3.如H5053这样不支持蓝牙，只同步到服务端的设备-->同步给服务端，以服务端的结果为准
     *
     *  !!!下同
     */
    fun updateTemWarningInfo(minAlarmTem: Int, maxAlarmTem: Int, alarmOn: Boolean) {
        if (Vm4ThOpManager.instance()?.isBindGateway().isTrue()) {
            if (Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                updateTemWarningInfoByBle(minAlarmTem, maxAlarmTem, alarmOn, false)
            }
            updateTemWarning2Service(minAlarmTem, maxAlarmTem, alarmOn, true)
        } else {
            if (ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)) {
                updateTemWarning2Service(minAlarmTem, maxAlarmTem, alarmOn, true)
            } else {
                if (Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                    updateTemWarningInfoByBle(minAlarmTem, maxAlarmTem, alarmOn, true)
                }
            }
        }
    }

    /**
     * 通过ble更新设备端的温度告警信息
     */
    private fun updateTemWarningInfoByBle(minAlarmTem: Int, maxAlarmTem: Int, alarmOn: Boolean, needResult: Boolean) {
        val supportSingleAlarm = Constant4L5.supportSingleAlarm(
            bindExt.goodsType,
            bindExt.sku,
            bindExt.bleSoftVersion,
            bindExt.bleHardVersion
        )
        Vm4ThOpManager.instance()?.sendCommand(
            Controller4TemWarning(
                alarmOn,
                minAlarmTem,
                maxAlarmTem,
                bindExt.delayPushTime,
                supportSingleAlarm
            ) {
                if (needResult) {
                    globalLaunch(Dispatchers.IO) {
                        if (it) {
                            //赋新值
                            bindExt.temMin = minAlarmTem
                            bindExt.temMax = maxAlarmTem
                            bindExt.temWarning = alarmOn
                            //更新告警信息缓存
                            WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                        }
                        Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.postValue(Pair(it, UPDATE_4_WARNING))
                        //若已登录则尝试同步到服务端
                        if (AccountConfig.read().isHadToken) {
                            updateTemWarning2Service(minAlarmTem, maxAlarmTem, alarmOn, false)
                        } else {
                            Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                        }
                    }
                }
            })
    }

    /**
     * 将温度告警信息同步到服务端
     */
    private fun updateTemWarning2Service(temMin: Int, temMax: Int, temWarning: Boolean, needResult: Boolean) {
        updateTemInfo2Service(temMin, temMax, temWarning, bindExt.temCali, bindExt.delayPushTime, UPDATE_4_WARNING, needResult)
    }

    /**
     * 更新温度校准信息
     */
    fun updateTemCali(temCali: Int) {
        if (Vm4ThOpManager.instance()?.isBindGateway().isTrue()) {
            if (Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                updateTemCaliByBle(temCali, false)
            }
            updateTemCali2Service(temCali, true)
        } else {
            if (ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)) {
                updateTemCali2Service(temCali, true)
            } else {
                if (Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                    updateTemCaliByBle(temCali, true)
                }
            }
        }
    }

    /**
     * 通过ble更新设备端的温度校准
     */
    private fun updateTemCaliByBle(temCali: Int, needResult: Boolean) {
        Vm4ThOpManager.instance()?.sendCommand(Controller4TemCali(temCali) {
            if (needResult) {
                globalLaunch(Dispatchers.IO) {
                    if (it) {
                        bindExt.temCali = temCali
                        //更新告警信息缓存
                        WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                    }
                    Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.postValue(Pair(it, UPDATE_4_CALIBRATION))
                    //若已登录则尝试同步到服务端
                    if (AccountConfig.read().isHadToken) {
                        updateTemCali2Service(temCali, false)
                    } else {
                        Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                    }
                }
            }
        })
    }

    /**
     * 将温度校准信息同步到服务端
     */
    private fun updateTemCali2Service(temCali: Int, needResult: Boolean) {
        updateTemInfo2Service(bindExt.temMin, bindExt.temMax, bindExt.temWarning, temCali, bindExt.delayPushTime, UPDATE_4_CALIBRATION, needResult)
    }

    /**
     * 更新温度延时告警时间
     */
    fun updateDelayPushTime(delayPushTime: Int) {
        if (Vm4ThOpManager.instance()?.isBindGateway().isTrue()) {
            if (Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                updateDelayPushTimeByBle(delayPushTime, false)
            }
            updateDelayPushTime2Service(delayPushTime, true)
        } else {
            if (ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)) {
                updateDelayPushTime2Service(delayPushTime, true)
            } else {
                if (Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                    updateDelayPushTimeByBle(delayPushTime, true)
                }
            }
        }
    }

    /**
     * 通过ble更新设备端的温度延时告警时间
     */
    private fun updateDelayPushTimeByBle(delayPushTime: Int, needResult: Boolean) {
        val supportSingleAlarm = Constant4L5.supportSingleAlarm(
            bindExt.goodsType,
            bindExt.sku,
            bindExt.bleSoftVersion,
            bindExt.bleHardVersion
        )
        Vm4ThOpManager.instance()?.sendCommand(
            Controller4TemWarning(
                bindExt.temWarning,
                bindExt.temMin,
                bindExt.temMax,
                delayPushTime,
                supportSingleAlarm
            ) {
                if (needResult) {
                    globalLaunch(Dispatchers.IO) {
                        if (it) {
                            bindExt.delayPushTime = delayPushTime
                        }
                        Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.postValue(Pair(it, UPDATE_4_DELAY_ALARM))
                        //若已登录则尝试同步到服务端
                        if (AccountConfig.read().isHadToken) {
                            updateDelayPushTime2Service(delayPushTime, false)
                        } else {
                            Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                        }
                    }
                }
            })
    }

    /**
     * 将温度延时告警时间同步给服务端
     */
    private fun updateDelayPushTime2Service(delayPushTime: Int, needResult: Boolean) {
        updateTemInfo2Service(bindExt.temMin, bindExt.temMax, bindExt.temWarning, bindExt.temCali, delayPushTime, UPDATE_4_DELAY_ALARM, needResult)
    }

    /**
     * 将温度相关变更信息同步到服务端
     */
    private fun updateTemInfo2Service(temMin: Int, temMax: Int, temWarning: Boolean, temCali: Int, delayPushTime: Int, updateType: Int, needResult: Boolean) {
        if (needResult) {
            Vm4ThOpManager.instance()?.showLoading()
        }
        Vm4ThOpManager.instance()?.request({
            if (ThConfig4Setting.setSettingsWithNew(bindExt.goodsType, bindExt.sku)) {
                smwNetService.updateSetting4Callback(Req4SetSettingsNewV1().apply {
                    sku = bindExt.sku
                    device = bindExt.device
                    settings = CommonSettings().apply {
                        this.temMin = temMin
                        this.temMax = temMax
                        this.temWarning = temWarning
                        this.temCali = temCali
                        this.delayPushTime = delayPushTime
                    }
                })
            } else {
                thNewNetService.updateSettings(Request4Setting(transactions.createTransaction(), bindExt.sku, bindExt.device).apply {
                    this.temMin = temMin
                    this.temMax = temMax
                    this.temWarning = temWarning
                    this.temCali = temCali
                    this.delayPushTime = delayPushTime
                })
            }
        }, success = {
            if (needResult) {
                //赋新值
                bindExt.temMin = temMin
                bindExt.temMax = temMax
                bindExt.temWarning = temWarning
                bindExt.temCali = temCali
                bindExt.delayPushTime = delayPushTime
                if (updateType == UPDATE_4_WARNING || updateType == UPDATE_4_CALIBRATION) {
                    //更新告警信息缓存
                    WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                }
                //回调
                Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.value = Pair(true, updateType)
                //关闭弹窗
                Vm4ThOpManager.instance()?.closeLoading()
            }
        }, error = {
            if (needResult) {
                Vm4ThOpManager.instance()?.closeLoading()
                toast(it.errorMsg)
            }
        })
    }

    /**
     * 更新湿度告警信息
     */
    fun updateHumWarningInfo(minAlarmHum: Int, maxAlarmHum: Int, alarmOn: Boolean) {
        if (Vm4ThOpManager.instance()?.isBindGateway().isTrue()) {
            if (Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                updateHumWarningByBle(minAlarmHum, maxAlarmHum, alarmOn, false)
            }
            updateHumWarning2Service(minAlarmHum, maxAlarmHum, alarmOn, true)
        } else {
            if (ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)) {
                updateHumWarning2Service(minAlarmHum, maxAlarmHum, alarmOn, true)
            } else {
                if (Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                    updateHumWarningByBle(minAlarmHum, maxAlarmHum, alarmOn, true)
                }
            }
        }
    }

    /**
     * 通过ble更新设备端的湿度告警信息
     */
    private fun updateHumWarningByBle(humMin: Int, humMax: Int, humWarning: Boolean, needResult: Boolean) {
        Vm4ThOpManager.instance()?.sendCommand(Controller4HumWarning(humWarning, humMin, humMax) {
            if (needResult) {
                globalLaunch(Dispatchers.IO) {
                    if (it) {
                        //赋新值
                        bindExt.humMin = humMin
                        bindExt.humMax = humMax
                        bindExt.humWarning = humWarning
                        //更新告警信息缓存
                        WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                    }
                    Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.postValue(Pair(it, UPDATE_4_WARNING))
                    //若已登录则尝试同步到服务端
                    if (AccountConfig.read().isHadToken) {
                        updateHumWarning2Service(humMin, humMax, humWarning, false)
                    } else {
                        Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                    }
                }
            }
        })
    }

    /**
     * 将湿度告警信息同步给服务端
     */
    private fun updateHumWarning2Service(humMin: Int, humMax: Int, humWarning: Boolean, needResult: Boolean) {
        updateHumInfo2Service(humMin, humMax, humWarning, bindExt.humCali, UPDATE_4_WARNING, needResult)
    }

    /**
     * 更新湿度校准信息
     */
    fun updateHumCali(humCali: Int) {
        if (Vm4ThOpManager.instance()?.isBindGateway().isTrue()) {
            if (Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                updateHumCaliByBle(humCali, false)
            }
            updateHumCali2Service(humCali, true)
        } else {
            if (ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)) {
                updateHumCali2Service(humCali, true)
            } else {
                if (Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                    updateHumCaliByBle(humCali, true)
                }
            }
        }
    }

    /**
     * 通过ble更新设备端的湿度校准信息
     */
    private fun updateHumCaliByBle(humCali: Int, needResult: Boolean) {
        Vm4ThOpManager.instance()?.sendCommand(Controller4HumCali(humCali) {
            if (needResult) {
                globalLaunch(Dispatchers.IO) {
                    if (it) {
                        bindExt.humCali = humCali
                        //更新告警信息缓存
                        WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                    }
                    Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.postValue(Pair(it, UPDATE_4_CALIBRATION))
                    //若已登录则尝试同步到服务端
                    if (AccountConfig.read().isHadToken) {
                        updateHumCali2Service(humCali, false)
                    } else {
                        Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                    }
                }
            }
        })
    }

    /**
     * 将湿度校准信息同步给服务端
     */
    private fun updateHumCali2Service(humCali: Int, needResult: Boolean) {
        updateHumInfo2Service(bindExt.humMin, bindExt.humMax, bindExt.humWarning, humCali, UPDATE_4_CALIBRATION, needResult)
    }

    /**
     * 将湿度变更信息同步到服务端
     */
    private fun updateHumInfo2Service(humMin: Int, humMax: Int, humWarning: Boolean, humCali: Int, updateType: Int, needResult: Boolean) {
        if (needResult) {
            Vm4ThOpManager.instance()?.showLoading()
        }
        Vm4ThOpManager.instance()?.request({
            if (ThConfig4Setting.setSettingsWithNew(bindExt.goodsType, bindExt.sku)) {
                smwNetService.updateSetting4Callback(Req4SetSettingsNewV1().apply {
                    sku = bindExt.sku
                    device = bindExt.device
                    settings = CommonSettings().apply {
                        this.humMin = humMin
                        this.humMax = humMax
                        this.humWarning = humWarning
                        this.humCali = humCali
                    }
                })
            } else {
                thNewNetService.updateSettings(Request4Setting(transactions.createTransaction(), bindExt.sku, bindExt.device).apply {
                    this.humMin = humMin
                    this.humMax = humMax
                    this.humWarning = humWarning
                    this.humCali = humCali
                })
            }
        }, success = {
            if (needResult) {
                //赋新值
                bindExt.humMin = humMin
                bindExt.humMax = humMax
                bindExt.humWarning = humWarning
                bindExt.humCali = humCali
                //更新告警信息缓存
                WarnConfig.read().updateWarningRange(bindExt.warnRange, true)
                //回调
                Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.value = Pair(true, updateType)
                //关闭弹窗
                Vm4ThOpManager.instance()?.closeLoading()
            }
        }, error = {
            if (needResult) {
                Vm4ThOpManager.instance()?.closeLoading()
                toast(it.errorMsg)
            }
        })
    }

    /**
     * 更新温湿度数据的上传频率
     * @param frequency (单位：分钟)
     */
    fun updateThCdUploadFrequency(frequency: Int) {
        val hour = frequency / 60
        val minute = frequency % 60
        Vm4ThOpManager.instance()?.sendCommand(Controller4UploadFreq(hour, minute) {
            globalLaunch(Dispatchers.IO) {
                if (it) {
                    bindExt.uploadRate = frequency
                }
                Vm4ThOpManager.instance()?.ld4UpdateDeviceInfo?.postValue(Pair(it, UPDATE_4_UPLOAD_FREQUENCY))
                //若已登录则尝试同步到服务端
                if (AccountConfig.read().isHadToken) {
                    Vm4ThOpManager.instance()?.request({
                        if (ThConfig4Setting.setSettingsWithNew(bindExt.goodsType, bindExt.sku)) {
                            smwNetService.updateSetting4Callback(Req4SetSettingsNewV1().apply {
                                sku = bindExt.sku
                                device = bindExt.device
                                settings = CommonSettings().apply {
                                    this.uploadRate = frequency
                                }
                            })
                        } else {
                            thNewNetService.updateSettings(Request4Setting(transactions.createTransaction(), bindExt.sku, bindExt.device).apply {
                                this.uploadRate = frequency
                            })
                        }
                    })
                } else {
                    Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                }
            }
        })
    }

    /**
     * 低电量报警开关切换
     */
    fun updateLowBatAlarm(batWarning: Boolean, callback: ((suc: Boolean) -> Unit)?) {
        ThConfig4Setting.supportLowBatAlarm(bindExt.goodsType, bindExt.sku).let {
            if (!it.first) {
                return
            }
            Vm4ThOpManager.instance()?.showLoading()
            if (AccountConfig.read().isHadToken) {
                updateLowBatAlarmToService(batWarning, callback)
            } else {
                //缓存值更新
                bindExt.batteryWarning = batWarning
                //更新本地数据
                Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                //回调结果
                callback?.invoke(true)
                Vm4ThOpManager.instance()?.closeLoading()
            }
        }
    }

    private fun updateLowBatAlarmToService(batWarning: Boolean, callback: ((suc: Boolean) -> Unit)?) {
        Vm4ThOpManager.instance()?.request({
            if (ThConfig4Setting.setSettingsWithNew(bindExt.goodsType, bindExt.sku)) {
                smwNetService.updateSetting4Callback(Req4SetSettingsNewV1().apply {
                    sku = bindExt.sku
                    device = bindExt.device
                    settings = CommonSettings().apply {
                        this.batteryWarning = batWarning
                    }
                })
            } else {
                thNewNetService.updateSettings(Request4Setting(transactions.createTransaction(), bindExt.sku, bindExt.device).apply {
                    this.batteryWarning = batWarning
                })
            }
        }, success = {
            Vm4ThOpManager.instance()?.closeLoading()
            bindExt.batteryWarning = batWarning
            callback?.invoke(true)
        }, error = {
            Vm4ThOpManager.instance()?.closeLoading()
            toast(it.errorMsg)
            callback?.invoke(false)
        })
    }

    /**
     * 音量档位设置
     * 备注：只用ble设置到设备端即可
     */
    fun setVolumeLevel(selectVolumeLevel: Int, callback: ((suc: Boolean) -> Unit)?) {
        Vm4ThOpManager.instance()?.showLoading()
        Vm4ThOpManager.instance()?.sendCommand(Controller4Volume(selectVolumeLevel) { result ->
            if (result) {
                //更新缓存
                bindExt.muteLevel = selectVolumeLevel
                //更新服务端或本地
                if (AccountConfig.read().isHadToken) {
                    //将档位设置到服务端,无须处理结果(音量只设备端使用,传服务端只是用于初始化显示)
                    Vm4ThOpManager.instance()?.request({
                        if (ThConfig4Setting.setSettingsWithNew(bindExt.goodsType, bindExt.sku)) {
                            smwNetService.updateSetting4Callback(Req4SetSettingsNewV1().apply {
                                sku = bindExt.sku
                                device = bindExt.device
                                settings = CommonSettings().apply {
                                    this.muteLevel = selectVolumeLevel
                                }
                            })
                        } else {
                            thNewNetService.updateSettings(Request4Setting(transactions.createTransaction(), bindExt.sku, bindExt.device).apply {
                                this.muteLevel = selectVolumeLevel
                            })
                        }
                    })
                } else {
                    Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                }
            }
            globalLaunch(Dispatchers.Main) {
                Vm4ThOpManager.instance()?.closeLoading()
                if (result) {
                    toast(R.string.text_4_set_volume_suc)
                } else {
                    toast(R.string.text_4_set_volume_failed)
                }
                //回调结果
                callback?.invoke(result)
            }
        })
    }

    /**
     * 设置设备声音报警
     */
    fun setDeviceWarning(open: Boolean, seconds: Int, callback: ((suc: Boolean) -> Unit)?) {
        Vm4ThOpManager.instance()?.showLoading()
        Vm4ThOpManager.instance()?.sendCommand(Controller4DeviceWarningTime(open, seconds) { result ->
            if (result) {
                //更新缓存
                bindExt.deviceWarning = open
                bindExt.deviceWarningSeconds = seconds
                //更新服务端或本地
                if (AccountConfig.read().isHadToken) {
                    //将档位设置到服务端,无须处理结果(音量只设备端使用,传服务端只是用于初始化显示)
                    val request = Request4Setting(transactions.createTransaction(), bindExt.sku, bindExt.device).apply {
                        deviceWarning = open
                        deviceWarningSeconds = seconds
                    }
                    Vm4ThOpManager.instance()?.request({ thNewNetService.updateSettings(request) })
                } else {
                    Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                }
            }
            globalLaunch(Dispatchers.Main) {
                Vm4ThOpManager.instance()?.closeLoading()
                if (result) {
                    toast(R.string.bbq_presettem_successful)
                } else {
                    toast(R.string.bbq_presettem_failed)
                }
                callback?.invoke(result)
            }
        })
    }

    /**
     * 舒适度设置
     */
    fun setComfortTemHum(temRange: IntArray, humRange: IntArray, callback: ((suc: Boolean) -> Unit)?) {
        Vm4ThOpManager.instance()?.showLoading()
        Vm4ThOpManager.instance()?.sendCommand(Controller4ComfortTemHum(temRange, humRange) { result ->
            if (result) {
                //更新缓存
                bindExt.comfortTemMin = temRange.getOrNull(0)
                bindExt.comfortTemMax = temRange.getOrNull(1)
                bindExt.comfortHumMin = humRange.getOrNull(0)
                bindExt.comfortHumMax = humRange.getOrNull(1)
                //更新服务端或本地
                if (AccountConfig.read().isHadToken) {
                    //将档位设置到服务端,无须处理结果(音量只设备端使用,传服务端只是用于初始化显示)
                    val request = Request4Setting(transactions.createTransaction(), bindExt.sku, bindExt.device).apply {
                        comfortTemMin = temRange.getOrNull(0)
                        comfortTemMax = temRange.getOrNull(1)
                        comfortHumMin = humRange.getOrNull(0)
                        comfortHumMax = humRange.getOrNull(1)
                    }
                    Vm4ThOpManager.instance()?.request({ thNewNetService.updateSettings(request) })
                } else {
                    Vm4ThOpManager.instance()?.updateOfflineDeviceInfo()
                }
            }
            globalLaunch(Dispatchers.Main) {
                Vm4ThOpManager.instance()?.closeLoading()
                if (result) {
                    toast(R.string.bbq_presettem_successful)
                } else {
                    toast(R.string.bbq_presettem_failed)
                }
                callback?.invoke(result)
            }
        })
    }

    /**
     * 删除设备
     */
    fun deleteDevice(callback: ((suc: Boolean) -> Unit)?) {
        Vm4ThOpManager.instance()?.showLoading()
        //未登录则本地删除
        if (!AccountConfig.read().isHadToken) {
            globalLaunch(Dispatchers.IO) {
                OfflineDeviceListConfig.read().removeBoundDevice(bindExt.sku, bindExt.device)
                //清理缓存
                clearCache()
                withContext(Dispatchers.Main) {
                    mHandler.postDelayed({
                        Vm4ThOpManager.instance()?.closeLoading()
                        toast(ResUtil.getString(R.string.delete_success))
                        callback?.invoke(true)
                    }, 1000)
                }
            }
        } else {
            Vm4ThOpManager.instance()?.request({
                netService4Base.deleteDevice(Request4DeleteDevice(bindExt.sku, bindExt.device))
            }, success = {
                globalLaunch(Dispatchers.IO) {
                    when (bindExt.sku) {
                        Constant4L5.H5112 -> {
                            h5112SettingOp.unbindSubDev2Gw()
                        }

                        else -> {}
                    }
                    //统计删除设备
                    AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.delete_device, bindExt.sku)
                    withContext(Dispatchers.Main) {
                        //结果回调(loading消失放在页面中处理)
                        toast(ResUtil.getString(R.string.delete_success))
                        callback?.invoke(true)
                    }
                }
            }, error = {
                Vm4ThOpManager.instance()?.closeLoading()
                toast(it.errorMsg)
                callback?.invoke(false)
            })
        }
    }

    //-------------------------------------H5112设置相关(1、多探针；2、settings使用新接口)-------------------------------------

    private val h5112SettingOp by lazy {
        SettingOp4H5112(bindExt)
    }

    /**
     * 温度报警设置
     */
    fun updateTemWarningInfo4H5112(minAlarmTem: Int, maxAlarmTem: Int, alarmOn: Boolean, probeIndex: Int) {
        h5112SettingOp.updateTemWarningInfo(minAlarmTem, maxAlarmTem, alarmOn, probeIndex)
    }

    /**
     * 更新温度校准信息
     */
    fun updateTemCali4H5112(temCali: Int, probeIndex: Int) {
        h5112SettingOp.updateTemCali(temCali, probeIndex)
    }

    /**
     * 更新温度延时告警时间
     */
    fun updateDelayPushTime4H5112(delayPushTime: Int, probeIndex: Int) {
        h5112SettingOp.updateDelayPushTime(delayPushTime, probeIndex)
    }

    /**
     * 更新湿度告警信息
     */
    fun updateHumWarningInfo4H5112(minAlarmHum: Int, maxAlarmHum: Int, alarmOn: Boolean) {
        h5112SettingOp.updateHumWarning(minAlarmHum, maxAlarmHum, alarmOn)
    }

    /**
     * 更新湿度校准信息
     */
    fun updateHumCali4H5112(humCali: Int) {
        h5112SettingOp.updateHumCali(humCali)
    }

    /**
     * 删除设备后清除相关的本地缓存
     * 备注：此处只处理未登录的情况(蓝牙温湿度计)，登录的情况在AbsThItem的remove方法中统一处理
     */
    private fun clearCache() {
        //删除数据加载配置、缓存等
        globalLaunch(Dispatchers.IO) {
            ThConsV1.deleteCache(bindExt.sku, bindExt.device)
        }
    }
}