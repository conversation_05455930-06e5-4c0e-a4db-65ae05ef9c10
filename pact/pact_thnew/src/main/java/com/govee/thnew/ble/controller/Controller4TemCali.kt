package com.govee.thnew.ble.controller

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4TemCali

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->读写温度校准值的Controller
 */
class Controller4TemCali : AbsControllerWithCallback {
    /**
     * 温度校准值*100
     */
    private var temCali = 0

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     *
     * @param temCali
     */
    constructor(temCali: Int, writeCallBack: ((result: Boolean) -> Unit)) : super(writeCallBack) {
        this.temCali = checkTemCali(temCali)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_tem_cali
    }

    private fun checkTemCali(temCali: Int): Int {
        var lastTemCali = temCali
        val minTemCali = (ThConsV1.TEM_MIN_CALI * 100).toInt()
        val maxTemCali = (ThConsV1.TEM_MAX_CALI * 100).toInt()
        lastTemCali = lastTemCali.coerceAtLeast(minTemCali)
        lastTemCali = lastTemCali.coerceAtMost(maxTemCali)
        return lastTemCali
    }

    override fun translateWrite(): ByteArray {
        return BleUtil.getSignedBytesFor2(temCali, false)
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        var temCali = BleUtil.getSignedShort(validBytes[1], validBytes[0]).toInt()
        temCali = checkTemCali(temCali)
        Event4TemCali.sendSuc(isWrite, commandType, proType, temCali)
        return true
    }

    override fun fail() {
        super.fail()
        Event4TemCali.sendFail(isWrite, commandType, proType)
    }
}