package com.govee.thnew.ble.event

import com.govee.base2home.Constant4L5
import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2025/5/12
 * @description 温湿度计(H5112)-->读写温度校准值的事件
 */
class Event4TemCaliH5112 private constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON>ole<PERSON>, commandType: Byte, proType: Byte) : AbsControllerEvent(result, write, commandType, proType, !write) {
    /**
     * 温度校准值*100--探针1
     */
    var temCali4Pb1 = 0

    /**
     * 温度校准值*100--探针2
     */
    var temCali4Pb2 = 0

    /**
     * 探针序号(只用于写操作)
     */
    var probeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(Event4TemCaliH5112(false, write, commandType, proType))
        }

        fun sendSuc(write: Bo<PERSON><PERSON>, commandType: Byte, proType: Byte, temCali4Pb1: Int, temCali4Pb2: Int) {
            val event = Event4TemCaliH5112(true, write, commandType, proType)
            event.temCali4Pb1 = temCali4Pb1
            event.temCali4Pb2 = temCali4Pb2
            EventBus.getDefault().post(event)
        }
    }
}