package com.govee.thnew.ui.compare

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import com.govee.base2home.Constant4L5
import com.govee.base2home.common.BaseRvAdapter
import com.govee.base2home.util.ClickUtil
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.order.ChartType
import com.govee.base2newth.other.Config4MultiChartType
import com.govee.base2newth.other.Config4SingleChartType
import com.govee.mvvm.globalLaunch
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.databinding.ThnewDialog4SelectChartTypeBinding
import com.ihoment.base2app.dialog.BaseDialog
import com.ihoment.base2app.util.AppUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * <AUTHOR>
 * @date created on 2024/4/7
 * @description 温湿度计=->设备图表对比选择图表类型的弹窗
 */
class Dialog4ThSelectChartType private constructor(context: Context, private val goodsType: Int, private val sku: String, private val device: String, private val isSingleCompare: Boolean, private val probeIndex: Int) : BaseDialog(context),
    View.OnClickListener {

    companion object {
        fun createDialog(context: Context, goodsType: Int, sku: String, device: String, isSingleCompare: Boolean, probeIndex: Int): Dialog4ThSelectChartType {
            return Dialog4ThSelectChartType(context, goodsType, sku, device, isSingleCompare, probeIndex)
        }
    }

    private lateinit var binding: ThnewDialog4SelectChartTypeBinding
    private var selectedCallback: (() -> Unit)? = null

    private val selectChartTypeAdapter by lazy {
        Adapter4SelChartType(context, arrayListOf())
    }

    init {
        changeDialogOutside(false)
        immersionMode()
        binding.rvList4SelChartType.layoutManager = LinearLayoutManager(context)
        binding.rvList4SelChartType.adapter = selectChartTypeAdapter.also {
            it.setOnRvItemClickListener(object : BaseRvAdapter.OnRvItemClickListener {
                @SuppressLint("NotifyDataSetChanged")
                override fun onItemClick(v: View, position: Int) {
                    if (!it.canCancelSelected(position)) {
                        return
                    }
                    it.getItem(position).apply {
                        selected = !selected
                    }
                    it.notifyDataSetChanged()
                }
            })
        }
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getLayoutView(): View {
        binding = ThnewDialog4SelectChartTypeBinding.inflate(LayoutInflater.from(context))
        binding.tvCancel4SelChartType.setOnClickListener(this)
        binding.tvSure4SelChartType.setOnClickListener(this)
        return binding.root
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth() * 335 / 375
    }

    /**
     * 显示弹窗
     */
    fun show(selectedCallback: () -> Unit) {
        globalLaunch(Dispatchers.IO) {
            <EMAIL> = selectedCallback
            val cacheChartTypes = if (isSingleCompare) {
                Config4SingleChartType.getConfig().getChartTypes(sku, device)
            } else {
                Config4MultiChartType.getConfig().getChartTypes(sku, device)
            }
            withContext(Dispatchers.Main) {
                selectChartTypeAdapter.updateList(cacheChartTypes ?: arrayListOf<ChartType>().apply {
                    if (sku == Constant4L5.H5106) {
                        add(ChartType().apply {
                            type = ThConsV1.PM25
                            selected = false
                        })
                    }
                    if (sku == Constant4L5.H5140) {
                        add(ChartType().apply {
                            type = ThConsV1.CO2
                            selected = false
                        })
                    }
                    add(ChartType().apply {
                        type = ThConsV1.TEM
                        selected = true
                    })
                    val supportHum = when (sku) {
                        Constant4L5.H5112 -> {
                            probeIndex == Constant4L5.PROBE_INDEX_4_TEM_HUM
                        }

                        else -> {
                            ThConfig4Support.supportHum(goodsType, sku)
                        }
                    }
                    if (supportHum) {
                        add(ChartType().apply {
                            type = ThConsV1.HUM
                            selected = true
                        })
                        add(ChartType().apply {
                            type = ThConsV1.DP
                            selected = false
                        })
                        add(ChartType().apply {
                            type = ThConsV1.VPD
                            selected = false
                        })
                    }
                })
                super.show()
            }
        }
    }

    override fun onClick(v: View) {
        if (ClickUtil.getInstance.clickQuick()) {
            return
        }
        when (v) {
            binding.tvCancel4SelChartType -> {
                hide()
            }

            binding.tvSure4SelChartType -> {
                if (isSingleCompare) {
                    Config4SingleChartType.getConfig().updateChartTypes(sku, device, selectChartTypeAdapter.getChartData())
                } else {
                    Config4MultiChartType.getConfig().updateChartTypes(sku, device, selectChartTypeAdapter.getChartData())
                }
                selectedCallback?.invoke()
                hide()
            }

            else -> {}
        }
    }
}