package com.govee.thnew.ble

import com.govee.base2newth.AbsBleComm
import com.govee.base2newth.AbsThGattCallbackImp
import com.govee.base2newth.BleThProtocol
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.one2multi.AbProtocol
import java.util.UUID

/**
 * <AUTHOR>
 * @date created on 2024/2/18
 * @description 温湿度计的蓝牙通讯-->单包通讯处理器
 */
class ThBleSingleComm : AbsBleComm() {

    companion object {
        private const val serviceUuid = "494e5445-4c4c-495f-524f-434b535f4857"
        private const val characteristicUuid = "494e5445-4c4c-495f-524f-434b535f2011"
    }

    override fun thGattCallback(): AbsThGattCallbackImp {
        return ThGattCallbackImp()
    }

    override fun serviceUUID(): UUID {
        return UUID.fromString(serviceUuid)
    }

    override fun characteristicUUID(): UUID {
        return UUID.fromString(characteristicUuid)
    }

    override fun isSelfComm(serviceUUID: String, characteristicUUID: String, bytes: ByteArray): Boolean {
        val result = serviceUuid == serviceUUID && characteristicUuid == characteristicUUID
        return if (result && bytes.size >= 2) {
            val proType = bytes[0]
            val isAbComm = proType == AbProtocol.PRO_TYPE
            val isCommonPro = bytes[0] == BleThProtocol.SINGLE_READ || bytes[0] == BleThProtocol.SINGLE_WRITE
            //是ab指令或通用的单包指令
            isAbComm || isCommonPro
        } else {
            result
        }
    }

    override fun parsePriority(): Int {
        return ThConsV1.comm_parse_priority_th_comm
    }
}