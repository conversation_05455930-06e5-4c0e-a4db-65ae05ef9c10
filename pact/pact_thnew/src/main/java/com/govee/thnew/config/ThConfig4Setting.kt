package com.govee.thnew.config

import com.govee.base2home.Constant4L5
import com.govee.base2home.ota.OtaType
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.util.NumberUtil
import com.govee.base2newth.ThConsV1

/**
 * <AUTHOR>
 * @date created on 2024/2/26
 * @description 温湿度计-->设置页相关的配置类
 */
object ThConfig4Setting {

    const val UNDEFINED = -1
    const val UPGRADE_WAY_4_FRK = 0
    const val UPGRADE_WAY_4_V1 = 1
    const val UPGRADE_WAY_4_V2 = 2

    const val TH_WARN_TYPE_4_OLD = 1
    const val TH_WARN_TYPE_4_NEW = 2
    const val TH_WARN_TYPE_4_NO = 3

    /**
     * 是否使用新设置页
     */
    fun useNewSettingPage(goodsType: Int, sku: String): Boolean {
        return when (sku) {
            Constant4L5.H5108,
            Constant4L5.H5111,
            Constant4L5.H5171,
                -> {
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 获取升级方式
     */
    fun getUpgradeWay(goodsType: Int, sku: String, bleHv: String): Int {
        return when (sku) {
            Constant4L5.H5100,
            Constant4L5.H5103,
            Constant4L5.H5104,
            Constant4L5.H5105,
            Constant4L5.H5106,
            Constant4L5.H5108,
            Constant4L5.H5110,
            Constant4L5.H5111,
            Constant4L5.H5171,
            Constant4L5.H5112,
                -> {
                UPGRADE_WAY_4_FRK
            }

            Constant4L5.H5074,
                -> {
                val isOtaV1 = NumberUtil.parseVersion(bleHv) >= NumberUtil.parseVersion("2.00.00")
                if (isOtaV1) {
                    UPGRADE_WAY_4_V1
                } else {
                    UPGRADE_WAY_4_V2
                }
            }

            Constant4L5.H5101,
            Constant4L5.H5102,
            Constant4L5.H5174,
            Constant4L5.H5177,
                -> {
                val isFRKOta = OtaType.isFRKOtaV1(bleHv)
                if (isFRKOta) {
                    UPGRADE_WAY_4_FRK
                } else {
                    UPGRADE_WAY_4_V1
                }
            }

            Constant4L5.H5051,
            Constant4L5.H5052,
            Constant4L5.H5071
                -> {
                UPGRADE_WAY_4_V2
            }

            Constant4L5.H5179,
                -> {
                when (bleHv) {
                    Constant4L5.H5179_BLE_HV_OLD -> {
                        UPGRADE_WAY_4_V2
                    }

                    Constant4L5.H5179_BLE_HV_NEW -> {
                        UPGRADE_WAY_4_FRK
                    }

                    else -> {
                        UNDEFINED
                    }
                }
            }

            Constant4L5.H5072,
            Constant4L5.H5075,
            Constant4L5.B5178,
                -> {
                UPGRADE_WAY_4_V1
            }

            else -> {
                UNDEFINED
            }
        }
    }

    /**
     * 是否支持低电量告警设置
     * 备注：first->是否支持服务端推送，second->是否支持本地push,third->是否需要介绍弹窗
     */
    fun supportLowBatAlarm(goodsType: Int, sku: String): Triple<Boolean, Boolean, Boolean> {
        return when (sku) {
            Constant4L5.H5051,
            Constant4L5.H5108,
                -> {
                Triple(true, ThConfig4Support.supportLocalPush(goodsType, sku), false)
            }

            Constant4L5.H5111,
            Constant4L5.H5171,
            Constant4L5.H5112,
                -> {
                Triple(true, ThConfig4Support.supportLocalPush(goodsType, sku), true)
            }

            else -> {
                Triple(false, false, false)
            }
        }
    }

    /**
     * 是否支持音量设置
     */
    fun supportVolumeLevel(goodsType: Int, sku: String): Boolean {
        return when (sku) {
            Constant4L5.H5111,
            Constant4L5.H5171,
            Constant4L5.H5112,
                -> {
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 是否支持设备报警时长设置
     */
    fun supportDeviceWarningTime(goodsType: Int, sku: String): Boolean {
        return false
    }

    /**
     * 是否支持舒适度设置
     */
    fun supportComfortSetting(goodsType: Int, sku: String): Boolean {
        return when (goodsType) {
            GoodsType.GOODS_TYPE_VALUE_H5171 -> {
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 获取温湿度校准值的范围
     * 备注：first->摄氏度温度范围(first->low,second->high)，second->华氏度温度范围，third->湿度范围
     */
    fun getCalibrationRange(goodsType: Int, sku: String): Triple<Pair<Float, Float>, Pair<Float, Float>, Pair<Float, Float>> {
        return when (sku) {
            Constant4L5.H5051,
            Constant4L5.H5052,
            Constant4L5.H5074,
                -> {
                Triple(Pair(ThConsV1.TEM_MIN_CALI_V1, ThConsV1.TEM_MAX_CALI_V1), Pair(ThConsV1.TEM_MIN_CALI_FAH_V1, ThConsV1.TEM_MAX_CALI_FAH_V1), Pair(ThConsV1.HUM_MIN_CALI_V1, ThConsV1.HUM_MAX_CALI_V1))
            }

            else -> {
                Triple(Pair(ThConsV1.TEM_MIN_CALI, ThConsV1.TEM_MAX_CALI), Pair(ThConsV1.TEM_MIN_CALI_FAH, ThConsV1.TEM_MAX_CALI_FAH), Pair(ThConsV1.HUM_MIN_CALI, ThConsV1.HUM_MAX_CALI))
            }
        }
    }

    /**
     * 是否支持延时告警
     */
    fun supportDelayAlarm(goodsType: Int, sku: String): Boolean {
        return when (sku) {
            Constant4L5.H5112 -> {
                true
            }

            else -> {
                Constant4L5.isRefrigeratorTh(goodsType, sku)
            }
        }
    }

    /**
     * 获取设备电池类型
     * 备注:若返回空字符，说明设备需直连电源，无需该项
     */
    fun getBatteryType(goodsType: Int, sku: String, order: Int = 0): String {
        return when (sku) {
            Constant4L5.H5053,
            Constant4L5.H5075,
            Constant4L5.H5104,
            Constant4L5.H5108,
            Constant4L5.H5110,
            Constant4L5.H5111,
            Constant4L5.H5171,
                -> {
                "2 AAA"
            }

            Constant4L5.H5074,
                -> {
                "1 CR2477"
            }

            Constant4L5.H5101,
            Constant4L5.H5105,
                -> {
                "1 CR2450"
            }

            Constant4L5.H5100,
            Constant4L5.H5102,
                -> {
                "1 AAA"
            }

            Constant4L5.H5051,
            Constant4L5.H5052,
            Constant4L5.H5071,
            Constant4L5.H5072,
            Constant4L5.H5174,
            Constant4L5.H5177,
            Constant4L5.H5112,
                -> {
                "3 AAA"
            }

            Constant4L5.H5103,
            Constant4L5.H5179,
                -> {
                "3 AA"
            }

            Constant4L5.B5178,
                -> {
                if (order == 0) {
                    "3 AAA"
                } else {
                    "2 AAA"
                }
            }

            else -> {
                ""
            }
        }
    }

    /**
     * 是否支持告警设置
     */
    fun supportWarnSetting(goodsType: Int, sku: String): Int {
        return when {
            arrayListOf(
                Constant4L5.H5179,
                Constant4L5.H5110
            ).contains(sku) -> {
                TH_WARN_TYPE_4_OLD
            }

            Constant4L5.supportThSkuList4NewThWarn().contains(sku)
                -> {
                TH_WARN_TYPE_4_NEW
            }

            else -> {
                TH_WARN_TYPE_4_NO
            }
        }
    }

    /**
     * 温度、湿度等只能在连接蓝牙的情况下设置，即绑定网关的情况下也只能通过蓝牙设置
     * 备注：first-->可通过网关设置，second:连接蓝牙设置
     */
    fun setAlarmByWay(goodsType: Int, sku: String): Pair<Boolean, Boolean> {
        val canGw = Constant4L5.isH5151SubSku(sku)
        return when (sku) {
            Constant4L5.H5111,
            Constant4L5.H5171
                -> {
                Pair(false, canGw)
            }

            else -> {
                Pair(canGw, canGw)
            }
        }
    }

    /**
     * 即使绑定了网关，也只支持蓝牙连接下操作
     * 需要联网才能进行设置页的设置，没联网的时候会toast提示
     */
    fun onlyBleBindGateway(goodsType: Int, sku: String): Boolean {
        return when (sku) {
            Constant4L5.H5171 -> true
            else -> false
        }
    }

    /**
     * 设置页属性是否使用新设置接口
     */
    fun setSettingsWithNew(goodsType: Int, sku: String): Boolean {
        return Constant4L5.setSettingsWithNew(goodsType, sku)
    }
}