package com.govee.thnew.pact

import com.govee.base2home.Constant4L5
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.sku.IMaker
import com.govee.base2home.sku.ISkuItem
import com.govee.thnew.config.ThConfig4Support

/**
 * <AUTHOR>
 * @date created on 2024/2/20
 * @description 温湿度计-->添加绑定时选中sku item的注册辅助类
 */
class ThSkuMaker : IMaker {

    private val makers: MutableList<ISkuItem> = ArrayList()

    init {
        for (deviceTypePair in ThConfig4Support.deviceItemTypes) {
            makers.add(ThSkuBindItem(deviceTypePair.first, deviceTypePair.second))
        }
        makers.add(ThSkuBindItem(GoodsType.GOODS_TYPE_VALUE_4_H5112, Constant4L5.H5112))
    }

    override fun getSupportMakers(): List<ISkuItem> {
        return makers
    }
}