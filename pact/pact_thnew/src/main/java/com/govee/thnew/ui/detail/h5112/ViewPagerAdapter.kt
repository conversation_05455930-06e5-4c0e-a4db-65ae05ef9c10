package com.govee.thnew.ui.detail.h5112

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.lifecycle.Lifecycle
import androidx.viewpager2.adapter.FragmentStateAdapter

/**
 * <AUTHOR>
 * @date created on 2025/5/19
 * @description  Fragment中ViewPageV2+Fragment的适配
 */
class ViewPagerAdapter(private val fragments: ArrayList<Pair<Int, Fragment>>, fragmentManager: FragmentManager, lifecycle: Lifecycle) : FragmentStateAdapter(fragmentManager, lifecycle) {

    override fun getItemCount(): Int {
        return fragments.size
    }

    override fun createFragment(position: Int): Fragment {
        return fragments[position].second
    }

    override fun getItemId(position: Int): Long {
        return fragments[position].first.toLong()
    }
}