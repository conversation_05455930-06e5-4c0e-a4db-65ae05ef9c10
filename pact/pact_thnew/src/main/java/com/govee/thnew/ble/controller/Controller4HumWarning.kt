package com.govee.thnew.ble.controller

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4HumWarning

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->读写湿度告警信息的Controller
 */
class Controller4HumWarning : AbsControllerWithCallback {
    /**
     * 湿度告警下限值*100
     */
    private var minHum = 0

    /**
     * 湿度告警上限值*100
     */
    private var maxHum = 0
    private var openWarning = false

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     *
     * @param openWarning
     * @param minHum
     * @param maxHum
     */
    constructor(
        openWarning: Boolean,
        minHum: Int,
        maxHum: Int,
        writeCallBack: ((result: Boolean) -> Unit)
    ) : super(writeCallBack) {
        this.minHum = checkHum(minHum)
        this.maxHum = checkHum(maxHum)
        this.openWarning = openWarning
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_hum_warning
    }

    private fun checkHum(hum: Int): Int {
        var lastHum = hum
        val minHum: Int = ThConsV1.HUM_MIN_VALUE * 100
        val maxHum: Int = ThConsV1.HUM_MAX_VALUE * 100
        lastHum = lastHum.coerceAtLeast(minHum)
        lastHum = lastHum.coerceAtMost(maxHum)
        return lastHum
    }

    override fun translateWrite(): ByteArray {
        val openWarningValue = if (openWarning) 1.toByte() else 0.toByte()
        val minHumValues = BleUtil.getSignedBytesFor2(minHum, false)
        val maxHumValues = BleUtil.getSignedBytesFor2(maxHum, false)
        return byteArrayOf(openWarningValue, minHumValues[0], minHumValues[1], maxHumValues[0], maxHumValues[1])
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val openWarning = validBytes[0].toInt() == 1
        var minHum = BleUtil.convertTwoBytesToShort(validBytes[1], validBytes[2])
        minHum = checkHum(minHum)
        var maxHum = BleUtil.convertTwoBytesToShort(validBytes[3], validBytes[4])
        maxHum = checkHum(maxHum)
        Event4HumWarning.sendSuc(isWrite, commandType, proType, openWarning, minHum, maxHum)
        return true
    }

    override fun fail() {
        super.fail()
        Event4HumWarning.sendFail(isWrite, commandType, proType)
    }
}