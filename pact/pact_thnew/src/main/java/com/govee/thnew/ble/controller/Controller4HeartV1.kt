package com.govee.thnew.ble.controller

import com.govee.base2home.Constant4L5
import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsOnlyReadSingleController
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.event.Event4H5112Heart
import com.govee.thnew.ble.event.Event4Heart
import com.govee.thnew.pact.ThBroadcastUtil

/**
 * <AUTHOR>
 * @date created on 2024/2/18
 * @description 温湿度计-->心跳包controller-=>V1
 * 备注：心跳返回数据：2bytes/温度+2bytes/湿度+1byte/电量
 */
class Controller4HeartV1(private val sku: String) : AbsOnlyReadSingleController() {

    override fun getCommandType(): Byte {
        return BleProtocol.HEART_value_heart
    }


    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        when (sku) {
            Constant4L5.H5112 -> {
                parseHeartInfo4H5112(validBytes)
            }

            else -> {
                parseHeartInfo4Common(validBytes)
            }
        }
        return true
    }

    /**
     * @param 2bytes/温度+2bytes/湿度+1byte/电量
     */
    private fun parseHeartInfo4Common(validBytes: ByteArray) {
        var tem = -1
        var hum = -1
        val isInvalidTh = validBytes[0] == ThBroadcastUtil.INVALID_BYTE && validBytes[1] == ThBroadcastUtil.INVALID_BYTE || validBytes[2] == ThBroadcastUtil.INVALID_BYTE && validBytes[3] == ThBroadcastUtil.INVALID_BYTE
        if (!isInvalidTh) {
            //温度
            tem = BleUtil.getSignedShort(validBytes[0], validBytes[1]).toInt()
            //湿度
            hum = BleUtil.getSignedInt(byteArrayOf(validBytes[2], validBytes[3]), true)
        }
        //电量
        val battery = BleUtil.getUnsignedByte(validBytes[4])
        //是否已处于温度告警-->H5108的延时告警需要
        val isTemAlarming = BleUtil.getUnsignedByte(validBytes[5]) > 0
        Event4Heart.sendSuc(isWrite, commandType, proType, tem, hum, battery, isTemAlarming = isTemAlarming)
//        SafeLog.i("xiaobing") { "Controller4HeartV1--parseHeartInfo4Common-->心跳数据-=>${JsonUtil.toJson(arrayOf(tem, hum, battery, isTemAlarming))}" }
    }

    /**
     * @param 2bytes/温度(温湿度探针)+2bytes/湿度+2bytes/温度(温度探针)+1byte/电量+1byte报警标识位
     */
    private fun parseHeartInfo4H5112(validBytes: ByteArray) {
        var tem4Pb1 = -1
        var hum = -1
        val isInvalid4Pb1 = validBytes[0] == ThBroadcastUtil.INVALID_BYTE && validBytes[1] == ThBroadcastUtil.INVALID_BYTE || validBytes[2] == ThBroadcastUtil.INVALID_BYTE && validBytes[3] == ThBroadcastUtil.INVALID_BYTE
        if (!isInvalid4Pb1) {
            //温度
            tem4Pb1 = BleUtil.getSignedIntV2(byteArrayOf(validBytes[0], validBytes[1]), true)
            //湿度
            hum = BleUtil.getSignedInt(byteArrayOf(validBytes[2], validBytes[3]), true)
        }
        var tem4Pb2 = -1
        val isInvalid4Pb2 = validBytes[4] == ThBroadcastUtil.INVALID_BYTE && validBytes[5] == ThBroadcastUtil.INVALID_BYTE
        if (!isInvalid4Pb2) {
            //温度
            tem4Pb2 = BleUtil.getSignedIntV2(byteArrayOf(validBytes[4], validBytes[5]), true)
        }
        //电量
        val battery = BleUtil.getUnsignedByte(validBytes[6])
        //报警标志位，bit 位=0表示对应事件无报警。
        //其中 bit0=1：低于设置温度报警，bit1=1高于设置温度报警，
        //bit2=1：低于设置湿度报警，bit3=1高于设置湿度报警，
        //bit4=1：低于设置NTC温度报警，bit5=1高于设置NTC温度报警
        val warnBinaryStr = BleUtil.hexToBinary(BleUtil.toHex(validBytes[7])).reversed()
        //温湿度探针--温度报警状态(0：无报警，1：低于，2高于，下同)
        val pb1TemWarnType = warnBinaryStr.substring(0, 2).reversed().toInt(2)
        //温湿度探针--湿度报警状态
        val pb1HumWarnType = warnBinaryStr.substring(2, 4).reversed().toInt(2)
        //温度探针--湿度报警值状态
        val pb2TemWarnType = warnBinaryStr.substring(4, 6).reversed().toInt(2)

        Event4H5112Heart.sendSuc(isWrite, commandType, proType, tem4Pb1, hum, tem4Pb2, battery, pb1TemWarnType, pb1HumWarnType, pb2TemWarnType)
//        SafeLog.i("xiaobing") { "Controller4HeartV1--parseHeartInfo4H5112-->原始数据-=>${BleUtil.bytesToHexString(validBytes)}" }
//        SafeLog.i("xiaobing") { "Controller4HeartV1--parseHeartInfo4H5112-->心跳数据-=>${JsonUtil.toJson(arrayOf(tem4Pb1, hum, tem4Pb2, battery, pb1TemWarnType, pb1HumWarnType, pb2TemWarnType))}" }
    }

    override fun fail() {
        //心跳包；失败不处理
    }
}