package com.govee.thnew.add.h5112

import android.os.Handler
import android.os.Looper
import android.os.Message
import androidx.lifecycle.MutableLiveData
import com.govee.base2home.Constant4L5
import com.govee.base2home.bean.Gateway
import com.govee.base2home.event.Event4RefreshDevListFinish
import com.govee.base2home.iot.Cmd
import com.govee.base2home.iot.Iot
import com.govee.base2home.iot.IotTransactions
import com.govee.base2home.iot.ResultPt
import com.govee.base2home.iot.getJsonObjectStr
import com.govee.base2home.iot.protype.v2.IotMsgEventV2
import com.govee.base2home.main.choose.DeviceFilter
import com.govee.base2home.main.gw.GwInfo.Ext4Other
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.push.EventDeviceListFresh
import com.govee.base2kt.utils.BleUtils
import com.govee.base2light.kt.ble.AbsController
import com.govee.base2light.pact.iot.CmdPtReal
import com.govee.base2newth.BleThProtocol
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.ble.BleController
import com.govee.ble.event.EventBleBroadcastListenerTrigger
import com.govee.home.account.config.AccountConfig
import com.govee.kt.ui.device.GatewaysOp
import com.govee.mvvm.base.viewmodel.BaseViewModel
import com.govee.mvvm.ext.request
import com.govee.thnew.net.Request4BindH5112
import com.govee.thnew.net.SubDevice4H5112
import com.govee.thnew.net.thNewNetService
import com.govee.thnew.pact.h5112.Ext4H5112
import com.ihoment.base2app.infra.SafeLog.Companion.i
import com.ihoment.base2app.util.CaughtRunnable
import com.ihoment.base2app.util.JsonUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2025/5/21
 * @description H5112-绑定H5044网关页面的ViewModel
 */
class Vm4H5112BindGw : BaseViewModel() {

    companion object {
        private const val what_timer_iot_check = 1000

        //iot通讯回复超时
        private const val IOT_COMM_OVER_TIME = 20 * 1000L
    }

    private var sku = ""
    private var device = ""

    /**
     * h5044网关列表回调
     */
    val ld4H5044GwList = MutableLiveData<List<Gateway>>()

    /**
     * 列表定时刷新
     */
    val ld4AdapterFresh: MutableLiveData<Boolean> = MutableLiveData()

    /**
     * 绑定网关相关
     */
    private var bindResultCallback: ((suc: Boolean) -> Unit)? = null
    private var bindGwOutTimeRunnable: BindGwOutTimeRunnable? = null
    private var toBindGw: Gateway? = null
    private var isLoadingDeviceList = false

    init {
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
    }

    private val handler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == what_timer_iot_check) {
                doHandler(what_timer_iot_check)
                GatewaysOp.iotCheck4Timer()
                ld4AdapterFresh.value = true
            }
        }
    }

    private fun doHandler(what: Int, delayTimeMills: Long = 60 * 1000L) {
        handler.removeMessages(what)
        handler.sendEmptyMessageDelayed(what, delayTimeMills)
    }

    /**
     * 初始化
     */
    fun init(sku: String, device: String) {
        this.sku = sku
        this.device = device
        if (AccountConfig.read().isHadToken) {
            getGwList()
        } else {
            ld4H5044GwList.value = arrayListOf()
        }
    }

    /**
     * 获取网关列表
     */
    private fun getGwList() {
        request({
            thNewNetService.getGatewayList()
        }, success = {
            if (it.isNullOrEmpty()) {
                ld4H5044GwList.postValue(arrayListOf())
            } else {
                val h5044GwList = arrayListOf<Gateway>()
                it.forEach { gwInfo ->
                    if (gwInfo.goodsType == GoodsType.GOODS_TYPE_VALUE_4_GW_H5044) {
                        val gwDevice = gwInfo.getDevice()
                        val model = GatewaysOp.makeGatewayModel(gwDevice)
                        h5044GwList.add(Gateway(Gateway.item_type_4_can_show, model))
                    }
                }
                if (h5044GwList.isNotEmpty()) {
                    //断开蓝牙连接
                    BleController.getInstance().disconnectBleAndNotify()
                    //开启蓝牙广播扫描
                    doHandler(what_timer_iot_check, 200L)
                    EventBleBroadcastListenerTrigger.sendEventBleBroadcastListenerTrigger(true)
                } else {
                    handler.removeMessages(what_timer_iot_check)
                    EventBleBroadcastListenerTrigger.sendEventBleBroadcastListenerTrigger(false)
                }
                DeviceFilter.getInstance.updateGwBleAddress(it)
                ld4H5044GwList.postValue(h5044GwList)
            }
        }, error = {
            ld4H5044GwList.value = arrayListOf()
        })
    }

    /**
     * iot读写操作的回调事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onIotMsgEventV2(iotEvent: IotMsgEventV2) {
        toBindGw?.run {
            val iotMsg = iotEvent.msgV2
            val isSameDevice = iotMsg.isSameDevice(this.gatewayModel.device.sku, this.gatewayModel.device.device)
            if (!isSameDevice) {
                return
            }
            //status,ptReal,multiSync中的数据字端都在"op"字段中，若有其他情况，用parseIotExtInfo进行特殊对待
            getJsonObjectStr(iotEvent.jsonStr, Cmd.parse_json_op)?.let {
                JsonUtil.fromJson(it, ResultPt::class.java)?.run {
                    parseIotOpInfo(iotMsg.cmd, this.bytes())
                }
            }
        }
    }

    /**
     * 通用iot回复信息解析(即数据字段在"op"中)
     */
    private fun parseIotOpInfo(cmd: String, iotResponseBytes: List<ByteArray>) {
        when (cmd) {
            Cmd.ptReal,
            Cmd.multiSync -> {
                val isValidDevice = (Constant4L5.H5044 == toBindGw?.gatewayModel?.device?.sku) && (Constant4L5.H5112 == sku)
                val isValidCommand = (bindGwOutTimeRunnable != null) && iotResponseBytes.isNotEmpty()
                if (isValidDevice && isValidCommand) {
                    //处理回复
                    val iotRespBys = iotResponseBytes[0]
                    i(TAG) { "Vm4H5112BindGw--parseIotOpInfo-->cmd:${cmd},${BleUtil.bytesToHexString(iotRespBys)}" }
                    val isBindResult = (iotRespBys.size == 20) && (iotRespBys[0] == BleThProtocol.NOTIFY) && (iotRespBys[1] == BleProtocol.COMMAND_TYPE_4_BIND_SUB_NOTIFY)
                    if (isBindResult) {
                        handler.removeCallbacks(bindGwOutTimeRunnable!!)
                        bindGwOutTimeRunnable = null
                        val validBytes = ByteArray(17)
                        System.arraycopy(iotRespBys, 2, validBytes, 0, validBytes.size)
                        //1byte/子设备序号&绑定结果(最高位1bit：1 绑定成功，0绑定失败 低7bit：子设备序号（绑定成功时序号才有效）)+16bytes/子设备id
                        val firstBinaryStr = BleUtil.hexToBinary(BleUtil.toHex(validBytes[0]))
                        val result = firstBinaryStr.substring(0, 1).toInt(2) == 1
                        if (result) {
                            val sno = firstBinaryStr.substring(1, firstBinaryStr.length).toInt(2)
                            val deviceIdBytes = ByteArray(16)
                            System.arraycopy(validBytes, 1, deviceIdBytes, 0, deviceIdBytes.size)
                            val deviceId = BleUtil.toAddressBytes(deviceIdBytes, true)
                            toBindGw?.run {
                                if (deviceId == device) {
                                    toBindH51124H5044ToService(this.gatewayModel.device.sku, this.gatewayModel.device.device, SubDevice4H5112(sku, device).apply {
                                        settings = Ext4H5112(sno)
                                    }) {
                                        if (it) {
                                            //绑定成功，重新加载设备列表
                                            EventDeviceListFresh.sendEventDeviceListFresh(true)
                                            isLoadingDeviceList = true
                                        } else {
                                            bindResultCallback?.invoke(false)
                                        }
                                    }
                                } else {
                                    bindResultCallback?.invoke(false)
                                }
                            } ?: run {
                                bindResultCallback?.invoke(false)
                            }
                        } else {
                            bindResultCallback?.invoke(false)
                        }
                    }
                }
            }

            else -> {}
        }
    }

    private fun toBindH51124H5044ToService(
        gwSku: String,
        gwDevice: String,
        subDevice: SubDevice4H5112,
        callback: (result: Boolean) -> Unit,
    ) {
        request({
            thNewNetService.bindSubDevices4H5112(Request4BindH5112(gwSku, gwDevice, mutableListOf<SubDevice4H5112>().apply {
                add(subDevice)
            }))
        }, success = {
            callback.invoke(true)
        }, error = {
            callback.invoke(false)
        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4DeviceListRefresh(event: Event4RefreshDevListFinish) {
        if (!isLoadingDeviceList) {
            return
        }
        isLoadingDeviceList = false
        handler.postDelayed(object : CaughtRunnable() {
            override fun runSafe() {
                i(TAG) { "Vm4H5112BindGw--runSafe-->设备列表更新完毕..." }
                bindResultCallback?.invoke(true)
            }
        }, 1000)
    }

    /**
     * 所选网关添加H5112是否已达上限
     * 备注：first->915通讯设备是否已达上限，second->温湿度计设备是否已达上限
     */
    val ld4AddH5112Full by lazy {
        MutableLiveData<Pair<Boolean, Boolean>>()
    }

    /**
     * 将H5112绑定到H5044
     */
    fun bindH5112ToH5044(gwInfo: Gateway, bindResultCallback: (suc: Boolean) -> Unit) {
        this.toBindGw = gwInfo
        this.bindResultCallback = bindResultCallback
        //发送iot指令
        getGwTopic()?.run {
            request({
                thNewNetService.subDevices(gwInfo.gatewayModel.device.sku ?: "", gwInfo.gatewayModel.device.device ?: "")
            }, success = {
                var h5059Num = 0
                var h5112Num = 0
                var bleThNum = 0
                for (absDevice in it) {
                    when (absDevice.sku) {
                        Constant4L5.H5059 -> {
                            h5059Num++
                        }

                        Constant4L5.H5112 -> {
                            h5112Num++
                        }

                        else -> {
                            bleThNum++
                        }
                    }
                }
                if ((bleThNum + h5112Num) >= ThConsV1.MAX_NUM_4_H5044_BIND_TH_SUB) {
                    bindResultCallback.invoke(false)
                    ld4AddH5112Full.postValue(Pair(false, true))
                    return@request
                }
                if ((h5059Num + h5112Num) >= ThConsV1.MAX_BIND_SUB_DEVICE_NUM_H5044) {
                    bindResultCallback.invoke(false)
                    ld4AddH5112Full.postValue(Pair(true, false))
                    return@request
                }
                //先写入网关设备，再绑定到服务器
                val bindController = Controller4H5044BindH5112.makeController(device)
                val ptRealCmd = CmdPtReal(bindController.getNextCommBytes())
                Iot.getInstance.write(this, IotTransactions().createTransaction(false), ptRealCmd)
                //设置iot指令回复超时(上报的时间较长)
                bindGwOutTimeRunnable = BindGwOutTimeRunnable(bindController)
                handler.removeCallbacks(bindGwOutTimeRunnable!!)
                handler.postDelayed(bindGwOutTimeRunnable!!, IOT_COMM_OVER_TIME)
            }, error = {
                bindResultCallback.invoke(false)
            })
        } ?: run {
            bindResultCallback.invoke(false)
        }
    }

    private fun getGwTopic(): String? {
        return JsonUtil.fromJson(toBindGw?.gatewayModel?.device?.deviceExt?.deviceSettings ?: "", Ext4Other::class.java)?.topic
    }

    override fun onCleared() {
        handler.removeCallbacksAndMessages(null)
        GatewaysOp.clearUiFreshCallback()
        EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
        super.onCleared()
    }

    inner class BindGwOutTimeRunnable(var controller: AbsController) : CaughtRunnable() {

        override fun runSafe() {
            bindResultCallback?.invoke(false)
        }
    }
}

/**
 * H5044绑定H5112子设备
 */
private class Controller4H5044BindH5112 private constructor(extBytes: ByteArray?) :
    AbsController(true, extBytes) {
    override fun getCommandType(): Byte {
        return BleProtocol.COMMAND_TYPE_4_BIND_SUB
    }

    companion object {
        fun makeController(uuid: String, reverseOrder: Boolean = false): Controller4H5044BindH5112 {
            val bytes = BleUtils.address2Bytes(uuid, reverseOrder)
            return Controller4H5044BindH5112(bytes)
        }
    }
}