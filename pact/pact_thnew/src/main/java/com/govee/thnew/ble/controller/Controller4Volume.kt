package com.govee.thnew.ble.controller

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4Volume

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->从设备读取剩余电量的Controller
 */
class Controller4Volume : AbsControllerWithCallback {

    private var volumeLevel = 0

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     */
    constructor(volumeLevel: Int, writeCallBack: ((result: Boolean) -> Unit)) : super(writeCallBack) {
        this.volumeLevel = volumeLevel
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_volume_level
    }

    override fun translateWrite(): ByteArray {
        return byteArrayOf(volumeLevel.toByte())
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val volumeLevel = BleUtil.getUnsignedByte(validBytes[0])
        Event4Volume.sendSuc4Read(isWrite, commandType, proType, volumeLevel)
        return true
    }

    override fun fail() {
        Event4Volume.sendFail(isWrite, commandType, proType)
    }
}