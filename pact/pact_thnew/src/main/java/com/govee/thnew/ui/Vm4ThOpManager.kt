package com.govee.thnew.ui

import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Paint
import android.os.Handler
import android.os.Looper
import android.provider.MediaStore
import android.text.TextUtils
import android.view.View
import androidx.core.content.contentValuesOf
import androidx.core.graphics.createBitmap
import androidx.lifecycle.MutableLiveData
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.config.DeviceRoomOrderConfig
import com.govee.base2home.iot.Iot
import com.govee.base2home.iot.IotTransactions
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.main.OfflineDeviceListConfig
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.reform4dbgw.view.Dialog4ToBindRemind.Companion.createDialog
import com.govee.base2home.reform4dbgw.view.Event4ChangeGwForSub
import com.govee.base2home.reform4dbgw.view.Event4StickBleOp
import com.govee.base2home.reform4dbgw.view.ThDbgwLinkageSupport
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.temUnit.TemperatureUnitType
import com.govee.base2home.update.IUpdateNet
import com.govee.base2home.update.UpdateHint4VersionConfig
import com.govee.base2home.update.download.CheckVersion
import com.govee.base2home.update.net.DeviceUpdateRequest
import com.govee.base2home.update.net.DeviceUpdateResponse
import com.govee.base2home.util.CaughtRunnable
import com.govee.base2light.pact.iot.CmdPtReal
import com.govee.base2newth.AbsControllerEvent
import com.govee.base2newth.AbsSingleController
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.newchart.Event4ChangeTemUnit
import com.govee.base2newth.chart.order.Event4ChangeChartOrder
import com.govee.base2newth.data.DataConfig
import com.govee.base2newth.data.THMemoryUtil
import com.govee.base2newth.data.controller.EventBuzzerWarning
import com.govee.base2newth.db.DbController
import com.govee.base2newth.deviceitem.Ext
import com.govee.base2newth.deviceitem.Ext4Gw
import com.govee.base2newth.deviceitem.GatewayInfo
import com.govee.base2newth.form4Dbgw.ThBleLinkage
import com.govee.base2newth.net.Request4Setting
import com.govee.base2newth.other.ClearThDataUtils
import com.govee.base2newth.other.Event4HasClearCache
import com.govee.base2newth.other.Event4HasClearCache.Companion.sendEvent
import com.govee.base2newth.other.Event4LoadAllData
import com.govee.base2newth.update.UpdateSucEvent
import com.govee.bind.SafeBindMgr
import com.govee.bind.bean.ConfirmGidReq.Companion.createConfirmGidReq
import com.govee.ble.BleController
import com.govee.home.account.config.AccountConfig
import com.govee.kt.net.Request4DeviceOp
import com.govee.mvvm.base.viewmodel.BaseViewModel
import com.govee.mvvm.ext.request
import com.govee.mvvm.globalLaunch
import com.govee.mvvm.network.NetworkUtil
import com.govee.thnew.add.AddInfo
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.ThBle
import com.govee.thnew.ble.controller.Controller4ClearData
import com.govee.thnew.ble.controller.Controller4ProbeIcon
import com.govee.thnew.ble.controller.Controller4PtRealOp
import com.govee.thnew.ble.controller.Controller4TemUnit
import com.govee.thnew.ble.event.Event4H5112Heart
import com.govee.thnew.ble.event.Event4Heart
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.gidsafe.GidSafeManager
import com.govee.thnew.net.thNewNetService
import com.govee.thnew.pact.h5112.Dialog4H5112CloseWarn
import com.govee.thnew.pact.h5112.Ext4H5112
import com.govee.thnew.push.LocalWarningImp
import com.govee.thnew.ui.op.BleOpManager
import com.govee.thnew.ui.op.IotOpManager
import com.govee.thnew.ui.op.LoadThcdManager
import com.govee.thnew.ui.op.SettingOpManager
import com.govee.thnew.ui.op.TriggersSyncManager
import com.govee.ui.R
import com.govee.util.recordUseCount
import com.govee.widget.model.TemRequestDevice
import com.govee.widget.net.IWidgetNet
import com.govee.widget.net.TemHumDataListRequest
import com.govee.widget.net.TemHumDataListResponse
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.Cache
import com.ihoment.base2app.ext.isFalse
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.infra.SafeLog.Companion.i
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2024/2/22
 * @description 温湿度计-->通用的通讯操作管理类
 */
class Vm4ThOpManager private constructor(private val bindExt: AddInfo, private var gatewayInfo: GatewayInfo?, private val initFromType: Int) :
    BaseViewModel(), ThBleLinkage {

    init {
        //注册EvenBus
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
        i("xiaobing") { "Vm4ThOpManager---->设备信息->${JsonUtil.toJson(bindExt)},\n其网关信息位=->${JsonUtil.toJson(gatewayInfo)}" }
    }

    private val mHandler by lazy {
        Handler(Looper.getMainLooper())
    }

    private val transactions by lazy {
        Transactions()
    }

    companion object {
        /**
         * 该管理类的初始化来源类型
         */
        const val INIT_FROM_OLD_DETAIL = 1
        const val INIT_FROM_NEW_DETAIL = 2
        const val INIT_FROM_COMPARE = 3

        //刷新图表的来源类型
        const val RC_FROM_DETAIL = 1
        const val RC_FROM_SETTING_LOAD_ALL = 2
        const val RC_FROM_SETTING_CLEAR_DATA = 3
        const val RC_FROM_COMPARE_DATA = 4

        /**
         * 图表数据加载状态常量
         */
        const val THCD_4_WAIT_TO_LOAD = 1
        const val THCD_4_IS_LOAD_FROM_SERVICE = 2
        const val THCD_4_IS_LOAD_FROM_DEVICE = 3
        const val THCD_4_IS_LOAD_FINISH = 4

        //更新信息类型
        const val UPDATE_4_WARNING = 1
        const val UPDATE_4_DELAY_ALARM = 2
        const val UPDATE_4_CALIBRATION = 3
        const val UPDATE_4_UPLOAD_FREQUENCY = 4
        const val UPDATE_4_LOW_BATTERY = 5
        const val UPDATE_4_BUZZER_WARNING_CHANGE = 6

        //固件升级流程类型
        const val UPGRADE_FIRMWARE_4_NO = 1
        const val UPGRADE_FIRMWARE_4_FIRST_REMIND = 2
        const val UPGRADE_FIRMWARE_4_DOT_REMIND = 3
        const val UPGRADE_FIRMWARE_4_FINISH = 4

        //初始化标识位,防止重复初始化
        private var hasInit = false
        private var thOpManager: Vm4ThOpManager? = null


        /**
         * 初始化
         * 备注：1，其他的调用都要在此之后
         *      2，务必在对应页面的销毁方法中调用release()
         * @param initFromType 初始化来源类型(目前有两个初始化的地方：1->重构后的温湿度计详情页，2->未重构的温湿度计，从其自身工程的详情页跳转至数据对比页(H5106/H5107/H5109))
         *
         */
        @Synchronized
        fun init(addInfo: AddInfo, gatewayInfo: GatewayInfo?, initFromType: Int) {
            if (thOpManager == null && !hasInit) {
                hasInit = true
                //创建该管理类
                thOpManager = Vm4ThOpManager(addInfo, gatewayInfo, initFromType).apply {
                    loadThcdManager = LoadThcdManager(bindExt, mHandler, initFromType, bleOpManager)
                    settingOpManager = SettingOpManager(bindExt, mHandler, transactions)
                    initAfterCreated()
                    //将该蓝牙操作类作为粘性事件发送出去，在联网检测、添加/编辑联动等页面调用
                    EventBus.getDefault().removeStickyEvent(Event4StickBleOp::class.java)
                    Event4StickBleOp.postBleOp(this)
                }
            }
        }

        /**
         * 获取Vm4ThOpManager的实例
         */
        fun instance(): Vm4ThOpManager? {
            return thOpManager
        }
    }

    /**
     * 向外部提供该设备相关信息
     */
    fun getDeviceInfo(): AddInfo {
        return bindExt
    }

    /**
     * 向外部提供设备绑定的网关信息
     */
    fun getGateInfo(): GatewayInfo? {
        return gatewayInfo
    }

    /**
     * 是否已经绑定了网关
     * 备注:
     *      1.类H5151网关的子设备，index>=1,sno无用
     *      2.H5112作为H5044的子设备，sno>=0，index无用
     */
    fun isBindGateway(): Boolean {
        return when (bindExt.sku) {
            Constant4L5.H5112 -> {
                gatewayInfo != null && gatewayInfo?.sno != null && (gatewayInfo?.sno ?: -1) >= 0
            }

            else -> {
                gatewayInfo?.let {
                    it.index > 0
                } == true
            }
        }
    }

    /**
     * 多设备数据对比时，在加载完服务端数据后需要调用此方法，保证bleOpManager不为空才能加载蓝牙数据
     */
    fun getBleOpManager() {
        if (bleOpManager == null) {
            bleOpManager = BleOpManager()
            loadThcdManager.setBleManager(bleOpManager)
        }
    }

    /**
     * 获取设备key
     */
    private fun getKey(sku: String = bindExt.sku, device: String = bindExt.device): String {
        return "${sku}_${device}"
    }

    private fun initAfterCreated() {
        if (initFromType == INIT_FROM_COMPARE) {
            //!!!从数据对比页进行初始化后，无须如下操作
            return
        }
        if (ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)) {
            //H5053通过http接口轮询实时温湿度数据
            mHandler.post(getRealInfoRunnable)
            return
        }
        //创建蓝牙操作对象并即刻进行连接
        bleOpManager = BleOpManager().also { bleOp ->
            loadThcdManager.setBleManager(bleOp)
            toConnectOriginDevice(bleOp)
        }
        //若绑定了网关，可通过iot获取实时数据
        if (isBindGateway()) {
            iotOpManager = IotOpManager().also {
                gatewayInfo?.let { gwInfo ->
                    it.start(gwInfo, iotOpListener)
                }
            }
        }
    }

    /**
     * 去连接原始主设备
     */
    fun toConnectOriginDevice(bleOp: BleOpManager?) {
        bleOp?.start(bindExt.goodsType, bindExt.sku, bindExt.device, bindExt.address, true) { bleConnectedStatus, hasWarnInfoChanged ->
            ld4ConnectStatusChange.postValue(Pair(bleConnectedStatus, Pair(iotOpManager?.isSubIotConnected.isTrue(), iotOpManager?.isGwIotConnected.isTrue())))
            when (bleConnectedStatus) {
                BleOpManager.BLE_READ_INFO_FINISH -> {
                    //有告警信息更新
                    if (hasWarnInfoChanged) {
                        ld4UpdateDeviceInfo.postValue(Pair(true, UPDATE_4_WARNING))
                        if (!AccountConfig.read().isHadToken) {
                            updateOfflineDeviceInfo()
                        }
                    }
                    //执行蓝牙连接成功的后续操作
                    operationAfterBleReadInfo()
                }

                BleOpManager.BLE_DISCONNECT, BleOpManager.BLE_UNABLE -> {
                    //处理蓝牙连接超时
                    if (loadThcdManager.loadBleThCdIsNecessary) {
                        loadThcdManager.loadBleThCdIsNecessary = false
                        loadThcdManager.loadThCdFinish(
                            bindExt.sku,
                            bindExt.device,
                            loadThcdManager.loadServiceThCdResult,
                            null
                        )
                    }
                    //蓝牙关闭、断连不显示升级提示相关
                    ld4UpgradeVersion.postValue(Pair(UPGRADE_FIRMWARE_4_NO, null))
                }
            }
        }
    }

    /**
     * iot的信息回调监听
     */
    private val iotOpListener by lazy {
        object : IotOpManager.IotOpListener {
            override fun onStatusChange(isSubIotConnected: Boolean, isGwIotConnected: Boolean) {
                gatewayInfo?.isIotOnLine = isSubIotConnected
                ld4ConnectStatusChange.postValue(Pair(bleOpManager?.curBleStatus ?: BleOpManager.BLE_DISCONNECT, Pair(isSubIotConnected, isGwIotConnected)))
            }

            override fun iotHeartInfo(heartInfo: Pair<Triple<Int, Int, Int>, Long>) {
                //需要做时间校验(iot数据具有滞后性,另外更新时间值为0表示数据无效)
                if (heartInfo.second > 0) {
                    ld4RealTemHum.value?.second?.let { lastUpdateTimeMills ->
                        if (heartInfo.second > lastUpdateTimeMills) {
                            ld4RealTemHum.value = heartInfo
                        }
                    } ?: run {
                        ld4RealTemHum.value = heartInfo
                    }
                }
            }
        }
    }

    /**
     * 用于柱状图表页、数据对比页更新温度单位，同步给详情页的LiveData
     */
    val ld4ChangeTemUnit by lazy {
        MutableLiveData<Boolean>()
    }

    /**
     * 图表顺序变化，同步给详情页的LiveData
     */
    val ld4ChangeChartOrder by lazy {
        MutableLiveData<ArrayList<String>>()
    }

    //H5053定时获取实时温湿度数据-----------------------------------------------start-----------------------------------------------
    private val request4RealTh by lazy {
        TemHumDataListRequest(transactions.createTransaction()).apply {
            devices = ArrayList<TemRequestDevice>().apply {
                this.add(TemRequestDevice(bindExt.device, bindExt.sku))
            }
        }
    }

    /**
     * 循环从服务端获取温湿度值的Runnable
     * 备注：目前H5053使用
     */
    private val getRealInfoRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                //通过http接口获取温湿度计的最后实时数据
                //1分钟一次
                Cache.get(IWidgetNet::class.java).getTemHumDataList(request4RealTh).enqueue(IHCallBack(request4RealTh))
                mHandler.removeCallbacks(this)
                mHandler.postDelayed(this, 60 * 1000L)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onResponse4RealTh(response: TemHumDataListResponse?) {
        if (response?.data != null && response.request is TemHumDataListRequest) {
            for (temHumModel in response.data) {
                if (temHumModel.isSameDevice(bindExt.sku, bindExt.device)) {
                    //回调实时温湿度等信息
                    ld4RealTemHum.postValue(Pair(Triple(temHumModel.tem, temHumModel.hum, 100), temHumModel.time))
                    return
                }
            }
        }
    }
    //H5053定时获取实时温湿度数据-----------------------------------------------end-----------------------------------------------

    //蓝牙连接相关-----------------------------------------------start-----------------------------------------------
    /**
     * 连接状态(ble+iot)发生变化的LiveData
     * 备注：first->ble状态类型，second:first->子设备是否在线，second->gw的iot是否在线
     */
    val ld4ConnectStatusChange by lazy {
        MutableLiveData<Pair<Int, Pair<Boolean, Boolean>>>()
    }

    /**
     * 该设备是否已经连接蓝牙的实时状态
     */
    fun hasBleConnected(): Boolean {
        return bleOpManager?.curBleStatus == BleOpManager.BLE_CONNECTED_SUC || bleOpManager?.curBleStatus == BleOpManager.BLE_READ_INFO_FINISH
    }

    /**
     * 蓝牙连接成功后的后续操作步骤
     */
    //1-->读/写设备相关信息(每次蓝牙连接设备后须操作)
    @Volatile
    var loadDevInfoFinish = false

    //2-->如果支持并加入分布式网关（部分蓝牙设备），须从设备读取bdType,再同步给服务端(进详情页操作一次即可)
    @Volatile
    var loadBdTypeFinish = false

    //3-->如果支持并加入分布式网关（部分蓝牙设备），须从服务端获取联动triggers(触发条件)，同步给设备(进详情页操作一次即可)
    @Volatile
    var syncTriggersFinish = false

    //4-->载入图表数据
    //5-->检测固件升级

    /**
     * 连接蓝牙
     * 备注：主设备自身连接，前提是须设备支持ble功能
     */
    fun toConnectBle(needReadInfo: Boolean = true) {
        if (ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)) {
            return
        }
        when (bleOpManager?.curBleStatus) {
            BleOpManager.BLE_UNABLE, BleOpManager.BLE_CONNECTING -> {}

            else -> {
                //初始化标识位
                loadDevInfoFinish = false
                //ble连接
                bleOpManager?.toConnectBle(needReadInfo)
            }
        }
    }

    /**
     * 通过蓝牙从设备端载入图表数据时，进行的蓝牙连接
     */
    fun toConnectBleByLoadThcd(goodsType: Int, sku: String, device: String, bleAddress: String) {
        i("xiaobing") { "Vm4ThOpManager--toConnectBleByLoadThcd-->deviceKey=->${sku}_${device},bleAddress->${bleAddress}" }
        bleOpManager?.start(goodsType, sku, device, bleAddress, false) { bleConnectedStatus, _ ->
            when (bleConnectedStatus) {
                BleOpManager.BLE_READ_INFO_FINISH -> {
                    //执行蓝牙连接成功的后续操作
                    bleLoadThcdAfterBleReadInfo(goodsType, sku, device)
                }

                BleOpManager.BLE_DISCONNECT, BleOpManager.BLE_UNABLE -> {
                    //处理蓝牙连接超时
                    if (loadThcdManager.loadBleThCdIsNecessary) {
                        loadThcdManager.loadBleThCdIsNecessary = false
                        loadThcdManager.loadThCdFinish(
                            sku,
                            device,
                            loadThcdManager.loadServiceThCdResult,
                            null
                        )
                    }
                }
            }
        }
    }

    /**
     * 主设备蓝牙读取完设备信息后的后续操作
     * 备注："主设备"是相对与切换连接的设备而言
     */
    private fun operationAfterBleReadInfo() {
        loadDevInfoFinish = true
        //其他相关操作
        if (ThDbgwLinkageSupport.linkageSupport(bindExt.sku, bindExt.bleHardVersion, bindExt.bleSoftVersion)) {
            //分布式网关子设备的相关通讯,若已执行完成则跳过
            if (!loadBdTypeFinish || !syncTriggersFinish) {
                var triggersSyncManager: TriggersSyncManager? = TriggersSyncManager()
                triggersSyncManager?.toComm4DbgwSub(bindExt.sku, bindExt.device, true) {
                    loadBdTypeFinish = true
                    syncTriggersFinish = true
                    //载入设备端图表数据
                    bleLoadThcdAfterBleReadInfo(bindExt.goodsType, bindExt.sku, bindExt.device)
                    //置空资源
                    triggersSyncManager = null
                }
            } else {
                //载入设备端图表数据
                bleLoadThcdAfterBleReadInfo(bindExt.goodsType, bindExt.sku, bindExt.device)
            }
            //从分布式网关的子设备管理页添加设备进入详情页
            if (Constant4L5.addDeviceFromDbgwManager) {
                //修改标志位
                Constant4L5.addDeviceFromDbgwManager = false
                //显示去绑定的提示弹窗
                BaseApplication.getBaseApplication().topActivity?.let {
                    createDialog(it).show(bindExt.sku, bindExt.device)
                }
            }
        } else {
            loadBdTypeFinish = true
            syncTriggersFinish = true
            //载入设备端图表数据
            bleLoadThcdAfterBleReadInfo(bindExt.goodsType, bindExt.sku, bindExt.device)
        }
        //检测固件升级
        //主+从款sku从设备不检测升级
        if (ThConfig4Support.isMainSubDevice(bindExt.goodsType, bindExt.sku)) {
            if (bindExt.groupOrder == 0) {
                mHandler.post(checkFirmwareVsRunnable)
            } else {
                i("xiaobing") { "Vm4ThOpManager--operationAfterBleReadInfo-->从设备不检测升级。" }
            }
        } else {
            mHandler.post(checkFirmwareVsRunnable)
        }
    }

    /**
     * ble连接成功后载入温湿度图表数据，如果须要的话
     */
    private fun bleLoadThcdAfterBleReadInfo(goodsType: Int, sku: String, device: String) {
        if (loadThcdManager.loadBleThCdIsNecessary) {
            loadThcdManager.loadBleThCdIsNecessary = false
            loadThcdManager.toLoadThCdFromDeviceByBle(goodsType, sku, device)
        }
    }
    //蓝牙连接相关-----------------------------------------------end-----------------------------------------------

    //图表相关-----------------------------------------------start-----------------------------------------------
    lateinit var loadThcdManager: LoadThcdManager

    //对比数据的部分功能==================================start==================================
    /**
     *导出数据
     */
    fun exportData4Compare(views: List<View>) {
        if (views.isEmpty()) {
            return
        }
        try {
            showLoading()
            var totalHeight = 0
            for (view in views) {
                totalHeight += view.height
            }
            // 创建一个Bitmap对象，用于存储截图
            val saveBitmap = createBitmap(AppUtil.getScreenWidth(), totalHeight)
            val saveCanvas = Canvas(saveBitmap)
            //添加背景色
            saveCanvas.drawColor(ResUtil.getColor(R.color.ui_bg_color_style_35))
            //将要保存的视图拼接在一个bitmap中
            val paint = Paint()
            var lastHeight = 0.0f
            for (view in views) {
                val bitmap = createBitmap(view.width, view.height)
                val canvas = Canvas(bitmap)
                view.draw(canvas)
                saveCanvas.drawBitmap(bitmap, 0.0f, lastHeight, paint)
                lastHeight += view.height
                bitmap.recycle()
            }
            // 将Bitmap保存为图片放到相册中
            views.first().context.run {
                contentResolver.insert(MediaStore.Images.Media.EXTERNAL_CONTENT_URI, contentValuesOf())?.let {
                    contentResolver.openOutputStream(it, "rw")?.let { outputStream ->
                        saveBitmap.compress(Bitmap.CompressFormat.PNG, 90, outputStream).let { result ->
                            closeLoading()
                            val toastText = if (result) {
                                ResUtil.getString(R.string.th_text_4_export_suc)
                            } else {
                                ResUtil.getString(R.string.th_text_4_export_failure)
                            }
                            toast(toastText)
                            return
                        }
                    }
                }
            }
            closeLoading()
            toast(ResUtil.getString(R.string.th_text_4_export_failure))
        } catch (ex: Exception) {
            ex.printStackTrace()
            closeLoading()
            toast(ResUtil.getString(R.string.th_text_4_export_failure))
        }
    }

    /**
     * 是否是华氏度展示
     */
    fun isFahOpen4Compare(): Boolean {
        return TemUnitConfig.read().isTemUnitFah(
            bindExt.sku,
            bindExt.device,
            bindExt.bleHardVersion,
            bindExt.bleSoftVersion
        )
    }

    /**
     * 设置温度单位
     */
    fun setTemUnit4Compare(fahOpen: Boolean) {
        val temUnit = if (fahOpen) TemperatureUnitType.Fahrenheit else TemperatureUnitType.Celsius
        when (bindExt.sku) {
            Constant4L5.H5106,
            Constant4L5.H5140,
                -> {
                TemUnitConfig.read().setTemUnit(bindExt.sku, bindExt.device, bindExt.bleHardVersion, bindExt.bleSoftVersion, temUnit)
                //尝试向设备发送指令,不处理结果
                val tuType = if (fahOpen) Controller4TemUnit.FAH else Controller4TemUnit.CEN
                val controller4Tu = Controller4TemUnit(tuType) {}
                when (bleOpManager?.curBleStatus) {
                    BleOpManager.BLE_CONNECTED_SUC,
                    BleOpManager.BLE_READ_INFO_FINISH,
                        -> {
                        sendCommand(controller4Tu)
                    }

                    else -> {
                        DeviceListConfig.read().getDeviceByKey(bindExt.sku, bindExt.device)?.deviceExt?.deviceSettings?.let {
                            JsonUtil.fromJson(it, Ext::class.java)?.topic.let { topic ->
                                val ptCmd = CmdPtReal(controller4Tu.value)
                                Iot.getInstance.write(topic, IotTransactions().createTransaction(false), ptCmd)
                            }
                        }
                    }
                }
                //发送事件给详情页
                Event4ChangeTemUnit.sendResult(bindExt.sku, bindExt.device, temUnit)
            }

            else -> {
                TemUnitConfig.read().setTemUnit(bindExt.sku, temUnit)
            }
        }
    }

    //对比数据的部分功能==================================start==================================

    /**
     * 设置页-->获取全部数据
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4LoadAllDataStart(event: Event4LoadAllData) {
        if (event.step == Event4LoadAllData.LOAD_TH_DATA_TO_START) {
            loadThcdManager.toLoadThcdBySingle(null, RC_FROM_SETTING_LOAD_ALL)
        }
    }

    /**
     * 按数据段类型清理本地数据库数据
     *
     * @param selectType 保留数据类型
     */
    fun clearDataByType(selectType: Int) {
        showLoading()
        when (bindExt.sku) {
            Constant4L5.H5112 -> {
                ClearThDataUtils.clearByPeriodType4Thp(bindExt.sku, bindExt.device, selectType)
            }

            else -> {
                ClearThDataUtils.clearByPeriodType(bindExt.sku, bindExt.device, selectType)
            }
        }
        //有删除部分或全部数据，则数据对比时须要重新加载全部数据
        loadThcdManager.hasLoadedDevices4Compare.remove(getKey(bindExt.sku, bindExt.device))
        //提示、更新
        mHandler.postDelayed(object : CaughtRunnable() {
            override fun runSafe() {
                closeLoading()
                loadThcdManager.refreshThChartData("删除数据，selectRemain=->${selectType},保留部分数据...")
                loadThcdManager.ld4LoadThCdStep.value = loadThcdManager.loadThcdInfoMap.apply {
                    put(Pair(bindExt.sku, bindExt.device), Pair(Pair(bindExt.goodsType, bindExt.address), Triple(RC_FROM_SETTING_CLEAR_DATA, THCD_4_IS_LOAD_FINISH, null)))
                }
                toast(R.string.temhum_data_clear_suc)
            }
        }, 1000)
    }

    /**
     * 清除全部数据
     * 备注：1.若已登录，须清除服务端、设备端和app本地的图表数据，若未登录，须清除设备端和app本地的图表数据
     *      2.执行顺序：设备端-->服务端-->本地，都成功才视为成功
     */
    fun clearAllData(callback: ((suc: Boolean) -> Unit)) {
        showLoading()
        //有删除部分或全部数据，则数据对比时须要重新加载全部数据
        val isSupportOnly433 = ThConfig4Support.supportOnly433(bindExt.goodsType, bindExt.sku)
        //有删除部分或全部数据，则数据对比时须要重新加载全部数据
        loadThcdManager.hasLoadedDevices4Compare.remove(getKey(bindExt.sku, bindExt.device))
        //清除设备端数据
        if (isSupportOnly433) {
            clearAllData4Service(callback)
        } else {
            sendCommand(Controller4ClearData {
                if (it) {
                    //若已登录则删除到服务端
                    if (AccountConfig.read().isHadToken) {
                        clearAllData4Service(callback)
                    } else {
                        //若未登录则删除app本地数据
                        globalLaunch(Dispatchers.IO) {
                            //清空本地数据
                            DbController.delete(bindExt.sku, bindExt.device, false)
                            //更新清除数据时间点
                            DataConfig.read().updateLastDataClearTime(bindExt.sku, bindExt.device, System.currentTimeMillis())
                            //将该事件扩散给其他须要的地方
                            sendEvent(Event4HasClearCache.DELETE_ALL)
                            //相关回调处理
                            withContext(Dispatchers.Main) {
                                mHandler.postDelayed(object : CaughtRunnable() {
                                    override fun runSafe() {
                                        closeLoading()
                                        loadThcdManager.refreshThChartData("删除数据，设备端清空全部数据...")
                                        loadThcdManager.ld4LoadThCdStep.value =
                                            loadThcdManager.loadThcdInfoMap.apply {
                                                put(Pair(bindExt.sku, bindExt.device), Pair(Pair(bindExt.goodsType, bindExt.address), Triple(RC_FROM_SETTING_CLEAR_DATA, THCD_4_IS_LOAD_FINISH, null)))
                                            }
                                        toast(R.string.temhum_data_clear_suc)
                                        callback.invoke(true)
                                    }
                                }, 1000)
                            }
                        }
                    }
                } else {
                    globalLaunch(Dispatchers.Main) {
                        closeLoading()
                        toast(R.string.h5072_reset_device_fail)
                        callback.invoke(false)
                    }
                }
            })
        }
    }

    /**
     * 清除服务端的全部数据
     */
    private fun clearAllData4Service(callback: ((suc: Boolean) -> Unit)) {
        //设备端数据清除成功后，进行下一步=->清除服务端图表数据
        request({
            thNewNetService.clearData(Request4DeviceOp(bindExt.sku, bindExt.device))
        }, success = {
            //服务端数据清除成功，进行下一步=->清除app本地图表数据
            globalLaunch(Dispatchers.IO) {
                //清除本地数据
                DbController.delete(bindExt.sku, bindExt.device, false)
                //更新清除数据时间点
                DataConfig.read().updateLastDataClearTime(bindExt.sku, bindExt.device, System.currentTimeMillis())
                //将该事件扩散给其他须要的地方
                sendEvent(Event4HasClearCache.DELETE_ALL)
                //相关回调处理
                withContext(Dispatchers.Main) {
                    mHandler.postDelayed(object : CaughtRunnable() {
                        override fun runSafe() {
                            closeLoading()
                            loadThcdManager.refreshThChartData("删除数据，服务端清除全部数据...")
                            loadThcdManager.ld4LoadThCdStep.value =
                                loadThcdManager.loadThcdInfoMap.apply {
                                    put(Pair(bindExt.sku, bindExt.device), Pair(Pair(bindExt.goodsType, bindExt.address), Triple(RC_FROM_SETTING_CLEAR_DATA, THCD_4_IS_LOAD_FINISH, null)))
                                }
                            //统计删除数据次数
                            recordUseCount(ParamKey.delete_data, bindExt.sku)
                            //回调结果
                            callback.invoke(true)
                        }
                    }, 1000)
                }
            }
        }, successMsg = { msg ->
            toast(msg)
        }, error = { exception ->
            closeLoading()
            toast(exception.errorMsg)
            callback.invoke(false)
        })
    }

    /**
     * 切换图表顺序
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun event4ChangeChartOrder(event: Event4ChangeChartOrder) {
        ld4ChangeChartOrder.value = event.chartOrder
    }
    //图表相关-----------------------------------------------end-----------------------------------------------

    //通讯操作相关-----------------------------------------------start-----------------------------------------------
    /**
     * 蓝牙通讯管理器
     */
    private var bleOpManager: BleOpManager? = null

    /**
     * iot通讯管理器
     */
    private var iotOpManager: IotOpManager? = null

    /**
     * 实时温湿度信息(温、湿度值==实际值*100)的LiveData
     * 备注：1.first=->元组Triple:first->tem,second->hum,third->pm2.5(sku==H5112时，为探针2的温度);second->更新时间
     *      2.使用处：a->ble心跳数据，b->h5053轮询实时温湿度，c->iot的心跳数据,d->更新完图表数据
     */
    val ld4RealTemHum by lazy {
        MutableLiveData<Pair<Triple<Int, Int, Int>, Long>>()
    }

    /**
     * 蓝牙心跳回复
     * 备注：!!!主+从款sku须取切换通道后的结果，因为未切换通道默认都是读取的主设备的心跳信息
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4BleHeart(event: Event4Heart) {
        if (ThConfig4Support.isMainSubDevice(bindExt.goodsType, bindExt.sku)) {
            //从设备须读取完设备信息后再处理心跳
            if (bindExt.groupOrder > 0 && bleOpManager?.curBleStatus != BleOpManager.BLE_READ_INFO_FINISH) {
                return
            }
        }
        //回调实时温湿度等信息
        ld4RealTemHum.value = Pair(Triple(event.tem, event.hum, event.pm25), System.currentTimeMillis())
        //告警推送，iot在线则有服务端推送,否则用本地告警
        val isSubOnline = getGateInfo()?.isIotOnLine.isTrue()
        if (!isSubOnline && ThConfig4Support.supportLocalPush(bindExt.goodsType, bindExt.sku)) {
            if (AccountConfig.read().isHadToken) {
                DeviceListConfig.read().getDeviceByKey(bindExt.sku, bindExt.device)
            } else {
                OfflineDeviceListConfig.read().getDevices(bindExt.sku, bindExt.device)
            }?.let { absDevice ->
                //如果有app提醒开关，需打开提醒开关才能push
                //低电量提醒，如果有低电量开关，也需先打开低电量开关
                JsonUtil.fromJson(absDevice.deviceExt.deviceSettings ?: "", Request4Setting::class.java)?.let {
                    if (it.normalPushOnOff.isFalse()) {
                        return
                    }
                }
            }
            var supportTem = true
            val supportHum = ThConfig4Support.supportHum(bindExt.goodsType, bindExt.sku)
            //有延时告警
            if (ThConfig4Support.broadcastType(bindExt.goodsType, bindExt.sku, bindExt.device) == ThConfig4Support.BROADCAST_TYPE_V3) {
                supportTem = event.isTemAlarming
            }
            LocalWarningImp.getInstance.checkWarning(
                bindExt.sku,
                bindExt.device,
                event.tem,
                supportTem,
                event.hum,
                supportHum,
                if (bindExt.batteryWarning) event.battery else 100,
                System.currentTimeMillis()
            )
        }
    }

    /**
     * 蓝牙心跳回复
     * 备注：H5112的蓝牙心跳信息
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4H5112BleHeart(event: Event4H5112Heart) {
        //回调实时温湿度等信息
        ld4RealTemHum.value = Pair(Triple(event.tem4Pb1, event.hum, event.tem4Pb2), System.currentTimeMillis())
        //报警弹窗
        val isPb1TemWarn = event.pb1TemWarnType > 0
        val isPb1TemWarnHigh = event.pb1TemWarnType == 2
        if (isPb1TemWarn) {
            Dialog4H5112CloseWarn.showDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, true, isPb1TemWarnHigh, Pair(event.tem4Pb1, event.hum))
        } else {
            Dialog4H5112CloseWarn.hideDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, true)
        }
        val isPb1HumWarn = event.pb1HumWarnType > 0
        val isPb1HumWarnHigh = event.pb1HumWarnType == 2
        if (isPb1HumWarn) {
            Dialog4H5112CloseWarn.showDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, false, isPb1HumWarnHigh, Pair(event.tem4Pb1, event.hum))
        } else {
            Dialog4H5112CloseWarn.hideDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, false)
        }
        val isPb2TemWarn = event.pb2TemWarnType > 0
        val isPb2TemWarnHigh = event.pb2TemWarnType == 2
        if (isPb2TemWarn) {
            Dialog4H5112CloseWarn.showDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM, true, isPb2TemWarnHigh, Pair(event.tem4Pb2, 6000))
        } else {
            Dialog4H5112CloseWarn.hideDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM, true)
        }
        //告警推送，iot在线则有服务端推送,否则用本地告警
        val isSubOnline = getGateInfo()?.isIotOnLine.isTrue()
        if (!isSubOnline && ThConfig4Support.supportLocalPush(bindExt.goodsType, bindExt.sku)) {
            if (AccountConfig.read().isHadToken) {
                DeviceListConfig.read().getDeviceByKey(bindExt.sku, bindExt.device)
            } else {
                OfflineDeviceListConfig.read().getDevices(bindExt.sku, bindExt.device)
            }?.let { absDevice ->
                //如果有app提醒开关，需打开提醒开关才能push
                //低电量提醒，如果有低电量开关，也需先打开低电量开关
                JsonUtil.fromJson(absDevice.deviceExt.deviceSettings ?: "", Request4Setting::class.java)?.let {
                    if (it.normalPushOnOff.isFalse()) {
                        return
                    }
                }
            }
            //探针1(温湿度探针)
            LocalWarningImp.getInstance.checkWarning(
                bindExt.sku,
                bindExt.device,
                event.tem4Pb1,
                isPb1TemWarn,
                event.hum,
                isPb1HumWarn,
                if (bindExt.batteryWarning) event.battery else 100,
                System.currentTimeMillis()
            )
            //探针2(温度探针)
            LocalWarningImp.getInstance.checkWarning(
                bindExt.sku,
                ThConsV1.getH5112Device4Pb2(bindExt.device),
                event.tem4Pb2,
                isPb2TemWarn,
                6000,
                false,
                100,
                System.currentTimeMillis()
            )
        }
    }

    /**
     * 更新设备相关信息后回调的LiveData
     * 备注：first->结果，second->更新的类型
     */
    val ld4UpdateDeviceInfo by lazy {
        MutableLiveData<Pair<Boolean, Int>>()
    }

    /**
     * 发送ble写入指令
     */
    fun sendCommand(controller: AbsControllerWithCallback) {
        when (bindExt.sku) {
            Constant4L5.H5112 -> {
                if (hasBleConnected()) {
                    ThBle.getInstance.startControllers(controller)
                } else if (isBindGateway() && NetworkUtil.isNetworkAvailable(BaseApplication.getContext())) {
                    iotOpManager?.sendCommand(Controller4PtRealOp(gatewayInfo?.sno ?: 0, controller))
                } else {
                    controller.setFail()
                }
            }

            else -> {
                ThBle.getInstance.startControllers(controller)
            }
        }
    }

    /**
     * H5112的联网检测
     */
    fun checkNet4H5112(resultCallback: (isOk2Gw: Boolean, isOk2Sub: Boolean) -> Unit) {
        iotOpManager?.checkNet4H5112(gatewayInfo?.sno ?: 0, resultCallback)
    }

    /**
     * 设置探针图标icon序号
     */
    fun setPbIconIndex(probeIndex: Int, pbIconIndex: Int, callback: (suc: Boolean) -> Unit) {
        showLoading()
        sendCommand(Controller4ProbeIcon(probeIndex, pbIconIndex) { result ->
            if (result) {
                when (probeIndex) {
                    Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                        bindExt.pb1IconIndex = pbIconIndex
                    }

                    Constant4L5.PROBE_INDEX_4_TEM -> {
                        bindExt.pb2IconIndex = pbIconIndex
                    }

                    else -> {}
                }
                if (AccountConfig.read().isHadToken) {
                    //将探针序号设置到服务端,无须处理结果(序号只设备端使用,传服务端只是用于初始化显示)
                    val request = Request4Setting(transactions.createTransaction(), bindExt.sku, bindExt.device).apply {
                        when (probeIndex) {
                            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                                pb1IconIndex = pbIconIndex
                            }

                            Constant4L5.PROBE_INDEX_4_TEM -> {
                                pb2IconIndex = pbIconIndex
                            }

                            else -> {}
                        }
                    }
                    request({ thNewNetService.updateSettings(request) })
                    //更新到本地(用于设备列表的即时刷新)
                    DeviceListConfig.read().getDeviceByKey(bindExt.sku, bindExt.device)?.let { absDevice ->
                        JsonUtil.parseToHashmap(absDevice.deviceExt?.deviceSettings ?: "")?.let { settingMap ->
                            JsonUtil.parseToHashmap(JsonUtil.toJson(bindExt))?.let { lastInfoMap ->
                                for (key in settingMap.keys) {
                                    lastInfoMap[key]?.let { newValue ->
                                        settingMap[key] = newValue
                                    }
                                }
                            }
                            DeviceRoomOrderConfig.read().changeDevice(bindExt.sku, bindExt.device, JsonUtil.toJson(settingMap))
                        }
                    }
                } else {
                    updateOfflineDeviceInfo()
                }
            }
            globalLaunch(Dispatchers.Main) {
                closeLoading()
                if (result) {
                    toast(R.string.bbq_presettem_successful)
                } else {
                    toast(R.string.bbq_presettem_failed)
                }
                callback.invoke(result)
            }
        })
    }

    /**
     * 设置设备属性相关操作的管理类
     */
    lateinit var settingOpManager: SettingOpManager

    /**
     * 更新离线设备缓存的设备信息
     */
    fun updateOfflineDeviceInfo() {
        OfflineDeviceListConfig.read().getDevices(bindExt.sku, bindExt.device)?.let { absDevice ->
            JsonUtil.parseToHashmap(absDevice.deviceExt?.deviceSettings ?: "")?.let { settingMap ->
                JsonUtil.parseToHashmap(JsonUtil.toJson(bindExt))?.let { lastInfoMap ->
                    for (key in settingMap.keys) {
                        lastInfoMap[key]?.let { newValue ->
                            settingMap[key] = newValue
                        }
                    }
                }
                absDevice.deviceExt?.deviceSettings = JsonUtil.toJson(settingMap)
                OfflineDeviceListConfig.read().addOfflineDevice(absDevice)
            }
        }
    }

    //通讯操作相关-----------------------------------------------end-----------------------------------------------

    //升级相关-----------------------------------------------start-----------------------------------------------
    /**
     * 检测固件升级的LiveData
     * 备注：first->流程步骤类型，升级信息对象
     */
    val ld4UpgradeVersion by lazy {
        MutableLiveData<Pair<Int, CheckVersion?>>()
    }

    /**
     * 去检测固件升级的Runnable
     * 备注：升级固件和蓝牙拉取图表数据不能并行，故将升级检测放在拉取图表数据结束后
     */
    private val checkFirmwareVsRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                mHandler.removeCallbacks(this)
                if (!THMemoryUtil.getInstance().isTrendChartReading) {
                    //图表数据拉取完毕或无需拉取图表数据，进行升级检测
                    val request = DeviceUpdateRequest(transactions.createTransaction(), bindExt.bleSoftVersion, bindExt.bleHardVersion, bindExt.sku, bindExt.device, "")
                    Cache.get(IUpdateNet::class.java).checkUpdate(request).enqueue(IHCallBack(request))
                    //详情页的gid逻辑
                    if (ThConfig4Support.supportGidSafe(bindExt.goodsType, bindExt.sku, bindExt.bleHardVersion)) {
                        DeviceListConfig.read().getDeviceByKey(bindExt.sku, bindExt.device)?.let {
                            SafeBindMgr.checkDetailPageGid(
                                GidSafeManager(ThBle.getInstance),
                                createConfirmGidReq(it, bindExt.wifiHardVersion, bindExt.wifiSoftVersion)
                            )
                        }
                    }
                } else {
                    //等待图表数据拉取完毕
                    mHandler.postDelayed(this, 2000)
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventBuzzerWarning(event: EventBuzzerWarning) {
        bindExt.deviceWarning = event.open
        bindExt.deviceWarningSeconds = event.timeSeconds
        ld4UpdateDeviceInfo.postValue(Pair(true, UPDATE_4_BUZZER_WARNING_CHANGE))
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponse4CheckVersion(response: DeviceUpdateResponse) {
        if (!transactions.isMyTransaction(response)) return
        //主+从款sku的从设备只检测升级但不处理升级(检测的目的是同步版本信息)
        if (ThConfig4Support.isMainSubDevice(bindExt.goodsType, bindExt.sku)) {
            if (bindExt.groupOrder > 0) {
                return
            }
        }
        response.checkVersion?.let {
            if (it.isNeedUpdate) {
                it.curVersionSoft = response.getRequest().versionSoft
                val newSoftVersion = it.versionSoft
                val newHardVersion = it.versionHard
                if (TextUtils.isEmpty(newSoftVersion) || TextUtils.isEmpty(newHardVersion)) return
                val needShowUpdateHint = UpdateHint4VersionConfig.read().needShowUpdateHint(bindExt.sku, bindExt.device, newSoftVersion, newHardVersion)
                if (needShowUpdateHint) {
                    //首次：弹窗提醒+红点提醒
                    //记录升级提示
                    UpdateHint4VersionConfig.read().recordUpdateHint(bindExt.sku, bindExt.device, newSoftVersion, newHardVersion)
                    ld4UpgradeVersion.value = Pair(UPGRADE_FIRMWARE_4_FIRST_REMIND, it)
                } else {
                    //仅红点提示升级
                    ld4UpgradeVersion.value = Pair(UPGRADE_FIRMWARE_4_DOT_REMIND, it)
                }
            } else {
                //无需升级
                ld4UpgradeVersion.value = Pair(UPGRADE_FIRMWARE_4_NO, it)
            }
        } ?: run {
            //无需升级
            ld4UpgradeVersion.value = Pair(UPGRADE_FIRMWARE_4_NO, null)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onUpdateSucEvent(event: UpdateSucEvent) {
        //升级结束
        ld4UpgradeVersion.value = Pair(UPGRADE_FIRMWARE_4_FINISH, null)
    }
    //升级相关-----------------------------------------------end-----------------------------------------------


    //子设备切换网关相关-----------------------------------------------start-----------------------------------------------
    /**
     * 绑定网关事件的回调
     */
    val ld4ChangeGateway by lazy {
        MutableLiveData<Boolean>()
    }

    /**
     * 绑定的网关信息发生变化
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun event4GwHasUpdate(event: Event4ChangeGwForSub) {
        if (event.isSameSubDev(bindExt.sku, bindExt.device)) {
            //重置网关信息
            DeviceListConfig.read().getDeviceByKey(bindExt.sku, bindExt.device)?.deviceExt?.deviceSettings?.let {
                JsonUtil.fromJson(it, Ext4Gw::class.java)?.gatewayInfo?.let { gwInfo ->
                    gatewayInfo = gwInfo
                    when (bindExt.sku) {
                        //若为H5112,需赋值该子设备的序号
                        Constant4L5.H5112 -> {
                            JsonUtil.fromJson(it, Ext4H5112::class.java)?.let { ext4H5112 ->
                                gatewayInfo?.sno = ext4H5112.sno
                            }
                        }

                        else -> {}
                    }
                    ld4ChangeGateway.value = true
                    if (isBindGateway()) {
                        if (iotOpManager == null) {
                            iotOpManager = IotOpManager()
                        }
                        iotOpManager?.start(gwInfo, iotOpListener)
                    }
                }
            }
        }
    }
    //子设备切换网关相关-----------------------------------------------end-----------------------------------------------

    //联网检测相关-------------------------------------------------------------------------------
    override fun getBleAddress(): String {
        return bindExt.address
    }

    override fun bleConnect() {
        toConnectBle(false)
    }

    override fun startControllers(vararg controllers: AbsSingleController) {
        if (controllers.isEmpty()) {
            return
        }
        mHandler.post(SendControllersRunnable(controllers.toList()))
    }

    override fun controllerEvent(event: AbsControllerEvent?) {
        event?.let {
            ThBle.getInstance.controllerEvent(it)
        }
    }

    inner class SendControllersRunnable(private val controllers: List<AbsSingleController>) :
        CaughtRunnable() {
        override fun runSafe() {
            mHandler.removeCallbacks(this)
            if (bleOpManager?.curBleStatus == BleOpManager.BLE_READ_INFO_FINISH) {
                ThBle.getInstance.startControllers(*controllers.toTypedArray())
            } else {
                mHandler.postDelayed(this, 1000L)
            }
        }
    }

    /**
     * 资源释放
     */
    fun release(fromType: Int) {
        if (initFromType == fromType) {
            hasInit = false
            mHandler.removeCallbacksAndMessages(null)
            ThBle.getInstance.clearComm()
            ThBle.getInstance.clearDataComm()
            bleOpManager?.release()
            bleOpManager = null
            iotOpManager?.release()
            iotOpManager = null
            loadThcdManager.loadThcdInfoMap.clear()
            BleController.getInstance().disconnectBleAndNotify()
            EventBus.getDefault().removeStickyEvent(Event4StickBleOp::class.java)
            EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
            //原旧详情页共用此操作类，不能释放
            if (fromType != INIT_FROM_COMPARE && bindExt.goodsType != GoodsType.GOODS_TYPE_VALUE_H5140) {
                loadThcdManager.serviceThCdOp?.destroy()
            }
            loadThcdManager.release()
            thOpManager = null
            i("xiaobing") { "Vm4ThOpManager--release-->资源释放,fromType=->${fromType}" }
        }
    }
}
