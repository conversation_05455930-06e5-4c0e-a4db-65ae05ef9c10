package com.govee.thnew.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->读写湿度告警信息的事件
 */
class Event4HumWarning private constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON>olean, commandType: Byte, proType: Byte) : AbsControllerEvent(result, write, commandType, proType, !write) {

    /**
     * 湿度告警上限值*100
     */
    var maxHum = 0

    /**
     * 湿度告警下限值*100
     */
    var minHum = 0
    var openWarning = false

    companion object {

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(Event4HumWarning(false, write, commandType, proType))
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, openWarning: Boolean, minHum: Int, maxHum: Int) {
            val event = Event4HumWarning(true, write, commandType, proType)
            event.minHum = minHum
            event.maxHum = maxHum
            event.openWarning = openWarning
            EventBus.getDefault().post(event)
        }
    }
}