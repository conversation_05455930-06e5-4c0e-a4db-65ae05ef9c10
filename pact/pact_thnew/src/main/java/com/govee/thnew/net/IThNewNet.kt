package com.govee.thnew.net

import com.govee.base2home.UrlConstants
import com.govee.base2home.main.AbsDevice
import com.govee.base2home.main.gw.GwInfo
import com.govee.base2newth.UrlConsV1
import com.govee.base2newth.net.Request4Setting
import com.govee.kt.net.Request4DeviceOp
import com.govee.mvvm.network.BaseStatusResponse
import com.ihoment.base2app.networkV2.NetWork
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

/**
 * 接口服务对象
 */
val thNewNetService by lazy {
    NetWork.getService(IThNewNet::class.java)
}

/**
 * <AUTHOR>
 * @date created on 2024/3/18
 * @description 温湿度计_部分相关接口
 */
interface IThNewNet {

    @POST(UrlConsV1.DEVICES_DATA_CLEAR)
    suspend fun clearData(@Body request: Request4DeviceOp): BaseStatusResponse<Any?>

    /**
     * 更新设备settings的接口(旧,7.0.0及后续版本的sku设置页设置的属性基本不再使用此接口)
     * 备注：新接口 {@link com.govee.base2newth.net.smw.ISmwNet.updateSetting(或updateSetting4Callback)}
     */
    @POST(UrlConstants.DEVICE_SETTING)
    suspend fun updateSettings(@Body request: Request4Setting): BaseStatusResponse<Any?>

    @GET(UrlConstants.GATEWAY_LIST)
    suspend fun getGatewayList(): BaseStatusResponse<List<GwInfo>?>

    @GET("/app/v1/gateway/sub-devices")
    suspend fun subDevices(
        @Query("gatewaySku") sku: String,
        @Query("gatewayDevice") device: String
    ): BaseStatusResponse<MutableList<AbsDevice>>

    @POST("/bff-app/v1/gateway/bind-sub")
    suspend fun bindSubDevices4H5112(@Body request: Request4BindH5112): BaseStatusResponse<Any?>
}