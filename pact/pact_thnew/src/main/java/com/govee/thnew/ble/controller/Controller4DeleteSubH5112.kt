package com.govee.thnew.ble.controller

import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback

/**
 * <AUTHOR>
 * @date created on 2025/7/24
 * @description H5112-->绑定了网关后，删除H5112时，向网关发送解绑关系的Controller
 */
class Controller4DeleteSubH5112(private val sno: Int) : AbsControllerWithCallback(true) {

    override fun getCommandType(): Byte {
        return BleProtocol.COMMAND_TYPE_4_UNBIND_SUB_DEVICE
    }

    override fun translateWrite(): ByteArray {
        return byteArrayOf(sno.toByte())
    }
}