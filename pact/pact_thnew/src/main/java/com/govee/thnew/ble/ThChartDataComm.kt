package com.govee.thnew.ble

import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.TextUtils
import androidx.annotation.WorkerThread
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.pact.GoodsType
import com.govee.base2newth.AbsControllerEvent
import com.govee.base2newth.AbsSingleController
import com.govee.base2newth.BleThProtocol
import com.govee.base2newth.ControllerComm
import com.govee.base2newth.IAbsComm
import com.govee.base2newth.IController
import com.govee.base2newth.IControllerComm
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.data.DataTimeSet
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.ble.BleController
import com.govee.db.utils.globalLaunch
import com.govee.thnew.ble.event.Event4BleLoadThcdFinishByV0
import com.govee.thnew.config.ThConfig4Support
import com.ihoment.base2app.infra.SafeLog
import kotlinx.coroutines.Dispatchers
import java.util.UUID
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * <AUTHOR>
 * @date created on 2024/3/06
 * @description 温湿度计-->从设备端获取图表数据的通讯器
 * 备注:使用场景=->1.ThChartDataManager中
 *               2.升级页面
 */
class ThChartDataComm : IControllerComm, IAbsComm {

    companion object {
        private const val TAG = "ThChartDataComm"
        private const val serviceUuidStr = "494e5445-4c4c-495f-524f-434b535f4857"

        //加载设备端数据时通讯的特征值
        private const val characteristicUuidStr = "494e5445-4c4c-495f-524f-434b535f2012"

        //10s通信超时
        const val delay_countdown_comm_mills = 10 * 1000L
        const val what_countdown_overtime = 100

        //V0方式加载时查询相关结果
        private const val value_result_suc: Byte = 0x00
        private const val value_result_fail: Byte = 0x01
        private const val value_result_data_over: Byte = 0x02
        private const val value_result_data_querying: Byte = 0x03
    }

    private var goodsType = GoodsType.GOODES_TYPE_NO_SUPPORT
    private var sku = ""
    private var device = ""
    private val controllers = ConcurrentLinkedQueue<IController>()
    private val comm: ControllerComm
    private val mHandler: Handler

    init {
        mHandler = object : Handler(Looper.getMainLooper()) {
            override fun handleMessage(msg: Message) {
                super.handleMessage(msg)
                if (msg.what == what_countdown_overtime) {
                    dataTimeSet?.let {
                        val nextTime = it.nextTime
                        if (nextTime == null) {
                            //读取完成
                            Event4BleLoadThcdFinishByV0.sendResult(true, "加载倒计时到，已无下一段数据可加载，加载结束")
                            return
                        }
                    }
                    Event4BleLoadThcdFinishByV0.sendResult(false, "加载倒计时到，加载失败...")
                }
            }
        }
        comm = ControllerComm(mHandler, serviceUUID(), characteristicUUID())
    }

    /**
     * !!!=->须在调用该类的startController前调用,为配置不同的加载图表数据方式使用
     */
    fun setDeviceInfo(goodsType: Int, sku: String, device: String) {
        this.goodsType = goodsType
        this.sku = sku
        this.device = device
    }

    override fun startController(vararg iControllers: AbsSingleController?) {
        controllers.clear()
        controllers.addAll(listOf(*iControllers))
        next()
    }

    private operator fun next() {
        if (controllers.isEmpty()) {
            comm.controllerOver()
            return
        }
        val controller = controllers.peek()
        if (controller != null) {
            comm.sendController(controller)
        } else {
            comm.controllerOver()
        }
    }

    override fun controllerEvent(event: AbsControllerEvent?) {
        event?.let {
            val result = it.isResult
            val proType = it.proType
            val commandType = it.commandType
            val commandRetry = it.commandRetry()
            //操作失败，且指令不需要重试，则判断当前待执行的指令是否是相同指令，若是，则移除指令
            if (!result && !commandRetry) {
                val controller = controllers.peek()
                if (controller != null && controller.isSameController(proType, commandType)) {
                    controllers.remove(controller)
                }
            }
            //执行下一个
            mHandler.postDelayed({ next() }, 5)
        }
    }

    override fun clearControllers() {
        controllers.clear()
        comm.controllerOver()
        mHandler.removeCallbacksAndMessages(null)
    }

    override fun serviceUUID(): UUID {
        return UUID.fromString(serviceUuidStr)
    }

    override fun characteristicUUID(): UUID {
        return UUID.fromString(characteristicUuidStr)
    }

    //加载方式v0----------------------------------start----------------------------------
    private var sendLoadDataResult = false
    private var resendTimes = 0
    private var dataTimeSet: DataTimeSet? = null

    @Synchronized
    fun transport4V0(goodsType: Int = GoodsType.GOODES_TYPE_NO_SUPPORT, sku: String = "", dataTimeSet: DataTimeSet? = null) {
        if (goodsType > GoodsType.GOODES_TYPE_NO_SUPPORT) {
            this.goodsType = goodsType
        }
        if (!TextUtils.isEmpty(sku)) {
            this.sku = sku
        }
        if (dataTimeSet != null) {
            this.dataTimeSet = dataTimeSet
        }
        resendTimes = 0
        sendLoadDataResult = false
        this.dataTimeSet?.let {
            val nextTime = it.nextTime
            if (nextTime == null) {
                //读取完成
                Event4BleLoadThcdFinishByV0.sendResult(true, "加载完成，已无下一段数据")
                return
            } else {
                //先判断加载时间的有效性，开始时间<结束时间
                //拉取时间范围无效，会影响重拉数据
                if (nextTime.startTime >= nextTime.endTime) {
                    transport4V0()
                    return
                }
            }
            //开始载入数据
            //备注：这个在读取设备信息之后，须略作延时，不然容易失败
            mHandler.postDelayed({
                loadChartDataByV0(makeReadBytes4V0(nextTime.startTime, nextTime.endTime))
            }, 1000L)
        }
    }

    @Synchronized
    @WorkerThread
    private fun makeReadBytes4V0(startTime: Int, endTime: Int): ByteArray {
        SafeLog.i(TAG) { "ThChartDataComm--makeReadBytes4V0-->加载时间端=->${startTime}--${endTime}" }
        val value = ByteArray(10)
        value[0] = 0x00.toByte()
        value[1] = 0x00.toByte()
        val startTimeBytes = BleUtil.getSignedBytesFor4(startTime, false)
        val endTimeBytes = BleUtil.getSignedBytesFor4(endTime, false)
        System.arraycopy(startTimeBytes, 0, value, 2, 4)
        System.arraycopy(endTimeBytes, 0, value, 6, 4)
        return value
    }

    /**
     * 获取图表数据的Runnable,并加入失败重试=->V0
     */
    private fun loadChartDataByV0(loadCdByteArray: ByteArray?) {
        globalLaunch(Dispatchers.IO) {
            loadCdByteArray?.let {
                resendTimes++
                sendLoadDataResult = BleController.getInstance().sendMsg(serviceUUID(), characteristicUUID(), it)
                SafeLog.i(TAG) { "ThChartDataComm--runSafe-->resendIndex=>${resendTimes},sendBleData=->${BleUtil.bytesToHexString(it)}" }
                if (!sendLoadDataResult) {
                    if (resendTimes > 3) {
                        //发送获取数据指令失败
                        Event4BleLoadThcdFinishByV0.sendResult(false, "超出最大重试次数")
                    } else {
                        loadChartDataByV0(loadCdByteArray)
                    }
                } else {
                    doCountdown4V0()
                }
            }
        }
    }

    private fun removeCountdown4V0() {
        mHandler.removeMessages(what_countdown_overtime)
    }

    /**
     * 更新倒计时
     */
    fun doCountdown4V0() {
        mHandler.removeMessages(what_countdown_overtime)
        mHandler.sendEmptyMessageDelayed(what_countdown_overtime, delay_countdown_comm_mills)
    }

    //加载方式v0----------------------------------end----------------------------------

    override fun isSelfComm(serviceUuid: String?, characteristicUuid: String?, values: ByteArray?): Boolean {
        if (TextUtils.isEmpty(serviceUuid) || TextUtils.isEmpty(characteristicUuid) || values == null) {
            return false
        }
        if (serviceUuid != serviceUuidStr) {
            return false
        }
        if (characteristicUuid != characteristicUuidStr) {
            return false
        }
        //加载方式V1
        if (values.size > 2) {
            //这个仅3条指令
            //BleProtocol.value_write_read_data_prepare
            //BleProtocol.value_write_read_time_range
            //BleProtocol.write_stop_transport_data
            //BleProtocol.value_change_order_4_load_data
            val isSameProType = values[0] == BleThProtocol.SINGLE_WRITE
            val isChartDataComm = when (values[1]) {
                BleProtocol.value_write_read_data_prepare,
                BleProtocol.value_write_read_time_range,
                BleProtocol.write_stop_transport_data,
                BleProtocol.value_change_order_4_load_data,
                    -> {
                    true
                }

                else -> {
                    false
                }
            }
            return isSameProType && isChartDataComm
            //加载方式V0
        } else {
            return ThConfig4Support.loadBleChartDataType(goodsType, sku, device) == ThConfig4Support.LOAD_BLE_CHART_DATA_TYPE_V0
        }
    }

    override fun parse(serviceUuid: String?, characteristicUuid: String?, values: ByteArray?): Boolean {
        if (TextUtils.isEmpty(serviceUuid) || TextUtils.isEmpty(characteristicUuid) || values == null) {
            return false
        }
        when (ThConfig4Support.loadBleChartDataType(goodsType, sku, device)) {
            ThConfig4Support.LOAD_BLE_CHART_DATA_TYPE_V0 -> {
                SafeLog.i(TAG) { "ThChartDataComm--parse-->v0加载方式，指令回复=->${BleUtil.bytesToHexString(values)}" }
                if (values.size == 1) {
                    when (values[0]) {
                        value_result_suc, value_result_data_querying -> {
                            doCountdown4V0()
                        }

                        value_result_fail -> {
                            removeCountdown4V0()
                            Event4BleLoadThcdFinishByV0.sendResult(false, "指令发送失败...")
                        }

                        value_result_data_over -> {
                            transport4V0()
                        }

                        else -> {}
                    }
                }
                return false
            }

            ThConfig4Support.LOAD_BLE_CHART_DATA_TYPE_V1 -> {
                val proType = values[0]
                val commType = values[1]
                val curController = controllers.peek()
                if (curController != null && curController.isSameController(proType, commType)) {
                    val result = curController.onResult(true, values)
                    if (result) {
                        //移除已通信完的controller
                        controllers.remove(curController)
                        //无后续controller,则移除超时
                        if (controllers.isEmpty()) {
                            comm.controllerOver()
                        }
                    }
                    return true
                }
                return false
            }

            else -> {
                return false
            }
        }
    }

    override fun parsePriority(): Int {
        return ThConsV1.comm_parse_priority_heart_comm
    }
}