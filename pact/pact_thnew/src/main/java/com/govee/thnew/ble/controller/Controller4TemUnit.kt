package com.govee.thnew.ble.controller

import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4HumCali

/**
 * <AUTHOR>
 * @date created on 2024/6/11
 * @description 温湿度计-->读写温度单位的Controller
 */
class Controller4TemUnit : AbsControllerWithCallback {

    companion object {
        const val CEN = 0x00
        const val FAH = 0X01
    }

    /**
     * 温度单位类型
     */
    private var tuType = FAH

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     *
     * @param tuType
     */
    constructor(tuType: Int, writeCallBack: ((result: Boolean) -> Unit)) : super(writeCallBack) {
        this.tuType = tuType
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_tem_unit
    }

    override fun translateWrite(): ByteArray {
        return byteArrayOf(tuType.toByte())
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val tyType = validBytes[0].toInt()
        Event4HumCali.sendSuc(isWrite, commandType, proType, tyType)
        return true
    }

    override fun fail() {
        super.fail()
        Event4HumCali.sendFail(isWrite, commandType, proType)
    }
}