package com.govee.thnew.ble

import android.os.Handler
import android.os.Looper
import android.os.Message
import android.text.TextUtils
import androidx.annotation.WorkerThread
import com.govee.base2home.Constant4L5
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.util.NumberUtil
import com.govee.base2newth.IDataComm
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.data.DataTimeSet
import com.govee.base2newth.data.controller.ControllerHeartPrepare
import com.govee.base2newth.data.controller.ControllerHeartTimeRange
import com.govee.base2newth.data.controller.EventDataOver
import com.govee.base2newth.data.controller.EventDataQuerying
import com.govee.base2newth.data.controller.EventHeartPrepare
import com.govee.base2newth.data.controller.EventHeartTimeRange
import com.govee.base2newth.db.DbController
import com.govee.base2newth.db.TemHum
import com.govee.base2newth.db.TemHumPm
import com.govee.base2newth.deviceitem.AbsModel4Th
import com.govee.mvvm.globalLaunch
import com.govee.thnew.ble.controller.Controller4ChangeOrder2Ld
import com.govee.thnew.ble.event.Event4BleLoadThcdFinishByV0
import com.govee.thnew.ble.event.Event4BleLoadThcdProgress
import com.govee.thnew.ble.event.Event4BleLoadThcdResult
import com.govee.thnew.config.ThConfig4Support
import com.govee.thnew.pact.ThBroadcastUtil
import com.ihoment.base2app.infra.SafeLog.Companion.i
import com.ihoment.base2app.util.JsonUtil
import kotlinx.coroutines.Dispatchers
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.UUID

/**
 * <AUTHOR>
 * @date created on 2024/2/18
 * @description 温湿度计-->从设备端获取图表数据的通讯管理类
 */
class ThChartDataManager : IDataComm {

    companion object {
        private const val TAG = "ThChartDataManager"
        private const val serviceUuidStr = "494e5445-4c4c-495f-524f-434b535f4857"

        //数据接收特征值
        private const val characteristicUuidStr = "494e5445-4c4c-495f-524f-434b535f2013"
    }

    private var goodsType = GoodsType.GOODES_TYPE_NO_SUPPORT
    private var sku: String = ""

    @Volatile
    private var device: String = ""

    @Volatile
    private var dataTimeSet: DataTimeSet? = null
    private var inDataComm = false
    private var progress = 0
    private var all = 0
    private var curTime: Long = 0

    private val mHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (msg.what == ThChartDataComm.what_countdown_overtime) {
                loadFinish(false, "v1加载方式，count down over time")
            }
        }
    }

    override fun inDataComm(): Boolean {
        return inDataComm
    }

    override fun serviceUUID(): UUID {
        return UUID.fromString(serviceUuidStr)
    }

    override fun characteristicUUID(): UUID {
        return UUID.fromString(characteristicUuidStr)
    }

    override fun isSelfComm(serviceUuid: String, characteristicUuid: String, values: ByteArray): Boolean {
        return inDataComm && serviceUuidStr == serviceUuid && characteristicUuidStr == characteristicUuid
    }

    override fun parse(serviceUuid: String, characteristicUuid: String, values: ByteArray): Boolean {
        if (inDataComm) {
            //查询数据中；重置倒计时
            doCountdown()
            return when (ThConfig4Support.loadBleChartDataType(goodsType, sku, device)) {
                ThConfig4Support.LOAD_BLE_CHART_DATA_TYPE_V0 -> {
                    thChartDataComm.doCountdown4V0()
                    doParse4V0(BleUtil.getTimeStamp(curTime), values)
                    true
                }

                ThConfig4Support.LOAD_BLE_CHART_DATA_TYPE_V1 -> {
                    if (ThConfig4Support.supportPm25(goodsType, sku)) {
                        doParse4Thp(BleUtil.getTimeStamp(curTime), values)
                    } else {
                        when (sku) {
                            Constant4L5.H5112 -> {
                                doParse4H5112(BleUtil.getTimeStamp(curTime), values)
                            }

                            else -> {
                                doParse4V1(BleUtil.getTimeStamp(curTime), values)
                            }
                        }
                    }
                    true
                }

                else -> {
                    false
                }
            }
        }
        return false
    }

    override fun parsePriority(): Int {
        return ThConsV1.comm_parse_priority_data_comm
    }

    /**
     * 开始拉取蓝牙图表数据的入口
     * 备注：除 主+从款设备(B5178)外的设备通用
     */
    fun startDataOp(goodsType: Int, sku: String, device: String, dataTimeSet: DataTimeSet) {
        if (TextUtils.isEmpty(sku) || TextUtils.isEmpty(device)) return
        i(TAG) { "ThChartDataManager--startDataOp-->key=->${sku}_${device},开始通过ble加载设备端的图表数据..." }
        registerEvent(true)
        this.goodsType = goodsType
        this.sku = sku
        this.device = device
        this.dataTimeSet = dataTimeSet
        all = dataTimeSet.waitingDataSize
        progress = 0
        curTime = System.currentTimeMillis()
        //通知设备准备读取数据
        prepareReadData()
    }

    /**
     * 主+从款设备读取设备温湿度图表数据相关变量
     */
    private var order4CurRead = 0
    private var fromMain = true
    private var mainDevice: String = ""
    private var dataTimeSet4Main: DataTimeSet? = null
    private var dataTimeSet4Sub: DataTimeSet? = null

    /**
     * 开始拉取蓝牙图表数据的入口
     * 备注：主+从款设备用
     */
    fun startDataOp(goodsType: Int, sku: String, mainDevice: String, fromMain: Boolean, dataTimeSet4Main: DataTimeSet, dataTimeSet4Sub: DataTimeSet) {
        if (TextUtils.isEmpty(sku) || TextUtils.isEmpty(mainDevice)) return
        i(TAG) { "ThChartDataManager--startDataOp-->mainKey=->${sku}_${mainDevice},开始通过ble加载设备端的图表数据..." }
        registerEvent(true)
        this.fromMain = fromMain
        this.goodsType = goodsType
        this.sku = sku
        this.mainDevice = mainDevice
        this.dataTimeSet4Main = dataTimeSet4Main
        this.dataTimeSet4Sub = dataTimeSet4Sub
        all = dataTimeSet4Main.waitingDataSize + dataTimeSet4Sub.waitingDataSize
        progress = 0
        curTime = System.currentTimeMillis()
        //设置加载通道
        //主+从设备：先读主设备再读从设备
        order4CurRead = 0
        prepareReadData()
    }

    private fun removeCountdown() {
        mHandler.removeMessages(ThChartDataComm.what_countdown_overtime)
    }

    private fun doCountdown() {
        mHandler.removeMessages(ThChartDataComm.what_countdown_overtime)
        mHandler.sendEmptyMessageDelayed(ThChartDataComm.what_countdown_overtime, ThChartDataComm.delay_countdown_comm_mills)
    }

    private val thChartDataComm: ThChartDataComm
        get() = ThBle.getInstance.thChartDataComm

    /**
     * 通知设备准备读取数据
     */
    private fun prepareReadData() {
        if (ThConfig4Support.isMainSubDevice(goodsType, sku)) {
            device = if (order4CurRead == 0) {
                mainDevice
            } else {
                "${mainDevice}_1"
            }
        }
        //为通讯类设置设备信息
        thChartDataComm.setDeviceInfo(goodsType, sku, device)
        //根据设备类型设置其对应的加载方式
        when (ThConfig4Support.loadBleChartDataType(goodsType, sku, device)) {
            ThConfig4Support.LOAD_BLE_CHART_DATA_TYPE_V0 -> {
                dataTimeSet?.let {
                    //开始加载数据，修改标识位
                    inDataComm = true
                    //开始进行数据读取
                    thChartDataComm.transport4V0(goodsType, sku, it)
                }
            }

            ThConfig4Support.LOAD_BLE_CHART_DATA_TYPE_V1 -> {
                i(TAG) { "ThChartDataManager--prepareReadData-->加载方式V1,准备数据..." }
                if (ThConfig4Support.isMainSubDevice(goodsType, sku)) {
                    //主+从设备须先切换通讯通道
                    thChartDataComm.startController(Controller4ChangeOrder2Ld(order4CurRead) {
                        if (it) {
                            dataTimeSet = if (order4CurRead == 0) {
                                dataTimeSet4Main
                            } else {
                                dataTimeSet4Sub
                            }
                            i(TAG) { "ThChartDataManager--prepareReadData-->开始加载设备图表数据,key=->${sku}_${device}" }
                            thChartDataComm.startController(ControllerHeartPrepare())
                        } else {
                            globalLaunch(Dispatchers.Main) {
                                loadFinish(false, "主+从款设备设置拉取数据通道失败,设备序号=->${order4CurRead}")
                            }
                        }
                    })
                } else {
                    thChartDataComm.startController(ControllerHeartPrepare())
                }
            }

            else -> {}
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4V0LoadFinish(event: Event4BleLoadThcdFinishByV0) {
        loadFinish(event.loadResult, "v0方式加载结束=->${event.from}")
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4PrepareResult(event: EventHeartPrepare) {
        thChartDataComm.controllerEvent(event)
        if (event.isResult) {
            i(TAG) { "ThChartDataManager--onEvent4PrepareResult-->key=->${sku}_${device},加载方式V1,数据准备完成，下一步..." }
            //准备完成，修改标识位
            inDataComm = true
            //开始进行数据读取
            transport4V1()
        } else {
            loadFinish(false, "v1加载方式，准备数据失败")
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4LoadTimeRange(event: EventHeartTimeRange) {
        thChartDataComm.controllerEvent(event)
        if (event.isResult) {
            i(TAG) { "ThChartDataManager--加载方式V1,onEvent4LoadTimeRange-->读取某一段时间数据操作成功" }
            //读取某一段时间数据操作成功；开启倒计时
            //更新倒计时
            doCountdown()
        } else {
            //读取失败；移除倒计时，通知读取结果
            removeCountdown()
            loadFinish(false, "v1加载方式，读取某段数据失败")
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventDataQuerying(event: EventDataQuerying?) {
        //查询数据中；重置倒计时
        doCountdown()
    }

    /**
     * !!!这个结果的上报可能出现重复，故须进行限制
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventDataOver(event: EventDataOver) {
        if (!inDataComm) {
            return
        }
        i(TAG) { "ThChartDataManager--onEventDataOver-->加载结果上报,packageNum=->${event.packageNums}" }
        //移除超时
        removeCountdown()
        //某一段时间内数据读取完成；读取下一段数据
        transport4V1()
    }

    @Synchronized
    private fun transport4V1() {
        dataTimeSet?.let {
            it.nextTime?.run {
                //先判断加载时间的有效性，开始时间<结束时间
                //拉取时间范围无效，会影响重拉数据
                if (startTime >= endTime) {
                    transport4V1()
                    return
                }
                //继续读取下一段数据
                val offset = getOffset4V1(BleUtil.getValidTimeStamp(startTime), BleUtil.getValidTimeStamp(endTime), curTime)
                thChartDataComm.startController(ControllerHeartTimeRange(offset[0], offset[1], it.haveNextTime()))
                i(TAG) { "ThChartDataManager--transport-->加载方式V1,加载数据段=->${it.getFormatTime(this)}" }
            } ?: loadFinish(true, "读取完毕")
        }
    }

    private fun getOffset4V1(millsStart: Long, millsEnd: Long, millsCur: Long): IntArray {
        val startMinutes = BleUtil.getTimeStamp(millsStart)
        val endMinutes = BleUtil.getTimeStamp(millsEnd)
        val curMinutes = BleUtil.getTimeStamp(millsCur)
        val result = IntArray(2)
        result[0] = (curMinutes - startMinutes).coerceAtLeast(0)
        result[1] = (curMinutes - endMinutes).coerceAtLeast(0)
        return result
    }

    /**
     * 解析温湿度数据=->v0
     * 20bytes=4bytes->时间戳+4*(4bytes-温湿度数据)
     */
    @WorkerThread
    @Synchronized
    private fun doParse4V0(curTime4Minute: Int, values: ByteArray) {
        val timeBytes = ByteArray(4)
        System.arraycopy(values, 0, timeBytes, 0, 4)
        val dataTimeStamp = BleUtil.getSignedInt(timeBytes, false)
        val temHumList: MutableList<TemHum> = ArrayList()
        val thValueBytes = ByteArray(4)
        var srcPos = 4
        for (i in 0..3) {
            System.arraycopy(values, srcPos, thValueBytes, 0, 4)
            srcPos += 4
            val invalidTHValue: Boolean = isInvalidTHValue(thValueBytes)
            if (invalidTHValue) {
                //无效数据
                continue
            }
            val timeStampMinute = dataTimeStamp - i
            if (timeStampMinute >= curTime4Minute) {
                //若数据点时间戳>=当前的时间，则必然为无效数据，不记录
                continue
            }
            val tem = BleUtil.convertTwoBytesToShort(thValueBytes[0], thValueBytes[1])
            val hum = BleUtil.convertTwoBytesToShort(thValueBytes[2], thValueBytes[3])
            val temHum = TemHum(tem, hum, BleUtil.getValidTimeStamp(timeStampMinute), TemHum.FROM_TYPE_DEVICE)
            //加入有效数据集合
//            i(TAG) { "ThChartDataManager--doParse4V0-->${JsonUtil.toJson(temHum)}" }
            temHumList.add(temHum)
            progress++
        }
        //存储有效数据
        if (temHumList.isNotEmpty()) {
            DbController.insertDeviceData(sku, device, temHumList)
        }
        Event4BleLoadThcdProgress.sendEventDataProgress(sku, device, all, progress)
    }

    /**
     * 解析温湿度数据=->v1
     * 20bytes=2bytes->剩余时长+6*(3bytes-温湿度数据)
     */
    @WorkerThread
    @Synchronized
    private fun doParse4V1(curTime4Minute: Int, value: ByteArray) {
        val timeOffset = ByteArray(2)
        System.arraycopy(value, 0, timeOffset, 0, timeOffset.size)
        val temHumList: MutableList<TemHum> = ArrayList()
        var timeOffsetMinutes = BleUtil.getSignedInt(timeOffset, true)
        val thBytes = getTHBytes4V1(value)
        for (thByte in thBytes) {
            val time = curTime4Minute - timeOffsetMinutes
            timeOffsetMinutes--
            if (isInvalidTHValue(thByte)) {
                //无效数据
                continue
            }
            //数据不在读取范围内
            if (time >= curTime4Minute) {
                //若数据点时间戳>=当前的时间，则必然为无效数据，不记录
                continue
            }
            //解析温湿度数据
            val thValue: IntArray = ThBroadcastUtil.parseThValueV1(thByte)
            val tem = thValue[0]
            val hum = thValue[1]
            val temHum = TemHum(tem, hum, BleUtil.getValidTimeStamp(time), TemHum.FROM_TYPE_DEVICE)
            //加入有效数据集合
            temHumList.add(temHum)
//            i(TAG) { "ThChartDataManager--doParse4V1-->temHum=->${JsonUtil.toJson(temHum)}" }
            progress++
        }
        //存储有效数据
        if (temHumList.isNotEmpty()) {
            DbController.insertDeviceData(sku, device, temHumList)
        }
        Event4BleLoadThcdProgress.sendEventDataProgress(sku, device, all, progress)
    }

    private fun getTHBytes4V1(value: ByteArray): Array<ByteArray> {
        val thBytes = Array(6) { ByteArray(3) }
        var srcPos = 2
        for (thByte in thBytes) {
            System.arraycopy(value, srcPos, thByte, 0, thByte.size)
            srcPos += 3
        }
        return thBytes
    }

    private fun isInvalidTHValue(thValueBytes: ByteArray): Boolean {
        var invalid = true
        for (thValueByte in thValueBytes) {
            if (thValueByte != ThBroadcastUtil.INVALID_BYTE) {
                invalid = false
                break
            }
        }
        return invalid
    }

    /**
     * 解析温湿度+pm25数据
     * 20bytes=2bytes->剩余时长+3*(3bytes-温湿度数据+2bytes-pm2.5)
     */
    @WorkerThread
    @Synchronized
    private fun doParse4Thp(curTime4Minute: Int, values: ByteArray) {
        val timeOffset = ByteArray(2)
        System.arraycopy(values, 0, timeOffset, 0, timeOffset.size)
        val thpList: MutableList<TemHumPm> = ArrayList()
        var timeOffsetMinutes = BleUtil.getSignedInt(timeOffset, true)
        val thpBytes = getTHPBytes(values)
        for (thByte in thpBytes) {
            val time = curTime4Minute - timeOffsetMinutes
            timeOffsetMinutes--
            if (isInvalidTHValue(thByte)) {
                //无效数据
                continue
            }
            if (time >= curTime4Minute) {
                //若数据点时间戳>=当前的时间，则必然为无效数据，不记录
                continue
            }
            //解析温湿度数据
            val thpValue = com.govee.base2newth.chart.BleUtil.parseThpValue(thByte) ?: continue
            val tem = thpValue[0]
            val hum = thpValue[1]
            val pm25 = thpValue[2]
            val thp = TemHumPm(
                tem,
                hum,
                pm25,
                com.govee.base2newth.chart.BleUtil.getValidTimeStamp(time),
                TemHumPm.FROM_TYPE_DEVICE
            )
//            i(TAG) { "ThChartDataManager--doParse4Thp-->${JsonUtil.toJson(thp)}" }
            //加入有效数据集合
            thpList.add(thp)
            progress++
        }
        //存储有效数据
        if (thpList.isNotEmpty()) {
            DbController.insertDeviceData4Thp(sku, device, thpList)
        }
        Event4BleLoadThcdProgress.sendEventDataProgress(sku, device, all, progress)
    }

    /**
     * 根据协议截取温度、湿度、pm2.5的数据端段(其中也可能存在无效数据，全用0xff填充)
     *
     * @param value
     * @return
     */
    private fun getTHPBytes(value: ByteArray): Array<ByteArray> {
        val thpBytes = Array(3) { ByteArray(5) }
        var srcPos = 2
        for (thByte in thpBytes) {
            System.arraycopy(value, srcPos, thByte, 0, thByte.size)
            srcPos += 5
        }
        return thpBytes
    }

    /**
     * 解析温湿度数据=->H5112
     * 20bytes=4bytes->时间戳+3*(5bytes->3byte探针1的温湿度数据+2byte探针2的温度数据)
     */
    @WorkerThread
    @Synchronized
    private fun doParse4H5112(curTime4Minute: Int, values: ByteArray) {
        i(TAG) { "ThChartDataManager--doParse4H5112-->thCdHex:${BleUtil.bytesToHexString(values)}" }
        val timeOffset = ByteArray(2)
        System.arraycopy(values, 0, timeOffset, 0, timeOffset.size)
        val thpList: MutableList<TemHumPm> = ArrayList()
        var timeOffsetMinutes = BleUtil.getSignedInt(timeOffset, true)
        val thpBytes = getTHPBytes(values)
        for (thByte in thpBytes) {
            val time = curTime4Minute - timeOffsetMinutes
            timeOffsetMinutes--
            if (time >= curTime4Minute) {
                //若数据点时间戳>=当前的时间，则必然为无效数据，不记录
                continue
            }
            //探针1
            val thBas4Pb1 = ByteArray(3)
            System.arraycopy(thByte, 0, thBas4Pb1, 0, thBas4Pb1.size)
            val isInvalid4Pb1 = isInvalidTHValue(thBas4Pb1)
            val thValue4Pb1: IntArray = if (isInvalid4Pb1) {
                intArrayOf(AbsModel4Th.INVALID_INT_VALUE, AbsModel4Th.INVALID_INT_VALUE)
            } else {
                ThBroadcastUtil.parseThValueV1(thBas4Pb1)
            }
            val tem4Pb1 = thValue4Pb1[0]
            val hum = thValue4Pb1[1]
            //探针2
            val temBas4Pb2 = ByteArray(2)
            System.arraycopy(thByte, thBas4Pb1.size, temBas4Pb2, 0, temBas4Pb2.size)
            val isInvalid4Pb2 = isInvalidTHValue(temBas4Pb2)
            val tem4Pb2 = if (isInvalid4Pb2) {
                AbsModel4Th.INVALID_INT_VALUE
            } else {
                parseTem24H5112(temBas4Pb2)
            }
            //两个探针的数据都无效则视作无效值
            val type = if (isInvalid4Pb1 && isInvalid4Pb2) {
                TemHum.FROM_TYPE_INVALID
            } else {
                TemHum.FROM_TYPE_DEVICE
            }
            val temHum = TemHumPm(tem4Pb1, hum, tem4Pb2, BleUtil.getValidTimeStamp(time), type)
            i(TAG) { "ThChartDataManager--doParse4H5112-->${JsonUtil.toJson(temHum)}" }
            //加入有效数据集合
            thpList.add(temHum)
            progress++
        }
        //存储有效数据
        if (thpList.isNotEmpty()) {
            DbController.insertDeviceData4Thp(sku, device, thpList)
        }
        Event4BleLoadThcdProgress.sendEventDataProgress(sku, device, all, progress)
    }

    fun parseTem24H5112(tem2Bytes: ByteArray): Int {
        val temOver0 = NumberUtil.toBinaryString(tem2Bytes[0], 8).startsWith("0")
        if (!temOver0) {
            //需要将第一个字节的最高位置0
            val temByte0 = (BleUtil.getUnsignedByte(tem2Bytes[0]) - 128).toByte()
            tem2Bytes[0] = temByte0
        }
        val signedInt = BleUtil.getSignedInt(tem2Bytes, true)
        val tem = if (temOver0) signedInt * 10 else signedInt * (-10)
        return tem
    }

    /**
     * 加载完成
     * @param readOver 全部数据是否读取完成
     */
    private fun loadFinish(readOver: Boolean, from: String) {
        //修改标识位
        inDataComm = false
        //移除倒计时
        removeCountdown()
        //主+从框设备读取完主设备数据后，继续读取从设备数据
        if (readOver && !TextUtils.isEmpty(mainDevice) && order4CurRead <= 0) {
            order4CurRead++
            prepareReadData()
        } else {
            i(TAG) { "ThChartDataManager--loadFinish-->加载结果=->${readOver},from=->${from}" }
            //通知数据读取结果
            //备注：主、从一体设备会先读取主设备，再读取从设备的数据，但这里
            if (ThConfig4Support.isMainSubDevice(goodsType, sku)) {
                if (fromMain) {
                    Event4BleLoadThcdResult.sendEvent4BleLoadThcdResult(sku, mainDevice, readOver)
                } else {
                    Event4BleLoadThcdResult.sendEvent4BleLoadThcdResult(sku, mainDevice.plus("_1"), readOver)
                }
            } else {
                Event4BleLoadThcdResult.sendEvent4BleLoadThcdResult(sku, device, readOver)
            }
            //释放资源
            release()
        }
    }

    override fun release() {
        registerEvent(false)
        mHandler.removeCallbacksAndMessages(null)
        //置空数据
        sku = ""
        device = ""
        mainDevice = ""
        all = 0
        progress = 0
        curTime = 0
        order4CurRead = 0
        dataTimeSet = null
        dataTimeSet4Main = null
        dataTimeSet4Sub = null
    }

    private fun registerEvent(register: Boolean) {
        if (register) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this)
            }
        } else {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this)
            }
        }
    }
}
