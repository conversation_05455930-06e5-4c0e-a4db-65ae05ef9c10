package com.govee.thnew.ui.detail.h5112

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.viewpager2.widget.ViewPager2
import com.govee.base2home.Constant4L5
import com.govee.base2kt.ac.AbsAc
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.setVisibility
import com.govee.base2newth.ThConsV1
import com.govee.kt.hideLoading
import com.govee.kt.showLoading
import com.govee.thnew.add.AddInfo
import com.govee.thnew.databinding.ThnewAc4H5112SquareChartBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.op.BleOpManager
import com.govee.thnew.ui.setting.h5112.Ac4H5112Setting
import com.govee.thnew.update.ota4frk.Ac4UpdateByFrk
import com.govee.ui.dialog.BleUpdateHintDialog
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil

/**
 * <AUTHOR>
 * @date created on 2025/05/06
 * @description 温湿度计-->H5112的柱状图表页
 */
class Ac4H5112SquareChart : AbsAc<ThnewAc4H5112SquareChartBinding>() {

    private var selectedProbeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM
    private val setProbeIconDialog by lazy {
        Dialog4EditProbeInfo.createDialog(this)
    }

    private var originalBindExt: AddInfo? = null

    override fun layoutId(): Int {
        return com.govee.thnew.R.layout.thnew_ac_4_h5112_square_chart
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.thnew.R.id.ac_container
    }

    override fun adapterInsetViewId(): Int {
        return com.govee.thnew.R.id.top_flag
    }

    override fun statusBarLightMode(): Boolean {
        return false
    }

    /**
     * 该设备自身的实时信息
     */
    private fun getDeviceInfo(): AddInfo? {
        return Vm4ThOpManager.instance()?.getDeviceInfo()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        initData()
        initUi()
        initOpClick()
        initObserver()
    }

    private fun initData() {
        getDeviceInfo()?.let { bindExt ->
            originalBindExt = bindExt.copy()
        }
    }

    private fun initUi() {
        selectedProbeIndex = intent.getIntExtra(Constant4L5.KEY_4_TH_PROBE_INDEX, Constant4L5.PROBE_INDEX_4_TEM_HUM)
        viewBinding.let { vb ->
            getDeviceInfo()?.let { bindExt ->
                vb.tvTitle.text = bindExt.deviceName
            }
        }
        //探针内容
        val probeFragmentList = arrayListOf<Pair<Int, Fragment>>().apply {
            add(Pair(Constant4L5.PROBE_INDEX_4_TEM_HUM, Fragment4H5112SquareChart.newInstance(Constant4L5.PROBE_INDEX_4_TEM_HUM)))
            add(Pair(Constant4L5.PROBE_INDEX_4_TEM, Fragment4H5112SquareChart.newInstance(Constant4L5.PROBE_INDEX_4_TEM)))
        }
        viewBinding.vpContentContainer4H5112Sc.adapter = ViewPagerAdapter(probeFragmentList, supportFragmentManager, this.lifecycle)
        viewBinding.vpContentContainer4H5112Sc.offscreenPageLimit = probeFragmentList.size
        //选中对应探针
        selectTab(selectedProbeIndex - 1)
        viewBinding.vpContentContainer4H5112Sc.setCurrentItem(selectedProbeIndex - 1, true)
    }

    /**
     * 选中tab
     */
    private fun selectTab(position: Int) {
        viewBinding.run {
            clTab14H5112Sc.alpha = 0.2f
            clTab24H5112Sc.alpha = 0.2f
            ivEditIcon14H5112Probe.setVisibility(false)
            ivEditIcon24H5112Probe.setVisibility(false)
            when (position) {
                0 -> {
                    clTab14H5112Sc.alpha = 1f
                    ivEditIcon14H5112Probe.setVisibility(true)
                }

                1 -> {
                    clTab24H5112Sc.alpha = 1f
                    ivEditIcon24H5112Probe.setVisibility(true)
                }

                else -> {
                    return
                }
            }
        }
    }

    @SuppressLint("RtlHardcoded")
    private fun initOpClick() {
        viewBinding.let { vb ->
            vb.btnBack.clickDelay {
                onBackPressed()
            }
            vb.btnChart.clickDelay {
                finish()
            }
            vb.btnSetting.clickDelay {
                JumpUtil.jump(this, Ac4H5112Setting::class.java)
            }
            //Tab+ViewPage2联动
            vb.clTab14H5112Sc.clickDelay {
                selectTab(0)
                vb.vpContentContainer4H5112Sc.setCurrentItem(0, true)
            }
            vb.clTab24H5112Sc.clickDelay {
                selectTab(1)
                vb.vpContentContainer4H5112Sc.setCurrentItem(1, true)
            }
            vb.vpContentContainer4H5112Sc.registerOnPageChangeCallback(object :
                ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    selectTab(position)
                    selectedProbeIndex = if (position == 0) {
                        Constant4L5.PROBE_INDEX_4_TEM_HUM
                    } else {
                        Constant4L5.PROBE_INDEX_4_TEM
                    }
                }
            })
            //编辑探针信息
            vb.ivEditIcon14H5112Probe.clickDelay {
                setProbeIconDialog.showDialog(
                    selectedProbeIndex,
                    getDeviceInfo()?.pb1IconIndex ?: 1,
                    vb.tvName14H5112Probe.text.toString(),
                    selPbIconCallback = { selectedProbeIcon ->
                        Vm4ThOpManager.instance()?.setPbIconIndex(Constant4L5.PROBE_INDEX_4_TEM_HUM, selectedProbeIcon.index) {
                            if (it) {
                                Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM_HUM, selectedProbeIcon.index, true)?.run {
                                    ResUtil.setImageResource(vb.ivIcon14H5112Probe, this)
                                }
                            }
                        }
                    },
                    editPbNameCallback = {
                        vb.tvName14H5112Probe.text = it
                    })
            }
            vb.ivEditIcon24H5112Probe.clickDelay {
                setProbeIconDialog.showDialog(
                    selectedProbeIndex,
                    getDeviceInfo()?.pb2IconIndex ?: 1,
                    vb.tvName24H5112Probe.text.toString(), selPbIconCallback = { selectedProbeIcon ->
                        Vm4ThOpManager.instance()?.setPbIconIndex(Constant4L5.PROBE_INDEX_4_TEM, selectedProbeIcon.index) {
                            if (it) {
                                Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM, selectedProbeIcon.index, true)?.run {
                                    ResUtil.setImageResource(vb.ivIcon24H5112Probe, this)
                                }
                            }
                        }
                    }, editPbNameCallback = {
                        vb.tvName24H5112Probe.text = it
                    })
            }
        }
    }

    private fun initObserver() {
        Vm4ThOpManager.instance()?.let { vm ->
            vm.loadingChange.showDialog.observeInActivity(this) {
                showLoading()
            }
            vm.loadingChange.dismissDialog.observeInActivity(this) {
                hideLoading()
            }
            vm.ld4ConnectStatusChange.observe(this) {
                val hasGetDeviceInfo = (it.first == BleOpManager.BLE_READ_INFO_FINISH) || it.second.first
                if (hasGetDeviceInfo) {
                    Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM_HUM, getDeviceInfo()?.pb1IconIndex ?: 1, true)?.let { iconRes ->
                        ResUtil.setImageResource(viewBinding.ivIcon14H5112Probe, iconRes)
                        viewBinding.ivIcon14H5112Probe.setVisibility(true)
                    }
                    Dialog4EditProbeInfo.getPbIcon(Constant4L5.PROBE_INDEX_4_TEM, getDeviceInfo()?.pb2IconIndex ?: 1, true)?.let { iconRes ->
                        ResUtil.setImageResource(viewBinding.ivIcon24H5112Probe, iconRes)
                        viewBinding.ivIcon24H5112Probe.setVisibility(true)
                    }
                } else {
                    viewBinding.ivIcon14H5112Probe.setVisibility(false)
                    viewBinding.ivIcon24H5112Probe.setVisibility(false)
                }
                setProbeIconDialog.updateUi(hasGetDeviceInfo)
            }
            vm.settingOpManager.ld4UpdateDeviceName.observe(this) {
                if (!hasCreated) {
                    return@observe
                }
                getDeviceInfo()?.let { bindExt ->
                    if (it.first == bindExt.getKey()) {
                        viewBinding.tvTitle.text = it.second
                    }
                }
            }
            vm.ld4UpgradeVersion.observe(this) {
                when (it.first) {
                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_FIRST_REMIND -> {
                        getDeviceInfo()?.let { bindExt ->
                            BleUpdateHintDialog.showDialog(this, bindExt.sku, {
                                toUpgradePage()
                            }, this.javaClass.name)
                        }
                        //再执行红点
                        Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value = Pair(Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND, it.second)
                    }

                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_DOT_REMIND -> {
                        //显示升级提示红点
                        viewBinding.versionFlag.setVisibility(true)
                    }

                    Vm4ThOpManager.UPGRADE_FIRMWARE_4_FINISH -> {
                        //隐藏升级提示
                        viewBinding.versionFlag.setVisibility(false)
                    }

                    else -> {
                        viewBinding.versionFlag.setVisibility(false)
                    }
                }
            }
        }
    }

    /**
     * 跳转至升级页面
     */
    private fun toUpgradePage() {
        getDeviceInfo()?.let { bindExt ->
            Vm4ThOpManager.instance()?.ld4UpgradeVersion?.value?.let { upgradePair ->
                upgradePair.second?.run {
                    Ac4UpdateByFrk.jump2OtaUpdate(this@Ac4H5112SquareChart, bindExt.sku, bindExt.deviceName, this)
                }
            }
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        getDeviceInfo()?.let { bindExt ->
            //有信息更新，通知刷新首页卡片
            if (bindExt != originalBindExt) {
                ThConsV1.refreshDeviceInfo(bindExt.sku, bindExt.device, bindExt, true)
            }
        }
        BaseApplication.getBaseApplication().finishAc(Ac4H5112Detail::class.java)
        super.onBackPressed()
    }
}