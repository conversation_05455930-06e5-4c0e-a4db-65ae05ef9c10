package com.govee.thnew.ble.controller

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4DeviceWarningTime

/**
 * <AUTHOR>
 * @date created on 2025/03/31
 * @description 温湿度计-->设置设备声音报警开关/时长的controller
 * 备注：H5171使用
 */
class Controller4DeviceWarningTime : AbsControllerWithCallback {

    private var open = false
    private var seconds = 10

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     */
    constructor(open: Bo<PERSON>an, seconds: Int, writeCallBack: ((result: Boolean) -> Unit)) : super(writeCallBack) {
        this.open = open
        this.seconds = seconds
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_device_warning_time
    }

    override fun translateWrite(): ByteArray {
        val openByte = if (open) 0x01.toByte() else 0x00.toByte()
        val secondsBytes = BleUtil.getSignedBytesFor2(seconds, true)
        return byteArrayOf(openByte, secondsBytes[0], secondsBytes[1])
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val openByte = BleUtil.getUnsignedByte(validBytes[0])
        Event4DeviceWarningTime.sendSuc(
            true, isWrite, commandType, proType,
            openByte == 1,
            BleUtil.getUnsignedInt(validBytes[1], validBytes[2])
        )
        return true
    }

    override fun fail() {
        Event4DeviceWarningTime.sendFail(isWrite, commandType, proType)
    }
}