package com.govee.thnew.ui.detail.h5112

import android.os.Bundle
import android.text.TextUtils
import android.view.View
import androidx.appcompat.widget.LinearLayoutCompat
import androidx.core.widget.NestedScrollView
import androidx.fragment.app.viewModels
import com.govee.base2home.Constant4L5
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.temUnit.TemperatureUnitType
import com.govee.base2home.util.IntroUtils.showDewPointIntro
import com.govee.base2home.util.IntroUtils.showVpdIntro
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.setVisibility
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.ChartVisibleConfig
import com.govee.base2newth.chart.DataGroup
import com.govee.base2newth.chart.IntervalType
import com.govee.base2newth.chart.TimeUtil
import com.govee.base2newth.chart.newchart.ChartController4New
import com.govee.base2newth.chart.newchart.ConfigParams
import com.govee.base2newth.chart.newchart.DbDataVm4New
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.db.TemHum
import com.govee.base2newth.db.TemHumPm
import com.govee.base2newth.deviceitem.LastData
import com.govee.base2newth.other.Config4DeviceChartOrder
import com.govee.base2newth.other.Config4LastThValue.Companion.getConfig
import com.govee.base2newth.other.SyncTemUnitUtil
import com.govee.home.account.config.AccountConfig
import com.govee.mvvm.BaseFragment
import com.govee.mvvm.base.viewmodel.BaseViewModel
import com.govee.mvvm.globalLaunch
import com.govee.thnew.R
import com.govee.thnew.ThNewConstant
import com.govee.thnew.add.AddInfo
import com.govee.thnew.config.ThConfig4Detail
import com.govee.thnew.databinding.ThnewChartNew4DewPointBinding
import com.govee.thnew.databinding.ThnewChartNew4HumBinding
import com.govee.thnew.databinding.ThnewChartNew4TemBinding
import com.govee.thnew.databinding.ThnewChartNew4VpdBinding
import com.govee.thnew.databinding.ThnewFragment4H5112DetailBinding
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.compare.Ac4ThMultiDevCompare
import com.govee.thnew.ui.compare.Ac4ThSingleDevCompare
import com.govee.thnew.ui.detail.Ac4ThExportData
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.ResUtil
import kotlinx.coroutines.Dispatchers
import java.util.concurrent.ConcurrentHashMap

/**
 * <AUTHOR>
 * @date created on 2025/4/28
 * @description H5112详情页Fragment
 */
class Fragment4H5112Detail : BaseFragment<BaseViewModel, ThnewFragment4H5112DetailBinding>() {

    companion object {
        fun newInstance(pbIndex: Int): Fragment4H5112Detail {
            val args = Bundle().apply {
                putInt(Constant4L5.KEY_4_TH_PROBE_INDEX, pbIndex)
            }
            val fragment = Fragment4H5112Detail()
            fragment.arguments = args
            return fragment
        }
    }

    /**
     * 实时信息
     */
    private var refreshTime = 0L
    private var realTem4Pb1 = 0
    private var realHum = 0
    private var realTem4Pb2 = 0

    /**
     * 数据展示的vm
     */
    private val vm4ChartData: DbDataVm4New by viewModels()

    /**
     * 图表展示控制类
     */
    private lateinit var chartOp4Th: ChartController4New

    /**
     * 当前选中的图表时间段
     */
    private var selectedDataGroup: DataGroup? = DataGroup.hour

    /**
     * 图表控件相关
     */
    private val temBinding by lazy {
        ThnewChartNew4TemBinding.inflate(layoutInflater)
    }
    private val humBinding by lazy {
        ThnewChartNew4HumBinding.inflate(layoutInflater)
    }
    private val dpBinding by lazy {
        ThnewChartNew4DewPointBinding.inflate(layoutInflater)
    }
    private val vpdBinding by lazy {
        ThnewChartNew4VpdBinding.inflate(layoutInflater)
    }

    private var probeIndex = Constant4L5.PROBE_INDEX_4_TEM_HUM
    private val refreshFlagMap = ConcurrentHashMap<String, Boolean>()

    /**
     * 该设备自身的实时信息
     */
    private fun getDeviceInfo(): AddInfo? {
        return Vm4ThOpManager.instance()?.getDeviceInfo()
    }

    override fun initView(savedInstanceState: Bundle?) {
        probeIndex = arguments?.getInt(Constant4L5.KEY_4_TH_PROBE_INDEX, Constant4L5.PROBE_INDEX_4_TEM_HUM) ?: Constant4L5.PROBE_INDEX_4_TEM_HUM
        getDeviceInfo()?.let { bindExt ->
            //实时信息展示控件初始化配置
            mDatabind.thRealInfo45112Detail.init(bindExt.goodsType, bindExt.sku, bindExt.device, probeIndex)
            //温度单位
            updateTemUnit(TemUnitConfig.read().isTemUnitFah(bindExt.sku))
        }
        //初始化图表相关配置
        initChartSet()
        //图表排序
        changeChartOrder()
        //展示本地已有温湿度图表数据
        Vm4ThOpManager.instance()?.loadThcdManager?.refreshThChartData("先展示本地已有温湿度图表数据...", true)
        //只有登录才有导出数据
        mDatabind.tvExportData45112Detail.setVisibility(AccountConfig.read().isHadToken)
        if (!AccountConfig.read().isHadToken) {
            mDatabind.clExportContainer45112Detail.background = null
            mDatabind.clExportContainer45112Detail.isEnabled = false
        }
        mDatabind.tvHistoryCompare45112Detail.setVisibility(AccountConfig.read().isHadToken)
        mDatabind.tvMultiDeviceCompare45112Detail.setVisibility(AccountConfig.read().isHadToken)
        //操作监听
        initOpListener()
        //观察订阅
        initObserver()
    }

    private fun initOpListener() {
        mDatabind.run {
            //切换图表时间段相关
            getDeviceInfo()?.let { bindExt ->
                spvChartSelectPeriod45112Detail.setSelectListener(bindExt.sku) { type, dataGroup ->
                    if (type != null) {
                        chartOp4Th.setIntervalType(type)
                    }
                    selectedDataGroup = dataGroup
                }
                thstvContainer45112Detail.setOnSwitchTimeListener(bindExt.goodsType, bindExt.sku, bindExt.device, probeIndex) { isToNext ->
                    chartOp4Th.setOneStage(isToNext)
                }
            }
            //导出数据
            clExportContainer45112Detail.clickDelay {
                activity?.let {
                    Ac4ThExportData.jump2ExportData4H5112(it, probeIndex)
                }
            }
            //单设备数据对比
            tvHistoryCompare45112Detail.clickDelay {
                getDeviceInfo()?.let { bindExt ->
                    activity?.let {
                        Ac4ThSingleDevCompare.jump2SingleDevCompare(it, JsonUtil.toJson(bindExt), probeIndex)
                    }
                }
            }
            //多设备数据对比
            tvMultiDeviceCompare45112Detail.clickDelay {
                getDeviceInfo()?.let { bindExt ->
                    activity?.let {
                        Ac4ThMultiDevCompare.jump2MultiDevCompare(it, JsonUtil.toJson(bindExt), probeIndex)
                    }
                }
            }
            activity?.let {
                if (it is Ac4H5112Detail) {
                    ablTopContentContainer45112Detail.addOnOffsetChangedListener { _, verticalOffset ->
                        it.ablVerticalOffsetMap[probeIndex] = verticalOffset
                    }
                    nsvScrollContainer45112Detail.setOnScrollChangeListener(NestedScrollView.OnScrollChangeListener { _, _, scrollY, _, _ ->
                        it.subPageScrollYMap[probeIndex] = scrollY
                    })
                }
            }
        }
        //切换温度单位
        temBinding.ivTemUnitIcon4ThDetailNew.clickDelay {
            getDeviceInfo()?.let { bindExt ->
                val fahOpen = !TemUnitConfig.read().isTemUnitFah(bindExt.sku)
                TemUnitConfig.read().setTemUnit(bindExt.sku, if (fahOpen) TemperatureUnitType.Fahrenheit else TemperatureUnitType.Celsius)
                //同步温度单位到服务端
                SyncTemUnitUtil.syncTemUnit(Transactions().createTransaction(), bindExt.sku, fahOpen)
                //通知更新ui
                Vm4ThOpManager.instance()?.ld4ChangeTemUnit?.value = true
            }
        }
        //显/隐露点、vpd相关
        dpBinding.ivDpSwitch4ThDetailNew.clickDelay {
            dpSwitch()
        }
        dpBinding.ivDpSwitch14ThDetailNew.clickDelay {
            dpSwitch()
        }
        dpBinding.ivDpIntroIcon4ThDetailNew.clickDelay {
            getDeviceInfo()?.let { bindExt ->
                activity?.let {
                    showDewPointIntro(it, bindExt.sku)
                }
            }
        }
        dpBinding.ivDpIntroIcon14ThDetailNew.clickDelay {
            getDeviceInfo()?.let { bindExt ->
                activity?.let {
                    showDewPointIntro(it, bindExt.sku)
                }
            }
        }
        vpdBinding.ivVpdSwitch4ThDetailNew.clickDelay {
            vpdSwitch()
        }
        vpdBinding.ivVpdSwitch14ThDetailNew.clickDelay {
            vpdSwitch()
        }
        vpdBinding.ivVpdIntroIcon4ThDetailNew.clickDelay {
            getDeviceInfo()?.let { bindExt ->
                activity?.let {
                    showVpdIntro(it, bindExt.sku)
                }
            }
        }
        vpdBinding.ivVpdIntroIcon14ThDetailNew.clickDelay {
            getDeviceInfo()?.let { bindExt ->
                activity?.let {
                    showVpdIntro(it, bindExt.sku)
                }
            }
        }
    }

    /**
     * 切换露点显/隐开关
     */
    private fun dpSwitch() {
        getDeviceInfo()?.let {
            val selected: Boolean = !dpBinding.ivDpSwitch4ThDetailNew.isSelected
            ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_DEW_P, selected)
            refreshDewPView()
        }
    }

    /**
     * 切换vpd显/隐开关
     */
    private fun vpdSwitch() {
        getDeviceInfo()?.let {
            val selected: Boolean = !vpdBinding.ivVpdSwitch4ThDetailNew.isSelected
            ChartVisibleConfig.read().setVisible(it.sku, ChartVisibleConfig.CHART_TYPE_VPD, selected)
            refreshVpdView()
        }
    }

    private fun initObserver() {
        Vm4ThOpManager.instance()?.let { vm ->
            vm.ld4RealTemHum.observe(this) {
                if (isResumed) {
                    refreshRealInfo(it)
                } else {
                    //标记需要刷新
                    refreshFlagMap[ThNewConstant.REFRESH_FLAG_4_REAL_INFO] = true
                }
            }
            vm.ld4ChangeTemUnit.observe(this) {
                if (it) {
                    getDeviceInfo()?.let { bindExt ->
                        val fahOpen = TemUnitConfig.read().isTemUnitFah(bindExt.sku)
                        updateTemUnit(fahOpen)
                    }
                }
            }
            vm.loadThcdManager.ld4ShowChartNew.observe(this) {
                if (isResumed) {
                    refreshChartInfo(it)
                } else {
                    refreshFlagMap[ThNewConstant.REFRESH_FLAG_4_CHART_INFO] = true
                }
            }
            //设置页更新告警和校准时会影响到图表展示
            vm.ld4UpdateDeviceInfo.observe(this) {
                when (it.second) {
                    Vm4ThOpManager.UPDATE_4_WARNING -> {
                        updateChartWarnSet()
                    }

                    Vm4ThOpManager.UPDATE_4_CALIBRATION -> {
                        updateChartWarnSet()
                        //更新实时数据
                        when (probeIndex) {
                            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                                mDatabind.thRealInfo45112Detail.updateRealInfo(refreshTime, realTem4Pb1, realHum, 100)
                            }

                            Constant4L5.PROBE_INDEX_4_TEM -> {
                                mDatabind.thRealInfo45112Detail.updateRealInfo(refreshTime, realTem4Pb2, 6000, 100)
                            }

                            else -> {}
                        }
                    }

                    else -> {}
                }
            }
            //更新图表顺序
            vm.ld4ChangeChartOrder.observe(this) {
                changeChartOrder(it)
            }
        }
        //给vm4Data加入生命周期监听
        lifecycle.addObserver(vm4ChartData)
        vm4ChartData.thpDataLiveData.observe(this) { realDataMap ->
            realDataMap[ThConsV1.LINE_1]?.let {
                val mainLineTsPair = if (it.size < 2) {
                    val currTs = System.currentTimeMillis()
                    Pair(currTs - ThConsV1.ONE_DAY_MILLIS, currTs)
                } else {
                    updateSyncTime(it.last().time)
                    Pair(it.first().time, it.last().time)
                }
                chartOp4Th.updateRealData(realDataMap, mainLineTsPair, 0) {
                    mDatabind.spvChartSelectPeriod45112Detail.updateDataGroupChoose(selectedDataGroup)
                }
                showChartBtn(it.size >= 2)
            } ?: run {
                showChartBtn(false)
            }
        }
    }

    /**
     * 刷新事实信息
     */
    private fun refreshRealInfo(realInfo: Pair<Triple<Int, Int, Int>, Long>) {
        refreshTime = realInfo.second
        val thInfo = realInfo.first
        realTem4Pb1 = thInfo.first
        realHum = thInfo.second
        realTem4Pb2 = thInfo.third
        when (probeIndex) {
            Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                //更新实时数据
                mDatabind.thRealInfo45112Detail.updateRealInfo(refreshTime, realTem4Pb1, realHum, 100)
            }

            Constant4L5.PROBE_INDEX_4_TEM -> {
                //更新实时数据
                mDatabind.thRealInfo45112Detail.updateRealInfo(refreshTime, realTem4Pb2, 6000, 100)
            }

            else -> {}
        }
    }

    /**
     * 刷新图表
     */
    private fun refreshChartInfo(chartInfo: HashMap<Int, ArrayList<TemHumPm>>) {
        if (chartInfo.isEmpty()) {
            val currTs = System.currentTimeMillis()
            chartOp4Th.updateRealData(chartInfo, Pair(currTs - ThConsV1.ONE_DAY_MILLIS, currTs), 0) {
                mDatabind.spvChartSelectPeriod45112Detail.updateDataGroupChoose(DataGroup.hour)
            }
            updateSyncTime(-1)
        } else {
            globalLaunch(Dispatchers.IO) {
                getDeviceInfo()?.let { bindExt ->
                    val dealDataMap = hashMapOf<Int, Pair<Triple<Int, String, String>, ArrayList<TemHumPm>>>()
                    for (key in chartInfo.keys) {
                        chartInfo[key]?.let { thpList ->
                            val realDataList = arrayListOf<TemHumPm>()
                            when (probeIndex) {
                                Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                                    for (temHumPm in thpList) {
                                        var type = temHumPm.from
                                        if (type != TemHum.FROM_TYPE_INVALID && !ThConsV1.isValidThValue(temHumPm.tem, temHumPm.hum)) {
                                            type = TemHum.FROM_TYPE_INVALID
                                        }
                                        realDataList.add(TemHumPm(temHumPm.tem, temHumPm.hum, 100, temHumPm.time, type))
                                    }
                                    dealDataMap[key] = Pair(Triple(bindExt.goodsType, bindExt.sku, bindExt.device), realDataList)
                                }

                                Constant4L5.PROBE_INDEX_4_TEM -> {
                                    for (temHumPm in thpList) {
                                        var type = temHumPm.from
                                        if (type != TemHum.FROM_TYPE_INVALID && !ThConsV1.isValidThValue(temHumPm.pm25, 6000)) {
                                            type = TemHum.FROM_TYPE_INVALID
                                        }
                                        realDataList.add(TemHumPm(temHumPm.pm25, 6000, 100, temHumPm.time, type))
                                    }
                                    dealDataMap[key] = Pair(Triple(bindExt.goodsType, bindExt.sku, ThConsV1.getH5112Device4Pb2(bindExt.device)), realDataList)
                                }

                                else -> {}
                            }
                        }
                    }
                    vm4ChartData.dealAllLineThpData(dealDataMap)
                }
            }
        }
    }

    private fun showChartBtn(show: Boolean) {
        activity?.let {
            if (it is Ac4H5112Detail) {
                it.showChartBtn(show)
            }
        }
    }

    /**
     * 更新图表的展示时间段
     */
    private fun updateTime(startTimeStamp: Long, endTimeStamp: Long, intervalType: IntervalType) {
        val calibration = selectedDataGroup?.let {
            //在默认标签下，需要格式时间起点的一致
            TimeUtil.calibration(startTimeStamp, endTimeStamp, it)
        } ?: longArrayOf(startTimeStamp, endTimeStamp)
        val strByMDHMStart: String
        val strByMDHMEnd: String
        if (IntervalType.year_1_month == intervalType) {
            strByMDHMStart = TimeFormatM.getInstance().formatTimeToYM(calibration[0])
            strByMDHMEnd = TimeFormatM.getInstance().formatTimeToYM(calibration[1])
        } else {
            strByMDHMStart = TimeFormatM.getInstance().formatTimeToHMMD(calibration[0])
            strByMDHMEnd = TimeFormatM.getInstance().formatTimeToHMMD(calibration[1])
        }
        //更新展示时间
        temBinding.tvTemStartTime4ThDetailNew.text = strByMDHMStart
        temBinding.tvTemEndTime4ThDetailNew.text = strByMDHMEnd
        humBinding.tvHumStartTime4ThDetailNew.text = strByMDHMStart
        humBinding.tvHumEndTime4ThDetailNew.text = strByMDHMEnd
        dpBinding.tvDpStartTime4ThDetailNew.text = strByMDHMStart
        dpBinding.tvDpEndTime4ThDetailNew.text = strByMDHMEnd
        vpdBinding.tvVpdStartTime4ThDetailNew.text = strByMDHMStart
        vpdBinding.tvVpdEndTime4ThDetailNew.text = strByMDHMEnd
    }

    /**
     * 更新最后的数据同步时间点
     */
    private fun updateSyncTime(syncTime: Long) {
        getDeviceInfo()?.let {
            val syncTimeStr = if (syncTime <= 0) {
                ""
            } else {
                String.format(ResUtil.getString(com.govee.ui.R.string.h5072_last_sync_time), TimeFormatM.getInstance().formatTimeToHMYMD(syncTime))
            }
            mDatabind.let { vb ->
                vb.tvSyncTime45112Detail.text = syncTimeStr
                vb.tvSyncTime45112Detail.setVisibility(!TextUtils.isEmpty(syncTimeStr))
            }
        }
    }

    /**
     * 更新温度单位的显示
     */
    private fun updateTemUnit(fahOpen: Boolean) {
        temBinding.ivTemUnitIcon4ThDetailNew.setImageDrawable(ResUtil.getDrawable(if (fahOpen) com.govee.ui.R.mipmap.new_sensor_setting_switch_fahrenheit else com.govee.ui.R.mipmap.new_sensor_setting_switch_celsius))
        temBinding.ttcTemChart4ThDetailNew.setChangeValueShow(fahOpen, if (fahOpen) getString(com.govee.ui.R.string.tem_unit_fah) else getString(com.govee.ui.R.string.tem_unit_cel))
        dpBinding.ttcDpChart4ThDetailNew.setChangeValueShow(fahOpen, if (fahOpen) getString(com.govee.ui.R.string.tem_unit_fah) else getString(com.govee.ui.R.string.tem_unit_cel))
    }

    /**
     * 刷新露点图表的显示
     */
    private fun refreshDewPView() {
        getDeviceInfo()?.let { bindExt ->
            val selected = ChartVisibleConfig.read().getVisible(bindExt.sku, ChartVisibleConfig.CHART_TYPE_DEW_P)
            dpBinding.gpDpHideChartIds4ThDetailNew.setVisibility(!selected)
            dpBinding.ivDpSwitch14ThDetailNew.isSelected = selected
            dpBinding.gpDpShowChartIds4ThDetailNew.setVisibility(selected)
            dpBinding.ivDpSwitch4ThDetailNew.isSelected = selected
        }
    }

    /**
     * 刷新vpd的图表显示
     */
    private fun refreshVpdView() {
        getDeviceInfo()?.let { bindExt ->
            val selected = ChartVisibleConfig.read().getVisible(bindExt.sku, ChartVisibleConfig.CHART_TYPE_VPD)
            vpdBinding.gpVpdHideChartIds4ThDetailNew.setVisibility(!selected)
            vpdBinding.ivVpdSwitch14ThDetailNew.isSelected = selected
            vpdBinding.gpVpdShowChartIds4ThDetailNew.setVisibility(selected)
            vpdBinding.ivVpdSwitch4ThDetailNew.isSelected = selected
        }
    }

    /**
     * 更新图表告警配置
     */
    private fun updateChartWarnSet() {
        getDeviceInfo()?.let {
            val warnRange = when (probeIndex) {
                Constant4L5.PROBE_INDEX_4_TEM_HUM -> {
                    WarnConfig.read().queryWarningRangeByKey(it.sku, it.device)
                }

                Constant4L5.PROBE_INDEX_4_TEM -> {
                    WarnConfig.read().queryWarningRangeByKey(it.sku, ThConsV1.getH5112Device4Pb2(it.device))
                }

                else -> {
                    return
                }
            }
            if (warnRange != null) {
                temBinding.ttcTemChart4ThDetailNew.updateWarn(warnRange.temMin, warnRange.temMax)
                humBinding.ttcHumChart4ThDetailNew.updateWarn(warnRange.humMin, warnRange.humMax)
                //配置处理数据的校准值
                vm4ChartData.setCali(warnRange.temCali, warnRange.humCali)
            } else {
                val defTemWarnRange = ThConfig4Detail.getTemRange(it.goodsType, it.sku, it.bleSoftVersion, it.bleHardVersion)
                temBinding.ttcTemChart4ThDetailNew.updateWarn(defTemWarnRange.first * 100, defTemWarnRange.second * 100)
                humBinding.ttcHumChart4ThDetailNew.updateWarn(ThConsV1.HUM_MIN_VALUE * 100, ThConsV1.HUM_MAX_VALUE * 100)
                //配置处理数据的校准值
                vm4ChartData.setCali(0, 0)
            }
        }
    }

    /**
     * 更新图表相关配置
     */
    private fun initChartSet() {
        getDeviceInfo()?.let { bindExt ->
            activity?.let {
                //图表初始化相关设置
                chartOp4Th = ChartController4New(it, ConfigParams().apply {
                    //设备信息
                    goodsType = bindExt.goodsType
                    sku = bindExt.sku
                    device = if (probeIndex == Constant4L5.PROBE_INDEX_4_TEM_HUM) {
                        bindExt.device
                    } else {
                        ThConsV1.getH5112Device4Pb2(bindExt.device)
                    }
                    bleAddress = bindExt.address
                    bleSv = bindExt.bleSoftVersion
                    bleHv = bindExt.bleHardVersion
                    //初始化配置参数
                    isSupportScale = true
                    isForNewDetail = true
                    needFill = true
                }).apply {
                    //设置时间段显示切换的监听
                    setChartListener(object : ChartController4New.ChartListener {
                        override fun beScale() {
                            mDatabind.spvChartSelectPeriod45112Detail.clearSelect()
                        }

                        override fun timeChange(startTimeStamp: Long, endTimeStamp: Long, intervalType: IntervalType) {
                            updateTime(startTimeStamp, endTimeStamp, intervalType)
                        }

                        override fun hasDrawnChart() {}
                    })
                    ThConfig4Detail.getChartValueDecimalDigits(bindExt.goodsType, bindExt.sku).run {
                        setPointEndNum(this[0], this[1], this[2], this[3], this[4])
                    }
                }
            }
            //图表相关试图初始化
            //温度
            temBinding.ttcTemChart4ThDetailNew.setChart(chartOp4Th)
            //湿度
            humBinding.ttcHumChart4ThDetailNew.setChart(chartOp4Th)
            //露点
            dpBinding.ttcDpChart4ThDetailNew.setChart(chartOp4Th)
            //VPD
            vpdBinding.ttcVpdChart4ThDetailNew.setChart(chartOp4Th)
        }
        //告警配置
        updateChartWarnSet()
    }

    /**
     * 根据图表排序展示图表
     *
     * @param chartOrder 备注：A->pm2.5，B->温度，C->湿度，D->露点，E->vpd
     */
    private fun changeChartOrder(chartOrder: ArrayList<String>? = null) {
        getDeviceInfo()?.let { bindExt ->
            var usedChartOrder = chartOrder
            if (chartOrder.isNullOrEmpty()) {
                usedChartOrder = Config4DeviceChartOrder.getConfig().getChartOrder(bindExt.sku, bindExt.device) ?: arrayListOf(ThConsV1.PM25, ThConsV1.TEM, ThConsV1.HUM, ThConsV1.DP, ThConsV1.VPD)
            }
            mDatabind.let {
                it.flContent1Container45112Detail.removeAllViews()
                it.flContent2Container45112Detail.removeAllViews()
                it.flContent3Container45112Detail.removeAllViews()
                it.flContent4Container45112Detail.removeAllViews()
                it.flContent5Container45112Detail.removeAllViews()
            }
            usedChartOrder?.let {
                fillToContainer(0, usedChartOrder[0])
                fillToContainer(1, usedChartOrder[1])
                fillToContainer(2, usedChartOrder[2])
                fillToContainer(3, usedChartOrder[3])
                fillToContainer(4, usedChartOrder[4])
            }
        }
    }

    private fun fillToContainer(order: Int, type: String) {
        mDatabind.let { vb ->
            //排序填充
            val container = when (order) {
                0 -> vb.flContent1Container45112Detail
                1 -> vb.flContent2Container45112Detail
                2 -> vb.flContent3Container45112Detail
                3 -> vb.flContent4Container45112Detail
                else -> {
                    vb.flContent5Container45112Detail
                }
            }
            val supportHum = probeIndex == Constant4L5.PROBE_INDEX_4_TEM_HUM
            when (type) {
                ThConsV1.TEM -> {
                    container.setVisibility(true)
                    container.addView(temBinding.root)
                }

                ThConsV1.HUM -> {
                    container.setVisibility(supportHum)
                    if (supportHum) {
                        container.addView(humBinding.root)
                    }
                }

                ThConsV1.DP -> {
                    container.setVisibility(supportHum)
                    if (supportHum) {
                        container.addView(dpBinding.root)
                    }
                }

                ThConsV1.VPD -> {
                    container.setVisibility(supportHum)
                    if (supportHum) {
                        container.addView(vpdBinding.root)
                    }
                }

                else -> {}
            }
            if (vb.flContent1Container45112Detail == container && container.visibility != View.VISIBLE) {
                val lp: LinearLayoutCompat.LayoutParams = vb.flContent2Container45112Detail.layoutParams as LinearLayoutCompat.LayoutParams
                lp.topMargin = 0
                vb.flContent2Container45112Detail.layoutParams = lp
            }
        }
    }

    override fun onPause() {
        getDeviceInfo()?.let {
            //保存最新的实时温湿度值
            getConfig().updateLastTh(it.sku, it.device, getLastData())
        }
        super.onPause()
    }

    override fun onResume() {
        super.onResume()
        SafeLog.i("xiaobing") { "Fragment4H5112Detail--onResume-->${probeIndex}" }
        Vm4ThOpManager.instance()?.let { vm ->
            if (refreshFlagMap[ThNewConstant.REFRESH_FLAG_4_REAL_INFO].isTrue()) {
                refreshFlagMap.remove(ThNewConstant.REFRESH_FLAG_4_REAL_INFO)
                vm.ld4RealTemHum.value?.let {
                    refreshRealInfo(it)
                }
            }
            if (refreshFlagMap[ThNewConstant.REFRESH_FLAG_4_CHART_INFO].isTrue()) {
                refreshFlagMap.remove(ThNewConstant.REFRESH_FLAG_4_CHART_INFO)
                vm.loadThcdManager.ld4ShowChartNew.value?.let {
                    refreshChartInfo(it)
                }
            }
        }
        //vpd图表展示
        refreshVpdView()
        //露点图表展示
        refreshDewPView()
    }

    private fun getLastData(): LastData {
        val lastData = LastData()
        lastData.tem = realTem4Pb1
        lastData.hum = realHum
        lastData.pm = realTem4Pb2
        lastData.lastTime = refreshTime
        return lastData
    }

    override fun layoutId(): Int {
        return R.layout.thnew_fragment_4_h5112_detail
    }
}