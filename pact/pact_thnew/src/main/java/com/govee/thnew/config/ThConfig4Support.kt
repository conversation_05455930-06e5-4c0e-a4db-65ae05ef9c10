package com.govee.thnew.config

import com.govee.base2home.Constant4L5
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.pact.Pact
import com.govee.base2home.pact.Protocol
import com.govee.base2home.theme.ThemeM
import com.govee.thnew.R
import java.util.concurrent.ConcurrentHashMap

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->sku协议、功能等相关配置类
 */
object ThConfig4Support {

    private val supportProtocolsMap by lazy {
        ConcurrentHashMap<Int, ArrayList<Protocol>>()
    }

    const val UNDEFINED = -1

    /**
     * 配对方式
     */
    const val PAIR_WAY_V0 = 0
    const val PAIR_WAY_V1 = 1

    /**
     * 绑定接口类型常量
     */
    const val BIND_API_V0 = 0
    const val BIND_API_V1 = 1
    const val BIND_API_V2 = 2

    /**
     * 广播类型常量
     */
    const val BROADCAST_TYPE_V0 = 0
    const val BROADCAST_TYPE_V1 = 1
    const val BROADCAST_TYPE_V2 = 2
    const val BROADCAST_TYPE_V3 = 3
    const val BROADCAST_TYPE_V4 = 4
    const val BROADCAST_TYPE_V5 = 5
    const val BROADCAST_TYPE_V6 = 6

    /**
     * 心跳类型常量
     */
    const val BLE_HEART_TYPE_V0 = 0
    const val BLE_HEART_TYPE_V1 = 1
    const val BLE_HEART_TYPE_V2 = 2

    /**
     * 上传温湿度数据频率的类型常量
     */
    const val UPLOAD_FREQUENCY_V0 = 0
    const val UPLOAD_FREQUENCY_V1 = 1

    /**
     * 通过ble加载设备端图表数据方式的常量
     */
    const val LOAD_BLE_CHART_DATA_TYPE_V0 = 0
    const val LOAD_BLE_CHART_DATA_TYPE_V1 = 1

    /**
     * 设备列表的类型集合
     * 备注：1.有goodsType则用goodsType表示类型，没有则用sku表示
     *      2.H5112不共用卡片，另行处理
     */
    val deviceItemTypes = arrayOf(
        //H5051
        Pair(GoodsType.GOODES_TYPE_NO_SUPPORT, Constant4L5.H5051),
        //H5052
        Pair(GoodsType.GOODES_TYPE_NO_SUPPORT, Constant4L5.H5052),
        //H5053
        Pair(GoodsType.GOODES_TYPE_NO_SUPPORT, Constant4L5.H5053),
        //H5071
        Pair(GoodsType.GOODES_TYPE_NO_SUPPORT, Constant4L5.H5071),
        //H5072
        Pair(GoodsType.GOODES_TYPE_NO_SUPPORT, Constant4L5.H5072),
        //H5074
        Pair(GoodsType.GOODES_TYPE_NO_SUPPORT, Constant4L5.H5074),
        //H5075
        Pair(GoodsType.GOODES_TYPE_NO_SUPPORT, Constant4L5.H5075),
        //H5100
        Pair(GoodsType.GOODS_TYPE_VALUE_TH_BLE, ""),
        //H5103
        Pair(GoodsType.GOODS_TYPE_VALUE_TH_BLE_WIFI_V2, ""),
        //H5104
        Pair(GoodsType.GOODS_TYPE_VALUE_4_TH_H5104, ""),
        //H5105
        Pair(GoodsType.GOODS_TYPE_VALUE_TH_BLE_H5105, ""),
        //H5108
        Pair(GoodsType.GOODS_TYPE_VALUE_4_TH_H5108, ""),
        //H5101/H5102/H5174/H5177
        Pair(GoodsType.GOODS_TYPE_VALUE_TH_BLE_V1, ""),
        //H5110
        Pair(GoodsType.GOODS_TYPE_VALUE_4_H5110, ""),
        //H5111
        Pair(GoodsType.GOODS_TYPE_VALUE_4_H5111, ""),
        //H5179
        Pair(GoodsType.GOODS_TYPE_VALUE_TH_BLE_WIFI_V1, ""),
        //B5178
        Pair(GoodsType.GOODS_TYPE_VALUE_TH_BLE_MULTI_V1, ""),
        //H5171
        Pair(GoodsType.GOODS_TYPE_VALUE_H5171, ""),
    )

    /**
     * 可作为类H5151网关(H5151/H5042/H5043)子设备的类型集合
     * 备注：有goodsType则用goodsType表示类型，没有则用sku表示
     */
    val h5151SubDeviceItemTypes = arrayOf(
        //H5072
        Constant4L5.H5072,
        //H5074
        Constant4L5.H5074,
        //H5075
        Constant4L5.H5075,
        //H5100
        GoodsType.GOODS_TYPE_VALUE_TH_BLE.toString(),
        //H5104
        GoodsType.GOODS_TYPE_VALUE_4_TH_H5104.toString(),
        //H5105
        GoodsType.GOODS_TYPE_VALUE_TH_BLE_H5105.toString(),
        //H5108
        GoodsType.GOODS_TYPE_VALUE_4_TH_H5108.toString(),
        //H5101/H5102/H5174/H5177
        GoodsType.GOODS_TYPE_VALUE_TH_BLE_V1.toString(),
        //H5110
        GoodsType.GOODS_TYPE_VALUE_4_H5110.toString(),
        //H5111
        GoodsType.GOODS_TYPE_VALUE_4_H5111.toString(),
        //H5171
        GoodsType.GOODS_TYPE_VALUE_H5171.toString(),
        //H5112
        GoodsType.GOODS_TYPE_VALUE_4_H5112.toString(),//!!!H5112并不是类H5151网关的真实子设备，只是用到其相关类的一些功能
    )

    /**
     *Application中注册支持的sku的相关信息
     */
    fun addSupportPact() {
        //H5051
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5151, R.mipmap.add_list_type_device_5051)
        //H5052
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5052, R.mipmap.add_list_type_device_5052)
        //H5053
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5053, R.mipmap.add_list_type_device_5053)
        //H5071
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5071, R.mipmap.add_list_type_device_5051)
        //H5072
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5072, R.mipmap.add_list_type_device_5052)
        //H5074
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5074, R.mipmap.add_list_type_device_5074)
        //H5075
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5075, R.mipmap.add_list_type_device_5075)

        //H5100
        val protocol4H5100 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_TH_BLE_1, GoodsType.PACT_CODE_4_TH_BLE_1)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_TH_BLE] = arrayListOf(protocol4H5100)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_TH_BLE, protocol4H5100)
        //初始化默认的sku图
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5100, R.mipmap.add_list_type_device_5100)

        //H5103
        val protocol4H5103 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_TH_BLE_WIFI_V2, GoodsType.PACT_CODE_4_TH_BLE_WIFI_V2)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_TH_BLE_WIFI_V2] = arrayListOf(protocol4H5103)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_TH_BLE_WIFI_V2, protocol4H5103)
        //初始化默认的sku图
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5103, R.mipmap.add_list_type_device_5103)

        //H5104
        val protocol4H5104 = GoodsType.beProtocol(GoodsType.PACT_TYPE_H5104, GoodsType.PACT_CODE_H5104)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_4_TH_H5104] = arrayListOf(protocol4H5104)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_4_TH_H5104, protocol4H5104)
        //初始化默认的sku图
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5104, R.mipmap.add_list_type_device_5104)

        //H5105
        val protocol4H5105 = GoodsType.beProtocol(GoodsType.PACT_TYPE_H5105, GoodsType.PACT_CODE_H5105)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_TH_BLE_H5105] = arrayListOf(protocol4H5105)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_TH_BLE_H5105, protocol4H5105)
        //初始化默认的sku图
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5105, R.mipmap.add_list_type_device_5105)

        //H5108
        val protocol4H5108 = GoodsType.beProtocol(GoodsType.PACT_TYPE_H5108, GoodsType.PACT_CODE_H5108)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_4_TH_H5108] = arrayListOf(protocol4H5108)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_4_TH_H5108, protocol4H5108)
        //初始化默认的sku图
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5108, R.mipmap.add_list_type_device_5108)

        //H5101/H5102/H5174/H5177
        val protocol4ThV1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_TH_BLE_V1_1, GoodsType.PACT_CODE_4_TH_BLE_V1_1)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_TH_BLE_V1] = arrayListOf(protocol4ThV1)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_TH_BLE_V1, protocol4ThV1)
        //初始化默认的sku图
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5101, R.mipmap.add_list_type_device_h5101)
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5102, R.mipmap.add_list_type_device_h5102)
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5177, R.mipmap.add_list_type_device_h5174)
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5177, R.mipmap.add_list_type_device_h5177)

        //H5110
        val protocol4H5110 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_H5110, GoodsType.PACT_CODE_VALUE_H5110)
//        val protocol4H5110V1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_H5110_V1, GoodsType.PACT_CODE_VALUE_H5110_V1)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_4_H5110] = arrayListOf(protocol4H5110)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_4_H5110, protocol4H5110)
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5110, R.mipmap.add_list_type_device_5100)

        //H5111
        val protocol4H5111 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_4_H5111, GoodsType.PACT_CODE_VALUE_4_H5111)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_4_H5111] = arrayListOf(protocol4H5111)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_4_H5111, protocol4H5111)
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5111, R.mipmap.add_list_type_device_5111)

        //H5112
        val protocol4H5112 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_4_H5112, GoodsType.PACT_CODE_VALUE_4_H5112)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_4_H5112] = arrayListOf(protocol4H5112)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_4_H5112, protocol4H5112)
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5112, com.govee.ui.R.mipmap.add_list_type_device_5112)

        //H5179
        val protocol4H5179 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_TH_BLE_WIFI_V1_1, GoodsType.PACT_CODE_4_TH_BLE_WIFI_V1_1)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_TH_BLE_WIFI_V1] = arrayListOf(protocol4H5179)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_TH_BLE_WIFI_V1, protocol4H5179)
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5179, R.mipmap.add_list_type_device_h5179)

        //B5178
        val protocol4B5178 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_TH_MULTI_BLE_V1_1, GoodsType.PACT_CODE_4_TH_MULTI_BLE_V1_1)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_TH_BLE_MULTI_V1] = arrayListOf(protocol4B5178)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_TH_BLE_MULTI_V1, protocol4B5178)
        val orderSkuResMap4B5178 = HashMap<Int, Int>()
        orderSkuResMap4B5178[0] = R.mipmap.add_list_type_device_b5178_1
        orderSkuResMap4B5178[1] = R.mipmap.add_list_type_device_b5178_2
        ThemeM.getInstance.addDefSkuOrderRes(Constant4L5.B5178, orderSkuResMap4B5178)
        ThemeM.getInstance.addDefSkuRes(Constant4L5.B5178, R.mipmap.add_list_type_device_b5178_1)

        //H5171
        val protocol4H5171 = GoodsType.beProtocol(1, 1)
        supportProtocolsMap[GoodsType.GOODS_TYPE_VALUE_H5171] = arrayListOf(protocol4H5171)
        Pact.getInstance.addProtocol(GoodsType.GOODS_TYPE_VALUE_H5171, protocol4H5171)
        ThemeM.getInstance.addDefSkuRes(Constant4L5.H5171, R.mipmap.add_list_type_device_5171)
    }

    /**
     * 是否支持不登录即可添加设备(纯蓝牙款都支持)
     */
    fun supportAddNotLogin(goodsType: Int, sku: String): Boolean {
        return when (goodsType) {
            //H5100
            GoodsType.GOODS_TYPE_VALUE_TH_BLE,
                //H5101/H5102/H5174/H5177
            GoodsType.GOODS_TYPE_VALUE_TH_BLE_V1,
                //H5104
            GoodsType.GOODS_TYPE_VALUE_4_TH_H5104,
                //H5105
            GoodsType.GOODS_TYPE_VALUE_TH_BLE_H5105,
                //H5108
            GoodsType.GOODS_TYPE_VALUE_4_TH_H5108,
                //H5110
            GoodsType.GOODS_TYPE_VALUE_4_H5110,
                //H5111
            GoodsType.GOODS_TYPE_VALUE_4_H5111,
                //H5112
            GoodsType.GOODS_TYPE_VALUE_4_H5112,
                //B5178
            GoodsType.GOODS_TYPE_VALUE_TH_BLE_MULTI_V1,
                //H5171
            GoodsType.GOODS_TYPE_VALUE_H5171,
                -> {
                true
            }

            else -> {
                when (sku) {
                    Constant4L5.H5052,
                    Constant4L5.H5072,
                    Constant4L5.H5074,
                    Constant4L5.H5075,
                        -> {
                        true
                    }

                    else -> {
                        false
                    }
                }
            }
        }
    }

    /**
     * 添加流程中判断设备协议是否支持
     * 备注:为方便对应sku设备，采用sku名进行区分，下同
     *      结果中，first-->协议是否支持，second-->该工程是否支持该sku的添加
     */
    fun supportPact(goodsType: Int, sku: String, protocol: Protocol?): Pair<Boolean, Boolean> {
        when (sku) {
            //无goodsType(即goodsType==0)
            Constant4L5.H5051,
            Constant4L5.H5052,
            Constant4L5.H5071,
            Constant4L5.H5072,
            Constant4L5.H5074,
            Constant4L5.H5075,
                -> {
                return Pair(true, true)
            }
            //有goodsType
            Constant4L5.H5100,
            Constant4L5.H5101,
            Constant4L5.H5102,
            Constant4L5.H5103,
            Constant4L5.H5104,
            Constant4L5.H5105,
            Constant4L5.H5108,
            Constant4L5.H5110,
            Constant4L5.H5111,
            Constant4L5.H5112,
            Constant4L5.H5174,
            Constant4L5.H5177,
            Constant4L5.H5179,
            Constant4L5.B5178,
            Constant4L5.H5171,
                -> {
                if (protocol == null) return Pair(false, true)
                val pactType = protocol.pactType
                val pactCode = protocol.pactCode
                if (supportProtocolsMap.isEmpty()) {
                    return Pair(false, true)
                }
                supportProtocolsMap[goodsType]?.let {
                    for (pro in it) {
                        if (pro.isSameProtocol(pactType, pactCode)) return Pair(true, true)
                    }
                }
            }

            else -> {}
        }
        return Pair(false, false)
    }

    /**
     * 账号下无H5151网关时，添加该设备是否须要提示无法使用远程功能
     */
    fun noGwRemind(goodsType: Int = 0, sku: String): Boolean {
        return when (sku) {
            Constant4L5.H5108,
            Constant4L5.H5111,
                -> {
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 设备命名页是否有贴标签的提示
     * 备注：若标签示意图不同时再配置示意图
     */
    fun hasFlagRemind(goodsType: Int = 0, sku: String): Boolean {
        return when (sku) {
            Constant4L5.H5100,
            Constant4L5.H5110,
                -> {
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 添加该设备时，是否有绑定网关的指导页面
     * 备注：绑定网关指导页面，不同sku可能需要再配置
     */
    fun hasBindGwGuide(goodsType: Int = 0, sku: String): Boolean {
        return when (sku) {
            Constant4L5.H5100,
            Constant4L5.H5110,
            Constant4L5.H5111,
            Constant4L5.H5171,
            Constant4L5.H5112,
            Constant4L5.H5108,
                -> {
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 绑定网关的指导视频
     */
    fun guideVideo4BindGw(goodsType: Int = 0, sku: String): String {
        return when (sku) {
            Constant4L5.H5111 -> {
                "https://d1f2504ijhdyjw.cloudfront.net/img/0c1d05f77e93f6faea8f68ce0eedfd00-5111_bind_gateway_tutorial.mp4"
            }

            Constant4L5.H5108 -> {
                "https://d1f2504ijhdyjw.cloudfront.net/img/b0a75d3c119326e6f4b58ab669e6d018-H5108_bind_gateway_tutorial.mp4"
            }

            else -> {
                ""
            }
        }
    }

    /**
     * 提示绑定网关时，指导页面的示意图配置
     * 备注：H5112单独页面
     */
    fun getBindGwGuideImgRes(goodsType: Int = 0, sku: String): Int {
        return when (sku) {
            Constant4L5.H5100,
            Constant4L5.H5110,
                -> {
                R.mipmap.new_pics_add_5151_anzhuang_wancheng_2
            }

            Constant4L5.H5111 -> {
                R.mipmap.new_pics_add_5111_anzhuang_wancheng_2
            }

            Constant4L5.H5108 -> {
                R.mipmap.h5108_pics_anzhuang_yindao_2
            }

            Constant4L5.H5171 -> {
                R.mipmap.h5171_pics_anzhuang_yindao
            }

            else -> {
                com.govee.ui.R.mipmap.new_govee_icon_widget_android
            }
        }
    }

    /**
     * 心跳逻辑版本配置
     * 备注：目前心跳所走协议有两套
     */
    fun bleHeartType(goodsType: Int = 0, sku: String): Int {
        return when (sku) {
            Constant4L5.H5051,
            Constant4L5.H5052,
            Constant4L5.H5071,
            Constant4L5.H5179,
                -> {
                BLE_HEART_TYPE_V0
            }

            Constant4L5.H5140,
                -> {
                BLE_HEART_TYPE_V2
            }

            else -> {
                BLE_HEART_TYPE_V1
            }
        }
    }

    /**
     * 通过ble加载设备端图表数据也有多种形式
     */
    fun loadBleChartDataType(goodsType: Int = 0, sku: String, device: String): Int {
        when (sku) {
            Constant4L5.H5051,
            Constant4L5.H5052,
            Constant4L5.H5071,
                -> {
                return LOAD_BLE_CHART_DATA_TYPE_V0
            }

            Constant4L5.H5179,
                -> {
                var bleHv = ""
                DeviceListConfig.read().getDeviceByKey(sku, device)?.let {
                    bleHv = it.versionHard
                }
                return when (bleHv) {
                    Constant4L5.H5179_BLE_HV_OLD -> {
                        //旧款
                        LOAD_BLE_CHART_DATA_TYPE_V0
                    }

                    else -> {
                        //新款
                        LOAD_BLE_CHART_DATA_TYPE_V1
                    }
                }
            }

            else -> {
                return LOAD_BLE_CHART_DATA_TYPE_V1
            }
        }
    }

    /**
     * 不同sku的广播可能不同，故需配置
     */
    fun broadcastType(goodsType: Int, sku: String, device: String): Int {
        return when (sku) {
            Constant4L5.H5051,
            Constant4L5.H5052,
            Constant4L5.H5071,
            Constant4L5.H5074,
                -> {
                //数据部分格式类型:batteryWarn(1byte)+tem(2bytes)+hum(2bytes)+battery(1byte)
                BROADCAST_TYPE_V0
            }

            Constant4L5.H5100,
            Constant4L5.H5101,
            Constant4L5.H5102,
            Constant4L5.H5103,
            Constant4L5.H5104,
            Constant4L5.H5105,
            Constant4L5.H5110,
            Constant4L5.H5174,
            Constant4L5.H5177,
            Constant4L5.H5171,
                -> {
                //数据部分格式类型:pactType(2bytes)+pactCode(1byte)+tem_hum(3bytes)+battery(1byte)
                BROADCAST_TYPE_V1
            }

            Constant4L5.H5179,
                -> {
                var bleHv = ""
                DeviceListConfig.read().getDeviceByKey(sku, device)?.let {
                    bleHv = it.versionHard
                }
                return when (bleHv) {
                    Constant4L5.H5179_BLE_HV_OLD -> {
                        //旧款
                        //数据部分格式类型:pactType(2bytes)+pactCode(1byte)+tem(2bytes)+hum(2bytes)+battery(1byte)
                        BROADCAST_TYPE_V2
                    }

                    else -> {
                        //新款
                        BROADCAST_TYPE_V1
                    }
                }
            }

            Constant4L5.H5108,
            Constant4L5.H5111,
                -> {
                //数据部分格式类型:pactType(2bytes)+pactCode(1byte)+tem_hum(3bytes)+battery(1byte)+alarmType(1byte)
                //备注:alarmType的值为0/1，为延时告警所专用，值为1表示设备已经开始告警
                BROADCAST_TYPE_V3
            }

            Constant4L5.H5112,
                -> {
                //数据部分格式类型:pactType(2bytes)+pactCode(1byte)+tem_hum(3bytes)+battery(1byte)+alarmType(1byte)+pbIndex(1byte)
                //备注:alarmType的值为0/1，为延时告警所专用，值为1表示设备已经开始告警
                BROADCAST_TYPE_V6
            }

            Constant4L5.H5072,
            Constant4L5.H5075,
                -> {
                //数据部分格式类型:未知(1byte)+tem_hum(3bytes)+battery(1byte)
                BROADCAST_TYPE_V4
            }

            else -> {
                UNDEFINED
            }
        }
    }

    /**
     * 添加时是否需要配对
     * 备注:1.需配对的设备须要读取设备密钥、需要点击或长按设备对应按钮的操作
     *     2.配对方式有两种：A:v0=->直接跳转到配对页，同时连接设备；再长按设备完成配对（读密钥-->读其他信息）
     *                          ：!!!此种方式配对的sku,在通讯前须先校验密钥，否则连接10s左右，设备会主动断开连接
     *
     *                     B:v1=->先跳出连接弹窗，连接后跳转至配对页面；再单击设备完成配对（读密钥-->检测密钥-->读其他信息）
     */
    fun supportPair(goodsType: Int, sku: String): Int {
        return when (sku) {
            Constant4L5.H5051,
            Constant4L5.H5052,
            Constant4L5.H5071,
            Constant4L5.H5072,
                -> {
                PAIR_WAY_V0
            }

            Constant4L5.H5103,
            Constant4L5.H5104,
            Constant4L5.H5105,
            Constant4L5.H5108,
            Constant4L5.H5110,
            Constant4L5.H5111,
            Constant4L5.H5112,
            Constant4L5.H5171,
                -> {
                PAIR_WAY_V1
            }

            else -> {
                UNDEFINED
            }
        }
    }

    /**
     * 添加时需要配对设备的配对描述图
     */
    fun getPairDesImgRes(goodsType: Int, sku: String): Int {
        return when (sku) {
            Constant4L5.H5051,
            Constant4L5.H5071,
                -> {
                R.mipmap.new_add_pics_sensor_set_5051
            }

            Constant4L5.H5052,
            Constant4L5.H5072,
                -> {
                R.mipmap.new_add_pics_sensor_set_5052
            }

            Constant4L5.H5103,
                -> {
                R.mipmap.new_add_pics_sensor_set_5103
            }

            Constant4L5.H5104,
                -> {
                R.mipmap.new_add_list_type_device_5104
            }

            Constant4L5.H5105,
                -> {
                R.mipmap.new_add_list_type_device_5105
            }

            Constant4L5.H5108,
                -> {
                R.mipmap.new_add_list_type_device_5108
            }

            Constant4L5.H5110,
                -> {
                R.mipmap.h5110_pics_anzhuang_yindao
            }

            Constant4L5.H5111,
                -> {
                R.mipmap.new_pics_add_h5111_anzhuang_yindao
            }

            else -> {
                com.govee.ui.R.mipmap.new_govee_icon_widget_android
            }
        }
    }

    /**
     * 配对页图片是否服务器配置
     */
    fun supportPairPicServiceConfig(goodsType: Int, sku: String): Pair<Boolean, String> {
        return when (sku) {
            Constant4L5.H5171 -> {
                Pair(true, "new_add_pics_sensor_set_5171")
            }

            Constant4L5.H5112 -> {
                Pair(true, "h5112_pics_anzhuang_yindao_1")
            }

            else -> {
                Pair(false, "")
            }
        }
    }

    /**
     * 设备只支持433协议与网关通讯
     * 备注：1.通过433协议与网关通讯的子设备即不支持ble,也不支持wifi
     *      2.该款有许多操作比较特殊，需注意
     */
    fun supportOnly433(goodsType: Int, sku: String): Boolean {
        return when (sku) {
            Constant4L5.H5053 -> {
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 是否为主+从款设备
     */
    fun isMainSubDevice(goodsType: Int, sku: String): Boolean {
        return when (sku) {
            Constant4L5.B5178 -> {
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 设备是否具备wifi能力
     */
    fun supportWifi(goodsType: Int, sku: String): Boolean {
        return when (sku) {
            Constant4L5.H5051,
            Constant4L5.H5071,
            Constant4L5.H5103,
            Constant4L5.H5106,
            Constant4L5.H5179,
                -> {
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 获取设备上传温湿度数据频率的信息
     */
    fun getThCdUploadFrequencyInfo(goodsType: Int, sku: String): Pair<Int, IntArray> {
        return when (sku) {
            Constant4L5.H5071,
            Constant4L5.H5103,
            Constant4L5.H5179,
                -> {
                Pair(UPLOAD_FREQUENCY_V0, intArrayOf(10, 30, 60))
            }

            Constant4L5.H5051,
                -> {
                Pair(UPLOAD_FREQUENCY_V1, intArrayOf(10, 30))
            }

            else -> {
                Pair(UNDEFINED, intArrayOf())
            }
        }
    }

    /**
     * 不同sku的绑定接口可能有区别，故需配置
     */
    fun bindApiType(goodsType: Int, sku: String): Int {
        return when (sku) {
            Constant4L5.H5100,
            Constant4L5.H5101,
            Constant4L5.H5102,
            Constant4L5.H5103,
            Constant4L5.H5174,
            Constant4L5.H5177,
            Constant4L5.H5179,
                -> {
                //使用接口：app/v2/bind?type=th
                BIND_API_V0
            }

            Constant4L5.H5051,
            Constant4L5.H5052,
            Constant4L5.H5071,
            Constant4L5.H5072,
            Constant4L5.H5074,
            Constant4L5.H5075,
            Constant4L5.H5104,
            Constant4L5.H5105,
            Constant4L5.H5108,
            Constant4L5.H5110,
            Constant4L5.H5111,
            Constant4L5.H5112,
            Constant4L5.H5171,
            Constant4L5.H5140
                -> {
                //使用接口：device/rest/devices/v1/bind
                BIND_API_V1
            }

            Constant4L5.B5178,
                -> {
                //使用接口：app/v1/multi-bind?type=th
                BIND_API_V2
            }

            else -> {
                UNDEFINED
            }
        }
    }

    /**
     * 是否支持设备锁
     * 备注：一般来说，添加时须要配对的一般是不需要设备锁，故当前的配对页面中，在添加的时候并没有检测设备锁的操作
     *      故后续如果有既须配对又须设备锁的sku，须将配对页的设备锁校验步骤加上
     */
    fun supportDeviceLock(goodsType: Int, sku: String): Boolean {
        return when (sku) {
            Constant4L5.H5074,
            Constant4L5.H5075,
            Constant4L5.H5100,
            Constant4L5.H5101,
            Constant4L5.H5102,
            Constant4L5.H5174,
            Constant4L5.H5177,
            Constant4L5.H5179,
            Constant4L5.B5178,
                -> {
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 是否支持湿度
     * 备注：H5112需另行处理
     */
    fun supportHum(goodsType: Int, sku: String): Boolean {
        return Constant4L5.supportHum(goodsType, sku)
    }

    /**
     * 是否支持pm2.5
     */
    fun supportPm25(goodsType: Int, sku: String): Boolean {
        return Constant4L5.supportPm25(goodsType, sku)
    }

    /**
     * 是否支持推送
     */
    fun supportPush(goodsType: Int, sku: String): Boolean {
        return when (sku) {
            Constant4L5.H5051,
            Constant4L5.H5052,
            Constant4L5.H5053,
            Constant4L5.H5071,
            Constant4L5.H5072,
            Constant4L5.H5074,
            Constant4L5.H5075,
            Constant4L5.H5100,
            Constant4L5.H5101,
            Constant4L5.H5102,
            Constant4L5.H5103,
            Constant4L5.H5104,
            Constant4L5.H5105,
            Constant4L5.H5106,
            Constant4L5.H5108,
            Constant4L5.H5110,
            Constant4L5.H5111,
            Constant4L5.H5112,
            Constant4L5.H5174,
            Constant4L5.H5177,
            Constant4L5.H5179,
            Constant4L5.B5178,
            Constant4L5.H5171,
                -> {
                true
            }

            else -> {
                false
            }
        }
    }

    /**
     * 是否支持本地告警
     * 备注：含wifi功能的不支持本地告警
     */
    fun supportLocalPush(goodsType: Int, sku: String): Boolean {
        return supportPush(goodsType, sku) && !supportWifi(goodsType, sku)
    }

    /**
     * 时间步进值(连续的两个点，其标准时间间隔值,单位分钟)
     */
    fun getTimeStep(goodsType: Int = GoodsType.GOODES_TYPE_NO_SUPPORT, sku: String): Int {
        return Constant4L5.getTimeStep(goodsType, sku)
    }

    /**
     * 是否支持gid安全
     */
    fun supportGidSafe(goodsType: Int, sku: String, bleHv: String): Boolean {
        return when (sku) {
            Constant4L5.H5179,
                -> {
                bleHv == Constant4L5.H5179_BLE_HV_NEW
            }

            else -> {
                false
            }
        }
    }
}
