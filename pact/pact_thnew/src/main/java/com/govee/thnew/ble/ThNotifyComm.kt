package com.govee.thnew.ble

import com.govee.base2newth.AbsNotifyComm
import com.govee.base2newth.BleThProtocol
import com.govee.base2newth.ThConsV1
import com.govee.thnew.ble.notify.Notify4BuzzerWarning
import com.govee.thnew.ble.notify.Notify4ThCdLoadOver
import com.govee.thnew.ble.notify.Notify4ThCdQuerying
import com.govee.thnew.ble.notify.Notify4WifiConnect
import java.util.UUID

/**
 * <AUTHOR>
 * @date created on 2024/2/18
 * @description 温湿度计-->设备主动上报的解析器
 */
class ThNotifyComm : AbsNotifyComm() {

    companion object {
        private const val serviceUuidStr = "494e5445-4c4c-495f-524f-434b535f4857"

        //从设备端同步图表数据时相关指令的主动上报特征值
        private const val characteristicUuidStr4ThCd = "494e5445-4c4c-495f-524f-434b535f2012"

        //配网时设备连上网络的主动上报
        private const val characteristicUuidStr4Wifi = "494e5445-4c4c-495f-524f-434b535f2011"
    }

    init {
        //配网结果
        notifyParseList.add(Notify4WifiConnect(serviceUuidStr, characteristicUuidStr4Wifi))
        //数据读取完成解析器
        notifyParseList.add(Notify4ThCdLoadOver(serviceUuidStr, characteristicUuidStr4ThCd))
        //数据读取中解析器
        notifyParseList.add(Notify4ThCdQuerying(serviceUuidStr, characteristicUuidStr4ThCd))
        //设备蜂鸣器
        notifyParseList.add(Notify4BuzzerWarning(serviceUuidStr, characteristicUuidStr4Wifi))
    }

    override fun serviceUUID(): UUID {
        return UUID.fromString(serviceUuidStr)
    }

    override fun characteristicUUID(): UUID {
        return UUID.fromString(characteristicUuidStr4ThCd)
    }

    override fun supportWifiConnectNotify(): Boolean {
        return false
    }

    override fun isSelfComm(serviceUuid: String, characteristicUuid: String, values: ByteArray): Boolean {
        if (serviceUuidStr != serviceUuid) {
            return false
        }
        if (characteristicUuidStr4ThCd != characteristicUuid && characteristicUuidStr4Wifi != characteristicUuid) {
            return false
        }
        return if (values.size > 2) {
            values[0] == BleThProtocol.NOTIFY
        } else false
    }

    override fun parsePriority(): Int {
        return ThConsV1.comm_parse_priority_notify_comm
    }
}