package com.govee.thnew.ui.op

import android.os.Handler
import android.os.Looper
import android.text.TextUtils
import com.govee.base2home.Constant4L5
import com.govee.base2home.broadcast.event.NetChangeEvent
import com.govee.base2home.device.DeviceM
import com.govee.base2home.device.EventDeviceTopic
import com.govee.base2home.iot.Cmd
import com.govee.base2home.iot.Cmd4Status
import com.govee.base2home.iot.ConnectType
import com.govee.base2home.iot.Iot
import com.govee.base2home.iot.IotConnectEvent
import com.govee.base2home.iot.IotTransactions
import com.govee.base2home.iot.ResultPt
import com.govee.base2home.iot.getJsonObjectStr
import com.govee.base2home.iot.protype.v2.IotMsgEventV2
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.reform4dbgw.view.Event4GwIotOnline.Companion.sendEvent
import com.govee.base2kt.utils.BleUtils
import com.govee.base2light.ble.controller.BleProtocolConstants
import com.govee.base2light.pact.iot.CmdPtReal
import com.govee.base2newth.BleThProtocol
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.base2newth.deviceitem.GatewayInfo
import com.govee.base2newth.deviceitem.GwIot4StatusCommand
import com.govee.kt.ui.device.Event4IotHeartInSub
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.controller.Controller4CheckNetH5112
import com.govee.thnew.ble.controller.Controller4PtRealOp
import com.govee.thnew.pact.ThBroadcastUtil
import com.govee.thnew.pact.h5112.Dialog4H5112CloseWarn
import com.govee.thnew.pact.h5112.Ext4H5112
import com.govee.thnew.ui.Vm4ThOpManager
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.CaughtRunnable
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.NetUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2024/3/25
 * @description 温湿度计-->子设备网关的iot操作管理类
 */
internal class IotOpManager {

    companion object {
        //iot心跳间隔时长
        private const val IOT_CONNECT_HEART_INTERVAL = 30 * 1000L

        //iot重新连接的间隔时长
        private const val IOT_RECONNECT_TIME_4_FAILURE = 10 * 1000L

        //iot通讯回复超时
        private const val IOT_COMM_OVER_TIME = 15 * 1000L
    }

    init {
        //注册EvenBus
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
    }

    private var gwInfo: GatewayInfo? = null
    private var gwSku: String = ""
    private var gwDevice: String = ""
    private var gwTopic: String? = ""

    /**
     * 子设备iot在线与否(即app-设备-服务端的来回链路是通畅的)
     */
    @Volatile
    var isSubIotConnected: Boolean? = null
        private set

    /**
     * 网关iot是否在线
     */
    @Volatile
    var isGwIotConnected: Boolean? = null
        private set

    private val mHandler by lazy {
        Handler(Looper.getMainLooper())
    }
    private val iotTransactions by lazy {
        IotTransactions()
    }

    private var iotOpListener: IotOpListener? = null

    /**
     * 初始化方法
     */
    fun start(gwInfo: GatewayInfo, iotOpListener: IotOpListener) {
        SafeLog.w("xiaobing") { "IotOpManager--start-->开始网关的iot心跳..." }
        //旧网关恢复首页的定时心跳
        Event4IotHeartInSub.sendEvent(this.gwSku, this.gwDevice, true)
        //赋值
        this.gwInfo = gwInfo
        this.gwSku = gwInfo.sku ?: ""
        this.gwDevice = gwInfo.device ?: ""
        this.gwTopic = gwInfo.topic ?: ""
        this.iotOpListener = iotOpListener
        //连接iot并发送iot心跳
        toConnectIot()
    }

    private fun toConnectIot() {
        Event4IotHeartInSub.sendEvent(gwSku, gwDevice, false)
        if (TextUtils.isEmpty(gwTopic)) {
            //无topic，则去获取
            DeviceM.getInstance.getDeviceTopic(gwSku, gwDevice)
            //设置超时
            mHandler.removeCallbacks(iotCommOtRunnable)
            mHandler.postDelayed(iotCommOtRunnable, IOT_RECONNECT_TIME_4_FAILURE)
            return
        }
        checkAndConnectIot()
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onDeviceTopicResponse(topicEvent: EventDeviceTopic) {
        if (gwSku == topicEvent.sku && gwDevice == topicEvent.device) {
            //移除超时
            mHandler.removeCallbacks(iotCommOtRunnable)
            //根据有无topic做相应的操作
            if (!TextUtils.isEmpty(topicEvent.topic)) {
                gwTopic = topicEvent.topic
                checkAndConnectIot()
            } else {
                changeConnectType(isSubIotConnected = false, isGwIotConnected = false)
            }
        }
    }

    /**
     * 检测并连接iot
     */
    private fun checkAndConnectIot() {
        if (Iot.getInstance.isConnected) {
            //开始iot心跳
            mHandler.removeCallbacks(iotHeartRunnable)
            mHandler.postDelayed(iotHeartRunnable, 0)
        } else {
            //尝试连接
            mHandler.removeCallbacks(connectIotRunnable)
            mHandler.postDelayed(connectIotRunnable, 0)
        }
    }

    /**
     * 连接状态发生变化
     */
    private fun changeConnectType(isSubIotConnected: Boolean, isGwIotConnected: Boolean) {
        //状态是否发生了变化
        if (this.isSubIotConnected != isSubIotConnected || this.isGwIotConnected != isGwIotConnected) {
            SafeLog.w("xiaobing") { "IotOpManager--changeConnectType-->sub->${isSubIotConnected},gw->${isGwIotConnected}" }
            this.isSubIotConnected = isSubIotConnected
            this.isGwIotConnected = isGwIotConnected
            iotOpListener?.onStatusChange(isSubIotConnected, isGwIotConnected)
            //将事件扩撒
            sendEvent(isGwIotConnected)
        }
    }

    /**
     * iot通讯超时的Runnable
     */
    private val iotCommOtRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                SafeLog.w("xiaobing") { "IotOpManager--runSafe-->iot通讯超时" }
                changeConnectType(isSubIotConnected = false, isGwIotConnected = false)
                //移除自身
                mHandler.removeCallbacks(this)
                //置空-iotSetOtRunnable
                iotOpOutTimeRunnable?.run {
                    //设置回复失败
                    this.controller.run {
                        this.onResult(false, null)
                    }
                    //移除超时
                    mHandler.removeCallbacks(this)
                }
                iotOpOutTimeRunnable = null
            }
        }
    }

    /**
     * 连接iot的Runnable
     */
    private val connectIotRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                SafeLog.w("xiaobing") { "IotOpManager--runSafe-->尝试iot连接" }
                Iot.getInstance.toConnectIot()
                //移除自身
                mHandler.removeCallbacks(this)
            }
        }
    }

    /**
     * iot心跳Runnable
     */
    private val iotHeartRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
//                SafeLog.w("xiaobing") { "IotOpManager--runSafe-->deviceKey=->${gwSku}_${gwDevice},发送心跳..." }
                val statusCmd = Cmd4Status()
                Iot.getInstance.read(gwTopic, iotTransactions.createTransaction(true), statusCmd.cmd, statusCmd.cmdVersion)
                //设置回复超时
                mHandler.removeCallbacks(iotCommOtRunnable)
                mHandler.postDelayed(iotCommOtRunnable, IOT_COMM_OVER_TIME)
                //准备下一次心跳
                mHandler.removeCallbacks(this)
                mHandler.postDelayed(this, IOT_CONNECT_HEART_INTERVAL)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onIotConnectEvent(event: IotConnectEvent) {
        if (ConnectType.connected == event.type) {
            //移除iot连接超时
            mHandler.removeCallbacks(connectIotRunnable)
            //重新开始iot心跳
            mHandler.removeCallbacks(iotHeartRunnable)
            mHandler.postDelayed(iotHeartRunnable, 0)
        } else if (ConnectType.disconnect == event.type) {
            //移除心跳
            mHandler.removeCallbacks(iotHeartRunnable)
            //尝试重连
            mHandler.removeCallbacks(connectIotRunnable)
            mHandler.postDelayed(connectIotRunnable, IOT_RECONNECT_TIME_4_FAILURE)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onNetChange(event: NetChangeEvent?) {
        //网络连接上
        if (NetUtil.isNetworkAvailable(BaseApplication.getContext())) {
            toConnectIot()
            //网络断开
        } else {
            mHandler.removeCallbacks(iotCommOtRunnable)
            mHandler.postDelayed(iotCommOtRunnable, 0)
        }
    }

    @Volatile
    private var iotOpOutTimeRunnable: IotOpOutTimeRunnable? = null

    /**
     * 发送iot指令
     */
    fun sendCommand(controller: Controller4PtRealOp) {
        //发送iot指令
        val ptRealCmd = CmdPtReal(controller.value)
        Iot.getInstance.write(gwTopic, iotTransactions.createTransaction(false), ptRealCmd)
        //设置iot指令回复超时(上报的时间较长)
        iotOpOutTimeRunnable = IotOpOutTimeRunnable(controller.originalController)
        mHandler.removeCallbacks(iotOpOutTimeRunnable!!)
        mHandler.postDelayed(iotOpOutTimeRunnable!!, IOT_COMM_OVER_TIME * 2)
    }

    /**
     * 联网检测
     */
    fun checkNet4H5112(sno: Int, resultCallback: (isOk2Gw: Boolean, isOk2Sub: Boolean) -> Unit) {
        sendCommand(Controller4PtRealOp(sno, Controller4CheckNetH5112 {
            if (it) {
                resultCallback.invoke(true, true)
            } else {
                resultCallback.invoke(isGwIotConnected.isTrue(), false)
            }
        }))
    }

    /**
     * iot读写操作的回调事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onIotMsgEventV2(iotEvent: IotMsgEventV2) {
        val iotMsg = iotEvent.msgV2
        val isSameDevice = iotMsg.isSameDevice(gwSku, gwDevice)
        if (!isSameDevice) {
            return
        }
        //移除iot通讯超时
        mHandler.removeCallbacks(iotCommOtRunnable)
        //status,ptReal中的数据字端都在"op"字段中，若有其他情况，用parseIotExtInfo进行特殊对待
        getJsonObjectStr(iotEvent.jsonStr, Cmd.parse_json_op)?.let {
            JsonUtil.fromJson(it, ResultPt::class.java)?.run {
                parseIotOpInfo(iotMsg.cmd, this.bytes())
            } ?: parseIotExtInfo(iotMsg.cmd, iotEvent.jsonStr)
        } ?: parseIotExtInfo(iotMsg.cmd, iotEvent.jsonStr)
    }

    /**
     * 通用iot回复信息解析(即数据字段在"op"中)
     */
    private fun parseIotOpInfo(cmd: String, iotResponseBytes: List<ByteArray>) {
        //返回值在"op"中cmd,如：status,ptReal,multiSync
        when (cmd) {
            Cmd.status -> {
                when (gwSku) {
                    Constant4L5.H5151 -> {
                        parseInfo4H5151(iotResponseBytes)
                    }

                    Constant4L5.H5042,
                    Constant4L5.H5043 -> {
                        parseInfo4Common(iotResponseBytes)
                    }

                    Constant4L5.H5044 -> {
                        Vm4ThOpManager.instance()?.getDeviceInfo()?.let {
                            when (it.sku) {
                                Constant4L5.H5112 -> {
                                    parseInfo4H5112Sub(iotResponseBytes)
                                }

                                else -> {
                                    parseInfo4Common(iotResponseBytes)
                                }
                            }
                        }
                    }

                    else -> {}
                }
            }

            Cmd.ptReal,
            Cmd.multiSync -> {
                val isValidDevice = (Constant4L5.H5044 == gwSku) && (Constant4L5.H5112 == Vm4ThOpManager.instance()?.getDeviceInfo()?.sku)
                val isValidCommand = (iotOpOutTimeRunnable != null) && iotResponseBytes.isNotEmpty()
                if (isValidDevice && isValidCommand) {
                    //处理回复
                    val iotRespBys = iotResponseBytes[0]
                    SafeLog.i("xiaobing") { "IotOpManager--parseIotCommonInfo-->cmd:${cmd},${BleUtil.bytesToHexString(iotRespBys)}" }
                    val isResultReport = (iotRespBys.size == 20) && (iotRespBys[0] == BleThProtocol.NOTIFY) && (iotRespBys[1] == BleProtocol.COMMAND_TYPE_4_PT_REAL_OP_RESULT_NOTIFY)
                    if (isResultReport) {
                        val respDataBys = ByteArray(18)
                        System.arraycopy(iotRespBys, 2, respDataBys, 0, respDataBys.size)
                        //确认为同一个设备
                        if (gwInfo?.sno != respDataBys[0].toInt()) return
                        //处理数据
                        iotOpOutTimeRunnable!!.controller.run {
                            //确认为同一条指令后，回复成功
                            if (this.isSameController(BleThProtocol.SINGLE_WRITE, respDataBys[1])) {
                                val respValues = ByteArray(20).apply {
                                    fill(0)
                                }
                                respValues[0] = BleThProtocol.SINGLE_WRITE
                                respValues[1] = respDataBys[1]
                                this.onResult(true, respValues)
                                //移除超时
                                mHandler.removeCallbacks(iotOpOutTimeRunnable!!)
                                //置空-iotSetOtRunnable
                                iotOpOutTimeRunnable = null
                            }
                        }
                    }
                }
            }

            else -> {
                if (iotResponseBytes.isNotEmpty()) {
                    SafeLog.w("xiaobing") { "IotOpManager--parseIotCommonInfo-->非status->${BleUtil.bytesToHexString(iotResponseBytes[0])}" }
                }
            }
        }
    }

    /**
     * 网关为H5044,子设备为H5112
     */
    private fun parseInfo4H5112Sub(iotResponseBytes: List<ByteArray>) {
        for (respBys in iotResponseBytes) {
            if (respBys[0] == BleProtocolConstants.SINGLE_READ || respBys[0] == BleProtocolConstants.NOTIFY) {
                when (respBys[1]) {
                    Ext4H5112.COMMAND_TYPE_4_DEVICE_INFO,
                    Ext4H5112.COMMAND_TYPE_4_DEVICE_INFO_NOTIFY,
                        -> {
                        //确认子设备为H5112
                        if (BleUtils.getUnsignedByte(respBys[3]) == Ext4H5112.DEVICE_TYPE) {
                            val validBytes = ByteArray(respBys.size - 3)
                            System.arraycopy(respBys, 2, validBytes, 0, validBytes.size)
                            Ext4H5112.parseSubInfo(validBytes).let {
                                if (gwInfo?.sno == it.sno) {
                                    SafeLog.i("xiaobing") { "IotOpManager--parseInfo4H5112Sub-->hex:${BleUtil.bytesToHexString(respBys)},\n${JsonUtil.toJson(it)}" }
                                    //更新信号值
                                    Vm4ThOpManager.instance()?.getGateInfo()?.signal = it.signal
                                    //修改连接标识位
                                    changeConnectType(it.online.isTrue(), true)
                                    //更新子设备信息
                                    if (isSubIotConnected.isTrue() && !Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                                        //回调心跳信息
                                        iotOpListener?.iotHeartInfo(Pair(Triple(it.tem4Pb1, it.hum4Pb1, it.tem4Pb2), it.updateTimeMills ?: 0))
                                    }
                                    //报警弹窗
                                    Vm4ThOpManager.instance()?.getDeviceInfo()?.let { bindExt ->
                                        it.status?.let { warnInfo ->
                                            //存在子设备离线，但网关还是上报报警的情况。故显示报警弹窗的前提是设备在线
                                            if (it.online.isTrue()) {
                                                if (warnInfo.first.first) {
                                                    Dialog4H5112CloseWarn.showDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, true, warnInfo.first.second, Pair(it.tem4Pb1, it.hum4Pb1))
                                                } else {
                                                    Dialog4H5112CloseWarn.hideDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, true)
                                                }
                                                if (warnInfo.second.first) {
                                                    Dialog4H5112CloseWarn.showDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, false, warnInfo.second.second, Pair(it.tem4Pb1, it.hum4Pb1))
                                                } else {
                                                    Dialog4H5112CloseWarn.hideDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM_HUM, false)
                                                }
                                                if (warnInfo.third.first) {
                                                    Dialog4H5112CloseWarn.showDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM, true, warnInfo.third.second, Pair(it.tem4Pb2, 6000))
                                                } else {
                                                    Dialog4H5112CloseWarn.hideDialog(bindExt.sku, bindExt.device, Constant4L5.PROBE_INDEX_4_TEM, true)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    else -> {}
                }
            }
        }
    }

    /**
     * 其网关为H5151
     */
    private fun parseInfo4H5151(iotResponseBytes: List<ByteArray>) {
        try {
            val statusBa = ByteArray(iotResponseBytes.size * 20)
            for ((index, bytes) in iotResponseBytes.withIndex()) {
                System.arraycopy(bytes, 0, statusBa, index * 20, bytes.size)
            }
            SafeLog.w("xiaobing") { "IotOpManager--parseInfo4H5151-->${BleUtil.bytesToHexString(statusBa)}" }
            //下标从1开始
            val subIndex = gwInfo?.index ?: 0
            if (subIndex <= 0) {
                changeConnectType(isSubIotConnected = false, isGwIotConnected = true)
                return
            }
            //第一个字节为绑定的子设备个数，每个子设备10个字节数据 1byte 在线状态，1 byte电量，4 byte实时温湿度，4 byte 时间戳分钟
            val startIndex = 1 + (subIndex - 1) * 10
            //修改连接标识位
            changeConnectType(statusBa[startIndex].toInt() > 0, true)
            //优先使用蓝牙心跳数据
            //若在线-则需要解析后续的相关信息
            if (isSubIotConnected.isTrue() && !Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                val validBytes = ByteArray(10)
                System.arraycopy(statusBa, startIndex, validBytes, 0, validBytes.size)
                parseThInfo(validBytes)
            }
        } catch (e: Exception) {
            SafeLog.w("xiaobing") { "IotOpManager--parseIotCommonInfo-->解析iot的status指令回复的相关数据出错->\n${e.message}" }
            e.printStackTrace()
        }
    }

    /**
     * 其网关为类H5151，如H5042、H5043、H5044
     */
    private fun parseInfo4Common(iotResponseBytes: List<ByteArray>) {
        //下标从1开始
        val subIndex = gwInfo?.index ?: 0
        if (subIndex <= 0) {
            changeConnectType(isSubIotConnected = false, isGwIotConnected = true)
            return
        }
        try {
            for (iotRespBytes in iotResponseBytes) {
                val proType = iotRespBytes[0]
                val commType = iotRespBytes[1]
                val subCommType = iotRespBytes[2]
                if (proType == BleThProtocol.SINGLE_READ && commType == GwIot4StatusCommand.commType4Common) {
                    SafeLog.i("xiaobing") { "IotOpManager--parseInfo4Common-->${BleUtil.bytesToHexString(iotRespBytes)}" }
                    when (subCommType) {
                        //H5151网关功能下的子设备总数
                        GwIot4StatusCommand.subNum4Common -> {
                            //无需处理
                        }
                        //子设备的详细信息
                        GwIot4StatusCommand.subInfo4Common -> {
                            val index = iotRespBytes[13].toInt()
                            if (subIndex == index) {
                                val validBytes = ByteArray(10)
                                System.arraycopy(iotRespBytes, 3, validBytes, 0, validBytes.size)
                                //优先使用蓝牙心跳数据
                                //若在线-则需要解析后续的相关信息
                                if (isSubIotConnected.isTrue() && !Vm4ThOpManager.instance()?.hasBleConnected().isTrue()) {
                                    parseThInfo(validBytes)
                                }
                                //修改连接标识位
                                changeConnectType(validBytes[0].toInt() == 1, true)
                                break
                            }
                        }

                        else -> {}
                    }
                }
            }
        } catch (e: Exception) {
            SafeLog.w("xiaobing") { "IotOpManager--parseInfo4Common-->${e.message}" }
        }
    }

    /**
     * 解析实时信息数据
     * @param validBytes 10个字节的信息数据
     */
    private fun parseThInfo(validBytes: ByteArray) {
        val timeBts = ByteArray(4)
        System.arraycopy(validBytes, 6, timeBts, 0, 4)
        val time = BleUtil.getSignedInt(timeBts) * 60 * 1000L
        SafeLog.w("xiaobing") { "IotOpManager--parseThInfo-->${gwSku}_${gwDevice}-->${BleUtil.bytesToHexString(validBytes)}" }

        var tem = 0
        var hum = 0
        val temBytes = ByteArray(2)
        System.arraycopy(validBytes, 2, temBytes, 0, 2)
        //温度无效时，湿度已无需处理。目前的温湿度是一个传感器所得
        if (temBytes[0] == ThBroadcastUtil.INVALID_BYTE && temBytes[1] == ThBroadcastUtil.INVALID_BYTE) {
            //无需其他处理
        } else {
            tem = BleUtil.getSignedIntV2(temBytes, true)
            val humBytes = ByteArray(2)
            System.arraycopy(validBytes, 4, humBytes, 0, 2)
            hum = BleUtil.getSignedIntV2(humBytes, true)
        }
        //回调心跳信息
        iotOpListener?.iotHeartInfo(Pair(Triple(tem, hum, 100), time))
    }

    /**
     * 其他cmd指令，自己解去
     */
    private fun parseIotExtInfo(cmd: String, jsonStr: String) {
        SafeLog.w("xiaobing") { "IotOpManager--parseIotExtInfo-->回复数据不在-op-字段中，回复数据->${jsonStr}" }
    }

    interface IotOpListener {
        fun onStatusChange(isSubIotConnected: Boolean, isGwIotConnected: Boolean)
        fun iotHeartInfo(heartInfo: Pair<Triple<Int, Int, Int>, Long>)
    }

    fun release() {
        Event4IotHeartInSub.sendEvent(gwSku, gwDevice, true)
        mHandler.removeCallbacksAndMessages(null)
        //注销EventBus
        EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
    }
}

/**
 * <AUTHOR>
 * @date created on 2025/06/6
 * @description iot设置指令超时的Runnable
 */
private class IotOpOutTimeRunnable(var controller: AbsControllerWithCallback) : CaughtRunnable() {

    override fun runSafe() {
        controller.onResult(false, null)
    }
}