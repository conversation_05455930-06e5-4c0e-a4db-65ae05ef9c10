package com.govee.thnew.ble.controller

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4ComfortTemHum

/**
 * @description 温湿度计-->设置温、湿度舒适度的controller
 * 备注：H5171使用
 */
class Controller4ComfortTemHum : AbsControllerWithCallback {

    private var temRange = intArrayOf()
    private var humRange = intArrayOf()

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     */
    constructor(temRange: IntArray, humRange: IntArray, writeCallBack: ((result: Boolean) -> Unit)) : super(writeCallBack) {
        this.temRange = temRange
        this.humRange = humRange
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_comfort_tem_hum
    }

    override fun translateWrite(): ByteArray {
        val bytes = mutableListOf<Byte>()
        bytes.addAll(BleUtil.getSignedBytesFor2(temRange[0], false).toList())
        bytes.addAll(BleUtil.getSignedBytesFor2(temRange[1], false).toList())
        bytes.addAll(BleUtil.getSignedBytesFor2(humRange[0], false).toList())
        bytes.addAll(BleUtil.getSignedBytesFor2(humRange[1], false).toList())
        return bytes.toByteArray()
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val temLow = BleUtil.getSignedIntV2(byteArrayOf(validBytes[0], validBytes[1]), false)
        val temHigh = BleUtil.getSignedIntV2(byteArrayOf(validBytes[2], validBytes[3]), false)
        val humLow = BleUtil.getSignedIntV2(byteArrayOf(validBytes[4], validBytes[5]), false)
        val humHigh = BleUtil.getSignedIntV2(byteArrayOf(validBytes[6], validBytes[7]), false)
        Event4ComfortTemHum.sendSuc(
            true, isWrite, commandType, proType,
            intArrayOf(temLow, temHigh),
            intArrayOf(humLow, humHigh)
        )
        return true
    }

    override fun fail() {
        Event4ComfortTemHum.sendFail(isWrite, commandType, proType)
    }
}