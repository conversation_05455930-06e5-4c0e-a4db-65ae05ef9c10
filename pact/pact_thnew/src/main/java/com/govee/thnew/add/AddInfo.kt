package com.govee.thnew.add

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.config.WarnRange
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.util.JsonUtil

/**
 * <AUTHOR>
 * @date created on 2024/2/6
 * @description 温湿度计-->添加/绑定设备时的相关信息类
 */
@Keep
class AddInfo : Parcelable {
    var sku = ""
    var device = ""
    var goodsType = 0
    var deviceName = ""
    var bleName: String = ""

    @SerializedName(value = "address", alternate = ["bleAddress"])
    var address: String = ""

    @SerializedName(value = "bleSoftVersion", alternate = ["versionSoft"])
    var bleSoftVersion = ""

    @SerializedName(value = "bleHardVersion", alternate = ["versionHard"])
    var bleHardVersion = ""

    var wifiSoftVersion = ""
    var wifiHardVersion = ""
    var wifiMac = ""

    var pactType = 0
    var pactCode = 0

    var secretCode = ""

    var batteryWarning = true
    var uploadRate = 0
    var battery = 100
    var fahOpen = true

    //湿度报警相关
    var humMin = ThConsV1.HUM_MIN_VALUE * 100
    var humMax = ThConsV1.HUM_MAX_VALUE * 100
    var humWarning = false
    var delayPushTime = 0

    //温度报警相关
    var temMin = ThConsV1.TEM_MIN_VALUE * 100
    var temMax = ThConsV1.TEM_MAX_VALUE * 100
    var temWarning = false

    //校准值
    var humCali = 0
    var temCali = 0

    //音量档位
    var muteLevel: Int? = null

    //推送开关
    var normalPushOnOff: Boolean? = null

    //B5178相关(主+从设备)
    var groupOrder = 0//主、从设备序号
    var groupRole: Int? = null//主、从角色类型
    var pDevice: String? = null//主设备id(从设备中包含)

    //H5171相关
    var deviceWarning: Boolean? = null//设备声音告警开关
    var deviceWarningSeconds: Int? = null//设备声音告警时长
    var comfortTemMin: Int? = null //舒适度
    var comfortTemMax: Int? = null
    var comfortHumMin: Int? = null
    var comfortHumMax: Int? = null

    //H5112相关
    var temWarning2: Boolean? = null//探针2相关
    var temMin2: Int? = null
    var temMax2: Int? = null
    var delayPushTime2: Int? = null
    var temCali2: Int? = null
    var probeName1: String? = null//探针名称
    var pb1IconIndex: Int? = null//探针图标序号
    var probeName2: String? = null
    var pb2IconIndex: Int? = null

    //H5140相关
    var airQualityOnOff: Int? = null
    var co2LevelLower: Int? = null
    var co2LevelUpper: Int? = null
    var co2Min: Int? = null
    var co2Max: Int? = null
    var co2Warning: Boolean? = null

    fun getKey(): String {
        return "${sku}_${device}"
    }

    fun copy(): AddInfo {
        return JsonUtil.fromJson(JsonUtil.toJson(this), AddInfo::class.java)
    }

    fun comfortRange(): Pair<IntArray, IntArray> {
        val temRange = intArrayOf(comfortTemMin ?: -2000, comfortTemMax ?: 6000)
        val humRange = intArrayOf(comfortHumMin ?: 0, comfortHumMax ?: 10000)
        return Pair(temRange, humRange)
    }

    /**
     * 绑定时保存一份本地告警信息
     */
    val warnRange: WarnRange
        get() {
            val warnRange = WarnRange()
            warnRange.goodsType = goodsType
            warnRange.sku = sku
            warnRange.device = device
            warnRange.order = groupOrder
            warnRange.deviceName = deviceName
            warnRange.bleAddress = address

            warnRange.temMin = temMin
            warnRange.temMax = temMax
            warnRange.temWarning = temWarning
            warnRange.temCali = temCali

            warnRange.humMin = humMin
            warnRange.humMax = humMax
            warnRange.humWarning = humWarning
            warnRange.humCali = humCali
            return warnRange
        }

    /**
     * 绑定时保存一份本地告警信息
     */
    val warnRange4Pb2: WarnRange
        get() {
            val warnRange = WarnRange()
            warnRange.goodsType = goodsType
            warnRange.sku = sku
            warnRange.device = ThConsV1.getH5112Device4Pb2(device)
            warnRange.order = groupOrder
            warnRange.deviceName = deviceName
            warnRange.bleAddress = address

            warnRange.temMin = temMin2 ?: 0
            warnRange.temMax = temMax2 ?: 0
            warnRange.temWarning = temWarning2.isTrue()
            warnRange.temCali = temCali2 ?: 0

            warnRange.humMin = 0
            warnRange.humMax = 100
            warnRange.humWarning = false
            warnRange.humCali = 0
            return warnRange
        }

    constructor()

    constructor(goodsType: Int, sku: String, bleName: String, bleAddress: String) {
        this.sku = sku
        this.goodsType = goodsType
        this.address = bleAddress
        this.bleName = bleName
        humMin = ThConsV1.HUM_MIN_VALUE * 100
        humMax = ThConsV1.HUM_MAX_VALUE * 100
        temMin = ThConsV1.TEM_MIN_VALUE * 100
        temMax = ThConsV1.TEM_MAX_VALUE * 100
    }

    constructor(parcel: Parcel) : this() {
        sku = parcel.readString().toString()
        device = parcel.readString().toString()
        goodsType = parcel.readInt()
        deviceName = parcel.readString().toString()
        bleName = parcel.readString().toString()
        address = parcel.readString().toString()
        bleSoftVersion = parcel.readString().toString()
        bleHardVersion = parcel.readString().toString()
        wifiSoftVersion = parcel.readString().toString()
        wifiHardVersion = parcel.readString().toString()
        wifiMac = parcel.readString().toString()
        pactType = parcel.readInt()
        pactCode = parcel.readInt()
        secretCode = parcel.readString().toString()
        batteryWarning = parcel.readByte() != 0.toByte()
        uploadRate = parcel.readInt()
        battery = parcel.readInt()
        fahOpen = parcel.readByte() != 0.toByte()
        humMin = parcel.readInt()
        humMax = parcel.readInt()
        humWarning = parcel.readByte() != 0.toByte()
        delayPushTime = parcel.readInt()
        temMin = parcel.readInt()
        temMax = parcel.readInt()
        temWarning = parcel.readByte() != 0.toByte()
        humCali = parcel.readInt()
        temCali = parcel.readInt()
        muteLevel = parcel.readValue(Int::class.java.classLoader) as? Int
        normalPushOnOff = parcel.readValue(Boolean::class.java.classLoader) as? Boolean
        groupOrder = parcel.readInt()
        groupRole = parcel.readValue(Int::class.java.classLoader) as? Int
        pDevice = parcel.readString()
        deviceWarning = parcel.readValue(Boolean::class.java.classLoader) as? Boolean
        deviceWarningSeconds = parcel.readValue(Int::class.java.classLoader) as? Int
        comfortTemMin = parcel.readValue(Int::class.java.classLoader) as? Int
        comfortTemMax = parcel.readValue(Int::class.java.classLoader) as? Int
        comfortHumMin = parcel.readValue(Int::class.java.classLoader) as? Int
        comfortHumMax = parcel.readValue(Int::class.java.classLoader) as? Int
        temWarning2 = parcel.readValue(Boolean::class.java.classLoader) as? Boolean
        temMin2 = parcel.readValue(Int::class.java.classLoader) as? Int
        temMax2 = parcel.readValue(Int::class.java.classLoader) as? Int
        delayPushTime2 = parcel.readValue(Int::class.java.classLoader) as? Int
        temCali2 = parcel.readValue(Int::class.java.classLoader) as? Int
        probeName1 = parcel.readString()
        pb1IconIndex = parcel.readValue(Int::class.java.classLoader) as? Int
        probeName2 = parcel.readString()
        pb2IconIndex = parcel.readValue(Int::class.java.classLoader) as? Int
        airQualityOnOff = parcel.readValue(Int::class.java.classLoader) as? Int
        co2LevelLower = parcel.readValue(Int::class.java.classLoader) as? Int
        co2LevelUpper = parcel.readValue(Int::class.java.classLoader) as? Int
        co2Min = parcel.readValue(Int::class.java.classLoader) as? Int
        co2Max = parcel.readValue(Int::class.java.classLoader) as? Int
        co2Warning = parcel.readValue(Boolean::class.java.classLoader) as? Boolean
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(sku)
        parcel.writeString(device)
        parcel.writeInt(goodsType)
        parcel.writeString(deviceName)
        parcel.writeString(bleName)
        parcel.writeString(address)
        parcel.writeString(bleSoftVersion)
        parcel.writeString(bleHardVersion)
        parcel.writeString(wifiSoftVersion)
        parcel.writeString(wifiHardVersion)
        parcel.writeString(wifiMac)
        parcel.writeInt(pactType)
        parcel.writeInt(pactCode)
        parcel.writeString(secretCode)
        parcel.writeByte(if (batteryWarning) 1 else 0)
        parcel.writeInt(uploadRate)
        parcel.writeInt(battery)
        parcel.writeByte(if (fahOpen) 1 else 0)
        parcel.writeInt(humMin)
        parcel.writeInt(humMax)
        parcel.writeByte(if (humWarning) 1 else 0)
        parcel.writeInt(delayPushTime)
        parcel.writeInt(temMin)
        parcel.writeInt(temMax)
        parcel.writeByte(if (temWarning) 1 else 0)
        parcel.writeInt(humCali)
        parcel.writeInt(temCali)
        parcel.writeValue(muteLevel)
        parcel.writeValue(normalPushOnOff)
        parcel.writeInt(groupOrder)
        parcel.writeValue(groupRole)
        parcel.writeString(pDevice)
        parcel.writeValue(deviceWarning)
        parcel.writeValue(deviceWarningSeconds)
        parcel.writeValue(comfortTemMin)
        parcel.writeValue(comfortTemMax)
        parcel.writeValue(comfortHumMin)
        parcel.writeValue(comfortHumMax)
        parcel.writeValue(temWarning2)
        parcel.writeValue(temMin2)
        parcel.writeValue(temMax2)
        parcel.writeValue(delayPushTime2)
        parcel.writeValue(temCali2)
        parcel.writeString(probeName1)
        parcel.writeValue(pb1IconIndex)
        parcel.writeString(probeName2)
        parcel.writeValue(pb2IconIndex)
        parcel.writeValue(airQualityOnOff)
        parcel.writeValue(co2LevelLower)
        parcel.writeValue(co2LevelUpper)
        parcel.writeValue(co2Min)
        parcel.writeValue(co2Max)
        parcel.writeValue(co2Warning)
    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<AddInfo> {
        override fun createFromParcel(parcel: Parcel): AddInfo {
            return AddInfo(parcel)
        }

        override fun newArray(size: Int): Array<AddInfo?> {
            return arrayOfNulls(size)
        }
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (other !is AddInfo) return false

        if (goodsType != other.goodsType) return false
        if (pactType != other.pactType) return false
        if (pactCode != other.pactCode) return false
        if (batteryWarning != other.batteryWarning) return false
        if (uploadRate != other.uploadRate) return false
        if (battery != other.battery) return false
        if (fahOpen != other.fahOpen) return false
        if (humMin != other.humMin) return false
        if (humMax != other.humMax) return false
        if (humWarning != other.humWarning) return false
        if (delayPushTime != other.delayPushTime) return false
        if (temMin != other.temMin) return false
        if (temMax != other.temMax) return false
        if (temWarning != other.temWarning) return false
        if (humCali != other.humCali) return false
        if (temCali != other.temCali) return false
        if (muteLevel != other.muteLevel) return false
        if (normalPushOnOff != other.normalPushOnOff) return false
        if (groupOrder != other.groupOrder) return false
        if (groupRole != other.groupRole) return false
        if (deviceWarning != other.deviceWarning) return false
        if (deviceWarningSeconds != other.deviceWarningSeconds) return false
        if (comfortTemMin != other.comfortTemMin) return false
        if (comfortTemMax != other.comfortTemMax) return false
        if (comfortHumMin != other.comfortHumMin) return false
        if (comfortHumMax != other.comfortHumMax) return false
        if (temWarning2 != other.temWarning2) return false
        if (temMin2 != other.temMin2) return false
        if (temMax2 != other.temMax2) return false
        if (delayPushTime2 != other.delayPushTime2) return false
        if (temCali2 != other.temCali2) return false
        if (pb1IconIndex != other.pb1IconIndex) return false
        if (pb2IconIndex != other.pb2IconIndex) return false
        if (airQualityOnOff != other.airQualityOnOff) return false
        if (co2LevelLower != other.co2LevelLower) return false
        if (co2LevelUpper != other.co2LevelUpper) return false
        if (co2Min != other.co2Min) return false
        if (co2Max != other.co2Max) return false
        if (co2Warning != other.co2Warning) return false
        if (sku != other.sku) return false
        if (device != other.device) return false
        if (deviceName != other.deviceName) return false
        if (bleName != other.bleName) return false
        if (address != other.address) return false
        if (bleSoftVersion != other.bleSoftVersion) return false
        if (bleHardVersion != other.bleHardVersion) return false
        if (wifiSoftVersion != other.wifiSoftVersion) return false
        if (wifiHardVersion != other.wifiHardVersion) return false
        if (wifiMac != other.wifiMac) return false
        if (secretCode != other.secretCode) return false
        if (pDevice != other.pDevice) return false
        if (probeName1 != other.probeName1) return false
        if (probeName2 != other.probeName2) return false

        return true
    }

    override fun hashCode(): Int {
        var result = goodsType
        result = 31 * result + pactType
        result = 31 * result + pactCode
        result = 31 * result + batteryWarning.hashCode()
        result = 31 * result + uploadRate
        result = 31 * result + battery
        result = 31 * result + fahOpen.hashCode()
        result = 31 * result + humMin
        result = 31 * result + humMax
        result = 31 * result + humWarning.hashCode()
        result = 31 * result + delayPushTime
        result = 31 * result + temMin
        result = 31 * result + temMax
        result = 31 * result + temWarning.hashCode()
        result = 31 * result + humCali
        result = 31 * result + temCali
        result = 31 * result + (muteLevel ?: 0)
        result = 31 * result + (normalPushOnOff?.hashCode() ?: 0)
        result = 31 * result + groupOrder
        result = 31 * result + (groupRole ?: 0)
        result = 31 * result + (deviceWarning?.hashCode() ?: 0)
        result = 31 * result + (deviceWarningSeconds ?: 0)
        result = 31 * result + (comfortTemMin ?: 0)
        result = 31 * result + (comfortTemMax ?: 0)
        result = 31 * result + (comfortHumMin ?: 0)
        result = 31 * result + (comfortHumMax ?: 0)
        result = 31 * result + (temWarning2?.hashCode() ?: 0)
        result = 31 * result + (temMin2 ?: 0)
        result = 31 * result + (temMax2 ?: 0)
        result = 31 * result + (delayPushTime2 ?: 0)
        result = 31 * result + (temCali2 ?: 0)
        result = 31 * result + (pb1IconIndex ?: 0)
        result = 31 * result + (pb2IconIndex ?: 0)
        result = 31 * result + (airQualityOnOff ?: 0)
        result = 31 * result + (co2LevelLower ?: 0)
        result = 31 * result + (co2LevelUpper ?: 0)
        result = 31 * result + (co2Min ?: 0)
        result = 31 * result + (co2Max ?: 0)
        result = 31 * result + (co2Warning?.hashCode() ?: 0)
        result = 31 * result + sku.hashCode()
        result = 31 * result + device.hashCode()
        result = 31 * result + deviceName.hashCode()
        result = 31 * result + bleName.hashCode()
        result = 31 * result + address.hashCode()
        result = 31 * result + bleSoftVersion.hashCode()
        result = 31 * result + bleHardVersion.hashCode()
        result = 31 * result + wifiSoftVersion.hashCode()
        result = 31 * result + wifiHardVersion.hashCode()
        result = 31 * result + wifiMac.hashCode()
        result = 31 * result + secretCode.hashCode()
        result = 31 * result + (pDevice?.hashCode() ?: 0)
        result = 31 * result + (probeName1?.hashCode() ?: 0)
        result = 31 * result + (probeName2?.hashCode() ?: 0)
        return result
    }
}