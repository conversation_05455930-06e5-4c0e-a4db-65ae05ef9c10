package com.govee.thnew.ble

import com.govee.base2home.pact.GoodsType
import com.govee.base2light.ble.ota.v3.OtaOpV3
import com.govee.base2newth.AbsHeartComm
import com.govee.base2newth.AbsOnlyReadSingleController
import com.govee.base2newth.ThConsV1
import com.govee.thnew.ble.controller.Controller4HeartV0
import com.govee.thnew.ble.controller.Controller4HeartV1
import com.govee.thnew.config.ThConfig4Support
import java.util.UUID

/**
 * <AUTHOR>
 * @date created on 2024/2/18
 * @description 温湿度计蓝牙心跳通讯器
 */
class ThHeartComm : AbsHeartComm() {

    private var goodsType = GoodsType.GOODES_TYPE_NO_SUPPORT
    private var sku = ""

    companion object {
        private const val serviceUuidStr = "494e5445-4c4c-495f-524f-434b535f4857"

        private const val characteristicUuidV0Str = "494e5445-4c4c-495f-524f-434b535f2011"

        private const val characteristicUuidV1Str = "494e5445-4c4c-495f-524f-434b535f2012"

        private const val characteristicUuidV2Str = "494e5445-4c4c-495f-524f-434b535f2014"
    }

    private var controllerHeart: AbsOnlyReadSingleController? = null
    override fun heartCommOvertime(proType: Byte, proCommandType: Byte) {
        //心跳包超时无需处理
    }

    override fun getHeartBytes(): ByteArray {
        if (controllerHeart == null) {
            controllerHeart = Controller4HeartV1(sku)
        }
        return controllerHeart!!.value
    }

    /**
     * !!!=->须在连接ble前调用,为配置心跳用
     */
    fun setDeviceInfo(goodsType: Int, sku: String) {
        this.goodsType = goodsType
        this.sku = sku
    }

    private var heartConfig: Triple<String, String, ByteArray>? = null

    override fun config(): Triple<String, String, ByteArray>? {
        return when (ThConfig4Support.bleHeartType(goodsType, sku)) {
            ThConfig4Support.BLE_HEART_TYPE_V0 -> {
                if (heartConfig == null || controllerHeart == null || controllerHeart !is Controller4HeartV0) {
                    controllerHeart = Controller4HeartV0()
                    heartConfig = Triple(serviceUuidStr, characteristicUuidV0Str, controllerHeart!!.value)
                }
                heartConfig
            }

            ThConfig4Support.BLE_HEART_TYPE_V1 -> {
                if (heartConfig == null || controllerHeart == null || controllerHeart !is Controller4HeartV1) {
                    controllerHeart = Controller4HeartV1(sku)
                    heartConfig = Triple(serviceUuidStr, characteristicUuidV1Str, controllerHeart!!.value)
                }
                heartConfig
            }

            ThConfig4Support.BLE_HEART_TYPE_V2 -> {
                if (heartConfig == null || controllerHeart == null || controllerHeart !is Controller4HeartV1) {
                    controllerHeart = Controller4HeartV1(sku)
                    heartConfig = Triple(serviceUuidStr, characteristicUuidV2Str, controllerHeart!!.value)
                }
                heartConfig
            }

            else -> {
                null
            }
        }
    }

    override fun isCanSendHeart(): Boolean {
        //ota时不处理心跳
        val inOta = ThBle.getInstance.inOta() || OtaOpV3.getInstance().inOta()
        return !inOta
    }

    override fun serviceUUID(): UUID {
        return UUID.fromString(serviceUuidStr)
    }

    override fun characteristicUUID(): UUID {
        return UUID.fromString(characteristicUuidV1Str)
    }

    override fun isSelfComm(serviceUuid: String, characteristicUuid: String, values: ByteArray): Boolean {
        if (serviceUuidStr != serviceUuid) {
            return false
        }
        if (values.size < 2) {
            return false
        }
        when (ThConfig4Support.bleHeartType(goodsType, sku)) {
            ThConfig4Support.BLE_HEART_TYPE_V0 -> {
                if (characteristicUuid != characteristicUuidV0Str) {
                    return false
                }
            }

            ThConfig4Support.BLE_HEART_TYPE_V1 -> {
                if (characteristicUuid != characteristicUuidV1Str) {
                    return false
                }
            }

            ThConfig4Support.BLE_HEART_TYPE_V2 -> {
                if (characteristicUuid != characteristicUuidV2Str) {
                    return false
                }
            }

            else -> {
                return false
            }
        }
        return if (controllerHeart != null) {
            values[0] == controllerHeart!!.value[0] && values[1] == controllerHeart!!.value[1]
        } else {
            false
        }
    }

    override fun parse(serviceUuid: String, characteristicUuid: String, values: ByteArray): Boolean {
        //心跳包解析
        if (controllerHeart != null) {
            controllerHeart!!.onResult(true, values)
        }

        return true
    }

    fun clearControllers() {
        heartComm.commOver()
        handler.removeCallbacksAndMessages(null)
        goodsType = GoodsType.GOODES_TYPE_NO_SUPPORT
        sku = ""
    }

    override fun parsePriority(): Int {
        return ThConsV1.comm_parse_priority_heart_comm
    }
}