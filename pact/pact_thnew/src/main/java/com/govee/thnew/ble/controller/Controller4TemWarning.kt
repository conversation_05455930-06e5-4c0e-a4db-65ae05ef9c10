package com.govee.thnew.ble.controller

import com.govee.base2home.Constant4L5
import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.thnew.ble.AbsControllerWithCallback
import com.govee.thnew.ble.event.Event4TemWarning

/**
 * <AUTHOR>
 * @date created on 2024/2/19
 * @description 温湿度计-->读写温度告警信息的Controller
 */
class Controller4TemWarning : AbsControllerWithCallback {
    /**
     * 温度告警下限值*100
     */
    private var minTem = 0

    /**
     * 温度告警上限值*100
     */
    private var maxTem = 0
    private var openWarning = false

    /**
     * 延迟告警分钟(目前只有H5108有，其他默认为0)
     */
    private var delayPushTime = 0

    /**
     * 是否支持单值告警
     */
    private var supportSingleAlarm = false

    /**
     * 读操作
     */
    constructor() : super()

    /**
     * 写操作
     *
     * @param openWarning
     * @param minTem
     * @param maxTem
     */
    constructor(
        openWarning: Boolean,
        minTem: Int,
        maxTem: Int,
        delayPushTime: Int = 0,
        supportSingleAlarm: Boolean = false,
        writeCallBack: ((result: Boolean) -> Unit)
    ) : super(writeCallBack) {
        this.openWarning = openWarning
        this.supportSingleAlarm = supportSingleAlarm
        this.minTem = checkTem(minTem)
        this.maxTem = checkTem(maxTem)
        this.delayPushTime = delayPushTime
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_tem_warning
    }

    private fun checkTem(tem: Int): Int {
        var lastTem = tem
        val tem4Min = if (supportSingleAlarm) Constant4L5.SINGLE_LIMIT_LOW * 100 else ThConsV1.TEM_MIN_VALUE * 100
        val tem4Max = if (supportSingleAlarm) Constant4L5.SINGLE_LIMIT_HIGH * 100 else ThConsV1.TEM_MAX_VALUE * 100
        lastTem = lastTem.coerceAtLeast(tem4Min)
        lastTem = lastTem.coerceAtMost(tem4Max)
        return lastTem
    }

    override fun translateWrite(): ByteArray {
        val openWarningValue = if (openWarning) 1.toByte() else 0.toByte()
        val minHumValues = BleUtil.getSignedBytesFor2(minTem, false)
        val maxHumValues = BleUtil.getSignedBytesFor2(maxTem, false)
        return byteArrayOf(openWarningValue, minHumValues[0], minHumValues[1], maxHumValues[0], maxHumValues[1], delayPushTime.toByte())
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val openWarning = validBytes[0].toInt() == 1
        val minTem = BleUtil.convertTwoBytesToShort(validBytes[1], validBytes[2])
        val maxTem = BleUtil.convertTwoBytesToShort(validBytes[3], validBytes[4])
        val delayPushTime = BleUtil.getUnsignedByte(validBytes[5])
        Event4TemWarning.sendSuc(isWrite, commandType, proType, openWarning, minTem, maxTem, delayPushTime)
        return true
    }

    override fun fail() {
        super.fail()
        Event4TemWarning.sendFail(isWrite, commandType, proType)
    }
}