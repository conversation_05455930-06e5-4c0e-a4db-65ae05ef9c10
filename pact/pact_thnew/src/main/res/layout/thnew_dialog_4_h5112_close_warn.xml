<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/component_bg_style_2">

            <TextView
                android:id="@+id/tv_title_text_4_close_warn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="32.5dp"
                android:layout_marginTop="38dp"
                android:gravity="center"
                android:text="@string/dbgw_remind_text"
                android:textColor="@color/font_style_4_1_textColor"
                android:textSize="18sp"
                app:layout_constraintTop_toTopOf="parent"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tv_warn_content_text_4_close_warn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="32.5dp"
                android:layout_marginTop="15dp"
                android:gravity="center"
                android:textColor="@color/font_style_12_textColor"
                android:textSize="14sp"
                app:layout_constraintTop_toBottomOf="@+id/tv_title_text_4_close_warn"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tv_warn_remind_text_4_close_warn"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="32.5dp"
                android:layout_marginTop="12dp"
                android:text="@string/h5112_text_4_close_warn_remind"
                android:textColor="@color/font_style_11_2_textColor"
                android:textSize="12sp"
                app:layout_constraintTop_toBottomOf="@+id/tv_warn_content_text_4_close_warn"
                tools:ignore="HardcodedText" />

            <TextView
                android:id="@+id/tv_not_unbind_btn_4_close_warn"
                android:layout_width="148dp"
                android:layout_height="55dp"
                android:layout_marginStart="23.5dp"
                android:layout_marginTop="30dp"
                android:layout_marginBottom="10dp"
                android:background="@drawable/component_btn_style_5_1"
                android:gravity="center"
                android:paddingBottom="10dp"
                android:text="@string/h5112_text_4_pause_not_unbind"
                android:textColor="@color/ui_btn_style_5_1_text_color"
                android:textSize="@dimen/ui_btn_style_5_1_text_size"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tv_warn_remind_text_4_close_warn" />

            <TextView
                android:id="@+id/tv_sure_4_close_warn"
                android:layout_width="148dp"
                android:layout_height="55dp"
                android:layout_marginEnd="23.5dp"
                android:background="@drawable/component_btn_style_3_1"
                android:gravity="center"
                android:paddingBottom="10dp"
                android:text="@string/confirm"
                android:textColor="@color/ui_btn_style_3_1_text_color"
                android:textSize="@dimen/ui_btn_style_3_1_text_size"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@+id/tv_not_unbind_btn_4_close_warn" />

            <androidx.legacy.widget.Space
                android:layout_width="match_parent"
                android:layout_height="10dp"
                app:layout_constraintTop_toBottomOf="@+id/tv_not_unbind_btn_4_close_warn" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </RelativeLayout>
</layout>
