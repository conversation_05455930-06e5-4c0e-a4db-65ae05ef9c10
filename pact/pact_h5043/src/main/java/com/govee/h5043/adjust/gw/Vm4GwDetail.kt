package com.govee.h5043.adjust.gw

import android.bluetooth.BluetoothDevice
import androidx.annotation.WorkerThread
import androidx.lifecycle.MutableLiveData
import com.govee.base2home.Constant4L5
import com.govee.base2home.iot.Cmd
import com.govee.base2home.iot.ResultPt
import com.govee.base2home.iot.protype.v2.IotMsgV2
import com.govee.base2home.main.AbsDevice
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.main.gw.GwInfo
import com.govee.base2home.pact.Protocol
import com.govee.base2home.reform4dbgw.view.Event4ChangeGwForSub
import com.govee.base2kt.utils.BleUtils
import com.govee.base2light.ac.AbsIotManagerV1
import com.govee.base2light.ble.AbsBle
import com.govee.base2light.ble.controller.BleProtocolConstants
import com.govee.base2light.ble.controller.IControllerNoEvent
import com.govee.base2light.kt.ble.Compose4DefWrite
import com.govee.base2light.kt.ble.Compose4DefWrite4Multi
import com.govee.base2light.kt.comm.AbsViewMode4Op
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.base2newth.deviceitem.Ext
import com.govee.base2newth.net.CommonSettings
import com.govee.base2newth.net.smw.ISmwNet
import com.govee.base2newth.net.smw.Req4SetSettingsNew
import com.govee.base2newth.other.Event4DeleteThFromGw
import com.govee.bind.SafeBindMgr
import com.govee.bind.bean.ConfirmGidReq.Companion.createConfirmGidReq
import com.govee.bind.engine.ReadDeviceGid4Ble
import com.govee.ble.BleController
import com.govee.ble.event.EventBleBroadcastListenerTrigger
import com.govee.ble.event.EventBleConnect
import com.govee.ble.event.ScanEvent
import com.govee.h5043.H5043Cons
import com.govee.h5043.add.DeviceInfo
import com.govee.h5043.adjust.Event4AddSubDeviceSuc4H5107
import com.govee.h5043.adjust.Event4AddSubDeviceSuc4Leak
import com.govee.h5043.adjust.Event4ChangeBuzzerGear
import com.govee.h5043.adjust.Event4ChangeGwLightSwitch
import com.govee.h5043.adjust.Event4GwConnectCheck
import com.govee.h5043.adjust.Event4GwOnline
import com.govee.h5043.adjust.Event4MuteLevelChange
import com.govee.h5043.adjust.Event4ResultBuzzerGearSwitch
import com.govee.h5043.adjust.Event4ResultGwLightSwitch
import com.govee.h5043.adjust.Event4SubDeviceDeleted
import com.govee.h5043.adjust.Event4SubDeviceNameChange
import com.govee.h5043.adjust.Event4SyncSubDevices
import com.govee.h5043.adjust.Warning
import com.govee.h5043.adjust.WarningOp4Leak
import com.govee.h5043.adjust.WarningOp4LowBat
import com.govee.h5043.adjust.h5059.Vm4H5059Detail.Companion.SET_FOR_RESULT_OVER_TIME
import com.govee.h5043.ble.Ble
import com.govee.h5043.ble.Event4BleConnectResult
import com.govee.h5043.ble.Event4H5058StatusUpdate
import com.govee.h5043.ble.Event4H5059DevSetting
import com.govee.h5043.ble.Event4H5059InfoUpdate
import com.govee.h5043.ble.Event4IdentitySubDevice
import com.govee.h5043.ble.Event4RetryConnectBle
import com.govee.h5043.ble.Event4SubOpResult
import com.govee.h5043.ble.Parser
import com.govee.h5043.ble.controller.Compose4ReadGatewayInfo
import com.govee.h5043.ble.controller.Controller
import com.govee.h5043.ble.controller.Controller4BuzzerGear
import com.govee.h5043.ble.controller.Controller4CheckSignal4H5059
import com.govee.h5043.ble.controller.Controller4DeleteSubDevice
import com.govee.h5043.ble.controller.Controller4FindDevice4H5059
import com.govee.h5043.ble.controller.Controller4LightSwitch
import com.govee.h5043.ble.controller.Controller4LowBatSwitch4H5059
import com.govee.h5043.ble.controller.Controller4SetVolume4H5059
import com.govee.h5043.ble.controller.Controller4SyncSubDevice
import com.govee.h5043.model.Ext4GwInSubDevice
import com.govee.h5043.model.Info4ThWithCali
import com.govee.h5043.model.List4SubDevice
import com.govee.h5043.model.SubDevice
import com.govee.h5043.model.SubDeviceStatus
import com.govee.h5043.net.netService
import com.govee.h5043.pact.H5059ParseUtils
import com.govee.h5043.pact.Support
import com.govee.h5151.ble.controller.Controller4H5044BindH5112
import com.govee.h5151.ble.controller.Controller4SubDeviceThRange4Common
import com.govee.h5151.ble.controller.Event4H5112BindResult
import com.govee.h5151.ble.controller.Ext4SubH5112
import com.govee.h5151.ble.controller.Request4BindH5112
import com.govee.h5151.ble.controller.SubDevice4H5112
import com.govee.h5151.gw.Event4AddSubToAccount
import com.govee.h5151.gw.Event4BindSubDevice
import com.govee.h5151.gw.Event4BindSubDeviceResult
import com.govee.h5151.gw.Event4SubDeviceChange
import com.govee.kt.net.Request4ChangeBattery
import com.govee.kt.setting.event.Event4DeviceNameChange
import com.govee.kt.setting.event.Event4VersionChange
import com.govee.kt.ui.device.Event4GwSubBleStatusUpdate
import com.govee.kt.ui.device.Event4RefreshSubList
import com.govee.kt.ui.device.GatewaysOp
import com.govee.kt.ui.device.GwH5151Op
import com.govee.mvvm.ext.request
import com.govee.mvvm.globalLaunch
import com.govee.ui.R
import com.ihoment.base2app.Cache
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.CaughtRunnable
import com.ihoment.base2app.util.JsonUtil
import kotlinx.coroutines.Dispatchers
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Create by xieyingwu on 2023/3/21
 * 详情页的ViewModel
 */
class Vm4GwDetail : AbsViewMode4Op() {
    companion object {
        const val ui_type_fail = 0
        const val ui_type_loading = 1
        const val ui_type_suc = 2
        const val ui_type_suc_empty = 3

        const val what_sync_sub_devices = 10000

        const val delay_time_4_sync_sub_devices = 1000L
        const val IOT_HEART_INTERVAL_TIME = 60 * 1000L
    }

    override val ble: AbsBle
        get() = Ble

    /*ui类型*/
    val uiType: MutableLiveData<Int?> = MutableLiveData()

    /*子设备列表*/
    val ld4SubDevices: MutableLiveData<MutableList<List4SubDevice>?> = MutableLiveData()

    /*子设备识别*/
    val subDeviceIdentity: MutableLiveData<String?> = MutableLiveData()
    val deviceName: MutableLiveData<String?> = MutableLiveData()

    /*ble的连接状态*/
    val bleConnectResult: MutableLiveData<Int?> = MutableLiveData()

    /*是否同步过当前子设备列表信息--用于更新网关实际有效子设备信息*/
    private var subDeviceSnoList: MutableList<Int>? = null
    private var firstSyncSubDevices: Boolean = false

    var deviceInfo: DeviceInfo? = null
        private set
    var topic: String? = ""
    var gwLightSwitchOn: Boolean = false
        private set
    var gwBuzzerGear: Int? = null
        private set

    private var needPauseBleConnect = false

    fun init(deviceInfo: DeviceInfo, topic: String?) {
        SafeLog.i(TAG) { "Vm4GwDetail--init-->${JsonUtil.toJson(deviceInfo)}" }
        this.deviceInfo = deviceInfo
        this.topic = topic
        deviceName.postValue(deviceInfo.deviceName)
        ble(true)
        registerEventBus(true)
    }

    /**
     * 各类型子设备的数量
     */
    var leakNum = 0
    var thNum = 0
    var h5151SubNum = 0

    /**
     * 查询子设备列表信息
     */
    fun querySubDevices(
        firstRequest: Boolean = false, callback: ((suc: Boolean) -> Unit)? = null,
    ) {
        val sku = deviceInfo?.sku ?: ""
        val device = deviceInfo?.device ?: ""
        SafeLog.i(TAG) { "querySubDevices() sku = $sku ; firstRequest = $firstRequest" }
        if (sku.isEmpty() || device.isEmpty()) return
        if (firstRequest) {
            uiType.postValue(ui_type_loading)
        }
        request({
            netService.subDevices(sku, device)
        }, success = {
            val empty = it.isEmpty()
            SafeLog.i(TAG) { "querySubDevices() empty = $empty" }
            val leakList = mutableListOf<List4SubDevice>()
            val h5107List = mutableListOf<List4SubDevice>()
            val h5112List = mutableListOf<List4SubDevice>()
            val h5151SubList = mutableListOf<List4SubDevice>()
            val h5151SubList4Abs = mutableListOf<AbsDevice>()
            val subDeviceSnoList = mutableListOf<Int>()
            for (info in it) {
                List4SubDevice.toSubDevice(info).let { list4SubDevice ->
                    //子设备为漏水器(H5058/H5059)
                    list4SubDevice.subDevice4Leak?.let { newSubExt ->
                        ld4SubDevices.value?.let { oldListInfo ->
                            for (oldSub in oldListInfo) {
                                if (newSubExt.key() == oldSub.key()) {
                                    newSubExt.settings?.online = oldSub.subDevice4Leak?.settings?.online
                                    break
                                }
                            }
                        }
                        leakList.add(list4SubDevice)
                        list4SubDevice.sno()?.let { sno ->
                            subDeviceSnoList.add(sno)
                        }
                    }
                    //子设备为H5107
                    list4SubDevice.subDevice4H5107?.let { newSubExt ->
                        ld4SubDevices.value?.let { oldListInfo ->
                            for (oldSub in oldListInfo) {
                                if (newSubExt.key() == oldSub.key()) {
                                    newSubExt.settings?.online = oldSub.subDevice4H5107?.settings?.online.isTrue()
                                    break
                                }
                            }
                        }
                        h5107List.add(list4SubDevice)
                        list4SubDevice.sno()?.let { sno ->
                            subDeviceSnoList.add(sno)
                        }
                    }
                    //子设备为H5112
                    list4SubDevice.subDevice4H5112?.let { newSubExt ->
                        ld4SubDevices.value?.let { oldListInfo ->
                            for (oldSub in oldListInfo) {
                                if (newSubExt.key() == oldSub.key()) {
                                    newSubExt.settings?.online = oldSub.subDevice4H5112?.settings?.online
                                    break
                                }
                            }
                        }
                        h5112List.add(list4SubDevice)
                        list4SubDevice.sno()?.let { sno ->
                            subDeviceSnoList.add(sno)
                        }
                        //H5112也需要同步蓝牙状态
                        h5151SubList4Abs.add(info)
                    }
                    //子设备为类H5151网关的子设备
                    list4SubDevice.subDevice4BleTh?.let {
                        h5151SubList.add(list4SubDevice)
                        //类H5151网关子设备(用于同步网关列表页的蓝牙/wifi状态)
                        h5151SubList4Abs.add(info)
                    }
                }
            }
            ld4SubDevices.postValue(arrayListOf<List4SubDevice>().apply {
                addAll(leakList)
                addAll(h5107List)
                addAll(h5112List)
                addAll(h5151SubList)
            })
            val uiTypeValue = if (empty) ui_type_suc_empty else ui_type_suc
            uiType.postValue(uiTypeValue)
            callback?.invoke(true)
            //只有首次进网关列表的时候才去同步数据
            if (firstRequest) {
                syncGwSubDevices(subDeviceSnoList)
            }
            deviceInfo?.run {
                //有H5151网关的子设备，还需检测蓝牙、wifi的在线状态
                GwH5151Op.syncLinkModels(key(), h5151SubList4Abs).run {
                    if (this[0]) {
                        handler.post(scanH5151SubRunnable)
                    }
                }
                //各类型子设备数量
                leakNum = leakList.size
                when (sku) {
                    H5043Cons.H5043 -> {
                        thNum = h5107List.size
                    }

                    H5043Cons.H5044 -> {
                        thNum = h5112List.size
                    }

                    else -> {}
                }
                h5151SubNum = h5151SubList.size
                //刷新信号值
                val hasDirectSub = (leakList.size + h5107List.size + h5112List.size) > 0
                if (hasDirectSub) {
                    handler.postDelayed(iotHeartRunnable, 0)
                }
            }
        }, error = {
            if (firstRequest) {
                uiType.postValue(ui_type_fail)
            }
            callback?.invoke(false)
        })
    }

    override fun tryRequestTopic() {
        super.tryRequestTopic()
        deviceInfo?.let {
            GatewaysOp.queryGatewayModel(it.key())?.device?.deviceExt?.deviceSettings?.let { settingsStr ->
                JsonUtil.fromJson(settingsStr, GwInfo.Ext4Other::class.java)?.let { ext4Other ->
                    topic = ext4Other.topic
                }
            }
        }
    }

    private val iotHeartRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                sendCmd4Status()
                handler.removeCallbacks(this)
                handler.postDelayed(this, IOT_HEART_INTERVAL_TIME)
            }
        }
    }

    private fun syncGwSubDevices(subDeviceSnoList: MutableList<Int>) {
        this.subDeviceSnoList = subDeviceSnoList
        SafeLog.i(TAG) { "syncGwSubDevices() firstSyncSubDevices = $firstSyncSubDevices" }
        if (firstSyncSubDevices) return
        SafeLog.i(TAG) { "syncGwSubDevices() subDeviceSnoList.size = ${subDeviceSnoList.size}" }
        doHandler(what_sync_sub_devices, delay_time_4_sync_sub_devices)
    }

    override fun connectBleSuc() {
        SafeLog.i(TAG) { "connectSuc() " }
        deviceInfo?.run {
            afterBleConnectSuc4ReadInfo(Compose4ReadGatewayInfo(sku, resultSuc = {
                changeBleStatus(connect_status_suc)
                changeLightSwitch(it.lightSwitch)
                it.buzzerGear?.let { gear ->
                    changeBuzzerGear(gear)
                }
                if (updateVersions(it.versionSoft, it.versionHard)) {
                    SafeLog.i(TAG) { "afterBleConnectSuc() 版本信息更新" }
                    Event4VersionChange.sendEvent(
                        this.versionSoft, this.versionHard, this.sku, this.device
                    )
                }
                //详情页检测gid流程
                when (sku) {
                    H5043Cons.H5044 -> {
                        DeviceListConfig.read().getDeviceByKey(sku, device)?.let { absDevice ->
                            SafeBindMgr.checkDetailPageGid(
                                ReadDeviceGid4Ble(ble),
                                createConfirmGidReq(absDevice, wifiHardVersion, wifiSoftVersion),
                            )
                        }
                    }

                    else -> {}
                }
            }, resultFail = {
                changeBleStatus(connect_status_fail)
            }))
        }
    }

    override fun bleAddress(): String? {
        return deviceInfo?.bleAddress
    }

    override fun isCurDevice(device: BluetoothDevice, scanRecord: ByteArray?): Boolean {
        return deviceInfo?.bleAddress == device.address
    }

    override fun topic(): String? = topic

    //H5107--子设备信息相关:key->sno,value->是否在线
    val h5107OnlineInfos = hashMapOf<Int, Boolean>()

    //网关离线
    val mld4GwOffline = MutableLiveData<Boolean>()

    //iot心跳超时检测
    private val iotOverTimeRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                h5107OnlineInfos.clear()
                mld4GwOffline.postValue(true)
                //网关iot离线
                GwH5151Op.gwOffline(deviceInfo?.key() ?: "")
            }
        }
    }

    override fun parseIotMsg4Online(msgV2: IotMsgV2, jsonStr: String?) {
        if (jsonStr.isNullOrEmpty()) {
            return
        }
        deviceInfo?.run {
            if (this.updateVersions(msgV2.softVersion, null)) {
                Event4VersionChange.sendEvent(
                    this.versionSoft, this.versionHard, this.sku, this.device
                )
            }
        }
        val opJsonStr = AbsIotManagerV1.getJsonObjectStr(jsonStr, Cmd.parse_json_op) ?: return
        val resultPt = JsonUtil.fromJson(opJsonStr, ResultPt::class.java) ?: return
        val bytes = resultPt.bytes()
        if (bytes.isEmpty()) return
        //子设备电量(H5058,H5059,H5112)、（H5059,H5112:信号强度，是否在线）相关
        val subDevBatInfos = hashMapOf<Int, Triple<Int, Int, Boolean>>()
        //子设备信息相关(H5058,H5059)
        val leakStatusInfos = mutableListOf<SubDeviceStatus>()
        for (byteArray in bytes) {
            if (byteArray[0] == BleProtocolConstants.SINGLE_READ || byteArray[0] == BleProtocolConstants.NOTIFY) {
                when (byteArray[1]) {
                    //网关指示灯开关相关
                    Controller.comm_light_switch -> {
                        val isOpen = byteArray[2] == 0x01.toByte()
                        changeLightSwitch(isOpen)
                    }
                    //网关蜂鸣器音量设置
                    Controller.comm_4_buzzer_gear -> {
                        val buzzerGear = byteArray[2].toInt()
                        changeBuzzerGear(buzzerGear)
                    }
                    //子设备信息
                    Controller.comm_sub_device_all_info -> {
                        //子设备类型
                        when (BleUtils.getUnsignedByte(byteArray[3])) {
                            //子设备为H5058
                            H5043Cons.sub_device_type_h5058 -> {
                                //电量相关信息
                                val batteryResult = Parser.parseBattery4Sno(byteArray)
                                if (batteryResult[0] == 1) {
                                    val battery = batteryResult[2]
                                    if (battery >= 0) {
                                        subDevBatInfos[batteryResult[1]] = Triple(battery, 0, true)
                                    }
                                }
                                //告警等信息
                                SubDeviceStatus.parse4IotMsg(byteArray)?.run {
                                    leakStatusInfos.add(this)
                                }
                            }
                            //子设备为H5059
                            H5043Cons.sub_device_type_h5059 -> {
//                                SafeLog.i(TAG) { "Vm4GwDetail--parseIotMsg4Online-->H5059: hexStr->${BleUtil.bytesToHexString(byteArray)}" }
                                val validBytes = ByteArray(byteArray.size - 3)
                                System.arraycopy(byteArray, 2, validBytes, 0, validBytes.size)
                                H5059ParseUtils.parseSubDevice(validBytes).let {
                                    //电量相关信息
                                    val batteryResult = intArrayOf(1, it.first.sno, it.first.battery)
                                    if (batteryResult[0] == 1) {
                                        val battery = batteryResult[2]
                                        if (battery >= 0) {
                                            subDevBatInfos[batteryResult[1]] = Triple(battery, it.second, it.first.online)
                                        }
                                    }
                                    //告警等信息
                                    it.first.let { subInfo ->
                                        leakStatusInfos.add(SubDeviceStatus(subInfo.sno, subInfo.deviceType, subInfo.leakTimeSeconds, subInfo.leakStatus, subInfo.muteLevel, subInfo.isLowBattery, subInfo.lowBatOpen))
                                    }
                                }
                            }
                            //子设备为H5112
                            H5043Cons.sub_device_type_h5112 -> {
//                                SafeLog.i(TAG) { "Vm4GwDetail--parseIotMsg4Online-->H5112: hexStr->${BleUtil.bytesToHexString(byteArray)}" }
                                val validBytes = ByteArray(byteArray.size - 3)
                                System.arraycopy(byteArray, 2, validBytes, 0, validBytes.size)
                                Ext4SubH5112.parseSubDevice(validBytes).let {
                                    subDevBatInfos[it.first] = it.second
                                }
                            }
                            //子设备为H5107
                            H5043Cons.sub_device_type_h5107 -> {
                                val validBytes = ByteArray(17)
                                System.arraycopy(byteArray, 2, validBytes, 0, validBytes.size)
                                Info4ThWithCali.parse(validBytes)?.let {
                                    h5107OnlineInfos[it.sno] = it.online
                                }
                            }

                            else -> {}
                        }
                    }

                    else -> {}
                }
            }
        }
        //对比子设备信息(H5058,H5059,H5112)
        compareSubInfo(subDevBatInfos)
        //漏水检测(H5058,H5059)的告警相关
        parseWarning(leakStatusInfos)
        //设置iot断连
        handler.removeCallbacks(iotOverTimeRunnable)
        handler.postDelayed(iotOverTimeRunnable, 70 * 1000)
    }

    override fun otherReceiveCmd(msgV2: IotMsgV2, jsonStr: String) {
        super.otherReceiveCmd(msgV2, jsonStr)
        parseSubDeviceCheck(jsonStr)
        when (msgV2.sku) {
            H5043Cons.H5044 -> {
                deviceInfo?.run {
                    if (this.updateVersions(msgV2.softVersion, null)) {
                        Event4VersionChange.sendEvent(
                            this.versionSoft, this.versionHard, this.sku, this.device
                        )
                    }
                }
                val opJsonStr = AbsIotManagerV1.getJsonObjectStr(jsonStr, Cmd.parse_json_op) ?: return
                val resultPt = JsonUtil.fromJson(opJsonStr, ResultPt::class.java) ?: return
                val bytes = resultPt.bytes()
                if (bytes.isEmpty()) return
                //子设备电量、信号强度，是否在线相关(H5059,H5112)
                val subDevBatInfos = hashMapOf<Int, Triple<Int, Int, Boolean>>()
                //子设备信息相关(H5059)
                val h5059StatusInfos = mutableListOf<SubDeviceStatus>()
                for (byteArray in bytes) {
                    if (byteArray[0] == BleProtocolConstants.NOTIFY) {
                        when (byteArray[1]) {
                            //子设备信息
                            Controller.comm_notify_identify_sub_device,
                            Controller.comm_notify_sub_device_status_update -> {
                                //子设备类型
                                when (BleUtils.getUnsignedByte(byteArray[3])) {
                                    //子设备为H5059
                                    H5043Cons.sub_device_type_h5059 -> {
//                                        SafeLog.i(TAG) { "ViewMode4Adjust--otherReceiveCmd-->H5059: hexStr->${BleUtil.bytesToHexString(byteArray)}" }
                                        val validBytes = ByteArray(byteArray.size - 3)
                                        System.arraycopy(byteArray, 2, validBytes, 0, validBytes.size)
                                        H5059ParseUtils.parseSubDevice(validBytes).let {
                                            //电量相关信息
                                            val batteryResult = intArrayOf(1, it.first.sno, it.first.battery)
                                            if (batteryResult[0] == 1) {
                                                val battery = batteryResult[2]
                                                if (battery >= 0) {
                                                    subDevBatInfos[batteryResult[1]] = Triple(battery, it.second, it.first.online)
                                                }
                                            }
                                            //告警等信息
                                            it.first.let { subInfo ->
                                                h5059StatusInfos.add(SubDeviceStatus(subInfo.sno, subInfo.deviceType, subInfo.leakTimeSeconds, subInfo.leakStatus, subInfo.muteLevel, subInfo.isLowBattery, subInfo.lowBatOpen))
                                            }
                                        }
                                    }
                                    //子设备为H5112
                                    H5043Cons.sub_device_type_h5112 -> {
//                                        SafeLog.i(TAG) { "Vm4GwDetail--otherReceiveCmd-->H5112: hexStr->${BleUtil.bytesToHexString(byteArray)}" }
                                        val validBytes = ByteArray(byteArray.size - 3)
                                        System.arraycopy(byteArray, 2, validBytes, 0, validBytes.size)
                                        Ext4SubH5112.parseSubDevice(validBytes).let {
                                            subDevBatInfos[it.first] = it.second
                                        }
                                    }

                                    else -> {}
                                }
                            }

                            BleProtocol.COMMAND_TYPE_4_BIND_SUB_NOTIFY -> {
                                //H5044绑定H5112的结果上报
                                val validBytes = ByteArray(byteArray.size - 3)
                                System.arraycopy(byteArray, 2, validBytes, 0, validBytes.size)
                                Event4H5112BindResult.sendEvent(validBytes)
                            }

                            else -> {}
                        }
                    }
                }
                //对比子设备信息(H5059，H5112)
                compareSubInfo(subDevBatInfos)
                //漏水检测(H5059)的告警相关
                parseWarning(h5059StatusInfos)
            }

            H5043Cons.H5043 -> {
                parseWarning(jsonStr)
            }

            else -> {}
        }
        //设置iot断连
        handler.removeCallbacks(iotOverTimeRunnable)
        handler.postDelayed(iotOverTimeRunnable, 70 * 1000)
    }

    private fun parseSubDeviceCheck(jsonStr: String) {
        val parseSubDeviceIdentity = Parser.parseSubDeviceIdentity(jsonStr)
        SafeLog.i(TAG) { "parseSubDeviceCheck() parseSubDeviceIdentity = $parseSubDeviceIdentity" }
    }

    private fun parseWarning(jsonStr: String) {
        deviceInfo?.run {
            val warnings = Parser.parseIotMsg4Warning(sku, jsonStr)
            if (warnings.isNullOrEmpty()) return
            var listChange = false
            for (subDeviceStatus in warnings) {
                findSubDeviceBySno(subDeviceStatus.sno)?.let { subDevice ->
                    val key = subDevice.key()
                    val syncMuteLevel = subDevice.syncMuteLevel(subDeviceStatus.muteLevel)
                    val noLeakWarning = subDeviceStatus.noLeakWarning()
                    SafeLog.i(TAG) { "parseWarning() syncMuteLevel = $syncMuteLevel ; noWarning = $noLeakWarning ; key = $key" }
                    //漏水报警
                    if (noLeakWarning) {
                        WarningOp4Leak.noLeakWarning(key)
                    } else {
                        val ext = Ext4GwInSubDevice.toExt4GwInSubDevice(this, topic)
                        WarningOp4Leak.showWarningDialog(
                            Warning(
                                subDevice,
                                ext,
                                subDeviceStatus.timeSeconds,
                                Warning.from_adjust_detail,
                                false
                            )
                        )
                    }
                    //低电量报警
                    when (subDevice.sku) {
                        H5043Cons.H5059 -> {
                            if (!subDeviceStatus.isLowBattery) {
                                WarningOp4LowBat.noLowBatWarning(key)
                            } else {
                                val ext = Ext4GwInSubDevice.toExt4GwInSubDevice(this, topic)
                                WarningOp4LowBat.showWarningDialog(
                                    Warning(
                                        subDevice,
                                        ext,
                                        subDeviceStatus.timeSeconds,
                                        Warning.from_adjust_detail,
                                        false,
                                        Warning.WARN_TYPE_4_LOW_BAT
                                    )
                                )
                            }
                        }

                        else -> {}
                    }
                    //同步音量档位
                    if (syncMuteLevel) {
                        listChange = true
                        subDevice.settings?.run {
                            Support.reportMuteLevel(
                                Event4MuteLevelChange.from_type_GwAdjustIotMsg,
                                subDevice.sku,
                                subDevice.device,
                                this.muteLevel
                            )
                        }
                    }
                }
            }
            if (listChange) {
                ld4SubDevices.value?.run {
                    ld4SubDevices.postValue(this)
                }
            }
        }
    }

    private fun parseWarning(warnings: MutableList<SubDeviceStatus>?) {
        deviceInfo?.run {
            if (warnings.isNullOrEmpty()) return
            var listChange = false
            for (subDeviceStatus in warnings) {
                findSubDeviceBySno(subDeviceStatus.sno)?.let { subDevice ->
                    val key = subDevice.key()
                    val syncMuteLevel = subDevice.syncMuteLevel(subDeviceStatus.muteLevel)
                    //漏水报警
                    if (subDeviceStatus.noLeakWarning()) {
                        WarningOp4Leak.noLeakWarning(key)
                    } else {
                        val ext = Ext4GwInSubDevice.toExt4GwInSubDevice(this, topic)
                        WarningOp4Leak.showWarningDialog(
                            Warning(
                                subDevice,
                                ext,
                                subDeviceStatus.timeSeconds,
                                Warning.from_adjust_detail,
                                false
                            )
                        )
                    }
                    //低电量报警
                    when (subDevice.sku) {
                        H5043Cons.H5059 -> {
                            if (!subDeviceStatus.isLowBattery) {
                                WarningOp4LowBat.noLowBatWarning(key)
                            } else {
                                val ext = Ext4GwInSubDevice.toExt4GwInSubDevice(this, topic)
                                WarningOp4LowBat.showWarningDialog(
                                    Warning(
                                        subDevice,
                                        ext,
                                        subDeviceStatus.timeSeconds,
                                        Warning.from_adjust_detail,
                                        false,
                                        Warning.WARN_TYPE_4_LOW_BAT
                                    )
                                )
                            }
                        }

                        else -> {}
                    }
                    //同步音量档位
                    if (syncMuteLevel) {
                        listChange = true
                        subDevice.settings?.run {
                            Support.reportMuteLevel(
                                Event4MuteLevelChange.from_type_GwAdjustIotMsg,
                                subDevice.sku,
                                subDevice.device,
                                this.muteLevel
                            )
                        }
                    }
                }
            }
            if (listChange) {
                ld4SubDevices.value?.run {
                    ld4SubDevices.postValue(this)
                }
            }
        }
    }

    /**
     * value:triple->first:battery,second:signal,third:online
     */
    private fun compareSubInfo(subDevInfos: HashMap<Int, Triple<Int, Int, Boolean>>) {
        SafeLog.i(TAG) { "Vm4GwDetail--compareSubInfo-->${JsonUtil.toJson(subDevInfos)}" }
        ld4SubDevices.value?.run {
            val request4ChangeBatteryList = mutableListOf<Request4ChangeBattery>()
            var hasSignalChanged = false
            for (subDevice in this) {
                val sku = subDevice.sku() ?: continue
                val device = subDevice.device() ?: continue
                val sno = subDevice.sno() ?: continue
                subDevInfos.remove(sno)?.let { pairInfo ->
                    when (sku) {
                        H5043Cons.H5059 -> {
                            //赋值新的信号强度
                            subDevice.subDevice4Leak?.settings?.apply {
                                if (this.online != pairInfo.third) {
                                    this.online = pairInfo.third
                                    hasSignalChanged = true
                                }
                                //电量>0才是有效的
                                if (pairInfo.first > 0) {
                                    if (this.signal != pairInfo.second) {
                                        this.signal = pairInfo.second
                                        hasSignalChanged = true
                                    }
                                }
                            }
                        }

                        Constant4L5.H5112 -> {
                            //赋值新的信号强度
                            subDevice.subDevice4H5112?.settings?.apply {
                                if (this.online != pairInfo.third) {
                                    this.online = pairInfo.third
                                    hasSignalChanged = true
                                }
                                //电量>0才是有效的
                                if (pairInfo.first > 0) {
                                    if (this.signal != pairInfo.second) {
                                        this.signal = pairInfo.second
                                        hasSignalChanged = true
                                    }
                                }
                            }
                        }

                        else -> {}
                    }
                    //电量>0才是有效的
                    if (pairInfo.first > 0) {
                        val changeBattery = subDevice.changeBattery(pairInfo.first)
                        if (changeBattery) {
                            request4ChangeBatteryList.add(Request4ChangeBattery(sku, device, pairInfo.first))
                        }
                    }
                }
                if (subDevInfos.isEmpty()) break
            }
            ld4SubDevices.postValue(this)
            if (request4ChangeBatteryList.isEmpty() && (!hasSignalChanged)) return
            SafeLog.i(TAG) { "compareBatteries() 当前有${request4ChangeBatteryList.size}台子设备电量发生改变" }/*更新子设备信息*/
            for (request in request4ChangeBatteryList) {
                reportBattery(request)
            }
        }
    }

    private fun reportBattery(request: Request4ChangeBattery) {
        val request4Setting = Req4SetSettingsNew(Transactions().createTransaction()).apply {
            this.sku = request.sku
            this.device = request.device
            this.settings = CommonSettings().apply {
                this.battery = request.battery
            }
        }
        Cache.get(ISmwNet::class.java).updateSetting(request4Setting).enqueue(IHCallBack(request4Setting))
    }

    override fun isSameDevice(sku: String?, device: String?): Boolean {
        return deviceInfo?.isSame(sku, device) ?: false
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4RetryConnectBle(event: Event4RetryConnectBle) {
        val key = event.key()
        SafeLog.i(TAG) { "onEvent4RetryConnectBle() key = $key" }
        deviceInfo?.run {
            if (this.isSame(key) && isBleConnectFail()) {
                startScan(true)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4IdentitySubDevice(event: Event4IdentitySubDevice) {
        SafeLog.i(TAG) { "onEvent4IdentitySubDevice() " }
        val list = ld4SubDevices.value ?: return
        val list4SubDevice = event.bindSubDevice.toList4SubDevice()
        val inSubDeviceListAndMuteLevelChange: BooleanArray = findInSubDeviceList(list, list4SubDevice)
        SafeLog.i(TAG) { "onEvent4IdentitySubDevice() inSubDeviceList = $inSubDeviceListAndMuteLevelChange" }
        if (inSubDeviceListAndMuteLevelChange[0]) {
            subDeviceIdentity.postValue(list4SubDevice.key())
        }
        if (inSubDeviceListAndMuteLevelChange[1]) {
            ld4SubDevices.postValue(list)
            /*上报muteLevel变更*/
            list4SubDevice.subDevice4Leak?.let {
                it.settings?.run {
                    Support.reportMuteLevel(
                        Event4MuteLevelChange.from_type_GwAdjustIdentitySubDevice,
                        it.sku,
                        it.device,
                        this.muteLevel
                    )
                }
            }
        }
    }

    @Synchronized
    @WorkerThread
    private fun findInSubDeviceList(
        subDevices: MutableList<List4SubDevice>, subDevice: List4SubDevice,
    ): BooleanArray {
        val result = BooleanArray(2)
        for (info in subDevices) {
            if (subDevice.sku() == H5043Cons.H5059) {
                if (info.sku() == subDevice.sku() && info.sno() == subDevice.sno()) {
                    //须对找的设备对象赋值deviceId
                    subDevice.subDevice4Leak?.device = info.device() ?: ""
                    //其他处理
                    result[0] = true
                    val syncMuteLevel = info.syncSetting(subDevice)
                    if (syncMuteLevel) {
                        result[1] = true
                    }
                    return result
                }
            }
            if (info.key() == subDevice.key()) {
                result[0] = true
                val syncMuteLevel = info.syncSetting(subDevice)
                if (syncMuteLevel) {
                    result[1] = true
                }
                return result
            }
        }
        return result
    }

    override fun doConnect() {
        if (needPauseBleConnect) {
            return
        }
        super.doConnect()
    }

    override fun onBtConnect(event: EventBleConnect) {
        if (event.address != deviceInfo?.bleAddress) {
            return
        }
        super.onBtConnect(event)
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEventGwConnectCheck(event: Event4GwConnectCheck) {
        deviceInfo?.run {
            if (this.isSame(event.key())) {
                if (isBleConnectSuc() || isIotConnectSuc()) {
                    Event4GwOnline.sendEvent(sku, device, true)
                } else {/*若当前设备连接失败，则需要重新尝试连接*/
                    if (isDeviceConnectFail()) {
                        tryConnect()
                    }
                    Event4GwOnline.sendEvent(sku, device, false)
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEventSubDeviceNameChange(event: Event4SubDeviceNameChange) {
        val key = event.key()
        SafeLog.i(TAG) { "onEventSubDeviceNameChane() key = $key" }
        ld4SubDevices.value?.run {
            var change = false
            for (subDeviceInfo in this) {
                if (subDeviceInfo.key() == key) {
                    change = true
                    subDeviceInfo.changeDeviceName(event.newDeviceName)
                    break
                }
            }
            if (change) {
                ld4SubDevices.postValue(this)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEventDeviceNameChange(event: Event4DeviceNameChange) {
        deviceInfo?.run {
            if (this.isSame(event.key())) {
                val newDeviceName = event.newDeviceName
                SafeLog.i(TAG) { "onEventDeviceNameChange() newDeviceName = $newDeviceName" }
                this.deviceName = newDeviceName
                <EMAIL>(newDeviceName)
                //子设备名称发送变化
            } else {
                querySubDevices(false)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4SubDeviceDeleted(event: Event4SubDeviceDeleted) {
        val key = event.key()
        SafeLog.i(TAG) { "onEvent4SubDeviceDeleted() key = $key" }
        ld4SubDevices.value?.run {
            var change = false
            val iterator = this.iterator()
            while (iterator.hasNext()) {
                val next = iterator.next()
                if (next.key() == key) {
                    change = true
                    iterator.remove()
                    comm4DeleteSubDevice(next, event.opSucCallback)
                    break
                }
            }
            if (change) {
                if (this.isEmpty()) {
                    uiType.postValue(ui_type_suc_empty)
                }
                ld4SubDevices.postValue(this)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4AddSubDeviceSuc4Leak(event: Event4AddSubDeviceSuc4Leak) {
        val key = event.key()
        SafeLog.i(TAG) { "onEvent4AddSubDeviceSuc() key = $key" }
        deviceInfo?.run {
            if (this.isSame(key)) {
                querySubDevices(false)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4AddSubDeviceSuc4H5107(event: Event4AddSubDeviceSuc4H5107) {
        val key = event.key()
        SafeLog.i(TAG) { "Vm4GwDetail--onEvent4AddSubDeviceSuc4H5107-->key = $key" }
        deviceInfo?.run {
            if (this.isSame(key)) {
                querySubDevices(false)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4ChangeGwLightSwitch(event: Event4ChangeGwLightSwitch) {
        val key = event.key()
        SafeLog.i(TAG) { "onEvent4ChangeGwLightSwitch() key = $key" }
        deviceInfo?.run {
            if (this.isSame(key)) {
                val switchOn = event.switchOn
                SafeLog.i(TAG) { "onEvent4ChangeGwLightSwitch() switchOn = $switchOn" }
                doCommController(Compose4DefWrite.makeWriteController(Controller4LightSwitch.makeController4Write(switchOn)) {
                    if (it) {
                        changeLightSwitch(switchOn)
                    } else {
                        Event4ResultGwLightSwitch.sendFail(sku, device)
                    }
                })
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4ChangeBuzzerGear(event: Event4ChangeBuzzerGear) {
        val key = event.key()
        SafeLog.i(TAG) { "ViewMode4Adjust--onEvent4ChangeBuzzerGear-->key = $key" }
        deviceInfo?.run {
            if (this.isSame(key)) {
                val buzzerGear = event.buzzerGear
                SafeLog.i(TAG) { "ViewMode4Adjust--onEvent4ChangeBuzzerGear-->buzzerGear->${buzzerGear}" }
                doCommController(Compose4DefWrite.makeWriteController(Controller4BuzzerGear.makeController4Write(buzzerGear)) {
                    if (it) {
                        changeBuzzerGear(buzzerGear)
                    } else {
                        Event4ResultBuzzerGearSwitch.sendFail(sku, device)
                    }
                })
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4H5058StatusUpdate(event: Event4H5058StatusUpdate) {
        SafeLog.i(TAG) { "onEvent4H5058StatusUpdate() " }
        deviceInfo?.run {
            val gwBleAddress = event.gwBleAddress
            if (this.bleAddress != gwBleAddress) {
                return
            }
            val subDeviceStatus = event.subDeviceStatus
            findSubDeviceBySno(subDeviceStatus.sno)?.let { subDevice ->
                val key = subDevice.key()
                val noLeakWarning = subDeviceStatus.noLeakWarning()
                SafeLog.i(TAG) { "onEvent4H5058StatusUpdate() noWarning = $noLeakWarning ; key = $key" }
                if (noLeakWarning) {
                    WarningOp4Leak.noLeakWarning(key)
                } else {
                    val ext = Ext4GwInSubDevice.toExt4GwInSubDevice(this, topic)
                    WarningOp4Leak.showWarningDialog(
                        Warning(
                            subDevice,
                            ext,
                            subDeviceStatus.timeSeconds,
                            Warning.from_adjust_detail,
                            true
                        )
                    )
                }
                val syncMuteLevel = subDevice.syncMuteLevel(subDeviceStatus.muteLevel)
                if (syncMuteLevel) {
                    subDevice.settings?.let {
                        Support.reportMuteLevel(
                            Event4MuteLevelChange.from_type_GwAdjustSubDeviceStatusUpdate,
                            subDevice.sku,
                            subDevice.device,
                            it.muteLevel
                        )
                    }
                    ld4SubDevices.value?.run {
                        ld4SubDevices.postValue(this)
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4H5059InfoUpdate(event: Event4H5059InfoUpdate) {
        SafeLog.i(TAG) { "onEvent4H5059InfoUpdate() " }
        deviceInfo?.run {
            val gwBleAddress = event.gwBleAddress
            if (this.bleAddress != gwBleAddress) {
                return
            }
            val subInfo = event.subInfoPair.first
            val signal = event.subInfoPair.second
            val subDeviceStatus = SubDeviceStatus(subInfo.sno, subInfo.deviceType, subInfo.leakTimeSeconds, subInfo.leakStatus, subInfo.muteLevel, subInfo.isLowBattery, subInfo.lowBatOpen)
            findSubDeviceBySno(subDeviceStatus.sno)?.let { subDevice ->
                val key = subDevice.key()
                //漏水报警
                if (subDeviceStatus.noLeakWarning()) {
                    WarningOp4Leak.noLeakWarning(key)
                } else {
                    val ext = Ext4GwInSubDevice.toExt4GwInSubDevice(this, topic)
                    WarningOp4Leak.showWarningDialog(
                        Warning(
                            subDevice,
                            ext,
                            subDeviceStatus.timeSeconds,
                            Warning.from_adjust_detail,
                            !isIotConnectSuc()
                        )
                    )
                }
                //低电量报警
                if (!subDeviceStatus.isLowBattery) {
                    WarningOp4LowBat.noLowBatWarning(key)
                } else {
                    val ext = Ext4GwInSubDevice.toExt4GwInSubDevice(this, topic)
                    WarningOp4LowBat.showWarningDialog(
                        Warning(
                            subDevice,
                            ext,
                            subDeviceStatus.timeSeconds,
                            Warning.from_adjust_detail,
                            !isIotConnectSuc(),
                            Warning.WARN_TYPE_4_LOW_BAT
                        )
                    )
                }
                //同步音量档位
                val syncMuteLevel = subDevice.syncMuteLevel(subDeviceStatus.muteLevel)
                if (syncMuteLevel) {
                    subDevice.settings?.let {
                        Support.reportMuteLevel(
                            Event4MuteLevelChange.from_type_GwAdjustSubDeviceStatusUpdate,
                            subDevice.sku,
                            subDevice.device,
                            it.muteLevel
                        )
                    }
                }
                subDevice.run {
                    //更新subDevice的信息
                    settings?.online = subInfo.online
                    settings?.battery = subInfo.battery
                    settings?.muteLevel = subInfo.muteLevel
                    settings?.signal = signal
                }
                ld4SubDevices.value?.run {
                    SafeLog.i(TAG) { "Vm4GwDetail--onEvent4H5059InfoUpdate-->${JsonUtil.toJson(this)}" }
                    ld4SubDevices.postValue(this)
                }
            }
        }
    }

    private fun findSubDeviceBySno(sno: Int): SubDevice? {
        ld4SubDevices.value?.run {
            for (subDevice in this) {
                subDevice.subDevice4Leak?.let {
                    it.settings?.run {
                        if (this.sno == sno) return it
                    }
                }
            }
        }
        return null
    }

    private fun changeLightSwitch(switchOn: Boolean) {
        SafeLog.i(TAG) { "changeLightSwitch() switchOn = $switchOn" }
        this.gwLightSwitchOn = switchOn
        deviceInfo?.run {
            Event4ResultGwLightSwitch.sendSuc(sku, device, switchOn)
        }
    }

    private fun changeBuzzerGear(buzzerGear: Int) {
        SafeLog.i(TAG) { "ViewMode4Adjust--changeBuzzerGear-->${buzzerGear}" }
        this.gwBuzzerGear = buzzerGear
        deviceInfo?.run {
            Event4ResultBuzzerGearSwitch.sendSuc(sku, device, buzzerGear)
        }
    }

    private fun comm4DeleteSubDevice(subDevice: List4SubDevice, resultCallback: (() -> Unit)?) {
        subDevice.sno()?.let { sno ->
            SafeLog.i(TAG) { "comm4DeleteSubDevice() sno = $sno" }
            val controller = Controller4DeleteSubDevice.makeController(sno)
            doCommController(Compose4DefWrite.makeWriteController(controller) { opResult ->
                if (opResult) {
                    resultCallback?.invoke()
                }
            })
        }
    }

    override fun changeBleStatus(status: Int) {
        super.changeBleStatus(status)
        deviceInfo?.run {
            if (isBleConnectFail()) {
                bleConnectResult.postValue(connect_status_fail)
                Event4BleConnectResult.sendEvent(false, sku, device)
            } else if (isBleConnectSuc()) {
                bleConnectResult.postValue(connect_status_suc)
                Event4BleConnectResult.sendEvent(true, sku, device)
            } else {
                bleConnectResult.postValue(connect_status_ing)
            }
        }
    }

    override fun deviceConnectResult(suc: Boolean) {
        SafeLog.i(TAG) { "deviceConnectResult() suc =$suc ; syncList = $firstSyncSubDevices" }
        deviceInfo?.run {
            Event4GwOnline.sendEvent(sku, device, suc)
        }
        if (suc) {
            syncSubDevicesAfterConnectSuc()
        }
    }

    fun syncSubDevicesAfterConnectSuc() {
        SafeLog.i(TAG) { "syncSubDevicesAfterConnectSuc() syncList = $firstSyncSubDevices" }
        if (firstSyncSubDevices) return
        doHandler(what_sync_sub_devices, delay_time_4_sync_sub_devices)
    }

    override fun doWhat(what: Int): Boolean {
        val result = super.doWhat(what)
        if (result) return true
        return when (what) {
            what_sync_sub_devices -> {
                trySyncSubDevices()
                true
            }

            else -> {
                false
            }
        }
    }

    override fun showAppOldDialog(curProtocol: Protocol) {}

    private fun trySyncSubDevices() {
        SafeLog.i(TAG) { "trySyncSubDevices() firstSyncSubDevices = $firstSyncSubDevices" }
        if (firstSyncSubDevices) return
        val connectSuc = isConnectSuc()
        SafeLog.i(TAG) { "trySyncSubDevices() connectSuc = $connectSuc" }
        if (!connectSuc) return
        subDeviceSnoList?.run {
            val empty = this.isEmpty()
            SafeLog.i(TAG) { "Vm4GwDetail--trySyncSubDevices-->${JsonUtil.toJson(this)}" }
            val resultCallback: (suc: Boolean) -> Unit = { suc ->
                SafeLog.i(TAG) { "trySyncSubDevices() result-> suc = $suc" }
                if (suc) {
                    firstSyncSubDevices = true
                }
                //同步子设备数据完毕
                Event4SyncSubDevices.sendEvent(false)
            }
            val controller = if (empty) {
                Compose4DefWrite.makeWriteController(
                    Controller4DeleteSubDevice.makeController4DeletedAll(), resultCallback
                )
            } else {
                Compose4DefWrite4Multi.makeWriteController4Multi(
                    Controller4SyncSubDevice.makeController(
                        this
                    ), resultCallback
                )
            }
            //开始同步子设备数据
            Event4SyncSubDevices.sendEvent(true)
            doCommController(controller)
        }
    }

    //类H5151网关功能子设备相关-------------------------start----------------------------
    /**
     * 类H5151网关功能的子设备发生变化
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4SubDeviceChange(event: Event4SubDeviceChange) {
        if (deviceInfo?.isSame(event.sku, event.device) == true) {
            //重置子设备的iot状态
            GwH5151Op.iotStatusReset(deviceInfo?.key())
            //重新载入子设备列表
            querySubDevices(false)
        }
    }

    /**
     * 绑定的H5151子设备列表
     */
    private val bindH5151SubDevs = ArrayList<AbsDevice>()

    /**
     * 类H5151网关功能-->绑定子设备
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4BindSubDevice(event: Event4BindSubDevice) {
        deviceInfo?.run {
            if (this.isSame(event.key())) {
                bindH5151SubDevs.clear()
                bindH5151SubDevs.addAll(event.devices)
                bindSubDevices4H5151Gw()
            }
        }
    }

    private fun bindSubDevices4H5151Gw() {
        if (isBleConnectSuc() || isIotConnectSuc()) {
            val controllers = mutableListOf<IControllerNoEvent>().apply {
                add(Controller4SubDeviceThRange4Common.makeController(bindH5151SubDevs))
            }
            var hasCallBack = false
            val controller4Multi = Compose4DefWrite4Multi.makeWriteController4Multi(controllers) {
                if (!hasCallBack) {
                    SafeLog.i(TAG) { "ViewMode4Adjust--onEvent4BindSubDevice--类H5151网关绑定子设备=>result->${it}" }
                    hasCallBack = true
                    Event4BindSubDeviceResult.sendEvent(deviceInfo?.sku ?: "", deviceInfo?.device ?: "", it)
                }
            }
            doCommController(controller4Multi, false)
        } else {
            handler.postDelayed({
                Event4BindSubDeviceResult.sendEvent(deviceInfo?.sku ?: "", deviceInfo?.device ?: "", false)
            }, 1000)
        }
    }

    override fun onScanEvent(event: ScanEvent) {
        if (needPauseBleConnect) {
            return
        }
        super.onScanEvent(event)
    }

    /**
     * 类H5151网关功能-->添加新设备到帐号下
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4AddNewSubDevice(event: Event4AddSubToAccount) {
        needPauseBleConnect = event.isStart
        if (event.isStart) {
            //断开蓝牙
            if (BleController.getInstance().connectedBleAddress == deviceInfo?.bleAddress) {
                BleController.getInstance().disconnectBleAndNotify()
                ble.curConnectBleAddress = ""
            }
        } else {
            if (BleController.getInstance().connectedBleAddress != deviceInfo?.bleAddress) {
                reConnectAndScanSubBle()
            }
        }
    }

    /**
     * 暂停连接
     */
    fun pauseConnect() {
        //修改标识位
        needPauseBleConnect = true
        //断开蓝牙
        if (BleController.getInstance().connectedBleAddress == deviceInfo?.bleAddress) {
            BleController.getInstance().disconnectBleAndNotify()
            ble.curConnectBleAddress = ""
        }
    }

    /**
     * 从蓝牙温湿度计详情页返回或添加新子设备返回，处理原设备的连接、子设备的扫描等
     */
    fun reConnectAndScanSubBle() {
        //修改标识位
        needPauseBleConnect = false
        //重新连接并扫描蓝牙广播
        tryConnect()
        handler.removeCallbacks(scanH5151SubRunnable)
        handler.postDelayed(scanH5151SubRunnable, 0)
    }

    /**
     * 扫描H5151子设备蓝牙的Runnable
     */
    private val scanH5151SubRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                handler.removeCallbacks(this)
                if (isBleConnectSuc() || isBleConnectFail()) {
                    EventBleBroadcastListenerTrigger.sendEvent4EnforceStartScan()
                    SafeLog.i(TAG) { "ViewMode4Adjust--runSafe-->开始扫描子设备..." }
                } else {
                    handler.postDelayed(this, 1000L)
                }
            }
        }
    }

    /**
     * 蓝牙状态发生变化
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4GwSubBleStatusUpdate(event: Event4GwSubBleStatusUpdate) {
        val oneDeviceUpdate = event.isOneDeviceUpdate()
        ld4SubDevices.value?.run {
            if (oneDeviceUpdate) {
                val oneDeviceKey = event.oneDeviceKey
                if (!oneDeviceKey.isNullOrEmpty()) {
                    var hadChange = false
                    for (absDevice in this) {
                        if (absDevice.key() == oneDeviceKey) {
                            hadChange = true
                            break
                        }
                    }
                    if (hadChange) {
                        ld4SubDevices.postValue(this)
                    }
                }
            } else {
                ld4SubDevices.postValue(this)
            }
        }
    }

    /**
     * 网关状态发生变化或回复信息，刷新子设备列表=->更新wifi图标
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4GwSubIotStatusUpdate(event: Event4RefreshSubList) {
        if (event.isSame(deviceInfo?.key() ?: "")) {
            ld4SubDevices.value?.run {
                ld4SubDevices.postValue(this)
            }
        }
    }

    /**
     * 子设备切换网关绑定
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun event4GwHasUpdate(event: Event4ChangeGwForSub) {
        ld4SubDevices.value?.let {
            for (subDev in it) {
                if (event.isSameSubDev(subDev.sku(), subDev.device())) {
                    querySubDevices()
                    return
                }
            }
        }
    }
    //类H5151网关功能子设备相关-------------------------end----------------------------

    /**
     * 删除蓝牙温湿度计子设备
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4DeleteBleTh(event: Event4DeleteThFromGw) {
        //重新载入子设备列表
        querySubDevices(false)
    }

    /**
     * H5059相关设置操作处理
     */
    private var setEventType = 0
    private var setSno = -1

    private val h5059SetOverTimeRunnable by lazy {
        object : com.govee.base2home.util.CaughtRunnable() {
            override fun runSafe() {
                when (setEventType) {
                    Event4H5059DevSetting.EVENT_TYPE_4_SET_VOLUME_GEAR -> {
                        Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_SET_VOLUME_GEAR_RESULT, setSno, Pair(false, isIotConnectSuc()))
                    }

                    Event4H5059DevSetting.EVENT_TYPE_4_FIND_DEVICE -> {
                        Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_FIND_DEVICE_RESULT, setSno, Pair(false, isIotConnectSuc()))
                    }

                    Event4H5059DevSetting.EVENT_TYPE_4_CHECK_NET -> {
                        Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_CHECK_NET_RESULT, setSno, Pair(false, isIotConnectSuc()))
                    }

                    Event4H5059DevSetting.EVENT_TYPE_4_SWITCH_LOW_BAT -> {
                        Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_SWITCH_LOW_BAT_RESULT, setSno, Pair(false, isIotConnectSuc()))
                    }

                    else -> {}
                }
            }
        }
    }

    /**
     * H5059的设置操作事件响应
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4H5059Setting(event: Event4H5059DevSetting) {
        findSubDeviceBySno(event.sno)?.let { subDevice ->
            if (event.request4Setting?.getKey() != subDevice.key()) {
                return
            }
            if (event.sno != subDevice.settings?.sno) {
                return
            }
            this.setEventType = event.eventType
            this.setSno = event.sno
            when (event.eventType) {
                Event4H5059DevSetting.EVENT_TYPE_4_SET_VOLUME_GEAR -> {
                    val setVolumeController = Controller4SetVolume4H5059.makeController4Write(event.sno, event.request4Setting?.muteLevel ?: 0)
                    doCommController(Compose4DefWrite.makeWriteController(setVolumeController) { opResult ->
                        if (!opResult) {
                            //移除超时
                            handler.removeCallbacks(h5059SetOverTimeRunnable)
                            //执行回调
                            Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_SET_VOLUME_GEAR_RESULT, event.sno, Pair(false, isIotConnectSuc()))
                        }
                    })
                }

                Event4H5059DevSetting.EVENT_TYPE_4_FIND_DEVICE -> {
                    val findDeviceController = Controller4FindDevice4H5059.makeController4Write(event.sno)
                    doCommController(Compose4DefWrite.makeWriteController(findDeviceController) { opResult ->
                        if (!opResult) {
                            //移除超时
                            handler.removeCallbacks(h5059SetOverTimeRunnable)
                            //执行回调
                            Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_FIND_DEVICE_RESULT, event.sno, Pair(false, isIotConnectSuc()))
                        }
                    })
                }

                Event4H5059DevSetting.EVENT_TYPE_4_CHECK_NET -> {
                    val checkNetController = Controller4CheckSignal4H5059.makeController4Write(event.sno)
                    doCommController(Compose4DefWrite.makeWriteController(checkNetController) { opResult ->
                        if (!opResult) {
                            //移除超时
                            handler.removeCallbacks(h5059SetOverTimeRunnable)
                            //执行回调
                            Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_CHECK_NET_RESULT, event.sno, Pair(false, isIotConnectSuc()))
                        }
                    })
                }

                Event4H5059DevSetting.EVENT_TYPE_4_SWITCH_LOW_BAT -> {
                    val switchLowBatController = Controller4LowBatSwitch4H5059.makeController4Write(event.sno, event.request4Setting?.batteryWarning ?: false)
                    doCommController(Compose4DefWrite.makeWriteController(switchLowBatController) { opResult ->
                        if (!opResult) {
                            //移除超时
                            handler.removeCallbacks(h5059SetOverTimeRunnable)
                            //执行回调
                            Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_SWITCH_LOW_BAT_RESULT, event.sno, Pair(false, isIotConnectSuc()))
                        }
                    })
                }

                else -> {
                    return
                }
            }
            //设置超时
            handler.removeCallbacks(h5059SetOverTimeRunnable)
            handler.postDelayed(h5059SetOverTimeRunnable, SET_FOR_RESULT_OVER_TIME)
        }
    }

    /**
     * 操作子设备属性时的结果上报
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4SubOpResult(event: Event4SubOpResult) {
        if (event.sno == setSno) {
            when (event.opType) {
                //音量档位
                Controller.comm_h5059_op_4_set_volume_gear.toInt() -> {
                    if (setEventType == Event4H5059DevSetting.EVENT_TYPE_4_SET_VOLUME_GEAR) {
                        //移除超时
                        handler.removeCallbacks(h5059SetOverTimeRunnable)
                        //执行回调
                        Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_SET_VOLUME_GEAR_RESULT, event.sno, Pair(true, isIotConnectSuc()))
                    }
                }

                //查找设备
                Controller.comm_h5059_op_4_find_sub_device.toInt() -> {
                    if (setEventType == Event4H5059DevSetting.EVENT_TYPE_4_FIND_DEVICE) {
                        //移除超时
                        handler.removeCallbacks(h5059SetOverTimeRunnable)
                        //执行回调
                        Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_FIND_DEVICE_RESULT, event.sno, Pair(true, isIotConnectSuc()))
                    }
                }
                //联网检测
                Controller.comm_h5059_op_4_signal_check.toInt() -> {
                    if (setEventType == Event4H5059DevSetting.EVENT_TYPE_4_CHECK_NET) {
                        //移除超时
                        handler.removeCallbacks(h5059SetOverTimeRunnable)
                        //执行回调
                        Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_CHECK_NET_RESULT, event.sno, Pair(true, isIotConnectSuc()))
                    }
                }
                //低电量告警开/关
                Controller.comm_h5059_op_4_low_battery_switch.toInt() -> {
                    if (setEventType == Event4H5059DevSetting.EVENT_TYPE_4_SWITCH_LOW_BAT) {
                        //移除超时
                        handler.removeCallbacks(h5059SetOverTimeRunnable)
                        //执行回调
                        Event4H5059DevSetting.resultEvent(Event4H5059DevSetting.EVENT_TYPE_4_SWITCH_LOW_BAT_RESULT, event.sno, Pair(true, isIotConnectSuc()))
                    }
                }

                else -> {}
            }
        }
    }

    //===================================-------H5044绑定/解绑H5112相关-------===================================
    private var bindH5112Callback: ((result: Boolean) -> Unit)? = null
    private var opSubDevice: AbsDevice? = null
    private val bindH5112OverTimeRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                bindH5112Callback?.invoke(false)
                //置空相关信息
                bindH5112Callback = null
                handler.removeCallbacks(this)
            }
        }
    }

    /**
     * 接收到H5044绑定H5112的事件
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4H5044ToBindH5112(event: Event4H5044ToBindH5112) {
        deviceInfo?.run {
            if (this.isSame(event.key())) {
                toBindH51124H5044(event.subDevice) {
                    if (it) {
                        querySubDevices(false)
                    }
                    Event4BindSubDeviceResult.sendEvent(event.sku, event.device, it)
                    handler.postDelayed(object : CaughtRunnable() {
                        override fun runSafe() {
                            globalLaunch(Dispatchers.Main) {
                                if (it) {
                                    toast(R.string.temhum_bind_suc)
                                } else {
                                    toast(R.string.h512x_bind_failure)
                                }
                            }
                        }
                    }, 100)
                }
            }
        }
    }

    /**
     * 执行H5044绑定H5112子设备
     */
    private fun toBindH51124H5044(
        subDevice: AbsDevice,
        callback: ((result: Boolean) -> Unit),
    ) {
        deviceInfo?.let {
            //先写入网关设备，再绑定到服务器
            doCommController(Compose4DefWrite.makeWriteController(Controller4H5044BindH5112.makeController(subDevice.device)) { opResult ->
                if (opResult) {
                    //等待结果上报
                    opSubDevice = subDevice
                } else {
                    //移除超时
                    handler.removeCallbacks(bindH5112OverTimeRunnable)
                    //回调结果
                    callback.invoke(false)
                    bindH5112Callback = null
                }
            })
            bindH5112Callback = callback
            //设置总超时
            handler.removeCallbacks(bindH5112OverTimeRunnable)
            handler.postDelayed(bindH5112OverTimeRunnable, 30 * 1000)
        } ?: run {
            callback.invoke(false)
        }
    }

    /**
     * H5044绑定H5112(ble/iot都回调到此处)
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4H5044BindH5112Result(event: Event4H5112BindResult) {
        bindH5112Callback?.let {
            //移除超时
            handler.removeCallbacks(bindH5112OverTimeRunnable)
            //处理结果
            val validResult = (event.sno >= 0) && (deviceInfo != null) && (opSubDevice != null) && (opSubDevice!!.device == event.deviceId)
            if (validResult) {
                toBindH51124H5044ToService(
                    deviceInfo!!.sku,
                    deviceInfo!!.device,
                    SubDevice4H5112(opSubDevice!!.sku, opSubDevice!!.device).apply {
                        settings = Ext4SubH5112().apply {
                            this.sno = event.sno
                        }
                    },
                    it
                )
            } else {
                it.invoke(false)
            }
        }
    }

    private fun toBindH51124H5044ToService(
        sku: String,
        device: String,
        subDevice: SubDevice4H5112,
        callback: (result: Boolean) -> Unit,
    ) {
        bindH5112Callback = null
        request({
            com.govee.h5151.gw.net.netService.bindSubDevices4H5112(Request4BindH5112(sku, device, mutableListOf<SubDevice4H5112>().apply {
                add(subDevice)
            }))
        }, success = {
            callback.invoke(true)
        }, error = {
            callback.invoke(false)
        })
    }

    /**
     * 接收到H5044解绑H5112的事件
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4DeleteH5112FromH5044(event: Event4DeleteH5112FromH5044) {
        deviceInfo?.run {
            if (this.isSame(event.key())) {
                deleteH5112FromH5044ToService(this.sku, this.device, event.subDevice) {
                    if (it) {
                        querySubDevices(false)
                    }
                    Event4BindSubDeviceResult.sendEvent(event.sku, event.device, it)
                    handler.postDelayed(object : CaughtRunnable() {
                        override fun runSafe() {
                            globalLaunch(Dispatchers.Main) {
                                if (it) {
                                    toast(R.string.dbgw_remove_success_text)
                                } else {
                                    toast(R.string.dbgw_remove_failure_text)
                                }
                            }
                        }
                    }, 100)
                }
            }
        }
    }

    /**
     * 先删除服务端的绑定关系
     */
    private fun deleteH5112FromH5044ToService(
        sku: String,
        device: String,
        subDevice: AbsDevice,
        callback: (result: Boolean) -> Unit,
    ) {
        request({
            netService.unbindSubDevices4H5112(Request4BindH5112(sku, device, mutableListOf<SubDevice4H5112>().apply {
                add(SubDevice4H5112(subDevice.sku, subDevice.device))
            }))
        }, success = {
            //再解绑设备上的绑定关系
            JsonUtil.fromJson(subDevice.deviceExt.deviceSettings, Ext::class.java)?.let {
                if (it.sno >= 0) {
                    val controller = Controller4DeleteSubDevice.makeController(it.sno)
                    doCommController(Compose4DefWrite.makeWriteController(controller, null))
                }
            }
            callback.invoke(true)
        }, error = {
            callback.invoke(false)
        })
    }
}