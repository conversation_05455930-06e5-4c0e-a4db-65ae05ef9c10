package com.govee.h5043.adjust.gw

import android.content.Context
import android.os.Bundle
import android.os.Vibrator
import android.view.View
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.govee.base2home.Constant
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.h5.WebActivity
import com.govee.base2home.kt.ac.AbsBTRp4EnforceAcHint
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.push.EventDeviceListFresh
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.dp4Int
import com.govee.base2kt.ext.itemDecorationConfig
import com.govee.base2kt.ext.toast
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2kt.utils.IntentUtils
import com.govee.base2light.kt.comm.AbsViewMode4Op
import com.govee.base2light.light.EventBleTimeMillsUpdate
import com.govee.base2light.vm4sku2op.BaseDevInfo
import com.govee.base2newth.other.Dialog4ConnectShowing
import com.govee.ble.BleController
import com.govee.h5043.H5043Cons
import com.govee.h5043.add.DeviceInfo
import com.govee.h5043.adjust.Config
import com.govee.h5043.adjust.h5058.Ac4H5058Detail
import com.govee.h5043.adjust.h5059.Ac4H5059Detail
import com.govee.h5043.adjust.h5107.Ac4H5107Detail
import com.govee.h5043.ble.Event4RetryConnectBle
import com.govee.h5043.databinding.H5043Ac4AdjustBinding
import com.govee.h5043.model.Ext4GwInSubDevice
import com.govee.kt.ui.device.GwH5151Op
import com.govee.kt.ui.device.Item
import com.govee.kt.ui.device.base.GwIotOnline
import com.govee.ui.R
import com.govee.ui.dialog.ImageHintDialogV2
import com.govee.util.recordUseCount
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil

/**
 * Create by xieyingwu on 2023/3/21
 * 详情页
 */
@Suppress("OVERRIDE_DEPRECATION", "DEPRECATION")
class Ac4Detail : AbsBTRp4EnforceAcHint<H5043Ac4AdjustBinding>() {
    companion object {
        private const val intent_ac_key_device_info = "intent_ac_key_device_info"
        private const val intent_ac_key_topic = "intent_ac_key_topic"

        fun jump2DetailAc(context: Context, deviceInfo: DeviceInfo, topic: String?) {
            JumpUtil.jump(context, Ac4Detail::class.java, Bundle().apply {
                putParcelable(intent_ac_key_device_info, deviceInfo)
                putString(intent_ac_key_topic, topic)
            })
        }
    }

    private val viewModel by lazy { ViewModelProvider(this)[Vm4GwDetail::class.java] }
    private val adapter by lazy { Adapter4SubDeviceList() }
    private val vibrator: Vibrator by lazy {
        getSystemService(Vibrator::class.java)
    }
    private var dialog4ConnectShowing: Dialog4ConnectShowing? = null
    private val vibratePattern = longArrayOf(500, 200, 500, 200, 500, 200)
    private val vibrateKeyHashSet = hashSetOf<Int>()

    /**
     * 标记是否进入蓝牙温湿度计子设备的详情页
     */
    private var backFromBleThDetail = false

    private val vibrateListener: (start: Boolean, key: Int, clear: Boolean) -> Unit =
        { start, key, clear ->
            if (clear) {
                vibrateKeyHashSet.clear()
                vibrator.cancel()
            } else {
                if (start) {
                    vibrateKeyHashSet.add(key)
                    vibrator.vibrate(vibratePattern, -1)
                } else {
                    vibrateKeyHashSet.remove(key)
                    if (vibrateKeyHashSet.isEmpty()) {
                        vibrator.cancel()
                    }
                }
            }
        }

    override fun doPreCheck(savedInstanceState: Bundle?) {
        val deviceInfo = IntentUtils.parseParcelable<DeviceInfo>(intent, intent_ac_key_device_info) ?: run {
            toast(R.string.h721214_other_listen)
            finish()
            return
        }
        viewModel.init(deviceInfo, intent.getStringExtra(intent_ac_key_topic))
        initUi(deviceInfo)
        initOpClick()
        initObserver()
        viewModel.querySubDevices(true)
    }

    override fun statusBarLightMode(): Boolean {
        return false
    }

    private fun initOpClick() {
        viewBinding.run {
            tv4TipsBind.clickDelay {
                AnalyticsRecorder.getInstance().recordUseCount(
                    viewModel.deviceInfo?.sku, ParamFixedValue.pair_sub_device_guide_click
                )
                val url = when (viewModel.deviceInfo?.sku) {
                    Constant4L5.H5044 -> {
                        String.format(Constant.guide_4_pair_sub_device_4_h5044, Constant.getH5RunModeDomainV2());
                    }

                    else -> {
                        Constant.getGuide4PairSubDevice4Gw(viewModel.deviceInfo?.sku ?: "")
                    }
                }
                WebActivity.jump2WebAc(
                    this@Ac4Detail,
                    ResUtil.getString(R.string.how_to_add_sub_device),
                    url
                )
                //埋点
                viewModel.deviceInfo?.sku?.let {
                    when (it) {
                        H5043Cons.H5043 -> {
                            AnalyticsRecorder.getInstance().recordUseCount(it, ParamFixedValue.pair_sub_device_guide_click)
                        }

                        H5043Cons.H5044 -> {
                            recordUseCount(it, ParamFixedValue.click_how_bind_sub_device)
                        }

                        else -> {}
                    }
                }
            }
            tv4BtnAdd.clickDelay {
                viewModel.deviceInfo?.let {
                    when (it.goodsType) {
                        GoodsType.GOODS_TYPE_VALUE_GATEWAY_H5043 -> {
                            if (H5043Cons.supportNewAddFuc(it.versionSoft)) {
                                Ac4ChooseSubDevice.jump2ChooseSubDeviceAc(this@Ac4Detail, it, viewModel.leakNum, viewModel.thNum)
                            } else {
                                AnalyticsRecorder.getInstance().recordUseCount(it.sku, ParamFixedValue.add_sub_device_click)
                                val bleConnectSuc = viewModel.isBleConnectSuc()
                                if (bleConnectSuc) {
                                    Ac4BindSubDevice.jump2BindSubDeviceAc(this@Ac4Detail, it, adapter.itemCount, H5043Cons.sub_device_type_h5058)
                                } else {
                                    showBleConnectDialog()
                                }
                            }
                        }

                        GoodsType.GOODS_TYPE_VALUE_4_GW_H5044 -> {
                            Ac4ChooseSubDevice.jump2ChooseSubDeviceAc(this@Ac4Detail, it, viewModel.leakNum, viewModel.thNum, viewModel.h5151SubNum)
                        }

                        else -> {}
                    }

                }
            }
            iv4Back.clickDelay {
                onBackPressed()
            }
            iv4Setting.clickDelay {
                jump2SettingAc()
            }
        }
    }

    private fun recyclerDialog() {
        dialog4ConnectShowing?.hide()
        dialog4ConnectShowing = null
    }

    private fun showBleConnectDialog() {
        viewModel.deviceInfo?.run {
            if (BleController.getInstance().isBlueToothOpen) {
                recyclerDialog()
                dialog4ConnectShowing = Dialog4ConnectShowing.makeDialog(this@Ac4Detail).apply {
                    manualCloseCallback = {
                        dialog4ConnectShowing = null
                    }
                    retryConnectClick = {
                        Event4RetryConnectBle.sendEvent(sku, device)
                    }
                    if (!viewModel.isBleConnectSuc()) {
                        show()
                    }
                }
                Event4RetryConnectBle.sendEvent(sku, device)
            } else {
                toast(R.string.ble_not_open_msg)
            }
        }
    }

    private fun jump2SettingAc() {
        SafeLog.i(TAG) { "jump2SettingAc() " }
        viewModel.deviceInfo?.run {
            Ac4GwSetting.jump2GwSettingAc(this@Ac4Detail, this, viewModel.gwLightSwitchOn, viewModel.gwBuzzerGear)
        }
    }

    private fun initUi(deviceInfo: DeviceInfo) {
        viewBinding.run {
            adapter.itemClick {
                AnalyticsRecorder.getInstance().recordUseCount(it.sku(), ParamFixedValue.gateway_sub_device_click)
                it.subDevice4Leak?.let { sub4Leak ->
                    when (sub4Leak.sku) {
                        H5043Cons.H5058 -> {
                            Ac4H5058Detail.jump2SubDeviceDetailAcFromGwSubDeviceList(
                                this@Ac4Detail,
                                GoodsType.GOODS_TYPE_VALUE_GW_SUB_H5058,
                                sub4Leak,
                                Ext4GwInSubDevice.toExt4GwInSubDevice(deviceInfo, viewModel.topic)
                            )
                        }

                        H5043Cons.H5059 -> {
                            Ac4H5059Detail.jump2SubDeviceDetailAcFromGwSubDeviceList(
                                this@Ac4Detail,
                                GoodsType.GOODS_TYPE_VALUE_4_H5059,
                                sub4Leak.apply {
                                    settings?.online = settings?.online.isTrue() && adapter.gwOnline.isTrue()
                                },
                                Ext4GwInSubDevice.toExt4GwInSubDevice(deviceInfo, viewModel.topic)
                            )
                            //埋点
                            recordUseCount(sub4Leak.sku, ParamFixedValue.click_sub_device)
                        }

                        else -> {}
                    }

                    return@itemClick
                }
                it.subDevice4H5107?.let { sub4H5107 ->
                    val baseDevInfo = BaseDevInfo()
                    baseDevInfo.sku = sub4H5107.gwInfo?.sku ?: ""
                    baseDevInfo.device = sub4H5107.gwInfo?.device ?: ""
                    baseDevInfo.bleAddress = sub4H5107.gwInfo?.address ?: ""
                    baseDevInfo.topic = sub4H5107.gwInfo?.topic ?: ""
                    baseDevInfo.extInfo = JsonUtil.toJson(sub4H5107)
                    val gwIotOnline = GwIotOnline.queryGwIotOnline(baseDevInfo.key()).iotOnline()
                    val subOnline = viewModel.h5107OnlineInfos[sub4H5107.settings?.sno ?: -1] ?: false
                    Ac4H5107Detail.jump2SubDetail(this@Ac4Detail, baseDevInfo, false, gwIotOnline, subOnline, true)
                    return@itemClick
                }
                it.subDevice4H5112?.let { subDev45112 ->
                    DeviceListConfig.read().getDeviceByKey(subDev45112.sku, subDev45112.device)?.run {
                        //断开当前连接的设备
                        viewModel.pauseConnect()
                        //跳转至子设备详情页
                        GwH5151Op.itemClick(this@Ac4Detail, this)
                        //更改标识位
                        backFromBleThDetail = true
                    }
                    return@itemClick
                }

                it.subDevice4BleTh?.let { sub4BleTh ->
                    DeviceListConfig.read().getDeviceByKey(sub4BleTh.sku, sub4BleTh.device)?.run {
                        //断开当前连接的设备
                        viewModel.pauseConnect()
                        //跳转至子设备详情页
                        GwH5151Op.itemClick(this@Ac4Detail, this)
                        //更改标识位
                        backFromBleThDetail = true
                    }
                    return@itemClick
                }
                SafeLog.e(TAG) { "initUi() itemClick() not support subDevice!" }
            }
            adapter.vibratorListener = vibrateListener
            subDeviceList.initAdapter(
                adapter,
                LinearLayoutManager(this@Ac4Detail, RecyclerView.VERTICAL, false),
                refreshCallBack = {
                    viewModel.querySubDevices(false)
                })
            subDeviceList.retryData {
                viewModel.querySubDevices(true)
            }
            subDeviceList.recycle.run {
                itemDecorationConfig {
                    bottomOp(10.dp4Int)
                }
                clipToPadding = false
                setPadding(paddingLeft, paddingTop, paddingRight, 85.dp4Int)
            }
            //空白文案设置
            var emptyHintStr = ResUtil.getString(R.string.gateway_device_empty_hint)
            when (deviceInfo.sku) {
                Constant4L5.H5043 -> {
                    emptyHintStr += "\n" + ResUtil.getString(R.string.gateway_device_hint_4_sub_device_identify)
                }

                else -> {}
            }
            tv4EmptyHint.text = emptyHintStr
        }
    }

    private fun initObserver() {
        viewModel.uiType.observe(this) {
            it?.run {
                ui(it)
            }
        }
        viewModel.ld4SubDevices.observe(this) {
            val nullOrEmpty = it.isNullOrEmpty()
            if (!nullOrEmpty) {
                viewModel.syncSubDevicesAfterConnectSuc()
                viewModel.deviceInfo?.run {
                    val needHint4FirstIntoAdjustAc = Config.makeConfig().needHint4FirstIntoAdjustAc(sku, device)
                    SafeLog.i(TAG) { "initObserver() needHint4FirstIntoAdjustAc = $needHint4FirstIntoAdjustAc" }
                    if (needHint4FirstIntoAdjustAc) {
                        when (sku) {
                            Constant4L5.H5043 -> {
                                for (subDevice in it!!) {
                                    //仅漏水需要弹窗，H5107不需要
                                    if (subDevice.sku() == Constant4L5.H5058) {
                                        showHint4FirstIntoAdjustAc(sku, device)
                                        break
                                    }
                                }
                            }

                            Constant4L5.H5044 -> {
                                for (subDevice in it!!) {
                                    //仅漏水需要弹窗，H5112不需要
                                    if (subDevice.sku() == Constant4L5.H5059) {
                                        showHint4FirstIntoAdjustAc(sku, device)
                                        break
                                    }
                                }
                            }

                            else -> {}
                        }
                    }
                }
            }
            adapter.setList(it, object : Adapter4SubDeviceList.DataChangeListener {
                override fun onChange(hasChange: Boolean) {
                    if (hasChange) {
                        //子设备列表发生变化，首页卡片列表更新
                        EventDeviceListFresh.sendEventDeviceListFresh()
                    }
                }
            })
        }
        viewModel.subDeviceIdentity.observe(this) {
            if (!it.isNullOrEmpty() && isResume) {
                val pos = adapter.anim(it)
                if (pos != -1) {
                    tryScroll2Pos(pos)
                }
            }
        }
        viewModel.deviceName.observe(this) {
            val curDeviceName: String = it ?: ""
            viewBinding.tv4Title.text = curDeviceName
        }
        viewModel.bleConnectResult.observe(this) {
            it?.run {
                bleConnectStatus(it)
            }
        }
        viewModel.connectStatus4Iot.observe(this) {
            it?.run {
                when (it) {
                    AbsViewMode4Op.connect_status_suc -> {
                        adapter.gwOnlineRefresh(true)
                    }

                    AbsViewMode4Op.connect_status_fail -> {
                        adapter.gwOnlineRefresh(false)
                    }

                    else -> {
                        GwIotOnline.queryGwIotOnline(viewModel.deviceInfo?.key() ?: "").let { iotOnline ->
                            if (iotOnline.wifiStatus() != Item.WIFI_STATUS_INVISIBLE) {
                                adapter.gwOnlineRefresh(iotOnline.iotOnline())
                            }
                        }
                    }
                }
            }
        }
        viewModel.mld4GwOffline.observe(this) {
            adapter.gwOnlineRefresh(false)
        }
    }

    private fun tryScroll2Pos(pos: Int) {
        SafeLog.i(TAG) { "tryScroll2Pos() pos = $pos" }
        viewBinding.run {
            val lm = subDeviceList.recycle.layoutManager as? LinearLayoutManager ?: return@run
            lm.scrollToPosition(pos)
        }
    }

    private fun bleConnectStatus(connectStatus: Int) {
        if (connectStatus == AbsViewMode4Op.connect_status_suc) {
            val afterConnectSuc2BindSubDeviceAc = dialog4ConnectShowing != null
            recyclerDialog()
            SafeLog.i(TAG) { "bleConnectStatus() afterConnectSuc2BindSubDeviceAc = $afterConnectSuc2BindSubDeviceAc" }
            if (afterConnectSuc2BindSubDeviceAc) {
                viewModel.deviceInfo?.run {
                    Ac4BindSubDevice.jump2BindSubDeviceAc(this@Ac4Detail, this, adapter.itemCount, H5043Cons.sub_device_type_h5058)
                }
            }
            return
        }
        if (connectStatus == AbsViewMode4Op.connect_status_fail) {
            if (BleController.getInstance().isBlueToothOpen) {
                dialog4ConnectShowing?.connectStatusListener?.invoke(Dialog4ConnectShowing.ui_type_connected_fail)
            } else {
                recyclerDialog()
            }
        }
    }

    private fun showHint4FirstIntoAdjustAc(sku: String, device: String) {
        SafeLog.i(TAG) { "showHint4FirstIntoAdjustAc()" }
        Config.makeConfig().hadHint4FirstIntoAdjustAc(sku, device)
        when (sku) {
            H5043Cons.H5043 -> {
                val hintStr = ResUtil.getString(R.string.h5043_hint_4_first_into_adjust) + "\n\n" + ResUtil.getString(R.string.h5043_hint_4_sub_device_flashing)
                ImageHintDialogV2.createDialog4Gif(
                    this,
                    ResUtil.getString(R.string.b2light_hint_title),
                    hintStr,
                    com.govee.h5043.R.drawable.h5043_popup_tishi,
                    ResUtil.getString(R.string.hint_done_got_it),
                    true
                ).doneEnableDelay(3).show()
            }

            H5043Cons.H5044 -> {
                val hintStr = ResUtil.getString(R.string.h5059_text_4_first_add_remind)
                ImageHintDialogV2.createDialog(
                    this,
                    ResUtil.getString(R.string.b2light_hint_title),
                    hintStr,
                    com.govee.h5043.R.mipmap.h5044_popup_tishi,
                    ResUtil.getString(R.string.hint_done_got_it),
                    true
                ).doneEnableDelay(3).show()
            }

            else -> {
            }
        }
    }

    private fun ui(uiType: Int) {
        SafeLog.i(TAG) { "ui() uiType = $uiType" }
        viewBinding.run {
            when (uiType) {
                Vm4GwDetail.ui_type_loading -> {
                    makeMarginBottom(subDeviceList, 0)
                    subDeviceList.visibleByBoolean(true)
                    subDeviceList.showLoadingUI()
                    tv4BtnAdd.visibleByBoolean(false)
                    tv4TipsBind.visibleByBoolean(false)
                    tv4Tips.visibleByBoolean(false)
                    tv4EmptyHint.visibleByBoolean(false)
                }

                Vm4GwDetail.ui_type_fail -> {
                    makeMarginBottom(subDeviceList, 0)
                    subDeviceList.visibleByBoolean(true)
                    subDeviceList.loadDataFinish(isSus = false, hasData = false, noMoreData = false)
                    tv4BtnAdd.visibleByBoolean(false)
                    tv4TipsBind.visibleByBoolean(false)
                    tv4Tips.visibleByBoolean(false)
                    tv4EmptyHint.visibleByBoolean(false)
                }

                Vm4GwDetail.ui_type_suc -> {
                    makeMarginBottom(subDeviceList, 122.dp4Int)
                    subDeviceList.visibleByBoolean(true)
                    subDeviceList.loadDataFinish(isSus = true, hasData = true, noMoreData = false)
                    tv4BtnAdd.visibleByBoolean(true)
                    tv4TipsBind.visibleByBoolean(true)
                    tv4Tips.visibleByBoolean(true)
                    tv4EmptyHint.visibleByBoolean(false)
                }

                Vm4GwDetail.ui_type_suc_empty -> {
                    makeMarginBottom(subDeviceList, 0)
                    subDeviceList.visibleByBoolean(false)
                    tv4EmptyHint.visibleByBoolean(true)
                    tv4BtnAdd.visibleByBoolean(true)
                    tv4TipsBind.visibleByBoolean(true)
                    tv4Tips.visibleByBoolean(false)
                }
            }
        }
    }

    private fun makeMarginBottom(view: View, marginBottom: Int) {
        (view.layoutParams as? ConstraintLayout.LayoutParams)?.run {
            this.bottomMargin = marginBottom
            view.layoutParams = this
        }
    }

    override fun onBtPerGrantedOver() {
        SafeLog.i(TAG) { "onBtPerGrantedOver() " }
        viewModel.tryConnect()
    }

    override fun getAcContentRootViewId(): Int = com.govee.h5043.R.id.ac_container

    override fun adapterInsetViewId(): Int = com.govee.h5043.R.id.topFlag

    override fun layoutId(): Int = com.govee.h5043.R.layout.h5043_ac_4_adjust

    override fun onResume() {
        super.onResume()
        if (backFromBleThDetail) {
            backFromBleThDetail = false
            viewModel.reConnectAndScanSubBle()
        }
    }

    override fun onBackPressed() {
        recyclerDialog()
        viewModel.deviceInfo?.run {
            if (viewModel.isBleConnectSuc()) {
                EventBleTimeMillsUpdate.sendEventBleTimeMillsUpdate(sku, device)
            }
        }
        adapter.stopAllAnim()
        super.onBackPressed()
    }

    override fun onDestroy() {
        adapter.release()
        super.onDestroy()
    }
}