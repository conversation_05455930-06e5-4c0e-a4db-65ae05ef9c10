package com.govee.h5043.adjust.gw

import android.content.Context
import android.os.Bundle
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.govee.base2home.Constant
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.custom.DragSortRecyclerView
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.event.Event4RefreshDevListFinish
import com.govee.base2home.h5.WebActivity
import com.govee.base2home.main.AbsDevice
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.main.choose.BaseBleDeviceChooseActivity
import com.govee.base2home.push.EventDeviceListFresh
import com.govee.base2home.util.CaughtRunnable
import com.govee.base2kt.ac.AbsAc
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.toast
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.deviceitem.Ext4Gw
import com.govee.ble.BleController
import com.govee.ble.event.EventBleBroadcastListenerTrigger
import com.govee.h5043.databinding.H5044Ac4AddH5112SubDeviceBinding
import com.govee.h5151.ConsV1
import com.govee.h5151.gw.Event4AddSubToAccount.Companion.sendEvent
import com.govee.h5151.gw.Event4BindSubDeviceResult
import com.govee.h5151.gw.addSubDevice.AddedSubDeviceAdapter
import com.govee.h5151.gw.addSubDevice.WaitAddSubDeviceAdapter
import com.govee.mvvm.network.NetworkUtil
import com.govee.ui.R
import com.govee.ui.dialog.ConfirmDialogV1
import com.govee.ui.dialog.HintDialog1
import com.govee.util.recordUseCount
import com.ihoment.base2app.infra.SafeLog.Companion.e
import com.ihoment.base2app.infra.SafeLog.Companion.i
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.NetUtil
import com.ihoment.base2app.util.ResUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2025/5/26
 * @description H5044添加H5112子设备的页面
 */
class Ac4BindH5112SubDevice : AbsAc<H5044Ac4AddH5112SubDeviceBinding>() {

    companion object {
        /**
         * 跳转到添加子设备提示页
         */
        fun jump2AddSubDeviceAc(context: Context?, sku: String?, device: String?, bleSv: String?, bleHv: String?) {
            val bundle = Bundle()
            bundle.putString(Constant.intent_ac_key_sku, sku)
            bundle.putString(Constant.intent_ac_key_device, device)
            bundle.putString(ConsV1.intent_ac_adjust_versionSoft, bleSv)
            bundle.putString(ConsV1.intent_ac_adjust_versionHard, bleHv)
            JumpUtil.jumpWithBundle(context, Ac4BindH5112SubDevice::class.java, bundle)
        }

        private const val ui_type_loading = 101
        private const val ui_type_fail = 102
        private const val ui_type_suc = 103
    }

    //待添加的设备列表相关
    private var mWaitAddAdapter: WaitAddSubDeviceAdapter? = null
    private var mWaitAddDevicesList: MutableList<AbsDevice> = arrayListOf()

    //已添加的设备列表相关
    private var mAddedAdapter: AddedSubDeviceAdapter? = null
    private val mAddedDevicesList: MutableList<AbsDevice> = arrayListOf()

    private var sku = ""
    private var device = ""
    private var bleSv: String? = ""
    private var bleHv: String? = ""

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val intent = intent
        sku = intent.getStringExtra(Constant.intent_ac_key_sku) ?: ""
        device = intent.getStringExtra(Constant.intent_ac_key_device) ?: ""
        bleSv = intent.getStringExtra(ConsV1.intent_ac_adjust_versionSoft)
        bleHv = intent.getStringExtra(ConsV1.intent_ac_adjust_versionHard)
        initView()
        initListener()
        refreshDeviceList()
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
    }

    private fun initListener() {
        viewBinding.tvHowAddSubDevice.clickDelay {
            //登录，H5044绑定H5112介绍的H5页面
            val url = Constant.getH5RemoteControlUrl(sku)
            WebActivity.jump2WebAc(this, ResUtil.getString(R.string.how_to_add_sub_device), url)
        }
        viewBinding.tvToAddNew.clickDelay {
            sendEvent(true)
            BleController.getInstance().disconnectBleAndNotify()
            toBleScanAc()
        }
        viewBinding.btnBack.clickDelay {
            onBackPressed()
        }
    }

    private var isBackFromAddNew = false

    override fun onResume() {
        super.onResume()
        if (isBackFromAddNew) {
            isBackFromAddNew = false
            sendEvent(false)
            refreshDeviceList()
        }
    }

    private fun initView() {
        viewBinding.tvAddedTag.text = getString(R.string.widget_added_devices)
        viewBinding.tvTypeTag.text = getString(R.string.widget_available_devices)
        viewBinding.netFailFresh.setListener {
            if (!NetUtil.isNetworkAvailable(this)) {
                toast(R.string.network_anomaly)
                return@setListener
            }
            refreshDeviceList()
        }
        initAddedRecyclerView()
        initWaitAddRecyclerView()
    }

    /**
     * 初始化待添加的列表
     */
    private fun initWaitAddRecyclerView() {
        mWaitAddAdapter = WaitAddSubDeviceAdapter(true, object :
            WaitAddSubDeviceAdapter.OnEventListener {
            override fun onClickAdd(itemModel: AbsDevice): Boolean {
                //H5044支持添加32个温湿度计设备(H5112+类H5151网关支持的子设备)
                var h5151SubBleThNum = 0
                for (absDevice in DeviceListConfig.read().allDeviceList) {
                    if (Constant4L5.isH5151SubSku(absDevice.sku)) {
                        JsonUtil.fromJson(absDevice.deviceExt.deviceSettings, Ext4Gw::class.java)?.gatewayInfo?.let {
                            if ((sku + "_" + device) == it.key()) {
                                h5151SubBleThNum++
                            }
                        }
                    }
                }
                if ((mAddedDevicesList.size + h5151SubBleThNum) >= ThConsV1.MAX_NUM_4_H5044_BIND_TH_SUB) {
                    HintDialog1.showHintDialog1(this@Ac4BindH5112SubDevice, ResUtil.getString(R.string.h5044_text_4_max_th_add_remind), ResUtil.getString(R.string.hint_done_got_it))
                    //统计
                    recordUseCount(Constant4L5.H5044, ParamFixedValue.th_show_over_limit_dialog)
                    return false
                }
                showLoading()
                Event4H5044ToBindH5112.sendEvent(sku, device, itemModel)
                return false
            }

            override fun onClickItem() {}
        })
        val llm = LinearLayoutManager(this)
        viewBinding.rcDeviceScenes.layoutManager = llm
        viewBinding.rcDeviceScenes.adapter = mWaitAddAdapter
    }

    /**
     * 初始化已添加的列表
     */
    private fun initAddedRecyclerView() {
        mAddedAdapter = AddedSubDeviceAdapter(false, object :
            AddedSubDeviceAdapter.OnEventListener {
            override fun onDelete(absDaevice: AbsDevice) {
                val content = ResUtil.getString(R.string.h5112_text_4_delete_sub_remind)
                val cancelText = ResUtil.getString(R.string.cancel)
                val confirmText = ResUtil.getString(R.string.confirm)
                ConfirmDialogV1.showConfirmDialog(this@Ac4BindH5112SubDevice, "", content, cancelText, confirmText) {
                    showLoading()
                    Event4DeleteH5112FromH5044.sendEvent(sku, device, absDaevice)
                }
            }

            override fun onAddedNumChanged(addedNum: Int) {
                val addedNumText = ""
                viewBinding.tvAddedNum.text = addedNumText
                viewBinding.rcAddedDevicesScenes.setEnableDragNum(addedNum)
            }
        })
        val gridLayoutManager = GridLayoutManager(this, 4)
        viewBinding.rcAddedDevicesScenes.layoutManager = gridLayoutManager
        viewBinding.rcAddedDevicesScenes.setOnDragListener(object :
            DragSortRecyclerView.OnDragListener {
            override fun onStart() {}

            override fun onStop() {}
        })
        viewBinding.rcAddedDevicesScenes.setItemType(1)
        viewBinding.rcAddedDevicesScenes.setAdapter(mAddedAdapter)
    }

    private fun notifyChangedAdded() {
        viewBinding.tvNoDevice.visibility = if (mAddedDevicesList.isEmpty()) View.VISIBLE else View.GONE
        mAddedAdapter?.setNewData(mAddedDevicesList)
    }

    private fun updateUI(uiType: Int) {
        when (uiType) {
            ui_type_loading -> viewBinding.netFailFresh.beLoading()
            ui_type_fail -> viewBinding.netFailFresh.beFail()
            ui_type_suc -> viewBinding.netFailFresh.beHide()
            else -> {}
        }
    }

    private fun refreshDeviceList() {
        if (NetworkUtil.isNetworkAvailable(this)) {
            updateUI(ui_type_suc)
            mAddedDevicesList.clear()
            mWaitAddDevicesList.clear()
            DeviceListConfig.read().allDeviceList.let {
                it.forEach { absDevice ->
                    if (Constant4L5.H5112 == absDevice.sku) {
                        JsonUtil.fromJson(absDevice.deviceExt.deviceSettings, Ext4Gw::class.java)?.gatewayInfo?.let { gwInfo ->
                            //已绑定的H5112设备
                            if ((sku + "_" + device) == gwInfo.key()) {
                                mAddedDevicesList.add(absDevice)
                            } else {
                                //未绑定在该H5044下的H5112设备
                                mWaitAddDevicesList.add(absDevice)
                            }
                        } ?: run {
                            //未绑定的H5112设备
                            mWaitAddDevicesList.add(absDevice)
                        }
                    }
                }
                //刷新已添加列表
                mAddedAdapter?.setNewData(mAddedDevicesList)
                notifyChangedAdded()
                //刷新待添加列表
                mWaitAddAdapter?.setNewData(mWaitAddDevicesList)
            }
        } else {
            updateUI(ui_type_fail)
        }
    }

    private fun toBleScanAc() {
        val bundle = Bundle()
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG, true)
        bundle.putString(BaseBleDeviceChooseActivity.INTENT_KEY_SKU_NAME, Constant4L5.H5112)
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG_HINT, true)
        bundle.putInt(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG_HINT_DES, R.string.h5074_more_device_5074_hint_des)
        bundle.putInt(BaseBleDeviceChooseActivity.INTENT_KEY_DEVICE_FLASHING_RSSI_VALUE, ConsV1.ble_ssid_compare_value)
        JumpUtil.jump(this, BaseBleDeviceChooseActivity::class.java, bundle)
        //进入蓝牙扫描界面；需要关闭主界面蓝牙广播
        EventBleBroadcastListenerTrigger.sendEventBleBroadcastListenerTrigger(false)
        //修改标识位
        isBackFromAddNew = true
    }

    /**
     * 载入设备列表的控制变量
     */
    private var isLoadingDeviceList = false

    /**
     * 绑定、解绑都通过此事件回调
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4BindSubDeviceResult(event: Event4BindSubDeviceResult) {
        val same = event.isSame(sku, device)
        val result = event.result
        if (!same) return
        if (result) {
            i(TAG) { "Ac4BindH5112SubDevice--onEvent4BindSubDeviceResult-->绑定或解绑成功，更新设备列表..." }
            //刷新首页设备列表(添加新设备，添加/删除子设备都会引起设备列表对应子设备的相关数据变化)
            EventDeviceListFresh.sendEventDeviceListFresh(true)
            isLoadingDeviceList = true
        } else {
            e(TAG) { "Ac4BindH5112SubDevice--onEvent4BindSubDeviceResult-->绑定或解绑失败!" }
            hideLoading()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4DeviceListRefresh(event: Event4RefreshDevListFinish) {
        if (!isLoadingDeviceList) {
            return
        }
        isLoadingDeviceList = false
        window.decorView.postDelayed(object : CaughtRunnable() {
            override fun runSafe() {
                i(TAG) { "Ac4BindH5112SubDevice--onEvent4DeviceListRefresh-->设备列表更新完毕..." }
                hideLoading()
                refreshDeviceList()
            }
        }, 1000)
    }

    private fun showLoading(delayTimeMills: Long = 60 * 1000L) {
        LoadingDialog.createDialog(this, com.ihoment.base2app.R.style.DialogDim, delayTimeMills).setEventKey(TAG).show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(TAG)
    }

    override fun statusBarLightMode(): Boolean {
        return false
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.h5043.R.id.ac_container
    }

    override fun adapterInsetViewId(): Int {
        return com.govee.h5043.R.id.top_flag
    }

    override fun layoutId(): Int {
        return com.govee.h5043.R.layout.h5044_ac_4_add_h5112_sub_device
    }

    override fun onDestroy() {
        EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
        super.onDestroy()
    }
}