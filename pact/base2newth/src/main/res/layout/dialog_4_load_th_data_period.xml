<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="MissingDefaultResource">

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/component_bg_style_2">

        <TextView
            android:id="@+id/tv_title_4_th_ldpd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="35.5dp"
            android:gravity="center"
            android:text="@string/b2light_hint_title"
            android:textColor="@color/font_style_67_1_textColor"
            android:textSize="18sp"
            android:textStyle="bold"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_content_4_th_ldpd"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:gravity="center"
            android:paddingHorizontal="32.5dp"
            android:textColor="@color/font_style_71_3_textColor"
            android:textSize="13sp"
            app:layout_constraintTop_toBottomOf="@+id/tv_title_4_th_ldpd" />

        <View
            android:id="@+id/v_wp_center_bg"
            android:layout_width="match_parent"
            android:layout_height="37dp"
            android:layout_marginHorizontal="32.5dp"
            android:background="@drawable/h512x_selected_time_bg"
            app:layout_constraintBottom_toBottomOf="@+id/wp_recent_period_4_th_ldpd"
            app:layout_constraintTop_toTopOf="@+id/wp_recent_period_4_th_ldpd" />

        <com.aigestudio.wheelpicker.WheelPicker
            android:id="@+id/wp_recent_period_4_th_ldpd"
            android:layout_width="match_parent"
            android:layout_height="185dp"
            android:layout_marginTop="23.5dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_content_4_th_ldpd"
            app:wheel_cyclic="false"
            app:wheel_indicator_color="@color/font_style_36_3_textColor"
            app:wheel_indicator_size="0.5dp"
            app:wheel_item_align="center"
            app:wheel_item_text_color="@color/font_style_36_2_textColor"
            app:wheel_item_text_size="15sp"
            app:wheel_selected_item_position="3"
            app:wheel_selected_item_text_color="@color/font_style_36_3_textColor"
            app:wheel_visible_item_count="5" />

        <TextView
            android:id="@+id/tv_cancel_4_th_ldpd"
            android:layout_width="148dp"
            android:layout_height="55dp"
            android:layout_marginStart="24dp"
            android:layout_marginTop="49.5dp"
            android:background="@drawable/component_btn_style_5_1"
            android:gravity="center"
            android:paddingBottom="10dp"
            android:text="@string/cancel"
            android:textColor="@color/ui_btn_style_5_1_text_color"
            android:textSize="@dimen/ui_btn_style_5_1_text_size"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/wp_recent_period_4_th_ldpd" />

        <TextView
            android:id="@+id/tv_sure_4_th_ldpd"
            android:layout_width="148dp"
            android:layout_height="55dp"
            android:layout_marginEnd="24dp"
            android:background="@drawable/component_btn_style_3_1"
            android:gravity="center"
            android:paddingBottom="10dp"
            android:text="@string/confirm"
            android:textColor="@color/ui_btn_style_3_1_text_color"
            android:textSize="@dimen/ui_btn_style_3_1_text_size"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_cancel_4_th_ldpd" />

        <View
            android:layout_width="match_parent"
            android:layout_height="25.5dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_cancel_4_th_ldpd" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>