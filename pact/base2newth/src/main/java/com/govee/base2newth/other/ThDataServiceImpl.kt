package com.govee.base2newth.other

import com.alibaba.android.arouter.facade.annotation.Route
import com.govee.base2home.constant.PathBaseHome
import com.govee.base2home.storage.ThDataService
import com.govee.base2newth.ThConsV1
import com.ihoment.base2app.infra.SafeLog

/**
 * author  : sinrow
 * time    : 2024/6/13
 * version : 1.0.0
 * desc    : 温湿度数据服务实现类，提供温湿度设备数据获取和清理功能。
 */
@Route(path = PathBaseHome.URL_TH_DATA_SERVICE)
class ThDataServiceImpl : ThDataService {
    private val TAG = "ThDataServiceImpl"

    override fun getThDataCacheMemory(sku: String, device: String): Long {
        return try {
            ClearThDataUtils.getThDataCacheMemory4All(sku, device)
        } catch (e: Exception) {
            SafeLog.e(TAG) { "getThDataCacheMemory4All() ${e.message}" }
            0L
        }
    }

    override fun clearByPeriodType(sku: String, device: String, periodType: Int) {
        try {
            if (periodType == ThConsV1.CLEAR) {
                ClearThDataUtils.clearAllData(sku, device)
            } else {
                ClearThDataUtils.clearByPeriodType(sku, device, periodType)
            }
        } catch (e: Exception) {
            // 记录错误但不抛出异常，避免影响主流程
            SafeLog.e(TAG) { "clearByPeriodType() ${e.message}" }
        }
    }

    override fun getThAllDataSize(): Long {
        return try {
            ClearThDataUtils.getThAllDataSize()
        } catch (e: Exception) {
            // 记录错误但不抛出异常，避免影响主流程
            SafeLog.e(TAG) { "getThAllDataSize() ${e.message}" }
            0L
        }
    }

    override fun clearNonThData() {
        try {
            ClearThDataUtils.clearNonThData()
            // TODO: @陈信儒 2025/7/21 @尹江兵 补充数据库迁移后，非温湿度计的清理方法。
        } catch (e: Exception) {
            // 记录错误但不抛出异常，避免影响主流程
            SafeLog.e(TAG) { "clearNonThData() ${e.message}" }
        }
    }

    override fun init(context: android.content.Context) {
        // ARouter 初始化回调，这里不需要特殊处理
    }
}