package com.govee.base2newth.other

import android.annotation.SuppressLint
import androidx.annotation.WorkerThread
import com.govee.base2home.Constant4L5
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.data.DataConfig
import com.govee.base2newth.db.DbController
import com.ihoment.base2app.infra.SafeLog

/**
 * <AUTHOR>
 * @date created on 2023/9/20
 * @description 温湿度计清除数据库缓存数据的对象类
 */
object ClearThDataUtils {

    const val REFRESH_TH_WAIT_TIME = 1000L

    /**
     * 按保留时间段清楚数据
     * 备注：remove数据库里的数据仅能清除数据，不能释放相应的存储控件。需要删除对应的文件，然后重建
     */
    @WorkerThread
    fun clearByPeriodType(sku: String, device: String, periodType: Int) {
        val lastTimeToRemain = getPeriodTypeStartTime(sku, device, periodType)
        if (lastTimeToRemain > 0) {
            DbController.clearThCache(sku, device, lastTimeToRemain)
            Event4HasClearCache.sendEvent(Event4HasClearCache.DELETE_OTHER)
        }
    }

    /**
     * 按保留时间段清楚数据
     * 备注:H5106、H5140、H5112使用
     */
    @WorkerThread
    fun clearByPeriodType4Thp(sku: String, device: String, periodType: Int) {
        val lastTimeToRemain = getPeriodTypeStartTime(sku, device, periodType)
        if (lastTimeToRemain > 0) {
            DbController.clearThpCache(sku, device, lastTimeToRemain)
            Event4HasClearCache.sendEvent(Event4HasClearCache.DELETE_OTHER)
        }
    }

    @WorkerThread
    private fun getPeriodTypeStartTime(sku: String, device: String, periodType: Int): Long {
        var lastTimeToRemain = -1L
        val currTime = System.currentTimeMillis()
        when (periodType) {
            ThConsV1.REMIND_7_DAYS -> {
                lastTimeToRemain = currTime - 7 * 24 * 60 * 60 * 1000L
                //记录该值
                Config4LthdInfoKey.getConfig().saveSelPeriodInfo(sku, device, Pair(false, lastTimeToRemain))
            }

            ThConsV1.REMIND_1_MONTH -> {
                lastTimeToRemain = currTime - 30 * 24 * 60 * 60 * 1000L
                Config4LthdInfoKey.getConfig().saveSelPeriodInfo(sku, device, Pair(false, lastTimeToRemain))
            }

            ThConsV1.REMIND_3_MONTHS -> {
                lastTimeToRemain = currTime - 90 * 24 * 60 * 60 * 1000L
                Config4LthdInfoKey.getConfig().saveSelPeriodInfo(sku, device, Pair(false, lastTimeToRemain))
            }

            ThConsV1.REMIND_HALF_YEAR -> {
                lastTimeToRemain = currTime - 183 * 24 * 60 * 60 * 1000L
                Config4LthdInfoKey.getConfig().saveSelPeriodInfo(sku, device, Pair(false, lastTimeToRemain))
            }

            else -> {}
        }
        //更新已加载到本地的数据的起、止时间点
        DataConfig.read().getHasLoadedTr(sku, device)?.let {
            DataConfig.read().updateHasLoadedTr(sku, device, Pair(lastTimeToRemain, it.second))
        }
        return lastTimeToRemain
    }

    /**
     * 获取温湿度图表数据（tem+hum）所占用的存储空间
     */
    @WorkerThread
    fun getThDataCacheMemory(sku: String, device: String): String {
        var memory4Bytes = 0L
        val allThDataNum = DbController.queryAllDataNum(sku, device)
        if (allThDataNum > 0L) {
            DbController.getInstance.getBoxStore(sku, device)?.run {
                memory4Bytes = this.dbSizeOnDisk
            }
        }
        return calculateMemory(memory4Bytes)
    }

    /**
     * 获取图表数（tem+hum+pm2.5）据所占用的存储空间
     */
    @WorkerThread
    fun getThpDataCacheMemory(sku: String, device: String): String {
        var memory4Bytes = 0L
        val allThDataNum = DbController.queryAllThpDataNum(sku, device)
        if (allThDataNum > 0L) {
            DbController.getInstance.getBoxStore(sku, device)?.run {
                memory4Bytes = this.dbSizeOnDisk
            }
        }
        return calculateMemory(memory4Bytes)
    }

    @SuppressLint("DefaultLocale")
    private fun calculateMemory(memory4Bytes: Long): String {
        return if (memory4Bytes < 1024) {
            "$memory4Bytes B"
        } else if (memory4Bytes < 1024 * 1024) {
            val memory4Kb = memory4Bytes / 1024.0f
            "${String.format("%.1f", memory4Kb)} KB"
        } else if (memory4Bytes < 1024 * 1024 * 1024) {
            val memory4Mb = memory4Bytes / (1024 * 1024.0f)
            "${String.format("%.2f", memory4Mb)} MB"
        } else {
            ""
        }
    }

    /**
     * 清除设备所有数据；不包括 lock.mdb 文件
     */
    fun clearAllData(sku: String, device: String) {
        DbController.delete(sku, device, true)
        DataConfig.read().updateLastDataClearTime(sku, device, System.currentTimeMillis())
        Event4HasClearCache.sendEvent(Event4HasClearCache.DELETE_ALL)
    }

    /**
     * 清除非温湿度计数据（只要不是以支持的 sku 开头的 dbName 都清理）
     */
    fun clearNonThData() {
        val supportAllThList = Constant4L5.supportAllThList()
        val supportSkuPrefixes = supportAllThList.map { it + "_" }
        DbController.getInstance.boxStoreHashMap.keys.filter { dbName ->
            supportSkuPrefixes.none { dbName.startsWith(it) }
        }.forEach { dbName ->
            try {
                val boxStore = DbController.getInstance.boxStoreHashMap[dbName]
                boxStore?.run {
                    close()
                    if (isClosed) {
                        deleteAllFiles()
                        SafeLog.i("ClearThDataUtils") { "已彻底删除非温湿度计 boxStore 文件, dbName=$dbName" }
                    } else {
                        SafeLog.e("ClearThDataUtils") { "boxStore 未关闭，无法删除文件, dbName=$dbName" }
                    }
                }
                DbController.getInstance.boxStoreHashMap.remove(dbName)
            } catch (e: Exception) {
                SafeLog.e("ClearThDataUtils") { "清除非温湿度计数据异常, dbName=$dbName, err=${e.message}" }
            }
        }
    }

    fun getThAllDataSize(): Long {
        SafeLog.i("ClearThDataUtils") { "开始计算所有温湿度设备数据大小" }

        // 只计算温湿度设备数据大小，包含 data.mdb 和 lock.mdb
        val supportAllThList = Constant4L5.supportAllThList()
        val supportSkuPrefixes = supportAllThList.map { it + "_" }

        SafeLog.i("ClearThDataUtils") { "支持的温湿度设备前缀: $supportSkuPrefixes" }
        SafeLog.i("ClearThDataUtils") { "当前数据库总数: ${DbController.getInstance.boxStoreHashMap.size}" }

        val thDatabases = DbController.getInstance.boxStoreHashMap.filter { (dbName, _) ->
            // 只包含以支持的温湿度设备 sku 开头的 dbName
            supportSkuPrefixes.any { dbName.startsWith(it) }
        }

        SafeLog.i("ClearThDataUtils") { "筛选出的温湿度数据库: ${thDatabases.keys}" }

        val totalSize = thDatabases.map { (dbName, boxStore) ->
            getCompleteDbSize(dbName, boxStore)
        }.sum()

        SafeLog.i("ClearThDataUtils") { "所有温湿度设备数据大小计算完成: $totalSize bytes" }
        return totalSize
    }

    /**
     * 获取完整的数据库文件大小，通过计算整个数据库目录的所有文件
     * 这样无论 ObjectBox 将来添加什么新文件格式都能准确计算
     */
    private fun getCompleteDbSize(dbName: String, boxStore: io.objectbox.BoxStore): Long {
        return try {
            // 计算整个数据库目录的大小（最准确、最通用）
            val context = com.ihoment.base2app.BaseApplication.getContext()
            val objectBoxDir = java.io.File(context.filesDir, "objectbox")

            if (objectBoxDir.exists()) {
                // ObjectBox 为每个数据库创建一个以数据库名称命名的目录
                val dbDir = java.io.File(objectBoxDir, dbName)

                if (dbDir.exists() && dbDir.isDirectory) {
                    val totalSize = calculateDirectorySize(dbDir)

                    if (totalSize > 0) {
                        SafeLog.i("ClearThDataUtils") { "数据库 $dbName 目录完整大小: $totalSize bytes (包含所有数据库文件)" }

                        // 记录目录中的文件详情（用于调试）
                        val files = dbDir.listFiles()
                        files?.forEach { file ->
                            if (file.isFile) {
                                SafeLog.i("ClearThDataUtils") { "  - ${file.name}: ${file.length()} bytes" }
                            }
                        }

                        return totalSize
                    }
                }
            }

            // fallback 尝试使用 BoxStore.sizeOnDisk()
            val sizeOnDiskResult = try {
                val sizeOnDisk = boxStore.sizeOnDisk()
                SafeLog.i("ClearThDataUtils") {
                    "数据库 $dbName sizeOnDisk: $sizeOnDisk bytes"
                }
                sizeOnDisk
            } catch (e: Exception) {
                SafeLog.w("ClearThDataUtils") { "数据库 $dbName sizeOnDisk() 失败: ${e.message}" }
                0L
            }

            // fallback 使用 BoxStore.dbSize（只包含 data.mdb）
            val dbSizeResult = try {
                val dbSize = boxStore.dbSize
                SafeLog.i("ClearThDataUtils") { "数据库 $dbName dbSize: $dbSize bytes (仅 data.mdb)" }
                dbSize
            } catch (e: Exception) {
                SafeLog.w("ClearThDataUtils") { "数据库 $dbName dbSize 获取失败: ${e.message}" }
                0L
            }

            // 返回最大的那个值（更可能是完整大小）
            val finalSize = maxOf(sizeOnDiskResult, dbSizeResult)
            SafeLog.i("ClearThDataUtils") { "数据库 $dbName 最终大小: $finalSize bytes (fallback)" }
            return finalSize

        } catch (e: Exception) {
            SafeLog.e("ClearThDataUtils") { "getCompleteDbSize error for $dbName: ${e.message}" }
            // 最终 fallback
            boxStore.dbSize
        }
    }

    /**
     * 递归计算目录大小（包含所有子文件和子目录）
     */
    private fun calculateDirectorySize(directory: java.io.File): Long {
        var size = 0L
        try {
            if (directory.exists()) {
                if (directory.isFile) {
                    size = directory.length()
                } else if (directory.isDirectory) {
                    val files = directory.listFiles()
                    if (files != null) {
                        for (file in files) {
                            size += calculateDirectorySize(file)
                        }
                    }
                }
            }
        } catch (e: Exception) {
            SafeLog.e("ClearThDataUtils") { "calculateDirectorySize error for ${directory.absolutePath}: ${e.message}" }
        }
        return size
    }

    /**
     * 获取温湿度图表数据,先获取：查询指定设备的总图表数据个数，如果为0，则再查询一下，是否是 带pm2.5的对象TemHumPm的数据处理相关
     */
    @WorkerThread
    fun getThDataCacheMemory4All(sku: String, device: String): String {
        var memory4Bytes = 0L
        var allThDataNum = DbController.queryAllDataNum(sku, device)
        if (allThDataNum == 0L) {
            allThDataNum = DbController.queryAllThpDataNum(sku, device)
        }
        if (allThDataNum > 0L) {
            DbController.getInstance.getBoxStore(sku, device)?.run {
                memory4Bytes = this.dbSizeOnDisk
            }
        }
        return calculateMemory(memory4Bytes)
    }
}