package com.govee.base2newth.db;

import android.content.Context;
import android.text.TextUtils;

import com.govee.base2home.Constant4L5;
import com.govee.base2newth.ThConsV1;
import com.govee.base2newth.ThUtil;
import com.govee.base2newth.chart.TimeUtil;
import com.govee.base2newth.data.DataTimeSet;
import com.govee.base2newth.other.Config4LthdInfoKey;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.infra.SafeLog;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;

import io.objectbox.Box;
import io.objectbox.BoxStore;
import io.objectbox.internal.NativeLibraryLoader;
import io.objectbox.query.Query;

/**
 * Create by xieyingwu on 2020/3/17
 * 数据操作类$
 */
public class DbController {
    private static final String TAG = "DbController";

    private DbController() {
    }

    private static class Builder {
        private static final DbController instance = new DbController();
    }

    public static DbController getInstance = Builder.instance;

    private final HashMap<String, BoxStore> boxStoreHashMap = new HashMap<>();

    /**
     * 初始化数据库
     */
    public void initDb(Context context) {
        DbConfig dbConfig = DbConfig.read();
        HashSet<String> dbNameList = dbConfig.getDbNameList();
        //初始化so加载
        try {
            NativeLibraryLoader.ensureLoaded();
        } catch (LinkageError | Exception e) {
            SafeLog.Companion.e(TAG, () -> "DbController--NativeLibraryLoader.ensureLoaded()-->" + e.getMessage());
        }
        //初始化数据库
        for (String dbName : dbNameList) {
            BoxStore boxStore = null;
            try {
                boxStore = MyObjectBox.builder().name(dbName).androidContext(context).build();
            } catch (LinkageError | Exception e) {
                SafeLog.Companion.e(TAG, () -> "DbController--initDb-->" + e.getMessage());
            }
            if (boxStore != null) {
                boxStoreHashMap.put(dbName, boxStore);
            }
        }
    }

    /**
     * 创建数据库表
     *
     * @param sku    sku
     * @param device 设备id
     */
    public void createDbTable(String sku, String device) {
        String dbName = getDbName(sku, device);
        if (TextUtils.isEmpty(dbName)) {
            return;
        }
        boolean containsKey = boxStoreHashMap.containsKey(dbName);
        if (!containsKey) {
            BoxStore boxStore = null;
            try {
                boxStore = MyObjectBox.builder().name(dbName).androidContext(BaseApplication.getContext()).build();
            } catch (LinkageError | Exception e) {
                SafeLog.Companion.e(TAG, () -> "DbController--createDbTable-->" + e.getMessage());
            }
            if (boxStore != null) {
                boxStoreHashMap.put(dbName, boxStore);
                //储存到dbConfig
                DbConfig.read().addDbName(dbName);
            }
        }
    }

    public static String getDbName(String sku, String device) {
        if (TextUtils.isEmpty(sku) || TextUtils.isEmpty(device)) {
            return "";
        }
        //50系列的设备在原module有了数据库表，为了防止数据库重名而导致崩溃，创建新表的名称加上后缀"_50"以区分
        if (Constant4L5.H5072.equals(sku) || Constant4L5.H5075.equals(sku) || Constant4L5.H5074.equals(sku) || Constant4L5.H5052.equals(sku) || Constant4L5.H5051.equals(sku) || Constant4L5.H5053.equals(sku) || Constant4L5.H5071.equals(sku)) {
            return sku + "_" + device + "_50";
        }
        return sku + "_" + device;
    }

    public BoxStore getBoxStore(String sku, String device) {
        String dbName = getDbName(sku, device);
        return boxStoreHashMap.get(dbName);
    }

    /**
     * 获取所有的数据库表
     *
     * @return
     */
    public HashMap<String, BoxStore> getBoxStoreHashMap() {
        return boxStoreHashMap;
    }

    /**
     * 删除指定设备数据
     *
     * @param sku          指定类型设备的Sku
     * @param device       设备uuid
     * @param deleteDbFile 是否需要删除数据库文件(删除设备等情况下删除缓存时，需要删除数据库文件,并且删除其对应的数据库名缓存)
     */
    public synchronized static void delete(String sku, String device, boolean deleteDbFile) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return;
        }
        boxStore.close();
        if (boxStore.isClosed()) {
            boxStore.deleteAllFiles();
            String dbName = getDbName(sku, device);
            getInstance.boxStoreHashMap.remove(dbName);
            if (deleteDbFile) {
                //删除缓存的数据库文件名
                DbConfig.read().removeDbName(dbName);
            } else {
                //重新创建新数据库
                getInstance.createDbTable(sku, device);
            }
        }
    }

    //数据具体操作相关-----------------------------------------------------------

    /**
     * 查询指定设备的总图表数据个数
     */
    public synchronized static long queryAllDataNum(String sku, String device) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return 0;
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return 0;
        }
        return temHumBox.query().build().count();
    }

    /**
     * 查询所有的有效数据集合，顺序
     *
     * @param sku    指定类型设备的Sku
     * @param device 待查询的设备uuid
     * @return List<TemHum>
     */
    public synchronized static List<TemHum> queryAllData(String sku, String device) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return new ArrayList<>();
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return new ArrayList<>();
        }
        Query<TemHum> temHumQuery = temHumBox.query().notEqual(TemHum_.from, TemHum.FROM_TYPE_INVALID).less(TemHum_.time, System.currentTimeMillis()).order(TemHum_.time).build();
        return temHumQuery.find();
    }

    /**
     * 查询所有的有效数据集合，顺序
     *
     * @param sku    指定类型设备的Sku
     * @param device 待查询的设备uuid
     * @return List<TemHum>
     */
    public synchronized static List<TemHum> queryAllData(String sku, String device, long validMinTimeMills, long validMaxTimeMills) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return new ArrayList<>();
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return new ArrayList<>();
        }
        Query<TemHum> temHumQuery = temHumBox.query().notEqual(TemHum_.from, TemHum.FROM_TYPE_INVALID).greater(TemHum_.time, validMinTimeMills).less(TemHum_.time, validMaxTimeMills).order(TemHum_.time).build();
        return temHumQuery.find();
    }

    /***
     * 查询模式相关的数据
     */
    public synchronized static List<ModeGearEntity> queryAllModeData(String sku, String device, long validMinTimeMills, long validMaxTimeMills) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return new ArrayList<>();
        }
        Box<ModeGearEntity> modeGearEntityBox = boxStore.boxFor(ModeGearEntity.class);
        if (modeGearEntityBox == null) {
            return new ArrayList<>();
        }
        Query<ModeGearEntity> modeGearEntityQuery = modeGearEntityBox.query().notEqual(ModeGearEntity_.from, TemHum.FROM_TYPE_INVALID).greater(ModeGearEntity_.time, validMinTimeMills).less(ModeGearEntity_.time, validMaxTimeMills).order(ModeGearEntity_.time).build();
        return modeGearEntityQuery.find();
    }

    public synchronized static List<TemHum> queryAllDataEqual(String sku, String device, long validMinTimeMills, long validMaxTimeMills) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return new ArrayList<>();
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return new ArrayList<>();
        }
        Query<TemHum> temHumQuery = temHumBox.query().notEqual(TemHum_.from, TemHum.FROM_TYPE_INVALID).greaterOrEqual(TemHum_.time, validMinTimeMills).less(TemHum_.time, validMaxTimeMills).order(TemHum_.time).build();
        return temHumQuery.find();
    }

    /**
     * 查询最新的一条数据
     *
     * @param sku    指定类型设备的Sku
     * @param device 待查询的设备uuid
     * @return TemHum
     */
    public synchronized static TemHum queryLastData(String sku, String device) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return null;
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return null;
        }
        Query<TemHum> query = temHumBox.query().less(TemHum_.time, System.currentTimeMillis()).orderDesc(TemHum_.time).build();
        try {
            if (query == null || query.count() < 1) {
                return null;
            }
            return query.findFirst();
        } catch (Exception e) {
            SafeLog.Companion.e(TAG, () -> "DbController--queryLastData-->" + e.getMessage());
            return null;
        }
    }

    public synchronized static void insertDeviceData(String sku, String device, List<TemHum> values) {
        if (values == null || values.isEmpty()) {
            return;
        }
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return;
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return;
        }
        /*对插入的湿度数据做处理，若湿度范围大于100%，则默认显示为99.9%*/
        for (TemHum temHum : values) {
            temHum.caliHum();
        }
        /*插入数据库*/
        temHumBox.put(values);
    }

    /**
     * 插入挡位模式
     */
    public synchronized static void insertDeviceModeGearData(String sku, String device, int modeGear, long validTimeStamp) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return;
        }
        Box<ModeGearEntity> modeGearEntityBox = boxStore.boxFor(ModeGearEntity.class);
        if (modeGearEntityBox == null) {
            return;
        }
        ModeGearEntity mode = new ModeGearEntity(modeGear, validTimeStamp);
        mode.setFrom(ModeGearEntity.FROM_TYPE_DEVICE);
        /*插入数据库*/
        modeGearEntityBox.put(mode);
    }

    public synchronized static List<TemHum> getThDataFromDevice4List(String sku, String device, int dataLimit) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return null;
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return null;
        }
        return temHumBox.query().equal(TemHum_.from, TemHum.FROM_TYPE_DEVICE).build().find(0, dataLimit);
    }

    public synchronized static List<ModeGearEntity> getModeGear4List(String sku, String device, int dataLimit) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return null;
        }
        Box<ModeGearEntity> modeGearEntityBox = boxStore.boxFor(ModeGearEntity.class);
        if (modeGearEntityBox == null) {
            return null;
        }
        return modeGearEntityBox.query().equal(ModeGearEntity_.from, ModeGearEntity.FROM_TYPE_DEVICE).build().find(0, dataLimit);
    }

    public synchronized static void updateFromType2upload(String sku, String device, List<TemHum> temHums) {
        if (temHums == null || temHums.isEmpty()) {
            return;
        }
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return;
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return;
        }
        for (TemHum temHum : temHums) {
            temHum.setFrom(TemHum.FROM_TYPE_UPLOAD);
        }
        /*插入数据库*/
        temHumBox.put(temHums);
    }

    public synchronized static void updateModeGearFromType2upload(String sku, String device, List<ModeGearEntity> modeGearEntities) {
        if (modeGearEntities == null || modeGearEntities.isEmpty()) {
            return;
        }
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return;
        }
        Box<ModeGearEntity> modeGearEntityBox = boxStore.boxFor(ModeGearEntity.class);
        if (modeGearEntityBox == null) {
            return;
        }
        for (ModeGearEntity modeGear : modeGearEntities) {
            modeGear.setFrom(TemHum.FROM_TYPE_UPLOAD);
        }
        /*插入数据库*/
        modeGearEntityBox.put(modeGearEntities);
    }

    public synchronized static void updateModeGearByPull(String sku, String device, List<ModeGearEntity> modeDatas) {
        if (modeDatas == null || modeDatas.isEmpty()) {
            return;
        }
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return;
        }
        Box<ModeGearEntity> modeGearEntityBox = boxStore.boxFor(ModeGearEntity.class);
        if (modeGearEntityBox == null) {
            return;
        }
        for (ModeGearEntity modeGearEntity : modeDatas) {
            modeGearEntity.setFrom(TemHum.FROM_TYPE_UPLOAD);
        }
        /*插入数据库*/
        modeGearEntityBox.put(modeDatas);
    }

    /**
     * 插入数据库数据
     *
     * @param thDataStrSet      从服务端拉取到的形如：2345,7689,1752652393000 的数据集
     * @param validMinTimeMills 限制数据有效时间戳的最小值
     * @param needFilterHumZero 是否需要过滤湿度为0的数据
     * @return 插入结果
     */
    public synchronized static boolean insertServiceData(String sku, String device, String[] thDataStrSet, long validMinTimeMills, boolean needFilterHumZero) {
        if (thDataStrSet == null || thDataStrSet.length == 0) {
            return false;
        }
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return false;
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return false;
        }
        List<TemHum> values = new ArrayList<>();
        for (String thDataStr : thDataStrSet) {
            String[] str = thDataStr.split(",");
            if (str.length == 3) {
                int tem = ThUtil.parseTem(str[0]);
                int hum = ThUtil.parseHum(str[1]);
                long timeMills = ThUtil.parseTime(str[2]);
                //时间做整分钟处理
                timeMills -= timeMills % 60000;

                //5.9.0拉取数据做可能小于开始拉取的时间点，故小于拉取开始时间点以外的点不保存
                long minTime = Config4LthdInfoKey.Companion.getConfig().getSelPeriodStartTime(sku, device);
                if (timeMills < minTime) {
                    continue;
                }
                boolean validThData = ThUtil.isValidThData(tem, hum, timeMills);
                if (needFilterHumZero && hum == 0) {
                    validThData = false;
                }
                if (validThData && timeMills >= validMinTimeMills) {
                    TemHum temHum = new TemHum(tem, hum, timeMills, TemHum.FROM_TYPE_CLOUD);
                    /*对插入的湿度数据做处理，若湿度范围大于100%，则默认显示为99.9%*/
                    temHum.caliHum();
                    values.add(temHum);
                }
            }
        }
        if (!values.isEmpty()) {
            SafeLog.Companion.i(TAG, () -> "DbController--insertServiceData-->values.size = " + values.size());
            /*插入数据库*/
            temHumBox.put(values);
        }
        return true;
    }

    /**
     * 查询有效数据条数
     *
     * @param sku    指定类型设备的Sku
     * @param device 设备uuid
     * @return 数据条数
     */
    public synchronized static long queryValidDataLength(String sku, String device) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return -1;
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return -1;
        }
        Query<TemHum> query = temHumBox.query().notEqual(TemHum_.from, TemHum.FROM_TYPE_INVALID).less(TemHum_.time, System.currentTimeMillis()).build();

        return query.count();
    }

    /**
     * 查询最新有效数据时间
     *
     * @param sku    指定类型设备的Sku
     * @param device 设备uuid
     * @return 数据时间
     */
    public synchronized static long queryLastValidTime(String sku, String device) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return -1;
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return -1;
        }
        Query<TemHum> query = temHumBox.query().notEqual(TemHum_.from, TemHum.FROM_TYPE_INVALID).less(TemHum_.time, System.currentTimeMillis()).orderDesc(TemHum_.time).build();
        TemHum temHum = query.findFirst();
        if (temHum == null) {
            return -1;
        }
        return temHum.getTime();
    }

    /**
     * 查询最早的有效数据时间
     *
     * @param sku    指定类型设备的Sku
     * @param device 设备uuid
     * @return 数据时间
     */
    public synchronized static long queryFirstValidTime(String sku, String device) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return -1;
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return -1;
        }
        Query<TemHum> query = temHumBox.query().notEqual(TemHum_.from, TemHum.FROM_TYPE_INVALID).less(TemHum_.time, System.currentTimeMillis()).order(TemHum_.time).build();
        TemHum temHum = query.findFirst();
        if (temHum == null) {
            return -1;
        }
        return temHum.getTime();
    }

    /**
     * 查询待向设备读取的数据段
     *
     * @param lastClearDataTime 最后清理数据的时间点
     * @param minutesMax        最大拉取的数据个数(一分钟一个数据点)
     * @return 需向设备端拉取数据的时间段
     */
    public synchronized static DataTimeSet queryDataTimeSet(String sku, String device, long lastClearDataTime, int minutesMax) {
        if (lastClearDataTime >= System.currentTimeMillis()) {
            lastClearDataTime = -1;
        }
        DataTimeSet dataTimeSet = new DataTimeSet();
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return dataTimeSet;
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return dataTimeSet;
        }
        long minTimeStamp = System.currentTimeMillis() - TimeUtil.getMills(minutesMax);
        long limitTime = Config4LthdInfoKey.Companion.getConfig().getSelPeriodStartTime(sku, device);
        minTimeStamp = Math.max(minTimeStamp, lastClearDataTime);
        minTimeStamp = Math.max(minTimeStamp, limitTime);
        //倒序查询，先读取旧数据，在读取新数据
        List<TemHum> temHums = temHumBox.query().greater(TemHum_.time, minTimeStamp).less(TemHum_.time, System.currentTimeMillis()).orderDesc(TemHum_.time).build().find();
        dataTimeSet.generateDataTimeSet(temHums, Math.max(lastClearDataTime, limitTime), minutesMax);
        return dataTimeSet;
    }

    /**
     * 清除设备图表数据缓存
     *
     * @param sku           指定类型设备的Sku
     * @param device        设备uuid
     * @param dataClearTime 上次清除数据时间戳
     */
    public synchronized static void clearThCache(String sku, String device, long dataClearTime) {
        dataClearTime = Math.max(0, dataClearTime);
        BoxStore boxStore = DbController.getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return;
        }
        Box<TemHum> temHumBox = boxStore.boxFor(TemHum.class);
        if (temHumBox == null) {
            return;
        }
        TemHum allThLast = temHumBox.query().greater(TemHum_.time, 0).build().findFirst();
        //当前已有数据的最早值时间点大于要清除的时间点，则无需处理
        if (allThLast != null && allThLast.getTime() > dataClearTime) {
            return;
        }
        List<TemHum> remainThs = temHumBox.query().greater(TemHum_.time, dataClearTime).build().find();
        //删除已有文件，释放内存空间。
        delete(sku, device, false);
        //将要保留的数据保存到新的数据文件中
        insertDeviceData(sku, device, remainThs);
    }


    //-----------------------------------------带pm2.5的对象TemHumPm的数据处理相关----------------------------------------

    /**
     * 查询指定设备的总图表数据个数
     */
    public synchronized static long queryAllThpDataNum(String sku, String device) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return 0;
        }
        Box<TemHumPm> temHumBox = boxStore.boxFor(TemHumPm.class);
        if (temHumBox == null) {
            return 0;
        }
        return temHumBox.query().build().count();
    }

    /**
     * 查询所有的有效数据集合，顺序
     *
     * @param sku    指定类型设备的Sku
     * @param device 待查询的设备uuid
     * @return List<TemHum>
     */
    public synchronized static List<TemHumPm> queryAllData4Thp(String sku, String device, long validMinTimeMills, long validMaxTimeMills) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return new ArrayList<>();
        }
        Box<TemHumPm> temHumBox = boxStore.boxFor(TemHumPm.class);
        if (temHumBox == null) {
            return new ArrayList<>();
        }
        Query<TemHumPm> thpQuery = temHumBox.query().notEqual(TemHumPm_.from, TemHum.FROM_TYPE_INVALID).greater(TemHumPm_.time, validMinTimeMills).less(TemHumPm_.time, validMaxTimeMills).order(TemHumPm_.time).build();
        return thpQuery.find();
    }

    public synchronized static List<TemHumPm> queryAllData4ThpEqual(String sku, String device, long validMinTimeMills, long validMaxTimeMills) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return new ArrayList<>();
        }
        Box<TemHumPm> temHumBox = boxStore.boxFor(TemHumPm.class);
        if (temHumBox == null) {
            return new ArrayList<>();
        }
        Query<TemHumPm> thpQuery = temHumBox.query().notEqual(TemHumPm_.from, TemHum.FROM_TYPE_INVALID).greaterOrEqual(TemHumPm_.time, validMinTimeMills).less(TemHumPm_.time, validMaxTimeMills).order(TemHumPm_.time).build();
        return thpQuery.find();
    }

    /**
     * 查询最新的一条数据
     *
     * @param sku    指定类型设备的Sku
     * @param device 待查询的设备uuid
     * @return TemHum
     */
    public synchronized static TemHumPm queryLastData4Thp(String sku, String device) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return null;
        }
        Box<TemHumPm> thpBox = boxStore.boxFor(TemHumPm.class);
        if (thpBox == null) {
            return null;
        }
        return thpBox.query().orderDesc(TemHumPm_.time).build().findFirst();
    }

    public synchronized static void insertDeviceData4Thp(String sku, String device, List<TemHumPm> values) {
        if (values == null || values.isEmpty()) {
            return;
        }
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return;
        }
        Box<TemHumPm> temHumBox = boxStore.boxFor(TemHumPm.class);
        if (temHumBox == null) {
            return;
        }
        //对插入的湿度数据做处理，若湿度范围大于100%，则默认显示为99.9%
        for (TemHumPm thp : values) {
            thp.caliHum();
        }
        //插入数据库
        temHumBox.put(values);
        //释放
        values.clear();
    }

    public synchronized static List<TemHumPm> getThDataFromDevice4List4Thp(String sku, String device, int dataLimit) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return null;
        }
        Box<TemHumPm> thpBox = boxStore.boxFor(TemHumPm.class);
        if (thpBox == null) {
            return null;
        }
        return thpBox.query().equal(TemHumPm_.from, TemHumPm.FROM_TYPE_DEVICE).build().find(0, dataLimit);
    }

    public synchronized static void updateFromType2upload4Thp(String sku, String device, List<TemHumPm> thps) {
        if (thps == null || thps.isEmpty()) {
            return;
        }
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return;
        }
        Box<TemHumPm> temHumBox = boxStore.boxFor(TemHumPm.class);
        if (temHumBox == null) {
            return;
        }
        for (TemHumPm thp : thps) {
            thp.setFrom(TemHumPm.FROM_TYPE_UPLOAD);
        }
        //插入数据库
        temHumBox.put(thps);
    }

    /**
     * 插入数据库数据
     *
     * @param thDataStrSet      一个数据点的形式为：2560,5870,9,1654592700000
     * @param validMinTimeMills 限制数据有效时间戳的最小值
     */
    public synchronized static boolean insertServiceData4Thp(String sku, String device, String[] thDataStrSet, long validMinTimeMills) {
        if (thDataStrSet == null || thDataStrSet.length == 0) {
            return false;
        }
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return false;
        }
        Box<TemHumPm> thpBox = boxStore.boxFor(TemHumPm.class);
        if (thpBox == null) {
            return false;
        }
        List<TemHumPm> values = new ArrayList<>();
        for (String thDataStr : thDataStrSet) {
            String[] str = thDataStr.split(",");
            if (str.length == 4) {
                int tem = ThUtil.parseTem(str[0]);
                int hum = ThUtil.parseHum(str[1]);
                int pm25 = ThUtil.parseHum(str[2]);
                long timeMills = ThUtil.parseTime(str[3]);
                //时间做整分钟处理
                timeMills -= timeMills % 60000;
                //5.9.0拉取数据做可能小于开始拉取的时间点，故小于拉取开始时间点以外的点不保存
                long minTime = Config4LthdInfoKey.Companion.getConfig().getSelPeriodStartTime(sku, device);
                if (timeMills < minTime) {
                    continue;
                }
                //保存有效数据
                boolean validThData = ThUtil.isValidThpData(tem, hum, pm25, timeMills);
                if (Constant4L5.H5112.equals(sku)) {
                    boolean isValid4Pb1 = ThConsV1.isValidThValue(tem, hum);
                    boolean isValid4Pb2 = ThConsV1.isValidThValue(pm25, 6000);
                    validThData = isValid4Pb1 || isValid4Pb2;
                }
                if (validThData && timeMills >= validMinTimeMills) {
                    TemHumPm thp = new TemHumPm(tem, hum, pm25, timeMills, TemHum.FROM_TYPE_CLOUD);
                    //对插入的湿度数据做处理，若湿度范围大于100%，则默认显示为99.9%
                    thp.caliHum();
                    values.add(thp);
                }
            }
        }
        if (!values.isEmpty()) {
            //插入数据库
            thpBox.put(values);
        }
        return true;
    }

    /**
     * 查询有效数据条数
     *
     * @param sku    指定类型设备的Sku
     * @param device 设备uuid
     * @return 数据条数
     */
    public synchronized static long queryValidDataLength4Thp(String sku, String device) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return -1;
        }
        Box<TemHumPm> thpBox = boxStore.boxFor(TemHumPm.class);
        if (thpBox == null) {
            return -1;
        }
        return thpBox.query().notEqual(TemHumPm_.from, TemHumPm.FROM_TYPE_INVALID).build().count();
    }

    /**
     * 查询最新有效数据时间
     *
     * @param sku    指定类型设备的Sku
     * @param device 设备uuid
     * @return 数据时间
     */
    public synchronized static long queryLastValidTime4Thp(String sku, String device) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return -1;
        }
        Box<TemHumPm> temHumBox = boxStore.boxFor(TemHumPm.class);
        if (temHumBox == null) {
            return -1;
        }
        TemHumPm thp = temHumBox.query().notEqual(TemHumPm_.from, TemHumPm.FROM_TYPE_INVALID).orderDesc(TemHumPm_.time).build().findFirst();
        if (thp == null) {
            return -1;
        }
        return thp.getTime();
    }

    /**
     * 查询最早有效数据的时间点
     *
     * @param sku    指定类型设备的Sku
     * @param device 设备uuid
     * @return 数据时间
     */
    public synchronized static long queryFirstValidTime4Thp(String sku, String device) {
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return -1;
        }
        Box<TemHumPm> temHumBox = boxStore.boxFor(TemHumPm.class);
        if (temHumBox == null) {
            return -1;
        }
        TemHumPm thp = temHumBox.query().notEqual(TemHumPm_.from, TemHumPm.FROM_TYPE_INVALID).order(TemHumPm_.time).build().findFirst();
        if (thp == null) {
            return -1;
        }
        return thp.getTime();
    }

    /**
     * 查询待向设备读取的数据段
     * 备注：H5106用到
     *
     * @param lastClearDataTime 最后清理数据的时间点
     * @param minutesMax        最大拉取的数据个数(一分钟一个数据点)
     * @return 需向设备端拉取数据的时间段
     */
    public synchronized static DataTimeSet queryDataTimeSet4Thp(String sku, String device, long lastClearDataTime, int minutesMax) {
        DataTimeSet dataTimeSet = new DataTimeSet();
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return dataTimeSet;
        }
        long minTimeStamp = System.currentTimeMillis() - TimeUtil.getMills(minutesMax);
        long limitTime = Config4LthdInfoKey.Companion.getConfig().getSelPeriodStartTime(sku, device);
        minTimeStamp = Math.max(minTimeStamp, lastClearDataTime);
        minTimeStamp = Math.max(minTimeStamp, limitTime);
        Box<TemHumPm> temHumBox = boxStore.boxFor(TemHumPm.class);
        if (temHumBox == null) {
            return dataTimeSet;
        }
        //倒序查询，先读取旧数据，在读取新数据
        List<TemHumPm> thps = temHumBox.query().greater(TemHumPm_.time, minTimeStamp).orderDesc(TemHumPm_.time).build().find();
        dataTimeSet.generateDataTimeSet4Thp(thps, Math.max(lastClearDataTime, limitTime), minutesMax);
        return dataTimeSet;
    }

    /**
     * 读取数据库中上一次存储的最大时间，作为下一次请求数据的起始时间
     */
    public synchronized static DataTimeSet queryDataTimeH5140(String sku, String device, long lastClearDataTime, int minutesMax) {
        DataTimeSet dataTimeSet = new DataTimeSet();
        BoxStore boxStore = getInstance.getBoxStore(sku, device);
        Box<TemHumPm> temHumBox = boxStore.boxFor(TemHumPm.class);
        if (temHumBox == null) {
            return dataTimeSet;
        }
        long minTimeStamp = System.currentTimeMillis() - TimeUtil.getMills(minutesMax);// 一个月前的时间戳
        List<TemHumPm> thps = temHumBox.query().greater(TemHumPm_.time, minTimeStamp).orderDesc(TemHumPm_.time).build().find();
        if (!thps.isEmpty()) {
            long lastDataTime = thps.get(0).getTime();
            dataTimeSet.setMinStartTime(lastDataTime);
            dataTimeSet.setMaxEndTime(System.currentTimeMillis());
        }
        return dataTimeSet;
    }

    /**
     * 清除图表数据缓存
     *
     * @param sku           指定类型设备的Sku
     * @param device        设备uuid
     * @param dataClearTime 上次清除数据时间戳
     */
    public synchronized static void clearThpCache(String sku, String device, long dataClearTime) {
        dataClearTime = Math.max(0, dataClearTime);
        BoxStore boxStore = DbController.getInstance.getBoxStore(sku, device);
        if (boxStore == null) {
            return;
        }
        Box<TemHumPm> temHumBox = boxStore.boxFor(TemHumPm.class);
        if (temHumBox == null) {
            return;
        }
        TemHumPm allThLast = temHumBox.query().greater(TemHumPm_.time, 0).build().findFirst();
        //当前已有数据的最早值时间点大于要清除的时间点，则无需处理
        if (allThLast != null && allThLast.getTime() > dataClearTime) {
            return;
        }
        List<TemHumPm> remainThps = temHumBox.query().greater(TemHumPm_.time, dataClearTime).build().find();
        //删除已有文件，释放内存空间。
        delete(sku, device, false);
        //将要保留的数据保存到新的数据文件中
        insertDeviceData4Thp(sku, device, remainThps);
    }
}