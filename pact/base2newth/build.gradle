apply plugin: 'com.android.library'
apply plugin: 'com.jakewharton.butterknife'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'
apply plugin: 'io.objectbox'

android {
    namespace 'com.govee.base2newth'
    compileSdk COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                        AROUTER_MODULE_NAME: project.getName()
                ]
            }
        }
        kapt {
            //ARouter配置
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
    }

    // 开启 DataBinding
    dataBinding {
        enabled true
    }

    buildTypes {
        release {
            isDefault = RELEASE_BUILD_VARIANT_IS_DEFAULT
            minifyEnabled false
            consumerProguardFiles = ['proguard-rules.pro']
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            isDefault = !RELEASE_BUILD_VARIANT_IS_DEFAULT
            minifyEnabled false
            consumerProguardFiles = ['proguard-rules.pro']
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }

}

dependencies {
    implementation fileTree(include: ['*.jar'], dir: 'libs')
    api project(':base2home')
    /*ARouter相关配置*/
    //noinspection DependencyNotationArgument
    implementation rootProject.ext.sdk.arouter_api
    //noinspection DependencyNotationArgument
    kapt rootProject.ext.sdk.arouter_compiler
    /*butterknife注解实现*/
    kapt 'com.jakewharton:butterknife-compiler:' + BUTTERKNIFE_VERSION
    //AppLifecycle
    implementation 'com.govee.lifecycle:lifecycle-api:' + lifecycle_api_version
    kapt 'com.govee.lifecycle:lifecycle-compiler:' + lifecycle_compiler_version
}
