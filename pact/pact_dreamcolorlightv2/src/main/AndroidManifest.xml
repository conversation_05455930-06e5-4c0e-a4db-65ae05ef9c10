<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.govee.dreamcolorlightv2"
    tools:ignore="DiscouragedApi"
    >
    <application>
        <activity
            android:name=".add.DeviceNameAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.WifiChooseAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.AdjustAc"
            android:configChanges="uiMode|screenSize|orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.SettingBleWifiAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
    </application>
</manifest>