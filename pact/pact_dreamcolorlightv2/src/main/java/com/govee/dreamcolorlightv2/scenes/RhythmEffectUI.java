package com.govee.dreamcolorlightv2.scenes;

import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.light.IScenes;
import com.govee.base2light.rhythm.RhyRule;
import com.govee.base2light.rhythm.model.SmartConsType;
import com.govee.base2light.rhythm.ui.AbsRhythmSceneUI;
import com.govee.base2light.rhythm.ui.AbsRhythmUI;
import com.govee.base2light.rhythm.ui.AbsSnapshotEffectUI;
import com.govee.base2light.rhythm.ui.BaseRhythmSnapshotUI;
import com.govee.base2light.widget.RhythmSleepModeUI;
import com.govee.dreamcolorlightv2.adjust.Diy;
import com.govee.dreamcolorlightv2.pact.Support;
import com.zhy.android.percent.support.PercentRelativeLayout;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by lins<PERSON>ong on 12/2/20
 * 昼夜节律显示UI
 */
public class RhythmEffectUI extends AbsSnapshotEffectUI {
    @Override
    public void layout(AppCompatActivity ac, PercentRelativeLayout parent, DeviceModel deviceModel, RhyRule rhyRule, OnConfirmListener confirmListener) {
        super.layout(ac, parent, deviceModel, rhyRule, confirmListener);
        /*初始化ui组件*/
        /*添加布局-快照*/
        if (supportSnapshot()) {
            BaseRhythmSnapshotUI snapshotUI = new BaseRhythmSnapshotUI(ac, deviceModel, this);
            uiList.add(snapshotUI);
        }

        /*添加布局-开关*/
        RhythmSwitchUI switchUI = new RhythmSwitchUI(ac, deviceModel, this);
        uiList.add(switchUI);

        /*添加布局-亮度*/
        RhythmBrightnessUI brightnessUI = new RhythmBrightnessUI(ac, deviceModel, this);
        uiList.add(brightnessUI);

        /*添加布局-色温*/
        RhythmColorTemUI colorTemUI = new RhythmColorTemUI(ac, deviceModel, this);
        uiList.add(colorTemUI);

        /*添加布局-颜色*/
        RhythmColorUI colorUI = new RhythmColorUI(ac, deviceModel, this);
        uiList.add(colorUI);

        /*添加布局-模式*/
        int newScenesVersion = Support.newScenesVersion(deviceModel.getGoodsType(), deviceModel.versionSoft, deviceModel.pactCode, deviceModel.getSku());
        CategoryV1.OnScenesCheckListener listener = sceneV1 -> {
            int sceneType = sceneV1.getSceneType(0, deviceModel.getSku());
            int cmdVersion = sceneV1.getCmdVersion(deviceModel.getSku(), 0);
            return Support.checkShareEffectVersion(sceneType, cmdVersion, deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType,
                    deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard);
        };
        RhythmSceneUI modeUI;

        int newVersion = Support.getDiyVersion(deviceModel.getGoodsType(), deviceModel.versionSoft, deviceModel.versionHard, deviceModel.pactCode, deviceModel.getSku());
        DiySupportV1 diySupport = Diy.getDiySupport(newVersion);
        int[] scenesOpSet = Support.getSupportScenesOpSet(deviceModel.getGoodsType(), deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard);
        if (newScenesVersion == 0) {
            IScenes iScenes = Support.getScenes(0);
            modeUI = new RhythmSceneUI(ac, iScenes, deviceModel, this);
            modeUI.setServiceSceneVersion(false, AbsRhythmSceneUI.service_scene_version_not_support);
        } else {
            modeUI = new RhythmSceneUI(ac, deviceModel, this);
            modeUI.setServiceSceneVersion(true, AbsRhythmSceneUI.service_scene_version_new);
            modeUI.setSceneFilter(listener, scenesOpSet, diySupport);
        }
        uiList.add(modeUI);

        /*添加布局-DIY*/
        RhythmDiyUI diyUI = new RhythmDiyUI(ac, deviceModel, this);
        uiList.add(diyUI);

        if (smartConsType == SmartConsType.side_op_group_oneClick
                || smartConsType == SmartConsType.side_op_group_nfc
                || smartConsType == SmartConsType.side_op_group_linkages) {
            RhythmSleepModeUI sleepModeUI = new RhythmSleepModeUI(ac, deviceModel, new AbsRhythmUI.OnSelectChangeListener() {
                boolean brightnessSelectTemp = false;

                @Override
                public void onSelectChange(AbsRhythmUI ui, boolean isSelect) {
                    RhythmEffectUI.this.onSelectChange(ui, isSelect);
                    if (isSelect) {
                        if (brightnessUI.isSelect()) {
                            brightnessSelectTemp = true;
                            brightnessUI.maualClickBrightness();
                        } else {
                            brightnessSelectTemp = false;
                        }
                        brightnessUI.enable(false, 0.5f);
                    } else if (ui.isCanClick()) {
                        brightnessUI.enable(true, 1f);
                        if (brightnessSelectTemp && !brightnessUI.isSelect()) {
                            brightnessUI.maualClickBrightness();
                        }
                    }
                }

                @Override
                public void onStateChange(AbsRhythmUI ui, int state) {
                    RhythmEffectUI.this.onStateChange(ui, state);
                }
            });
            uiList.add(sleepModeUI);
        }
        RhythmMorningUpUI rhythmMorningUpUI = getRhythmMorningUpUI(ac, deviceModel, newScenesVersion);
        if (rhythmMorningUpUI != null) {
            uiList.add(rhythmMorningUpUI);
        }
        autoAddViewAndApplyRule(parent, rhyRule);

    }

    @Nullable
    private RhythmMorningUpUI getRhythmMorningUpUI(AppCompatActivity ac, DeviceModel deviceModel, int newScenesVersion) {
        if (smartConsType != SmartConsType.side_op_group_rhythm || newScenesVersion <= 0) {
            return null;
        }
        return new RhythmMorningUpUI(ac, deviceModel, this);
    }
}