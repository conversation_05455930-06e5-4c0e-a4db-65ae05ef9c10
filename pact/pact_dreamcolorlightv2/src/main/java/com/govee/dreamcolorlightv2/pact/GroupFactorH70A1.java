package com.govee.dreamcolorlightv2.pact;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.group.AbsGroupFactor;
import com.govee.base2light.group.ble.AbsFactorBleUi;
import com.govee.base2light.group.ble.FactorInfo;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;

import androidx.annotation.NonNull;

/**
 * Create by xie<PERSON><PERSON> on 12/1/20
 * 群组因子定义$
 */
public class GroupFactorH70A1 extends AbsGroupFactor {
    @Override
    public boolean isSupport(int goodsType, String sku, String versionSoft, String versionHard) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3;
    }

    @Override
    public int factorVersion() {
        return AbsGroupFactor.FACTOR_VERSION_H70A1_A2_A3;
    }

    @Override
    public AbsFactorBleUi getFactorUi(@NonNull FactorInfo factor) {
        if (factor.hasSupportPartBrightness) {
            Mode mode = new Mode();
            mode.subMode = new SubModeColorV2();
            factor.mode = mode;
        }
        return new FactorBleUi(factor);
    }

    @Override
    public FactorInfo makeFactorInfo(DeviceModel deviceModel) {
        FactorInfo factorInfo = new FactorInfo();
        factorInfo.setGoodsType(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3);
        factorInfo.brightness4Percent = 100;
        Mode mode = new Mode();
        mode.subMode = new SubModeColorV2();
        factorInfo.mode = mode;
        factorInfo.deviceModel = deviceModel;
        return factorInfo;
    }

    @Override
    public ConvertFactorFuc getConvertFactorInfo() {
        /*device取最多的ic数的设备*/
        FactorInfo.ConvertFactorInfo convertFactorInfo = new FactorInfo.ConvertFactorInfo(null, true);
        return deviceModel -> convertFactorInfo;
    }
}