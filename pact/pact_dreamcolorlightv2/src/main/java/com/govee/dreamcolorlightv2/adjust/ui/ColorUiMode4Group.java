package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.ui.R;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsColorUiMode;

import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;

/**
 * Create by hey on 2021/3/3
 * $
 */
public class ColorUiMode4Group extends AbsColorUiMode {
    public ColorUiMode4Group(String sku, int goodsType) {
        super(sku, goodsType);
    }

    @Override
    public AbsModeFragment getUiFragment() {
        ColorFragmentV2 fragment = new ColorFragmentV2();
        fragment.makeArguments(getSku(), getGoodsType(), false);
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_color_mini, R.mipmap.new_control_light_btb_mode_color_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        subModeColor.loadLocal();
        return subModeColor;
    }
}
