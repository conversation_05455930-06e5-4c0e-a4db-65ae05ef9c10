package com.govee.dreamcolorlightv2.pact;

import com.govee.base2home.sku.IMaker;
import com.govee.base2home.sku.ISkuItem;
import com.govee.dreamcolorlightv2.pact.bleiot.V1BleIotSkuItem;

import java.util.ArrayList;
import java.util.List;


/**
 * Create by xieyingwu on 2019-12-10
 * maker
 */
public class SubMaker implements IMaker {
    private List<ISkuItem> makers = new ArrayList<>();

    public SubMaker() {
        makers.add(new V1BleIotSkuItem());
    }

    @Override
    public List<ISkuItem> getSupportMakers() {
        return makers;
    }
}