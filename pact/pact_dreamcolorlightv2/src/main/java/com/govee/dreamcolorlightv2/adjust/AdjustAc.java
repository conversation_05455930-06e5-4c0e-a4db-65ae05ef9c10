package com.govee.dreamcolorlightv2.adjust;

import android.content.Intent;

import androidx.annotation.NonNull;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.sku.SkuIcM;
import com.govee.base2light.ble.scenes.ScenesM;
import com.govee.base2light.pact.AbsPactAdjustAc4BleIot;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.IFrame;
import com.govee.base2light.pact.IFrameResult;
import com.govee.dreamcolorlightv2.ConsV1;
import com.govee.dreamcolorlightv2.adjust.v1.FrameV1;
import com.govee.dreamcolorlightv2.pact.Support;

/**
 * Create by xieyingwu on 2020/3/5
 * 控制页-v2版本-支持ble+iot$
 */
public class AdjustAc extends AbsPactAdjustAc4BleIot {

    @NonNull
    @Override
    protected IFrame makeFrame(IFrameResult iFrameResult, BleIotInfo bleIotInfo) {
        return new FrameV1(iFrameResult, bleIotInfo);
    }

    @NonNull
    @Override
    protected BleIotInfo makeInfoFromIntent(Intent intent) {
        String sku = intent.getStringExtra(ConsV1.intent_ac_adjust_sku);
        String device = intent.getStringExtra(ConsV1.intent_ac_adjust_device);
        String spec = intent.getStringExtra(ConsV1.intent_ac_adjust_spec);
        String deviceName = intent.getStringExtra(ConsV1.intent_ac_adjust_deviceName);
        int goodsType = intent.getIntExtra(ConsV1.intent_ac_adjust_goodsType, GoodsType.GOODES_TYPE_NO_SUPPORT);
        String bleAddress = intent.getStringExtra(ConsV1.intent_ac_adjust_bleAddress);
        String bleName = intent.getStringExtra(ConsV1.intent_ac_adjust_bleName);
        String wifiMac = intent.getStringExtra(ConsV1.intent_ac_adjust_wifiMac);
        String topic = intent.getStringExtra(ConsV1.intent_ac_adjust_topic);
        String versionHard = intent.getStringExtra(ConsV1.intent_ac_versionHard);
        String versionSoft = intent.getStringExtra(ConsV1.intent_ac_versionSoft);
        int ic = intent.getIntExtra(ConsV1.intent_ac_ic, 0);
        if (ic == 0) {
            ic = SkuIcM.getInstance().getDefIcNum(sku);
        }
        BleIotInfo bleIotInfo = new BleIotInfo(sku, goodsType, device, spec, deviceName, bleName, bleAddress, wifiMac, topic, versionHard);
        bleIotInfo.versionSoft = versionSoft;
        bleIotInfo.ic = ic;
        return bleIotInfo;
    }

    @Override
    protected int[] getDefHeaderRes() {
        return Support.getDefHeaderRes(info.goodsType);
    }

    @Override
    protected String getTag() {
        return "AdjustAc4DreamColorLightV2";
    }

    @Override
    protected boolean supportDeviceLock() {
        return true;
    }

    @Override
    protected int getScenesEffectVersion() {
        return ScenesM.version_scenes_effect_v1;
    }

    @Override
    protected int[] supportScenesOp() {
        if (info == null) return null;
        return Support.getSupportScenesOpSet(info.goodsType, info.pactCode, info.versionSoft, info.versionHard);
    }

    @Override
    protected void inNormal() {
        super.inNormal();
        showSnapshotFloatBar(info.open);
    }
}