package com.govee.dreamcolorlightv2.pact;

import android.app.Activity;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.event.EventBleStatus;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2home.sku.Colors;
import com.govee.base2light.ac.club.EventColorStrip;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.EventDiyApply;
import com.govee.base2light.ac.diy.EventDiyApplyV2;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v1.EffectCodes;
import com.govee.base2light.ac.diy.v1.EventDiyEffectOp;
import com.govee.base2light.ac.diy.v1.EventGroupOpDiyResult;
import com.govee.base2light.ac.diy.v2.DiyOpM;
import com.govee.base2light.ac.diy.v2.DiyTemplate;
import com.govee.base2light.ac.diy.v2.DiyValue;
import com.govee.base2light.ac.diy.v2.LastDiyConfig;
import com.govee.base2light.ac.diy.v3.AcDiyGroup;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyGroup;
import com.govee.base2light.ac.diy.v3.DiyShortcutManger;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ac.diy.v3.Event2AcDiyGroup;
import com.govee.base2light.ac.diy.v3.EventDiyApply4InModeShowing;
import com.govee.base2light.ac.diy.v3.EventDiyModeShowingChange;
import com.govee.base2light.ac.diy.v3.Util4Diy;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.mic.MicBleProtocol;
import com.govee.base2light.ble.mic.controller.EventSwitchMicPickUpType;
import com.govee.base2light.ble.mic.controller.MicSetRgbController;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.EventNewMusicOpResult;
import com.govee.base2light.ble.music.EventSetMultiMusicEffect;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.ble.scenes.ScenesM;
import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.colortemp.base.AbsColorTemFactorUI;
import com.govee.base2light.group.AbsGroupFactor;
import com.govee.base2light.group.ble.AbsGroupOp;
import com.govee.base2light.group.ble.FactorInfo;
import com.govee.base2light.light.EventScenesUpdateV1;
import com.govee.base2light.light.EventServiceScenesFresh;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.ui.mode.ChangeModeEvent;
import com.govee.dreamcolorlightv2.ConsV1;
import com.govee.dreamcolorlightv2.adjust.Diy;
import com.govee.dreamcolorlightv2.adjust.ui.ModeUi4Factor;
import com.govee.dreamcolorlightv2.adjust.ui.ModeUi4FactorV2;
import com.govee.dreamcolorlightv2.ble.AbsMusicIc;
import com.govee.dreamcolorlightv2.ble.EventChangeGradual;
import com.govee.dreamcolorlightv2.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv2.ble.GroupOp;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.ModeController;
import com.govee.dreamcolorlightv2.ble.SubModeColor;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;
import com.govee.dreamcolorlightv2.ble.SubModeMusic;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV1;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV2;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV3;
import com.govee.dreamcolorlightv2.ble.SubModeMusicV1;
import com.govee.dreamcolorlightv2.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv2.ble.SubModeScenes;
import com.govee.home.account.config.AccountConfig;
import com.govee.ui.R;
import com.govee.ui.component.BrightnessUI;
import com.govee.ui.dialog.HintDialog1;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.CaughtRunnable;
import com.ihoment.base2app.util.ResUtil;
import com.zhy.android.percent.support.PercentRelativeLayout;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 12/1/20
 * factor的ui$
 */
public class FactorBleUi extends AbsColorTemFactorUI {
    private static final String TAG = "FactorBleUi";
    private BrightnessUI brightnessUI;
    private AbsMode4UIV1 modeUI;
    private Activity ac;
    private final GroupOp groupOp;
    private boolean firstPop;
    private final boolean supportPartBrightness;
    private final boolean hasNotSupportPartBrightness;
    private final List<DiyGroup> curDiyGroups = new ArrayList<>();
    private final Handler handler = new Handler(Looper.getMainLooper());
    private int[] lastRgbSet;
    //同型号类下支持切换手机拾音 若当前存在设备版本不支持，则切换手机拾音时提示“部分设备当前固件版本不支持手机拾音”
    private boolean showMicByPhoneHint;
    //是否是切换拾音方式
    private boolean isSwitchMicPickUpType;
    private MusicOp musicOp = new MusicOp();

    public FactorBleUi(@NonNull FactorInfo factorInfo) {
        super(factorInfo);
        groupOp = new GroupOp(factorInfo);
        supportPartBrightness = factorInfo.hasSupportPartBrightness;
        hasNotSupportPartBrightness = factorInfo.hasNotSupportPartBrightness;
        firstPop = false;
        if (LogInfra.openLog()) {
            LogInfra.Log.d(TAG, "FactorBleUi() supportPartBrightness = " + supportPartBrightness + " , factorInfo.hasNotSupportPartBrightness = " + factorInfo.hasNotSupportPartBrightness);
        }
    }

    @Override
    public int factorVersion() {
        return AbsGroupFactor.factor_version_4_rgbic;
    }

    @Override
    public void layoutDetail(@NonNull AppCompatActivity ac, @NonNull PercentRelativeLayout factorParent) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "layoutFactor()");
        }
        this.ac = ac;
        /*初始化ui组件*/
        int[] ids = new int[]{100, 101, 102, 103, 104};
        /*添加布局-亮度*/
        brightnessUI = new BrightnessUI(ac, 100, 1, false);
        View fucViewBrightness = brightnessUI.getFucView();
        fucViewBrightness.setId(ids[0]);
        addViewMargin(factorParent, fucViewBrightness, -1, brightnessUI.getWidth(), brightnessUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375, 0);
        /*添加布局-mode*/
        if (supportPartBrightness) {
            modeUI = new ModeUi4FactorV2(ac, factorInfo.getSku(), factorInfo.deviceModel.device, factorInfo.getGoodType());
        } else {
            modeUI = new ModeUi4Factor(ac, factorInfo.getSku(), factorInfo.deviceModel.device, factorInfo.getGoodType());
        }
        View fucViewMode = modeUI.getFucView();
        fucViewMode.setId(ids[2]);
        addViewMargin(factorParent, fucViewMode, fucViewBrightness.getId(), modeUI.getWidth(), modeUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375, 0);
        layoutSuc = true;
        registerEvent(true);
        /*通知请求服务器sku场景配置刷新逻辑*/
        ScenesM.getInstance.getScenes4Sku(ScenesM.version_scenes_effect_v1, factorInfo.getGoodType(), factorInfo.getSku(), factorInfo.getDevice());
        /*刷新DIY*/
        DiyOpM.getInstance.getDiyList(factorInfo.getSku(), factorInfo.getGoodType());
        /*通知刷新DIY模式*/
        EventDiyModeShowingChange.sendEventDiyModeShowingChange();
    }

    @Override
    public int getSceneVersion() {
        return ScenesM.version_scenes_effect_v1;
    }


    @Override
    public void updateUi() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "updateUi()");
        }
        if (!isUiOk()) return;
        brightnessUI.show();
        AbsMode mode = factorInfo.mode;
        /*群控不区分场景是否支持亮度调节，默认都支持调节*/
        brightnessUI.updateBrightness(true, factorInfo.brightness4Percent);
        if (isSwitchMicPickUpType) {
            AbsMicFragmentV4.saveMicModeByPhone(factorInfo.deviceModel.getSku(), factorInfo.deviceModel.device);
        }
        checkDiyMode(mode);
        checkDiyTemplate();
        checkColorMode(mode);
        checkMusicMode();
        checkMusicIc();
        modeUI.show();
        modeUI.setMode(factorInfo.mode);
        checkUIStatusWhenConnected(true, mode);
        isSwitchMicPickUpType = false;
        if (checkMicByPhoneDialog(mode)) {
            HintDialog1.showHintDialog1(ac, ResUtil.getString(R.string.base2light_no_support_pick_up_by_phone), ResUtil.getString(R.string.hint_done_got_it));
        }
        if (checkPartBrightnessDialog(mode)) {
            HintDialog1.showHintDialog1(ac, ResUtil.getString(R.string.no_support_section_color_now), ResUtil.getString(R.string.hint_done_got_it));
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "updateUi() checkPartBrightnessDialog() = " + checkPartBrightnessDialog(mode));
        }
    }

    private void checkMusicIc() {
        AbsMode mode = factorInfo.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof AbsMusicIc) {
            int minIc = musicOp.minIc;
            LogInfra.Log.i(TAG, "checkMusicIc() minIc = " + minIc);
            ((AbsMusicIc) subMode).updateIc4Music(minIc);
        }
    }

    private void checkMusicMode() {
        if (modeUI instanceof ModeUi4Factor) {
            int musicVersion = musicOp.maxMusicVersion;
            ((ModeUi4Factor) modeUI).checkMusicMode(musicVersion);
        } else if (modeUI instanceof ModeUi4FactorV2) {
            int musicVersion = musicOp.maxMusicVersion;
            ((ModeUi4FactorV2) modeUI).checkMusicMode(musicVersion);
        }
    }

    private void checkColorMode(AbsMode mode) {
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeColor) {
                ((SubModeColor) subMode).gradual = factorInfo.gradual;
                ((SubModeColor) subMode).opType = 0;
            } else if (subMode instanceof SubModeColorV2) {
                ((SubModeColorV2) subMode).gradual = factorInfo.gradual;
                ((SubModeColorV2) subMode).opType = 0;
            }
        }
    }

    private boolean checkPartBrightnessDialog(AbsMode mode) {
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeColorV2) {
                if (((SubModeColorV2) subMode).brightness != 0 && supportPartBrightness && hasNotSupportPartBrightness && !firstPop) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.d(TAG, "checkPartBrightnessDialog() return true");
                    }
                    firstPop = true;
                    return true;
                }
                return false;
            }
        }
        return false;
    }

    private void checkDiyTemplate() {
        AbsMode mode = factorInfo.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenes) {
            int scenesCode = ((SubModeScenes) subMode).getEffect();
            boolean scenes4DiyTemplate = ScenesOp.isScenes4DiyTemplate(scenesCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkDiyTemplate() scenes4DiyTemplate " + scenes4DiyTemplate + " ; scenesCode = " + scenesCode);
            }
            if (scenes4DiyTemplate) {
                /*当前是diy模版效果；将模式切换长diy模式*/
                int diyCode = DiyOpM.getInstance.getDiyCode4DiyTemplate(factorInfo.getSku(), scenesCode);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "checkDiyTemplate() diyCode = " + diyCode);
                }
                mode.subMode = new SubModeNewDiy(diyCode);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onBrightnessEndEvent(BrightnessUI.EventBrightnessClickEvent event) {
        if (!isUiOk()) return;
        int brightness = event.brightness;
        factorInfo.brightness4Percent = brightness;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onBrightnessEndEvent() brightness = " + brightness);
        }
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            return;
        }
        recordEffectTimes();
        BrightnessController controller = new BrightnessController(brightness);
        groupOp.sendController4CheckOn(controller.getWriteBytes());
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onModeV1Event(Mode mode) {
        if (!isUiOk()) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onModeV1Event()");
        }
        /*检测是否色温单独控制的设备子模式操作*/
        checkColorTemReal(mode.subMode);
        changeMode(mode);
    }

    private void checkColorTemReal(ISubMode subMode) {
        boolean colorTemRealDevice = Support.isColorTemRealDevice(factorInfo.getGoodType());
        if (!colorTemRealDevice) return;
        if (subMode instanceof SubModeColorV2) {
            boolean isColorTemSet = ((SubModeColorV2) subMode).kelvin > 0;
            LogInfra.Log.i(TAG, "checkColorTemReal() isColorTemSet = " + isColorTemSet);
            if (isColorTemSet) {
                /*色温设置-整条灯带都是同一个色温值*/
                Arrays.fill(((SubModeColorV2) subMode).ctlLight, true);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeModeEvent(ChangeModeEvent event) {
        if (!isUiOk()) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent()");
        }
        checkMicByColorMode(event);
        ISubMode subMode = event.getSubMode();
        boolean change2NewMultiMusicCode = change2NewMultiMusicMode(subMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent() change2NewMultiMusicCode = " + change2NewMultiMusicCode);
        }
        if (change2NewMultiMusicCode) return;
        boolean changeDiyMode = changeDiyMode(subMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent() changeDiyMode = " + changeDiyMode);
        }
        if (changeDiyMode) return;
        /*检测场景模式下的效果是否支持*/
        ISubMode subModeNew = Support.checkScenesModeEffect(subMode, factorInfo.deviceModel.getSku(), factorInfo.getGoodType(),
                factorInfo.deviceModel.pactType, factorInfo.deviceModel.pactCode, 1,
                groupOp.isConnected(), factorInfo.deviceModel.versionSoft, factorInfo.deviceModel.versionHard,
                false, factorInfo.deviceModel.wifiSoftVersion, factorInfo.deviceModel.wifiHardVersion);
        if (subModeNew != null) {
            subMode = subModeNew;
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        changeMode(mode);
        /*统计场景使用次数*/
        if (subMode instanceof SubModeScenes) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, ParamFixedValue.times);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, "scene_" + ((SubModeScenes) subMode).getEffect());
        }
        factorInfo.gradual = 0;/*把渐变开关置为默认关闭*/
    }

    /**
     * 离开 通过颜色模式实现的手机拾音模式之前，从新设置灯带的颜色为纯色
     */
    private void checkMicByColorMode(ChangeModeEvent event) {
        int color = event.getColor();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkMicByColorMode ：" + color);
        }
        if (color != 0) {
            Mode mode = new Mode();
            mode.subMode = SubModeColor.makeSubModeColor(color);
            if (event.getSubMode() instanceof SubModeColor) {
                event.setSubMode(mode.subMode);
            }
            handler.postDelayed(new CaughtRunnable() {
                @Override
                protected void runSafe() {
                    groupOp.sendController4CheckOn(mode.subMode.getWriteBytes());
                }
            }, 0);
        }
    }

    private boolean checkMicByPhoneDialog(AbsMode mode) {
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeMusic || subMode instanceof SubModeMusicV1) {
                if (factorInfo.deviceModel.hasSupportMicByPhone && factorInfo.deviceModel.hasNotSupportMicByPhone
                        && !showMicByPhoneHint && AbsMicFragmentV4.isBleGroupMicByPhoneMode()) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.d(TAG, "checkPartBrightnessDialog() return true");
                    }
                    showMicByPhoneHint = true;
                    return true;
                }
                return false;
            }
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSwitchMicPickUpType(EventSwitchMicPickUpType event) {
        isSwitchMicPickUpType = true;
        modeUI.switchMicPickUpMode(event.isMicMode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onMicSetRgbController(MicSetRgbController controller) {
        if (!isUiOk()) return;
        if (controller.isInitMode) {
            EventBleStatus.sendEvent(EventBleStatus.BleStatus.read_info_over);
        }
        byte[] cmd = new byte[1 + controller.data.length];
        cmd[0] = MicBleProtocol.mic_mode_start;
        System.arraycopy(controller.data, 0, cmd, 1, controller.data.length);
        groupOp.sendExtControllerCheckOn(new MicSetRgbController(cmd));
    }

    private boolean change2NewMultiMusicMode(ISubMode subMode) {
        int musicCode = -1;
        int sensitivity = -1;

        if (subMode instanceof SubModeMusicMultiV1) {
            musicCode = ((SubModeMusicMultiV1) subMode).getMusicCode();
            sensitivity = ((SubModeMusicMultiV1) subMode).getSensitivity();
        } else if (subMode instanceof SubModeMusicMultiV2) {
            musicCode = ((SubModeMusicMultiV2) subMode).getMusicCode();
            sensitivity = ((SubModeMusicMultiV2) subMode).getSensitivity();
        }
        if (musicCode != -1 && sensitivity != -1) {
            boolean newMusicCode = Support.isNewMusicCode(musicCode);
            if (newMusicCode) {
                /*若当前是新的音乐模式-则需要先发送新的多包参数*/
                boolean setLocalNewMusicMode = AbsNewMusicEffect.setLocalNewMusicMode(factorInfo.getSku(), factorInfo.deviceModel.device, musicCode, sensitivity);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "change2NewMultiMusicMode() setLocalNewMusicMode = " + setLocalNewMusicMode);
                }
                AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
                return true;
            }
        }
        return false;
    }

    private boolean changeDiyMode(ISubMode subMode) {
        if (subMode instanceof SubModeNewDiy) {
            boolean hadToken = AccountConfig.read().isHadToken();
            String diyValueKey = ((SubModeNewDiy) subMode).getDiyValueKey();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "changeDiyMode() diyValueKey = " + diyValueKey + " ; hadToken = " + hadToken);
            }
            DiyValue diyValue = DiyShortcutManger.queryLastApplyDiy(factorInfo.getSku(), factorInfo.getGoodType(), getDiySupport(), factorInfo.deviceModel.ic, diyValueKey, true);
            Util4Diy.toApplyDiy(diyValue);
            return true;
        }
        return false;
    }

    private void changeMode(Mode mode) {
        if (mode.subMode instanceof SubModeColorV2) {
            SubModeColorV2 subModeColorV2 = ((SubModeColorV2) mode.subMode);
            if (subModeColorV2.rgbSet == null && subModeColorV2.rgb != 0) { /*设单个颜色*/
                int[] rgb;
                if (lastRgbSet == null) {
                    rgb = new int[subModeColorV2.ctlLight.length];
                } else {
                    rgb = lastRgbSet;
                }
                for (int i = 0; i < subModeColorV2.ctlLight.length; i++) {
                    if (subModeColorV2.ctlLight[i]) {
                        rgb[i] = subModeColorV2.rgb;
                    }
                }
                lastRgbSet = rgb;
                subModeColorV2.rgbSet = rgb;
            } else if (subModeColorV2.rgbSet != null) { /*设色条*/
                lastRgbSet = subModeColorV2.rgbSet;
            } else if (subModeColorV2.brightness != 0) { /*设亮度*/
                subModeColorV2.rgbSet = lastRgbSet;
            } else if (subModeColorV2.temRgb != 0) { /*设色温*/
                int[] rgb;
                if (lastRgbSet == null) {
                    rgb = new int[subModeColorV2.ctlLight.length];
                } else {
                    rgb = lastRgbSet;
                }
                for (int i = 0; i < subModeColorV2.ctlLight.length; i++) {
                    if (subModeColorV2.ctlLight[i]) {
                        rgb[i] = subModeColorV2.temRgb;
                    }
                }
                lastRgbSet = rgb;
                subModeColorV2.rgbSet = rgb;
            }
        } else if (mode.subMode instanceof SubModeColor) {
            SubModeColor subModeColor = ((SubModeColor) mode.subMode);
            if (subModeColor.rgbSet == null && subModeColor.rgb != 0) {
                int[] rgb;
                if (lastRgbSet == null) {
                    rgb = new int[subModeColor.ctlLight.length];
                } else {
                    rgb = lastRgbSet;
                }
                for (int i = 0; i < subModeColor.ctlLight.length; i++) {
                    if (subModeColor.ctlLight[i]) {
                        rgb[i] = subModeColor.rgb;
                    }
                }
                lastRgbSet = rgb;
                subModeColor.rgbSet = rgb;
            } else if (subModeColor.rgbSet != null) { /*设色条*/
                lastRgbSet = subModeColor.rgbSet;
            } else { /*设亮度*/
                subModeColor.rgbSet = lastRgbSet;
            }
        }
        if (mode.subMode instanceof SubModeMusic) {
            /*需要将mode变更成支持多包的音乐模式*/
            if (musicOp.maxMusicVersion == 1) {
                mode.subMode = SubModeMusicMultiV1.toNewSubModeMusic((SubModeMusic) mode.subMode);
            } else if (musicOp.maxMusicVersion == 2) {
                mode.subMode = SubModeMusicMultiV2.toNewSubModeMusic((SubModeMusic) mode.subMode);
            } else if (musicOp.maxMusicVersion == 3) {
                mode.subMode = SubModeMusicMultiV3.toNewSubModeMusic((SubModeMusic) mode.subMode);
            }
        }
        factorInfo.mode = mode;
        updateUi();
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            return;
        }
        recordEffectTimes();
        /*多包场景有特定sku区分*/
        AbsMultipleControllerV14Scenes newScenesMode = Support.is2NewScenesMode(factorInfo.getSku(), factorInfo.deviceModel.device, mode, 1);
        if (newScenesMode != null) {
            groupOp.sendMultipleControllerV14Scenes(newScenesMode.getScenesCode());
        } else {
            /*音乐模式需要区分协议*/
            ISubMode subMode = mode.subMode;
            /*音乐模式需求区分pactType发生不同指令，BK方案的音乐模式协议发生变化*/
            if (isMusicMode(subMode)) {
                groupOp.sendController4CheckOnBySubMode(subMode);
            } else if (subMode instanceof SubModeColor) {
                byte[] writeBytes = subMode.getWriteBytes();
                groupOp.sendController4CheckOn(writeBytes);
            } else if (subMode instanceof SubModeColorV2) {
                byte[] writeBytes = subMode.getWriteBytes();
                groupOp.sendController4CheckOn(writeBytes);
            } else {
                ModeController controller = new ModeController(mode);
                groupOp.sendController4CheckOn(controller);
            }
        }
    }

    private boolean isMusicMode(ISubMode subMode) {
        if (subMode instanceof SubModeMusic) return true;
        if (subMode instanceof SubModeMusicMultiV1) return true;
        return subMode instanceof SubModeMusicMultiV2;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeScenes(CategoryV1.SceneV1 scene) {
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            return;
        }
        int sceneType = scene.getSceneType(0, factorInfo.getSku());
        int cmdVersion = scene.getCmdVersion(factorInfo.getSku(), 0);
        boolean support = Support.checkShareEffectVersion(sceneType, cmdVersion, factorInfo.getSku(), factorInfo.getGoodType(), factorInfo.deviceModel.pactType,
                factorInfo.deviceModel.pactCode, factorInfo.deviceModel.versionSoft, factorInfo.deviceModel.versionHard);
        if (!support) {
            HintDialog1.createHintDialog1(ac, ResUtil.getString(R.string.b2light_scenes_ble_not_support), ResUtil.getString(R.string.hint_done_got_it)).show();
            return;
        }
        recordEffectTimes();
        int sceneCode = scene.getSceneCode(0, factorInfo.getSku());
        String sceneEffectStr = scene.getScenesParam(factorInfo.getSku(), 0);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "sku:" + factorInfo.getSku() + "---sceneEffectStr:" + sceneEffectStr + "---onChangeScenes() sceneType = " + sceneType + " ; scene.sceneCode = " + sceneCode);
        }
        if (sceneType == ScenesOp.scene_type_static) {
            /*静态场景效果*/
            SubModeScenes subModeScenes = new SubModeScenes();
            subModeScenes.setEffect(sceneCode);
            Mode mode = new Mode();
            mode.subMode = subModeScenes;
            factorInfo.mode = mode;
            updateUi();
            ModeController controller = new ModeController(mode);
            ScenesOp.toSetScenes(ac, scene, 0, () -> groupOp.sendController4CheckOn(controller));
            return;
        }
        if (sceneType == ScenesOp.scene_type_rgb) {
            /*rgb效果*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgb(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                SubModeScenes subModeScenes = new SubModeScenes();
                subModeScenes.setEffect(sceneCode);
                Mode mode = new Mode();
                mode.subMode = subModeScenes;
                factorInfo.mode = mode;
                updateUi();
                ScenesOp.toSetScenes(ac, scene, 0, () -> groupOp.sendMultipleControllerV14Scenes(controllerV14Scenes));
            } else {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "onChangeScenes() rgb指令解析出错!");
                }
            }
            return;
        }
        if (sceneType == ScenesOp.scene_type_rgbic) {
            /*rgbic效果*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgbic(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                SubModeScenes subModeScenes = new SubModeScenes();
                subModeScenes.setEffect(sceneCode);
                Mode mode = new Mode();
                mode.subMode = subModeScenes;
                factorInfo.mode = mode;
                updateUi();
                ScenesOp.toSetScenes(ac, scene, 0, () -> groupOp.sendMultipleControllerV14Scenes(controllerV14Scenes));
            } else {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "onChangeScenes() rgbic指令解析出错!");
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyOp(EventDiyEffectOp event) {
        if (!isUiOk()) return;
        checkDiyMode(factorInfo.mode);
    }

    /**
     * 注册diy的输出
     *
     * @param event event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApply(EventDiyApply event) {
        if (!isUiOk()) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApply()");
        }
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            EventGroupOpDiyResult.sendEventGroupOpDiyResult(false);
            return;
        }
        DiyProtocol diyProtocol = event.getDiyProtocol();
        applyDiyV0(diyProtocol);
    }

    private void applyDiyV0(DiyProtocol diyProtocol) {
        int diyCode = diyProtocol.getDiyCode();
        SubModeNewDiy subModeNewDiy = new SubModeNewDiy(diyCode);
        Mode mode = new Mode();
        mode.subMode = subModeNewDiy;
        factorInfo.mode = mode;
        groupOp.sendMultipleController4DiyProtocol(diyProtocol);
        updateUi();
        recordEffectTimes();
    }

    /**
     * diy模版
     *
     * @param diyTemplate
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyTemplate(DiyTemplate diyTemplate) {
        if (!isUiOk()) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyTemplate()");
        }
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            EventGroupOpDiyResult.sendEventGroupOpDiyResult(false);
            return;
        }
        applyDiyV2(diyTemplate);
    }

    private void applyDiyV2(DiyTemplate diyTemplate) {
        int diyCode = diyTemplate.diyCode;
        int templateCode = diyTemplate.scenesCode;
        String effectStr = diyTemplate.effectStr;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyDiyV2() diyCode = " + diyCode + " ; templateCode = " + templateCode + " ; effectStr = " + effectStr);
        }
        /*diy模版也需要区分sku发送不同指令*/
        groupOp.sendMultipleControllerV14DiyTemplate(diyTemplate);
        SubModeNewDiy subModeNewDiy = new SubModeNewDiy(diyCode);
        Mode mode = new Mode();
        mode.subMode = subModeNewDiy;
        factorInfo.mode = mode;
        updateUi();
        recordEffectTimes();
    }

    /**
     * 来自于Studio的 DIY模版
     *
     * @param diyStudio
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyStudio(DiyStudio diyStudio) {
        if (!isUiOk()) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyTemplate()");
        }
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            EventGroupOpDiyResult.sendEventGroupOpDiyResult(false);
            return;
        }
        applyDiyV4(diyStudio);
    }

    /**
     * 来自于Ai的 DIY模版
     *
     * @param diyAi
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyAi(DiyAi diyAi) {
        if (!isUiOk()) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyTemplate()");
        }
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            EventGroupOpDiyResult.sendEventGroupOpDiyResult(false);
            return;
        }
        applyDiyV5(diyAi);
    }

    private void applyDiyV5(DiyAi diyAi) {
        int diyCode = diyAi.diyCode;
        int scenesCode = diyAi.scenesCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyDiyV5() diyCode = " + diyCode + " ; scenesCode = " + scenesCode);
        }
        groupOp.sendMultiControllerV14DiyAi(diyAi);
        SubModeNewDiy subModeNewDiy = new SubModeNewDiy(diyCode);
        Mode mode = new Mode();
        mode.subMode = subModeNewDiy;
        factorInfo.mode = mode;
        updateUi();
        recordEffectTimes();
    }

    private void applyDiyV4(DiyStudio diyStudio) {
        int diyCode = diyStudio.diyCode;
        int scenesCode = diyStudio.scenesCode;
        String effectStr = diyStudio.effectStr;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyDiyV4() diyCode = " + diyCode + " ; scenesCode = " + scenesCode + " ; effectStr = " + effectStr);
        }
        groupOp.sendMultipleControllerV14DiyStudio(diyStudio);
        SubModeNewDiy subModeNewDiy = new SubModeNewDiy(diyCode);
        Mode mode = new Mode();
        mode.subMode = subModeNewDiy;
        factorInfo.mode = mode;
        updateUi();
        recordEffectTimes();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApplyV2(EventDiyApplyV2 event) {
        if (!isUiOk()) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyV2()");
        }
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            EventGroupOpDiyResult.sendEventGroupOpDiyResult(false);
            return;
        }
        DiyValue diyValue = event.getDiyValue();
        applyDiyV3(diyValue);
    }

    private void applyDiyV3(DiyValue diyValue) {
        boolean diyGraffitiV2 = groupOp.sendMultipleControllerV14DiyGraffitiV2(getDiySupport(), diyValue);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyDiyV3() diyGraffitiV2 = " + diyGraffitiV2);
        }
        int diyCode = diyValue.diyCode;
        SubModeNewDiy subModeNewDiy = new SubModeNewDiy(diyCode);
        Mode mode = new Mode();
        mode.subMode = subModeNewDiy;
        factorInfo.mode = mode;
        updateUi();
        if (!diyGraffitiV2) {
            toast(R.string.part_device_no_support_diy);
        }
        recordEffectTimes();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventChangeGradual(EventChangeGradual event) {
        if (!isUiOk()) return;
        boolean open = event.open;
        factorInfo.gradual = open ? 1 : 0;
        updateUi();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventChangeGradual() open = " + open);
        }
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            return;
        }
        Gradual4BleWifiController gradualController = new Gradual4BleWifiController(open);
        byte[] gradualControllerValue = gradualController.getValue();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventChangeGradual() gradualControllerValue = " + BleUtil.bytesToHexString(gradualControllerValue));
        }
        groupOp.sendController4CheckOn(gradualControllerValue);
        recordEffectTimes();
    }

    @Override
    protected void recycler() {
        ac = null;
        groupOp.destroy();
        if (brightnessUI != null) brightnessUI.onDestroy();
        if (modeUI != null) modeUI.onDestroy();
    }

    @Override
    public void updateDevices(List<DeviceModel> deviceModels) {
        musicOp.changeDevices(deviceModels);
        groupOp.updateDevices(deviceModels);
        updateUi();
    }

    @Override
    public void switchChange(boolean on) {
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            return;
        }
        groupOp.sendController(new SwitchController(on));
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventServiceSceneFresh(EventServiceScenesFresh event) {
        String sku = event.sku;
        String skuCur = factorInfo.getSku();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventServiceSceneFresh() sku = " + sku + " ; skuCur = " + skuCur);
        }
        if (!TextUtils.isEmpty(sku) && sku.equals(skuCur)) {
            /*检查是否有服务器配置场景*/
            List<CategoryV1> supportCategories = ScenesM.getInstance.getSkuServiceScenesV1(factorInfo.deviceModel.getSku());
            if (supportCategories != null && !supportCategories.isEmpty()) {
                EventScenesUpdateV1.sendEventScenesUpdateV1(supportScenesOp(), sku, supportCategories, supportDiyEffectCodes(), factorInfo.deviceModel.versionSoft, factorInfo.deviceModel.versionHard);
            }
        }
    }

    /**
     * 支持场景op操作集合-默认为null仅支持静态场景
     *
     * @return
     */
    @Override
    public int[] supportScenesOp() {
        DeviceModel deviceModel = factorInfo.deviceModel;
        return Support.getSupportScenesOpSet(factorInfo.getGoodType(), deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard);
    }

    /**
     * 支持diy in 场景的支持逻辑-默认不支持diy在场景内
     *
     * @return
     */
    @Override
    public EffectCodes supportDiyEffectCodes() {
        return null;
    }

    /*新DIY交互补充代码逻辑*/

    private DiySupportV1 getDiySupport() {
        int diyVersion = Support.getDiyVersion(factorInfo.deviceModel.getGoodsType(), factorInfo.deviceModel.versionSoft,
                factorInfo.deviceModel.versionHard, factorInfo.deviceModel.pactCode, factorInfo.getSku());
        return Diy.getDiySupport(diyVersion);
    }

    private void checkDiyModeInfo(SubModeNewDiy diy) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyModeInfo()");
        }
        if (diy == null) return;
        /*刷新在模式中展示的DIY列表*/
        diy.setDiyGroups(curDiyGroups);
        /*获取当前选中的diyValueKey*/
        int diyCode = diy.getDiyCode();
        int diyVersion = Support.getDiyVersion(factorInfo.deviceModel.getGoodsType(), factorInfo.deviceModel.versionSoft, factorInfo.deviceModel.versionHard, factorInfo.deviceModel.pactCode, factorInfo.getSku());
        String lastDiyApplyKey = Diy.getLastDiyApplyKey(diyVersion, factorInfo.getGoodType(), factorInfo.deviceModel.ic, factorInfo.getSku(), diyCode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyMode() lastDiyApplyKey = " + lastDiyApplyKey);
        }
        diy.setDiyValueKey(lastDiyApplyKey);
    }

    private void checkDiyMode(AbsMode mode) {
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeNewDiy) {
            checkDiyModeInfo((SubModeNewDiy) subMode);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEvent2AcDiyGroup(Event2AcDiyGroup event) {
        int icNum = factorInfo.deviceModel.ic;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEvent2AcDiyGroup icNum = " + icNum);
        }
        AcDiyGroup.jump2DiyGroupAc(ac, factorInfo.getSku(), factorInfo.getGoodType(), getDiySupport(), icNum, true, true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApplyInModeShowing(EventDiyApply4InModeShowing event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing()");
        }
        DiyProtocol diyProtocol = event.getDiyProtocol();
        if (diyProtocol != null) {
            DiyValue applyingDiyValue = event.getDiyValue();
            int diyCode = applyingDiyValue.diyCode;
            String diyValueKey = applyingDiyValue.getDiyValueKey();
            /*记录上次应用的DIY*/
            LastDiyConfig.read().saveLastDiyValueKey(factorInfo.getSku(), diyValueKey, diyCode, applyingDiyValue.effectCode);
            applyDiyV0(diyProtocol);
            return;
        }

        DiyTemplate diyTemplate = event.getDiyTemplate();
        if (diyTemplate != null) {
            DiyValue applyingDiyValue = event.getDiyValue();
            int diyCode = applyingDiyValue.diyCode;
            String diyValueKey = applyingDiyValue.getDiyValueKey();
            /*记录上次应用的DIY*/
            LastDiyConfig.read().saveLastDiyValueKey(factorInfo.getSku(), diyValueKey, diyCode, applyingDiyValue.effectCode);
            applyDiyV2(diyTemplate);
            return;
        }

        DiyGraffitiV2 diyGraffitiV2 = event.getDiyGraffitiV2();
        if (diyGraffitiV2 != null) {
            DiyValue applyingDiyValue = event.getDiyValue();
            int diyCode = applyingDiyValue.diyCode;
            String diyValueKey = applyingDiyValue.getDiyValueKey();
            /*记录上次应用的DIY*/
            LastDiyConfig.read().saveLastDiyValueKey(factorInfo.getSku(), diyValueKey, diyCode, applyingDiyValue.effectCode);
            applyDiyV3(applyingDiyValue);
            return;
        }

        DiyStudio diyStudio = event.getDiyStudio();
        if (diyStudio != null) {
            DiyValue applyingDiyValue = event.getDiyValue();
            int diyCode = applyingDiyValue.diyCode;
            String diyValueKey = applyingDiyValue.getDiyValueKey();
            /*记录上次应用的DIY*/
            LastDiyConfig.read().saveLastDiyValueKey(factorInfo.getSku(), diyValueKey, diyCode, applyingDiyValue.effectCode);
            applyDiyV4(diyStudio);
        }

        DiyAi diyAi = event.getDiyAi();
        if (diyAi != null) {
            DiyValue applyingDiyValue = event.getDiyValue();
            int diyCode = applyingDiyValue.diyCode;
            String diyValueKey = applyingDiyValue.getDiyValueKey();
            /*记录上次应用的DIY*/
            LastDiyConfig.read().saveLastDiyValueKey(factorInfo.getSku(), diyValueKey, diyCode, applyingDiyValue.effectCode);
            applyDiyV5(diyAi);
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventDiyModeShowingChange(EventDiyModeShowingChange event) {
        List<DiyGroup> diyShortcuts = DiyShortcutManger.getDiyShortcuts(factorInfo.getSku(), factorInfo.getGoodType(), factorInfo.deviceModel.ic, getDiySupport());
        curDiyGroups.clear();
        if (diyShortcuts != null && !diyShortcuts.isEmpty()) {
            curDiyGroups.addAll(diyShortcuts);
        }
        handler.post(new CaughtRunnable() {
            @Override
            protected void runSafe() {
                updateUi();
            }
        });
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyModeShowingChange() curDiyGroups.size = " + curDiyGroups.size());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventColorStrip(EventColorStrip event) {
        if (!isUiOk()) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventColorStrip()");
        }
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            return;
        }
        applyColorStrip(event.colors);
        recordEffectTimes();
    }

    private void applyColorStrip(Colors colors) {
        AbsSingleController[] modeControllers;
        if (!supportPartBrightness) {
            SubModeColor subModeColor = SubModeColor.makeSubModeColor4Group(colors);
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            factorInfo.mode = mode;
        } else {
            SubModeColorV2 subModeColorV2 = SubModeColorV2.makeSubModeColor4Group(colors);
            Mode mode = new Mode();
            mode.subMode = subModeColorV2;
            factorInfo.mode = mode;
        }
        if (lastRgbSet == null) {
            lastRgbSet = new int[colors.colorSet.length];
        }
        System.arraycopy(colors.colorSet, 0, lastRgbSet, 0, colors.colorSet.length);
        groupOp.sendColorStrip(supportPartBrightness, colors);
        updateUi();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSetMultiMusicEffect(EventSetMultiMusicEffect event) {
        if (!groupOp.isConnected()) {
            toast(R.string.app_ble_group_no_connected_device_msg);
            return;
        }
        boolean hintNewMusic = musicOp.isHintNewMusic();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSetMultiMusicEffect() hintNewMusic = " + hintNewMusic);
        }
        if (hintNewMusic) {
            /*提示当前部分设备不支持*/
            HintDialog1.showHintDialog1(ac, R.string.part_device_no_support_new_misic, R.string.hint_done_got_it);
        }
        int sensitivity = event.sensitivity;
        AbsNewMusicEffect newMusicEffect = event.newMusicEffect;
        int musicCode = newMusicEffect.getMusicCode();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNewMusicEffect() sensitivity = " + sensitivity + " ; newMusicEffect.musicCode = " + musicCode);
        }
        Mode mode = new Mode();
        if (musicOp.maxMusicVersion == 3) {
            mode.subMode = SubModeMusicMultiV3.toNewSubModeMusic(sensitivity, musicCode);
        } else if (musicOp.maxMusicVersion == 2) {
            mode.subMode = SubModeMusicMultiV2.toNewSubModeMusic(sensitivity, musicCode);
        } else {
            mode.subMode = SubModeMusicMultiV1.toNewSubModeMusic(sensitivity, musicCode);
        }
        factorInfo.mode = mode;
        updateUi();
        MultipleController4Music multipleController4Music = new MultipleController4Music(sensitivity, newMusicEffect, mode.subMode);
        groupOp.sendMultipleControllerV24NewMusic(multipleController4Music);
        EventNewMusicOpResult.sendEventNewMusicOpResult(true, musicCode);
        recordEffectTimes();
    }

    private static class MusicOp {
        public int maxMusicVersion = 0;
        public boolean allSupportMultiMusic = true;
        private boolean hint;
        private int minIc = -1;

        public boolean isHintNewMusic() {
            if (maxMusicVersion > 0 && !allSupportMultiMusic && !hint && needHint(music_type_hint)) {
                hint = true;
                recordTypeHint(music_type_hint);
                return true;
            }
            return false;
        }

        public void changeDevices(List<DeviceModel> models) {
            if (models == null || models.isEmpty()) {
                maxMusicVersion = 0;
                allSupportMultiMusic = false;
            } else {
                allSupportMultiMusic = true;
                for (DeviceModel model : models) {
                    int version = Support.getNewMusicModeVersion(model.getGoodsType(), model.getSku(), model.versionSoft, model.versionHard);
                    if (version == ConsV1.multi_new_music_version_v0) {
                        maxMusicVersion = Math.max(maxMusicVersion, 1);
                    } else if (version == ConsV1.multi_new_music_version_v1) {
                        maxMusicVersion = Math.max(maxMusicVersion, 2);
                    } else if (version == ConsV1.multi_new_music_version_v2) {
                        maxMusicVersion = Math.max(maxMusicVersion, 3);
                    } else {
                        allSupportMultiMusic = false;
                    }
                    int ic = model.ic;
                    LogInfra.Log.i(TAG, "changeDevices() ic = " + ic + " ; minIc = " + minIc);
                    if (ic > 0) {
                        if (minIc == -1) {
                            minIc = ic;
                        } else {
                            minIc = Math.min(minIc, ic);
                        }
                    }

                }
            }
        }
    }

    @NonNull
    @Override
    public String getColorTemSku() {
        return factorInfo.getSku();
    }

    @Override
    public int getBrightness() {
        return factorInfo.brightness4Percent;
    }

    @Nullable
    @Override
    public AbsSingleController getColorTemController4Ble(int kelvin) {
        //H70A1/2/3同型号
        int temColor = com.govee.base2home.Constant.getTemColorByKelvin(kelvin)[2];
        return Comm.makeColorTemController4BleComm(
                factorInfo.getSku(),
                factorInfo.getGoodType(),
                factorInfo.getPactType(),
                factorInfo.getPactCode(),
                factorInfo.getVersionSoft(),
                factorInfo.getVersionHard(),
                temColor
        );
    }

    @Override
    public void onColorTemAndBrightnessChangeCallback(int kelvin, int brightness) {
        factorInfo.brightness4Percent = brightness;
        if (kelvin > 0) {
            if (factorInfo.mode.subMode instanceof SubModeColorV2) {
                //SubModeColorV2 H70A1/2/3
                SubModeColorV2 subModeColorV2 = new SubModeColorV2();
                int ctRgb = com.govee.base2home.Constant.getTemColorByKelvin(kelvin)[2];
                subModeColorV2.setTemRgbAndKelvin(ctRgb, kelvin);
                /*色温设置-整条灯带都是同一个色温值*/
                Arrays.fill(subModeColorV2.ctlLight, true);
                Mode mode = new Mode();
                mode.subMode = subModeColorV2;
                factorInfo.mode = mode;
            }
        }
        updateUi();
    }

    @NonNull
    @Override
    public AbsGroupOp getGroupOp() {
        return groupOp;
    }
}
