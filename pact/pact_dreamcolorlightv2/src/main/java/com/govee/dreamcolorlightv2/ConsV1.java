package com.govee.dreamcolorlightv2;

import android.os.Bundle;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/6/24
 * 常量定义$
 */
public final class ConsV1 {
    private ConsV1() {
    }

    public static final String intent_ac_key_addInfo = "intent_ac_key_addInfo";

    public static final String intent_ac_adjust_sku = "intent_ac_adjust_sku";
    public static final String intent_ac_adjust_spec = "intent_ac_adjust_spec";
    public static final String intent_ac_adjust_device = "intent_ac_adjust_device";
    public static final String intent_ac_adjust_deviceName = "intent_ac_adjust_deviceName";
    public static final String intent_ac_adjust_goodsType = "intent_ac_adjust_goodsType";
    public static final String intent_ac_adjust_bleAddress = "intent_ac_adjust_bleAddress";
    public static final String intent_ac_adjust_bleName = "intent_ac_adjust_bleName";

    public static final String intent_ac_adjust_wifiMac = "intent_ac_adjust_wifiMac";
    public static final String intent_ac_adjust_topic = "intent_ac_adjust_topic";
    public static final String intent_ac_versionHard = "intent_ac_versionHard";
    public static final String intent_ac_versionSoft = "intent_ac_versionSoft";
    public static final String intent_ac_ic = "intent_ac_ic";

    public static Bundle makeAdjustAcBundle(String sku, String device, String spec, int goodsType, String deviceName, String bleName, String bleAddress, String wifiMac, String topic, String versionHard, String versionSoft) {
        Bundle bundle = new Bundle();
        bundle.putString(intent_ac_adjust_sku, sku);
        bundle.putString(intent_ac_adjust_spec, spec);
        bundle.putString(intent_ac_adjust_device, device);
        bundle.putString(intent_ac_adjust_deviceName, deviceName);
        bundle.putInt(intent_ac_adjust_goodsType, goodsType);
        bundle.putString(intent_ac_adjust_bleAddress, bleAddress);
        bundle.putString(intent_ac_adjust_bleName, bleName);
        bundle.putString(intent_ac_adjust_wifiMac, wifiMac);
        bundle.putString(intent_ac_adjust_topic, topic);
        bundle.putString(intent_ac_versionHard, versionHard);
        bundle.putString(intent_ac_versionSoft, versionSoft);
        return bundle;
    }

    public static final int multi_new_music_version_noSupport = -1;/*不支持多包音乐模式*/
    public static final int multi_new_music_version_v0 = 0;/*旧协议支持-0x11-多包音乐模式效果*/
    public static final int multi_new_music_version_v1 = 1;/*新协议支持-0x13-多包音乐模式效果*/
    public static final int multi_new_music_version_v2 = 2;/*新协议支持-0x13-多包音乐模式效果-非新音乐模式后续都是8bytes描述*/
}