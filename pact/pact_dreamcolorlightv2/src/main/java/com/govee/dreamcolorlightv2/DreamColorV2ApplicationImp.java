package com.govee.dreamcolorlightv2;

import com.govee.base2home.main.choose.BleProcessorManager;
import com.govee.base2home.scenes.CmdBuilderManager;
import com.govee.base2home.sku.ModelMaker;
import com.govee.base2light.group.FactorOpM;
import com.govee.base2light.rhythm.RhythmOpM;
import com.govee.dreamcolorlightv2.add.BleBroadcastProcessor;
import com.govee.dreamcolorlightv2.adjust.DetailConfig;
import com.govee.dreamcolorlightv2.pact.GroupFactor;
import com.govee.dreamcolorlightv2.pact.GroupFactorH70A1;
import com.govee.dreamcolorlightv2.pact.Register4Item;
import com.govee.dreamcolorlightv2.pact.SubMaker;
import com.govee.dreamcolorlightv2.pact.Support;
import com.govee.dreamcolorlightv2.scenes.BleBrightnessBuilderV1;
import com.govee.dreamcolorlightv2.scenes.BleColorCmdBuilderV1;
import com.govee.dreamcolorlightv2.scenes.BleColorTemCmdBuilderV1;
import com.govee.dreamcolorlightv2.scenes.BleHeartCmdBuilderV1;
import com.govee.dreamcolorlightv2.scenes.BleSwitchCmdBuilderV1;
import com.govee.dreamcolorlightv2.scenes.IotBrightnessCmdBuilderV1;
import com.govee.dreamcolorlightv2.scenes.IotColorCmdBuilderV1;
import com.govee.dreamcolorlightv2.scenes.IotColorTemCmdBuilderV1;
import com.govee.dreamcolorlightv2.scenes.IotSwitchCmdBuilderV1;
import com.govee.dreamcolorlightv2.scenes.RhythmBuilder;
import com.govee.lifecycle.annotation.AppLifecycle;
import com.ihoment.base2app.IApplication;

/**
 * Create by xieyingwu on 2020/6/24
 * ApplicationImp$
 */
@AppLifecycle
public class DreamColorV2ApplicationImp implements IApplication {
    @Override
    public void create() {
        /*item的构建*/
        Register4Item.INSTANCE.register();
        ModelMaker.getInstance().addMaker(new SubMaker());
        /*发现设备后操作逻辑*/
        BleProcessorManager.getInstance().addProcessorFirst(new BleBroadcastProcessor());
        /*添加默认的支持产品类型sku*/
        Support.addSupportPact();
        /*scenes场景ble支持*/
        CmdBuilderManager.getInstance().registerBleCmdBuilder(
                new BleBrightnessBuilderV1(),
                new BleColorCmdBuilderV1(),
                new BleColorTemCmdBuilderV1(),
                new BleHeartCmdBuilderV1(),
                new BleSwitchCmdBuilderV1()
        );
        /*scenes场景iot支持*/
        CmdBuilderManager.getInstance().registerCmdBuilder(
                new IotSwitchCmdBuilderV1(),
                new IotBrightnessCmdBuilderV1(),
                new IotColorCmdBuilderV1(),
                new IotColorTemCmdBuilderV1()
        );
        /*场景-同类型支持*/
        FactorOpM.getInstance.registerGroupFactor(new GroupFactor());
        FactorOpM.getInstance.registerGroupFactor(new GroupFactorH70A1());
        /*昼夜节律*/
        RhythmOpM.getInstance.registerRhythmOp(new RhythmBuilder());
        DetailConfig.INSTANCE.addConfig();
    }
}