package com.govee.dreamcolorlightv2.add;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.govee.base2home.device.AbsDeviceNameAcV1;
import com.govee.dreamcolorlightv2.ConsV1;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.util.JumpUtil;

import androidx.annotation.NonNull;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020/3/6
 * 设备命名页v1$
 */
public class DeviceNameAc extends AbsDeviceNameAcV1 {
    private AddInfo addInfo;

    /**
     * 跳转到设备命名页
     *
     * @param context
     * @param addInfo
     */
    public static void jump2DeviceNameAcV2(Context context, @NonNull AddInfo addInfo) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ConsV1.intent_ac_key_addInfo, addInfo);
        JumpUtil.jumpWithBundle(context, DeviceNameAc.class, bundle);
    }

    @Override
    protected void initParams(Intent intent) {
        addInfo = intent.getParcelableExtra(ConsV1.intent_ac_key_addInfo);
    }

    @Override
    protected void doSkip() {
        if (BaseApplication.getBaseApplication().isInBackground()) return;/*若应用处于后台，则不进行界面跳转*/
        /*跳转到wifi设置页*/
        WifiChooseAc.jump2wifiChooseAcByAdd(this, addInfo);
    }

    @Override
    protected void onSaveDeviceNameSuc(String newDeviceName) {
        addInfo.deviceName = newDeviceName;
        doSkip();
    }

    @Override
    protected String getDevice() {
        return addInfo.device;
    }

    @Override
    protected String getSku() {
        return addInfo.sku;
    }

    @Override
    protected String getDeviceName() {
        return addInfo.deviceName;
    }
}