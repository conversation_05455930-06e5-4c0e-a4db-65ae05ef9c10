package com.govee.dreamcolorlightv2.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.dreamcolorlightv2.ble.Ble;
import com.govee.dreamcolorlightv2.ble.SubModeColor;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by linshicong on 1/28/21
 * op color操作应用$
 */
public class OpColorCommDialog4SquareBleIotV1 extends AbsOpCommDialog4BleIotV2 {
    private AbsCmd absCmd;
    private AbsSingleController[] controllers;

    protected OpColorCommDialog4SquareBleIotV1(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isBk, boolean isSupportBrightness) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_COLOR, -1);
        if (isBk || isSupportBrightness) {
            controllers = SubModeColorV2.makeSubModeColor(colorEffect.colorSet, colorEffect.brightnessSet, colorEffect.isFade(), !colorEffect.noFadeController());
        } else {
            controllers = SubModeColor.makeSubModeColor(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
        }
        if (controllers != null) {
            size = controllers.length;
            absCmd = new CmdPtReal(controllers);
        }
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isBk, boolean isSupportBrightness) {
        new OpColorCommDialog4SquareBleIotV1(context, bleAddress, bleName, topic, sku, device, colorEffect, isBk, isSupportBrightness).show();
    }

    @Nullable
    @Override
    protected AbsCmd getOpCmd() {
        return absCmd;
    }

    private int size;

    @Override
    protected void bleOping() {
        if (controllers != null) {
            getBle().startController(controllers);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        size--;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result + "---size:" + size);
        }
        if (!result || size <= 1) {
            updateBleResult(result);
            hide();
        }
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}