package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.ui.R;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsScenesUiMode;

import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.SubModeScenes;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2019-07-22
 * scenes ui mode
 */
public class ScenesUiMode extends AbsScenesUiMode {
    public ScenesUiMode(String sku, String device) {
        super(sku,device);
    }

    @Override
    public AbsModeFragment getUiFragment() {
        ScenesFragment fragment = new ScenesFragment();
        fragment.makeArguments(getSku(),getDevice());
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_scene_mini, R.mipmap.new_control_light_btb_mode_scenes_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_scenes;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.loadLocal();
        return subModeScenes;
    }
}
