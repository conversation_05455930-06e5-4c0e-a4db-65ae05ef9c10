package com.govee.dreamcolorlightv2.adjust.v1;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.Protocol;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.pact.ble.AbsBlePact;
import com.govee.base2light.pact.ble.IPactResult4Ble;
import com.govee.dreamcolorlightv2.ble.Ble;
import com.govee.dreamcolorlightv2.pact.Support;

import androidx.annotation.NonNull;

/**
 * Create by xie<PERSON><PERSON> on 2019-12-16
 * v1版本的蓝牙协议$
 */
class BlePactV1 extends AbsBlePact {

    BlePactV1(IPactResult4Ble iPactResult) {
        super(iPactResult);
    }

    @Override
    protected String getTag() {
        return "BlePactV1";
    }

    @Override
    @NonNull
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected boolean isSupportProtocol(Protocol protocol) {
        if (protocol == null) return false;
        for (Protocol pro : Support.supportProtocolsV1) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        /*判断bk方案*/
        for (Protocol pro : Support.supportProtocolsV2) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        return false;
    }

    @Override
    protected Protocol parseBleBroadcastProtocol(int goodsType, byte[] scanRecord) {
        return GoodsType.parseBleBroadcastPactInfo(goodsType, scanRecord);
    }
}