package com.govee.dreamcolorlightv2.ble;

import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.ISubMode;
import com.ihoment.base2app.infra.LogInfra;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-04-19
 * 模式解析对象
 */
public class Mode extends AbsMode {
    private static final String TAG = "Mode";

    @Override
    protected void parseSubMode(byte subModeType, byte[] subModeValidBytes) {
        if (subModeType == BleProtocol.sub_mode_music) {
            subMode = new ParamsSubMode4MusicTelink();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_scenes) {
            subMode = new SubModeScenes();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_diy) {
            subMode = new SubModeNewDiy();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_music) {
            subMode = new ParamsSubMode4MusicBk();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_color_v2) {
            subMode = new SubModeColorV2();
            subMode.parse(subModeValidBytes);
        } else {
            /*默认都按照颜色模式解析*/
            subMode = new SubModeColor();
            subMode.parse(subModeValidBytes);
        }
    }

    public static ISubMode parseWriteSubMode(int musicVersion, byte subModeType, byte[] subModeValidBytes) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parseWriteSubMode() subModeType = " + subModeType + " ; subModeValidBytes = " + BleUtil.bytesToHexString(subModeValidBytes));
        }
        ISubMode subMode;
        if (subModeType == BleProtocol.sub_mode_music) {
            ParamsSubMode4MusicTelink paramsSubMode4MusicTelink = new ParamsSubMode4MusicTelink();
            paramsSubMode4MusicTelink.parse(subModeValidBytes);
            subMode = paramsSubMode4MusicTelink.toSupportSubMode(musicVersion);
        } else if (subModeType == BleProtocol.sub_mode_scenes) {
            subMode = SubModeScenes.parseSubModeScenes4Write(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_diy) {
            subMode = SubModeNewDiy.parseSubModeNewDiy4Write(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_music) {
            ParamsSubMode4MusicBk paramsSubMode4MusicBk = new ParamsSubMode4MusicBk();
            paramsSubMode4MusicBk.parse(subModeValidBytes);
            subMode = paramsSubMode4MusicBk.toSupportSubMode(musicVersion);
        } else if (subModeType == BleProtocol.sub_mode_color_v2) {
            subMode = SubModeColorV2.parseSubModeColor4Write(subModeValidBytes);
        } else {
            /*默认都按颜色模式解析*/
            subMode = SubModeColor.parseSubModeColor4Write(subModeValidBytes);
        }
        return subMode;
    }

    public void changeMusicMode4MultiNewMusic4Telink(int musicVersion) {
        boolean musicV0 = subMode instanceof SubModeMusic;
        boolean musicV1 = subMode instanceof SubModeMusicMultiV1;
        boolean isMusic = musicV0 || musicV1;
        if (!isMusic) return;
        if (musicVersion == 0) {
            if (!musicV0) subMode = new SubModeMusic();
            return;
        }
        if (musicVersion == 1) {
            if (!musicV1) subMode = new SubModeMusicMultiV1();
        }
    }

    public void changeMusicMode4MultiNewMusic4BK(int musciVersion) {
        boolean musicV0 = subMode instanceof SubModeMusicV1;
        boolean musicV1 = subMode instanceof SubModeMusicMultiV2;
        boolean isMusic = musicV0 || musicV1;
        if (!isMusic) return;
        if (musciVersion == 0) {
            if (!musicV0) subMode = new SubModeMusicV1();
            return;
        }
        if (musciVersion == 1) {
            if (!musicV1) subMode = new SubModeMusicMultiV2();
        }
    }
}