package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsMusicNoIcUiMode;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV3;
import com.govee.ui.R;


/**
 * Create by xieying<PERSON> on 2019-07-22
 * music ui mode
 */
public class MusicUiModeMultiV3 extends AbsMusicNoIcUiMode {
    public MusicUiModeMultiV3(String sku, String device) {
        super(sku);
        this.device = device;
    }

    @Override
    public AbsModeFragment getUiFragment() {
        MusicFragmentMultiV3 fragment = new MusicFragmentMultiV3();
        fragment.makeArguments(getSku(), device);
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_music_mini, R.mipmap.new_control_light_btb_mode_music_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeMusicMultiV3 subModeMusic = new SubModeMusicMultiV3();
        subModeMusic.loadLocal();
        return subModeMusic;
    }
}
