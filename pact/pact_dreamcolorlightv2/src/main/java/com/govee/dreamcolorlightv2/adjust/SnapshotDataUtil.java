package com.govee.dreamcolorlightv2.adjust;

import com.govee.base2home.pact.Command4PtReal;
import com.govee.base2home.scenes.builder.CmdBuilder;
import com.govee.base2home.sku.Colors;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v2.DiyTemplate;
import com.govee.base2light.ac.diy.v2.DiyValue;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyShortcutManger;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.rhythm.RhyRule;
import com.govee.base2light.snapshot.BaseSnapshotDataUtil;
import com.govee.base2light.snapshot.EventGeneralSnapshotCmd;
import com.govee.base2light.snapshot.SnapshotMode;
import com.govee.dreamcolorlightv2.adjust.v1.ExtV1;
import com.govee.dreamcolorlightv2.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.ModeController;
import com.govee.dreamcolorlightv2.ble.SubModeColor;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;
import com.govee.dreamcolorlightv2.ble.SubModeMusic;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV1;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV2;
import com.govee.dreamcolorlightv2.ble.SubModeMusicOldV0;
import com.govee.dreamcolorlightv2.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv2.ble.SubModeScenes;
import com.govee.dreamcolorlightv2.iot.CmdBrightness;
import com.govee.dreamcolorlightv2.iot.CmdPtReal;
import com.govee.dreamcolorlightv2.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

/**
 * 组装UI界面当前模式的指令集合（ble + iot）
 */
public class SnapshotDataUtil extends BaseSnapshotDataUtil {
    private ExtV1 ext;

    private static class Builder {
        public final static SnapshotDataUtil instance = new SnapshotDataUtil();
    }

    public static SnapshotDataUtil getInstance(ExtV1 ext) {
        Builder.instance.ext = ext;
        return Builder.instance;
    }

    private static final String TAG = "SnapshotDataM";

    public void dealData(AbsMode4UIV1 modeUI, BleIotInfo info) {
        super.dealData(modeUI, info);
        //模式没有改变过
        if (!checkEnable(modeUI)) {
            return;
        }
        //组装指令
        List<SnapshotMode.CmdMode> cmds = new ArrayList<>();

        //亮度指令
        CmdBrightness cmdBrightness = new CmdBrightness(ext.brightness);
        SnapshotMode.CmdMode cmdModeBrightness = new SnapshotMode.CmdMode();
        cmdModeBrightness.cmdType = RhyRule.op_type_snapshot_switch;
        cmdModeBrightness.bleCmds = makeBlueMsg(new CmdPtReal(new BrightnessController(ext.brightness)).getCommand());
        cmdModeBrightness.iotCmd = CmdBuilder.makeCmdStr(cmdBrightness);
        cmds.add(cmdModeBrightness);

        ISubMode iSubMode = info.mode.subMode;
        if (iSubMode instanceof SubModeMusic || iSubMode instanceof SubModeMusicOldV0) {
            recordAddMusicSnapshot();
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            Mode mode = new Mode();
            mode.subMode = iSubMode;
            ModeController controller = new ModeController(mode);
            CmdPtReal cmdPtReal = new CmdPtReal(controller);

            cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
            cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);

            cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode);
        } else if (iSubMode instanceof SubModeMusicMultiV1 || iSubMode instanceof SubModeMusicMultiV2) {
            recordAddMusicSnapshot();
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            int musicCode;
            int sensitivity;
            boolean isV2 = false;
            if (iSubMode instanceof SubModeMusicMultiV1) {
                musicCode = ((SubModeMusicMultiV1) iSubMode).getMusicCode();
                sensitivity = ((SubModeMusicMultiV1) iSubMode).getSensitivity();
            } else {
                musicCode = ((SubModeMusicMultiV2) iSubMode).getMusicCode();
                sensitivity = ((SubModeMusicMultiV2) iSubMode).getSensitivity();
                isV2 = true;

            }
            CmdPtReal cmdPtReal = getMusicCmdPtReal(info, musicCode, sensitivity, isV2);

            cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
            cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);

            cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode);
        } else if (iSubMode instanceof SubModeColorV2) {
            recordAddColorSnapshot();
            //颜色模式需要从ColorFragment拿取对应的色条
            SubModeColorV2 subModeColor = (SubModeColorV2) modeUI.getCurCompleteMode();

            Gradual4BleWifiController gradualController = new Gradual4BleWifiController(subModeColor.gradual == 1);
            SnapshotMode.CmdMode cmdMode3 = new SnapshotMode.CmdMode();
            CmdPtReal cmdPtRealGradual = new CmdPtReal(gradualController);
            cmdMode3.bleCmds = makeBlueMsg(cmdPtRealGradual.getCommand());
            cmdMode3.iotCmd = CmdBuilder.makeCmdStr(cmdPtRealGradual);
            cmdMode3.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode3);
            //色条与亮度
            SnapshotMode.CmdMode cmdMode2 = new SnapshotMode.CmdMode();
            Colors color = new Colors(subModeColor.rgbSet, subModeColor.brightnessSet, "");
            AbsSingleController[] modeControllers = SubModeColorV2.makeSubModeColor(color);
            List<byte[]> bytes = new ArrayList<>();
            if (modeControllers != null) {
                for (AbsSingleController absSingleController : modeControllers) {
                    bytes.add(absSingleController.getValue());
                }
            }
            CmdPtReal cmdPtReal = new CmdPtReal(bytes);
            cmdMode2.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
            cmdMode2.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
            cmdMode2.cmdType = RhyRule.op_type_snapshot_mode_2;
            cmds.add(cmdMode2);

        } else if (iSubMode instanceof SubModeColor) {
            recordAddColorSnapshot();
            //颜色模式需要从ColorFragment拿取对应的色条
            SubModeColor subModeColor = (SubModeColor) modeUI.getCurCompleteMode();

            Gradual4BleWifiController gradualController = new Gradual4BleWifiController(subModeColor.gradual == 1);
            SnapshotMode.CmdMode cmdMode3 = new SnapshotMode.CmdMode();
            CmdPtReal cmdPtRealGradual = new CmdPtReal(gradualController);
            cmdMode3.bleCmds = makeBlueMsg(cmdPtRealGradual.getCommand());
            cmdMode3.iotCmd = CmdBuilder.makeCmdStr(cmdPtRealGradual);
            cmdMode3.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode3);
            //色条与亮度
            SnapshotMode.CmdMode cmdMode2 = new SnapshotMode.CmdMode();
            Colors color = new Colors(subModeColor.rgbSet, null, "");

            AbsSingleController[] modeControllers = SubModeColor.makeSubModeColor(color);
            List<byte[]> bytes = new ArrayList<>();
            if (modeControllers != null) {
                for (AbsSingleController absSingleController : modeControllers) {
                    bytes.add(absSingleController.getValue());
                }
            }
            CmdPtReal cmdPtReal = new CmdPtReal(bytes);
            cmdMode2.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
            cmdMode2.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
            cmdMode2.cmdType = RhyRule.op_type_snapshot_mode_2;
            cmds.add(cmdMode2);

        } else if (iSubMode instanceof SubModeScenes) {
            recordAddSceneSnapshot();
            Mode mode = new Mode();
            mode.subMode = iSubMode;
            ModeController controller = new ModeController(mode);
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            AbsMultipleControllerV14Scenes newMultiScenesModeV1 = Support.is2NewScenesMode(info.sku, info.device, mode, Support.newScenesVersion(info.goodsType, info.versionSoft, info.pactCode));
            CmdPtReal cmdPtReal;
            if (newMultiScenesModeV1 != null) {
                cmdPtReal = CmdPtReal.getNewScenesCmdPtReal(newMultiScenesModeV1);
            } else {
                cmdPtReal = new CmdPtReal(controller);
            }

            cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
            cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
            cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode);

        } else if (iSubMode instanceof SubModeNewDiy) {
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            CmdPtReal cmdPtReal = getDiyCmdPtReal((SubModeNewDiy) iSubMode, info);

            if (cmdPtReal != null) {
                cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
                cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
                cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
                cmds.add(cmdMode);
            } else {
                LogInfra.Log.e(TAG, " 无法获取快照diy参数-解析失败");
            }
        }
        EventGeneralSnapshotCmd.sendEvent(cmds, ext.wifiSoftVersion, ext.wifiHardVersion);
    }

    private static CmdPtReal getMusicCmdPtReal(BleIotInfo info, int musicCode, int sensitivity, boolean isV2) {
        CmdPtReal cmdPtReal;
        boolean newMusicCode = Support.isNewMusicCode(musicCode);
        if (newMusicCode) {
            AbsNewMusicEffect localNewMusicEffect = AbsNewMusicEffect.getLocalNewMusicEffect(info.sku, info.device, musicCode, info.ic);

            if (localNewMusicEffect == null) {
                Mode mode = new Mode();
                mode.subMode = info.mode.subMode;
                ModeController controller = new ModeController(mode);
                cmdPtReal = new CmdPtReal(controller);
            } else {
                //当前音乐模式的颜色多包
                MultipleController4Music musicController = new MultipleController4Music((byte) sensitivity, localNewMusicEffect);
                if (isV2) {
                    cmdPtReal = CmdPtReal.getMultiNewMusicModeV2(musicController);
                } else {
                    cmdPtReal = CmdPtReal.getMultiNewMusicMode(musicController);
                }
            }
        } else {
            Mode mode = new Mode();
            mode.subMode = info.mode.subMode;
            ModeController controller = new ModeController(mode);
            cmdPtReal = new CmdPtReal(controller);
        }
        return cmdPtReal;
    }

    private CmdPtReal getDiyCmdPtReal(SubModeNewDiy modeNewDiy, BleIotInfo info) {
        String diyValueKey = modeNewDiy.getDiyValueKey();
        DiySupportV1 diySupport = Diy.getDiySupport(Support.getDiyVersion(info.goodsType, info.versionSoft, info.versionHard, info.pactCode, info.sku));
        DiyValue diyValue = DiyShortcutManger.queryLastApplyDiy(info.sku, info.goodsType, diySupport, info.ic, diyValueKey, false);

        DiyGraffitiV2 highColor = getHighColor(info, modeNewDiy.getDiyCode());
        if (highColor != null) {
            return CmdPtReal.getDiyCmdPt4DiyGraffiti(highColor);
        }
        recordAddDiySnapshot();
        boolean aiEffect = diyValue.isAiDiy();
        if (aiEffect) {
            DiyAi diyAi = diyValue.toDiyAi();
            if (diyAi != null) {
                Command4PtReal ptReal = diyAi.command4PtReal;
                return CmdPtReal.makeCmdPtReal(ptReal.getCommands4IotPtReal());
            }
        }
        boolean studioDiy = diyValue.isStudioDiyEffect();
        if (studioDiy) {
            DiyStudio diyStudio = diyValue.toDiyStudio4Apply();
            boolean supportRgbIcV1 = Support.supportRgbicV1(info.sku, info.versionSoft, info.versionHard, info.goodsType, info.pactType, info.pactCode);
            AbsMultipleControllerV14DiyTemplate multi4Scenes = diyStudio.toMulti4Scenes4RgbicV1(supportRgbIcV1 ? 1 : 0);
            if (diyStudio.isValid()) {
                return CmdPtReal.getDiyTemplateCmdPt(multi4Scenes);
            }
        }
        DiyProtocol diyProtocol = diyValue.toDiyProtocol4Apply();
        if (diyProtocol != null) {
            return CmdPtReal.getDiyCmdPt(diyProtocol);
        }
        DiyGraffitiV2 diyGraffitiV2 = diyValue.toDiyGraffiti4Apply4Rgbic();
        if (diyGraffitiV2 != null) {
            return CmdPtReal.getDiyCmdPt4DiyGraffiti(diyGraffitiV2);
        }
        DiyTemplate diyTemplate = diyValue.toDiyTemplate();
        if (diyTemplate != null) {
            AbsMultipleControllerV14DiyTemplate diyTemplateController = ScenesOp.parseDiyTemplate(diyTemplate.scenesCode, diyTemplate.diyCode, diyTemplate.effectStr);
            if (null == diyTemplateController) {
                diyTemplateController = ScenesOp.parseDiyTemplate(diyTemplate.scenesCode, diyTemplate.diyCode, diyTemplate.effectStr);
            }
            return CmdPtReal.getDiyTemplateCmdPt(diyTemplateController);
        }


        return null;
    }

}