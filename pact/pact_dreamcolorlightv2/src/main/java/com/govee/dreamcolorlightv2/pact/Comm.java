package com.govee.dreamcolorlightv2.pact;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.util.NumberUtil;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.v2.DiyTemplate;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.HeartController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.util.NumUtil;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.ModeController;
import com.govee.dreamcolorlightv2.ble.SubModeColor;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;
import com.govee.dreamcolorlightv2.ble.SubModeScenes;
import com.govee.dreamcolorlightv2.iot.CmdBrightness;
import com.govee.dreamcolorlightv2.iot.CmdColorWc;
import com.govee.dreamcolorlightv2.iot.CmdPtReal;
import com.govee.dreamcolorlightv2.iot.CmdTurn;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Create by xieyingwu on 2021/10/22
 * 生成指令类$
 */
public final class Comm {
    private Comm() {
    }

    /**
     * 生成亮度指令-ble
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param brightness
     * @return
     */
    public static AbsSingleController makeBrightnessController4BleComm(String sku, int goodsType, int pactType, int pactCode, int brightness) {
        int newBrightness;
        int[] brightnessRange = Support.getBrightnessRange(sku, goodsType, pactType, pactCode);
        /*非百分比，需要转化计算*/
        if (brightnessRange[0] == 1) {
            newBrightness = NumUtil.calculateProgress(brightnessRange[2], brightnessRange[1], brightness);
        } else {
            newBrightness = Math.max(brightness, brightnessRange[1]);
            newBrightness = Math.min(newBrightness, brightnessRange[2]);
        }
        return new BrightnessController(newBrightness);
    }

    /**
     * 生成亮度指令-iot
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param brightness
     * @return
     */
    public static AbsCmd makeBrightnessCmd4IotComm(String sku, int goodsType, int pactType, int pactCode, int brightness) {
        int[] brightnessRange = Support.getBrightnessRange(sku, goodsType, pactType, pactCode);
        AbsCmd absCmd;
        if (brightnessRange[0] == 0) {
            absCmd = new CmdBrightness(brightness);
        } else {
            int realMin = brightnessRange[1];
            int realMax = brightnessRange[2];
            absCmd = new CmdBrightness(NumberUtil.calculateProgress(realMax, realMin, brightness));
        }
        return absCmd;
    }

    /**
     * 生成颜色指令-ble
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param color
     * @return
     */
    public static AbsSingleController makeColorController4BleComm(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, int color) {
        int subModeColorVersion = Support.getSubModeColorVersion(sku, goodsType, pactType, pactCode, versionSoft, versionHard);
        ISubMode subMode;
        if (subModeColorVersion > 0) {
            subMode = SubModeColorV2.makeSubModeColor(color);
        } else {
            subMode = SubModeColor.makeSubModeColor(color);
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        return new ModeController(mode);
    }

    /**
     * 生成颜色指令-ble
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param colors
     * @return
     */
    public static AbsSingleController[] makeColorStripController4BleComm(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, Colors colors) {
        int subModeColorVersion = Support.getSubModeColorVersion(sku, goodsType, pactType, pactCode, versionSoft, versionHard);
        if (subModeColorVersion > 0) {
            return SubModeColorV2.makeSubModeColor(colors);
        } else {
            return SubModeColor.makeSubModeColor(colors);
        }
    }

    /**
     * 生成颜色指令-iot
     *
     * @param color
     * @return
     */
    public static AbsCmd makeColorCmd4IotComm(int color) {
        return CmdColorWc.makeCmdColorWc4Color(color);
    }

    /**
     * 生成颜色指令-iot
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param colors
     * @return
     */
    public static AbsCmd makeColorStripCmd4IotComm(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, Colors colors) {
        AbsSingleController[] modeControllers = makeColorStripController4BleComm(sku, goodsType, pactType, pactCode, versionSoft, versionHard, colors);
        List<byte[]> bytes = new ArrayList<>();
        for (AbsSingleController absSingleController : modeControllers) {
            bytes.add(absSingleController.getValue());
        }
        return new CmdPtReal(bytes);
    }

    /**
     * 生成色温指令-ble
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param temColor
     * @return
     */
    public static AbsSingleController makeColorTemController4BleComm(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, int temColor) {
        int subModeColorVersion = Support.getSubModeColorVersion(sku, goodsType, pactType, pactCode, versionSoft, versionHard);
        boolean isColorTemRealDevice = Support.isColorTemRealDevice(goodsType);
        ISubMode subMode;
        if (subModeColorVersion > 0) {
            if (isColorTemRealDevice) {
                SubModeColorV2 subModeColorV2 = new SubModeColorV2();
                int kelvin = com.govee.base2home.Constant.getColorTemKelvin(temColor);
                subModeColorV2.setTemRgbAndKelvin(temColor, kelvin);
                /*色温设置-整条灯带都是同一个色温值*/
                Arrays.fill(subModeColorV2.ctlLight, true);
                subMode = subModeColorV2;
            } else {
                subMode = SubModeColorV2.makeSubModeColor(temColor);
            }
        } else {
            subMode = SubModeColor.makeSubModeColor(temColor);
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        return new ModeController(mode);
    }

    /**
     * 生成色温指令-iot
     *
     * @param colorTemInKelvin
     * @return
     */
    public static AbsCmd makeColorTemCmd4IotComm(int colorTemInKelvin) {
        return CmdColorWc.makeCmdColorWc4Kelvin(colorTemInKelvin);
    }

    /**
     * 生成心跳指令-ble
     *
     * @return
     */
    public static AbsSingleController makeHeartController4BleComm() {
        return new HeartController();
    }

    /**
     * 生成开关指令-ble
     *
     * @param open
     * @return
     */
    public static AbsSingleController makeSwitchController4BleComm(boolean open) {
        return new SwitchController(open);
    }

    /**
     * 生成开关指令-iot
     *
     * @param open
     * @return
     */
    public static AbsCmd makeSwitchCmd4IotComm(boolean open) {
        return new CmdTurn(open);
    }

    /**
     * 生成场景指令-iot
     *
     * @param sku
     * @param versionSoft
     * @param goodsType
     * @param pactCode
     * @param sceneCode
     * @return
     */
    public static AbsCmd makeSceneCmd4IotComm(String sku,String device, String versionSoft, int goodsType, int pactCode, int sceneCode) {
        Mode mode = new Mode();
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.setEffect(sceneCode);
        mode.subMode = subModeScenes;
        CmdPtReal cmdPtReal;
        int newScenesVersion = Support.newScenesVersion(goodsType, versionSoft, pactCode, sku);
        AbsMultipleControllerV14Scenes newMultiScenesModeV1 = Support.is2NewScenesMode(sku, device, mode, newScenesVersion);
        if (newMultiScenesModeV1 != null) {
            cmdPtReal = CmdPtReal.getNewScenesCmdPtReal(newMultiScenesModeV1);
        } else {
            ModeController modeController = new ModeController(mode);
            cmdPtReal = new CmdPtReal(modeController);
        }
        return cmdPtReal;
    }

    /**
     * 生成场景指令-iot
     */
    public static AbsCmd makeSceneCmd4IotComm(CategoryV1.SceneV1 scene, String sku) {
        AbsMultipleControllerV14Scenes newMultiScenesModeV1 = ScenesOp.parseSceneV1(sku, scene, ScenesOp.scene_type_rgb, ScenesOp.scene_type_rgbic);
        if (newMultiScenesModeV1 != null) {
            return CmdPtReal.getNewScenesCmdPtReal(newMultiScenesModeV1);
        }
        return null;
    }

    /**
     * 生成场景蓝牙指令集合
     *
     * @param sku
     * @param versionSoft
     * @param goodsType
     * @param pactCode
     * @param sceneCode
     * @return
     */
    public static List<String> makeScene4BleComm(String sku,String device, String versionSoft, int goodsType, int pactCode, int sceneCode) {
        AbsCmd cmd = makeSceneCmd4IotComm(sku,device, versionSoft, goodsType, pactCode, sceneCode);
        if (cmd instanceof CmdPtReal) {
            return ((CmdPtReal) cmd).getCommand();
        }
        return null;
    }

    /**
     * 生成diy指令-iot
     *
     * @param sku
     * @param versionSoft
     * @param versionHard
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param diyProtocol
     * @param diyGraffiti
     * @param diyTemplate
     * @param diyStudio
     * @param diyAi
     * @return
     */
    public static AbsCmd makeDiyCmd4IotComm(String sku, String versionSoft, String versionHard, int goodsType, int pactType, int pactCode, DiyProtocol diyProtocol, DiyGraffitiV2 diyGraffiti, DiyTemplate diyTemplate, DiyStudio diyStudio, DiyAi diyAi) {
        boolean bkProtocol = Support.isBKProtocol(sku, goodsType, pactType, pactCode);
        if (diyProtocol != null) {
            return bkProtocol ? CmdPtReal.getDiyCmdPtV1(diyProtocol) : CmdPtReal.getDiyCmdPt(diyProtocol);
        }
        if (diyGraffiti != null) {
            CmdPtReal cmdPtReal = CmdPtReal.getDiyCmdPt4DiyGraffiti(diyGraffiti);
            int commandPacketNum = cmdPtReal.getCommand().size();
            if (commandPacketNum > 19) {
                return null;
            }
            return cmdPtReal;
        }
        if (diyTemplate != null) {
            int diyCode = diyTemplate.diyCode;
            int templateCode = diyTemplate.scenesCode;
            String effectStr = diyTemplate.effectStr;
            AbsMultipleControllerV14DiyTemplate diyTemplateController = ScenesOp.parseDiyTemplate(templateCode, diyCode, effectStr);
            if (diyTemplateController != null) {
                return CmdPtReal.getDiyTemplateCmdPt(diyTemplateController);
            }
        }
        if (diyStudio != null) {
            int diyCode = diyStudio.diyCode;
            int scenesCode = diyStudio.scenesCode;
            String effectStr = diyStudio.effectStr;
            boolean supportRgbicV1 = Support.supportRgbicV1(sku, versionSoft, versionHard, goodsType, pactType, pactCode);
            AbsMultipleControllerV14DiyTemplate controllerV14DiyStudio = diyStudio.toMulti4Scenes4RgbicV1(supportRgbicV1 ? 1 : 0);
            if (controllerV14DiyStudio != null) {
                return CmdPtReal.getDiyTemplateCmdPt(controllerV14DiyStudio);
            }
            return null;
        }
        if (diyAi != null) {
            return CmdPtReal.makeCmdPtReal(diyAi.command4PtReal.getCommands4IotPtReal());
        }
        return null;
    }

    /**
     * 生成diy蓝牙指令集合
     *
     * @param sku
     * @param versionSoft
     * @param versionHard
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param diyProtocol
     * @param diyGraffiti
     * @param diyTemplate
     * @param diyStudio
     * @param diyAi
     * @return
     */
    public static List<String> makeDiy4BleComm(String sku, String versionSoft, String versionHard, int goodsType, int pactType, int pactCode, DiyProtocol diyProtocol, DiyGraffitiV2 diyGraffiti, DiyTemplate diyTemplate, DiyStudio diyStudio, DiyAi diyAi) {
        AbsCmd cmd = makeDiyCmd4IotComm(sku, versionSoft, versionHard, goodsType, pactType, pactCode, diyProtocol, diyGraffiti, diyTemplate, diyStudio, diyAi);
        if (cmd == null) return null;
        if (cmd instanceof CmdPtReal) {
            return ((CmdPtReal) cmd).getCommand();
        }
        return null;
    }
}