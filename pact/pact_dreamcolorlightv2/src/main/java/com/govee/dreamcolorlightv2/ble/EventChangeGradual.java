package com.govee.dreamcolorlightv2.ble;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/4/9
 * event-更改渐变开关$
 */
public class EventChangeGradual {
    public boolean open;

    private EventChangeGradual() {
    }

    public static void sendEventChangeGradual(boolean open) {
        EventChangeGradual event = new EventChangeGradual();
        event.open = open;
        EventBus.getDefault().post(event);
    }

}