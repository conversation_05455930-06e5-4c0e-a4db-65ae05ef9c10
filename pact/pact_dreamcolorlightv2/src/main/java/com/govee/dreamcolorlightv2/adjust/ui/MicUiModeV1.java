package com.govee.dreamcolorlightv2.adjust.ui;


import com.govee.ui.R;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.mic.AbsMicByPhoneUiMode;
import com.govee.base2light.ble.mic.MicDj;
import com.govee.base2light.ble.mic.MicDynamic;
import com.govee.base2light.ble.mic.MicSoft;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.SubModeMusicV1;

/**
 * Create by DengFei on 2021-2-4
 * mic的uiMode
 */
public class MicUiModeV1 extends AbsMicByPhoneUiMode {
    protected String TAG = "MicFragment";

    public MicUiModeV1(String sku, String device) {
        super(sku);
        this.device = device;
    }

    @Override
    public AbsModeFragment getUiFragment() {
        MicFragmentV2 micFragment = new MicFragmentV2();
        micFragment.makeArguments(getSku(),device);
        micFragment.setMicMode(new MicDj(), new MicDynamic(), new MicSoft());
        return micFragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_music_mini, R.mipmap.new_control_light_btb_mode_music_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeMusicV1 subModeMusic = new SubModeMusicV1();
        subModeMusic.loadLocal();
        return subModeMusic;
    }
}
