package com.govee.dreamcolorlightv2.scenes;

import com.govee.base2home.scenes.BaseCmd;
import com.govee.base2home.scenes.ICmd;
import com.govee.base2home.scenes.builder.model.ColorModel;
import com.govee.dreamcolorlightv2.pact.Comm;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * iot-颜色控制$
 */
public class IotColorCmdBuilderV1 extends AbsIotCmdBuilderV1<ColorModel> {
    @Override
    public ICmd createCmd(ColorModel colorModel) {
        return new BaseCmd() {
            @Override
            public String getIotCmd() {
                return makeCmdStr(Comm.makeColorCmd4IotComm(colorModel.color));
            }
        };
    }
}