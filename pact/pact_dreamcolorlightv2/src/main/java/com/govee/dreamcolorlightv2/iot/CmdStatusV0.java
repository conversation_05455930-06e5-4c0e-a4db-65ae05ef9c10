package com.govee.dreamcolorlightv2.iot;

import android.text.TextUtils;

import com.govee.base2light.ac.AbsIotManagerV1;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.timer.Timer;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.NewTimerV1Controller;
import com.govee.base2light.ble.controller.SleepController;
import com.govee.base2light.ble.controller.WakeUpController;
import com.govee.dreamcolorlightv2.ble.BleParser;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.SubModeColor;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JsonUtil;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Keep;

/**
 * Create by xieyingwu on 2020/5/13
 * cmd=status的iotV0版本$
 */
public class CmdStatusV0 {
    private static final String TAG = "CmdStatusV0";
    public boolean on;/*开关*/
    public int brightness;/*亮度*/
    public Mode mode;/*模式*/
    public String softVersion;/*蓝牙软件版本*/

    public Timer timer1 = new Timer();
    public Timer timer2 = new Timer();
    public Timer timer3 = new Timer();
    public Timer timer4 = new Timer();
    public WakeUpInfo wakeUpInfo = new WakeUpInfo();/*唤醒*/
    public SleepInfo sleepInfo = new SleepInfo();/*睡眠*/

    public static CmdStatusV0 parseJsonWithBrightness(String softVersion, String json) {
        if (TextUtils.isEmpty(json)) return null;
        CmdStatusV0 statusV0 = new CmdStatusV0();
        statusV0.softVersion = softVersion;
        String stateJsonStr = AbsIotManagerV1.getJsonObjectStr(json, Cmd.parse_json_key_state);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "stateJsonStr = " + stateJsonStr);
        }
        /*解析state字段内参数*/
        State state = JsonUtil.fromJson(stateJsonStr, State.class);
        if (state != null) {
            statusV0.on = state.onOff == 1;
            statusV0.brightness = state.brightness;
        }
        /*解析op字段内参数*/
        String opJsonStr = AbsIotManagerV1.getJsonObjectStr(json, Cmd.parse_json_op);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "opJsonStr = " + opJsonStr);
        }
        ResultPt op = JsonUtil.fromJson(opJsonStr, ResultPt.class);
        if (op != null) {
            List<byte[]> ptBytes = op.getPtBytes();
            if (ptBytes != null && !ptBytes.isEmpty()) {
                /*解析模式*/
                Mode mode = parseStatusPtModeV1(ptBytes);
                if (mode != null) {
                    statusV0.mode = mode;
                }
                /*解析定时*/
                List<Timer> timers = parsePtTimer(ptBytes);
                if (timers != null && !timers.isEmpty()) {
                    int index = 0;
                    for (Timer timer : timers) {
                        if (index == 0) {
                            statusV0.timer1 = timer;
                        } else if (index == 1) {
                            statusV0.timer2 = timer;
                        } else if (index == 2) {
                            statusV0.timer3 = timer;
                        } else if (index == 3) {
                            statusV0.timer4 = timer;
                        }
                        index++;
                    }
                }
                /*解析睡眠*/
                WakeUpInfo wakeUpInfo = parsePtWakeUp(ptBytes);
                if (wakeUpInfo != null) {
                    statusV0.wakeUpInfo = wakeUpInfo;
                }
                /*解析唤醒*/
                SleepInfo sleepInfo = parsePtSleep(ptBytes);
                if (sleepInfo != null) {
                    statusV0.sleepInfo = sleepInfo;
                }
            }
        }
        return statusV0;
    }

    private static SleepInfo parsePtSleep(List<byte[]> ptBytes) {
        if (ptBytes == null || ptBytes.isEmpty()) return null;
        for (byte[] ptByte : ptBytes) {
            if (ptByte != null && ptByte.length == 20) {
                byte op = ptByte[1];
                if (op == BleProtocolConstants.SINGLE_SLEEP) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parsePtSleep()");
                    }
                    return SleepController.parseSleep(ptByte);
                }
            }
        }
        return null;
    }

    private static WakeUpInfo parsePtWakeUp(List<byte[]> ptBytes) {
        if (ptBytes == null || ptBytes.isEmpty()) return null;
        for (byte[] ptByte : ptBytes) {
            if (ptByte != null && ptByte.length == 20) {
                byte op = ptByte[1];
                if (op == BleProtocolConstants.SINGLE_WAKEUP) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parsePtWakeUp()");
                    }
                    return WakeUpController.parse2WakeUp(ptByte);
                }
            }
        }
        return null;
    }

    private static List<Timer> parsePtTimer(List<byte[]> ptBytes) {
        if (ptBytes == null || ptBytes.isEmpty()) return null;
        for (byte[] ptByte : ptBytes) {
            if (ptByte != null && ptByte.length == 20) {
                byte op = ptByte[1];
                if (op == BleProtocolConstants.SINGLE_NEW_TIME_V1) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parsePtTimer()");
                    }
                    return NewTimerV1Controller.parseAllTimer(ptByte);
                }
            }
        }
        return null;
    }

    /**
     * 解析颜色透传设置整段的分段和渐变
     *
     * @param subModeColorVersion
     * @param ptBytes
     * @return
     */
    public static Mode parsePtRealColorModeV1(int subModeColorVersion, List<byte[]> ptBytes) {
        if (ptBytes == null || ptBytes.isEmpty()) return null;
        List<byte[]> colorBytes = new ArrayList<>();
        byte[] gradualBytes = null;
        for (byte[] ptByte : ptBytes) {
            if (ptByte != null && ptByte.length == 20) {
                byte op = ptByte[1];
                if (op == BleProtocol.MSG_TYPE_READ_BULB_COLOR || op == BleProtocolConstants.MSG_READ_BULB_LIGHT_COLOR_WITH_BRIGHTNESS) {
                    /*解析到色值*/
                    colorBytes.add(ptByte);
                } else if (op == BleProtocol.SINGLE_GRADUAL_CHANGE_4_WIFI_BLE) {
                    /*解析到渐变*/
                    gradualBytes = ptByte;
                    break;
                }
            }
        }
        if (colorBytes.isEmpty()) return null;
        ISubMode subMode;
        if (subModeColorVersion == 0) {
            SubModeColor subModeColor = new SubModeColor();
            subModeColor.rgbSet = BleParser.parseColorModeColors(colorBytes);
            if (gradualBytes != null) {
                int gradual = Gradual4BleWifiController.parseGradual(gradualBytes);
                if (gradual != -1) {
                    subModeColor.gradual = gradual;
                }
            }
            subMode = subModeColor;
        } else if (subModeColorVersion == 1) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.rgbSet = BleParser.parseColorModeColors(colorBytes);
            if (gradualBytes != null) {
                int gradual = Gradual4BleWifiController.parseGradual(gradualBytes);
                if (gradual != -1) {
                    subModeColor.gradual = gradual;
                }
            }
            subMode = subModeColor;
        } else if (subModeColorVersion == 2) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            /*解析各个IC的色值*/
            subModeColor.rgbSet = BleParser.parseColorModeColorsWithBrightness(colorBytes);
            subModeColor.brightnessSet = BleParser.parseColorModeColors4Brightness(colorBytes);
            if (gradualBytes != null) {
                int gradual = Gradual4BleWifiController.parseGradual(gradualBytes);
                if (gradual != -1) {
                    subModeColor.gradual = gradual;
                }
            }
            subMode = subModeColor;
        } else {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "parsePtRealColorModeV1() subModeColorVersion = " + subModeColorVersion + " ; 暂不支持该subModeColor解析");
            }
            return null;
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        return mode;
    }

    /**
     * 解析status内的透传指令的模式信息
     *
     * @param ptBytes
     * @return
     */
    public static Mode parseStatusPtModeV1(List<byte[]> ptBytes) {
        if (ptBytes == null || ptBytes.isEmpty()) return null;
        List<byte[]> modeBytes = new ArrayList<>();
        boolean isColorMode = false;
        for (byte[] ptByte : ptBytes) {
            if (ptByte != null && ptByte.length == 20) {
                byte op = ptByte[1];
                if (op == BleProtocolConstants.SINGLE_MODE) {
                    /*加入模式包*/
                    modeBytes.add(ptByte);
                    /*若当前是颜色模式，则后续紧跟着的是颜色参数*/
                    byte subModeByte = ptByte[2];
                    if (subModeByte == BleProtocol.sub_mode_color_v2 || subModeByte == BleProtocol.sub_mode_color) {
                        isColorMode = true;
                        continue;
                    } else {
                        break;
                    }
                }
                /*若当前是颜色模式，则接着解析颜色数据*/
                if (isColorMode) {
                    if (op == BleProtocolConstants.MSG_READ_BULB_LIGHT_COLOR_WITH_BRIGHTNESS || op == BleProtocol.MSG_TYPE_READ_BULB_COLOR) {
                        modeBytes.add(ptByte);
                    } else {
                        break;
                    }
                }
            }
        }
        if (modeBytes.isEmpty()) return null;
        byte[] mode20Bytes = modeBytes.remove(0);
        Mode mode = new Mode();
        mode.parse(BleUtil.parseValidBleBytes(mode20Bytes));
        if (isColorMode) {
            /*解析是否是旧的颜色模式*/
            byte subModeCommandType = mode20Bytes[2];
            if (subModeCommandType == BleProtocol.sub_mode_color) {
                /*解析渐变值*/
                SubModeColor subModeColor = new SubModeColor();
                subModeColor.parse(BleUtil.parseValidBleBytes4SubMode(mode20Bytes));
                /*解析各个IC的色值*/
                subModeColor.rgbSet = BleParser.parseColorModeColors(modeBytes);
                mode.subMode = subModeColor;
            } else {
                /*解析渐变值*/
                SubModeColorV2 subModeColor = new SubModeColorV2();
                subModeColor.parse(BleUtil.parseValidBleBytes4SubMode(mode20Bytes));
                /*判断读取灯串颜色的指令-0xa2还是0xa5*/
                if (!modeBytes.isEmpty()) {
                    byte[] bytes = modeBytes.get(0);
                    if (bytes[1] == BleProtocol.MSG_TYPE_READ_BULB_COLOR) {
                        subModeColor.rgbSet = BleParser.parseColorModeColors(modeBytes);
                    } else {
                        /*解析各个IC的色值*/
                        subModeColor.rgbSet = BleParser.parseColorModeColorsWithBrightness(modeBytes);
                        subModeColor.brightnessSet = BleParser.parseColorModeColors4Brightness(modeBytes);
                    }
                }
                mode.subMode = subModeColor;
            }
        }
        return mode;
    }

    @Keep
    static class State {
        public int onOff;
        public int brightness;
        public int mode;
    }

}