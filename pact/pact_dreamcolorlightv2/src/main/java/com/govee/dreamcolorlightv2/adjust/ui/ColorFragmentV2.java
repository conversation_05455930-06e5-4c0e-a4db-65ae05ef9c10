package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.ui.R;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

import com.govee.base2home.Constant;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.v1.AbsColorFragmentV8;

import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.EventChangeGradual;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;
import com.govee.dreamcolorlightv2.pact.Support;
import com.govee.ui.Cons;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.Arrays;

/**
 * Create by hey on 2021/2/5
 * $
 */
public class ColorFragmentV2 extends AbsColorFragmentV8 {
    private SubModeColorV2 subModeColor = new SubModeColorV2();

    @Override
    protected void init() {
        super.init();
        setRelativeBrightnessProgressCheckHandler((isNewEvent, type) -> {
            if (Support.isColorTemRealDevice(getGoodsType())) {
                boolean allColorTem = SubModeColorV2.isAllColorTem(rgb, bulb_num_max);
                if (allColorTem) {
                    toast(R.string.no_support_brightness_in_tem);
                    return false;
                }
            }
            return true;
        });
        updateUi();
    }

    @Override
    public String getSku() {
        if (!getCanColorStripHadWhite()) return ParamFixedValue.group;
        return super.getSku();
    }

    @Override
    protected void colorChange(int type, int color, int kevin, boolean[] checks, int brightness) {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        if (color == 0 && kevin == 0) {
            subModeColor.brightness = brightness;
        } else if (kevin == 0) {
            subModeColor.setRgb(color);
        } else {
            subModeColor.setTemRgbAndKelvin(color, kevin);
        }
        subModeColor.ctlLight = checks;
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        EventBus.getDefault().post(mode);
        /*统计颜色变更来源*/
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, getSku(), ParamFixedValue.getColorFromTypeStr(type));
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.color_mode, ParamFixedValue.times);
        if (type == Cons.color_from_type_wcBar && kevin > 0) {
            /*统计颜色色温k值*/
            AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.temperature_ + kevin);
        }
    }

    private void updateUi() {
        if (isViewInflateOk()) {
            int opType = subModeColor.opType;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "updateUi() opType = " + opType);
            }
            if (opType == SubModeColorV2.op_type_ui_no_fresh) return;
            if (opType == SubModeColorV2.op_type_gradual) {
                /*更新渐变开关*/
                updateGradual(subModeColor.gradual == 1);
                subModeColor.opType = SubModeColorV2.op_type_ui_no_fresh;
                return;
            }
            subModeColor.opType = SubModeColorV2.op_type_ui_no_fresh;
            int rgb = subModeColor.rgb;
            int ctRgb = subModeColor.temRgb;
            int brightness = subModeColor.brightness;
            /*当前处于设置分段颜色逻辑*/
            if ((subModeColor.rgb == 0 && ColorUtils.isColorTem(ctRgb)) || (subModeColor.rgb == ColorUtils.toWhite() && ColorUtils.isColorTem(ctRgb))) {
                updateSelectedBulbColor(subModeColor.temRgb);
                boolean colorTemRealDevice = Support.isColorTemRealDevice(getGoodsType());
                if (colorTemRealDevice) {
                    /*若当前是真色温设备-则整条灯带都是色温值，且亮度为100%*/
                    subModeColor.rgbSet = new int[bulb_num_max];
                    Arrays.fill(subModeColor.rgbSet, ctRgb);
                    subModeColor.brightnessSet = new int[bulb_num_max];
                    Arrays.fill(subModeColor.brightnessSet, 100);
                    setRelativeBrightnessProgressCenter();
                }
            } else if (rgb != ColorUtils.toColor(0, 0, 0) && rgb != 0 && rgb != ResUtil.getColor(R.color.black)) {
                updateSelectedBulbColor(rgb);
                boolean colorTemRealDevice = Support.isColorTemRealDevice(getGoodsType());
                if (colorTemRealDevice) {
                    /*若当前是真色温设备-则整条灯带都是除选中位置，其余色温位置都是无色*/
                    int[] newRgbSet = checkCurHadColorTemInColorList(this.rgb);
                    if (newRgbSet != null && newRgbSet.length > 0) {
                        subModeColor.rgbSet = newRgbSet;
                    }
                }
            } else if (brightness == 0 && subModeColor.brightnessSet == null) {
                updateSelectedBulbColor(ResUtil.getColor(R.color.ui_light_style_1));
            }
            if (brightness != 0) {
                updateSelectedBulbBrightness(brightness);
            }
            int[] rgbSet = subModeColor.rgbSet;
            if (rgbSet != null && rgbSet.length > 0) {
                /*当前处于设置全部颜色逻辑*/
                updateBulbUI(rgbSet);
            }
            int[] brightnessSet = subModeColor.brightnessSet;
            if (brightnessSet != null && brightnessSet.length > 0) {
                updateBulbBrightnessUI(brightnessSet);
            }
            if (ColorUtils.isColorTem(rgb)) ctRgb = rgb;/*wifi透传色温*/
            /*更新色块和色条颜色*/
            boolean colorTem = ColorUtils.isColorTem(ctRgb);
            if (colorTem) {
                setColorWithTemColor(ColorUtils.toWhite(), ctRgb);
            } else {
                setColorWithTemColor(rgb, 0);
            }
            /*更新渐变开关*/
            updateGradual(subModeColor.gradual == 1);
        }
    }

    @Nullable
    private int[] checkCurHadColorTemInColorList(int[] rgbSet) {
        if (rgbSet == null || rgbSet.length == 0) return null;
        int[] newColorSet = new int[rgbSet.length];
        int noColor = ColorUtils.toNoColor();
        boolean hadColorTem = false;
        for (int i = 0; i < rgbSet.length; i++) {
            newColorSet[i] = rgbSet[i];
            if (Constant.isColorTemp(rgbSet[i])) {
                hadColorTem = true;
                newColorSet[i] = noColor;
            }
        }
        if (hadColorTem) {
            return newColorSet;
        }
        return null;
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeColorV2) {
            subModeColor = (SubModeColorV2) subMode;
            ((SubModeColorV2) subMode).checkAnalytic4SubModeUse(getSku());
            updateUi();
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    protected void onGradualChange() {
        boolean gradualOpen = subModeColor.gradual == 1;
        EventChangeGradual.sendEventChangeGradual(!gradualOpen);
    }

    @Override
    protected boolean supportRealTimeColor() {
        /*支持实时颜色设置*/
        return true;
    }

    @NonNull
    @Override
    protected ISubMode makePartChooseSubMode(boolean[] check, int color) {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        subModeColor.setRgb(color);
        subModeColor.ctlLight = check;
        return subModeColor;
    }

    @Override
    public ISubMode getCompleteSubMode() {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        if (rgb == null) return null;
        int[] rgbArray = Arrays.copyOf(rgb, bulb_num_max);
        int[] brightnessArray = new int[bulb_num_max];
        for (int i = 0; i < bulb_num_max; i++) {
            brightnessArray[i] = 100;
        }
        subModeColor.rgbSet = rgbArray;
        subModeColor.brightnessSet = brightnessArray;
        subModeColor.gradual = this.subModeColor.gradual;
        return subModeColor;
    }
}
