package com.govee.dreamcolorlightv2.pact.bleiot;

import com.govee.dreamcolorlightv2.pact.Support;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/8/13
 * $
 */
public class EffectOp4BleOp extends AbsEffectOp4BleIot {
    @Override
    protected int[] supportGoodsType() {
        return Support.effect4OpBleWifiGoodsTypes;
    }

    private EffectOp4BleOp() {
    }

    private static class Builder {
        private static final EffectOp4BleOp instance = new EffectOp4BleOp();
    }

    public static EffectOp4BleOp op = Builder.instance;
}