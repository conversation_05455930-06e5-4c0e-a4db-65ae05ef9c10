package com.govee.dreamcolorlightv2.ble;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2home.util.NumberUtil;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v2.DiyTemplate;
import com.govee.base2light.ac.diy.v2.DiyValue;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.comm.AbsBleCommGroup;
import com.govee.base2light.ble.comm.AbsMultipleBleCommGroup;
import com.govee.base2light.ble.controller.AbsController;
import com.govee.base2light.ble.controller.AbsMultipleControllerV1;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultiDiyGraffitiController;
import com.govee.base2light.ble.controller.MultipleDiyController;
import com.govee.base2light.ble.controller.MultipleDiyControllerV1;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.mic.MicBleProtocol;
import com.govee.base2light.ble.mic.controller.MicSetRgbController;
import com.govee.base2light.group.ble.AbsGroupBle;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.dreamcolorlightv2.ConsV1;
import com.govee.dreamcolorlightv2.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import java.util.Arrays;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/11/11
 * 群控ble$
 */
public class GroupBle extends AbsGroupBle {
    private static final String TAG = "GroupBle";
    private final boolean supportMicByPhoneColorMode;
    private final boolean supportMicByPhone;

    public GroupBle(DeviceModel deviceModel, String bleAddress) {
        super(deviceModel, bleAddress);
        supportMicByPhoneColorMode = AbsMicFragmentV4.SupportMicStatus.support_color_order
                == AbsMicFragmentV4.getSupportMicModeStatus(sku, deviceModel.device);
        supportMicByPhone = AbsMicFragmentV4.SupportMicStatus.support_new_order
                == AbsMicFragmentV4.getSupportMicModeStatus(sku, deviceModel.device);
    }

    @Override
    protected AbsMultipleBleCommGroup generateMultipleBleComm() {
        return new BleMultiCommGroup();
    }

    @Override
    protected AbsBleCommGroup generateBleComm() {
        return new BleCommGroup();
    }

    @Override
    protected boolean needSkipHeart() {
        return inMultipleComm();
    }

    @Override
    public AbsSingleController makeController(byte[] sendBytes) {
        if (sendBytes == null || sendBytes.length < 2) return null;
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "sku = " + sku + " ; makeController() sendBytes = " + BleUtil.bytesToHexString(sendBytes)
                    + "\t supportMicByPhoneColorMode = " + supportMicByPhoneColorMode
                    + "\t supportMicByPhone = " + supportMicByPhone);
        }
        String sku = deviceModel.getSku();
        if (sendBytes[0] == MicBleProtocol.mic_mode_start) {
            if (supportMicByPhoneColorMode) {
                Mode mode = new Mode();
                mode.subMode = SubModeColor.makeSubModeColor(ColorUtils.toColor(sendBytes[1], sendBytes[2], sendBytes[3]));
                return new ModeController(mode);
            } else if (supportMicByPhone) {
                return new MicSetRgbController(new byte[]{sendBytes[1], sendBytes[2], sendBytes[3]});
            }
        }
        /*颜色模式需要转化*/
        if (sendBytes[0] == BleProtocol.sub_mode_color) {
            boolean supportNewColorMode = Support.supportNewColorMode(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft)
                    || Support.isBKProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
            ISubMode subMode;
            if (supportNewColorMode) {
                subMode = SubModeColorV2.parseSubModeColor2New(sendBytes);
            } else {
                subMode = SubModeColor.parseWriteBytes(sendBytes);
            }
            Mode mode = new Mode();
            mode.subMode = subMode;
            return new ModeController(mode);
        }
        if (sendBytes[0] == BleProtocol.sub_mode_color_v2) {
            ISubMode subMode;
            boolean supportNewColorMode = Support.supportSubModeColor4PartBrightness(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard,
                    deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
            if (supportNewColorMode) {
                subMode = SubModeColorV2.parseWriteBytes(sendBytes);
            } else {
                /*在不支持分段亮度的版本设亮度返null*/
                if (sendBytes[1] == 0x02) {
                    return null;
                }
                subMode = SubModeColor.parseWriteBytesV2(sendBytes);
            }
            Mode mode = new Mode();
            mode.subMode = subMode;
            return new ModeController(mode);
        }
        /*渐变开关需要转化*/
        if (sendBytes[0] == BleProtocolConstants.SINGLE_WRITE && sendBytes[1] == BleProtocol.SINGLE_GRADUAL_CHANGE_4_WIFI_BLE) {
            int goodsType = deviceModel.getGoodsType();
            /*不支持渐变指令的产品*/
            if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1 ||
                    goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1
                    || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA
            ) {
                return null;
            }
            boolean gradualOpen = sendBytes[2] == 0x01;
            boolean bkProtocol = Support.isBKProtocol(sku, deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
            /*bk协议，统一采用新的渐变开关方式*/
            if (bkProtocol) {
                return new Gradual4BleWifiController(gradualOpen);
            }
            if (goodsType == GoodsType.GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1 ||
                    goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1 ||
                    goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1) {
                /*蓝牙类-指令是蓝牙指令*/
                return new GradualController(gradualOpen);
            } else if (Support.isBleWifiLight(deviceModel.getSku())) {
                /*ble+wifi设备 渐变用a3指令*/
                return new Gradual4BleWifiController(gradualOpen);
            } else if (Support.isOldLight(deviceModel.getSku())) {
                /*蓝牙类-指令是蓝牙指令*/
                return new GradualController(gradualOpen);
            }
            return new Gradual4BleWifiController(gradualOpen);
        }
        /*亮度指令需要转化-区分BK;传递进来的是亮度范围1-100；bk下的亮度范围是1-100*/
        if (sendBytes[0] == BleProtocolConstants.SINGLE_BRIGHTNESS) {
            int brightness = BleUtil.getUnsignedByte(sendBytes[1]);
            int goodsType = deviceModel.getGoodsType();
            int pactType = deviceModel.pactType;
            int pactCode = deviceModel.pactCode;
            int[] brightnessRange = Support.getBrightnessRange(sku, goodsType, pactType, pactCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeController() brightnessRange = " + Arrays.toString(brightnessRange));
            }
            /*无需转化，对应百分比*/
            if (brightnessRange[0] == 0) {
                return new BrightnessController(brightness);
            } else {
                int realMin = brightnessRange[1];
                int realMax = brightnessRange[2];
                int realBrightness = NumberUtil.calculateProgress(realMax, realMin, brightness);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "makeController() realBrightness = " + realBrightness);
                }
                return new BrightnessController(realBrightness);
            }
        }
        if (sendBytes[0] == BleProtocolConstants.SINGLE_WRITE && sendBytes[1] == BleProtocolConstants.SINGLE_MODE && sendBytes[2] == BleProtocol.sub_mode_color_v2) {
            ISubMode subMode;
            boolean supportNewColorMode = Support.supportSubModeColor4PartBrightness(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard,
                    deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
            byte[] validBytes = new byte[sendBytes.length - 4];
            System.arraycopy(sendBytes, 3, validBytes, 0, validBytes.length);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeController() supportNewColorMode = " + supportNewColorMode + " validBytes = " + BleUtil.bytesToHexString(validBytes));
            }
            if (supportNewColorMode) {
                subMode = SubModeColorV2.parseSubModeColor4Write(validBytes);
            } else {
                if (validBytes[0] == 0x03) {
                    return null;
                } else {
                    byte[] validBytes4rgb = new byte[validBytes.length - 1];
                    System.arraycopy(validBytes, 1, validBytes4rgb, 0, validBytes4rgb.length);
                    subMode = SubModeColor.parseSubModeColor4WriteV2(validBytes4rgb);
                }
            }
            Mode mode = new Mode();
            mode.subMode = subMode;
            return new ModeController(mode);
        }
        if (sendBytes[0] == BleProtocolConstants.SINGLE_WRITE && sendBytes[1] == BleProtocolConstants.SINGLE_MODE && sendBytes[2] == BleProtocol.sub_mode_color) {
            ISubMode subMode;
            boolean supportNewColorMode = Support.supportSubModeColor4PartBrightness(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard,
                    deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
            byte[] validBytes = new byte[sendBytes.length - 4];
            System.arraycopy(sendBytes, 3, validBytes, 0, validBytes.length);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeController() supportNewColorMode = " + supportNewColorMode + " validBytes = " + BleUtil.bytesToHexString(validBytes));
            }
            if (supportNewColorMode) {
                subMode = SubModeColorV2.parseSubModeColor4WriteV2(validBytes);
            } else {
                subMode = SubModeColor.parseSubModeColor4Write(validBytes);
            }
            Mode mode = new Mode();
            mode.subMode = subMode;
            return new ModeController(mode);
        }
        return null;
    }

    @Override
    protected AbsSingleController getOnController() {
        return new SwitchController(true);
    }

    @Override
    public AbsSingleController makeController(@NonNull ISubMode subMode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "makeController() subMode = " + subMode);
        }
        /*旧协议-0x11的ui风格*/
        if (subMode instanceof SubModeMusic) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeController()---SubModeMusic");
            }
            return parseSubMusicV0((SubModeMusic) subMode);
        }
        boolean supportMultiNewMusic4TelinkV1 = Support.supportMultiNewMusic4TelinkV1(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "makeController() supportMultiNewMusic4TelinkV1 = " + supportMultiNewMusic4TelinkV1);
        }
        /*旧协议-0x11的新ui风格*/
        if (subMode instanceof SubModeMusicMultiV1) {
            boolean newMusic = ((SubModeMusicMultiV1) subMode).isNewMusic();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeController() SubModeMusicMultiV1 newMusic = " + newMusic);
            }
            boolean supportMultiNewMusic4Telink = Support.supportMultiNewMusic4Telink(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeController() supportMultiNewMusic4Telink = " + supportMultiNewMusic4Telink);
            }
            if (supportMultiNewMusic4Telink) {
                Mode mode = new Mode();
                mode.subMode = subMode;
                return new ModeController(mode);
            }
            if (newMusic && supportMultiNewMusic4TelinkV1) {
                /*转化成旧协议-0x0C的新ui风格*/
                Mode mode = new Mode();
                mode.subMode = ((SubModeMusicMultiV1) subMode).toSubModeMultiV1_1();
                return new ModeController(mode);
            }
            if (newMusic) {
                /*不是支持新音乐模式的设备-不执行*/
                return null;
            }
            /*非新音乐模式-能量+节奏+滚动+频谱都是通用支持的*/
            SubModeMusic subModeMusic = ((SubModeMusicMultiV1) subMode).toSubModeMusic();
            return parseSubMusicV0(subModeMusic);
        }
        /*新音乐协议-0x13的新ui风格*/
        if (subMode instanceof SubModeMusicMultiV2) {
            boolean newMusic = ((SubModeMusicMultiV2) subMode).isNewMusic();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeController() SubModeMusicMultiV2 newMusic = " + newMusic);
            }
            int version = Support.getNewMusicModeVersion(deviceModel.getGoodsType(), deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeController() version = " + version);
            }
            if (version == ConsV1.multi_new_music_version_v1) {
                Mode mode = new Mode();
                mode.subMode = subMode;
                return new ModeController(mode);
            }
            if (version == ConsV1.multi_new_music_version_v2) {
                Mode mode = new Mode();
                mode.subMode = SubModeMusicMultiV3.toNewSubModeMusic((SubModeMusicMultiV2) subMode);
                return new ModeController(mode);
            }
            /*支持新音乐模式*/
            if (newMusic) {
                if (version == ConsV1.multi_new_music_version_v0) {
                    Mode mode = new Mode();
                    mode.subMode = supportMultiNewMusic4TelinkV1 ? ((SubModeMusicMultiV2) subMode).toSubModeMultiV1_1() : ((SubModeMusicMultiV2) subMode).toSubModeMultiV1();
                    return new ModeController(mode);
                }
                /*不支持新音乐模式的设备-不执行*/
                return null;
            }
            /*非新的音乐模式-那就是都支持的节奏模式-动感+柔和*/
            SubModeMusic subModeMusic = ((SubModeMusicMultiV2) subMode).toSubModeMusic();
            return parseSubMusicV0(subModeMusic);
        }
        /*新音乐协议-0x13的新ui风格-非多包协议字节描述一致8bytes*/
        if (subMode instanceof SubModeMusicMultiV3) {
            boolean newMusic = ((SubModeMusicMultiV3) subMode).isNewMusic();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeController() SubModeMusicMultiV3 newMusic = " + newMusic);
            }
            int version = Support.getNewMusicModeVersion(deviceModel.getGoodsType(), deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeController() version = " + version);
            }
            if (version == ConsV1.multi_new_music_version_v2) {
                Mode mode = new Mode();
                mode.subMode = subMode;
                return new ModeController(mode);
            }
            if (version == ConsV1.multi_new_music_version_v1) {
                Mode mode = new Mode();
                mode.subMode = SubModeMusicMultiV2.toNewSubModeMusic((SubModeMusicMultiV3) subMode);
                return new ModeController(mode);
            }
            /*支持新音乐模式*/
            if (newMusic) {
                if (version == ConsV1.multi_new_music_version_v0) {
                    Mode mode = new Mode();
                    mode.subMode = supportMultiNewMusic4TelinkV1 ? ((SubModeMusicMultiV3) subMode).toSubModeMultiV1_1() : ((SubModeMusicMultiV3) subMode).toSubModeMultiV1();
                    return new ModeController(mode);
                }
                /*不支持新音乐模式的设备-不执行*/
                return null;
            }
            /*非新的音乐模式-那就是都支持的节奏模式-动感+柔和*/
            SubModeMusic subModeMusic = ((SubModeMusicMultiV3) subMode).toSubModeMusic();
            return parseSubMusicV0(subModeMusic);
        }
        return null;
    }

    private AbsSingleController parseSubMusicV0(SubModeMusic subMode) {
        int goodsType = deviceModel.getGoodsType();
        int pactType = deviceModel.pactType;
        int pactCode = deviceModel.pactCode;
        boolean bk = Support.isBKProtocol(sku, goodsType, pactType, pactCode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "makeController() sub_mode_music sku = " + sku + " ; bk = " + bk);
        }
        Mode mode = new Mode();
        if (bk) {
            mode.subMode = SubModeMusicV1.parse(subMode);
        } else {
            /*该goodsType下的音乐模式是旧协议*/
            if (goodsType == GoodsType.GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1) {
                mode.subMode = subMode.toSubModeMusicOldV0();
            } else {
                mode.subMode = subMode;
            }
        }
        return new ModeController(mode);
    }

    @Override
    public AbsMultipleControllerV1 makeControllerV14Scenes(int effect) {
        Mode mode = new Mode();
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.setEffect(effect);
        mode.subMode = subModeScenes;
        int version = Support.newScenesVersion(deviceModel.getGoodsType(), deviceModel.versionSoft, deviceModel.pactCode);
        return Support.is2NewScenesMode(deviceModel.getSku(), deviceModel.device, mode, version);
    }

    @Override
    public AbsMultipleControllerV1 makeControllerV14DiyTemplate(DiyTemplate diyTemplate) {
        /*diy模版；有特定区分sku进行ic数转化的，需要重新生成指令effectStr*/
        String effectStr = ScenesOp.changeEffectStr4Sku(deviceModel, diyTemplate);
        return ScenesOp.parseDiyTemplate(diyTemplate.scenesCode, diyTemplate.diyCode, effectStr);
    }

    @Override
    public AbsMultipleControllerV1 makeControllerV14DiyStudio(DiyStudio diyStudio) {
        /*需要区分关于亮度的算法值变化*/
        boolean supportRgbicV1 = Support.supportRgbicV1(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard, deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "makeControllerV14DiyStudio() supportRgbicV1 = " + supportRgbicV1);
        }
        return diyStudio.toMulti4Scenes4RgbicV1(supportRgbicV1 ? 1 : 0);
    }

    @Override
    public AbsController makeController4DiyProtocol(DiyProtocol diyProtocol) {
        int goodsType = deviceModel.getGoodsType();
        int pactType = deviceModel.pactType;
        int pactCode = deviceModel.pactCode;
        boolean bk = Support.isBKProtocol(deviceModel.getSku(), goodsType, pactType, pactCode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "makeController4DiyProtocol() bk = " + bk);
        }
        if (bk) {
            return new MultipleDiyControllerV1(diyProtocol);
        } else {
            return new MultipleDiyController(diyProtocol);
        }
    }

    @Override
    public AbsMultipleControllerV1 makeControllerV14DiyGraffitiV2(@NonNull DiySupportV1 supportV1, DiyValue diyValue) {
        /*若当前设备不支持rgbic涂鸦效果，则返回null*/
        boolean supportSubModeColor4PartBrightness = Support.supportSubModeColor4PartBrightness(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard, deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
        if (!supportSubModeColor4PartBrightness) return null;
        DiyValue newDiyValue = supportV1.changeParamsCheck(diyValue, deviceModel.ic);
        newDiyValue.checkParams();
        DiyGraffitiV2 diyGraffitiV2 = newDiyValue.toDiyGraffiti4Apply4Rgbic();
        if (diyGraffitiV2 != null) {
            return new MultiDiyGraffitiController(diyGraffitiV2.getDiyCode(), diyGraffitiV2.getEffectBytes());
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.e(TAG, "makeControllerV14DiyGraffitiV2()");
        }
        return null;
    }

    @Override
    protected boolean isSupportMultiNewMusic() {
        return Support.supportMultiMusic(deviceModel.getGoodsType(), deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard);
    }

    @Override
    public boolean isSupportColorStripMulti() {
        return Support.supportColorStripMulti(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
    }
}