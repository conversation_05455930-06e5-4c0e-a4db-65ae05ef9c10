package com.govee.dreamcolorlightv2.ble;

import com.govee.base2light.ble.controller.AbsParamsSubMode;
import com.govee.base2light.ble.controller.ISubMode;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2021/5/12
 * 音乐模式的参数支持subMode-telink$
 */
public class ParamsSubMode4MusicTelink extends AbsParamsSubMode {

    @Override
    public ISubMode toSupportSubMode(int version) {
        if (version == 1) {
            SubModeMusicMultiV1 subModeMusicV2 = new SubModeMusicMultiV1();
            subModeMusicV2.parse(validBytes);
            return subModeMusicV2;
        }
        SubModeMusic subModeMusic = new SubModeMusic();
        subModeMusic.parse(validBytes);
        return subModeMusic;
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_music;
    }
}