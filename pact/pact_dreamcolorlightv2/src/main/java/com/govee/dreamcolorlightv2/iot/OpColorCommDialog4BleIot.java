package com.govee.dreamcolorlightv2.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsOpCommDialog4BleIot;
import com.govee.dreamcolorlightv2.ble.Ble;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.ModeController;
import com.govee.dreamcolorlightv2.ble.SubModeColor;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by lins<PERSON><PERSON> on 1/28/21
 * op color操作应用$
 */
public class OpColorCommDialog4BleIot extends AbsOpCommDialog4BleIot {
    private AbsCmd absCmd;
    private AbsSingleController controller;

    protected OpColorCommDialog4BleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isBk, boolean isSupportBrightness) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_COLOR, -1);
        absCmd = CmdColorWc.makeCmdColorWc4Color(colorEffect.colorSet[0]);
        ISubMode subModeColor = isBk || isSupportBrightness ? SubModeColorV2.makeSubModeColor(colorEffect.colorSet[0]) : SubModeColor.makeSubModeColor(colorEffect.colorSet[0]);
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        controller = new ModeController(mode);
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isBk, boolean isSupportBrightness) {
        new OpColorCommDialog4BleIot(context, bleAddress, bleName, topic, sku, device, colorEffect, isBk, isSupportBrightness).show();
    }

    @Nullable
    @Override
    protected AbsCmd getOpCmd() {
        return absCmd;
    }

    @Override
    protected void bleOping() {
        if (controller != null) {
            getBle().startController(controller);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateBleResult(result);
        hide();

    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}