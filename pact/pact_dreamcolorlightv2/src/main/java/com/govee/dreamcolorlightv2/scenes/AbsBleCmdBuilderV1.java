package com.govee.dreamcolorlightv2.scenes;

import com.govee.base2home.scenes.builder.BaseCmdModel;
import com.govee.base2home.scenes.builder.BleCmdBuilder;
import com.govee.dreamcolorlightv2.ble.BleComm;
import com.govee.dreamcolorlightv2.pact.Support;

import java.util.UUID;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * ble-抽象cmd构造器v1版本$
 */
public abstract class AbsBleCmdBuilderV1<T extends BaseCmdModel> extends BleCmdBuilder<T> {
    @Override
    public String[] getSupportKeys() {
        return Support.supportBleGoodsTypeSet;
    }

    @Override
    public UUID getServiceUUID(String key) {
        return BleComm.serviceUuid;
    }

    @Override
    public UUID getCharacteristicUUID(String key) {
        return BleComm.characteristicUuid;
    }
}