package com.govee.dreamcolorlightv2.add;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020/3/6
 * 添加信息$
 */
public class AddInfo implements Parcelable {
    /*广播字段*/
    public String sku;
    public int goodsType;
    public String bleName;
    public String bleAddress;
    public String deviceName;
    public int pactType;
    public int pactCode;
    /*协议字段*/
    public String versionSoft;
    public String versionHard;
    public String device;

    public String wifiSoftVersion;
    public String wifiHardVersion;
    public String wifiMac;

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.sku);
        dest.writeInt(this.goodsType);
        dest.writeString(this.bleName);
        dest.writeString(this.bleAddress);
        dest.writeString(this.deviceName);
        dest.writeInt(this.pactType);
        dest.writeInt(this.pactCode);
        dest.writeString(this.versionSoft);
        dest.writeString(this.versionHard);
        dest.writeString(this.device);
        dest.writeString(this.wifiSoftVersion);
        dest.writeString(this.wifiHardVersion);
        dest.writeString(this.wifiMac);
    }

    public AddInfo() {
    }

    protected AddInfo(Parcel in) {
        this.sku = in.readString();
        this.goodsType = in.readInt();
        this.bleName = in.readString();
        this.bleAddress = in.readString();
        this.deviceName = in.readString();
        this.pactType = in.readInt();
        this.pactCode = in.readInt();
        this.versionSoft = in.readString();
        this.versionHard = in.readString();
        this.device = in.readString();
        this.wifiSoftVersion = in.readString();
        this.wifiHardVersion = in.readString();
        this.wifiMac = in.readString();
    }

    public static final Creator<AddInfo> CREATOR = new Creator<AddInfo>() {
        @Override
        public AddInfo createFromParcel(Parcel source) {
            return new AddInfo(source);
        }

        @Override
        public AddInfo[] newArray(int size) {
            return new AddInfo[size];
        }
    };
}