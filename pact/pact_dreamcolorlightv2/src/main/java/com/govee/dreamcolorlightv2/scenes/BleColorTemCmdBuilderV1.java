package com.govee.dreamcolorlightv2.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.ColorTempModel;
import com.govee.dreamcolorlightv2.pact.Comm;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * ble-色温控制$
 */
public class BleColorTemCmdBuilderV1 extends AbsBleCmdBuilderV1<ColorTempModel> {
    @Override
    public IBleCmd createCmd(ColorTempModel colorTempModel) {
        return () -> Comm.makeColorTemController4BleComm(colorTempModel.sku, colorTempModel.goodsType, colorTempModel.pactType, colorTempModel.pactCode, colorTempModel.model.versionSoft, colorTempModel.model.versionHard, colorTempModel.temColor).getValue();
    }
}