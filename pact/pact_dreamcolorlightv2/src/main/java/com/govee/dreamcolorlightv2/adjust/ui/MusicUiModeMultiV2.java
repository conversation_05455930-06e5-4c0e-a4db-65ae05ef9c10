package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsMusicNoIcUiMode;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV2;
import com.govee.ui.R;


/**
 * Create by xieying<PERSON> on 2019-07-22
 * music ui mode
 */
public class MusicUiModeMultiV2 extends AbsMusicNoIcUiMode {
    public MusicUiModeMultiV2(String sku, String device) {
        super(sku);
        this.device = device;
    }

    @Override
    public AbsModeFragment getUiFragment() {
        MusicFragmentMultiV2 fragment = new MusicFragmentMultiV2();
        fragment.makeArguments(getSku(), device);
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_music_mini, R.mipmap.new_control_light_btb_mode_music_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeMusicMultiV2 subModeMusic = new SubModeMusicMultiV2();
        subModeMusic.loadLocal();
        return subModeMusic;
    }
}
