package com.govee.dreamcolorlightv2.ble;

import com.govee.base2light.ble.controller.AbsOnlyReadSingleController;
import com.govee.base2light.ble.controller.BleProtocolConstants;

/**
 * Create by hey on 2021/2/5
 * $
 */
public class BulbStringColorControllerV2 extends AbsOnlyReadSingleController {
    private int group;

    public BulbStringColorControllerV2(int group) {
        this.group = group;
    }

    @Override
    protected void fail() {
        EventBulbStringColorV2.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocolConstants.MSG_READ_BULB_LIGHT_COLOR_WITH_BRIGHTNESS;
    }

    @Override
    protected byte[] translateRead() {
        return new byte[]{(byte) group};
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        BulbGroupColorV2 bulbGroupColor = BulbGroupColorV2.parseBytes(validBytes);
        EventBulbStringColorV2.sendSuc(isWrite(), getCommandType(), getProType(), bulbGroupColor);
        return true;
    }

    public static boolean isReadBulbStringColorController(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opType = original20Bytes[1];
            return opTypeByte == BleProtocolConstants.SINGLE_READ &&
                    opType == BleProtocolConstants.MSG_READ_BULB_LIGHT_COLOR_WITH_BRIGHTNESS;
        }
        return false;
    }
}