package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.ui.R;
import android.os.Bundle;

import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsScenesUiMode;

import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.SubModeScenes;

/**
 * Create by xie<PERSON>wu on 2019-07-22
 * scenes ui mode
 */
public class ScenesUiModeV1 extends AbsScenesUiMode {
    public static final String intent_key_show_edit = "intent_key_show_edit";

    public ScenesUiModeV1(String sku,String device) {
        super(sku,device, true);
    }

    public ScenesUiModeV1(String sku,String device, boolean showEdit) {
        super(sku,device, showEdit);
    }

    @Override
    public AbsModeFragment getUiFragment() {
        ScenesFragmentV2 fragment = new ScenesFragmentV2();
        Bundle bundle = new Bundle();
        bundle.putBoolean(intent_key_show_edit, showEdit);
        fragment.addExtArguments(bundle, getSku(),getDevice());
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_scene_mini, R.mipmap.new_control_light_btb_mode_scenes_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_scenes;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.loadLocal();
        return subModeScenes;
    }
}
