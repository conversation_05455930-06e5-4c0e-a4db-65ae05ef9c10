package com.govee.dreamcolorlightv2.ble;

import androidx.annotation.NonNull;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022/7/19
 * 音乐模式ic-针对音乐效果支持ic
 */
public abstract class AbsMusicIc extends AbsSubMode4Analytic {
    private int ic4Music = -1;

    final public int ic4Music() {
        return ic4Music;
    }

    final public void updateIc4Music(int ic) {
        this.ic4Music = ic;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_music;
    }
}