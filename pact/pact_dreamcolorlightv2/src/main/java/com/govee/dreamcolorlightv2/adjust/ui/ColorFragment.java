package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.ui.R;
import androidx.annotation.NonNull;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.v1.AbsColorFragmentV3;

import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.EventChangeGradual;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.SubModeColor;
import com.govee.ui.Cons;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.Arrays;


/**
 * Create by xieyingwu on 2020/4/9
 */
public class ColorFragment extends AbsColorFragmentV3 {
    private SubModeColor subModeColor = new SubModeColor();

    @Override
    protected void init() {
        super.init();
        updateUi();
    }

    @Override
    protected void colorChange(int type, int color, int kevin, boolean[] checks) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgb = color;
        subModeColor.ctlLight = checks;
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        EventBus.getDefault().post(mode);
        /*统计颜色变更来源*/
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, getSku(), ParamFixedValue.getColorFromTypeStr(type));
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.color_mode, ParamFixedValue.times);
        if (type == Cons.color_from_type_wcBar && kevin > 0) {
            /*统计颜色色温k值*/
            AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.temperature_ + kevin);
        }
    }

    private void updateUi() {
        if (isViewInflateOk()) {
            int opType = subModeColor.opType;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "updateUi() opType = " + opType);
            }
            if (opType == SubModeColor.op_type_ui_no_fresh) return;/*ui已经刷新过*/
            if (opType == SubModeColor.op_type_gradual) {
                /*重置op的标志*/
                subModeColor.opType = SubModeColor.op_type_ui_no_fresh;
                /*更新渐变开关*/
                updateGradual(subModeColor.gradual == 1);
                return;
            }
            /*重置op的标志*/
            subModeColor.opType = SubModeColor.op_type_ui_no_fresh;
            int rgb = subModeColor.rgb;
            /*当前处于设置分段颜色逻辑*/
            if (rgb != ColorUtils.toColor(0, 0, 0) && rgb != 0 && rgb != ResUtil.getColor(R.color.black)) {
                updateSelectedBulbColor(rgb);
            } else {
                updateSelectedBulbColor(ResUtil.getColor(R.color.ui_light_style_1));
            }
            int[] rgbSet = subModeColor.rgbSet;
            if (rgbSet != null && rgbSet.length > 0) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "updateUi() 设置全段颜色");
                }
                /*当前处于设置全部颜色逻辑*/
                updateBulbUI(rgbSet);
            }
            boolean hadSelect = hadSelect();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "updateUi() hadSelect = " + hadSelect + " ; rgb = " + rgb);
            }
            if (!hadSelect) {
                rgb = 0;
            }
            /*更新色块和色条颜色*/
            boolean colorTem = ColorUtils.isColorTem(rgb);
            if (colorTem) {
                setColorWithTemColor(ColorUtils.toWhite(), rgb);
            } else {
                setColorWithTemColor(rgb, 0);
            }
            /*更新渐变开关*/
            updateGradual(subModeColor.gradual == 1);
        }
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeColor) {
            subModeColor = (SubModeColor) subMode;
            ((SubModeColor) subMode).checkAnalytic4SubModeUse(getSku());
            updateUi();
        }

    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color;
    }

    @Override
    protected void onGradualChange() {
        boolean gradualOpen = subModeColor.gradual == 1;
        EventChangeGradual.sendEventChangeGradual(!gradualOpen);
    }

    @Override
    protected boolean supportRealTimeColor() {
        /*支持实时颜色设置*/
        return true;
    }

    @NonNull
    @Override
    protected ISubMode makePartChooseSubMode(boolean[] check, int color) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgb = color;
        subModeColor.ctlLight = check;
        return subModeColor;
    }

    @Override
    public ISubMode getCompleteSubMode() {
        SubModeColor subModeColor = new SubModeColor();
        if (rgb == null) return null;
        int[] rgbArray = Arrays.copyOf(rgb, bulb_num_max);
        int[] brightnessArray = new int[bulb_num_max];
        for (int i = 0; i < bulb_num_max; i++) {
            brightnessArray[i] = 100;
        }
        subModeColor.rgbSet = rgbArray;
        subModeColor.gradual = this.subModeColor.gradual;
        return subModeColor;
    }
}
