package com.govee.dreamcolorlightv2.ble;

import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;

/**
 * Create by x<PERSON>ying<PERSON> on 2020/3/5
 * 球泡组色值$
 */
public class BulbGroupColor {
    public int group;
    public int[] rgb;

    public static BulbGroupColor parseBytes(byte[] validBytes) {
        BulbGroupColor bulbGroupColor = new BulbGroupColor();
        int[] rgb = new int[4];
        byte[] rgbBytes = new byte[3];
        int group = BleUtil.getUnsignedByte(validBytes[0]);
        int index = 1;
        for (int i = 0; i < rgb.length; i++) {
            System.arraycopy(validBytes, index, rgbBytes, 0, rgbBytes.length);
            int r = BleUtil.getUnsignedByte(rgbBytes[0]);
            int g = BleUtil.getUnsignedByte(rgbBytes[1]);
            int b = BleUtil.getUnsignedByte(rgbBytes[2]);
            rgb[i] = ColorUtils.toColor(r, g, b);
            index += 3;
        }
        bulbGroupColor.group = group;
        bulbGroupColor.rgb = rgb;
        return bulbGroupColor;
    }
}