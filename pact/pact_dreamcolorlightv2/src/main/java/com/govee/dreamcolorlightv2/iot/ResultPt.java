package com.govee.dreamcolorlightv2.iot;

import com.govee.base2home.util.Encode;
import com.govee.base2light.ble.BleUtil;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Keep;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2019-08-02
 * cmd=pt的解析处理
 */
@Keep
public class ResultPt {
    private static final String TAG = "ResultPt";
    List<String> command;

    public List<byte[]> getPtBytes() {
        List<byte[]> ptBytes = new ArrayList<>();
        if (command == null || command.isEmpty()) return ptBytes;
        for (String command : command) {
            byte[] commandBytes = Encode.decryByBase64(command);
            if (commandBytes != null) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "getPtBytes() commandBytes = " + BleUtil.bytesToHexString(commandBytes));
                }
                ptBytes.add(commandBytes);
            }
        }
        return ptBytes;
    }
}