package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.ui.mode.IUiMode;
import com.ihoment.base2app.infra.LogInfra;

import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by hey on 2021/2/24
 * $
 */
public class ModeUi4FactorV2 extends AbsMode4UIV1 {
    private static final String TAG = "ModeUi4FactorV2";

    public ModeUi4FactorV2(AppCompatActivity ac, String sku, String device, int goodsType) {
        super(ac, sku, device, goodsType);
    }

    @Override
    protected IUiMode getMode4() {
        return new DiyUiMode(sku, goodsType);
    }

    @Override
    protected IUiMode getMode3() {
        return new ScenesUiModeV1(sku, device, false);
    }

    @Override
    protected IUiMode getMode2() {
        return new ColorUiMode4Group(sku, goodsType);
    }

    @Override
    protected IUiMode getMode1() {
        return new MusicUiMode(sku, device);
    }

    @Override
    protected String getTAG() {
        return TAG;
    }

    public void checkMusicMode(int musicVersion) {
        boolean mic = AbsMicFragmentV4.isBleGroupMicByPhoneMode();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkMusicMode() mic = " + mic + " ; musicVersion = " + musicVersion);
        }
        IUiMode positionUiMode = getPositionUiMode(0);
        /*v0版本音乐模式*/
        if (musicVersion == 0) {
            boolean isMicV0 = positionUiMode instanceof MicUiMode;
            boolean isMusicV0 = positionUiMode instanceof MusicUiMode;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicMode() isMicV0 = " + isMicV0 + " ; isMusicV0 = " + isMusicV0);
            }
            /*切换到mic*/
            if (mic && !isMicV0) {
                changePositionUiMode(0, new MicUiMode(sku, ""));
                return;
            }
            /*切换到音乐*/
            if (!mic && !isMusicV0) {
                changePositionUiMode(0, new MusicUiMode(sku, device));
                return;
            }
            return;
        }
        /*v1版本音乐模式*/
        if (musicVersion == 1) {
            boolean isMicV1 = positionUiMode instanceof MicUiModeMultiV1;
            boolean isMusicV1 = positionUiMode instanceof MusicUiModeMultiV1;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicMode() isMicV1 = " + isMicV1 + " ; isMusicV1 = " + isMusicV1);
            }
            if (mic && !isMicV1) {
                changePositionUiMode(0, new MicUiModeMultiV1(sku, ""));
                return;
            }
            if (!mic && !isMusicV1) {
                changePositionUiMode(0, new MusicUiModeMultiV1(sku, device));
            }
            return;
        }
        /*v2版本音乐模式*/
        if (musicVersion == 2) {
            boolean isMicV2 = positionUiMode instanceof MicUiModeMultiV2;
            boolean isMusicV2 = positionUiMode instanceof MusicUiModeMultiV2;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicMode() isMicV2 = " + isMicV2 + " ; isMusicV2 = " + isMusicV2);
            }
            if (mic && !isMicV2) {
                changePositionUiMode(0, new MicUiModeMultiV2(sku, device));
                return;
            }
            if (!mic && !isMusicV2) {
                changePositionUiMode(0, new MusicUiModeMultiV2(sku, device));
            }
        }
        /*v3版本音乐模式*/
        if (musicVersion == 3) {
            boolean isMicV3 = positionUiMode instanceof MicUiModeMultiV3;
            boolean isMusicV3 = positionUiMode instanceof MusicUiModeMultiV3;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicMode() isMicV3 = " + isMicV3 + " ; isMusicV3 = " + isMusicV3);
            }
            if (mic && !isMicV3) {
                changePositionUiMode(0, new MicUiModeMultiV3(sku, device));
                return;
            }
            if (!mic && !isMusicV3) {
                changePositionUiMode(0, new MusicUiModeMultiV3(sku, device));
            }
        }
    }
}