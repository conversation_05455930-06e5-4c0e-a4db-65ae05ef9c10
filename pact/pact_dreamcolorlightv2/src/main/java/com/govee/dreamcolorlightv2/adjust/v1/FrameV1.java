package com.govee.dreamcolorlightv2.adjust.v1;

import com.govee.base2light.pact.AbsFrameBleIot;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.IFrameResult;
import com.govee.base2light.pact.IUi;
import com.govee.base2light.pact.IUiResult4BleIot;
import com.govee.base2light.pact.ble.AbsBlePact;
import com.govee.base2light.pact.ble.IPactResult4Ble;
import com.govee.base2light.pact.iot.AbsIotPact;
import com.govee.base2light.pact.iot.IPactResult4Iot;
import com.govee.dreamcolorlightv2.adjust.SettingBleWifiAc;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2020/3/5
 * v1版的框架实现-ble+iot$
 */
public class FrameV1 extends AbsFrameBleIot {
    public FrameV1(IFrameResult frameResult, BleIotInfo info) {
        super(frameResult, info);
    }

    @Override
    protected List<IUi> getSupportUiList(IUiResult4BleIot iUiResult4BleIot, BleIotInfo bleIotInfo) {
        List<IUi> uiList = new ArrayList<>();
        uiList.add(new UiV1(iUiResult4BleIot, bleIotInfo));
        uiList.add(new UiV2(iUiResult4BleIot, bleIotInfo));
        return uiList;
    }

    @Override
    protected AbsBlePact makeBlePact(IPactResult4Ble iPactResult4Ble) {
        return new BlePactV1(iPactResult4Ble);
    }

    @Override
    protected AbsIotPact makeIotPact(IPactResult4Iot iPactResult4Iot) {
        return new IotPactV1(iPactResult4Iot);
    }

    @Override
    public void toSettingAc(@NonNull AppCompatActivity ac) {
        if (isDestroy()) return;
        SettingBleWifiAc.jumpSettingBleWifiAc(ac,
                info.goodsType,
                info.sku,
                info.device,
                info.topic,
                info.deviceName,
                22,
                info.bleAddress,
                info.wifiMac,
                info.versionHard,
                info.versionSoft
                );
    }

}