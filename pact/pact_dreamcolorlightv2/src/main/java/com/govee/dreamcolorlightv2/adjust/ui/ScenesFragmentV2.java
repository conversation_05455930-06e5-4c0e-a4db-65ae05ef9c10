package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.light.ScenesHint;
import com.govee.base2light.light.v1.AbsScenesFragmentV4;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.SubModeScenes;
import com.govee.dreamcolorlightv2.pact.Support;
import com.ihoment.base2app.util.AppUtil;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by xie<PERSON>wu on 2020/9/28
 * 场景效果
 */
public class ScenesFragmentV2 extends AbsScenesFragmentV4 {

    @Override
    protected void sendScenesMode(ScenesLayout.ScenesItem item) {
        Mode mode = new Mode();
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.setEffect(item.scenesValue);
        mode.subMode = subModeScenes;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, ParamFixedValue.times);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, "scene_" + subModeScenes.getEffect());
    }

    @Override
    protected void sendScenesMode(CategoryV1.SceneV1 scene) {
        EventBus.getDefault().post(scene);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, ParamFixedValue.times);
        int sceneCode = scene.getSceneCode(0, getSku());
        if (sceneCode > -1) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, "scene_" + sceneCode);
        }
    }

    @Override
    protected ScenesHint getScenesHint(int scenesValue) {
        return Support.getScenesHint(scenesValue);
    }

    @Override
    public int marginTop() {
        return AppUtil.getScreenWidth() * 37 / 750;
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeScenes) {
            boolean checkAnalytic4SubModeUse = ((SubModeScenes) subMode).checkAnalytic4SubModeUse(getSku());
            SubModeScenes subModeScenes = (SubModeScenes) subMode;
            if (checkAnalytic4SubModeUse) {
                recordModeUseSceneCode(subModeScenes.getEffect());
            }
            updateSelectedScenes(subModeScenes.getEffect());
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_scenes;
    }
}