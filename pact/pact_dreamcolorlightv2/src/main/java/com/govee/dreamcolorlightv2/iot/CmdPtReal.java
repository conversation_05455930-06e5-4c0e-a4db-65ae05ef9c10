package com.govee.dreamcolorlightv2.iot;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.util.Encode;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.comm.MultipleBleBytes;
import com.govee.base2light.ble.controller.AbsController;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.MultiDiyGraffitiController;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.controller.MultipleDiyController;
import com.govee.base2light.ble.controller.MultipleDiyControllerV1;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.BulbStringColorController;
import com.govee.dreamcolorlightv2.ble.BulbStringColorControllerV2;
import com.govee.dreamcolorlightv2.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.ModeController;
import com.govee.dreamcolorlightv2.ble.SubModeColor;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV1;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV2;
import com.govee.dreamcolorlightv2.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv2.ble.SubModeScenes;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/6/24
 * cmd=ptReal$
 */
@Keep
public class CmdPtReal extends AbsCmd {
    private static final String TAG = "CmdPtReal";
    private final List<String> command = new ArrayList<>();

    public List<String> getCommand() {
        return command;
    }

    public int commandSize() {
        return command.size();
    }

    @Override
    public String getCmd() {
        return Cmd.ptReal;
    }

    protected CmdPtReal() {
    }

    public CmdPtReal(@NonNull AbsController controller) {
        byte[] bytes = controller.getValue();
        String valueStr = Encode.encryptByBase64(bytes);
        command.add(valueStr);
    }

    public CmdPtReal(@NonNull AbsController[] controllers) {
        for (AbsController controller : controllers) {
            byte[] value = controller.getValue();
            String valueStr = Encode.encryptByBase64(value);
            command.add(valueStr);
        }
    }

    public CmdPtReal(@NonNull List<byte[]> multipleBytes) {
        for (byte[] multipleByte : multipleBytes) {
            String valueStr = Encode.encryptByBase64(multipleByte);
            command.add(valueStr);
        }
    }

    public Byte getOpCommandByte() {
        if (command.isEmpty()) return null;
        int size = command.size();
        String commandByteStr = command.get(size - 1);
        byte[] bytes = Encode.decryByBase64(commandByteStr);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getOpCommandByte() bytes = " + BleUtil.bytesToHexString(bytes));
        }
        if (bytes != null && bytes.length == 20) {
            return bytes[1];
        }
        return null;
    }

    public byte[] getOpCommandBytes() {
        if (command.isEmpty()) return null;
        int size = command.size();
        String commandByteStr = command.get(size - 1);
        return Encode.decryByBase64(commandByteStr);
    }

    /**
     * 是否是采用ptReal设置分段颜色
     */
    public SubModeColor isPtReal4SetPartColor() {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "isPtReal4SetPartColor() size = " + size);
        }
        if (size >= 2) {
            SubModeColor subModeColor = new SubModeColor();
            /*至少一包设置颜色+一包设置渐变值*/
            for (int i = 0; i < size - 1; i++) {
                String encodeStr = command.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                boolean setMode2Color = SubModeColor.isSetMode2Color(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColor.parsePosColor(setColor4PosBytes, subModeColor);
            }
            String encodeStr = command.get(size - 1);
            byte[] gradual20Bytes = Encode.decryByBase64(encodeStr);
            boolean setGradual = Gradual4BleWifiController.isWriteGradualController(gradual20Bytes);
            if (setGradual) {
                subModeColor.gradual = Gradual4BleWifiController.parseGradual(gradual20Bytes);
                return subModeColor;
            }
            return null;
        }
        return null;
    }

    /**
     * 是否是采用ptReal设置分段颜色
     */
    public SubModeColorV2 isPtReal4SetPartColorV1() {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size >= 2) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            /*至少一包设置颜色+一包设置渐变值*/
            for (int i = 0; i < size - 1; i++) {
                String encodeStr = command.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "isPtReal4SetPartColor() setColor4PosBytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
                boolean setMode2Color = SubModeColorV2.isSetMode2Color(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColorV2.parsePosColor(setColor4PosBytes, subModeColor);
            }
            String encodeStr = command.get(size - 1);
            byte[] gradual20Bytes = Encode.decryByBase64(encodeStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isPtReal4SetPartColor() gradual20Bytes = " + BleUtil.bytesToHexString(gradual20Bytes));
            }
            boolean setGradual = Gradual4BleWifiController.isWriteGradualController(gradual20Bytes);
            if (setGradual) {
                subModeColor.gradual = Gradual4BleWifiController.parseGradual(gradual20Bytes);
                return subModeColor;
            }
            return null;
        }
        return null;
    }

    /**
     * 是否是由其他模式切换到颜色模式 带亮度
     *
     * @return
     */
    public boolean isOtherMode2ColorModePtWithBrightness() {
        if (command.isEmpty()) return false;
        int size = command.size();
        if (size >= 3) {
            byte[] setSubModeColorBytes = Encode.decryByBase64(command.get(0));
            boolean isSetSubModeColor = SubModeColor.isSetMode2Color(setSubModeColorBytes) || SubModeColorV2.isSetMode2Color(setSubModeColorBytes);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isOtherMode2ColorModePtWithBrightness() isSetSubModeColor = " + isSetSubModeColor + " ; setSubModeColorBytes = " + BleUtil.bytesToHexString(setSubModeColorBytes));
            }
            if (!isSetSubModeColor) return false;
            byte[] getPartColorBytes = Encode.decryByBase64(command.get(1));
            boolean isGetPartColor = BulbStringColorController.isReadBulbStringColorController(getPartColorBytes) || BulbStringColorControllerV2.isReadBulbStringColorController(getPartColorBytes);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isOtherMode2ColorModePtWithBrightness() isGetPartColor = " + isGetPartColor + " ; getPartColorBytes = " + BleUtil.bytesToHexString(getPartColorBytes));
            }
            if (!isGetPartColor) return false;
            byte[] getGradualBytes = Encode.decryByBase64(command.get(size - 1));
            boolean isReadGradualController = Gradual4BleWifiController.isReadGradualController(getGradualBytes);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isOtherMode2ColorModePtWithBrightness() isReadGradualController = " + isReadGradualController + " ; getGradualBytes = " + BleUtil.bytesToHexString(getGradualBytes));
            }
            return isReadGradualController;
        }
        return false;
    }

    public static CmdPtReal getDiyCmdPt(DiyProtocol diyProtocol) {
        MultipleDiyController multipleDiyController = new MultipleDiyController(diyProtocol);
        /*diy效果数据*/
        List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytes(multipleDiyController);
        Mode mode = new Mode();
        mode.subMode = new SubModeNewDiy(diyProtocol.getDiyCode());
        ModeController modeController = new ModeController(mode);
        /*切换至diy模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytes.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytes);
    }

    public static CmdPtReal getDiyCmdPtV1(DiyProtocol diyProtocol) {
        MultipleDiyControllerV1 multipleDiyController = new MultipleDiyControllerV1(diyProtocol);
        /*diy效果数据*/
        List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(multipleDiyController);
        Mode mode = new Mode();
        mode.subMode = new SubModeNewDiy(diyProtocol.getDiyCode());
        ModeController modeController = new ModeController(mode);
        /*切换至diy模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytes.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytes);
    }

    public static CmdPtReal getNewScenesCmdPtReal(AbsMultipleControllerV14Scenes multiNewScenesControllerV1) {
        List<byte[]> multipleWriteBytesV1 = MultipleBleBytes.getMultipleWriteBytesV1(multiNewScenesControllerV1);
        Mode mode = new Mode();
        SubModeScenes subMode = new SubModeScenes();
        subMode.setEffect(multiNewScenesControllerV1.getScenesCode());
        mode.subMode = subMode;
        ModeController modeController = new ModeController(mode);
        /*切换至场景模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV1.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytesV1);
    }

    public static CmdPtReal getDiyTemplateCmdPt(AbsMultipleControllerV14DiyTemplate multipleControllerV1) {
        List<byte[]> multipleWriteBytesV1 = MultipleBleBytes.getMultipleWriteBytesV1(multipleControllerV1);
        Mode mode = new Mode();
        SubModeScenes subMode = new SubModeScenes();
        subMode.setEffect(multipleControllerV1.getScenesCode());
        mode.subMode = subMode;
        ModeController modeController = new ModeController(mode);
        /*切换至场景模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV1.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytesV1);
    }

    public static CmdPtReal getPartColorCmdPt(int[] colorSet, boolean gradual, boolean hadFadeController) {
        if (colorSet == null || colorSet.length != 15) return null;
        AbsSingleController[] controllers = SubModeColor.makeSubModeColor(colorSet, gradual, hadFadeController);
        if (controllers == null) return null;
        return new CmdPtReal(controllers);
    }

    public static CmdPtReal getDiyCmdPt4DiyGraffiti(DiyGraffitiV2 diyGraffiti) {
        MultiDiyGraffitiController multiDiyGraffitiController = new MultiDiyGraffitiController(diyGraffiti.getDiyCode(), diyGraffiti.getEffectBytes());
        List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(multiDiyGraffitiController);
        Mode mode = new Mode();
        mode.subMode = new SubModeNewDiy(diyGraffiti.getDiyCode());
        ModeController modeController = new ModeController(mode);
        /*切换至diy模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytes.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytes);
    }

    public static CmdPtReal getMultiNewMusicModeV2(@NonNull MultipleController4Music multipleController4Music) {
        List<byte[]> multipleWriteBytesV2 = MultipleBleBytes.getMultipleWriteBytesV2(multipleController4Music);
        Mode mode = new Mode();
        mode.subMode = SubModeMusicMultiV2.toNewSubModeMusic(multipleController4Music.getSensitivity(), multipleController4Music.getMusicCode());
        ModeController modeController = new ModeController(mode);
        /*切换至音乐模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV2.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytesV2);
    }

    public static CmdPtReal getMultiNewMusicMode(@NonNull MultipleController4Music multipleController4Music) {
        List<byte[]> multipleWriteBytesV2 = MultipleBleBytes.getMultipleWriteBytesV2(multipleController4Music);
        Mode mode = new Mode();
        mode.subMode = SubModeMusicMultiV1.toNewSubModeMusic(multipleController4Music.getSensitivity(), multipleController4Music.getMusicCode());
        ModeController modeController = new ModeController(mode);
        /*切换至音乐模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV2.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytesV2);
    }

    /**
     * 点击色条应用整段颜色
     */
    public SubModeColor setPartColor4ColorStrip() {
        if (command.isEmpty()) return null;
        int size = command.size();
        SubModeColor subModeColor = new SubModeColor();
        for (int i = 0; i < size; i++) {
            String encodeStr = command.get(i);
            byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
            boolean setMode2Color = SubModeColor.isSetMode2Color4ColorEffect(setColor4PosBytes);
            if (!setMode2Color) return null;
            /*解析对应位置的颜色*/
            SubModeColor.parsePosColor(setColor4PosBytes, subModeColor);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "setPartColor4ColorStrip commandStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
            }
        }
        return subModeColor;
    }

    /**
     * 点击色条应用整段颜色-带亮度
     */
    public SubModeColorV2 setPartColor4ColorStripWithBrightness() {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size >= 2) {
            /*至少一包颜色+一包亮度*/
            SubModeColorV2 subModeColor = new SubModeColorV2();
            for (int i = 0; i < size; i++) {
                String encodeStr = command.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                boolean setMode2Color = SubModeColorV2.isSetMode2Color4ColorEffect(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColorV2.parsePosColorWithBrightness(setColor4PosBytes, subModeColor);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "setPartColor4ColorStripWithBrightness commandStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
            }
            return subModeColor;
        }
        return null;
    }

    public boolean isSetColorStrip() {
        if (command.isEmpty()) return false;
        if (command.size() == 1) return false; /*只有一包时直接走pt*/
        for (int i = 0; i < command.size(); i++) {
            String encodeStr = command.get(i);
            byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
            if (setColor4PosBytes == null) return false;
            if (setColor4PosBytes[2] != BleProtocol.sub_mode_color && setColor4PosBytes[2] != BleProtocol.sub_mode_color_v2)
                return false;
        }
        return true;
    }

    public boolean isSetColorStripWithBrightness() {
        if (command.isEmpty()) return false;
        String encodeStr = command.get(0);
        byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
        if (setColor4PosBytes == null) return false;
        return setColor4PosBytes[2] == BleProtocol.sub_mode_color_v2;
    }

    public static CmdPtReal makeCmdPtReal(List<String> iotCommands) {
        CmdPtReal cmdPtReal = new CmdPtReal();
        cmdPtReal.command.addAll(iotCommands);
        return cmdPtReal;
    }
}