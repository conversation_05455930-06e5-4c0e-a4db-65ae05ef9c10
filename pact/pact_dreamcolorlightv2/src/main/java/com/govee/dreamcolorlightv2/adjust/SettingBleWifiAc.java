package com.govee.dreamcolorlightv2.adjust;

import android.content.Context;
import android.os.Bundle;

import androidx.annotation.Nullable;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2light.wlanControl.WlanControlOperator;
import com.govee.dreamcolorlightv2.ConsV1;

import com.govee.dreamcolorlightv2.add.WifiChooseAc;
import com.govee.dreamcolorlightv2.pact.Support;
import com.govee.ui.ac.AbsWifiBleSettingAcV1;
import com.govee.ui.component.WlanControlView;
import com.ihoment.base2app.util.JumpUtil;

/**
 * Create by xie<PERSON><PERSON> on 2019-05-16
 * 设置Ac
 */
public class SettingBleWifiAc extends AbsWifiBleSettingAcV1 {
    private int goodsType;
    private String topic;

    public static void jumpSettingBleWifiAc(Context context, int goodsType, String sku, String device, String topic, String deviceName,
                                            int deviceNameInputLimit, String bleAddress, String mac, String versionHard, String versionSoft) {
        int[] wifiInputLimit = Support.getWifiInputLimit();
        Bundle jumpBundle = makeAcBundle(true,sku, device, deviceName, deviceNameInputLimit, mac, bleAddress, wifiInputLimit[0], wifiInputLimit[1], versionHard, versionSoft);
        if (jumpBundle == null) return;
        jumpBundle.putInt(ConsV1.intent_ac_adjust_goodsType, goodsType);
        jumpBundle.putString(com.govee.base2light.homepage.ConsV1.intent_ac_adjust_topic, topic);
        JumpUtil.jump(context, SettingBleWifiAc.class, jumpBundle);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        goodsType = getIntent().getIntExtra(ConsV1.intent_ac_adjust_goodsType, GoodsType.GOODES_TYPE_NO_SUPPORT);
        topic = getIntent().getStringExtra(com.govee.base2light.homepage.ConsV1.intent_ac_adjust_topic);
        checkSupportWlanControl();
    }

    @Override
    protected void jump2WifiSettingAc() {
        WifiChooseAc.jump2wifiChooseAcByChangeWifi(this, goodsType, sku, device, deviceName, "", bleAddress, versionHard);
    }

    private void checkSupportWlanControl() {
        WlanControlView wlanControlView = findViewById(com.govee.base2home.R.id.wlan_control);
        if (wlanControlView != null) {
            wlanControlView.checkSupport(new WlanControlOperator(goodsType, sku, device, topic));
        }
    }
}