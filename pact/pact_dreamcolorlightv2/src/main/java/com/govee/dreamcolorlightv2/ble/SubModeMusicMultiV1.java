package com.govee.dreamcolorlightv2.ble;

import com.govee.ui.R;
import android.text.TextUtils;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.IMusicEffectStatic;
import com.govee.base2light.ble.music.OldMusicEffect;
import com.govee.base2light.util.NumUtil;

import com.govee.dreamcolorlightv2.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.StorageInfra;
import com.ihoment.base2app.util.ResUtil;

/**
 * Create by xieyingwu on 2021/5/5
 * 音乐模式-v2$
 */
public class SubModeMusicMultiV1 extends AbsMusicIc {
    private static final String TAG = "SubModeMusicV2";
    private static final byte auto_type_open = 0;
    private static final byte auto_type_close = 1;
    private static final int min_sensitivity = 0;
    private static final int max_sensitivity = 99;
    private static final byte sub_effect_dynamic = 0x00;
    private static final byte sub_effect_soft = 0x01;

    private boolean isNewMusic = false;

    private int musicCode = BleProtocol.value_sub_mode_music_rhythm;

    private int sensitivity = max_sensitivity;
    private boolean autoColor = true;
    private boolean dynamic = true;
    private int rgb = 0xFFFF0000;

    public boolean isNewMusic() {
        return isNewMusic;
    }

    @Override
    public void loadLocal() {
        SubModeMusicMultiV1 subModeMusicV2 = StorageInfra.get(SubModeMusicMultiV1.class);
        if (subModeMusicV2 == null) return;
        isNewMusic = subModeMusicV2.isNewMusic;
        musicCode = subModeMusicV2.musicCode;
        sensitivity = subModeMusicV2.sensitivity;
        autoColor = subModeMusicV2.autoColor;
        dynamic = subModeMusicV2.dynamic;
        rgb = subModeMusicV2.rgb;
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(this);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_music;
    }

    @Override
    public String getAnalyticModeName() {
        String subStr = parseSubStr(musicCode);
        if (!TextUtils.isEmpty(subStr)) return ParamFixedValue.mode_music + "_" + subStr;
        return ParamFixedValue.mode_music;
    }

    private String parseSubStr(int musicCode) {
        if (isNewMusic) return IMusicEffectStatic.parseSubStr4New(musicCode);
        if (musicCode == BleProtocol.value_sub_mode_music_energy)
            return ResUtil.getString4English(R.string.effect_energic_des);
        if (musicCode == BleProtocol.value_sub_mode_music_rhythm)
            return ResUtil.getString4English(R.string.effect_rhythm_des);
        if (musicCode == BleProtocol.value_sub_mode_music_spectrum)
            return ResUtil.getString4English(R.string.effect_spectrum_des);
        if (musicCode == BleProtocol.value_sub_mode_music_scroll)
            return ResUtil.getString4English(R.string.effect_rolling_des);
        return "";
    }

    @Override
    public void parse(byte[] validBytes) {
        musicCode = BleUtil.getUnsignedByte(validBytes[0]);
        sensitivity = BleUtil.getUnsignedByte(validBytes[1]);
        isNewMusic = Support.isNewMusicCode(musicCode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parse() musicCode = " + musicCode + " ; isNewMusic = " + isNewMusic);
        }
        /*旧音乐模式-单包指令包含全部音乐模式参数*/
        if (!isNewMusic) {
            if (musicCode == BleProtocol.value_sub_mode_music_rhythm) {
                /*节奏下有动感柔和*/
                dynamic = BleUtil.getUnsignedByte(validBytes[2]) == sub_effect_dynamic;
                autoColor = validBytes[3] == auto_type_open;
                /*未开启颜色自动，则是指定了单色*/
                if (!autoColor) {
                    byte[] rgbBytes = new byte[3];
                    System.arraycopy(validBytes, 4, rgbBytes, 0, rgbBytes.length);
                    rgb = ColorUtils.toColor(rgbBytes);
                }
            } else if (musicCode != BleProtocol.value_sub_mode_music_energy) {
                autoColor = validBytes[2] == auto_type_open;
                /*未开启颜色自动，则是指定了单色*/
                if (!autoColor) {
                    byte[] rgbBytes = new byte[3];
                    System.arraycopy(validBytes, 3, rgbBytes, 0, rgbBytes.length);
                    rgb = ColorUtils.toColor(rgbBytes);
                }
            }
        }
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] valueBytes;
        if (isNewMusic) {
            valueBytes = new byte[3];
            valueBytes[0] = subModeCommandType();
            valueBytes[1] = (byte) musicCode;
            valueBytes[2] = (byte) sensitivity;
        } else {
            if (musicCode == BleProtocol.value_sub_mode_music_energy) {
                valueBytes = new byte[3];
                valueBytes[0] = subModeCommandType();
                valueBytes[1] = (byte) musicCode;
                valueBytes[2] = (byte) sensitivity;
            } else if (musicCode == BleProtocol.value_sub_mode_music_rhythm) {
                valueBytes = new byte[8];
                valueBytes[0] = subModeCommandType();
                valueBytes[1] = (byte) musicCode;
                valueBytes[2] = (byte) sensitivity;
                valueBytes[3] = dynamic ? sub_effect_dynamic : sub_effect_soft;
                valueBytes[4] = autoColor ? auto_type_open : auto_type_close;
                if (!autoColor) {
                    byte[] rgbBytes = ColorUtils.getRgbBytes(rgb);
                    System.arraycopy(rgbBytes, 0, valueBytes, 5, rgbBytes.length);
                }
            } else {
                valueBytes = new byte[7];
                valueBytes[0] = subModeCommandType();
                valueBytes[1] = (byte) musicCode;
                valueBytes[2] = (byte) sensitivity;
                valueBytes[3] = autoColor ? auto_type_open : auto_type_close;
                if (!autoColor) {
                    byte[] rgbBytes = ColorUtils.getRgbBytes(rgb);
                    System.arraycopy(rgbBytes, 0, valueBytes, 4, rgbBytes.length);
                }
            }
        }
        return valueBytes;
    }

    public int getMusicCode() {
        return musicCode;
    }

    public void setMusicCode(int musicCode) {
        this.musicCode = musicCode;
        isNewMusic = Support.isNewMusicCode(musicCode);
    }

    public int getSensitivity() {
        return sensitivity;
    }

    public void setSensitivity(int sensitivity) {
        this.sensitivity = NumUtil.checkNum(sensitivity, min_sensitivity, max_sensitivity);
    }

    public boolean isAutoColor() {
        return autoColor;
    }

    public int getRgb() {
        return rgb;
    }

    public void beAutoColor() {
        this.autoColor = true;
    }

    public void setAutoColor(boolean autoColor) {
        this.autoColor = autoColor;
    }

    public void setRgb(int rgb) {
        this.autoColor = false;
        this.rgb = rgb;
    }

    public boolean isDynamic() {
        return dynamic;
    }

    public void setDynamic(boolean dynamic) {
        this.dynamic = dynamic;
    }

    public void oldMusicEffectChange(OldMusicEffect oldMusicEffect) {
        isNewMusic = false;
        musicCode = oldMusicEffect.musicCode;
        boolean autoColor = oldMusicEffect.autoColor;
        if (autoColor) {
            beAutoColor();
        } else {
            setRgb(oldMusicEffect.rgb);
        }
        dynamic = oldMusicEffect.dynamic;
        setSensitivity(oldMusicEffect.sensitivity);
    }

    public SubModeMusicMultiV1 copy() {
        SubModeMusicMultiV1 subModeMusicV2 = new SubModeMusicMultiV1();
        subModeMusicV2.isNewMusic = isNewMusic;
        subModeMusicV2.musicCode = musicCode;
        subModeMusicV2.sensitivity = sensitivity;
        subModeMusicV2.autoColor = autoColor;
        subModeMusicV2.dynamic = dynamic;
        subModeMusicV2.rgb = rgb;
        return subModeMusicV2;
    }

    public static SubModeMusicMultiV1 toNewSubModeMusic(int sensitivity, int musicCode) {
        SubModeMusicMultiV1 subModeMusicV2 = new SubModeMusicMultiV1();
        subModeMusicV2.isNewMusic = true;
        subModeMusicV2.musicCode = musicCode;
        subModeMusicV2.setSensitivity(sensitivity);
        return subModeMusicV2;
    }

    public static SubModeMusicMultiV1 toNewSubModeMusic(SubModeMusic subModeMusic) {
        SubModeMusicMultiV1 subModeMusicV2 = new SubModeMusicMultiV1();
        subModeMusicV2.sensitivity = subModeMusic.getSensitivity();
        subModeMusicV2.autoColor = subModeMusic.isAutoColor();
        subModeMusicV2.isNewMusic = false;
        subModeMusicV2.musicCode = subModeMusic.getEffect();
        subModeMusicV2.setDynamic(subModeMusic.isDynamic());
        return null;
    }

    public SubModeMusicMultiV1_1 toSubModeMultiV1_1() {
        SubModeMusicMultiV1_1 subModeMusicMultiV1 = new SubModeMusicMultiV1_1();
        subModeMusicMultiV1.setMusicCode(musicCode);
        subModeMusicMultiV1.setSensitivity(sensitivity);
        subModeMusicMultiV1.setDynamic(dynamic);
        subModeMusicMultiV1.setRgb(rgb);
        subModeMusicMultiV1.setAutoColor(autoColor);
        return subModeMusicMultiV1;
    }

    public SubModeMusic toSubModeMusic() {
        SubModeMusic subModeMusic = new SubModeMusic();
        subModeMusic.setEffect(musicCode);
        subModeMusic.setSensitivity(sensitivity);
        subModeMusic.setAutoColor(autoColor);
        subModeMusic.setDynamic(dynamic);
        subModeMusic.setRgb(rgb);
        return subModeMusic;
    }
}
