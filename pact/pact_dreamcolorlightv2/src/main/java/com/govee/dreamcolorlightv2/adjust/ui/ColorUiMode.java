package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.ui.R;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsColorUiMode;

import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.SubModeColor;

/**
 * Create by xie<PERSON><PERSON> on 2019-07-22
 * color ui mode
 */
public class ColorUiMode extends AbsColorUiMode {
    /**
     * 群控下的ColorUiMode
     *
     * @param sku
     * @return
     */
    public static ColorUiMode makeColorUiMode4Factor(String sku) {
        return new ColorUiMode(sku);
    }

    private ColorUiMode(String sku) {
        super(sku);
    }

    public ColorUiMode(int goodsType, String sku, String device, int ic, DiySupportV1 diySupport) {
        super(goodsType, sku, device, ic, diySupport);
    }

    @Override
    public AbsModeFragment getUiFragment() {
        ColorFragment fragment = new ColorFragment();
        fragment.makeArguments(getSku());
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_color_mini, R.mipmap.new_control_light_btb_mode_color_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.loadLocal();
        return subModeColor;
    }
}
