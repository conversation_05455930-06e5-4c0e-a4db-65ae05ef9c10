package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.IScenes;
import com.govee.base2light.light.ScenesHint;
import com.govee.base2light.light.v1.AbsScenesFragmentV1;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.SubModeScenes;
import com.govee.dreamcolorlightv2.pact.Support;
import com.govee.ui.dialog.HintDialog1;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by linshicong on 2020-02-11
 */
public class ScenesFragment extends AbsScenesFragmentV1 {
    @Override
    protected void toSendScenesMode(ScenesItem item) {
        Mode mode = new Mode();
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.setEffect(item.scenesValue);
        mode.subMode = subModeScenes;
        toSetScenes(item.scenesValue, () -> {
            EventBus.getDefault().post(mode);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, ParamFixedValue.times);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, "scene_" + subModeScenes.getEffect());
        });
    }

    @Override
    protected ScenesHint getScenesHint(int scenesValue) {
        return Support.getScenesHint(scenesValue);
    }

    @Override
    public void onDestroy() {
        HintDialog1.hideDialog();
        super.onDestroy();
    }

    @Override
    protected List<ScenesItem> getSupportItems() {
        IScenes scenes = Support.getScenes(0);
        return makeScenesItems(scenes.defResSet(), scenes.selectedResSet(), scenes.strSet(), scenes.scenesEffectSet());
    }

    private List<ScenesItem> makeScenesItems(int[] defResSet, int[] selectedResSet, int[] strSet, int[] scenesEffectSet) {
        List<ScenesItem> scenesItems = new ArrayList<>();
        for (int i = 0; i < defResSet.length; i++) {
            ScenesItem scenesItem = new ScenesItem();
            scenesItem.defRes = defResSet[i];
            scenesItem.selectRes = selectedResSet[i];
            scenesItem.valueStr = ResUtil.getString(strSet[i]);
            scenesItem.strRes = strSet[i];
            scenesItem.scenesValue = scenesEffectSet[i];
            scenesItems.add(scenesItem);
        }
        return scenesItems;
    }

    @Override
    public int marginTop() {
        return AppUtil.getScreenWidth() * 37 / 750;
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeScenes) {
            boolean checkAnalytic4SubModeUse = ((SubModeScenes) subMode).checkAnalytic4SubModeUse(getSku());
            SubModeScenes subModeScenes = (SubModeScenes) subMode;
            if (checkAnalytic4SubModeUse) {
                recordModeUseSceneCode(subModeScenes.getEffect());
            }
            updateSelectedScenes(subModeScenes.getEffect());
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_scenes;
    }
}