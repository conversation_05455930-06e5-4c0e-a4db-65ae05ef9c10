package com.govee.dreamcolorlightv2.ble;

import androidx.annotation.NonNull;

import com.govee.base2home.Constant;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.BleProtocolConstants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Create by hey on 2021/2/5
 * $
 */
public class SubModeColorV2 extends AbsSubMode4Analytic {
    public static final int op_type_ui_no_fresh = -1;
    public static final int op_type_gradual = 1;
    private static final byte sub_mode_color_set_color = 0x01;/*设置分段颜色*/
    private static final byte sub_mode_color_set_brightness = 0x02;/*设置分段亮度*/
    private static final byte sub_mode_color_set_brightnessSet = 0x03;/*设置整组分段亮度*/

    public int rgb = 0;/*默认颜色-红色*/
    public int kelvin;
    public int temRgb;
    public int gradual = 0;/*默认不开启渐变*/

    public boolean[] ctlLight = new boolean[15];
    public int[] rgbSet;

    public int[] brightnessSet;
    public int brightness;
    public int opType;

    public boolean isSelectAll() {
        if (ctlLight != null) {
            for (boolean b : ctlLight) {
                if (!b) return false;
            }
            return true;
        }
        return false;
    }

    @Override
    public void loadLocal() {
    }

    @Override
    public void saveLocal() {
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public String getAnalyticModeName() {
        int value = kelvin > 0 ? temRgb : rgb;
        String subModeStr = UtilColor.colorName(value);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_color, subModeStr);
    }

    @Override
    public void parse(byte[] value) {
        this.gradual = BleUtil.getUnsignedByte(value[0]);
    }

    private byte[] getSetColorBytes() {
        byte[] values = new byte[12];
        values[0] = subModeCommandType();
        values[1] = sub_mode_color_set_color;/*设置颜色*/
        byte[] rgbBytes = ColorUtils.getRgbBytes(this.rgb);
        values[2] = rgbBytes[0];
        values[3] = rgbBytes[1];
        values[4] = rgbBytes[2];
        byte[] kelvinBytes = BleUtil.getSignedBytesFor2(this.kelvin, true);
        values[5] = kelvinBytes[0];
        values[6] = kelvinBytes[1];
        byte[] temRgbBytes = ColorUtils.getRgbBytes(this.temRgb);
        values[7] = temRgbBytes[0];
        values[8] = temRgbBytes[1];
        values[9] = temRgbBytes[2];

        values[10] = (byte) 0x00;
        int temp = 1;
        for (int i = 0; i < 8; i++) {
            if (ctlLight[i]) {
                values[10] = (byte) (values[10] | temp);
            }
            temp = temp << 1;
        }
        values[11] = (byte) 0x00;
        temp = 1;
        for (int i = 8; i < 15; i++) {
            if (ctlLight[i]) {
                values[11] = (byte) (values[11] | temp);
            }
            temp = temp << 1;
        }
        return values;
    }

    private byte[] getSetBrightnessBytes() {
        byte[] values = new byte[5];
        values[0] = subModeCommandType();
        values[1] = sub_mode_color_set_brightness;
        values[2] = (byte) brightness;
        int temp = 1;
        values[3] = (byte) 0x00;
        for (int i = 0; i < 8; i++) {
            if (ctlLight[i]) {
                values[3] = (byte) (values[3] | temp);
            }
            temp = temp << 1;
        }
        temp = 1;
        values[4] = (byte) 0x00;
        for (int i = 8; i < 15; i++) {
            if (ctlLight[i]) {
                values[4] = (byte) (values[4] | temp);
            }
            temp = temp << 1;
        }
        return values;
    }

    @Override
    public byte[] getWriteBytes() {
        if (brightness == 0 && this.brightnessSet == null) {
            return getSetColorBytes();
        } else if (this.brightnessSet != null) {
            byte[] bytes = new byte[17];
            bytes[0] = subModeCommandType();
            bytes[1] = (byte) 0x03;
            for (int i = 0; i < brightnessSet.length; i++) {
                bytes[i + 2] = (byte) brightnessSet[i];
            }
            return bytes;
        }
        return getSetBrightnessBytes();
    }

    public int getRealRgb() {
        if (kelvin > 0) return temRgb;
        return rgb;
    }

    public void setRgb(int rgb) {
        this.rgb = rgb;
        this.temRgb = 0;
        this.kelvin = 0;
    }

    public void setTemRgbAndKelvin(int temRgb, int kelvin) {
        this.rgb = 0;
        this.temRgb = temRgb;
        this.kelvin = kelvin;
    }

    public static SubModeColorV2 makeSubModeColor(int color) {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        subModeColor.rgb = color;
        subModeColor.kelvin = 0;
        subModeColor.temRgb = 0;
        int length = subModeColor.ctlLight.length;
        int[] rgbSet = new int[length];
        for (int i = 0; i < length; i++) {
            subModeColor.ctlLight[i] = true;
            rgbSet[i] = color;
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }


    public static AbsSingleController[] makeSubModeColor(int[] colors, boolean isFade, boolean hadFadeController) {
        if (colors.length != 15) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        int fadeSize = hadFadeController ? 1 : 0;
        AbsSingleController[] modeControllers = new AbsSingleController[hashMap.size() + fadeSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[15];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            modeControllers[i] = new Gradual4BleWifiController(isFade);
        }
        return modeControllers;
    }

    public static boolean isSetMode2Color(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opByte == BleProtocolConstants.SINGLE_MODE && modeType == BleProtocol.sub_mode_color_v2;
        }
        return false;
    }

    public static SubModeColorV2 parseSubModeColor4Write(byte[] validBytes) {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        boolean[] ctlLight = new boolean[15];
        boolean[] group1Value8 = new boolean[8];
        boolean[] group2Value8 = new boolean[8];
        int[] brightnessSet = new int[15];
        if (validBytes[0] == sub_mode_color_set_color) {
            subModeColor.rgb = ColorUtils.toColor(validBytes[1], validBytes[2], validBytes[3]);
            subModeColor.kelvin = BleUtil.getSignedShort(validBytes[4], validBytes[5]);
            subModeColor.temRgb = ColorUtils.toColor(validBytes[6], validBytes[7], validBytes[8]);
            /*前8盏灯的选中状态*/
            group1Value8 = BleUtil.parseBytes4BitReverse(validBytes[9]);
            group2Value8 = BleUtil.parseBytes4BitReverse(validBytes[10]);
        } else if (validBytes[0] == sub_mode_color_set_brightness) {
            subModeColor.brightness = validBytes[1];
            /*前8盏灯的选中状态*/
            group1Value8 = BleUtil.parseBytes4BitReverse(validBytes[2]);
            group2Value8 = BleUtil.parseBytes4BitReverse(validBytes[3]);
        } else if (validBytes[0] == sub_mode_color_set_brightnessSet) {
            for (int i = 0; i < validBytes.length - 1; i++) {
                brightnessSet[i] = BleUtil.getUnsignedByte(validBytes[i + 1]);
            }
            subModeColor.brightnessSet = brightnessSet;
        }
        System.arraycopy(group1Value8, 0, ctlLight, 0, group1Value8.length);
        System.arraycopy(group2Value8, 0, ctlLight, 8, ctlLight.length - group1Value8.length);
        subModeColor.ctlLight = ctlLight;
        return subModeColor;
    }

    public static SubModeColorV2 parseSubModeColor4WriteV2(byte[] validBytes) {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        boolean[] ctlLight = new boolean[15];
        boolean[] group1Value8;
        boolean[] group2Value8;
        subModeColor.rgb = ColorUtils.toColor(validBytes[0], validBytes[1], validBytes[2]);
        /*前8盏灯的选中状态*/
        group1Value8 = BleUtil.parseBytes4BitReverse(validBytes[3]);
        group2Value8 = BleUtil.parseBytes4BitReverse(validBytes[4]);
        System.arraycopy(group1Value8, 0, ctlLight, 0, group1Value8.length);
        System.arraycopy(group2Value8, 0, ctlLight, 8, ctlLight.length - group1Value8.length);
        subModeColor.ctlLight = ctlLight;
        return subModeColor;
    }

    public static void parsePosColor(byte[] setPosColorBytes, @NonNull SubModeColorV2 subModeColor) {
        if (setPosColorBytes == null || setPosColorBytes.length != 20) return;
        if (subModeColor.rgbSet == null) {
            subModeColor.rgbSet = new int[subModeColor.ctlLight.length];
        }
        int rgb = ColorUtils.toColor(setPosColorBytes[4], setPosColorBytes[5], setPosColorBytes[6]);
        int kelvin = BleUtil.convertTwoBytesToShort(setPosColorBytes[7], setPosColorBytes[8]);
        int temRgb = ColorUtils.toColor(setPosColorBytes[9], setPosColorBytes[10], setPosColorBytes[11]);
        int rgbValue;
        if (kelvin > 0) {
            rgbValue = temRgb;
        } else {
            rgbValue = rgb;
        }

        /*从低位bit开始描述*/
        boolean[] low8Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[12]);
        for (int i = 0; i < low8Set.length; i++) {
            if (low8Set[i]) {
                subModeColor.rgbSet[i] = rgbValue;
            }
        }
        int index = 8;
        boolean[] high5Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[13]);
        for (int i = 0; i < high5Set.length - 1; i++) {
            if (high5Set[i]) {
                subModeColor.rgbSet[index] = rgbValue;
            }
            index++;
        }
    }

    public static SubModeColorV2 parseSubModeColor2New(byte[] oldWriteBytes) {
        SubModeColor subModeColor = SubModeColor.parseWriteBytes(oldWriteBytes);
        return parseSubModeColor2New(subModeColor);
    }

    public static SubModeColorV2 parseSubModeColor2New(@NonNull SubModeColor subModeColor) {
        SubModeColorV2 subModeColorV2 = new SubModeColorV2();
        subModeColorV2.ctlLight = subModeColor.ctlLight;
        subModeColorV2.rgb = subModeColor.rgb;
        subModeColorV2.rgbSet = subModeColor.rgbSet;
        return subModeColorV2;
    }

    public static SubModeColorV2 parseWriteBytes(byte[] writeBytes) {
        byte[] validBytes = new byte[writeBytes.length - 1];
        System.arraycopy(writeBytes, 1, validBytes, 0, validBytes.length);
        return parseSubModeColor4Write(validBytes);
    }

    public static AbsSingleController[] makeSubModeColor(Colors colorStrip) {
        int[] colors = colorStrip.colorSet;
        if (colors.length != 15) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }

        boolean hadBrightness = colorStrip.brightnessSet != null && colorStrip.brightnessSet.length == 15;
        AbsSingleController[] modeControllers = new AbsSingleController[hadBrightness ? hashMap.size() + 1 : hashMap.size()];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[15];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hadBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadBrightness) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.brightnessSet = colorStrip.brightnessSet;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i] = modeController;
        }
        return modeControllers;
    }

    public static AbsSingleController[] makeSubModeColor(int[] colors, int[] brightnessArray, boolean isFade, boolean hadFadeController) {
        if (colors != null && colors.length == 1) {
            //单色
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.rgb = colors[0];
            Arrays.fill(subModeColor.ctlLight, true);
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            ModeController modeController = new ModeController(mode);
            return new AbsSingleController[]{modeController};
        }
        if (colors == null || colors.length != 15) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        boolean hadBrightness = brightnessArray != null && brightnessArray.length == 15;
        int fadeSize = hadFadeController ? 1 : 0;
        int brightnessSize = hadBrightness ? 1 : 0;
        AbsSingleController[] modeControllers = new AbsSingleController[hashMap.size() + brightnessSize + fadeSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[15];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hadBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadBrightness) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.brightnessSet = brightnessArray;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            modeControllers[i] = new Gradual4BleWifiController(isFade);
        }
        return modeControllers;
    }

    public static boolean isSetMode2Color4ColorEffect(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opByte == BleProtocolConstants.SINGLE_MODE && modeType == BleProtocol.sub_mode_color_v2;
        }
        return false;
    }

    public static void parsePosColorWithBrightness(byte[] setPosColorBytes, @NonNull SubModeColorV2 subModeColor) {
        if (setPosColorBytes == null || setPosColorBytes.length != 20) return;
        if (subModeColor.rgbSet == null) {
            subModeColor.rgbSet = new int[subModeColor.ctlLight.length];
        }
        if (setPosColorBytes[3] == 0x01) {
            int rgb = ColorUtils.toColor(setPosColorBytes[4], setPosColorBytes[5], setPosColorBytes[6]);
            int kelvin = BleUtil.convertTwoBytesToShort(setPosColorBytes[7], setPosColorBytes[8]);
            int temRgb = ColorUtils.toColor(setPosColorBytes[9], setPosColorBytes[10], setPosColorBytes[11]);
            int rgbValue;
            if (kelvin > 0) {
                rgbValue = temRgb;
            } else {
                rgbValue = rgb;
            }
            /*从低位bit开始描述*/
            boolean[] low8Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[12]);
            for (int i = 0; i < low8Set.length; i++) {
                if (low8Set[i]) {
                    subModeColor.rgbSet[i] = rgbValue;
                }
            }
            int index = 8;
            boolean[] high5Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[13]);
            for (int i = 0; i < high5Set.length - 1; i++) {
                if (high5Set[i]) {
                    subModeColor.rgbSet[index] = rgbValue;
                }
                index++;
            }
        } else if (setPosColorBytes[3] == 0x03) {
            if (subModeColor.brightnessSet == null) {
                subModeColor.brightnessSet = new int[subModeColor.ctlLight.length];
            }
            int[] brightnessArray = new int[subModeColor.ctlLight.length];
            for (int i = 0; i < subModeColor.ctlLight.length; i++) {
                brightnessArray[i] = BleUtil.getUnsignedByte(setPosColorBytes[i + 4]);
            }
            subModeColor.brightnessSet = brightnessArray;
        }
    }

    public static SubModeColorV2 makeSubModeColor4Group(Colors colors) {
        SubModeColorV2 subModeColorV2 = new SubModeColorV2();
        subModeColorV2.rgbSet = colors.colorSet;
        subModeColorV2.brightnessSet = colors.brightnessSet;
        return subModeColorV2;
    }

    public static boolean isAllColorTem(int[] rgbSet, int len) {
        if (rgbSet == null || rgbSet.length == 0) return false;
        int index = 0;
        for (int rgb : rgbSet) {
            if (!Constant.isColorTemp(rgb)) return false;
            index++;
            if (index == len) break;
        }
        return true;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_color;
    }

}