package com.govee.dreamcolorlightv2.adjust.v1;

import com.govee.base2home.pact.Protocol;
import com.govee.base2light.pact.iot.AbsIotPact;
import com.govee.base2light.pact.iot.IPactResult4Iot;
import com.govee.dreamcolorlightv2.iot.Cmd;
import com.govee.dreamcolorlightv2.pact.Support;

import java.util.List;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-11
 * v1版本的iot协议$
 */
class IotPactV1 extends AbsIotPact {

    public IotPactV1(IPactResult4Iot iPactResult) {
        super(iPactResult);
    }

    @Override
    protected String getPactCmd() {
        return Cmd.online;
    }

    @Override
    protected boolean isSupportPact(int pactType, int pactCode) {
        List<Protocol> protocols = Support.supportProtocolsV1;
        for (Protocol pro : protocols) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        /*判断bk方案*/
        for (Protocol pro : Support.supportProtocolsV2) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }
}