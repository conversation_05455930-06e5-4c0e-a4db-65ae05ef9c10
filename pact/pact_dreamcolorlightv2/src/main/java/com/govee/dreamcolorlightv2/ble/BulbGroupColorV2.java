package com.govee.dreamcolorlightv2.ble;

import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;

/**
 * Create by hey on 2021/2/5
 * $
 */
public class BulbGroupColorV2 {
    public int group;
    public int[] rgb;
    public int[] relativeBrightness;

    public static BulbGroupColorV2 parseBytes(byte[] validBytes) {
        BulbGroupColorV2 bulbGroupColor = new BulbGroupColorV2();
        int[] rgb = new int[3];
        byte[] rgbBytes = new byte[4];
        int[] relativeBrightness = new int[3];
        int group = BleUtil.getUnsignedByte(validBytes[0]);
        int index = 1;
        for (int i = 0; i < rgb.length; i++) {
            System.arraycopy(validBytes, index, rgbBytes, 0, rgbBytes.length);
            relativeBrightness[i] = BleUtil.getUnsignedByte(rgbBytes[0]);
            int r = BleUtil.getUnsignedByte(rgbBytes[1]);
            int g = BleUtil.getUnsignedByte(rgbBytes[2]);
            int b = BleUtil.getUnsignedByte(rgbBytes[3]);
            rgb[i] = ColorUtils.toColor(r, g, b);
            index += 4;
        }
        bulbGroupColor.group = group;
        bulbGroupColor.rgb = rgb;
        bulbGroupColor.relativeBrightness = relativeBrightness;
        return bulbGroupColor;
    }
}