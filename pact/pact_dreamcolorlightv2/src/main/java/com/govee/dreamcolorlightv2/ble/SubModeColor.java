package com.govee.dreamcolorlightv2.ble;

import androidx.annotation.NonNull;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.BleProtocolConstants;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Create by linshicong on 2019-09-10
 * 颜色模式
 */
public class SubModeColor extends AbsSubMode4Analytic {
    public static final int op_type_ui_no_fresh = -1;
    public static final int op_type_gradual = 1;
    public int rgb = 0;
    public int gradual = 0;/*默认不开启渐变*/

    public boolean[] ctlLight = new boolean[15];
    public int[] rgbSet;
    public int opType;

    public boolean isSelectAll() {
        if (ctlLight != null) {
            for (boolean b : ctlLight) {
                if (!b) return false;
            }
            return true;
        }
        return false;
    }


    @Override
    public void loadLocal() {
    }

    @Override
    public void saveLocal() {
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_color;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr = UtilColor.colorName(rgb);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_color, subModeStr);
    }

    @Override
    public void parse(byte[] value) {
        this.gradual = BleUtil.getUnsignedByte(value[0]);
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] values = new byte[8];
        values[0] = subModeCommandType();
        int[] rgb = ColorUtils.getRgb(this.rgb);
        values[1] = (byte) rgb[0];
        values[2] = (byte) rgb[1];
        values[3] = (byte) rgb[2];
        values[4] = (byte) 0x00;
        int temp = 1;
        for (int i = 0; i < 8; i++) {
            if (ctlLight[i]) {
                values[4] = (byte) (values[4] | temp);
            }
            temp = temp << 1;
        }
        values[5] = (byte) 0x00;
        temp = 1;
        for (int i = 8; i < 15; i++) {
            if (ctlLight[i]) {
                values[5] = (byte) (values[5] | temp);
            }
            temp = temp << 1;
        }

        return values;
    }

    public static SubModeColor parseWriteBytes(byte[] writeBytes) {
        byte[] validBytes = new byte[writeBytes.length - 1];
        System.arraycopy(writeBytes, 1, validBytes, 0, validBytes.length);
        return parseSubModeColor4Write(validBytes);
    }

    public static SubModeColor parseWriteBytesV2(byte[] writeBytes) {
        byte[] validBytes = new byte[writeBytes.length - 2];
        System.arraycopy(writeBytes, 2, validBytes, 0, validBytes.length);
        return parseSubModeColor4WriteV2(validBytes);
    }

    public static SubModeColor parseSubModeColor4WriteV2(byte[] validBytes) {
        SubModeColor subModeColor = new SubModeColor();
        int r = BleUtil.getUnsignedByte(validBytes[0]);
        int g = BleUtil.getUnsignedByte(validBytes[1]);
        int b = BleUtil.getUnsignedByte(validBytes[2]);
        subModeColor.rgb = ColorUtils.toColor(r, g, b);
        if (r == 0 && g == 0 && b == 0) {
            r = BleUtil.getUnsignedByte(validBytes[5]);
            g = BleUtil.getUnsignedByte(validBytes[6]);
            b = BleUtil.getUnsignedByte(validBytes[7]);
            subModeColor.rgb = ColorUtils.toColor(r, g, b);
        }
        boolean[] ctlLight = new boolean[15];
        /*前8盏灯的选中状态*/
        boolean[] group1Value8 = BleUtil.parseBytes4BitReverse(validBytes[8]);
        boolean[] group2Value8 = BleUtil.parseBytes4BitReverse(validBytes[9]);
        System.arraycopy(group1Value8, 0, ctlLight, 0, group1Value8.length);
        System.arraycopy(group2Value8, 0, ctlLight, 8, ctlLight.length - group1Value8.length);
        subModeColor.ctlLight = ctlLight;
        return subModeColor;
    }

    public static void parsePosColor(byte[] setPosColorBytes, @NonNull SubModeColor subModeColor) {
        if (setPosColorBytes == null || setPosColorBytes.length != 20) return;
        if (subModeColor.rgbSet == null) {
            subModeColor.rgbSet = new int[subModeColor.ctlLight.length];
        }
        int rgb = ColorUtils.toColor(setPosColorBytes[3], setPosColorBytes[4], setPosColorBytes[5]);
        /*从低位bit开始描述*/
        boolean[] low8Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[6]);
        for (int i = 0; i < low8Set.length; i++) {
            if (low8Set[i]) {
                subModeColor.rgbSet[i] = rgb;
            }
        }
        int index = 8;
        boolean[] high5Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[7]);
        for (int i = 0; i < high5Set.length - 1; i++) {
            if (high5Set[i]) {
                subModeColor.rgbSet[index] = rgb;
            }
            index++;
        }
    }

    public static SubModeColor parseSubModeColor4Write(byte[] validBytes) {
        SubModeColor subModeColor = new SubModeColor();
        int r = BleUtil.getUnsignedByte(validBytes[0]);
        int g = BleUtil.getUnsignedByte(validBytes[1]);
        int b = BleUtil.getUnsignedByte(validBytes[2]);
        subModeColor.rgb = ColorUtils.toColor(r, g, b);
        boolean[] ctlLight = new boolean[15];
        /*前8盏灯的选中状态*/
        boolean[] group1Value8 = BleUtil.parseBytes4BitReverse(validBytes[3]);
        boolean[] group2Value8 = BleUtil.parseBytes4BitReverse(validBytes[4]);
        System.arraycopy(group1Value8, 0, ctlLight, 0, group1Value8.length);
        System.arraycopy(group2Value8, 0, ctlLight, 8, ctlLight.length - group1Value8.length);
        subModeColor.ctlLight = ctlLight;
        return subModeColor;
    }

    public static SubModeColor makeSubModeColor(int color) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgb = color;
        int length = subModeColor.ctlLight.length;
        int[] rgbSet = new int[length];
        for (int i = 0; i < length; i++) {
            subModeColor.ctlLight[i] = true;
            rgbSet[i] = color;
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }

    public static AbsSingleController[] makeSubModeColor(int[] colors, boolean isFade, boolean hadFadeController) {
        if (colors != null && colors.length == 1) {
            //单色
            SubModeColor subModeColor = new SubModeColor();
            subModeColor.rgb = colors[0];
            Arrays.fill(subModeColor.ctlLight, true);
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            ModeController modeController = new ModeController(mode);
            return new AbsSingleController[]{modeController};
        }
        if (colors == null || colors.length != 15) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        int fadeSize = hadFadeController ? 1 : 0;
        AbsSingleController[] modeControllers = new AbsSingleController[hashMap.size() + fadeSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColor subModeColor = new SubModeColor();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[15];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            modeControllers[i] = new Gradual4BleWifiController(isFade);
        }
        return modeControllers;
    }

    public static SubModeColor parseSubModeColor(int color) {
        SubModeColor subModeColor = new SubModeColor();
        int length = subModeColor.ctlLight.length;
        int[] rgbSet = new int[length];
        for (int i = 0; i < length; i++) {
            rgbSet[i] = color;
        }
        subModeColor.rgb = color;
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }

    public static boolean isSetMode2Color(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opByte == BleProtocolConstants.SINGLE_MODE && modeType == BleProtocol.sub_mode_color;
        }
        return false;
    }


    public static AbsSingleController[] makeSubModeColor(Colors colorStrip) {
        int[] colors = colorStrip.colorSet;
        if (colors.length != 15) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        AbsSingleController[] modeControllers = new AbsSingleController[hashMap.size()];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColor subModeColor = new SubModeColor();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[15];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        return modeControllers;
    }


    public static boolean isSetMode2Color4ColorEffect(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opByte == BleProtocolConstants.SINGLE_MODE && modeType == BleProtocol.sub_mode_color;
        }
        return false;
    }

    public static SubModeColor makeSubModeColor4Group(Colors colors) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgbSet = colors.colorSet;
        return subModeColor;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_color;
    }
}