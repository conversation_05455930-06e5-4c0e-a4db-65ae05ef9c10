package com.govee.dreamcolorlightv2.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.light.AbsWifiCommDialogV2;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-11
 * iot通信的dialog$
 */
public class IotCommDialog extends AbsWifiCommDialogV2 {
    private IotCommDialog(Context context, String topic, String device, String sku, boolean open, boolean hadPact, CheckPactListener listener) {
        super(context, topic, device, sku, open, hadPact, listener);
    }

    public static IotCommDialog createDialog(Context context, String topic, String device, String sku, boolean open, boolean hadPact, CheckPactListener listener) {
        return new IotCommDialog(context, topic, device, sku, open, hadPact, listener);
    }

    @Override
    protected AbsCmd getCmdTurn(boolean open) {
        return new CmdTurn(open);
    }

    @Override
    protected String getCmdTurnBackCmd() {
        return Cmd.status;
    }
}