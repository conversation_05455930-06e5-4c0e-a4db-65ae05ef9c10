package com.govee.dreamcolorlightv2.add;

import android.app.Activity;

import com.govee.base2home.main.choose.BaseBleDeviceModel;
import com.govee.base2home.main.choose.BaseBleProcessor;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.sku.DefaultDeviceNameUtil;
import com.govee.dreamcolorlightv2.pact.Support;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2019-11-27
 * 蓝牙广播处理器$
 */
public class BleBroadcastProcessor extends BaseBleProcessor {
    @Override
    public boolean onItemClick(Activity activity, BaseBleDeviceModel model, boolean singleSku) {
        int goodsType = model.goodsType;
        Protocol protocol = model.protocol;
        /*是否支持协议处理;需要登录检测*/
        if (Support.supportPact(goodsType, protocol)) {
            boolean checkLogin = checkLogin(activity);
            if (checkLogin) {
                return true;
            }
            AddInfo addInfo = new AddInfo();
            addInfo.sku = model.getSku();
            addInfo.goodsType = goodsType;
            addInfo.pactType = protocol.pactType;
            addInfo.pactCode = protocol.pactCode;
            addInfo.deviceName = model.getDeviceName();
            addInfo.deviceName = DefaultDeviceNameUtil.INSTANCE.getSkuDefaultName(model.getSku(), addInfo.deviceName);
            addInfo.bleName = model.getBleName();
            addInfo.bleAddress = model.getDevice().getAddress();
            ConnectDialog.createDialog(activity, model.getDevice(), addInfo).show();
            return true;
        }
        return false;
    }
}