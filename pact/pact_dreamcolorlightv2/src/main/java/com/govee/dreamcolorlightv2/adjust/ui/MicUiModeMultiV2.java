package com.govee.dreamcolorlightv2.adjust.ui;


import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.mic.MicDj;
import com.govee.base2light.ble.mic.MicDynamic;
import com.govee.base2light.ble.mic.MicSoft;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV2;
import com.ihoment.base2app.infra.LogInfra;

/**
 * Create by DengFei on 2021-2-4
 * mic的uiMode
 */
public class MicUiModeMultiV2 extends MicUiMode {
    protected String TAG = "MicFragment";

    public MicUiModeMultiV2(String sku, String device) {
        super(sku, device);
    }

    @Override
    public AbsModeFragment getUiFragment() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i("MicFragment", "MicUiMode sku device：" + getSku() + device);
        }
        MicFragmentV2 micFragment = new MicFragmentV2();
        micFragment.makeArguments(getSku(), device);
        micFragment.setMicMode(new MicDj(), new MicDynamic(), new MicSoft());
        return micFragment;
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeMusicMultiV2 subModeMusic = new SubModeMusicMultiV2();
        subModeMusic.loadLocal();
        return subModeMusic;
    }
}