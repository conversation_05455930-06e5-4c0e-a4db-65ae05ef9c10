package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.music.OldMusicEffect;
import com.govee.base2light.light.v1.AbsNewMusicFragment;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV1;
import com.govee.dreamcolorlightv2.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2021/5/5
 * 新音乐模式$
 */
public class MusicFragmentMultiV1 extends AbsNewMusicFragment {
    private SubModeMusicMultiV1 subModeMusicV2 = new SubModeMusicMultiV1();
    private List<SubMusicMode> subMusicModeList;

    @Override
    protected void init() {
        super.init();
        /*防止fragment视图还未加载就已传递了模式信息；因此在init完成时也需要刷新ui*/
        updateUi();
    }

    @Override
    protected int getCurSubMusicCode() {
        return subModeMusicV2.getMusicCode();
    }

    @Override
    protected void oldMusicParamsChange(OldMusicEffect oldMusicEffect) {
        SubModeMusicMultiV1 copy = subModeMusicV2.copy();
        copy.oldMusicEffectChange(oldMusicEffect);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
    }

    @Override
    protected int getSensitivity() {
        return subModeMusicV2.getSensitivity();
    }

    @Override
    protected void showOldSubMusicEditDialog(SubMusicMode musicMode) {
        showOldMusicEditV1(musicMode, getSensitivity(), subModeMusicV2.isAutoColor(), subModeMusicV2.getRgb(), subModeMusicV2.isDynamic());
    }

    @NonNull
    @Override
    protected List<SubMusicMode> getSupportMusicModes() {
        if (subMusicModeList == null) {
            subMusicModeList = makeRgbicSubMusicModes(
                    BleProtocol.value_sub_mode_music_energy,
                    BleProtocol.value_sub_mode_music_rhythm,
                    BleProtocol.value_sub_mode_music_spectrum,
                    BleProtocol.value_sub_mode_music_scroll
            );
        }
        return subMusicModeList;
    }

    @Override
    protected void onSensitivityChange(int sensitivity) {
        SubModeMusicMultiV1 copy = subModeMusicV2.copy();
        copy.setSensitivity(sensitivity);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeMusicMultiV1) {
            boolean checkAnalytic4SubModeUse = ((SubModeMusicMultiV1) subMode).checkAnalytic4SubModeUse(getSku());
            this.subModeMusicV2 = (SubModeMusicMultiV1) subMode;
            boolean newMusicCode = Support.isNewMusicCode(subModeMusicV2.getMusicCode());
            if (!newMusicCode) {
                /*旧音乐模式-需要存储到OldMusicEffect*/
                saveOldMusic(subModeMusicV2.getSensitivity(), subModeMusicV2.isAutoColor(), subModeMusicV2.isDynamic(), subModeMusicV2.getRgb());
            }
            updateUi();
            if (checkAnalytic4SubModeUse) {
                SafeLog.i(TAG, () -> "updateSubMode() analyticSubModeDetail");
                SubMusicMode subMusicMode = querySubMusicModeByMusicCode(getCurSubMusicCode());
                if (subMusicMode != null) {
                    analyticSubModeDetail(ParamFixedValue.mode_use_music_, subMusicMode.getAnalyticSubModeStr());
                }
            }
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_music;
    }

    private void updateUi() {
        if (!isViewInflateOk()) return;
        /*灵敏度*/
        sensitivityUi(subModeMusicV2.getSensitivity());
        /*选中的子音乐模式*/
        subMusicUi();
    }

    @Override
    protected int getIcNum() {
        if (subModeMusicV2 != null) {
            int ic4Music = subModeMusicV2.ic4Music();
            LogInfra.Log.i(TAG, "getIcNum() ic4Music = " + ic4Music);
            if (ic4Music > 0) return ic4Music;
        }
        return super.getIcNum();
    }
}