package com.govee.dreamcolorlightv2.ble;

import android.text.TextUtils;

import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2home.sku.Colors;
import com.govee.base2light.ac.diy.v1.EventGroupOpDiyResult;
import com.govee.base2light.ble.comm.MultipleBleBytes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.EventBrightness;
import com.govee.base2light.ble.controller.EventDiyTemplate4NewScenes;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultiNewDiyGraffiti;
import com.govee.base2light.ble.controller.EventMultiNewScenes;
import com.govee.base2light.ble.controller.EventMultipleDiy;
import com.govee.base2light.ble.controller.EventMultipleMusic;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultipleColorStripControllerV1;
import com.govee.base2light.group.AbsGroupFactor;
import com.govee.base2light.group.EventGroupOp;
import com.govee.base2light.group.ble.AbsGroupBle;
import com.govee.base2light.group.ble.AbsGroupOp;
import com.govee.base2light.group.ble.FactorInfo;
import com.govee.base2light.group.iot.IColorStripMultiBytes;
import com.govee.dreamcolorlightv2.ConsV1;
import com.govee.dreamcolorlightv2.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 12/1/20
 * 群组op操作$
 */
public class GroupOp extends AbsGroupOp {
    public GroupOp(FactorInfo factorInfo) {
        super(factorInfo);
    }

    @Override
    protected AbsGroupBle makeGroupBle(DeviceModel deviceModel, String bleAddress) {
        return new GroupBle(deviceModel, bleAddress);
    }

    @Override
    protected int isHeartOnOff(byte[] value) {
        if (value != null && value[0] == BleProtocolConstants.SINGLE_READ && value[1] == BleProtocolConstants.SINGLE_HEART) {
            boolean open = value[2] != 0;
            return open ? 1 : 0;
        }
        return -1;
    }

    @Override
    public int factorVersion() {
        return AbsGroupFactor.factor_version_4_rgbic;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBrightness(EventBrightness event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBrightness() result = " + result + " ; write = " + write);
        }
        for (AbsGroupBle groupBle : groupBleMap.values()) {
            if (groupBle.isConnected()) {
                groupBle.controllerEvent(event);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        String bleAddress = event.bleAddress;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result + " ; write = " + write + " ; bleAddress = " + bleAddress);
        }
        if (!TextUtils.isEmpty(bleAddress)) {
            /*需要针对特定的设备做事件处理*/
            AbsGroupBle groupBle = groupBleMap.get(bleAddress);
            if (groupBle != null) {
                if (groupBle.isConnected()) {
                    groupBle.controllerEvent(event);
                }
            } else {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "onEventMode() bleAddress = " + bleAddress + " ; 未找到对应的设备AbsGroupBle");
                }
            }
            return;
        }
        /*bleAddress为null,表明为所有分组设置*/
        for (AbsGroupBle groupBle : groupBleMap.values()) {
            if (groupBle.isConnected()) {
                groupBle.controllerEvent(event);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventGradual(EventGradual event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventGradual() result = " + result + " ; write = " + write);
        }
        for (AbsGroupBle groupBle : groupBleMap.values()) {
            if (groupBle.isConnected()) {
                groupBle.controllerEvent(event);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleDiy(EventMultipleDiy event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        String bleAddress = event.bleAddress;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiy diyCode = " + diyCode + " result = " + result + " ; bleAddress = " + bleAddress);
        }
        if (TextUtils.isEmpty(bleAddress)) return;
        AbsGroupBle groupBle = groupBleMap.get(bleAddress);
        if (groupBle != null) {
            if (groupBle.isConnected()) {
                groupBle.onMultipleControllerOk(event);
            }
            if (result) {
                /*diy效果传输完成;通知设备设置成diy模式*/
                Mode mode = new Mode();
                mode.subMode = new SubModeNewDiy(diyCode);
                ModeController modeController = new ModeController(mode);
                sendController4CheckOn(bleAddress, modeController);
                EventGroupOpDiyResult.sendEventGroupOpDiyResult(true);
            }
        } else {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "onEventMultipleDiy() bleAddress = " + bleAddress + " ; 未找到对应的设备AbsGroupBle");
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleNewScenes(EventMultiNewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        String bleAddress = event.bleAddress;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleNewScenes() result = " + result + " ; scenesCode = " + scenesCode + " ; bleAddress = " + bleAddress);
        }
        if (TextUtils.isEmpty(bleAddress)) return;
        AbsGroupBle groupBle = groupBleMap.get(bleAddress);
        if (groupBle != null) {
            if (groupBle.isConnected()) {
                groupBle.onMultipleControllerOk(event);
            }
            if (result) {
                /*场景效果传输完成，通知设备设置成场景模式*/
                Mode mode = new Mode();
                SubModeScenes subMode = new SubModeScenes();
                subMode.setEffect(scenesCode);
                mode.subMode = subMode;
                ModeController modeController = new ModeController(mode);
                sendController4CheckOn(bleAddress, modeController);
            }
        } else {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "onEventMultipleNewScenes() bleAddress = " + bleAddress + " ; 未找到对应的设备AbsGroupBle");
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyTemplate4NewScenes(EventDiyTemplate4NewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        int diyCode = event.diyCode;
        String bleAddress = event.bleAddress;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyTemplate4NewScenes() result = " + result + " ; scenesCode = " + scenesCode + " ; diyCode = " + diyCode + " ; bleAddress = " + bleAddress);
        }
        if (TextUtils.isEmpty(bleAddress)) return;
        AbsGroupBle groupBle = groupBleMap.get(bleAddress);
        if (groupBle != null) {
            if (groupBle.isConnected()) {
                groupBle.onMultipleControllerOk(event);
            }
            if (result) {
                /*diy模版效果传输完成，通知设备设置成场景模式*/
                Mode mode = new Mode();
                SubModeScenes subMode = new SubModeScenes();
                subMode.setEffect(scenesCode);
                mode.subMode = subMode;
                ModeController modeController = new ModeController(mode);
                sendController4CheckOn(bleAddress, modeController);
                EventGroupOpDiyResult.sendEventGroupOpDiyResult(true);
            }
        } else {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "onEventDiyTemplate4NewScenes() bleAddress = " + bleAddress + " ; 未找到对应的设备AbsGroupBle");
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultiNewDiyGraffiti(EventMultiNewDiyGraffiti event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        String bleAddress = event.bleAddress;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultiNewDiyGraffiti() diyCode = " + diyCode + " ; result = " + result + " ； bleAddress = " + bleAddress);
        }
        if (TextUtils.isEmpty(bleAddress)) return;
        AbsGroupBle groupBle = groupBleMap.get(bleAddress);
        if (groupBle != null) {
            if (result) {
                Mode mode = new Mode();
                mode.subMode = new SubModeNewDiy(diyCode);
                ModeController modeController = new ModeController(mode);
                sendController4CheckOn(bleAddress, modeController);
                EventGroupOpDiyResult.sendEventGroupOpDiyResult(true);
            }
        } else {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "onEventMultiNewDiyGraffiti() bleAddress = " + bleAddress + " ; 未找到对应的设备AbsGroupBle");
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleMusic(EventMultipleMusic event) {
        boolean result = event.isResult();
        int sensitivity = event.getSensitivity();
        int subMusicCode = event.getSubMusicCode();
        String bleAddress = event.bleAddress;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleMusic() result = " + result + " ; sensitivity = " + sensitivity + " ; subMusicCode = " + subMusicCode + " ; bleAddress = " + bleAddress);
        }
        AbsGroupBle groupBle = groupBleMap.get(bleAddress);
        if (groupBle != null) {
            if (groupBle.isConnected()) {
                groupBle.onMultipleControllerOk(event);
            }
            if (result) {
                /*新音乐模式效果传输完成-通知设备切换到音乐模式*/
                Mode mode = new Mode();
                DeviceModel deviceModel = groupBle.getDeviceModel();
                int newMusicModeVersion = Support.getNewMusicModeVersion(deviceModel.getGoodsType(), deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventMultipleMusic() newMusicModeVersion = " + newMusicModeVersion);
                }
                ISubMode subMode = null;
                if (newMusicModeVersion == ConsV1.multi_new_music_version_v2) {
                    subMode = SubModeMusicMultiV3.toNewSubModeMusic(sensitivity, subMusicCode);
                } else if (newMusicModeVersion == ConsV1.multi_new_music_version_v1) {
                    subMode = SubModeMusicMultiV2.toNewSubModeMusic(sensitivity, subMusicCode);
                } else if (newMusicModeVersion == ConsV1.multi_new_music_version_v0) {
                    boolean supportMultiNewMusic4TelinkV1 = Support.supportMultiNewMusic4TelinkV1(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard);
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "onEventMultipleMusic() supportMultiNewMusic4TelinkV1 = " + supportMultiNewMusic4TelinkV1);
                    }
                    subMode = supportMultiNewMusic4TelinkV1 ? SubModeMusicMultiV1_1.toNewSubModeMusic(sensitivity, subMusicCode) : SubModeMusicMultiV1.toNewSubModeMusic(sensitivity, subMusicCode);
                }
                if (subMode == null) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.e(TAG, "onEventMultipleMusic() bleAddress = " + bleAddress + " 的新多包音乐模式判断异常");
                    }
                    return;
                }
                mode.subMode = subMode;
                ModeController modeController = new ModeController(mode);
                sendController4CheckOn(bleAddress, modeController);
            }
        } else {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventMultipleMusic() bleAddress = " + bleAddress + " ; 未找到对应的设备AbsGroupBle");
            }
        }
    }

    public void sendColorStrip(boolean supportPartBrightness, Colors colors) {
        AbsSingleController[] modeControllers;
        if (!supportPartBrightness) {
            modeControllers = SubModeColor.makeSubModeColor(colors);
        } else {
            modeControllers = SubModeColorV2.makeSubModeColor(colors);
        }
        List<byte[]> writeBytesSet = new ArrayList<>();
        if (modeControllers != null) {
            for (AbsSingleController absSingleController : modeControllers) {
                writeBytesSet.add(absSingleController.getValue());
            }
        }
        boolean groupOp = false;
        if (deviceType == DEVICE_TYPE_BLE) {
            for (AbsGroupBle groupBle : groupBleMap.values()) {
                List<AbsSingleController> controllers = new ArrayList<>();
                if (groupBle.isConnected()) {
                    groupOp = true;
                    String bleAddress = groupBle.getBleAddress();
                    boolean supportColorStripMulti = groupBle.isSupportColorStripMulti();
                    if (supportColorStripMulti) {
                        MultipleColorStripControllerV1 stripControllerV1 = new MultipleColorStripControllerV1(colors);
                        groupBle.sendMultipleControllerV1(bleAddress, stripControllerV1);
                    } else {
                        if (writeBytesSet.isEmpty()) continue;
                        for (byte[] sendBytes : writeBytesSet) {
                            AbsSingleController controller = groupBle.makeController(sendBytes);
                            if (controller == null) continue;
                            controller.setBleAddress(bleAddress);
                            controllers.add(controller);
                        }
                        groupBle.startControllerV1(bleAddress, controllers);
                    }
                }
            }
        } else if (groupIotOp != null) {
            groupOp = true;
            HashMap<String, List<byte[]>> bytesMap = new HashMap<>();
            outer:
            for (AbsGroupBle groupBle : groupBleMap.values()) {
                boolean supportColorStripMulti = groupBle.isSupportColorStripMulti();
                if (supportColorStripMulti) {
                    MultipleColorStripControllerV1 stripControllerV1 = new MultipleColorStripControllerV1(colors);
                    List<byte[]> bytes = MultipleBleBytes.getMultipleWriteBytesV1(stripControllerV1);
                    bytesMap.put(groupBle.getDeviceModel().getSkuDeviceKey(), bytes);
                } else {
                    if (writeBytesSet.isEmpty()) continue;
                    List<byte[]> setColorBytes = new ArrayList<>();
                    for (byte[] sendBytes : writeBytesSet) {
                        AbsSingleController controller = groupBle.makeController(sendBytes);
                        if (controller == null) continue outer;
                        setColorBytes.add(controller.getValue());
                    }
                    bytesMap.put(groupBle.getDeviceModel().getSkuDeviceKey(), setColorBytes);
                }
            }
            groupIotOp.sendControllerWithColorStrip(new IColorStripMultiBytes() {
                @Nullable
                @Override
                public List<byte[]> generateColorStripMultiBytes(DeviceModel deviceModel) {
                    return bytesMap.get(deviceModel.getSkuDeviceKey());
                }
            });
        }
        if (groupOp) {
            EventGroupOp.sendEventGroupOp(0);
        }
        /*检查是否有未开启的设备*/
        for (AbsGroupBle groupBle : groupBleMap.values()) {
            groupBle.checkOn();
        }
    }
}