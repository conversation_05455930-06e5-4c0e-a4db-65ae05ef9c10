package com.govee.dreamcolorlightv2.ble;

import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;

import java.util.List;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020/6/24
 * ble指令解析器$
 */
public final class BleParser {
    private BleParser() {
    }

    public static int[] parseColorModeColors(List<byte[]> bytes) {
        if (bytes != null && !bytes.isEmpty()) {
            int[] colors = new int[bytes.size() * 4];
            for (byte[] original20Bytes : bytes) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(original20Bytes);
                ColorParse colorParse = new ColorParse(valid17Bytes);
                int destPos = Math.max(0, colorParse.group - 1) * colorParse.rgb.length;
                System.arraycopy(colorParse.rgb, 0, colors, destPos, colorParse.rgb.length);
            }
            return colors;
        }
        return null;
    }

    private static class ColorParse {
        int group;
        int[] rgb;

        ColorParse(byte[] valid17Bytes) {
            rgb = new int[4];
            byte[] rgbBytes = new byte[3];
            group = BleUtil.getUnsignedByte(valid17Bytes[0]);
            int index = 1;
            for (int i = 0; i < rgb.length; i++) {
                System.arraycopy(valid17Bytes, index, rgbBytes, 0, rgbBytes.length);
                int r = BleUtil.getUnsignedByte(rgbBytes[0]);
                int g = BleUtil.getUnsignedByte(rgbBytes[1]);
                int b = BleUtil.getUnsignedByte(rgbBytes[2]);
                rgb[i] = ColorUtils.toColor(r, g, b);
                index += 3;
            }
        }
    }

    public static int[] parseColorModeColorsWithBrightness(List<byte[]> bytes) {
        if (bytes != null && !bytes.isEmpty()) {
            int[] colors = new int[bytes.size() * 3];
            for (byte[] original20Bytes : bytes) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(original20Bytes);
                ColorParse2 colorParse = new ColorParse2(valid17Bytes);
                int destPos = Math.max(0, colorParse.group - 1) * colorParse.rgb.length;
                System.arraycopy(colorParse.rgb, 0, colors, destPos, colorParse.rgb.length);
            }
            return colors;
        }
        return null;
    }

    public static int[] parseColorModeColors4Brightness(List<byte[]> bytes) {
        if (bytes != null && !bytes.isEmpty()) {
            int[] brightness = new int[bytes.size() * 3];
            for (byte[] original20Bytes : bytes) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(original20Bytes);
                BrightnessParse brightnessParse = new BrightnessParse(valid17Bytes);
                int destPos = Math.max(0, brightnessParse.group - 1) * brightnessParse.brightness.length;
                System.arraycopy(brightnessParse.brightness, 0, brightness, destPos, brightnessParse.brightness.length);
            }
            return brightness;
        }
        return null;
    }


    private static class ColorParse2 {
        int group;
        int[] rgb;

        ColorParse2(byte[] valid17Bytes) {
            rgb = new int[3];
            byte[] rgbBytes = new byte[3];
            group = BleUtil.getUnsignedByte(valid17Bytes[0]);
            int index = 2;
            for (int i = 0; i < rgb.length; i++) {
                System.arraycopy(valid17Bytes, index, rgbBytes, 0, rgbBytes.length);
                int r = BleUtil.getUnsignedByte(rgbBytes[0]);
                int g = BleUtil.getUnsignedByte(rgbBytes[1]);
                int b = BleUtil.getUnsignedByte(rgbBytes[2]);
                rgb[i] = ColorUtils.toColor(r, g, b);
                index += 4;
            }
        }
    }

    private static class BrightnessParse {
        int group;
        int[] brightness;

        BrightnessParse(byte[] valid17Bytes) {
            brightness = new int[3];
            group = BleUtil.getUnsignedByte(valid17Bytes[0]);
            int index = 1;
            for (int i = 0; i < brightness.length; i++) {
                brightness[i] = BleUtil.getUnsignedByte(valid17Bytes[index]);
                index += 4;
            }
        }
    }
}