package com.govee.dreamcolorlightv2.iot;

import com.govee.base2home.iot.AbsCmd;

import androidx.annotation.Keep;

/**
 * Create by x<PERSON>ying<PERSON> on 2019-07-23
 * cmd=brightness
 */
@Keep
public class CmdBrightness extends AbsCmd {
    int val;

    public CmdBrightness(int brightness) {
        this.val = brightness;
    }

    @Override
    public String getCmd() {
        return Cmd.brightness;
    }

}