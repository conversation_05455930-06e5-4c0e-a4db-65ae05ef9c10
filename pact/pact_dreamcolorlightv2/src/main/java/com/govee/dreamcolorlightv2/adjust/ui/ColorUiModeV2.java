package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.ui.R;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsColorUiMode;

import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;

/**
 * Create by hey on 2021/2/7
 * $
 */
public class ColorUiModeV2 extends AbsColorUiMode {
    public ColorUiModeV2(int goodsType, String sku, String device, int ic, DiySupportV1 diySupport) {
        super(goodsType, sku, device, ic, diySupport);
    }

    @Override
    public AbsModeFragment getUiFragment() {
        ColorFragmentV2 fragment = new ColorFragmentV2();
        fragment.makeArguments(getSku());
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_color_mini, R.mipmap.new_control_light_btb_mode_color_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        subModeColor.loadLocal();
        return subModeColor;
    }
}
