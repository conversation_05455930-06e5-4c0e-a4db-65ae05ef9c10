package com.govee.dreamcolorlightv2.scenes;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.rhythm.ui.AbsRhythmDiyUI;
import com.govee.dreamcolorlightv2.adjust.Diy;
import com.govee.dreamcolorlightv2.pact.Comm;
import com.govee.dreamcolorlightv2.pact.Support;

import java.util.List;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by linshicong on 12/28/20
 */
public class RhythmDiyUI extends AbsRhythmDiyUI {
    public RhythmDiyUI(AppCompatActivity ac, DeviceModel deviceModel, OnSelectChangeListener selectChangeListener) {
        super(ac, deviceModel, selectChangeListener);
    }

    @Override
    protected DiySupportV1 getDiySupport() {
        return Diy.getDiySupport(Support.getDiyVersion(deviceModel.getGoodsType(), deviceModel.versionSoft, deviceModel.versionHard, deviceModel.pactCode, deviceModel.getSku()));
    }

    @Nullable
    @Override
    protected AbsCmd makeRuleCmd() {
        return Comm.makeDiyCmd4IotComm(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard, deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, getDiyProtocol(), getDiyGraffiti4Rgbic(), getDiyTemplate(), getDiyStudio(), getDiyAi());
    }

    @Nullable
    @Override
    protected List<String> makeBleCmd() {
        return Comm.makeDiy4BleComm(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard, deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, getDiyProtocol(), getDiyGraffiti4Rgbic(), getDiyTemplate(), getDiyStudio(), getDiyAi());
    }

}