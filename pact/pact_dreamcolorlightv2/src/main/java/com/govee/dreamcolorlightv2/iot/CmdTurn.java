package com.govee.dreamcolorlightv2.iot;

import com.govee.base2home.iot.AbsCmd;

import androidx.annotation.Keep;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-11
 * cmd=turn$
 */
@Keep
public class CmdTurn extends AbsCmd {
    private static final int cmd_turn_off = 0;/*0关闭*/
    private static final int cmd_turn_on = 1;/*1打开*/
    int val;

    public CmdTurn(boolean on) {
        val = on ? cmd_turn_on : cmd_turn_off;
    }

    @Override
    public String getCmd() {
        return Cmd.turn;
    }
}