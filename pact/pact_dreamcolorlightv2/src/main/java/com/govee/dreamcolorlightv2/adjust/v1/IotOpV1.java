package com.govee.dreamcolorlightv2.adjust.v1;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.pact.iot.AbsIotOpV1;
import com.govee.dreamcolorlightv2.iot.Cmd;
import com.govee.dreamcolorlightv2.iot.CmdStatus;

import androidx.annotation.NonNull;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020/7/25
 * iot操作op$
 */
class IotOpV1 extends AbsIotOpV1 {
    @Override
    protected String getCmd4Pt() {
        return Cmd.ptReal;
    }

    @NonNull
    @Override
    protected AbsCmd getCmd4DeviceInfo() {
        return new CmdStatus();
    }

    @NonNull
    @Override
    protected String getAutoStatusCmd() {
        return Cmd.status;
    }

    @Override
    protected String getReadCmdParserKey(String cmd) {
        return Cmd.getCmdReadParseKey(cmd);
    }
}