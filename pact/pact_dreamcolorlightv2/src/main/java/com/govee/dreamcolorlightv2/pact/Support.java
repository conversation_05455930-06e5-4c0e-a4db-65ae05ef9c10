package com.govee.dreamcolorlightv2.pact;

import com.govee.ui.R;
import android.text.TextUtils;
import android.util.SparseArray;

import com.govee.base2home.ota.OtaType;
import com.govee.base2home.pact.BleUtil;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.Pact;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.pact.support.OldDreamColorUtil;
import com.govee.base2home.pact.support.OldRgbicBkUtil;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2home.util.NumberUtil;
import com.govee.base2light.ac.diy.DiyM;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.ScenesRgbIC;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.ble.scenes.RgbIcScenesV1;
import com.govee.base2light.ble.scenes.ScenesM;
import com.govee.base2light.light.IScenes;
import com.govee.base2light.light.ScenesHint;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.util.UtilSku;
import com.govee.dreamcolorlightv2.ConsV1;

import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.SubModeScenes;
import com.govee.dreamcolorlightv2.pact.bleiot.EffectOp4BleOp;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

/**
 * Create by xieyingwu on 2020/6/24
 * $
 */
public final class Support {
    private static final String TAG = "Support";

    private Support() {
    }

    private static final String H6143 = "H6143";
    private static final String H6144 = "H6144";
    private static final String H611A = "H611A";

    public static List<Protocol> supportProtocolsV1 = new ArrayList<>();
    public static List<Protocol> supportProtocolsV2 = new ArrayList<>();

    public static void addSupportPact() {
        Pact pact = Pact.getInstance;
        Protocol protocol1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V2_1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V2_1);
        Protocol protocol2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V2_1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V2_2);
        supportProtocolsV1.add(protocol1);
        supportProtocolsV1.add(protocol2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2, protocol1);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2, protocol2);

        /*支持BK方案*/
        Protocol protocol3 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V3_1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V3_1);
        supportProtocolsV2.add(protocol3);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2, protocol3);

        /*初始化支持的默认sku图*/
        ThemeM.getInstance.addDefSkuRes(H6143, com.govee.dreamcolorlightv2.R.mipmap.add_list_type_device_6143);
        ThemeM.getInstance.addDefSkuRes(H6144, com.govee.dreamcolorlightv2.R.mipmap.add_list_type_device_6144);
        ThemeM.getInstance.addDefSkuRes(H611A, com.govee.dreamcolorlightv2.R.mipmap.add_list_type_device_611a);
        /*注册diyOp*/
        DiyM.getInstance.addDiyOp(EffectOp4BleOp.op);
    }

    /**
     * 设备列表的goodsType集合
     */
    public static final int[] deviceItemGoodsTypes = {
            GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2,
    };

    /**
     * 效果操作op-支持ble+wifi的goodsType
     */
    public static final int[] effect4OpBleWifiGoodsTypes = {
            GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2,
    };

    /**
     * 支持昼夜节律的goodsType（必须是Wi-Fi设备）
     *
     * @return
     */
    public static List<Integer> getSupportRhythmGoodsType() {
        List<Integer> supports = new ArrayList<>();
        supports.add(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2);
        return supports;
    }

    public static boolean supportPact(int goodsType, Protocol protocol) {
        return supportPactV1(goodsType, protocol) || supportPactV2(goodsType, protocol);
    }

    private static boolean supportPactV1(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2) {
            if (!supportProtocolsV1.isEmpty()) {
                for (Protocol pro : supportProtocolsV1) {
                    if (pro.isSameProtocol(pactType, pactCode)) return true;
                }
            }
        }
        return false;
    }

    private static boolean supportPactV2(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2) {
            if (!supportProtocolsV2.isEmpty()) {
                for (Protocol pro : supportProtocolsV2) {
                    if (pro.isSameProtocol(pactType, pactCode)) return true;
                }
            }
        }
        return false;
    }

    /**
     * 支持ble的goodsType集合
     */
    public static String[] supportBleGoodsTypeSet = new String[]{
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2),
    };

    /**
     * 支持iot的goodsType集合
     */
    public static String[] supportIotGoodsTypeSet = new String[]{
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2)
    };

    /**
     * 获取wifi输入限制
     *
     * @return [2];[0]ssidInputLimit;[1]passwordInputLimit
     */
    public static int[] getWifiInputLimit() {
        return new int[]{32, 64};
    }

    public static int[] getDefHeaderRes(int goodsType) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2) {
            return new int[]{R.mipmap.new_light_title_6160_on, R.mipmap.new_light_title_6160_off, R.mipmap.new_light_title_6160_off};
        }
        return null;
    }

    /**
     * 获取亮度范围
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return [3];[0-是否需要计算百分比转化][1-最低亮度][2-最高亮度]
     */
    public static int[] getBrightnessRange(String sku, int goodsType, int pactType, int pactCode) {
        boolean bk = isBKProtocol(sku, goodsType, pactType, pactCode);
        if (bk) {
            return new int[]{0, 1, 100};
        }
        if (OldDreamColorUtil.oldSku4ChangeMinBrightnessList.contains(sku)) {
            return new int[]{1, 6, 254};
        }
        return new int[]{1, 20, 254};
    }

    /**
     * 检测给到wifi设备的当前服务器域名版本值
     *
     * @return
     */
    public static int check2WifiDeviceRunModeVersion() {
        return 1;/*新sku默认使用v1*/
    }

    /**
     * 获取最大的灯带段数
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return
     */
    public static int getBulbStringMaxNum(int goodsType, int pactType, int pactCode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getBulbStringMaxNum() goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode);
        }
        return 15;
    }

    /**
     * 是否支持场景模式下，特定子模式是否支持亮度调节
     *
     * @param goodsType
     * @param effect
     * @return
     */
    public static boolean supportScenesBrightnessOp(String sku, int goodsType, int pactType, int pactCode, int effect) {
        boolean bkProtocol = isBKProtocol(sku, goodsType, pactType, pactCode);
        if (bkProtocol) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2) {
            /*日出，日落效果，当前goodsType下，不支持调节亮度*/
            return effect != BleProtocol.value_sub_mode_scenes_sunset && effect != BleProtocol.value_sub_mode_scenes_gm;
        }
        /*默认场景支持调节亮度*/
        return true;
    }

    private static final String new_soft_version_v1 = "1.04.00";/*支持rgb+rgbIC场景的固件版本*/
    private static final String new_hard_version_v1_rgbic_h6163_h6117 = "1.00.02";

    /**
     * 新场景版本H6117
     *
     * @param goodsType
     * @param softVersion
     * @param pactCode
     * @return
     */
    public static int newScenesVersion(int goodsType, String softVersion, int pactCode) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2) {
            if (pactCode == GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V2_1) {
                int curVersionInt = UtilSku.parseVersion(softVersion);
                int compareVersionInt = UtilSku.parseVersion(new_soft_version_v1);
                return curVersionInt >= compareVersionInt ? 1 : 0;
            }
        }
        return 1;
    }


    public static int newScenesVersion(int goodsType, String softVersion, int pactCode, String sku) {
        if (pactCode > 0) {
            return newScenesVersion(goodsType, softVersion, pactCode);
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2) {
            if (H6143.equals(sku) || H6144.equals(sku)) {
                int curVersionInt = UtilSku.parseVersion(softVersion);
                int compareVersionInt = UtilSku.parseVersion(new_soft_version_v1);
                return curVersionInt >= compareVersionInt ? 1 : 0;
            }
        }
        return 1;
    }


    private static final HashMap<Integer, ScenesHint> scenesHintMap = new HashMap<>();

    /**
     * 获取场景提示信息
     *
     * @param effect
     * @return null表明当前不支持hint提示
     */
    public static ScenesHint getScenesHint(int effect) {
        checkHintMap();
        return scenesHintMap.get(effect);
    }

    private synchronized static void checkHintMap() {
        if (scenesHintMap.isEmpty()) {
            /*日出提示*/
            ScenesHint scenesHint4gm = ScenesHint.makeHint4gm(BleProtocol.value_sub_mode_scenes_gm);
            scenesHintMap.put(scenesHint4gm.effect, scenesHint4gm);
            /*日落提示*/
            ScenesHint scenesHint4sunset = ScenesHint.makeHint4sunset(BleProtocol.value_sub_mode_scenes_sunset);
            scenesHintMap.put(scenesHint4sunset.effect, scenesHint4sunset);
            /*警报提示*/
            ScenesHint scenesHint4alarm = ScenesHint.makeHint4alarm(ScenesRgbIC.effect_alarm, ScenesOp.scene_type_rgbic);
            scenesHintMap.put(scenesHint4alarm.effect, scenesHint4alarm);
        }
    }

    private static final SparseArray<IScenes> scenesMap = new SparseArray<>();

    /**
     * 获取对应版本支持的场景
     *
     * @param version
     * @return
     */
    public static IScenes getScenes(int version) {
        checkScenesMap();
        IScenes iScenes = scenesMap.get(version);
        if (iScenes != null) return iScenes;
        return scenesMap.get(0);
    }

    private synchronized static void checkScenesMap() {
        if (scenesMap.size() == 0) {
            scenesMap.append(0, new ScenesV0());
            scenesMap.append(1, RgbIcScenesV1.scenesV1);
        }
    }

    /**
     * 判断是不是场景多包
     *
     * @param sku        sku
     * @param device
     * @param mode       mode
     * @param newVersion newVersion
     * @return AbsMultipleControllerV14Scenes
     */
    public static AbsMultipleControllerV14Scenes is2NewScenesMode(String sku, String device, Mode mode, int newVersion) {
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenes) {
            if (newVersion == 1) {
                int effect = ((SubModeScenes) subMode).getEffect();
                CategoryV1.SceneV1 scene = ScenesM.getInstance.getSceneV1(sku, device, effect);
                if (scene != null) {
                    return ScenesOp.parseSceneV1(sku, scene, ScenesOp.scene_type_rgb, ScenesOp.scene_type_rgbic);
                }
                return ScenesOp.parseEffect(sku, effect, 1, 2);
            }
        }
        return null;
    }

    /**
     * 支持rgbic的群控因子判断
     *
     * @param goodsType
     * @param sku
     * @param versionSoft
     * @param versionHard
     * @return
     */
    public static boolean supportRgbicFactor(int goodsType, String sku, String versionSoft, String versionHard) {
        if (H6143.equals(sku)
                || H6144.equals(sku)
                || OldDreamColorUtil.oldDreamColor4BleSkuArray.contains(sku)
        ) {
            int curVersionInt = UtilSku.parseVersion(versionSoft);
            int compareVersionInt = UtilSku.parseVersion(new_soft_version_v1);
            return curVersionInt >= compareVersionInt;
        }
        if (OldDreamColorUtil.oldDreamColor4BleIotSkuArray.contains(sku)) {
            /*蓝牙+Wi-Fi的旧幻彩设备，需要加入硬件版本判断*/
            int curSoftVersionInt = UtilSku.parseVersion(versionSoft);
            int curHardVersionInt = UtilSku.parseVersion(versionHard);
            int compareHardVersionInt = UtilSku.parseVersion(new_hard_version_v1_rgbic_h6163_h6117);
            int compareSoftVersionInt = UtilSku.parseVersion(new_soft_version_v1);
            return curSoftVersionInt >= compareSoftVersionInt && curHardVersionInt >= compareHardVersionInt;
        }
        /*当前goodsType下，特定sku需要判断软件版本*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1) {
            if ("H6145".equals(sku) || "H6146".equals(sku) || "H6147".equals(sku) || "H6171".equals(sku)) {
                int curVersionInt = UtilSku.parseVersion(versionSoft);
                int compareVersionInt = UtilSku.parseVersion(new_soft_version_v1);
                return curVersionInt >= compareVersionInt;
            }
            return true;
        }
        /*非特定sku直接依据goodsType判断*/
        return goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2
                || goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_DESK_LIGHT
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H616C
                ;
    }

    /**
     * 自身项目是否支持BK协议
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return
     */
    private static boolean supportBkProtocol(int goodsType, int pactType, int pactCode) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_DESK_LIGHT) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2) {
            return pactType >= GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V3_1
                    && pactCode >= GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V3_1;
        }
        return false;
    }

    /**
     * 是否支持新的颜色模式
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @return
     */
    public static boolean supportNewColorMode(String sku, int goodsType, int pactType, int pactCode, String versionSoft) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "supportNewColorMode() sku = " + sku + " ; goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode + " ; versionSoft = " + versionSoft);
        }
        return isBKProtocol(sku, goodsType, pactType, pactCode);
    }


    /**
     * 是否是bk协议-需要考虑整体的幻彩设备
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return
     */
    public static boolean isBKProtocol(String sku, int goodsType, int pactType, int pactCode) {
        /*是否是旧款幻彩设备*/
        if (OldDreamColorUtil.supportOldDreamColorUpdateSkuArray.contains(sku)) {
            return OldDreamColorUtil.isBkProtocol4OldDreamDevices(sku, pactType, pactCode);
        }
        /*旧款幻彩设备-配置的goodsType*/
        if (OldDreamColorUtil.isBkProtocol4OldLib(goodsType, pactType, pactCode)) {
            return true;
        }
        /*已配置了goodsType的bk升级方案兼容*/
        if (OldRgbicBkUtil.isRgbicBk4Ble(sku, pactType, pactCode)) {
            return true;
        }
        /*当前H6143,H6144的bk协议判断*/
        return supportBkProtocol(goodsType, pactType, pactCode);
    }

    /**
     * 是否是旧灯带
     *
     * @param sku
     * @return
     */
    public static boolean isOldLight(String sku) {
        /*是否是旧款幻彩设备*/
        return OldDreamColorUtil.supportOldDreamColorUpdateSkuArray.contains(sku);
    }

    public static boolean isBleWifiLight(String sku) {
        return bleWifiLightSkuArray.contains(sku);
    }

    private static final String[] bleWifiLight = {
            "H6143",
            "H6144",
            "H6163",
            "H6117",
    };

    public static final List<String> bleWifiLightSkuArray = Arrays.asList(bleWifiLight);

    /**
     * 检查场景是否支持
     *
     * @param subMode         subMode
     * @param sku             sku
     * @param version         version
     * @param isBleOp         isBleOp
     * @param versionSoft     versionSoft
     * @param versionHard     versionHard
     * @param isIotOp         isIotOp
     * @param wifiSoftVersion wifiSoftVersion
     * @param wifiHardVersion wifiHardVersion
     * @return ISubMode                                                                                                                        ccx
     */
    public static ISubMode checkScenesModeEffect(ISubMode subMode, String sku, int goodsType, int pactType, int pactCode, int version,
                                                 boolean isBleOp, String versionSoft, String versionHard,
                                                 boolean isIotOp, String wifiSoftVersion, String wifiHardVersion) {
        if (subMode instanceof SubModeScenes) {
            int effect = ((SubModeScenes) subMode).getEffect();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkScenesModeEffect() effect = " + effect + "---version:" + version);
            }
            List<CategoryV1> categoriesV1 = ScenesM.getInstance.getCategoriesV1(sku);
            boolean isHaveMyScenes = categoriesV1 != null && !categoriesV1.isEmpty() && version == 1;
            if (isHaveMyScenes) {
                for (CategoryV1 category : categoriesV1) {
                    for (CategoryV1.SceneV1 scene : category.scenes) {
                        if (scene.getSceneCode(0, sku) == effect) {
                            int sceneType = scene.getSceneType(0, sku);
                            int cmdVersion = scene.getCmdVersion(sku, 0);
                            if (Support.checkShareEffectVersion(sceneType, cmdVersion, sku, goodsType, pactType, pactCode, versionSoft, versionHard)) {
                                return null;
                            }
                            if (LogInfra.openLog()) {
                                LogInfra.Log.e(TAG, "上次应用场景版本不支持，重新查找支持场景");
                            }
                            return getSupportScenes(version, true, sku, goodsType, pactType, pactCode,
                                    isBleOp, versionSoft, versionHard, isIotOp, wifiSoftVersion, wifiHardVersion);
                        }
                    }
                }
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "我的场景查找不到,重新查找支持场景");
                }
                return getSupportScenes(version, true, sku, goodsType, pactType, pactCode, isBleOp, versionSoft,
                        versionHard, isIotOp, wifiSoftVersion, wifiHardVersion);
            }
            if (checkScenesCode(version, effect)) {
                return null;
            }
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "从静态场景中重新查找支持场景");
            }
            return getSupportScenes(version, false, sku, goodsType, pactType, pactCode, isBleOp, versionSoft,
                    versionHard, isIotOp, wifiSoftVersion, wifiHardVersion);
        }
        return null;
    }

    /**
     * 获取第一个支持的场景
     *
     * @param version        version
     * @param isHaveMyScenes isHaveMyScenes
     * @return ISubMode
     */
    private static ISubMode getSupportScenes(int version, boolean isHaveMyScenes, String sku, int goodsType, int pactType, int pactCode,
                                             boolean isBleOp, String versionSoft, String versionHard, boolean isIotOp, String wifiSoftVersion,
                                             String wifiHardVersion) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getSupportScenes() version = " + version + " ; isHaveMyScenes = "
                    + isHaveMyScenes + " ; sku = " + sku + " ; goodsType = " + goodsType
                    + " ; pactType = " + pactType + " ; pactCode = " + pactCode
                    + " ; isBleOp = " + isBleOp + " ; versionSoft = " + versionSoft + " ; versionHard = " + versionHard + " ; isIotOp = " + isIotOp + " ; wifiSoftVersion = " + wifiSoftVersion + " ; wifiHardVersion = " + wifiHardVersion);
        }
        if (isHaveMyScenes) {
            List<CategoryV1> categoriesV1 = ScenesM.getInstance.getCategoriesV1(sku);
            for (CategoryV1 category : categoriesV1) {
                for (CategoryV1.SceneV1 scene : category.scenes) {
                    int sceneType = scene.getSceneType(0, sku);
                    int cmdVersion = scene.getCmdVersion(sku, 0);
                    if (Support.checkShareEffectVersion(sceneType, cmdVersion, sku, goodsType, pactType, pactCode, versionSoft, versionHard)) {
                        SubModeScenes subModeScenes = new SubModeScenes();
                        subModeScenes.setEffect(scene.getSceneCode(0, sku));
                        return subModeScenes;
                    }
                }
            }
        }
        IScenes scenes = Support.getScenes(version);
        /*当前版本下的场景不支持该effect，因此采用默认第一个effect*/
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.setEffect(scenes.scenesEffectSet()[0]);
        return subModeScenes;
    }

    /**
     * 检查code是否在支持的静态列表内
     *
     * @param newVersion newVersion
     * @param effect     effect
     * @return boolean
     */
    private static boolean checkScenesCode(int newVersion, int effect) {
        IScenes scenes = Support.getScenes(newVersion);
        return scenes.supportEffects().contains(effect);
    }

    public static void isSupportMicByPhone(String sku, String device, String softVersion, int goodsType) {
        AbsMicFragmentV4.SupportMicStatus support = AbsMicFragmentV4.SupportMicStatus.not_support;
        try {
            if (TextUtils.isEmpty(softVersion)) {
                return;
            }
            int curVersion = Integer.parseInt(softVersion.replace(".", ""));
            int flagVersion = 0;
            //H6143、H6144、H611A
            if (GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2 == goodsType) {
                flagVersion = 10600;//10600;
            }
            if (flagVersion != 0) {
                if (flagVersion <= curVersion) {
                    support = AbsMicFragmentV4.SupportMicStatus.support_new_order;
                }
            }
            AbsMicFragmentV4.saveSupportMicModeByPhone(sku, device, support);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static AbsMicFragmentV4.SupportMicStatus getMicStatus(int goodsType, String versionSoft) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2) {
            int curVersion = NumberUtil.parseVersion(versionSoft);
            if (curVersion >= NumberUtil.parseVersion("1.06.00"))
                return AbsMicFragmentV4.SupportMicStatus.support_new_order;
        }
        return AbsMicFragmentV4.SupportMicStatus.not_support;
    }

    private static final String hard_version_v0_new_color_mode = "1.00.01";
    private static final String soft_version_v0_new_color_mode = "1.06.00";

    private static final String hard_version_v2_new_color_mode = "1.00.02"; /*6163 6117硬件版本号*/
    private static final String hard_version_v3_new_color_mode = "1.00.03"; /*6119硬件版本号*/

    /**
     * 是否支持带分段亮度控制 dreamcolorV2里面项目使用 bk版本号2开头 一定支持
     *
     * @param versionSoft
     * @param versionHard
     * @return
     */
    public static boolean supportSubModeColor4PartBrightness(String versionSoft, String versionHard) {
        int curVersionHardInt = BleUtil.parseVersion(versionHard);
        int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
        if (curVersionHardInt <= 0 || curVersionSoftInt <= 0) return false;
        boolean telinkOta = OtaType.isTelinkOta(versionHard);
        if (!telinkOta) return true;
        int compareVersionHardInt = BleUtil.parseVersion(hard_version_v0_new_color_mode);
        int compareVersionSoftInt = BleUtil.parseVersion(soft_version_v0_new_color_mode);
        return curVersionHardInt >= compareVersionHardInt && curVersionSoftInt >= compareVersionSoftInt;
    }

    /*判断是否支持分段亮度 包括版本判断和bk判断 包含旧sku*/
    public static boolean supportSubModeColor4PartBrightness(String sku, String versionSoft, String versionHard, int goodsType, int pactType, int pactCode) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H616C) return true;
        int curVersionHardInt = BleUtil.parseVersion(versionHard);
        int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
        if (curVersionHardInt <= 0 || curVersionSoftInt <= 0) return false;
        boolean bkProtocol = isBKProtocol(sku, goodsType, pactType, pactCode);
        if (bkProtocol) return true;
        int compareVersionHardInt = 0;
        switch (sku) {
            case "H6163":
            case "H6117":
                compareVersionHardInt = BleUtil.parseVersion(hard_version_v2_new_color_mode);
                break;
            case "H6102":
            case "H6127":
            case "H6116":
            case "H6161":
            case "H6125":
            case "H6126":
                compareVersionHardInt = BleUtil.parseVersion(hard_version_v3_new_color_mode);
                break;
            case "H6143":
            case "H6144":
            case "H6145":
            case "H6146":
            case "H6147":
            case "H6171":
            case "H6184":
                compareVersionHardInt = BleUtil.parseVersion(hard_version_v0_new_color_mode);
                break;
        }
        /*旧款设备*/
        if (compareVersionHardInt > 0) {
            int compareVersionSoftInt = BleUtil.parseVersion(soft_version_v0_new_color_mode);
            return curVersionHardInt >= compareVersionHardInt && curVersionSoftInt >= compareVersionSoftInt;
        }
        return false;
    }

    /**
     * 是否支持rgbicV1版本
     *
     * @param sku
     * @param versionSoft
     * @param versionHard
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return
     */
    public static boolean supportRgbicV1(String sku, String versionSoft, String versionHard, int goodsType, int pactType, int pactCode) {
        return supportSubModeColor4PartBrightness(sku, versionSoft, versionHard, goodsType, pactType, pactCode);
    }

    /**
     * 获取颜色模式的对应UI版本号
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param versionHard
     * @return
     */
    public static int getSubModeColorVersion(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard) {
        boolean supportSubModeColor4PartBrightness = supportSubModeColor4PartBrightness(versionSoft, versionHard);
        if (supportSubModeColor4PartBrightness) return 2;
        boolean supportNewColorMode = supportNewColorMode(sku, goodsType, pactType, pactCode, versionSoft);
        return supportNewColorMode ? 1 : 0;
    }

    /**
     * 获取diy 版本
     *
     * @param goodsType
     * @param softVersion
     * @param versionHard
     * @param pactCode
     * @param sku
     * @return
     */
    public static int getDiyVersion(int goodsType, String softVersion, String versionHard, int pactCode, String sku) {
        boolean supportGraffiti = Support.supportSubModeColor4PartBrightness(softVersion, versionHard);
        return supportGraffiti ? 2 : newScenesVersion(goodsType, softVersion, pactCode, sku);
    }

    public static boolean checkShareEffectVersion(int sceneType, int version, String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkShareEffectVersion() sceneType = " + sceneType + " ; version = " + version + " ; sku = " + sku + " ; goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode + " ; versionSoft = " + versionSoft + " ; versionHard = " + versionHard);
        }
        if (version == 0) return true;
        if (sceneType == ScenesOp.scene_type_rgbic) {
            return version == 1 && supportSubModeColor4PartBrightness(versionSoft, versionHard);
        }
        return false;
    }

    /**
     * 是否是新的音乐模式code
     *
     * @param musicCode
     * @return
     */
    public static boolean isNewMusicCode(int musicCode) {
        return AbsNewMusicEffect.newMusicCode4RgbicList.contains(musicCode) || AbsNewMusicEffect.newMusicCode4RgbList.contains(musicCode);
    }

    private static final String support_multi_new_music_soft_version_4_telink = "1.07.00";
    private static final String support_multi_new_music_soft_version_4_bk = "2.04.00";
    private static final String support_multi_new_music_soft_version_4_frk = "3.01.00";

    private static final String[] telinkOldSkuMultiMusic4Ble = {
            "H6163",
            "H6117",
            "H6125",
            "H6126",
            H6143,
            H6144,
            "H6116",
            "H6127",
            "H6102"
    };
    private static final List<String> telinkOldSkuMultiMusic4BleList = Arrays.asList(telinkOldSkuMultiMusic4Ble);

    private static final String[] telinkOldSkuMultiMusic4BleV1 = {
            "H6145",
            "H6146",
            "H6147",
            "H6171"
    };

    private static final List<String> telinkOldSkuMultiMusic4BleListV1 = Arrays.asList(telinkOldSkuMultiMusic4BleV1);

    /**
     * 是否支持新的音乐多包效果-telink-v1
     *
     * @param sku
     * @param softVersion
     * @param versionHard
     * @return
     */
    public static boolean supportMultiNewMusic4TelinkV1(String sku, String softVersion, String versionHard) {
        if (telinkOldSkuMultiMusic4BleListV1.contains(sku)) {
            /*目前仅telink支持新音乐模式*/
            OtaType otaType = OtaType.parseHardVersion(versionHard);
            if (!OtaType.Telink.equals(otaType)) return false;
            int curVersionInt = UtilSku.parseVersion(softVersion);
            int compareVersionInt = UtilSku.parseVersion(support_multi_new_music_soft_version_4_telink);
            return curVersionInt >= compareVersionInt;
        }
        return false;
    }

    /**
     * 是否支持新的音乐多包效果-telink
     *
     * @param sku
     * @param softVersion
     * @param versionHard
     * @return
     */
    public static boolean supportMultiNewMusic4Telink(String sku, String softVersion, String versionHard) {
        if (telinkOldSkuMultiMusic4BleList.contains(sku)) {
            /*目前仅telink支持新音乐模式*/
            OtaType otaType = OtaType.parseHardVersion(versionHard);
            if (!OtaType.Telink.equals(otaType)) return false;
            int curVersionInt = UtilSku.parseVersion(softVersion);
            int compareVersionInt = UtilSku.parseVersion(support_multi_new_music_soft_version_4_telink);
            return curVersionInt >= compareVersionInt;
        }
        return false;
    }

    public static boolean supportMultiNewMusic4BK(String softVersion, String versionHard) {
        /*bk方案支持新音乐模式*/
        OtaType otaType = OtaType.parseHardVersion(versionHard);
        if (!OtaType.BK_01.equals(otaType)) return false;
        int curVersionInt = UtilSku.parseVersion(softVersion);
        int compareVersionInt = UtilSku.parseVersion(support_multi_new_music_soft_version_4_bk);
        return curVersionInt >= compareVersionInt;
    }

    /**
     * frk方案设备是否支持新多包音乐模式
     *
     * @param versionSoft
     * @param versionHard
     * @return
     */
    private static boolean supportMultiMusicMode4Frk(String versionSoft, String versionHard) {
        boolean frkOtaV1 = OtaType.isFRKOtaV1(versionHard);
        if (!frkOtaV1) return false;/*非frk方案-不处理*/
        int curVersionInt = UtilSku.parseVersion(versionSoft);
        int compareVersionInt = UtilSku.parseVersion(support_multi_new_music_soft_version_4_frk);
        return curVersionInt >= compareVersionInt;
    }

    /**
     * 获取支持新音乐模式版本的协议值
     *
     * @param sku
     * @param versionSoft
     * @param versionHard
     * @return
     */
    public static int getNewMusicModeVersion(int goodsType, String sku, String versionSoft, String versionHard) {
        /*新goodsType的音乐模式，非多包参数-2级效果不再区分，统一协议bytes长度*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET
        ) {
            return ConsV1.multi_new_music_version_v2;
        }
        if (supportMultiNewMusic4Telink(sku, versionSoft, versionHard)) {
            return ConsV1.multi_new_music_version_v0;
        }
        if (supportMultiNewMusic4TelinkV1(sku, versionSoft, versionHard)) {
            return ConsV1.multi_new_music_version_v0;
        }
        if (supportMultiNewMusic4BK(versionSoft, versionHard)) {
            return ConsV1.multi_new_music_version_v1;
        }
        if (supportMultiMusicMode4Frk(versionSoft, versionHard)) {
            return ConsV1.multi_new_music_version_v1;
        }
        /*默认富芮坤的新定义的产品类型支持新音乐模式*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_DESK_LIGHT ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_H616C
        ) {
            return ConsV1.multi_new_music_version_v1;
        }
        return ConsV1.multi_new_music_version_noSupport;
    }

    public static boolean supportMultiMusic(int goodsType, String sku, String versionSoft, String versionHard) {
        int version = getNewMusicModeVersion(goodsType, sku, versionSoft, versionHard);
        return version != ConsV1.multi_new_music_version_noSupport;
    }

    /**
     * 获取支持的场景操作op集合
     *
     * @param goodsType
     * @param pactCode
     * @param softVersion
     * @param versionHard
     * @return
     */
    public static int[] getSupportScenesOpSet(int goodsType, int pactCode, String softVersion, String versionHard) {
        int newScenesVersion = newScenesVersion(goodsType, softVersion, pactCode);
        if (newScenesVersion == 0) return null;/*不支持抽象场景*/
        /*支持抽象场景-rgb/rgbic/是否支持rgbicV1？*/
        boolean supportRgbicV1 = supportSubModeColor4PartBrightness(softVersion, versionHard);
        return supportRgbicV1 ? new int[]{
                ScenesOp.scene_type_op_static,
                ScenesOp.scene_type_op_rgb,
                ScenesOp.scene_type_op_rgbic_v0,
                ScenesOp.scene_type_op_rgbic_v1,
        } : new int[]{
                ScenesOp.scene_type_op_static,
                ScenesOp.scene_type_op_rgb,
                ScenesOp.scene_type_op_rgbic_v0,
        };
    }

    /**
     * 是否是真色温设备-颜色模式下色温控制只能整条灯带设置
     */
    public static boolean isColorTemRealDevice(int goodType) {
        return GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3 == goodType;
    }

    public static boolean supportColorStripMulti(int goodsType, int pactType, int pactCode) {
        boolean support = goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA;
        if (support) return true;
        /*旧sku升级固件支持*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
            return pactType >= GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET_V3
                    && pactCode >= GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET_FRK;
        }
        return false;
    }

}
