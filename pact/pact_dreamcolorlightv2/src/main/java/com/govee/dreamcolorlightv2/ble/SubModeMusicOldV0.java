package com.govee.dreamcolorlightv2.ble;

import androidx.annotation.NonNull;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.light.ModeStr;

/**
 * Create by lins<PERSON><PERSON> on 2019-09-10
 * music模式
 */
public class SubModeMusicOldV0 extends AbsSubMode4Analytic {
    private static final int min_sensitivity = 0;
    private static final int max_sensitivity = 99;

    private int effect = BleUtil.getUnsignedByte(BleProtocol.value_sub_mode_music_energy_old_v0);/*效果-默认能量*/
    private int sensitivity = max_sensitivity;/*灵敏度*/
    private boolean isDynamic = true;
    private boolean autoColor = false;/*是否自动颜色*/
    private int rgb = 0xFFFF0000;/*颜色-默认红色*/

    @Override
    public void loadLocal() {
    }

    @Override
    public void saveLocal() {
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_music_old_v0;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr;
        if (effect == BleProtocol.value_sub_mode_music_rhythm_old_v0) {
            subModeStr = ModeStr.value_sub_mode_music_rhythm_str;
        } else if (effect == BleProtocol.value_sub_mode_music_energy_old_v0) {
            subModeStr = ModeStr.value_sub_mode_music_energy_str;
        } else if (effect == BleProtocol.value_sub_mode_music_scroll_old_v0) {
            subModeStr = ModeStr.value_sub_mode_music_rolling_str;
        } else {
            subModeStr = ModeStr.value_sub_mode_music_spectrum_str;
        }
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_music, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        effect = BleUtil.getUnsignedByte(validBytes[0]);
        int sensitivity = BleUtil.getUnsignedByte(validBytes[1]);
        this.sensitivity = Math.max(min_sensitivity, Math.min(sensitivity, max_sensitivity));
        if (effect == BleProtocol.value_sub_mode_music_rhythm_old_v0) {
            isDynamic = BleUtil.getUnsignedByte(validBytes[2]) == 0;
            autoColor = BleUtil.getUnsignedByte(validBytes[3]) == 0;
            if (!autoColor) {
                int r = BleUtil.getUnsignedByte(validBytes[4]);
                int g = BleUtil.getUnsignedByte(validBytes[5]);
                int b = BleUtil.getUnsignedByte(validBytes[6]);
                rgb = ColorUtils.toColor(r, g, b);
            }
        } else if (effect == BleProtocol.value_sub_mode_music_scroll_old_v0 || effect == BleProtocol.value_sub_mode_music_spectrum_old_v0) {
            autoColor = BleUtil.getUnsignedByte(validBytes[2]) == 0;
            if (!autoColor) {
                int r = BleUtil.getUnsignedByte(validBytes[3]);
                int g = BleUtil.getUnsignedByte(validBytes[4]);
                int b = BleUtil.getUnsignedByte(validBytes[5]);
                rgb = ColorUtils.toColor(r, g, b);
            }
        }
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] bytes;
        if (effect == BleProtocol.value_sub_mode_music_energy_old_v0) {
            /*能量效果*/
            bytes = new byte[3];
            bytes[0] = subModeCommandType();
            bytes[1] = (byte) effect;
            bytes[2] = (byte) sensitivity;
        } else if (effect == BleProtocol.value_sub_mode_music_rhythm_old_v0) {
            bytes = new byte[8];
            bytes[0] = subModeCommandType();
            bytes[1] = (byte) effect;
            bytes[2] = (byte) sensitivity;
            bytes[3] = (byte) (isDynamic ? 0 : 1);
            bytes[4] = (byte) (autoColor ? 0 : 1);
            if (!autoColor) {
                int[] rgb = ColorUtils.getRgb(this.rgb);
                bytes[5] = (byte) rgb[0];
                bytes[6] = (byte) rgb[1];
                bytes[7] = (byte) rgb[2];
            }
        } else {
            bytes = new byte[7];
            bytes[0] = subModeCommandType();
            bytes[1] = (byte) effect;
            bytes[2] = (byte) sensitivity;
            bytes[3] = (byte) (autoColor ? 0 : 1);
            if (!autoColor) {
                int[] rgb = ColorUtils.getRgb(this.rgb);
                bytes[4] = (byte) rgb[0];
                bytes[5] = (byte) rgb[1];
                bytes[6] = (byte) rgb[2];
            }
        }
        return bytes;
    }

    public void setEffect(int effect) {
        this.effect = effect;
    }

    public void setSensitivity(int sensitivity) {
        this.sensitivity = Math.max(min_sensitivity, Math.min(sensitivity, max_sensitivity));
    }

    public void setAutoColor(boolean autoColor) {
        this.autoColor = autoColor;
    }

    public void setRgb(int rgb) {
        this.rgb = rgb;
    }

    public void setDynamic(boolean dynamic) {
        isDynamic = dynamic;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_music;
    }
}
