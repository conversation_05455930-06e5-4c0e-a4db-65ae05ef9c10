package com.govee.dreamcolorlightv2.scenes;

import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.rhythm.AbsRhythmEffectUi;
import com.govee.base2light.rhythm.ui.AbsEffectUI;
import com.govee.dreamcolorlightv2.pact.Support;

/**
 * Create by l<PERSON><PERSON><PERSON> on 11/27/20
 * 昼夜节律
 */
public class RhythmBuilder implements AbsRhythmEffectUi {
    @Override
    public AbsEffectUI getEffectUI() {
        return new RhythmEffectUI();
    }

    @Override
    public boolean supportFuc(DeviceModel deviceModel) {
        return Support.getSupportRhythmGoodsType().contains(deviceModel.getGoodsType());
    }
}