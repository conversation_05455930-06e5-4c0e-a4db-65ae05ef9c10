package com.govee.dreamcolorlightv2.add;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.govee.base2home.config.Base2homeConfig;
import com.govee.base2home.constant.PathBaseHome;
import com.govee.base2home.main.tab.EventTabDefault;
import com.govee.base2home.sku.SkuIcM;
import com.govee.base2home.util.SchemeUtils;
import com.govee.base2light.ac.AbsBleWifiChooseActivity;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.kt.comm.DefInfo;
import com.govee.ble.BleController;
import com.govee.dreamcolorlightv2.ConsV1;
import com.govee.dreamcolorlightv2.ble.Ble;
import com.govee.dreamcolorlightv2.pact.Support;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.util.JumpUtil;

import androidx.annotation.NonNull;

/**
 * Create by xie<PERSON><PERSON> on 2020/3/6
 * wifi选择界面$
 */
public class WifiChooseAc extends AbsBleWifiChooseActivity {
    private static final String intent_ac_key_ble_disconnect_close_ac = "intent_ac_key_ble_disconnect_close_ac";
    private static final String intent_ac_key_support_back = "intent_ac_key_support_back";
    private static final String intent_ac_key_support_skip = "intent_ac_key_support_skip";
    private static final String intent_ac_key_wifi_mac = "intent_ac_key_wifi_mac";

    private boolean bleDisconnectCloseAc;
    private boolean supportBack;
    private int goodsType;

    private String wifiMac;

    /**
     * 跳转到wifi选择页面-添加流程
     *
     * @param ac
     * @param addInfo
     */
    public static void jump2wifiChooseAcByAdd(Activity ac, @NonNull AddInfo addInfo) {
        int[] wifiInputLimit = Support.getWifiInputLimit();
        Bundle jumpBundle = getJumpBundle(addInfo.sku, addInfo.device, addInfo.bleName, addInfo.bleAddress, addInfo.deviceName, "", wifiInputLimit[0], wifiInputLimit[1], addInfo.versionHard, addInfo.versionSoft);
        if (jumpBundle == null) return;
        jumpBundle.putBoolean(intent_ac_key_ble_disconnect_close_ac, true);
        jumpBundle.putBoolean(intent_ac_key_support_back, false);
        jumpBundle.putBoolean(intent_ac_key_support_skip, true);
        jumpBundle.putInt(ConsV1.intent_ac_adjust_goodsType, addInfo.goodsType);
        jumpBundle.putInt(intent_ac_pacttype, addInfo.pactType);
        jumpBundle.putInt(intent_ac_pactcode, addInfo.pactCode);
        jumpBundle.putString(intent_ac_key_wifi_mac, addInfo.wifiMac);
        jumpBundle.putString(com.govee.base2light.homepage.ConsV1.intent_ac_adjust_versionHard_4_wifi, addInfo.wifiHardVersion);
        jumpBundle.putString(com.govee.base2light.homepage.ConsV1.intent_ac_adjust_versionSoft_4_wifi, addInfo.wifiSoftVersion);
        JumpUtil.jumpWithBundle(ac, WifiChooseAc.class, true, jumpBundle);
    }

    /**
     * 跳转到wifi选择界面-修改wifi
     *
     * @param goodsType
     * @param context
     * @param sku
     * @param device
     * @param bleName
     * @param bleAddress
     */
    public static void jump2wifiChooseAcByChangeWifi(Context context, int goodsType, String sku, String device, String deviceName, String bleName, String bleAddress, String versionHard) {
        int[] wifiInputLimit = Support.getWifiInputLimit();
        Bundle jumpBundle = getJumpBundle(sku, device, bleName, bleAddress, deviceName, "", wifiInputLimit[0], wifiInputLimit[1], versionHard, "");
        if (jumpBundle == null) return;
        jumpBundle.putBoolean(intent_ac_key_ble_disconnect_close_ac, false);
        jumpBundle.putBoolean(intent_ac_key_support_back, true);
        jumpBundle.putBoolean(intent_ac_key_support_skip, false);
        jumpBundle.putInt(ConsV1.intent_ac_adjust_goodsType, goodsType);
        JumpUtil.jump(context, WifiChooseAc.class, jumpBundle);
    }

    @Override
    protected void doCheckPermissionPre() {
        super.doCheckPermissionPre();
        Intent intent = getIntent();
        goodsType = intent.getIntExtra(ConsV1.intent_ac_adjust_goodsType, 0);
        bleDisconnectCloseAc = intent.getBooleanExtra(intent_ac_key_ble_disconnect_close_ac, false);
        supportBack = intent.getBooleanExtra(intent_ac_key_support_back, false);
        boolean supportSkip = intent.getBooleanExtra(intent_ac_key_support_skip, false);
        updateBackAndSkip(supportBack, supportSkip);
        wifiMac = intent.getStringExtra(intent_ac_key_wifi_mac);
    }

    private void toJumpAdjustAc() {
        if (BaseApplication.getBaseApplication().isInBackground()) return;/*若应用处于后台，则不进行界面跳转*/
        /*关闭蓝牙*/
        getBle().stopHeart();
        BleController.getInstance().toBtClose();
        /*通知Tab更改为Default*/
        EventTabDefault.sendEventTabDefault();

        Bundle bundle = new Bundle();
        DefInfo defInfo = new DefInfo();
        defInfo.setGoodsType(goodsType);
        defInfo.setSku(sku);
        defInfo.setSpec("");
        defInfo.setDevice(device);
        defInfo.setDeviceName(deviceName);
        defInfo.setBleAddress(bleAddress);
        defInfo.setBleName(bleName);
        defInfo.setVersionSoft(versionSoft);
        defInfo.setVersionHard(versionHard);
        defInfo.setIc(SkuIcM.getInstance().getDefIcNum(sku));
        defInfo.setWifiHardVersion(wifiHv);
        defInfo.setWifiSoftVersion(wifiSv);
        defInfo.setPactType(pactType);
        defInfo.setPactCode(pactCode);
        if (wifiMac != null) {
            defInfo.setWifiMac(wifiMac);
        }
        Class<?> mainAcClass = Base2homeConfig.getConfig().getMainAcClass();
        BaseApplication.getBaseApplication().finishOther(mainAcClass);
        bundle.putParcelable("intent_ac_key_info", defInfo);
        SchemeUtils.jump(this, PathBaseHome.URL_RGBIC_NEW_DETAIL, bundle);
    }

    @Override
    protected boolean bleDisconnectCloseAc() {
        return bleDisconnectCloseAc;
    }

    @Override
    protected void toCloseAc() {
        if (supportBack) {
            finish();
        } else {
            /*跳转到adjustAc*/
            toJumpAdjustAc();
        }
    }

    @Override
    protected int runModeVersion() {
        return Support.check2WifiDeviceRunModeVersion();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected boolean isCanBack() {
        return supportBack;
    }

    @Override
    protected void toSkip() {
        toCloseAc();
    }
}