package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsMusicNoIcUiMode;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.SubModeMusicV1;
import com.govee.ui.R;


/**
 * Create by xie<PERSON>wu on 2019-07-22
 * music ui mode
 */
public class MusicUiModeV1 extends AbsMusicNoIcUiMode {
    public MusicUiModeV1(String sku,String device) {
        super(sku);
        this.device = device;
    }

    @Override
    public AbsModeFragment getUiFragment() {
        MusicFragmentV1 fragment = new MusicFragmentV1();
        fragment.makeArguments(getSku(),device);
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_music_mini, R.mipmap.new_control_light_btb_mode_music_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeMusicV1 subModeMusic = new SubModeMusicV1();
        subModeMusic.loadLocal();
        return subModeMusic;
    }
}
