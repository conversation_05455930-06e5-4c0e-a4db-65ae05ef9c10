package com.govee.dreamcolorlightv2.adjust;

import com.govee.base2light.ac.diy.DiyM;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v1.EffectCodes;
import com.govee.base2light.ac.diy.v2.DiyOpM;
import com.govee.base2light.ac.diy.v2.DiyProtocolParser;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by lins<PERSON><PERSON> on 2019-09-09
 * diy效果信息
 * <p>淡入淡出</p>
 * <p>跳跃</p>
 * <p>闪烁</p>
 * <p>跑马灯</p>
 * <p>音乐</p>
 * <p>流水</p>
 * <p>渐变流水</p>
 * <p>混合</p>
 * <p>有二级分类</p>
 */

public class Diy {
    private Diy() {
    }

    private static DiySupportV1 supportV0;
    private static DiySupportV1 supportV1;
    private static DiySupportV1 supportV2;

    private static DiySupportV1 getDiySupport() {
        if (supportV0 != null) return supportV0;
        supportV0 = makeDiySupport();
        return supportV0;
    }

    public static DiySupportV1 getDiySupport(int newVersion) {
        if (newVersion == 1) return getDiySupportV1();
        if (newVersion == 2) return getDiySupportV2();
        return getDiySupport();
    }

    private static DiySupportV1 getDiySupportV2() {
        if (supportV2 != null) return supportV2;
        supportV2 = makeDiySupportV2();
        return supportV2;
    }

    private static DiySupportV1 makeDiySupportV2() {
        DiySupportV1 diySupportV1 = new DiySupportV1();
        DiySupportV1.Effect effectFade = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_fade,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );
        DiySupportV1.Effect effectJump = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jump,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                });
        DiySupportV1.Effect effectTwinkling = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_twinkling,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );
        DiySupportV1.Effect effectMarquee = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_marquee,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_whole,
                        DiyM.EffectSubCode.diy_sub_effect_code_gather,
                        DiyM.EffectSubCode.diy_sub_effect_code_separate,
                }
        );
        DiySupportV1.Effect effectMusic = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_music,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_rhythm,
                        DiyM.EffectSubCode.diy_sub_effect_code_spectrum,
                        DiyM.EffectSubCode.diy_sub_effect_code_scroll,
                }
        );
        DiySupportV1.Effect effectWater = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_water,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );
        DiySupportV1.Effect effectRainbow = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_rainbow,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );
        DiySupportV1.Effect effectChase = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_chase[0],
                DiyM.EffectCode.diy_effect_code_chase[1]
        );
        DiySupportV1.Effect effectMix = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_mix[0],
                DiyM.EffectCode.diy_effect_code_mix[1]
        );
        DiySupportV1.Effect effectColorful = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_colorful[0],
                DiyM.EffectCode.diy_effect_code_tempalte_colorful[1]
        );
        effectColorful.setParserVersion(DiyProtocolParser.diy_protocol_version_template_colorful);
        DiySupportV1.Effect effectColorfulSky = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_sky[0],
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_sky[1]
        );
        effectColorfulSky.setParserVersion(DiyProtocolParser.diy_protocol_version_template_colorful_sky);
        DiySupportV1.Effect effectColorfulMeteor = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_meteor[0],
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_meteor[1]
        );
        effectColorfulMeteor.setParserVersion(DiyProtocolParser.diy_protocol_version_template_colorful_meteor);
        DiySupportV1.Effect effectColorfulMeteorShower = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_meteor_shower[0],
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_meteor_shower[1]
        );
        effectColorfulMeteorShower.setParserVersion(DiyProtocolParser.diy_protocol_version_template_colorful_meteor_shower);
        DiySupportV1.Effect effectShine = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_shine[0],
                DiyM.EffectCode.diy_effect_code_tempalte_shine[1]
        );
        effectShine.setParserVersion(DiyProtocolParser.diy_protocol_version_template_shine);
        DiySupportV1.Effect effectBloom = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_bloom[0],
                DiyM.EffectCode.diy_effect_code_tempalte_bloom[1]
        );
        effectBloom.setParserVersion(DiyProtocolParser.diy_protocol_version_template_bloom);
        DiySupportV1.Effect effectStack = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_stack[0],
                DiyM.EffectCode.diy_effect_code_tempalte_stack[1]
        );
        effectStack.setParserVersion(DiyProtocolParser.diy_protocol_version_template_stack);


        DiySupportV1.Effect effectGraffiti = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_graffiti_4_rgbic,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle,
                        DiyM.EffectSubCode.diy_sub_effect_code_fade,
                        DiyM.EffectSubCode.diy_sub_effect_code_blinking,
                        DiyM.EffectSubCode.diy_sub_effect_code_breath
                }
        );
        effectGraffiti.setSpecialDiyGraffiti4Rgbic();

        /*效果*/
        ArrayList<DiySupportV1.Effect> effects = new ArrayList<>();
        effects.add(effectGraffiti);
        effects.add(effectFade);
        effects.add(effectJump);
        effects.add(effectTwinkling);
        effects.add(effectMarquee);
        effects.add(effectWater);
        effects.add(effectRainbow);
        effects.add(effectChase);
        effects.add(effectMusic);
        effects.add(effectMix);
        effects.add(effectColorful);
        effects.add(effectColorfulSky);
        effects.add(effectColorfulMeteor);
        effects.add(effectColorfulMeteorShower);
        effects.add(effectShine);
        effects.add(effectBloom);
        effects.add(effectStack);
        diySupportV1.effects = effects;
        /*混合支持效果*/
        ArrayList<DiySupportV1.Effect> mixEffects = new ArrayList<>();
        mixEffects.add(effectFade);
        mixEffects.add(effectJump);
        mixEffects.add(effectTwinkling);
        mixEffects.add(effectMarquee);
        mixEffects.add(effectWater);
        mixEffects.add(effectRainbow);
        diySupportV1.mixEffects = mixEffects;
        diySupportV1.mix4EffectsNum = 4;
        /*颜色数量支持*/
        ArrayList<DiySupportV1.Effect4Color> effect4Colors = new ArrayList<>();
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_music[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_water[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 3));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 8));
        diySupportV1.effect4Colors = effect4Colors;
        /*速度调节*/
        ArrayList<DiySupportV1.Effect4Speed> effect4Speeds = new ArrayList<>();
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_music[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_water[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_graffiti_4_rgbic[0], 100));
        diySupportV1.effect4Speeds = effect4Speeds;
        /*效果code码+支持rgb的抽象+支持rgbic v0的抽象+支持rgbic v1的抽象*/
        List<Integer> flagCodes = DiyM.getInstance.getFlagCodes(effects,
                DiyM.EffectCode.diy_effect_code_rgb_from_studio,
                DiyM.EffectCode.diy_effect_code_rgbic_v0_from_studio,
                DiyM.EffectCode.diy_effect_code_rgbic_v1_from_studio);
        diySupportV1.effectCodes = new EffectCodes(flagCodes, DiyM.getInstance.getFlagCodes(mixEffects));
        return diySupportV1;
    }

    private static DiySupportV1 getDiySupportV1() {
        if (supportV1 != null) return supportV1;
        supportV1 = makeDiySupportV1();
        return supportV1;
    }

    private static DiySupportV1 makeDiySupportV1() {
        DiySupportV1 diySupportV1 = new DiySupportV1();
        DiySupportV1.Effect effectFade = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_fade,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );
        DiySupportV1.Effect effectJump = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jump,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                });
        DiySupportV1.Effect effectTwinkling = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_twinkling,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );
        DiySupportV1.Effect effectMarquee = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_marquee,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_whole,
                        DiyM.EffectSubCode.diy_sub_effect_code_gather,
                        DiyM.EffectSubCode.diy_sub_effect_code_separate,
                }
        );
        DiySupportV1.Effect effectMusic = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_music,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_rhythm,
                        DiyM.EffectSubCode.diy_sub_effect_code_spectrum,
                        DiyM.EffectSubCode.diy_sub_effect_code_scroll,
                }
        );
        DiySupportV1.Effect effectWater = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_water,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );
        DiySupportV1.Effect effectRainbow = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_rainbow,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );
        DiySupportV1.Effect effectChase = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_chase[0],
                DiyM.EffectCode.diy_effect_code_chase[1]
        );
        DiySupportV1.Effect effectMix = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_mix[0],
                DiyM.EffectCode.diy_effect_code_mix[1]
        );
        DiySupportV1.Effect effectColorful = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_colorful[0],
                DiyM.EffectCode.diy_effect_code_tempalte_colorful[1]
        );
        effectColorful.setParserVersion(DiyProtocolParser.diy_protocol_version_template_colorful);
        DiySupportV1.Effect effectColorfulSky = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_sky[0],
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_sky[1]
        );
        effectColorfulSky.setParserVersion(DiyProtocolParser.diy_protocol_version_template_colorful_sky);
        DiySupportV1.Effect effectColorfulMeteor = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_meteor[0],
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_meteor[1]
        );
        effectColorfulMeteor.setParserVersion(DiyProtocolParser.diy_protocol_version_template_colorful_meteor);
        DiySupportV1.Effect effectColorfulMeteorShower = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_meteor_shower[0],
                DiyM.EffectCode.diy_effect_code_tempalte_colorful_meteor_shower[1]
        );
        effectColorfulMeteorShower.setParserVersion(DiyProtocolParser.diy_protocol_version_template_colorful_meteor_shower);
        DiySupportV1.Effect effectShine = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_shine[0],
                DiyM.EffectCode.diy_effect_code_tempalte_shine[1]
        );
        effectShine.setParserVersion(DiyProtocolParser.diy_protocol_version_template_shine);
        DiySupportV1.Effect effectBloom = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_bloom[0],
                DiyM.EffectCode.diy_effect_code_tempalte_bloom[1]
        );
        effectBloom.setParserVersion(DiyProtocolParser.diy_protocol_version_template_bloom);
        DiySupportV1.Effect effectStack = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_tempalte_stack[0],
                DiyM.EffectCode.diy_effect_code_tempalte_stack[1]
        );
        effectStack.setParserVersion(DiyProtocolParser.diy_protocol_version_template_stack);


        /*效果*/
        ArrayList<DiySupportV1.Effect> effects = new ArrayList<>();
        effects.add(effectFade);
        effects.add(effectJump);
        effects.add(effectTwinkling);
        effects.add(effectMarquee);
        effects.add(effectWater);
        effects.add(effectRainbow);
        effects.add(effectChase);
        effects.add(effectMusic);
        effects.add(effectMix);
        effects.add(effectColorful);
        effects.add(effectColorfulSky);
        effects.add(effectColorfulMeteor);
        effects.add(effectColorfulMeteorShower);
        effects.add(effectShine);
        effects.add(effectBloom);
        effects.add(effectStack);
        diySupportV1.effects = effects;
        /*混合支持效果*/
        ArrayList<DiySupportV1.Effect> mixEffects = new ArrayList<>();
        mixEffects.add(effectFade);
        mixEffects.add(effectJump);
        mixEffects.add(effectTwinkling);
        mixEffects.add(effectMarquee);
        mixEffects.add(effectWater);
        mixEffects.add(effectRainbow);
        diySupportV1.mixEffects = mixEffects;
        diySupportV1.mix4EffectsNum = 4;
        /*颜色数量支持*/
        ArrayList<DiySupportV1.Effect4Color> effect4Colors = new ArrayList<>();
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_music[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_water[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 3));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 8));
        diySupportV1.effect4Colors = effect4Colors;
        /*速度调节*/
        ArrayList<DiySupportV1.Effect4Speed> effect4Speeds = new ArrayList<>();
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_music[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_water[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 100));
        diySupportV1.effect4Speeds = effect4Speeds;
        /*效果code码+支持rgb的抽象+支持rgbic v0的抽象*/
        List<Integer> flagCodes = DiyM.getInstance.getFlagCodes(effects,
                DiyM.EffectCode.diy_effect_code_rgb_from_studio,
                DiyM.EffectCode.diy_effect_code_rgbic_v0_from_studio);
        diySupportV1.effectCodes = new EffectCodes(flagCodes, DiyM.getInstance.getFlagCodes(mixEffects));
        return diySupportV1;
    }

    private static DiySupportV1 makeDiySupport() {
        DiySupportV1 diySupportV1 = new DiySupportV1();
        DiySupportV1.Effect effectFade = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_fade,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );
        DiySupportV1.Effect effectJump = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_jump,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                });
        DiySupportV1.Effect effectTwinkling = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_twinkling,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_all,
                        DiyM.EffectSubCode.diy_sub_effect_code_part,
                        DiyM.EffectSubCode.diy_sub_effect_code_cycle
                }
        );

        DiySupportV1.Effect effectMarquee = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_marquee,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_whole,
                        DiyM.EffectSubCode.diy_sub_effect_code_gather,
                        DiyM.EffectSubCode.diy_sub_effect_code_separate,
                }
        );
        DiySupportV1.Effect effectWater = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_water,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );
        DiySupportV1.Effect effectRainbow = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_rainbow,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_clockwise,
                        DiyM.EffectSubCode.diy_sub_effect_code_counterclockwise
                }
        );
        DiySupportV1.Effect effectChase = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_chase[0],
                DiyM.EffectCode.diy_effect_code_chase[1]
        );
        DiySupportV1.Effect effectMusic = DiySupportV1.Effect.makeEffectWithSubSet(
                DiyM.EffectCode.diy_effect_code_music,
                new int[][]{
                        DiyM.EffectSubCode.diy_sub_effect_code_rhythm,
                        DiyM.EffectSubCode.diy_sub_effect_code_spectrum,
                        DiyM.EffectSubCode.diy_sub_effect_code_scroll,
                }
        );
        DiySupportV1.Effect effectMix = DiySupportV1.Effect.makeEffect(
                DiyM.EffectCode.diy_effect_code_mix[0],
                DiyM.EffectCode.diy_effect_code_mix[1]
        );
        /*效果*/
        ArrayList<DiySupportV1.Effect> effects = new ArrayList<>();
        effects.add(effectFade);
        effects.add(effectJump);
        effects.add(effectTwinkling);
        effects.add(effectMarquee);
        effects.add(effectWater);
        effects.add(effectRainbow);
        effects.add(effectChase);
        effects.add(effectMusic);
        effects.add(effectMix);
        diySupportV1.effects = effects;
        /*混合支持效果*/
        ArrayList<DiySupportV1.Effect> mixEffects = new ArrayList<>();
        mixEffects.add(effectFade);
        mixEffects.add(effectJump);
        mixEffects.add(effectTwinkling);
        mixEffects.add(effectMarquee);
        mixEffects.add(effectWater);
        mixEffects.add(effectRainbow);
        diySupportV1.mixEffects = mixEffects;
        diySupportV1.mix4EffectsNum = 4;
        /*颜色数量支持*/
        ArrayList<DiySupportV1.Effect4Color> effect4Colors = new ArrayList<>();
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_music[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_water[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 8));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 3));
        effect4Colors.add(DiySupportV1.Effect4Color.makeMaxColor4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 8));
        diySupportV1.effect4Colors = effect4Colors;
        /*速度调节*/
        ArrayList<DiySupportV1.Effect4Speed> effect4Speeds = new ArrayList<>();
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_fade[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_jump[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_twinkling[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_marquee[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_water[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_rainbow[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_chase[0], 100));
        effect4Speeds.add(DiySupportV1.Effect4Speed.makeMaxSpeed4Effect(DiyM.EffectCode.diy_effect_code_mix[0], 100));
        diySupportV1.effect4Speeds = effect4Speeds;
        /*效果code码*/
        diySupportV1.effectCodes = new EffectCodes(DiyM.getInstance.getFlagCodes(effects), DiyM.getInstance.getFlagCodes(mixEffects));
        return diySupportV1;
    }

    public static String getLastDiyApplyKey(int newVersion, int goodsType, int icNum, String sku, int diyCode) {
        DiySupportV1 diySupport = getDiySupport(newVersion);
        return DiyOpM.getInstance.getCurChooseDiyValueKey(sku, goodsType, icNum, diySupport, diyCode);
    }
}