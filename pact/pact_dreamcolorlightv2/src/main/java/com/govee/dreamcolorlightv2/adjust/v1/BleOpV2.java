package com.govee.dreamcolorlightv2.adjust.v1;

import android.text.TextUtils;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.diy.EventDiyApplyResult;
import com.govee.base2light.ac.timer.SleepSucEvent;
import com.govee.base2light.ac.timer.Timer;
import com.govee.base2light.ac.timer.TimerResultEvent;
import com.govee.base2light.ac.timer.WakeupSucEvent;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.EventBrightness;
import com.govee.base2light.ble.controller.EventDiyTemplate4NewScenes;
import com.govee.base2light.ble.controller.EventHardVersion;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultiNewDiyGraffiti;
import com.govee.base2light.ble.controller.EventMultiNewScenes;
import com.govee.base2light.ble.controller.EventMultipleDiy;
import com.govee.base2light.ble.controller.EventMultipleMusic;
import com.govee.base2light.ble.controller.EventNewTimeV1;
import com.govee.base2light.ble.controller.EventSleep;
import com.govee.base2light.ble.controller.EventSoftVersion;
import com.govee.base2light.ble.controller.EventSyncTime;
import com.govee.base2light.ble.controller.EventWakeUp;
import com.govee.base2light.ble.controller.EventWifiHardVersion;
import com.govee.base2light.ble.controller.EventWifiMac;
import com.govee.base2light.ble.controller.EventWifiSoftVersion;
import com.govee.base2light.ble.controller.HardVersionController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.NewTimerV1;
import com.govee.base2light.ble.controller.NewTimerV1Controller;
import com.govee.base2light.ble.controller.SleepController;
import com.govee.base2light.ble.controller.SoftVersionController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.controller.SyncTimeController;
import com.govee.base2light.ble.controller.SyncTimeInfo;
import com.govee.base2light.ble.controller.WakeUpController;
import com.govee.base2light.ble.controller.WifiHardVersionController;
import com.govee.base2light.ble.controller.WifiMacController;
import com.govee.base2light.ble.controller.WifiSoftVersionController;
import com.govee.base2light.ble.music.EventNewMusicOpResult;
import com.govee.base2light.ble.ota.v2.OtaFlagV2;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.ble.AbsBleOp;
import com.govee.dreamcolorlightv2.ble.Ble;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.BulbGroupColor;
import com.govee.dreamcolorlightv2.ble.BulbGroupColorV2;
import com.govee.dreamcolorlightv2.ble.BulbStringColorController;
import com.govee.dreamcolorlightv2.ble.BulbStringColorControllerV2;
import com.govee.dreamcolorlightv2.ble.EventBulbStringColor;
import com.govee.dreamcolorlightv2.ble.EventBulbStringColorV2;
import com.govee.dreamcolorlightv2.ble.EventGradual;
import com.govee.dreamcolorlightv2.ble.EventOtaPrepare;
import com.govee.dreamcolorlightv2.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.Mode4ColorStrip;
import com.govee.dreamcolorlightv2.ble.ModeController;
import com.govee.dreamcolorlightv2.ble.ParamsSubMode4MusicBk;
import com.govee.dreamcolorlightv2.ble.SubModeColor;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV2;
import com.govee.dreamcolorlightv2.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv2.ble.SubModeScenes;
import com.govee.dreamcolorlightv2.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2020-02-12
 * ble-op控制v1版本$
 */
class BleOpV2 extends AbsBleOp {
    private static final String TAG = "BleOpV2";

    private int diyCode = -1;
    private int diyTemplateCode = -1;
    private ExtV1 ext;

    public BleOpV2(BleIotInfo info, ExtV1 ext) {
        super(info);
        this.ext = ext;
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    protected void onOffChangeReadingInfo() {
        SyncTimeInfo info = SyncTimeInfo.getCurrentTime();
        AbsSingleController[] controllers = new AbsSingleController[]{
                new SoftVersionController(),
                new HardVersionController(),
                new SyncTimeController(info.hour, info.minute, info.second, info.week),
                new NewTimerV1Controller(0xFF),
                new WakeUpController(),
                new SleepController(),
                new BrightnessController(),
                new SwitchController(),
                new Gradual4BleWifiController(),
                new ModeController(),
        };
        getBle().startController(controllers);
    }

    @Nullable
    @Override
    protected AbsSingleController getReadModeController() {
        return new ModeController();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWifiSoftVersion(EventWifiSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWifiSoftVersion() softVersion = " + softVersion);
            }
            if (!TextUtils.isEmpty(softVersion)) {
                ext.wifiSoftVersion = softVersion;
            }
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWifiHardVersion(EventWifiHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWifiHardVersion() hardVersion = " + hardVersion);
            }
            if (!TextUtils.isEmpty(hardVersion)) {
                /*判断是否需要上报wifi版本信息*/
                boolean needReportWifiVersion = TextUtils.isEmpty(ext.wifiSoftVersion) || TextUtils.isEmpty(ext.wifiHardVersion);
                ext.wifiHardVersion = hardVersion;
                if (needReportWifiVersion) {
                    String curWifiSoftVersion = ext.wifiSoftVersion;
                    String curWifiHardVersion = ext.wifiHardVersion;
                    reportWifiVersion(curWifiSoftVersion, curWifiHardVersion);
                }
            }
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWifiMac(EventWifiMac event) {
        if (event.isResult()) {
            String mac = event.getMac();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWifiMac()");
            }
            if (!TextUtils.isEmpty(mac)) {
                /*判断是否需要上报wifiMac*/
                boolean needReportWifiMac = TextUtils.isEmpty(info.wifiMac);
                info.wifiMac = mac;
                if (needReportWifiMac) {
                    reportWifiMac(mac);
                }
            }
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSoftVersion(EventSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventSoftVersion() softVersion = " + softVersion);
            }
            info.versionSoft = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventHardVersion(EventHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventHardVersion() hardVersion = " + hardVersion);
            }
            info.versionHard = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSyncTime(EventSyncTime event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSyncTime()");
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBrightness(EventBrightness event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBrightness() write = " + write + " ; result = " + result);
        }
        if (result) {
            int brightness = event.getBrightness();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventBrightness() brightness = " + brightness);
            }
            ext.brightness = brightness;
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.effect_apply_all_times, ParamFixedValue.times);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.adjust_effect_apply_time, ParamFixedValue.times);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWakeUp(EventWakeUp event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventWakeUp()  write = " + write + " ; result = " + result);
        }
        if (result) {
            WakeUpInfo wakeUpInfo = new WakeUpInfo();
            wakeUpInfo.enable = event.getEnable();
            wakeUpInfo.endBri = event.getEndBri();
            wakeUpInfo.wakeHour = event.getWakeHour();
            wakeUpInfo.wakeMin = event.getWakeMin();
            wakeUpInfo.wakeTime = event.getWakeTime();
            wakeUpInfo.repeat = event.getRepeat();
            wakeUpInfo.check();
            ext.wakeUpInfo = wakeUpInfo;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWakeUp() ext.wakeUpInfo = " + ext.wakeUpInfo);
            }
            WakeupSucEvent.sendWakeUpSucEvent(write, wakeUpInfo);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSleep(EventSleep event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSleep()  write = " + write + " ; result = " + result);
        }
        if (result) {
            SleepInfo sleepInfo = new SleepInfo();
            sleepInfo.enable = event.getEnable();
            sleepInfo.startBri = event.getStartBri();
            sleepInfo.closeTime = event.getCloseTime();
            sleepInfo.curTime = event.getCurTime();
            sleepInfo.check();
            ext.sleepInfo = sleepInfo;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventSleep() ext.sleepInfo = " + ext.sleepInfo);
            }
            SleepSucEvent.sendSleepSucEvent(write, sleepInfo);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventNewTimerV1(EventNewTimeV1 event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNewTimerV1()  write = " + write + " ; result = " + result);
        }
        if (result) {
            int group = event.getGroup();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventNewTimerV1() group = " + group);
            }
            List<Timer> timers = event.getTimers();
            if (group == 0xFF && timers.size() >= 4) {
                ext.timer1 = timers.get(0);
                ext.timer2 = timers.get(1);
                ext.timer3 = timers.get(2);
                ext.timer4 = timers.get(3);
            } else if (timers.size() > 0) {
                Timer timer = timers.get(0);
                if (group == 0) {
                    ext.timer1 = timer;
                } else if (group == 1) {
                    ext.timer2 = timer;
                } else if (group == 2) {
                    ext.timer3 = timer;
                } else if (group == 3) {
                    ext.timer4 = timer;
                }
            }
            TimerResultEvent.sendTimerResultEvent(write, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
        } else if (write) {
            TimerResultEvent.sendTimerResultEventFail(true);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventGradual(EventGradual event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventGradual() result = " + result + " ; write = " + write);
        }
        if (result) {
            int value = event.getValue();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventGradual() value = " + value);
            }
            ext.gradual = value;
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.effect_apply_all_times, ParamFixedValue.times);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.adjust_effect_apply_time, ParamFixedValue.times);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    private final int[] lastRgbSet4ColorStrip = new int[15];

    private void toUpdateUI4ColorStrip(byte commandType) {
        ISubMode subMode = info.mode.subMode;
        SubModeColor subModeColor = new SubModeColor();
        SubModeColorV2 subModeColorV2 = new SubModeColorV2();
        if (subMode instanceof SubModeColor) {
            subModeColor = ((SubModeColor) subMode);
            for (int i = 0; i < subModeColor.ctlLight.length; i++) {
                if (subModeColor.ctlLight[i]) {
                    lastRgbSet4ColorStrip[i] = subModeColor.rgb;
                }
            }
        }
        if (subMode instanceof SubModeColorV2) {
            subModeColorV2 = ((SubModeColorV2) subMode);
            for (int i = 0; i < subModeColorV2.ctlLight.length; i++) {
                if (subModeColorV2.ctlLight[i]) {
                    lastRgbSet4ColorStrip[i] = subModeColorV2.rgb;
                }
            }
        }
        if (((Mode4ColorStrip) info.mode).isLastController) {
            if (subMode instanceof SubModeColor) {
                subModeColor.rgbSet = lastRgbSet4ColorStrip;
            }
            if (subMode instanceof SubModeColorV2) {
                subModeColorV2.rgbSet = lastRgbSet4ColorStrip;
            }
            opResult.bleWrite(commandType, true);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.effect_apply_all_times, ParamFixedValue.times);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.adjust_effect_apply_time, ParamFixedValue.times);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() write = " + write + " ; result = " + result);
        }
        /*标记是否是由其他模式切换到颜色模式*/
        boolean otherSubMode2Color = false;
        if (result) {
            if (write) {
                otherSubMode2Color = otherMode2Color(info.mode, event.getMode());
            }
            info.mode = event.getMode();
        }
        /*写操作结果处理*/
        if (write) {
            /*检测是否设置DIY模式*/
            if (result) {
                /*musicModeResult*/
                musicModeResult();
                if (diyCode != -1 && info.mode.subMode.subModeCommandType() == BleProtocol.sub_mode_new_diy) {
                    EventDiyApplyResult.sendEventDiyApplyResult(true, diyCode);
                }
                /*检测是否是diy模版效果*/
                if (diyCode != -1 && diyTemplateCode != -1) {
                    ISubMode subMode = info.mode.subMode;
                    if (subMode instanceof SubModeScenes) {
                        /*diy模版操作*/
                        if (((SubModeScenes) subMode).getEffect() == diyTemplateCode) {
                            /*通知diy操作成功*/
                            EventDiyApplyResult.sendEventDiyApplyResult(true, diyCode);
                            /*更新模式为diy模式*/
                            info.mode.subMode = new SubModeNewDiy(diyCode);
                        }
                    }
                }
            }
            if (result) {
                boolean bleWriteResult = true;
                /*若当前是由其他模式切换到颜色模式，需要读取灯带分段颜色*/
                if (otherSubMode2Color) {
                    bleWriteResult = false;
                    readPartColor();
                }
                if (bleWriteResult) {
                    if (info.mode instanceof Mode4ColorStrip) {
                        toUpdateUI4ColorStrip(event.getCommandType());
                    } else {
                        opResult.bleWrite(event.getCommandType(), true);
                        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.effect_apply_all_times, ParamFixedValue.times);
                        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.adjust_effect_apply_time, ParamFixedValue.times);
                    }
                }
            } else {
                /*模式写失败，且当前是diy模式设置*/
                if (diyCode != -1) {
                    EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
                }
                opResult.bleWrite(event.getCommandType(), false);
            }
        } else {
            if (result) {
                /*检测音乐模式版本*/
                checkMusicMode();
                /*若当前读出来是颜色模式-则需要读取灯串的颜色-且当前有颜色模式的渐变信息*/
                ISubMode subMode = info.mode.subMode;
                boolean isReadSubModeColor = checkReadSubModeColor(subMode);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventMode() isReadSubModeColor = " + isReadSubModeColor);
                }
                if (!isReadSubModeColor) {
                    /*其他模式，信息读取完成*/
                    infoOver();
                }
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
        /*设置完mode，则重置diyCode*/
        diyCode = -1;
        diyTemplateCode = -1;
    }

    private void checkMusicMode() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof ParamsSubMode4MusicBk) {
            boolean supportMultiNewMusic4BK = Support.supportMultiNewMusic4BK(info.versionSoft, info.versionHard);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicMode() supportMultiNewMusic4BK = " + supportMultiNewMusic4BK);
            }
            mode.subMode = ((ParamsSubMode4MusicBk) subMode).toSupportSubMode(supportMultiNewMusic4BK ? 1 : 0);
        }
    }

    private void musicModeResult() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeMusicMultiV2) {
            EventNewMusicOpResult.sendEventNewMusicOpResult(true, ((SubModeMusicMultiV2) subMode).getMusicCode());
        }
    }

    protected void readPartColor() {
        boolean supportSubModeColor4PartBrightness = Support.supportSubModeColor4PartBrightness(info.versionSoft, info.versionHard);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "readPartColor() supportSubModeColor4PartBrightness = " + supportSubModeColor4PartBrightness);
        }
        if (supportSubModeColor4PartBrightness) {
            readBulbStringColorV2();
        } else {
            readBulbStringColor();
        }
    }

    /**
     * 是否读取的是颜色模式
     *
     * @param subMode
     * @return
     */
    private boolean checkReadSubModeColor(ISubMode subMode) {
        byte subModeCommandType = subMode.subModeCommandType();
        boolean subModeColor = subModeCommandType == BleProtocol.sub_mode_color || subModeCommandType == BleProtocol.sub_mode_color_v2;
        if (subModeColor) {
            /*若当前读取到是颜色模式，则需要读取分段颜色;读取的颜色模式中带渐变开关属性*/
            if (subMode instanceof SubModeColor) {
                ext.gradual = ((SubModeColor) subMode).gradual;
            } else if (subMode instanceof SubModeColorV2) {
                ext.gradual = ((SubModeColorV2) subMode).gradual;
            }
            /*读取分段颜色*/
            readPartColor();
        }
        return subModeColor;
    }

    /**
     * 当前操作是否由其他模式切换到颜色模式
     *
     * @param lastMode - 上次的模式
     * @param opMode   - 当前操作的模式
     * @return
     */
    private boolean otherMode2Color(@Nullable AbsMode lastMode, @NonNull AbsMode opMode) {
        byte subModeCommandType = opMode.subMode.subModeCommandType();
        boolean mode2Color = subModeCommandType == BleProtocol.sub_mode_color || subModeCommandType == BleProtocol.sub_mode_color_v2;
        if (mode2Color && lastMode != null) {
            ISubMode subMode = lastMode.subMode;
            if (subMode != null) {
                byte lastSubModeCommandType = subMode.subModeCommandType();
                boolean lastSubModeIsColorMode = lastSubModeCommandType == BleProtocol.sub_mode_color || lastSubModeCommandType == BleProtocol.sub_mode_color_v2;
                return !lastSubModeIsColorMode;
            }
        }
        return mode2Color;
    }

    private void readBulbStringColor() {
        /*每组4个颜色*/
        int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
        int maxGroup = bulbStringMaxNum / 4 + (bulbStringMaxNum % 4 == 0 ? 0 : 1);
        AbsSingleController[] controllers = new AbsSingleController[maxGroup];
        for (int i = 0; i < maxGroup; i++) {
            controllers[i] = new BulbStringColorController(i + 1);
        }
        getBle().addController(controllers);
    }

    private void readBulbStringColorV2() {
        /*每组3个颜色*/
        int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
        int maxGroup = bulbStringMaxNum / 3 + (bulbStringMaxNum % 3 == 0 ? 0 : 1);
        AbsSingleController[] controllers = new AbsSingleController[maxGroup];
        for (int i = 0; i < maxGroup; i++) {
            controllers[i] = new BulbStringColorControllerV2(i + 1);
        }
        getBle().addController(controllers);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleDiy(EventMultipleDiy event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiy diyCode = " + diyCode + " result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            this.diyCode = diyCode;
            /*diy效果传输完成;通知设备设置成diy模式*/
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(diyCode);
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            this.diyCode = -1;
            /*diy效果传输失败，则通知传输失败*/
            EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
            getBle().clearControllers();
        }
    }

    private int[] lastRgbSet;
    private int[] lastBrightnessSet;

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBulbStringColor(EventBulbStringColor event) {
        boolean result = event.isResult();
        if (result) {
            BulbGroupColor groupColor = event.groupColor;
            int group = groupColor.group;
            int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
            int maxGroup = bulbStringMaxNum / 4 + (bulbStringMaxNum % 4 == 0 ? 0 : 1);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventBulbStringColor() bulbStringMaxNum = " + bulbStringMaxNum + " ; maxGroup = " + maxGroup + " ； group = " + group);
            }
            if (group == 1) {
                lastRgbSet = new int[maxGroup * 4];
            }
            int[] rgb = groupColor.rgb;
            int destPos = Math.max(group - 1, 0) * 4;
            System.arraycopy(rgb, 0, lastRgbSet, destPos, rgb.length);
            if (group == maxGroup) {
                boolean supportNewColorMode = Support.supportNewColorMode(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventBulbStringColor() supportNewColorMode = " + supportNewColorMode + " ; lastRgbSet = " + Arrays.toString(lastRgbSet));
                }
                ISubMode subMode;
                if (supportNewColorMode) {
                    SubModeColorV2 subModeColorV2 = new SubModeColorV2();
                    subModeColorV2.rgbSet = lastRgbSet;
                    subMode = subModeColorV2;
                } else {
                    SubModeColor subModeColor = new SubModeColor();
                    subModeColor.rgbSet = lastRgbSet;
                    subMode = subModeColor;
                }
                /*读取球泡串颜色完成*/
                Mode mode = new Mode();
                mode.subMode = subMode;
                info.mode = mode;
                /*信息读取完成*/
                infoOver();
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBulbStringColorV2(EventBulbStringColorV2 event) {
        boolean result = event.isResult();
        if (result) {
            BulbGroupColorV2 groupColor = event.groupColor;
            int group = groupColor.group;
            int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
            int maxGroup = bulbStringMaxNum / 3 + (bulbStringMaxNum % 3 == 0 ? 0 : 1);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventBulbStringColor() bulbStringMaxNum = " + bulbStringMaxNum + " ; maxGroup = " + maxGroup + " ； group = " + group);
            }
            if (group == 1) {
                lastRgbSet = new int[maxGroup * 3];
                lastBrightnessSet = new int[maxGroup * 3];
            }
            int[] rgb = groupColor.rgb;
            int[] brightness = groupColor.relativeBrightness;
            int destPos = Math.max(group - 1, 0) * 3;
            System.arraycopy(rgb, 0, lastRgbSet, destPos, rgb.length);
            System.arraycopy(brightness, 0, lastBrightnessSet, destPos, brightness.length);
            if (group == maxGroup) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventBulbStringColor() lastRgbSet = " + Arrays.toString(lastRgbSet));
                }
                /*读取球泡串颜色完成*/
                SubModeColorV2 subModeColor = new SubModeColorV2();
                subModeColor.rgbSet = lastRgbSet;
                subModeColor.brightnessSet = lastBrightnessSet;
                Mode mode = new Mode();
                mode.subMode = subModeColor;
                info.mode = mode;
                /*信息读取完成*/
                infoOver();
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Override
    protected void readExtDeviceInfoAfterInfoOver() {
        AbsSingleController[] singleControllers = new AbsSingleController[]{
                new WifiSoftVersionController(),
                new WifiHardVersionController(),
                new WifiMacController(),
        };
        getBle().addController(singleControllers);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleNewScenes(EventMultiNewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleNewScenes() result = " + result + " ; scenesCode = " + scenesCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*场景效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*diy效果传输失败，则通知设置新场景模式失败*/
            getBle().clearControllers();
            opResult.bleWrite(event.getCommandType(), false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyTemplate4NewScenes(EventDiyTemplate4NewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        int diyCode = event.diyCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyTemplate4NewScenes() result = " + result + " ; scenesCode = " + scenesCode + " ; diyCode = " + diyCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            this.diyCode = diyCode;
            this.diyTemplateCode = scenesCode;
            /*diy模版效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            this.diyCode = -1;
            this.diyTemplateCode = -1;
            /*diy效果传输失败，则通知传输失败*/
            EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
            getBle().clearControllers();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventOtaPrepare(EventOtaPrepare event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventOtaPrepare() result = " + result);
        }
        getBle().controllerEvent(event);
        OtaFlagV2.getInstance.onOtaPrepare(result);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultiNewDiyGraffiti(EventMultiNewDiyGraffiti event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultiNewDiyGraffiti() diyCode = " + diyCode + " ; result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            this.diyCode = diyCode;
            /*diy效果传输完成;通知设备设置成diy模式*/
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(diyCode);
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            this.diyCode = -1;
            /*diy效果传输失败，则通知传输失败*/
            EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
            getBle().clearControllers();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleMusic(EventMultipleMusic event) {
        boolean result = event.isResult();
        int sensitivity = event.getSensitivity();
        int subMusicCode = event.getSubMusicCode();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleMusic() result = " + result + " ; sensitivity = " + sensitivity + " ; subMusicCode = " + subMusicCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*新音乐模式效果传输完成-通知设备切换到音乐模式*/
            Mode mode = new Mode();
            mode.subMode = SubModeMusicMultiV2.toNewSubModeMusic(sensitivity, subMusicCode);
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*新音乐效果传输失败*/
            EventNewMusicOpResult.sendEventNewMusicOpResult(false, subMusicCode);
            getBle().clearControllers();
        }
    }
}