package com.govee.dreamcolorlightv2.pact.bleiot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.pact.Command4PtReal;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2home.util.NumberUtil;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.DiyShare;
import com.govee.base2light.ac.diy.IColorOp;
import com.govee.base2light.ac.diy.IDiyOp;
import com.govee.base2light.ac.diy.IRoomOp4Ble;
import com.govee.base2light.ac.diy.IRoomOp4Iot;
import com.govee.base2light.ac.diy.ISceneOp;
import com.govee.base2light.ac.diy.ISmartRoomOp;
import com.govee.base2light.ac.diy.ISwitchAndBrightnessOp;
import com.govee.base2light.ac.diy.local.ShareDiy;
import com.govee.base2light.ac.diy.v1.EffectCodes;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.HeartController;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.util.NumUtil;
import com.govee.dreamcolorlightv2.adjust.Diy;
import com.govee.dreamcolorlightv2.ble.Ble;
import com.govee.dreamcolorlightv2.ble.BleComm;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.ModeController;
import com.govee.dreamcolorlightv2.ble.SubModeColor;
import com.govee.dreamcolorlightv2.ble.SubModeColorV2;
import com.govee.dreamcolorlightv2.iot.Cmd;
import com.govee.dreamcolorlightv2.iot.CmdBrightness;
import com.govee.dreamcolorlightv2.iot.CmdPtReal;
import com.govee.dreamcolorlightv2.iot.CmdTurn;
import com.govee.dreamcolorlightv2.iot.OpColorCommDialog4BleIot;
import com.govee.dreamcolorlightv2.iot.OpColorCommDialog4BleIotV1;
import com.govee.dreamcolorlightv2.iot.OpColorCommDialog4SquareBleIot;
import com.govee.dreamcolorlightv2.iot.OpColorCommDialog4SquareBleIotV1;
import com.govee.dreamcolorlightv2.iot.OpDiyCommDialog4BleIot;
import com.govee.dreamcolorlightv2.iot.OpDiyCommDialog4BleIotV1;
import com.govee.dreamcolorlightv2.iot.OpDiyCommDialog4BleIotV2;
import com.govee.dreamcolorlightv2.iot.OpDiyCommDialog4SquareBleIot;
import com.govee.dreamcolorlightv2.iot.OpDiyCommDialog4SquareBleIotV1;
import com.govee.dreamcolorlightv2.iot.OpDiyCommDialog4SquareBleIotV2;
import com.govee.dreamcolorlightv2.iot.OpSceneCommDialog4BleIot;
import com.govee.dreamcolorlightv2.iot.OpSceneCommDialog4BleIotV1;
import com.govee.dreamcolorlightv2.iot.OpSceneCommDialog4SquareBleIot;
import com.govee.dreamcolorlightv2.iot.OpSceneCommDialog4SquareBleIotV1;
import com.govee.dreamcolorlightv2.pact.Comm;
import com.govee.dreamcolorlightv2.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JsonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/8/12
 * diy的op操作$
 */
abstract class AbsEffectOp4BleIot implements IDiyOp, IColorOp, ISceneOp, ISmartRoomOp, ISwitchAndBrightnessOp {
    private static final String TAG = "AbsEffectOp4BleIot";

    @Override
    public String getKey() {
        StringBuilder sb = new StringBuilder();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            sb.append(goodsType).append("_");
        }
        return sb.toString();
    }

    /**
     * 支持的goodsType
     *
     * @return
     */
    protected abstract int[] supportGoodsType();

    @Override
    public boolean support(@NonNull AbsDevice absDevice, int effectType) {
        int curGoodsType = absDevice.getGoodsType();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) return true;
        }
        return false;
    }

    @Override
    public boolean supportDiyEffect(@NonNull AbsDevice absDevice, int[] effectCodes) {
        int diyVersion = Support.getDiyVersion(absDevice.getGoodsType(), absDevice.getVersionSoft(), absDevice.getVersionHard(), absDevice.getPactCode(), absDevice.getSku());
        EffectCodes effectCodesCur = Diy.getDiySupport(diyVersion).effectCodes;
        if (effectCodesCur != null) {
            return effectCodesCur.supportDiyEffect(effectCodes);
        }
        return false;
    }

    @Override
    public boolean applyDiyEffect(Context context, @NonNull AbsDevice absDevice, @NonNull DiyShare diyShare, boolean needConnect) {
        return applyEffect(context, absDevice, diyShare.effectStr, diyShare.effectCodes, diyShare.type, needConnect);
    }

    private boolean applyEffect(Context context, @NonNull AbsDevice absDevice, String effectStr, int[] effectCodes, int type, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        /*普通DIY*/
        DiyProtocol diyProtocol = ShareDiy.parseDiyProtocol(effectStr);
        if (diyProtocol != null) {
            int pactType = ext.pactType;
            int pactCode = ext.pactCode;
            boolean bk = Support.isBKProtocol(absDevice.getSku(), absDevice.getGoodsType(), pactType, pactCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyEffect() bk = " + bk + " ; pactType = " + pactType + " ; pactCode = " + pactCode);
            }
            if (needConnect) {
                OpDiyCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyProtocol, bk, type);
            } else {
                OpDiyCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyProtocol, bk, type);
            }
            return true;
        }
        /*RGBIC 涂鸦效果DIY*/
        DiyGraffitiV2 diyGraffiti = ShareDiy.parseDiyGraffiti4Rgbic(effectStr);
        if (diyGraffiti != null) {
            boolean support = diyGraffiti.checkDiyValue4RgbicGraffiti(ext.ic);
            if (!support) return false;
            if (needConnect) {
                OpDiyCommDialog4BleIotV2.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyGraffiti, type);
            } else {
                OpDiyCommDialog4SquareBleIotV2.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyGraffiti, type);
            }
            return true;
        }
        /*RGBIC DIY 模版*/
        AbsMultipleControllerV14DiyTemplate shareDiyTemplate = ScenesOp.isShareDiyTemplate(effectCodes, effectStr);
        if (shareDiyTemplate != null) {
            if (needConnect) {
                OpDiyCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareDiyTemplate, type);
            } else {
                OpDiyCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareDiyTemplate, type);
            }
            return true;
        }
        /*来自Govee Studio*/
        AbsMultipleControllerV14DiyTemplate shareDiyStudio = ScenesOp.isShareDiyStudio(effectCodes, effectStr);
        if (shareDiyStudio != null) {
            if (needConnect) {
                OpDiyCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareDiyStudio, type);
            } else {
                OpDiyCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareDiyStudio, type);
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean supportColorEffect(@NonNull AbsDevice absDevice, @NonNull EffectData.ColorEffect colorEffect) {
        int size = colorEffect.colorSet.length;
        return size == 1 || size == 15;
    }

    @Override
    public boolean applyColorEffect(Context context, @NonNull AbsDevice absDevice, @NonNull EffectData.ColorEffect colorEffect, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext != null) {
            boolean bkProtocol = Support.isBKProtocol(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode());
            boolean isSupportBrightness = Support.supportSubModeColor4PartBrightness(absDevice.getVersionSoft(), absDevice.getVersionHard());
            if (colorEffect.isSingleColor()) {
                if (needConnect) {
                    OpColorCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, bkProtocol, isSupportBrightness);
                } else {
                    OpColorCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, bkProtocol, isSupportBrightness);
                }
            } else {
                if (needConnect) {
                    OpColorCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, bkProtocol, isSupportBrightness);
                } else {
                    OpColorCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, bkProtocol, isSupportBrightness);
                }
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean supportSceneEffect(@NonNull AbsDevice absDevice, @NonNull EffectData.ShareEffect shareEffect) {
        int parseVersion = shareEffect.parseVersion;
        if (parseVersion == EffectData.ShareEffect.parser_version_diy) {
            return supportDiyEffect(absDevice, shareEffect.effectCodes);
        }
        if (!shareEffect.isValidEffect()) return false;
        int version = Support.getDiyVersion(absDevice.getGoodsType(), absDevice.getVersionSoft(), absDevice.getVersionHard(), absDevice.getPactCode(), absDevice.getSku());
        if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgb || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v0) {
            return version >= 1;
        } else if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v1) {
            return version >= 2;
        }
        return false;
    }

    @Override
    public boolean applySceneEffect(Context context, @NonNull AbsDevice absDevice, @NonNull EffectData.ShareEffect shareEffect, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        int parseVersion = shareEffect.parseVersion;
        if (parseVersion == EffectData.ShareEffect.parser_version_diy) {
            return applyEffect(context, absDevice, shareEffect.effectStr, shareEffect.effectCodes, DiyShare.type_recommend, needConnect);
        } else if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgb && ext != null) {
            if (needConnect) {
                OpSceneCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareEffect.effectStr);
            } else {
                OpSceneCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareEffect.effectStr);
            }
            return true;
        } else if ((parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v0
                || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v1)
                && ext != null) {
            if (needConnect) {
                OpSceneCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareEffect.effectStr);
            } else {
                OpSceneCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareEffect.effectStr);
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean applySwitch(Context context, @NonNull AbsDevice absDevice, boolean on) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        OpSceneCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), on);
        return true;
    }

    @Override
    public boolean applySwitch(Context context, @NonNull AbsDevice absDevice, boolean on, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpSceneCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), on);
        } else {
            OpSceneCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), on);
        }
        return true;
    }

    @Override
    public boolean applyBrightness(Context context, @NonNull AbsDevice absDevice, int brightness4Percent) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        OpSceneCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), absDevice, brightness4Percent);
        return true;
    }

    @Override
    public boolean applyBrightness(Context context, @NonNull AbsDevice absDevice, int brightness4Percent, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpSceneCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), absDevice, brightness4Percent);
        } else {
            OpSceneCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), absDevice, brightness4Percent);
        }
        return true;
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    public EffectCodes getSupportDiyEffectCode(@NonNull AbsDevice absDevice) {
        int diyVersion = Support.getDiyVersion(absDevice.getGoodsType(), absDevice.getVersionSoft(), absDevice.getVersionHard(), absDevice.getPactCode(), absDevice.getSku());
        return Diy.getDiySupport(diyVersion).effectCodes;
    }

    @Override
    public boolean isWifiLight() {
        return true;
    }

    @Override
    public String getWifiOnlineCmd() {
        return Cmd.online;
    }

    @Override
    public int supportRandomColorSize(AbsDevice absDevice) {
        return 15;
    }

    @Override
    public int[] supportScenesOpSet(AbsDevice absDevice) {
        return Support.getSupportScenesOpSet(absDevice.getGoodsType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
    }

    @Override
    public boolean applyPtControllers(Context context, @NonNull AbsDevice absDevice, int type, @NonNull Command4PtReal ptReal, boolean needConnect) {
        boolean invalid = ptReal.isInvalid();
        if (invalid) return false;
        boolean supportBleOp = ptReal.supportBleOp();
        boolean supportWifiOp = ptReal.supportWifiOp();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyPtControllers() supportBleOp = " + supportBleOp + " ; supportWifiOp = " + supportWifiOp);
        }
        if (!supportBleOp && !supportWifiOp) return false;
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        PtRealController ptRealController = PtRealController.makePtRealController(ptReal.opCommands, -1, -1);
        if (ptRealController == null) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "applyPtControllers() 透传指令解析失败");
            }
            return false;
        }
        List<String> commandList = ptReal.getCommands4IotPtReal();
        if (needConnect) {
            OpSceneCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), type, ptRealController, commandList, supportBleOp, supportWifiOp);
        } else {
            OpSceneCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), type, ptRealController, commandList, supportBleOp, supportWifiOp);
        }
        return true;
    }
    /*smartRoom*/

    @Override
    public boolean supportBle() {
        return true;
    }

    @Override
    public boolean supportWifi() {
        return true;
    }

    @Override
    public int supportColorSize(DeviceModel model) {
        return 15;
    }

    @Override
    public boolean supportDevice(DeviceModel deviceModel) {
        int curGoodsType = deviceModel.getGoodsType();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) return true;
        }
        return false;
    }

    @Override
    public IRoomOp4Ble getRoomOp() {
        return new IRoomOp4Ble() {
            @Override
            public UUID getServiceUUID() {
                return BleComm.serviceUuid;
            }

            @Override
            public UUID getCharacteristicUUID() {
                return BleComm.characteristicUuid;
            }

            @Override
            public byte[] heartBytes() {
                return new HeartController().getValue();
            }

            @Override
            public byte[] makeSwitchOpBytes(DeviceModel deviceModel, boolean on) {
                return new SwitchController(on).getValue();
            }

            @Override
            public byte[] makeBrightnessOpBytes(DeviceModel deviceModel, int brightnessPercent) {
                int brightness;
                int[] brightnessRange = Support.getBrightnessRange(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                /*非百分比，需要转化计算*/
                if (brightnessRange[0] == 1) {
                    brightness = NumUtil.calculateProgress(brightnessRange[2], brightnessRange[1], brightnessPercent);
                } else {
                    brightness = Math.max(brightnessPercent, brightnessRange[1]);
                    brightness = Math.min(brightness, brightnessRange[2]);
                }
                return new BrightnessController(brightness).getValue();
            }

            @Override
            public List<byte[]> makeSetColorOpBytes(DeviceModel deviceModel, int[] colors) {
                boolean bkProtocol = Support.isBKProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                boolean isSupportBrightness = Support.supportSubModeColor4PartBrightness(deviceModel.versionSoft, deviceModel.versionHard);
                AbsSingleController[] controllers;
                if (bkProtocol || isSupportBrightness) {
                    controllers = SubModeColorV2.makeSubModeColor(colors, null, false, false);
                } else {
                    controllers = SubModeColor.makeSubModeColor(colors, false, false);
                }
                if (controllers == null) return null;
                List<byte[]> bytes = new ArrayList<>();
                for (AbsSingleController controller : controllers) {
                    bytes.add(controller.getValue());
                }
                return bytes;
            }

            @Override
            public List<byte[]> makeSetColorTemOpBytes(DeviceModel deviceModel, int[] kelvin, int[] temColor) {
                return makeSetColorOpBytes(deviceModel, temColor);
            }

            @Override
            public byte[] makeSetColorOpBytes(DeviceModel deviceModel, int color) {
                return Comm.makeColorController4BleComm(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard, color).getValue();
            }

            @Override
            public byte[] makeSetColorTemOpBytes(DeviceModel deviceModel, int kelvin, int temColor) {
                return Comm.makeColorTemController4BleComm(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard, temColor).getValue();
            }

            @Override
            public int supportMic(DeviceModel deviceModel) {
                AbsMicFragmentV4.SupportMicStatus micStatus = Support.getMicStatus(deviceModel.getGoodsType(), deviceModel.versionSoft);
                if (AbsMicFragmentV4.SupportMicStatus.support_new_order.equals(micStatus))
                    return support_mic_new;
                if (AbsMicFragmentV4.SupportMicStatus.support_color_order.equals(micStatus))
                    return support_mic_rgb;
                return support_mic_no;
            }

            @Override
            public byte[] makeMicBytesByRgb(DeviceModel deviceModel, int rgb) {
                boolean bkProtocol = Support.isBKProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                boolean isSupportBrightness = Support.supportSubModeColor4PartBrightness(deviceModel.versionSoft, deviceModel.versionHard);
                Mode mode = new Mode();
                if (bkProtocol || isSupportBrightness) {
                    mode.subMode = SubModeColorV2.makeSubModeColor(rgb);
                } else {
                    mode.subMode = SubModeColor.makeSubModeColor(rgb);
                }
                return new ModeController(mode).getValue();
            }
        };
    }

    @Override
    public IRoomOp4Iot getRoomOp4Iot() {
        return new IRoomOp4Iot() {
            @Override
            public AbsCmd makeSwitchCmd(DeviceModel deviceModel, boolean on) {
                return new CmdTurn(on);
            }

            @Override
            public AbsCmd makeBrightnessCmd(DeviceModel deviceModel, int brightnessPercent) {
                int goodsType = deviceModel.getGoodsType();
                int pactType = deviceModel.pactType;
                int pactCode = deviceModel.pactCode;
                int[] brightnessRange = Support.getBrightnessRange(deviceModel.getSku(), goodsType, pactType, pactCode);
                AbsCmd absCmd;
                if (brightnessRange[0] == 0) {
                    absCmd = new CmdBrightness(brightnessPercent);
                } else {
                    int realMin = brightnessRange[1];
                    int realMax = brightnessRange[2];
                    absCmd = new CmdBrightness(NumberUtil.calculateProgress(realMax, realMin, brightnessPercent));
                }
                return absCmd;
            }

            @Override
            public List<AbsCmd> makeColorCmd(DeviceModel deviceModel, int[] colors) {
                boolean bkProtocol = Support.isBKProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                boolean isSupportBrightness = Support.supportSubModeColor4PartBrightness(deviceModel.versionSoft, deviceModel.versionHard);
                AbsSingleController[] controllers;
                if (bkProtocol || isSupportBrightness) {
                    controllers = SubModeColorV2.makeSubModeColor(colors, null, false, false);
                } else {
                    controllers = SubModeColor.makeSubModeColor(colors, false, false);
                }
                if (controllers == null) return null;
                AbsCmd absCmd = new CmdPtReal(controllers);
                List<AbsCmd> cmds = new ArrayList<>();
                cmds.add(absCmd);
                return cmds;
            }

            @Override
            public List<AbsCmd> makeColorTemCmd(DeviceModel deviceModel, int[] kelvin, int[] temColors) {
                return makeColorCmd(deviceModel, temColors);
            }

            @Override
            public String getOnlineCmd() {
                return Cmd.online;
            }

            @Override
            public int onlineCmdVersion(DeviceModel deviceModel) {
                return 0;
            }
        };
    }

    /*smartRoom*/
}