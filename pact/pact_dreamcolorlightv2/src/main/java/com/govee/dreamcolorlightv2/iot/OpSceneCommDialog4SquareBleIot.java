package com.govee.dreamcolorlightv2.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultiNewScenes;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.dreamcolorlightv2.ble.Ble;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.ModeController;
import com.govee.dreamcolorlightv2.ble.SubModeScenes;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Create by linshicong on 1/28/21
 * op scene操作应用$
 */
public class OpSceneCommDialog4SquareBleIot extends AbsOpCommDialog4BleIotV2 {
    private final CmdPtReal cmdPt;
    private final String effectStr;

    protected OpSceneCommDialog4SquareBleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, String effectStr) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_SCENES,-1);
        this.effectStr = effectStr;
        AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parseShareEffect(ScenesOp.scenes_code_share_rgb, effectStr);
        cmdPt = CmdPtReal.getNewScenesCmdPtReal(controllerV14Scenes);
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, String effectStr) {
        new OpSceneCommDialog4SquareBleIot(context, bleAddress, bleName, topic, sku, device, effectStr).show();
    }

    @Override
    protected @androidx.annotation.Nullable
    AbsCmd getOpCmd() {
        return cmdPt;
    }

    @Override
    protected void bleOping() {
        /*rgb场景*/
        AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parseShareEffect(ScenesOp.scenes_code_share_rgb, effectStr);
        getBle().sendMultipleControllerV1(controllerV14Scenes);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleNewScenes(EventMultiNewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleNewScenes() result = " + result + " ; scenesCode = " + scenesCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*场景效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*diy效果传输失败，则通知设置新场景模式失败*/
            getBle().clearControllers();
            updateBleResult(false);
            hide();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateBleResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}