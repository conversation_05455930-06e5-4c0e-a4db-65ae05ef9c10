package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.ui.mode.IUiMode;
import com.ihoment.base2app.infra.LogInfra;

import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by x<PERSON>ying<PERSON> on 2019-07-22
 * mode ui
 */
public class ModeUi4Factor extends AbsMode4UIV1 {
    private static final String TAG = "ModeUi4Factor";

    public ModeUi4Factor(AppCompatActivity ac, String sku, String device, int goodsType) {
        super(ac, sku, device, goodsType);
    }

    @Override
    protected IUiMode getMode4() {
        return new DiyUiMode(sku, goodsType);
    }

    @Override
    protected IUiMode getMode3() {
        return new ScenesUiModeV1(sku, device, false);
    }

    @Override
    protected IUiMode getMode2() {
        return ColorUiMode.makeColorUiMode4Factor(sku);
    }

    @Override
    protected IUiMode getMode1() {
        return new MusicUiMode(sku, device);
    }

    @Override
    protected String getTAG() {
        return TAG;
    }

    public void checkMusicMode(int musicVersion) {
        boolean mic = AbsMicFragmentV4.isBleGroupMicByPhoneMode();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkMusicMode() mic = " + mic + " ; musicVersion = " + musicVersion);
        }
        IUiMode positionUiMode = getPositionUiMode(0);
        boolean isMicV0 = positionUiMode instanceof MicUiMode;
        boolean isMusicV0 = positionUiMode instanceof MusicUiMode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkMusicMode() isMicV0 = " + isMicV0 + " ; isMusicV0 = " + isMusicV0);
        }
        /*v0版本音乐模式*/
        if (musicVersion == 0) {
            /*切换到mic*/
            if (mic && !isMicV0) {
                changePositionUiMode(0, new MicUiMode(sku, ""));
                return;
            }
            /*切换到音乐*/
            if (!mic && !isMusicV0) {
                changePositionUiMode(0, new MusicUiMode(sku, device));
                return;
            }
            return;
        }
        /*v1版本音乐模式*/
        boolean isMicV1 = positionUiMode instanceof MicUiModeMultiV1;
        boolean isMusicV1 = positionUiMode instanceof MusicUiModeMultiV1;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkMusicMode() isMicV1 = " + isMicV1 + " ; isMusicV1 = " + isMusicV1);
        }
        if (musicVersion == 1) {
            if (mic && !isMicV1) {
                changePositionUiMode(0, new MicUiModeMultiV1(sku, ""));
                return;
            }
            if (!mic && !isMusicV1) {
                changePositionUiMode(0, new MusicUiModeMultiV1(sku, device));
            }
        }
    }
}