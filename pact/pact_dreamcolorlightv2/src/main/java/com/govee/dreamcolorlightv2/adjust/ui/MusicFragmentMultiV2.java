package com.govee.dreamcolorlightv2.adjust.ui;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.IMusicEffectStatic;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.music.OldMusicEffect;
import com.govee.base2light.light.v1.AbsNewMusicFragment;
import com.govee.dreamcolorlightv2.ble.BleProtocol;
import com.govee.dreamcolorlightv2.ble.Mode;
import com.govee.dreamcolorlightv2.ble.SubModeMusicMultiV2;
import com.govee.dreamcolorlightv2.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2021/5/5
 * 新音乐模式$
 */
public class MusicFragmentMultiV2 extends AbsNewMusicFragment {
    private SubModeMusicMultiV2 subModeMusic = new SubModeMusicMultiV2();
    private List<SubMusicMode> subMusicModeList;

    @Override
    protected void init() {
        super.init();
        /*防止fragment视图还未加载就已传递了模式信息；因此在init完成时也需要刷新ui*/
        updateUi();
    }

    @Override
    protected int getCurSubMusicCode() {
        return subModeMusic.getMusicCode();
    }

    @Override
    protected void oldMusicParamsChange(OldMusicEffect oldMusicEffect) {
        SubModeMusicMultiV2 copy = subModeMusic.copy();
        copy.oldMusicEffectChange(oldMusicEffect);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
    }

    @Override
    protected int getSensitivity() {
        return subModeMusic.getSensitivity();
    }

    @Override
    protected void showOldSubMusicEditDialog(SubMusicMode musicMode) {
        int musicCode = musicMode.musicCode;
        if (musicCode == IMusicEffectStatic.single_value_sub_rhythm) {
            showOldMusicEditV1(musicMode, getSensitivity(), subModeMusic.isAutoColor(), subModeMusic.getRgb(), subModeMusic.isDynamic());
        } else {
            showOldMusicEditV0(musicMode, getSensitivity(), subModeMusic.isAutoColor(), subModeMusic.getRgb(), subModeMusic.isDynamic());
        }
    }

    @NonNull
    @Override
    protected List<SubMusicMode> getSupportMusicModes() {
        if (subMusicModeList == null) {
            subMusicModeList = makeRgbicSubMusicModes(
                    IMusicEffectStatic.single_value_sub_energy,
                    IMusicEffectStatic.single_value_sub_rhythm,
                    IMusicEffectStatic.single_value_sub_specturm,
                    IMusicEffectStatic.single_value_sub_scroll
            );
        }
        return subMusicModeList;
    }

    @Override
    protected void onSensitivityChange(int sensitivity) {
        SubModeMusicMultiV2 copy = subModeMusic.copy();
        copy.setSensitivity(sensitivity);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeMusicMultiV2) {
            boolean checkAnalytic4SubModeUse = ((SubModeMusicMultiV2) subMode).checkAnalytic4SubModeUse(getSku());
            this.subModeMusic = (SubModeMusicMultiV2) subMode;
            boolean newMusicCode = Support.isNewMusicCode(subModeMusic.getMusicCode());
            if (!newMusicCode) {
                /*旧音乐模式-需要存储到OldMusicEffect*/
                saveOldMusic(subModeMusic.getSensitivity(), subModeMusic.isAutoColor(), subModeMusic.isDynamic(), subModeMusic.getRgb());
            }
            updateUi();
            if (checkAnalytic4SubModeUse) {
                SafeLog.i(TAG, () -> "updateSubMode() analyticSubModeDetail");
                SubMusicMode subMusicMode = querySubMusicModeByMusicCode(getCurSubMusicCode());
                if (subMusicMode != null) {
                    analyticSubModeDetail(ParamFixedValue.mode_use_music_, subMusicMode.getAnalyticSubModeStr());
                }
            }
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

    private void updateUi() {
        if (!isViewInflateOk()) return;
        /*灵敏度*/
        sensitivityUi(subModeMusic.getSensitivity());
        /*选中的子音乐模式*/
        subMusicUi();
    }

    @Override
    protected int getIcNum() {
        if (subModeMusic != null) {
            int ic4Music = subModeMusic.ic4Music();
            LogInfra.Log.i(TAG, "getIcNum() ic4Music = " + ic4Music);
            if (ic4Music > 0) return ic4Music;
        }
        return super.getIcNum();
    }
}