package com.govee.dreamcolorlightv2.ble;

import com.govee.base2light.ble.controller.AbsParamsSubMode;
import com.govee.base2light.ble.controller.ISubMode;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2021/5/12
 * 音乐模式的参数支持subMode-bk$
 */
public class ParamsSubMode4MusicBk extends AbsParamsSubMode {

    @Override
    public ISubMode toSupportSubMode(int version) {
        if (version == 1) {
            SubModeMusicMultiV2 subModeMusicV2 = new SubModeMusicMultiV2();
            subModeMusicV2.parse(validBytes);
            return subModeMusicV2;
        }
        SubModeMusicV1 subModeMusic = new SubModeMusicV1();
        subModeMusic.parse(validBytes);
        return subModeMusic;
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_new_music;
    }
}