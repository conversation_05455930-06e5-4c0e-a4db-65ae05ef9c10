package com.govee.dreamcolorlightv2.adjust.v1;

import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.ui.mode.IUiMode;
import com.govee.dreamcolorlightv2.adjust.ui.ColorUiMode;
import com.govee.dreamcolorlightv2.adjust.ui.ColorUiModeV1;
import com.govee.dreamcolorlightv2.adjust.ui.ColorUiModeV2;
import com.govee.dreamcolorlightv2.adjust.ui.DiyUiMode;
import com.govee.dreamcolorlightv2.adjust.ui.MicUiMode;
import com.govee.dreamcolorlightv2.adjust.ui.MicUiModeMultiV1;
import com.govee.dreamcolorlightv2.adjust.ui.MusicUiMode;
import com.govee.dreamcolorlightv2.adjust.ui.MusicUiModeMultiV1;
import com.govee.dreamcolorlightv2.adjust.ui.ScenesUiMode;
import com.govee.dreamcolorlightv2.adjust.ui.ScenesUiModeV1;
import com.ihoment.base2app.infra.LogInfra;

import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2019-07-22
 * mode ui
 */
class ModeUiV1 extends AbsMode4UIV1 {
    private static final String TAG = "ModeUiV1";

    public ModeUiV1(AppCompatActivity ac, String sku, String device, int goodsType, int ic, DiySupportV1 diySupportV1) {
        super(ac, sku, device, goodsType, ic, diySupportV1);
    }

    public void checkMusicMode(int musicVersion) {
        boolean isMic = AbsMicFragmentV4.isMicModeByPhone(sku, device);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkMusicMode() musicVersion = " + musicVersion + " ; isMic = " + isMic);
        }
        IUiMode positionUiMode = getPositionUiMode(0);
        if (musicVersion == 0) {
            boolean isMicV0 = positionUiMode instanceof MicUiMode;
            boolean isMusicV0 = positionUiMode instanceof MusicUiMode;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicMode() isMicV0 = " + isMicV0 + " ; isMusicV0 = " + isMusicV0);
            }
            if (isMic && !isMicV0) {
                changePositionUiMode(0, new MicUiMode(sku, device));
                return;
            }
            if (!isMic && !isMusicV0) {
                changePositionUiMode(0, new MusicUiMode(sku, device));
                return;
            }
            return;
        }

        if (musicVersion == 1) {
            boolean isMicV1 = positionUiMode instanceof MicUiModeMultiV1;
            boolean isMusicV1 = positionUiMode instanceof MusicUiModeMultiV1;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicMode() isMicV1 = " + isMicV1 + " ; isMusicV1 = " + isMusicV1);
            }
            if (isMic && !isMicV1) {
                changePositionUiMode(0, new MicUiModeMultiV1(sku, device));
                return;
            }
            if (!isMic && !isMusicV1) {
                changePositionUiMode(0, new MusicUiModeMultiV1(sku, device));
            }
        }

    }

    /**
     * 更改场景模式兼容
     *
     * @param scenesModeVersion
     */
    public void setScenesModeVersion(int scenesModeVersion) {
        IUiMode positionUiMode = getPositionUiMode(2);
        if (scenesModeVersion == 0) {
            /*v0版本的场景-且当前场景模式是非v0版本，则需要切换*/
            boolean isV0 = positionUiMode instanceof ScenesUiMode;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "setScenesModeVersion() isV0 = " + isV0);
            }
            if (!isV0) {
                changePositionUiMode(2, new ScenesUiMode(sku, device));
            }
        } else if (scenesModeVersion == 1) {
            /*v1版本的场景-且当前场景模式是非v1版本，则需要切换*/
            boolean isV1 = positionUiMode instanceof ScenesUiModeV1;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "setScenesModeVersion() isV1 = " + isV1);
            }
            if (!isV1) {
                changePositionUiMode(2, new ScenesUiModeV1(sku, device));
            }
        }
    }

    /**
     * 更改颜色模式
     * <p>0:旧颜色模式-0x0b<p/>
     * <p>2:新颜色模式-0x15-支持分段控制亮度<p/>
     *
     * @param subModeColorVersion
     */
    public void changeColorMode(int subModeColorVersion, int ic, DiySupportV1 diySupportV1) {
        IUiMode positionUiMode = getPositionUiMode(1);
        int curColorUiModeVersion = getCurColorUiModeVersion(positionUiMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "changeColorMode() subModeColorVersion = " + subModeColorVersion + " ; curColorUiModeVersion = " + curColorUiModeVersion);
        }
        if (curColorUiModeVersion > -1 && curColorUiModeVersion != subModeColorVersion) {
            /*需要进行颜色模式ui界面转化*/
            if (subModeColorVersion == 0) {
                changePositionUiMode(1, new ColorUiMode(goodsType, sku, device, ic, diySupportV1));
                updateSupportHighColor(diySupportV1);
            } else if (subModeColorVersion == 2) {
                changePositionUiMode(1, new ColorUiModeV2(goodsType, sku, device, ic, diySupportV1));
                updateSupportHighColor(diySupportV1);
            }
        }
    }

    private int getCurColorUiModeVersion(IUiMode positionUiMode) {
        if (positionUiMode instanceof ColorUiMode) return 0;
        if (positionUiMode instanceof ColorUiModeV1) return 1;
        if (positionUiMode instanceof ColorUiModeV2) return 2;
        return -1;
    }

    @Override
    protected IUiMode getMode4() {
        return new DiyUiMode(sku, goodsType);
    }

    @Override
    protected IUiMode getMode3() {
        return new ScenesUiMode(sku,device);
    }

    @Override
    protected IUiMode getMode2() {
        return new ColorUiMode(goodsType, sku, device, ic, diySupportV1);
    }

    @Override
    protected IUiMode getMode1() {
        return new MusicUiMode(sku, device);
    }

    @Override
    protected String getTAG() {
        return "ModeUiV1";
    }

    @Override
    protected boolean showHighColorContainer() {
        if (diySupportV1 != null) {
            return isSupportHighColor(diySupportV1);
        }
        return false;
    }
}