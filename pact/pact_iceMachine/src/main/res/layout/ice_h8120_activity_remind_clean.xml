<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    tools:ignore="ResourceName,UselessLeaf,UseCompoundDrawables"
    >


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <ImageView
            android:id="@+id/v_top_bg"
            android:layout_width="match_parent"
            android:layout_height="203.5dp"
            android:background="@color/ui_bg_color_style_21"
            android:scaleType="centerCrop"
            android:src="@mipmap/new_bg_control_blue2"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/top_flag"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/iv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/h8120_clean_descaling"
            android:textColor="@color/font_style_1_textColor"
            android:textSize="@dimen/font_style_1_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/iv_back"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@+id/iv_back" />

        <ImageView
            android:id="@+id/iv_back"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="30.5dp"
            android:background="@drawable/component_bg_public_top_corner"
            android:contentDescription="@null"
            android:scaleType="centerInside"
            android:src="@mipmap/new_home_icon_top_corner_back"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/top_flag" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="10.5dp"
            android:background="@drawable/component_bg_color_style_1_lr"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@id/iv_back">

            <ScrollView
                app:layout_constraintTop_toTopOf="parent"
                android:layout_width="match_parent"
                android:layout_marginBottom="20dp"
                app:layout_constraintBottom_toTopOf="@id/btnReset"
                android:layout_height="0dp"
                >
            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clHeader"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/component_bg_style_4"
                    android:layout_marginTop="16dp"
                    android:layout_marginHorizontal="13dp"
                    android:paddingBottom="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    >

                    <TextView
                        android:id="@+id/tvSwitch"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/h7140_label_clean_switch"
                        android:textSize="@dimen/font_style_24_1_textSize"
                        android:textColor="@color/font_style_24_1_textColor"
                        android:layout_marginStart="17dp"
                        android:layout_marginTop="20dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <ImageView
                        android:contentDescription="@null"
                        android:id="@+id/ivSwitch"
                        android:layout_width="49dp"
                        android:layout_height="30dp"
                        android:src="@mipmap/new_public_btn_switch_on"
                        android:layout_marginEnd="10dp"
                        app:layout_constraintTop_toTopOf="@id/tvSwitch"
                        app:layout_constraintBottom_toBottomOf="@id/tvSwitch"
                        app:layout_constraintEnd_toEndOf="parent"
                        />

                    <TextView
                        android:id="@+id/tvRemindLabel"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/h8120_clean_switch_remind"
                        android:textSize="@dimen/font_style_58_2_textSize"
                        android:textColor="@color/font_style_58_2_textColor"
                        android:layout_marginTop="11dp"
                        android:layout_marginHorizontal="17dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/ivSwitch"
                        />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/clCenter"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:background="@drawable/component_bg_style_4"
                    android:layout_marginTop="18dp"
                    android:layout_marginHorizontal="13dp"
                    android:paddingBottom="16dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/clHeader"
                    >

                    <LinearLayout
                        android:id="@+id/llPeriodSet"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        android:layout_marginTop="14dp"
                        android:gravity="center_vertical"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        >

                        <TextView
                            android:id="@+id/tvSubTitle"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginStart="17dp"
                            android:maxWidth="120dp"
                            android:textColor="@color/font_style_24_1_textColor"
                            android:textSize="@dimen/font_style_24_1_textSize"
                            app:layout_constraintStart_toStartOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            tools:text="清洁清洁清洁清洁清洁清洁"
                            />

                        <View
                            android:layout_width="0dp"
                            android:layout_height="1dp"
                            android:layout_weight="99"
                            />


                        <TextView
                            android:paddingVertical="5.5dp"
                            android:paddingHorizontal="20dp"
                            android:background="@drawable/component_bg_btn_style_39"
                            android:textColor="@color/ui_btn_style_39_1_text_color"
                            android:textSize="@dimen/ui_btn_style_39_1_text_size"
                            android:id="@+id/tvPeriodSet"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:maxWidth="180dp"
                            app:layout_constraintTop_toTopOf="parent"
                            app:layout_constraintEnd_toEndOf="parent"
                            android:text="@string/h8120_remind_period_set"
                            />

                        <ImageView
                            android:id="@+id/ivPeriodSet"
                            android:layout_marginEnd="14dp"
                            android:layout_marginStart="1dp"
                            android:contentDescription="@null"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:src="@mipmap/new_light_icon_diy_auto_what"
                            />

                    </LinearLayout>


                    <com.govee.base2light.view.CountDownViewV2
                        android:id="@+id/countdownView"
                        android:layout_width="150dp"
                        android:layout_height="150dp"
                        android:layout_marginTop="35dp"
                        app:cv_ringColor="@color/ui_bg_color_style_1"
                        app:cv_ringLoadingColor="@color/ui_bg_color_style_9"
                        app:cv_ringWidth="5"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/llPeriodSet"
                        />

                    <TextView
                        android:id="@+id/tvRemain"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="36dp"
                        android:text="@string/skipv1_remaining"
                        android:textSize="@dimen/font_style_58_2_textSize"
                        android:textColor="@color/font_style_58_2_textColor"
                        app:layout_constraintStart_toStartOf="@id/countdownView"
                        app:layout_constraintEnd_toEndOf="@id/countdownView"
                        app:layout_constraintTop_toTopOf="@id/countdownView"
                        />

                    <TextView
                        android:id="@+id/tvRemainHour"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        tools:text="60h"
                        android:textSize="@dimen/font_style_149_7_textSize"
                        android:textColor="@color/font_style_149_7_textColor"
                        app:layout_constraintStart_toStartOf="@id/countdownView"
                        app:layout_constraintEnd_toEndOf="@id/countdownView"
                        app:layout_constraintTop_toBottomOf="@id/tvRemain"
                        />


                    <TextView
                        android:id="@+id/tvRemindDescLabel"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="17dp"
                        android:layout_marginTop="36dp"
                        android:text="@string/h8120_clean_descaling_remind"
                        android:textColor="@color/font_style_58_2_textColor"
                        android:textSize="@dimen/font_style_58_2_textSize"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintHorizontal_bias="0.529"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/countdownView"
                        />

                    <LinearLayout
                        android:id="@+id/llHowClear"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginTop="8dp"
                        android:layout_marginStart="17dp"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/tvRemindDescLabel"
                        >

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/h8120_label_how_clean_descaling"
                            android:textSize="@dimen/font_style_39_2_textSize"
                            android:textColor="@color/font_style_39_2_textColor"
                            android:layout_marginVertical="6dp"
                            />

                        <ImageView
                            android:contentDescription="@null"
                            android:layout_width="16dp"
                            android:layout_height="16dp"
                            android:src="@mipmap/new_icon_workshop_check_more"
                            android:layout_marginEnd="8dp"
                            android:layout_marginStart="5dp"
                            />
                    </LinearLayout>

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tvCloseTip"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16.5dp"
                    android:layout_marginHorizontal="22dp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/clHeader"
                    />

                <LinearLayout
                    android:id="@+id/llHowClearClose"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginTop="8dp"
                    android:visibility="gone"
                    app:layout_constraintStart_toStartOf="@id/tvCloseTip"
                    app:layout_constraintTop_toBottomOf="@id/tvCloseTip"
                    >

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/h8120_label_how_clean_descaling"
                        android:textSize="@dimen/font_style_39_2_textSize"
                        android:textColor="@color/font_style_39_2_textColor"
                        android:layout_marginVertical="6dp"
                        />

                    <ImageView
                        android:contentDescription="@null"
                        android:layout_width="16dp"
                        android:layout_height="16dp"
                        android:src="@mipmap/new_icon_workshop_check_more"
                        android:layout_marginEnd="8dp"
                        android:layout_marginStart="5dp"
                        />
                </LinearLayout>

            </androidx.constraintlayout.widget.ConstraintLayout>
            </ScrollView>

            <TextView
                android:id="@+id/btnReset"
                android:layout_width="0dp"
                android:layout_height="45dp"
                android:layout_marginHorizontal="22.5dp"
                android:layout_marginBottom="24dp"
                android:gravity="center"
                android:text="@string/h7140_reset_time"
                android:background="@drawable/component_btn_style_12"
                android:textColor="@color/ui_btn_style_12_1_text_color"
                android:textSize="@dimen/ui_btn_style_12_1_text_size"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
