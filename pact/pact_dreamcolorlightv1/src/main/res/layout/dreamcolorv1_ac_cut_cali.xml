<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:id="@+id/ac_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/ui_bg_color_style_1"
        >

        <TextView
            android:id="@+id/top_flag"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:maxHeight="0dp"
            app:layout_constraintTop_toTopOf="parent"
            />


        <ImageView
            android:id="@+id/ivBack"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="30.5dp"
            android:background="@drawable/component_btn_style_23"
            android:padding="5dp"
            android:src="@mipmap/new_sensor_setting_icon_arrow_left"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/top_flag"
            />

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:gravity="center"
            android:text="@string/hint_check_light"
            android:textColor="@color/font_style_105_textColor"
            android:textSize="@dimen/font_style_105_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/ivBack"
            app:layout_constraintTop_toTopOf="@+id/ivBack"
            />

        <View
            android:id="@+id/bg"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="10.5dp"
            android:background="@drawable/component_bg_style_2_lr"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/ivBack"
            />

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintTop_toTopOf="@+id/bg"
            >

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                >


                <TextView
                    android:id="@+id/tvPreHint1"
                    android:layout_width="329dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp"
                    android:gravity="center"
                    android:text="@string/str_cut_hint1"
                    android:textColor="@color/font_style_68_1_textColor"
                    android:textSize="@dimen/font_style_68_1_textSize"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    />

                <ImageView
                    android:id="@+id/ivHint"
                    android:layout_width="270dp"
                    android:layout_height="201dp"
                    android:layout_marginTop="28dp"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvPreHint1"
                    />


                <TextView
                    android:id="@+id/tvPreHint2"
                    android:layout_width="329dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="28dp"
                    android:gravity="center"
                    android:text="@string/str_cut_hint2"
                    android:textColor="@color/font_style_68_1_textColor"
                    android:textSize="@dimen/font_style_68_1_textSize"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/ivHint"
                    />

                <androidx.legacy.widget.Space
                    android:id="@+id/space"
                    android:layout_width="match_parent"
                    android:layout_height="228dp"
                    app:layout_constraintTop_toBottomOf="@+id/tvPreHint2"
                    />

                <TextView
                    android:id="@+id/tvPreHint3"
                    android:layout_width="329dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="28dp"
                    android:layout_marginBottom="20dp"
                    android:gravity="center"
                    android:text="@string/str_cut_hint3"
                    android:textColor="@color/font_style_14_1_textColor"
                    android:textSize="@dimen/font_style_14_1_textSize"
                    app:layout_constraintBottom_toTopOf="@+id/tvStart"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    />

                <TextView
                    android:id="@+id/tvStart"
                    android:layout_width="185dp"
                    android:layout_height="65dp"
                    android:layout_marginBottom="10dp"
                    android:background="@drawable/component_btn_style_3"
                    android:gravity="center_horizontal"
                    android:paddingTop="12.5dp"
                    android:text="@string/cubelight_start_calibration"
                    android:textColor="@color/ui_btn_style_3_1_text_color"
                    android:textSize="@dimen/ui_btn_style_3_1_text_size"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/groupStep1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="tvPreHint1,ivHint,tvPreHint2,space,tvPreHint3,tvStart"
                    />


                <com.govee.base2light.light.v1.ViewGraffiti
                    android:id="@+id/viewGraffiti"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="131dp"
                    app:layout_constraintTop_toTopOf="parent"
                    />

                <TextView
                    android:id="@+id/tvHint4"
                    android:layout_width="331dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="40dp"
                    android:text="@string/h61d3_cut_length_change"
                    android:textColor="@color/font_style_14_1_textColor"
                    android:textSize="@dimen/font_style_14_1_textSize"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/viewGraffiti"
                    />

                <ImageView
                    android:id="@+id/ivReduce"
                    android:layout_width="115dp"
                    android:layout_height="40dp"
                    android:layout_marginStart="74dp"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/component_btn_cut_cali_minus"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tvHint4"
                    />

                <ImageView
                    android:id="@+id/ivAdd"
                    android:layout_width="115dp"
                    android:layout_height="40dp"
                    android:background="@drawable/component_btn_cut_cali_add"
                    app:layout_constraintStart_toEndOf="@+id/ivReduce"
                    app:layout_constraintTop_toTopOf="@+id/ivReduce"
                    />


                <TextView
                    android:id="@+id/tvPreHint5"
                    android:layout_width="329dp"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="113.5dp"
                    android:text="@string/h61d3_str_cut_hint4"
                    android:textColor="@color/font_style_14_1_textColor"
                    android:textSize="@dimen/font_style_14_1_textSize"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    />

                <TextView
                    android:id="@+id/tvSure"
                    android:layout_width="203dp"
                    android:layout_height="65dp"
                    android:layout_marginEnd="20dp"
                    android:layout_marginBottom="10dp"
                    android:background="@drawable/component_btn_style_3"
                    android:gravity="center_horizontal"
                    android:paddingTop="12.5dp"
                    android:text="@string/confirm"
                    android:textColor="@color/ui_btn_style_3_1_text_color"
                    android:textSize="@dimen/ui_btn_style_3_1_text_size"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    />

                <androidx.constraintlayout.widget.Group
                    android:id="@+id/groupStep2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:constraint_referenced_ids="tvSure,tvPreHint5,tvHint4,ivReduce,ivAdd,viewGraffiti"
                    />


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.core.widget.NestedScrollView>


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>