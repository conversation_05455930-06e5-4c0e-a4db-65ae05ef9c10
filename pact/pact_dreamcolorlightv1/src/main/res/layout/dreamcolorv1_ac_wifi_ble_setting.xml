<?xml version="1.0" encoding="utf-8"?>
<com.zhy.android.percent.support.PercentRelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:background="@color/ui_bg_color_style_1"
    android:id="@+id/ac_container"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_height="match_parent"
    android:layout_width="match_parent"
    >

    <TextView
        android:id="@+id/top_flag"
        android:layout_height="0dp"
        android:layout_width="match_parent"
        />

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/btn_back"
        android:layout_alignBottom="@+id/btn_back"
        android:gravity="center"
        android:text="@string/h5072_ads_title"
        android:textColor="@color/font_style_105_textColor"
        android:textSize="@dimen/font_style_105_textSize"
        />

    <ImageView
        android:background="@drawable/component_btn_style_23"
        android:contentDescription="@null"
        android:id="@+id/btn_back"
        android:layout_below="@+id/top_flag"
        android:layout_height="0dp"
        android:layout_width="0dp"
        android:padding="5dp"
        android:src="@mipmap/new_sensor_setting_icon_arrow_left"
        app:layout_heightPercent="9.0667%w"
        app:layout_marginBottomPercent="@string/percent_10dp_w"
        app:layout_marginLeftPercent="3.2%w"
        app:layout_marginStartPercent="3.2%w"
        app:layout_marginTopPercent="8.1333%w"
        app:layout_widthPercent="9.0667%w"
        />

    <androidx.core.widget.NestedScrollView
        android:fillViewport="true"
        android:layout_below="@+id/btn_back"
        android:layout_height="match_parent"
        android:layout_width="match_parent"
        android:overScrollMode="never"
        android:scrollbars="none"
        >

        <com.zhy.android.percent.support.PercentRelativeLayout
            android:id="@+id/scroll_content_container"
            android:layout_height="match_parent"
            android:layout_width="match_parent"
            >

            <TextView
                android:gravity="center_vertical"
                android:id="@+id/device_info_label"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:lines="1"
                android:text="@string/device_info_label"
                android:textColor="@color/font_style_70_1_textColor"
                android:textSize="@dimen/font_style_70_1_textSize"
                app:layout_marginTopPercent="@string/percent_18dp_w"
                app:layout_minHeightPercent="5.3333%w"
                app:layout_paddingLeftPercent="6.1333%w"
                app:layout_paddingRightPercent="6.1333%w"
                />

            <com.zhy.android.percent.support.PercentRelativeLayout
                android:background="@drawable/component_bg_style_4"
                android:id="@+id/device_name_container"
                android:layout_below="@+id/device_info_label"
                android:layout_centerHorizontal="true"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                app:layout_marginTopPercent="2.6667%w"
                app:layout_widthPercent="93.0667%w"
                >

                <TextView
                    android:gravity="center_vertical"
                    android:id="@+id/device_name_label"
                    android:layout_below="@+id/device_info_label"
                    android:layout_height="wrap_content"
                    android:layout_width="match_parent"
                    android:lines="1"
                    android:text="@string/device_name_hint"
                    android:textColor="@color/font_style_70_2_textColor"
                    android:textSize="@dimen/font_style_70_2_textSize"
                    app:layout_marginTopPercent="4.0115%w"
                    app:layout_minHeightPercent="5.7307%w"
                    app:layout_paddingLeftPercent="4.2980%w"
                    app:layout_paddingRightPercent="4.2980%w"
                    />

                <com.govee.base2home.custom.ClearEditText
                    android:gravity="center_vertical"
                    android:id="@+id/device_name_edit"
                    android:layout_below="@+id/device_name_label"
                    android:layout_height="wrap_content"
                    android:layout_toStartOf="@+id/btn_device_name_cancel"
                    android:layout_width="match_parent"
                    android:textColor="@color/font_style_70_3_textColor"
                    android:textColorHint="@color/font_style_28_4_textColor"
                    android:textSize="@dimen/font_style_70_3_textSize"
                    app:cet_clear_drawable="@drawable/component_btn_close"
                    app:cet_clear_drawable_height="27dp"
                    app:cet_clear_drawable_width="27dp"
                    app:cet_input_limit="22"
                    app:layout_marginBottomPercent="4.0115%w"
                    app:layout_marginEndPercent="2.8653%w"
                    app:layout_marginLeftPercent="0.3333%w"
                    app:layout_marginRightPercent="2.8653%w"
                    app:layout_marginStartPercent="0.3333%w"
                    app:layout_marginTopPercent="1.4327%w"
                    app:layout_minHeightPercent="7.7364%w"
                    app:layout_paddingLeftPercent="4.2980%w"
                    />

                <TextView
                    android:gravity="center"
                    android:id="@+id/btn_device_name_done"
                    android:layout_alignParentEnd="true"
                    android:layout_height="0dp"
                    android:layout_width="wrap_content"
                    android:text="@string/done"
                    android:textColor="@color/component_text_color_70_7_8"
                    android:textSize="14sp"
                    app:layout_heightPercent="11.4613%w"
                    app:layout_marginEndPercent="2.8653%w"
                    app:layout_marginRightPercent="2.8653%w"
                    app:layout_marginTopPercent="9.3123%w"
                    app:layout_minWidthPercent="14.3266%w"
                    />

                <TextView
                    android:gravity="center"
                    android:id="@+id/btn_device_name_cancel"
                    android:layout_alignTop="@+id/btn_device_name_done"
                    android:layout_height="0dp"
                    android:layout_toStartOf="@+id/btn_device_name_done"
                    android:layout_width="wrap_content"
                    android:text="@string/cancel"
                    android:textColor="@color/component_text_color_70_7_8"
                    android:textSize="14sp"
                    app:layout_heightPercent="11.4613%w"
                    app:layout_marginEndPercent="2.8653%w"
                    app:layout_marginRightPercent="2.8653%w"
                    app:layout_minWidthPercent="14.3266%w"
                    />
            </com.zhy.android.percent.support.PercentRelativeLayout>

            <TextView
                android:gravity="center_vertical"
                android:id="@+id/other_info_label"
                android:layout_below="@+id/device_name_container"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:lines="1"
                android:text="@string/bbq_preset_others"
                android:textColor="@color/font_style_70_1_textColor"
                android:textSize="@dimen/font_style_70_1_textSize"
                app:layout_marginBottomPercent="2.6667%w"
                app:layout_marginTopPercent="4.8%w"
                app:layout_minHeightPercent="5.3333%w"
                app:layout_paddingLeftPercent="6.1333%w"
                app:layout_paddingRightPercent="6.1333%w"
                />

            <com.govee.ui.component.DeviceLockView
                android:id="@+id/device_lock"
                android:layout_below="@+id/other_info_label"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:visibility="gone"
                app:layout_marginBottomPercent="4.8%w"
                />

            <TextView
                android:background="@drawable/component_bg_style_4"
                android:id="@+id/bg_round2"
                android:layout_alignBottom="@+id/bg_round_2_bottom_flag"
                android:layout_below="@+id/device_lock"
                android:layout_centerHorizontal="true"
                android:layout_height="0dp"
                android:layout_width="0dp"
                app:layout_widthPercent="93.0667%w"
                />

            <com.zhy.android.percent.support.PercentLinearLayout
                android:gravity="center_vertical"
                android:id="@+id/ll_release_splicing"
                android:layout_below="@+id/device_lock"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:orientation="horizontal"
                app:layout_marginTopPercent="@string/percent_5dp_w"
                app:layout_paddingLeftPercent="5.3333%w"
                app:layout_paddingRightPercent="6.1333%w"
                android:visibility="gone"
                tools:visibility="visible"
                >

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                    android:src="@mipmap/h70b1_sensor_setting_icon_pinjie_jiechu"
                    app:layout_heightPercent="16%w"
                    app:layout_widthPercent="13.3333%w"
                    />

                <TextView
                    android:gravity="center_vertical"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:text="@string/unsplicing_device"
                    android:textColor="@color/font_style_70_4_textColor"
                    android:textSize="@dimen/font_style_70_4_textSize"
                    app:layout_minHeightPercent="8%w"
                    app:layout_paddingLeftPercent="1.8667%w"
                    />

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                    android:src="@mipmap/new_update_list_arrow"
                    app:layout_heightPercent="8%w"
                    app:layout_widthPercent="8%w"
                    />

            </com.zhy.android.percent.support.PercentLinearLayout>

            <View
                android:id="@+id/v_line_release_splicing"
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="@color/ui_split_line_style_1_1"
                android:layout_marginStart="13dp"
                android:layout_marginEnd="13dp"
                android:layout_below="@id/ll_release_splicing"
                android:visibility="gone"
                tools:visibility="visible"
                />

            <com.zhy.android.percent.support.PercentLinearLayout
                android:gravity="center_vertical"
                android:id="@+id/directionSettingLayout"
                android:layout_below="@+id/v_line_release_splicing"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:orientation="horizontal"
                app:layout_marginTopPercent="@string/percent_5dp_w"
                app:layout_paddingLeftPercent="5.3333%w"
                app:layout_paddingRightPercent="6.1333%w"
                android:visibility="gone"
                tools:visibility="visible"
                >

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                    android:src="@mipmap/new_sensor_setting_icon_qiehuan"
                    app:layout_heightPercent="16%w"
                    app:layout_widthPercent="13.3333%w"
                    />

                <TextView
                    android:gravity="center_vertical"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:text="@string/change_installation_direction"
                    android:textColor="@color/font_style_70_4_textColor"
                    android:textSize="@dimen/font_style_70_4_textSize"
                    app:layout_minHeightPercent="8%w"
                    app:layout_paddingLeftPercent="1.8667%w"
                    />

                <TextView
                    android:id="@+id/tv_direction"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/font_style_131_2_textColor"
                    android:textSize="@dimen/font_style_131_2_textSize"
                    />

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                    android:src="@mipmap/new_diy_btn_qiehuan"
                    app:layout_heightPercent="8%w"
                    app:layout_widthPercent="8%w"
                    />

            </com.zhy.android.percent.support.PercentLinearLayout>

            <View
                android:id="@+id/directionSettingDivider"
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="@color/ui_split_line_style_1_1"
                android:layout_marginStart="13dp"
                android:layout_marginEnd="13dp"
                android:layout_below="@id/directionSettingLayout"
                android:visibility="gone"
                />

            <com.zhy.android.percent.support.PercentLinearLayout
                android:gravity="center_vertical"
                android:id="@+id/pll_matter_container"
                android:layout_below="@+id/directionSettingDivider"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:orientation="horizontal"
                app:layout_marginTopPercent="@string/percent_5dp_w"
                app:layout_paddingLeftPercent="5.3333%w"
                app:layout_paddingRightPercent="6.1333%w"
                android:visibility="gone"
                >

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                    android:src="@mipmap/new_sensor_setting_icon_matter"
                    app:layout_heightPercent="16%w"
                    app:layout_widthPercent="13.3333%w"
                    />

                <TextView
                    android:gravity="center_vertical"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:text="Matter"
                    android:textColor="@color/font_style_70_4_textColor"
                    android:textSize="@dimen/font_style_70_4_textSize"
                    app:layout_minHeightPercent="8%w"
                    app:layout_paddingLeftPercent="1.8667%w"
                    />

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                    android:src="@mipmap/new_update_list_arrow"
                    app:layout_heightPercent="8%w"
                    app:layout_widthPercent="8%w"
                    />

            </com.zhy.android.percent.support.PercentLinearLayout>

            <View
                android:id="@+id/v_matter_container_divider"
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="@color/ui_split_line_style_1_1"
                android:layout_marginHorizontal="13dp"
                android:layout_marginTop="6dp"
                android:layout_below="@id/pll_matter_container"
                android:visibility="gone"
                />

            <com.zhy.android.percent.support.PercentLinearLayout
                android:gravity="center_vertical"
                android:id="@+id/wifi_setting_container"
                android:layout_below="@+id/v_matter_container_divider"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:orientation="horizontal"
                app:layout_marginTopPercent="@string/percent_5dp_w"
                app:layout_paddingLeftPercent="5.3333%w"
                app:layout_paddingRightPercent="6.1333%w"
                android:paddingBottom="6dp"
                >

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                    android:src="@mipmap/new_sensor_setting_icon_wifi_blue"
                    app:layout_heightPercent="16%w"
                    app:layout_widthPercent="13.3333%w"
                    />

                <TextView
                    android:gravity="center_vertical"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:text="@string/wifi_setting_label"
                    android:textColor="@color/font_style_70_4_textColor"
                    android:textSize="@dimen/font_style_70_4_textSize"
                    app:layout_minHeightPercent="8%w"
                    app:layout_paddingLeftPercent="1.8667%w"
                    />

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                    android:src="@mipmap/new_update_list_arrow"
                    app:layout_heightPercent="8%w"
                    app:layout_widthPercent="8%w"
                    />

            </com.zhy.android.percent.support.PercentLinearLayout>

            <View
                android:id="@+id/v_splicing_container_divider"
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="@color/ui_split_line_style_1_1"
                android:layout_marginStart="13dp"
                android:layout_marginEnd="13dp"
                android:layout_below="@id/wifi_setting_container"
                android:visibility="gone"
                />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:gravity="center_vertical"
                android:id="@+id/device_splicing_container"
                android:layout_below="@+id/v_splicing_container_divider"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:orientation="horizontal"
                app:layout_marginTopPercent="@string/percent_5dp_w"
                app:layout_paddingLeftPercent="5.3333%w"
                app:layout_paddingRightPercent="6.1333%w"
                android:visibility="gone"
                tools:visibility="visible"
                >

                <ImageView
                    android:id="@+id/iv_splicing"
                    android:contentDescription="@null"
                    android:layout_height="60dp"
                    android:layout_width="50dp"
                    android:src="@mipmap/h70b1_icon_setting_shebeipingjie"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    />

                <TextView
                    android:id="@+id/tv_splicing"
                    android:gravity="center_vertical"
                    android:layout_height="wrap_content"
                    android:layout_width="wrap_content"
                    android:text="@string/device_splicing"
                    android:textColor="@color/font_style_70_4_textColor"
                    android:textSize="@dimen/font_style_70_4_textSize"
                    android:layout_marginStart="7dp"
                    app:layout_constraintStart_toEndOf="@id/iv_splicing"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    />

                <ImageView
                    android:id="@+id/iv_splicing_what"
                    android:layout_width="28dp"
                    android:layout_height="28dp"
                    android:layout_marginHorizontal="4dp"
                    android:src="@mipmap/new_light_icon_diy_auto_what"
                    android:layout_marginStart="8.5dp"
                    android:layout_marginTop="1.5dp"
                    app:layout_constraintStart_toEndOf="@id/tv_splicing"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    />

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="30dp"
                    android:layout_width="30dp"
                    android:src="@mipmap/new_update_list_arrow"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:id="@+id/v_low_blue_container_divider"
                android:layout_width="match_parent"
                android:layout_height="1px"
                android:background="@color/ui_split_line_style_1_1"
                android:layout_marginStart="13dp"
                android:layout_marginEnd="13dp"
                android:layout_below="@id/device_splicing_container"
                android:visibility="gone"
                />

            <com.zhy.android.percent.support.PercentLinearLayout
                android:gravity="center_vertical"
                android:id="@+id/low_blue_light_container"
                android:layout_below="@+id/v_low_blue_container_divider"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:orientation="horizontal"
                app:layout_marginTopPercent="@string/percent_5dp_w"
                app:layout_paddingLeftPercent="5.3333%w"
                app:layout_paddingRightPercent="6.1333%w"
                android:paddingBottom="6dp"
                android:visibility="gone"
                tools:visibility="visible"
                >

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                    android:src="@mipmap/newr_setting_icon_low_blue_light_temp"
                    app:layout_heightPercent="16%w"
                    app:layout_widthPercent="13.3333%w"
                    />

                <TextView
                    android:gravity="center_vertical"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:text="@string/straightfloorlamp_low_blue_light_mode"
                    android:textColor="@color/font_style_70_4_textColor"
                    android:textSize="@dimen/font_style_70_4_textSize"
                    app:layout_minHeightPercent="8%w"
                    app:layout_paddingLeftPercent="1.8667%w"
                    />

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                    android:src="@mipmap/new_update_list_arrow"
                    app:layout_heightPercent="8%w"
                    app:layout_widthPercent="8%w"
                    />

            </com.zhy.android.percent.support.PercentLinearLayout>

            <com.govee.ui.component.WlanControlView
                android:id="@+id/wlan_control"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:visibility="gone"
                android:layout_below="@+id/low_blue_light_container"
                />

            <com.govee.ui.component.OpItemWithHintView
                android:id="@+id/op_item_with_hint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/wlan_control"
                android:visibility="gone"
                tools:visibility="visible"
                />

            <com.govee.ui.component.GuideView
                android:id="@+id/guide_view"
                android:layout_below="@+id/op_item_with_hint"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                />

            <include
                layout="@layout/component_dividing_line"
                android:id="@+id/line_device_model"
                android:layout_below="@+id/guide_view"
                android:layout_centerHorizontal="true"
                android:layout_height="wrap_content"
                android:layout_width="0dp"
                app:layout_marginBottomPercent="1.7333%w"
                app:layout_marginTopPercent="1.7333%w"
                app:layout_widthPercent="93.0667%w"
                />

            <com.govee.ui.component.DeviceBaseInfoViewV2
                android:id="@+id/device_base_info_container"
                android:layout_below="@+id/line_device_model"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                />

            <include
                layout="@layout/component_dividing_line"
                android:id="@+id/line_device_battery"
                android:layout_below="@+id/device_base_info_container"
                android:layout_centerHorizontal="true"
                android:layout_height="wrap_content"
                android:layout_width="0dp"
                android:visibility="gone"
                app:layout_marginBottomPercent="1.7333%w"
                app:layout_marginTopPercent="1.7333%w"
                app:layout_widthPercent="93.0667%w"
                />

            <com.zhy.android.percent.support.PercentLinearLayout
                android:gravity="center_vertical"
                android:id="@+id/device_battery_container"
                android:layout_below="@+id/line_device_battery"
                android:layout_height="wrap_content"
                android:layout_width="match_parent"
                android:orientation="horizontal"
                android:visibility="gone"
                app:layout_paddingLeftPercent="5.3333%w"
                app:layout_paddingRightPercent="6.1333%w"
                >

                <ImageView
                    android:contentDescription="@null"
                    android:layout_height="0dp"
                    android:layout_width="0dp"
                    android:src="@mipmap/new_sensor_setting_icon_wifi_blue"
                    app:layout_heightPercent="16%w"
                    app:layout_widthPercent="13.3333%w"
                    />

                <TextView
                    android:gravity="center_vertical"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:text="@string/battery_type_label"
                    android:textColor="@color/font_style_70_4_textColor"
                    android:textSize="@dimen/font_style_70_4_textSize"
                    app:layout_minHeightPercent="8%w"
                    app:layout_paddingLeftPercent="1.8667%w"
                    />

                <TextView
                    android:gravity="center_vertical|end"
                    android:id="@+id/tv_battery_type"
                    android:layout_height="match_parent"
                    android:layout_weight="1"
                    android:layout_width="0dp"
                    android:textColor="@color/font_style_70_5_textColor"
                    android:textSize="@dimen/font_style_70_5_textSize"
                    />

            </com.zhy.android.percent.support.PercentLinearLayout>

            <TextView
                android:id="@+id/bg_round_2_bottom_flag"
                android:layout_below="@+id/device_battery_container"
                android:layout_height="0dp"
                android:layout_width="match_parent"
                app:layout_marginTopPercent="1.4667%w"
                />

            <TextView
                style="@style/compoent_btn_style_1"
                android:background="@drawable/component_btn_style_6"
                android:id="@+id/btn_delete"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:text="@string/text_delete_device"
                android:textColor="@color/ui_btn_style_6_1_text_color"
                android:textSize="@dimen/ui_btn_style_6_1_text_size"
                app:layout_marginBottomPercent="1.0667%w"
                app:layout_marginTopPercent="8%w"
                app:layout_minWidthPercent="54.1333%w"
                app:layout_paddingLeftPercent="19.6%w"
                app:layout_paddingRightPercent="19.6%w"
                />
        </com.zhy.android.percent.support.PercentRelativeLayout>
    </androidx.core.widget.NestedScrollView>


</com.zhy.android.percent.support.PercentRelativeLayout>