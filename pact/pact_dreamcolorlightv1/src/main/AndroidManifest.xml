<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.govee.dreamcolorlightv1"
    tools:ignore="DiscouragedApi"
    >
    <application>
        <activity
            android:name=".add.DeviceNameAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.AdjustAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.setting.LimitSettingAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.setting.SettingBleWifiAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:launchMode="singleTop"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.v2.WifiChooseAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.v2.DeviceNameAcV2"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.v3.PairAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.v3.DeviceNameV3"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.v3.PairAcV1"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.AdjustAcV3"
            android:configChanges="uiMode|screenSize|orientation|keyboardHidden"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.setting.SettingBleWifiAcV1"
            android:configChanges="screenSize|orientation|keyboardHidden|uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.setting.Ac4BleWifiSettingNew"
            android:configChanges="screenSize|orientation|keyboardHidden|uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.setting.Ac4BleWifiSettingNewNoMemory"
            android:configChanges="screenSize|orientation|keyboardHidden|uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.setting.SettingAcH61B5"
            android:configChanges="screenSize|orientation|keyboardHidden|uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.setting.AcCutCali"
            android:configChanges="screenSize|orientation|keyboardHidden|uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />

    </application>
</manifest>
