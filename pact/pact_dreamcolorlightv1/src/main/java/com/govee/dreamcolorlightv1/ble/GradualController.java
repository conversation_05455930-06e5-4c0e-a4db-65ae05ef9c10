package com.govee.dreamcolorlightv1.ble;


import com.govee.base2light.ble.controller.AbsSingleController;

/**
 * Create by lins<PERSON><PERSON> on 2019-09-10
 * 开启渐变
 */
public class GradualController extends AbsSingleController {
    private int gradual;

    public GradualController(boolean gradualOpen) {
        super(true);
        this.gradual = gradualOpen ? 1 : 0;
    }

    public GradualController() {
        super(false);
    }

    @Override
    protected boolean writeResult(boolean writeSuc) {
        EventGradual.sendSuc(isWrite(), getCommandType(), getProType(), gradual);
        return true;
    }

    @Override
    protected byte[] translateWrite() {
        return new byte[]{(byte) gradual};
    }

    @Override
    protected void fail() {
        EventGradual.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.SINGLE_GRADUAL_CHANGE;
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        int gradual = validBytes[0];
        EventGradual.sendSuc(isWrite(), getCommandType(), getProType(), gradual);
        return true;
    }
}