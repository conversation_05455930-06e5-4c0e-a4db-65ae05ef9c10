package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.ui.R;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsColorUiMode;

import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;

/**
 * Create by xieying<PERSON> on 2019-07-22
 * color ui mode
 */
public class ColorUiModeV1 extends AbsColorUiMode {
    public ColorUiModeV1(int goodsType, String sku, String device, int ic, DiySupportV1 diySupport) {
        super(goodsType, sku, device, ic, diySupport);
    }

    @Override
    public AbsModeFragment getUiFragment() {
        ColorFragmentV1 fragment = new ColorFragmentV1();
        fragment.makeArguments(getSku());
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_color_mini, R.mipmap.new_control_light_btb_mode_color_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        subModeColor.loadLocal();
        return subModeColor;
    }
}
