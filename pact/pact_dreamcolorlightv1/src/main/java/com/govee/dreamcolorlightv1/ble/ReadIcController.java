package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsOnlyReadSingleController;
import com.ihoment.base2app.infra.LogInfra;

import java.util.List;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022/6/29
 * ic读取
 */
public class ReadIcController extends AbsOnlyReadSingleController {
    @Override
    protected void fail() {
        EventReadIc.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.SINGLE_READ_IC;
    }

    public static int[] parseIc(List<byte[]> bytes) {
        for (byte[] ptByte : bytes) {
            if (ptByte != null && ptByte.length == 20) {
                byte op = ptByte[1];
                if (op == BleProtocol.SINGLE_READ_IC) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i("ReadIcControllerV2", "parseIc()");
                    }
                    int icNum1 = BleUtil.getSignedShort(ptByte[2], ptByte[3]);
                    int sectionNum1 = ptByte[4];
                    int icNum2 = BleUtil.getSignedShort(ptByte[5], ptByte[6]);
                    int sectionNum2 = ptByte[7];
                    return new int[]{icNum1, sectionNum1, icNum2, sectionNum2};
                }
            }
        }
        return null;
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        int icNum1 = BleUtil.getSignedShort(validBytes[0], validBytes[1]);
        int sectionNum1 = validBytes[2];
        int icNum2 = BleUtil.getSignedShort(validBytes[3], validBytes[4]);
        int sectionNum2 = validBytes[5];
        EventReadIc.sendSuc(isWrite(), getCommandType(), getProType(), sectionNum1, sectionNum2, icNum1, icNum2);
        return true;
    }

}