package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.OldMusicEffect;
import com.govee.base2light.light.v1.AbsNewMusicFragment;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV2;
import com.govee.dreamcolorlightv1.pact.Support;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2021/5/5
 * 新音乐模式$
 */
public class MusicFragmentV2 extends AbsNewMusicFragment {
    private SubModeMusicV2 subModeMusicV2 = new SubModeMusicV2();
    private List<SubMusicMode> subMusicModeList;

    @Override
    protected void init() {
        super.init();
        /*防止fragment视图还未加载就已传递了模式信息；因此在init完成时也需要刷新ui*/
        updateUi();
    }

    @Override
    protected int getCurSubMusicCode() {
        return subModeMusicV2.getMusicCode();
    }

    @Override
    protected void oldMusicParamsChange(OldMusicEffect oldMusicEffect) {
        SubModeMusicV2 copy = subModeMusicV2.copy();
        copy.oldMusicEffectChange(oldMusicEffect);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
    }

    @Override
    protected int getSensitivity() {
        return subModeMusicV2.getSensitivity();
    }

    @Override
    protected void showOldSubMusicEditDialog(SubMusicMode musicMode) {
        int musicCode = musicMode.musicCode;
        if (musicCode == BleProtocol.value_sub_mode_music_rhythm) {
            showOldMusicEditV1(musicMode, getSensitivity(), subModeMusicV2.isAutoColor(), subModeMusicV2.getRgb(), subModeMusicV2.isDynamic());
        } else {
            showOldMusicEditV0(musicMode, getSensitivity(), subModeMusicV2.isAutoColor(), subModeMusicV2.getRgb(), subModeMusicV2.isDynamic());
        }
    }

    @NonNull
    @Override
    protected List<SubMusicMode> getSupportMusicModes() {
        if (subMusicModeList == null) {
            subMusicModeList = makeRgbicSubMusicModes(
                    BleProtocol.value_sub_mode_music_energy,
                    BleProtocol.value_sub_mode_music_rhythm,
                    BleProtocol.value_sub_mode_music_spectrum,
                    BleProtocol.value_sub_mode_music_scroll
            );
        }
        return subMusicModeList;
    }

    @Override
    protected void onSensitivityChange(int sensitivity) {
        SubModeMusicV2 copy = subModeMusicV2.copy();
        copy.setSensitivity(sensitivity);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeMusicV2) {
            boolean checkAnalytic4SubModeUse = ((SubModeMusicV2) subMode).checkAnalytic4SubModeUse(getSku());
            this.subModeMusicV2 = (SubModeMusicV2) subMode;
            boolean newMusicCode = Support.isNewMusicCode(subModeMusicV2.getMusicCode());
            if (!newMusicCode) {
                /*旧音乐模式-需要存储到OldMusicEffect*/
                saveOldMusic(subModeMusicV2.getSensitivity(), subModeMusicV2.isAutoColor(), subModeMusicV2.isDynamic(), subModeMusicV2.getRgb());
            }
            updateUi();
            if (checkAnalytic4SubModeUse) {
                SubMusicMode subMusicMode = querySubMusicModeByMusicCode(getCurSubMusicCode());
                if (subMusicMode != null) {
                    analyticSubModeDetail(ParamFixedValue.mode_use_music_, subMusicMode.getAnalyticSubModeStr());
                }
            }
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_music;
    }

    private void updateUi() {
        if (!isViewInflateOk()) return;
        /*灵敏度*/
        sensitivityUi(subModeMusicV2.getSensitivity());
        /*选中的子音乐模式*/
        subMusicUi();
    }

    @Override
    public ISubMode getCompleteSubMode() {
        SubModeMusicV2 subModeMusic = this.subModeMusicV2.copy();
        boolean oldSubMusic = !this.subModeMusicV2.isNewMusic;
        if (oldSubMusic) {
            OldMusicEffect oldMusicEffect = OldMusicEffect.read4OldMusic(getSku(), getDevice(), this.subModeMusicV2.getMusicCode(), this.subModeMusicV2.getSensitivity());
            subModeMusic.oldMusicEffectChange(oldMusicEffect);
        } else {
            subModeMusic.setSensitivity(this.subModeMusicV2.getSensitivity());
            subModeMusic.musicEffect = AbsNewMusicEffect.getLocalNewMusicEffect(getSku(), getDevice(), this.subModeMusicV2.getMusicCode(), getIcNum());
        }
        return subModeMusic;
    }
}