package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.comm.AbsNotifyParse;
import com.ihoment.base2app.infra.LogInfra;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022/6/29
 * ic变更解析
 */
public class CheckIcNumParse extends AbsNotifyParse {

    @Override
    protected byte getNotifyType() {
        return BleProtocol.SINGLE_NOTIFY_IC;
    }

    @Override
    protected void parseValue(byte[] value) {
        int icNum = BleUtil.getSignedShort(value[0], value[1]);
        if (LogInfra.openLog()) {
            LogInfra.Log.w("CheckIcNumParse", "CheckIcNumParse ic:" + icNum);
        }
        EventIcNotify.sendEventIcNotify(icNum);
    }
}