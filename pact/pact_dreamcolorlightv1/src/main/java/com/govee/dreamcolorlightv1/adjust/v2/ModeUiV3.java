package com.govee.dreamcolorlightv1.adjust.v2;

import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.light.config.MusicSupportConfig;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.ui.mode.IUiMode;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiMode;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiModeV1;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiModeV2;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiModeV3;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiModeV4;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiModeV5;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiModeV6;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiModeV7;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiModeV8;
import com.govee.dreamcolorlightv1.adjust.ui.DiyUiMode;
import com.govee.dreamcolorlightv1.adjust.ui.MicUiModeV1;
import com.govee.dreamcolorlightv1.adjust.ui.MicUiModeV3;
import com.govee.dreamcolorlightv1.adjust.ui.MusicUiMode4AbsMusic;
import com.govee.dreamcolorlightv1.adjust.ui.MusicUiModeV1;
import com.govee.dreamcolorlightv1.adjust.ui.MusicUiModeV3;
import com.govee.dreamcolorlightv1.adjust.ui.ScenesUiModeV1;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;

import java.util.ArrayList;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2019-07-22
 * mode ui
 */
class ModeUiV3 extends AbsMode4UIV1 {
    private static final String TAG = "ModeUiV3";

    public ModeUiV3(AppCompatActivity ac, String sku, String device, int goodsType, int ic, DiySupportV1 diySupportV1, boolean noSupportHighColor4SpecialSku) {
        super(ac, sku, device, goodsType, ic, diySupportV1, null, noSupportHighColor4SpecialSku);
    }

    public void checkMusicVersion4BkProtocol(int musicVersion, String versionSoft, String versionHard) {
        boolean isMic = AbsMicFragmentV4.isMicModeByPhone(sku, device);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkMusicVersion4BkProtocol() isMic = " + isMic);
        }
        IUiMode positionUiMode = getPositionUiMode(0);
        if (musicVersion == 0) {
            boolean isMicV0 = positionUiMode instanceof MicUiModeV1;
            boolean isMusicV0 = positionUiMode instanceof MusicUiModeV1;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicVersion4BkProtocol() isMicV0 = " + isMicV0 + " ; isMusicV0 = " + isMusicV0);
            }
            if (isMic && !isMicV0) {
                changePositionUiMode(0, new MicUiModeV1(sku, device));
                return;
            }
            if (!isMic && !isMusicV0) {
                changePositionUiMode(0, new MusicUiModeV1(sku, device));
                return;
            }
            return;
        }

        if (musicVersion == 1) {
            boolean isMicV1 = positionUiMode instanceof MicUiModeV3;
            boolean isMusicV1 = positionUiMode instanceof MusicUiModeV3;
            boolean isAbsMusic = positionUiMode instanceof MusicUiMode4AbsMusic;
            boolean supportAbsMusic = MusicSupportConfig.INSTANCE.supportAbsMusic(goodsType, sku, versionSoft, versionHard);
            SafeLog.Companion.i(getTAG(), () -> "changeMusicModeVersion() supportAbsMusic = " + supportAbsMusic);
            if (supportAbsMusic) {
                if (!isMic && !isAbsMusic) {
                    changePositionUiMode(0, new MusicUiMode4AbsMusic(ac, sku, device, arguments));
                    return;
                }
            }
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicVersion4BkProtocol() isMicV1 = " + isMicV1 + " ; isMusicV1 = " + isMusicV1);
            }
            if (isMic && !isMicV1) {
                changePositionUiMode(0, new MicUiModeV3(ac, sku, device));
                return;
            }
            if (!isMic && !isMusicV1 && !isAbsMusic) {
                changePositionUiMode(0, new MusicUiModeV3(sku, device));
            }
        }

    }

    @Override
    protected IUiMode getMode4() {
        return new DiyUiMode(sku, goodsType);
    }

    @Override
    protected IUiMode getMode3() {
        return new ScenesUiModeV1(sku, device, goodsType, true);
    }

    @Override
    protected IUiMode getMode2() {
        return new ColorUiModeV1(goodsType, sku, device, ic, diySupportV1);
    }

    @Override
    protected IUiMode getMode1() {
        return new MusicUiModeV1(sku, device);
    }

    @Override
    protected String getTAG() {
        return "ModeUiV3";
    }

    /**
     * 更改颜色模式
     * <p>0:旧颜色模式-0x0b<p/>
     * <p>2:新颜色模式-0x15-支持分段控制亮度<p/>
     *
     * @param subModeColorVersion
     */
    public void changeColorMode(int subModeColorVersion, BleIotInfo info, DiySupportV1 diySupportV1, @Nullable ArrayList<Integer> icSubList) {
        IUiMode positionUiMode = getPositionUiMode(1);
        int ic = info.ic;
        int curColorUiModeVersion = getCurColorUiModeVersion(positionUiMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "changeColorMode() subModeColorVersion = " + subModeColorVersion + " ; curColorUiModeVersion = " + curColorUiModeVersion);
        }
        if (curColorUiModeVersion > -1 && curColorUiModeVersion != subModeColorVersion) {
            /*需要进行颜色模式ui界面转化*/
            if (subModeColorVersion == 2) {
                changePositionUiMode(1, new ColorUiModeV2(goodsType, sku, device, ic, diySupportV1));
                updateSupportHighColor(diySupportV1);
                return;
            }
            if (subModeColorVersion == 3) {
                changePositionUiMode(1, new ColorUiModeV3(goodsType, sku, device, ic, diySupportV1));
                updateSupportHighColor(diySupportV1);
                return;
            }
            if (subModeColorVersion == 4) {
                changePositionUiMode(1, new ColorUiModeV4(goodsType, sku, device, ic, diySupportV1));
                updateSupportHighColor(diySupportV1);
                return;
            }
            if (subModeColorVersion == 5) {
                changePositionUiMode(1, new ColorUiModeV5(goodsType, sku, device, ic, diySupportV1));
                updateSupportHighColor(diySupportV1);
            }
            if (subModeColorVersion == 6) {
                changePositionUiMode(1, new ColorUiModeV6(goodsType, sku, device, ic, diySupportV1));
                updateSupportHighColor(diySupportV1);
            }
            if (subModeColorVersion == 7) {
                ColorUiModeV7 colorUiModeV7 = new ColorUiModeV7(goodsType, sku, device, ic, diySupportV1);
                colorUiModeV7.setDiyGraffitiFragmentIcSubList(icSubList);
                changePositionUiMode(1, colorUiModeV7);
                updateSupportHighColor(diySupportV1);
            }
            if (subModeColorVersion == 8) {
                changePositionUiMode(1, new ColorUiModeV8(goodsType, sku, device, ic, diySupportV1));
                updateSupportHighColor(diySupportV1);
            }
        }
    }

    private int getCurColorUiModeVersion(IUiMode positionUiMode) {
        if (positionUiMode instanceof ColorUiMode) return 0;
        if (positionUiMode instanceof ColorUiModeV1) return 1;
        if (positionUiMode instanceof ColorUiModeV2) return 2;
        if (positionUiMode instanceof ColorUiModeV3) return 3;
        if (positionUiMode instanceof ColorUiModeV4) return 4;
        if (positionUiMode instanceof ColorUiModeV5) return 5;
        if (positionUiMode instanceof ColorUiModeV6) return 6;
        if (positionUiMode instanceof ColorUiModeV7) return 7;
        if (positionUiMode instanceof ColorUiModeV8) return 8;
        return -1;
    }

    @Override
    protected boolean showHighColorContainer() {
        if (diySupportV1 != null) {
            return isSupportHighColor(diySupportV1);
        }
        return false;
    }

    public void updateGradientEffect(boolean gradual) {
        for (int i = 0; i < getModeNum(); i++) {
            IUiMode uiMode = getPositionUiMode(i);
            if (uiMode instanceof ColorUiModeV8) {
                ((ColorUiModeV8) uiMode).updateGradientEffect(gradual);
                break;
            }
        }
    }
}