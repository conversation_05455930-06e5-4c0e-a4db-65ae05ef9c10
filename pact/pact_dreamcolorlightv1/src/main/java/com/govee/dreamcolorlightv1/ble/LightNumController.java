package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsOnlyReadSingleController;

/**
 * Create by l<PERSON><PERSON><PERSON> on 2019-09-10
 * 灯串数量读取
 */
public class LightNumController extends AbsOnlyReadSingleController {
    @Override
    protected void fail() {
        EventLightNum.sendFail(false, getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.SINGLE_LIGHT_NUM;
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        int num = validBytes[0];
        if (num <= 0) {
            EventLightNum.sendFail(false, getCommandType(), getProType());
        } else {
            EventLightNum.sendSuc(false, getCommandType(), getProType(), num);
        }
        return true;
    }
}