package com.govee.dreamcolorlightv1.adjust;

import com.govee.base2home.scenes.builder.CmdBuilder;
import com.govee.base2home.sku.Colors;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v2.DiyTemplate;
import com.govee.base2light.ac.diy.v2.DiyValue;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyShortcutManger;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultipleColorStripControllerV1;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.rhythm.RhyRule;
import com.govee.base2light.snapshot.BaseSnapshotDataUtil;
import com.govee.base2light.snapshot.EventGeneralSnapshotCmd;
import com.govee.base2light.snapshot.SnapshotMode;
import com.govee.dreamcolorlightv1.adjust.v2.ExtV1;
import com.govee.dreamcolorlightv1.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeMusic;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV2;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.govee.dreamcolorlightv1.iot.CmdBrightness;
import com.govee.dreamcolorlightv1.iot.CmdBulb;
import com.govee.dreamcolorlightv1.iot.CmdPt;
import com.govee.dreamcolorlightv1.iot.CmdPtReal;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

/**
 * 组装UI界面当前模式的指令集合（ble + iot）
 */
public class SnapshotDataUtilV2 extends BaseSnapshotDataUtil {
    ExtV1 ext;

    private static class Builder {
        public final static SnapshotDataUtilV2 instance = new SnapshotDataUtilV2();
    }

    public static SnapshotDataUtilV2 getInstance(ExtV1 ext) {
        Builder.instance.ext = ext;
        return Builder.instance;
    }

    private static final String TAG = "SnapshotDataUtilV2";

    public void dealData(AbsMode4UIV1 modeUI, BleIotInfo info) {
        super.dealData(modeUI, info);
        //模式没有改变过
        if (!checkEnable(modeUI)) {
            return;
        }
        //组装指令
        List<SnapshotMode.CmdMode> cmds = new ArrayList<>();

        //亮度指令
        CmdBrightness cmdBrightness = new CmdBrightness(ext.brightness);
        SnapshotMode.CmdMode cmdModeBrightness = new SnapshotMode.CmdMode();
        cmdModeBrightness.cmdType = RhyRule.op_type_snapshot_switch;
        cmdModeBrightness.bleCmds = makeBlueMsg(new CmdPtReal(new BrightnessController(ext.brightness)).getCommand());
        cmdModeBrightness.iotCmd = CmdBuilder.makeCmdStr(cmdBrightness);
        cmds.add(cmdModeBrightness);

        ISubMode iSubMode = info.mode.subMode;
        if (iSubMode instanceof SubModeMusic) {
            recordAddMusicSnapshot();
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            Mode mode = new Mode();
            mode.subMode = iSubMode;
            ModeController controller = new ModeController(mode);

            //iot指令混合了音乐模式设置与其它参数设置
            CmdPt cmdPtReal = new CmdPt(CmdPt.pt_op_mode, controller.getValue());
            cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getValue());
            cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);

            cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode);
        } else if (iSubMode instanceof SubModeMusicV2) {
            recordAddMusicSnapshot();
            int musicCode = ((SubModeMusicV2) iSubMode).getMusicCode();
            int sensitivity = ((SubModeMusicV2) iSubMode).getSensitivity();
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();

            AbsNewMusicEffect localNewMusicEffect = AbsNewMusicEffect.getLocalNewMusicEffect(info.sku, info.device, musicCode, info.ic);
            CmdPt cmdPtReal;
            if (localNewMusicEffect == null) {
                Mode mode = new Mode();
                mode.subMode = iSubMode;
                ModeController controller = new ModeController(mode);
                cmdPtReal = new CmdPt(CmdPt.pt_op_mode, controller.getValue());
            } else {
                //当前音乐模式的颜色多包
                MultipleController4Music musicController = new MultipleController4Music((byte) sensitivity, localNewMusicEffect);
                cmdPtReal = CmdPt.getMultiNewMusicMode(musicController);
            }

            cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getValue());
            cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);

            cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode);
        } else if (iSubMode instanceof SubModeColor || iSubMode instanceof SubModeColorV2) {
            recordAddColorSnapshot();
            int[] rgbSet;
            int[] brightnessSet;
            Colors color;
            AbsSingleController[] modeControllers;
            int gradual;
            iSubMode = modeUI.getCurCompleteMode();
            if (iSubMode instanceof SubModeColor) {
                rgbSet = ((SubModeColor) iSubMode).rgbSet;
                color = new Colors(rgbSet, null, "");
                modeControllers = SubModeColor.makeSubModeColor(color);
                gradual = ((SubModeColor) iSubMode).gradual;
            } else {
                rgbSet = ((SubModeColorV2) iSubMode).rgbSet;
                brightnessSet = ((SubModeColorV2) iSubMode).brightnessSet;
                color = new Colors(rgbSet, brightnessSet, "");
                modeControllers = SubModeColorV2.makeSubModeColor(color);
                gradual = ((SubModeColorV2) iSubMode).gradual;
            }
            Gradual4BleWifiController gradualController = new Gradual4BleWifiController(gradual == 1);
            SnapshotMode.CmdMode cmdMode3 = new SnapshotMode.CmdMode();
            CmdPt cmdPtRealGradual = new CmdPt(CmdPt.pt_op_gradual, gradualController.getValue());
            cmdMode3.bleCmds = makeBlueMsg(cmdPtRealGradual.getValue());
            cmdMode3.iotCmd = CmdBuilder.makeCmdStr(cmdPtRealGradual);
            cmdMode3.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode3);

            boolean supportColorStripMulti = Support.supportColorStripMulti(info.goodsType, info.pactType, info.pactCode);
            LogInfra.Log.i(TAG, "onEventColorStrip() supportColorStripMulti = " + supportColorStripMulti);
            if (supportColorStripMulti) {
                MultipleColorStripControllerV1 stripControllerV1 = new MultipleColorStripControllerV1(color);
                CmdPtReal cmdPtReal = CmdPtReal.makeColorStripCmdPtReal(stripControllerV1, null);
                SnapshotMode.CmdMode cmdMode2 = new SnapshotMode.CmdMode();
                cmdMode2.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
                cmdMode2.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
                cmdMode2.cmdType = RhyRule.op_type_snapshot_mode_2;
                cmds.add(cmdMode2);
            } else {
                //色条与亮度
                SnapshotMode.CmdMode cmdMode2 = new SnapshotMode.CmdMode();
                List<byte[]> bytes = new ArrayList<>();
                if (modeControllers != null) {
                    for (AbsSingleController absSingleController : modeControllers) {
                        bytes.add(absSingleController.getValue());
                    }
                }
                CmdPtReal cmdPtReal = new CmdPtReal(bytes);
                cmdMode2.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
//            int subModeColorVersion = Support.supportPartBrightness(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, info.wifiSoftVersion);
                CmdBulb cmdBulb = new CmdBulb(bytes);
                cmdMode2.iotCmd = CmdBuilder.makeCmdStr(cmdBulb);
                cmdMode2.cmdType = RhyRule.op_type_snapshot_mode_2;
                cmds.add(cmdMode2);
            }

        } else if (iSubMode instanceof SubModeScenes) {
            recordAddSceneSnapshot();
            Mode mode = new Mode();
            mode.subMode = iSubMode;
            ModeController controller = new ModeController(mode);
            int scenesModeVersion = Support.newVersion(1, info.sku, info.goodsType, info.pactCode, info.versionSoft, info.versionHard, ext.wifiSoftVersion, ext.wifiHardVersion);
            AbsMultipleControllerV14Scenes newScenesMode = Support.is2NewScenesMode(info.sku, info.device, mode, scenesModeVersion);

            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            CmdPt cmdPtReal;
            if (newScenesMode == null) {
                cmdPtReal = new CmdPt(CmdPt.pt_op_mode, controller.getValue());
            } else {
                cmdPtReal = CmdPt.getNewScenesCmdPt(newScenesMode);
            }
            cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getValue());
            cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
            cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode);

        } else if (iSubMode instanceof SubModeNewDiy) {
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            CmdPt cmdPtReal = getDiyCmdPtReal((SubModeNewDiy) iSubMode, info);
            if (cmdPtReal != null) {
                cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getValue());
                cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
                cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
                cmds.add(cmdMode);
            } else {
                LogInfra.Log.e(TAG, " 无法获取快照diy参数-解析失败");
            }
        }
        EventGeneralSnapshotCmd.sendEvent(cmds, ext.wifiSoftVersion, ext.wifiHardVersion);
    }

    private CmdPt getDiyCmdPtReal(SubModeNewDiy modeNewDiy, BleIotInfo info) {
        DiySupportV1 diySupport = Diy.getDiySupport(Support.getDiyVersion(1, info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard, ext.wifiSoftVersion, ext.wifiHardVersion));
        String diyValueKey = modeNewDiy.getDiyValueKey();
        DiyValue diyValue = DiyShortcutManger.queryLastApplyDiy(info.sku, info.goodsType, diySupport, info.ic, diyValueKey, false);

        DiyGraffitiV2 highColor = getHighColor(info, modeNewDiy.getDiyCode());
        if (highColor != null) {
            return CmdPt.getDiyCmdPt4DiyGraffiti(highColor);
        }
        recordAddDiySnapshot();
        boolean aiEffect = diyValue.isAiDiy();
        if (aiEffect) {
            DiyAi diyAi = diyValue.toDiyAi();
            if (diyAi != null) {

                return CmdPt.getPtRealCmdPt(diyAi.command4PtReal.getCommands4IotPtReal());
            }
        }
        boolean studioDiy = diyValue.isStudioDiyEffect();
        if (studioDiy) {
            DiyStudio diyStudio = diyValue.toDiyStudio4Apply();
            if (diyStudio != null && diyStudio.isValid()) {
                boolean supportRgbIcV1 = Support.isSupportRgbicV1(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, info.wifiSoftVersion);

                AbsMultipleControllerV14DiyTemplate controllerV14DiyTemplate = diyStudio.toMulti4Scenes4RgbicV1(supportRgbIcV1 ? 1 : 0);
                return CmdPt.getDiyTemplateCmdPt(controllerV14DiyTemplate);
            }
        }
        DiyProtocol diyProtocol = diyValue.toDiyProtocol4Apply();
        if (diyProtocol != null) {
            return CmdPt.getDiyCmdPt(diyProtocol);
        }

        DiyGraffitiV2 diyGraffitiV2 = diyValue.toDiyGraffiti4Apply4Rgbic();
        if (diyGraffitiV2 != null) {
            return CmdPt.getDiyCmdPt4DiyGraffiti(diyGraffitiV2);
        }
        DiyTemplate diyTemplate = diyValue.toDiyTemplate();
        if (diyTemplate != null) {
            AbsMultipleControllerV14DiyTemplate diyTemplateController = ScenesOp.parseDiyTemplateV2(diyTemplate.scenesCode, diyTemplate.diyCode, diyTemplate.effectStr);
            if (null == diyTemplateController) {
                diyTemplateController = ScenesOp.parseDiyTemplate(diyTemplate.scenesCode, diyTemplate.diyCode, diyTemplate.effectStr);
            }
            return CmdPt.getDiyTemplateCmdPt(diyTemplateController);
        }
        return null;
    }


}