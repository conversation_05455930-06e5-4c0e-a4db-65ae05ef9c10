package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsMusicNoIcUiMode;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV3;
import com.govee.ui.R;


/**
 * Create by xie<PERSON>wu on 2019-07-22
 * music ui mode
 */
public class MusicUiModeV3 extends AbsMusicNoIcUiMode {
    public MusicUiModeV3(String sku, String device) {
        super(sku);
        this.device = device;
    }

    @Override
    public AbsModeFragment getUiFragment() {
        MusicFragmentV3 fragment = new MusicFragmentV3();
        fragment.makeArguments(getSku(), device);
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_music_mini, R.mipmap.new_control_light_btb_mode_music_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeMusicV3 subModeMusic = new SubModeMusicV3();
        subModeMusic.loadLocal();
        return subModeMusic;
    }
}
