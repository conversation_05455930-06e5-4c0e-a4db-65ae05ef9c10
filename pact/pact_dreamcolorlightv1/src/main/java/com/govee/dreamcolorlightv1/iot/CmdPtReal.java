package com.govee.dreamcolorlightv1.iot;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.util.Encode;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.comm.MultipleBleBytes;
import com.govee.base2light.ble.controller.AbsController;
import com.govee.base2light.ble.controller.AbsMultipleControllerV1;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.MultipleColorStripControllerV1;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.controller.MultipleDiyControllerV1;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.BulbStringColorController;
import com.govee.dreamcolorlightv1.ble.BulbStringColorControllerV2;
import com.govee.dreamcolorlightv1.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeColorV3;
import com.govee.dreamcolorlightv1.ble.SubModeColorV4;
import com.govee.dreamcolorlightv1.ble.SubModeColorV6;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV3;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/6/24
 * cmd=ptReal$
 */
@Keep
public class CmdPtReal extends AbsCmd {
    private static final String TAG = "CmdPtReal";
    private final List<String> command = new ArrayList<>();

    public static int OP_VERSION_ABS_MUSIC_MODE = 1;
    public int opVersion;
    public transient int musicCode = 0;
    public transient int musicSensitivity = 0;
    public transient ICmdPtRealOpResult opResult;

    protected CmdPtReal() {
    }

    public int getCommandSize() {
        return command.size();
    }

    @Override
    public String getCmd() {
        return Cmd.ptReal;
    }

    public List<String> getCommand() {
        return command;
    }

    public CmdPtReal(@NonNull AbsController controller) {
        byte[] bytes = controller.getValue();
        String valueStr = Encode.encryptByBase64(bytes);
        command.add(valueStr);
    }

    public CmdPtReal(@NonNull AbsController[] controllers) {
        for (AbsController controller : controllers) {
            byte[] value = controller.getValue();
            String valueStr = Encode.encryptByBase64(value);
            command.add(valueStr);
        }
    }

    public CmdPtReal(@NonNull List<byte[]> multipleBytes) {
        for (byte[] multipleByte : multipleBytes) {
            String valueStr = Encode.encryptByBase64(multipleByte);
            command.add(valueStr);
        }
    }

    public Byte getOpCommandByte() {
        if (command.isEmpty()) return null;
        int size = command.size();
        String commandByteStr = command.get(size - 1);
        byte[] bytes = Encode.decryByBase64(commandByteStr);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getOpCommandByte() bytes = " + BleUtil.bytesToHexString(bytes));
        }
        if (bytes != null && bytes.length == 20) {
            return bytes[1];
        }
        return null;
    }

    public byte[] getOpCommandBytes() {
        if (command.isEmpty()) return null;
        int size = command.size();
        String commandByteStr = command.get(size - 1);
        return Encode.decryByBase64(commandByteStr);
    }

    /**
     * 是否是透传指令-进行颜色控制
     *
     * @return
     */
    public SubModeColorV6 isPtReal4ColorModeV6() {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size == 1) {
            /*若单一包，则可设置分段颜色，分段亮度*/
            String encodeStr = command.get(0);
            byte[] originalBytes = Encode.decryByBase64(encodeStr);
            if (originalBytes == null) return null;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isPtReal4ColorModeV4() originalBytes = " + BleUtil.bytesToHexString(originalBytes));
            }
            int[] setColor = SubModeColorV6.isSetColor(originalBytes);
            if (setColor[0] == 0) return null;
            SubModeColorV6 subModeColorV6 = new SubModeColorV6();
            subModeColorV6.parseWriteBytes(originalBytes);
            return subModeColorV6;
        }
        /*多包指令，设置色条，可带亮度，也可不带亮度*/
        SubModeColorV6 subModeColorV6 = new SubModeColorV6();
        for (String encodeStr : command) {
            byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isPtReal4SetPartColor() setColor4PosBytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
            }
            boolean setMode2Color = SubModeColorV6.isSetMode2Color(setColor4PosBytes);
            if (!setMode2Color) return null;
            /*解析对应位置的颜色*/
            SubModeColorV6.parsePosColor(setColor4PosBytes, subModeColorV6);
        }
        return subModeColorV6;
    }


    /**
     * 是否是透传指令-进行颜色控制
     *
     * @return
     */
    public SubModeColorV4 isPtReal4ColorModeV4() {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size == 1) {
            /*若单一包，则可设置分段颜色，分段亮度*/
            String encodeStr = command.get(0);
            byte[] originalBytes = Encode.decryByBase64(encodeStr);
            if (originalBytes == null) return null;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isPtReal4ColorModeV4() originalBytes = " + BleUtil.bytesToHexString(originalBytes));
            }
            int[] setColor = SubModeColorV4.isSetColor(originalBytes);
            if (setColor[0] == 0) return null;
            SubModeColorV4 subModeColorV4 = new SubModeColorV4();
            subModeColorV4.parseWriteBytes(originalBytes);
            return subModeColorV4;
        }
        /*多包指令，设置色条，可带亮度，也可不带亮度*/
        SubModeColorV4 subModeColorV4 = new SubModeColorV4();
        for (String encodeStr : command) {
            byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isPtReal4SetPartColor() setColor4PosBytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
            }
            boolean setMode2Color = SubModeColorV4.isSetMode2Color(setColor4PosBytes);
            if (!setMode2Color) return null;
            /*解析对应位置的颜色*/
            SubModeColorV4.parsePosColor(setColor4PosBytes, subModeColorV4);
        }
        return subModeColorV4;
    }

    /**
     * 是否是采用ptReal设置分段颜色
     */
    public SubModeColorV3 isPtReal4SetPartColor4V3() {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size >= 2) {
            SubModeColorV3 subModeColor = new SubModeColorV3();
            /*至少一包设置颜色+一包设置渐变值*/
            for (int i = 0; i < size - 1; i++) {
                String encodeStr = command.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "isPtReal4SetPartColor() setColor4PosBytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
                boolean setMode2Color = SubModeColorV3.isSetMode2Color(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColorV3.parsePosColor(setColor4PosBytes, subModeColor);
            }
            String encodeStr = command.get(size - 1);
            byte[] gradual20Bytes = Encode.decryByBase64(encodeStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isPtReal4SetPartColor() gradual20Bytes = " + BleUtil.bytesToHexString(gradual20Bytes));
            }
            boolean setGradual = Gradual4BleWifiController.isWriteGradualController(gradual20Bytes);
            if (setGradual) {
                subModeColor.gradual = Gradual4BleWifiController.parseGradual(gradual20Bytes);
                return subModeColor;
            }
            return null;
        }
        return null;
    }

    /**
     * 是否是采用ptReal设置分段颜色
     */
    public SubModeColorV2 isPtReal4SetPartColor() {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size >= 2) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            /*至少一包设置颜色+一包设置渐变值*/
            for (int i = 0; i < size - 1; i++) {
                String encodeStr = command.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "isPtReal4SetPartColor() setColor4PosBytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
                boolean setMode2Color = SubModeColorV2.isSetMode2Color(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColorV2.parsePosColor(setColor4PosBytes, subModeColor);
            }
            String encodeStr = command.get(size - 1);
            byte[] gradual20Bytes = Encode.decryByBase64(encodeStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isPtReal4SetPartColor() gradual20Bytes = " + BleUtil.bytesToHexString(gradual20Bytes));
            }
            boolean setGradual = Gradual4BleWifiController.isWriteGradualController(gradual20Bytes);
            if (setGradual) {
                subModeColor.gradual = Gradual4BleWifiController.parseGradual(gradual20Bytes);
                return subModeColor;
            }
            return null;
        }
        return null;
    }

    public static CmdPtReal makeColorStripCmdPtReal(MultipleColorStripControllerV1 controllerV1, ICmdPtRealOpResult iColorStripResult) {
        List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(controllerV1);
        CmdPtReal cmdPtReal = new CmdPtReal(multipleWriteBytes);
        cmdPtReal.opResult = iColorStripResult;
        return cmdPtReal;
    }

    public static CmdPtReal makeColorStripCmdPtRealV2(MultipleColorStripControllerV1 controllerV1, AbsController controller) {
        List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(controllerV1);
        multipleWriteBytes.add(controller.getValue());
        CmdPtReal cmdPtReal = new CmdPtReal(multipleWriteBytes);
        return cmdPtReal;
    }

    public static CmdPtReal makeCmdPtRealWithCallBack(AbsSingleController controller, ICmdPtRealOpResult iCmdPtRealOpResult) {
        CmdPtReal cmdPtReal = new CmdPtReal(controller);
        cmdPtReal.opResult = iCmdPtRealOpResult;
        return cmdPtReal;
    }

    public static CmdPtReal getDiyCmdPtV1(DiyProtocol diyProtocol) {
        MultipleDiyControllerV1 multipleDiyController = new MultipleDiyControllerV1(diyProtocol);
        /*diy效果数据*/
        List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(multipleDiyController);
        Mode mode = new Mode();
        mode.subMode = new SubModeNewDiy(diyProtocol.getDiyCode());
        ModeController modeController = new ModeController(mode);
        /*切换至diy模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytes.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytes);
    }

    public static CmdPtReal getDiyCmdPt4DiyGraffitiV2(int goodsType, DiyGraffitiV2 diyGraffiti) {
        AbsMultipleControllerV1 controllerV1 = Support.makeGraffitiController(goodsType, diyGraffiti);
        List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(controllerV1);
        Mode mode = new Mode();
        mode.subMode = new SubModeNewDiy(diyGraffiti.getDiyCode());
        ModeController modeController = new ModeController(mode);
        /*切换至diy模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytes.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytes);
    }


    public static CmdPtReal getNewScenesCmdPtReal(AbsMultipleControllerV14Scenes multiNewScenesControllerV1) {
        List<byte[]> multipleWriteBytesV1 = MultipleBleBytes.getMultipleWriteBytesV1(multiNewScenesControllerV1);
        Mode mode = new Mode();
        SubModeScenes subMode = new SubModeScenes();
        subMode.setEffect(multiNewScenesControllerV1.getScenesCode());
        mode.subMode = subMode;
        ModeController modeController = new ModeController(mode);
        /*切换至场景模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV1.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytesV1);
    }

    public static CmdPtReal getDiyTemplateCmdPt(AbsMultipleControllerV14DiyTemplate multipleControllerV1) {
        List<byte[]> multipleWriteBytesV1 = MultipleBleBytes.getMultipleWriteBytesV1(multipleControllerV1);
        Mode mode = new Mode();
        SubModeScenes subMode = new SubModeScenes();
        subMode.setEffect(multipleControllerV1.getScenesCode());
        mode.subMode = subMode;
        ModeController modeController = new ModeController(mode);
        /*切换至场景模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV1.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytesV1);
    }

    public static CmdPtReal getPartColorCmdPt(int[] colorSet, boolean gradual, boolean hadFadeController) {
        if (colorSet == null || colorSet.length != 15) return null;
        AbsSingleController[] controllers = SubModeColorV2.makeSubModeColor(colorSet, gradual, hadFadeController);
        if (controllers == null) return null;
        return new CmdPtReal(controllers);
    }

    /**
     * 是否是由其他模式切换到颜色模式 带亮度
     *
     * @return
     */
    public boolean isOtherMode2ColorModePtWithBrightness() {
        if (command.isEmpty()) return false;
        int size = command.size();
        if (size >= 3) {
            byte[] setSubModeColorBytes = Encode.decryByBase64(command.get(0));
            boolean isSetSubModeColor = SubModeColor.isSetMode2Color(setSubModeColorBytes) || SubModeColorV2.isSetMode2Color(setSubModeColorBytes);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isOtherMode2ColorModePtWithBrightness() isSetSubModeColor = " + isSetSubModeColor + " ; setSubModeColorBytes = " + BleUtil.bytesToHexString(setSubModeColorBytes));
            }
            if (!isSetSubModeColor) return false;
            byte[] getPartColorBytes = Encode.decryByBase64(command.get(1));
            boolean isGetPartColor = BulbStringColorController.isReadBulbStringColorController(getPartColorBytes) || BulbStringColorControllerV2.isReadBulbStringColorController(getPartColorBytes);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isOtherMode2ColorModePtWithBrightness() isGetPartColor = " + isGetPartColor + " ; getPartColorBytes = " + BleUtil.bytesToHexString(getPartColorBytes));
            }
            if (!isGetPartColor) return false;
            byte[] getGradualBytes = Encode.decryByBase64(command.get(size - 1));
            boolean isReadGradualController = Gradual4BleWifiController.isReadGradualController(getGradualBytes);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "isOtherMode2ColorModePtWithBrightness() isReadGradualController = " + isReadGradualController + " ; getGradualBytes = " + BleUtil.bytesToHexString(getGradualBytes));
            }
            return isReadGradualController;
        }
        return false;
    }

    /**
     * 点击色条应用整段颜色
     */
    public SubModeColor setPartColor4ColorStrip() {
        if (command.isEmpty()) return null;
        int size = command.size();
        SubModeColor subModeColor = new SubModeColor();
        for (int i = 0; i < size; i++) {
            String encodeStr = command.get(i);
            byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
            boolean setMode2Color = SubModeColor.isSetMode2Color4ColorEffect(setColor4PosBytes);
            if (!setMode2Color) return null;
            /*解析对应位置的颜色*/
            SubModeColor.parsePosColor(setColor4PosBytes, subModeColor);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "setPartColor4ColorStrip commandStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
            }
        }
        return subModeColor;
    }

    /**
     * 点击色条应用整段颜色-带亮度
     *
     * @return
     */
    public SubModeColorV4 setPartColor4ColorStripWithBrightness4V4() {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size >= 2) {
            /*至少一包颜色+一包亮度*/
            SubModeColorV4 subModeColor = new SubModeColorV4();
            for (int i = 0; i < size; i++) {
                String encodeStr = command.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                boolean setMode2Color = SubModeColorV4.isSetMode2Color4ColorEffect(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColorV4.parsePosColorWithBrightness(setColor4PosBytes, subModeColor);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "setPartColor4ColorStripWithBrightness4V4 commandStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
            }
            return subModeColor;
        }
        return null;
    }

    /**
     * 点击色条应用整段颜色-带亮度
     *
     * @return
     */
    public SubModeColorV3 setPartColor4ColorStripWithBrightness4V3() {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size >= 2) {
            /*至少一包颜色+一包亮度*/
            SubModeColorV3 subModeColor = new SubModeColorV3();
            for (int i = 0; i < size; i++) {
                String encodeStr = command.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                boolean setMode2Color = SubModeColorV3.isSetMode2Color4ColorEffect(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColorV3.parsePosColorWithBrightness(setColor4PosBytes, subModeColor);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "setPartColor4ColorStripWithBrightness commandStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
            }
            return subModeColor;
        }
        return null;
    }

    /**
     * 点击色条应用整段颜色-带亮度
     */
    public SubModeColorV2 setPartColor4ColorStripWithBrightness() {
        if (command.isEmpty()) return null;
        int size = command.size();
        if (size >= 2) {
            /*至少一包颜色+一包亮度*/
            SubModeColorV2 subModeColor = new SubModeColorV2();
            for (int i = 0; i < size; i++) {
                String encodeStr = command.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                boolean setMode2Color = SubModeColorV2.isSetMode2Color4ColorEffect(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColorV2.parsePosColorWithBrightness(setColor4PosBytes, subModeColor);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "setPartColor4ColorStripWithBrightness commandStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
            }
            return subModeColor;
        }
        return null;
    }

    public boolean isSetColorStrip() {
        if (command.isEmpty()) return false;
        if (command.size() == 1) return false; /*只有一包时直接走pt*/
        for (int i = 0; i < command.size(); i++) {
            String encodeStr = command.get(i);
            byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
            if (setColor4PosBytes == null) return false;
            if (setColor4PosBytes[2] != BleProtocol.sub_mode_color && setColor4PosBytes[2] != BleProtocol.sub_mode_color_v2)
                return false;
        }
        return true;
    }

    public boolean isSetColorStripWithBrightness() {
        if (command.isEmpty()) return false;
        String encodeStr = command.get(0);
        byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
        if (setColor4PosBytes == null) return false;
        return setColor4PosBytes[2] == BleProtocol.sub_mode_color_v2;
    }

    public static CmdPtReal getMultiNewMusicMode(@NonNull MultipleController4Music multipleController4Music) {
        List<byte[]> multipleWriteBytesV2 = MultipleBleBytes.getMultipleWriteBytesV2(multipleController4Music);
        Mode mode = new Mode();
        mode.subMode = SubModeMusicV3.toNewSubModeMusic(multipleController4Music.getSensitivity(), multipleController4Music.getMusicCode());
        ModeController modeController = new ModeController(mode);
        /*切换至音乐模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV2.add(modeValueBytes);
        return new CmdPtReal(multipleWriteBytesV2);
    }

    public static CmdPtReal getPtRealCmd(List<String> commands) {
        CmdPtReal cmdPtReal = new CmdPtReal();
        cmdPtReal.command.addAll(commands);
        return cmdPtReal;
    }
}