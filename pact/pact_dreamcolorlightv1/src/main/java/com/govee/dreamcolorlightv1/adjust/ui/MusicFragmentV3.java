package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.IMusicEffectStatic;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.OldMusicEffect;
import com.govee.base2light.light.v1.AbsNewMusicFragment;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV3;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.SafeLog;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2021/5/5
 * 新音乐模式$
 */
public class MusicFragmentV3 extends AbsNewMusicFragment {
    private SubModeMusicV3 subModeMusic = new SubModeMusicV3();
    private List<SubMusicMode> subMusicModeList;

    @Override
    protected void init() {
        super.init();
        /*防止fragment视图还未加载就已传递了模式信息；因此在init完成时也需要刷新ui*/
        updateUi();
    }

    @Override
    protected int getCurSubMusicCode() {
        return subModeMusic.getMusicCode();
    }

    @Override
    protected void oldMusicParamsChange(OldMusicEffect oldMusicEffect) {
        SubModeMusicV3 copy = subModeMusic.copy();
        copy.oldMusicEffectChange(oldMusicEffect);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
    }

    @Override
    protected int getSensitivity() {
        return subModeMusic.getSensitivity();
    }

    @Override
    protected void showOldSubMusicEditDialog(SubMusicMode musicMode) {
        int musicCode = musicMode.musicCode;
        if (musicCode == IMusicEffectStatic.single_value_sub_rhythm) {
            showOldMusicEditV1(musicMode, getSensitivity(), subModeMusic.isAutoColor(), subModeMusic.getRgb(), subModeMusic.isDynamic());
        } else {
            showOldMusicEditV0(musicMode, getSensitivity(), subModeMusic.isAutoColor(), subModeMusic.getRgb(), subModeMusic.isDynamic());
        }
    }

    @NonNull
    @Override
    protected List<SubMusicMode> getSupportMusicModes() {
        if (subMusicModeList == null) {
            subMusicModeList = makeRgbicSubMusicModes(
                    IMusicEffectStatic.single_value_sub_energy,
                    IMusicEffectStatic.single_value_sub_rhythm,
                    IMusicEffectStatic.single_value_sub_specturm,
                    IMusicEffectStatic.single_value_sub_scroll
            );
        }
        return subMusicModeList;
    }

    @Override
    protected void onSensitivityChange(int sensitivity) {
        SubModeMusicV3 copy = subModeMusic.copy();
        copy.setSensitivity(sensitivity);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeMusicV3) {
            boolean checkAnalytic4SubModeUse = ((SubModeMusicV3) subMode).checkAnalytic4SubModeUse(getSku());
            this.subModeMusic = (SubModeMusicV3) subMode;
            boolean newMusicCode = Support.isNewMusicCode(subModeMusic.getMusicCode());
            if (!newMusicCode) {
                /*旧音乐模式-需要存储到OldMusicEffect*/
                saveOldMusic(subModeMusic.getSensitivity(), subModeMusic.isAutoColor(), subModeMusic.isDynamic(), subModeMusic.getRgb());
            }
            updateUi();
            if (checkAnalytic4SubModeUse) {
                SafeLog.i(TAG, () -> "updateSubMode() analyticSubModeDetail");
                SubMusicMode subMusicMode = querySubMusicModeByMusicCode(getCurSubMusicCode());
                if (subMusicMode != null) {
                    analyticSubModeDetail(ParamFixedValue.mode_use_music_, subMusicMode.getAnalyticSubModeStr());
                }
            }
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

    private void updateUi() {
        if (!isViewInflateOk()) return;
        /*灵敏度*/
        sensitivityUi(subModeMusic.getSensitivity());
        /*选中的子音乐模式*/
        subMusicUi();
    }

    @Override
    protected int getIcNum() {
        int icNum = subModeMusic.getIcNum();
        if (icNum > 0) return icNum;
        return super.getIcNum();
    }

    @Override
    public ISubMode getCompleteSubMode() {
        SubModeMusicV3 subModeMusic = this.subModeMusic.copy();
        boolean oldSubMusic = !this.subModeMusic.isNewMusic;
        if (oldSubMusic) {
            OldMusicEffect oldMusicEffect = OldMusicEffect.read4OldMusic(getSku(), getDevice(), this.subModeMusic.getMusicCode(), this.subModeMusic.sensitivity);
            subModeMusic.oldMusicEffectChange(oldMusicEffect);
        } else {
            subModeMusic.setSensitivity(this.subModeMusic.getSensitivity());
            subModeMusic.musicEffect = AbsNewMusicEffect.getLocalNewMusicEffect(getSku(), getDevice(), this.subModeMusic.getMusicCode(), getIcNum());
        }
        return subModeMusic;
    }
}