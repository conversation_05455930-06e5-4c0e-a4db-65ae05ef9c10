package com.govee.dreamcolorlightv1.adjust;

import com.govee.ui.R;
import android.content.Context;

import com.govee.base2home.Constant;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.h5.WebActivity;

import com.govee.home.account.config.AccountConfig;
import com.govee.ui.dialog.ConfirmDialog;
import com.ihoment.base2app.infra.SharedPreManager;
import com.ihoment.base2app.util.ResUtil;

/**
 * author  : sinrow
 * time    : 2022/3/23
 * version : 1.0.0
 * desc    :
 */
public class RecommendGraphicManager {
    private static final String KEY_LOCAL = "KEY_LOCAL_RECOMMEND_GRAPHIC";
    private static final String TAG = "RecommendGraphicManager";
    private static RecommendGraphicManager instance;


    public static RecommendGraphicManager getInstance() {
        if (instance == null) {
            instance = new RecommendGraphicManager();
        }
        return instance;
    }


    public void saveRecommendGraphicStatus(String sku, String bleAddress) {
        String account = String.valueOf(AccountConfig.read().getAccountId());
        String key = KEY_LOCAL + account + sku + bleAddress;
        SharedPreManager.getInstance().saveBoolean(key, true);
    }

    public boolean getRecommendGraphicStatus(String sku, String bleAddress) {
        String account = String.valueOf(AccountConfig.read().getAccountId());
        String key = KEY_LOCAL + account + sku + bleAddress;
        return SharedPreManager.getInstance().getBoolean(key, false);
    }

    public void clearGraphicStatus(String sku, String bleAddress) {
        String account = String.valueOf(AccountConfig.read().getAccountId());
        String key = KEY_LOCAL + account + sku + bleAddress;
        SharedPreManager.getInstance().remove(key);
    }

    public void showDialog(Context context, String sku, String bleAddress, boolean showDialog) {
        /* 是否显示提示框 */
        //霓虹灯
        if (showDialog) {
            ConfirmDialog.showConfirmDialog(
                    context, ResUtil.getString(R.string.dreamcolorv1_app_neon_light_recommend_shape_tips), ResUtil.getString(R.string.cancel),
                    ResUtil.getString(R.string.dreamcolorv1_app_read), false, () -> {
                        jumpWebView(context, sku);
                        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.check_recommend_graphics, sku);
                    });
        } else {
            jumpWebView(context, sku);
        }
        saveRecommendGraphicStatus(sku, bleAddress);
    }

    private void jumpWebView(Context context, String sku) {
        String lightGraphicsUrl = Constant.getH5NeonLightGraphicsUrl();
        String url = lightGraphicsUrl + "/" + sku;
        WebActivity.jump2WebAc(context, "", url);
    }

}
