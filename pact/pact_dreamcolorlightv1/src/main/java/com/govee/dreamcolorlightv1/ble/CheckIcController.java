package com.govee.dreamcolorlightv1.ble;


import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsOnlyWriteSingleController;
import com.govee.base2light.light.EventCheckICResult;

/**
 * Create by lins<PERSON><PERSON> on 2019-09-10
 * 612x系列裁剪controller
 */
public class CheckIcController extends AbsOnlyWriteSingleController {
    int type = 0;//0开始检测 1设置裁剪位置 2位置发送完成 3检测取消 4用户确认
    int channel = 0;//设置裁剪位置时灯下标0第一串1第二串
    int icPos = 0;//设置裁剪位置时ic位置

    public CheckIcController(int type, int channel, int icPos) {
        super();
        this.type = type;
        this.channel = channel;
        this.icPos = icPos;
    }


    @Override
    protected boolean writeResult(boolean writeSuc) {
        EventCheckICResult.sendSuc(isWrite(), getCommandType(), getProType(), type);
        return true;
    }

    @Override
    protected byte[] translateWrite() {
        byte[] icPosByte = BleUtil.getSignedBytesFor2(icPos, true);
        return new byte[]{(byte) type, (byte) channel, icPosByte[0], icPosByte[1]};
    }

    @Override
    protected void fail() {
        EventCheckICResult.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.SINGLE_CHECK_IC;
    }
}