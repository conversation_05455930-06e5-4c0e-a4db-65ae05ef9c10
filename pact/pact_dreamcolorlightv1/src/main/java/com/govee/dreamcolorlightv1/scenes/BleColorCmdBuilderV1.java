package com.govee.dreamcolorlightv1.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.ColorModel;
import com.govee.dreamcolorlightv1.pact.Comm;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * ble-颜色控制$
 */
public class BleColorCmdBuilderV1 extends AbsBleCmdBuilderV1<ColorModel> {
    @Override
    public IBleCmd createCmd(ColorModel colorModel) {
        return () -> Comm.makeColorController4BleComm(colorModel.sku, colorModel.goodsType, colorModel.pactType, colorModel.pactCode, colorModel.color).getValue();
    }
}