package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.pact.BleIotInfo;

/**
 * Create by hey on 2023/12/19
 * $
 */
public class BleIotInfoV1 extends BleIotInfo {
    public int sectionNum1;
    public int sectionNum2;

    public BleIotInfoV1(String sku, int goodsType, String device, String spec, String deviceName, String bleName, String bleAddress, String wifiMac, String topic, String versionHard, String secretCode) {
        super(sku, goodsType, device, spec, deviceName, bleName, bleAddress, wifiMac, topic, versionHard, secretCode);
    }

    public BleIotInfoV1(String sku, int goodsType, String device, String spec, String deviceName, String bleName, String bleAddress, String wifiMac, String topic, String versionHard) {
        super(sku, goodsType, device, spec, deviceName, bleName, bleAddress, wifiMac, topic, versionHard);
    }

    public BleIotInfoV1(String sku) {
        super(sku);
    }


}