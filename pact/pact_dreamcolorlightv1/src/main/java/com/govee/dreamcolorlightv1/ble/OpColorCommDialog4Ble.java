package com.govee.dreamcolorlightv1.ble;

import android.content.Context;

import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.light.AbsOpCommDialog4Ble;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by linshicong on 1/28/21
 * op color操作应用$
 */
public class OpColorCommDialog4Ble extends AbsOpCommDialog4Ble {
    private final EffectData.ColorEffect colorEffect;
    int size = 0;
    private final boolean isBk;
    private final boolean isTelinkBle;
    private final boolean isSupportPartBrightness;

    protected OpColorCommDialog4Ble(Context context, String bleAddress, String bleName, @NonNull EffectData.ColorEffect colorEffect, boolean isBk, boolean isSupportPartBrightness, boolean isTelinkBle) {
        super(context, bleAddress, bleName, EFFECT_TYPE_COLOR, -1);
        this.colorEffect = colorEffect;
        this.isBk = isBk;
        this.isTelinkBle = isTelinkBle;
        this.isSupportPartBrightness = isSupportPartBrightness;
    }

    @Override
    protected void bleOping() {
        Mode mode = new Mode();
        boolean singleColor = colorEffect.isSingleColor();
        if (singleColor) {
            mode.subMode = isBk || isSupportPartBrightness ? SubModeColorV2.makeSubModeColor(colorEffect.colorSet[0]) : SubModeColor.makeSubModeColor(colorEffect.colorSet[0]);
            ModeController controller = new ModeController(mode);
            getBle().startController(controller);
        } else {
            AbsSingleController[] controllers;
            if (isBk || isSupportPartBrightness) {
                controllers = SubModeColorV2.makeSubModeColor(colorEffect.colorSet, colorEffect.brightnessSet, colorEffect.isFade(), isTelinkBle, !colorEffect.noFadeController());
            } else {
                controllers = SubModeColor.makeSubModeColor(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
            }
            if (controllers == null) {
                updateResult(false);
            } else {
                size = controllers.length;
                getBle().startController(controllers);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        size--;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result + "---size:" + size);
        }
        if (!result || size <= 1) {
            updateResult(result);
            hide();
        }
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    public static void showDialog(Context context, String bleAddress, String bleName, @NonNull EffectData.ColorEffect colorEffect, boolean isBk, boolean isSupportPartBrightness, boolean isTelinkBle) {
        new OpColorCommDialog4Ble(context, bleAddress, bleName, colorEffect, isBk, isSupportPartBrightness, isTelinkBle).show();
    }
}