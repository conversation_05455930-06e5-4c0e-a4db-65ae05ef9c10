package com.govee.dreamcolorlightv1.iot;

import com.govee.base2home.iot.AbsCmd;

import androidx.annotation.Keep;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-07-23
 * cmd=brightness
 */
@Keep
public class CmdBrightness extends AbsCmd {
    int val;

    public CmdBrightness(int brightness) {
        this.val = brightness;
    }

    @Override
    public int getCmdVersion() {
        return 1;
    }

    @Override
    public String getCmd() {
        return Cmd.brightness;
    }
}