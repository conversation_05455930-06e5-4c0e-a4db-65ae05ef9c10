package com.govee.dreamcolorlightv1.adjust.v2;

import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.IFrameResult;
import com.govee.base2light.pact.IUi;
import com.govee.base2light.pact.IUiResult4BleIot;
import com.govee.base2light.pact.ble.AbsBlePact;
import com.govee.base2light.pact.ble.IPactResult4Ble;
import com.govee.base2light.pact.iot.AbsIotPact;
import com.govee.base2light.pact.iot.IPactResult4Iot;
import com.govee.dreamcolorlightv1.adjust.setting.NewSettingConfig;
import com.govee.dreamcolorlightv1.adjust.setting.SettingAcH61B5;
import com.govee.dreamcolorlightv1.adjust.setting.SettingBleWifiAcV1;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.matter.pact.MtPairProtocol;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2022/5/12
 * 新的针对adjustV3的frame
 */
public class FrameV3 extends FrameV2 {
    public FrameV3(IFrameResult frameResult, BleIotInfo info) {
        super(frameResult, info);
    }

    @Override
    protected List<IUi> getSupportUiList(IUiResult4BleIot iUiResult4BleIot, BleIotInfo bleIotInfo) {
        List<IUi> uiList = new ArrayList<>();
        uiList.add(new UiV4(iUiResult4BleIot, bleIotInfo));
        return uiList;
    }

    @Override
    protected AbsBlePact makeBlePact(IPactResult4Ble iPactResult4Ble) {
        return new BlePactV3(iPactResult4Ble);
    }

    @Override
    protected AbsIotPact makeIotPact(IPactResult4Iot iPactResult4Iot) {
        return new IotPactV3(iPactResult4Iot);
    }

    @Override
    public void toSettingAc(@NonNull AppCompatActivity ac) {
        if (isDestroy()) return;
        if (Support.isH61B5(info.goodsType, info.sku)) {
            SettingAcH61B5.jump2Ac(ac, info);
            return;
        }

        boolean supportNewSettingAc4BleWifi = Support.supportNewSettingAc4BleWifi(info.goodsType, info.sku, info.pactType, info.pactCode);
        LogInfra.Log.i("FrameV3", "toSettingAc() supportNewSettingAc4BleWifi = " + supportNewSettingAc4BleWifi);
        if (supportNewSettingAc4BleWifi) {
            NewSettingConfig.jump2NewSettingAc(ac, info);
            return;
        }
        boolean supportDeviceLock = Support.supportDeviceLock(info.goodsType, info.sku, info.pactType, info.pactCode);
        if (MtPairProtocol.INSTANCE.supportMatter(info.goodsType, info.sku, info.versionSoft, info.versionHard, info.wifiSoftVersion, info.wifiHardVersion)) {
            SettingBleWifiAcV1.jump2SettingBleWifiAcV1(ac,
                    supportDeviceLock,
                    info.goodsType,
                    info.sku,
                    info.device,
                    info.deviceName,
                    info.wifiMac,
                    info.bleAddress,
                    info.versionHard,
                    info.versionSoft,
                    info.wifiHardVersion,
                    info.wifiSoftVersion,
                    info.checkVersion,
                    Support.supportIcFresh(info.sku, info.goodsType, info.pactType, info.pactCode),
                    isUiSuc(),
                    info.topic,
                    info.isLowBlueLightOpen,
                    info.lowBlueLightValue
            );
            return;
        }
        SettingBleWifiAcV1.jump2SettingBleWifiAcV1(ac,
                supportDeviceLock,
                info.goodsType,
                info.sku,
                info.device,
                info.deviceName,
                info.wifiMac,
                info.bleAddress,
                info.versionHard,
                info.versionSoft,
                info.checkVersion,
                Support.supportIcFresh(info.sku, info.goodsType, info.pactType, info.pactCode),
                isUiSuc(),
                info.topic,
                info.isLowBlueLightOpen,
                info.lowBlueLightValue
        );
    }

}