package com.govee.dreamcolorlightv1.adjust.ui;


import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.dreamcolorlightv1.ble.BleProtocol;

/**
 * Create by linshicong on 2020/9/21
 * dj+动感+柔和+灵敏度 mic 模式
 */
public class MicFragmentV1 extends MicFragment {

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }


    @Override
    public byte[] getSubModeTypeArray() {
        return new byte[]{BleProtocol.sub_mode_new_music, BleProtocolConstants.sub_mode_abs_music};
    }
}