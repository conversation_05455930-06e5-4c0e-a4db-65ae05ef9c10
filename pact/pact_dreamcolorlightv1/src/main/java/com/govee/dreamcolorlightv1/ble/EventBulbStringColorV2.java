package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by hey on 2021/2/5
 * $
 */
public class EventBulbStringColorV2 extends AbsC<PERSON>rollerEvent {
    public BulbGroupColorV2 groupColor;

    protected EventBulbStringColorV2(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    public static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventBulbStringColorV2(false, write, commandType, proType));
    }

    public static void sendSuc(boolean write, byte commandType, byte proType, BulbGroupColorV2 groupColor) {
        EventBulbStringColorV2 event = new EventBulbStringColorV2(true, write, commandType, proType);
        event.groupColor = groupColor;
        EventBus.getDefault().post(event);
    }
}