package com.govee.dreamcolorlightv1.pact;

import android.text.TextUtils;
import android.util.SparseArray;

import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.ota.OtaType;
import com.govee.base2home.pact.BleUtil;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.Pact;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.pact.support.OldDreamColorUtil;
import com.govee.base2home.pact.support.OldRgbicBkUtil;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2home.util.NumberUtil;
import com.govee.base2home.util.UuidV1;
import com.govee.base2light.DeviceNameConfig;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyM;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.ScenesRgbIC;
import com.govee.base2light.ble.controller.AbsMultipleControllerV1;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventDynamicApiSupport;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultiDiyGraffitiController;
import com.govee.base2light.ble.controller.MultiDiyGraffitiControllerV3;
import com.govee.base2light.ble.controller.SleepController;
import com.govee.base2light.ble.controller.SleepControllerV2;
import com.govee.base2light.ble.controller.WakeUpController;
import com.govee.base2light.ble.controller.WakeUpControllerV2;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.ble.scenes.RgbIcScenesV1;
import com.govee.base2light.ble.scenes.RgbIcScenesV2;
import com.govee.base2light.ble.scenes.RgbIcScenesV3;
import com.govee.base2light.ble.scenes.ScenesM;
import com.govee.base2light.config.DynamicDomainConfig;
import com.govee.base2light.light.IScenes;
import com.govee.base2light.light.ScenesHint;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.neonlight.recommend.Jump;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.util.NumUtil;
import com.govee.base2light.util.UtilSku;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeColorV3;
import com.govee.dreamcolorlightv1.ble.SubModeColorV6;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.govee.dreamcolorlightv1.pact.ble.DiyOp4Ble;
import com.govee.dreamcolorlightv1.pact.bleiot.DiyOp4BleIot;
import com.govee.ui.R;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2020/3/17
 * 支持pact管理类$
 */
public final class Support {
    private static final String TAG = "Support";

    private Support() {
    }

    /*支持升级到新的功能页面的sku-仅ble*/
    private static final String H6102 = "H6102";
    private static final String H6127 = "H6127";
    private static final String H6161 = "H6161";
    private static final String H6116 = "H6116";
    private static final String H6125 = "H6125";
    private static final String H6126 = "H6126";
    /*支持升级到新的功能页面的sku-ble+wifi*/
    private static final String H6163 = "H6163";
    private static final String H6117 = "H6117";
    /*已配置的goodsType=13的rgbic升级至BK方案*/
    private static final String H6145 = "H6145";
    private static final String H6146 = "H6146";
    private static final String H6147 = "H6147";
    private static final String H6171 = "H6171";
    /*新配置sku*/
    private static final String H6123 = "H6123";
    private static final String H614C = "H614C";
    private static final String H611B = "H611B";
    private static final String H611Z = "H611Z";
    /*新配置ble-支持设备加密*/
    private static final String H617A = "H617A";
    private static final String H617B = "H617B";
    private static final String H617C = "H617C";
    /*新配置ble+wifi-支持设备加密*/
    private static final String H618A = "H618A";
    private static final String H618B = "H618B";
    private static final String H618C = "H618C";
    private static final String H619A = "H619A";
    private static final String H619B = "H619B";
    private static final String H619C = "H619C";
    private static final String H619D = "H619D";
    private static final String H619E = "H619E";
    private static final String H61A5 = "H61A5";
    private static final String H619Z = "H619Z";
    private static final String H6172 = "H6172";
    private static final String H6173 = "H6173";
    private static final String H618F = "H618F";//同H618E
    private static final String H617E = "H617E";//
    private static final String H617F = "H617F";//
    private static final String H61A0 = "H61A0";
    private static final String H61A1 = "H61A1";
    private static final String H61A2 = "H61A2";
    private static final String H61A3 = "H61A3";
    private static final String H61B2 = "H61B2";

    /*ble+wifi的新RGBIC-高端灯带-支持音乐盛宴*/
    public static final String H61E1 = "H61E1";
    public static final String H61E0 = "H61E0";
    private static final String H61B1 = "H61B1";
    private static final String H61BC = "H61BC";
    private static final String H61BE = "H61BE";
    private static final String H61BA = "H61BA";
    private static final String H61B5 = "H61B5";
    private static final String H80B5 = "H80B5";

    private static final String H6168 = "H6168";
    /*ble+wifi-不支持设备加密 */
    private static final String H61A8 = "H61A8";
    private static final String H61A9 = "H61A9";

    private static final String H70A1 = "H70A1";
    private static final String H70A2 = "H70A2";
    private static final String H70A3 = "H70A3";
    private static final String H6176 = "H6176";
    private static final String H6175 = "H6175";

    public static final List<Protocol> supportProtocolsV11 = new ArrayList<>();/* H612x */
    /**
     * 设备列表的goodsType集合-ble+iot
     */
    public static final int[] deviceItemGoodsTypes4BleIot = {
            GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3,
            GoodsType.GOODS_TYPE_VALUE_H6169,
            GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC,
            GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3,
            GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA,
            GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT,
            GoodsType.GOODS_TYPE_VALUE_H61A9,
            GoodsType.GOODS_TYPE_VALUE_H616C,
            GoodsType.GOODS_TYPE_VALUE_H612x,
    };
    private static final String H612A = "H612A";
    private static final String H612B = "H612B";
    private static final String H612C = "H612C";
    private static final String H612D = "H612D";

    public static final List<Protocol> supportProtocolsV10 = new ArrayList<>();/* H616C/D/E */
    private static final String H612E = "H612E";
    private static final String H612F = "H612F";
    /**
     * 效果操作op-针对ble+wifi
     */
    public static final int[] effect4OpBleWifiGoodsTypes = {
            GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET,
            GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3,
            GoodsType.GOODS_TYPE_VALUE_H6169,
            GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC,
            GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3,
            GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA,
            GoodsType.GOODS_TYPE_VALUE_H61A9,
            GoodsType.GOODS_TYPE_VALUE_H616C,
            GoodsType.GOODS_TYPE_VALUE_H612x,
    };

    private static final String first_support_limit_h6102_version_hard = "1.00.02";

    private static final String[] supportUpdateSkuSet4Ble = {H6102, H6127, H6161,
            H6116,
            H6125,
            H6126,
    };

    private static final String[] supportUpdateSkuSet4BleWifi = {
            H6163,
            H6117,
    };
    public static final String[] supportBleV1GoodsSet = {
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H6169),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H61A9),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H616C),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H612x),
    };

    public static boolean supportWifi(int goodsType, String sku, String versionHard) {
        return getSupportRhythmGoodsType().contains(goodsType) ||
                OldDreamColorUtil.checkSupportNewDreamColorFuc4BleWifi(sku, versionHard);
    }

    public static boolean supportGoodsType(int goodsType) {
        for (int i : effect4OpBleGoodsTypes) {
            if (i == goodsType) return true;
        }
        for (int i : effect4OpBleWifiGoodsTypes) {
            if (i == goodsType) return true;
        }
        return false;
    }

    private static final List<String> supportUpdateSkuArray4Ble = Arrays.asList(supportUpdateSkuSet4Ble);
    private static final List<String> supportUpdateSkuArray4BleWifi = Arrays.asList(supportUpdateSkuSet4BleWifi);

    public static List<Protocol> supportProtocolsV1 = new ArrayList<>();/*ble*/
    public static List<Protocol> supportProtocolsV2 = new ArrayList<>();/*ble+wifi*/

    public static List<Protocol> supportProtocolsV3 = new ArrayList<>();/*ble-bk*/
    public static List<Protocol> supportProtocolsV4 = new ArrayList<>();/*ble-限流bk*/
    public static List<Protocol> supportProtocolsV5 = new ArrayList<>();/*ble+wifi-bk*/
    public static List<Protocol> supportProtocolsV5_1 = new ArrayList<>();/*ble+wifi-frk-不支持设备校验*/

    public static List<Protocol> supportProtocolsV6 = new ArrayList<>();/*ble-支持设备校验*/
    public static List<Protocol> supportProtocolsV6_1 = new ArrayList<>();/*ble-支持设备校验-H6171迭代款*/

    public static List<Protocol> supportProtocolsV7 = new ArrayList<>();/*ble+wifi-支持设备校验*/

    public static final List<Protocol> supportProtocolsV8 = new ArrayList<>();/*ble+wifi的支持主盛宴的高端rgbic灯带*/
    public static final List<Protocol> supportProtocolsV8_1 = new ArrayList<>();/*ble+wifi的支持主盛宴的高端rgbic灯带-支持ic刷新*/
    public static final List<Protocol> supportProtocolsV8_2 = new ArrayList<>();/*ble+wifi的支持主盛宴的高端rgbic灯带-支持睡眠唤醒带颜色*/
    public static final List<Protocol> supportProtocolsV8_2_1 = new ArrayList<>();/*ble+wifi的支持主盛宴的高端rgbic灯带-支持睡眠唤醒带颜色-无渐变*/
    public static final List<Protocol> supportProtocolsV8_3 = new ArrayList<>();/*ble+wifi的支持主盛宴的高端rgbic灯带-支持睡眠唤醒带颜色-支持场景涂鸦对称*/
    public static final List<Protocol> supportProtocolsV8_4 = new ArrayList<>();/*ble+wifi的支持主盛宴的高端rgbic灯带-支持睡眠唤醒带颜色-10段*/

    public static final List<Protocol> supportProtocolsV9 = new ArrayList<>();/* H61A9 */
    public static final List<Protocol> supportProtocolsH80B5 = new ArrayList<>();//H61B6 迭代款，支持蓝牙加密
    public static final String[] supportBleWifiV1GoodsSet = {
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H6169),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H61A9),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H616C),
    };

    private static final List<Integer> supportProtocolsV1GoodsTypeArray = new ArrayList<>();
    private static final List<Integer> supportProtocolsV2GoodsTypeArray = new ArrayList<>();

    private static final SparseArray<IScenes> scenesMap = new SparseArray<>();
    private static final String H616C = "H616C";

    /**
     * 设备列表的goodsType集合-ble
     */
    public static final int[] deviceItemGoodsTypes4Ble = {
            GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1,
            GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1,
    };
    private static final String H616D = "H616D";

    /**
     * 效果操作op-针对ble
     */
    public static final int[] effect4OpBleGoodsTypes = {
            GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1,
            GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET,
            GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1,
    };
    private static final String H616E = "H616E";

    private static final List<Integer> bleWifiGoodsTypeArray = NumberUtil.toListByIntArray(effect4OpBleWifiGoodsTypes);

    public static boolean isBleWifiDevice(int goodsType) {
        return bleWifiGoodsTypeArray.contains(goodsType);
    }

    public static boolean supportPactV2(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (supportProtocolsV2GoodsTypeArray.contains(goodsType)) {
            for (Protocol pro : supportProtocolsV2) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
            /*ble+wifi-BK*/
            for (Protocol pro : supportProtocolsV5) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
            /*ble+wifi-FRk*/
            for (Protocol pro : supportProtocolsV5_1) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
            /* H61A9 */
            for (Protocol pro : supportProtocolsV9) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    public static boolean supportPactV3(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        /*H6171迭代款*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1) {
            for (Protocol pro : supportProtocolsV6_1) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }

        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1
        ) {
            for (Protocol pro : supportProtocolsV6) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    /**
     * 支持昼夜节律的goodsType（必须是Wi-Fi设备）
     *
     * @return
     */
    public static List<Integer> getSupportRhythmGoodsType() {
        List<Integer> supports = new ArrayList<>();
        supports.add(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1);
        supports.add(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1);
        supports.add(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2);
        supports.add(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3);
        supports.add(GoodsType.GOODS_TYPE_VALUE_H6169);
        supports.add(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET);
        supports.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END);
        supports.add(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE);
        supports.add(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC);
        supports.add(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3);
        supports.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175);
        supports.add(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA);
        supports.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT);
        supports.add(GoodsType.GOODS_TYPE_VALUE_H61A9);
        supports.add(GoodsType.GOODS_TYPE_VALUE_H616C);
        supports.add(GoodsType.GOODS_TYPE_VALUE_H612x);
        return supports;
    }

    /**
     * 是否是旧的telink的ble设备
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return
     */
    public static boolean isTelinkBle(String sku, int goodsType, int pactType, int pactCode) {
        if (goodsType == 0) {
            int newGoodsType = OldDreamColorUtil.parseOldDreamDeviceNewGoodsType(sku);
            if (newGoodsType != -1) {
                goodsType = newGoodsType;
            }
        }
        if (supportProtocolsV1GoodsTypeArray.contains(goodsType)) {
            for (Protocol pro : supportProtocolsV1) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    public static boolean supportPactV1(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        /*针对此goodsType,frk方案需要补充支持条件*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1) {
            /*ble-限流-bk*/
            for (Protocol pro : supportProtocolsV4) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        if (supportProtocolsV1GoodsTypeArray.contains(goodsType)) {
            for (Protocol pro : supportProtocolsV1) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
            /*ble-bk*/
            for (Protocol pro : supportProtocolsV3) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
            /*ble-限流-bk*/
            for (Protocol pro : supportProtocolsV4) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    /**
     * 是否支持rgbic的高端灯带-支持盛宴主设备
     *
     * @param goodsType
     * @param protocol
     * @return
     */
    public static boolean supportPact4RgbicHighEnd(int goodsType, @Nullable Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END) {
            for (Protocol pro : supportProtocolsV8) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
            for (Protocol pro : supportProtocolsV8_1) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
            for (Protocol pro : supportProtocolsH80B5) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC || goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3) {
            for (Protocol pro : supportProtocolsV8_2) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175) {
            for (Protocol pro : supportProtocolsV8_4) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA) {
            for (Protocol pro : supportProtocolsV8_2_1) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE) {
            for (Protocol pro : supportProtocolsV8_3) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    public static void addSupportPact() {
        Pact pact = Pact.getInstance;
        /*ble幻彩*/
        Protocol protocol1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_V1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_V1);
        supportProtocolsV1.add(protocol1);
        Protocol protocol1_2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_V1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_V2);
        supportProtocolsV1.add(protocol1_2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1, protocol1);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1, protocol1_2);
        /*ble幻彩-BK*/
        Protocol protocol3_bk = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_BK_V1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_BK_V1);
        Protocol protocol3_bk_frk = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_BK_V1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_BK_V1_FRK);

        supportProtocolsV3.add(protocol3_bk);
        supportProtocolsV3.add(protocol3_bk_frk);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1, protocol3_bk);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1, protocol3_bk_frk);
        supportProtocolsV1GoodsTypeArray.add(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1);

        /*ble幻彩+支持限流*/
        Protocol protocol2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V1);
        supportProtocolsV1.add(protocol2);
        Protocol protocol2_2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V2);
        supportProtocolsV1.add(protocol2_2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1, protocol2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1, protocol2_2);
        /*ble幻彩+支持限流-BK*/
        Protocol protocol4_bk = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_BK_V1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_BK_V1);
        Protocol protocol4_bk_frk = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_BK_V1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_BK_V1_FRK);
        supportProtocolsV4.add(protocol4_bk);
        supportProtocolsV4.add(protocol4_bk_frk);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1, protocol4_bk);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1, protocol4_bk_frk);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1, protocol4_bk_frk);
        supportProtocolsV1GoodsTypeArray.add(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1);

        /*ble+wifi幻彩*/
        Protocol protocol3 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_WIFI_1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_WIFI_1);
        supportProtocolsV2.add(protocol3);
        Protocol protocol3_2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_WIFI_1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_WIFI_2);
        supportProtocolsV2.add(protocol3_2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1, protocol3);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1, protocol3_2);
        /*ble+wifi幻彩-BK*/
        Protocol protocol5_bk = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_WIFI_BK_1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_WIFI_BK_1);
        supportProtocolsV5.add(protocol5_bk);
        Protocol protocol5_frk = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_WIFI_BK_1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_WIFI_FRK_1);
        supportProtocolsV5_1.add(protocol5_frk);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1, protocol5_bk);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1, protocol5_frk);
        supportProtocolsV2GoodsTypeArray.add(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1);

        /*ble-支持设备校验*/
        Protocol protocol6 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_RGBIC_BLE_SECRET, GoodsType.PACT_CODE_4_RGBIC_BLE_SECRET);
        Protocol protocol6_1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_RGBIC_BLE_SECRET, GoodsType.PACT_CODE_4_RGBIC_BLE_SECRET);
        Protocol protocol6_2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_BK_V1, GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_BK_V1_FRK_V1);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1, protocol6_2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET, protocol6);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1, protocol6_1);
        supportProtocolsV6.add(protocol6);
        supportProtocolsV6.add(protocol6_1);
        supportProtocolsV6_1.add(protocol6_2);
        /*ble+wifi-支持设备校验*/
        Protocol protocol7 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET, GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET);
        Protocol protocol7_1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET, GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET);
        Protocol protocol7_2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET, GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET);
        Protocol protocol7_3 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET, GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET_FRK);
        Protocol protocol7_4 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET, GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET);
        Protocol protocol7_5 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_4_BLE_WIFI_RGBIC_TV_LIGHT, GoodsType.PACT_CODE_VALUE_4_BLE_WIFI_RGBIC_TV_LIGHT);
        Protocol protocol7_6 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET_V3, GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET_FRK);
        Protocol protocol7_7 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_H6169, GoodsType.PACT_CODE_VALUE_H6169);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET, protocol7);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET, protocol7_3);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET, protocol7_6);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1, protocol7_1);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2, protocol7_2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3, protocol7_4);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H6169, protocol7_7);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT, protocol7_5);
        supportProtocolsV7.add(protocol7);
        supportProtocolsV7.add(protocol7_1);
        supportProtocolsV7.add(protocol7_2);
        supportProtocolsV7.add(protocol7_3);
        supportProtocolsV7.add(protocol7_4);
        supportProtocolsV7.add(protocol7_5);
        supportProtocolsV7.add(protocol7_6);
        supportProtocolsV7.add(protocol7_7);

        /*ble+wifi的高端rgbic灯带-支持主盛宴设备*/
        Protocol protocol8 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END, GoodsType.PACT_CODE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END, protocol8);
        supportProtocolsV8.add(protocol8);
        /*支持H61E1刷新ic数逻辑*/
        Protocol protocol8_1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END, GoodsType.PACT_CODE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END_V2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END, protocol8_1);
        supportProtocolsV8_1.add(protocol8_1);

        Protocol protocolH80b5 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END_V2, GoodsType.PACT_CODE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END, protocolH80b5);
        supportProtocolsH80B5.add(protocolH80b5);

        /*支持H61BC-睡眠唤醒支持设置颜色*/
        Protocol protocol8_2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_RGBIC_LIGHT_H61BC, GoodsType.PACT_CODE_RGBIC_LIGHT_H61BC);
        Protocol protocol8_2_ENCRYPT = GoodsType.beProtocol(GoodsType.PACT_TYPE_RGBIC_LIGHT_H61BC_ENCRYPT, GoodsType.PACT_CODE_RGBIC_LIGHT_H61BC);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC, protocol8_2_ENCRYPT);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC, protocol8_2);
        supportProtocolsV8_2.add(protocol8_2_ENCRYPT);
        supportProtocolsV8_2.add(protocol8_2);
        Protocol protocol8_2_v1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_H70A1_A2_A3, GoodsType.PACT_CODE_H70A1_A2_A3);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3, protocol8_2_v1);
        supportProtocolsV8_2.add(protocol8_2_v1);
        //70A123蓝牙加密
        Protocol protocol8_2_v2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_H70A1_A2_A3_ENCRYPT, GoodsType.PACT_CODE_H70A1_A2_A3_ENCRYPT);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3, protocol8_2_v2);
        supportProtocolsV8_2.add(protocol8_2_v2);
        Protocol protocol8_2_1_ENCRYPT = GoodsType.beProtocol(GoodsType.PACT_TYPE_RGBIC_LIGHT_H61BA_ENCRYPT, GoodsType.PACT_CODE_RGBIC_LIGHT_H61BA);
        Protocol protocol8_2_1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_RGBIC_LIGHT_H61BA, GoodsType.PACT_CODE_RGBIC_LIGHT_H61BA);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA, protocol8_2_1_ENCRYPT);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA, protocol8_2_1);
        supportProtocolsV8_2_1.add(protocol8_2_ENCRYPT);
        supportProtocolsV8_2_1.add(protocol8_2_1);
        Protocol protocol8_4 = GoodsType.beProtocol(GoodsType.PACT_TYPE_H6175, GoodsType.PACT_CODE_H6175);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175, protocol8_2);
        supportProtocolsV8_4.add(protocol8_4);
        /*支持H618E-睡眠唤醒支持设置颜色/20段颜色模式/场景模式支持涂鸦对称*/
        Protocol protocol8_3_ENCRYPT = GoodsType.beProtocol(GoodsType.PACT_TYPE_RGBIC_LIGHT_H61BE_ENCRYPT, GoodsType.PACT_CODE_RGBIC_LIGHT_H61BE);
        Protocol protocol8_3 = GoodsType.beProtocol(GoodsType.PACT_TYPE_RGBIC_LIGHT_H61BE, GoodsType.PACT_CODE_RGBIC_LIGHT_H61BE);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE, protocol8_3_ENCRYPT);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE, protocol8_3);
        supportProtocolsV8_3.add(protocol8_3_ENCRYPT);
        supportProtocolsV8_3.add(protocol8_3);

        /* H61A9 */
        Protocol protocol9 = GoodsType.beProtocol(GoodsType.PACT_TYPE_H61A9, GoodsType.PACT_CODE_H61A9);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H61A9, protocol9);
        supportProtocolsV9.add(protocol9);
        supportProtocolsV2GoodsTypeArray.add(GoodsType.GOODS_TYPE_VALUE_H61A9);
        //H616C/D/E
        Protocol protocol10 = GoodsType.beProtocol(GoodsType.PACT_TYPE_H616C, GoodsType.PACT_CODE_H616C);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H616C, protocol10);
        supportProtocolsV10.add(protocol10);
        Protocol protocol_encrypt = GoodsType.beProtocol(GoodsType.PACT_TYPE_H616C_ENCRYPT, GoodsType.PACT_CODE_H616C);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H616C, protocol_encrypt);
        supportProtocolsV10.add(protocol_encrypt);
        //H612x
        Protocol protocol11 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_H612x, GoodsType.PACT_CODE_VALUE_H612x);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H612x, protocol11);
        supportProtocolsV11.add(protocol11);
        Protocol protocol12 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_H612x, GoodsType.PACT_CODE_VALUE_H612x_2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H612x, protocol12);
        supportProtocolsV11.add(protocol12);
        Protocol protocol13 = GoodsType.beProtocol(GoodsType.PACT_TYPE_VALUE_H612x_encryption, GoodsType.PACT_CODE_VALUE_H612x_encryption);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H612x, protocol13);
        supportProtocolsV11.add(protocol13);

        /*初始化支持的默认sku图*/
        ThemeM.getInstance.addDefSkuRes(H6102, R.mipmap.add_list_type_device_6102);
        ThemeM.getInstance.addDefSkuRes(H6127, R.mipmap.add_list_type_device_6127);
        ThemeM.getInstance.addDefSkuRes(H6161, R.mipmap.add_list_type_device_6161);
        ThemeM.getInstance.addDefSkuRes(H6116, R.mipmap.add_list_type_device_6116);
        ThemeM.getInstance.addDefSkuRes(H6125, R.mipmap.add_list_type_device_6125);
        ThemeM.getInstance.addDefSkuRes(H6126, R.mipmap.add_list_type_device_6126);

        ThemeM.getInstance.addDefSkuRes(H6163, R.mipmap.add_list_type_device_6163);
        ThemeM.getInstance.addDefSkuRes(H6117, R.mipmap.add_list_type_device_6117);

        ThemeM.getInstance.addDefSkuRes(H6123, com.govee.dreamcolorlightv1.R.mipmap.add_list_type_device_6123);
        ThemeM.getInstance.addDefSkuRes(H614C, com.govee.dreamcolorlightv1.R.mipmap.add_list_type_device_614c);
        ThemeM.getInstance.addDefSkuRes(H611B, com.govee.dreamcolorlightv1.R.mipmap.add_list_type_device_611b);
        ThemeM.getInstance.addDefSkuRes(H611Z, com.govee.dreamcolorlightv1.R.mipmap.add_list_type_device_611z);
        ThemeM.getInstance.addDefSkuRes(H617A, com.govee.dreamcolorlightv1.R.mipmap.add_list_type_device_617ac);
        ThemeM.getInstance.addDefSkuRes(H617C, com.govee.dreamcolorlightv1.R.mipmap.add_list_type_device_617ac);
        ThemeM.getInstance.addDefSkuRes(H618A, com.govee.dreamcolorlightv1.R.mipmap.add_list_type_device_618ac);
        ThemeM.getInstance.addDefSkuRes(H618C, com.govee.dreamcolorlightv1.R.mipmap.add_list_type_device_618ac);
        ThemeM.getInstance.addDefSkuRes(H619A, com.govee.dreamcolorlightv1.R.mipmap.add_list_type_device_619ac);
        ThemeM.getInstance.addDefSkuRes(H619C, com.govee.dreamcolorlightv1.R.mipmap.add_list_type_device_619ac);

        /*加入固定支持旧幻彩diyOp*/
        DiyM.getInstance.addFixedDiyOp(DiyOp4Ble.op);
        DiyM.getInstance.addFixedDiyOp(DiyOp4BleIot.op);
        /*加入支持的场景版本*/
        scenesMap.append(0, new ScenesV0());
        scenesMap.append(1, RgbIcScenesV1.scenesV1);
        scenesMap.append(2, RgbIcScenesV2.scenesV2);
        scenesMap.append(3, RgbIcScenesV3.scenesV3);
    }

    public static boolean supportPactV4(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H6169
        ) {
            for (Protocol pro : supportProtocolsV7) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H616C) {
            for (Protocol pro : supportProtocolsV10) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H612x) {
            for (Protocol pro : supportProtocolsV11) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    public static final String[] supportPairCheckGoodsType = {
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H6169),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA),
    };

    /**
     * 获取默认的头图信息-ble
     *
     * @param goodsType
     * @param sku
     * @return
     */
    public static int[] getDefHeaderRes4Ble(int goodsType, String sku) {
        if (H6102.equals(sku)) {
            return new int[]{R.mipmap.new_light_title_6104_on, R.mipmap.new_light_title_6104_off, R.mipmap.new_light_title_6104_off};
        }
        if (H6127.equals(sku)) {
            return new int[]{R.mipmap.new_light_title_home_on, R.mipmap.new_light_title_home_off, R.mipmap.new_light_title_home_off};
        }
        if (H6116.equals(sku)) {
            return new int[]{R.mipmap.new_light_title_home_on, R.mipmap.new_light_title_home_off, R.mipmap.new_light_title_home_off};
        }
        if (H6161.equals(sku)) {
            return new int[]{R.mipmap.new_light_title_home_on, R.mipmap.new_light_title_home_off, R.mipmap.new_light_title_home_off};
        }
        if (H6125.equals(sku)) {
            return new int[]{R.mipmap.new_light_title_home_on, R.mipmap.new_light_title_home_off, R.mipmap.new_light_title_home_off};
        }
        if (H6126.equals(sku)) {
            return new int[]{R.mipmap.new_light_title_home_on, R.mipmap.new_light_title_home_off, R.mipmap.new_light_title_home_off};
        }
        if (H617A.equals(sku) || H617C.equals(sku)) {
            return new int[]{R.mipmap.new_light_title_home_on, R.mipmap.new_light_title_home_off, R.mipmap.new_light_title_home_off};
        }
        if (H618A.equals(sku) || H618C.equals(sku) || H619A.equals(sku) || H619C.equals(sku)) {
            return new int[]{R.mipmap.new_light_title_6160_on, R.mipmap.new_light_title_6160_off, R.mipmap.new_light_title_6160_off};
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1) {
            return new int[]{R.mipmap.new_light_title_home_on, R.mipmap.new_light_title_home_off, R.mipmap.new_light_title_home_off};
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1) {
            return new int[]{R.mipmap.new_light_title_6104_on, R.mipmap.new_light_title_6104_off, R.mipmap.new_light_title_6104_off};
        }
        return null;
    }

    /**
     * 获取默认的头图信息-ble+wifi
     *
     * @param goodsType
     * @param sku
     * @return
     */
    public static int[] getDefHeaderRes4BleWifi(int goodsType, String sku) {
        if (H6163.equals(sku)) {
            return new int[]{R.mipmap.new_light_title_6160_on, R.mipmap.new_light_title_6160_off, R.mipmap.new_light_title_6160_off};
        }
        if (H6117.equals(sku)) {
            return new int[]{R.mipmap.new_light_title_6160_on, R.mipmap.new_light_title_6160_off, R.mipmap.new_light_title_6160_off};
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1) {
            return new int[]{R.mipmap.new_light_title_6160_on, R.mipmap.new_light_title_6160_off, R.mipmap.new_light_title_6160_off};
        }
        return null;
    }

    /**
     * 获取最大的灯带段数
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return
     */
    public static int getBulbStringMaxNum(int goodsType, int pactType, int pactCode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getBulbStringMaxNum() goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode);
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2) return 10;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175) return 10;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3) return 12;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE) return 20;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H61A9) return 18;
        return 15;
    }

    /**
     * 获取最大的灯带段数
     *
     * @param goodsType
     * @param sku
     * @param pactType
     * @param pactCode
     * @param icSubList
     * @return
     */
    public static int getBulbStringMaxNum(int goodsType, String sku, int pactType, int pactCode, List<Integer> icSubList) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getBulbStringMaxNum() goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode + " icSubList = " + icSubList.toString());
        }
        if (goodsTypeH612x(goodsType)) {
            if (icSubList.size() >= 2) {
                int[] numArray = get612xSectionNum(sku, icSubList.get(0), icSubList.get(1));
                return numArray[0] + numArray[1];
            } else {
                switch (sku) {
                    case "H612A":
                        return 10;
                    case "H612B":
                        return 10;
                    case "H612C":
                        return 10;
                    case "H612D":
                        return 20;
                    case "H612E":
                        return 20;
                    default:
                        return 6;
                }
            }
        }
        return getBulbStringMaxNum(goodsType, pactType, pactCode);
    }

    /**
     * 是否是10段颜色的颜色模式
     *
     * @param goodsType
     * @return
     */
    public static boolean isSubModeColorPiece10(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                ;
    }

    /**
     * 是否是12段颜色的颜色模式
     *
     * @param goodsType
     * @return
     */
    public static boolean isSubModeColorPiece12(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3;
    }

    /**
     * 是否是20段颜色的颜色模式
     *
     * @param goodsType
     * @return
     */
    public static boolean isSubModeColorPiece20(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE;
    }

    /**
     * 是否是18段颜色的颜色模式
     * H61A9
     */
    public static boolean isSubModeColorPiece18(int goodsType) {
        return isGoodsTypeH61A9(goodsType);
    }

    /**
     * 获取对应新颜色模式的版本
     *
     * @param goodsType
     * @return
     */
    public static int getModeColorVersion4New(int goodsType, String sku) {
        if (goodsTypeH612x(goodsType)) {
            if (supportChangePieces20(goodsType, sku)) return 7;
            return 8;
        }
        if (supportFactor20Pieces(goodsType)) return 6;
        boolean factor12PiecesNoGradual = supportFactor12PiecesNoGradual(goodsType);
        if (factor12PiecesNoGradual) return 5;
        boolean supportFactor10Pieces = supportFactor10Pieces(goodsType, sku);
        if (supportFactor10Pieces) return 4;
        boolean noSupportGradual = noSupportGradual(goodsType);
        if (noSupportGradual) return 3;
        return 2;
    }

    /**
     * 是否需要检测头图更新
     *
     * @param sku
     * @return
     */
    public static boolean needCheckHeaderRes4Ble(String sku) {
        return !supportUpdateSkuArray4Ble.contains(sku);
    }

    public static boolean needCheckHeaderRes4BleWifi(String sku) {
        return !supportUpdateSkuArray4BleWifi.contains(sku);
    }

    /**
     * 是否支持限流指令逻辑
     *
     * @param goodsType
     * @return
     */
    public static boolean supportLimit(int goodsType) {
        return GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1 == goodsType;
    }

    public static boolean supportLimit4SkuHardVersion(String sku, String versionHard) {
        if (H6102.equals(sku)) {
            int curVersionInt = UtilSku.parseVersion(versionHard);
            int compareVersionInt = UtilSku.parseVersion(first_support_limit_h6102_version_hard);
            return curVersionInt >= compareVersionInt;
        }
        return true;
    }

    /**
     * 获取wifi输入限制
     *
     * @return [2];[0]ssidInputLimit;[1]passwordInputLimit
     */
    public static int[] getWifiInputLimit() {
        return new int[]{32, 64};
    }

    /**
     * 检测给到wifi设备的当前服务器域名版本值
     *
     * @return
     */
    public static int check2WifiDeviceRunModeVersion() {
        return 0;/*因之前已经出去了旧款sku因此默认使用v0*/
    }

    public static boolean supportScenesBrightnessOp(int pactType, int pactCode, int goodsType, int effect, String sku) {
        boolean bkProtocol = isBkProtocol(sku, goodsType, pactType, pactCode);
        if (bkProtocol) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H61A9
        ) {
            /*日出，日落效果，当前goodsType下，仅之前已发版的sku支持调节亮度*/
            if (effect == BleProtocol.value_sub_mode_scenes_sunset || effect == BleProtocol.value_sub_mode_scenes_gm) {
                return H6102.equals(sku)
                        || H6127.equals(sku)
                        || H6161.equals(sku)
                        || H6116.equals(sku)
                        || H6125.equals(sku)
                        || H6126.equals(sku)
                        || H6163.equals(sku)
                        || H6117.equals(sku);
            }
        }
        /*默认场景支持调节亮度*/
        return true;
    }

    private static final String new_soft_version_v1_ble = "1.04.00";/*支持rgb+rgbIC场景的固件版本*/

    private static final String new_hard_version_v1_rgbic_h6163_h6117 = "1.00.02";
    private static final String new_wifi_hard_version_v1_rgbic_h6163_h6117 = "1.00.02";
    private static final String new_wifi_soft_version_v1_rgbic_h6163_h6117 = "1.02.05";

    /**
     * 升级版本-V1版本
     *
     * @param goodsType
     * @param pactCode
     * @param softVersion
     * @return
     */
    public static int newVersion4Ble(String sku, int goodsType, int pactCode, String softVersion, String hardVersion) {
        return newVersion(0, sku, goodsType, pactCode, softVersion, hardVersion, null, null);
    }

    /**
     * 升级版本-v1版本
     *
     * @param opType      通信op类型;0:ble;1:wifi
     * @param sku
     * @param goodsType
     * @param pactCode
     * @param softVersion
     * @param hardVersion
     * @return
     */
    public static int newVersion(int opType, String sku, int goodsType, int pactCode, String softVersion, String hardVersion, String wifiSoftVersion, String wifiHardVersion) {
        if (H6163.equals(sku) || H6117.equals(sku)) {
            if (opType == 0) {
                /*蓝牙控制-蓝牙版本判断*/
                int curSoftVersionInt = UtilSku.parseVersion(softVersion);
                int curHardVersionInt = UtilSku.parseVersion(hardVersion);
                int compareHardVersionInt = UtilSku.parseVersion(new_hard_version_v1_rgbic_h6163_h6117);
                int compareSoftVersionInt = UtilSku.parseVersion(new_soft_version_v1_ble);
                if (curSoftVersionInt >= compareSoftVersionInt && curHardVersionInt >= compareHardVersionInt) {
                    return 1;
                }
            }
            if (opType == 1) {
                /*wifi控制-Wi-Fi版本判断*/
                int curWifiSoftVersionInt = UtilSku.parseVersion(wifiSoftVersion);
                int curWifiHardVersionInt = UtilSku.parseVersion(wifiHardVersion);
                int compareWifiSoftVersionInt = UtilSku.parseVersion(new_wifi_soft_version_v1_rgbic_h6163_h6117);
                int compareWifiHardVersionInt = UtilSku.parseVersion(new_wifi_hard_version_v1_rgbic_h6163_h6117);
                if (curWifiSoftVersionInt >= compareWifiSoftVersionInt && curWifiHardVersionInt >= compareWifiHardVersionInt) {
                    return 1;
                }
            }
            return 0;
        }
        if (goodsType == 0) {
            int newGoodsType = OldDreamColorUtil.parseOldDreamDeviceNewGoodsType(sku);
            if (newGoodsType != -1) {
                goodsType = newGoodsType;
            }
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1) {
            if (pactCode == GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_V2) return 1;
        }

        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1) {
            if (pactCode == GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V2) return 1;
        }

        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1) {
            if (pactCode == GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_WIFI_2) return 1;
        }

        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1
        ) {
            int curVersionInt = UtilSku.parseVersion(softVersion);
            int compareVersionInt = UtilSku.parseVersion(new_soft_version_v1_ble);
            return curVersionInt >= compareVersionInt ? 1 : 0;
        }
        return 1;
    }

    /**
     * 判断是不是场景多包
     *
     * @param sku        sku
     * @param device
     * @param mode       mode
     * @param newVersion newVersion
     * @return AbsMultipleControllerV14Scenes
     */
    public static AbsMultipleControllerV14Scenes is2NewScenesMode(String sku, String device, Mode mode, int newVersion) {
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenes) {
            if (newVersion == 1) {
                int effect = ((SubModeScenes) subMode).getEffect();
                CategoryV1.SceneV1 scene = ScenesM.getInstance.getSceneV1(sku, device, effect);
                if (scene != null) {
                    return ScenesOp.parseSceneV1(sku, scene, ScenesOp.scene_type_rgb, ScenesOp.scene_type_rgbic);
                }
                return ScenesOp.parseEffect(sku, effect, 1, 2);
            }
        }
        return null;
    }

    /**
     * 获取对应版本支持的场景
     *
     * @param version
     * @return
     */
    public static IScenes getScenes(int version) {
        IScenes iScenes = scenesMap.get(version);
        if (iScenes != null) return iScenes;
        return scenesMap.get(0);
    }

    private static final HashMap<Integer, ScenesHint> scenesHintMap = new HashMap<>();

    /**
     * 获取场景提示信息
     *
     * @param effect
     * @return null表明当前不支持hint提示
     */
    public static ScenesHint getScenesHint(int effect) {
        checkHintMap();
        return scenesHintMap.get(effect);
    }

    private synchronized static void checkHintMap() {
        if (scenesHintMap.isEmpty()) {
            /*日出提示*/
            ScenesHint scenesHint4gm = ScenesHint.makeHint4gm(BleProtocol.value_sub_mode_scenes_gm);
            scenesHintMap.put(scenesHint4gm.effect, scenesHint4gm);
            /*日落提示*/
            ScenesHint scenesHint4sunset = ScenesHint.makeHint4sunset(BleProtocol.value_sub_mode_scenes_sunset);
            scenesHintMap.put(scenesHint4sunset.effect, scenesHint4sunset);
            /*警报提示*/
            ScenesHint scenesHint4alarm = ScenesHint.makeHint4alarm(ScenesRgbIC.effect_alarm, ScenesOp.scene_type_rgbic);
            scenesHintMap.put(scenesHint4alarm.effect, scenesHint4alarm);
            /*警报提示*/
            ScenesHint scenesHint4alarmV1 = ScenesHint.makeHint4alarm(ScenesRgbIC.effect_15ic_jingbao, ScenesOp.scene_type_rgbic);
            scenesHintMap.put(scenesHint4alarmV1.effect, scenesHint4alarmV1);
        }
    }

    public static IScenes getSupportScenes(DeviceModel deviceModel) {
        boolean bkProtocol = Support.isBkProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
        int version;
        if (bkProtocol) {
            version = 1;
        } else {
            version = newVersion(1, deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactCode, deviceModel.versionSoft, deviceModel.versionHard, deviceModel.wifiSoftVersion, deviceModel.wifiHardVersion);
        }
        return getScenes(version);
    }

    /**
     * 检查场景是否支持
     *
     * @param subMode         subMode
     * @param sku             sku
     * @param version         version
     * @param isBleOp         isBleOp
     * @param versionSoft     versionSoft
     * @param versionHard     versionHard
     * @param isIotOp         isIotOp
     * @param wifiSoftVersion wifiSoftVersion
     * @param wifiHardVersion wifiHardVersion
     * @return ISubMode
     */
    public static ISubMode checkScenesModeEffect(ISubMode subMode, String sku, int goodsType, int pactType, int pactCode, int version,
                                                 boolean isBleOp, String versionSoft, String versionHard,
                                                 boolean isIotOp, String wifiSoftVersion, String wifiHardVersion) {
        if (subMode instanceof SubModeScenes) {
            int effect = ((SubModeScenes) subMode).getEffect();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkScenesModeEffect() effect = " + effect + "---version:" + version);
            }
            List<CategoryV1> categoriesV1 = ScenesM.getInstance.getCategoriesV1(sku);
            boolean isHaveMyScenes = categoriesV1 != null && !categoriesV1.isEmpty() && version == 1;
            if (isHaveMyScenes) {
                for (CategoryV1 category : categoriesV1) {
                    for (CategoryV1.SceneV1 scene : category.scenes) {
                        int sceneType = scene.getSceneType(0, sku);
                        int cmdVersion = scene.getCmdVersion(sku, 0);
                        if (scene.getSceneCode(0, sku) == effect) {
                            if (isBleOp) {
                                if (Support.checkShareEffectVersion4Ble(sceneType, cmdVersion, sku, goodsType, pactType, pactCode, versionSoft, versionHard)) {
                                    return null;
                                }
                            } else if (isIotOp) {
                                if (Support.checkShareEffectVersion4BleWifi(1, sceneType, cmdVersion, sku, goodsType, pactType, pactCode, versionSoft, versionHard, wifiSoftVersion)) {
                                    return null;
                                }
                            }
                            if (LogInfra.openLog()) {
                                LogInfra.Log.e(TAG, "上次应用场景版本不支持，重新查找支持场景");
                            }
                            return getSupportScenes(version, true, sku, goodsType, pactType, pactCode, isBleOp, versionSoft, versionHard, isIotOp, wifiSoftVersion, wifiHardVersion);
                        }
                    }
                }
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "我的场景查找不到,重新查找支持场景");
                }
                return getSupportScenes(version, true, sku, goodsType, pactType, pactCode, isBleOp, versionSoft, versionHard, isIotOp, wifiSoftVersion, wifiHardVersion);
            }
            if (checkScenesCode(version, effect)) {
                return null;
            }
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "从静态场景中重新查找支持场景");
            }
            return getSupportScenes(version, false, sku, goodsType, pactType, pactCode, isBleOp, versionSoft, versionHard, isIotOp, wifiSoftVersion, wifiHardVersion);
        }
        return null;
    }

    /**
     * 获取第一个支持的场景
     *
     * @param version        version
     * @param isHaveMyScenes isHaveMyScenes
     * @return ISubMode
     */
    private static ISubMode getSupportScenes(int version, boolean isHaveMyScenes, String sku, int goodsType, int pactType, int pactCode, boolean isBleOp, String versionSoft, String versionHard, boolean isIotOp, String wifiSoftVersion, String wifiHardVersion) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getSupportScenes() version = " + version + " ; isHaveMyScenes = " + isHaveMyScenes + " ; sku = " + sku + " ; goodsType = " + goodsType + " ; pactType = " + pactType + " ; isBleOp = " + isBleOp + " ; versionSoft = " + versionSoft + " ; versionHard = " + versionHard + " ; isIotOp = " + isIotOp + " ; wifiSoftVersion = " + wifiSoftVersion + " ; wifiHardVersion = " + wifiHardVersion);
        }
        if (isHaveMyScenes) {
            List<CategoryV1> categoriesV1 = ScenesM.getInstance.getCategoriesV1(sku);
            for (CategoryV1 category : categoriesV1) {
                for (CategoryV1.SceneV1 scene : category.scenes) {
                    int sceneType = scene.getSceneType(0, sku);
                    int cmdVersion = scene.getCmdVersion(sku, 0);
                    if (isBleOp) {
                        if (Support.checkShareEffectVersion4Ble(sceneType, cmdVersion, sku, goodsType, pactType, pactCode, versionSoft, versionHard)) {
                            SubModeScenes subModeScenes = new SubModeScenes();
                            subModeScenes.setEffect(scene.getSceneCode(0, sku));
                            return subModeScenes;
                        }
                    } else if (isIotOp) {
                        if (Support.checkShareEffectVersion4BleWifi(1, sceneType, cmdVersion, sku, goodsType, pactType, pactCode, versionSoft, versionHard, wifiSoftVersion)) {
                            SubModeScenes subModeScenes = new SubModeScenes();
                            subModeScenes.setEffect(scene.getSceneCode(0, sku));
                            return subModeScenes;
                        }
                    }
                }
            }
        }
        IScenes scenes = Support.getScenes(version);
        /*当前版本下的场景不支持该effect，因此采用默认第一个effect*/
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.setEffect(scenes.scenesEffectSet()[0]);
        return subModeScenes;
    }

    /**
     * 检查code是否在支持的静态列表内
     *
     * @param newVersion newVersion
     * @param effect     effect
     * @return boolean
     */
    private static boolean checkScenesCode(int newVersion, int effect) {
        IScenes scenes = Support.getScenes(newVersion);
        return scenes.supportEffects().contains(effect);
    }

    /**
     * 是否是BK协议
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return
     */
    public static boolean isBkProtocol(String sku, int goodsType, int pactType, int pactCode) {
        if (goodsType == 0) {
            int newGoodsType = OldDreamColorUtil.parseOldDreamDeviceNewGoodsType(sku);
            if (newGoodsType != -1) {
                goodsType = newGoodsType;
            }
        }
        /*是否已配置goodsType的bk方案设备*/
        if (OldRgbicBkUtil.isRgbicBk4Ble(sku, pactType, pactCode)) {
            return true;
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1) {
            return pactType >= GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_BK_V1
                    && pactCode >= GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_BK_V1;
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1) {
            return pactType >= GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_BK_V1
                    && pactCode >= GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_BK_V1;
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1) {
            return pactType >= GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_WIFI_BK_1
                    && pactCode >= GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_WIFI_BK_1;
        }
        /*其他新goodsType默认都支持bk协议*/
        return true;
    }

    /**
     * 判断纯蓝牙设备是否支持-蓝牙下是否支持
     *
     * @param versionSoft
     * @param versionHard
     * @return
     */
    public static boolean supportPartBrightness4Ble(String versionSoft, String versionHard) {
        int curVersionHardInt = BleUtil.parseVersion(versionHard);
        int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
        if (curVersionHardInt <= 0 || curVersionSoftInt <= 0) return false;
        int compareVersionHardInt = BleUtil.parseVersion(hard_version_v0_new_color_mode);
        int compareVersionSoftInt = BleUtil.parseVersion(soft_version_v0_new_color_mode);
        return curVersionHardInt >= compareVersionHardInt && curVersionSoftInt >= compareVersionSoftInt;
    }

    /**
     * 判断蓝牙+Wi-Fi的设备是否支持-蓝牙下是否支持
     *
     * @param versionSoft
     * @param versionHard
     * @return
     */
    public static boolean supportPartBrightness4BleWifi(int goodsType, String sku, String versionSoft, String versionHard) {
        if (isH61B5(goodsType, sku)) return true;
        if (isH80B5(goodsType, sku)) return true;
        if (isGoodsTypeH61A9(goodsType)) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H616C) return true;
        if (goodsTypeH612x(goodsType)) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3) return true;

        int curVersionHardInt = BleUtil.parseVersion(versionHard);
        int curVersionSoftInt = BleUtil.parseVersion(versionSoft);
        if (curVersionHardInt <= 0 || curVersionSoftInt <= 0) return false;
        int compareVersionHardInt = BleUtil.parseVersion(hard_version_v0_new_color_mode_ble_wifi);
        int compareVersionSoftInt = BleUtil.parseVersion(soft_version_v0_new_color_mode);
        return curVersionHardInt >= compareVersionHardInt && curVersionSoftInt >= compareVersionSoftInt;
    }

    public static boolean supportRgbWithBrightnessOnePkg4Colors(int goodsType, int pactType, int pactCode) {
        boolean support = GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC == goodsType
                || GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3 == goodsType
                || GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA == goodsType
                || GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175 == goodsType
                || GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE == goodsType
                || GoodsType.GOODS_TYPE_VALUE_H612x == goodsType;
        if (support) return true;
        /*旧sku升级固件支持*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
            return pactType >= GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET_V3
                    && pactCode >= GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET_FRK;
        }
        return false;
    }

    /**
     * 判断蓝牙+Wi-Fi的设备是否支持-Wi-Fi下是否支持
     *
     * @param sku
     * @param wifiVersionSoft
     * @return
     */
    public static boolean supportPartBrightness4Wifi(String sku, String wifiVersionSoft, String versionHard) {
        boolean bkOtaV1 = OtaType.isBKOtaV1(versionHard);
        if (bkOtaV1) return true;
        if (H6163.equals(sku) || H6117.equals(sku)) {
            int curWifiVersionSoftInt = BleUtil.parseVersion(wifiVersionSoft);
            if (curWifiVersionSoftInt <= 0) return false;
            if (wifiVersionSoft.startsWith("1")) {
                int compareWifiVersionSoftInt = BleUtil.parseVersion(wifi_soft_version_v0_new_color_mode);
                return curWifiVersionSoftInt >= compareWifiVersionSoftInt;
            }
        }
        return true;
    }

    public static int getDiyVersion(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getDiyVersion() sku = " + sku + " ; goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode + " ; versionSoft = " + versionSoft + " ; versionHard = " + versionHard);
        }
        if (isGoodsTypeH61A9(goodsType)) return 2;
        if (Support.supportGraffitiInSeries(goodsType)) return 3;
        boolean supportSubModeColor4PartBrightness = supportPartBrightness4Ble(versionSoft, versionHard);
        if (supportSubModeColor4PartBrightness) return 2;
        return newVersion4Ble(sku, goodsType, pactCode, versionSoft, versionHard);
    }

    public static int getDiyVersion4BleWifi(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getDiyVersion4BleWifi() sku = " + sku + " ; goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode + " ; versionSoft = " + versionSoft + " ; versionHard = " + versionHard);
        }
        boolean supportGraffitiInSeries = Support.supportGraffitiInSeries(goodsType);
        if (supportGraffitiInSeries) {
            return 3;
        }
        boolean bkProtocol = isBkProtocol(sku, goodsType, pactType, pactCode);
        if (bkProtocol) {
            return 2;
        }
        boolean supportSubModeColor4PartBrightness = supportPartBrightness4BleWifi(goodsType, sku, versionSoft, versionHard);
        if (supportSubModeColor4PartBrightness) return 2;
        return newVersion4Ble(sku, goodsType, pactCode, versionSoft, versionHard);
    }

    public static int getDiyVersion(int opType, String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, String wifiVersionSoft, String wifiHardVersion) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getDiyVersion() opType = " + opType + " ; goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode + " ; versionSoft = " + versionSoft + " ; versionHard = " + versionHard + " ; wifiVersionSoft = " + wifiVersionSoft + " ; wifiHardVersion = " + wifiHardVersion);
        }
        boolean supportGraffitiInSeries = Support.supportGraffitiInSeries(goodsType);
        if (supportGraffitiInSeries) {
            return 3;
        }
        boolean bkProtocol = isBkProtocol(sku, goodsType, pactType, pactCode);
        if (bkProtocol) {
            return 2;
        }
        boolean supportSubModeColor4PartBrightness;
        if (opType == 0) {
            supportSubModeColor4PartBrightness = supportPartBrightness4BleWifi(goodsType, sku, versionSoft, versionHard);
        } else {
            supportSubModeColor4PartBrightness = supportPartBrightness4BleWifi(goodsType, sku, versionSoft, versionHard) && supportPartBrightness4Wifi(sku, wifiVersionSoft, versionHard);
        }
        if (supportSubModeColor4PartBrightness) return 2;
        return newVersion(opType, sku, goodsType, pactCode, versionSoft, versionHard, wifiVersionSoft, wifiHardVersion);
    }

    public static boolean checkShareEffectVersion4Ble(int sceneType, int version, String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkShareEffectVersion4Ble() sceneType = " + sceneType + " ; version = " + version + " ; sku = " + sku + " ; goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode + " ; versionSoft = " + versionSoft + " ; versionHard = " + versionHard);
        }
        if (version == 0) return true;
        if (sceneType == ScenesOp.scene_type_rgbic) {
            if (version == 1) {
                return supportPartBrightness4BleWifi(goodsType, sku, versionSoft, versionHard);
            }
        }
        return false;
    }

    public static boolean checkShareEffectVersion4BleWifi(int opType, int sceneType, int version, String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, String wifiVersionSoft) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkShareEffectVersion4BleWifi() opType = " + opType + " ; sceneType = " + sceneType + " ; version = " + version + " ; sku = " + sku + " ; goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode + " ; versionSoft = " + versionSoft + " ; versionHard = " + versionHard + " ; wifiVersionSoft = " + wifiVersionSoft);
        }
        if (version == 0) return true;
        if (sceneType == ScenesOp.scene_type_rgbic) {
            if (version == 1) {
                boolean supportSubModeColor4PartBrightness;
                if (opType == 0) {
                    supportSubModeColor4PartBrightness = supportPartBrightness4BleWifi(goodsType, sku, versionSoft, versionHard);
                } else {
                    boolean bkProtocol = isBkProtocol(sku, goodsType, pactType, pactCode);
                    if (bkProtocol) {
                        supportSubModeColor4PartBrightness = supportPartBrightness4BleWifi(goodsType, sku, versionSoft, versionHard);
                    } else {
                        supportSubModeColor4PartBrightness = supportPartBrightness4BleWifi(goodsType, sku, versionSoft, versionHard) && supportPartBrightness4Wifi(sku, wifiVersionSoft, versionHard);
                    }
                }
                return supportSubModeColor4PartBrightness;
            }
        }
        return false;
    }

    public static void isSupportMicByPhone(String sku, String device, String softVersion, int goodsType) {
        AbsMicFragmentV4.SupportMicStatus support = AbsMicFragmentV4.SupportMicStatus.not_support;
        try {
            if (TextUtils.isEmpty(softVersion)) {
                return;
            }
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "sku:" + sku + "\t softVersion:" + softVersion);
            }
            int curVersion = Integer.parseInt(softVersion.replace(".", ""));

            int flagVersion = 0;
            int flagBleMinVersion = 0;
            boolean isBleDevice = false;
            if (GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1 == goodsType) {
                flagVersion = 10600;
                isBleDevice = true;
            }
            if (GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1 == goodsType) {
                flagVersion = 10600;
                isBleDevice = true;
                flagBleMinVersion = 10112;
            }

            if (H6116.equals(sku)) {
                flagBleMinVersion = 10120;
            }
            if (H6126.equals(sku) || H6125.equals(sku)) {
                flagBleMinVersion = 10011;
            }
            if (H6161.equals(sku)) {
                flagBleMinVersion = 10108;
            }
            if (H6127.equals(sku)) {
                flagBleMinVersion = 10110;
            }
            if (GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1 == goodsType) {
                flagVersion = 10500;
            }

            if (flagVersion != 0) {
                if (flagVersion <= curVersion) {
                    support = AbsMicFragmentV4.SupportMicStatus.support_new_order;
                } else if (isBleDevice && curVersion >= flagBleMinVersion) {
                    support = AbsMicFragmentV4.SupportMicStatus.support_color_order;
                }
            } else {
                /*非特殊sku，默认都支持新指令的mic手机模式*/
                support = AbsMicFragmentV4.SupportMicStatus.support_new_order;
            }
            AbsMicFragmentV4.saveSupportMicModeByPhone(sku, device, support);
        } catch (Exception e) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i("MicFragment", "isSupportMicByPhone异常：" + e.getMessage());
            }
            e.printStackTrace();
        }
    }

    public static AbsMicFragmentV4.SupportMicStatus getMicStatus(String sku, String versionSoft, int goodsType) {
        int curVersion = NumberUtil.parseVersion(versionSoft);
        if (goodsType == 0) {
            int goodsTypeNew = OldDreamColorUtil.parseOldDreamDeviceNewGoodsType(sku);
            if (goodsTypeNew != -1) {
                goodsType = goodsTypeNew;
            }
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1) {
            if (curVersion >= NumberUtil.parseVersion("1.06.00"))
                return AbsMicFragmentV4.SupportMicStatus.support_new_order;
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1) {
            if (curVersion >= NumberUtil.parseVersion("1.06.00"))
                return AbsMicFragmentV4.SupportMicStatus.support_new_order;
            if (curVersion >= NumberUtil.parseVersion("1.01.12"))
                return AbsMicFragmentV4.SupportMicStatus.support_color_order;
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1) {
            if (curVersion >= NumberUtil.parseVersion("1.05.00"))
                return AbsMicFragmentV4.SupportMicStatus.support_new_order;
        }
        if (H6116.equals(sku)) {
            if (curVersion >= NumberUtil.parseVersion("1.01.20"))
                return AbsMicFragmentV4.SupportMicStatus.support_color_order;
            return AbsMicFragmentV4.SupportMicStatus.not_support;
        }
        if (H6125.equals(sku) || H6126.equals(sku)) {
            if (curVersion >= NumberUtil.parseVersion("1.00.11"))
                return AbsMicFragmentV4.SupportMicStatus.support_color_order;
            return AbsMicFragmentV4.SupportMicStatus.not_support;
        }
        if (H6161.equals(sku)) {
            if (curVersion >= NumberUtil.parseVersion("1.01.08"))
                return AbsMicFragmentV4.SupportMicStatus.support_color_order;
            return AbsMicFragmentV4.SupportMicStatus.not_support;
        }
        if (H6127.equals(sku)) {
            if (curVersion >= NumberUtil.parseVersion("1.01.10"))
                return AbsMicFragmentV4.SupportMicStatus.support_color_order;
            return AbsMicFragmentV4.SupportMicStatus.not_support;
        }
        return AbsMicFragmentV4.SupportMicStatus.support_new_order;
    }

    public static boolean supportDeviceLock(int goodsType, String sku, int pactType, int pactCode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "supportDeviceLock() goodsType = " + goodsType + " ; sku = " + sku + " ; pactType = " + pactType + " ; pactCode = " + pactCode);
        }
        /*H6171新固件支持设备按键配对*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1
                && pactType == GoodsType.PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_BK_V1
                && pactCode >= GoodsType.PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_BK_V1_FRK_V1
        ) {
            return false;
        }
        /*已支持设备校验的无需支持设备加密逻辑*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_H6169 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA
        ) {
            return false;
        }
        return !UuidV1.needMakeDeviceIdSkuList.contains(sku);
    }

    private static final String hard_version_v0_new_color_mode = "1.00.03";           /*旧纯蓝牙设备硬件版本*/
    private static final String hard_version_v0_new_color_mode_ble_wifi = "1.00.02";  /*旧蓝牙+wifi设备硬件版本*/
    private static final String soft_version_v0_new_color_mode = "1.06.00";           /*旧设备软件版本*/
    private static final String wifi_soft_version_v0_new_color_mode = "1.03.00";      /*旧蓝牙+wifi设备wifi软件版本*/

    /*旧纯蓝牙设备*/
    private static final String[] oldDreamColorBleSkuSet = {
            "H6102",
            "H6127",
            "H6161",
            "H6116",
            "H6125",
            "H6126",
    };
    public static final List<String> OldDreamColorBleSkuArray = Arrays.asList(oldDreamColorBleSkuSet);

    /*旧蓝牙+wifi设备*/
    private static final String[] oldDreamColorBleWifiSkuSet = {
            "H6163",
            "H6117"
    };
    public static final List<String> OldDreamColorBleWifiSkuArray = Arrays.asList(oldDreamColorBleWifiSkuSet);

    /**
     * @param sku
     * @param versionSoft
     * @param versionHard
     * @param wifiVersionSoft wifi软件版本 蓝牙设备传"",蓝牙+wifi设备蓝牙连接下传"",wifi下传wifi软件版本
     * @return 2支持 0不支持
     */
    public static int supportPartBrightness(int goodsType, int pactType, int pactCode, String sku, String versionSoft, String versionHard, String wifiVersionSoft) {
        if (isGoodsTypeH61A9(goodsType)) return 2;
        if (isBkProtocol(sku, goodsType, pactType, pactCode)) {
            return 2;
        }
        if (OldDreamColorBleSkuArray.contains(sku)) {
            return supportPartBrightness4Ble(versionSoft, versionHard) ? 2 : 0;
        } else if (OldDreamColorBleWifiSkuArray.contains(sku)) {
            if (wifiVersionSoft == null) {
                return 0;
            } else {
                if (wifiVersionSoft.isEmpty()) {
                    return supportPartBrightness4BleWifi(goodsType, sku, versionSoft, versionHard) ? 2 : 0;
                } else {
                    return supportPartBrightness4BleWifi(goodsType, sku, versionSoft, versionHard) && supportPartBrightness4Wifi(sku, wifiVersionSoft, versionHard) ? 2 : 0;
                }
            }
        } else {
            return 2; /*不在旧的sku列表,默认支持分段亮度*/
        }
    }

    /**
     * 是否支持rgbicV1的协议
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param sku
     * @param versionSoft
     * @param versionHard
     * @param wifiVersionSoft
     * @return
     */
    public static boolean isSupportRgbicV1(int goodsType, int pactType, int pactCode, String sku, String versionSoft, String versionHard, String wifiVersionSoft) {
        int version = supportPartBrightness(goodsType, pactType, pactCode, sku, versionSoft, versionHard, wifiVersionSoft);
        return version == 2;
    }

    /**
     * 获取亮度范围
     *
     * @param sku
     * @return
     */
    public static int[] getBrightnessRange(String sku, String hardVersion) {
        if (OldDreamColorUtil.oldSku4ChangeMinBrightnessList.contains(sku)) {
            boolean oldNoChangeMinBrightness = OldDreamColorUtil.isOldNoChangeMinBrightness(sku, hardVersion);
            if (!oldNoChangeMinBrightness) {
                return new int[]{6, 254};
            }
        }
        return new int[]{20, 254};
    }

    private static final String support_multi_new_music_soft_version = "1.07.00";
    private static final String support_multi_new_music_wifi_soft_version = "1.03.04";
    private static final String[] telink4SkuBleMultiNewMusicSku = {
            H6163,
            H6117,
            H6125,
            H6126,
            H6116,
            H6127,
            H6102
    };
    private static final List<String> telink4SkuBleMultiNewMusicSkuList = Arrays.asList(telink4SkuBleMultiNewMusicSku);
    private static final String[] telink4SkuWifiMultiNewMusicSku = {
            H6163,
            H6117,
    };
    private static final List<String> telink4SkuWifiMultiNewMusicSkuList = Arrays.asList(telink4SkuWifiMultiNewMusicSku);

    /**
     * 是否支持新的音乐多包效果
     *
     * @param softVersion
     * @return
     */
    public static boolean supportMultiNewMusic4Ble(String sku, String softVersion) {
        if (telink4SkuBleMultiNewMusicSkuList.contains(sku)) {
            int curVersionInt = UtilSku.parseVersion(softVersion);
            int compareVersionInt = UtilSku.parseVersion(support_multi_new_music_soft_version);
            return curVersionInt >= compareVersionInt;
        }
        return false;
    }

    /**
     * 是否支持新的音乐多包效果-Wi-Fi下
     *
     * @param wifiSoftVersion
     * @return
     */
    public static boolean supportMultiNewMusic4WifiOp(String sku, String wifiSoftVersion) {
        if (telink4SkuWifiMultiNewMusicSkuList.contains(sku)) {
            int curVersionInt = UtilSku.parseVersion(wifiSoftVersion);
            int compareVersionInt = UtilSku.parseVersion(support_multi_new_music_wifi_soft_version);
            return curVersionInt >= compareVersionInt;
        }
        return false;
    }

    /**
     * 蓝牙下是否支持新多包音乐模式
     *
     * @param sku
     * @param softVersion
     * @return
     */
    public static int getMultiNewMusicVersion4Telink(String sku, String softVersion) {
        return getMultiNewMusicVersion4Telink(true, sku, softVersion, null);
    }

    /**
     * 获取支持新多包音乐模式的版本判断
     *
     * @param bleOp
     * @param sku
     * @param softVersion
     * @return
     */
    public static int getMultiNewMusicVersion4Telink(boolean bleOp, String sku, String softVersion, String wifiSoftVersion) {
        boolean supportMultiNewMusic = supportMultiNewMusic4Ble(sku, softVersion);
        if (supportMultiNewMusic) {
            if (bleOp) return 1;
            boolean supportMultiNewMusic4WifiOp = supportMultiNewMusic4WifiOp(sku, wifiSoftVersion);
            return supportMultiNewMusic4WifiOp ? 1 : 0;
        }
        return 0;
    }

    private static final String support_multi_new_music_soft_version_4_bk = "2.04.00";
    private static final String support_multi_new_music_soft_version_4_frk = "3.01.00";

    /**
     * 新协议支持新音乐模式
     *
     * @param versionSoft
     * @param versionHard
     * @return
     */
    public static boolean newProtocol4SupportMultiMusicMode(int goodsType, String versionSoft, String versionHard) {
        if (isGoodsTypeH61A9(goodsType)) return true;
        if (goodsTypeH612x(goodsType)) return true;
        boolean supportMultiMusicMode4BleRgbic = supportMultiMusicMode4BleRgbic(goodsType);
        if (supportMultiMusicMode4BleRgbic) return true;
        boolean supportMultiMusicMode4Bk = supportMultiMusicMode4Bk(versionSoft, versionHard);
        if (supportMultiMusicMode4Bk) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H616C) return true;
        return supportMultiMusicMode4Frk(versionSoft, versionHard);
    }

    /**
     * frk方案设备是否支持新多包音乐模式
     *
     * @param versionSoft
     * @param versionHard
     * @return
     */
    private static boolean supportMultiMusicMode4Frk(String versionSoft, String versionHard) {
        boolean frkOtaV1 = OtaType.isFRKOtaV1(versionHard);
        if (!frkOtaV1) return false;/*非frk方案-不处理*/
        int curVersionInt = UtilSku.parseVersion(versionSoft);
        int compareVersionInt = UtilSku.parseVersion(support_multi_new_music_soft_version_4_frk);
        return curVersionInt >= compareVersionInt;
    }

    /**
     * bk方案设备是否支持新多包音乐模式
     *
     * @param versionSoft
     * @param versionHard
     * @return
     */
    private static boolean supportMultiMusicMode4Bk(String versionSoft, String versionHard) {
        boolean bkOtaV1 = OtaType.isBKOtaV1(versionHard);
        if (!bkOtaV1) return false;/*非bk方案-不处理*/
        int curVersionInt = UtilSku.parseVersion(versionSoft);
        int compareVersionInt = UtilSku.parseVersion(support_multi_new_music_soft_version_4_bk);
        return curVersionInt >= compareVersionInt;
    }

    /**
     * ble款的rgbic设备-目前仅此2款新产品类型支持新音乐模式；其他sku后续用版本统一判断
     *
     * @param goodsType
     * @return
     */
    private static boolean supportMultiMusicMode4BleRgbic(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA
                ;
    }

    /**
     * 是否是新的音乐模式code
     *
     * @param musicCode
     * @return
     */
    public static boolean isNewMusicCode(int musicCode) {
        return AbsNewMusicEffect.newMusicCode4RgbicList.contains(musicCode) || AbsNewMusicEffect.newMusicCode4RgbList.contains(musicCode);
    }

    /**
     * 是否是支持设备校验密钥的设备
     *
     * @param goodsType
     * @return
     */
    public static boolean isSupportDeviceSecret(int goodsType) {
        return false;
    }

    /**
     * 是否支持动态域名
     *
     * @param goodsType
     * @param sku
     * @return
     */
    public static boolean supportDynamicApi(int goodsType, String sku) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT) return true;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3) return true;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "supportDynamicApi() goodsType = " + goodsType + " ; sku = " + sku);
        }
        return false;
    }

    /**
     * H61E0
     * 强制支持动态域名，supportDynamicApi()返回false就行
     *
     * @param goodsType
     * @return
     */
    public static String dynamicApi(int goodsType) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA
        ) {
            return EventDynamicApiSupport.getDynamicApi(EventDynamicApiSupport.api_support_https);
        }
        return EventDynamicApiSupport.getDynamicApi(-1);
    }

    /**
     * 是否不支持渐变功能
     *
     * @param goodsType
     * @return
     */
    public static boolean noSupportGradual(int goodsType) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "noSupportGradual() goodsType = " + goodsType);
        }
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA
                ;
    }

    /**
     * 获取支持场景的op集合
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param versionHard
     * @return
     */
    public static int[] getSupportScenesOpSet(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard) {
        int newVersion4Ble = newVersion4Ble(sku, goodsType, pactCode, versionSoft, versionHard);
        if (newVersion4Ble == 0) return null;/*不支持抽象协议*/
        boolean isBleDevice = NumberUtil.toListByIntArray(deviceItemGoodsTypes4Ble).contains(goodsType);
        boolean supportRgbicV1 = isBleDevice ? supportPartBrightness4Ble(versionSoft, versionHard) : supportPartBrightness4BleWifi(goodsType, sku, versionSoft, versionHard);
        return supportRgbicV1 ? new int[]{
                ScenesOp.scene_type_op_static,
                ScenesOp.scene_type_op_rgb,
                ScenesOp.scene_type_op_rgbic_v0,
                ScenesOp.scene_type_op_rgbic_v1,
        } : new int[]{
                ScenesOp.scene_type_op_static,
                ScenesOp.scene_type_op_rgb,
                ScenesOp.scene_type_op_rgbic_v0,
        };
    }

    public static boolean supportFactor10Pieces(int goodsType, String sku) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                ;
    }

    public static boolean supportFactor12PiecesNoGradual(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3;
    }

    public static boolean supportFactor20Pieces(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE;
    }

    public static boolean supportFactor15PiecesNoGradual(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_H6169;
    }

    public static boolean supportChangePieces20(int goodsType, String sku) {
        return goodsTypeH612x(goodsType) && (sku.equalsIgnoreCase("H612D") || sku.equalsIgnoreCase("H612E"));
    }

    public static boolean goodsTypeH612x(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_H612x;
    }

    private static boolean localScenesV2(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1;
    }

    private static boolean localScenesV3(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_H6169;
    }

    public static int localScenesVersion(int goodsType) {
        boolean localScenesV3 = localScenesV3(goodsType);
        if (localScenesV3) return 3;
        boolean localScenesV2 = localScenesV2(goodsType);
        if (localScenesV2) return 2;
        return 1;
    }

    public static int getBrightness(AbsDevice absDevice, int brightnessPercent) {
        boolean bkProtocol = Support.isBkProtocol(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode());
        int brightness;
        if (bkProtocol) {
            brightness = brightnessPercent;
        } else {
            int[] brightnessRange = Support.getBrightnessRange(absDevice.getSku(), absDevice.getVersionHard());
            brightness = NumUtil.calculateProgress(brightnessRange[1], brightnessRange[0], brightnessPercent);
        }
        return brightness;
    }

    public static int getBrightness(DeviceModel deviceModel, int brightnessPercent) {
        boolean bkProtocol = Support.isBkProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
        int brightness;
        if (bkProtocol) {
            brightness = brightnessPercent;
        } else {
            int[] brightnessRange = Support.getBrightnessRange(deviceModel.getSku(), deviceModel.versionHard);
            brightness = NumUtil.calculateProgress(brightnessRange[1], brightnessRange[0], brightnessPercent);
        }
        return brightness;
    }

    public static boolean uiV14bleIotProtocol(int pactType, int pactCode) {
        List<Protocol> supportProtocolsV2 = Support.supportProtocolsV2;
        for (Protocol protocol : supportProtocolsV2) {
            if (protocol.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    public static boolean uiV34bleIotProtocol(int pactType, int pactCode) {
        /*ble+wifi-BK-不支持设备校验*/
        for (Protocol protocol : Support.supportProtocolsV5) {
            if (protocol.isSameProtocol(pactType, pactCode)) return true;
        }
        /*ble+wifi-frk-不支持设备校验*/
        for (Protocol pro : Support.supportProtocolsV5_1) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        /*ble+wifi-bk-支持设备校验*/
        for (Protocol pro : Support.supportProtocolsV7) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        for (Protocol pro : Support.supportProtocolsV10) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        for (Protocol pro : Support.supportProtocolsV11) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    public static boolean isUiV4bleIotProtocol(int pactType, int pactCode) {
        /*ble+wifi的高端rgbic灯带-支持主盛宴*/
        for (Protocol pro : supportProtocolsV8) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    public static boolean isUi4BleIotProtocol4H80B5(int pactType, int pactCode) {
        for (Protocol pro : supportProtocolsH80B5) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    public static boolean isUi4BleIotProtocol4H61BC(int pactType, int pactCode) {
        for (Protocol pro : supportProtocolsV8_2) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    public static boolean isUi4BleIotProtocol4H61BE(int pactType, int pactCode) {
        for (Protocol pro : supportProtocolsV8_3) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    public static boolean isUi4BleIotProtocol4H6175(int pactType, int pactCode) {
        for (Protocol pro : supportProtocolsV8_4) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    public static boolean isUiV4bleIotProtocol4FreshIc(int pactType, int pactCode) {
        /*ble+wifi的高端rgbic灯带-支持主盛宴-支持ic刷新*/
        for (Protocol pro : supportProtocolsV8_1) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    /**
     * 真的特定sku有默认名称配置
     *
     * @param sku
     * @param defDeviceName
     * @return
     */
    public static String getSkuDefName(String sku, String defDeviceName) {
        if (H70A1.equals(sku)) return "Outdoor Strip Light";
        if (H70A2.equals(sku)) return "Outdoor Strip Light";
        if (H70A3.equals(sku)) return "Outdoor Strip Light";
        if (H6173.equals(sku)) return "Outdoor Strip Lights";
        if (H6172.equals(sku)) return "Outdoor Strip Lights";
        if (H618F.equals(sku)) return "RGBIC LED Strip Light";
        if (H617E.equals(sku) || H617F.equals(sku)) return DeviceNameConfig.h617e_name;
        if (H61A3.equals(sku)) return "RGBIC Neon Rope Lights";
        if (H61E1.equals(sku)) return "LED Strip Light M1";
        if (H61A5.equals(sku)) return "RGBIC Neon Rope Lights";
        if (H6168.equals(sku)) return "RGBIC TV Backlight";
        if (H6171.equals(sku)) return "Outdoor Strip Lights";
        if (H61A8.equals(sku) || H61A9.equals(sku)) return "RGBIC Outdoor Neon Rope Light";
        if (H61BA.equals(sku) || H61B1.equals(sku) || H61BE.equals(sku) || H61BC.equals(sku))
            return "RGBICW LED Strip Light";
        if (H61B5.equals(sku)) return "Strip Light";
        if (H616C.equals(sku) || H616D.equals(sku) || H616E.equals(sku))
            return "Outdoor Strip Lights 2";
        if (H612A.equals(sku) || H612B.equals(sku) || H612C.equals(sku) || H612D.equals(sku) || H612E.equals(sku) || H612F.equals(sku))
            return "Strip Light";
        return defDeviceName;
    }

    /**
     * (霓虹灯系列)是否支持推荐图形, 本地实现
     *
     * @param sku
     * @return
     */
    public static boolean supportRecommendedShapeLocal(String sku) {
        String[] supports = Jump.INSTANCE.getDreamColorLightV1SupportSku();
        for (String support : supports) {
            if (support.equals(sku)) return true;
        }
        return false;
    }

    /**
     * (霓虹灯系列)是否支持推荐图形，
     *
     * @param sku
     * @return
     */
    public static boolean supportRecommendedGraphic(String sku) {
        String[] supports = {H61A0, H61A1, H61A2, H61A3};
        for (String support : supports) {
            if (support.contains(sku)) return true;
        }
        return false;
    }

    public static boolean supportQrCodesScene(String sku, int goodsType) {
        return H619D.equals(sku) || H619Z.equals(sku) || H619A.equals(sku);
    }

    /**
     * 是否支持音乐盛宴
     */
    public static boolean supportMusicFeast(int goodsType, String sku) {
        if (isH61B5(goodsType, sku)) return false;      //H61B5 不支持音乐盛宴
        if (isH80B5(goodsType, sku)) return false;      //H80B5 不支持音乐盛宴
        return goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END;
    }

    public static boolean isJump2AdjustAcV3(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA
                ;
    }

    /**
     * 是否支持刷新ic
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return
     */
    public static boolean supportIcFresh(String sku, int goodsType, int pactType, int pactCode) {
        if (goodsTypeH612x(goodsType)) return true;
        if (tempNoSupportIcFresh(sku)) return false;
        if (H61B5.equals(sku)) return true;
        if (isGoodsTypeH61A9(goodsType)) return true;
        if (isGoodsTypeH61A9(goodsType) && H61A9.equals(sku)) return false;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END) {
            return (pactType == GoodsType.PACT_TYPE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END
                    && pactCode >= GoodsType.PACT_CODE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END_V2) || pactType == GoodsType.PACT_TYPE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END_V2;
        }
        return false;
    }

    /**
     * 暂时H61B1不支持拼接，等后续售卖才支持
     */
    private static boolean tempNoSupportIcFresh(String sku) {
        return H61B1.equals(sku);
    }

    public static boolean isH61B2(String sku) {
        return H61B2.equals(sku);
    }

    /**
     * 是否针对特定sku不支持高阶颜色
     *
     * @param sku
     * @param versionHard
     * @return
     */
    public static boolean noSupportHighColor4SpecialSku(String sku, String versionHard) {
        if (H6163.equals(sku) || H6117.equals(sku)) {
            boolean telinkOta = OtaType.isTelinkOta(versionHard);
            LogInfra.Log.i(TAG, "noSupportHighColor4SpecialSku() telinkOta = " + telinkOta);
            return telinkOta;
        }
        return false;
    }

    /**
     * 是否支持上报ic信息
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param sku
     * @return
     */
    public static boolean supportBindWithIcNum(int goodsType, int pactType, int pactCode, String sku) {
        /*是否支持绑定流程添加icNum参数上报 暂时仅针对H6171支持-后续需要扩展到支持全部新sku*/
        return H6171.equals(sku) || supportRecommendedShapeLocal(sku);
    }

    /**
     * 版本显示是否放置到设置页
     *
     * @param goodsType
     * @return
     */
    public static boolean versionShowInSettingAc(int goodsType, int pactType, int pactCode) {
        boolean support = goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H6169
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H616C;
        if (support) return true;
        /*旧sku升级固件支持*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
            return pactType >= GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET_V3
                    && pactCode >= GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET_FRK;
        }
        return false;
    }

    public static boolean supportTimerWithColorConfig(int goodsType, String sku, int pactType, int pactCode) {
        boolean support = GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC == goodsType
                || GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3 == goodsType
                || GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA == goodsType
                || GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175 == goodsType
                || GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE == goodsType
                || GoodsType.GOODS_TYPE_VALUE_H616C == goodsType
                || GoodsType.GOODS_TYPE_VALUE_H612x == goodsType;
        if (support) return true;
        /*旧sku升级固件支持*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
            return pactType >= GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET_V3
                    && pactCode >= GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET_FRK;
        }
        if (isH80B5(goodsType, sku)) {
            return true;
        }
        return isH61B5(goodsType, sku);
    }

    public static AbsSingleController makeSleepController4Read(int goodsType, String sku, int pactType, int pactCode) {
        boolean supportTimerWithColorConfig = supportTimerWithColorConfig(goodsType, sku, pactType, pactCode);
        if (supportTimerWithColorConfig) return new SleepControllerV2();
        return new SleepController();
    }

    public static AbsSingleController makeWakeUpController4Read(int goodsType, String sku, int pactType, int pactCode) {
        boolean supportTimerWithColorConfig = supportTimerWithColorConfig(goodsType, sku, pactType, pactCode);
        if (supportTimerWithColorConfig) return new WakeUpControllerV2();
        return new WakeUpController();
    }

    public static AbsSingleController makeSleepController(int goodsType, String sku, int pactType, int pactCode, SleepInfo sleepInfo) {
        return makeSleepController(goodsType, sku, pactType, pactCode, sleepInfo, false);
    }

    /**
     * defaultLight这个字段大部分灯是 1代表定义颜色，0代表默认，但6057是个例外刚好取反，睡眠最初基于6057做的，这里需要配置下
     * <p>
     * 如果是昼夜节律带过来的SleepInfo， 0代表默认颜色的话这里  needDefaultLightAntonyms 要给true
     *
     * @param needDefaultLightAntonyms true: 0代表自定义   false：1代表自定义
     * @return
     */
    public static AbsSingleController makeSleepController(int goodsType, String sku, int pactType, int pactCode,
                                                          SleepInfo sleepInfo, boolean needDefaultLightAntonyms) {
        boolean supportTimerWithColorConfig = supportTimerWithColorConfig(goodsType, sku, pactType, pactCode);
        if (supportTimerWithColorConfig) {
            int lightDefault = sleepInfo.defaultLight;
            if (needDefaultLightAntonyms) {
                if (sleepInfo.defaultLight == 1) {
                    lightDefault = 0;
                } else {
                    lightDefault = 1;
                }
            }
            return new SleepControllerV2(sleepInfo.enable, sleepInfo.startBri, sleepInfo.closeTime, sleepInfo.curTime, sleepInfo.openVoice,
                    sleepInfo.voiceCode, sleepInfo.voiceVolume, lightDefault, sleepInfo.rgb);
        }
        return new SleepController(sleepInfo.enable, sleepInfo.startBri, sleepInfo.closeTime, sleepInfo.closeTime);
    }

    public static AbsSingleController makeWakeUpController(int goodsType, String sku, int pactType, int pactCode, WakeUpInfo wakeUpInfo) {
        boolean supportTimerWithColorConfig = supportTimerWithColorConfig(goodsType, sku, pactType, pactCode);
        if (supportTimerWithColorConfig) {
            return new WakeUpControllerV2(wakeUpInfo.enable, wakeUpInfo.endBri, wakeUpInfo.wakeHour, wakeUpInfo.wakeMin, wakeUpInfo.repeat, wakeUpInfo.wakeTime, wakeUpInfo.openVoice, wakeUpInfo.voiceCode, wakeUpInfo.voiceVolume, wakeUpInfo.defaultLight, wakeUpInfo.rgb);
        }
        return new WakeUpController(wakeUpInfo.enable, wakeUpInfo.endBri, wakeUpInfo.wakeHour, wakeUpInfo.wakeMin, wakeUpInfo.repeat, wakeUpInfo.wakeTime);
    }

    public static WakeUpInfo parseWakeUpInfo(int goodsType, String sku, int pactType, int pactCode, byte[] opCommandBytes) {
        boolean supportTimerWithColorConfig = supportTimerWithColorConfig(goodsType, sku, pactType, pactCode);
        if (supportTimerWithColorConfig) {
            return WakeUpInfo.parseBytesOnlyWithColor4Original20Bytes(opCommandBytes);
        }
        return WakeUpController.parse2WakeUp(opCommandBytes);
    }

    public static SleepInfo parseSleepInfo(int goodsType, String sku, int pactType, int pactCode, byte[] opCommandBytes) {
        boolean supportTimerWithColorConfig = supportTimerWithColorConfig(goodsType, sku, pactType, pactCode);
        if (supportTimerWithColorConfig) {
            return SleepInfo.parseBytesOnlyWithColor4Original20Bytes(opCommandBytes);
        }
        return SleepController.parseSleep(opCommandBytes);
    }

    /**
     * 是否支持串联涂鸦
     */
    public static boolean supportGraffitiInSeries(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE;
    }

    public static AbsMultipleControllerV1 makeGraffitiController(int goodsType, DiyGraffitiV2 graffitiV2) {
        boolean supportGraffitiInSeries = supportGraffitiInSeries(goodsType);
        if (supportGraffitiInSeries) {
            return new MultiDiyGraffitiControllerV3(graffitiV2.getDiyCode(), graffitiV2.getEffectBytes());
        }
        return new MultiDiyGraffitiController(graffitiV2.getDiyCode(), graffitiV2.getEffectBytes());
    }

    public static boolean supportColorStripMulti(int goodsType, int pactType, int pactCode) {
        boolean support = goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H61A9
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H612x
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H616C;
        if (support) return true;
        /*旧sku升级固件支持*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
            return pactType >= GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET_V3
                    && pactCode >= GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET_FRK;
        }
        return false;
    }

    public static void makeColorStripMode(BleIotInfo info, Colors colors) {
        boolean supportChangeColorPiece = Support.goodsTypeH612x(info.goodsType);
        boolean supportFactor20Pieces = Support.supportFactor20Pieces(info.goodsType);
        boolean supportFactor10Pieces = Support.supportFactor10Pieces(info.goodsType, info.sku);
        if (supportFactor20Pieces || supportChangeColorPiece) {
            SubModeColorV6 subModeColorV6 = new SubModeColorV6();
            subModeColorV6.rgbSet = colors.colorSet;
            subModeColorV6.brightnessSet = colors.brightnessSet;
            Mode mode = new Mode();
            mode.subMode = subModeColorV6;
            info.mode = mode;
        } else if (supportFactor10Pieces) {
            SubModeColorV3 subModeColor = new SubModeColorV3();
            subModeColor.rgbSet = colors.colorSet;
            subModeColor.brightnessSet = colors.brightnessSet;
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            info.mode = mode;
        } else {
            SubModeColorV2 subModeColorV6 = new SubModeColorV2();
            subModeColorV6.rgbSet = colors.colorSet;
            subModeColorV6.brightnessSet = colors.brightnessSet;
            Mode mode = new Mode();
            mode.subMode = subModeColorV6;
            info.mode = mode;
        }
    }

    public static boolean supportNewSettingAc4BleWifi(int goodsType, String sku, int pactType, int pactCode) {
        boolean support = goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H6169
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H616C;
        if (support) return true;
        /*旧sku升级固件支持*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
            return pactType >= GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET_V3
                    && pactCode >= GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET_FRK;
        }
        if (isH61B5(goodsType, sku)) return true;
        if (isH80B5(goodsType, sku)) return true;
        return isGoodsTypeH61A9(goodsType);
    }

    public static boolean supportPowerOffMemory(int goodsType, String sku, int pactType, int pactCode) {
        boolean support = goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H616C
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H612x;
        if (support) return true;
        /*旧sku升级固件支持*/
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET) {
            return pactType >= GoodsType.PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET_V3
                    && pactCode >= GoodsType.PACT_CODE_4_RGBIC_BLE_WIFI_SECRET_FRK;
        }
        if (isH61B5(goodsType, sku)) return true;
        if (isH80B5(goodsType, sku)) return true;
        return isGoodsTypeH61A9(goodsType);
    }

    public static boolean noSupportPlayList(int goodsType, int pactType, int pactCode) {
        boolean noSupport = goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                || goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H61A9
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H616C
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H612x;
        return noSupport;
        /*GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET-从V5.9.10版本开始都放开自动播放*/
    }

    /**
     * 是否是真色温设备-颜色模式下色温控制只能整条灯带设置
     */
    public static boolean isColorTemRealDevice(int goodType) {
        return GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3 == goodType;
    }

    public static int pairRes(int goodsType, String sku) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175
                || H6176.equals(sku)
                || H6173.equals(sku)
                || goodsType == GoodsType.GOODS_TYPE_VALUE_H616C
                || H6171.equals(sku)
        ) {
            return R.mipmap.new_pics_add_6061_anzhuang_yindao;
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H612x) {
            return R.mipmap.new_pics_add_h7095_anzhuang_yindao;
        }
        return R.mipmap.new_pics_add_public_anzhuang_yindao;
    }

    /**
     * 配置动态域名
     *
     * @return
     */
    public static void setDynamicApi() {
        DynamicDomainConfig.getInstance().setSupportHttps(H61A9);
        DynamicDomainConfig.getInstance().setSupportHttps("H802A");
    }

    public static boolean isH61B5(String sku) {
        return H61B5.equals(sku);
    }

    public static boolean isH80B5(String sku) {
        return H80B5.equals(sku);
    }

    public static boolean isH61B5(int goodsType, String sku) {
        return GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END == goodsType && H61B5.equals(sku);
    }

    public static boolean isH80B5(int goodsType, String sku) {
        return GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END == goodsType && H80B5.equals(sku);
    }

    public static boolean isH61A9(String sku) {
        return H61A9.equals(sku);
    }

    public static boolean isGoodsTypeH61A9(int goodsType) {
        return GoodsType.GOODS_TYPE_VALUE_H61A9 == goodsType;
    }

    public static void updateColorLen(int goodsType) {
        if (isGoodsTypeH61A9(goodsType)) {
            SubModeColorV2.len = 18;/*修改颜色分段长度  H61A9->18段*/
        } else {
            SubModeColorV2.len = 15;
        }
    }

    //详情页是否显示ai灯效入口
    public static boolean supportAiLight(int goodsType, String sku) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA && TextUtils.equals(sku, H61BA)) {
            return true;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC && TextUtils.equals(sku, H61BC)) {
            return true;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE && TextUtils.equals(sku, H61BE)) {
            return true;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET
                && (TextUtils.equals(sku, H61A1) || TextUtils.equals(sku, H61A2) || TextUtils.equals(sku, H61A3) || TextUtils.equals(sku, H61A0)
                || TextUtils.equals(sku, H619E) || TextUtils.equals(sku, H619D) || TextUtils.equals(sku, H619C) || TextUtils.equals(sku, H619B))) {
            return true;
        } else if (goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END
                && (TextUtils.equals(sku, H61E0) || TextUtils.equals(sku, H61E1))) {
            return true;
        } else
            return goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1 && TextUtils.equals(sku, H6117);
    }

    /**
     * @param sku
     * @param ic1
     * @param ic2
     * @return 612x系列段数
     */
    public static int[] get612xSectionNum(String sku, int ic1, int ic2) {
        int sectionNum1 = 0;
        int sectionNum2 = 0;
        switch (sku) {
            case "H612A":
                sectionNum1 = ic1 / 2 + ic1 % 2 == 0 ? 0 : 1;
                sectionNum1 = Math.max(Math.min(sectionNum1, 10), 1);
                break;
            case "H612B":
                sectionNum1 = ic1 / 3 + ic1 % 3 == 0 ? 0 : 1;
                sectionNum1 = Math.max(Math.min(sectionNum1, 10), 1);
                break;
            case "H612C":
                sectionNum1 = ic1 / 4 + ic1 % 4 == 0 ? 0 : 1;
                sectionNum1 = Math.max(Math.min(sectionNum1, 10), 1);
                break;
            case "H612D":
                sectionNum1 = ic1 / 3 + (ic1 % 3 == 0 ? 0 : 1);
                sectionNum2 = ic2 / 3 + (ic2 % 3 == 0 ? 0 : 1);
                sectionNum1 = Math.max(Math.min(sectionNum1, 10), 1);
                sectionNum2 = Math.max(Math.min(sectionNum2, 10), 1);
                break;
            case "H612E":
                sectionNum1 = ic1 / 4 + (ic1 % 4 == 0 ? 0 : 1);
                sectionNum2 = ic2 / 4 + (ic2 % 4 == 0 ? 0 : 1);
                sectionNum1 = Math.max(Math.min(sectionNum1, 10), 1);
                sectionNum2 = Math.max(Math.min(sectionNum2, 10), 1);
                break;
            case "H612F":
                sectionNum1 = ic1 / 2 + ic1 % 2 == 0 ? 0 : 1;
                sectionNum1 = Math.max(Math.min(sectionNum1, 10), 1);
                break;
        }
        return new int[]{sectionNum1, sectionNum2};
    }

    public static boolean supportV1UIV1(int goodsType, int pactType, int pactCode) {
        boolean goodsTypeSupport = false;
        boolean pactSupport = false;

        //校验goodsType
        for (int goodsType4Ble : deviceItemGoodsTypes4Ble) {
            if (goodsType4Ble == goodsType) {
                goodsTypeSupport = true;
                break;
            }
        }
        List<Protocol> supportProtocolsV1 = Support.supportProtocolsV1;
        for (Protocol protocol : supportProtocolsV1) {
            if (protocol.isSameProtocol(pactType, pactCode)) pactSupport = true;
        }

        return goodsTypeSupport && pactSupport;

    }

    public static boolean supportV1UIV3(int goodsType, int pactType, int pactCode) {
        boolean goodsTypeSupport = false;
        boolean pactSupport = false;

        //校验goodsType
        for (int goodsType4Ble : deviceItemGoodsTypes4Ble) {
            if (goodsType4Ble == goodsType) {
                goodsTypeSupport = true;
                break;
            }
        }
        //校验协议
        /*ble-BK*/
        for (Protocol protocol : Support.supportProtocolsV3) {
            if (protocol.isSameProtocol(pactType, pactCode)) pactSupport = true;
        }
        /*ble-限流-BK*/
        for (Protocol protocol : Support.supportProtocolsV4) {
            if (protocol.isSameProtocol(pactType, pactCode)) pactSupport = true;
        }
        /*ble-bk-支持设备校验*/
        for (Protocol pro : Support.supportProtocolsV6) {
            if (pro.isSameProtocol(pactType, pactCode)) pactSupport = true;
        }

        /*ble-frk-支持设备校验-H6171迭代款*/
        for (Protocol pro : Support.supportProtocolsV6_1) {
            if (pro.isSameProtocol(pactType, pactCode)) pactSupport = true;
        }

        return goodsTypeSupport && pactSupport;
    }

    public static boolean supportV2UIV1(int goodsType, int pactType, int pactCode) {
        boolean goodsTypeSupport = false;
        boolean pactSupport = false;

        //校验goodsType
        for (int goodsType4Ble : deviceItemGoodsTypes4BleIot) {
            if (goodsType4Ble == goodsType) {
                goodsTypeSupport = true;
                break;
            }
        }
        //AdjustAcV3走v2 uiv4
        if (isJump2AdjustAcV3(goodsType)) {
            goodsTypeSupport = false;
        }
        //这几个goodsType走v2 uiv3
        if (Support.isGoodsTypeH61A9(goodsType) || goodsType == GoodsType.GOODS_TYPE_VALUE_H616C || Support.goodsTypeH612x(goodsType)) {
            goodsTypeSupport = false;
        }

        pactSupport = Support.uiV14bleIotProtocol(pactType, pactCode);

        return goodsTypeSupport && pactSupport;
    }

    public static boolean supportV2UIV3(int goodsType, int pactType, int pactCode) {
        boolean goodsTypeSupport = false;
        boolean pactSupport = false;

        //校验goodsType
        for (int goodsType4Ble : deviceItemGoodsTypes4BleIot) {
            if (goodsType4Ble == goodsType) {
                goodsTypeSupport = true;
                break;
            }
        }
        boolean supportV2UIV1 = supportV2UIV1(goodsType, pactType, pactCode);

        pactSupport = Support.uiV34bleIotProtocol(pactType, pactCode);
        return goodsTypeSupport && pactSupport && !supportV2UIV1;
    }

    public static boolean supportV2UIV4(int goodsType, int pactType, int pactCode) {
        boolean goodsTypeSupport = false;
        boolean pactSupport = false;

        goodsTypeSupport = Support.isJump2AdjustAcV3(goodsType);

        pactSupport = Support.isUiV4bleIotProtocol(pactType, pactCode)
                || Support.isUiV4bleIotProtocol4FreshIc(pactType, pactCode)
                || Support.isUi4BleIotProtocol4H61BC(pactType, pactCode)
                || Support.isUi4BleIotProtocol4H6175(pactType, pactCode) || Support.isUi4BleIotProtocol4H61BE(pactType, pactCode) || Support.isUi4BleIotProtocol4H80B5(pactType, pactCode);

        return goodsTypeSupport && pactSupport;
    }

    /**
     * dreamcolorlightv1里支持可动态配置分段数的颜色模式
     *
     * @param goodsType
     * @return
     */
    public static boolean supportNormalColorMode(int goodsType) {
        //不用通用的颜色模式的sku
        boolean isH61A9 = Support.isGoodsTypeH61A9(goodsType);
        boolean isH61BE = goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE;
        boolean isTrueColorTem = Support.isColorTemRealDevice(goodsType) || Support.goodsTypeH612x(goodsType); //真色温
        return !isH61A9 && !isH61BE && !isTrueColorTem;
    }
}
