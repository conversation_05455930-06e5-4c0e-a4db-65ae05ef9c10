package com.govee.dreamcolorlightv1.scenes;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.iot.protype.IotMsgProType;
import com.govee.base2home.iot.protype.v2.IotMsgV2;
import com.govee.base2home.scenes.BaseCmd;
import com.govee.base2home.scenes.ICmd;
import com.govee.base2home.scenes.ICmdCallBack;
import com.govee.base2home.scenes.builder.model.SwitchModel;
import com.govee.dreamcolorlightv1.iot.Cmd;
import com.govee.dreamcolorlightv1.pact.Comm;
import com.ihoment.base2app.util.JsonUtil;

/**
 * Create by xieying<PERSON> on 2019-12-18
 * iot-开关控制$
 */
public class IotSwitchCmdBuilderV1 extends AbsIotCmdBuilderV1<SwitchModel> {
    @Override
    public ICmd createCmd(SwitchModel switchModel) {
        AbsCmd cmd = Comm.makeSwitchCmd4IotComm(switchModel.isOpen);
        String[] valueStr = makeTransactionAndCmdStr(cmd, switchModel.device);
        String transaction = valueStr[0];
        return new BaseCmd() {

            @Override
            public String getIotCmd() {
                return valueStr[1];
            }

            @Override
            public boolean parseMsg(String jsonStr, ICmdCallBack callBack) {
                IotMsgV2 msgV2 = JsonUtil.fromJson(jsonStr, IotMsgV2.class);
                if (msgV2 == null) return false;
                if (!transaction.equals(msgV2.transaction)) return false;
                if (callBack != null) {
                    callBack.onResult(Cmd.status.equals(msgV2.getCmd()));
                }
                return true;
            }

            @Override
            public IotMsgProType getIotProType() {
                return IotMsgProType.V2;
            }
        };
    }
}