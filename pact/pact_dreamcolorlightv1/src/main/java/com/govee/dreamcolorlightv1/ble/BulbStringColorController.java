package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsOnlyReadSingleController;
import com.govee.base2light.ble.controller.BleProtocolConstants;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020-03-03
 * 球泡串颜色-controller$
 */
public class BulbStringColorController extends AbsOnlyReadSingleController {
    private int group;

    public BulbStringColorController(int group) {
        this.group = group;
    }

    @Override
    protected void fail() {
        EventBulbStringColor.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.MSG_TYPE_READ_BULB_COLOR;
    }

    @Override
    protected byte[] translateRead() {
        return new byte[]{(byte) group};
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        BulbGroupColor bulbGroupColor = BulbGroupColor.parseBytes(validBytes);
        EventBulbStringColor.sendSuc(isWrite(), getCommandType(), getProType(), bulbGroupColor);
        return true;
    }

    public static boolean isReadBulbStringColorController(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opType = original20Bytes[1];
            return opTypeByte == BleProtocolConstants.SINGLE_READ &&
                    opType == BleProtocol.MSG_TYPE_READ_BULB_COLOR;
        }
        return false;
    }
}