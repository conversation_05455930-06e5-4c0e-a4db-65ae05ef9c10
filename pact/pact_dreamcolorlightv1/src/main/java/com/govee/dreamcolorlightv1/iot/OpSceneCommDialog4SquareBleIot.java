package com.govee.dreamcolorlightv1.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultiNewScenes;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.govee.dreamcolorlightv1.pact.Comm;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by linshicong on 1/28/21
 * op scene操作应用$
 */
public class OpSceneCommDialog4SquareBleIot extends AbsOpCommDialog4BleIotV2 {
    private AbsCmd absCmd;
    private AbsMultipleControllerV14Scenes controllerV14Scenes;

    protected OpSceneCommDialog4SquareBleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull AbsMultipleControllerV14Scenes controllerV14Scenes, boolean isBk) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_SCENES, -1);
        this.controllerV14Scenes = controllerV14Scenes;
        if (isBk) {
            absCmd = CmdPtReal.getNewScenesCmdPtReal(controllerV14Scenes);
        } else {
            absCmd = CmdPt.getNewScenesCmdPt(controllerV14Scenes);
        }
    }

    protected OpSceneCommDialog4SquareBleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, boolean on) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_SWITCH, -1);
        singleController = Comm.makeSwitchController4BleComm(on);
        absCmd = Comm.makeSwitchCmd4IotComm(on);
    }

    protected OpSceneCommDialog4SquareBleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, AbsDevice absDevice, int brightness4Percent) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_BRIGHTNESS, -1);
        singleController = Comm.makeBrightnessController4BleComm(absDevice, brightness4Percent);
        absCmd = Comm.makeBrightnessCmd4IotComm(absDevice.getSku(), absDevice.getVersionHard(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), brightness4Percent);
    }

    protected OpSceneCommDialog4SquareBleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, int type, @NonNull PtRealController ptRealController, @NonNull List<String> commands, boolean bleOp, boolean wifiOp, boolean isBk) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_DIY, type);
        if (bleOp) {
            this.ptRealController = ptRealController;
        }
        if (wifiOp) {
            if (isBk) {
                absCmd = CmdPtReal.getPtRealCmd(commands);
            } else {
                absCmd = CmdPt.getPtRealCmdPt(commands);
            }
        }
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, boolean on) {
        new OpSceneCommDialog4SquareBleIot(context, bleAddress, bleName, topic, sku, device, on).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, AbsDevice absDevice, int brightness4Percent) {
        new OpSceneCommDialog4SquareBleIot(context, bleAddress, bleName, topic, sku, device, absDevice, brightness4Percent).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull AbsMultipleControllerV14Scenes controllerV14Scenes, boolean isBk) {
        new OpSceneCommDialog4SquareBleIot(context, bleAddress, bleName, topic, sku, device, controllerV14Scenes, isBk).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, int type, @NonNull PtRealController ptRealController, @NonNull List<String> commands, boolean bleOp, boolean wifiOp, boolean isBk) {
        new OpSceneCommDialog4SquareBleIot(context, bleAddress, bleName, topic, sku, device, type, ptRealController, commands, bleOp, wifiOp, isBk).show();
    }

    @Nullable
    @Override
    protected AbsCmd getOpCmd() {
        return absCmd;
    }

    @Override
    protected void bleOping() {
        if (controllerV14Scenes != null) {
            getBle().sendMultipleControllerV1(controllerV14Scenes);
            return;
        }
        if (ptRealController != null) {
            getBle().sendMultipleController4PtReal(ptRealController);
            return;
        }
        if (singleController != null) {
            getBle().startController(singleController);
            return;
        }
        updateBleResult(false);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleNewScenes(EventMultiNewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleNewScenes() result = " + result + " ; scenesCode = " + scenesCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*场景效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*diy效果传输失败，则通知设置新场景模式失败*/
            getBle().clearControllers();
            updateBleResult(false);
            hide();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateBleResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}