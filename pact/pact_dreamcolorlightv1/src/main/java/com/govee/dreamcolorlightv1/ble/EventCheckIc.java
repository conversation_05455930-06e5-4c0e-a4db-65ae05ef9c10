package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by linshicong on 3/4/21
 */
public class EventCheckIc extends AbsControllerEvent {

    protected EventCheckIc(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventCheckIc(false, write, commandType, proType));
    }

    static void sendSuc(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventCheckIc(true, write, commandType, proType));
    }
}