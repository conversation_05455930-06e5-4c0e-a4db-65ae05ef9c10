package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-01-19
 * event-限流$
 */
public class EventLimit extends Abs<PERSON><PERSON>rollerEvent {
    public boolean openLimit;

    protected EventLimit(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    public static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventLimit(false, write, commandType, proType));
    }

    public static void sendSuc(boolean write, byte commandType, byte proType, boolean openLimit) {
        EventLimit event = new EventLimit(true, write, commandType, proType);
        event.openLimit = openLimit;
        EventBus.getDefault().post(event);
    }

    public static void sendWriteResult(boolean result, byte commandType, byte proType, boolean openLimit) {
        EventLimit event = new EventLimit(result, true, commandType, proType);
        event.openLimit = openLimit;
        EventBus.getDefault().post(event);
    }
}