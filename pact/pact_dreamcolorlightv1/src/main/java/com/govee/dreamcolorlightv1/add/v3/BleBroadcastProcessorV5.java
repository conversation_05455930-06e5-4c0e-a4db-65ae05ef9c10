package com.govee.dreamcolorlightv1.add.v3;

import android.app.Activity;

import com.govee.base2home.main.choose.BaseBleDeviceModel;
import com.govee.base2home.main.choose.BaseBleProcessor;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.sku.DefaultDeviceNameUtil;
import com.govee.dreamcolorlightv1.add.v2.AddInfoV2;
import com.govee.dreamcolorlightv1.adjust.AdjustAcV3;
import com.govee.dreamcolorlightv1.pact.Support;

/**
 * Create by xieyingwu on 2022/5/12
 */
public class BleBroadcastProcessorV5 extends BaseBleProcessor {
    @Override
    public boolean onItemClick(Activity activity, BaseBleDeviceModel model, boolean singleSku) {
        int goodsType = model.goodsType;
        Protocol protocol = model.protocol;
        /*ble+wifi高端灯带-支持做主盛宴设备*/
        if (Support.supportPact4RgbicHighEnd(goodsType, protocol)) {
            boolean checkLogin = checkLogin(activity);
            if (checkLogin) {
                return true;
            }
            AddInfoV2 addInfo = new AddInfoV2();
            String sku = model.getSku();
            addInfo.sku = sku;
            addInfo.goodsType = goodsType;
            addInfo.pactType = protocol.pactType;
            addInfo.pactCode = protocol.pactCode;
            addInfo.deviceName = Support.getSkuDefName(sku, model.getDeviceName());
            addInfo.deviceName = DefaultDeviceNameUtil.INSTANCE.getSkuDefaultName(model.getSku(), addInfo.deviceName);
            addInfo.bleName = model.getBleName();
            addInfo.bleAddress = model.getDevice().getAddress();
            addInfo.adjustAcClassName = AdjustAcV3.class.getName();
            ConnectDialogV4.showDialog(activity, addInfo, model.getDevice());
            return true;
        }
        return false;
    }
}