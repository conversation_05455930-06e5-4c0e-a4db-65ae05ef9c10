package com.govee.dreamcolorlightv1.adjust.v2;


import com.govee.ui.R;
import android.app.Activity;
import android.bluetooth.BluetoothDevice;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.custom.LoadingDialog;
import com.govee.base2home.event.EventBleStatus;
import com.govee.base2home.guide.GuideDialog;
import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.pact.Command4PtReal;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2home.util.Encode;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.Constant;
import com.govee.base2light.ac.adjust.EventSleepUpdate;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.TimerInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ac.club.EventColorStrip;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.EventDiyApply;
import com.govee.base2light.ac.diy.EventDiyApplyResult;
import com.govee.base2light.ac.diy.EventDiyApplyV2;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v1.EventDiyEffectOp;
import com.govee.base2light.ac.diy.v2.DiyOpM;
import com.govee.base2light.ac.diy.v2.DiyTemplate;
import com.govee.base2light.ac.diy.v2.DiyValue;
import com.govee.base2light.ac.diy.v2.LastDiyConfig;
import com.govee.base2light.ac.diy.v3.AcDiyGroup;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyGroup;
import com.govee.base2light.ac.diy.v3.DiyShortcutManger;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ac.diy.v3.Event2AcDiyGroup;
import com.govee.base2light.ac.diy.v3.EventDiyApply4InModeShowing;
import com.govee.base2light.ac.diy.v3.EventDiyModeShowingChange;
import com.govee.base2light.ac.diy.v3.Util4Diy;
import com.govee.base2light.ac.effect.ConnectDialog;
import com.govee.base2light.ac.effect.EffectAc;
import com.govee.base2light.ac.effect.EffectEvent;
import com.govee.base2light.ac.effect.EventEffectSquareOpResult;
import com.govee.base2light.ac.effect.EventScenesEffect;
import com.govee.base2light.ac.effect.EventTryConnectDevice;
import com.govee.base2light.ac.timer.NewShowTimerAcV2;
import com.govee.base2light.ac.timer.NewSleepSetEvent;
import com.govee.base2light.ac.timer.NewTimerSetEvent;
import com.govee.base2light.ac.timer.NewWakeupSetEvent;
import com.govee.base2light.ac.timer.SleepFailEvent;
import com.govee.base2light.ac.timer.SleepSucEvent;
import com.govee.base2light.ac.timer.TimerFailEvent;
import com.govee.base2light.ac.timer.TimerResultEvent;
import com.govee.base2light.ac.timer.TimerSucEvent;
import com.govee.base2light.ac.timer.UpdateTimeEvent;
import com.govee.base2light.ac.timer.WakeupFailEvent;
import com.govee.base2light.ac.timer.WakeupSucEvent;
import com.govee.base2light.ac.update.OtaUpdateAcV1;
import com.govee.base2light.aieffect.ui.AILightUI;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.ScenesRgb;
import com.govee.base2light.ble.ScenesRgbIC;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.AbsMultipleControllerV1;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.AutoTimeController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultiDiyGraffitiController;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.controller.MultipleDiyController;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.ble.controller.SleepController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.controller.SyncTimeController;
import com.govee.base2light.ble.controller.SyncTimeInfo;
import com.govee.base2light.ble.controller.WakeUpController;
import com.govee.base2light.ble.mic.controller.EventSwitchMicPickUpType;
import com.govee.base2light.ble.mic.controller.MicSetRgbController;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.EventNewMusicOpResult;
import com.govee.base2light.ble.music.EventSetMultiMusicEffect;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.ble.scenes.EditScenesAc;
import com.govee.base2light.ble.scenes.EventChangeScenes;
import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.effectPlay.EffectPlayUI;
import com.govee.base2light.iot.ResultBrightness;
import com.govee.base2light.light.EventServiceScenesFresh;
import com.govee.base2light.light.v1.AbsColorFragmentV8;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.light.v1.RealtimeColorChangeEvent;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.EventSceneCheck4BleIot;
import com.govee.base2light.pact.EventSceneCheck4BleIotV1;
import com.govee.base2light.pact.FilterSceneInfo4BleIot;
import com.govee.base2light.pact.IUi4BleIot;
import com.govee.base2light.pact.IUiResult4BleIot;
import com.govee.base2light.pact.ble.IBleOpResult;
import com.govee.base2light.pact.iot.IIotOpResult;
import com.govee.base2light.snapshot.EventApplySnapshotCmd;
import com.govee.base2light.snapshot.EventCheckSnapshotEnable;
import com.govee.base2light.snapshot.SnapshotCmdM;
import com.govee.base2light.snapshot.SnapshotUI;
import com.govee.base2light.ui.mode.ChangeModeEvent;
import com.govee.base2light.util.Util4ColorRealtime;
import com.govee.base2light.util.UtilFlag;
import com.govee.ble.BleController;

import com.govee.dreamcolorlightv1.adjust.Diy;
import com.govee.dreamcolorlightv1.adjust.SnapshotDataUtilV2;
import com.govee.dreamcolorlightv1.ble.BleComm;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.EventChangeGradual;
import com.govee.dreamcolorlightv1.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.ParamsSubMode4Music;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeMusic;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV1;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV2;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.govee.dreamcolorlightv1.iot.Cmd;
import com.govee.dreamcolorlightv1.iot.CmdBrightness;
import com.govee.dreamcolorlightv1.iot.CmdBulb;
import com.govee.dreamcolorlightv1.iot.CmdPt;
import com.govee.dreamcolorlightv1.iot.CmdStatus;
import com.govee.dreamcolorlightv1.iot.CmdStatusV0;
import com.govee.dreamcolorlightv1.iot.CmdTurn;
import com.govee.dreamcolorlightv1.iot.ResultBulb;
import com.govee.dreamcolorlightv1.iot.ResultPt;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.home.account.config.AccountConfig;
import com.govee.ui.Cons;
import com.govee.ui.component.BrightnessUI;
import com.govee.ui.component.EffectUI;
import com.govee.ui.component.NewTimerUI;
import com.govee.ui.dialog.ConfirmDialog;
import com.govee.ui.dialog.HintDialog1;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.CaughtRunnable;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.ResUtil;
import com.ihoment.base2app.util.ToastUtil;
import com.zhy.android.percent.support.PercentRelativeLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2020/3/5
 * ui-v1版本$
 */
class UiV1 implements IUi4BleIot {
    private static final String TAG = "UiV1";

    private static final int ui_type_def = -1;
    private static final int ui_type_normal = 1;
    private static final int ui_type_fail = 2;

    private static final int max_retry_connect_device_times = 2;

    private IUiResult4BleIot uiResult;
    private BleIotInfo info;
    private ExtV1 ext = new ExtV1();
    private BleOpV2 bleOp;
    private IotOpV2 iotOp;

    private Activity ac;
    private boolean layoutSuc;/*布局是否已初始化*/
    private boolean destroy;
    private int uiTypeBle = ui_type_def;
    private int uiTypeIot = ui_type_def;

    private NewTimerUI timerUI;
    private EffectPlayUI playUi;
    private AILightUI aiLightUI;
    private SnapshotUI snapshotUI;
    private BrightnessUI brightnessUI;
    private EffectUI effectUI;
    private AbsMode4UIV1 modeUI;
    private List<DiyGroup> curDiyGroups = new ArrayList<>();
    private Handler handler = new Handler(Looper.getMainLooper());

    private int diyCode = -1;
    private int diyTemplateCode = -1;
    private int lastOpType = -1;

    private boolean isSwitchMicPickUpType;

    private int retryConnectDeviceTimes;/*尝试连接设备次数*/

    private boolean toUpdateUI;

    private int[] rgbSet = new int[15];/*记下每段颜色*/

    private final IBleOpResult bleOpResult = new IBleOpResult() {
        @Override
        public void onOffChange() {
            showLoading();
        }

        @Override
        public void infoOver() {
            uiTypeBle = ui_type_normal;
            toUpdateUI = true;
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
        }

        @Override
        public void noConnect() {
            uiTypeBle = ui_type_fail;
            /*蓝牙断开，需要检测iot是否可用*/
            bleUnable2CheckIot();
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
        }

        @Override
        public void bleWrite(byte proCommandType, boolean result) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "bleWrite() proCommandType = " + proCommandType + " ; result = " + result);
            }
            if (result && proCommandType == BleProtocol.SINGLE_GRADUAL_CHANGE_4_WIFI_BLE) {
                /*若当前就是颜色模式，则提示当前仅仅设置渐变逻辑*/
                subModeColor2Gradual();
            }
            diyCode = -1;
            diyTemplateCode = -1;
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(result ? EventEffectSquareOpResult.result_op_suc : EventEffectSquareOpResult.result_op_fail);
        }
    };

    private void subModeColor2Gradual() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeColorV2) {
            ((SubModeColorV2) subMode).opType = SubModeColorV2.op_type_gradual;
        }
        if (subMode instanceof SubModeColor) {
            ((SubModeColor) subMode).opType = SubModeColor.op_type_gradual;
        }
    }

    private void bleUnable2CheckIot() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "bleUnable2CheckIot() uiTypeIot = " + uiTypeIot + " ; retryConnectDeviceTimes = " + retryConnectDeviceTimes);
        }
        if (uiTypeIot != ui_type_normal) {
            if (retryConnectDeviceTimes < max_retry_connect_device_times) {
                retryConnectDeviceTimes++;
                if (iotOp.isOpCommEnable()) {
                    iotOp.readCmd(new CmdStatus());
                } else {
                    uiTypeIot = ui_type_def;
                    iotOp.beOpComm(info.sku, info.device, info.topic);
                }
            }
        }
    }

    private void bleUnable2CheckIot4OldColorMode() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "bleUnable2CheckIot4OldColorMode() uiTypeBle = " + uiTypeBle + " ; retryConnectDeviceTimes = " + retryConnectDeviceTimes);
        }
        if (toUpdateUI && ui_type_fail == uiTypeBle) {
            if (retryConnectDeviceTimes < max_retry_connect_device_times) {
                retryConnectDeviceTimes++;
                if (iotOp.isOpCommEnable()) {
                    iotOp.readCmd(new CmdStatus());
                } else {
                    uiTypeIot = ui_type_def;
                    iotOp.beOpComm(info.sku, info.device, info.topic);
                }
            }
        }
    }

    private final IIotOpResult iotOpResult = new IIotOpResult() {
        @Override
        public void noConnectIot() {
            uiTypeIot = ui_type_fail;
            /*iot断开，检测蓝牙是否可用*/
            iotUnable2CheckBle();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
            checkUi();
        }

        @Override
        public void cmdWriteFail(boolean overtime, AbsCmd absCmd) {
            String cmd = absCmd.getCmd();
            if (Cmd.pt.equals(cmd)) {
                CmdPt cmdPt = (CmdPt) absCmd;
                iotOpPtFail(cmdPt);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
            if (overtime) {
                uiTypeIot = ui_type_fail;
                /*iot断开，检测蓝牙是否可用*/
                iotUnable2CheckBle();
            }
            checkUi();
        }

        @Override
        public void cmdWriteSuc(AbsCmd absCmd) {
            String cmd = absCmd.getCmd();
            if (Cmd.bulb.equals(cmd)) {
                ((CmdBulb) absCmd).logValue();
                SubModeColorV2 subModeColor = ((CmdBulb) absCmd).isPtReal4SetPartColor();
                boolean isSetColor4Strip = ((CmdBulb) absCmd).isSetColorStrip();
                if (subModeColor != null) {
                    /*采用ptReal进行颜色设置-更新刷新ui逻辑*/
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "cmdWriteSuc4Pt() 采用ptReal进行颜色设置");
                    }
                    Mode mode = new Mode();
                    mode.subMode = subModeColor;
                    ext.gradual = subModeColor.gradual;
                    info.mode = mode;
                } else if (isSetColor4Strip) {
                    boolean isSetColor4StripWithBrightness = ((CmdBulb) absCmd).isSetColorStripWithBrightness();
                    if (isSetColor4StripWithBrightness) {
                        SubModeColorV2 subModeColorV2 = ((CmdBulb) absCmd).setPartColor4ColorStripWithBrightness();
                        Mode mode = new Mode();
                        mode.subMode = subModeColorV2;
                        info.mode = mode;
                    } else {
                        SubModeColor subModeColor4Strip = ((CmdBulb) absCmd).setPartColor4ColorStrip();
                        Mode mode = new Mode();
                        mode.subMode = subModeColor4Strip;
                        info.mode = mode;
                    }
                } else {
                    byte[] opCommandBytes = ((CmdBulb) absCmd).getOpCommandBytes();
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parseWritePt() opCommandBytes = " + BleUtil.bytesToHexString(opCommandBytes));
                    }
                    if (opCommandBytes == null || opCommandBytes.length != 20) {
                        if (LogInfra.openLog()) {
                            LogInfra.Log.e(TAG, "parseWritePt() ble协议不完整");
                        }
                        return;
                    }
                    byte[] valid17Bytes = BleUtil.parseValidBleBytes(opCommandBytes);
                    /*模式操作成功*/
                    byte subModeType = valid17Bytes[0];
                    byte[] subModeValidBytes = new byte[valid17Bytes.length - 1];
                    System.arraycopy(valid17Bytes, 1, subModeValidBytes, 0, subModeValidBytes.length);
                    int multiNewMusicVersion = Support.getMultiNewMusicVersion4Telink(bleOp.isOpCommEnable(), info.sku, info.versionSoft, ext.wifiSoftVersion);
                    ISubMode subMode = Mode.parseWriteSubMode(0, multiNewMusicVersion, subModeType, subModeValidBytes);
                    if (subMode != null) {
                        Mode mode = new Mode();
                        mode.subMode = subMode;
                        info.mode = mode;
                        diyCode = -1;
                        diyTemplateCode = -1;
                    }
                }
            }
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
        }

        @Override
        public void cmdRead(String cmd, String cmdJsonStr) {
            if (uiTypeBle == ui_type_normal) return;/*蓝牙通信可用，忽略iot通信信息*/
            if (Cmd.brightness.equals(cmd)) {
                /*解析亮度*/
                ResultBrightness resultBrightness = JsonUtil.fromJson(cmdJsonStr, ResultBrightness.class);
                if (resultBrightness != null) {
                    ext.brightness = resultBrightness.getBrightness();
                }
            } else if (Cmd.pt.equals(cmd)) {
                /*解析透传*/
                ResultPt resultPt = JsonUtil.fromJson(cmdJsonStr, ResultPt.class);
                parseResultPt(resultPt);
            } else if (Cmd.bulb.equals(cmd)) {
                /*解析球泡串颜色*/
                ResultBulb resultBulb = JsonUtil.fromJson(cmdJsonStr, ResultBulb.class);
                if (resultBulb != null) {
                    List<String> value = resultBulb.value;
                    for (String str : value) {
                        if (LogInfra.openLog()) {
                            LogInfra.Log.d(TAG, "resultBulb value = " + BleUtil.bytesToHexString(Encode.decryByBase64(str)));
                        }
                    }
                    if (value.size() == 6 && CmdStatusV0.isNewColorMode4Bulb(value.get(0)) && CmdStatusV0.isReadColorMode4Bulb(value.get(1))) {/*第一包写新颜色模式带亮度，后5包返灯的颜色和亮度*/
                        SubModeColorV2 subModeColor = CmdStatusV0.parseBulbColorRgbSet4NewColor(value);
                        if (subModeColor.brightness != 0) {
                            /*调节亮度*/
                            subModeColor.rgb = ((SubModeColorV2) info.mode.subMode).rgb;
                            subModeColor.rgbSet = ((SubModeColorV2) info.mode.subMode).rgbSet;
                        }
                        Mode mode = new Mode();
                        mode.subMode = subModeColor;
                        info.mode = mode;
                    } else if (value.size() > 5 && CmdStatusV0.isNewColorMode4Bulb(value.get(0))) { /*多包写新颜色模式带亮度,点我的颜色色条时执行*/
                        SubModeColorV2 subModeColor = CmdStatusV0.parseColorStrip(value);
                        Mode mode = new Mode();
                        mode.subMode = subModeColor;
                        info.mode = mode;
                    } else if (value.size() > 5 && CmdStatusV0.isColorMode4Bulb(value.get(0))) { /*多包写颜色模式,点我的颜色色条时执行*/
                        SubModeColor subModeColor = CmdStatusV0.parseColorStrip4SubModeColor(value);
                        Mode mode = new Mode();
                        mode.subMode = subModeColor;
                        info.mode = mode;
                    } else if (value.size() > 5 && CmdStatusV0.isSetColorStrip4Ios(value.get(0))) { /*ios那边发色条第一包是设渐变,兼容ios发色条android同步*/
                        value.remove(0);/*去掉第一包渐变包然后解析*/
                        if (CmdStatusV0.isNewColorMode4Bulb(value.get(0))) {
                            SubModeColorV2 subModeColor = CmdStatusV0.parseColorStrip(value);
                            Mode mode = new Mode();
                            mode.subMode = subModeColor;
                            info.mode = mode;
                        } else if (CmdStatusV0.isColorMode4Bulb(value.get(0))) {
                            SubModeColor subModeColor = CmdStatusV0.parseColorStrip4SubModeColor(value);
                            Mode mode = new Mode();
                            mode.subMode = subModeColor;
                            info.mode = mode;
                        }
                    } else {
                        SubModeColor subModeColor = CmdStatusV0.parseBulbColorRgbSet(value);
                        Mode mode = new Mode();
                        mode.subMode = subModeColor;
                        info.mode = mode;
                    }
                }
            }
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
        }

        @Override
        public void cmdOnline(String softVersion, String cmdJsonStr, String wifiSoftVersion) {
            CmdStatusV0 statusV0 = CmdStatusV0.parseJson(info.sku, softVersion, cmdJsonStr);
            uiTypeIot = statusV0 == null ? ui_type_fail : ui_type_normal;
            if (!TextUtils.isEmpty(wifiSoftVersion)) {
                ext.wifiSoftVersion = wifiSoftVersion;
            }
            if (uiTypeBle == ui_type_normal) {
                /*但需要同步Wi-Fi的版本信息*/
                if (statusV0 != null) {
                    if (!TextUtils.isEmpty(statusV0.wifiSoftVersion)) {
                        ext.wifiSoftVersion = statusV0.wifiSoftVersion;
                    }
                    if (!TextUtils.isEmpty(statusV0.wifiHardVersion)) {
                        ext.wifiHardVersion = statusV0.wifiHardVersion;
                    }
                }
                return;/*蓝牙通信可用，忽略iot通信信息*/
            }
            if (statusV0 == null) {
                /*iot断开，检测蓝牙是否可用*/
                iotUnable2CheckBle();
            } else {
                info.open = statusV0.on;
                ext.brightness = statusV0.brightness;

                info.versionSoft = statusV0.softVersion;
                info.mode = statusV0.mode;
                if (isSubColorMode(info.mode)) {
                    ext.gradual = statusV0.gradual;
                }
                if (!TextUtils.isEmpty(statusV0.wifiSoftVersion)) {
                    ext.wifiSoftVersion = statusV0.wifiSoftVersion;
                }
                if (!TextUtils.isEmpty(statusV0.wifiHardVersion)) {
                    ext.wifiHardVersion = statusV0.wifiHardVersion;
                }
                ext.timerInfo1 = statusV0.timerInfo1;
                ext.timerInfo2 = statusV0.timerInfo2;
                ext.wakeUpInfo = statusV0.wakeUpInfo;
                ext.sleepInfo = statusV0.sleepInfo;
                /*通知事件*/
                TimerSucEvent.sendTimerSucEvent(false, ext.timerInfo1, ext.timerInfo2);
                SleepSucEvent.sendSleepSucEvent(false, ext.sleepInfo);
                WakeupSucEvent.sendWakeUpSucEvent(false, ext.wakeUpInfo);
                EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
            }
            checkUi();
        }
    };

    private void iotUnable2CheckBle() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "iotUnable2CheckBle() uiTypeBle = " + uiTypeBle + " ; retryConnectDeviceTimes = " + retryConnectDeviceTimes);
        }
        if (uiTypeBle != ui_type_normal) {
            if (retryConnectDeviceTimes < max_retry_connect_device_times) {
                retryConnectDeviceTimes++;
                boolean opCommEnable = bleOp.isOpCommEnable();
                if (!opCommEnable) {
                    uiTypeBle = ui_type_def;
                    BluetoothDevice bluetoothDevice = null;
                    if (uiResult != null) bluetoothDevice = uiResult.getCurBluetoothDevice();
                    bleOp.beOpComm(bluetoothDevice);
                }
            }

        }
    }

    private void parseResultPt(ResultPt resultPt) {
        if (resultPt == null) return;
        String opcode = resultPt.getOpcode();
        byte[] opBytes = resultPt.getOpBytes();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parseResultPt() opcode = " + opcode + " ; bytesHexString = " + BleUtil.bytesToHexString(opBytes));
        }
        if (TextUtils.isEmpty(opcode) || opBytes == null || opBytes.length != 20) return;
        byte[] validBytes = BleUtil.parseValidBleBytes(opBytes);
        switch (opcode) {
            case CmdPt.pt_op_wakeup:
                /*解析唤醒透传信息*/
                ext.wakeUpInfo = WakeUpInfo.parseBytes(validBytes);
                WakeupSucEvent.sendWakeUpSucEvent(true, ext.wakeUpInfo);
                break;
            case CmdPt.pt_op_sleep:
                /*解析睡眠透传信息*/
                ext.sleepInfo = SleepInfo.parseBytes(validBytes);
                SleepSucEvent.sendSleepSucEvent(true, ext.sleepInfo);
                break;
            case CmdPt.pt_op_timer:
                /*解析定时透传信息*/
                List<String> timerValueSet = resultPt.getTimerValue();
                if (timerValueSet != null && !timerValueSet.isEmpty()) {
                    for (String bleBase64BytesStr : timerValueSet) {
                        byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
                        if (LogInfra.openLog()) {
                            LogInfra.Log.i(TAG, "parseResultPt() timer originalBytes = " + BleUtil.bytesToHexString(originalBytes));
                        }
                        if (originalBytes != null && originalBytes.length == 20) {
                            byte[] timerValidBytes = BleUtil.parseValidBleBytes(originalBytes);
                            int group = BleUtil.getUnsignedByte(timerValidBytes[5]);/*群组*/
                            if (LogInfra.openLog()) {
                                LogInfra.Log.i(TAG, "parseResultPt() time group = " + group);
                            }
                            TimerInfo timerInfo = TimerInfo.parseBytes(timerValidBytes);
                            if (group == 0) {
                                ext.timerInfo1 = timerInfo;
                            } else {
                                ext.timerInfo2 = timerInfo;
                            }
                        }
                    }
                }
                TimerSucEvent.sendTimerSucEvent(true, ext.timerInfo1, ext.timerInfo2);
                break;
            case CmdPt.pt_op_mode:
                /*解析模式透传信息*/
                Mode mode = new Mode();
                mode.parse(validBytes);
                ISubMode subMode = mode.subMode;
                if (subMode instanceof SubModeColor) {
                    /*颜色模式;需要解析球泡串的各个球泡的色值*/
                    ((SubModeColor) subMode).rgbSet = resultPt.getOpMode4SubModeColorEffectRgbSet();
                } else if (subMode instanceof SubModeNewDiy) {
                    EventDiyApplyResult.sendEventDiyApplyResult(true, ((SubModeNewDiy) subMode).getDiyCode());
                } else if (subMode instanceof SubModeScenes) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parseResultPt() diyCode = " + diyCode + " ; diyTemplateCode = " + diyTemplateCode);
                    }
                    if (diyCode != -1 && diyTemplateCode != -1) {
                        int effect = ((SubModeScenes) subMode).getEffect();
                        if (effect == diyTemplateCode) {
                            /*表明当前是diy模版效果应用成功*/
                            mode.subMode = new SubModeNewDiy(diyCode);
                            EventDiyApplyResult.sendEventDiyApplyResult(true, diyCode);
                        }
                    }
                } else if (subMode instanceof ParamsSubMode4Music) {
                    int multiNewMusicVersion = Support.getMultiNewMusicVersion4Telink(false, info.sku, info.versionSoft, ext.wifiSoftVersion);
                    ISubMode subModeNew = ((ParamsSubMode4Music) subMode).toSupportSubMode(multiNewMusicVersion);
                    mode.subMode = subModeNew;
                    if (subModeNew instanceof SubModeMusicV2) {
                        EventNewMusicOpResult.sendEventNewMusicOpResult(true, ((SubModeMusicV2) subModeNew).getMusicCode());
                    }
                }
                diyCode = -1;
                diyTemplateCode = -1;
                info.mode = mode;
                break;
            case CmdPt.pt_op_gradual:
                /*解析渐变信息*/
                ext.gradual = validBytes[0];
                subModeColor2Gradual();
                break;
        }
    }

    private void iotOpPtFail(CmdPt cmdPt) {
        String op = cmdPt.getOp();
        if (CmdPt.pt_op_timer.equals(op)) {
            /*定时操作失败*/
            TimerResultEvent.sendTimerResultEventFail(true);
        } else if (CmdPt.pt_op_sleep.equals(op)) {
            /*睡眠操作失败*/
            SleepFailEvent.sendSleepFailEvent(true);
        } else if (CmdPt.pt_op_wakeup.equals(op)) {
            /*唤醒操作失败*/
            WakeupFailEvent.sendWakeupFailEvent(true);
        } else if (CmdPt.pt_op_mode.equals(op)) {
            /*diy模式应用失败*/
            byte[] modeBytes = cmdPt.getModeBytes();
            if (modeBytes != null && modeBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(modeBytes);
                Mode modeModel = new Mode();
                modeModel.parse(valid17Bytes);
                ISubMode subMode = modeModel.subMode;
                if (subMode instanceof SubModeNewDiy) {
                    int diyCode = ((SubModeNewDiy) subMode).getDiyCode();
                    EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
                }
            }
        }
    }

    public UiV1(IUiResult4BleIot uiResult, BleIotInfo info) {
        this.uiResult = uiResult;
        this.info = info;
        bleOp = new BleOpV2(info, ext);
        iotOp = new IotOpV2();
    }

    private void registerEvent(boolean register) {
        if (register) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this);
            }
        } else {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        }
    }

    @Override
    public boolean isSupportProtocol(int pactType, int pactCode) {
        return Support.uiV14bleIotProtocol(pactType, pactCode);
    }

    @Override
    public void destroy() {
        if (!destroy) {
            destroy = true;
            registerEvent(false);
            bleOp.destroy();
            iotOp.destroy();
            hideLoading();
            ac = null;
            if (timerUI != null) timerUI.onDestroy();
            if (aiLightUI != null) aiLightUI.onDestroy();
            if (playUi != null) playUi.onDestroy();
            if (snapshotUI != null) snapshotUI.onDestroy();
            if (brightnessUI != null) brightnessUI.onDestroy();
            if (modeUI != null) modeUI.onDestroy();
            if (effectUI != null) effectUI.onDestroy();
            hideDiyGuide();
        }
    }

    @Override
    public void onOffChange() {
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            showLoading();
            int delay = 0;
            if (modeUI.isMicMode()) {
                delay = 100;
            }
            handler.postDelayed(new CaughtRunnable() {
                @Override
                protected void runSafe() {
                    SwitchController switchController = new SwitchController(!info.open);
                    bleOp.executeOp(switchController);
                }
            }, delay);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            CmdTurn cmdTurn = new CmdTurn(!info.open);
            iotOp.writeCmd(cmdTurn);
        }
    }

    @Override
    public void toUpdateAc(@NonNull AppCompatActivity ac) {
        if (info.canUpdate() && bleOp.isOpCommEnable()) {
            int defSkuRes = ThemeM.getDefSkuRes(info.sku, info.spec);
            OtaUpdateAcV1.jump2OtaUpdateAcV1(ac, info.sku, info.deviceName, info.versionSoft, defSkuRes, info.checkVersion);
        }
    }

    @Override
    public void layout(@NonNull AppCompatActivity ac, @NonNull PercentRelativeLayout contentParent, int headerId) {
        /*注册事件*/
        registerEvent(true);
        checkMicMode();
        this.ac = ac;
        if (!layoutSuc) {
            layoutSuc = true;
            /*初始化ui组件*/
            int[] ids = new int[]{100, 101, 102, 103, 104, 105, 106, 107};

            effectUI = new EffectUI(ac);
            View fucViewEffect = effectUI.getFucView();
            fucViewEffect.setId(ids[4]);
            addViewMargin(contentParent, fucViewEffect, headerId, effectUI.getWidth(), effectUI.getHeight(), AppUtil.getScreenWidth() * 16 / 750);

            /*添加布局-定时*/
            timerUI = new NewTimerUI(ac);
            View fucViewTimer = timerUI.getFucView();
            fucViewTimer.setId(ids[0]);
            addViewMargin(contentParent, fucViewTimer, fucViewEffect.getId(), timerUI.getWidth(), timerUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);

            /*添加布局-自动播放*/
            playUi = new EffectPlayUI(ac, info, getDiySupport(), Support.getSupportScenesOpSet(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard));
            View fucViewPlay = playUi.getFucView();
            fucViewPlay.setId(ids[7]);
            addViewMargin(contentParent, fucViewPlay, fucViewTimer.getId(), playUi.getWidth(), playUi.getHeight(), AppUtil.getScreenWidth() * 5 / 375);

            int belowIdForSnapshot = fucViewPlay.getId();
            if (Support.supportAiLight(info.goodsType, info.sku)) {
                aiLightUI = new AILightUI(ac, bleOp, null, iotOp, info);
                View fucViewAiLight = aiLightUI.getFucView();
                fucViewAiLight.setId(ids[5]);
                addViewMargin(contentParent, fucViewAiLight, fucViewPlay.getId(), aiLightUI.getWidth(), aiLightUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
                belowIdForSnapshot = fucViewAiLight.getId();
            }

            snapshotUI = new SnapshotUI(ac, info);
            /*添加布局-快照*/
            View fucViewSnapshot = snapshotUI.getFucView();
            fucViewSnapshot.setId(ids[6]);
            addViewMargin(contentParent, fucViewSnapshot, belowIdForSnapshot, snapshotUI.getWidth(), snapshotUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);

            /*添加布局-亮度*/
            int[] brightnessRange = Support.getBrightnessRange(info.sku, info.versionHard);
            brightnessUI = new BrightnessUI(ac, brightnessRange[1], brightnessRange[0], true);
            View fucViewBrightness = brightnessUI.getFucView();
            fucViewBrightness.setId(ids[1]);
            addViewMargin(contentParent, fucViewBrightness, fucViewSnapshot.getId(), brightnessUI.getWidth(), brightnessUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
            modeUI = new ModeUiV2(ac, info.sku, info.device, info.goodsType, info.ic, getDiySupport(), Support.noSupportHighColor4SpecialSku(info.sku, info.versionHard));
            View fucViewMode = modeUI.getFucView();
            fucViewMode.setId(ids[3]);
            addViewMargin(contentParent, fucViewMode, fucViewBrightness.getId(), modeUI.getWidth(), modeUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
        }
        bleOp.setOpResult(bleOpResult);
        iotOp.setOpResult(iotOpResult);
        /*开始进行op通信*/
        startOpComm();
        /*通知刷新DIY模式*/
        EventDiyModeShowingChange.sendEventDiyModeShowingChange();
    }

    private void startOpComm() {
        /*ble通信处理器*/
        boolean opCommEnableBle = bleOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "startOpComm() opCommEnableBle = " + opCommEnableBle);
        }
        if (!opCommEnableBle) {
            uiTypeBle = ui_type_def;
            BluetoothDevice bluetoothDevice = null;
            if (uiResult != null) bluetoothDevice = uiResult.getCurBluetoothDevice();
            bleOp.beOpComm(bluetoothDevice);
        }
        /*iot通信处理器*/
        boolean opCommEnableIot = iotOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "startOpComm() opCommEnableIot = " + opCommEnableIot);
        }
        if (!opCommEnableIot) {
            uiTypeIot = ui_type_def;
            iotOp.beOpComm(info.sku, info.device, info.topic);
        }
        /*刷新ui*/
        checkUi();
    }

    @Override
    public void uiLost() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "uiLost() layoutSuc = " + layoutSuc + " ; uiTypeIot = " + uiTypeIot + " ; uiTypeBle = " + uiTypeBle);
        }
        if (layoutSuc && (uiTypeIot == ui_type_normal || uiTypeBle == ui_type_normal)) {
            uiTypeBle = ui_type_fail;
            uiTypeIot = ui_type_fail;
            checkUi(false);
        }
        hideDiyGuide();
    }

    private void checkMicMode() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i("MicFragment", "checkMicMode sku device：" + info.sku + info.device);
        }
        String versionSoft = info.versionSoft;
        Support.isSupportMicByPhone(info.sku, info.device, versionSoft, info.goodsType);
    }

    private void checkUi() {
        checkUi(true);
    }

    private void checkUi(boolean reportResult) {
        if (destroy) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkUi() uiTypeBle = " + uiTypeBle + " ; uiTypeIot = " + uiTypeIot);
        }
        if (ui_type_normal == uiTypeBle || ui_type_normal == uiTypeIot) {
            int opType = getOpType();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkUi() opType = " + opType + " ; lastOpType = " + lastOpType);
            }
            /*操作可控发生变化，需要重新判断支持的场景逻辑*/
            if (lastOpType != opType) {
                lastOpType = opType;
                EventSceneCheck4BleIot.sendEventSceneCheck4BleIot(info, opType == 0, ext.wifiSoftVersion, ext.wifiHardVersion);
            }
            /*重置连接次数*/
            retryConnectDeviceTimes = 0;
            /*通信成功*/
            hideLoading();
            timerUI.show();
            if (Support.supportAiLight(info.goodsType, info.sku) && aiLightUI != null) {
                aiLightUI.show();
            }
            playUi.show();
            effectUI.show();
            info.headerOpType = ui_type_normal == uiTypeBle ? Cons.op_type_ble : Cons.op_type_iot;
            boolean open = info.open;
            if (open) {
                brightnessUI.show();
                snapshotUI.show();
                brightnessUI.updateBrightness(checkBrightnessEnable(), ext.brightness);
                if (isSwitchMicPickUpType) {
                    AbsMicFragmentV4.saveMicModeByPhone(info.sku, info.device);
                }
                checkMusicMode();
                checkDiyStudio();
                checkDiyTemplate();
                checkDiyMode(info.mode);
                checkDiyGuide();
                changeColorMode(info.mode);
                checkColorMode(info.mode);
                checkMicMode();
                checkScenesMode();
                analytic4ModeUse(info.mode);
                modeUI.show();
                modeUI.setMode(info.mode);
            } else {
                hideDiyGuide();
                brightnessUI.hide();
                snapshotUI.hide();
                modeUI.hide();
            }

            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_suc);
        } else if (ui_type_fail == uiTypeBle && ui_type_fail == uiTypeIot) {
            /*重置连接次数*/
            retryConnectDeviceTimes = 0;
            /*通信失败*/
            hideLoading();
            timerUI.hide();
            if (aiLightUI != null) {
                aiLightUI.hide();
            }
            playUi.hide();
            snapshotUI.hide();
            effectUI.hide();
            brightnessUI.hide();
            modeUI.hide();
            hideLoading();
            /*断开连接-重置通信Op*/
            iotOp.destroy();
            bleOp.destroy();
            hideDiyGuide();
            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_fail);
        } else {
            hideLoading();
            /*通信中*/
            effectUI.hide();
            timerUI.hide();
            if (aiLightUI != null) {
                aiLightUI.hide();
            }
            playUi.hide();
            snapshotUI.hide();
            brightnessUI.hide();
            modeUI.hide();
            hideDiyGuide();
            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_ing);
        }
        isSwitchMicPickUpType = false;
    }

    /*标志位-是否统计了mode的使用;进去一次仅统计一次*/
    private boolean hadAnalyticModeUse = false;

    private void analytic4ModeUse(AbsMode mode) {
        if (hadAnalyticModeUse) return;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode == null) return;
        hadAnalyticModeUse = true;
        subMode.setAnalyticType(ISubMode.ANALYTIC_TYPE_MODE_USE_DETAIL);
    }

    private void checkMusicMode() {
        int musicModeVersion = Support.getMultiNewMusicVersion4Telink(bleOp.isOpCommEnable(), info.sku, info.versionSoft, ext.wifiSoftVersion);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkMusicMode() musicModeVersion = " + musicModeVersion);
        }
        /*更换对应的uiMode*/
        if (modeUI instanceof ModeUiV2) {
            ((ModeUiV2) modeUI).changeMusicMode(musicModeVersion);
        }
        /*设置默认音乐模式*/
        AbsMode mode = info.mode;
        if (mode instanceof Mode) {
            ((Mode) mode).changeMusicMode4MultiNewMusic4Telink(musicModeVersion);
        }
    }

    private void checkDiyTemplate() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenes) {
            int scenesCode = ((SubModeScenes) subMode).getEffect();
            boolean scenes4DiyTemplate = ScenesOp.isScenes4DiyTemplate(scenesCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkDiyTemplate() scenes4DiyTemplate " + scenes4DiyTemplate + " ; scenesCode = " + scenesCode);
            }
            if (scenes4DiyTemplate) {
                /*当前是diy模版效果；将模式切换长diy模式*/
                int diyCode = DiyOpM.getInstance.getDiyCode4DiyTemplate(info.sku, scenesCode);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "checkDiyTemplate() diyCode = " + diyCode);
                }
                mode.subMode = new SubModeNewDiy(diyCode);
            }
        }
    }

    private void checkDiyStudio() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenes) {
            int scenesCode = ((SubModeScenes) subMode).getEffect();
            boolean scenes4DiyStudio = ScenesOp.isScenes4DiyStudio(scenesCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkDiyStudio() scenesCode = " + scenesCode + " ; scenes4DiyStudio = " + scenes4DiyStudio);
            }
            if (scenes4DiyStudio) {
                /*来自于Studio的DIY-切换成DIY模式*/
                int diyCode = DiyOpM.getInstance.getDiyCode4DiyTemplate(info.sku, scenesCode);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "checkDiyStudio() diyCode = " + diyCode);
                }
                mode.subMode = new SubModeNewDiy(diyCode);
            }
        }
    }

    private void changeColorMode(AbsMode mode) {
        int subModeColorVersion = -1;
        if (ui_type_normal == uiTypeBle) {
            subModeColorVersion = Support.supportPartBrightness(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, "");
        } else if (ui_type_normal == uiTypeIot) {
            subModeColorVersion = Support.supportPartBrightness(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, ext.wifiSoftVersion);
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "changeColorMode() subModeColorVersion = " + subModeColorVersion);
        }
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subModeColorVersion > 0 && subMode instanceof SubModeColor) {
                /*转化成新的颜色模式*/
                mode.subMode = SubModeColorV2.parseSubModeColor2New((SubModeColor) subMode);
                if (bleOp.isOpCommEnable()) {
                    bleOp.readPartColor();
                }
            } else if (subModeColorVersion == 0 && subMode instanceof SubModeColorV2) {
                /*蓝牙支持,wifi不支持,才可能从新的转旧的*/
                /*转化成旧的颜色模式*/
                mode.subMode = SubModeColor.parseSubModeColor2Old((SubModeColorV2) subMode);
                ((SubModeColor) mode.subMode).rgbSet = this.rgbSet;
                bleUnable2CheckIot4OldColorMode();
            }
        }
        if (modeUI instanceof ModeUiV2) {
            ((ModeUiV2) modeUI).changeColorMode(subModeColorVersion, info.ic, getDiySupport());
        }
    }

    private int getOpType() {
        if (bleOp.isOpCommEnable()) return 0;
        if (iotOp.isOpCommEnable()) return 1;
        return -1;
    }

    private void checkScenesMode() {
        if (modeUI instanceof ModeUiV2) {
            int newVersion = getNewVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkScenesMode() newVersion = " + newVersion);
            }
            ((ModeUiV2) modeUI).setScenesModeVersion(newVersion);
        }
    }

    private int getNewVersion() {
        return Support.newVersion(getOpType(), info.sku, info.goodsType, info.pactCode, info.versionSoft, info.versionHard, ext.wifiSoftVersion, ext.wifiHardVersion);
    }

    private boolean checkBrightnessEnable() {
        AbsMode mode = info.mode;
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeScenes) {
                int effect = ((SubModeScenes) subMode).getEffect();
                int newVersion = getNewVersion();
                if (newVersion == 1) {
                    boolean[] brightnessChangeEnable = ScenesRgb.brightnessChangeEnable(effect);
                    if (brightnessChangeEnable[0]) {
                        return brightnessChangeEnable[1];
                    }
                }
                return Support.supportScenesBrightnessOp(info.pactType, info.pactCode, info.goodsType, effect, info.sku);
            }
        }
        return true;
    }

    private void checkColorMode(AbsMode mode) {
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeColor) {
                ((SubModeColor) subMode).gradual = ext.gradual;
            } else if (subMode instanceof SubModeColorV2) {
                ((SubModeColorV2) subMode).gradual = ext.gradual;
            }
        }
    }

    private int getIotOpType() {
        return getOpType(uiTypeIot);
    }

    private int getBleOpType() {
        return getOpType(uiTypeBle);
    }

    private int getOpType(int step) {
        if (step == ui_type_fail) return IUiResult4BleIot.op_type_fail;
        if (step == ui_type_normal) return IUiResult4BleIot.op_type_suc;
        return IUiResult4BleIot.op_type_ing;
    }

    private void showLoading() {
        LoadingDialog.createDialog(ac, com.ihoment.base2app.R.style.DialogDim, 60 * 1000).setEventKey(TAG).show();
    }

    private void hideLoading() {
        LoadingDialog.hideDialog(TAG);
    }

    private void toast(@StringRes int strRes) {
        ToastUtil.getInstance().toast(strRes);
    }

    private void addViewMargin(PercentRelativeLayout contentParent, View subView, int belowId, int width, int height, int marginTop) {
        PercentRelativeLayout.LayoutParams lp = getLP(width, height);
        lp.addRule(RelativeLayout.BELOW, belowId);
        lp.topMargin = marginTop;
        lp.bottomMargin = 0;
        contentParent.addView(subView, lp);
    }

    private PercentRelativeLayout.LayoutParams getLP(int width, int height) {
        return new PercentRelativeLayout.LayoutParams(width, height);
    }

    /**
     * diy效果发生改变操作
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyEffectOp(EventDiyEffectOp event) {
        int opType = event.opType;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyEffectOp() opType = " + opType);
        }
        checkDiyMode(info.mode);
    }

    /**
     * 注册diy的输出
     *
     * @param event event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApply(EventDiyApply event) {
        boolean applyDiyV0 = applyDiyV0(event.getDiyProtocol());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApply() applyDiyV0 = " + applyDiyV0);
        }
        if (!applyDiyV0) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    /**
     * diy模版
     *
     * @param diyTemplate
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyTemplate(DiyTemplate diyTemplate) {
        boolean applyDiyV2 = applyDiyV2(diyTemplate);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyTemplate() applyDiyV2 = " + applyDiyV2);
        }
        if (!applyDiyV2) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    /**
     * 来自于Studio的 DIY模版
     *
     * @param diyStudio
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyStudio(DiyStudio diyStudio) {
        boolean applyDiyV4 = applyDiyV4(diyStudio);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyStudio() applyDiyV4 = " + applyDiyV4);
        }
        if (!applyDiyV4) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyAi(DiyAi diyAi) {
        boolean applyDiyV5 = applyDiyV5(diyAi);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyAi() applyDiyV5 = " + applyDiyV5);
        }
        if (!applyDiyV5) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }


    /**
     * 进入定时界面
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewTimerClickEvent(NewTimerUI.NewTimerClickEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewTimerClickEvent()");
        }
        NewShowTimerAcV2.jump2NewShowTimerAc(ac, info.sku, info.device, ext.timerInfo1, ext.timerInfo2, ext.wakeUpInfo, ext.sleepInfo, info.isSupportIot());
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onBrightnessEndEvent(BrightnessUI.EventBrightnessClickEvent event) {
        int brightness = event.brightness;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onBrightnessEndEvent() brightness = " + brightness);
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            showLoading();
            BrightnessController controller = new BrightnessController(brightness);
            bleOp.executeOp(controller);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            CmdBrightness cmdBrightness = new CmdBrightness(brightness);
            iotOp.writeCmd(cmdBrightness);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewTimerSetEvent(NewTimerSetEvent event) {
        TimerInfo info = event.getInfo();
        int group = event.getGroup();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "group = " + group + " ; info.str = " + info.getDisplayTimeStr() + " ; open = " + info.isOpen());
        }
        if (bleOp.isOpCommEnable()) {
            info.check();
            AutoTimeController autoTimeController = new AutoTimeController(group, info.isOpen(), info.openHour, info.openMin, info.closeHour, info.closeMin, info.repeat);
            bleOp.executeOp(autoTimeController);
        } else if (iotOp.isOpCommEnable()) {
            info.check();
            AutoTimeController autoTimeController = new AutoTimeController(group, info.isOpen(), info.openHour, info.openMin, info.closeHour, info.closeMin, info.repeat);
            CmdPt cmdPt = new CmdPt(CmdPt.pt_op_timer, autoTimeController.getValue());
            iotOp.writeCmd(cmdPt);
        } else {
            TimerFailEvent.sendTimerFailEvent(false);
            toast(R.string.b2light_aal_light_connect_label_error);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewWakeupSetEvent(NewWakeupSetEvent event) {
        WakeUpInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewWakeupSetEvent() info = " + info.toString());
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            info.check();
            WakeUpController wakeupModeController = new WakeUpController(info.enable, info.endBri, info.wakeHour, info.wakeMin, info.repeat, info.wakeTime);
            bleOp.executeOp(wakeupModeController);
        } else if (iotOp.isOpCommEnable()) {
            info.check();
            WakeUpController wakeupModeController = new WakeUpController(info.enable, info.endBri, info.wakeHour, info.wakeMin, info.repeat, info.wakeTime);
            CmdPt cmdPt = new CmdPt(CmdPt.pt_op_wakeup, wakeupModeController.getValue());
            iotOp.writeCmd(cmdPt);
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            WakeupFailEvent.sendWakeupFailEvent(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNewSleepSetEvent(NewSleepSetEvent event) {
        SleepInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewSleepSetEvent() info = " + info.toString());
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            /*若当前是开启睡眠模式，但设备未开启，则提示用户打开灯*/
            if (event.isOpen() && !this.info.open) {
                toast(R.string.b2light_please_open_the_light);
                SleepFailEvent.sendSleepFailEvent(false);
            } else {
                info.check();
                SleepController sleepModeController = new SleepController(info.enable, info.startBri, info.closeTime, info.closeTime);
                bleOp.executeOp(sleepModeController);
            }
        } else if (iotOp.isOpCommEnable()) {
            /*若当前是开启睡眠模式，但设备未开启，则提示用户打开灯*/
            if (event.isOpen() && !this.info.open) {
                toast(R.string.b2light_please_open_the_light);
                SleepFailEvent.sendSleepFailEvent(false);
            } else {
                info.check();
                SleepController sleepModeController = new SleepController(info.enable, info.startBri, info.closeTime, info.closeTime);
                CmdPt cmdPt = new CmdPt(CmdPt.pt_op_sleep, sleepModeController.getValue());
                iotOp.writeCmd(cmdPt);
            }
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            SleepFailEvent.sendSleepFailEvent(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSleepTimeSub(EventSleepUpdate event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onSleepTimeSub()");
        }
        if (ext.sleepInfo != null && ext.sleepInfo.isOn()) {
            SleepInfo info = event.getInfo();
            info.check();
            ext.sleepInfo = info;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventChangeGradual(EventChangeGradual event) {
        boolean open = event.open;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventChangeGradual() open = " + open);
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            Gradual4BleWifiController gradualController = new Gradual4BleWifiController(open);
            bleOp.executeOp(gradualController);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            Gradual4BleWifiController gradualController = new Gradual4BleWifiController(open);
            CmdPt cmdPt = new CmdPt(CmdPt.pt_op_gradual, gradualController.getValue());
            iotOp.writeCmd(cmdPt);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onModeV1Event(Mode mode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onModeV1Event()");
        }
        changeMode(mode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeModeEvent(ChangeModeEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent()");
        }
        /*若是采用颜色指令进行mic逻辑发送的，需要在切换模式时将模式设置成颜色模式的纯色*/
        if (!Support.supportPartBrightness4Ble(info.versionSoft, info.versionHard)) {
            checkMicByColorMode(event);
        }
        ISubMode subMode = event.getSubMode();
        boolean change2NewMultiMusicCode = change2NewMultiMusicMode(subMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent() change2NewMultiMusicCode = " + change2NewMultiMusicCode);
        }
        if (change2NewMultiMusicCode) return;
        boolean changeDiyMode = changeDiyMode(subMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent() changeDiyMode = " + changeDiyMode);
        }
        if (changeDiyMode) return;
        /*检测场景模式下的效果是否支持*/
        ISubMode subModeNew = Support.checkScenesModeEffect(subMode, info.sku, info.goodsType, info.pactType, info.pactCode, getNewVersion(),
                bleOp.isOpCommEnable(), info.versionSoft, info.versionHard,
                iotOp.isOpCommEnable(), ext.wifiSoftVersion, ext.wifiHardVersion);
        if (subModeNew != null) {
            subMode = subModeNew;
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        changeMode(mode);
        /*统计场景使用次数*/
        if (subMode instanceof SubModeScenes) {
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.scene_mode);
        } else if (subMode instanceof SubModeColor || subMode instanceof SubModeColorV2) {
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.color_mode);
        } else if (subMode instanceof SubModeMusic || subMode instanceof SubModeMusicV1) {
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.music_mode);
        }
    }

    private boolean change2NewMultiMusicMode(ISubMode subMode) {
        if (subMode instanceof SubModeMusicV2) {
            int musicCode = ((SubModeMusicV2) subMode).getMusicCode();
            int sensitivity = ((SubModeMusicV2) subMode).getSensitivity();
            boolean newMusicCode = Support.isNewMusicCode(musicCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "change2NewMultiMusicMode() newMusicCode = " + newMusicCode);
            }
            if (newMusicCode) {
                /*若当前是新的音乐模式-则需要先发送新的多包参数*/
                boolean setLocalNewMusicMode = AbsNewMusicEffect.setLocalNewMusicMode(info.sku, info.device, musicCode, sensitivity);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "change2NewMultiMusicMode() setLocalNewMusicMode = " + setLocalNewMusicMode);
                }
                AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.music_mode);
                return true;
            }
        }
        return false;
    }

    /**
     * 离开 通过颜色模式实现的手机拾音模式之前，从新设置灯带的颜色为纯色
     */
    private void checkMicByColorMode(ChangeModeEvent event) {
        if (bleOp.isOpCommEnable()) {
            int color = event.getColor();
            if (color != 0) {
                Mode mode = new Mode();
                mode.subMode = SubModeColor.makeSubModeColor(color);
                mode.subMode.saveLocal();
                if (event.getSubMode() instanceof SubModeColor) {
                    event.setSubMode(mode.subMode);
                }
                ModeController modeController = new ModeController(mode);
                bleOp.executeExtOp(modeController);
            }
        }
    }

    private boolean changeDiyMode(ISubMode subMode) {
        if (subMode instanceof SubModeNewDiy) {
            boolean hadToken = AccountConfig.read().isHadToken();
            String diyValueKey = ((SubModeNewDiy) subMode).getDiyValueKey();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "changeDiyMode() diyValueKey = " + diyValueKey + " ; hadToken = " + hadToken);
            }
            DiyValue diyValue = DiyShortcutManger.queryLastApplyDiy(info.sku, info.goodsType, getDiySupport(), info.ic, diyValueKey, true);
            Util4Diy.toApplyDiy(diyValue);
            return true;
        }
        return false;
    }

    private void changeMode(Mode mode) {
        modeUI.setModeChanged(true);
        if (bleOp.isOpCommEnable()) {
            showLoading();
            AbsMultipleControllerV14Scenes newScenesMode = Support.is2NewScenesMode(info.sku, info.device, mode, getNewVersion());
            if (newScenesMode != null) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "changeMode() AbsMultipleControllerV1");
                }
                bleOp.executeMultiOpV1(newScenesMode);
            } else {
                ModeController controller = new ModeController(mode);
                bleOp.executeOp(controller);
            }
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            if (isSubColorMode(mode)) {
                /*颜色模式；走cmd=bulb指令*/
                iotOpSubColorMode(mode);
            } else {
                AbsMultipleControllerV14Scenes newScenesMode = Support.is2NewScenesMode(info.sku, info.device, mode, getNewVersion());
                if (newScenesMode != null) {
                    /*多包场景效果-采用效果包+模式包组合方式*/
                    CmdPt cmdPt = CmdPt.getNewScenesCmdPt(newScenesMode);
                    iotOp.writeCmd(cmdPt);
                } else {
                    /*场景模式-音乐模式；透传模式信息即可*/
                    ModeController modeController = new ModeController(mode);
                    CmdPt cmdPt = new CmdPt(CmdPt.pt_op_mode, modeController.getValue());
                    iotOp.writeCmd(cmdPt);
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventScenesEffect(EventScenesEffect effect) {
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "onEventScenesEffect");
        }
        CategoryV1.SceneV1 sceneV1 = effect.sceneV1;
        int pos = effect.pos;
        if (bleOp.isOpCommEnable() || iotOp.isOpCommEnable()) {
            boolean result = false;
            int status = EventEffectSquareOpResult.status_iot_no_support;
            if (bleOp.isOpCommEnable()) {
                result = scenesEffect4Ble(sceneV1, pos);
                status = EventEffectSquareOpResult.status_ble_no_support;
            } else if (iotOp.isOpCommEnable()) {
                result = scenesEffect4Iot(sceneV1, pos);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(result ? EventEffectSquareOpResult.result_op_support : EventEffectSquareOpResult.result_op_no_support, status);
        } else {
            /*当前设备未连接*/
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_unable);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventEditScenes(EffectEvent.EventEditScenes event) {
        String sku = event.sku;
        int categoryId = event.categoryId;
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "onEventEditScenes:" + sku + "---categoryId:" + categoryId);
        }
        if (!info.sku.equals(sku)) return;
        EditScenesAc.jump2EditScenesAc(ac, info.sku, info.device, info.goodsType, categoryId, true, true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeScenes(CategoryV1.SceneV1 scene) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeScenes()");
        }
        int sceneType = scene.getSceneType(0, info.sku);
        int sceneCode = scene.getSceneCode(0, info.sku);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeScenes() sceneType = " + sceneType + " ; scene.sceneCode = " + sceneCode);
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            if (!scenesEffect4Ble(scene, 0)) {
                HintDialog1.createHintDialog1(ac, ResUtil.getString(R.string.b2light_scenes_ble_not_support), ResUtil.getString(R.string.hint_done_got_it)).show();
                hideLoading();
            }
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            if (!scenesEffect4Iot(scene, 0)) {
                HintDialog1.createHintDialog1(ac, ResUtil.getString(R.string.b2light_scenes_no_support_hint), ResUtil.getString(R.string.hint_done_got_it)).show();
                hideLoading();
            }
        }
    }


    /**
     * ble 发送配置场景
     *
     * @param sceneV1 sceneV1
     * @param pos     pos
     * @return boolean
     */
    private boolean scenesEffect4Ble(CategoryV1.SceneV1 sceneV1, int pos) {
        int cmdVersion = sceneV1.getCmdVersion(info.sku, pos);
        int sceneType = sceneV1.getSceneType(pos, info.sku);
        int sceneCode = sceneV1.getSceneCode(pos, info.sku);
        if (!Support.checkShareEffectVersion4BleWifi(0, sceneType, cmdVersion, info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard, ext.wifiSoftVersion)) {
            return false;
        }
        String sceneEffectStr = sceneV1.getScenesParam(info.sku, pos);
        if (sceneType == ScenesOp.scene_type_static) {
            /*静态场景效果*/
            SubModeScenes subModeScenes = new SubModeScenes();
            subModeScenes.setEffect(sceneCode);
            Mode mode = new Mode();
            mode.subMode = subModeScenes;
            ModeController controller = new ModeController(mode);
            ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeOp(controller));
            return true;
        }
        if (sceneType == ScenesOp.scene_type_rgb) {
            /*rgb效果*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgb(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeMultiOpV1(controllerV14Scenes));
            } else {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "onChangeScenes() rgb指令解析出错!");
                }
                return false;
            }
            return true;
        }
        if (sceneType == ScenesOp.scene_type_rgbic) {
            /*rgbic效果*/
            int[] rgbicProtocolVersionBytes = ScenesRgbIC.isRGBICProtocolVersionBytes(Encode.decryByBase64(sceneEffectStr));
            if (rgbicProtocolVersionBytes[0] == 0) return false;
            int parseVersion = rgbicProtocolVersionBytes[1];
            if (parseVersion != cmdVersion) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "版本不一致  parseVersion:" + parseVersion + "----cmdVersion:" + cmdVersion);
                }
                return false;
            }
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgbic(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeMultiOpV1(controllerV14Scenes));
            } else {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "onChangeScenes() rgbic指令解析出错!");
                }
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * iot 发送配置场景
     *
     * @param sceneV1 sceneV1
     * @param pos     pos
     * @return boolean
     */
    private boolean scenesEffect4Iot(CategoryV1.SceneV1 sceneV1, int pos) {
        int cmdVersion = sceneV1.getCmdVersion(info.sku, pos);
        int sceneType = sceneV1.getSceneType(pos, info.sku);
        int sceneCode = sceneV1.getSceneCode(pos, info.sku);
        if (!Support.checkShareEffectVersion4BleWifi(1, sceneType, cmdVersion, info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard, ext.wifiSoftVersion)) {
            return false;
        }
        String sceneEffectStr = sceneV1.getScenesParam(info.sku, pos);
        if (sceneType == ScenesOp.scene_type_static) {
            SubModeScenes subModeScenes = new SubModeScenes();
            subModeScenes.setEffect(sceneCode);
            Mode mode = new Mode();
            mode.subMode = subModeScenes;
            ModeController controller = new ModeController(mode);
            CmdPt cmdPt = new CmdPt(CmdPt.pt_op_mode, controller.getValue());
            ScenesOp.toSetScenes(ac, sceneV1, pos, () -> iotOp.writeCmd(cmdPt));
            return true;
        }
        if (sceneType == ScenesOp.scene_type_rgb) {
            /*rgb效果*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgb(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                /*多包场景效果-采用效果包+模式包组合方式*/
                CmdPt cmdPtReal = CmdPt.getNewScenesCmdPt(controllerV14Scenes);
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> iotOp.writeCmd(cmdPtReal));
                return true;
            }
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "scenesEffect4Iot() rgb指令解析出错!");
            }
            return false;
        }
        if (sceneType == ScenesOp.scene_type_rgbic) {
            /*rgbic效果*/
            int[] rgbicProtocolVersionBytes = ScenesRgbIC.isRGBICProtocolVersionBytes(Encode.decryByBase64(sceneEffectStr));
            if (rgbicProtocolVersionBytes[0] == 0) return false;
            int parseVersion = rgbicProtocolVersionBytes[1];
            if (parseVersion != cmdVersion) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "版本不一致  parseVersion:" + parseVersion + "----cmdVersion:" + cmdVersion);
                }
                return false;
            }
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgbic(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                /*多包场景效果-采用效果包+模式包组合方式*/
                CmdPt cmdPtReal = CmdPt.getNewScenesCmdPt(controllerV14Scenes);
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> iotOp.writeCmd(cmdPtReal));
                return true;
            }
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "scenesEffect4Iot() rgbic指令解析出错!");
            }
            return false;
        }
        return false;
    }

    private void iotOpSubColorMode(Mode mode) {
        /*若当前是全选*/
        ModeController modeController = new ModeController(mode);
        byte[] value = modeController.getValue();
        if (LogInfra.openLog()) {
            LogInfra.Log.d(TAG, "iotOpSubColorMode bytes = " + BleUtil.bytesToHexString(value));
        }
        CmdBulb cmdBulb = new CmdBulb(value);
        iotOp.writeCmd(cmdBulb);
    }

    private boolean isSubColorMode(AbsMode mode) {
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode != null) {
                return subMode.subModeCommandType() == BleProtocol.sub_mode_color || subMode.subModeCommandType() == BleProtocol.sub_mode_color_v2;
            }
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onUpdateTimeEvent(UpdateTimeEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onUpdateTimeEvent()");
        }
        readTimer();
    }

    private void readTimer() {
        /*重新读取定时信息*/
        if (bleOp.isOpCommEnable()) {
            SyncTimeInfo info = SyncTimeInfo.getCurrentTime();
            AbsSingleController[] controllers = new AbsSingleController[]{
                    new SyncTimeController(info.hour, info.minute, info.second, info.week),
                    new AutoTimeController(0),
                    new AutoTimeController(1),
                    new WakeUpController(),
                    new SleepController(),
            };
            bleOp.executeOp(controllers);
        } else if (iotOp.isOpCommEnable()) {
            iotOp.readCmd(new CmdStatus());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEffectClickEvent(EffectUI.EffectClickEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEffectClickEvent()");
        }
        boolean isSupportScenesLib = getNewVersion() == 1;
        int[] protocolSet = null;
        if (isSupportScenesLib) {
            protocolSet = Support.getSupportScenesOpSet(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard);
        }
        EffectAc.jump2EffectAc(ac, info.goodsType, info.sku, info.device, protocolSet, true, true, isSupportScenesLib, info.versionSoft, info.versionHard);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventColorEffect(EffectData.ColorEffect colorEffect) {
        /*设置颜色模式*/
        if (bleOp.isOpCommEnable() || iotOp.isOpCommEnable()) {
            boolean singleColor = colorEffect.isSingleColor();
            int size = colorEffect.colorSet.length;
            if (size != 1 && size != 15) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "颜色数量不支持：" + size);
                }
                EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_no_support);
                return;
            }
            Mode mode = new Mode();
            mode.subMode = SubModeColor.makeSubModeColor(colorEffect.colorSet[0]);
            if (singleColor) {
                if (bleOp.isOpCommEnable()) {
                    changeMode(mode);
                } else {
                    /*颜色模式；走cmd=bulb指令*/
                    iotOpSubColorMode(mode);
                }
            } else {
                if (bleOp.isOpCommEnable()) {
                    AbsSingleController[] controllers = SubModeColor.makeSubModeColor(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
                    if (controllers == null) {
                        //不支持
                        EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_no_support);
                        return;
                    }
                    bleOp.executeOp(controllers);
                } else {
                    EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_only_ble);
                    return;
                }
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_support);
        } else {
            /*当前设备未连接*/
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_unable);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventServiceSceneFresh(EventServiceScenesFresh event) {
        String sku = event.sku;
        String skuCur = info.sku;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventServiceSceneFresh() sku = " + sku + " ; skuCur = " + skuCur + " ; uiTypeBle = " + uiTypeBle + " ; uiTypeIot = " + uiTypeIot);
        }
        if (!TextUtils.isEmpty(sku) && sku.equals(skuCur) && (uiTypeBle == ui_type_normal || uiTypeIot == ui_type_normal)) {
            /*检查是否有服务器配置场景*/
            EventSceneCheck4BleIotV1.sendEventSceneCheck4BleIotV1(info);
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventChangeScenes(EventChangeScenes event) {
        String sku = event.sku;
        String skuCur = info.sku;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventChangeScenes() sku = " + sku + " ; skuCur = " + skuCur);
        }
        if (!TextUtils.isEmpty(sku) && sku.equals(skuCur) && (uiTypeBle == ui_type_normal || uiTypeIot == ui_type_normal)) {
            /*检查是否有服务器配置场景*/
            EventSceneCheck4BleIotV1.sendEventSceneCheck4BleIotV1(info);
        }
    }

    /**
     * 注册diy的输出-v1版本
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApplyGraffiti(EventDiyApplyV2 event) {
        boolean applyDiyV3 = applyDiyV3(event.getDiyGraffiti());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyGraffiti() applyDiyV3 = " + applyDiyV3);
        }
        if (!applyDiyV3) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventColorStrip(EventColorStrip event) {
        /*色条设置颜色模式*/
        if (bleOp.isOpCommEnable() || iotOp.isOpCommEnable()) {
            int size = event.colors.colorSet.length;
            if (size != 15) {
                //不支持
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "颜色数和灯段数不等：" + size);
                }
                return;
            }
            showLoading();
            AbsSingleController[] modeControllers;
            int subModeColorVersion = -1;
            if (ui_type_normal == uiTypeBle) {
                subModeColorVersion = Support.supportPartBrightness(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, "");
            } else if (ui_type_normal == uiTypeIot) {
                subModeColorVersion = Support.supportPartBrightness(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, ext.wifiSoftVersion);
                if (subModeColorVersion == 0) {
                    ConfirmDialog.showConfirmDialog(ac, ResUtil.getString(R.string.ble_control_hint_des),
                            ResUtil.getString(R.string.no), ResUtil.getString(R.string.yes), () -> {
                                if (!BleController.getInstance().isBlueToothOpen()) {
                                    toast(R.string.main_operation_fail_ble_not_open);
                                } else {
                                    ConnectDialog.showConnectDialog(ac, new ConnectDialog.OnCliCkListener() {
                                        @Override
                                        public void onClickConnect() {
                                            EventTryConnectDevice.sendEventTryConnectDevice(true);
                                        }

                                        @Override
                                        public void onClickClose() {

                                        }
                                    }, ConnectDialog.connect_status_ing);
                                }
                            });
                    return;
                }
            }
            if (subModeColorVersion == 0) {
                modeControllers = SubModeColor.makeSubModeColor(event.colors);
            } else {
                modeControllers = SubModeColorV2.makeSubModeColor(event.colors);
            }
            if (bleOp.isOpCommEnable()) {
                bleOp.executeOp(modeControllers);
            } else {
                if (modeControllers != null) {
                    List<byte[]> bytes = new ArrayList<>();
                    for (AbsSingleController absSingleController : modeControllers) {
                        bytes.add(absSingleController.getValue());
                    }
                    CmdBulb cmdBulb = new CmdBulb(bytes);
                    iotOp.writeCmd(cmdBulb);
                }
            }
        }
    }

    /*新DIY交互补充代码逻辑*/

    private void checkDiyModeInfo(SubModeNewDiy diy) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyModeInfo()");
        }
        if (diy == null) return;
        /*刷新在模式中展示的DIY列表*/
        diy.setDiyGroups(curDiyGroups);
        /*获取当前选中的diyValueKey*/
        int diyCode = diy.getDiyCode();
        int newVersion = Support.getDiyVersion(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard);
        String lastDiyApplyKey = Diy.getLastDiyApplyKey(newVersion, info.goodsType, info.ic, info.sku, diyCode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyMode() lastDiyApplyKey = " + lastDiyApplyKey);
        }
        diy.setDiyValueKey(lastDiyApplyKey);
    }

    private void checkDiyMode(AbsMode mode) {
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeNewDiy) {
            checkDiyModeInfo((SubModeNewDiy) subMode);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEvent2AcDiyGroup(Event2AcDiyGroup event) {
        int icNum = info.ic;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEvent2AcDiyGroup icNum = " + icNum);
        }
        AcDiyGroup.jump2DiyGroupAc(ac, info.sku, info.goodsType, getDiySupport(), icNum, true);
    }

    private DiySupportV1 getDiySupport() {
        int version = Support.getDiyVersion(getOpType(), info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard, ext.wifiSoftVersion, ext.wifiHardVersion);
        return Diy.getDiySupport(version);
    }

    private DiyValue applyingDiyValue;

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApplyInModeShowing(EventDiyApply4InModeShowing event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing()");
        }
        modeUI.setModeChanged(true);
        DiyProtocol diyProtocol = event.getDiyProtocol();
        if (diyProtocol != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV0 = applyDiyV0(diyProtocol);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV0 = " + applyDiyV0);
            }
            if (applyDiyV0) {
                showLoading();
            }
            return;
        }
        DiyTemplate diyTemplate = event.getDiyTemplate();
        if (diyTemplate != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV2 = applyDiyV2(diyTemplate);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV2 = " + applyDiyV2);
            }
            if (applyDiyV2) {
                showLoading();
            }
            return;
        }
        DiyGraffitiV2 diyGraffitiV2 = event.getDiyGraffitiV2();
        if (diyGraffitiV2 != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV3 = applyDiyV3(diyGraffitiV2);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV3 = " + applyDiyV3);
            }
            if (applyDiyV3) {
                showLoading();
            }
            return;
        }

        DiyStudio diyStudio = event.getDiyStudio();
        if (diyStudio != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV4 = applyDiyV4(diyStudio);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV4 = " + applyDiyV4);
            }
            if (applyDiyV4) {
                showLoading();
            }
            return;
        }

        DiyAi diyAi = event.getDiyAi();
        if (diyAi != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV5 = applyDiyV5(diyAi);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV5 = " + applyDiyV5);
            }
            if (applyDiyV5) {
                showLoading();
            }
        }
    }

    private boolean applyDiyV0(@NonNull DiyProtocol diyProtocol) {
        if (bleOp.isOpCommEnable()) {
            MultipleDiyController multipleDiyController = new MultipleDiyController(diyProtocol);
            bleOp.executeMultiOp(multipleDiyController);
            return true;
        } else if (iotOp.isOpCommEnable()) {
            CmdPt cmdPt = CmdPt.getDiyCmdPt(diyProtocol);
            iotOp.writeCmd(cmdPt);
            return true;
        }
        return false;
    }

    private boolean applyDiyV2(@NonNull DiyTemplate diyTemplate) {
        if (bleOp.isOpCommEnable()) {
            int diyCode = diyTemplate.diyCode;
            int templateCode = diyTemplate.scenesCode;
            String effectStr = diyTemplate.effectStr;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV2() diyCode = " + diyCode + " ; templateCode = " + templateCode + " ; effectStr = " + effectStr);
            }
            AbsMultipleControllerV1 diyTemplateController = ScenesOp.parseDiyTemplate(templateCode, diyCode, effectStr);
            bleOp.executeMultiOpV1(diyTemplateController);
            return true;
        } else if (iotOp.isOpCommEnable()) {
            int newVersion = getNewVersion();
            if (newVersion != 1) {
                toast(R.string.b2light_aal_light_connect_label_error);
                EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
                return false;
            }
            int diyCode = diyTemplate.diyCode;
            int templateCode = diyTemplate.scenesCode;
            String effectStr = diyTemplate.effectStr;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV2() diyCode = " + diyCode + " ; templateCode = " + templateCode + " ; effectStr = " + effectStr);
            }
            AbsMultipleControllerV14DiyTemplate diyTemplateController = ScenesOp.parseDiyTemplate(templateCode, diyCode, effectStr);
            CmdPt cmdPt = CmdPt.getDiyTemplateCmdPt(diyTemplateController);
            this.diyCode = diyCode;
            this.diyTemplateCode = templateCode;
            iotOp.writeCmd(cmdPt);
            return true;
        }
        return false;
    }

    private boolean applyDiyV3(@NonNull DiyGraffitiV2 diyGraffitiV2) {
        if (bleOp.isOpCommEnable()) {
            MultiDiyGraffitiController controller = new MultiDiyGraffitiController(diyGraffitiV2.getDiyCode(), diyGraffitiV2.getEffectBytes());
            bleOp.executeMultiOpV1(controller);
            return true;
        } else if (iotOp.isOpCommEnable()) {
            CmdPt cmdPt = CmdPt.getDiyCmdPt4DiyGraffiti(diyGraffitiV2);
            int commandPacketNum = cmdPt.getValue().size();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyGraffiti() commandPacketNum = " + commandPacketNum);
            }
            if (commandPacketNum > 19) {
                toast(R.string.b2light_ble_disconnect_hint);
                EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
                return true;
            }
            iotOp.writeCmd(cmdPt);
            return true;
        }
        return false;
    }

    private boolean applyDiyV4(@NonNull DiyStudio diyStudio) {
        if (bleOp.isOpCommEnable()) {
            int scenesCode = diyStudio.scenesCode;
            String effectStr = diyStudio.effectStr;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV4() scenesCode = " + scenesCode + " ; effectStr = " + effectStr);
            }
            boolean supportRgbicV1 = Support.isSupportRgbicV1(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, ext.wifiSoftVersion);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV4() supportRgbicV1 = " + supportRgbicV1);
            }
            AbsMultipleControllerV14DiyTemplate multi4Scenes = diyStudio.toMulti4Scenes4RgbicV1(supportRgbicV1 ? 1 : 0);
            if (multi4Scenes != null) {
                bleOp.executeMultiOpV1(multi4Scenes);
                return true;
            }
        } else if (iotOp.isOpCommEnable()) {
            int newVersion = getNewVersion();
            if (newVersion != 1) {
                toast(R.string.b2light_aal_light_connect_label_error);
                EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
                return false;
            }
            int diyCode = diyStudio.diyCode;
            int scenesCode = diyStudio.scenesCode;
            String effectStr = diyStudio.effectStr;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV4() diyCode = " + diyCode + " ; scenesCode = " + scenesCode + " ; effectStr = " + effectStr);
            }
            boolean supportRgbicV1 = Support.isSupportRgbicV1(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, ext.wifiSoftVersion);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV4() supportRgbicV1 = " + supportRgbicV1);
            }
            AbsMultipleControllerV14DiyTemplate multi4Scenes = diyStudio.toMulti4Scenes4RgbicV1(supportRgbicV1 ? 1 : 0);
            if (multi4Scenes != null) {
                CmdPt cmdPt = CmdPt.getDiyTemplateCmdPt(multi4Scenes);
                this.diyCode = diyCode;
                this.diyTemplateCode = scenesCode;
                iotOp.writeCmd(cmdPt);
                return true;
            }
        }
        return false;
    }

    private boolean applyDiyV5(@NonNull DiyAi diyAi) {
        Command4PtReal ptReal = diyAi.command4PtReal;
        if (bleOp.isOpCommEnable()) {
            PtRealController controller = PtRealController.makePtRealController(ptReal.opCommands, diyAi.diyCode, diyAi.scenesCode);
            bleOp.executeMultiple4PtReal(controller);
            return true;
        } else if (iotOp.isOpCommEnable()) {
            diyCode = diyAi.diyCode;
            diyTemplateCode = diyAi.scenesCode;
            CmdPt ptRealCmdPt = CmdPt.getPtRealCmdPt(ptReal.getCommands4IotPtReal());
            iotOp.writeCmd(ptRealCmdPt);
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN, priority = 100)
    public void onEventDiyApplyResult(EventDiyApplyResult event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyResult() result = " + result + " ; diyCode = " + diyCode);
        }
        if (applyingDiyValue != null && applyingDiyValue.diyCode == diyCode) {
            if (result) {
                String diyValueKey = applyingDiyValue.getDiyValueKey();
                /*记录上次应用的DIY*/
                LastDiyConfig.read().saveLastDiyValueKey(info.sku, diyValueKey, diyCode, applyingDiyValue.effectCode);
            } else {
                toast(R.string.b2light_diy_apply_fail);
                hideLoading();
            }
        }
        applyingDiyValue = null;
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventDiyModeShowingChange(EventDiyModeShowingChange event) {
        List<DiyGroup> diyShortcuts = DiyShortcutManger.getDiyShortcuts(info.sku, info.goodsType, info.ic, getDiySupport());
        curDiyGroups.clear();
        if (diyShortcuts != null && !diyShortcuts.isEmpty()) {
            curDiyGroups.addAll(diyShortcuts);
        }
        handler.post(new CaughtRunnable() {
            @Override
            protected void runSafe() {
                checkUi();
            }
        });
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyModeShowingChange() curDiyGroups.size = " + curDiyGroups.size());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSwitchMicPickUpType(EventSwitchMicPickUpType event) {
        isSwitchMicPickUpType = true;
        modeUI.switchMicPickUpMode(event.isMicMode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onMicSetRgbController(MicSetRgbController controller) {
        if (bleOp.isOpCommEnable()) {
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_updating)) return;/*升级中，忽略mic发送逻辑*/
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_rebooting))
                return;/*重启过程中，忽略mic发送逻辑*/
            if (controller.isInitMode) {
                EventBleStatus.sendEvent(EventBleStatus.BleStatus.read_info_over);
            }
            if (controller.sendByColorMode) {
                Mode mode = new Mode();
                mode.subMode = SubModeColor.makeSubModeColor(ColorUtils.toColor(controller.data[0], controller.data[1], controller.data[2]));
                ModeController modeController = new ModeController(mode);
                bleOp.executeExtOp(modeController);
            } else {
                bleOp.executeExtOp(controller);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onRealtimeColorChangeEvent(RealtimeColorChangeEvent event) {
        if (bleOp.isOpCommEnable()) {
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_updating)) return;/*升级中，忽略mic发送逻辑*/
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_rebooting))
                return;/*重启过程中，忽略mic发送逻辑*/
            if (!Util4ColorRealtime.supportColorRealtime(info.sku, info.versionSoft, info.versionHard))
                return;
            AbsMicFragmentV4.SupportMicStatus micStatus = Support.getMicStatus(info.sku, info.versionSoft, info.goodsType);
            int controllerType = Util4ColorRealtime.getControllerType(micStatus, event.rgbicPartChoose);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onRealtimeColorChangeEvent() controllerType = " + controllerType);
            }
            if (controllerType == Util4ColorRealtime.CONTROLLER_TYPE_NO_SUPPORT) return;
            ISubMode subMode = event.subMode;
            int color = event.color;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onRealtimeColorChangeEvent() subMode = " + subMode + " ; color = " + color);
            }
            AbsSingleController controller;
            if (controllerType == Util4ColorRealtime.CONTROLLER_TYPE_COLOR) {
                Mode mode = new Mode();
                mode.subMode = subMode;
                controller = new ModeController(mode);
            } else {
                controller = new MicSetRgbController(ColorUtils.getRgbBytes(color));
            }
            bleOp.executeExtOp(controller);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventColorSet(AbsColorFragmentV8.EventColorSet eventColorSet) {
        if (eventColorSet.rgbSet != null) {
            System.arraycopy(eventColorSet.rgbSet, 0, this.rgbSet, 0, Math.min(eventColorSet.rgbSet.length, this.rgbSet.length));
        }
    }

    /*DIY引导逻辑*/

    private void hideDiyGuide() {
        if (hadShowGuide) {
            hadShowGuide = false;
            GuideDialog.hideDialog(TAG);
        }
    }

    private boolean hadDiyGuide = false;
    private boolean hadShowGuide;

    private void checkDiyGuide() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyGuide() hadDiyGuide = " + hadDiyGuide);
        }
        if (hadDiyGuide) return;
        if (modeUI != null) {
            hadDiyGuide = true;
            hadShowGuide = true;
            Util4Diy.checkShowDiyGuide4ModeNum(ac, TAG, modeUI.getModeNum());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSetMultiMusicEffect(EventSetMultiMusicEffect event) {
        modeUI.setModeChanged(true);
        int sensitivity = event.sensitivity;
        AbsNewMusicEffect newMusicEffect = event.newMusicEffect;
        int musicCode = newMusicEffect.getMusicCode();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNewMusicEffect() sensitivity = " + sensitivity + " ; newMusicEffect.musicCode = " + musicCode);
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            MultipleController4Music multipleController4Music = new MultipleController4Music(sensitivity, newMusicEffect);
            bleOp.executeMultiOpV2(multipleController4Music);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            MultipleController4Music multipleController4Music = new MultipleController4Music(sensitivity, newMusicEffect);
            CmdPt cmdPt = CmdPt.getMultiNewMusicMode(multipleController4Music);
            iotOp.writeCmd(cmdPt);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventCheckSnapshotEnable(EventCheckSnapshotEnable event) {
        LogInfra.Log.i(TAG, "onEventCheckSnapshotEnable()");
        SnapshotDataUtilV2.getInstance(ext).dealData(modeUI, info);
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventApplySnapshotCmd(EventApplySnapshotCmd event) {
        SnapshotCmdM.sendMsg(bleOp.isOpCommEnable(), iotOp.isOpCommEnable(),
                BleComm.serviceUuid, BleComm.characteristicUuid, info.topic, event.snapshotMode);
    }

    @NonNull
    @Override
    public FilterSceneInfo4BleIot getFilterSceneInfo() {
        return new FilterSceneInfo4BleIot(info.sku, info.versionSoft, info.versionHard, uiTypeBle == ui_type_normal, ext.wifiSoftVersion, ext.wifiHardVersion, null, null);
    }
}
