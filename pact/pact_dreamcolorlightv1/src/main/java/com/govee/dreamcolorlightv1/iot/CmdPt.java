package com.govee.dreamcolorlightv1.iot;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.util.Encode;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.comm.MultipleBleBytes;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.MultiDiyGraffitiController;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.controller.MultipleDiyController;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV2;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2019-07-22
 * cmd=pt
 */
@Keep
public class CmdPt extends AbsCmd {
    public static final String pt_op_timer = "timer";
    public static final String pt_op_sleep = "sleep";
    public static final String pt_op_wakeup = "wakeup";
    public static final String pt_op_mode = "mode";
    public static final String pt_op_gradual = "gradual";

    private static final String TAG = "CmdPt";
    private String opcode;
    private List<String> value = new ArrayList<>();

    public List<String> getValue() {
        return value;
    }

    @Override
    public int getCmdVersion() {
        return 1;
    }

    protected CmdPt() {
    }

    /**
     * 单包的透传
     *
     * @param op    op
     * @param bytes bytes
     */
    public CmdPt(String op, byte[] bytes) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "op = " + op + " ; bytesHexString = " + BleUtil.bytesToHexString(bytes));
        }
        this.opcode = op;
        String valueStr = Encode.encryptByBase64(bytes);
        value.add(valueStr);
    }

    public CmdPt(@NonNull List<byte[]> multipleBytes) {
        for (byte[] multipleByte : multipleBytes) {
            String valueStr = Encode.encryptByBase64(multipleByte);
            value.add(valueStr);
        }
    }

    /**
     * 多包的透传
     *
     * @param op            op
     * @param multipleBytes multipleBytes
     */
    public CmdPt(String op, byte[]... multipleBytes) {
        this.opcode = op;
        for (byte[] bytes : multipleBytes) {
            String valueStr = Encode.encryptByBase64(bytes);
            value.add(valueStr);
        }
    }

    public byte[] getModeBytes() {
        if (CmdPt.pt_op_mode.equals(opcode)) {
            int size = value.size();
            if (size >= 1) {
                String modeValueStr = value.get(size - 1);
                return Encode.decryByBase64(modeValueStr);
            }
        }
        return null;
    }

    /**
     * 多包的透传
     *
     * @param op            op
     * @param multipleBytes multipleBytes
     */
    public CmdPt(String op, List<byte[]> multipleBytes) {
        this.opcode = op;
        if (multipleBytes != null) {
            for (byte[] bytes : multipleBytes) {
                String valueStr = Encode.encryptByBase64(bytes);
                value.add(valueStr);
            }
        }
    }

    public String getOp() {
        return opcode;
    }

    @Override
    public String getCmd() {
        return Cmd.pt;
    }

    public static CmdPt getDiyCmdPt(DiyProtocol diyProtocol) {
        MultipleDiyController multipleDiyController = new MultipleDiyController(diyProtocol);
        /*diy效果数据*/
        List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytes(multipleDiyController);
        Mode mode = new Mode();
        mode.subMode = new SubModeNewDiy(diyProtocol.getDiyCode());
        ModeController modeController = new ModeController(mode);
        /*切换至diy模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytes.add(modeValueBytes);
        return new CmdPt(CmdPt.pt_op_mode, multipleWriteBytes);
    }

    public static CmdPt getNewScenesCmdPt(AbsMultipleControllerV14Scenes multiNewScenesControllerV1) {
        List<byte[]> multipleWriteBytesV1 = MultipleBleBytes.getMultipleWriteBytesV1(multiNewScenesControllerV1);
        Mode mode = new Mode();
        SubModeScenes subMode = new SubModeScenes();
        subMode.setEffect(multiNewScenesControllerV1.getScenesCode());
        mode.subMode = subMode;
        ModeController modeController = new ModeController(mode);
        /*切换至场景模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV1.add(modeValueBytes);
        return new CmdPt(CmdPt.pt_op_mode, multipleWriteBytesV1);
    }

    public static CmdPt getDiyCmdPt4DiyGraffiti(DiyGraffitiV2 diyGraffiti) {
        MultiDiyGraffitiController multiDiyGraffitiController = new MultiDiyGraffitiController(diyGraffiti.getDiyCode(), diyGraffiti.getEffectBytes());
        List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1(multiDiyGraffitiController);
        Mode mode = new Mode();
        mode.subMode = new SubModeNewDiy(diyGraffiti.getDiyCode());
        ModeController modeController = new ModeController(mode);
        /*切换至diy模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytes.add(modeValueBytes);
        return new CmdPt(CmdPt.pt_op_mode, multipleWriteBytes);
    }

    public static CmdPt getMultiNewMusicMode(@NonNull MultipleController4Music multipleController4Music) {
        List<byte[]> multipleWriteBytesV2 = MultipleBleBytes.getMultipleWriteBytesV2(multipleController4Music);
        Mode mode = new Mode();
        mode.subMode = SubModeMusicV2.toNewSubModeMusic(multipleController4Music.getSensitivity(), multipleController4Music.getMusicCode());
        ModeController modeController = new ModeController(mode);
        /*切换至音乐模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV2.add(modeValueBytes);
        return new CmdPt(CmdPt.pt_op_mode, multipleWriteBytesV2);
    }

    public static CmdPt getDiyTemplateCmdPt(AbsMultipleControllerV14DiyTemplate multipleControllerV1) {
        List<byte[]> multipleWriteBytesV1 = MultipleBleBytes.getMultipleWriteBytesV1(multipleControllerV1);
        Mode mode = new Mode();
        SubModeScenes subMode = new SubModeScenes();
        subMode.setEffect(multipleControllerV1.getScenesCode());
        mode.subMode = subMode;
        ModeController modeController = new ModeController(mode);
        /*切换至场景模式*/
        byte[] modeValueBytes = modeController.getValue();
        multipleWriteBytesV1.add(modeValueBytes);
        return new CmdPt(CmdPt.pt_op_mode, multipleWriteBytesV1);
    }

    public static CmdPt getPtRealCmdPt(List<String> commands) {
        if (commands == null || commands.isEmpty()) return null;
        CmdPt cmdPt = new CmdPt();
        cmdPt.value = commands;
        cmdPt.opcode = CmdPt.pt_op_mode;
        return cmdPt;
    }
}