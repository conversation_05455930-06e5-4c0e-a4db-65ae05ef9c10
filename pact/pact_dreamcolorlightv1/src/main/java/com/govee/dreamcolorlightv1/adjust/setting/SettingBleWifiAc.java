package com.govee.dreamcolorlightv1.adjust.setting;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.govee.barcode.barcodescanner.BarcodeActivity;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.custom.LoadingDialog;
import com.govee.base2home.device.DeviceM;
import com.govee.base2home.device.net.Config4IcSettings;
import com.govee.base2home.device.net.Event4NotifyGuideUpdate;
import com.govee.base2home.device.net.Guide;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.util.ClickUtil;
import com.govee.base2light.ac.effect.EventTryConnectDeviceResult;
import com.govee.base2light.neonlight.recommend.NeonRecommendListAc;
import com.govee.base2light.wlanControl.WlanControlOperator;
import com.govee.dreamcolorlightv1.ConsV1;
import com.govee.dreamcolorlightv1.add.v2.WifiChooseAc;
import com.govee.dreamcolorlightv1.adjust.RecommendGraphicManager;
import com.govee.dreamcolorlightv1.adjust.v2.EventOpFreshIc;
import com.govee.dreamcolorlightv1.adjust.v2.EventOpFreshIcResult;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.ui.R;
import com.govee.ui.ac.AbsWifiBleSettingAcV1;
import com.govee.ui.component.OpItemWithHintView;
import com.govee.ui.component.WlanControlView;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JumpUtil;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.Nullable;
import butterknife.BindView;
import butterknife.OnClick;

/**
 * Create by xieyingwu on 2019-05-16
 * 设置Ac
 */
public class SettingBleWifiAc extends AbsWifiBleSettingAcV1 {
    private static final String intent_ac_key_support_ic_fresh = "intent_ac_key_support_ic_fresh";
    private static final String intent_ac_key_adjust_connect_suc = "intent_ac_key_adjust_connect_suc";

    @BindView(com.govee.base2home.R2.id.scanContainer)
    View scanContainer;
    @BindView(com.govee.base2home.R2.id.lineScan)
    View lineScan;

    private int goodsType;
    private boolean supportIcFresh;
    private String topic;
    private OpItemWithHintView opItemWithHintView;
    private boolean inRefreshIc = false;
    private boolean adjustConnectSuc = false;

    public static void jumpSettingBleWifiAc(Context context, boolean supportDeviceLock, int goodsType, boolean supportIcFresh, boolean adjustConnectSuc, String sku, String device, String topic,
                                            String deviceName, int deviceNameInputLimit, String bleAddress, String mac, String versionHard, String versionSoft) {
        int[] wifiInputLimit = Support.getWifiInputLimit();
        Bundle jumpBundle = makeAcBundle(supportDeviceLock, sku, device, deviceName, deviceNameInputLimit, mac, bleAddress, wifiInputLimit[0], wifiInputLimit[1], versionHard, versionSoft);
        if (jumpBundle == null) return;
        jumpBundle.putBoolean(intent_ac_key_support_ic_fresh, supportIcFresh);
        jumpBundle.putBoolean(intent_ac_key_adjust_connect_suc, adjustConnectSuc);
        jumpBundle.putInt(ConsV1.intent_ac_adjust_goodsType, goodsType);
        jumpBundle.putString(ConsV1.intent_ac_adjust_topic, topic);
        JumpUtil.jump(context, SettingBleWifiAc.class, jumpBundle);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        goodsType = intent.getIntExtra(ConsV1.intent_ac_adjust_goodsType, GoodsType.GOODES_TYPE_NO_SUPPORT);
        supportIcFresh = intent.getBooleanExtra(intent_ac_key_support_ic_fresh, false);
        adjustConnectSuc = intent.getBooleanExtra(intent_ac_key_adjust_connect_suc, false);
        boolean support = Support.supportRecommendedGraphic(sku);
        if (support) {
            View viewById = findViewById(com.govee.base2home.R.id.shape_setting_container);
            if (viewById != null) {
                viewById.setVisibility(View.VISIBLE);
                viewById.setOnClickListener(v -> {
                    if (ClickUtil.getInstance.clickQuick()) return;
                    jump2ShapeSettingAc();
                    AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.enter_recommend_graphics, sku);
                });
            }
            View viewLine = findViewById(com.govee.base2home.R.id.line_shape_setting);
            if (viewLine != null) {
                viewLine.setVisibility(View.VISIBLE);
            }
        }
        if (Support.supportQrCodesScene(sku, goodsType)) {
            lineScan.setVisibility(View.VISIBLE);
            scanContainer.setVisibility(View.VISIBLE);
        }
        topic = intent.getStringExtra(ConsV1.intent_ac_adjust_topic);
        checkSupportWlanControl();
        checkOpItemHint();
        if (supportIcFresh) {
            DeviceM.getInstance.requestIcGuide(sku, goodsType);
        }
    }

    private void checkOpItemHint() {
        opItemWithHintView = findViewById(com.govee.base2home.R.id.op_item_with_hint);
        if (opItemWithHintView != null) {
            opItemWithHintView.setOpClickListener(this::sendOpComm);
            opItemWithHintView.makeOpItemConfig(R.mipmap.new_sensor_setting_icon_shuaxin,
                    ResUtil.getString(R.string.label_4_fresh_light_piece),
                    ResUtil.getString(R.string.refresh)
            );
            opItemHintUi();
        }
    }

    private void sendOpComm() {
        LogInfra.Log.i(TAG, "sendOpComm() adjustConnectSuc = " + adjustConnectSuc);
        if (adjustConnectSuc) {
            EventOpFreshIc.sendEventOpFreshIc();
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
        }
    }

    private void opItemHintUi() {
        LogInfra.Log.i(TAG, "opItemHintUi() supportIcFresh = " + supportIcFresh);
        if (opItemWithHintView != null) {
            opItemWithHintView.setVisibility(supportIcFresh ? View.VISIBLE : View.GONE);
            if (supportIcFresh) {
                Guide guide = Config4IcSettings.read().getGuide(sku, goodsType);
                if (guide == null) {
                    String popTitleStr = ResUtil.getString(R.string.label_4_fresh_light_piece);
                    String popDesStr = ResUtil.getString(R.string.hint_4_fresh_light_piece);
                    opItemWithHintView.makePopHint(popTitleStr, popDesStr);
                    opItemWithHintView.makeGuide(-1, "");
                } else {
                    String popTitleStr = ResUtil.getString(R.string.label_4_fresh_light_piece);
                    String popDes = guide.getPopDes();
                    if (popDes == null || TextUtils.isEmpty(popDes)) {
                        popDes = ResUtil.getString(R.string.hint_4_fresh_light_piece);
                    }
                    opItemWithHintView.makePopHint(popTitleStr, popDes);
                    opItemWithHintView.makeGuide(R.mipmap.new_setting_icon_segments_guide, guide.getGuideUrl());
                }
            }
        }
    }

    @Override
    protected void jump2WifiSettingAc() {
        WifiChooseAc.jump2wifiChooseAcByChangeWifi(this, goodsType, sku, device, deviceName, "", bleAddress, versionHard);
    }

    @OnClick(com.govee.base2home.R2.id.scanContainer)
    public void jump2Scan() {
        if (ClickUtil.getInstance.clickQuick()) return;
        getCameraPermission();
    }

    /**
     * 拥有摄像头权限
     */
    @Override
    protected void onCameraPermission() {
        BarcodeActivity.jump(this, sku, device);
    }

    protected void jump2ShapeSettingAc() {
        boolean supportLocal = Support.supportRecommendedShapeLocal(sku);
        if (supportLocal) {
            NeonRecommendListAc.jumpNeonRecommendListAc(this, sku);
        } else {
            RecommendGraphicManager.getInstance().showDialog(this, sku, bleAddress, false);
        }
    }

    @Override
    protected void unbindDevice() {
        super.unbindDevice();
        RecommendGraphicManager.getInstance().clearGraphicStatus(sku, bleAddress);
    }

    @Override
    protected int getLayout() {
        return com.govee.base2home.R.layout.compoent_ac_wifi_setting_barcode;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventAdjustConnectResult(EventTryConnectDeviceResult event) {
        int connectStatus = event.connectStatus;
        LogInfra.Log.i(TAG, "onEventAdjustConnectResult() connectStatus = " + connectStatus);
        adjustConnectSuc = connectStatus == EventTryConnectDeviceResult.connect_status_suc;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventOpFreshIcResult(EventOpFreshIcResult event) {
        int resultType = event.getResultType();
        boolean result = event.isResult();
        LogInfra.Log.i(TAG, "onEventOpFreshIcResult() resultType = " + resultType + " ; result = " + result);
        if (resultType == EventOpFreshIcResult.RESULT_TYPE_FRESH_OP) {
            inRefreshIc = result;
            if (result) {
                showLoading4RefreshIc();
            } else {
                hideLoading4RefreshIc();
            }
            return;
        }

        if (resultType == EventOpFreshIcResult.RESULT_TYPE_FRESH_OP_RESULT) {
            if (inRefreshIc) {
                toast(R.string.hint_fresh_done);
            }
            inRefreshIc = false;
            hideLoading4RefreshIc();
            return;
        }

        if (resultType == EventOpFreshIcResult.RESULT_TYPE_SUPPORT) {
            supportIcFresh = result;
            if (supportIcFresh) {
                DeviceM.getInstance.requestIcGuide(sku, goodsType);
            }
            opItemHintUi();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEvent4NotifyGuideUpdate(Event4NotifyGuideUpdate event) {
        LogInfra.Log.i(TAG, "onEvent4NotifyGuideUpdate()");
        opItemHintUi();
    }

    private void checkSupportWlanControl() {
        WlanControlView wlanControlView = findViewById(com.govee.base2home.R.id.wlan_control);
        if (wlanControlView != null) {
            WlanControlOperator wlanControlOperator = new WlanControlOperator(goodsType, sku, device, topic);
            wlanControlView.checkSupport(wlanControlOperator);
        }
    }

    private void showLoading4RefreshIc() {
        LoadingDialog.createDialog(this, com.ihoment.base2app.R.style.DialogDim, 10 * 1000, () -> {
            inRefreshIc = false;
            toast(R.string.hint_fresh_fail);
        }).setEventKey("onRefreshIc").show();
    }

    private void hideLoading4RefreshIc() {
        LoadingDialog.hideDialog("onRefreshIc");
    }

}