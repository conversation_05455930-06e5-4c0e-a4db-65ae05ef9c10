package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.ui.R;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.v1.AbsColorFragmentV8_3;

import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.EventChangeGradual;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV6;
import com.govee.ui.Cons;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.Arrays;

import androidx.annotation.NonNull;

/**
 * Create by hey on 2021/2/5
 * $
 */
public class ColorFragmentV6 extends AbsColorFragmentV8_3 {
    private SubModeColorV6 subModeColor = new SubModeColorV6();

    @Override
    protected void init() {
        super.init();
        updateUi();
    }

    @Override
    protected void colorChange(int type, int color, int kevin, boolean[] checks, int brightness) {
        SubModeColorV6 subModeColor = new SubModeColorV6();
        if (color == 0 && kevin == 0) {
            subModeColor.brightness = brightness;
        } else if (kevin == 0) {
            subModeColor.rgb = color;
        } else {
            subModeColor.kelvin = kevin;
            subModeColor.ctRgb = color;
            subModeColor.rgb = ColorUtils.toWhite();
        }
        subModeColor.ctlLight = checks;
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.mode_click_color_ + UtilColor.colorName(color));
        /*统计颜色变更来源*/
        AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.getColorFromTypeStr(type));
        AnalyticsRecorder.getInstance().recordUseCount(ParamKey.color_mode, ParamFixedValue.times);
        if (type == Cons.color_from_type_wcBar && kevin > 0) {
            /*统计颜色色温k值*/
            AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.temperature_ + kevin);
        }
    }

    private void updateUi() {
        if (isViewInflateOk()) {
            setSectionNum(subModeColor.sectionNum1, subModeColor.sectionNum2);
            int opType = subModeColor.getOpType();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "updateUi() opType = " + opType);
            }
            if (opType == SubModeColorV6.op_type_def) return;
            if (opType == SubModeColorV6.op_type_gradual) {
                /*更新渐变开关*/
                updateGradual(subModeColor.gradual == 1);
                return;
            }
            /*更新渐变开关*/
            updateGradual(subModeColor.gradual == 1);
            if (opType == SubModeColorV6.op_type_fresh_ui) {
                int[] rgbSet = subModeColor.rgbSet;
                /*设置全部颜色*/
                if (rgbSet != null && rgbSet.length > 0) {
                    updateBulbUI(rgbSet);
                }
                int[] brightnessSet = subModeColor.brightnessSet;
                /*设置全部亮度*/
                if (brightnessSet != null && brightnessSet.length > 0) {
                    updateBulbBrightnessUI(brightnessSet);
                }
                if (subModeColor.getRealRgb() == 0) {
                    return;
                }
            }
            if (opType == SubModeColorV6.op_type_piece_op_brightness) {
                int brightness = subModeColor.brightness;
                /*设置分段亮度*/
                if (brightness != 0) {
                    updateSelectedBulbBrightness(brightness);
                }
                return;
            }
            /*设置分段颜色*/
            int realRgb = subModeColor.getRealRgb();
            if (ColorUtils.invalidRgb(realRgb)) {
                updateSelectedBulbColor(ResUtil.getColor(R.color.ui_light_style_1));
            } else {
                updateSelectedBulbColor(realRgb);
            }
            pieceUi(realRgb);
        }
    }

    private void pieceUi(int rgb) {
        /*刷新色块色条位置*/
        boolean colorTem = ColorUtils.isColorTem(rgb);
        if (colorTem) {
            setColorWithTemColor(ColorUtils.toWhite(), rgb);
        } else {
            setColorWithTemColor(rgb, 0);
        }
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeColorV6) {
            ((SubModeColorV6) subMode).checkAnalytic4SubModeUse(getSku());
            subModeColor = (SubModeColorV6) subMode;
            updateUi();
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    protected void onGradualChange() {
        boolean gradualOpen = subModeColor.gradual == 1;
        EventChangeGradual.sendEventChangeGradual(!gradualOpen);
    }

    @Override
    protected boolean supportRealTimeColor() {
        /*支持实时颜色设置*/
        return true;
    }

    @NonNull
    @Override
    protected ISubMode makePartChooseSubMode(boolean[] check, int color) {
        SubModeColorV6 subModeColor = new SubModeColorV6();
        subModeColor.rgb = color;
        subModeColor.ctlLight = check;
        return subModeColor;
    }

    @Override
    public ISubMode getCompleteSubMode() {
        SubModeColor subMode = new SubModeColor();
        subMode.gradual = subModeColor.gradual;
        int[] rgbArray = Arrays.copyOf(rgb, bulb_num_max);
        for (int i : rgbArray) {
            if (i == 0) {
                return null;
            }
        }
        int[] brightnessArray;
        if (brightness == null) {
            brightnessArray = new int[bulb_num_max];
            for (int i = 0; i < bulb_num_max; i++) {
                brightnessArray[i] = 100;
            }
        } else {
            brightnessArray = Arrays.copyOf(brightness, bulb_num_max);
        }
        subMode.rgbSet = rgbArray;
        subMode.brightnessSet = brightnessArray;
        return subMode;
    }
}
