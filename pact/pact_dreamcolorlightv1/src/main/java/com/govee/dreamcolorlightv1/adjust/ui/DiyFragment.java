package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.base2light.ac.diy.v3.AbsDiyFragmentV1;
import com.govee.base2light.ac.diy.v3.DiyGroup;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;

import java.util.List;

/**
 * Create by xieyingwu on 2/25/21
 * $
 */
public class DiyFragment extends AbsDiyFragmentV1 {
    private SubModeNewDiy subModeNewDiy = new SubModeNewDiy();

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeNewDiy) {
            subModeNewDiy = (SubModeNewDiy) subMode;
            ((SubModeNewDiy) subMode).checkAnalytic4SubModeUse(getSku());
            updateUi();
        }
    }

    @Override
    protected void updateUi() {
        List<DiyGroup> diyGroups = subModeNewDiy.getDiyGroups();
        String diyValueKey = subModeNewDiy.getDiyValueKey();
        updateDiyUi(diyGroups, diyValueKey);
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_diy;
    }
}