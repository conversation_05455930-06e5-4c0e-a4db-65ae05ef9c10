package com.govee.dreamcolorlightv1.ble;

import android.content.Context;

import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.EventDiyTemplate4NewScenes;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.light.AbsOpCommDialog4BleV2;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieying<PERSON> on 2020/8/12
 * op diy操作应用$
 */
public class OpDiyCommDialog4SquareBleV1 extends AbsOpCommDialog4BleV2 {
    private final AbsMultipleControllerV14DiyTemplate diyTemplate;

    protected OpDiyCommDialog4SquareBleV1(Context context, String bleAddress, String bleName, @NonNull AbsMultipleControllerV14DiyTemplate diyTemplate, int type) {
        super(context, bleAddress, bleName, EFFECT_TYPE_DIY, type);
        this.diyTemplate = diyTemplate;
    }

    @Override
    protected void bleOping() {
        getBle().sendMultipleControllerV1(diyTemplate);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyTemplate4NewScenes(EventDiyTemplate4NewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiy() result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            getBle().clearControllers();
            updateResult(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateResult(result);
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    public static void showDialog(Context context, String bleAddress, String bleName, @NonNull AbsMultipleControllerV14DiyTemplate diyTemplate, int type) {
        new OpDiyCommDialog4SquareBleV1(context, bleAddress, bleName, diyTemplate, type).show();
    }
}