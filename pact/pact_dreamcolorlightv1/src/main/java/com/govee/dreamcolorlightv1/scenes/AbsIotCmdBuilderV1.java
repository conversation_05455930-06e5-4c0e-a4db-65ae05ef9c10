package com.govee.dreamcolorlightv1.scenes;

import com.govee.base2home.pact.support.OldDreamColorUtil;
import com.govee.base2home.scenes.builder.BaseCmdModel;
import com.govee.base2home.scenes.builder.CmdBuilder;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.dreamcolorlightv1.pact.Support;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/3/16
 * iot-抽象cmd构造器v1版本$
 */
public abstract class AbsIotCmdBuilderV1<T extends BaseCmdModel> extends CmdBuilder<T> {
    @Override
    public boolean needCheckDeviceModel() {
        return true;
    }

    @Override
    public boolean checkSupport(DeviceModel model) {
        String key = model.getKey();
        if (model.getGoodsType() > 0) {
            /*支持goodsType*/
            return checkSupportKeys(key);
        }
        /*判断是否是升级幻彩设备，则采用新项目进行支持*/
        return OldDreamColorUtil.checkSupportNewDreamColor4OldSku(model.getSku(), model.versionSoft, model.versionHard);
    }

    @Override
    public String[] getSupportKeys() {
        return Support.supportBleWifiV1GoodsSet;
    }
}