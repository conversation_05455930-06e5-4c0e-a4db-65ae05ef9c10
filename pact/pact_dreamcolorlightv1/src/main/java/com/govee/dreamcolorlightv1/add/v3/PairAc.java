package com.govee.dreamcolorlightv1.add.v3;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.govee.base2home.ac.ActivityMgr;
import com.govee.base2home.device.IDeviceNet;
import com.govee.base2home.device.net.DeviceBindRequest;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.main.OfflineDeviceListConfig;
import com.govee.base2home.scenes.SecretKeyConfig;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventHardVersion;
import com.govee.base2light.ble.controller.EventIcNum;
import com.govee.base2light.ble.controller.EventSn;
import com.govee.base2light.ble.controller.EventSoftVersion;
import com.govee.base2light.ble.controller.HardVersionController;
import com.govee.base2light.ble.controller.IcNumController;
import com.govee.base2light.ble.controller.SnController;
import com.govee.base2light.ble.controller.SoftVersionController;
import com.govee.base2light.pact.AbsPairAc4SecretV1;
import com.govee.ble.BleController;
import com.govee.db.memory.ShortMemoryMgr;
import com.govee.dreamcolorlightv1.add.AddInfo;
import com.govee.dreamcolorlightv1.add.BindExt;
import com.govee.dreamcolorlightv1.add.BindExt_1;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.home.account.config.AccountConfig;
import com.govee.kt.ui.device.FastConnectConfig;
import com.govee.ui.R;
import com.ihoment.base2app.Cache;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.network.Network;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.JumpUtil;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2021/6/8
 * 配对流程$
 */
public class PairAc extends AbsPairAc4SecretV1 {
    private static final String intent_ac_key_addInfo = "intent_ac_key_addInfo";
    private AddInfo addInfo;

    /**
     * 跳转到配对页面
     *
     * @param ac
     * @param addInfo
     * @param bluetoothDevice
     */
    public static void jump2PairAc(Context ac, @NonNull AddInfo addInfo, BluetoothDevice bluetoothDevice) {
        Bundle bundle = makeAcBundle(addInfo.sku, bluetoothDevice);
        bundle.putParcelable(intent_ac_key_addInfo, addInfo);
        JumpUtil.jump(ac, PairAc.class, bundle);
    }

    @Override
    protected void parseParams(Intent intent) {
        super.parseParams(intent);
        addInfo = intent.getParcelableExtra(intent_ac_key_addInfo);
    }

    @Override
    protected void finishAc() {
        finish();
    }

    @Override
    protected void readDeviceInfo() {
        AbsSingleController[] controllers;
        boolean supportBindWithIcNum = Support.supportBindWithIcNum(addInfo.goodsType, addInfo.pactType, addInfo.pactCode, addInfo.sku) || Support.supportIcFresh(addInfo.sku, addInfo.goodsType, addInfo.pactType, addInfo.pactCode);
        LogInfra.Log.i(TAG, "readDeviceInfo() supportBindWithIcNum = " + supportBindWithIcNum);
        if (supportBindWithIcNum) {
            controllers = new AbsSingleController[]{
                    new SoftVersionController(),
                    new HardVersionController(),
                    new IcNumController(),
                    new SnController(),
            };
        } else {
            controllers = new AbsSingleController[]{
                    new SoftVersionController(),
                    new HardVersionController(),
                    new SnController(),
            };
        }
        getBle().startController(controllers);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSoftVersionEvent(EventSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "softVersion = " + softVersion);
            }
            addInfo.versionSoft = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onHardVersionEvent(EventHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "hardVersion = " + hardVersion);
            }
            addInfo.versionHard = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onIcNumEvent(EventIcNum event) {
        if (event.isResult()) {
            int icNum = event.icNum;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onIcNumEvent() icNum = " + icNum);
            }
            if (icNum > 0) {
                addInfo.icNum = icNum;
            }
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSnEvent(EventSn event) {
        if (event.isResult()) {
            String device = event.getUuid();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "device = " + device);
            }
            addInfo.device = device;
        }
        getBle().controllerEvent(event);
        if (event.isResult()) {
            /*进步绑定操作*/
            bindDevice();
        }
    }

    private void bindDevice() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "bindDevice()");
        }
        /*构建absDevice对象，用于绑定设备*/
        DeviceExtMode deviceExt = new DeviceExtMode();
        deviceExt.setLastDeviceData("{}");
        deviceExt.setDeviceSettings(makeDeviceSettingsJsonStr());
        AbsDevice absDevice = new AbsDevice(addInfo.device, addInfo.sku, addInfo.versionHard, addInfo.versionSoft, addInfo.deviceName, addInfo.goodsType, deviceExt);
        AccountConfig accountConfig = AccountConfig.read();
        boolean login = accountConfig.isHadToken();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "stepBindDevice() login = " + login);
        }
        if (login) {
            bindDevice(absDevice);
        } else {
            OfflineDeviceListConfig.read().addOfflineDevice(absDevice);
            beBindSuc();
        }
    }

    private String makeDeviceSettingsJsonStr() {
        BindExt bindExt;
        if (addInfo.icNum > 0) {
            bindExt = new BindExt_1(addInfo.pactType, addInfo.pactCode, addInfo.icNum);
        } else {
            bindExt = new BindExt(addInfo.pactType, addInfo.pactCode);
        }
        bindExt.deviceName = addInfo.deviceName;
        bindExt.bleName = addInfo.bleName;
        bindExt.address = addInfo.bleAddress;
        bindExt.secretCode = addInfo.secretCode;
        FastConnectConfig.checkSupportFastConnectNotNull(addInfo.goodsType, addInfo.versionSoft, addInfo.versionHard, addInfo.sku, support -> {
            bindExt.supportBleBroadV3 = support;
            ShortMemoryMgr.INSTANCE.getBleBroadVersionCache().putBleBroadV3Support(bluetoothDevice.getAddress(), bindExt.supportBleBroadV3);
            return null;
        });
        return JsonUtil.toJson(bindExt);
    }

    private void bindDevice(AbsDevice absDevice) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i("makeModel", "SecretKeyController上传 :" + JsonUtil.toJson(absDevice));
        }
        DeviceBindRequest request = new DeviceBindRequest(transactions.createTransaction(), absDevice);
        Cache.get(IDeviceNet.class).bindDevice(request).enqueue(new Network.IHCallBack<>(request));
    }

    @Override
    protected void beBindSuc() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "beBindSuc()");
        }
        ActivityMgr.getInstance().finishAllExceptMain();
        destroy();
        getBle().stopHeart();
        getBle().release();
        BleController.getInstance().toBtClose();
        DeviceNameV3.jump2DeviceName(this, addInfo);
    }

    @Override
    protected void saveSecretKey(String secretCode) {
        /*读操作；获取成功密钥，存储密钥*/
        addInfo.secretCode = secretCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "secretCode = " + addInfo.secretCode);
        }
        String address = bluetoothDevice.getAddress();
        SecretKeyConfig.read().saveSecretKey(address, addInfo.secretCode);
    }

    @Override
    protected int getPairIconRes() {
        return Support.pairRes(addInfo.goodsType, addInfo.sku);
    }

    @Override
    protected void rebind() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "rebind()");
        }
        bindDevice();
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected String getTipsStr() {
        return ResUtil.getString(R.string.b2light_press_hint);
    }
}
