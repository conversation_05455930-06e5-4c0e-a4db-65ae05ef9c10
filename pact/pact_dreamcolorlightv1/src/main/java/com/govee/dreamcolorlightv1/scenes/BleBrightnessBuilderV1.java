package com.govee.dreamcolorlightv1.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.BrightnessModel;
import com.govee.dreamcolorlightv1.pact.Comm;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * ble-亮度控制$
 */
public class BleBrightnessBuilderV1 extends AbsBleCmdBuilderV1<BrightnessModel> {
    @Override
    public IBleCmd createCmd(BrightnessModel brightnessModel) {
        return () -> Comm.makeBrightnessController4BleComm(brightnessModel.model, brightnessModel.brightness).getValue();
    }

}