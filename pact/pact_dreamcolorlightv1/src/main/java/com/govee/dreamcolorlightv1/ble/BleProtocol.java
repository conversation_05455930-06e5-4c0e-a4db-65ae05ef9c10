package com.govee.dreamcolorlightv1.ble;


/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/3/17
 * 蓝牙协议
 */
public interface BleProtocol {
    /**
     * 子模式-music
     */
    byte sub_mode_music = (byte) 0x11;

    /**
     * music效果-能量
     */
    byte value_sub_mode_music_energy = (byte) 0x10;

    /**
     * music效果-节奏
     */
    byte value_sub_mode_music_rhythm = (byte) 0x11;

    /**
     * music效果-滚动
     */
    byte value_sub_mode_music_scroll = (byte) 0x13;

    /**
     * music 效果-频谱
     */
    byte value_sub_mode_music_spectrum = (byte) 0x12;

    /**
     * 子模式-场景
     */
    byte sub_mode_scenes = (byte) 0x04;

    /**
     * 场景效果-日出
     */
    byte value_sub_mode_scenes_gm = (byte) 0;

    /**
     * 场景效果-日落
     */
    byte value_sub_mode_scenes_sunset = (byte) 1;

    /**
     * 场景效果-电影
     */
    byte value_sub_mode_scenes_movie = (byte) 4;

    /**
     * 场景效果-约会
     */
    byte value_sub_mode_scenes_date = (byte) 5;

    /**
     * 场景效果-浪漫
     */
    byte value_sub_mode_scenes_romantic = (byte) 7;

    /**
     * 场景效果-闪烁
     */
    byte value_sub_mode_scenes_blinking = (byte) 8;

    /**
     * 场景效果-烛光
     */
    byte value_sub_mode_scenes_cl = (byte) 9;

    /**
     * 场景效果-呼吸
     */
    byte value_sub_mode_scenes_breath = (byte) 10;

    /**
     * 场景效果-活力
     */
    byte value_sub_mode_scenes_dynamic = (byte) 16;

    /**
     * 场景效果-雪花
     */
    int value_sub_mode_scenes_snow = (byte) 15;

    /**
     * 场景效果-追逐
     */
    byte value_sub_mode_scenes_chase = (byte) 21;


    /**
     * 场景效果-4彩流水
     */
    byte value_sub_mode_scenes_stream = (byte) 22;

    /**
     * 子模式-颜色
     */
    byte sub_mode_color = (byte) 0x0b;

    /**
     * 子模式-新diy
     */
    byte sub_mode_new_diy = (byte) 0x0a;

    /**
     * 多包-diy
     */
    byte value_multiple_ble_diy = (byte) 0x02;

    /**
     * 单包-灯串数量
     */
    byte SINGLE_LIGHT_NUM = (byte) 0x0f;

    /**
     * 单包-开启渐变--针对仅ble的设备
     */
    byte SINGLE_GRADUAL_CHANGE = (byte) 0x14;

    /**
     * 单包-开启渐变--针对ble+wifi的设备
     */
    byte SINGLE_GRADUAL_CHANGE_4_WIFI_BLE = (byte) 0xa3;

    /**
     * 读取灯串颜色
     */
    byte MSG_TYPE_READ_BULB_COLOR = (byte) 0xa2;

    /**
     * 单包协议-限流
     */
    byte SINGLE_LIMIT = (byte) 0x0e;

    /**
     * 音乐模式-新协议
     */
    byte sub_mode_new_music = (byte) 0x13;

    /**
     * music效果-新-能量
     */
    byte value_sub_mode_new_music_energy = (byte) 0x05;

    /**
     * music效果-新-节奏
     */
    byte value_sub_mode_new_music_rhythm = (byte) 0x03;
    /**
     * music效果-新-节奏；子模式-动感
     */
    byte value_sub_mode_new_music_rhythm_power = 0x00;
    /**
     * music效果-新-节奏；子模式-柔和
     */
    byte value_sub_mode_new_music_rhythm_soft = 0x01;

    /**
     * music效果-新-滚动
     */
    byte value_sub_mode_new_music_scroll = (byte) 0x06;

    /**
     * music 效果-新-频谱
     */
    byte value_sub_mode_new_music_spectrum = (byte) 0x04;

    /**
     * 单包-准备ota
     */
    byte SINGLE_OTA_PREPARE = (byte) 0xEE;

    /**
     * 子模式-新颜色 支持分段亮度
     */
    byte sub_mode_color_v2 = (byte) 0x15;

    /**
     * ic数主动上报
     */
    byte SINGLE_NOTIFY_IC = (byte) 0x22;

    /**
     * 检测ic数
     */
    byte SINGLE_WRITE_CHECK_IC = (byte) 0x42;
    /**
     * 读取ic数
     */
    byte SINGLE_READ_IC = 0x40;

    /**
     * 检测ic数
     */
    byte SINGLE_CHECK_IC = 0x46;

}