package com.govee.dreamcolorlightv1.adjust.v1;

import com.govee.base2light.pact.AbsFrameBle;
import com.govee.base2light.pact.BleInfo;
import com.govee.base2light.pact.IFrameResult;
import com.govee.base2light.pact.IUi;
import com.govee.base2light.pact.IUiResult4Ble;
import com.govee.base2light.pact.ble.AbsBlePact;
import com.govee.base2light.pact.ble.IPactResult4Ble;
import com.govee.dreamcolorlightv1.adjust.setting.LimitSettingAc;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.ui.ac.NormalBleSettingAc;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2020-02-12
 * v1版的框架实现-ble$
 * <p>1.协议判断<p/>
 * <p>2.构建ui布局<p/>
 * <p>3.协议控制<p/>
 */
public class FrameV1 extends AbsFrameBle {
    public FrameV1(IFrameResult frameResult, BleInfo info) {
        super(frameResult, info);
    }

    @Override
    protected AbsBlePact makeBlePact(IPactResult4Ble iPactResult4Ble) {
        return new BlePactV1(iPactResult4Ble);
    }

    @Override
    protected List<IUi> getSupportUiList(IUiResult4Ble iUiResult4Ble, BleInfo bleInfo) {
        List<IUi> uiList = new ArrayList<>();
        uiList.add(new UiV1(iUiResult4Ble, bleInfo));
        uiList.add(new UiV3(iUiResult4Ble, bleInfo));
        return uiList;
    }

    @Override
    public void toSettingAc(@NonNull AppCompatActivity ac) {
        if (isDestroy()) return;
        boolean supportLimit = false;
        boolean supportDeviceLock = Support.supportDeviceLock(info.goodsType, info.sku, info.pactType, info.pactCode);
        if (Support.supportLimit(info.goodsType)) {
            /*执行限流，跳转到限流设置页*/
            supportLimit = Support.supportLimit4SkuHardVersion(info.sku, info.versionHard);
        }
        if (supportLimit) {
            LimitSettingAc.jump2LimitSettingAc(ac, supportDeviceLock, info.sku, info.device, info.deviceName, info.versionHard, getLimitOpen());
        } else {
            NormalBleSettingAc.jump2NormalBleSettingAc(ac, supportDeviceLock, info.sku, info.device, info.deviceName, info.versionHard, 22);
        }
    }

    private boolean getLimitOpen() {
        if (curUi instanceof IUi4ExtInfo) {
            return ((IUi4ExtInfo) curUi).limitOpen();
        }
        return false;
    }
}