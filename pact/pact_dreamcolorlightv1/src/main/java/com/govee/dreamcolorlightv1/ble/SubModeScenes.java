package com.govee.dreamcolorlightv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.ISubModeScenes;
import com.govee.base2light.light.ModeStr;
import com.ihoment.base2app.infra.StorageInfra;

import androidx.annotation.NonNull;

/**
 * Create by xieying<PERSON> on 2019-04-19
 * 场景模式
 */
public class SubModeScenes extends AbsSubMode4Analytic implements ISubModeScenes {
    private int effect = BleUtil.getUnsignedByte(BleProtocol.value_sub_mode_scenes_gm);/*默认效果电影*/

    @Override
    public void loadLocal() {
        SubModeScenes subModeScenes = StorageInfra.get(SubModeScenes.class);
        if (subModeScenes == null) return;
        this.effect = subModeScenes.effect;
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(this);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_scenes;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr = ModeStr.getScenesSubModeStr(effect);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_scenes, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        effect = BleUtil.getSignedShort(validBytes[1], validBytes[0]);
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] effectBytes = BleUtil.getSignedBytesFor2(effect, false);
        return new byte[]{subModeCommandType(), effectBytes[0], effectBytes[1]};
    }

    public int getEffect() {
        return effect;
    }

    public void setEffect(int effect) {
        this.effect = effect;
    }

    /**
     * 设置场景模式，读写操作一致，解析一致
     *
     * @param validBytes
     * @return
     */
    public static SubModeScenes parseSubModeScenes4Write(byte[] validBytes) {
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.parse(validBytes);
        return subModeScenes;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_scenes;
    }
}