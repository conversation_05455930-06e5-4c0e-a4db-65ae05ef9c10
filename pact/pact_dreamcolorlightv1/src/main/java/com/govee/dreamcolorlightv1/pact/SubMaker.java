package com.govee.dreamcolorlightv1.pact;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.sku.IMaker;
import com.govee.base2home.sku.ISkuItem;
import com.govee.dreamcolorlightv1.pact.ble.V1BleSkuItem;
import com.govee.dreamcolorlightv1.pact.ble.V2BleSkuItem;
import com.govee.dreamcolorlightv1.pact.ble.V3BleSkuItem;
import com.govee.dreamcolorlightv1.pact.ble.V4BleSkuItem;
import com.govee.dreamcolorlightv1.pact.bleiot.AbsBleIotSkuItem;
import com.govee.dreamcolorlightv1.pact.bleiot.H6169BleIotSkuItem;
import com.govee.dreamcolorlightv1.pact.bleiot.V1BleIotSkuItem;
import com.govee.dreamcolorlightv1.pact.bleiot.V2BleIotSkuItem;
import com.govee.dreamcolorlightv1.pact.bleiot.V3BleIotSkuItem;
import com.govee.dreamcolorlightv1.pact.bleiot.V4BleIotSkuItem;
import com.govee.dreamcolorlightv1.pact.bleiot.V5BleIotSkuItem;
import com.govee.dreamcolorlightv1.pact.bleiot.V6BleIotSkuItem;
import com.govee.dreamcolorlightv1.pact.bleiot.V7BleIotSkuItem;

import java.util.ArrayList;
import java.util.List;


/**
 * Create by xieyingwu on 2019-12-10
 * maker
 */
public class SubMaker implements IMaker {
    private List<ISkuItem> makers = new ArrayList<>();

    public SubMaker() {
        makers.add(new V1BleSkuItem());
        makers.add(new V2BleSkuItem());
        makers.add(new V3BleSkuItem());
        makers.add(new V4BleSkuItem());
        makers.add(new V1BleIotSkuItem());
        makers.add(new V2BleIotSkuItem());
        makers.add(new V3BleIotSkuItem());
        makers.add(new V4BleIotSkuItem());
        makers.add(new V5BleIotSkuItem());
        makers.add(new V6BleIotSkuItem());
        makers.add(new V7BleIotSkuItem());
        makers.add(new H6169BleIotSkuItem());
        makers.add(new AbsBleIotSkuItem() {
            @Override
            protected int curGoodsType() {
                return GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC;
            }
        });
        makers.add(new AbsBleIotSkuItem() {
            @Override
            protected int curGoodsType() {
                return GoodsType.GOODS_TYPE_VALUE_H70A1_A2_A3;
            }
        });
        makers.add(new AbsBleIotSkuItem() {
            @Override
            protected int curGoodsType() {
                return GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H6175;
            }
        });
        makers.add(new AbsBleIotSkuItem() {
            @Override
            protected int curGoodsType() {
                return GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA;
            }
        });
        makers.add(new AbsBleIotSkuItem() {
            @Override
            protected int curGoodsType() {
                return GoodsType.GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE;
            }
        });
        makers.add(new AbsBleIotSkuItem() {
            @Override
            protected int curGoodsType() {
                return GoodsType.GOODS_TYPE_VALUE_H61A9;
            }
        });
        makers.add(new AbsBleIotSkuItem() {
            @Override
            protected int curGoodsType() {
                return GoodsType.GOODS_TYPE_VALUE_H616C;
            }
        });
        makers.add(new AbsBleIotSkuItem() {
            @Override
            protected int curGoodsType() {
                return GoodsType.GOODS_TYPE_VALUE_H612x;
            }
        });
    }

    @Override
    public List<ISkuItem> getSupportMakers() {
        return makers;
    }
}