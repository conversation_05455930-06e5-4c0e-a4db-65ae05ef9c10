package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.SubModeAbsMusic;
import com.ihoment.base2app.infra.LogInfra;

/**
 * Create by xie<PERSON><PERSON> on 2019-04-19
 * 模式解析对象
 */
public class Mode extends AbsMode {
    private static final String TAG = "Mode";

    public static ISubMode parseWriteSubMode(int colorVersion, int musicVersion, byte subModeType, byte[] subModeValidBytes) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parseWriteSubMode() subModeType = " + subModeType + " ; subModeValidBytes = " + BleUtil.bytesToHexString(subModeValidBytes));
        }
        ISubMode subMode;
        if (subModeType == BleProtocol.sub_mode_music) {
            ParamsSubMode4Music paramsSubMode4Music = new ParamsSubMode4Music();
            paramsSubMode4Music.parse(subModeValidBytes);
            subMode = paramsSubMode4Music.toSupportSubMode(musicVersion);
        } else if (subModeType == BleProtocol.sub_mode_color_v2) {
            if (colorVersion == 1) {
                subMode = SubModeColorV3.parseSubModeColor4Write(subModeValidBytes);
            } else {
                subMode = SubModeColorV2.parseSubModeColor4Write(subModeValidBytes);
            }
        } else if (subModeType == BleProtocol.sub_mode_scenes) {
            subMode = SubModeScenes.parseSubModeScenes4Write(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_diy) {
            subMode = SubModeNewDiy.parseSubModeNewDiy4Write(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_music) {
            ParamsSubMode4MusicV1 paramsSubMode4MusicV1 = new ParamsSubMode4MusicV1();
            paramsSubMode4MusicV1.parse(subModeValidBytes);
            subMode = paramsSubMode4MusicV1.toSupportSubMode(musicVersion);
        } else if (subModeType == BleProtocolConstants.sub_mode_abs_music) {
            subMode = SubModeAbsMusic.Companion.parseSubModeMusic4Write(subModeValidBytes);
        } else {
            /*默认都按颜色模式解析*/
            subMode = SubModeColor.parseSubModeColor4Write(subModeValidBytes);
        }
        return subMode;
    }

    @Override
    protected void parseSubMode(byte subModeType, byte[] subModeValidBytes) {
        if (subModeType == BleProtocol.sub_mode_music) {
            subMode = new ParamsSubMode4Music();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_music) {
            subMode = new ParamsSubMode4MusicV1();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_scenes) {
            subMode = new SubModeScenes();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_diy) {
            subMode = new SubModeNewDiy();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_color_v2) {
            subMode = new SubModeColorV2();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocolConstants.sub_mode_abs_music) {
            subMode = new SubModeAbsMusic();
            subMode.parse(subModeValidBytes);
        } else {
            /*默认都按照颜色模式解析*/
            subMode = new SubModeColor();
            subMode.parse(subModeValidBytes);
        }
    }

    public void changeMusicMode4MultiNewMusic4Telink(int musicVersion) {
        boolean musicV0 = subMode instanceof SubModeMusic;
        boolean musicV1 = subMode instanceof SubModeMusicV2;
        boolean isMusic = musicV0 || musicV1;
        if (!isMusic) return;
        if (musicVersion == 0) {
            if (!musicV0) subMode = new SubModeMusic();
            return;
        }
        if (musicVersion == 1) {
            if (!musicV1) subMode = new SubModeMusicV2();
        }
    }

    public void changeMusicMode4MultiNewMusic4Bk(int musicVersion) {
        boolean musicV0 = subMode instanceof SubModeMusicV1;
        boolean musicV1 = subMode instanceof SubModeMusicV3;
        boolean isMusic = musicV0 || musicV1;
        if (!isMusic) return;
        if (musicVersion == 0) {
            if (!musicV0) subMode = new SubModeMusicV1();
            return;
        }
        if (musicVersion == 1) {
            if (!musicV1) subMode = new SubModeMusicV3();
        }
    }
}