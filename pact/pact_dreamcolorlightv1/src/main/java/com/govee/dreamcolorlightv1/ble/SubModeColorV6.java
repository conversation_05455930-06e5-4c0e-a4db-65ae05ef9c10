package com.govee.dreamcolorlightv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.comm.MultipleBleBytes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.MultipleColorStripControllerV1;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

import androidx.annotation.NonNull;

/**
 * Create by hey on 2021/2/5
 * 颜色模式-20段$
 */
public class SubModeColorV6 extends AbsSubMode4Analytic {
    private static final byte sub_op_type_set_color = 0x01;
    private static final byte sub_op_type_set_brightness = 0x02;
    private static final byte sub_op_type_set_all_brightness = 0x03;

    public static final int op_type_def = -1;
    public static final int op_type_fresh_ui = 1;
    public static final int op_type_piece_op_brightness = 2;
    public static final int op_type_piece_op_color = 3;
    public static final int op_type_gradual = 4;

    public static final int op_write_type_set_color = 1;
    public static final int op_write_type_set_brightness = 2;
    public static final int op_write_type_set_all_brightness = 3;

    private static final int COLOR_PIECE = 20;

    public int gradual = 0;/*默认不开启渐变*/
    public boolean[] ctlLight = new boolean[COLOR_PIECE];
    public int[] rgbSet;
    public int[] brightnessSet;

    public int brightness;
    public int rgb = 0;/*默认颜色-红色*/
    public int kelvin;
    public int ctRgb;

    public int sectionNum1 = 10;
    public int sectionNum2 = 10;

    public int getOpType() {
        if (rgbSet != null && rgbSet.length > 0) return op_type_fresh_ui;
        if (brightnessSet != null && brightnessSet.length > 0) return op_type_fresh_ui;
        if (brightness > 0) return op_type_piece_op_brightness;
        return op_type_piece_op_color;
    }

    @Override
    public void loadLocal() {
    }

    @Override
    public void saveLocal() {
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr = UtilColor.colorName(rgb);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_color, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        this.gradual = BleUtil.getUnsignedByte(validBytes[0]);
    }

    /**
     * 设置颜色
     *
     * @return
     */
    private byte[] bytes4SetColor() {
        byte[] bytes = new byte[17];
        bytes[0] = subModeCommandType();
        bytes[1] = sub_op_type_set_color;
        int[] rgb = ColorUtils.getRgb(this.rgb);
        bytes[2] = (byte) rgb[0];
        bytes[3] = (byte) rgb[1];
        bytes[4] = (byte) rgb[2];
        if (kelvin != 0) {
            byte[] signedBytesFor2 = BleUtil.getSignedBytesFor2(kelvin, true);
            bytes[5] = signedBytesFor2[0];
            bytes[6] = signedBytesFor2[1];
            int[] ctRgb = ColorUtils.getRgb(this.ctRgb);
            bytes[7] = (byte) ctRgb[0];
            bytes[8] = (byte) ctRgb[1];
            bytes[9] = (byte) ctRgb[2];
        }
        byte[] selectPosBytes = BleUtil.makeBytes4SelectPosByOneBit(ctlLight);
        System.arraycopy(selectPosBytes, 0, bytes, 10, selectPosBytes.length);
        return bytes;
    }

    /**
     * 设置亮度
     *
     * @return
     */
    private byte[] bytes4SetBrightness() {
        byte[] bytes = new byte[17];
        bytes[0] = subModeCommandType();
        bytes[1] = sub_op_type_set_brightness;
        bytes[2] = (byte) brightness;
        byte[] selectPosBytes = BleUtil.makeBytes4SelectPosByOneBit(ctlLight);
        System.arraycopy(selectPosBytes, 0, bytes, 3, selectPosBytes.length);
        return bytes;
    }

    /**
     * 设置整组亮度
     *
     * @return
     */
    private byte[] bytes4SetAllBrightness() {
        byte[] bytes = new byte[17];
        bytes[0] = subModeCommandType();
        bytes[1] = sub_op_type_set_all_brightness;
        int index = 2;
        for (int brightness : brightnessSet) {
            bytes[index] = (byte) brightness;
            index++;
        }
        return bytes;
    }

    public int getWriteOpType() {
        if (brightnessSet != null && brightnessSet.length > 0) {
            return op_write_type_set_all_brightness;
        }
        if (brightness != 0) {
            return op_write_type_set_brightness;
        }
        return op_write_type_set_color;
    }

    @Override
    public byte[] getWriteBytes() {
        int writeOpType = getWriteOpType();
        /*设置整组亮度*/
        if (writeOpType == op_write_type_set_all_brightness) {
            return bytes4SetAllBrightness();
        }
        /*设置亮度*/
        if (writeOpType == op_write_type_set_brightness) {
            return bytes4SetBrightness();
        }
        /*设置颜色*/
        return bytes4SetColor();
    }

    public int getRealRgb() {
        if (kelvin > 0) return ctRgb;
        return rgb;
    }

    public void setRgb(int rgb) {
        this.rgb = rgb;
        this.ctRgb = 0;
        this.kelvin = 0;
    }

    public static SubModeColorV6 makeSubModeColor(int color) {
        SubModeColorV6 subModeColor = new SubModeColorV6();
        subModeColor.rgb = color;
        subModeColor.kelvin = 0;
        subModeColor.ctRgb = 0;
        int length = subModeColor.ctlLight.length;
        int[] rgbSet = new int[length];
        for (int i = 0; i < length; i++) {
            subModeColor.ctlLight[i] = true;
            rgbSet[i] = color;
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }

    public static boolean isSetMode2Color(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opByte == BleProtocolConstants.SINGLE_MODE && modeType == BleProtocol.sub_mode_color_v2;
        }
        return false;
    }

    /**
     * 是否是设置颜色模式指令-
     *
     * @param original20Bytes
     * @return int[2] - [0]:表示结果，0-不是设置颜色模式；1-是设置颜色模式；[1]:表示设置颜色模式类型
     */
    public static int[] isSetColor(byte[] original20Bytes) {
        int[] result = new int[2];
        if (original20Bytes == null || original20Bytes.length != 20) return result;
        byte opTypeByte = original20Bytes[0];
        byte opByte = original20Bytes[1];
        byte modeType = original20Bytes[2];
        if (opTypeByte != BleProtocolConstants.SINGLE_WRITE) return result;
        if (opByte != BleProtocolConstants.SINGLE_MODE) return result;
        if (modeType != BleProtocol.sub_mode_color_v2) return result;
        result[0] = 1;
        result[1] = original20Bytes[3];
        return result;
    }

    public void parseWriteBytes(byte[] original20Bytes) {
        if (original20Bytes[3] == sub_op_type_set_color) {
            rgb = ColorUtils.toColor(original20Bytes[4], original20Bytes[5], original20Bytes[6]);
            kelvin = BleUtil.convertTwoBytesToShort(original20Bytes[7], original20Bytes[8]);
            ctRgb = ColorUtils.toColor(original20Bytes[9], original20Bytes[10], original20Bytes[11]);
            return;
        }
        if (original20Bytes[3] == sub_op_type_set_brightness) {
            brightness = BleUtil.getUnsignedByte(original20Bytes[4]);
        }
    }

    public static AbsSingleController[] makeSubModeColor(int[] colors, boolean isFade, boolean hadFadeController) {
        if (colors.length != COLOR_PIECE) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        int fadeSize = hadFadeController ? 1 : 0;
        AbsSingleController[] modeControllers = new AbsSingleController[hashMap.size() + fadeSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV6 subModeColor = new SubModeColorV6();
            subModeColor.rgb = color;
            subModeColor.kelvin = 0;
            subModeColor.ctRgb = 0;
            subModeColor.ctlLight = new boolean[COLOR_PIECE];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            modeControllers[i] = new Gradual4BleWifiController(isFade);
        }
        return modeControllers;
    }

    private static void parseSetColor4Strip(byte[] bytes, SubModeColorV6 subModeColorV4) {
        int rgb = ColorUtils.toColor(bytes[4], bytes[5], bytes[6]);
        int kelvin = BleUtil.convertTwoBytesToShort(bytes[7], bytes[8]);
        int temRgb = ColorUtils.toColor(bytes[9], bytes[10], bytes[11]);
        int rgbValue;
        if (kelvin > 0) {
            rgbValue = temRgb;
        } else {
            rgbValue = rgb;
        }
        /*从低位bit开始描述*/
        boolean[] low8Set = BleUtil.parseBytes4BitReverse(bytes[12]);
        boolean[] high8Set = BleUtil.parseBytes4BitReverse(bytes[13]);
        boolean[] rgbSetSelect = new boolean[16];
        System.arraycopy(low8Set, 0, rgbSetSelect, 0, 8);
        System.arraycopy(high8Set, 0, rgbSetSelect, 8, 8);
        int len = Math.min(subModeColorV4.rgbSet.length, rgbSetSelect.length);
        for (int i = 0; i < len; i++) {
            if (rgbSetSelect[i]) {
                subModeColorV4.rgbSet[i] = rgbValue;
            }
        }
    }

    private static void parseSetBrightness4Strip(byte[] bytes, SubModeColorV6 subModeColorV4) {
        byte[] brightnessBytes = new byte[16];
        System.arraycopy(bytes, 4, brightnessBytes, 0, brightnessBytes.length);
        int len = Math.min(subModeColorV4.brightnessSet.length, brightnessBytes.length);
        for (int i = 0; i < len; i++) {
            subModeColorV4.brightnessSet[i] = BleUtil.getUnsignedByte(brightnessBytes[i]);
        }
    }

    public static void parsePosColor(byte[] setPosColorBytes, @NonNull SubModeColorV6 subModeColor) {
        if (setPosColorBytes == null || setPosColorBytes.length != 20) return;
        if (setPosColorBytes[3] == sub_op_type_set_color) {
            if (subModeColor.rgbSet == null) {
                subModeColor.rgbSet = new int[subModeColor.ctlLight.length];
            }
            parseSetColor4Strip(setPosColorBytes, subModeColor);
            return;
        }
        if (setPosColorBytes[3] == sub_op_type_set_all_brightness) {
            if (subModeColor.brightnessSet == null) {
                subModeColor.brightnessSet = new int[subModeColor.ctlLight.length];
            }
            parseSetBrightness4Strip(setPosColorBytes, subModeColor);
        }
    }

    public boolean isSelectAll() {
        if (ctlLight != null) {
            for (boolean b : ctlLight) {
                if (!b) return false;
            }
            return true;
        }
        return false;
    }

    public static AbsSingleController[] makeSubModeColor(Colors colorStrip) {
        int[] colors = colorStrip.colorSet;
        if (colors.length != COLOR_PIECE) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        boolean hasBrightness = colorStrip.brightnessSet != null && colorStrip.brightnessSet.length == COLOR_PIECE;
        AbsSingleController[] modeControllers = new AbsSingleController[hasBrightness ? hashMap.size() + 1 : hashMap.size()];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV6 subModeColor = new SubModeColorV6();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[COLOR_PIECE];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hasBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hasBrightness) {
            SubModeColorV6 subModeColor = new SubModeColorV6();
            subModeColor.brightnessSet = colorStrip.brightnessSet;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i] = modeController;
        }
        return modeControllers;
    }

    public static AbsSingleController[] makeSubModeColor(int[] colors, int[] brightnessArray, boolean isFade, boolean hadFadeController) {
        if (colors == null || colors.length != COLOR_PIECE) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        boolean hadBrightness = brightnessArray != null && brightnessArray.length == COLOR_PIECE;
        int fadeSize = hadFadeController ? 1 : 0;
        AbsSingleController[] modeControllers = new AbsSingleController[hadBrightness ? hashMap.size() + fadeSize + 1 : hashMap.size() + fadeSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV6 subModeColor = new SubModeColorV6();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[COLOR_PIECE];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hadBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadBrightness) {
            SubModeColorV6 subModeColor = new SubModeColorV6();
            subModeColor.brightnessSet = brightnessArray;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            modeControllers[i] = new Gradual4BleWifiController(isFade);
        }
        return modeControllers;
    }

    public static SubModeColorV6 makeSubModeColor4Group(Colors colors) {
        SubModeColorV6 subModeColorV2 = new SubModeColorV6();
        subModeColorV2.rgbSet = colors.colorSet;
        subModeColorV2.brightnessSet = colors.brightnessSet;
        return subModeColorV2;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_color;
    }

    @NonNull
    public static List<byte[]> makeSnapshotBytes(Colors colors, int gradual) {
        MultipleColorStripControllerV1 controllerV1 = new MultipleColorStripControllerV1(colors);
        List<byte[]> opBytes = MultipleBleBytes.getMultipleWriteBytesV1(controllerV1);
        if (gradual != -1) {
            opBytes.add(new Gradual4BleWifiController(gradual == 1).getValue());
        }
        return opBytes;
    }
}