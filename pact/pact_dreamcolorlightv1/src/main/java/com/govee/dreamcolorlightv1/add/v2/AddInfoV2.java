package com.govee.dreamcolorlightv1.add.v2;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020/3/6
 * 添加信息$
 */
public class AddInfoV2 implements Parcelable {
    /*广播字段*/
    public String sku;
    public int goodsType;
    public String bleName;
    public String bleAddress;
    public String deviceName;
    public int pactType;
    public int pactCode;
    /*协议字段*/
    public String versionSoft;
    public String versionHard;
    public String device;

    public String wifiSoftVersion;
    public String wifiHardVersion;
    public String wifiMac;
    /*设备密钥*/
    public String secretCode;
    /*详情页class*/
    public String adjustAcClassName;
    /*ic数*/
    public int icNum = -1;

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.sku);
        dest.writeInt(this.goodsType);
        dest.writeString(this.bleName);
        dest.writeString(this.bleAddress);
        dest.writeString(this.deviceName);
        dest.writeInt(this.pactType);
        dest.writeInt(this.pactCode);
        dest.writeString(this.versionSoft);
        dest.writeString(this.versionHard);
        dest.writeString(this.device);
        dest.writeString(this.wifiSoftVersion);
        dest.writeString(this.wifiHardVersion);
        dest.writeString(this.wifiMac);
        dest.writeString(this.secretCode);
        dest.writeString(this.adjustAcClassName);
        dest.writeInt(this.icNum);
    }

    public AddInfoV2() {
    }

    protected AddInfoV2(Parcel in) {
        this.sku = in.readString();
        this.goodsType = in.readInt();
        this.bleName = in.readString();
        this.bleAddress = in.readString();
        this.deviceName = in.readString();
        this.pactType = in.readInt();
        this.pactCode = in.readInt();
        this.versionSoft = in.readString();
        this.versionHard = in.readString();
        this.device = in.readString();
        this.wifiSoftVersion = in.readString();
        this.wifiHardVersion = in.readString();
        this.wifiMac = in.readString();
        this.secretCode = in.readString();
        this.adjustAcClassName = in.readString();
        this.icNum = in.readInt();
    }

    public static final Creator<AddInfoV2> CREATOR = new Creator<AddInfoV2>() {
        @Override
        public AddInfoV2 createFromParcel(Parcel source) {
            return new AddInfoV2(source);
        }

        @Override
        public AddInfoV2[] newArray(int size) {
            return new AddInfoV2[size];
        }
    };
}