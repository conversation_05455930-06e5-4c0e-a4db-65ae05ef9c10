package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.ui.R;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsColorUiMode;

import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.SubModeColorV3;

/**
 * Create by hey on 2021/2/5
 * $
 */
public class ColorUiModeV4 extends AbsColorUiMode {
    public static ColorUiModeV4 makeColorUiModeV44Factor(String sku) {
        return new ColorUiModeV4(sku);
    }

    private ColorUiModeV4(String sku) {
        super(sku);
    }

    public ColorUiModeV4(int goodsType, String sku, String device, int ic, DiySupportV1 diySupport) {
        super(goodsType, sku, device, ic, diySupport);
    }

    @Override
    public AbsModeFragment getUiFragment() {
        ColorFragmentV4 fragment = new ColorFragmentV4();
        fragment.makeArguments(getSku());
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_color_mini, R.mipmap.new_control_light_btb_mode_color_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeColorV3 subModeColor = new SubModeColorV3();
        subModeColor.loadLocal();
        return subModeColor;
    }
}
