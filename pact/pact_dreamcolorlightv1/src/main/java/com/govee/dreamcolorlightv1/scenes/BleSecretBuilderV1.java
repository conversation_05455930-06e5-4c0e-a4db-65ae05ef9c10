package com.govee.dreamcolorlightv1.scenes;

import com.govee.base2home.scenes.IBleCmd;
import com.govee.base2home.scenes.builder.model.SecretModel;
import com.govee.base2light.ble.controller.SecretKeyController;
import com.govee.dreamcolorlightv1.pact.Support;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * ble-验证密钥$
 */
public class BleSecretBuilderV1 extends AbsBleCmdBuilderV1<SecretModel> {

    @Override
    public IBleCmd createCmd(final SecretModel secretModel) {
        return () -> {
            SecretKeyController controller = new SecretKeyController(secretModel.secret);
            return controller.getValue();
        };
    }

    @Override
    public String[] getSupportKeys() {
        return Support.supportPairCheckGoodsType;
    }
}