package com.govee.dreamcolorlightv1.add.v3;

import android.bluetooth.BluetoothDevice;
import android.content.Context;

import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.AbsConnectDialog4Secret;
import com.govee.dreamcolorlightv1.add.v2.AddInfoV2;
import com.govee.dreamcolorlightv1.ble.Ble;

import androidx.annotation.NonNull;

/**
 * Create by xie<PERSON><PERSON> on 2021/6/10
 * $
 */
public class ConnectDialogV4 extends AbsConnectDialog4Secret {
    private final AddInfoV2 addInfo;

    protected ConnectDialogV4(Context context, BluetoothDevice device, @NonNull AddInfoV2 addInfo) {
        super(context, device, addInfo.sku);
        this.addInfo = addInfo;
    }

    public static void showDialog(Context context, @NonNull AddInfoV2 addInfo, BluetoothDevice bluetoothDevice) {
        new ConnectDialogV4(context, bluetoothDevice, addInfo).show();
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected void jumpToPairAc() {
        PairAcV1.jump2PairAc(context, addInfo, device);
    }
}