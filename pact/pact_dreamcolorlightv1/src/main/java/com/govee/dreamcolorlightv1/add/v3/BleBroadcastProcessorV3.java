package com.govee.dreamcolorlightv1.add.v3;

import android.app.Activity;

import com.govee.base2home.main.choose.BaseBleDeviceModel;
import com.govee.base2home.main.choose.BaseBleProcessor;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.sku.DefaultDeviceNameUtil;
import com.govee.dreamcolorlightv1.add.AddInfo;
import com.govee.dreamcolorlightv1.pact.Support;

/**
 * Create by xieyingwu on 2021/6/10
 * $
 */
public class BleBroadcastProcessorV3 extends BaseBleProcessor {
    @Override
    public boolean onItemClick(Activity activity, BaseBleDeviceModel model, boolean singleSku) {
        int goodsType = model.goodsType;
        Protocol protocol = model.protocol;
        /*ble=无需登录*/
        if (Support.supportPactV3(goodsType, protocol)) {
            AddInfo addInfo = new AddInfo();
            addInfo.sku = model.getSku();
            addInfo.goodsType = goodsType;
            addInfo.pactType = protocol.pactType;
            addInfo.pactCode = protocol.pactCode;
            addInfo.deviceName = Support.getSkuDefName(model.getSku(), model.getDeviceName());
            addInfo.deviceName = DefaultDeviceNameUtil.INSTANCE.getSkuDefaultName(model.getSku(), addInfo.deviceName);
            addInfo.bleName = model.getBleName();
            addInfo.bleAddress = model.getDevice().getAddress();
            ConnectDialogV3.showDialog(activity, addInfo, model.getDevice());
            return true;
        }
        return false;
    }
}