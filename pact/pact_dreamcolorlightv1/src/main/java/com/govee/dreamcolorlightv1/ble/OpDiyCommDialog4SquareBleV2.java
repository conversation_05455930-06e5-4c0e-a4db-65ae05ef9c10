package com.govee.dreamcolorlightv1.ble;

import android.content.Context;

import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultiNewDiyGraffiti;
import com.govee.base2light.ble.controller.MultiDiyGraffitiController;
import com.govee.base2light.light.AbsOpCommDialog4BleV2;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/8/12
 * op diy操作应用$
 */
public class OpDiyCommDialog4SquareBleV2 extends AbsOpCommDialog4BleV2 {
    private final DiyGraffitiV2 diyGraffiti;

    protected OpDiyCommDialog4SquareBleV2(Context context, String bleAddress, String bleName, @NonNull DiyGraffitiV2 diyGraffiti, int type) {
        super(context, bleAddress, bleName, EFFECT_TYPE_DIY, type);
        this.diyGraffiti = diyGraffiti;
    }

    @Override
    protected void bleOping() {
        MultiDiyGraffitiController multiDiyGraffitiController = new MultiDiyGraffitiController(diyGraffiti.getDiyCode(), diyGraffiti.getEffectBytes());
        getBle().sendMultipleControllerV1(multiDiyGraffitiController);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleDiyGraffiti(EventMultiNewDiyGraffiti event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiyGraffiti() result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(event.getDiyCode());
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            getBle().clearControllers();
            updateResult(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    public static void showDialog(Context context, String bleAddress, String bleName, @NonNull DiyGraffitiV2 diyGraffiti, int type) {
        new OpDiyCommDialog4SquareBleV2(context, bleAddress, bleName, diyGraffiti, type).show();
    }
}