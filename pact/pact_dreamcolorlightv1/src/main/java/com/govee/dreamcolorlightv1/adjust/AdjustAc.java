package com.govee.dreamcolorlightv1.adjust;

import android.content.Intent;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.sku.SkuIcM;
import com.govee.base2light.ble.scenes.ScenesM;
import com.govee.base2light.pact.AbsPactAdjustAc4Ble;
import com.govee.base2light.pact.BleInfo;
import com.govee.base2light.pact.IFrame;
import com.govee.base2light.pact.IFrameResult;
import com.govee.ble.BleController;
import com.govee.dreamcolorlightv1.ConsV1;
import com.govee.dreamcolorlightv1.adjust.setting.EventRetryConnectBle;
import com.govee.dreamcolorlightv1.adjust.v1.FrameV1;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/3/17
 * 控制页-仅支持ble$
 * OldDreamColorUtil.jump2NewDreamColorAdjustAcFromOldAdjustAc调用跳转至该详情页，还在使用
 */
public class AdjustAc extends AbsPactAdjustAc4Ble {
    private static final String TAG = "AdjustAc";

    @NonNull
    @Override
    protected IFrame makeFrame(IFrameResult iFrameResult, BleInfo bleInfo) {
        /*依据对应的bleInfo的goodsType进行框架选择*/
        return new FrameV1(iFrameResult, bleInfo);
    }

    @Override
    protected int[] getDefHeaderRes() {
        return Support.getDefHeaderRes4Ble(info.goodsType, info.sku);
    }

    @Override
    protected boolean needCheckDefHeaderRes() {
        return Support.needCheckHeaderRes4Ble(info.sku);
    }

    @NonNull
    @Override
    protected BleInfo makeInfoFromIntent(Intent intent) {
        String sku = intent.getStringExtra(ConsV1.intent_ac_adjust_sku);
        String device = intent.getStringExtra(ConsV1.intent_ac_adjust_device);
        String spec = intent.getStringExtra(ConsV1.intent_ac_adjust_spec);
        String deviceName = intent.getStringExtra(ConsV1.intent_ac_adjust_deviceName);
        int goodsType = intent.getIntExtra(ConsV1.intent_ac_adjust_goodsType, GoodsType.GOODES_TYPE_NO_SUPPORT);
        String bleAddress = intent.getStringExtra(ConsV1.intent_ac_adjust_bleAddress);
        String bleName = intent.getStringExtra(ConsV1.intent_ac_adjust_bleName);
        String versionSoft = intent.getStringExtra(ConsV1.intent_ac_adjust_versionSoft);
        String versionHard = intent.getStringExtra(ConsV1.intent_ac_adjust_versionHard);
        int ic = intent.getIntExtra(ConsV1.intent_ac_adjust_ic, 0);
        if (ic == 0) {
            ic = SkuIcM.getInstance().getDefIcNum(sku);
        }
        int pactType = intent.getIntExtra(ConsV1.intent_ac_adjust_pactType, -1);
        int pactCode = intent.getIntExtra(ConsV1.intent_ac_adjust_pactCode, -1);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "makeInfoFromIntent() pactType = " + pactType + " ; pactCode = " + pactCode);
        }
        String secretCode = intent.getStringExtra(ConsV1.intent_ac_adjust_secret);
        BleInfo bleInfo = new BleInfo(sku, goodsType, device, spec, deviceName, bleName, bleAddress);
        bleInfo.versionSoft = versionSoft;
        bleInfo.versionHard = versionHard;
        bleInfo.ic = ic;
        bleInfo.secretCode = secretCode;
        bleInfo.pactType = pactType;
        bleInfo.pactCode = pactCode;
        return bleInfo;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventRetryConnectBle(EventRetryConnectBle event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventRetryConnectBle()");
        }
        if (!BleController.getInstance().isConnected()) {
            if (frame != null) frame.findDevice();
        }
    }

    @Override
    protected int getScenesEffectVersion() {
        return ScenesM.version_scenes_effect_v1;
    }

    @Override
    protected boolean supportDeviceLock() {
        return Support.supportDeviceLock(info.goodsType, info.sku, info.pactType, info.pactCode);
    }

    @Override
    protected int[] supportScenesOp() {
        if (info == null) return null;
        return Support.getSupportScenesOpSet(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard);
    }
}