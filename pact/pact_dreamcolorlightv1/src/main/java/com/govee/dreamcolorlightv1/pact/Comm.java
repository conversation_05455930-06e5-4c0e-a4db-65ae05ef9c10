package com.govee.dreamcolorlightv1.pact;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.pact.Command4PtReal;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2home.sku.Colors;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.v2.DiyTemplate;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.comm.MultipleBleBytes;
import com.govee.base2light.ble.comm.MultipleControllerCommV1;
import com.govee.base2light.ble.controller.AbsController;
import com.govee.base2light.ble.controller.AbsMultipleControllerV1;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsOnlyWriteSingleController;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.HeartController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultipleColorStripControllerV1;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.util.NumUtil;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeColorV3;
import com.govee.dreamcolorlightv1.ble.SubModeColorV4;
import com.govee.dreamcolorlightv1.ble.SubModeColorV6;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.govee.dreamcolorlightv1.iot.CmdBrightness;
import com.govee.dreamcolorlightv1.iot.CmdBulb;
import com.govee.dreamcolorlightv1.iot.CmdColorWc;
import com.govee.dreamcolorlightv1.iot.CmdPt;
import com.govee.dreamcolorlightv1.iot.CmdPtReal;
import com.govee.dreamcolorlightv1.iot.CmdTurn;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2021/10/22
 * 指令生成类$
 */
public final class Comm {
    private Comm() {
    }

    /**
     * 生成亮度指令-ble
     *
     * @param model
     * @param brightnessPercent
     * @return
     */
    public static AbsSingleController makeBrightnessController4BleComm(DeviceModel model, int brightnessPercent) {
        int brightness = Support.getBrightness(model, brightnessPercent);
        return new BrightnessController(brightness);
    }

    public static AbsSingleController makeBrightnessController4BleComm(AbsDevice device, int brightnessPercent) {
        int brightness = Support.getBrightness(device, brightnessPercent);
        return new BrightnessController(brightness);
    }

    /**
     * 生成亮度指令-iot
     *
     * @param sku
     * @param versionHard
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param brightness
     * @return
     */
    public static AbsCmd makeBrightnessCmd4IotComm(String sku, String versionHard, int goodsType, int pactType, int pactCode, int brightness) {
        boolean bkProtocol = Support.isBkProtocol(sku, goodsType, pactType, pactCode);
        int newBrightness;
        if (bkProtocol) {
            newBrightness = brightness;
        } else {
            int[] brightnessRange = Support.getBrightnessRange(sku, versionHard);
            newBrightness = NumUtil.calculateProgress(brightnessRange[1], brightnessRange[0], brightness);
        }
        return new CmdBrightness(newBrightness);
    }

    /**
     * 生成颜色指令-ble
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param color
     * @return
     */
    public static AbsSingleController makeColorController4BleComm(String sku, int goodsType, int pactType, int pactCode, int color) {
        boolean bkProtocol = Support.isBkProtocol(sku, goodsType, pactType, pactCode);
        ISubMode subMode;
        if (bkProtocol) {
            if (Support.isSubModeColorPiece20(goodsType)) {
                subMode = SubModeColorV6.makeSubModeColor(color);
            } else {
                Support.updateColorLen(goodsType);
                subMode = SubModeColorV2.makeSubModeColor(color);
            }
        } else {
            subMode = SubModeColor.makeSubModeColor(color);
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        return new ModeController(mode);
    }

    /**
     * H612x生成颜色指令-ble
     */
    public static AbsSingleController makeColorController4BleH612x(DeviceModel deviceModel, int color) {
        ISubMode subMode;
        int[] num = Support.get612xSectionNum(deviceModel.getSku(), deviceModel.ic_sub_1, deviceModel.ic_sub_2);
        SubModeColorV2.len = num[0] + num[1];
        subMode = SubModeColorV2.makeSubModeColor(color);
        Mode mode = new Mode();
        mode.subMode = subMode;
        return new ModeController(mode);
    }

    /**
     * 生成颜色指令-ble
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param colors
     * @return
     */
    @Nullable
    public static AbsController[] makeColorStripController4BleComm(String sku, int goodsType, int pactType, int pactCode, Colors colors, String versionSoft, String versionHard, String wifiVersionSoft) {
        boolean supportColorStripMulti = Support.supportColorStripMulti(goodsType, pactType, pactCode);
        if (supportColorStripMulti) {
            MultipleColorStripControllerV1 colorStripControllerV1 = new MultipleColorStripControllerV1(colors);
            List<byte[]> bytes = MultipleControllerCommV1.makeWriteMultipleBytes(colorStripControllerV1);
            return AbsOnlyWriteSingleController.makeControllers(bytes);
        }
        boolean bkProtocol = Support.isBkProtocol(sku, goodsType, pactType, pactCode);
        boolean supportPartBrightness = Support.supportPartBrightness(goodsType, pactType, pactCode, sku, versionSoft, versionHard, wifiVersionSoft) == 2;
        AbsSingleController[] controllers;
        /*不同sku支持段数不一致，SubModeColor生成指令时段数不一致会反空，所以根据段数去匹配是哪个版本*/
        if (bkProtocol || supportPartBrightness) {
            controllers = SubModeColorV2.makeSubModeColor(colors);
        } else {
            controllers = SubModeColor.makeSubModeColor(colors);
        }
        if (controllers == null) {
            controllers = SubModeColorV3.makeSubModeColor(colors);
        }
        if (controllers == null) {
            controllers = SubModeColorV4.makeSubModeColor(colors);
        }
        return controllers;
    }

    /**
     * 生成颜色指令-iot
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param color
     * @return
     */
    public static AbsCmd makeColorCmd4IotComm(String sku, int goodsType, int pactType, int pactCode, int color) {
        boolean bkProtocol = Support.isBkProtocol(sku, goodsType, pactType, pactCode);
        AbsCmd absCmd;
        if (bkProtocol) {
            absCmd = CmdColorWc.makeCmdColorWc4Color(color);
        } else {
            SubModeColor subModeColor = new SubModeColor();
            subModeColor.rgb = color;
            Arrays.fill(subModeColor.ctlLight, true);
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            ModeController modeController = new ModeController(mode);
            absCmd = new CmdBulb(modeController.getValue());
        }
        return absCmd;
    }

    /**
     * 生成颜色指令-iot
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param colors
     * @return
     */
    public static AbsCmd makeColorStripCmd4IotComm(String sku, int goodsType, int pactType, int pactCode, Colors colors, String versionSoft, String versionHard, String wifiVersionSoft) {
        AbsController[] modeControllers = makeColorStripController4BleComm(sku, goodsType, pactType, pactCode, colors, versionSoft, versionHard, wifiVersionSoft);
        if (modeControllers == null) return null;
        List<byte[]> bytes = new ArrayList<>();
        for (AbsController absSingleController : modeControllers) {
            if (absSingleController instanceof AbsMultipleControllerV1) {
                List<byte[]> multipleWriteBytes = MultipleBleBytes.getMultipleWriteBytesV1((AbsMultipleControllerV1) absSingleController);
                bytes.addAll(multipleWriteBytes);
            } else {
                bytes.add(absSingleController.getValue());
            }
        }
        boolean bkProtocol = Support.isBkProtocol(sku, goodsType, pactType, pactCode);
        if (bkProtocol) {
            return new CmdPtReal(bytes);
        } else {
            return new CmdPt(CmdPt.pt_op_mode, bytes);
        }
    }

    /**
     * 生成色温指令-ble
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param temColor
     * @return
     */
    public static AbsSingleController makeColorTemController4BleComm(String sku, int goodsType, int pactType, int pactCode, int temColor) {
        boolean bkProtocol = Support.isBkProtocol(sku, goodsType, pactType, pactCode);
        boolean isColorTemRealDevice = Support.isColorTemRealDevice(goodsType);
        ISubMode subMode;
        if (bkProtocol) {
            if (Support.isSubModeColorPiece20(goodsType)) {
                subMode = SubModeColorV6.makeSubModeColor(temColor);
            } else if (isColorTemRealDevice) {
                SubModeColorV2 subModeColorV2 = new SubModeColorV2();
                int kelvin = com.govee.base2home.Constant.getColorTemKelvin(temColor);
                subModeColorV2.setTemRgbAndKelvin(temColor, kelvin);
                /*色温设置-整条灯带都是同一个色温值*/
                Arrays.fill(subModeColorV2.ctlLight, true);
                subMode = subModeColorV2;
            } else {
                Support.updateColorLen(goodsType);
                subMode = SubModeColorV2.makeSubModeColor(temColor);
            }
        } else {
            subMode = SubModeColor.makeSubModeColor(temColor);
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        return new ModeController(mode);
    }

    /**
     * 生成色温指令-iot
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param temColor
     * @return
     */
    public static AbsCmd makeColorTemCmd4IotComm(String sku, int goodsType, int pactType, int pactCode, int temColor) {
        boolean bkProtocol = Support.isBkProtocol(sku, goodsType, pactType, pactCode);
        AbsCmd absCmd;
        if (bkProtocol) {
            absCmd = CmdColorWc.makeCmdColorWc4ColorTem(temColor);
        } else {
            SubModeColor subModeColor = new SubModeColor();
            subModeColor.rgb = temColor;
            Arrays.fill(subModeColor.ctlLight, true);
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            ModeController modeController = new ModeController(mode);
            absCmd = new CmdBulb(modeController.getValue());
        }
        return absCmd;
    }

    /**
     * 生成心跳指令-ble
     *
     * @return
     */
    public static AbsSingleController makeHeartController4BleComm() {
        return new HeartController();
    }

    /**
     * 生成开关指令-ble
     *
     * @param open
     * @return
     */
    public static AbsSingleController makeSwitchController4BleComm(boolean open) {
        return new SwitchController(open);
    }

    /**
     * 生成开关指令-iot
     *
     * @param open
     * @return
     */
    public static AbsCmd makeSwitchCmd4IotComm(boolean open) {
        return new CmdTurn(open);
    }

    /**
     * 生成diy指令-iot
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param versionHard
     * @param wifiSoftVersion
     * @param diyProtocol
     * @param diyGraffiti4Rgbic
     * @param diyTemplate
     * @param diyStudio
     * @param diyAi
     * @return
     */
    public static AbsCmd makeDiyCmd4IotComm(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, String wifiSoftVersion, DiyProtocol diyProtocol, DiyGraffitiV2 diyGraffiti4Rgbic, DiyTemplate diyTemplate, DiyStudio diyStudio, DiyAi diyAi) {
        boolean bkProtocol = Support.isBkProtocol(sku, goodsType, pactType, pactCode);
        if (diyProtocol != null) {
            return bkProtocol ? CmdPtReal.getDiyCmdPtV1(diyProtocol) : CmdPt.getDiyCmdPt(diyProtocol);
        }
        if (diyGraffiti4Rgbic != null) {
            if (bkProtocol) {
                CmdPtReal cmdPtReal = CmdPtReal.getDiyCmdPt4DiyGraffitiV2(goodsType, diyGraffiti4Rgbic);
                int commandSize = cmdPtReal.getCommandSize();
                if (commandSize > 19) {
                    return null;
                }
                return cmdPtReal;
            } else {
                CmdPt cmdPt = CmdPt.getDiyCmdPt4DiyGraffiti(diyGraffiti4Rgbic);
                int commandPacketNum = cmdPt.getValue().size();
                if (commandPacketNum > 19) {
                    return null;
                }
                return cmdPt;
            }
        }
        if (diyTemplate != null) {
            int diyCode = diyTemplate.diyCode;
            int templateCode = diyTemplate.scenesCode;
            String effectStr = diyTemplate.effectStr;
            AbsMultipleControllerV14DiyTemplate diyTemplateController = ScenesOp.parseDiyTemplate(templateCode, diyCode, effectStr);
            if (diyTemplateController == null) return null;
            return bkProtocol ? CmdPtReal.getDiyTemplateCmdPt(diyTemplateController) : CmdPt.getDiyTemplateCmdPt(diyTemplateController);
        }
        if (diyStudio != null) {
            boolean supportRgbicV1 = Support.isSupportRgbicV1(goodsType, pactType, pactCode, sku, versionSoft, versionHard, wifiSoftVersion);
            AbsMultipleControllerV14DiyTemplate multi4Scenes = diyStudio.toMulti4Scenes4RgbicV1(supportRgbicV1 ? 1 : 0);
            if (multi4Scenes != null) {
                return bkProtocol ? CmdPtReal.getDiyTemplateCmdPt(multi4Scenes) : CmdPt.getDiyTemplateCmdPt(multi4Scenes);
            }
            return null;
        }
        if (diyAi != null) {
            Command4PtReal command4PtReal = diyAi.command4PtReal;
            return bkProtocol ? CmdPtReal.getPtRealCmd(command4PtReal.getCommands4IotPtReal()) : CmdPt.getPtRealCmdPt(command4PtReal.getCommands4IotPtReal());
        }
        return null;
    }

    /**
     * 生成diy蓝牙指令集合-ble
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param versionHard
     * @param diyProtocol
     * @param diyGraffiti4Rgbic
     * @param diyTemplate
     * @param diyStudio
     * @param diyAi
     * @return
     */
    public static List<String> makeDiy4BleComm(String sku, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, DiyProtocol diyProtocol, DiyGraffitiV2 diyGraffiti4Rgbic, DiyTemplate diyTemplate, DiyStudio diyStudio, DiyAi diyAi) {
        AbsCmd cmd = makeDiyCmd4IotComm(sku, goodsType, pactType, pactCode, versionSoft, versionHard, "", diyProtocol, diyGraffiti4Rgbic, diyTemplate, diyStudio, diyAi);
        if (cmd == null) return null;
        if (cmd instanceof CmdPt) {
            return ((CmdPt) cmd).getValue();
        }
        if (cmd instanceof CmdPtReal) {
            return ((CmdPtReal) cmd).getCommand();
        }
        return null;
    }

    /**
     * 生成场景指令-iot
     *
     * @param sku
     * @param device
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param versionHard
     * @param wifiSoftVersion
     * @param wifiHardVersion
     * @param sceneCode
     * @return
     */
    public static AbsCmd makeSceneCmd4IotComm(String sku, String device, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, String wifiSoftVersion, String wifiHardVersion, int sceneCode) {
        Mode mode = new Mode();
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.setEffect(sceneCode);
        mode.subMode = subModeScenes;
        AbsCmd cmd;
        boolean bkProtocol = Support.isBkProtocol(sku, goodsType, pactType, pactCode);
        int version = Support.newVersion(1, sku, goodsType, 1, versionSoft, versionHard, wifiSoftVersion, wifiHardVersion);
        AbsMultipleControllerV14Scenes newMultiScenesModeV1 = Support.is2NewScenesMode(sku, device, mode, version);
        if (bkProtocol) {
            if (newMultiScenesModeV1 != null) {
                cmd = CmdPtReal.getNewScenesCmdPtReal(newMultiScenesModeV1);
            } else {
                ModeController modeController = new ModeController(mode);
                cmd = new CmdPtReal(modeController);
            }
        } else {
            if (newMultiScenesModeV1 != null) {
                cmd = CmdPt.getNewScenesCmdPt(newMultiScenesModeV1);
            } else {
                ModeController modeController = new ModeController(mode);
                cmd = new CmdPt(CmdPt.pt_op_mode, modeController.getValue());
            }
        }
        return cmd;
    }

    /**
     * 生成场景指令-iot
     */
    public static AbsCmd makeSceneCmd4IotComm(String sku, int goodsType, int pactType, int pactCode, CategoryV1.SceneV1 scene) {
        AbsCmd cmd = null;
        boolean bkProtocol = Support.isBkProtocol(sku, goodsType, pactType, pactCode);
        AbsMultipleControllerV14Scenes newMultiScenesModeV1 = ScenesOp.parseSceneV1(sku, scene, ScenesOp.scene_type_rgb, ScenesOp.scene_type_rgbic);
        if (bkProtocol) {
            if (newMultiScenesModeV1 != null) {
                cmd = CmdPtReal.getNewScenesCmdPtReal(newMultiScenesModeV1);
            }
        } else {
            if (newMultiScenesModeV1 != null) {
                cmd = CmdPt.getNewScenesCmdPt(newMultiScenesModeV1);
            }
        }
        return cmd;
    }

    /**
     * 生成场景蓝牙指令集合-ble
     *
     * @param sku
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @param versionSoft
     * @param versionHard
     * @param sceneCode
     * @return
     */
    public static List<String> makeScene4BleComm(String sku, String device, int goodsType, int pactType, int pactCode, String versionSoft, String versionHard, int sceneCode) {
        AbsCmd cmd = makeSceneCmd4IotComm(sku, device, goodsType, pactType, pactCode, versionSoft, versionHard, "", "", sceneCode);
        if (cmd == null) return null;
        if (cmd instanceof CmdPt) {
            return ((CmdPt) cmd).getValue();
        }
        if (cmd instanceof CmdPtReal) {
            return ((CmdPtReal) cmd).getCommand();
        }
        return null;
    }
}