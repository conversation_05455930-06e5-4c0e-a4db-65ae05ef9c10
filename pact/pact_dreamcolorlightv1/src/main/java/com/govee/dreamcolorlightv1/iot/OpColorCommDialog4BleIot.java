package com.govee.dreamcolorlightv1.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.light.AbsOpCommDialog4BleIot;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeColorV6;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by lins<PERSON>ong on 1/28/21
 * op color操作应用$
 */
public class OpColorCommDialog4BleIot extends AbsOpCommDialog4BleIot {
    private final AbsCmd adsCmd;
    private final ModeController modeController;

    protected OpColorCommDialog4BleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isBk, boolean part20) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_COLOR, -1);
        if (part20) {
            SubModeColorV6 subModeColorV6 = SubModeColorV6.makeSubModeColor(colorEffect.colorSet[0]);
            Mode mode = new Mode();
            mode.subMode = subModeColorV6;
            modeController = new ModeController(mode);
            adsCmd = new CmdPtReal(modeController);
            return;
        }
        if (isBk) {
            SubModeColorV2 subModeColorV2 = SubModeColorV2.makeSubModeColor(colorEffect.colorSet[0]);
            Mode mode = new Mode();
            mode.subMode = subModeColorV2;
            modeController = new ModeController(mode);
            adsCmd = new CmdPtReal(modeController);
        } else {
            SubModeColor subModeColor = SubModeColor.makeSubModeColor(colorEffect.colorSet[0]);
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            modeController = new ModeController(mode);
            adsCmd = new CmdBulb(modeController.getValue());
        }
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isBk, boolean part20) {
        new OpColorCommDialog4BleIot(context, bleAddress, bleName, topic, sku, device, colorEffect, isBk, part20).show();
    }

    @Nullable
    @Override
    protected AbsCmd getOpCmd() {
        return adsCmd;
    }

    @Override
    protected void bleOping() {
        getBle().startController(modeController);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateBleResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}