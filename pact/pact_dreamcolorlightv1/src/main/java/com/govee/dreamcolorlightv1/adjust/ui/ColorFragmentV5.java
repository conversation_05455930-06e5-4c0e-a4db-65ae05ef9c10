package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.ui.R;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.v1.AbsColorFragmentV17;

import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV4;
import com.govee.ui.Cons;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.Arrays;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2021/8/4
 * 颜色模式-12段-无渐变开关$
 */
public class ColorFragmentV5 extends AbsColorFragmentV17 {
    private SubModeColorV4 subModeColorV4 = new SubModeColorV4();

    @Override
    protected void init() {
        super.init();
        updateUi();
    }

    @Override
    protected void colorChange(int type, int color, int kevin, boolean[] checks, int brightness) {
        SubModeColorV4 subModeColorV4 = new SubModeColorV4();
        /*设置亮度*/
        if (brightness != 0) {
            subModeColorV4.brightness = brightness;
        }
        /*设置颜色*/
        else if (kevin == 0) {
            subModeColorV4.rgb = color;
        }
        /*设置色温*/
        else {
            subModeColorV4.kelvin = kevin;
            subModeColorV4.ctRgb = color;
            subModeColorV4.rgb = ColorUtils.toWhite();
        }
        subModeColorV4.ctlLight = checks;
        Mode mode = new Mode();
        mode.subMode = subModeColorV4;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.mode_click_color_ + UtilColor.colorName(color));
        /*统计颜色变更来源*/
        AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.getColorFromTypeStr(type));
        AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.color_mode);
        if (type == Cons.color_from_type_wcBar && kevin > 0) {
            /*统计颜色色温k值*/
            AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.temperature_ + kevin);
        }
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeColorV4) {
            ((SubModeColorV4) subMode).checkAnalytic4SubModeUse(getSku());
            subModeColorV4 = (SubModeColorV4) subMode;
            updateUi();
        }
    }

    private void updateUi() {
        if (!isViewInflateOk()) return;
        int opType = subModeColorV4.getOpType();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "updateUi() opType = " + opType);
        }
        if (opType == SubModeColorV4.op_type_fresh_ui) {
            int[] rgbSet = subModeColorV4.rgbSet;
            /*设置全部颜色*/
            if (rgbSet != null && rgbSet.length > 0) {
                updateBulbUI(rgbSet);
            }
            int[] brightnessSet = subModeColorV4.brightnessSet;
            /*设置全部亮度*/
            if (brightnessSet != null && brightnessSet.length > 0) {
                updateBulbBrightnessUI(brightnessSet);
            }
            if (subModeColorV4.getRealRgb() == 0) {
                return;
            }
        }
        if (opType == SubModeColorV4.op_type_piece_op_brightness) {
            int brightness = subModeColorV4.brightness;
            /*设置分段亮度*/
            if (brightness != 0) {
                updateSelectedBulbBrightness(brightness);
            }
            return;
        }
        /*设置分段颜色*/
        int realRgb = subModeColorV4.getRealRgb();
        if (ColorUtils.invalidRgb(realRgb)) {
            updateSelectedBulbColor(ResUtil.getColor(R.color.ui_light_style_1));
        } else {
            updateSelectedBulbColor(realRgb);
        }
        pieceUi(realRgb);
    }

    private void pieceUi(int rgb) {
        /*刷新色块色条位置*/
        boolean colorTem = ColorUtils.isColorTem(rgb);
        if (colorTem) {
            setColorWithTemColor(ColorUtils.toWhite(), rgb);
        } else {
            setColorWithTemColor(rgb, 0);
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    protected boolean supportRealTimeColor() {
        /*支持实时颜色设置*/
        return true;
    }

    @NonNull
    @Override
    protected ISubMode makePartChooseSubMode(boolean[] check, int color) {
        SubModeColorV4 subModeColorV4 = new SubModeColorV4();
        subModeColorV4.rgb = color;
        subModeColorV4.ctlLight = check;
        return subModeColorV4;
    }

    @Override
    public ISubMode getCompleteSubMode() {
        SubModeColor subMode = new SubModeColor();

        int[] rgbArray = Arrays.copyOf(rgb, bulb_num_max);
        for (int i : rgbArray) {
            if (i == 0) {
                return null;
            }
        }
        int[] brightnessArray;
        if (brightness == null) {
            brightnessArray = new int[bulb_num_max];
            for (int i = 0; i < bulb_num_max; i++) {
                brightnessArray[i] = 100;
            }
        } else {
            brightnessArray = Arrays.copyOf(brightness, bulb_num_max);
        }
        subMode.rgbSet = rgbArray;
        subMode.brightnessSet = brightnessArray;
        return subMode;
    }
}
