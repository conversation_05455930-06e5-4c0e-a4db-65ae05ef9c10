package com.govee.dreamcolorlightv1.add;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.govee.base2home.config.Base2homeConfig;
import com.govee.base2home.constant.PathBaseHome;
import com.govee.base2home.device.AbsDeviceNameAcV1;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.OfflineDeviceListConfig;
import com.govee.base2home.main.tab.EventTabDefault;
import com.govee.base2home.sku.SkuIcM;
import com.govee.base2home.util.SchemeUtils;
import com.govee.base2light.kt.comm.DefInfo;
import com.govee.ble.BleController;
import com.govee.dreamcolorlightv1.ConsV1;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.home.account.config.AccountConfig;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JumpUtil;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020-02-12
 * 设备命名页$
 */
public class DeviceNameAc extends AbsDeviceNameAcV1 {
    private AddInfo addInfo;

    /**
     * 跳转到命名页面
     *
     * @param context
     * @param addInfo
     */
    public static void jump2DeviceNameAc(Context context, @NonNull AddInfo addInfo) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ConsV1.intent_ac_key_addInfo, addInfo);
        JumpUtil.jumpWithBundle(context, DeviceNameAc.class, bundle);
    }

    @Override
    protected void initParams(Intent intent) {
        addInfo = intent.getParcelableExtra(ConsV1.intent_ac_key_addInfo);
    }

    @Override
    protected void doSkip() {
        if (BaseApplication.getBaseApplication().isInBackground()) return;/*若应用处于后台，则不进行界面跳转*/
        //断开蓝牙
        Ble.getInstance.stopHeart();
        Ble.getInstance.release();
        BleController.getInstance().toBtClose();
        /*跳转到详情界面*/
        /*通知Tab更改为Default*/
        EventTabDefault.sendEventTabDefault();
        Class<?> mainAcClass = Base2homeConfig.getConfig().getMainAcClass();
        int ic;
        if (addInfo.icNum > 0) {
            ic = addInfo.icNum;
        } else {
            ic = SkuIcM.getInstance().getDefIcNum(addInfo.sku);
        }
        Bundle bundle = new Bundle();
        DefInfo defInfo = new DefInfo();
        defInfo.setGoodsType(addInfo.goodsType);
        defInfo.setSku(addInfo.sku);
        defInfo.setSpec("");
        defInfo.setDevice(addInfo.device);
        defInfo.setDeviceName(addInfo.deviceName);
        defInfo.setBleAddress(addInfo.bleAddress);
        defInfo.setBleName(addInfo.bleName);
        defInfo.setVersionSoft(addInfo.versionSoft);
        defInfo.setVersionHard(addInfo.versionHard);
        defInfo.setIc(ic);
        if (addInfo.secretCode != null){
            defInfo.setSecretCode(addInfo.secretCode);
        }
        defInfo.setPactType(addInfo.pactType);
        defInfo.setPactCode(addInfo.pactCode);
        bundle.putParcelable("intent_ac_key_info", defInfo);
        BaseApplication.getBaseApplication().finishOther(mainAcClass);
        SchemeUtils.jump(this, PathBaseHome.URL_RGBIC_NEW_DETAIL, bundle);
    }

    @Override
    protected void toSaveDeviceName(String deviceName) {
        boolean login = AccountConfig.read().isHadToken();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "toSaveDeviceName() login = " + login);
        }
        if (login) {
            super.toSaveDeviceName(deviceName);
        } else {
            String sku = addInfo.sku;
            String device = addInfo.device;
            AbsDevice devices = OfflineDeviceListConfig.read().getDevices(sku, device);
            if (devices == null) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "sku = " + sku + " ; device = " + device + " ; not found offline device info.");
                }
                return;
            }
            devices.setDeviceName(deviceName);
            /*存储到本地*/
            OfflineDeviceListConfig.read().addOfflineDevice(devices);
            onSaveDeviceNameSuc(deviceName);
        }
    }

    @Override
    protected void onSaveDeviceNameSuc(String newDeviceName) {
        addInfo.deviceName = newDeviceName;
        doSkip();
    }

    @Override
    protected String getDevice() {
        return addInfo.device;
    }

    @Override
    protected String getSku() {
        return addInfo.sku;
    }

    @Override
    protected String getDeviceName() {
        return addInfo.deviceName;
    }
}