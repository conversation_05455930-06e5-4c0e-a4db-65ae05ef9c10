package com.govee.dreamcolorlightv1.ble;

import android.content.Context;

import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultipleDiy;
import com.govee.base2light.ble.controller.MultipleDiyController;
import com.govee.base2light.ble.controller.MultipleDiyControllerV1;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.light.AbsOpCommDialog4BleV2;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/8/12
 * op diy操作应用$
 */
public class OpDiyCommDialog4SquareBle extends AbsOpCommDialog4BleV2 {
    private MultipleDiyControllerV1 multipleDiyControllerV1;
    private MultipleDiyController multipleDiyController;

    protected OpDiyCommDialog4SquareBle(Context context, String bleAddress, String bleName, @NonNull DiyProtocol diyProtocol, boolean isBk, int type) {
        super(context, bleAddress, bleName, EFFECT_TYPE_DIY, type);
        if (isBk) {
            multipleDiyControllerV1 = new MultipleDiyControllerV1(diyProtocol);
        } else {
            multipleDiyController = new MultipleDiyController(diyProtocol);
        }
    }

    protected OpDiyCommDialog4SquareBle(Context context, String bleAddress, String bleName, int type, @NonNull PtRealController ptRealController) {
        super(context, bleAddress, bleName, EFFECT_TYPE_DIY, type);
        this.ptRealController = ptRealController;
    }

    @Override
    protected void bleOping() {
        if (multipleDiyController != null) {
            getBle().sendMultipleController(multipleDiyController);
            return;
        }
        if (multipleDiyControllerV1 != null) {
            getBle().sendMultipleControllerV1(multipleDiyControllerV1);
            return;
        }
        if (ptRealController != null) {
            getBle().sendMultipleController4PtReal(ptRealController);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleDiy(EventMultipleDiy event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiy() result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(event.getDiyCode());
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            getBle().clearControllers();
            updateResult(false);
            hide();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    public static void showDialog(Context context, String bleAddress, String bleName, @NonNull DiyProtocol diyProtocol, boolean isBk, int type) {
        new OpDiyCommDialog4SquareBle(context, bleAddress, bleName, diyProtocol, isBk, type).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, int type, @NonNull PtRealController ptRealController) {
        new OpDiyCommDialog4SquareBle(context, bleAddress, bleName, type, ptRealController).show();
    }
}