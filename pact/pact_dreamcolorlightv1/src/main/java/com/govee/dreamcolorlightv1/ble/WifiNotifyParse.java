package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ac.EventNotifyWifiConnect;
import com.govee.base2light.ble.comm.AbsNotifyParse;
import com.govee.base2light.ble.controller.BleProtocolConstants;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-07-31
 * wifi连接成功的被动通知
 */
public class WifiNotifyParse extends AbsNotifyParse {
    @Override
    protected byte getNotifyType() {
        return BleProtocolConstants.MULTI_WIFI;
    }

    @Override
    protected void parseValue(byte[] value) {
        boolean connectSuc = value[0] == 0;
        EventNotifyWifiConnect.sendEventWifiConnect(connectSuc);
    }
}