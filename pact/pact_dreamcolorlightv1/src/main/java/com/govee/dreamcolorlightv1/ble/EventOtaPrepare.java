package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by x<PERSON><PERSON><PERSON> on 12/1/20
 * event-ota准备$
 */
public class EventOtaPrepare extends AbsControllerEvent {
    protected EventOtaPrepare(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    public static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventOtaPrepare(false, write, commandType, proType));
    }

    public static void sendWriteResult(boolean result, byte commandType, byte proType) {
        EventOtaPrepare event = new EventOtaPrepare(result, true, commandType, proType);
        EventBus.getDefault().post(event);
    }
}