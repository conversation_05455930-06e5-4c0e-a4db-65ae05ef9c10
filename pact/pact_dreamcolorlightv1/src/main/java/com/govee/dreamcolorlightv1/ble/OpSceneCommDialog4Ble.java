package com.govee.dreamcolorlightv1.ble;

import android.content.Context;

import com.govee.base2home.main.AbsDevice;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultiNewScenes;
import com.govee.base2light.light.AbsOpCommDialog4Ble;
import com.govee.dreamcolorlightv1.pact.Comm;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by linshicong on 1/28/21
 * op color操作应用$
 */
public class OpSceneCommDialog4Ble extends AbsOpCommDialog4Ble {
    private final EffectData.ShareEffect shareEffect;

    protected OpSceneCommDialog4Ble(Context context, String bleAddress, String bleName, @NonNull EffectData.ShareEffect shareEffect) {
        super(context, bleAddress, bleName, EFFECT_TYPE_SCENES, -1);
        this.shareEffect = shareEffect;
    }

    protected OpSceneCommDialog4Ble(Context context, String bleAddress, String bleName, boolean on) {
        super(context, bleAddress, bleName, EFFECT_TYPE_SWITCH, -1);
        this.shareEffect = new EffectData.ShareEffect();
        singleController = Comm.makeSwitchController4BleComm(on);
    }

    protected OpSceneCommDialog4Ble(Context context, String bleAddress, String bleName, AbsDevice absDevice, int brightness4Percent) {
        super(context, bleAddress, bleName, EFFECT_TYPE_BRIGHTNESS, -1);
        this.shareEffect = new EffectData.ShareEffect();
        singleController = Comm.makeBrightnessController4BleComm(absDevice, brightness4Percent);
    }

    @Override
    protected void bleOping() {
        if (singleController != null) {
            getBle().startController(singleController);
            return;
        }

        int parseVersion = shareEffect.parseVersion;
        String effectStr = shareEffect.effectStr;
        if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgb) {
            /*rgb场景*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parseShareEffect(ScenesOp.scenes_code_share_rgb, effectStr);
            if (controllerV14Scenes != null) {
                getBle().sendMultipleControllerV1(controllerV14Scenes);
            }
        } else if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v0
                || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v1) {
            /*rgbic场景*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parseShareEffect(ScenesOp.scenes_code_share_rgbic, effectStr);
            if (controllerV14Scenes != null) {
                getBle().sendMultipleControllerV1(controllerV14Scenes);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleNewScenes(EventMultiNewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleNewScenes() result = " + result + " ; scenesCode = " + scenesCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*场景效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*diy效果传输失败，则通知设置新场景模式失败*/
            getBle().clearControllers();
            updateResult(false);
            hide();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    public static void showDialog(Context context, String bleAddress, String bleName, @NonNull EffectData.ShareEffect shareEffect) {
        new OpSceneCommDialog4Ble(context, bleAddress, bleName, shareEffect).show();
    }

    public static void showDialog(Context context, String bleAddress, String bleName, boolean on) {
        new OpSceneCommDialog4Ble(context, bleAddress, bleName, on).show();

    }

    public static void showDialog(Context context, String bleAddress, String bleName, AbsDevice absDevice, int brightness4Percent) {
        new OpSceneCommDialog4Ble(context, bleAddress, bleName, absDevice, brightness4Percent).show();
    }
}