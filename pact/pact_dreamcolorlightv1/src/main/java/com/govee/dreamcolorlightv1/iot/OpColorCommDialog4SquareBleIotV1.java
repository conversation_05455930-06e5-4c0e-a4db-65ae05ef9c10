package com.govee.dreamcolorlightv1.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.sku.Colors;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsMultipleControllerV1;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultipleColor;
import com.govee.base2light.ble.controller.MultipleColorStripControllerV1;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeColorV3;
import com.govee.dreamcolorlightv1.ble.SubModeColorV4;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by linshicong on 1/28/21
 * op color操作应用$
 */
public class OpColorCommDialog4SquareBleIotV1 extends AbsOpCommDialog4BleIotV2 {
    private AbsSingleController[] controllers;
    private AbsCmd absCmd;
    private AbsMultipleControllerV1 multipleControllerV1;

    protected OpColorCommDialog4SquareBleIotV1(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isBk, boolean supportPartBrightnessInBle, boolean supportPartBrightnessInWifi, boolean part10, boolean part12, boolean supportColorStripMulti) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_COLOR, -1);
        if (supportColorStripMulti) {
            MultipleColorStripControllerV1 stripControllerV1 = new MultipleColorStripControllerV1(new Colors(colorEffect.colorSet, colorEffect.brightnessSet, ""));
            multipleControllerV1 = stripControllerV1;
            absCmd = CmdPtReal.makeColorStripCmdPtReal(stripControllerV1, null);
            return;
        }
        if (isBk) {
            if (part10) {
                controllers = SubModeColorV3.makeSubModeColor(colorEffect.colorSet, colorEffect.brightnessSet, colorEffect.isFade(), !colorEffect.noFadeController());
            } else if (part12) {
                controllers = SubModeColorV4.makeSubModeColor(colorEffect.colorSet, colorEffect.brightnessSet, colorEffect.isFade(), !colorEffect.noFadeController());
            } else {
                controllers = SubModeColorV2.makeSubModeColor(colorEffect.colorSet, colorEffect.brightnessSet, colorEffect.isFade(), false, !colorEffect.noFadeController());
            }
            absCmd = new CmdPtReal(controllers);
            return;
        }
        if (supportPartBrightnessInBle) {
            controllers = SubModeColorV2.makeSubModeColor(colorEffect.colorSet, colorEffect.brightnessSet, colorEffect.isFade(), false, !colorEffect.noFadeController());
        } else {
            controllers = SubModeColor.makeSubModeColor(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
        }
        AbsSingleController[] controllers;
        if (supportPartBrightnessInWifi) {
            controllers = SubModeColorV2.makeSubModeColor(colorEffect.colorSet, colorEffect.brightnessSet, colorEffect.isFade(), false, !colorEffect.noFadeController());
        } else {
            controllers = SubModeColor.makeSubModeColor(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
        }
        if (controllers == null) return;
        List<byte[]> bytes = new ArrayList<>();
        for (AbsSingleController absSingleController : controllers) {
            bytes.add(absSingleController.getValue());
        }
        absCmd = new CmdBulb(bytes);
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect, boolean isBk, boolean supportPartBrightnessInBle, boolean supportPartBrightnessInWifi, boolean part10, boolean part12, boolean supportColorStripMulti) {
        new OpColorCommDialog4SquareBleIotV1(context, bleAddress, bleName, topic, sku, device, colorEffect, isBk, supportPartBrightnessInBle, supportPartBrightnessInWifi, part10, part12, supportColorStripMulti).show();
    }

    @Nullable
    @Override
    protected AbsCmd getOpCmd() {
        return absCmd;
    }

    private int size;

    @Override
    protected void bleOping() {
        if (multipleControllerV1 != null) {
            getBle().sendMultipleControllerV1(multipleControllerV1);
            return;
        }
        if (controllers != null) {
            size = controllers.length;
            getBle().startController(controllers);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        size--;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result + "---size:" + size);
        }
        if (!result || size <= 1) {
            updateBleResult(result);
            hide();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleColor(EventMultipleColor event) {
        boolean result = event.isResult();
        updateBleResult(result);
    }


    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}