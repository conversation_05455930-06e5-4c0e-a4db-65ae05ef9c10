package com.govee.dreamcolorlightv1.pact;

import com.govee.ui.R;
import com.govee.base2light.light.IScenes;

import com.govee.dreamcolorlightv1.ble.BleProtocol;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/10/9
 * 场景参数定义V0$
 */
class ScenesV0 implements IScenes {
    private static final int[] defResSet = {
            R.mipmap.new_light_btn_scenes_morning,
            R.mipmap.new_light_btn_scenes_sunset,
            R.mipmap.new_light_btn_scenes_movie,
            R.mipmap.new_light_btn_scenes_date,
            R.mipmap.new_light_btn_scenes_romantic,
            R.mipmap.new_light_btn_scenes_blinking,
            R.mipmap.new_light_btn_scenes_candle,
            R.mipmap.new_light_btn_scenes_vivid,
            R.mipmap.new_light_btn_scenes_breath,
            R.mipmap.new_light_btn_scenes_snow,
            R.mipmap.new_light_btn_scenes_chase,
            R.mipmap.new_light_btn_scenes_four_color,
    };

    private static final int[] selectedResSet = {
            R.mipmap.new_light_btn_scenes_morning_press,
            R.mipmap.new_light_btn_scenes_sunset_press,
            R.mipmap.new_light_btn_scenes_movie_press,
            R.mipmap.new_light_btn_scenes_date_press,
            R.mipmap.new_light_btn_scenes_romantic_press,
            R.mipmap.new_light_btn_scenes_blinking_press,
            R.mipmap.new_light_btn_scenes_candle_press,
            R.mipmap.new_light_btn_scenes_vivid_press,
            R.mipmap.new_light_btn_scenes_breath_press,
            R.mipmap.new_light_btn_scenes_snow_press,
            R.mipmap.new_light_btn_scenes_chase_press,
            R.mipmap.new_light_btn_scenes_four_color_press,
    };

    private static final int[] strSet = {
            R.string.b2light_scenes_gm,
            R.string.b2light_scenes_sunset,
            R.string.b2light_scenes_film,
            R.string.b2light_scenes_date,
            R.string.b2light_scenes_romantic,
            R.string.b2light_scenes_blinking,
            R.string.b2light_scenes_cl,
            R.string.b2light_scenes_energetic,
            R.string.b2light_scenes_breath,
            R.string.b2light_scenes_snow,
            R.string.b2light_scenes_chase,
            R.string.b2light_scenes_stream,
    };

    private static final int[] scenesEffectSet = {
            BleProtocol.value_sub_mode_scenes_gm,
            BleProtocol.value_sub_mode_scenes_sunset,
            BleProtocol.value_sub_mode_scenes_movie,
            BleProtocol.value_sub_mode_scenes_date,
            BleProtocol.value_sub_mode_scenes_romantic,
            BleProtocol.value_sub_mode_scenes_blinking,
            BleProtocol.value_sub_mode_scenes_cl,
            BleProtocol.value_sub_mode_scenes_dynamic,
            BleProtocol.value_sub_mode_scenes_breath,
            BleProtocol.value_sub_mode_scenes_snow,
            BleProtocol.value_sub_mode_scenes_chase,
            BleProtocol.value_sub_mode_scenes_stream,
    };

    private List<Integer> supportEffectArray;

    @Override
    public int[] defResSet() {
        return defResSet;
    }

    @Override
    public int[] selectedResSet() {
        return selectedResSet;
    }

    @Override
    public int[] strSet() {
        return strSet;
    }

    @Override
    public int[] scenesEffectSet() {
        return scenesEffectSet;
    }

    @Override
    public List<Integer> supportEffects() {
        if (supportEffectArray == null) {
            initSupportEffectArray();
        }
        return supportEffectArray;
    }

    @Override
    public LinkedHashMap<Integer, int[][]> getGroup() {
        /*不支持分组*/
        return null;
    }

    private synchronized void initSupportEffectArray() {
        supportEffectArray = new ArrayList<>();
        for (int i : scenesEffectSet) {
            supportEffectArray.add(i);
        }
    }
}
