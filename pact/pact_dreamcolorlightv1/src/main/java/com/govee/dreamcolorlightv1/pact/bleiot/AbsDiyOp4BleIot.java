package com.govee.dreamcolorlightv1.pact.bleiot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.pact.Command4PtReal;
import com.govee.base2home.pact.support.OldDreamColorUtil;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.util.NumberUtil;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.DiyShare;
import com.govee.base2light.ac.diy.IColorOp;
import com.govee.base2light.ac.diy.IDiyOp;
import com.govee.base2light.ac.diy.IMusicFeastOp;
import com.govee.base2light.ac.diy.IRoomOp4Ble;
import com.govee.base2light.ac.diy.IRoomOp4Iot;
import com.govee.base2light.ac.diy.ISceneOp;
import com.govee.base2light.ac.diy.ISmartRoomOp;
import com.govee.base2light.ac.diy.ISwitchAndBrightnessOp;
import com.govee.base2light.ac.diy.local.ShareDiy;
import com.govee.base2light.ac.diy.v1.EffectCodes;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.comm.MultipleControllerCommV1;
import com.govee.base2light.ble.controller.AbsController;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.HeartController;
import com.govee.base2light.ble.controller.MultipleColorStripControllerV1;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.dreamcolorlightv1.adjust.Diy;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.ble.BleComm;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeColorV3;
import com.govee.dreamcolorlightv1.ble.SubModeColorV4;
import com.govee.dreamcolorlightv1.iot.Cmd;
import com.govee.dreamcolorlightv1.iot.CmdBrightness;
import com.govee.dreamcolorlightv1.iot.CmdBulb;
import com.govee.dreamcolorlightv1.iot.CmdColorWc;
import com.govee.dreamcolorlightv1.iot.CmdPtReal;
import com.govee.dreamcolorlightv1.iot.CmdTurn;
import com.govee.dreamcolorlightv1.iot.OpColorCommDialog4BleIot;
import com.govee.dreamcolorlightv1.iot.OpColorCommDialog4BleIotV1;
import com.govee.dreamcolorlightv1.iot.OpColorCommDialog4SquareBleIot;
import com.govee.dreamcolorlightv1.iot.OpColorCommDialog4SquareBleIotV1;
import com.govee.dreamcolorlightv1.iot.OpDiyCommDialog4BleIot;
import com.govee.dreamcolorlightv1.iot.OpDiyCommDialog4BleIotV1;
import com.govee.dreamcolorlightv1.iot.OpDiyCommDialog4BleIotV2;
import com.govee.dreamcolorlightv1.iot.OpDiyCommDialog4SquareBleIot;
import com.govee.dreamcolorlightv1.iot.OpDiyCommDialog4SquareBleIotV1;
import com.govee.dreamcolorlightv1.iot.OpDiyCommDialog4SquareBleIotV2;
import com.govee.dreamcolorlightv1.iot.OpSceneCommDialog4BleIot;
import com.govee.dreamcolorlightv1.iot.OpSceneCommDialog4SquareBleIot;
import com.govee.dreamcolorlightv1.pact.Comm;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JsonUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/8/12
 * diy的op操作$
 */
abstract class AbsDiyOp4BleIot implements IDiyOp, IColorOp, ISceneOp, ISmartRoomOp, IMusicFeastOp, ISwitchAndBrightnessOp {
    private static final String TAG = "AbsDiyOp4BleIot";

    @Override
    public String getKey() {
        StringBuilder sb = new StringBuilder();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            sb.append(goodsType).append("_");
        }
        return sb.toString();
    }

    /**
     * 支持的goodsType
     *
     * @return
     */
    protected abstract int[] supportGoodsType();

    @Override
    public boolean support(@NonNull AbsDevice absDevice, int effectType) {
        /*旧幻彩升级的sku-ble+wifi款*/
        boolean bleWifiRgbic4OldSku = OldDreamColorUtil.checkSupportNewDreamColorFuc4BleWifi(absDevice.getSku(), absDevice.getVersionHard());
        if (bleWifiRgbic4OldSku) return true;
        int curGoodsType = absDevice.getGoodsType();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) {
                Support.updateColorLen(curGoodsType);
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean supportDiyEffect(@NonNull AbsDevice absDevice, int[] effectCodes) {
        int version = Support.getDiyVersion4BleWifi(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        EffectCodes effectCodesCur = Diy.getDiySupport(version).effectCodes;
        if (effectCodesCur != null) {
            return effectCodesCur.supportDiyEffect(effectCodes);
        }
        return false;
    }

    @Override
    public boolean applyDiyEffect(Context context, @NonNull AbsDevice absDevice, @NonNull DiyShare diyShare, boolean needConnect) {
        return applyDiyEffect(context, absDevice, diyShare.effectStr, diyShare.effectCodes, diyShare.type, needConnect);
    }

    private boolean applyDiyEffect(Context context, @NonNull AbsDevice absDevice, String effectStr, int[] effectCodes, int type, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        boolean bkProtocol = Support.isBkProtocol(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyDiyEffect() bkProtocol = " + bkProtocol);
        }
        DiyProtocol diyProtocol = ShareDiy.parseDiyProtocol(effectStr);
        if (diyProtocol != null) {
            if (needConnect) {
                OpDiyCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyProtocol, bkProtocol, type);
            } else {
                OpDiyCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyProtocol, bkProtocol, type);
            }
            return true;
        }
        DiyGraffitiV2 diyGraffiti = ShareDiy.parseDiyGraffiti4Rgbic(effectStr);
        if (diyGraffiti != null) {
            boolean support = diyGraffiti.checkDiyValue4RgbicGraffiti(ext.ic);
            if (!support) return false;
            if (needConnect) {
                OpDiyCommDialog4BleIotV2.showDialog(absDevice.getGoodsType(), context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyGraffiti, type);
            } else {
                OpDiyCommDialog4SquareBleIotV2.showDialog(absDevice.getGoodsType(), context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), diyGraffiti, type);
            }
            return true;
        }
        AbsMultipleControllerV14DiyTemplate shareDiyTemplate = ScenesOp.isShareDiyTemplate(effectCodes, effectStr);
        if (shareDiyTemplate != null) {
            if (needConnect) {
                OpDiyCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareDiyTemplate, bkProtocol, type);
            } else {
                OpDiyCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareDiyTemplate, bkProtocol, type);
            }
            return true;
        }
        /*来自Govee Studio*/
        int diyVersion4BleWifi = Support.getDiyVersion4BleWifi(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyDiyEffect() diyVersion4BleWifi = " + diyVersion4BleWifi);
        }
        int rgbicVersion = diyVersion4BleWifi >= 2 ? 1 : 0;
        AbsMultipleControllerV14DiyTemplate shareDiyStudio = ScenesOp.isShareDiyStudio4Rgbic(rgbicVersion, effectCodes, effectStr);
        if (shareDiyStudio != null) {
            if (needConnect) {
                OpDiyCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareDiyStudio, bkProtocol, type);
            } else {
                OpDiyCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), shareDiyStudio, bkProtocol, type);
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean supportColorEffect(@NonNull AbsDevice absDevice, @NonNull EffectData.ColorEffect colorEffect) {
        int size = colorEffect.colorSet.length;
        if (Support.goodsTypeH612x(absDevice.getGoodsType())) return true;
        if (Support.isSubModeColorPiece10(absDevice.getGoodsType())) return size == 10 || size == 1;
        if (Support.isSubModeColorPiece12(absDevice.getGoodsType())) return size == 12 || size == 1;
        if (Support.isSubModeColorPiece20(absDevice.getGoodsType())) return size == 20 || size == 1;
        if (Support.isGoodsTypeH61A9(absDevice.getGoodsType())) return size == 18 || size == 1;
        return size == 1 || size == 15;
    }

    @Override
    public boolean applyColorEffect(Context context, @NonNull AbsDevice absDevice, @NonNull EffectData.ColorEffect colorEffect, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        boolean bkProtocol = Support.isBkProtocol(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode());
        boolean supportPartBrightnessInBle = Support.supportPartBrightness4BleWifi(absDevice.getGoodsType(), absDevice.getSku(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        boolean supportPartBrightnessInWifi = Support.supportPartBrightness4Wifi(absDevice.getSku(), ext.wifiSoftVersion, absDevice.getVersionHard());
        boolean subModeColorPiece10 = Support.isSubModeColorPiece10(absDevice.getGoodsType());
        boolean subModeColorPiece12 = Support.isSubModeColorPiece12(absDevice.getGoodsType());
        boolean subModeColorPiece20 = Support.isSubModeColorPiece20(absDevice.getGoodsType()) || Support.goodsTypeH612x(absDevice.getGoodsType());
        boolean supportColorStripMulti = Support.supportColorStripMulti(absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyColorEffect() bkProtocol = " + bkProtocol + " ; supportPartBrightnessInBle = " + supportPartBrightnessInBle + " ; supportPartBrightnessInWifi = " + supportPartBrightnessInWifi + " ; subModeColorPiece10 = " + subModeColorPiece10);
        }
        if (needConnect) {
            if (colorEffect.isSingleColor()) {
                OpColorCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, bkProtocol, subModeColorPiece20);
            } else {
                OpColorCommDialog4BleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, bkProtocol, supportPartBrightnessInBle, supportPartBrightnessInWifi, subModeColorPiece10, subModeColorPiece12, supportColorStripMulti);
            }
        } else {
            if (colorEffect.isSingleColor()) {
                OpColorCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, bkProtocol, subModeColorPiece20);
            } else {
                OpColorCommDialog4SquareBleIotV1.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), colorEffect, bkProtocol, supportPartBrightnessInBle, supportPartBrightnessInWifi, subModeColorPiece10, subModeColorPiece12, supportColorStripMulti);
            }
        }
        return true;
    }

    @Override
    public boolean supportSceneEffect(@NonNull AbsDevice absDevice, @NonNull EffectData.ShareEffect shareEffect) {
        int parseVersion = shareEffect.parseVersion;
        if (parseVersion == EffectData.ShareEffect.parser_version_diy) {
            return supportDiyEffect(absDevice, shareEffect.effectCodes);
        }
        if (!shareEffect.isValidEffect()) return false;
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        BleIotExt ext = null;
        if (deviceExt != null) {
            String extSettingJson = deviceExt.getDeviceSettings();
            ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        }
        int version = Support.getDiyVersion(0, absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard(), ext != null ? ext.wifiSoftVersion : "", ext != null ? ext.wifiHardVersion : "");
        if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgb || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v0) {
            return version >= 1;
        } else if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v1) {
            return version >= 2;
        }
        return false;
    }

    @Override
    public boolean applySceneEffect(Context context, @NonNull AbsDevice absDevice, @NonNull EffectData.ShareEffect shareEffect, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        int parseVersion = shareEffect.parseVersion;
        /*diy效果*/
        if (parseVersion == EffectData.ShareEffect.parser_version_diy) {
            return applyDiyEffect(context, absDevice, shareEffect.effectStr, shareEffect.effectCodes, DiyShare.type_recommend, needConnect);
        }
        boolean bkProtocol = Support.isBkProtocol(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode());
        /*rgb抽象场景*/
        if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgb) {
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parseShareEffect(ScenesOp.scenes_code_share_rgb, shareEffect.effectStr);
            if (controllerV14Scenes == null) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "applySceneEffect() rgb 效果解析异常! effectStr = " + shareEffect.effectStr);
                }
                return false;
            }
            if (needConnect) {
                OpSceneCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), controllerV14Scenes, bkProtocol);
            } else {
                OpSceneCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), controllerV14Scenes, bkProtocol);
            }
            return true;
        }
        /*rgbic抽象场景*/
        if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v0 || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v1) {
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parseShareEffect(ScenesOp.scenes_code_share_rgbic, shareEffect.effectStr);
            if (controllerV14Scenes == null) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "applySceneEffect() rgbic 效果解析异常! effectStr = " + shareEffect.effectStr);
                }
                return false;
            }
            if (needConnect) {
                OpSceneCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), controllerV14Scenes, bkProtocol);
            } else {
                OpSceneCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), controllerV14Scenes, bkProtocol);
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean applySwitch(Context context, @NonNull AbsDevice absDevice, boolean on) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        OpSceneCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), on);
        return true;
    }

    @Override
    public boolean applySwitch(Context context, @NonNull AbsDevice absDevice, boolean on, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpSceneCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), on);
        } else {
            OpSceneCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), on);
        }
        return true;
    }

    @Override
    public boolean applyBrightness(Context context, @NonNull AbsDevice absDevice, int brightness4Percent) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        OpSceneCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), absDevice, brightness4Percent);
        return true;
    }

    @Override
    public boolean applyBrightness(Context context, @NonNull AbsDevice absDevice, int brightness4Percent, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpSceneCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), absDevice, brightness4Percent);
        } else {
            OpSceneCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), absDevice, brightness4Percent);
        }
        return true;
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    public EffectCodes getSupportDiyEffectCode(@NonNull AbsDevice absDevice) {
        int version = Support.getDiyVersion4BleWifi(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        return Diy.getDiySupport(version).effectCodes;
    }

    @Override
    public boolean isWifiLight() {
        return true;
    }

    @Override
    public String getWifiOnlineCmd() {
        return Cmd.online;
    }

    @Override
    public int supportRandomColorSize(AbsDevice absDevice) {
        if (Support.isSubModeColorPiece10(absDevice.getGoodsType())) return 10;
        if (Support.isSubModeColorPiece12(absDevice.getGoodsType())) return 12;
        if (Support.isSubModeColorPiece20(absDevice.getGoodsType())) return 20;
        if (Support.isGoodsTypeH61A9(absDevice.getGoodsType())) return 18;
        return 15;
    }

    @Override
    public int[] supportScenesOpSet(AbsDevice absDevice) {
        int goodsType = absDevice.getGoodsType();
        if (goodsType == 0) {
            goodsType = OldDreamColorUtil.parseOldDreamDeviceNewGoodsType(absDevice.getSku());
        }
        goodsType = Math.max(0, goodsType);
        return Support.getSupportScenesOpSet(absDevice.getSku(), goodsType, absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
    }

    @Override
    public boolean applyPtControllers(Context context, @NonNull AbsDevice absDevice, int type, @NonNull Command4PtReal ptReal, boolean needConnect) {
        boolean invalid = ptReal.isInvalid();
        if (invalid) return false;
        boolean supportBleOp = ptReal.supportBleOp();
        boolean supportWifiOp = ptReal.supportWifiOp();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyPtControllers() supportBleOp = " + supportBleOp + " ; supportWifiOp = " + supportWifiOp);
        }
        if (!supportBleOp && !supportWifiOp) return false;
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleIotExt ext = JsonUtil.fromJson(extSettingJson, BleIotExt.class);
        if (ext == null) return false;
        PtRealController ptRealController = PtRealController.makePtRealController(ptReal.opCommands, -1, -1);
        if (ptRealController == null) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "applyPtControllers() 透传指令解析失败");
            }
            return false;
        }
        List<String> commandList = ptReal.getCommands4IotPtReal();
        /*需要区分Wi-Fi透传指令的新旧区分*/
        boolean bkProtocol = Support.isBkProtocol(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyPtControllers() bkProtocol = " + bkProtocol);
        }
        if (needConnect) {
            OpSceneCommDialog4BleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), type, ptRealController, commandList, supportBleOp, supportWifiOp, bkProtocol);
        } else {
            OpSceneCommDialog4SquareBleIot.showDialog(context, ext.address, ext.bleName, ext.topic, absDevice.getSku(), absDevice.getDevice(), type, ptRealController, commandList, supportBleOp, supportWifiOp, bkProtocol);
        }
        return true;
    }

    /*smartRoom*/

    @Override
    public boolean supportBle() {
        return true;
    }

    @Override
    public boolean supportWifi() {
        return true;
    }

    @Override
    public boolean supportDevice(DeviceModel deviceModel) {
        String sku = deviceModel.getSku();
        String versionHard = deviceModel.versionHard;
        int curGoodsType = deviceModel.getGoodsType();
        /*旧幻彩升级的sku-ble+wifi款*/
        boolean bleWifiRgbic4OldSku = OldDreamColorUtil.checkSupportNewDreamColorFuc4BleWifi(sku, versionHard);
        if (bleWifiRgbic4OldSku) return true;
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) return true;
        }
        return false;
    }

    @Override
    public int supportColorSize(DeviceModel model) {
        int goodsType = model.getGoodsType();
        if (Support.goodsTypeH612x(goodsType)) {
            int[] num = Support.get612xSectionNum(model.getSku(), model.ic_sub_1, model.ic_sub_2);
            return num[0] + num[1];
        }
        if (Support.isSubModeColorPiece10(goodsType)) return 10;
        if (Support.isSubModeColorPiece12(goodsType)) return 12;
        if (Support.isSubModeColorPiece20(goodsType)) return 20;
        if (Support.isGoodsTypeH61A9(goodsType)) return 18;
        return 15;
    }

    @Override
    public IRoomOp4Ble getRoomOp() {
        return new IRoomOp4Ble() {
            @Override
            public UUID getServiceUUID() {
                return BleComm.serviceUuid;
            }

            @Override
            public UUID getCharacteristicUUID() {
                return BleComm.characteristicUuid;
            }

            @Override
            public byte[] heartBytes() {
                return new HeartController().getValue();
            }

            @Override
            public byte[] makeSwitchOpBytes(DeviceModel deviceModel, boolean on) {
                return new SwitchController(on).getValue();
            }

            @Override
            public byte[] makeBrightnessOpBytes(DeviceModel deviceModel, int brightnessPercent) {
                return Comm.makeBrightnessController4BleComm(deviceModel, brightnessPercent).getValue();
            }

            @Override
            public List<byte[]> makeSetColorOpBytes(DeviceModel deviceModel, int[] colors) {
                boolean supportColorStripMulti = Support.supportColorStripMulti(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                if (supportColorStripMulti) {
                    MultipleColorStripControllerV1 stripControllerV1 = new MultipleColorStripControllerV1(new Colors(colors));
                    return MultipleControllerCommV1.makeWriteMultipleBytes(stripControllerV1);
                }
                boolean bkProtocol = Support.isBkProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                boolean supportPartBrightnessInBle = Support.supportPartBrightness4BleWifi(deviceModel.getGoodsType(), deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard);
                boolean subModeColorPiece10 = Support.isSubModeColorPiece10(deviceModel.getGoodsType());
                boolean subModeColorPiece12 = Support.isSubModeColorPiece12(deviceModel.getGoodsType());

                List<byte[]> setColorBytes = new ArrayList<>();
                Colors colors1 = new Colors();
                colors1.colorSet = colors;
                AbsSingleController[] controllers;
                if (bkProtocol) {
                    if (subModeColorPiece10) {
                        controllers = SubModeColorV3.makeSubModeColor(colors1);
                    } else if (subModeColorPiece12) {
                        controllers = SubModeColorV4.makeSubModeColor(colors, null, false, false);
                    } else {
                        controllers = SubModeColorV2.makeSubModeColor(colors1);
                    }
                } else {
                    if (supportPartBrightnessInBle) {
                        controllers = SubModeColorV2.makeSubModeColor(colors1);
                    } else {
                        controllers = SubModeColor.makeSubModeColor(colors1);
                    }
                }
                if (controllers != null) {
                    for (AbsSingleController controller : controllers) {
                        byte[] value = controller.getValue();
                        setColorBytes.add(value);
                    }
                }
                return setColorBytes;
            }

            @Override
            public List<byte[]> makeSetColorTemOpBytes(DeviceModel deviceModel, int[] kelvin, int[] temColor) {
                return makeSetColorOpBytes(deviceModel, temColor);
            }


            @Override
            public byte[] makeSetColorOpBytes(DeviceModel deviceModel, int color) {
                if (Support.goodsTypeH612x(deviceModel.getGoodsType())) {
                    return Comm.makeColorController4BleH612x(deviceModel, color).getValue();
                }
                return Comm.makeColorController4BleComm(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, color).getValue();
            }

            @Override
            public byte[] makeSetColorTemOpBytes(DeviceModel deviceModel, int kelvin, int temColor) {
                if (Support.goodsTypeH612x(deviceModel.getGoodsType())) {
                    return Comm.makeColorController4BleH612x(deviceModel, temColor).getValue();
                }
                return Comm.makeColorTemController4BleComm(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, temColor).getValue();
            }

            @Override
            public int supportMic(DeviceModel deviceModel) {
                AbsMicFragmentV4.SupportMicStatus micStatus = Support.getMicStatus(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.getGoodsType());
                if (AbsMicFragmentV4.SupportMicStatus.support_new_order.equals(micStatus))
                    return support_mic_new;
                if (AbsMicFragmentV4.SupportMicStatus.support_color_order.equals(micStatus))
                    return support_mic_rgb;
                return support_mic_no;
            }

            @Override
            public byte[] makeMicBytesByRgb(DeviceModel deviceModel, int rgb) {
                Mode mode = new Mode();
                boolean bkProtocol = Support.isBkProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                if (bkProtocol) {
                    mode.subMode = SubModeColorV2.makeSubModeColor(rgb);
                } else {
                    mode.subMode = SubModeColor.makeSubModeColor(rgb);
                }
                return new ModeController(mode).getValue();
            }
        };
    }

    @Override
    public IRoomOp4Iot getRoomOp4Iot() {
        return new IRoomOp4Iot() {
            @Override
            public AbsCmd makeSwitchCmd(DeviceModel deviceModel, boolean on) {
                return new CmdTurn(on);
            }

            @Override
            public AbsCmd makeBrightnessCmd(DeviceModel deviceModel, int brightnessPercent) {
                int brightness = Support.getBrightness(deviceModel, brightnessPercent);
                return new CmdBrightness(brightness);
            }

            @Override
            public List<AbsCmd> makeColorCmd(DeviceModel deviceModel, int[] colors) {
                if (NumberUtil.isAllSame(colors)) {
                    int color = colors[0];
                    boolean bkProtocol = Support.isBkProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                    AbsCmd absCmd;
                    if (bkProtocol) {
                        absCmd = CmdColorWc.makeCmdColorWc4Color(color);
                    } else {
                        SubModeColor subModeColor = new SubModeColor();
                        subModeColor.rgb = color;
                        Arrays.fill(subModeColor.ctlLight, true);
                        Mode mode = new Mode();
                        mode.subMode = subModeColor;
                        ModeController modeController = new ModeController(mode);
                        absCmd = new CmdBulb(modeController.getValue());
                    }
                    List<AbsCmd> cmds = new ArrayList<>();
                    cmds.add(absCmd);
                    return cmds;
                }
                boolean supportColorStripMulti = Support.supportColorStripMulti(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                if (supportColorStripMulti) {
                    MultipleColorStripControllerV1 stripControllerV1 = new MultipleColorStripControllerV1(new Colors(colors));
                    List<byte[]> bytes = MultipleControllerCommV1.makeWriteMultipleBytes(stripControllerV1);
                    CmdPtReal cmdPtReal = new CmdPtReal(bytes);
                    List<AbsCmd> cmds = new ArrayList<>();
                    cmds.add(cmdPtReal);
                    return cmds;
                }
                boolean bkProtocol = Support.isBkProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                boolean supportPartBrightnessInWifi = Support.supportPartBrightness4Wifi(deviceModel.getSku(), deviceModel.wifiSoftVersion, deviceModel.versionHard);
                boolean subModeColorPiece10 = Support.isSubModeColorPiece10(deviceModel.getGoodsType());
                boolean subModeColorPiece12 = Support.isSubModeColorPiece12(deviceModel.getGoodsType());
                AbsController[] controllers;
                if (bkProtocol) {
                    if (subModeColorPiece10) {
                        controllers = SubModeColorV3.makeSubModeColor(colors, null, false, false);
                    } else if (subModeColorPiece12) {
                        controllers = SubModeColorV4.makeSubModeColor(colors, null, false, false);
                    } else {
                        controllers = SubModeColorV2.makeSubModeColor(colors, null, false, false, false);
                    }
                    if (controllers == null) return null;
                    AbsCmd absCmd = new CmdPtReal(controllers);
                    List<AbsCmd> cmds = new ArrayList<>();
                    cmds.add(absCmd);
                    return cmds;
                } else {
                    if (NumberUtil.parseVersion(deviceModel.wifiSoftVersion) < NumberUtil.parseVersion("1.03.05")) {
                        //补充详情页支持bulb指令
                        if (supportCmdBulb(deviceModel)) {
                            int subModeColorVersion = Support.supportPartBrightness(deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, deviceModel.getSku(), deviceModel.versionSoft, deviceModel.versionHard, deviceModel.wifiSoftVersion);
                            if (subModeColorVersion == 2) {
                                AbsSingleController[] modeControllers = SubModeColorV2.makeSubModeColor(colors, false, false);
                                if (modeControllers != null) {
                                    List<byte[]> bytes = new ArrayList<>();
                                    for (AbsSingleController absSingleController : modeControllers) {
                                        bytes.add(absSingleController.getValue());
                                    }
                                    CmdBulb cmdBulb = new CmdBulb(bytes);
                                    List<AbsCmd> cmds = new ArrayList<>();
                                    cmds.add(cmdBulb);
                                    return cmds;
                                }
                            }
                        }
                        return null;
                    }
                }
                if (supportPartBrightnessInWifi) {
                    controllers = SubModeColorV2.makeSubModeColor(colors, null, false, false, false);
                } else {
                    controllers = SubModeColor.makeSubModeColor(colors, false, false);
                }
                if (controllers == null) return null;
                List<byte[]> bytes = new ArrayList<>();
                for (AbsController absSingleController : controllers) {
                    bytes.add(absSingleController.getValue());
                }
                AbsCmd absCmd = new CmdBulb(bytes);
                List<AbsCmd> cmds = new ArrayList<>();
                cmds.add(absCmd);
                return cmds;
            }

            @Override
            public List<AbsCmd> makeColorTemCmd(DeviceModel deviceModel, int[] kelvin, int[] temColors) {
                if (NumberUtil.isAllSame(temColors)) {
                    int color = temColors[0];
                    boolean bkProtocol = Support.isBkProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                    AbsCmd absCmd;
                    if (bkProtocol) {
                        absCmd = CmdColorWc.makeCmdColorWc4ColorTem(color);
                    } else {
                        SubModeColor subModeColor = new SubModeColor();
                        subModeColor.rgb = color;
                        Arrays.fill(subModeColor.ctlLight, true);
                        Mode mode = new Mode();
                        mode.subMode = subModeColor;
                        ModeController modeController = new ModeController(mode);
                        absCmd = new CmdBulb(modeController.getValue());
                    }
                    List<AbsCmd> cmds = new ArrayList<>();
                    cmds.add(absCmd);
                    return cmds;
                }
                return makeColorCmd(deviceModel, temColors);
            }

            @Override
            public String getOnlineCmd() {
                return Cmd.online;
            }

            @Override
            public int onlineCmdVersion(DeviceModel deviceModel) {
                return 0;
            }
        };
    }

    private boolean supportCmdBulb(DeviceModel deviceModel) {
        return Support.OldDreamColorBleWifiSkuArray.contains(deviceModel.getSku()) && Support.uiV14bleIotProtocol(deviceModel.pactType, deviceModel.pactCode);
    }

    /*smartRoom*/
}