package com.govee.dreamcolorlightv1.adjust.v2;

import android.text.TextUtils;

import com.govee.base2home.Constant;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.device.IDeviceNet;
import com.govee.base2home.device.net.DeviceIcRequest;
import com.govee.base2home.device.net.DeviceSubIcRequest;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.scenes.SecretKeyConfig;
import com.govee.base2home.sku.Colors;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.diy.EventDiyApplyResult;
import com.govee.base2light.ac.music.EventApplyAbsMusic;
import com.govee.base2light.ac.timer.SleepSucEvent;
import com.govee.base2light.ac.timer.Timer;
import com.govee.base2light.ac.timer.TimerResultEvent;
import com.govee.base2light.ac.timer.WakeupSucEvent;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ControllerNoEvent;
import com.govee.base2light.ble.controller.DspVersionInDeviceInfoController;
import com.govee.base2light.ble.controller.EventBrightness;
import com.govee.base2light.ble.controller.EventDiyTemplate4NewScenes;
import com.govee.base2light.ble.controller.EventDspVersion;
import com.govee.base2light.ble.controller.EventHardVersion;
import com.govee.base2light.ble.controller.EventIcNum;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultiNewDiyGraffiti;
import com.govee.base2light.ble.controller.EventMultiNewScenes;
import com.govee.base2light.ble.controller.EventMultipleColor;
import com.govee.base2light.ble.controller.EventMultipleDiy;
import com.govee.base2light.ble.controller.EventMultipleMusic;
import com.govee.base2light.ble.controller.EventNewTimeV1;
import com.govee.base2light.ble.controller.EventOnOffMemory;
import com.govee.base2light.ble.controller.EventSleep;
import com.govee.base2light.ble.controller.EventSoftVersion;
import com.govee.base2light.ble.controller.EventSyncTime;
import com.govee.base2light.ble.controller.EventWakeUp;
import com.govee.base2light.ble.controller.EventWifiHardVersion;
import com.govee.base2light.ble.controller.EventWifiMac;
import com.govee.base2light.ble.controller.EventWifiSoftVersion;
import com.govee.base2light.ble.controller.HardVersionController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.IcNumController;
import com.govee.base2light.ble.controller.NewTimerV1;
import com.govee.base2light.ble.controller.NewTimerV1Controller;
import com.govee.base2light.ble.controller.OnOffMemoryController;
import com.govee.base2light.ble.controller.SecretKeyController;
import com.govee.base2light.ble.controller.SoftVersionController;
import com.govee.base2light.ble.controller.SubModeAbsMusic;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.controller.SyncTimeController;
import com.govee.base2light.ble.controller.SyncTimeInfo;
import com.govee.base2light.ble.controller.WifiHardVersionController;
import com.govee.base2light.ble.controller.WifiMacController;
import com.govee.base2light.ble.controller.WifiSoftVersionController;
import com.govee.base2light.ble.music.EventNewMusicOpResult;
import com.govee.base2light.ble.ota.v2.OtaFlagV2;
import com.govee.base2light.light.EventCheckICResult;
import com.govee.base2light.light.EventLightStripResult;
import com.govee.base2light.lowBlueLightControl.EventLowBlueLightResult;
import com.govee.base2light.lowBlueLightControl.LowBlueLightConfig;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.ble.AbsBleOp;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.ble.BleIotInfoV1;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.BulbGroupColor;
import com.govee.dreamcolorlightv1.ble.BulbGroupColorV2;
import com.govee.dreamcolorlightv1.ble.BulbGroupColorV3;
import com.govee.dreamcolorlightv1.ble.BulbStringColorController;
import com.govee.dreamcolorlightv1.ble.BulbStringColorControllerV2;
import com.govee.dreamcolorlightv1.ble.BulbStringColorControllerV3;
import com.govee.dreamcolorlightv1.ble.EventBulbStringColor;
import com.govee.dreamcolorlightv1.ble.EventBulbStringColorV2;
import com.govee.dreamcolorlightv1.ble.EventBulbStringColorV3;
import com.govee.dreamcolorlightv1.ble.EventCheckIc;
import com.govee.dreamcolorlightv1.ble.EventGradual;
import com.govee.dreamcolorlightv1.ble.EventOtaPrepare;
import com.govee.dreamcolorlightv1.ble.EventReadIc;
import com.govee.dreamcolorlightv1.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.Mode4ColorStrip;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.ParamsSubMode4MusicV1;
import com.govee.dreamcolorlightv1.ble.ReadIcController;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeColorV3;
import com.govee.dreamcolorlightv1.ble.SubModeColorV4;
import com.govee.dreamcolorlightv1.ble.SubModeColorV6;
import com.govee.dreamcolorlightv1.ble.SubModeColorV8;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV3;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.kt.setting.event.Event4PowerOfMemoryOpResult;
import com.ihoment.base2app.Cache;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;
import com.ihoment.base2app.network.Network;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2020-02-12
 * ble-op控制v3版本$
 */
public class BleOpV3 extends AbsBleOp {
    private int diyCode = -1;
    private int diyTemplateCode = -1;
    private final ExtV3 ext;

    public BleOpV3(BleIotInfo info, ExtV3 ext) {
        super(info);
        this.ext = ext;
        if (Support.isGoodsTypeH61A9(info.goodsType)) {
            lastRgbSet4ColorStrip = new int[18];
        }
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected String getTag() {
        return "BleOpV3";
    }

    /**
     * 当前操作是否由其他模式切换到颜色模式
     *
     * @param lastMode - 上次的模式
     * @param opMode   - 当前操作的模式
     * @return
     */
    private boolean otherMode2Color(@Nullable AbsMode lastMode, @NonNull AbsMode opMode) {
        byte subModeCommandType = opMode.subMode.subModeCommandType();
        boolean mode2Color = subModeCommandType == BleProtocol.sub_mode_color || subModeCommandType == BleProtocol.sub_mode_color_v2;
        if (mode2Color && lastMode != null) {
            ISubMode subMode = lastMode.subMode;
            if (subMode != null) {
                byte lastSubModeCommandType = subMode.subModeCommandType();
                boolean lastSubModeIsColorMode = lastSubModeCommandType == BleProtocol.sub_mode_color || lastSubModeCommandType == BleProtocol.sub_mode_color_v2;
                return !lastSubModeIsColorMode;
            }
        }
        return mode2Color;
    }

    @Override
    protected void onOffChangeReadingInfo() {
        SyncTimeInfo info = SyncTimeInfo.getCurrentTime();
        boolean noSupportGradual = Support.noSupportGradual(this.info.goodsType);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onOffChangeReadingInfo() noSupportGradual = " + noSupportGradual);
        }
        AbsSingleController[] controllers;
        if (noSupportGradual) {
            controllers = new AbsSingleController[]{new SoftVersionController(), new HardVersionController(), new SyncTimeController(info.hour, info.minute, info.second, info.week), new NewTimerV1Controller(0xFF), Support.makeWakeUpController4Read(this.info.goodsType, this.info.sku, this.info.pactType, this.info.pactCode), Support.makeSleepController4Read(this.info.goodsType, this.info.sku, this.info.pactType, this.info.pactCode), new BrightnessController(), new SwitchController(), new ModeController(),};
        } else {
            controllers = new AbsSingleController[]{
                    new SoftVersionController(),
                    new HardVersionController(),
                    new SyncTimeController(info.hour, info.minute, info.second, info.week),
                    new NewTimerV1Controller(0xFF),
                    Support.makeWakeUpController4Read(this.info.goodsType, this.info.sku, this.info.pactType, this.info.pactCode),
                    Support.makeSleepController4Read(this.info.goodsType, this.info.sku, this.info.pactType, this.info.pactCode),
                    new BrightnessController(),
                    new SwitchController(),
                    new Gradual4BleWifiController(),
                    new ModeController(),};
        }
        boolean supportBindWithIcNum = Support.supportBindWithIcNum(this.info.goodsType, this.info.pactType, this.info.pactCode, this.info.sku);
        LogInfra.Log.i(TAG, "onOffChangeReadingInfo() supportBindWithIcNum = " + supportBindWithIcNum);
        if (supportBindWithIcNum) {
            AbsSingleController[] newControllers = new AbsSingleController[controllers.length + 1];
            System.arraycopy(controllers, 0, newControllers, 1, controllers.length);
            newControllers[0] = new IcNumController();
            controllers = newControllers;
        }
        boolean supportIcFresh = Support.supportIcFresh(this.info.sku, this.info.goodsType, this.info.pactType, this.info.pactCode);
        LogInfra.Log.i(TAG, "onOffChangeReadingInfo() supportIcFresh = " + supportIcFresh);
        if (supportIcFresh) {
            AbsSingleController[] newControllers = new AbsSingleController[controllers.length + 1];
            newControllers[0] = new ReadIcController();
            System.arraycopy(controllers, 0, newControllers, 1, controllers.length);
            controllers = newControllers;
        }
        boolean supportPowerOffMemory = Support.supportPowerOffMemory(this.info.goodsType, this.info.sku, this.info.pactType, this.info.pactCode);
        if (supportPowerOffMemory) {
            AbsSingleController[] newControllers = new AbsSingleController[controllers.length + 1];
            newControllers[0] = new OnOffMemoryController(0, false);
            System.arraycopy(controllers, 0, newControllers, 1, controllers.length);
            controllers = newControllers;
        }
        boolean goodsTypeH61A9 = Support.isGoodsTypeH61A9(this.info.goodsType);
        if (goodsTypeH61A9) {
            AbsSingleController[] newControllers = new AbsSingleController[controllers.length + 1];
            newControllers[0] = new DspVersionInDeviceInfoController();
            System.arraycopy(controllers, 0, newControllers, 1, controllers.length);
            controllers = newControllers;
        }
        getBle().startController(controllers);
    }

    @Nullable
    @Override
    protected AbsSingleController getReadModeController() {
        return new ModeController();
    }

    @Override
    protected AbsSingleController getController4DeviceSecret() {
        boolean supportDeviceSecret = Support.isSupportDeviceSecret(this.info.goodsType);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getController4DeviceSecret() supportDeviceSecret = " + supportDeviceSecret);
        }
        if (!supportDeviceSecret) return null;
        String secretCode = this.info.secretCode;
        if (TextUtils.isEmpty(secretCode)) {
            secretCode = SecretKeyConfig.read().getSecretKey(this.info.bleAddress);
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getController4DeviceSecret() secretCode = " + secretCode);
        }
        if (TextUtils.isEmpty(secretCode)) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "getController4DeviceSecret() secretCode未获取到 bleAddress = " + info.bleAddress);
            }
            return null;
        }
        return new SecretKeyController(secretCode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWifiSoftVersion(EventWifiSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWifiSoftVersion() softVersion = " + softVersion);
            }
            if (!TextUtils.isEmpty(softVersion)) {
                ext.wifiSoftVersion = softVersion;
            }
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWifiHardVersion(EventWifiHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWifiHardVersion() hardVersion = " + hardVersion);
            }
            if (!TextUtils.isEmpty(hardVersion)) {
                /*判断是否需要上报wifi版本信息*/
                boolean needReportWifiVersion = TextUtils.isEmpty(ext.wifiSoftVersion) || TextUtils.isEmpty(ext.wifiHardVersion);
                ext.wifiHardVersion = hardVersion;
                if (needReportWifiVersion) {
                    String curWifiSoftVersion = ext.wifiSoftVersion;
                    String curWifiHardVersion = ext.wifiHardVersion;
                    reportWifiVersion(curWifiSoftVersion, curWifiHardVersion);
                }
            }
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWifiMac(EventWifiMac event) {
        if (event.isResult()) {
            String mac = event.getMac();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWifiMac()");
            }
            if (!TextUtils.isEmpty(mac)) {
                /*判断是否需要上报wifiMac*/
                boolean needReportWifiMac = TextUtils.isEmpty(info.wifiMac);
                info.wifiMac = mac;
                if (needReportWifiMac) {
                    reportWifiMac(mac);
                }
            }
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSoftVersion(EventSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventSoftVersion() softVersion = " + softVersion);
            }
            info.versionSoft = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventHardVersion(EventHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventHardVersion() hardVersion = " + hardVersion);
            }
            info.versionHard = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSyncTime(EventSyncTime event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSyncTime()");
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBrightness(EventBrightness event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBrightness() write = " + write + " ; result = " + result);
        }
        if (result) {
            int brightness = event.getBrightness();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventBrightness() brightness = " + brightness);
            }
            ext.brightness = brightness;
        }
        if (write) {
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.effect_apply_all_times);
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.adjust_effect_apply_time);
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWakeUp(EventWakeUp event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventWakeUp()  write = " + write + " ; result = " + result);
        }
        if (result) {
            WakeUpInfo wakeUpInfo = new WakeUpInfo();
            wakeUpInfo.enable = event.getEnable();
            wakeUpInfo.endBri = event.getEndBri();
            wakeUpInfo.wakeHour = event.getWakeHour();
            wakeUpInfo.wakeMin = event.getWakeMin();
            wakeUpInfo.wakeTime = event.getWakeTime();
            wakeUpInfo.repeat = event.getRepeat();
            if (Support.supportTimerWithColorConfig(info.goodsType, info.sku, info.pactType, info.pactCode)) {
                wakeUpInfo.rgb = event.getRgb();
                wakeUpInfo.defaultLight = event.getDefaultLight();
                wakeUpInfo.openVoice = event.isOpenVoice();
                wakeUpInfo.voiceVolume = event.getVoiceVolume();
                wakeUpInfo.voiceCode = event.getVoiceCode();
            }
            wakeUpInfo.check();
            ext.wakeUpInfo = wakeUpInfo;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWakeUp() ext.wakeUpInfo = " + ext.wakeUpInfo);
            }
            WakeupSucEvent.sendWakeUpSucEvent(write, wakeUpInfo);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSleep(EventSleep event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSleep()  write = " + write + " ; result = " + result);
        }
        if (result) {
            SleepInfo sleepInfo = new SleepInfo();
            sleepInfo.enable = event.getEnable();
            sleepInfo.startBri = event.getStartBri();
            sleepInfo.closeTime = event.getCloseTime();
            sleepInfo.curTime = event.getCurTime();
            if (Support.supportTimerWithColorConfig(info.goodsType, info.sku, info.pactType, info.pactCode)) {
                sleepInfo.rgb = event.getRgb();
                sleepInfo.defaultLight = event.getDefaultLight();
                sleepInfo.voiceCode = event.getVoiceCode();
                sleepInfo.voiceVolume = event.getVoiceVolume();
                sleepInfo.openVoice = event.isOpenVoice();
            }
            sleepInfo.check();
            ext.sleepInfo = sleepInfo;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventSleep() ext.sleepInfo = " + ext.sleepInfo);
            }
            SleepSucEvent.sendSleepSucEvent(write, sleepInfo);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventNewTimerV1(EventNewTimeV1 event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNewTimerV1()  write = " + write + " ; result = " + result);
        }
        if (result) {
            int group = event.getGroup();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventNewTimerV1() group = " + group);
            }
            List<Timer> timers = event.getTimers();
            if (group == 0xFF && timers.size() >= 4) {
                ext.timer1 = timers.get(0);
                ext.timer2 = timers.get(1);
                ext.timer3 = timers.get(2);
                ext.timer4 = timers.get(3);
            } else if (timers.size() > 0) {
                Timer timer = timers.get(0);
                if (group == 0) {
                    ext.timer1 = timer;
                } else if (group == 1) {
                    ext.timer2 = timer;
                } else if (group == 2) {
                    ext.timer3 = timer;
                } else if (group == 3) {
                    ext.timer4 = timer;
                }
            }
            TimerResultEvent.sendTimerResultEvent(write, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
        } else if (write) {
            TimerResultEvent.sendTimerResultEventFail(true);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventGradual(EventGradual event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventGradual() result = " + result + " ; write = " + write);
        }
        if (result) {
            int value = event.getValue();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventGradual() value = " + value);
            }
            ext.gradual = value;
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onIcNumEvent(EventIcNum event) {
        if (event.isResult()) {
            int icNum = event.icNum;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onIcNumEvent() icNum = " + icNum + " ; info.ic = " + info.ic);
            }
            if (icNum > 0 && icNum != info.ic) {
                info.ic = icNum;
                /*上报ic数*/
                reportIc(icNum);
            }
        }
        getBle().controllerEvent(event);
    }

    private void reportIc(int ic) {
        LogInfra.Log.i(TAG, "reportIc() ic = " + ic);
        /*上报ic信息*/
        DeviceIcRequest request = new DeviceIcRequest(String.valueOf(System.currentTimeMillis()), info.sku, info.device, ic);
        Cache.get(IDeviceNet.class).updateDeviceIc(request).enqueue(new Network.IHCallBack<>(request));
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventReadIc(EventReadIc event) {
        boolean result = event.isResult();
        LogInfra.Log.i(TAG, "onEventReadIc() result = " + result);
        int ic1 = event.ic;
        int ic2 = event.ic2;
        SafeLog.Companion.i(TAG, () -> "onEventReadIc() ic1 = " + ic1 + " ic2 = " + ic2);
        if (result) {
            if (Support.goodsTypeH612x(info.goodsType)) {
                boolean icChange = info.ic_sub_1 != ic1 || info.ic_sub_2 != ic2;
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventIcAndPiece() icChange = " + icChange);
                }
                info.ic_sub_1 = ic1;
                info.ic_sub_2 = ic2;
                if (info instanceof BleIotInfoV1) {
                    ((BleIotInfoV1) info).sectionNum1 = event.sectionNum1;
                    ((BleIotInfoV1) info).sectionNum2 = event.sectionNum2;
                }
                if (icChange) {
                    reportSubIc(ic1, ic2);
                    if (info.mode != null && info.mode.subMode instanceof SubModeColorV6) {
                        readPartColor();
                    }
                }
            } else {
                int ic = event.ic;
                LogInfra.Log.i(TAG, "onEventReadIc() ic = " + ic + " ; info.ic = " + info.ic);
                boolean icChange = ic != info.ic && ic > 0;
                if (icChange) {
                    info.ic = ic;
                    reportIc(ic);
                }
            }

        }
        getBle().controllerEvent(event);
    }

    private void reportSubIc(int ic1, int ic2) {
        DeviceSubIcRequest request = new DeviceSubIcRequest(String.valueOf(System.currentTimeMillis()), info.sku, info.device, ic1, ic2);
        Cache.get(IDeviceNet.class).updateDeviceSubIc(request).enqueue(new Network.IHCallBack<>(request));
    }

    protected void readPartColor() {
        if (Support.supportRgbWithBrightnessOnePkg4Colors(info.goodsType, info.pactType, info.pactCode)) {
            readBulbStringColorV3();
            return;
        }
        if (Support.supportPartBrightness4BleWifi(info.goodsType, info.sku, info.versionSoft, info.versionHard)) {
            readBulbStringColorV2();
        } else {
            readBulbStringColor();
        }
    }

    /*读颜色带亮度*/
    private void readBulbStringColorV2() {
        int groupColorCount = 3;
        if (Support.isH61B5(info.sku) || Support.isGoodsTypeH61A9(info.goodsType) || info.goodsType == GoodsType.GOODS_TYPE_VALUE_H616C)
            groupColorCount = 4;
        int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
        int maxGroup = bulbStringMaxNum / groupColorCount + (bulbStringMaxNum % groupColorCount == 0 ? 0 : 1);
        AbsSingleController[] controllers = new AbsSingleController[maxGroup];
        for (int i = 0; i < maxGroup; i++) {
            controllers[i] = new BulbStringColorControllerV2(i + 1, groupColorCount);
        }
        getBle().addController(controllers);
    }

    /**
     * 每一条协议颜色值4组
     */
    private void readBulbStringColorV3() {
        int onePkgColorLen = 4;
        boolean is612x = Support.goodsTypeH612x(info.goodsType) && info instanceof BleIotInfoV1;
        int bulbStringMaxNum = is612x ? ((BleIotInfoV1) info).sectionNum1 + ((BleIotInfoV1) info).sectionNum2 : Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
        int maxGroup = bulbStringMaxNum / onePkgColorLen + (bulbStringMaxNum % onePkgColorLen == 0 ? 0 : 1);
        AbsSingleController[] controllers = new AbsSingleController[maxGroup];
        for (int i = 0; i < maxGroup; i++) {
            controllers[i] = new BulbStringColorControllerV3(i + 1);
        }
        getBle().addController(controllers);
    }

    private int[] lastBrightnessSet;

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBulbStringColorV3(EventBulbStringColorV3 event) {
        boolean result = event.isResult();
        if (result) {
            BulbGroupColorV3 groupColor = event.groupColor;
            boolean is612x = Support.goodsTypeH612x(info.goodsType) && info instanceof BleIotInfoV1;
            int bulbStringMaxNum = is612x ? ((BleIotInfoV1) info).sectionNum1 + ((BleIotInfoV1) info).sectionNum2 : Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
            int controllerSize = groupColor.controllerSize(bulbStringMaxNum);
            LogInfra.Log.i(TAG, "onEventBulbStringColorV3() bulbStringMaxNum = " + bulbStringMaxNum + " ; controllerSize = " + controllerSize);
            int group = groupColor.group;
            if (group == 1) {
                lastRgbSet = new int[controllerSize * BulbGroupColorV3.rgbSetSize];
                lastBrightnessSet = new int[controllerSize * BulbGroupColorV3.rgbSetSize];
            }
            int[] rgb = groupColor.rgb;
            int[] brightness = groupColor.relativeBrightness;
            int destPos = Math.max(group - 1, 0) * BulbGroupColorV3.rgbSetSize;
            System.arraycopy(rgb, 0, lastRgbSet, destPos, rgb.length);
            System.arraycopy(brightness, 0, lastBrightnessSet, destPos, brightness.length);
            if (group == controllerSize) {
                boolean subModeColorPiece10 = Support.isSubModeColorPiece10(info.goodsType);
                boolean subModeColorPiece20 = Support.isSubModeColorPiece20(info.goodsType);
                boolean subModeColorPiece12 = Support.isSubModeColorPiece12(info.goodsType);
                boolean h612de = Support.supportChangePieces20(info.goodsType, info.sku);
                boolean h612abcf = Support.goodsTypeH612x(info.goodsType) && !h612de;
                SafeLog.i(TAG, () -> "onEventBulbStringColorV3() subModeColorPiece20  = " + subModeColorPiece20);
                ISubMode subMode;
                if (subModeColorPiece20 || h612de) {
                    SubModeColorV6 subModeColorV6 = new SubModeColorV6();
                    subModeColorV6.rgbSet = lastRgbSet;
                    subModeColorV6.brightnessSet = lastBrightnessSet;
                    subMode = subModeColorV6;
                } else if (subModeColorPiece10) {
                    SubModeColorV3 subModeColor = new SubModeColorV3();
                    subModeColor.rgbSet = lastRgbSet;
                    subModeColor.brightnessSet = lastBrightnessSet;
                    subMode = subModeColor;
                } else if (subModeColorPiece12) {
                    SubModeColorV4 subModeColor = new SubModeColorV4();
                    subModeColor.rgbSet = lastRgbSet;
                    subModeColor.brightnessSet = lastBrightnessSet;
                    subMode = subModeColor;
                } else if (h612abcf) {
                    SubModeColorV8 subModeColor = new SubModeColorV8();
                    subModeColor.setRgbSet(lastRgbSet);
                    subModeColor.setBrightnessSet(lastBrightnessSet);
                    subMode = subModeColor;
                } else {
                    SubModeColorV2 subModeColor = new SubModeColorV2();
                    subModeColor.rgbSet = lastRgbSet;
                    subModeColor.brightnessSet = lastBrightnessSet;
                    subMode = subModeColor;
                }
                Mode mode = new Mode();
                mode.subMode = subMode;
                info.mode = mode;
                /*信息读取完成*/
                infoOver();
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBulbStringColorV2(EventBulbStringColorV2 event) {
        boolean result = event.isResult();
        if (result) {
            BulbGroupColorV2 groupColor = event.groupColor;
            int group = groupColor.group;
            int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
            int groupColorCount = 3;
            if (Support.isH61B5(info.sku) || Support.isGoodsTypeH61A9(info.goodsType) || info.goodsType == GoodsType.GOODS_TYPE_VALUE_H616C)
                groupColorCount = 4;
            int maxGroup = bulbStringMaxNum / groupColorCount + (bulbStringMaxNum % groupColorCount == 0 ? 0 : 1);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventBulbStringColor() bulbStringMaxNum = " + bulbStringMaxNum + " ; maxGroup = " + maxGroup + " ； group = " + group);
            }
            if (group == 1) {
                lastRgbSet = new int[maxGroup * groupColorCount];
                lastBrightnessSet = new int[maxGroup * groupColorCount];
            }
            int[] rgb = groupColor.rgb;
            int[] brightness = groupColor.relativeBrightness;
            int destPos = Math.max(group - 1, 0) * groupColorCount;
            System.arraycopy(rgb, 0, lastRgbSet, destPos, rgb.length);
            System.arraycopy(brightness, 0, lastBrightnessSet, destPos, brightness.length);
            if (group == maxGroup) {
                int modeColorVersion4New = Support.getModeColorVersion4New(info.goodsType, info.sku);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventBulbStringColorV2() modeColorVersion4New = " + modeColorVersion4New);
                }
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventBulbStringColor() lastRgbSet = " + Arrays.toString(lastRgbSet));
                }
                /*读取球泡串颜色完成*/
                ISubMode subMode;
                if (modeColorVersion4New == 5) {
                    SubModeColorV4 subModeColorV4 = new SubModeColorV4();
                    subModeColorV4.rgbSet = lastRgbSet;
                    subModeColorV4.brightnessSet = lastBrightnessSet;
                    subMode = subModeColorV4;
                } else if (modeColorVersion4New == 4) {
                    SubModeColorV3 subModeColorV3 = new SubModeColorV3();
                    subModeColorV3.rgbSet = lastRgbSet;
                    subModeColorV3.brightnessSet = lastBrightnessSet;
                    subMode = subModeColorV3;
                } else {
                    SubModeColorV2 subModeColor = new SubModeColorV2();
                    subModeColor.rgbSet = lastRgbSet;
                    subModeColor.brightnessSet = lastBrightnessSet;
                    subMode = subModeColor;
                }

                Mode mode = new Mode();
                mode.subMode = subMode;
                info.mode = mode;
                /*信息读取完成*/
                infoOver();
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    private int[] lastRgbSet4ColorStrip = new int[15];

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() write = " + write + " ; result = " + result);
        }
        /*标记是否是由其他模式切换到颜色模式*/
        boolean otherSubMode2Color = false;
        if (result) {
            if (write) {
                otherSubMode2Color = otherMode2Color(info.mode, event.getMode());
            }
            info.mode = event.getMode();
        }
        if (write) {
            if (result) {
                /*musicModeResult*/
                musicModeResult();
                if (diyCode != -1 && info.mode.subMode.subModeCommandType() == BleProtocol.sub_mode_new_diy) {
                    EventDiyApplyResult.sendEventDiyApplyResult(true, diyCode);
                }
                /*检测是否是diy模版效果*/
                if (diyCode != -1 && diyTemplateCode != -1) {
                    ISubMode subMode = info.mode.subMode;
                    if (subMode instanceof SubModeScenes) {
                        /*diy模版操作*/
                        if (((SubModeScenes) subMode).getEffect() == diyTemplateCode) {
                            /*通知diy操作成功*/
                            EventDiyApplyResult.sendEventDiyApplyResult(true, diyCode);
                            /*更新模式为diy模式*/
                            info.mode.subMode = new SubModeNewDiy(diyCode);
                        }
                    }
                }
            }
            if (result) {
                boolean bleWriteResult = true;
                if (otherSubMode2Color) {
                    bleWriteResult = false;
                    /*真色温灯，切换模式后，需要重新读取模式信息*/
                    if (Support.isColorTemRealDevice(info.goodsType)) {
                        getBle().addController(getReadModeController());
                    } else {
                        /*读取球泡串颜色*/
                        readPartColor();
                    }
                }
                if (bleWriteResult) {
                    if (info.mode instanceof Mode4ColorStrip) {
                        toUpdateUI4ColorStrip(event.getCommandType());
                    } else {
                        opResult.bleWrite(event.getCommandType(), true);
                        AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.effect_apply_all_times);
                        AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.adjust_effect_apply_time);
                    }
                }
            } else {
                /*模式写失败，且当前是diy模式设置*/
                if (diyCode != -1) {
                    EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
                }
                opResult.bleWrite(event.getCommandType(), false);
            }
        } else {
            if (result) {
                /*检测音乐模式版本*/
                checkMusicMode();
                /*若当前读出来是颜色模式-则需要读取灯串的颜色-且当前有颜色模式的渐变信息*/
                ISubMode subMode = info.mode.subMode;
                byte subModeCommandType = subMode.subModeCommandType();
                if (subModeCommandType == BleProtocol.sub_mode_color) {
                    if (subMode instanceof SubModeColor) {
                        ext.gradual = ((SubModeColor) subMode).gradual;
                    }
                    /*颜色模式；需要获取球泡串的色值*/
                    readPartColor();
                } else if (subModeCommandType == BleProtocol.sub_mode_color_v2) {
                    /*解析颜色模式-第二版协议，都是默认SubModeColorV2的构建解析-实际解析仅解析了渐变*/
                    if (subMode instanceof SubModeColorV2) {
                        ext.gradual = ((SubModeColorV2) subMode).gradual;
                        if (Support.isColorTemRealDevice(info.goodsType)) {
                            /*若是真色温设备，读取颜色模式时若当前灯带在色温展示下，则无需读取分段颜色，都是色温值,亮度都是100*/
                            int realKelvinByRead = ((SubModeColorV2) subMode).realKelvinByRead;
                            boolean isColorTemMode = realKelvinByRead > 0;
                            if (isColorTemMode) {
                                int[] temColorByKelvin = Constant.getTemColorByKelvin(realKelvinByRead);
                                if (temColorByKelvin[0] == 1) {
                                    int temColor = temColorByKelvin[2];
                                    LogInfra.Log.i(TAG, "onEventMode() 读取到真色温颜色模式");
                                    ((SubModeColorV2) subMode).rgbSet = new int[15];
                                    Arrays.fill(((SubModeColorV2) subMode).rgbSet, temColor);
                                    ((SubModeColorV2) subMode).brightnessSet = new int[15];
                                    Arrays.fill(((SubModeColorV2) subMode).brightnessSet, 100);
                                    infoOver();
                                    return;
                                }
                            }
                        }
                    }
                    /*读取球泡串颜色*/
                    readPartColor();
                } else {
                    /*其他模式，信息读取完成*/
                    infoOver();
                }
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
        /*设置完mode，则重置diyCode*/
        diyCode = -1;
        diyTemplateCode = -1;
    }

    private void musicModeResult() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeMusicV3) {
            EventNewMusicOpResult.sendEventNewMusicOpResult(true, ((SubModeMusicV3) subMode).getMusicCode());
        }
    }

    private void checkMusicMode() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof ParamsSubMode4MusicV1) {
            boolean newProtocol4SupportMultiMusicMode = Support.newProtocol4SupportMultiMusicMode(info.goodsType, info.versionSoft, info.versionHard);
            int multiNewMusicVersion = newProtocol4SupportMultiMusicMode ? 1 : 0;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicMode() newProtocol4SupportMultiMusicMode = " + newProtocol4SupportMultiMusicMode + " ; multiNewMusicVersion = " + multiNewMusicVersion);
            }
            mode.subMode = ((ParamsSubMode4MusicV1) subMode).toSupportSubMode(multiNewMusicVersion);
        }
    }

    private void toUpdateUI4ColorStrip(byte commandType) {
        AbsMode mode = info.mode;
        boolean isMode4ColorStrip = mode instanceof Mode4ColorStrip;
        if (!isMode4ColorStrip) return;
        boolean isLastController = ((Mode4ColorStrip) mode).isLastController;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeColor) {
            int rgb = ((SubModeColor) subMode).rgb;
            boolean[] ctlLight = ((SubModeColor) subMode).ctlLight;
            int length = ctlLight.length;
            for (int i = 0; i < length; i++) {
                if (ctlLight[i]) {
                    lastRgbSet4ColorStrip[i] = rgb;
                }
            }
            if (isLastController) {
                SubModeColor subModeColor = new SubModeColor();
                subModeColor.rgbSet = lastRgbSet4ColorStrip;
                mode.subMode = subModeColor;
                opResult.bleWrite(commandType, true);
                EventLightStripResult.sendEventLightStripResult(true);
            }
            return;
        }
        if (subMode instanceof SubModeColorV2) {
            int rgb = ((SubModeColorV2) subMode).rgb;
            boolean[] ctlLight = ((SubModeColorV2) subMode).ctlLight;
            int length = ctlLight.length;
            for (int i = 0; i < length; i++) {
                if (ctlLight[i]) {
                    lastRgbSet4ColorStrip[i] = rgb;
                }
            }
            if (isLastController) {
                SubModeColorV2 subModeColor = new SubModeColorV2();
                subModeColor.rgbSet = lastRgbSet4ColorStrip;
                subModeColor.brightnessSet = ((SubModeColorV2) subMode).brightnessSet;
                mode.subMode = subModeColor;
                opResult.bleWrite(commandType, true);
                EventLightStripResult.sendEventLightStripResult(true);
            }
            return;
        }
        if (subMode instanceof SubModeColorV3) {
            int rgb = ((SubModeColorV3) subMode).rgb;
            boolean[] ctlLight = ((SubModeColorV3) subMode).ctlLight;
            int length = ctlLight.length;
            for (int i = 0; i < length; i++) {
                if (ctlLight[i]) {
                    lastRgbSet4ColorStrip[i] = rgb;
                }
            }
            if (isLastController) {
                SubModeColorV3 subModeColor = new SubModeColorV3();
                subModeColor.rgbSet = lastRgbSet4ColorStrip;
                subModeColor.brightnessSet = ((SubModeColorV3) subMode).brightnessSet;
                mode.subMode = subModeColor;
                opResult.bleWrite(commandType, true);
                EventLightStripResult.sendEventLightStripResult(true);
            }
            return;
        }

        if (subMode instanceof SubModeColorV4) {
            int rgb = ((SubModeColorV4) subMode).rgb;
            int writeOpType = ((SubModeColorV4) subMode).getWriteOpType();
            if (writeOpType == SubModeColorV4.op_write_type_set_color) {
                boolean[] ctlLight = ((SubModeColorV4) subMode).ctlLight;
                int length = ctlLight.length;
                for (int i = 0; i < length; i++) {
                    if (ctlLight[i]) {
                        lastRgbSet4ColorStrip[i] = rgb;
                    }
                }
            }
            if (isLastController) {
                SubModeColorV4 subModeColor = new SubModeColorV4();
                subModeColor.rgbSet = lastRgbSet4ColorStrip;
                subModeColor.brightnessSet = ((SubModeColorV4) subMode).brightnessSet;
                mode.subMode = subModeColor;
                opResult.bleWrite(commandType, true);
                EventLightStripResult.sendEventLightStripResult(true);
            }
        }
    }

    private void readBulbStringColor() {
        int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
        int maxGroup = bulbStringMaxNum / 4 + (bulbStringMaxNum % 4 == 0 ? 0 : 1);
        AbsSingleController[] controllers = new AbsSingleController[maxGroup];
        for (int i = 0; i < maxGroup; i++) {
            controllers[i] = new BulbStringColorController(i + 1);
        }
        getBle().addController(controllers);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleDiy(EventMultipleDiy event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiy diyCode = " + diyCode + " result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            this.diyCode = diyCode;
            /*diy效果传输完成;通知设备设置成diy模式*/
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(diyCode);
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            this.diyCode = -1;
            /*diy效果传输失败，则通知传输失败*/
            EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
            getBle().clearControllers();
        }
    }

    private int[] lastRgbSet;

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBulbStringColor(EventBulbStringColor event) {
        boolean result = event.isResult();
        if (result) {
            BulbGroupColor groupColor = event.groupColor;
            int group = groupColor.group;
            int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
            int maxGroup = bulbStringMaxNum / 4 + (bulbStringMaxNum % 4 == 0 ? 0 : 1);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventBulbStringColor() bulbStringMaxNum = " + bulbStringMaxNum + " ; maxGroup = " + maxGroup + " ； group = " + group);
            }
            if (group == 1) {
                lastRgbSet = new int[maxGroup * 4];
            }
            int[] rgb = groupColor.rgb;
            int destPos = Math.max(group - 1, 0) * 4;
            System.arraycopy(rgb, 0, lastRgbSet, destPos, rgb.length);
            if (group == maxGroup) {
                int modeColorVersion4New = Support.getModeColorVersion4New(info.goodsType, info.sku);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventBulbStringColor() modeColorVersion4New = " + modeColorVersion4New);
                    LogInfra.Log.i(TAG, "onEventBulbStringColor() lastRgbSet = " + Arrays.toString(lastRgbSet));
                }
                /*读取球泡串颜色完成*/
                ISubMode subMode;
                if (modeColorVersion4New == 6) {
                    SubModeColorV6 subModeColor = new SubModeColorV6();
                    subModeColor.rgbSet = lastRgbSet;
                    subMode = subModeColor;
                } else if (modeColorVersion4New == 5) {
                    SubModeColorV4 subModeColorV4 = new SubModeColorV4();
                    subModeColorV4.rgbSet = lastRgbSet;
                    subMode = subModeColorV4;
                } else if (modeColorVersion4New == 4) {
                    SubModeColorV3 subModeColorV3 = new SubModeColorV3();
                    subModeColorV3.rgbSet = lastRgbSet;
                    subMode = subModeColorV3;
                } else {
                    SubModeColorV2 subModeColor = new SubModeColorV2();
                    subModeColor.rgbSet = lastRgbSet;
                    subMode = subModeColor;
                }
                Mode mode = new Mode();
                mode.subMode = subMode;
                info.mode = mode;
                /*信息读取完成*/
                infoOver();
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Override
    protected void readExtDeviceInfoAfterInfoOver() {
        AbsSingleController[] singleControllers = new AbsSingleController[]{new WifiSoftVersionController(), new WifiHardVersionController(), new WifiMacController(),};
        getBle().addController(singleControllers);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleNewScenes(EventMultiNewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleNewScenes() result = " + result + " ; scenesCode = " + scenesCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*场景效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*diy效果传输失败，则通知设置新场景模式失败*/
            getBle().clearControllers();
            opResult.bleWrite(event.getCommandType(), false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyTemplate4NewScenes(EventDiyTemplate4NewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        int diyCode = event.diyCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyTemplate4NewScenes() result = " + result + " ; scenesCode = " + scenesCode + " ; diyCode = " + diyCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            this.diyCode = diyCode;
            this.diyTemplateCode = scenesCode;
            /*diy模版效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            this.diyCode = -1;
            this.diyTemplateCode = -1;
            /*diy效果传输失败，则通知传输失败*/
            EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
            getBle().clearControllers();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleColor(EventMultipleColor event) {
        boolean result = event.isResult();
        if (result) {
            getBle().onMultipleControllerOk(event);
            Colors colors = event.colors;
            Support.makeColorStripMode(info, colors);
            opResult.bleWrite(event.getCommandType(), true);
            EventLightStripResult.sendEventLightStripResult(true);
        } else {
            getBle().clearControllers();
            EventLightStripResult.sendEventLightStripResult(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventOtaPrepare(EventOtaPrepare event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventOtaPrepare() result = " + result);
        }
        getBle().controllerEvent(event);
        OtaFlagV2.getInstance.onOtaPrepare(result);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultiNewDiyGraffiti(EventMultiNewDiyGraffiti event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultiNewDiyGraffiti() diyCode = " + diyCode + " ; result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            this.diyCode = diyCode;
            /*diy效果传输完成;通知设备设置成diy模式*/
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(diyCode);
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            this.diyCode = -1;
            /*diy效果传输失败，则通知传输失败*/
            EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
            getBle().clearControllers();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleMusic(EventMultipleMusic event) {
        boolean result = event.isResult();
        int sensitivity = event.getSensitivity();
        int subMusicCode = event.getSubMusicCode();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleMusic() result = " + result + " ; sensitivity = " + sensitivity + " ; subMusicCode = " + subMusicCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*新音乐模式效果传输完成-通知设备切换到音乐模式*/
            Mode mode = new Mode();
            mode.subMode = SubModeMusicV3.toNewSubModeMusic(sensitivity, subMusicCode);
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*新音乐效果传输失败*/
            EventNewMusicOpResult.sendEventNewMusicOpResult(false, subMusicCode);
            getBle().clearControllers();
        }
    }

    @Override
    public boolean autoReconnect() {
        if (Support.supportMusicFeast(info.goodsType, info.sku)) {
            return info.reconnectInDetail;
        }
        return super.autoReconnect();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventCheckIc(EventCheckIc event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventCheckIc");
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onAbsMusicControllerEvent(ControllerNoEvent.AbsMusicControllerEvent event) {
        LogInfra.Log.i(TAG, "onAbsMusicControllerEvent()");
        if (event.getResult()) {
            Mode mode = new Mode();
            SubModeAbsMusic subModeAbsMusic = new SubModeAbsMusic();
            subModeAbsMusic.setMusicCode(event.getMusicCode());
            subModeAbsMusic.setSensitivity(event.getSensitivity());
            mode.subMode = subModeAbsMusic;
            info.mode = mode;
        }
        getBle().onComposeControllerEvent(event);
        opResult.bleWrite(event.getCommandType(), event.getResult());
        EventApplyAbsMusic.EventApplyAbsMusicResult.sendEventApplyAbsMusicResult(event.getResult());
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventOnOffMemory(EventOnOffMemory event) {
        boolean result = event.isResult();
        SafeLog.i(TAG, () -> "onEventOnOffMemory() result = " + result);
        if (result) {
            int type = event.getType();
            SafeLog.i(TAG, () -> "onEventOnOffMemory() type = " + type);
            info.powerOffMemoryValue = type;
            Event4PowerOfMemoryOpResult.sendSuc(info.sku, info.device, info.powerOffMemoryValue);
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onDspVersionEvent(EventDspVersion event) {
        boolean result = event.isResult();
        SafeLog.i(TAG, "onDspVersionEvent() result = " + result);
        if (result) {
            int version = event.getVersion();
            SafeLog.i(TAG, "onDspVersionEvent() version = " + version);
            info.dspVersion = String.valueOf(version);
            if (info.aiVersion != null) {
                info.aiVersion.setDspVersionSoft(info.dspVersion);
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventCheckICResult(EventCheckICResult event) {
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventLowBlueLightResult(EventLowBlueLightResult event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventLowBlueLight");
        }
        if (event.isResult() && LowBlueLightConfig.Companion.checkSkuSupport(info.goodsType, info.sku, info.versionSoft)) {
            info.isLowBlueLightOpen = event.getSwitch();
            info.lowBlueLightValue = event.getBlue();
        }
        getBle().controllerEvent(event);
    }
}