package com.govee.dreamcolorlightv1.iot;

import android.text.TextUtils;

import com.govee.base2home.util.Encode;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ac.AbsIotManagerV1;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.TimerInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.BulbGroupColor;
import com.govee.dreamcolorlightv1.ble.BulbGroupColorV2;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ParamsSubMode4Music;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JsonUtil;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import androidx.annotation.Keep;

/**
 * Create by xieyingwu on 2020/5/13
 * cmd=status的iotV0版本$
 */
public class CmdStatusV0 {
    private static final String TAG = "CmdStatusV0";
    private static final int SubModeColor_RGB = 0x01;
    private static final int SubModeColor_BRIGHTNESS = 0x02;

    public boolean on;/*开关*/
    public int brightness;/*亮度*/
    public Mode mode;/*模式*/
    public String softVersion;/*蓝牙软件版本*/
    public int gradual;/*是否开启渐变-颜色模式下该字段才有效*/
    public String wifiSoftVersion;
    public String wifiHardVersion;

    public TimerInfo timerInfo1 = new TimerInfo();/*定时1*/
    public TimerInfo timerInfo2 = new TimerInfo();/*定时2*/
    public WakeUpInfo wakeUpInfo = new WakeUpInfo();/*唤醒*/
    public SleepInfo sleepInfo = new SleepInfo();/*睡眠*/

    public static CmdStatusV0 parseJson(String sku, String softVersion, String json) {
        if (TextUtils.isEmpty(json)) return null;
        CmdStatusV0 statusV0 = new CmdStatusV0();
        statusV0.softVersion = softVersion;
        String stateJsonStr = AbsIotManagerV1.getJsonObjectStr(json, Cmd.parse_json_key_state);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "stateJsonStr = " + stateJsonStr);
        }
        /*解析state字段内参数*/
        State state = JsonUtil.fromJson(stateJsonStr, State.class);
        boolean isColorSubMode = false;
        boolean isColorSubModeWithBrightness = false;
        if (state != null) {
            statusV0.on = state.onOff == 1;
            statusV0.brightness = state.brightness;
            isColorSubMode = state.mode == BleProtocol.sub_mode_color;
            isColorSubModeWithBrightness = state.mode == BleProtocol.sub_mode_color_v2;
            statusV0.wifiSoftVersion = state.wifiSoftVersion;
            statusV0.wifiHardVersion = state.wifiHardVersion;
        }
        /*解析op字段内参数*/
        String opJsonStr = AbsIotManagerV1.getJsonObjectStr(json, Cmd.parse_json_op);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "opJsonStr = " + opJsonStr);
        }
        ResultPt op = JsonUtil.fromJson(opJsonStr, ResultPt.class);
        if (op != null) {
            /*timer*/
            List<String> timerValueSet = op.getTimerValue();
            if (timerValueSet != null && !timerValueSet.isEmpty()) {
                for (String bleBase64BytesStr : timerValueSet) {
                    byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parseJson() timer originalBytes = " + BleUtil.bytesToHexString(originalBytes));
                    }
                    if (originalBytes != null && originalBytes.length == 20) {
                        byte[] validBytes = BleUtil.parseValidBleBytes(originalBytes);
                        int group = BleUtil.getUnsignedByte(validBytes[5]);/*群组*/
                        if (LogInfra.openLog()) {
                            LogInfra.Log.i(TAG, "parseJson() time group = " + group);
                        }
                        TimerInfo timerInfo = TimerInfo.parseBytes(validBytes);
                        if (group == 0) {
                            statusV0.timerInfo1 = timerInfo;
                        } else {
                            statusV0.timerInfo2 = timerInfo;
                        }
                    }
                }
            }
            /*sleep*/
            List<String> sleepValueSet = op.getSleepValue();
            if (sleepValueSet != null && !sleepValueSet.isEmpty()) {
                String bleBase64BytesStr = sleepValueSet.get(0);
                byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "parseJson() sleep originalBytes = " + BleUtil.bytesToHexString(originalBytes));
                }
                if (originalBytes != null && originalBytes.length == 20) {
                    byte[] validBytes = BleUtil.parseValidBleBytes(originalBytes);
                    statusV0.sleepInfo = SleepInfo.parseBytes(validBytes);
                }
            }
            /*wakeup*/
            List<String> wakeupValueSet = op.getWakeupValue();
            if (wakeupValueSet != null && !wakeupValueSet.isEmpty()) {
                String bleBase64BytesStr = wakeupValueSet.get(0);
                byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "parseJson() wakeup originalBytes = " + BleUtil.bytesToHexString(originalBytes));
                }
                if (originalBytes != null && originalBytes.length == 20) {
                    byte[] validBytes = BleUtil.parseValidBleBytes(originalBytes);
                    statusV0.wakeUpInfo = WakeUpInfo.parseBytes(validBytes);
                }
            }
            /*mode*/
            List<String> modeValueSet = op.getModeValue();
            if (modeValueSet != null && !modeValueSet.isEmpty()) {
                int size = modeValueSet.size();
                if (isColorSubMode) {
                    SubModeColor subModeColor = new SubModeColor();
                    subModeColor.rgbSet = parseColorModeRgbSet(modeValueSet);
                    int gradual = parseGradual(modeValueSet);
                    subModeColor.gradual = gradual;
                    Mode mode = new Mode();
                    mode.subMode = subModeColor;
                    statusV0.gradual = gradual;
                    statusV0.mode = mode;
                } else if (isColorSubModeWithBrightness) {
                    SubModeColorV2 subModeColor = new SubModeColorV2();
                    subModeColor.rgbSet = parseColorModeRgbSetWithBrightness(modeValueSet);
                    int gradual = parseGradual(modeValueSet);
                    subModeColor.gradual = gradual;
                    subModeColor.brightnessSet = parseColorModeRgbSet4Brightness(modeValueSet);
                    Mode mode = new Mode();
                    mode.subMode = subModeColor;
                    statusV0.gradual = gradual;
                    statusV0.mode = mode;
                } else {
                    String bleBase64BytesStr = modeValueSet.get(size - 1);
                    byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parseJson() modeValue originalBytes = " + BleUtil.bytesToHexString(originalBytes));
                    }
                    if (originalBytes != null && originalBytes.length == 20) {
                        byte[] valid17Bytes = BleUtil.parseValidBleBytes(originalBytes);
                        Mode mode = new Mode();
                        mode.parse(valid17Bytes);
                        ISubMode subMode = mode.subMode;
                        if (subMode instanceof ParamsSubMode4Music) {
                            int multiNewMusicVersion = Support.getMultiNewMusicVersion4Telink(false, sku, softVersion, statusV0.wifiSoftVersion);
                            if (LogInfra.openLog()) {
                                LogInfra.Log.i(TAG, "parseJson() multiNewMusicVersion = " + multiNewMusicVersion);
                            }
                            mode.subMode = ((ParamsSubMode4Music) subMode).toSupportSubMode(multiNewMusicVersion);
                        }
                        statusV0.mode = mode;
                    }
                }
            }
        }
        return statusV0;
    }

    public static int parseGradual(List<String> modeValueSet) {
        if (modeValueSet == null || modeValueSet.isEmpty()) return 0;
        /*第一包是颜色模式包，携带渐变参数*/
        String bleBase64BytesStr = modeValueSet.get(0);
        byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parseGradual() originalBytes = " + BleUtil.bytesToHexString(originalBytes));
        }
        if (originalBytes != null && originalBytes.length == 20) {
            if (originalBytes[1] == BleProtocolConstants.SINGLE_MODE && (originalBytes[2] == BleProtocol.sub_mode_color || originalBytes[2] == BleProtocol.sub_mode_color_v2)) {
                return BleUtil.getUnsignedByte(originalBytes[3]);
            }
        }
        return 0;
    }

    /**
     * 解析模式里面的球泡颜色
     *
     * @param modeValueSet
     * @return
     */
    public static int[] parseColorModeRgbSet(List<String> modeValueSet) {
        if (modeValueSet == null || modeValueSet.isEmpty()) return null;
        /*颜色模式需要单独解析;第一包不是颜色数据包*/
        int size = modeValueSet.size();
        List<BulbGroupColor> groupColors = new ArrayList<>();
        for (int i = 1; i < size; i++) {
            String bleBase64BytesStr = modeValueSet.get(i);
            byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "parseColorModeRgbSet() group = " + i + " ; colorMode originalBytes = " + BleUtil.bytesToHexString(originalBytes));
            }
            if (originalBytes != null && originalBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(originalBytes);
                BulbGroupColor bulbGroupColor = BulbGroupColor.parseBytes(valid17Bytes);
                groupColors.add(bulbGroupColor);
            }
        }
        int[] rgbSet = null;
        if (!groupColors.isEmpty()) {
            int sizeGroup = groupColors.size();
            rgbSet = new int[sizeGroup * 4];
            for (int i = 0; i < sizeGroup; i++) {
                int destPos = i * 4;
                BulbGroupColor bulbGroupColor = groupColors.get(i);
                int[] rgb = bulbGroupColor.rgb;
                System.arraycopy(rgb, 0, rgbSet, destPos, rgb.length);
            }
        }
        return rgbSet;
    }

    /**
     * 解析模式里面的球泡颜色
     *
     * @param modeValueSet
     * @return
     */
    public static int[] parseColorModeRgbSetWithBrightness(List<String> modeValueSet) {
        if (modeValueSet == null || modeValueSet.isEmpty()) return null;
        /*颜色模式需要单独解析;第一包不是颜色数据包*/
        int size = modeValueSet.size();
        List<BulbGroupColorV2> groupColors = new ArrayList<>();
        for (int i = 1; i < size; i++) {
            String bleBase64BytesStr = modeValueSet.get(i);
            byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "parseColorModeRgbSet() group = " + i + " ; colorMode originalBytes = " + BleUtil.bytesToHexString(originalBytes));
            }
            if (originalBytes != null && originalBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(originalBytes);
                BulbGroupColorV2 bulbGroupColor = BulbGroupColorV2.parseBytes(valid17Bytes);
                groupColors.add(bulbGroupColor);
            }
        }
        int[] rgbSet = null;
        if (!groupColors.isEmpty()) {
            int sizeGroup = groupColors.size();
            rgbSet = new int[sizeGroup * 3];
            for (int i = 0; i < sizeGroup; i++) {
                int destPos = i * 3;
                BulbGroupColorV2 bulbGroupColor = groupColors.get(i);
                int[] rgb = bulbGroupColor.rgb;
                System.arraycopy(rgb, 0, rgbSet, destPos, rgb.length);
            }
        }
        return rgbSet;
    }

    /**
     * 解析模式里面的球泡亮度
     *
     * @param modeValueSet
     * @return
     */
    public static int[] parseColorModeRgbSet4Brightness(List<String> modeValueSet) {
        if (modeValueSet == null || modeValueSet.isEmpty()) return null;
        /*颜色模式需要单独解析;第一包不是颜色数据包*/
        int size = modeValueSet.size();
        List<BulbGroupColorV2> groupColors = new ArrayList<>();
        for (int i = 1; i < size; i++) {
            String bleBase64BytesStr = modeValueSet.get(i);
            byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
            if (originalBytes != null && originalBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(originalBytes);
                BulbGroupColorV2 bulbGroupColor = BulbGroupColorV2.parseBytes(valid17Bytes);
                groupColors.add(bulbGroupColor);
            }
        }
        int[] brightnessSet = null;
        if (!groupColors.isEmpty()) {
            int sizeGroup = groupColors.size();
            brightnessSet = new int[sizeGroup * 3];
            for (int i = 0; i < sizeGroup; i++) {
                int destPos = i * 3;
                BulbGroupColorV2 bulbGroupColor = groupColors.get(i);
                int[] brightness = bulbGroupColor.relativeBrightness;
                System.arraycopy(brightness, 0, brightnessSet, destPos, brightness.length);
            }
        }
        return brightnessSet;
    }

    public static SubModeColor parseBulbColorRgbSet(List<String> bulbValueSet) {
        SubModeColor subModeColor = new SubModeColor();
        if (bulbValueSet == null || bulbValueSet.isEmpty()) return subModeColor;
        String firstValueStr = bulbValueSet.get(0);
        byte[] firstOriginalBytes = Encode.decryByBase64(firstValueStr);
        /*判断第一包是模式包还是数据包*/
        boolean firstBytesIsData = false;
        if (firstOriginalBytes != null && firstOriginalBytes.length == 20) {
            firstBytesIsData = firstOriginalBytes[1] == BleProtocol.MSG_TYPE_READ_BULB_COLOR;
            if (firstOriginalBytes[0] == BleProtocolConstants.SINGLE_WRITE && firstOriginalBytes[1] == BleProtocolConstants.SINGLE_MODE && firstOriginalBytes[2] == BleProtocol.sub_mode_color) {
                int r = BleUtil.getUnsignedByte(firstOriginalBytes[3]);
                int g = BleUtil.getUnsignedByte(firstOriginalBytes[4]);
                int b = BleUtil.getUnsignedByte(firstOriginalBytes[5]);
                subModeColor.rgb = ColorUtils.toColor(r, g, b);
            }
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parseBulbColorRgbSet() firstBytesIsData = " + firstBytesIsData + " ; firstOriginalBytes = " + BleUtil.bytesToHexString(firstOriginalBytes));
        }
        int size = bulbValueSet.size();
        int fromIndex = firstBytesIsData ? 0 : 1;
        List<BulbGroupColor> groupColors = new ArrayList<>();
        for (int i = fromIndex; i < size; i++) {
            String bleBase64BytesStr = bulbValueSet.get(i);
            byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "parseBulbColorRgbSet() group = " + i + " ; colorMode originalBytes = " + BleUtil.bytesToHexString(originalBytes));
            }
            if (originalBytes != null && originalBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(originalBytes);
                BulbGroupColor bulbGroupColor = BulbGroupColor.parseBytes(valid17Bytes);
                groupColors.add(bulbGroupColor);
            }
        }
        int[] rgbSet = null;
        if (!groupColors.isEmpty()) {
            int sizeGroup = groupColors.size();
            rgbSet = new int[sizeGroup * 4];
            for (int i = 0; i < sizeGroup; i++) {
                int destPos = i * 4;
                BulbGroupColor bulbGroupColor = groupColors.get(i);
                int[] rgb = bulbGroupColor.rgb;
                System.arraycopy(rgb, 0, rgbSet, destPos, rgb.length);
            }
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }

    public static SubModeColorV2 parseBulbColorRgbSet4NewColor(List<String> bulbValueSet) {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        if (bulbValueSet == null || bulbValueSet.isEmpty()) return subModeColor;
        String value1 = bulbValueSet.remove(0);
        byte[] firstOriginalBytes = Encode.decryByBase64(value1);
        if (firstOriginalBytes != null && firstOriginalBytes[3] == SubModeColor_RGB) {
            int r = BleUtil.getUnsignedByte(firstOriginalBytes[4]);
            int g = BleUtil.getUnsignedByte(firstOriginalBytes[5]);
            int b = BleUtil.getUnsignedByte(firstOriginalBytes[6]);
            subModeColor.rgb = ColorUtils.toColor(r, g, b);
            int ctr = BleUtil.getUnsignedByte(firstOriginalBytes[9]);
            int ctg = BleUtil.getUnsignedByte(firstOriginalBytes[10]);
            int ctb = BleUtil.getUnsignedByte(firstOriginalBytes[11]);
            subModeColor.ctRgb = ColorUtils.toColor(ctr, ctg, ctb);
        } else if (firstOriginalBytes != null && firstOriginalBytes[3] == SubModeColor_BRIGHTNESS) {
            subModeColor.brightness = BleUtil.getUnsignedByte(firstOriginalBytes[4]);
            return subModeColor;
        }
        int size = bulbValueSet.size();
        List<BulbGroupColorV2> groupColors = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            String bleBase64BytesStr = bulbValueSet.get(i);
            byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "parseBulbColorRgbSet() group = " + i + " ; colorMode originalBytes = " + BleUtil.bytesToHexString(originalBytes));
            }
            if (originalBytes != null && originalBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(originalBytes);
                BulbGroupColorV2 bulbGroupColor = BulbGroupColorV2.parseBytes(valid17Bytes);
                groupColors.add(bulbGroupColor);
            }
        }
        int[] rgbSet = null;
        int[] brightnessSet = null;
        if (!groupColors.isEmpty()) {
            int sizeGroup = groupColors.size();
            rgbSet = new int[sizeGroup * 3];
            brightnessSet = new int[sizeGroup * 3];
            for (int i = 0; i < sizeGroup; i++) {
                int destPos = i * 3;
                BulbGroupColorV2 bulbGroupColor = groupColors.get(i);
                int[] rgb = bulbGroupColor.rgb;
                int[] brightness = bulbGroupColor.relativeBrightness;
                System.arraycopy(rgb, 0, rgbSet, destPos, rgb.length);
                System.arraycopy(brightness, 0, brightnessSet, destPos, brightness.length);
            }
        }
        subModeColor.rgbSet = rgbSet;
        subModeColor.brightnessSet = brightnessSet;
        return subModeColor;
    }


    public static SubModeColor parseColorStrip4SubModeColor(List<String> bulbValueSet) {
        SubModeColor subModeColor = new SubModeColor();
        if (bulbValueSet == null || bulbValueSet.isEmpty()) return subModeColor;
        Iterator<String> iterator = bulbValueSet.iterator();
        while (iterator.hasNext()) {
            String next = iterator.next();
            byte[] originalBytes = Encode.decryByBase64(next);
            if (originalBytes == null) return subModeColor;
            if (originalBytes[0] == BleProtocolConstants.SINGLE_WRITE && originalBytes[1] == BleProtocolConstants.SINGLE_MODE && originalBytes[2] == BleProtocol.sub_mode_color) {
                /*移除所有rgb包,颜色会在最后4包解*/
                iterator.remove();
            }
        }
        int size = bulbValueSet.size();
        List<BulbGroupColor> groupColors = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            String bleBase64BytesStr = bulbValueSet.get(i);
            byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "parseBulbColorRgbSet() group = " + i + " ; colorMode originalBytes = " + BleUtil.bytesToHexString(originalBytes));
            }
            if (originalBytes != null && originalBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(originalBytes);
                BulbGroupColor bulbGroupColor = BulbGroupColor.parseBytes(valid17Bytes);
                groupColors.add(bulbGroupColor);
            }
        }
        int[] rgbSet = null;
        if (!groupColors.isEmpty()) {
            int sizeGroup = groupColors.size();
            rgbSet = new int[sizeGroup * 4];
            for (int i = 0; i < sizeGroup; i++) {
                int destPos = i * 4;
                BulbGroupColor bulbGroupColor = groupColors.get(i);
                int[] rgb = bulbGroupColor.rgb;
                System.arraycopy(rgb, 0, rgbSet, destPos, rgb.length);
            }
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }

    public static SubModeColorV2 parseColorStrip(List<String> bulbValueSet) {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        if (bulbValueSet == null || bulbValueSet.isEmpty()) return subModeColor;
        Iterator<String> iterator = bulbValueSet.iterator();
        while (iterator.hasNext()) {
            String next = iterator.next();
            byte[] originalBytes = Encode.decryByBase64(next);
            if (originalBytes != null) {
                if (originalBytes[0] == BleProtocolConstants.SINGLE_WRITE && originalBytes[1] == BleProtocolConstants.SINGLE_MODE && originalBytes[2] == BleProtocol.sub_mode_color_v2) {
                    iterator.remove();
                }
            }
        }
        int size = bulbValueSet.size();
        List<BulbGroupColorV2> groupColors = new ArrayList<>();
        for (int i = 0; i < size; i++) {
            String bleBase64BytesStr = bulbValueSet.get(i);
            byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "parseBulbColorRgbSet() group = " + i + " ; colorMode originalBytes = " + BleUtil.bytesToHexString(originalBytes));
            }
            if (originalBytes != null && originalBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(originalBytes);
                BulbGroupColorV2 bulbGroupColor = BulbGroupColorV2.parseBytes(valid17Bytes);
                groupColors.add(bulbGroupColor);
            }
        }
        int[] rgbSet = null;
        int[] brightnessSet = null;
        if (!groupColors.isEmpty()) {
            int sizeGroup = groupColors.size();
            rgbSet = new int[sizeGroup * 3];
            brightnessSet = new int[sizeGroup * 3];
            for (int i = 0; i < sizeGroup; i++) {
                int destPos = i * 3;
                BulbGroupColorV2 bulbGroupColor = groupColors.get(i);
                int[] rgb = bulbGroupColor.rgb;
                int[] brightness = bulbGroupColor.relativeBrightness;
                System.arraycopy(rgb, 0, rgbSet, destPos, rgb.length);
                System.arraycopy(brightness, 0, brightnessSet, destPos, brightness.length);
            }
        }
        subModeColor.rgbSet = rgbSet;
        subModeColor.brightnessSet = brightnessSet;
        return subModeColor;
    }

    public static boolean isSetColorStrip4Ios(String value) {
        byte[] bytes = Encode.decryByBase64(value);
        if (bytes == null) return false;
        return bytes[0] == BleProtocolConstants.SINGLE_WRITE && bytes[1] == BleProtocol.SINGLE_GRADUAL_CHANGE_4_WIFI_BLE;
    }

    @Keep
    static class State {
        public int onOff;
        public int brightness;
        public int mode;
        public String wifiSoftVersion;
        public String wifiHardVersion;
    }


    public static boolean isNewColorMode4Bulb(String value) {
        byte[] bytes = Encode.decryByBase64(value);
        if (bytes == null) return false;
        return bytes[0] == BleProtocolConstants.SINGLE_WRITE && bytes[1] == BleProtocolConstants.SINGLE_MODE && bytes[2] == BleProtocol.sub_mode_color_v2;
    }

    public static boolean isColorMode4Bulb(String value) {
        byte[] bytes = Encode.decryByBase64(value);
        if (bytes == null) return false;
        return bytes[0] == BleProtocolConstants.SINGLE_WRITE && bytes[1] == BleProtocolConstants.SINGLE_MODE && bytes[2] == BleProtocol.sub_mode_color;
    }

    public static boolean isReadColorMode4Bulb(String value) {
        byte[] bytes = Encode.decryByBase64(value);
        if (bytes == null) return false;
        return bytes[0] == BleProtocolConstants.SINGLE_READ && bytes[1] == BleProtocolConstants.MSG_READ_BULB_LIGHT_COLOR_WITH_BRIGHTNESS;
    }
}