package com.govee.dreamcolorlightv1.adjust.v1;

import android.text.TextUtils;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.device.IDeviceNet;
import com.govee.base2home.device.net.DeviceIcRequest;
import com.govee.base2home.scenes.SecretKeyConfig;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.diy.EventDiyApplyResult;
import com.govee.base2light.ac.timer.SleepFailEvent;
import com.govee.base2light.ac.timer.SleepSucEvent;
import com.govee.base2light.ac.timer.Timer;
import com.govee.base2light.ac.timer.TimerResultEvent;
import com.govee.base2light.ac.timer.WakeupFailEvent;
import com.govee.base2light.ac.timer.WakeupSucEvent;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.EventBrightness;
import com.govee.base2light.ble.controller.EventDiyTemplate4NewScenes;
import com.govee.base2light.ble.controller.EventHardVersion;
import com.govee.base2light.ble.controller.EventIcNum;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultiNewDiyGraffiti;
import com.govee.base2light.ble.controller.EventMultiNewScenes;
import com.govee.base2light.ble.controller.EventMultipleDiy;
import com.govee.base2light.ble.controller.EventMultipleMusic;
import com.govee.base2light.ble.controller.EventNewTimeV1;
import com.govee.base2light.ble.controller.EventSleep;
import com.govee.base2light.ble.controller.EventSoftVersion;
import com.govee.base2light.ble.controller.EventSyncTime;
import com.govee.base2light.ble.controller.EventWakeUp;
import com.govee.base2light.ble.controller.HardVersionController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.IcNumController;
import com.govee.base2light.ble.controller.NewTimerV1;
import com.govee.base2light.ble.controller.NewTimerV1Controller;
import com.govee.base2light.ble.controller.SecretKeyController;
import com.govee.base2light.ble.controller.SleepController;
import com.govee.base2light.ble.controller.SoftVersionController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.controller.SyncTimeController;
import com.govee.base2light.ble.controller.SyncTimeInfo;
import com.govee.base2light.ble.controller.WakeUpController;
import com.govee.base2light.ble.music.EventNewMusicOpResult;
import com.govee.base2light.ble.ota.v2.OtaFlagV2;
import com.govee.base2light.light.EventLightStripResult;
import com.govee.base2light.pact.BleInfo;
import com.govee.base2light.pact.ble.AbsBleOpV1;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.BulbGroupColorV2;
import com.govee.dreamcolorlightv1.ble.BulbStringColorControllerV2;
import com.govee.dreamcolorlightv1.ble.EventBulbStringColorV2;
import com.govee.dreamcolorlightv1.ble.EventGradual;
import com.govee.dreamcolorlightv1.ble.EventLightNum;
import com.govee.dreamcolorlightv1.ble.EventLimit;
import com.govee.dreamcolorlightv1.ble.EventOtaPrepare;
import com.govee.dreamcolorlightv1.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv1.ble.LightNumController;
import com.govee.dreamcolorlightv1.ble.LimitController;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.Mode4ColorStrip;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.ParamsSubMode4Music;
import com.govee.dreamcolorlightv1.ble.ParamsSubMode4MusicV1;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV3;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.util.LightExtInfoUtilKt;
import com.ihoment.base2app.Cache;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.network.Network;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;
import java.util.List;

import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2020-02-12
 * ble-op控制v3版本$
 */
class BleOpV3 extends AbsBleOpV1 {
    private static final String TAG = "BleOpV3";

    private int diyCode = -1;
    private int diyTemplateCode = -1;
    private final ExtV3 ext;

    BleOpV3(BleInfo info, ExtV3 extV3) {
        super(info);
        this.ext = extV3;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected void onOffChangeReadingInfo() {
        boolean supportLimit = Support.supportLimit(info.goodsType);
        boolean noSupportGradual = Support.noSupportGradual(info.goodsType);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onOffChangeInfoReading() supportLimit = " + supportLimit + " ; noSupportGradual = " + noSupportGradual);
        }
        boolean supportBindWithIcNum = Support.supportBindWithIcNum(info.goodsType, info.pactType, info.pactCode, info.sku);
        LogInfra.Log.i(TAG, "onOffChangeReadingInfo() supportBindWithIcNum = " + supportBindWithIcNum);
        SyncTimeInfo info = SyncTimeInfo.getCurrentTime();
        AbsSingleController[] controllers;
        if (supportLimit) {
            controllers = new AbsSingleController[]{
                    new SoftVersionController(),
                    new HardVersionController(),
                    new LimitController(),
                    new SyncTimeController(info.hour, info.minute, info.second, info.week),
                    new WakeUpController(),
                    new SleepController(),
                    new NewTimerV1Controller(0xFF),
                    new LightNumController(),
                    new SwitchController(),
                    new BrightnessController(),
                    new ModeController(),
                    new Gradual4BleWifiController(),
            };
            if (supportBindWithIcNum) {
                AbsSingleController[] newControllers = new AbsSingleController[controllers.length + 1];
                System.arraycopy(controllers, 0, newControllers, 1, controllers.length);
                newControllers[0] = new IcNumController();
                controllers = newControllers;
            }
            getBle().startController(controllers);
            return;
        }
        if (noSupportGradual) {
            controllers = new AbsSingleController[]{
                    new SoftVersionController(),
                    new HardVersionController(),
                    new SyncTimeController(info.hour, info.minute, info.second, info.week),
                    new WakeUpController(),
                    new SleepController(),
                    new NewTimerV1Controller(0xFF),
                    new LightNumController(),
                    new SwitchController(),
                    new BrightnessController(),
                    new ModeController()
            };
            if (supportBindWithIcNum) {
                AbsSingleController[] newControllers = new AbsSingleController[controllers.length + 1];
                System.arraycopy(controllers, 0, newControllers, 1, controllers.length);
                newControllers[0] = new IcNumController();
                controllers = newControllers;
            }
            getBle().startController(controllers);
            return;
        }
        controllers = new AbsSingleController[]{
                new SoftVersionController(),
                new HardVersionController(),
                new SyncTimeController(info.hour, info.minute, info.second, info.week),
                new WakeUpController(),
                new SleepController(),
                new NewTimerV1Controller(0xFF),
                new LightNumController(),
                new Gradual4BleWifiController(),
                new SwitchController(),
                new BrightnessController(),
                new ModeController()
        };
        if (supportBindWithIcNum) {
            AbsSingleController[] newControllers = new AbsSingleController[controllers.length + 1];
            System.arraycopy(controllers, 0, newControllers, 1, controllers.length);
            newControllers[0] = new IcNumController();
            controllers = newControllers;
        }
        getBle().startController(controllers);
    }

    @Nullable
    @Override
    protected AbsSingleController getReadModeController() {
        return new ModeController();
    }

    @Override
    protected AbsSingleController getController4DeviceSecret() {
        boolean supportDeviceSecret = Support.isSupportDeviceSecret(this.info.goodsType);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getController4DeviceSecret() supportDeviceSecret = " + supportDeviceSecret);
        }
        if (!supportDeviceSecret) {
            return null;
        }
        String secretCode = this.info.secretCode;
        if (TextUtils.isEmpty(secretCode)) {
            secretCode = SecretKeyConfig.read().getSecretKey(this.info.bleAddress);
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getController4DeviceSecret() secretCode = " + secretCode);
        }
        if (TextUtils.isEmpty(secretCode)) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "getController4DeviceSecret() 未找到对应的secretCode address = " + info.bleAddress);
            }
            return null;
        }
        return new SecretKeyController(secretCode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSyncTime(EventSyncTime event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSyncTime() result = " + result);
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventLimit(EventLimit event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventLimit() result = " + result + " ; write = " + write);
        }
        if (result) {
            boolean openLimit = event.openLimit;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventLimit() openLimit = " + openLimit);
            }
            ext.limitOpen = openLimit;
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onIcNumEvent(EventIcNum event) {
        if (event.isResult()) {
            int icNum = event.icNum;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onIcNumEvent() icNum = " + icNum + " ; info.ic = " + info.ic);
            }
            if (icNum > 0 && icNum != info.ic) {
                info.ic = icNum;
                /*上报ic数*/
                reportIc(icNum);
            }
        }
        getBle().controllerEvent(event);
    }

    private void reportIc(int ic) {
        LogInfra.Log.i(TAG, "reportIc() ic = " + ic);
        /*上报ic信息*/
        DeviceIcRequest request = new DeviceIcRequest(String.valueOf(System.currentTimeMillis()), info.sku, info.device, ic);
        Cache.get(IDeviceNet.class).updateDeviceIc(request).enqueue(new Network.IHCallBack<>(request));
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSoftVersion(EventSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventSoftVersion() softVersion = " + softVersion);
            }
            info.versionSoft = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventHardVersion(EventHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventHardVersion() hardVersion = " + hardVersion);
            }
            info.versionHard = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBrightness(EventBrightness event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBrightness() write = " + write + " ; result = " + result);
        }
        if (result) {
            int brightness = event.getBrightness();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventBrightness() brightness = " + brightness);
            }
            ext.brightness = brightness;
            LightExtInfoUtilKt.updateLocalBrightness(info.sku, info.device, brightness);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.effect_apply_all_times);
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.adjust_effect_apply_time);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    private final int[] lastRgbSet4ColorStrip = new int[15];

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() write = " + write + " ; result = " + result);
        }
        /*标记是否是由其他模式切换到颜色模式*/
        boolean mode2Color = false;
        if (result) {
            if (write) {
                AbsMode mode = event.getMode();
                byte subModeCommandType = mode.subMode.subModeCommandType();
                boolean isColorSubMode = subModeCommandType == BleProtocol.sub_mode_color_v2;
                if (isColorSubMode) {
                    mode2Color = true;
                    AbsMode lastMode = info.mode;
                    if (lastMode != null) {
                        ISubMode subMode = lastMode.subMode;
                        if (subMode != null) {
                            mode2Color = subMode.subModeCommandType() != BleProtocol.sub_mode_color_v2;
                        }
                    }
                }
            }
            info.mode = event.getMode();
        }
        if (write) {
            if (result) {
                /*musicModeResult*/
                musicModeResult();
                /*检测是否是diy效果*/
                if (diyCode != -1 && info.mode.subMode.subModeCommandType() == BleProtocol.sub_mode_new_diy) {
                    EventDiyApplyResult.sendEventDiyApplyResult(true, diyCode);
                }
                /*检测是否是diy模版效果*/
                if (diyCode != -1 && diyTemplateCode != -1) {
                    ISubMode subMode = info.mode.subMode;
                    if (subMode instanceof SubModeScenes) {
                        /*diy模版操作*/
                        if (((SubModeScenes) subMode).getEffect() == diyTemplateCode) {
                            /*通知diy操作成功*/
                            EventDiyApplyResult.sendEventDiyApplyResult(true, diyCode);
                            /*更新模式为diy模式*/
                            info.mode.subMode = new SubModeNewDiy(diyCode);
                        }
                    }
                }
            }
            if (result) {
                boolean bleWriteResult = true;
                if (mode2Color) {
                    bleWriteResult = false;
                    /*读取球泡串颜色*/
                    readPartColor();
                }
                if (bleWriteResult) {
                    if (info.mode instanceof Mode4ColorStrip) {
                        toUpdateUI4ColorStrip(event.getCommandType());
                    } else {
                        opResult.bleWrite(event.getCommandType(), true);
                        AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.effect_apply_all_times);
                        AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.adjust_effect_apply_time);
                    }
                }
            } else {
                /*模式写失败，且当前是diy模式设置*/
                if (diyCode != -1) {
                    EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
                }
                opResult.bleWrite(event.getCommandType(), false);
            }
        } else {
            if (result) {
                /*检测音乐模式版本*/
                checkMusicMode();
                /*若当前读出来是颜色模式-则需要读取灯串的颜色-且当前有颜色模式的渐变信息*/
                ISubMode subMode = info.mode.subMode;
                byte subModeCommandType = subMode.subModeCommandType();
                if (subModeCommandType == BleProtocol.sub_mode_color_v2) {
                    if (subMode instanceof SubModeColorV2) {
                        ext.gradual = ((SubModeColorV2) subMode).gradual;
                    }
                    /*颜色模式；需要获取球泡串的色值*/
                    readPartColor();
                } else {
                    /*其他模式，信息读取完成*/
                    infoOver();
                }
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
        /*设置完mode，则重置diyCode*/
        diyCode = -1;
        diyTemplateCode = -1;
    }

    private void musicModeResult() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeMusicV3) {
            EventNewMusicOpResult.sendEventNewMusicOpResult(true, ((SubModeMusicV3) subMode).getMusicCode());
        }
    }

    private void checkMusicMode() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof ParamsSubMode4Music) {
            int multiNewMusicVersion = Support.getMultiNewMusicVersion4Telink(info.sku, info.versionSoft);
            mode.subMode = ((ParamsSubMode4Music) subMode).toSupportSubMode(multiNewMusicVersion);
        } else if (subMode instanceof ParamsSubMode4MusicV1) {
            boolean newProtocol4SupportMultiMusicMode = Support.newProtocol4SupportMultiMusicMode(info.goodsType, info.versionSoft, info.versionHard);
            int multiNewMusicVersion = newProtocol4SupportMultiMusicMode ? 1 : 0;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicMode() newProtocol4SupportMultiMusicMode = " + newProtocol4SupportMultiMusicMode + " ; multiNewMusicVersion = " + multiNewMusicVersion);
            }
            mode.subMode = ((ParamsSubMode4MusicV1) subMode).toSupportSubMode(multiNewMusicVersion);
        }
    }

    private int[] lastBrightnessSet;

    private void toUpdateUI4ColorStrip(byte commandType) {
        ISubMode subMode = info.mode.subMode;
        SubModeColor subModeColor = new SubModeColor();
        SubModeColorV2 subModeColorV2 = new SubModeColorV2();
        if (subMode instanceof SubModeColor) {
            subModeColor = ((SubModeColor) subMode);
            for (int i = 0; i < subModeColor.ctlLight.length; i++) {
                if (subModeColor.ctlLight[i]) {
                    lastRgbSet4ColorStrip[i] = subModeColor.rgb;
                }
            }
        }
        if (subMode instanceof SubModeColorV2) {
            subModeColorV2 = ((SubModeColorV2) subMode);
            for (int i = 0; i < subModeColorV2.ctlLight.length; i++) {
                if (subModeColorV2.ctlLight[i]) {
                    lastRgbSet4ColorStrip[i] = subModeColorV2.rgb;
                }
            }
        }
        if (((Mode4ColorStrip) info.mode).isLastController) {
            if (subMode instanceof SubModeColor) {
                subModeColor.rgbSet = lastRgbSet4ColorStrip;
            }
            if (subMode instanceof SubModeColorV2) {
                subModeColorV2.rgbSet = lastRgbSet4ColorStrip;
            }
            opResult.bleWrite(commandType, true);
            EventLightStripResult.sendEventLightStripResult(true);
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.effect_apply_all_times);
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.adjust_effect_apply_time);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBulbStringColorV2(EventBulbStringColorV2 event) {
        boolean result = event.isResult();
        if (result) {
            BulbGroupColorV2 groupColor = event.groupColor;
            int group = groupColor.group;
            int bulbStringMaxNum = 15;/*15段*/
            int maxGroup = 5;/*5组*/
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventBulbStringColor() bulbStringMaxNum = " + bulbStringMaxNum + " ; maxGroup = " + maxGroup + " ； group = " + group);
            }
            if (group == 1) {
                lastRgbSet = new int[maxGroup * 3];
                lastBrightnessSet = new int[maxGroup * 3];
            }
            int[] rgb = groupColor.rgb;
            int[] brightness = groupColor.relativeBrightness;
            int destPos = Math.max(group - 1, 0) * 3;
            System.arraycopy(rgb, 0, lastRgbSet, destPos, rgb.length);
            System.arraycopy(brightness, 0, lastBrightnessSet, destPos, brightness.length);
            if (group == maxGroup) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventBulbStringColor() lastRgbSet = " + Arrays.toString(lastRgbSet));
                }
                /*读取球泡串颜色完成*/
                SubModeColorV2 subModeColor = new SubModeColorV2();
                subModeColor.rgbSet = lastRgbSet;
                subModeColor.brightnessSet = lastBrightnessSet;
                Mode mode = new Mode();
                mode.subMode = subModeColor;
                info.mode = mode;
                /*信息读取完成*/
                infoOver();
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    protected void readPartColor() {
        boolean supportSubModeColor4PartBrightness = Support.supportPartBrightness4Ble(info.versionSoft, info.versionHard);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "readPartColor() supportSubModeColor4PartBrightness = " + supportSubModeColor4PartBrightness);
        }
        if (supportSubModeColor4PartBrightness) {
            readBulbStringColorV2();
        }
    }

    /*读颜色带亮度*/
    private void readBulbStringColorV2() {
        int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
        int maxGroup = bulbStringMaxNum / 3 + (bulbStringMaxNum % 3 == 0 ? 0 : 1);
        AbsSingleController[] controllers = new AbsSingleController[maxGroup];
        for (int i = 0; i < maxGroup; i++) {
            controllers[i] = new BulbStringColorControllerV2(i + 1);
        }
        getBle().addController(controllers);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleDiy(EventMultipleDiy event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiy diyCode = " + diyCode + " result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            this.diyCode = diyCode;
            /*diy效果传输完成;通知设备设置成diy模式*/
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(diyCode);
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            this.diyCode = -1;
            /*diy效果传输失败，则通知传输失败*/
            EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
            getBle().clearControllers();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSleep(EventSleep event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSleep() result = " + result + " ; write = " + write);
        }
        if (result) {
            int enable = event.getEnable();
            int startBri = event.getStartBri();
            int curTime = event.getCurTime();
            int closeTime = event.getCloseTime();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventSleep() enable = " + enable + " ; startBri = " + startBri + " ; curTime = " + curTime + " ; closeTime = " + closeTime);
            }
            SleepInfo sleepInfo = new SleepInfo();
            sleepInfo.enable = enable;
            sleepInfo.startBri = startBri;
            sleepInfo.curTime = curTime;
            sleepInfo.closeTime = closeTime;
            sleepInfo.check();
            ext.sleepInfo = sleepInfo;
            SleepSucEvent.sendSleepSucEvent(write, sleepInfo);
        } else if (write) {
            SleepFailEvent.sendSleepFailEvent(true);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWakeUp(EventWakeUp event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventWakeUp() result = " + result + " ; write = " + write);
        }
        if (result) {
            int enable = event.getEnable();
            int endBri = event.getEndBri();
            byte repeat = event.getRepeat();
            int wakeHour = event.getWakeHour();
            int wakeMin = event.getWakeMin();
            int wakeTime = event.getWakeTime();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWakeUp() enable = " + enable + " ; endBri = " + endBri + " ; repeat = " + repeat + " ; wakeHour = " + wakeHour + " ; wakeMin = " + wakeMin + " ; wakeTime = " + wakeTime);
            }
            WakeUpInfo wakeUpInfo = new WakeUpInfo();
            wakeUpInfo.enable = enable;
            wakeUpInfo.endBri = endBri;
            wakeUpInfo.repeat = repeat;
            wakeUpInfo.wakeHour = wakeHour;
            wakeUpInfo.wakeMin = wakeMin;
            wakeUpInfo.wakeTime = wakeTime;
            wakeUpInfo.check();
            ext.wakeUpInfo = wakeUpInfo;
            WakeupSucEvent.sendWakeUpSucEvent(write, wakeUpInfo);
        } else if (write) {
            WakeupFailEvent.sendWakeupFailEvent(true);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventNewTimerV1(EventNewTimeV1 event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNewTimerV1()  write = " + write + " ; result = " + result);
        }
        if (result) {
            int group = event.getGroup();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventNewTimerV1() group = " + group);
            }
            List<Timer> timers = event.getTimers();
            if (group == 0xFF && timers.size() >= 4) {
                ext.timer1 = timers.get(0);
                ext.timer2 = timers.get(1);
                ext.timer3 = timers.get(2);
                ext.timer4 = timers.get(3);
            } else if (timers.size() > 0) {
                Timer timer = timers.get(0);
                if (group == 0) {
                    ext.timer1 = timer;
                } else if (group == 1) {
                    ext.timer2 = timer;
                } else if (group == 2) {
                    ext.timer3 = timer;
                } else if (group == 3) {
                    ext.timer4 = timer;
                }
            }
            TimerResultEvent.sendTimerResultEvent(write, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
        } else if (write) {
            TimerResultEvent.sendTimerResultEventFail(true);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }


    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventGradual(EventGradual event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventGradual() result = " + result + " ; write = " + write);
        }
        if (result) {
            int value = event.getValue();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventGradual() value = " + value);
            }
            ext.gradual = value;
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.effect_apply_all_times);
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.adjust_effect_apply_time);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventLightNum(EventLightNum event) {
        boolean result = event.isResult();
        if (result) {
            int num = event.getNum();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventLightNum() num = " + num);
            }
            ext.lightNum = num;
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    private int[] lastRgbSet;

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleNewScenes(EventMultiNewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleNewScenes() result = " + result + " ; scenesCode = " + scenesCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*场景效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*diy效果传输失败，则通知设置新场景模式失败*/
            getBle().clearControllers();
            opResult.bleWrite(event.getCommandType(), false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyTemplate4NewScenes(EventDiyTemplate4NewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        int diyCode = event.diyCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyTemplate4NewScenes() result = " + result + " ; scenesCode = " + scenesCode + " ; diyCode = " + diyCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            this.diyCode = diyCode;
            this.diyTemplateCode = scenesCode;
            /*diy模版效果传输完成，通知设备设置成场景模式*/
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            this.diyCode = -1;
            this.diyTemplateCode = -1;
            /*diy效果传输失败，则通知传输失败*/
            EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
            getBle().clearControllers();
        }
    }

    @Override
    protected void readExtDeviceInfoAfterInfoOver() {
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventOtaPrepare(EventOtaPrepare event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventOtaPrepare() result = " + result);
        }
        getBle().controllerEvent(event);
        OtaFlagV2.getInstance.onOtaPrepare(result);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultiNewDiyGraffiti(EventMultiNewDiyGraffiti event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultiNewDiyGraffiti() diyCode = " + diyCode + " ; result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            this.diyCode = diyCode;
            /*diy效果传输完成;通知设备设置成diy模式*/
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(diyCode);
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            this.diyCode = -1;
            /*diy效果传输失败，则通知传输失败*/
            EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
            getBle().clearControllers();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleMusic(EventMultipleMusic event) {
        boolean result = event.isResult();
        int sensitivity = event.getSensitivity();
        int subMusicCode = event.getSubMusicCode();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleMusic() result = " + result + " ; sensitivity = " + sensitivity + " ; subMusicCode = " + subMusicCode);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            /*新音乐模式效果传输完成-通知设备切换到音乐模式*/
            Mode mode = new Mode();
            mode.subMode = SubModeMusicV3.toNewSubModeMusic(sensitivity, subMusicCode);
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            /*新音乐效果传输失败*/
            EventNewMusicOpResult.sendEventNewMusicOpResult(false, subMusicCode);
            getBle().clearControllers();
        }
    }
}