package com.govee.dreamcolorlightv1.adjust.ui;


import com.govee.ui.R;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.mic.AbsMicByPhoneUiMode;
import com.govee.base2light.ble.mic.MicDj;
import com.govee.base2light.ble.mic.MicDynamic;
import com.govee.base2light.ble.mic.MicSoft;
import com.govee.base2light.light.AbsModeFragment;

import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.SubModeMusic;
import com.ihoment.base2app.infra.LogInfra;

/**
 * Create by DengFei on 2021-2-4
 * mic的uiMode
 */
public class MicUiMode extends AbsMicByPhoneUiMode {
    protected String TAG = "MicFragment";

    public MicUiMode(String sku,String device) {
        super(sku);
        this.device = device;
    }

    @Override
    public AbsModeFragment getUiFragment() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i("MicFragment", "MicUiMode sku device：" + getSku() + device);
        }
        MicFragment micFragment = new MicFragment();
        micFragment.makeArguments(getSku(),device);
        micFragment.setMicMode(new MicDj(), new MicDynamic(), new MicSoft());
        return micFragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_music_mini, R.mipmap.new_control_light_btb_mode_music_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_music;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeMusic subModeMusic = new SubModeMusic();
        subModeMusic.loadLocal();
        return subModeMusic;
    }
}
