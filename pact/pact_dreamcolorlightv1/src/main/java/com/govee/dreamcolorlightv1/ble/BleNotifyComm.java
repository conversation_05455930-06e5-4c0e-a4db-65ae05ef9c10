package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.DeviceStatusNotifyParse;
import com.govee.base2light.ble.comm.AbsNotify;
import com.govee.base2light.ble.comm.AbsNotifyParse;
import com.govee.base2light.ble.controller.MovieFeastOnOffNotifyParse;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Create by xieyingwu on 2019-07-31
 * ble主动通知notify
 */
public class BleNotifyComm extends AbsNotify {

    @Override
    protected List<AbsNotifyParse> getSupportNotify() {
        List<AbsNotifyParse> list = new ArrayList<>();
        list.add(new WifiNotifyParse());
        list.add(new DeviceStatusNotifyParse());
        list.add(new MovieFeastOnOffNotifyParse());
        list.add(new CheckIcNumParse());
        return list;
    }

    @Override
    public UUID getServiceUuid() {
        return BleComm.serviceUuid;
    }

}