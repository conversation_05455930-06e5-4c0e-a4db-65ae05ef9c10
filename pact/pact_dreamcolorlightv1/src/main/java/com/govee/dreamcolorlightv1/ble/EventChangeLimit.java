package com.govee.dreamcolorlightv1.ble;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-03-02
 * event-更改限流$
 */
public class EventChangeLimit {
    public boolean limitOpen;

    private EventChangeLimit() {
    }

    public static void sendEventChangeLimit(boolean limitOpen) {
        EventChangeLimit event = new EventChangeLimit();
        event.limitOpen = limitOpen;
        EventBus.getDefault().post(event);
    }
}