package com.govee.dreamcolorlightv1.add.v3;

import android.bluetooth.BluetoothDevice;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import com.govee.base2home.ac.ActivityMgr;
import com.govee.base2home.bean.CommonVideoParamBean;
import com.govee.base2home.custom.LoadingDialog;
import com.govee.base2home.device.IDeviceNet;
import com.govee.base2home.device.net.DeviceBindRequest;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.scenes.SecretKeyConfig;
import com.govee.base2home.videoplay.CommonVideoActivity;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventHardVersion;
import com.govee.base2light.ble.controller.EventIcNum;
import com.govee.base2light.ble.controller.EventSn;
import com.govee.base2light.ble.controller.EventSoftVersion;
import com.govee.base2light.ble.controller.EventWifiHardVersion;
import com.govee.base2light.ble.controller.EventWifiMac;
import com.govee.base2light.ble.controller.EventWifiSoftVersion;
import com.govee.base2light.ble.controller.HardVersionController;
import com.govee.base2light.ble.controller.IcNumController;
import com.govee.base2light.ble.controller.SnController;
import com.govee.base2light.ble.controller.SoftVersionController;
import com.govee.base2light.ble.controller.WifiHardVersionController;
import com.govee.base2light.ble.controller.WifiMacController;
import com.govee.base2light.ble.controller.WifiSoftVersionController;
import com.govee.base2light.neonlight.recommend.data.BleIotShare;
import com.govee.base2light.net.GuideVideoRequest;
import com.govee.base2light.net.GuideVideoResponse;
import com.govee.base2light.net.INet;
import com.govee.base2light.pact.AbsPairAc4SecretV1;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.bind.SafeBindMgr;
import com.govee.bind.bean.ConfirmGidReq;
import com.govee.bind.engine.BindDevServerAddInfoImpl;
import com.govee.bind.engine.ReadDeviceGid4Ble;
import com.govee.db.memory.ShortMemoryMgr;
import com.govee.dreamcolorlightv1.ConsV1;
import com.govee.dreamcolorlightv1.add.v2.AddInfoV2;
import com.govee.dreamcolorlightv1.add.v2.BindExtV2;
import com.govee.dreamcolorlightv1.add.v2.BindExtV2_1;
import com.govee.dreamcolorlightv1.add.v2.DeviceNameAcV2;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.kt.ui.device.FastConnectConfig;
import com.govee.matter.MatterManager;
import com.govee.matter.pact.MtPairProtocol;
import com.govee.matter.pair.MatterInfo;
import com.govee.ui.R;
import com.ihoment.base2app.Cache;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.network.ErrorResponse;
import com.ihoment.base2app.network.Network;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.JumpUtil;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2021/6/8
 * 配对流程$
 */
public class PairAcV1 extends AbsPairAc4SecretV1 {
    private static final String intent_ac_key_addInfo = "intent_ac_key_addInfo";
    private AddInfoV2 addInfo;
    private String videoUrl;

    /**
     * 跳转到配对页面
     *
     * @param ac
     * @param addInfo
     * @param bluetoothDevice
     */
    public static void jump2PairAc(Context ac, @NonNull AddInfoV2 addInfo, BluetoothDevice bluetoothDevice) {
        Bundle bundle = makeAcBundle(addInfo.sku, bluetoothDevice);
        bundle.putParcelable(intent_ac_key_addInfo, addInfo);
        JumpUtil.jump(ac, PairAcV1.class, bundle);
    }

    @Override
    protected void parseParams(Intent intent) {
        super.parseParams(intent);
        addInfo = intent.getParcelableExtra(intent_ac_key_addInfo);
    }

    @Override
    protected void finishAc() {
        finish();
    }

    @Override
    protected void readDeviceInfo() {
        AbsSingleController[] controllers;
        boolean supportBindWithIcNum = Support.supportBindWithIcNum(addInfo.goodsType, addInfo.pactType, addInfo.pactCode, addInfo.sku) || Support.supportIcFresh(addInfo.sku, addInfo.goodsType, addInfo.pactType, addInfo.pactCode);
        LogInfra.Log.i(TAG, "readDeviceInfo() supportBindWithIcNum = " + supportBindWithIcNum);
        if (supportBindWithIcNum) {
            controllers = new AbsSingleController[]{new SoftVersionController(), new HardVersionController(), new WifiSoftVersionController(), new WifiHardVersionController(), new WifiMacController(), new IcNumController(), new SnController(),};
        } else {
            controllers = new AbsSingleController[]{new SoftVersionController(), new HardVersionController(), new WifiSoftVersionController(), new WifiHardVersionController(), new WifiMacController(), new SnController(),};
        }
        getBle().startController(controllers);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSoftVersionEvent(EventSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "softVersion = " + softVersion);
            }
            addInfo.versionSoft = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onHardVersionEvent(EventHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "hardVersion = " + hardVersion);
            }
            addInfo.versionHard = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiSoftVersionEvent(EventWifiSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiSoftVersionEvent() softVersion = " + softVersion);
            }
            addInfo.wifiSoftVersion = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiHardVersionEvent(EventWifiHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiHardVersionEvent() hardVersion = " + hardVersion);
            }
            addInfo.wifiHardVersion = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWifiMacEvent(EventWifiMac event) {
        if (event.isResult()) {
            String mac = event.getMac();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiMacEvent() mac = " + mac);
            }
            addInfo.wifiMac = mac;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onIcNumEvent(EventIcNum event) {
        if (event.isResult()) {
            int icNum = event.icNum;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onIcNumEvent() icNum = " + icNum);
            }
            if (icNum > 0) {
                addInfo.icNum = icNum;
            }
        }
        getBle().controllerEvent(event);
    }

    /**
     * 设备matter信息
     */
    private MatterInfo matterPairInfo;

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSnEvent(EventSn event) {
        if (event.isResult()) {
            String device = event.getUuid();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "device = " + device);
            }
            addInfo.device = device;
        }
        getBle().controllerEvent(event);
        if (event.isResult()) {
            //获取matter信息
            if (MtPairProtocol.INSTANCE.supportMatter(addInfo.goodsType, addInfo.sku, addInfo.versionSoft, addInfo.versionHard, addInfo.wifiSoftVersion, addInfo.wifiHardVersion)) {
                MatterManager.Companion.instance().getMtInfo4AddOrUp(Ble.getInstance, new MatterManager.OnMtInfo4AddUpListener() {
                    @Override
                    public void getBaseInfo(boolean isSupportMatter, boolean hasPairNet, boolean hasAddedEcology) {
                        if (matterPairInfo == null) {
                            matterPairInfo = new MatterInfo();
                        }
                        matterPairInfo.setSupportMatter(isSupportMatter);
                        matterPairInfo.setHasPairNet(hasPairNet);
                        matterPairInfo.setHasAddedEcology(hasAddedEcology);
                    }

                    @Override
                    public void getGid(@NonNull String gid) {
                        if (matterPairInfo != null) {
                            matterPairInfo.setGid(gid);
                        }
                    }

                    @Override
                    public void getMatterId(@NonNull String matterId) {
                        if (matterPairInfo != null) {
                            matterPairInfo.setMatterId(matterId);
                        }
                    }

                    @Override
                    public void finish() {
                        bindDevice();
                    }
                });
            } else {
                //进行绑定操作
                bindDevice();
            }
        }
    }

    private void bindDevice() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "bindDevice()");
        }
        /*构建absDevice对象，用于绑定设备*/
        DeviceExtMode deviceExt = new DeviceExtMode();
        deviceExt.setLastDeviceData("{}");
        deviceExt.setDeviceSettings(makeDeviceSettingsJsonStr(addInfo.wifiMac));
        AbsDevice absDevice = new AbsDevice(addInfo.device, addInfo.sku, addInfo.versionHard, addInfo.versionSoft, addInfo.deviceName, addInfo.goodsType, deviceExt);
        BindDevServerAddInfoImpl bindImpl = new BindDevServerAddInfoImpl(this, absDevice, PairAcV1.this::makeDeviceSettingsJsonStr, bindResult -> {
            if (bindResult) {
                beBindSuc();
            } else {
                /*绑定失败*/
                exitPairProcessing();
            }
            return null;
        });
        SafeBindMgr.INSTANCE.bindDeviceGidCheck(
                new ReadDeviceGid4Ble(getBle()),
                ConfirmGidReq.createConfirmGidReq(absDevice, addInfo.wifiHardVersion, addInfo.wifiSoftVersion),
                addInfo.wifiMac,
                bindImpl,
                () -> {
                    // 直接绑定
                    bindDevice(absDevice);
                    return null;
                }
        );

    }

    private String makeDeviceSettingsJsonStr(String wifiMacFromGid) {
        if (!TextUtils.isEmpty(wifiMacFromGid)) {
            addInfo.wifiMac = wifiMacFromGid;
        }
        BindExtV2 bindExt;
        if (addInfo.icNum > 0) {
            bindExt = new BindExtV2_1(addInfo.pactType, addInfo.pactCode, addInfo.icNum);
        } else {
            bindExt = new BindExtV2(addInfo.pactType, addInfo.pactCode);
        }
        bindExt.deviceName = addInfo.deviceName;
        bindExt.bleName = addInfo.bleName;
        bindExt.address = addInfo.bleAddress;
        bindExt.wifiMac = addInfo.wifiMac;
        bindExt.secretCode = addInfo.secretCode;
        bindExt.wifiSoftVersion = addInfo.wifiSoftVersion;
        bindExt.wifiHardVersion = addInfo.wifiHardVersion;

        FastConnectConfig.checkSupportFastConnectNotNull(addInfo.goodsType, addInfo.versionSoft, addInfo.versionHard, addInfo.sku, support -> {
            bindExt.supportBleBroadV3 = support;
            ShortMemoryMgr.INSTANCE.getBleBroadVersionCache().putBleBroadV3Support(bluetoothDevice.getAddress(), bindExt.supportBleBroadV3);
            return null;
        });

        return JsonUtil.toJson(bindExt);
    }

    private void bindDevice(AbsDevice absDevice) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i("makeModel", "SecretKeyController上传 :" + JsonUtil.toJson(absDevice));
        }
        DeviceBindRequest request = new DeviceBindRequest(transactions.createTransaction(), absDevice);
        //5.6.0-->支持matter的设备新增
        if (matterPairInfo != null && matterPairInfo.hasMatterInfo()) {
            //有gid,matterId传1，其他默认传0
            request.bindVersion = 1;
            request.gid = matterPairInfo.getGid();
            request.matterId = matterPairInfo.getMatterId();
        }
        Cache.get(IDeviceNet.class).bindDevice(request).enqueue(new Network.IHCallBack<>(request));
    }

    @Override
    protected void beBindSuc() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "beBindSuc()");
        }
        getGuideVideoUrl();
    }

    @Override
    protected void saveSecretKey(String secretCode) {
        /*读操作；获取成功密钥，存储密钥*/
        addInfo.secretCode = secretCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "secretCode = " + addInfo.secretCode);
        }
        String address = bluetoothDevice.getAddress();
        SecretKeyConfig.read().saveSecretKey(address, addInfo.secretCode);
    }

    @Override
    protected int getPairIconRes() {
        return Support.pairRes(addInfo.goodsType, addInfo.sku);
    }

    @Override
    protected void rebind() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "rebind()");
        }
        bindDevice();
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected String getTipsStr() {
        return ResUtil.getString(R.string.b2light_press_hint);
    }

    private void getGuideVideoUrl() {
        /*统一绑定成功后都请求一下引导视频链接请求*/
        LoadingDialog.createDialog(this).show();
        GuideVideoRequest request = new GuideVideoRequest(transactions.createTransaction(), 2, addInfo.sku);
        Cache.get(INet.class).getVideoUrl(request.type, request.sku).enqueue(new Network.IHCallBack<>(request));
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onGetVideoUrlResponse(GuideVideoResponse response) {
        if (!transactions.isMyTransaction(response)) return;
        LoadingDialog.hideDialog();
        this.videoUrl = response.data.videoUrl;
        jump2Next();
    }


    @Override
    public void onErrorResponse(ErrorResponse response) {
        super.onErrorResponse(response);
        if (response.request instanceof GuideVideoRequest) {
            LoadingDialog.hideDialog();
            jump2Next();
        }
    }

    private void jump2Next() {
        ActivityMgr.getInstance().finishAllExceptMain();
        destroy();
        boolean supportLocalShapeRecommend = Support.supportRecommendedShapeLocal(addInfo.sku);
        if (!TextUtils.isEmpty(videoUrl) && !supportLocalShapeRecommend) {
            Bundle bundle = new Bundle();
            bundle.putParcelable(ConsV1.intent_ac_key_addInfo, addInfo);

            CommonVideoParamBean bean = new CommonVideoParamBean.Build().videoUrl(videoUrl).skuArray(new String[]{addInfo.sku}).contentStr(ResUtil.getString(R.string.hint_watch_video)).canBack(false).clazz(DeviceNameAcV2.class).extraData(bundle).needFinishPage(false).build();
            CommonVideoActivity.Companion.jump2CommonVideoActivity(this, bean);
        } else {
            if (supportLocalShapeRecommend) {
                int[] wifiInputLimit = Support.getWifiInputLimit();
                //跳转霓虹灯推荐列表用到
                BleIotShare.INSTANCE.setBle(getBle());
                BleIotShare.INSTANCE.setInfo(getBleIotInfo(addInfo));
                BleIotShare.INSTANCE.setAdjustAcClassName(addInfo.adjustAcClassName);
                BleIotShare.INSTANCE.setWifiSsidInputLimit(wifiInputLimit[0]);
                BleIotShare.INSTANCE.setWifiPasswordInputLimit(wifiInputLimit[1]);
            }
            DeviceNameAcV2.jump2DeviceNameAcV2(this, addInfo);
        }
    }

    private BleIotInfo getBleIotInfo(AddInfoV2 addInfo) {
        BleIotInfo info = new BleIotInfo(
                addInfo.sku,
                addInfo.goodsType,
                addInfo.device,
                "",
                addInfo.deviceName,
                addInfo.bleName,
                addInfo.bleAddress,
                addInfo.wifiMac,
                "",
                addInfo.versionHard,
                addInfo.secretCode
        );
        info.versionSoft = addInfo.versionSoft;
        info.pactType = addInfo.pactType;
        info.pactCode = addInfo.pactCode;
        info.ic = addInfo.icNum;
        return info;
    }
}
