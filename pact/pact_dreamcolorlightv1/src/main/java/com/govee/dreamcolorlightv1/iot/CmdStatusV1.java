package com.govee.dreamcolorlightv1.iot;

import android.text.TextUtils;

import com.govee.base2home.Constant;
import com.govee.base2light.ac.AbsIotManagerV1;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.timer.Timer;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.NewTimerV1Controller;
import com.govee.base2light.ble.controller.OnOffMemoryController;
import com.govee.base2light.ble.controller.SleepController;
import com.govee.base2light.ble.controller.WakeUpController;
import com.govee.base2light.kt.general_controller.ControllerEyeshadow;
import com.govee.base2light.lowBlueLightControl.LowBlueLightConfig;
import com.govee.dreamcolorlightv1.ble.BleParser;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ReadIcController;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeColorV3;
import com.govee.dreamcolorlightv1.ble.SubModeColorV4;
import com.govee.dreamcolorlightv1.ble.SubModeColorV6;
import com.govee.dreamcolorlightv1.ble.SubModeColorV8;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JsonUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import androidx.annotation.Keep;

/**
 * Create by xieyingwu on 2020/5/13
 * cmd=status的iotV1版本$
 */
public class CmdStatusV1 {
    private static final String TAG = "CmdStatusV1";
    public boolean on;/*开关*/
    public int brightness;/*亮度*/
    public Mode mode;/*模式*/
    public String softVersion;/*蓝牙软件版本*/

    public Timer timer1 = new Timer();
    public Timer timer2 = new Timer();
    public Timer timer3 = new Timer();
    public Timer timer4 = new Timer();
    public WakeUpInfo wakeUpInfo = new WakeUpInfo();/*唤醒*/
    public SleepInfo sleepInfo = new SleepInfo();/*睡眠*/
    public int powerOffMemory = 0;
    public int[] icResult;//612x ic数
    public boolean isLowBlueLightOpen = false;
    public int lowBlueLightValue = 1;


    public static CmdStatusV1 parseJsonWithBrightness(int goodsType, String sku, int pactType, int pactCode, String softVersion, String json) {
        if (TextUtils.isEmpty(json)) return null;
        CmdStatusV1 statusV1 = new CmdStatusV1();
        statusV1.softVersion = softVersion;
        String stateJsonStr = AbsIotManagerV1.getJsonObjectStr(json, Cmd.parse_json_key_state);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "stateJsonStr = " + stateJsonStr);
        }
        /*解析state字段内参数*/
        State state = JsonUtil.fromJson(stateJsonStr, State.class);
        if (state != null) {
            statusV1.on = state.onOff == 1;
            statusV1.brightness = state.brightness;
        }
        /*解析op字段内参数*/
        String opJsonStr = AbsIotManagerV1.getJsonObjectStr(json, Cmd.parse_json_op);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "opJsonStr = " + opJsonStr);
        }
        ResultPtV1 op = JsonUtil.fromJson(opJsonStr, ResultPtV1.class);
        if (op != null) {
            List<byte[]> ptBytes = op.getPtBytes();
            if (ptBytes != null && !ptBytes.isEmpty()) {
                /*解析模式*/
                Mode mode = parseStatusPtModeV1(goodsType, sku, pactType, pactCode, ptBytes);
                if (mode != null) {
                    statusV1.mode = mode;
                }
                /*解析定时*/
                List<Timer> timers = parsePtTimer(ptBytes);
                if (timers != null && !timers.isEmpty()) {
                    int index = 0;
                    for (Timer timer : timers) {
                        if (index == 0) {
                            statusV1.timer1 = timer;
                        } else if (index == 1) {
                            statusV1.timer2 = timer;
                        } else if (index == 2) {
                            statusV1.timer3 = timer;
                        } else if (index == 3) {
                            statusV1.timer4 = timer;
                        }
                        index++;
                    }
                }
                /*解析睡眠*/
                WakeUpInfo wakeUpInfo = parsePtWakeUp(goodsType, sku, pactType, pactCode, ptBytes);
                if (wakeUpInfo != null) {
                    statusV1.wakeUpInfo = wakeUpInfo;
                }
                /*解析唤醒*/
                SleepInfo sleepInfo = parsePtSleep(goodsType, sku, pactType, pactCode, ptBytes);
                if (sleepInfo != null) {
                    statusV1.sleepInfo = sleepInfo;
                }
                boolean supportPowerOffMemory = Support.supportPowerOffMemory(goodsType, sku, pactType, pactCode);
                if (supportPowerOffMemory) {
                    int[] powerOffMemoryResult = OnOffMemoryController.parsePowerOffMemory(ptBytes);
                    if (powerOffMemoryResult[0] == 1) {
                        statusV1.powerOffMemory = powerOffMemoryResult[1];
                    }
                }
                boolean supportChangeIcPieces = Support.goodsTypeH612x(goodsType);
                if (supportChangeIcPieces) {
                    statusV1.icResult = ReadIcController.parseIc(ptBytes);
                }
                boolean supportLowBlueLight = LowBlueLightConfig.Companion.checkSkuSupport(goodsType, sku, softVersion);
                if (supportLowBlueLight) {
                    ControllerEyeshadow.EyeshadowResult redBlueLight = ControllerEyeshadow.Companion.parseRedBlueLight(ptBytes);
                    if (redBlueLight != null) {
                        statusV1.isLowBlueLightOpen = redBlueLight.getSwitch();
                        statusV1.lowBlueLightValue = redBlueLight.getBlue();
                    }
                }
            }
        }
        return statusV1;
    }

    /**
     * 解析status内的透传指令的模式信息
     *
     * @param ptBytes
     * @return
     */
    public static Mode parseStatusPtModeV1(int goodsType, String sku, int pactType, int pactCode, List<byte[]> ptBytes) {
        if (ptBytes == null || ptBytes.isEmpty()) return null;
        List<byte[]> modeBytes = new ArrayList<>();
        boolean isColorMode = false;
        for (byte[] ptByte : ptBytes) {
            if (ptByte != null && ptByte.length == 20) {
                byte op = ptByte[1];
                if (op == BleProtocolConstants.SINGLE_MODE) {
                    /*加入模式包*/
                    modeBytes.add(ptByte);
                    /*若当前是颜色模式，则后续紧跟着的是颜色参数*/
                    byte subModeByte = ptByte[2];
                    if (subModeByte == BleProtocol.sub_mode_color_v2 || subModeByte == BleProtocol.sub_mode_color) {
                        isColorMode = true;
                        continue;
                    } else {
                        break;
                    }
                }
                /*若当前是颜色模式，则接着解析颜色数据*/
                if (isColorMode) {
                    if (op == BleProtocolConstants.MSG_READ_BULB_LIGHT_COLOR_WITH_BRIGHTNESS || op == BleProtocol.MSG_TYPE_READ_BULB_COLOR) {
                        modeBytes.add(ptByte);
                    } else {
                        break;
                    }
                }
            }
        }
        if (modeBytes.isEmpty()) return null;
        byte[] mode20Bytes = modeBytes.remove(0);
        Mode mode = new Mode();
        mode.parse(BleUtil.parseValidBleBytes(mode20Bytes));

        if (isColorMode) {
            /*解析是否是旧的颜色模式*/
            byte subModeCommandType = mode20Bytes[2];
            // TODO: 2023/12/25 解析h612abcf 
            if (subModeCommandType == BleProtocol.sub_mode_color) {
                /*解析渐变值*/
                SubModeColor subModeColor = new SubModeColor();
                subModeColor.parse(BleUtil.parseValidBleBytes4SubMode(mode20Bytes));
                /*解析各个IC的色值*/
                subModeColor.rgbSet = BleParser.parseColorModeColors(modeBytes);
                mode.subMode = subModeColor;
            } else {
                boolean subModeColorPiece10 = Support.isSubModeColorPiece10(goodsType);
                boolean subModeColorPiece12 = Support.isSubModeColorPiece12(goodsType);
                boolean subModeColorPiece20 = Support.isSubModeColorPiece20(goodsType);
                boolean h612de = Support.supportChangePieces20(goodsType, sku);
                boolean h612abcf = Support.goodsTypeH612x(goodsType) && !h612de;
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "parseStatusPtModeV1() subModeColorPiece10 = " + subModeColorPiece10 + " ; subModeColorPiece12 = " + subModeColorPiece12 + " ; subModeColorPiece20 = " + subModeColorPiece20);
                }
                ISubMode subMode;
                if (subModeColorPiece20 || h612de) {
                    SubModeColorV6 subModeColor = new SubModeColorV6();
                    subModeColor.parse(BleUtil.parseValidBleBytes4SubMode(mode20Bytes));
                    /*判断读取灯串颜色的指令-0xa5*/
                    if (!modeBytes.isEmpty()) {
                        /*解析各个IC的色值和亮度*/
                        subModeColor.rgbSet = BleParser.parseColorOnePkgLen4(modeBytes);
                        LogInfra.Log.i(TAG, "parseStatusPtModeV1()rgbSet = " + Arrays.toString(subModeColor.rgbSet));
                        subModeColor.brightnessSet = BleParser.parseBrightnessOnePkgLen4(modeBytes);
                        LogInfra.Log.i(TAG, "parseStatusPtModeV1()brightnessSet = " + Arrays.toString(subModeColor.rgbSet));
                    }
                    subMode = subModeColor;
                } else if (h612abcf) {
                    SubModeColorV8 subModeColor = new SubModeColorV8();
                    subModeColor.parse(BleUtil.parseValidBleBytes4SubMode(mode20Bytes));
                    /*判断读取灯串颜色的指令-0xa5*/
                    if (!modeBytes.isEmpty()) {
                        /*解析各个IC的色值和亮度*/
                        subModeColor.setRgbSet(BleParser.parseColorOnePkgLen4(modeBytes));
                        subModeColor.setBrightnessSet(BleParser.parseBrightnessOnePkgLen4(modeBytes));
                        LogInfra.Log.i(TAG, "parseStatusPtModeV1()rgbSet = " + Arrays.toString(subModeColor.getRgbSet()));
                        LogInfra.Log.i(TAG, "parseStatusPtModeV1()brightnessSet = " + Arrays.toString(subModeColor.getBrightnessSet()));
                    }
                    subMode = subModeColor;
                } else if (subModeColorPiece12) {
                    SubModeColorV4 subModeColor = new SubModeColorV4();
                    subModeColor.parse(BleUtil.parseValidBleBytes4SubMode(mode20Bytes));
                    /*判断读取灯串颜色的指令-0xa2还是0xa4*/
                    if (!modeBytes.isEmpty()) {
                        byte[] bytes = modeBytes.get(0);
                        if (bytes[1] == BleProtocol.MSG_TYPE_READ_BULB_COLOR) {
                            subModeColor.rgbSet = BleParser.parseColorModeColors(modeBytes);
                        } else {
                            /*解析各个IC的色值*/
                            subModeColor.rgbSet = BleParser.parseColorModeColorsWithBrightness(modeBytes);
                            subModeColor.brightnessSet = BleParser.parseColorModeColors4Brightness(modeBytes);
                        }
                    }
                    subMode = subModeColor;
                } else if (subModeColorPiece10) {
                    /*解析渐变值*/
                    SubModeColorV3 subModeColor = new SubModeColorV3();
                    subModeColor.parse(BleUtil.parseValidBleBytes4SubMode(mode20Bytes));
                    /*判断读取灯串颜色的指令-0xa2还是0xa4*/
                    if (!modeBytes.isEmpty()) {
                        byte[] bytes = modeBytes.get(0);
                        if (bytes[1] == BleProtocol.MSG_TYPE_READ_BULB_COLOR) {
                            subModeColor.rgbSet = BleParser.parseColorModeColors(modeBytes);
                        } else {
                            boolean supportRgbWithBrightnessOnePkg4Colors = Support.supportRgbWithBrightnessOnePkg4Colors(goodsType, pactType, pactCode);
                            LogInfra.Log.i(TAG, "parseStatusPtModeV1() supportRgbWithBrightnessOnePkg4Colors = " + supportRgbWithBrightnessOnePkg4Colors);
                            if (supportRgbWithBrightnessOnePkg4Colors) {
                                /*解析各个IC的色值*/
                                subModeColor.rgbSet = BleParser.parseColorOnePkgLen4(modeBytes);
                                subModeColor.brightnessSet = BleParser.parseBrightnessOnePkgLen4(modeBytes);
                            } else {
                                /*解析各个IC的色值*/
                                subModeColor.rgbSet = BleParser.parseColorModeColorsWithBrightness(modeBytes);
                                subModeColor.brightnessSet = BleParser.parseColorModeColors4Brightness(modeBytes);
                            }
                        }
                    }
                    subMode = subModeColor;
                } else {
                    /*解析渐变值*/
                    SubModeColorV2 subModeColor = new SubModeColorV2();
                    subModeColor.parse(BleUtil.parseValidBleBytes4SubMode(mode20Bytes));
                    boolean needParseColorStrip = true;
                    if (Support.isColorTemRealDevice(goodsType)) {
                        /*若是真色温设备，读取颜色模式时若当前灯带在色温展示下，则无需读取分段颜色，都是色温值,亮度都是100*/
                        int realKelvinByRead = subModeColor.realKelvinByRead;
                        if (realKelvinByRead > 0) {
                            int[] temColorByKelvin = Constant.getTemColorByKelvin(realKelvinByRead);
                            if (temColorByKelvin[0] == 1) {
                                int temColor = temColorByKelvin[2];
                                subModeColor.rgbSet = new int[15];
                                Arrays.fill(subModeColor.rgbSet, temColor);
                                subModeColor.brightnessSet = new int[15];
                                Arrays.fill(subModeColor.brightnessSet, 100);
                                needParseColorStrip = false;
                            }
                        }
                    }
                    /*判断读取灯串颜色的指令-0xa2还是0xa4*/
                    if (needParseColorStrip && !modeBytes.isEmpty()) {
                        byte[] bytes = modeBytes.get(0);
                        if (bytes[1] == BleProtocol.MSG_TYPE_READ_BULB_COLOR) {
                            subModeColor.rgbSet = BleParser.parseColorModeColors(modeBytes);
                        } else {
                            boolean supportRgbWithBrightnessOnePkg4Colors = Support.supportRgbWithBrightnessOnePkg4Colors(goodsType, pactType, pactCode);
                            LogInfra.Log.i(TAG, "parseStatusPtModeV1() supportRgbWithBrightnessOnePkg4Colors = " + supportRgbWithBrightnessOnePkg4Colors);
                            if (supportRgbWithBrightnessOnePkg4Colors) {
                                /*解析各个IC的色值*/
                                subModeColor.rgbSet = BleParser.parseColorOnePkgLen4(modeBytes);
                                subModeColor.brightnessSet = BleParser.parseBrightnessOnePkgLen4(modeBytes);
                            } else {
                                /*解析各个IC的色值*/
                                if (Support.isH80B5(goodsType, sku) || Support.isH61B5(goodsType, sku) || Support.isGoodsTypeH61A9(goodsType)) {
                                    subModeColor.rgbSet = BleParser.parseColorOnePkgLen4(modeBytes);
                                    subModeColor.brightnessSet = BleParser.parseBrightnessOnePkgLen4(modeBytes);
                                } else {
                                    subModeColor.rgbSet = BleParser.parseColorModeColorsWithBrightness(modeBytes);
                                    subModeColor.brightnessSet = BleParser.parseColorModeColors4Brightness(modeBytes);
                                }
                            }
                        }
                    }
                    subMode = subModeColor;
                }
                mode.subMode = subMode;
            }
        }
        return mode;
    }

    /**
     * 解析颜色透传设置整段的分段和渐变
     *
     * @param subModeColorVersion
     * @param ptBytes
     * @return
     */
    public static Mode parsePtRealColorModeV1(int subModeColorVersion, List<byte[]> ptBytes, String sku, int goodsType, int pactType, int pactCode) {
        if (ptBytes == null || ptBytes.isEmpty()) return null;
        List<byte[]> colorBytes = new ArrayList<>();
        byte[] gradualBytes = null;
        byte[] readModeBytes = null;
        boolean colorTemRealDevice = Support.isColorTemRealDevice(goodsType);
        for (byte[] ptByte : ptBytes) {
            if (ptByte != null && ptByte.length == 20) {
                byte op = ptByte[1];
                if (op == BleProtocol.MSG_TYPE_READ_BULB_COLOR || op == BleProtocolConstants.MSG_READ_BULB_LIGHT_COLOR_WITH_BRIGHTNESS) {
                    /*解析到色值*/
                    colorBytes.add(ptByte);
                } else if (op == BleProtocol.SINGLE_GRADUAL_CHANGE_4_WIFI_BLE) {
                    /*解析到渐变*/
                    gradualBytes = ptByte;
                    break;
                } else if (colorTemRealDevice && op == BleProtocolConstants.SINGLE_MODE && ptByte[0] == BleProtocolConstants.SINGLE_READ) {
                    readModeBytes = ptByte;
                }
            }
        }
        if (colorBytes.isEmpty()) return null;
        ISubMode subMode;
        if (subModeColorVersion == 0) {
            SubModeColor subModeColor = new SubModeColor();
            subModeColor.rgbSet = BleParser.parseColorModeColors(colorBytes);
            if (gradualBytes != null) {
                int gradual = Gradual4BleWifiController.parseGradual(gradualBytes);
                if (gradual != -1) {
                    subModeColor.gradual = gradual;
                }
            }
            subMode = subModeColor;
        } else if (subModeColorVersion == 1) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            boolean needParseColorSet = true;
            if (colorTemRealDevice && readModeBytes != null) {
                byte[] validBytes = new byte[16];
                System.arraycopy(readModeBytes, 3, validBytes, 0, validBytes.length);
                subModeColor.parse(validBytes);
                if (subModeColor.realKelvinByRead > 0) {
                    int[] temColorByKelvin = Constant.getTemColorByKelvin(subModeColor.realKelvinByRead);
                    if (temColorByKelvin[0] == 1) {
                        needParseColorSet = false;
                        int temColor = temColorByKelvin[2];
                        subModeColor.rgbSet = new int[15];
                        Arrays.fill(subModeColor.rgbSet, temColor);
                        subModeColor.brightnessSet = new int[15];
                        Arrays.fill(subModeColor.brightnessSet, 100);
                    }
                }
            }
            if (needParseColorSet) {
                subModeColor.rgbSet = BleParser.parseColorModeColors(colorBytes);
            }
            if (gradualBytes != null) {
                int gradual = Gradual4BleWifiController.parseGradual(gradualBytes);
                if (gradual != -1) {
                    subModeColor.gradual = gradual;
                }
            }
            subMode = subModeColor;
        } else if (subModeColorVersion == 2) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            boolean supportRgbWithBrightnessOnePkg4Colors = Support.supportRgbWithBrightnessOnePkg4Colors(goodsType, pactType, pactCode);
            if (supportRgbWithBrightnessOnePkg4Colors) {
                subModeColor.rgbSet = BleParser.parseColorOnePkgLen4(colorBytes);
                subModeColor.brightnessSet = BleParser.parseBrightnessOnePkgLen4(colorBytes);
            } else {
                if (Support.isGoodsTypeH61A9(goodsType) || Support.isH61B5(goodsType, sku) || Support.isH80B5(goodsType, sku)) {
                    subModeColor.rgbSet = BleParser.parseColorOnePkgLen4(colorBytes);
                    subModeColor.brightnessSet = BleParser.parseBrightnessOnePkgLen4(colorBytes);
                } else {
                    /*解析各个IC的色值*/
                    subModeColor.rgbSet = BleParser.parseColorModeColorsWithBrightness(colorBytes);
                    subModeColor.brightnessSet = BleParser.parseColorModeColors4Brightness(colorBytes);
                }
            }
            if (gradualBytes != null) {
                int gradual = Gradual4BleWifiController.parseGradual(gradualBytes);
                if (gradual != -1) {
                    subModeColor.gradual = gradual;
                }
            }
            subMode = subModeColor;
        } else if (subModeColorVersion == 3) {
            SubModeColorV3 subModeColor = new SubModeColorV3();
            /*解析各个IC的色值*/
            subModeColor.rgbSet = BleParser.parseColorModeColorsWithBrightness(colorBytes);
            subModeColor.brightnessSet = BleParser.parseColorModeColors4Brightness(colorBytes);
            if (gradualBytes != null) {
                int gradual = Gradual4BleWifiController.parseGradual(gradualBytes);
                if (gradual != -1) {
                    subModeColor.gradual = gradual;
                }
            }
            subMode = subModeColor;
        } else if (subModeColorVersion == 4) {
            SubModeColorV4 subModeColor = new SubModeColorV4();
            /*解析各个IC的色值 */
            subModeColor.rgbSet = BleParser.parseColorModeColorsWithBrightness(colorBytes);
            subModeColor.brightnessSet = BleParser.parseColorModeColors4Brightness(colorBytes);
            subMode = subModeColor;
        } else if (subModeColorVersion == 5) {
            SubModeColorV6 subModeColor = new SubModeColorV6();
            /*解析各个IC的色值 */
            subModeColor.rgbSet = BleParser.parseColorOnePkgLen4(colorBytes);
            subModeColor.brightnessSet = BleParser.parseBrightnessOnePkgLen4(colorBytes);
            if (gradualBytes != null) {
                int gradual = Gradual4BleWifiController.parseGradual(gradualBytes);
                if (gradual != -1) {
                    subModeColor.gradual = gradual;
                }
            }
            subMode = subModeColor;
        } else if (subModeColorVersion == 6) {
            SubModeColorV8 subModeColor = new SubModeColorV8();
            /*解析各个IC的色值 */
            subModeColor.setRgbSet(BleParser.parseColorOnePkgLen4(colorBytes));
            subModeColor.setBrightnessSet(BleParser.parseBrightnessOnePkgLen4(colorBytes));
            if (gradualBytes != null) {
                int gradual = Gradual4BleWifiController.parseGradual(gradualBytes);
                if (gradual != -1) {
                    subModeColor.setGradual(gradual);
                }
            }
            subMode = subModeColor;
        } else {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "parsePtRealColorModeV1() subModeColorVersion = " + subModeColorVersion + " ; 暂不支持该subModeColor解析");
            }
            return null;
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        return mode;
    }

    private static SleepInfo parsePtSleep(int goodsType, String sku, int pactType, int pactCode, List<byte[]> ptBytes) {
        if (ptBytes == null || ptBytes.isEmpty()) return null;
        for (byte[] ptByte : ptBytes) {
            if (ptByte != null && ptByte.length == 20) {
                byte op = ptByte[1];
                if (op == BleProtocolConstants.SINGLE_SLEEP) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parsePtSleep()");
                    }
                    if (Support.supportTimerWithColorConfig(goodsType, sku, pactType, pactCode)) {
                        return SleepInfo.parseBytesOnlyWithColor4Original20Bytes(ptByte);
                    }
                    return SleepController.parseSleep(ptByte);
                }
            }
        }
        return null;
    }

    private static WakeUpInfo parsePtWakeUp(int goodsType, String sku, int pactType, int pactCode, List<byte[]> ptBytes) {
        if (ptBytes == null || ptBytes.isEmpty()) return null;
        for (byte[] ptByte : ptBytes) {
            if (ptByte != null && ptByte.length == 20) {
                byte op = ptByte[1];
                if (op == BleProtocolConstants.SINGLE_WAKEUP) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parsePtWakeUp()");
                    }
                    if (Support.supportTimerWithColorConfig(goodsType, sku, pactType, pactCode)) {
                        return WakeUpInfo.parseBytesOnlyWithColor4Original20Bytes(ptByte);
                    }
                    return WakeUpController.parse2WakeUp(ptByte);
                }
            }
        }
        return null;
    }

    private static List<Timer> parsePtTimer(List<byte[]> ptBytes) {
        if (ptBytes == null || ptBytes.isEmpty()) return null;
        for (byte[] ptByte : ptBytes) {
            if (ptByte != null && ptByte.length == 20) {
                byte op = ptByte[1];
                if (op == BleProtocolConstants.SINGLE_NEW_TIME_V1) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parsePtTimer()");
                    }
                    return NewTimerV1Controller.parseAllTimer(ptByte);
                }
            }
        }
        return null;
    }

    @Keep
    static class State {
        public int onOff;
        public int brightness;
        public int mode;
    }

    /**
     * 解析ic
     *
     * @param ptBytes
     * @return result[2]-[0-是否解析成功:0-没有解析到；1-解析到][1-ic具体数值]
     */
    public static int[] parseIc(List<byte[]> ptBytes) {
        int[] result = new int[]{0, 0};
        if (ptBytes != null && !ptBytes.isEmpty()) {
            for (byte[] ptByte : ptBytes) {
                if (ptByte[0] == BleProtocolConstants.NOTIFY
                        && ptByte[1] == BleProtocol.SINGLE_NOTIFY_IC) {
                    int icNum = BleUtil.getSignedShort(ptByte[2], ptByte[3]);
                    LogInfra.Log.i(TAG, "parseIc() icNum = " + icNum);
                    result[0] = 1;
                    result[1] = icNum;
                    return result;
                }
            }
        }
        return result;
    }

}