package com.govee.dreamcolorlightv1.adjust.ui;


import com.govee.base2light.ac.music.AbsMusicConfig;
import com.govee.base2light.ac.music.MusicApplyM;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.SubModeAbsMusic;
import com.govee.base2light.ble.mic.MicDj;
import com.govee.base2light.ble.mic.MicDynamic;
import com.govee.base2light.ble.mic.MicSoft;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.light.v1.absmusic.viewmodel.MusicViewModel;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV3;
import com.ihoment.base2app.infra.LogInfra;

import androidx.fragment.app.FragmentActivity;
import androidx.lifecycle.ViewModelProvider;

/**
 * Create by DengFei on 2021-2-4
 * mic的uiMode
 */
public class MicUiModeV3 extends MicUiMode {
    protected String TAG = "MicFragment";
    private final MusicViewModel musicViewModel;

    public MicUiModeV3(FragmentActivity ac, String sku, String device) {
        super(sku, device);
        musicViewModel = new ViewModelProvider(ac).get(MusicViewModel.class);

    }

    @Override
    public AbsModeFragment getUiFragment() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i("MicFragment", "MicUiMode sku device：" + getSku() + device);
        }
        MicFragmentV1 micFragment = new MicFragmentV1();
        micFragment.makeArguments(getSku(), device);
        micFragment.setMicMode(new MicDj(), new MicDynamic(), new MicSoft());
        return micFragment;
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

    @Override
    public ISubMode getSubMode() {
        if (AbsMusicConfig.Companion.read().getLastAbsMusic(getSku(), device)) {
            int musicCode = MusicApplyM.INSTANCE.myMusicListContainsLastMusicCode(getSku(), device, musicViewModel.getMusicList().getValue());
            if (musicCode != -1) {
                SubModeAbsMusic subModeAbsMusic = new SubModeAbsMusic();
                subModeAbsMusic.setMusicCode(musicCode);
                return subModeAbsMusic;
            }
        }
        SubModeMusicV3 subModeMusic = new SubModeMusicV3();
        subModeMusic.loadLocal();
        return subModeMusic;
    }

    @Override
    public byte[] subModeCommandTypeArray() {
        return new byte[]{BleProtocol.sub_mode_music, BleProtocolConstants.sub_mode_abs_music};
    }
}