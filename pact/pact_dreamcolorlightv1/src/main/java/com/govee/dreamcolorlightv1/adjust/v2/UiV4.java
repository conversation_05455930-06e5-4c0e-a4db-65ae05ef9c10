package com.govee.dreamcolorlightv1.adjust.v2;

import com.govee.base2home.util.Encode;
import com.govee.base2light.ac.music.EventApplyAbsMusic;
import com.govee.base2light.ac.music.MusicApplyM;
import com.govee.base2light.ble.controller.MusicComposeController;
import com.govee.base2light.lowBlueLightControl.ControllerLowBlueLight;
import com.govee.base2light.lowBlueLightControl.EventLowBlueLight;
import com.govee.base2light.lowBlueLightControl.EventLowBlueLightResult;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.IUiResult4BleIot;
import com.govee.dreamcolorlightv1.iot.CmdPtReal;
import com.govee.dreamcolorlightv1.iot.ICmdPtRealOpResult;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;
import com.zhy.android.percent.support.PercentRelativeLayout;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2022/5/12
 * rgbic支持盛宴主设备高端灯带
 */
class UiV4 extends UiV3 {
    String TAG = "UiV4";

    public UiV4(IUiResult4BleIot uiResult, BleIotInfo info) {
        super(uiResult, info);
    }

    @Override
    public boolean isSupportProtocol(int pactType, int pactCode) {
        return Support.isUiV4bleIotProtocol(pactType, pactCode)
                || Support.isUiV4bleIotProtocol4FreshIc(pactType, pactCode)
                || Support.isUi4BleIotProtocol4H61BC(pactType, pactCode)
                || Support.isUi4BleIotProtocol4H6175(pactType, pactCode)
                || Support.isUi4BleIotProtocol4H61BE(pactType, pactCode);
    }

    @Override
    public void layout(@NonNull AppCompatActivity ac, @NonNull PercentRelativeLayout contentParent, int headerId) {
        super.layout(ac, contentParent, headerId);
        /*通知是否支持设置页是否支持ic刷新*/
        EventOpFreshIcResult.sendEventOpFreshIcResult(EventOpFreshIcResult.RESULT_TYPE_SUPPORT, supportIcFresh());
    }

    @Override
    protected boolean supportFeast() {
        return Support.supportMusicFeast(info.goodsType, info.sku);
    }

    @Override
    protected boolean supportIcFresh() {
        return Support.supportIcFresh(info.sku, info.goodsType, info.pactType, info.pactCode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventApplyAbsMusic(EventApplyAbsMusic event) {
        SafeLog.Companion.i(TAG, () -> "onEventApplyAbsMusic() effectStr = " + event.effectStr + " musicId = " + event.musicId);
        byte[] effectBytes = Encode.decryByBase64(event.effectStr);
        if (effectBytes == null || effectBytes.length == 0) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "onEventApplyAbsMusic() 音乐效果字符串无法转换成字节数组");
            }
            return;
        }
        MusicComposeController controller = MusicApplyM.INSTANCE.createAbsMusicController(event.effectStr, event.musicId);
        if (controller == null) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "onEventApplyAbsMusic() controller == null");
            }
            return;
        }
        modeUI.setModeChanged(true); //快照
        if (bleOp.isOpCommEnable()) {
            showLoading();
            bleOp.executeComposeOp(controller);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            CmdPtReal cmdPtReal = new CmdPtReal(controller.getAllBytes4Command());
            cmdPtReal.opVersion = CmdPtReal.OP_VERSION_ABS_MUSIC_MODE;
            cmdPtReal.musicSensitivity = controller.getSensitivity();
            cmdPtReal.musicCode = controller.getMusicCode();
            iotOp.writeCmd(cmdPtReal);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSetLowBlueLight(EventLowBlueLight event) {
        SafeLog.Companion.i(TAG, () -> "onEventSetLowBlueLight() open = " + event.getOpen() + " value = " + event.getValue());
        ControllerLowBlueLight controller = new ControllerLowBlueLight(ControllerLowBlueLight.SetMethod.SCALE, event.getOpen(), event.getValue(), 100, 100);
        if (bleOp.isOpCommEnable()) {
            bleOp.executeOp(controller);
        } else if (iotOp.isOpCommEnable()) {
            CmdPtReal cmdPtReal = CmdPtReal.makeCmdPtRealWithCallBack(controller, new ICmdPtRealOpResult() {
                @Override
                public void fail() {
                    EventLowBlueLightResult.Companion.sendFail(true, controller.getCommandType(), controller.getProType());
                }

                @Override
                public void scu() {
                    info.isLowBlueLightOpen = event.getOpen();
                    info.lowBlueLightValue = event.getValue();
                    EventLowBlueLightResult.Companion.sendSuc(true,
                            controller.getCommandType(),
                            controller.getProType(),
                            1, event.getOpen(), 100, 100, event.getValue());
                }
            });
            iotOp.writeCmd(cmdPtReal);
        } else {
            EventLowBlueLightResult.Companion.sendFail(true, controller.getCommandType(), controller.getProType());
        }
    }
}