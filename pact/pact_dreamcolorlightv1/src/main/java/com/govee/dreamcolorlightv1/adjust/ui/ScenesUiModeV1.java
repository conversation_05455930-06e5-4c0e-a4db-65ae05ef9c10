package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.ui.R;
import android.os.Bundle;

import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsScenesUiMode;

import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;

/**
 * Create by xieyingwu on 2019-07-22
 * scenes ui mode
 */
public class ScenesUiModeV1 extends AbsScenesUiMode {
    private int goodsType;

    public ScenesUiModeV1(String sku,String device, int goodsType,boolean showEdit) {
        super(sku, showEdit);
        this.goodsType = goodsType;
        this.device = device;
    }

    @Override
    public AbsModeFragment getUiFragment() {
        ScenesFragmentV2 fragment = new ScenesFragmentV2();
        Bundle bundle = fragment.makeArguments(getSku(),getDevice(), goodsType);
        Bundle editBundle = fragment.makeEditBundle(bundle, showEdit);
        fragment.setArguments(editBundle);
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_scene_mini, R.mipmap.new_control_light_btb_mode_scenes_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_scenes;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.loadLocal();
        return subModeScenes;
    }
}
