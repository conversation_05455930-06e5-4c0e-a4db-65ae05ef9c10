package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsSingleController;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-01-19
 * controller-限流$
 */
public class LimitController extends AbsSingleController {
    private boolean openLimit;

    /**
     * 写操作
     *
     * @param openLimit 是否开启限流
     */
    public LimitController(boolean openLimit) {
        super(true);
        this.openLimit = openLimit;
    }

    /**
     * 读操作
     */
    public LimitController() {
        super(false);
    }

    @Override
    protected boolean writeResult(boolean writeSuc) {
        EventLimit.sendWriteResult(writeSuc, getCommandType(), getProType(), openLimit);
        return true;
    }

    @Override
    protected byte[] translateWrite() {
        return new byte[]{openLimit ? (byte) 0x01 : (byte) 0x00};
    }

    @Override
    protected void fail() {
        EventLimit.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.SINGLE_LIMIT;
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        boolean openLimit = validBytes[0] == 1;
        EventLimit.sendSuc(isWrite(), getCommandType(), getProType(), openLimit);
        return true;
    }
}