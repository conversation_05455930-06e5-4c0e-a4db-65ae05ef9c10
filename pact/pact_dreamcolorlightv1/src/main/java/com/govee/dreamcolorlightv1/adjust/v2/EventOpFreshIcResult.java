package com.govee.dreamcolorlightv1.adjust.v2;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022/7/13
 * event-刷新ic的结果
 */
public class EventOpFreshIcResult {
    public static final int RESULT_TYPE_SUPPORT = 0;/*是否支持*/
    public static final int RESULT_TYPE_FRESH_OP = 1;/*刷新操作*/
    public static final int RESULT_TYPE_FRESH_OP_RESULT = 2;/*刷新操作结果*/
    private int resultType;
    private boolean result;

    private EventOpFreshIcResult() {
    }

    public static void sendEventOpFreshIcResult(int resultType, boolean result) {
        EventOpFreshIcResult event = new EventOpFreshIcResult();
        event.result = result;
        event.resultType = resultType;
        EventBus.getDefault().post(event);
    }

    public int getResultType() {
        return resultType;
    }

    public boolean isResult() {
        return result;
    }
}