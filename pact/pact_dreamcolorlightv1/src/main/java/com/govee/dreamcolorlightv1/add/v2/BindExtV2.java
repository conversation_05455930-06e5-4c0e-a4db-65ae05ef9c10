package com.govee.dreamcolorlightv1.add.v2;

import androidx.annotation.Keep;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/3/6
 * 绑定扩展参数v2$
 */
@Keep
public class BindExtV2 {
    public String deviceName;
    public String bleName;
    public String address;
    public String wifiSoftVersion;
    public String wifiHardVersion;
    public String wifiMac;
    public int pactType;
    public int pactCode;

    public String secretCode;
    /*是否支持蓝牙快速连接*/
    public boolean supportBleBroadV3 = false;

    public BindExtV2(int pactType, int pactCode) {
        this.pactType = pactType;
        this.pactCode = pactCode;
    }
}