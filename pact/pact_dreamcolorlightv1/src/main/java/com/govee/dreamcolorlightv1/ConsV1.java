package com.govee.dreamcolorlightv1;

import android.os.Bundle;

import com.govee.base2home.pact.support.OldDreamColorUtil;
import com.govee.base2home.sku.SkuIcM;
import com.govee.dreamcolorlightv1.add.AddInfo;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020/4/8
 * 常量定义$
 */
public class ConsV1 {
    private ConsV1() {
    }

    public static final String intent_ac_key_addInfo = "intent_ac_key_addInfo";

    public static final String intent_ac_adjust_sku = OldDreamColorUtil.intent_ac_adjust_sku;
    public static final String intent_ac_adjust_spec = OldDreamColorUtil.intent_ac_adjust_spec;
    public static final String intent_ac_adjust_device = OldDreamColorUtil.intent_ac_adjust_device;
    public static final String intent_ac_adjust_deviceName = OldDreamColorUtil.intent_ac_adjust_deviceName;
    public static final String intent_ac_adjust_goodsType = OldDreamColorUtil.intent_ac_adjust_goodsType;
    public static final String intent_ac_adjust_bleAddress = OldDreamColorUtil.intent_ac_adjust_bleAddress;
    public static final String intent_ac_adjust_bleName = OldDreamColorUtil.intent_ac_adjust_bleName;
    public static final String intent_ac_adjust_wifiMac = OldDreamColorUtil.intent_ac_adjust_wifiMac;
    public static final String intent_ac_adjust_topic = OldDreamColorUtil.intent_ac_adjust_topic;
    public static final String intent_ac_adjust_versionHard = OldDreamColorUtil.intent_ac_adjust_versionHard;
    public static final String intent_ac_adjust_versionSoft = OldDreamColorUtil.intent_ac_adjust_versionSoft;
    public static final String intent_ac_adjust_versionHard_4_wifi = OldDreamColorUtil.intent_ac_adjust_versionHard_4_wifi;
    public static final String intent_ac_adjust_versionSoft_4_wifi = OldDreamColorUtil.intent_ac_adjust_versionSoft_4_wifi;
    public static final String intent_ac_adjust_blue_light_open = "intent_ac_adjust_blue_light_open";
    public static final String intent_ac_adjust_blue_light_value = "intent_ac_adjust_blue_light_value";
    public static final String intent_ac_adjust_ic = OldDreamColorUtil.intent_ac_adjust_ic;

    public static final String intent_ac_adjust_secret = "intent_ac_adjust_secret";

    public static final String arguments_key_version_soft = "arguments_key_version_soft";
    public static final String arguments_key_goods_type = "arguments_key_goods_type";
    public static final String intent_ac_adjust_pactType = OldDreamColorUtil.intent_ac_adjust_pactType;
    public static final String intent_ac_adjust_pactCode = OldDreamColorUtil.intent_ac_adjust_pactCode;

    /**
     * 构建控制页bundle
     *
     * @param goodsType
     * @param sku
     * @param spec
     * @param device
     * @param deviceName
     * @param bleAddress
     * @param bleName
     * @return
     */
    public static Bundle makeAdjustAcBundle(int goodsType, String sku, String spec, String device, String deviceName, String bleAddress, String bleName, String versionHard, String versionSoft, int ic, String secret, int pactType, int pactCode) {
        Bundle bundle = new Bundle();
        bundle.putInt(intent_ac_adjust_goodsType, goodsType);
        bundle.putString(intent_ac_adjust_sku, sku);
        bundle.putString(intent_ac_adjust_spec, spec);
        bundle.putString(intent_ac_adjust_device, device);
        bundle.putString(intent_ac_adjust_deviceName, deviceName);
        bundle.putString(intent_ac_adjust_bleAddress, bleAddress);
        bundle.putString(intent_ac_adjust_bleName, bleName);
        bundle.putString(intent_ac_adjust_versionHard, versionHard);
        bundle.putString(intent_ac_adjust_versionSoft, versionSoft);
        bundle.putInt(intent_ac_adjust_ic, ic);
        bundle.putString(intent_ac_adjust_secret, secret);
        bundle.putInt(intent_ac_adjust_pactType, pactType);
        bundle.putInt(intent_ac_adjust_pactCode, pactCode);
        return bundle;
    }

    public static Bundle makeAdjustAcBundle4BleSecret(AddInfo addInfo) {
        Bundle bundle = new Bundle();
        bundle.putInt(intent_ac_adjust_goodsType, addInfo.goodsType);
        bundle.putString(intent_ac_adjust_sku, addInfo.sku);
        bundle.putString(intent_ac_adjust_spec, "");
        bundle.putString(intent_ac_adjust_device, addInfo.device);
        bundle.putString(intent_ac_adjust_deviceName, addInfo.deviceName);
        bundle.putString(intent_ac_adjust_bleAddress, addInfo.bleAddress);
        bundle.putString(intent_ac_adjust_bleName, addInfo.bleName);
        bundle.putString(intent_ac_adjust_versionHard, addInfo.versionHard);
        bundle.putString(intent_ac_adjust_versionSoft, addInfo.versionSoft);
        int ic;
        if (addInfo.icNum > 0) {
            ic = addInfo.icNum;
        } else {
            ic = SkuIcM.getInstance().getDefIcNum(addInfo.sku);
        }
        bundle.putInt(intent_ac_adjust_ic, ic);
        bundle.putString(intent_ac_adjust_secret, addInfo.secretCode);
        bundle.putInt(intent_ac_adjust_pactType, addInfo.pactType);
        bundle.putInt(intent_ac_adjust_pactCode, addInfo.pactCode);
        return bundle;
    }
}