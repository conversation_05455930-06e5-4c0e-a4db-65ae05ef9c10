package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.v1.AbsMusicNoIcFragmentV6;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV1;
import com.govee.ui.R;
import com.ihoment.base2app.util.AppUtil;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by linshicong on 2020/3/21
 */
public class MusicFragmentV1 extends AbsMusicNoIcFragmentV6 {
    private static final int[] iconResSet = new int[]{
            R.drawable.component_btn_effect_energic,
            R.drawable.component_btn_effect_rhythm,
            R.drawable.component_btn_effect_spectrum,
            R.drawable.component_btn_effect_rolling,
    };

    private static final int[] desResSet = new int[]{
            R.string.effect_energic_des,
            R.string.effect_rhythm_des,
            R.string.effect_spectrum_des,
            R.string.effect_rolling_des,
    };

    private static final int[] protocolSet = new int[]{
            BleProtocol.value_sub_mode_new_music_energy,
            BleProtocol.value_sub_mode_new_music_rhythm,
            BleProtocol.value_sub_mode_new_music_spectrum,
            BleProtocol.value_sub_mode_new_music_scroll,
    };

    private SubModeMusicV1 subModeMusic = new SubModeMusicV1();

    @Override
    protected int[] getSupportEffectIconResSet() {
        return iconResSet;
    }

    @Override
    protected int[] getSupportEffectDesResSet() {
        return desResSet;
    }

    @Override
    protected int[] getSupportEffects() {
        return protocolSet;
    }

    @Override
    protected void init() {
        super.init();
        /*防止fragment视图还未加载就已传递了模式信息；因此在init完成时也需要刷新ui*/
        updateUi();
    }

    @Override
    protected void chooseEffect(Effect effect) {
        if (Effect.power.equals(effect) && subModeMusic.isDynamic()
                || Effect.soft.equals(effect) && !subModeMusic.isDynamic())
            return;
        SubModeMusicV1 copy = subModeMusic.copy();
        copy.setDynamic(Effect.power.equals(effect));
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
        AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.mode_click_music_ + getSubModeDetailStr(copy.getEffect()));
    }

    @Override
    protected void onSensitivityChange(int sensitivity) {
        if (sensitivity == subModeMusic.getSensitivity()) return;
        SubModeMusicV1 copy = subModeMusic.copy();
        copy.setSensitivity(sensitivity);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
    }

    @Override
    protected boolean isSameEffect(int effect) {
        byte effectCur = (byte) subModeMusic.getEffect();
        return effectCur == effect;
    }

    @Override
    protected void changeSubEffect(int effect) {
        SubModeMusicV1 copy = subModeMusic.copy();
        copy.setEffect((byte) effect);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
    }

    @Override
    protected void autoColorChange() {
        SubModeMusicV1 copy = subModeMusic.copy();
        copy.setAutoRgb(!copy.isAutoRgb());
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
    }

    @Override
    protected void onColorTempChange(int colorTemp) {
        /*不支持色温调节*/
    }

    @Override
    protected boolean supportCameraGetColor() {
        return false;
    }

    @Override
    protected boolean supportColorTemBar() {
        return false;
    }

    @Override
    protected void onColorChange(int type, int color) {
        SubModeMusicV1 copy = subModeMusic.copy();
        copy.setRgb(color);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
    }

    @Override
    public int marginTop() {
        return AppUtil.getScreenWidth() * 37 / 750;
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeMusicV1) {
            boolean checkAnalytic4SubModeUse = ((SubModeMusicV1) subMode).checkAnalytic4SubModeUse(getSku());
            this.subModeMusic = (SubModeMusicV1) subMode;
            updateUi();
            if (checkAnalytic4SubModeUse) {
                analyticSubModeDetail(ParamFixedValue.mode_use_music_, subModeMusic.getEffect());
            }
        }
    }

    private void updateUi() {
        if (isViewInflateOk()) {
            updateSensitivity(subModeMusic.getSensitivity());
            int effect = subModeMusic.getEffect();
            updateEffectUi(effect);
            if (effect == BleProtocol.value_sub_mode_new_music_rhythm) {
                showSubEffectUi(true);
                updateSubEffect(subModeMusic.isDynamic() ? Effect.power : Effect.soft);
                boolean autoColor = subModeMusic.isAutoRgb();
                updateAutoSwitch(autoColor);
                boolean colorAreaShow = !autoColor;
                showColorArea(colorAreaShow);
                if (colorAreaShow) {
                    setColor(subModeMusic.getRgb());
                }
                showAutoColor(true);
            } else if (effect == BleProtocol.value_sub_mode_new_music_scroll || effect == BleProtocol.value_sub_mode_new_music_spectrum) {
                /*滚动*/
                showSubEffectUi(false);
                boolean autoColor = subModeMusic.isAutoRgb();
                updateAutoSwitch(autoColor);
                boolean colorAreaShow = !autoColor;
                showColorArea(colorAreaShow);
                if (colorAreaShow) {
                    setColor(subModeMusic.getRgb());
                }
                showAutoColor(true);
            } else {
                showAutoColor(false);
                showColorArea(false);
                showSubEffectUi(false);
            }
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

}
