package com.govee.dreamcolorlightv1.iot;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.util.Encode;
import com.govee.base2light.ble.BleUtil;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/3/11
 * 球泡颜色设置cmd$
 */
public class CmdBulb extends AbsCmd {
    List<String> value = new ArrayList<>();

    public CmdBulb(byte[] bytes) {
        String valueStr = Encode.encryptByBase64(bytes);
        value.add(valueStr);
    }

    public CmdBulb(@NonNull List<byte[]> multipleBytes) {
        for (byte[] multipleByte : multipleBytes) {
            String valueStr = Encode.encryptByBase64(multipleByte);
            value.add(valueStr);
        }
    }

    @Override
    public int getCmdVersion() {
        return 1;
    }

    @Override
    public String getCmd() {
        return Cmd.bulb;
    }

    public void logValue() {
        if (value == null || value.isEmpty()) return;
        for (String str : value) {
            byte[] valueBytes = Encode.decryByBase64(str);
            if (LogInfra.openLog()) {
                LogInfra.Log.d("CmdBulb", "value = " + BleUtil.bytesToHexString(valueBytes));
            }
        }
    }

    /**
     * 是否是采用ptReal设置分段颜色
     */
    public SubModeColorV2 isPtReal4SetPartColor() {
        if (value == null || value.isEmpty()) return null;
        int size = value.size();
        if (size >= 2) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            /*至少一包设置颜色+一包设置渐变值*/
            for (int i = 0; i < size - 1; i++) {
                String encodeStr = value.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i("TAG", "isPtReal4SetPartColor() setColor4PosBytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
                boolean setMode2Color = SubModeColorV2.isSetMode2Color(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColorV2.parsePosColor(setColor4PosBytes, subModeColor);
            }
            String encodeStr = value.get(size - 1);
            byte[] gradual20Bytes = Encode.decryByBase64(encodeStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i("TAG", "isPtReal4SetPartColor() gradual20Bytes = " + BleUtil.bytesToHexString(gradual20Bytes));
            }
            boolean setGradual = Gradual4BleWifiController.isWriteGradualController(gradual20Bytes);
            if (setGradual) {
                subModeColor.gradual = Gradual4BleWifiController.parseGradual(gradual20Bytes);
                return subModeColor;
            }
            return null;
        }
        return null;
    }

    public boolean isSetColorStrip() {
        if (value == null || value.isEmpty()) return false;
        if (value.size() == 1) return false; /*只有一包时直接走pt*/
        for (int i = 0; i < value.size(); i++) {
            String encodeStr = value.get(i);
            byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
            if (setColor4PosBytes == null) return false;
            if (setColor4PosBytes[2] != BleProtocol.sub_mode_color && setColor4PosBytes[2] != BleProtocol.sub_mode_color_v2)
                return false;
        }
        return true;
    }

    public boolean isSetColorStripWithBrightness() {
        if (value == null || value.isEmpty()) return false;
        String encodeStr = value.get(0);
        byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
        if (setColor4PosBytes == null) return false;
        return setColor4PosBytes[2] == BleProtocol.sub_mode_color_v2;
    }

    /**
     * 点击色条应用整段颜色-带亮度
     */
    public SubModeColorV2 setPartColor4ColorStripWithBrightness() {
        if (value == null || value.isEmpty()) return null;
        int size = value.size();
        if (size >= 2) {
            /*至少一包颜色+一包亮度*/
            SubModeColorV2 subModeColor = new SubModeColorV2();
            for (int i = 0; i < size; i++) {
                String encodeStr = value.get(i);
                byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
                boolean setMode2Color = SubModeColorV2.isSetMode2Color4ColorEffect(setColor4PosBytes);
                if (!setMode2Color) return null;
                /*解析对应位置的颜色*/
                SubModeColorV2.parsePosColorWithBrightness(setColor4PosBytes, subModeColor);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i("CmdBulb", "setPartColor4ColorStripWithBrightness valueStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
                }
            }
            return subModeColor;
        }
        return null;
    }

    /**
     * 点击色条应用整段颜色
     */
    public SubModeColor setPartColor4ColorStrip() {
        if (value == null || value.isEmpty()) return null;
        int size = value.size();
        SubModeColor subModeColor = new SubModeColor();
        for (int i = 0; i < size; i++) {
            String encodeStr = value.get(i);
            byte[] setColor4PosBytes = Encode.decryByBase64(encodeStr);
            boolean setMode2Color = SubModeColor.isSetMode2Color4ColorEffect(setColor4PosBytes);
            if (!setMode2Color) return null;
            /*解析对应位置的颜色*/
            SubModeColor.parsePosColor(setColor4PosBytes, subModeColor);
            if (LogInfra.openLog()) {
                LogInfra.Log.i("CmdBulb", "setPartColor4ColorStrip valueStr() bytes = " + BleUtil.bytesToHexString(setColor4PosBytes));
            }
        }
        return subModeColor;
    }

    public byte[] getOpCommandBytes() {
        if (value == null || value.isEmpty()) return null;
        int size = value.size();
        String valueByteStr = value.get(size - 1);
        return Encode.decryByBase64(valueByteStr);
    }
}