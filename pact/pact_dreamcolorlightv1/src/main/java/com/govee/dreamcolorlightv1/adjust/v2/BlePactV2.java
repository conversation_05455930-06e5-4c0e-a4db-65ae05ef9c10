package com.govee.dreamcolorlightv1.adjust.v2;

import android.text.TextUtils;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.scenes.SecretKeyConfig;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.SecretKeyController;
import com.govee.base2light.pact.ble.AbsBlePact;
import com.govee.base2light.pact.ble.IPactResult4Ble;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2019-12-16
 * v2版本的蓝牙协议$
 */
public class BlePactV2 extends AbsBlePact {
    public BlePactV2(IPactResult4Ble iPactResult) {
        super(iPactResult);
    }

    @Override
    protected String getTag() {
        return "BlePactV2";
    }

    @NonNull
    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected boolean isSupportProtocol(Protocol protocol) {
        if (protocol == null) return false;
        for (Protocol pro : Support.supportProtocolsV2) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        /*BK*/
        for (Protocol pro : Support.supportProtocolsV5) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        /*ble+wifi - frk - 不支持设备校验*/
        for (Protocol pro : Support.supportProtocolsV5_1) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }

        /*ble+wifi - bk -支持设备校验*/
        for (Protocol pro : Support.supportProtocolsV7) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        //616c/d/e
        for (Protocol pro : Support.supportProtocolsV10) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        //612x
        for (Protocol pro : Support.supportProtocolsV11) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        return false;
    }

    @Override
    protected Protocol parseBleBroadcastProtocol(int goodsType, byte[] scanRecord) {
        return GoodsType.parseBleBroadcastPactInfo(goodsType, scanRecord);
    }

    @Nullable
    @Override
    protected AbsSingleController getDeviceSecretController() {
        boolean supportDeviceSecret = Support.isSupportDeviceSecret(goodsType);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getDeviceSecretController() supportDeviceSecret = " + supportDeviceSecret + " ; goodsType = " + goodsType);
        }
        if (!supportDeviceSecret) return null;
        String secretCode = SecretKeyConfig.read().getSecretKey(bleAddress);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getController4DeviceSecret() secretCode = " + secretCode);
        }
        if (TextUtils.isEmpty(secretCode)) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "getController4DeviceSecret() secretCode未获取到 bleAddress = " + bleAddress);
            }
            return null;
        }
        return new SecretKeyController(secretCode);
    }
}