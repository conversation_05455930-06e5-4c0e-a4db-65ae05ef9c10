package com.govee.dreamcolorlightv1.add.v2;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.govee.base2home.bean.CommonVideoParamBean;
import com.govee.base2home.device.AbsDeviceNameAcV1;
import com.govee.base2home.videoplay.CommonVideoActivity;
import com.govee.base2light.neonlight.recommend.ConstJumpKey;
import com.govee.base2light.neonlight.recommend.NeonRecommendListAc;
import com.govee.base2light.neonlight.recommend.data.BleIotShare;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.dreamcolorlightv1.ConsV1;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.ui.R;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.util.JumpUtil;
import com.ihoment.base2app.util.ResUtil;

import androidx.annotation.NonNull;

import static com.govee.base2light.neonlight.recommend.NeonRecommendListAc.neon_shape_sku;

/**
 * Create by xieyingwu on 2020/3/6
 * 设备命名页v2$
 */
public class DeviceNameAcV2 extends AbsDeviceNameAcV1 {
    private AddInfoV2 addInfo;

    /**
     * 跳转到设备命名页
     *
     * @param context
     * @param addInfo
     */
    public static void jump2DeviceNameAcV2(Context context, @NonNull AddInfoV2 addInfo) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ConsV1.intent_ac_key_addInfo, addInfo);
        JumpUtil.jumpWithBundle(context, DeviceNameAcV2.class, bundle);
    }

    @Override
    protected void initParams(Intent intent) {
        addInfo = intent.getParcelableExtra(ConsV1.intent_ac_key_addInfo);
    }

    @Override
    protected void doSkip() {
        //若应用处于后台，则不进行界面跳转
        if (BaseApplication.getBaseApplication().isInBackground()) {
            return;
        }
        if (Support.supportRecommendedShapeLocal(addInfo.sku)) {
            BleIotInfo info = BleIotShare.INSTANCE.getInfo();
            if (info != null) {
                info.deviceName = addInfo.deviceName;
            }
            Bundle bundle = new Bundle();
            bundle.putString(neon_shape_sku, addInfo.sku);
            bundle.putBoolean(ConstJumpKey.neon_shape_is_from_add, true);

            String url = "https://d1f2504ijhdyjw.cloudfront.net/mp4/1c47d053d580c7caec037ee14f311b8" +
                    "0-%E9%9C%93%E8%99%B9%E7%81%AF%20%E6%B5%B7%E9%BE%9F%E9%80%A0%E5%9E%8B%20%E5%AE%" +
                    "89%E8%A3%85%E8%A7%86%E9%A2%91%204-18%20-1(2).mp4";
            CommonVideoParamBean bean = new CommonVideoParamBean.Build()
                    .videoUrl(url).skuArray(new String[]{addInfo.sku})
                    .contentStr(ResUtil.getString(R.string.hint_watch_video))
                    .canBack(true).clazz(NeonRecommendListAc.class).
                    extraData(bundle).needFinishPage(false).build();
            CommonVideoActivity.Companion.jump2CommonVideoActivity(this, bean);
        } else {
            //跳转到wifi设置界面
            WifiChooseAc.jump2wifiChooseAcByAdd(this, addInfo);
        }
    }

    @Override
    protected void onSaveDeviceNameSuc(String newDeviceName) {
        addInfo.deviceName = newDeviceName;
        doSkip();
    }

    @Override
    protected String getDevice() {
        return addInfo.device;
    }

    @Override
    protected String getSku() {
        return addInfo.sku;
    }

    @Override
    protected String getDeviceName() {
        return addInfo.deviceName;
    }

}
