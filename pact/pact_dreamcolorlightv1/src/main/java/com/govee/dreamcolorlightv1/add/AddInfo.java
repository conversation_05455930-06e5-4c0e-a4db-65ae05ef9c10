package com.govee.dreamcolorlightv1.add;

import android.os.Parcel;
import android.os.Parcelable;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-02-12
 * 添加信息$
 */
public class AddInfo implements Parcelable {
    /*广播字段*/
    public String sku;
    public int goodsType;
    public String bleName;
    public String bleAddress;
    public String deviceName;
    public int pactType;
    public int pactCode;
    /*协议字段*/
    public String versionSoft;
    public String versionHard;
    public String device;
    /*设备密钥*/
    public String secretCode;
    /*ic数*/
    public int icNum = -1;


    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.sku);
        dest.writeInt(this.goodsType);
        dest.writeString(this.bleName);
        dest.writeString(this.bleAddress);
        dest.writeString(this.deviceName);
        dest.writeInt(this.pactType);
        dest.writeInt(this.pactCode);
        dest.writeString(this.versionSoft);
        dest.writeString(this.versionHard);
        dest.writeString(this.device);
        dest.writeString(this.secretCode);
        dest.writeInt(this.icNum);
    }

    public AddInfo() {
    }

    protected AddInfo(Parcel in) {
        this.sku = in.readString();
        this.goodsType = in.readInt();
        this.bleName = in.readString();
        this.bleAddress = in.readString();
        this.deviceName = in.readString();
        this.pactType = in.readInt();
        this.pactCode = in.readInt();
        this.versionSoft = in.readString();
        this.versionHard = in.readString();
        this.device = in.readString();
        this.secretCode = in.readString();
        this.icNum = in.readInt();
    }

    public static final Creator<AddInfo> CREATOR = new Creator<AddInfo>() {
        @Override
        public AddInfo createFromParcel(Parcel source) {
            return new AddInfo(source);
        }

        @Override
        public AddInfo[] newArray(int size) {
            return new AddInfo[size];
        }
    };
}