package com.govee.dreamcolorlightv1;

import com.govee.base2home.main.choose.BleProcessorManager;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.scenes.CmdBuilderManager;
import com.govee.base2home.sku.ModelMaker;
import com.govee.base2light.connect.bean.AddDeviceParams;
import com.govee.base2light.connect.constant.AddDeviceConfig;
import com.govee.base2light.connect.interceptor.MatterInterceptor;
import com.govee.dreamcolorlightv1.add.BleBroadcastProcessorV1;
import com.govee.dreamcolorlightv1.add.v2.BleBroadcastProcessorV2;
import com.govee.dreamcolorlightv1.add.v3.BleBroadcastProcessorV3;
import com.govee.dreamcolorlightv1.add.v3.BleBroadcastProcessorV4;
import com.govee.dreamcolorlightv1.add.v3.BleBroadcastProcessorV5;
import com.govee.dreamcolorlightv1.adjust.DetailConfig;
import com.govee.dreamcolorlightv1.pact.Register4Item;
import com.govee.dreamcolorlightv1.pact.SubMaker;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.dreamcolorlightv1.scenes.BleBrightnessBuilderV1;
import com.govee.dreamcolorlightv1.scenes.BleColorCmdBuilderV1;
import com.govee.dreamcolorlightv1.scenes.BleColorTemCmdBuilderV1;
import com.govee.dreamcolorlightv1.scenes.BleHeartCmdBuilderV1;
import com.govee.dreamcolorlightv1.scenes.BleSwitchCmdBuilderV1;
import com.govee.dreamcolorlightv1.scenes.IotBrightnessCmdBuilderV1;
import com.govee.dreamcolorlightv1.scenes.IotColorCmdBuilderV1;
import com.govee.dreamcolorlightv1.scenes.IotColorTemCmdBuilderV1;
import com.govee.dreamcolorlightv1.scenes.IotSwitchCmdBuilderV1;
import com.govee.lifecycle.annotation.AppLifecycle;
import com.ihoment.base2app.IApplication;

import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2020/4/8
 * ApplicationImp$
 */
@AppLifecycle
public class DreamColorV1ApplicationImp implements IApplication {
    @Override
    public void create() {
        /*item的构建*/
        Register4Item.INSTANCE.register();
        ModelMaker.getInstance().addMaker(new SubMaker());
        /*发现设备后操作逻辑*/
        BleProcessorManager.getInstance().addProcessorFirst(new BleBroadcastProcessorV1());
        BleProcessorManager.getInstance().addProcessorFirst(new BleBroadcastProcessorV2());
        BleProcessorManager.getInstance().addProcessorFirst(new BleBroadcastProcessorV3());
        BleProcessorManager.getInstance().addProcessorFirst(new BleBroadcastProcessorV4());
        BleProcessorManager.getInstance().addProcessorFirst(new BleBroadcastProcessorV5());
        /*添加默认的支持产品类型sku*/
        Support.addSupportPact();
        /*scenes场景ble支持*/
        CmdBuilderManager.getInstance().registerBleCmdBuilder(
                new BleBrightnessBuilderV1(),
                new BleColorCmdBuilderV1(),
                new BleColorTemCmdBuilderV1(),
                new BleHeartCmdBuilderV1(),
                new BleSwitchCmdBuilderV1()
        );
        /*scenes场景iot支持*/
        CmdBuilderManager.getInstance().registerCmdBuilder(
                new IotSwitchCmdBuilderV1(),
                new IotBrightnessCmdBuilderV1(),
                new IotColorCmdBuilderV1(),
                new IotColorTemCmdBuilderV1()
        );

        /*支持https配置动态域名*/
        Support.setDynamicApi();

        //matter
        AddDeviceParams.Build build = new AddDeviceParams.Build();
        build.matterInterceptor(new MatterInterceptor() {
            @Override
            public boolean checkGoodsType(int goodsType) {
                return goodsType == GoodsType.GOODS_TYPE_VALUE_H616C || goodsType == GoodsType.GOODS_TYPE_VALUE_H612x;
            }

            @Override
            public boolean intercept(int goodsType,
                                     @Nullable String sku,
                                     @Nullable String softVersion,
                                     @Nullable String hardVersion,
                                     @Nullable String wifiSoftVersion,
                                     @Nullable String wifiHardVersion) {
                return goodsType == GoodsType.GOODS_TYPE_VALUE_H616C || goodsType == GoodsType.GOODS_TYPE_VALUE_H612x;
            }
        });
        AddDeviceConfig.addDeviceConfig(build.build());
        DetailConfig.INSTANCE.addConfig();
    }
}