package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.ui.R;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsColorUiMode;

import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.SubModeColorV6;

/**
 * Create by hey on 2021/2/5
 * $612d e
 */
public class ColorUiModeV7 extends AbsColorUiMode {
    private ColorUiModeV7(String sku) {
        super(sku);
    }

    public ColorUiModeV7(int goodsType, String sku, String device, int ic, DiySupportV1 diySupport) {
        super(goodsType, sku, device, ic, diySupport);
    }

    public ColorUiModeV7(int goodsType, String device, int ic1, int ic2, DiySupportV1 diySupport) {
        //612d e同型号 样式用612e的
        super(goodsType, "H612E", device, ic1, ic2, diySupport);
    }

    @Override
    public AbsModeFragment getUiFragment() {
        ColorFragmentV7 fragment = new ColorFragmentV7();
        fragment.makeArguments(getSku());
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_color_mini, R.mipmap.new_control_light_btb_mode_color_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeColorV6 subModeColor = new SubModeColorV6();
        subModeColor.loadLocal();
        return subModeColor;
    }
}
