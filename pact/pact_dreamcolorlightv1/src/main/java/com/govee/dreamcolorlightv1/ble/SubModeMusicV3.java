package com.govee.dreamcolorlightv1.ble;

import com.govee.ui.R;
import android.text.TextUtils;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.IMusicEffectStatic;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.OldMusicEffect;
import com.govee.base2light.util.NumUtil;

import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.StorageInfra;
import com.ihoment.base2app.util.ResUtil;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2021/5/5
 * 音乐模式-v3$
 */
public class SubModeMusicV3 extends AbsSubMode4Analytic {
    private static final String TAG = "SubModeMusicV3";
    private static final byte auto_type_open = 0;
    private static final byte auto_type_close = 1;
    private static final int min_sensitivity = 0;
    private static final int max_sensitivity = 99;
    private static final byte sub_effect_dynamic = 0x00;
    private static final byte sub_effect_soft = 0x01;
    public transient AbsNewMusicEffect musicEffect;

    public boolean isNewMusic = false;

    private int musicCode = IMusicEffectStatic.single_value_sub_rhythm;

    public int sensitivity = max_sensitivity;
    private boolean autoColor = true;
    private boolean dynamic = true;
    private int rgb = 0xFFFF0000;
    /*定义icNum的数值-用于进行音乐模式相关参数转化；该字段赋值有效才应用*/
    private transient int icNum;

    public int getIcNum() {
        return icNum;
    }

    @Override
    public void loadLocal() {
        SubModeMusicV3 subModeMusicV3 = StorageInfra.get(SubModeMusicV3.class);
        if (subModeMusicV3 == null) return;
        isNewMusic = subModeMusicV3.isNewMusic;
        musicCode = subModeMusicV3.musicCode;
        sensitivity = subModeMusicV3.sensitivity;
        autoColor = subModeMusicV3.autoColor;
        dynamic = subModeMusicV3.dynamic;
        rgb = subModeMusicV3.rgb;
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(this);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_new_music;
    }

    @Override
    public String getAnalyticModeName() {
        String subStr = parseSubStr(musicCode);
        if (!TextUtils.isEmpty(subStr)) return ParamFixedValue.mode_music + "_" + subStr;
        return ParamFixedValue.mode_music;
    }

    private String parseSubStr(int musicCode) {
        if (isNewMusic) return IMusicEffectStatic.parseSubStr4New(musicCode);
        if (musicCode == IMusicEffectStatic.single_value_sub_energy)
            return ResUtil.getString4English(R.string.effect_energic_des);
        if (musicCode == IMusicEffectStatic.single_value_sub_rhythm)
            return ResUtil.getString4English(R.string.effect_rhythm_des);
        if (musicCode == IMusicEffectStatic.single_value_sub_specturm)
            return ResUtil.getString4English(R.string.effect_spectrum_des);
        if (musicCode == IMusicEffectStatic.single_value_sub_scroll)
            return ResUtil.getString4English(R.string.effect_rolling_des);
        return "";
    }


    @Override
    public void parse(byte[] validBytes) {
        musicCode = BleUtil.getUnsignedByte(validBytes[0]);
        sensitivity = BleUtil.getUnsignedByte(validBytes[1]);
        isNewMusic = Support.isNewMusicCode(musicCode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parse() musicCode = " + musicCode + " ; isNewMusic = " + isNewMusic);
        }
        /*旧音乐模式-单包指令包含全部音乐模式参数*/
        if (!isNewMusic) {
            /*节奏下有动感柔和*/
            dynamic = BleUtil.getUnsignedByte(validBytes[2]) == sub_effect_dynamic;
            autoColor = validBytes[3] == auto_type_open;
            /*未开启颜色自动，则是指定了单色*/
            if (!autoColor) {
                byte[] rgbBytes = new byte[3];
                System.arraycopy(validBytes, 4, rgbBytes, 0, rgbBytes.length);
                rgb = ColorUtils.toColor(rgbBytes);
            }
        }
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] valueBytes;
        if (isNewMusic) {
            valueBytes = new byte[3];
            valueBytes[0] = subModeCommandType();
            valueBytes[1] = (byte) musicCode;
            valueBytes[2] = (byte) sensitivity;
        } else {
            valueBytes = new byte[8];
            valueBytes[0] = subModeCommandType();
            valueBytes[1] = (byte) musicCode;
            valueBytes[2] = (byte) sensitivity;
            valueBytes[3] = dynamic ? sub_effect_dynamic : sub_effect_soft;
            valueBytes[4] = autoColor ? auto_type_open : auto_type_close;
            if (!autoColor) {
                byte[] rgbBytes = ColorUtils.getRgbBytes(rgb);
                System.arraycopy(rgbBytes, 0, valueBytes, 5, rgbBytes.length);
            }
        }
        return valueBytes;
    }

    public int getMusicCode() {
        return musicCode;
    }

    public void setMusicCode(int musicCode) {
        this.musicCode = musicCode;
        isNewMusic = Support.isNewMusicCode(musicCode);
    }

    public int getSensitivity() {
        return sensitivity;
    }

    public void setSensitivity(int sensitivity) {
        this.sensitivity = NumUtil.checkNum(sensitivity, min_sensitivity, max_sensitivity);
    }

    public boolean isAutoColor() {
        return autoColor;
    }

    public int getRgb() {
        return rgb;
    }

    public void beAutoColor() {
        this.autoColor = true;
    }

    public void setRgb(int rgb) {
        this.autoColor = false;
        this.rgb = rgb;
    }

    public boolean isDynamic() {
        return dynamic;
    }

    public void setDynamic(boolean dynamic) {
        this.dynamic = dynamic;
    }

    public void oldMusicEffectChange(OldMusicEffect oldMusicEffect) {
        isNewMusic = false;
        musicCode = oldMusicEffect.musicCode;
        boolean autoColor = oldMusicEffect.autoColor;
        if (autoColor) {
            beAutoColor();
        } else {
            setRgb(oldMusicEffect.rgb);
        }
        dynamic = oldMusicEffect.dynamic;
        setSensitivity(oldMusicEffect.sensitivity);
    }

    public SubModeMusicV3 copy() {
        SubModeMusicV3 subModeMusicV3 = new SubModeMusicV3();
        subModeMusicV3.isNewMusic = isNewMusic;
        subModeMusicV3.musicCode = musicCode;
        subModeMusicV3.sensitivity = sensitivity;
        subModeMusicV3.autoColor = autoColor;
        subModeMusicV3.dynamic = dynamic;
        subModeMusicV3.rgb = rgb;
        return subModeMusicV3;
    }

    public static SubModeMusicV3 toNewSubModeMusic(int sensitivity, int musicCode) {
        SubModeMusicV3 subModeMusicV3 = new SubModeMusicV3();
        subModeMusicV3.isNewMusic = true;
        subModeMusicV3.musicCode = musicCode;
        subModeMusicV3.setSensitivity(sensitivity);
        return subModeMusicV3;
    }

    /**
     * 仅针对需要ic变化的需求-来进行相关赋值
     *
     * @param icNum
     */
    public void setIcNum(int icNum) {
        this.icNum = icNum;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_music;
    }
}
