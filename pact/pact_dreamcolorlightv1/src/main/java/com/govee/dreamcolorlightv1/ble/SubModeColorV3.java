package com.govee.dreamcolorlightv1.ble;

import androidx.annotation.NonNull;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Create by hey on 2021/2/5
 * $
 */
public class SubModeColorV3 extends AbsSubMode4Analytic {
    public static final int op_type_ui_no_fresh = -1;
    public static final int op_type_gradual = 1;
    private static final int COLOR_PIECE = 10;

    public int rgb = 0;/*默认颜色-红色*/
    public int gradual = 0;/*默认不开启渐变*/

    public boolean[] ctlLight = new boolean[COLOR_PIECE];
    public int[] rgbSet;

    public int[] brightnessSet;
    public int brightness;

    public int kelvin;
    public int ctRgb;

    public int opType;

    @Override
    public void loadLocal() {
    }

    @Override
    public void saveLocal() {
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr = UtilColor.colorName(rgb);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_color, subModeStr);
    }

    @Override
    public void parse(byte[] value) {
        this.gradual = BleUtil.getUnsignedByte(value[0]);
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] bytes = new byte[17];
        bytes[0] = subModeCommandType();
        if (this.brightness == 0 && this.brightnessSet == null) {
            bytes[1] = (byte) 0x01;
            int[] rgb = ColorUtils.getRgb(this.rgb);
            bytes[2] = (byte) rgb[0];
            bytes[3] = (byte) rgb[1];
            bytes[4] = (byte) rgb[2];
            if (this.kelvin != 0) {
                byte[] signedBytesFor2 = BleUtil.getSignedBytesFor2(kelvin, true);
                bytes[5] = signedBytesFor2[0];
                bytes[6] = signedBytesFor2[1];
                int[] ctRgb = ColorUtils.getRgb(this.ctRgb);
                bytes[7] = (byte) ctRgb[0];
                bytes[8] = (byte) ctRgb[1];
                bytes[9] = (byte) ctRgb[2];
            }
            int temp = 1;
            bytes[10] = (byte) 0x00;
            for (int i = 0; i < 8; i++) {
                if (ctlLight[i]) {
                    bytes[10] = (byte) (bytes[10] | temp);
                }
                temp = temp << 1;
            }
            temp = 1;
            bytes[11] = (byte) 0x00;
            for (int i = 8; i < COLOR_PIECE; i++) {
                if (ctlLight[i]) {
                    bytes[11] = (byte) (bytes[11] | temp);
                }
                temp = temp << 1;
            }
        } else if (this.brightnessSet != null) {
            bytes[1] = (byte) 0x03;
            for (int i = 0; i < brightnessSet.length; i++) {
                bytes[i + 2] = (byte) brightnessSet[i];
            }
        } else {
            bytes[1] = (byte) 0x02;
            bytes[2] = (byte) this.brightness;
            int temp = 1;
            bytes[3] = (byte) 0x00;
            for (int i = 0; i < 8; i++) {
                if (ctlLight[i]) {
                    bytes[3] = (byte) (bytes[3] | temp);
                }
                temp = temp << 1;
            }
            temp = 1;
            bytes[4] = (byte) 0x00;
            for (int i = 8; i < COLOR_PIECE; i++) {
                if (ctlLight[i]) {
                    bytes[4] = (byte) (bytes[4] | temp);
                }
                temp = temp << 1;
            }
        }
        return bytes;
    }

    public int getRealRgb() {
        if (kelvin > 0) return ctRgb;
        return rgb;
    }

    public void setRgb(int rgb) {
        this.rgb = rgb;
        this.ctRgb = 0;
        this.kelvin = 0;
    }

    public void setTemRgbAndKelvin(int temRgb, int kelvin) {
        this.rgb = 0;
        this.ctRgb = temRgb;
        this.kelvin = kelvin;
    }

    public static SubModeColorV3 makeSubModeColor(int color) {
        SubModeColorV3 subModeColor = new SubModeColorV3();
        subModeColor.rgb = color;
        subModeColor.kelvin = 0;
        subModeColor.ctRgb = 0;
        int length = subModeColor.ctlLight.length;
        int[] rgbSet = new int[length];
        for (int i = 0; i < length; i++) {
            subModeColor.ctlLight[i] = true;
            rgbSet[i] = color;
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }

    public static boolean isSetMode2Color(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opByte == BleProtocolConstants.SINGLE_MODE && modeType == BleProtocol.sub_mode_color_v2;
        }
        return false;
    }

    public static AbsSingleController[] makeSubModeColor(int[] colors, boolean isFade, boolean hadFadeController) {
        if (colors.length != COLOR_PIECE) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        int fadeSize = hadFadeController ? 1 : 0;
        AbsSingleController[] modeControllers = new AbsSingleController[hashMap.size() + fadeSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV3 subModeColor = new SubModeColorV3();
            subModeColor.rgb = color;
            subModeColor.kelvin = 0;
            subModeColor.ctRgb = 0;
            subModeColor.ctlLight = new boolean[COLOR_PIECE];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            modeControllers[i] = new Gradual4BleWifiController(isFade);
        }
        return modeControllers;
    }

    public static void parsePosColor(byte[] setPosColorBytes, @NonNull SubModeColorV3 subModeColor) {
        if (setPosColorBytes == null || setPosColorBytes.length != 20) return;
        if (subModeColor.rgbSet == null) {
            subModeColor.rgbSet = new int[subModeColor.ctlLight.length];
        }
        int rgb = ColorUtils.toColor(setPosColorBytes[4], setPosColorBytes[5], setPosColorBytes[6]);
        int kelvin = BleUtil.convertTwoBytesToShort(setPosColorBytes[7], setPosColorBytes[8]);
        int temRgb = ColorUtils.toColor(setPosColorBytes[9], setPosColorBytes[10], setPosColorBytes[11]);
        int rgbValue;
        if (kelvin > 0) {
            rgbValue = temRgb;
        } else {
            rgbValue = rgb;
        }

        /*从低位bit开始描述*/
        boolean[] low8Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[12]);
        for (int i = 0; i < low8Set.length; i++) {
            if (low8Set[i]) {
                subModeColor.rgbSet[i] = rgbValue;
            }
        }
        int index = 8;
        boolean[] high5Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[13]);
        for (int i = 0; i < high5Set.length - 1; i++) {
            if (high5Set[i]) {
                subModeColor.rgbSet[index] = rgbValue;
            }
            index++;
        }
    }

    public static SubModeColorV3 parseSubModeColor4Write(byte[] validBytes) {
        if (LogInfra.openLog()) {
            LogInfra.Log.d("parseSubModeColor4Write", "validBytes = " + BleUtil.bytesToHexString(validBytes));
        }
        if (validBytes[0] == 0x01) {
            SubModeColorV3 subModeColor = new SubModeColorV3();
            int rgb = ColorUtils.toColor(validBytes[1], validBytes[2], validBytes[3]);
            int kelvin = BleUtil.convertTwoBytesToShort(validBytes[4], validBytes[5]);
            int temRgb = ColorUtils.toColor(validBytes[6], validBytes[7], validBytes[8]);
            subModeColor.rgb = rgb;
            subModeColor.ctRgb = temRgb;
            subModeColor.kelvin = kelvin;

            boolean[] ctlLight = new boolean[COLOR_PIECE];
            /*前8盏灯的选中状态*/
            boolean[] group1Value8 = BleUtil.parseBytes4Bit(validBytes[9]);
            boolean[] group2Value8 = BleUtil.parseBytes4Bit(validBytes[10]);
            System.arraycopy(group1Value8, 0, ctlLight, 0, group1Value8.length);
            System.arraycopy(group2Value8, 0, ctlLight, 8, ctlLight.length - group1Value8.length);
            subModeColor.ctlLight = ctlLight;
            return subModeColor;
        } else if (validBytes[0] == 0x02) {
            SubModeColorV3 subModeColor = new SubModeColorV3();
            subModeColor.brightness = BleUtil.getUnsignedByte(validBytes[1]);
            boolean[] ctlLight = new boolean[COLOR_PIECE];
            /*前8盏灯的选中状态*/
            boolean[] group1Value8 = BleUtil.parseBytes4Bit(validBytes[2]);
            boolean[] group2Value8 = BleUtil.parseBytes4Bit(validBytes[3]);
            System.arraycopy(group1Value8, 0, ctlLight, 0, group1Value8.length);
            System.arraycopy(group2Value8, 0, ctlLight, 8, ctlLight.length - group1Value8.length);
            subModeColor.ctlLight = ctlLight;
            return subModeColor;
        }
        return null;
    }

    public boolean isSelectAll() {
        if (ctlLight != null) {
            for (boolean b : ctlLight) {
                if (!b) return false;
            }
            return true;
        }
        return false;
    }

    public static SubModeColorV3 parseSubModeColor2New(@NonNull SubModeColor subModeColor) {
        SubModeColorV3 subModeColorV2 = new SubModeColorV3();
        boolean[] ctlLight = subModeColor.ctlLight;
        if (ctlLight != null) {
            int length = ctlLight.length;
            int min = Math.max(COLOR_PIECE, length);
            System.arraycopy(ctlLight, 0, subModeColorV2.ctlLight, 0, min);
        }
        subModeColorV2.rgb = subModeColor.rgb;
        subModeColorV2.rgbSet = subModeColor.rgbSet;
        return subModeColorV2;
    }

    public static AbsSingleController[] makeSubModeColor(Colors colorStrip) {
        int[] colors = colorStrip.colorSet;
        if (colors.length != COLOR_PIECE) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        boolean hasBrightness = colorStrip.brightnessSet != null && colorStrip.brightnessSet.length == COLOR_PIECE;
        AbsSingleController[] modeControllers = new AbsSingleController[hasBrightness ? hashMap.size() + 1 : hashMap.size()];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV3 subModeColor = new SubModeColorV3();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[COLOR_PIECE];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hasBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hasBrightness) {
            SubModeColorV3 subModeColor = new SubModeColorV3();
            subModeColor.brightnessSet = colorStrip.brightnessSet;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i] = modeController;
        }
        return modeControllers;
    }

    public static AbsSingleController[] makeSubModeColor(int[] colors, int[] brightnessArray, boolean isFade, boolean hadFadeController) {
        if (colors == null || colors.length != COLOR_PIECE) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        boolean hadBrightness = brightnessArray != null && brightnessArray.length == COLOR_PIECE;
        int fadeSize = hadFadeController ? 1 : 0;
        AbsSingleController[] modeControllers = new AbsSingleController[hadBrightness ? hashMap.size() + fadeSize + 1 : hashMap.size() + fadeSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV3 subModeColor = new SubModeColorV3();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[COLOR_PIECE];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hadBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadBrightness) {
            SubModeColorV3 subModeColor = new SubModeColorV3();
            subModeColor.brightnessSet = brightnessArray;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            modeControllers[i] = new Gradual4BleWifiController(isFade);
        }
        return modeControllers;
    }

    public static boolean isSetMode2Color4ColorEffect(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opByte == BleProtocolConstants.SINGLE_MODE && modeType == BleProtocol.sub_mode_color_v2;
        }
        return false;
    }

    public static void parsePosColorWithBrightness(byte[] setPosColorBytes, @NonNull SubModeColorV3 subModeColor) {
        if (setPosColorBytes == null || setPosColorBytes.length != 20) return;
        if (subModeColor.rgbSet == null) {
            subModeColor.rgbSet = new int[subModeColor.ctlLight.length];
        }
        if (setPosColorBytes[3] == 0x01) {
            int rgb = ColorUtils.toColor(setPosColorBytes[4], setPosColorBytes[5], setPosColorBytes[6]);
            int kelvin = BleUtil.convertTwoBytesToShort(setPosColorBytes[7], setPosColorBytes[8]);
            int temRgb = ColorUtils.toColor(setPosColorBytes[9], setPosColorBytes[10], setPosColorBytes[11]);
            int rgbValue;
            if (kelvin > 0) {
                rgbValue = temRgb;
            } else {
                rgbValue = rgb;
            }
            /*从低位bit开始描述*/
            boolean[] low8Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[12]);
            for (int i = 0; i < low8Set.length; i++) {
                if (low8Set[i]) {
                    subModeColor.rgbSet[i] = rgbValue;
                }
            }
            int index = 8;
            boolean[] high5Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[13]);
            for (int i = 0; i < high5Set.length - 1; i++) {
                if (high5Set[i]) {
                    subModeColor.rgbSet[index] = rgbValue;
                }
                index++;
            }
        } else if (setPosColorBytes[3] == 0x03) {
            int[] brightnessArray = new int[subModeColor.ctlLight.length];
            for (int i = 0; i < subModeColor.ctlLight.length; i++) {
                brightnessArray[i] = BleUtil.getUnsignedByte(setPosColorBytes[i + 4]);
            }
            subModeColor.brightnessSet = brightnessArray;
        }
    }

    public static SubModeColorV3 makeSubModeColor4Group(Colors colors) {
        SubModeColorV3 subModeColorV2 = new SubModeColorV3();
        subModeColorV2.rgbSet = colors.colorSet;
        subModeColorV2.brightnessSet = colors.brightnessSet;
        return subModeColorV2;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_color;
    }
}