package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.ui.R;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsColorUiMode;

import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.SubModeColorV8;

/**
 * Create by hey on 2021/2/5
 * $612abcf
 */
public class ColorUiModeV8 extends AbsColorUiMode {
    private ColorFragmentV8 fragment = new ColorFragmentV8();

    private ColorUiModeV8(String sku) {
        super(sku);
    }

    public ColorUiModeV8(int goodsType, String sku, String device, int ic, DiySupportV1 diySupport) {
        super(goodsType, sku, device, ic, diySupport);
    }

    public static ColorUiModeV8 makeColorUiModeV54Factor(String sku) {
        return new ColorUiModeV8(sku);
    }

    @Override
    public AbsModeFragment getUiFragment() {
        fragment.makeArguments(getSku());
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_color_mini, R.mipmap.new_control_light_btb_mode_color_mini_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeColorV8 subModeColor = new SubModeColorV8();
        subModeColor.loadLocal();
        return subModeColor;
    }

    public void updateGradientEffect(boolean gradientEffect) {
        fragment.updateGradientEffect(gradientEffect);
    }
}
