package com.govee.dreamcolorlightv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.ihoment.base2app.infra.StorageInfra;

import androidx.annotation.NonNull;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 11/27/20
 * 音乐模式BK$
 */
public class SubModeMusicV1 extends AbsSubMode4Analytic {
    private static final byte auto_type_open = 0;
    private static final byte auto_type_close = 1;
    private static final int min_sensitivity = 0;
    private static final int max_sensitivity = 99;

    private int effect = BleProtocol.value_sub_mode_new_music_energy;
    private int sensitivity = max_sensitivity;
    int subEffect = BleProtocol.value_sub_mode_new_music_rhythm_power;
    private boolean autoRgb = false;
    private int rgb = 0xFFFF0000;

    @Override
    public void loadLocal() {
        SubModeMusicV1 subModeMusicV1 = StorageInfra.get(SubModeMusicV1.class);
        if (subModeMusicV1 == null) return;
        effect = subModeMusicV1.effect;
        sensitivity = subModeMusicV1.sensitivity;
        subEffect = subModeMusicV1.subEffect;
        autoRgb = subModeMusicV1.autoRgb;
        rgb = subModeMusicV1.rgb;
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(this);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_new_music;
    }

    @Override
    public String getAnalyticModeName() {
        return ParamFixedValue.mode_music;
    }

    @Override
    public void parse(byte[] validBytes) {
        this.effect = BleUtil.getUnsignedByte(validBytes[0]);
        int sensitivity = BleUtil.getUnsignedByte(validBytes[1]);
        this.sensitivity = Math.max(min_sensitivity, Math.min(sensitivity, max_sensitivity));
        this.subEffect = BleUtil.getUnsignedByte(validBytes[2]);
        this.autoRgb = validBytes[3] == auto_type_open;
        /*非有颜色字段*/
        if (!this.autoRgb) {
            int r = BleUtil.getUnsignedByte(validBytes[4]);
            int g = BleUtil.getUnsignedByte(validBytes[5]);
            int b = BleUtil.getUnsignedByte(validBytes[6]);
            rgb = ColorUtils.toColor(r, g, b);
        }
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] rgbBytes;
        if (autoRgb) {
            rgbBytes = new byte[3];
        } else {
            rgbBytes = ColorUtils.getRgbBytes(rgb);
        }
        return new byte[]{
                subModeCommandType(),
                (byte) effect,
                (byte) sensitivity,
                (byte) subEffect,
                autoRgb ? auto_type_open : auto_type_close,
                rgbBytes[0],
                rgbBytes[1],
                rgbBytes[2],
        };
    }

    public SubModeMusicV1 copy() {
        SubModeMusicV1 subModeMusic = new SubModeMusicV1();
        subModeMusic.sensitivity = this.sensitivity;
        subModeMusic.autoRgb = this.autoRgb;
        subModeMusic.rgb = this.rgb;
        subModeMusic.effect = this.effect;
        subModeMusic.subEffect = this.subEffect;
        return subModeMusic;
    }

    public void setEffect(int effect) {
        this.effect = effect;
    }

    public void setSensitivity(int sensitivity) {
        this.sensitivity = sensitivity;
    }

    public void setRgb(int rgb) {
        this.rgb = rgb;
    }

    public void setAutoRgb(boolean autoRgb) {
        this.autoRgb = autoRgb;
    }

    public boolean isAutoRgb() {
        return autoRgb;
    }

    public int getEffect() {
        return effect;
    }

    public int getRgb() {
        return rgb;
    }

    public int getSensitivity() {
        return sensitivity;
    }

    public static SubModeMusicV1 parseSubModeMusic4Write(byte[] validBytes) {
        SubModeMusicV1 subModeMusicV1 = new SubModeMusicV1();
        subModeMusicV1.parse(validBytes);
        return subModeMusicV1;
    }

    public boolean isDynamic() {
        return subEffect == BleProtocol.value_sub_mode_new_music_rhythm_power;
    }

    public void setDynamic(boolean dynamic) {
        subEffect = dynamic ? BleProtocol.value_sub_mode_new_music_rhythm_power : BleProtocol.value_sub_mode_new_music_rhythm_soft;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_music;
    }
}