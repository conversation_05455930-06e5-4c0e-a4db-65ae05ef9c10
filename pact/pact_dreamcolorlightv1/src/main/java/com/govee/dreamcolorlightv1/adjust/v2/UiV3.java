package com.govee.dreamcolorlightv1.adjust.v2;


import com.govee.ui.R;
import android.bluetooth.BluetoothDevice;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.custom.LoadingDialog;
import com.govee.base2home.device.IDeviceNet;
import com.govee.base2home.device.net.DeviceIcRequest;
import com.govee.base2home.event.EventBleStatus;
import com.govee.base2home.guide.GuideDialog;
import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.iot.Iot;
import com.govee.base2home.ota.OtaType;
import com.govee.base2home.pact.Command4PtReal;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2home.util.Encode;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.Constant;
import com.govee.base2light.ac.adjust.EventSleepUpdate;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ac.club.EventColorStrip;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.EventDiyApply;
import com.govee.base2light.ac.diy.EventDiyApplyResult;
import com.govee.base2light.ac.diy.EventDiyApplyV2;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v1.EventDiyEffectOp;
import com.govee.base2light.ac.diy.v2.DiyOpM;
import com.govee.base2light.ac.diy.v2.DiyTemplate;
import com.govee.base2light.ac.diy.v2.DiyValue;
import com.govee.base2light.ac.diy.v2.LastDiyConfig;
import com.govee.base2light.ac.diy.v3.AcDiyGroup;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyGroup;
import com.govee.base2light.ac.diy.v3.DiyShortcutManger;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ac.diy.v3.Event2AcDiyGroup;
import com.govee.base2light.ac.diy.v3.EventDiyApply4InModeShowing;
import com.govee.base2light.ac.diy.v3.EventDiyModeShowingChange;
import com.govee.base2light.ac.diy.v3.Util4Diy;
import com.govee.base2light.ac.effect.EffectAc;
import com.govee.base2light.ac.effect.EffectEvent;
import com.govee.base2light.ac.effect.EventEffectSquareOpResult;
import com.govee.base2light.ac.effect.EventScenesEffect;
import com.govee.base2light.ac.music.EventApplyAbsMusic;
import com.govee.base2light.ac.timer.NewShowTimerAcV1;
import com.govee.base2light.ac.timer.NewSleepSetEvent;
import com.govee.base2light.ac.timer.NewTimerSetEventV1;
import com.govee.base2light.ac.timer.NewWakeupSetEvent;
import com.govee.base2light.ac.timer.SleepFailEvent;
import com.govee.base2light.ac.timer.SleepSucEvent;
import com.govee.base2light.ac.timer.Timer;
import com.govee.base2light.ac.timer.TimerResultEvent;
import com.govee.base2light.ac.timer.UpdateTimeEvent;
import com.govee.base2light.ac.timer.WakeupFailEvent;
import com.govee.base2light.ac.timer.WakeupSucEvent;
import com.govee.base2light.ac.update.OtaUpdateAcV1;
import com.govee.base2light.ac.update.OtaUpdateAcV2;
import com.govee.base2light.ac.update.OtaUpdateAcV3;
import com.govee.base2light.aieffect.ui.AILightUI;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.ScenesRgb;
import com.govee.base2light.ble.ScenesRgbIC;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.AbsMultipleControllerV1;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultipleColorStripControllerV1;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.controller.MultipleDiyControllerV1;
import com.govee.base2light.ble.controller.NewTimerV1;
import com.govee.base2light.ble.controller.NewTimerV1Controller;
import com.govee.base2light.ble.controller.OnOffMemoryController;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.ble.controller.SubModeAbsMusic;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.controller.SyncTimeController;
import com.govee.base2light.ble.controller.SyncTimeInfo;
import com.govee.base2light.ble.mic.controller.EventSwitchMicPickUpType;
import com.govee.base2light.ble.mic.controller.MicSetRgbController;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.EventNewMusicOpResult;
import com.govee.base2light.ble.music.EventSetMultiMusicEffect;
import com.govee.base2light.ble.ota.v2.EventOtaPrepareOp;
import com.govee.base2light.ble.ota.v2.OtaFlagV2;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.ble.scenes.EditScenesAc;
import com.govee.base2light.ble.scenes.EventChangeScenes;
import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.colortemp.base.AbsColorTemIUI;
import com.govee.base2light.effectPlay.EffectPlayUI;
import com.govee.base2light.feast.Event2ConnectInDetail;
import com.govee.base2light.feast.FeastUI;
import com.govee.base2light.iot.ResultBrightness;
import com.govee.base2light.iot.ResultColorWc;
import com.govee.base2light.light.EventCutCaliIC;
import com.govee.base2light.light.EventLightStripResult;
import com.govee.base2light.light.EventServiceScenesFresh;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.light.v1.RealtimeColorChangeEvent;
import com.govee.base2light.lowBlueLightControl.ControllerLowBlueLight;
import com.govee.base2light.lowBlueLightControl.LowBlueLightConfig;
import com.govee.base2light.neonlight.recommend.data.BleIotShare;
import com.govee.base2light.pact.AbsDeviceInfo;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.EventSceneCheck4BleIotV1;
import com.govee.base2light.pact.FilterSceneInfo4BleIot;
import com.govee.base2light.pact.IUi4BleIot;
import com.govee.base2light.pact.IUiResult4BleIot;
import com.govee.base2light.pact.ble.IBleOpResult;
import com.govee.base2light.pact.iot.AbsIotOpV1;
import com.govee.base2light.pact.iot.IIotOpResultV1;
import com.govee.base2light.snapshot.EventApplySnapshotCmd;
import com.govee.base2light.snapshot.EventCheckSnapshotEnable;
import com.govee.base2light.snapshot.SnapshotCmdM;
import com.govee.base2light.snapshot.SnapshotUI;
import com.govee.base2light.ui.mode.ChangeModeEvent;
import com.govee.base2light.util.Util4ColorRealtime;
import com.govee.base2light.util.UtilFlag;
import com.govee.dreamcolorlightv1.CutConfig;

import com.govee.dreamcolorlightv1.adjust.Diy;
import com.govee.dreamcolorlightv1.adjust.SnapshotDataUtil;
import com.govee.dreamcolorlightv1.adjust.setting.AcCutCali;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.ble.BleComm;
import com.govee.dreamcolorlightv1.ble.BleIotInfoV1;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.BulbStringColorController;
import com.govee.dreamcolorlightv1.ble.BulbStringColorControllerV2;
import com.govee.dreamcolorlightv1.ble.CheckIcController;
import com.govee.dreamcolorlightv1.ble.EventChangeGradual;
import com.govee.dreamcolorlightv1.ble.EventIcNotify;
import com.govee.dreamcolorlightv1.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.OtaPrepareController;
import com.govee.dreamcolorlightv1.ble.ParamsSubMode4MusicV1;
import com.govee.dreamcolorlightv1.ble.ReadIcController;
import com.govee.dreamcolorlightv1.ble.RefreshIcController;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeColorV3;
import com.govee.dreamcolorlightv1.ble.SubModeColorV4;
import com.govee.dreamcolorlightv1.ble.SubModeColorV6;
import com.govee.dreamcolorlightv1.ble.SubModeColorV8;
import com.govee.dreamcolorlightv1.ble.SubModeMusic;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV1;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV3;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.govee.dreamcolorlightv1.iot.Cmd;
import com.govee.dreamcolorlightv1.iot.CmdBrightness;
import com.govee.dreamcolorlightv1.iot.CmdColorWc;
import com.govee.dreamcolorlightv1.iot.CmdPtReal;
import com.govee.dreamcolorlightv1.iot.CmdStatus;
import com.govee.dreamcolorlightv1.iot.CmdStatusV1;
import com.govee.dreamcolorlightv1.iot.CmdTurn;
import com.govee.dreamcolorlightv1.iot.ICmdPtRealOpResult;
import com.govee.dreamcolorlightv1.iot.ResultPtV1;
import com.govee.dreamcolorlightv1.pact.Comm;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.home.account.config.AccountConfig;
import com.govee.kt.setting.event.Event4PowerOfMemoryOp;
import com.govee.kt.setting.event.Event4PowerOfMemoryOpResult;
import com.govee.matter.MatterManager;
import com.govee.matter.net.IMatterNet;
import com.govee.matter.net.Request4ResetMtInfo;
import com.govee.matter.pact.MtPairProtocol;
import com.govee.matter.pair.MatterInfo;
import com.govee.ui.Cons;
import com.govee.ui.component.BrightnessUI;
import com.govee.ui.component.EffectUI;
import com.govee.ui.component.NewTimerUI;
import com.govee.ui.dialog.ConfirmDialogV1;
import com.govee.ui.dialog.HintDialog1;
import com.ihoment.base2app.Cache;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;
import com.ihoment.base2app.network.Network;
import com.ihoment.base2app.network.Transactions;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.CaughtRunnable;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.ResUtil;
import com.ihoment.base2app.util.ToastUtil;
import com.zhy.android.percent.support.PercentRelativeLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.FragmentActivity;

/**
 * Create by xieyingwu on 2020/3/5
 * ui-v3版本$
 */
public class UiV3 extends AbsColorTemIUI implements IUi4BleIot {
    private static final String TAG = "UiV3";

    private static final int ui_type_def = -1;
    private static final int ui_type_normal = 1;
    private static final int ui_type_fail = 2;

    private static final int max_retry_connect_device_times = 2;

    private final IUiResult4BleIot uiResult;
    protected BleIotInfo info;
    private final ExtV3 ext = new ExtV3();
    protected BleOpV3 bleOp;
    protected IotOpV3 iotOp;

    protected FragmentActivity ac;
    private boolean layoutSuc;/*布局是否已初始化*/
    private boolean destroy;
    private int uiTypeBle = ui_type_def;
    private int uiTypeIot = ui_type_def;

    private NewTimerUI timerUI;
    private EffectPlayUI playUi;
    private SnapshotUI snapshotUI;
    private AILightUI aiLightUI;
    private BrightnessUI brightnessUI;
    private EffectUI effectUI;
    private FeastUI feastUI;
    protected AbsMode4UIV1 modeUI;
    private final List<DiyGroup> curDiyGroups = new ArrayList<>();
    private final Handler handler = new Handler(Looper.getMainLooper());

    private boolean isSwitchMicPickUpType;

    private int diyCode = -1;
    private int diyTemplateCode = -1;

    private int retryConnectDeviceTimes;/*尝试连接设备次数*/
    /**
     * ======================================matter相关======================================
     */
    private MatterInfo matterInfo = null;
    private boolean hasUpdateMatterInfo = false;

    private final IBleOpResult bleOpResult = new IBleOpResult() {
        @Override
        public void onOffChange() {
            showLoading();
        }

        @Override
        public void infoOver() {
            uiTypeBle = ui_type_normal;
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
            if (hasUpdateMatterInfo) {
                return;
            }
            //获取matter信息更新到服务器
            SafeLog.Companion.i("xiaobing", () -> "UiV3--infoOver-->info->" + JsonUtil.toJson(info));
            if (MtPairProtocol.INSTANCE.supportMatter(info.goodsType, info.sku, info.versionSoft, info.versionHard, info.wifiSoftVersion, info.wifiHardVersion)) {
                hasUpdateMatterInfo = true;
                //更新逻辑在该类中统一处理
                MatterManager.Companion.instance().getMtInfo4AddOrUp(Ble.getInstance, new MatterManager.OnMtInfo4AddUpListener() {
                    @Override
                    public void getBaseInfo(boolean isSupportMatter, boolean hasPairNet, boolean hasAddedEcology) {
                        if (matterInfo == null) {
                            matterInfo = new MatterInfo();
                        }
                        matterInfo.setSupportMatter(isSupportMatter);
                        matterInfo.setHasPairNet(hasPairNet);
                        matterInfo.setHasAddedEcology(hasAddedEcology);
                    }

                    @Override
                    public void getGid(@NonNull String gid) {
                        if (matterInfo != null) {
                            matterInfo.setGid(gid);
                        }
                    }

                    @Override
                    public void getMatterId(@NonNull String matterId) {
                        if (matterInfo != null) {
                            matterInfo.setMatterId(matterId);
                        }
                    }

                    @Override
                    public void finish() {
                        //更新gId,matterId,不用去管结果
                        if (matterInfo != null && matterInfo.hasMatterInfo()) {
                            Request4ResetMtInfo mtInfoRequest = new Request4ResetMtInfo(new Transactions().createTransaction(), info.sku, info.device, matterInfo.getGid(), matterInfo.getMatterId());
                            Cache.get(IMatterNet.class).resetMatterInfo(mtInfoRequest).enqueue(new Network.IHCallBack<>(mtInfoRequest));
                        }
                    }
                });
            }
            //如果支持低蓝光，则需要读取低蓝光设置
            boolean supportLowBlueLight = LowBlueLightConfig.Companion.checkSkuSupport(info.goodsType, info.sku, info.versionSoft);
            if (supportLowBlueLight) {
                ControllerLowBlueLight controllerLowBlueLight = new ControllerLowBlueLight(false);
                bleOp.executeOp(controllerLowBlueLight);
            }
        }

        @Override
        public void noConnect() {
            uiTypeBle = ui_type_fail;
            /*蓝牙断开，需要检测iot是否可用*/
            bleUnable2CheckIot();
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
        }

        @Override
        public void bleWrite(byte proCommandType, boolean result) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "bleWrite() proCommandType = " + proCommandType + " ; result = " + result);
            }
            if (result && proCommandType == BleProtocol.SINGLE_GRADUAL_CHANGE_4_WIFI_BLE) {
                /*若当前就是颜色模式，则提示当前仅仅设置渐变逻辑*/
                subModeColor2Gradual();
            }
            checkUi();
            diyCode = -1;
            diyTemplateCode = -1;
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(result ? EventEffectSquareOpResult.result_op_suc : EventEffectSquareOpResult.result_op_fail);
        }
    };

    private void subModeColor2Gradual() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeColorV3) {
            ((SubModeColorV3) subMode).opType = SubModeColorV3.op_type_gradual;
        }
        if (subMode instanceof SubModeColorV2) {
            ((SubModeColorV2) subMode).opType = SubModeColorV2.op_type_gradual;
        }
        if (subMode instanceof SubModeColor) {
            ((SubModeColor) subMode).opType = SubModeColor.op_type_gradual;
        }
    }

    private void bleUnable2CheckIot() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "bleUnable2CheckIot() uiTypeIot = " + uiTypeIot + " ; retryConnectDeviceTimes = " + retryConnectDeviceTimes);
        }
        if (uiTypeIot != ui_type_normal) {
            if (retryConnectDeviceTimes < max_retry_connect_device_times) {
                retryConnectDeviceTimes++;
                if (iotOp.isOpCommEnable()) {
                    iotOp.readCmd(new CmdStatus());
                } else {
                    uiTypeIot = ui_type_def;
                    iotOp.beOpComm(info.sku, info.device, info.topic);
                }
            }
        }
    }

    private final IIotOpResultV1 iotOpResult = new IIotOpResultV1() {
        @Override
        public void noConnectIot() {
            uiTypeIot = ui_type_fail;
            /*iot断开，检测蓝牙是否可用*/
            iotUnable2CheckBle();
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
        }

        @Override
        public void cmdWriteFail(boolean overtime, AbsCmd absCmd) {
            String cmd = absCmd.getCmd();
            if (absCmd instanceof CmdPtReal && Cmd.ptReal.equals(cmd)) {
                CmdPtReal cmdPt = (CmdPtReal) absCmd;
                iotOpPtFail(cmdPt);
            }
            if (overtime) {
                uiTypeIot = ui_type_fail;
                /*iot断开，检测蓝牙是否可用*/
                iotUnable2CheckBle();
            }
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
        }

        @Override
        public void cmdWriteSuc(AbsCmd absCmd) {
            /*暂时不支持其他非透传指令的写操作响应*/
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
        }

        @Override
        public void cmdWriteSuc4Pt(AbsCmd absCmd, String cmdJsonStr) {
            if (absCmd instanceof CmdPtReal) {
                ICmdPtRealOpResult opResult = ((CmdPtReal) absCmd).opResult;
                if (opResult != null) {
                    opResult.scu();
                    checkUi();
                    return;
                }
            }
            if (absCmd instanceof CmdPtReal && ((CmdPtReal) absCmd).opVersion == CmdPtReal.OP_VERSION_ABS_MUSIC_MODE) {
                Mode mode = new Mode();
                SubModeAbsMusic subModeAbsMusic = new SubModeAbsMusic();
                subModeAbsMusic.setMusicCode(((CmdPtReal) absCmd).musicCode);
                subModeAbsMusic.setSensitivity(((CmdPtReal) absCmd).musicSensitivity);
                mode.subMode = subModeAbsMusic;
                info.mode = mode;
                checkUi();
                EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
                EventApplyAbsMusic.EventApplyAbsMusicResult.sendEventApplyAbsMusicResult(true);
                return;
            }
            if (absCmd instanceof CmdPtReal) {
                boolean parsePtReal4ColorMode = parsePtReal4ColorMode((CmdPtReal) absCmd);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "cmdWriteSuc4Pt() parsePtReal4ColorMode = " + parsePtReal4ColorMode);
                }
                if (!parsePtReal4ColorMode) {
                    boolean otherMode2ColorModePt = ((CmdPtReal) absCmd).isOtherMode2ColorModePtWithBrightness();
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "cmdWriteSuc4Pt() otherMode2ColorModePt = " + otherMode2ColorModePt);
                    }
                    if (otherMode2ColorModePt) {
                        /*切颜色模式透传*/
                        String ptRealOpJsonStr = Iot.getJsonObjectStr(cmdJsonStr, Cmd.getCmdReadParseKey(Cmd.ptReal));
                        ResultPtV1 resultPt = JsonUtil.fromJson(ptRealOpJsonStr, ResultPtV1.class);
                        if (resultPt != null) {
                            List<byte[]> ptBytes = resultPt.getPtBytes();
                            int subModeColorVersion = Support.supportPartBrightness(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, "");
                            boolean subModeColorPiece10 = Support.isSubModeColorPiece10(info.goodsType);
                            boolean subModeColorPiece12 = Support.isSubModeColorPiece12(info.goodsType);
                            boolean subModeColorPiece20 = Support.isSubModeColorPiece20(info.goodsType);
                            boolean h612de = Support.supportChangePieces20(info.goodsType, info.sku);
                            boolean h612abcf = Support.goodsTypeH612x(info.goodsType) && !h612de;
                            if (subModeColorPiece10) {
                                subModeColorVersion = 3;
                            } else if (subModeColorPiece12) {
                                subModeColorVersion = 4;
                            } else if (subModeColorPiece20 || h612de) {
                                subModeColorVersion = 5;
                            } else if (h612abcf) {
                                subModeColorVersion = 6;
                            }
                            Mode mode = CmdStatusV1.parsePtRealColorModeV1(subModeColorVersion, ptBytes, info.sku, info.goodsType, info.pactType, info.pactCode);
                            if (mode != null) {
                                info.mode = mode;
                                ISubMode subMode = mode.subMode;
                                if (subMode instanceof SubModeColor) {
                                    ext.gradual = ((SubModeColor) subMode).gradual;
                                } else if (subMode instanceof SubModeColorV2) {
                                    ext.gradual = ((SubModeColorV2) subMode).gradual;
                                } else if (subMode instanceof SubModeColorV3) {
                                    ext.gradual = ((SubModeColorV3) subMode).gradual;
                                } else if (subMode instanceof SubModeColorV6) {
                                    ext.gradual = ((SubModeColorV6) subMode).gradual;
                                } else if (subMode instanceof SubModeColorV8) {
                                    ext.gradual = ((SubModeColorV8) subMode).getGradual();
                                }
                            }
                        }
                    } else {
                        /*其他透传指令*/
                        parseWritePt((CmdPtReal) absCmd);
                    }
                }
                checkUi();
                EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
            }
        }

        @Override
        public void cmdRead(String cmd, String cmdJsonStr) {
            if (uiTypeBle == ui_type_normal) return;/*蓝牙通信可用，忽略iot通信信息*/
            if (Cmd.brightness.equals(cmd)) {
                /*解析亮度*/
                ResultBrightness resultBrightness = JsonUtil.fromJson(cmdJsonStr, ResultBrightness.class);
                if (resultBrightness != null) {
                    ext.brightness = resultBrightness.getBrightness();
                }
            } else if (Cmd.colorwc.equals(cmd)) {
                /*解析冷暖色*/
                ResultColorWc resultColorWc = JsonUtil.fromJson(cmdJsonStr, ResultColorWc.class);
                if (resultColorWc != null) {
                    int color = resultColorWc.getColor();
                    boolean subModeColorPiece10 = Support.isSubModeColorPiece10(info.goodsType);
                    boolean subModeColorPiece12 = Support.isSubModeColorPiece12(info.goodsType);
                    boolean subModeColorPiece20 = Support.isSubModeColorPiece20(info.goodsType);
                    boolean h612de = Support.supportChangePieces20(info.goodsType, info.sku);
                    boolean h612abcf = Support.goodsTypeH612x(info.goodsType) && !h612de;
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "cmdRead() subModeColorPiece10 = " + subModeColorPiece10 + " ; subModeColorPiece12 = " + subModeColorPiece12);
                    }
                    ISubMode subMode;
                    if (subModeColorPiece10) {
                        subMode = SubModeColorV3.makeSubModeColor(color);
                    } else if (subModeColorPiece12) {
                        subMode = SubModeColorV4.makeSubModeColor(color);
                    } else if (subModeColorPiece20 || h612de) {
                        subMode = SubModeColorV6.makeSubModeColor(color);
                    } else if (h612abcf) {
                        subMode = SubModeColorV8.Companion.makeSubModeColor(color);
                    } else {
                        boolean colorTemRealDevice = Support.isColorTemRealDevice(info.goodsType);
                        if (colorTemRealDevice && resultColorWc.isColorTem()) {
                            SubModeColorV2 subModeColorV2 = new SubModeColorV2();
                            subModeColorV2.ctRgb = color;
                            subModeColorV2.kelvin = resultColorWc.getColorTemInKelvin();
                            subMode = subModeColorV2;
                        } else {
                            subMode = SubModeColorV2.makeSubModeColor(color);
                        }
                    }
                    Mode mode = new Mode();
                    mode.subMode = subMode;
                    info.mode = mode;
                }
            } else if (Cmd.ptReal.equals(cmd)) {
                parseIcNotify(cmdJsonStr);
            }
            checkUi();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
        }

        @Override
        public void cmdOnline(String softVersion, String cmdJsonStr, String wifiSoftVersion) {
            if (!TextUtils.isEmpty(wifiSoftVersion)) {
                ext.wifiSoftVersion = wifiSoftVersion;
            }
            CmdStatusV1 statusV1 = CmdStatusV1.parseJsonWithBrightness(info.goodsType, info.sku, info.pactType, info.pactCode, softVersion, cmdJsonStr);
            uiTypeIot = statusV1 == null ? ui_type_fail : ui_type_normal;
            if (uiTypeBle == ui_type_normal) return;/*蓝牙通信可用，忽略iot通信信息*/
            if (statusV1 == null) {
                /*iot断开，检测蓝牙是否可用*/
                iotUnable2CheckBle();
            } else {
                info.open = statusV1.on;
                ext.brightness = statusV1.brightness;

                info.versionSoft = statusV1.softVersion;
                info.powerOffMemoryValue = statusV1.powerOffMemory;
                Mode mode = statusV1.mode;
                checkMusicMode(mode);
                info.mode = mode;
                if (isSubColorMode(info.mode)) {
                    ISubMode subMode = info.mode.subMode;
                    if (subMode instanceof SubModeColorV2) {
                        ext.gradual = ((SubModeColorV2) subMode).gradual;
                    } else if (subMode instanceof SubModeColorV3) {
                        ext.gradual = ((SubModeColorV3) subMode).gradual;
                    } else if (subMode instanceof SubModeColorV6) {
                        ext.gradual = ((SubModeColorV6) subMode).gradual;
                    }
                }
                ext.timer1 = statusV1.timer1;
                ext.timer2 = statusV1.timer2;
                ext.timer3 = statusV1.timer3;
                ext.timer4 = statusV1.timer4;

                ext.wakeUpInfo = statusV1.wakeUpInfo;
                ext.sleepInfo = statusV1.sleepInfo;
                if (LowBlueLightConfig.Companion.checkSkuSupport(info.goodsType, info.sku, info.versionSoft)) {
                    info.lowBlueLightValue = statusV1.lowBlueLightValue;
                    info.isLowBlueLightOpen = statusV1.isLowBlueLightOpen;
                }
                if (info instanceof BleIotInfoV1 && statusV1.icResult != null) {
                    info.ic_sub_1 = statusV1.icResult[0];
                    ((BleIotInfoV1) info).sectionNum1 = statusV1.icResult[1];
                    info.ic_sub_2 = statusV1.icResult[2];
                    ((BleIotInfoV1) info).sectionNum2 = statusV1.icResult[3];
                }
                /*通知事件*/
                TimerResultEvent.sendTimerResultEvent(true, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
                SleepSucEvent.sendSleepSucEvent(false, ext.sleepInfo);
                WakeupSucEvent.sendWakeUpSucEvent(false, ext.wakeUpInfo);
                EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
            }
            checkUi();
        }
    };

    private void parseIcNotify(String cmdJsonStr) {
        LogInfra.Log.i(TAG, "parseIcNotify()");
        String ptRealOpJsonStr = Iot.getJsonObjectStr(cmdJsonStr, Cmd.getCmdReadParseKey(Cmd.ptReal));
        LogInfra.Log.i(TAG, "parseIcNotify() ptRealOpJsonStr = " + ptRealOpJsonStr);
        ResultPtV1 resultPt = JsonUtil.fromJson(ptRealOpJsonStr, ResultPtV1.class);
        if (resultPt == null) return;
        List<byte[]> ptBytes = resultPt.getPtBytes();
        if (ptBytes == null || ptBytes.isEmpty()) return;
        int[] result = CmdStatusV1.parseIc(ptBytes);
        LogInfra.Log.i(TAG, "parseIcNotify() result = " + Arrays.toString(result));
        if (result[0] == 1) {
            updateIc(result[1]);
        }
    }

    private boolean parsePtReal4ColorMode(CmdPtReal cmdPtReal) {
        // TODO: 2023/12/25 解析h612abcf
        boolean subModeColorPiece10 = Support.isSubModeColorPiece10(info.goodsType);
        boolean subModeColorPiece12 = Support.isSubModeColorPiece12(info.goodsType);
        boolean subModeColorPiece20 = Support.isSubModeColorPiece20(info.goodsType) || Support.supportChangePieces20(info.goodsType, info.sku);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parsePtReal4ColorMode() subModeColorPiece10 = " + subModeColorPiece10 + " ; subModeColorPiece12 = " + subModeColorPiece12);
        }
        if (subModeColorPiece20) {
            SubModeColorV6 ptReal4ColorModeV6 = cmdPtReal.isPtReal4ColorModeV6();
            if (ptReal4ColorModeV6 != null) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "parsePtReal4ColorMode() 采用ptReal进行颜色设置");
                }
                Mode mode = new Mode();
                mode.subMode = ptReal4ColorModeV6;
                info.mode = mode;
                return true;
            }
        }
        if (subModeColorPiece12) {
            SubModeColorV4 ptReal4SetPartColor4V4 = cmdPtReal.isPtReal4ColorModeV4();
            if (ptReal4SetPartColor4V4 != null) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "parsePtReal4ColorMode() 采用ptReal进行颜色设置");
                }
                Mode mode = new Mode();
                mode.subMode = ptReal4SetPartColor4V4;
                info.mode = mode;
                return true;
            }
        }
        if (subModeColorPiece10) {
            SubModeColorV3 ptReal4SetPartColor4V3 = cmdPtReal.isPtReal4SetPartColor4V3();
            if (ptReal4SetPartColor4V3 != null) {
                /*采用ptReal进行颜色设置-更新刷新ui逻辑*/
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "parsePtReal4ColorMode() 采用ptReal进行颜色设置");
                }
                Mode mode = new Mode();
                mode.subMode = ptReal4SetPartColor4V3;
                ext.gradual = ptReal4SetPartColor4V3.gradual;
                info.mode = mode;
                return true;
            }
        }
        SubModeColorV2 subModeColor = cmdPtReal.isPtReal4SetPartColor();
        if (subModeColor != null) {
            /*采用ptReal进行颜色设置-更新刷新ui逻辑*/
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "parsePtReal4ColorMode() 采用ptReal进行颜色设置");
            }
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            ext.gradual = subModeColor.gradual;
            info.mode = mode;
            return true;
        }
        boolean isSetColor4Strip = cmdPtReal.isSetColorStrip();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parsePtReal4ColorMode() isSetColor4Strip = " + isSetColor4Strip);
        }
        if (!isSetColor4Strip) return false;
        boolean isSetColor4StripWithBrightness = cmdPtReal.isSetColorStripWithBrightness();
        if (isSetColor4StripWithBrightness) {
            ISubMode subMode;
            if (subModeColorPiece12) {
                subMode = cmdPtReal.setPartColor4ColorStripWithBrightness4V4();
            } else if (subModeColorPiece10) {
                subMode = cmdPtReal.setPartColor4ColorStripWithBrightness4V3();
            } else {
                subMode = cmdPtReal.setPartColor4ColorStripWithBrightness();
            }
            if (subMode == null) return false;
            Mode mode = new Mode();
            mode.subMode = subMode;
            info.mode = mode;
        } else {
            SubModeColor subModeColor4Strip = cmdPtReal.setPartColor4ColorStrip();
            if (subModeColor4Strip == null) return false;
            Mode mode = new Mode();
            mode.subMode = subModeColor4Strip;
            info.mode = mode;
        }
        return true;
    }

    private void checkMusicMode(Mode mode) {
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof ParamsSubMode4MusicV1) {
            boolean newProtocol4SupportMultiMusicMode = Support.newProtocol4SupportMultiMusicMode(info.goodsType, info.versionSoft, info.versionHard);
            int musicVersion = newProtocol4SupportMultiMusicMode ? 1 : 0;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicMode() newProtocol4SupportMultiMusicMode = " + newProtocol4SupportMultiMusicMode + " ; musicVersion = " + musicVersion);
            }
            mode.subMode = ((ParamsSubMode4MusicV1) subMode).toSupportSubMode(musicVersion);
        }
    }

    private void parseWritePt(CmdPtReal cmdPtReal) {
        Byte opCommandByte = cmdPtReal.getOpCommandByte();
        if (opCommandByte == null) return;
        byte[] opCommandBytes = cmdPtReal.getOpCommandBytes();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parseWritePt() opCommandBytes = " + BleUtil.bytesToHexString(opCommandBytes));
        }
        if (opCommandBytes == null || opCommandBytes.length != 20) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "parseWritePt() ble协议不完整");
            }
            return;
        }
        byte[] valid17Bytes = BleUtil.parseValidBleBytes(opCommandBytes);
        if (BleProtocolConstants.SINGLE_MODE == opCommandByte) {
            /*模式操作成功*/
            byte subModeType = valid17Bytes[0];
            byte[] subModeValidBytes = new byte[valid17Bytes.length - 1];
            System.arraycopy(valid17Bytes, 1, subModeValidBytes, 0, subModeValidBytes.length);
            boolean newProtocol4SupportMultiMusicMode = Support.newProtocol4SupportMultiMusicMode(info.goodsType, info.versionSoft, info.versionHard);
            int multiNewMusicVersion = newProtocol4SupportMultiMusicMode ? 1 : 0;
            boolean subModeColorPiece10 = Support.isSubModeColorPiece10(info.goodsType);
            int colorVersion = subModeColorPiece10 ? 1 : 0;
            ISubMode subMode = Mode.parseWriteSubMode(colorVersion, multiNewMusicVersion, subModeType, subModeValidBytes);
            if (subMode != null) {
                Mode mode = new Mode();
                mode.subMode = subMode;
                info.mode = mode;
                if (subMode instanceof SubModeMusicV3) {
                    EventNewMusicOpResult.sendEventNewMusicOpResult(true, ((SubModeMusicV3) subMode).getMusicCode());
                } else if (subMode instanceof SubModeNewDiy) {
                    EventDiyApplyResult.sendEventDiyApplyResult(true, ((SubModeNewDiy) subMode).getDiyCode());
                } else if (subMode instanceof SubModeScenes) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parseResultPt() diyCode = " + diyCode + " ; diyTemplateCode = " + diyTemplateCode);
                    }
                    if (diyCode != -1 && diyTemplateCode != -1) {
                        int effect = ((SubModeScenes) subMode).getEffect();
                        if (effect == diyTemplateCode) {
                            /*表明当前是diy模版效果应用成功*/
                            mode.subMode = new SubModeNewDiy(diyCode);
                            EventDiyApplyResult.sendEventDiyApplyResult(true, diyCode);
                        }
                    }
                }
                diyCode = -1;
                diyTemplateCode = -1;
            }
        } else if (BleProtocolConstants.SINGLE_WAKEUP == opCommandByte) {
            /*唤醒操作成功*/
            WakeUpInfo wakeUpInfo = Support.parseWakeUpInfo(info.goodsType, info.sku, info.pactType, info.pactCode, opCommandBytes);
            if (wakeUpInfo != null) {
                ext.wakeUpInfo = wakeUpInfo;
                WakeupSucEvent.sendWakeUpSucEvent(true, ext.wakeUpInfo);
            }
        } else if (BleProtocolConstants.SINGLE_SLEEP == opCommandByte) {
            /*睡眠操作成功*/
            SleepInfo sleepInfo = Support.parseSleepInfo(info.goodsType, info.sku, info.pactType, info.pactCode, opCommandBytes);
            if (sleepInfo != null) {
                ext.sleepInfo = sleepInfo;
                SleepSucEvent.sendSleepSucEvent(true, ext.sleepInfo);
            }
        } else if (BleProtocolConstants.SINGLE_NEW_TIME_V1 == opCommandByte) {
            /*定时操作成功*/
            int group = NewTimerV1Controller.parseGroup(opCommandBytes);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "parseWritePt() group = " + group);
            }
            if (group == BleUtil.getUnsignedByte((byte) 0xFF)) {
                /*解析全部定时*/
                List<Timer> timers = NewTimerV1Controller.parseAllTimer(opCommandBytes);
                if (timers != null && !timers.isEmpty()) {
                    int index = 0;
                    for (Timer timer : timers) {
                        if (index == 0) {
                            ext.timer1 = timer;
                        } else if (index == 1) {
                            ext.timer2 = timer;
                        } else if (index == 2) {
                            ext.timer3 = timer;
                        } else if (index == 3) {
                            ext.timer4 = timer;
                        }
                        index++;
                    }
                }
            } else {
                Timer timer = NewTimerV1Controller.parseTimer(opCommandBytes);
                if (timer != null) {
                    if (group == 0) {
                        ext.timer1 = timer;
                    } else if (group == 1) {
                        ext.timer2 = timer;
                    } else if (group == 2) {
                        ext.timer3 = timer;
                    } else if (group == 3) {
                        ext.timer4 = timer;
                    }
                }
            }
            TimerResultEvent.sendTimerResultEvent(true, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
        } else if (BleProtocol.SINGLE_GRADUAL_CHANGE_4_WIFI_BLE == opCommandByte) {
            ext.gradual = Gradual4BleWifiController.parseGradual(opCommandBytes);
            subModeColor2Gradual();
        }
    }

    private void iotUnable2CheckBle() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "iotUnable2CheckBle() uiTypeBle = " + uiTypeBle + " ; retryConnectDeviceTimes = " + retryConnectDeviceTimes);
        }
        if (uiTypeBle != ui_type_normal) {
            if (retryConnectDeviceTimes < max_retry_connect_device_times) {
                retryConnectDeviceTimes++;
                boolean opCommEnable = bleOp.isOpCommEnable();
                if (!opCommEnable) {
                    uiTypeBle = ui_type_def;
                    BluetoothDevice bluetoothDevice = null;
                    if (uiResult != null) bluetoothDevice = uiResult.getCurBluetoothDevice();
                    bleOp.beOpComm(bluetoothDevice);
                }
            }

        }
    }

    private void iotOpPtFail(CmdPtReal cmdPt) {
        Byte opCommandByte = cmdPt.getOpCommandByte();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "iotOpPtFail() opCommandByte = " + opCommandByte);
        }
        if (opCommandByte == null) return;
        ICmdPtRealOpResult stripResult = cmdPt.opResult;
        if (stripResult != null) {
            stripResult.fail();
            return;
        }
        if (BleProtocolConstants.SINGLE_MODE == opCommandByte) {
            /*模式操作失败*/
            byte[] opCommandBytes = cmdPt.getOpCommandBytes();
            if (opCommandBytes != null && opCommandBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(opCommandBytes);
                Mode mode = new Mode();
                mode.parse(valid17Bytes);
                ISubMode subMode = mode.subMode;
                if (subMode instanceof SubModeNewDiy) {
                    int diyCode = ((SubModeNewDiy) subMode).getDiyCode();
                    EventDiyApplyResult.sendEventDiyApplyResult(false, diyCode);
                }
            }
        } else if (BleProtocolConstants.SINGLE_WAKEUP == opCommandByte) {
            /*唤醒操作失败*/
            WakeupFailEvent.sendWakeupFailEvent(true);
        } else if (BleProtocolConstants.SINGLE_SLEEP == opCommandByte) {
            /*睡眠操作失败*/
            SleepFailEvent.sendSleepFailEvent(true);
        } else if (BleProtocolConstants.SINGLE_NEW_TIME_V1 == opCommandByte) {
            /*定时操作失败*/
            TimerResultEvent.sendTimerResultEventFail(true);
        }
    }

    public UiV3(IUiResult4BleIot uiResult, BleIotInfo info) {
        this.uiResult = uiResult;
        this.info = info;
        bleOp = new BleOpV3(info, ext);
        iotOp = new IotOpV3();
    }

    private void registerEvent(boolean register) {
        if (register) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this);
            }
        } else {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        }
    }

    @Override
    public boolean isSupportProtocol(int pactType, int pactCode) {
        return Support.uiV34bleIotProtocol(pactType, pactCode);
    }

    @Override
    public void destroy() {
        if (!destroy) {
            destroy = true;
            registerEvent(false);
            bleOp.destroy();
            iotOp.destroy();
            hideLoading();
            ac = null;
            if (timerUI != null) timerUI.onDestroy();
            if (playUi != null) playUi.onDestroy();
            if (snapshotUI != null) snapshotUI.onDestroy();
            if (aiLightUI != null) aiLightUI.onDestroy();
            if (brightnessUI != null) brightnessUI.onDestroy();
            if (modeUI != null) modeUI.onDestroy();
            if (effectUI != null) effectUI.onDestroy();
            if (feastUI != null) feastUI.onDestroy();
            hideDiyGuide();
            if (Support.isGoodsTypeH61A9(info.goodsType)) {
                SubModeColorV2.len = 15;
            }
        }
    }

    @Override
    public void onOffChange() {
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            showLoading();
            int delay = 0;
            if (modeUI.isMicMode()) {
                delay = 100;
            }
            handler.postDelayed(new CaughtRunnable() {
                @Override
                protected void runSafe() {
                    SwitchController switchController = new SwitchController(!info.open);
                    bleOp.executeOp(switchController);
                }
            }, delay);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            CmdTurn cmdTurn = new CmdTurn(!info.open);
            iotOp.writeCmd(cmdTurn);
        }
    }

    @Override
    public void toUpdateAc(@NonNull AppCompatActivity ac) {
        if (info.canUpdate() && bleOp.isOpCommEnable()) {
            int defSkuRes = ThemeM.getDefSkuRes(info.sku, info.spec);
            boolean bkOta = OtaType.isBKOtaV1(info.versionHard);
            boolean frkOtaV1 = OtaType.isFRKOtaV1(info.versionHard);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "toUpdateAc() bkOta = " + bkOta + " ; frkOtaV1 = " + frkOtaV1);
            }
            if (frkOtaV1) {
                OtaUpdateAcV3.jump2OtaUpdateAcV3(ac, info.sku, info.deviceName, info.versionSoft, defSkuRes, info.checkVersion);
            } else if (bkOta) {
                OtaUpdateAcV2.jump2OtaUpdateAcV2(ac, info.sku, info.deviceName, info.versionSoft, defSkuRes, info.checkVersion);
            } else {
                OtaUpdateAcV1.jump2OtaUpdateAcV1(ac, info.sku, info.deviceName, info.versionSoft, defSkuRes, info.checkVersion);
            }
        }
    }

    @Override
    public void layoutDetail(@NonNull AppCompatActivity ac, @NonNull PercentRelativeLayout contentParent, int headerId) {
        /*注册事件*/
        registerEvent(true);
        checkMicMode();
        this.ac = ac;
        if (!layoutSuc) {
            layoutSuc = true;
            dynamicAddView(ac, contentParent, headerId);
        }
        bleOp.setOpResult(bleOpResult);
        iotOp.setOpResult(iotOpResult);
        /*开始进行op通信*/
        startOpComm();
        //霓虹灯推荐列表用到
        BleIotShare.INSTANCE.setBle(bleOp.getBle());
        BleIotShare.INSTANCE.setInfo(info);
        BleIotShare.INSTANCE.setIotOp(iotOp);

        /*通知刷新DIY模式*/
        EventDiyModeShowingChange.sendEventDiyModeShowingChange();
    }

    /**
     * 是否支持盛宴入口
     *
     * @return
     */
    protected boolean supportFeast() {
        return false;
    }

    /**
     * 是否支持ic刷新
     *
     * @return
     */
    protected boolean supportIcFresh() {
        return Support.goodsTypeH612x(info.goodsType);
    }

    private void dynamicAddView(@NonNull AppCompatActivity ac, @NonNull PercentRelativeLayout contentParent, int headerId) {
        /*初始化ui组件*/
        int[] ids = new int[]{100, 101, 102, 103, 104, 105, 106, 107, 108};
        int effectUiId;
        if (supportFeast()) {
            feastUI = new FeastUI(ac, info.sku, info.device, info.goodsType, R.mipmap.new_control_icon_mini_light_music_base, R.string.str_music_feast, FeastUI.FeastType.MUSIC);
            View fucViewFeast = feastUI.getFucView();
            fucViewFeast.setId(ids[3]);
            addViewMargin(contentParent, fucViewFeast, headerId, feastUI.getWidth(), feastUI.getHeight(), AppUtil.getScreenWidth() * 9 / 750);
            effectUI = new EffectUI(ac);
            View fucViewEffect = effectUI.getFucView();
            fucViewEffect.setId(ids[4]);
            addViewMargin(contentParent, fucViewEffect, fucViewFeast.getId(), effectUI.getWidth(), effectUI.getHeight(), AppUtil.getScreenWidth() * 5 / 750);
            effectUiId = fucViewEffect.getId();
        } else {
            effectUI = new EffectUI(ac);
            View fucViewEffect = effectUI.getFucView();
            fucViewEffect.setId(ids[4]);
            addViewMargin(contentParent, fucViewEffect, headerId, effectUI.getWidth(), effectUI.getHeight(), AppUtil.getScreenWidth() * 16 / 750);
            effectUiId = fucViewEffect.getId();
        }
        /*添加布局-定时*/
        timerUI = new NewTimerUI(ac);
        View fucViewTimer = timerUI.getFucView();
        fucViewTimer.setId(ids[0]);
        addViewMargin(contentParent, fucViewTimer, effectUiId, timerUI.getWidth(), timerUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);

        /*添加布局-自动播放*/
        playUi = new EffectPlayUI(ac, info, getDiySupport(), Support.getSupportScenesOpSet(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard));
        boolean noSupportPlayList = Support.noSupportPlayList(info.goodsType, info.pactType, info.pactCode);
        LogInfra.Log.i(TAG, "dynamicAddView() noSupportPlayList = " + noSupportPlayList);
        /*不支持的场景下-才需要重置该条件，要不统一按照构建时的条件判断*/
        if (noSupportPlayList) {
            playUi.setSupportSnapshot(false);
        }
        View fucViewPlay = playUi.getFucView();
        fucViewPlay.setId(ids[7]);
        addViewMargin(contentParent, fucViewPlay, fucViewTimer.getId(), playUi.getWidth(), playUi.getHeight(), AppUtil.getScreenWidth() * 5 / 375);

        /*添加布局-ai灯效*/
        int belowIdForSnapshot = fucViewPlay.getId();
        if (Support.supportAiLight(info.goodsType, info.sku)) {
            aiLightUI = new AILightUI(ac, bleOp, iotOp, null, info);
            View fucViewAILight = aiLightUI.getFucView();
            fucViewAILight.setId(ids[8]);
            addViewMargin(contentParent, fucViewAILight, fucViewPlay.getId(), aiLightUI.getWidth(), aiLightUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
            belowIdForSnapshot = fucViewAILight.getId();
        }

        snapshotUI = new SnapshotUI(ac, info);
        /*添加布局-快照*/
        View fucViewSnapshot = snapshotUI.getFucView();
        fucViewSnapshot.setId(ids[6]);
        addViewMargin(contentParent, fucViewSnapshot, belowIdForSnapshot, snapshotUI.getWidth(), snapshotUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);

        /*添加布局-亮度*/
        brightnessUI = new BrightnessUI(ac, 100, 1, false);
        View fucViewBrightness = brightnessUI.getFucView();
        fucViewBrightness.setId(ids[1]);
        addViewMargin(contentParent, fucViewBrightness, fucViewSnapshot.getId(), brightnessUI.getWidth(), brightnessUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
        /*添加布局-mode*/
        modeUI = new ModeUiV3(ac, info.sku, info.device, info.goodsType, info.ic, getDiySupport(), Support.noSupportHighColor4SpecialSku(info.sku, info.versionHard));
        View fucViewMode = modeUI.getFucView();
        fucViewMode.setId(ids[2]);
        addViewMargin(contentParent, fucViewMode, fucViewBrightness.getId(), modeUI.getWidth(), modeUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventFreshIc(EventOpFreshIc event) {
        LogInfra.Log.i(TAG, "onEventFreshIc()");
        onRefreshIc();
    }

    private void onRefreshIc() {
        LogInfra.Log.i(TAG, "onRefreshIc()");
        if (bleOp.isOpCommEnable()) {
            RefreshIcController refreshIcController = new RefreshIcController();
            bleOp.executeOp(refreshIcController);
            EventOpFreshIcResult.sendEventOpFreshIcResult(EventOpFreshIcResult.RESULT_TYPE_FRESH_OP, true);
            return;
        }
        if (iotOp.isOpCommEnable()) {
            CmdPtReal cmdPtReal = new CmdPtReal(new RefreshIcController());
            iotOp.writeCmd(cmdPtReal);
            EventOpFreshIcResult.sendEventOpFreshIcResult(EventOpFreshIcResult.RESULT_TYPE_FRESH_OP, true);
            return;
        }
        toast(R.string.b2light_aal_light_connect_label_error);
        EventOpFreshIcResult.sendEventOpFreshIcResult(EventOpFreshIcResult.RESULT_TYPE_FRESH_OP, false);
    }

    private void startOpComm() {
        /*ble通信处理器*/
        boolean opCommEnableBle = bleOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "startOpComm() opCommEnableBle = " + opCommEnableBle);
        }
        if (!opCommEnableBle) {
            uiTypeBle = ui_type_def;
            BluetoothDevice bluetoothDevice = null;
            if (uiResult != null) bluetoothDevice = uiResult.getCurBluetoothDevice();
            bleOp.beOpComm(bluetoothDevice);
        }
        /*iot通信处理器*/
        boolean opCommEnableIot = iotOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "startOpComm() opCommEnableIot = " + opCommEnableIot);
        }
        if (!opCommEnableIot) {
            uiTypeIot = ui_type_def;
            iotOp.beOpComm(info.sku, info.device, info.topic);
        }
        /*刷新ui*/
        checkUi();
    }

    @Override
    public void uiLost() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "uiLost() layoutSuc = " + layoutSuc + " ; uiTypeIot = " + uiTypeIot + " ; uiTypeBle = " + uiTypeBle);
        }
        if (layoutSuc && (uiTypeIot == ui_type_normal || uiTypeBle == ui_type_normal)) {
            uiTypeBle = ui_type_fail;
            uiTypeIot = ui_type_fail;
            checkUi(false);
        }
        hideDiyGuide();
    }

    private void checkMicMode() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i("MicFragment", "checkMicMode sku device：" + info.sku + info.device);
        }
        String versionSoft = info.versionSoft;
        Support.isSupportMicByPhone(info.sku, info.device, versionSoft, info.goodsType);
    }

    private void checkUi() {
        checkUi(true);
    }

    private void checkUi(boolean reportResult) {
        if (destroy) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkUi() uiTypeBle = " + uiTypeBle + " ; uiTypeIot = " + uiTypeIot);
        }
        if (ui_type_normal == uiTypeBle || ui_type_normal == uiTypeIot) {
            /*重置连接次数*/
            retryConnectDeviceTimes = 0;
            /*通信成功*/
            hideLoading();
            timerUI.show();
            playUi.show();
            effectUI.show();
            if (supportFeast() && feastUI != null) {
                feastUI.show();
            }
            if (Support.supportAiLight(info.goodsType, info.sku) && aiLightUI != null) {
                aiLightUI.show();
            }
            info.headerOpType = ui_type_normal == uiTypeBle ? Cons.op_type_ble : Cons.op_type_iot;
            boolean open = info.open;
            checkUIStatusWhenConnected(open, info.mode);
            if (open) {
                brightnessUI.show();
                snapshotUI.show();
                brightnessUI.updateBrightness(checkBrightnessEnable(), ext.brightness);
                if (isSwitchMicPickUpType) {
                    AbsMicFragmentV4.saveMicModeByPhone(info.sku, info.device);
                }
                checkDiyStudio();
                checkDiyTemplate();
                checkDiyMode(info.mode);
                checkDiyGuide();
                checkMicMode();
                checkMusicIc();
                checkMusicModeVersion();
                changeColorMode(info.mode);
                checkHighColorIc();
                checkColorMode(info.mode);
                analytic4ModeUse(info.mode);
                checkCutDialog();
                modeUI.show();
                modeUI.setMode(info.mode);
            } else {
                hideDiyGuide();
                brightnessUI.hide();
                snapshotUI.hide();
                modeUI.hide();
            }

            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_suc);
        } else if (ui_type_fail == uiTypeBle && ui_type_fail == uiTypeIot) {
            /*重置连接次数*/
            retryConnectDeviceTimes = 0;
            /*通信失败*/
            hideLoading();
            snapshotUI.hide();
            timerUI.hide();
            playUi.hide();
            effectUI.hide();
            if (supportFeast() && feastUI != null) {
                feastUI.hide();
            }
            if (aiLightUI != null) {
                aiLightUI.hide();
            }
            brightnessUI.hide();
            modeUI.hide();
            deviceConnect();
            hideLoading();
            /*断开连接-重置通信Op*/
            iotOp.destroy();
            bleOp.destroy();
            hideDiyGuide();
            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_fail);
        } else {
            hideLoading();
            /*通信中*/
            effectUI.hide();
            if (supportFeast() && feastUI != null) {
                feastUI.hide();
            }
            if (aiLightUI != null) {
                aiLightUI.hide();
            }
            timerUI.hide();
            playUi.hide();
            snapshotUI.hide();
            brightnessUI.hide();
            modeUI.hide();
            deviceConnect();
            hideDiyGuide();
            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_ing);
        }
        isSwitchMicPickUpType = false;
    }

    //612x系列裁剪弹窗
    private void checkCutDialog() {
        if (Support.goodsTypeH612x(info.goodsType)) {
            boolean hadShow = CutConfig.Companion.read().hadShowCutDialog(info.sku, info.device);
            if (!hadShow) {
                ConfirmDialogV1.showConfirmDialog(ac, ResUtil.getString(R.string.str_show_cut), ResUtil.getString(R.string.str_show_cut_hint), ResUtil.getString(R.string.no), ResUtil.getString(R.string.yes), () -> {
                    AcCutCali.Companion.jump2AcCutCali(ac, info.sku, info.versionSoft);
                });
                CutConfig.Companion.read().updateShowCutDialog(info.sku, info.device);
            }
        }
    }

    /*标志位-是否统计了mode的使用;进去一次仅统计一次*/
    private boolean hadAnalyticModeUse = false;

    private void analytic4ModeUse(AbsMode mode) {
        if (hadAnalyticModeUse) return;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode == null) return;
        hadAnalyticModeUse = true;
        subMode.setAnalyticType(ISubMode.ANALYTIC_TYPE_MODE_USE_DETAIL);
    }

    /**
     * 高阶颜色模式-ic刷新
     */
    private void checkHighColorIc() {
        if (modeUI == null) return;
        LogInfra.Log.i(TAG, "checkHighColorIc() info.ic = " + info.ic);
        int ic = info.ic;
        if (info instanceof BleIotInfoV1) {
            if (Support.goodsTypeH612x(info.goodsType)) {
                if (Support.supportChangePieces20(info.goodsType, info.sku)) {
                    modeUI.updateIcNum(info.ic_sub_1, info.ic_sub_2);
                    return;
                } else {
                    ic = info.ic_sub_1;
                }
            }
        }
        modeUI.updateIcNum(ic);
    }

    /**
     * 检测音乐模式ic的变更
     */
    private void checkMusicIc() {
        boolean supportIcFresh = supportIcFresh();
        if (!supportIcFresh) return;
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeMusicV3) {
            if (Support.goodsTypeH612x(info.goodsType) && info instanceof BleIotInfoV1) {
                ((SubModeMusicV3) subMode).setIcNum(info.ic_sub_1 + info.ic_sub_2);
            } else {
                ((SubModeMusicV3) subMode).setIcNum(info.ic);
            }
        }
    }

    private void checkMusicModeVersion() {
        boolean newProtocol4SupportMultiMusicMode = Support.newProtocol4SupportMultiMusicMode(info.goodsType, info.versionSoft, info.versionHard);
        int musicVersion = newProtocol4SupportMultiMusicMode ? 1 : 0;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkMusicModeVersion() newProtocol4SupportMultiMusicMode = " + newProtocol4SupportMultiMusicMode + " ; musicVersion = " + musicVersion);
        }
        if (modeUI instanceof ModeUiV3) {
            ((ModeUiV3) modeUI).checkMusicVersion4BkProtocol(musicVersion, info.versionSoft, info.versionHard);
        }
        /*设置默认音乐模式*/
        AbsMode mode = info.mode;
        if (mode instanceof Mode) {
            ((Mode) mode).changeMusicMode4MultiNewMusic4Bk(musicVersion);
        }
    }

    private void checkDiyStudio() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenes) {
            int scenesCode = ((SubModeScenes) subMode).getEffect();
            boolean scenes4DiyStudio = ScenesOp.isScenes4DiyStudio(scenesCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkDiyStudio() scenesCode = " + scenesCode + " ; scenes4DiyStudio = " + scenes4DiyStudio);
            }
            if (scenes4DiyStudio) {
                /*来自于Studio的DIY-切换成DIY模式*/
                int diyCode = DiyOpM.getInstance.getDiyCode4DiyTemplate(info.sku, scenesCode);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "checkDiyStudio() diyCode = " + diyCode);
                }
                mode.subMode = new SubModeNewDiy(diyCode);
            }
        }
    }

    private void checkDiyTemplate() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenes) {
            int scenesCode = ((SubModeScenes) subMode).getEffect();
            boolean scenes4DiyTemplate = ScenesOp.isScenes4DiyTemplate(scenesCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkDiyTemplate() scenes4DiyTemplate " + scenes4DiyTemplate + " ; scenesCode = " + scenesCode);
            }
            if (scenes4DiyTemplate) {
                /*当前是diy模版效果；将模式切换长diy模式*/
                int diyCode = DiyOpM.getInstance.getDiyCode4DiyTemplate(info.sku, scenesCode);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "checkDiyTemplate() diyCode = " + diyCode);
                }
                mode.subMode = new SubModeNewDiy(diyCode);
            }
        }
    }

    private boolean checkBrightnessEnable() {
        AbsMode mode = info.mode;
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeScenes) {
                int effect = ((SubModeScenes) subMode).getEffect();
                boolean[] brightnessChangeEnable = ScenesRgb.brightnessChangeEnable(effect);
                if (brightnessChangeEnable[0]) {
                    return brightnessChangeEnable[1];
                }
                return Support.supportScenesBrightnessOp(info.pactType, info.pactCode, info.goodsType, effect, info.sku);
            }
        }
        return true;
    }

    private void checkColorMode(AbsMode mode) {
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeColorV2) {
                ((SubModeColorV2) subMode).gradual = ext.gradual;
            } else if (subMode instanceof SubModeColorV3) {
                ((SubModeColorV3) subMode).gradual = ext.gradual;
            } else if (subMode instanceof SubModeColorV6) {
                ((SubModeColorV6) subMode).gradual = ext.gradual;
                if (info instanceof BleIotInfoV1 && Support.supportChangePieces20(info.goodsType, info.sku)) {
                    SafeLog.Companion.i(TAG, () -> "checkColorMode() sectionNum1 = " + ((BleIotInfoV1) info).sectionNum1 + " sectionNum2 = " + ((BleIotInfoV1) info).sectionNum2);
                    ((SubModeColorV6) subMode).sectionNum1 = ((BleIotInfoV1) info).sectionNum1;
                    ((SubModeColorV6) subMode).sectionNum2 = ((BleIotInfoV1) info).sectionNum2;
                }
            } else if (subMode instanceof SubModeColorV8 && info instanceof BleIotInfoV1) {
                ((SubModeColorV8) subMode).setSectionNum(((BleIotInfoV1) info).sectionNum1);
                if (modeUI instanceof ModeUiV3) {
                    ((ModeUiV3) modeUI).updateGradientEffect(ext.gradual == 1);
                }
            }
        }
    }

    private void changeColorMode(AbsMode mode) {
        int modeColorVersion4New = Support.getModeColorVersion4New(info.goodsType, info.sku);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "changeColorMode() modeColorVersion4New = " + modeColorVersion4New);
        }
        /*若当前是旧颜色模式，需要转化成新的颜色模式*/
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            SafeLog.i(TAG, () -> "changeColorMode() subMode = " + subMode);
            if (subMode instanceof SubModeColor) {
                if (modeColorVersion4New == 5) {
                    mode.subMode = SubModeColorV4.parseSubModeColor2New((SubModeColor) subMode);
                } else if (modeColorVersion4New == 4) {
                    mode.subMode = SubModeColorV3.parseSubModeColor2New((SubModeColor) subMode);
                } else if (modeColorVersion4New == 3) {
                    mode.subMode = SubModeColorV2.parseSubModeColor2New((SubModeColor) subMode);
                }
                if (bleOp.isOpCommEnable()) {
                    bleOp.readPartColor();
                }
            }
        }
        if (modeUI instanceof ModeUiV3) {
            ArrayList<Integer> icSubList = null;
            if (info instanceof BleIotInfoV1 && Support.supportChangePieces20(info.goodsType, info.sku)) {
                icSubList = new ArrayList<>();
                icSubList.add(info.ic_sub_1);
                icSubList.add(info.ic_sub_2);
            }
            ((ModeUiV3) modeUI).changeColorMode(modeColorVersion4New, info, getDiySupport(), icSubList);
        }
    }

    private int getIotOpType() {
        return getOpType(uiTypeIot);
    }

    private int getBleOpType() {
        return getOpType(uiTypeBle);
    }

    private int getOpType(int step) {
        if (step == ui_type_fail) return IUiResult4BleIot.op_type_fail;
        if (step == ui_type_normal) return IUiResult4BleIot.op_type_suc;
        return IUiResult4BleIot.op_type_ing;
    }

    protected void showLoading() {
        LoadingDialog.createDialog(ac, com.ihoment.base2app.R.style.DialogDim, 60 * 1000).setEventKey(TAG).show();
    }

    protected void hideLoading() {
        LoadingDialog.hideDialog(TAG);
    }

    private void toast(@StringRes int strRes) {
        ToastUtil.getInstance().toast(strRes);
    }

    /**
     * diy效果发生改变操作
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyEffectOp(EventDiyEffectOp event) {
        int opType = event.opType;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyEffectOp() opType = " + opType);
        }
        checkDiyMode(info.mode);
    }

    /**
     * 注册diy的输出
     *
     * @param event event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApply(EventDiyApply event) {
        boolean applyDiyV0 = applyDiyV0(event.getDiyProtocol());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApply() applyDiyV0 = " + applyDiyV0);
        }
        if (!applyDiyV0) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    /**
     * 进入定时界面
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewTimerClickEvent(NewTimerUI.NewTimerClickEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewTimerClickEvent()");
        }
        NewShowTimerAcV1.jump2NewShowTimerAcV1(ac, info, ext.wakeUpInfo, ext.sleepInfo, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onBrightnessEndEvent(BrightnessUI.EventBrightnessClickEvent event) {
        int brightness = event.brightness;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onBrightnessEndEvent() brightness = " + brightness);
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            showLoading();
            BrightnessController controller = new BrightnessController(brightness);
            bleOp.executeOp(controller);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            CmdBrightness cmdBrightness = new CmdBrightness(brightness);
            iotOp.writeCmd(cmdBrightness);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewTimerSetEventV1(NewTimerSetEventV1 event) {
        int group = event.getGroup();
        NewTimerV1 info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewTimerSetEventV1() group = " + group);
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            info.check();
            NewTimerV1Controller controller = new NewTimerV1Controller(group, NewTimerV1.toTimer(info));
            bleOp.executeOp(controller);
        } else if (iotOp.isOpCommEnable()) {
            info.check();
            NewTimerV1Controller controller = new NewTimerV1Controller(group, NewTimerV1.toTimer(info));
            CmdPtReal cmdPtReal = new CmdPtReal(controller);
            iotOp.writeCmd(cmdPtReal);
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            TimerResultEvent.sendTimerResultEventFail(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewWakeupSetEvent(NewWakeupSetEvent event) {
        WakeUpInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewWakeupSetEvent() info = " + info.toString());
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            info.check();
            bleOp.executeOp(Support.makeWakeUpController(this.info.goodsType, this.info.sku, this.info.pactType, this.info.pactCode, info));
        } else if (iotOp.isOpCommEnable()) {
            info.check();
            CmdPtReal cmdPtReal = new CmdPtReal(Support.makeWakeUpController(this.info.goodsType, this.info.sku, this.info.pactType, this.info.pactCode, info));
            iotOp.writeCmd(cmdPtReal);
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            WakeupFailEvent.sendWakeupFailEvent(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNewSleepSetEvent(NewSleepSetEvent event) {
        SleepInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewSleepSetEvent() info = " + info.toString());
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            /*若当前是开启睡眠模式，但设备未开启，则提示用户打开灯*/
            if (event.isOpen() && !this.info.open) {
                toast(R.string.b2light_please_open_the_light);
                SleepFailEvent.sendSleepFailEvent(false);
            } else {
                info.check();
                AbsSingleController sleepModeController = Support.makeSleepController(this.info.goodsType, this.info.sku, this.info.pactType, this.info.pactCode, info);
                bleOp.executeOp(sleepModeController);
            }
        } else if (iotOp.isOpCommEnable()) {
            /*若当前是开启睡眠模式，但设备未开启，则提示用户打开灯*/
            if (event.isOpen() && !this.info.open) {
                toast(R.string.b2light_please_open_the_light);
                SleepFailEvent.sendSleepFailEvent(false);
            } else {
                info.check();
                CmdPtReal cmdPtReal = new CmdPtReal(Support.makeSleepController(this.info.goodsType, this.info.sku, this.info.pactType, this.info.pactCode, info));
                iotOp.writeCmd(cmdPtReal);
            }
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            SleepFailEvent.sendSleepFailEvent(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSleepTimeSub(EventSleepUpdate event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onSleepTimeSub()");
        }
        if (ext.sleepInfo != null && ext.sleepInfo.isOn()) {
            SleepInfo info = event.getInfo();
            info.check();
            ext.sleepInfo = info;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventChangeGradual(EventChangeGradual event) {
        boolean open = event.open;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventChangeGradual() open = " + open);
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            Gradual4BleWifiController gradualController = new Gradual4BleWifiController(open);
            bleOp.executeOp(gradualController);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            Gradual4BleWifiController gradualController = new Gradual4BleWifiController(open);
            CmdPtReal cmdPtReal = new CmdPtReal(gradualController);
            iotOp.writeCmd(cmdPtReal);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onModeV1Event(Mode mode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onModeV1Event()");
        }
        /*检测是否色温单独控制的设备子模式操作*/
        checkColorTemReal(mode.subMode);
        changeMode(mode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeModeEvent(ChangeModeEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent()");
        }
        ISubMode subMode = event.getSubMode();
        boolean change2NewMultiMusicCode = change2NewMultiMusicMode(subMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent() change2NewMultiMusicCode = " + change2NewMultiMusicCode);
        }
        if (change2NewMultiMusicCode) return;
        boolean changeDiyMode = changeDiyMode(subMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent() changeDiyMode = " + changeDiyMode);
        }
        if (changeDiyMode) return;
        /*检测场景模式下的效果是否支持*/
        ISubMode subModeNew = Support.checkScenesModeEffect(subMode, info.sku, info.goodsType, info.pactType, info.pactCode, 1, bleOp.isOpCommEnable(), info.versionSoft, info.versionHard, iotOp.isOpCommEnable(), ext.wifiSoftVersion, ext.wifiHardVersion);
        if (subModeNew != null) {
            subMode = subModeNew;
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        changeMode(mode);
        /*统计场景使用次数*/
        if (subMode instanceof SubModeScenes) {
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.scene_mode);
        } else if (subMode instanceof SubModeColor || subMode instanceof SubModeColorV2 || subMode instanceof SubModeColorV3 || subMode instanceof SubModeColorV4) {
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.color_mode);
        } else if (subMode instanceof SubModeMusic || subMode instanceof SubModeMusicV1) {
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.music_mode);
        }
    }

    private void checkColorTemReal(ISubMode subMode) {
        boolean colorTemRealDevice = Support.isColorTemRealDevice(info.goodsType);
        if (!colorTemRealDevice) return;
        if (subMode instanceof SubModeColorV2) {
            boolean isColorTemSet = ((SubModeColorV2) subMode).kelvin > 0;
            LogInfra.Log.i(TAG, "checkColorTemReal() isColorTemSet = " + isColorTemSet);
            if (isColorTemSet) {
                /*色温设置-整条灯带都是同一个色温值*/
                Arrays.fill(((SubModeColorV2) subMode).ctlLight, true);
            }
        }
    }

    private boolean change2NewMultiMusicMode(ISubMode subMode) {
        if (subMode instanceof SubModeMusicV3) {
            int sensitivity = ((SubModeMusicV3) subMode).getSensitivity();
            int musicCode = ((SubModeMusicV3) subMode).getMusicCode();
            boolean newMusicCode = Support.isNewMusicCode(musicCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "change2NewMultiMusicMode() newMusicCode = " + newMusicCode);
            }
            if (newMusicCode) {
                /*若当前是新的音乐模式-则需要先发送新的多包参数*/
                boolean setLocalNewMusicMode = AbsNewMusicEffect.setLocalNewMusicMode(info.sku, info.device, musicCode, sensitivity);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "change2NewMultiMusicMode() setLocalNewMusicMode = " + setLocalNewMusicMode);
                }
                AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.music_mode);
                return true;
            }
        }
        return false;
    }

    private boolean changeDiyMode(ISubMode subMode) {
        if (subMode instanceof SubModeNewDiy) {
            boolean hadToken = AccountConfig.read().isHadToken();
            String diyValueKey = ((SubModeNewDiy) subMode).getDiyValueKey();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "changeDiyMode() diyValueKey = " + diyValueKey + " ; hadToken = " + hadToken);
            }
            DiyValue diyValue = DiyShortcutManger.queryLastApplyDiy(info.sku, info.goodsType, getDiySupport(), info.ic, diyValueKey, true);
            Util4Diy.toApplyDiy(diyValue);
            return true;
        }
        return false;
    }

    private void changeMode(Mode mode) {
        modeUI.setModeChanged(true);
        if (bleOp.isOpCommEnable()) {
            showLoading();
            AbsMultipleControllerV14Scenes newScenesMode = Support.is2NewScenesMode(info.sku, info.device, mode, 1);
            if (newScenesMode != null) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "changeMode() AbsMultipleControllerV14Scenes");
                }
                bleOp.executeMultiOpV1(newScenesMode);
            } else {
                ModeController controller = new ModeController(mode);
                bleOp.executeOp(controller);
            }
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            /*若当前是从其他模式切换至颜色模式，则需要切换模式，同时读取对应色值和渐变开关*/
            boolean subColorModeCur = isSubColorMode(info.mode);
            boolean is2SubColorMode = isSubColorMode(mode);
            LogInfra.Log.i(TAG, "changeMode() subColorModeCur = " + subColorModeCur + " ; is2SubColorMode = " + is2SubColorMode);
            if (!subColorModeCur && is2SubColorMode) {
                List<byte[]> bytes = new ArrayList<>();
                ModeController modeController = new ModeController(mode);
                bytes.add(modeController.getValue());
                int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
                int subModeColorVersion = Support.supportPartBrightness(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, "");
                if (subModeColorVersion == 2) {
                    /*支持获取分段亮度-3个颜色一组*/
                    int groupColorCount = 3;
                    if (Support.isH61B5(info.sku) || Support.isGoodsTypeH61A9(info.goodsType))
                        groupColorCount = 4;
                    int maxGroup = bulbStringMaxNum / groupColorCount + (bulbStringMaxNum % groupColorCount == 0 ? 0 : 1);
                    for (int i = 0; i < maxGroup; i++) {
                        bytes.add(new BulbStringColorControllerV2(i + 1, groupColorCount).getValue());
                    }
                } else {
                    /*4个颜色一组*/
                    int maxGroup = bulbStringMaxNum / 4 + (bulbStringMaxNum % 4 == 0 ? 0 : 1);
                    for (int i = 0; i < maxGroup; i++) {
                        bytes.add(new BulbStringColorController(i + 1).getValue());
                    }
                }
                /*真色温设备-需要读取模式-因为真色温返回的色条是无色*/
                if (Support.isColorTemRealDevice(info.goodsType)) {
                    bytes.add(new ModeController().getValue());
                }
                bytes.add(new Gradual4BleWifiController().getValue());
                CmdPtReal ptReal = new CmdPtReal(bytes);
                iotOp.writeCmd(ptReal);
            } else {
                AbsMultipleControllerV14Scenes newScenesMode = Support.is2NewScenesMode(info.sku, info.device, mode, 1);
                if (newScenesMode != null) {
                    CmdPtReal cmdPtReal = CmdPtReal.getNewScenesCmdPtReal(newScenesMode);
                    showLoading();
                    iotOp.writeCmd(cmdPtReal);
                } else {
                    /*若当前是选中全段灯进行颜色设置，Wi-Fi下则采用colorWc指令进行通信，已达到同步逻辑*/
                    int[] subColorModeSelectAll = isSubColorModeSelectAll(mode);
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "changeMode() subColorModeSelectAll = " + Arrays.toString(subColorModeSelectAll));
                    }
                    AbsCmd absCmd;
                    if (subColorModeSelectAll[0] == 1 && subColorModeSelectAll[1] == 0) {
                        ModeController modeController = new ModeController(mode);
                        absCmd = new CmdPtReal(modeController);
                    } else if (subColorModeSelectAll[0] == 1) {
                        int rgb = subColorModeSelectAll[1];
                        boolean colorTem = ColorUtils.isColorTem(rgb);
                        if (LogInfra.openLog()) {
                            LogInfra.Log.i(TAG, "changeMode() colorTem = " + colorTem);
                        }
                        if (colorTem) {
                            /*色温*/
                            absCmd = CmdColorWc.makeCmdColorWc4ColorTem(rgb);
                        } else {
                            /*rgb颜色*/
                            absCmd = CmdColorWc.makeCmdColorWc4Color(rgb);
                        }
                    } else {
                        ModeController modeController = new ModeController(mode);
                        absCmd = new CmdPtReal(modeController);
                    }
                    iotOp.writeCmd(absCmd);
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventScenesEffect(EventScenesEffect effect) {
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "onEventScenesEffect");
        }
        CategoryV1.SceneV1 sceneV1 = effect.sceneV1;
        int pos = effect.pos;
        if (bleOp.isOpCommEnable() || iotOp.isOpCommEnable()) {
            boolean result = false;
            int status = EventEffectSquareOpResult.status_iot_no_support;
            if (bleOp.isOpCommEnable()) {
                result = scenesEffect4Ble(sceneV1, pos);
                status = EventEffectSquareOpResult.status_ble_no_support;
            } else if (iotOp.isOpCommEnable()) {
                result = scenesEffect4Iot(sceneV1, pos);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(result ? EventEffectSquareOpResult.result_op_support : EventEffectSquareOpResult.result_op_no_support, status);
        } else {
            /*当前设备未连接*/
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_unable);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventEditScenes(EffectEvent.EventEditScenes event) {
        String sku = event.sku;
        int categoryId = event.categoryId;
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "onEventEditScenes:" + sku + "---categoryId:" + categoryId);
        }
        if (!info.sku.equals(sku)) return;
        EditScenesAc.jump2EditScenesAc(ac, info.sku, info.device, info.goodsType, categoryId, true, true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeScenes(CategoryV1.SceneV1 scene) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeScenes()");
        }
        int sceneType = scene.getSceneType(0, info.sku);
        int sceneCode = scene.getSceneCode(0, info.sku);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeScenes() sceneType = " + sceneType + " ; scene.sceneCode = " + sceneCode);
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            if (!scenesEffect4Ble(scene, 0)) {
                HintDialog1.createHintDialog1(ac, ResUtil.getString(R.string.b2light_scenes_ble_not_support), ResUtil.getString(R.string.hint_done_got_it)).show();
                hideLoading();
            }
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            if (!scenesEffect4Iot(scene, 0)) {
                HintDialog1.createHintDialog1(ac, ResUtil.getString(R.string.b2light_scenes_no_support_hint), ResUtil.getString(R.string.hint_done_got_it)).show();
                hideLoading();
            }
        }
    }

    /**
     * ble 发送配置场景
     *
     * @param sceneV1 sceneV1
     * @param pos     pos
     * @return boolean
     */
    private boolean scenesEffect4Ble(CategoryV1.SceneV1 sceneV1, int pos) {
        int cmdVersion = sceneV1.getCmdVersion(info.sku, pos);
        int sceneType = sceneV1.getSceneType(pos, info.sku);
        int sceneCode = sceneV1.getSceneCode(pos, info.sku);
        if (!Support.checkShareEffectVersion4BleWifi(0, sceneType, cmdVersion, info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard, ext.wifiSoftVersion)) {
            return false;
        }
        String sceneEffectStr = sceneV1.getScenesParam(info.sku, pos);
        if (sceneType == ScenesOp.scene_type_static) {
            /*静态场景效果*/
            SubModeScenes subModeScenes = new SubModeScenes();
            subModeScenes.setEffect(sceneCode);
            Mode mode = new Mode();
            mode.subMode = subModeScenes;
            ModeController controller = new ModeController(mode);
            ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeOp(controller));
            return true;
        }
        if (sceneType == ScenesOp.scene_type_rgb) {
            /*rgb效果*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgb(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeMultiOpV1(controllerV14Scenes));
            } else {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "onChangeScenes() rgb指令解析出错!");
                }
                return false;
            }

            return true;
        }
        if (sceneType == ScenesOp.scene_type_rgbic) {
            /*rgbic效果*/
            int[] rgbicProtocolVersionBytes = ScenesRgbIC.isRGBICProtocolVersionBytes(Encode.decryByBase64(sceneEffectStr));
            if (rgbicProtocolVersionBytes[0] == 0) return false;
            int parseVersion = rgbicProtocolVersionBytes[1];
            if (parseVersion != cmdVersion) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "版本不一致  parseVersion:" + parseVersion + "----cmdVersion:" + cmdVersion);
                }
                return false;
            }
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgbic(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeMultiOpV1(controllerV14Scenes));
            } else {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "onChangeScenes() rgbic指令解析出错!");
                }
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * iot 发送配置场景
     *
     * @param sceneV1 sceneV1
     * @param pos     pos
     * @return boolean
     */
    private boolean scenesEffect4Iot(CategoryV1.SceneV1 sceneV1, int pos) {
        int cmdVersion = sceneV1.getCmdVersion(info.sku, pos);
        int sceneType = sceneV1.getSceneType(pos, info.sku);
        int sceneCode = sceneV1.getSceneCode(pos, info.sku);
        if (!Support.checkShareEffectVersion4BleWifi(1, sceneType, cmdVersion, info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard, ext.wifiSoftVersion)) {
            return false;
        }
        String sceneEffectStr = sceneV1.getScenesParam(info.sku, pos);
        if (sceneType == ScenesOp.scene_type_static) {
            SubModeScenes subModeScenes = new SubModeScenes();
            subModeScenes.setEffect(sceneCode);
            Mode mode = new Mode();
            mode.subMode = subModeScenes;
            ModeController controller = new ModeController(mode);
            CmdPtReal ptReal = new CmdPtReal(controller);
            ScenesOp.toSetScenes(ac, sceneV1, pos, () -> iotOp.writeCmd(ptReal));
            return true;
        }
        if (sceneType == ScenesOp.scene_type_rgb) {
            /*rgb效果*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgb(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                /*多包场景效果-采用效果包+模式包组合方式*/
                CmdPtReal cmdPtReal = CmdPtReal.getNewScenesCmdPtReal(controllerV14Scenes);
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> iotOp.writeCmd(cmdPtReal));
                return true;
            }
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "scenesEffect4Iot() rgb指令解析出错!");
            }
            return false;
        }
        if (sceneType == ScenesOp.scene_type_rgbic) {
            /*rgbic效果*/
            int[] rgbicProtocolVersionBytes = ScenesRgbIC.isRGBICProtocolVersionBytes(Encode.decryByBase64(sceneEffectStr));
            if (rgbicProtocolVersionBytes[0] == 0) return false;
            int parseVersion = rgbicProtocolVersionBytes[1];
            if (parseVersion != cmdVersion) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "版本不一致  parseVersion:" + parseVersion + "----cmdVersion:" + cmdVersion);
                }
                return false;
            }
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgbic(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                /*多包场景效果-采用效果包+模式包组合方式*/
                CmdPtReal cmdPtReal = CmdPtReal.getNewScenesCmdPtReal(controllerV14Scenes);
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> iotOp.writeCmd(cmdPtReal));
                return true;
            }
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "scenesEffect4Iot() rgbic指令解析出错!");
            }
            return false;
        }
        return false;
    }

    private boolean isSubColorMode(AbsMode mode) {
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode != null) {
                return subMode.subModeCommandType() == BleProtocol.sub_mode_color_v2;
            }
        }
        return false;
    }

    private int[] isSubColorModeSelectAll(AbsMode mode) {
        int[] result = new int[]{0, 0};
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeColorV2) {
                boolean selectAll = ((SubModeColorV2) subMode).isSelectAll();
                if (selectAll) {
                    int rgb = ((SubModeColorV2) subMode).getRealRgb();
                    result[0] = 1;
                    result[1] = rgb;
                }
            } else if (subMode instanceof SubModeColorV3) {
                boolean selectAll = ((SubModeColorV3) subMode).isSelectAll();
                if (selectAll) {
                    int rgb = ((SubModeColorV3) subMode).getRealRgb();
                    result[0] = 1;
                    result[1] = rgb;
                }
            } else if (subMode instanceof SubModeColorV4) {
                boolean selectAll = ((SubModeColorV4) subMode).isSelectAll();
                if (selectAll) {
                    int rgb = ((SubModeColorV4) subMode).getRealRgb();
                    result[0] = 1;
                    result[1] = rgb;
                }
            } else if (subMode instanceof SubModeColorV6) {
                boolean selectAll = ((SubModeColorV6) subMode).isSelectAll();
                if (selectAll) {
                    int realRgb = ((SubModeColorV6) subMode).getRealRgb();
                    result[0] = 1;
                    result[1] = realRgb;
                }
            }
        }
        return result;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onUpdateTimeEvent(UpdateTimeEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onUpdateTimeEvent()");
        }
        readTimer();
    }

    private void readTimer() {
        /*重新读取定时信息*/
        if (bleOp.isOpCommEnable()) {
            SyncTimeInfo info = SyncTimeInfo.getCurrentTime();
            AbsSingleController[] controllers = new AbsSingleController[]{new SyncTimeController(info.hour, info.minute, info.second, info.week), new NewTimerV1Controller(0xFF), Support.makeWakeUpController4Read(this.info.goodsType, this.info.sku, this.info.pactType, this.info.pactCode), Support.makeSleepController4Read(this.info.goodsType, this.info.sku, this.info.pactType, this.info.pactCode)};
            bleOp.executeOp(controllers);
        } else if (iotOp.isOpCommEnable()) {
            iotOp.readCmd(new CmdStatus());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEffectClickEvent(EffectUI.EffectClickEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEffectClickEvent()");
        }
        int[] supportScenesOpSet = Support.getSupportScenesOpSet(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard);
        EffectAc.jump2EffectAc(ac, info.goodsType, info.sku, info.device, supportScenesOpSet, true, true, true, info.versionSoft, info.versionHard);
    }

    private void colorPiece10(EffectData.ColorEffect colorEffect) {
        int size = colorEffect.colorSet.length;
        boolean singleColor = colorEffect.isSingleColor();
        if (size != 1 && size != 10) {
            //不支持
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "颜色数量不支持：" + size);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_no_support);
            return;
        }
        Mode mode = new Mode();
        /*单颜色*/
        if (singleColor) {
            mode.subMode = SubModeColorV3.makeSubModeColor(colorEffect.colorSet[0]);
            if (bleOp.isOpCommEnable()) {
                ModeController controller = new ModeController(mode);
                bleOp.executeOp(controller);
            } else {
                CmdColorWc cmdColorWc = CmdColorWc.makeCmdColorWc4Color(colorEffect.colorSet[0]);
                iotOp.writeCmd(cmdColorWc);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_support);
            return;
        }
        /*多颜色*/
        if (bleOp.isOpCommEnable()) {
            AbsSingleController[] modeControllers = SubModeColorV3.makeSubModeColor(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
            bleOp.executeOp(modeControllers);
        } else {
            /*支持Wi-Fi下组包设置分段颜色*/
            CmdPtReal cmdPt = CmdPtReal.getPartColorCmdPt(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
            if (cmdPt != null) {
                iotOp.writeCmd(cmdPt);
            } else {
                EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_only_ble);
                return;
            }
        }
        EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_support);
    }

    private void colorPiece12(EffectData.ColorEffect colorEffect) {
        int size = colorEffect.colorSet.length;
        boolean singleColor = colorEffect.isSingleColor();
        if (size != 1 && size != 12) {
            //不支持
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "颜色数量不支持：" + size);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_no_support);
            return;
        }
        Mode mode = new Mode();
        /*单颜色*/
        if (singleColor) {
            mode.subMode = SubModeColorV4.makeSubModeColor(colorEffect.colorSet[0]);
            if (bleOp.isOpCommEnable()) {
                ModeController controller = new ModeController(mode);
                bleOp.executeOp(controller);
            } else {
                CmdColorWc cmdColorWc = CmdColorWc.makeCmdColorWc4Color(colorEffect.colorSet[0]);
                iotOp.writeCmd(cmdColorWc);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_support);
            return;
        }
        /*多颜色*/
        if (bleOp.isOpCommEnable()) {
            AbsSingleController[] modeControllers = SubModeColorV4.makeSubModeColor(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
            bleOp.executeOp(modeControllers);
        } else {
            /*支持Wi-Fi下组包设置分段颜色*/
            CmdPtReal cmdPt = CmdPtReal.getPartColorCmdPt(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
            if (cmdPt != null) {
                iotOp.writeCmd(cmdPt);
            } else {
                EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_only_ble);
                return;
            }
        }
        EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_support);
    }

    private void colorPiece20(EffectData.ColorEffect colorEffect) {
        int size = colorEffect.colorSet.length;
        boolean singleColor = colorEffect.isSingleColor();
        if (size != 1 && size != 20) {
            //不支持
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "颜色数量不支持：" + size);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_no_support);
            return;
        }
        if (singleColor) {
            Mode mode = new Mode();
            mode.subMode = SubModeColorV6.makeSubModeColor(colorEffect.colorSet[0]);
            if (bleOp.isOpCommEnable()) {
                ModeController controller = new ModeController(mode);
                bleOp.executeOp(controller);
            } else {
                CmdColorWc cmdColorWc = CmdColorWc.makeCmdColorWc4Color(colorEffect.colorSet[0]);
                iotOp.writeCmd(cmdColorWc);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_support);
            return;
        }
        boolean supportColorStripMulti = Support.supportColorStripMulti(info.goodsType, info.pactType, info.pactCode);
        if (supportColorStripMulti) {
            Colors colors = new Colors(colorEffect.colorSet, colorEffect.brightnessSet, "");
            MultipleColorStripControllerV1 stripControllerV1 = new MultipleColorStripControllerV1(colors);
            if (bleOp.isOpCommEnable()) {
                bleOp.executeMultiOpV1(stripControllerV1);
            } else {
                CmdPtReal cmdPtReal = CmdPtReal.makeColorStripCmdPtReal(stripControllerV1, new ICmdPtRealOpResult() {
                    @Override
                    public void fail() {
                        EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
                    }

                    @Override
                    public void scu() {
                        Support.makeColorStripMode(info, colors);
                        EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
                    }
                });
                iotOp.writeCmd(cmdPtReal);
            }
        }
        EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_support);
    }

    private void colorPiece15(EffectData.ColorEffect colorEffect) {
        int size = colorEffect.colorSet.length;
        boolean singleColor = colorEffect.isSingleColor();
        if (size != 1 && size != 15) {
            //不支持
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "颜色数量不支持：" + size);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_no_support);
            return;
        }
        Mode mode = new Mode();
        /*单颜色*/
        if (singleColor) {
            mode.subMode = SubModeColorV2.makeSubModeColor(colorEffect.colorSet[0]);
            if (bleOp.isOpCommEnable()) {
                ModeController controller = new ModeController(mode);
                bleOp.executeOp(controller);
            } else {
                CmdColorWc cmdColorWc = CmdColorWc.makeCmdColorWc4Color(colorEffect.colorSet[0]);
                iotOp.writeCmd(cmdColorWc);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_support);
            return;
        }
        boolean supportColorStripMulti = Support.supportColorStripMulti(info.goodsType, info.pactType, info.pactCode);
        if (supportColorStripMulti) {
            Colors colors = new Colors(colorEffect.colorSet, colorEffect.brightnessSet, "");
            MultipleColorStripControllerV1 stripControllerV1 = new MultipleColorStripControllerV1(colors);
            if (bleOp.isOpCommEnable()) {
                bleOp.executeMultiOpV1(stripControllerV1);
            } else {
                CmdPtReal cmdPtReal = CmdPtReal.makeColorStripCmdPtReal(stripControllerV1, new ICmdPtRealOpResult() {
                    @Override
                    public void fail() {
                        EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
                    }

                    @Override
                    public void scu() {
                        Support.makeColorStripMode(info, colors);
                        EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
                    }
                });
                iotOp.writeCmd(cmdPtReal);
            }
            return;
        }
        /*多颜色*/
        if (bleOp.isOpCommEnable()) {
            AbsSingleController[] modeControllers = SubModeColorV2.makeSubModeColor(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
            bleOp.executeOp(modeControllers);
        } else {
            /*支持Wi-Fi下组包设置分段颜色*/
            CmdPtReal cmdPt = CmdPtReal.getPartColorCmdPt(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
            if (cmdPt != null) {
                iotOp.writeCmd(cmdPt);
            } else {
                EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_only_ble);
                return;
            }
        }
        EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_support);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventColorEffect(EffectData.ColorEffect colorEffect) {
        /*设置颜色模式*/
        if (bleOp.isOpCommEnable() || iotOp.isOpCommEnable()) {
            boolean subModeColorPiece10 = Support.isSubModeColorPiece10(info.goodsType);
            if (subModeColorPiece10) {
                colorPiece10(colorEffect);
                return;
            }
            boolean subModeColorPiece12 = Support.isSubModeColorPiece12(info.goodsType);
            if (subModeColorPiece12) {
                colorPiece12(colorEffect);
                return;
            }
            if (Support.isSubModeColorPiece20(info.goodsType) || Support.goodsTypeH612x(info.goodsType)) {
                colorPiece20(colorEffect);
                return;
            }
            colorPiece15(colorEffect);
        } else {
            /*当前设备未连接*/
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_unable);
        }
    }

    /**
     * diy模版
     *
     * @param diyTemplate
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyTemplate(DiyTemplate diyTemplate) {
        boolean applyDiyV2 = applyDiyV2(diyTemplate);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyTemplate() applyDiyV2 = " + applyDiyV2);
        }
        if (!applyDiyV2) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    /**
     * 来自于Studio的 DIY模版
     *
     * @param diyStudio
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyStudio(DiyStudio diyStudio) {
        boolean applyDiyV4 = applyDiyV4(diyStudio);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyStudio() applyDiyV4 = " + applyDiyV4);
        }
        if (!applyDiyV4) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyAi(DiyAi diyAi) {
        boolean applyDiyV5 = applyDiyV5(diyAi);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyAi() applyDiyV5 = " + applyDiyV5);
        }
        if (!applyDiyV5) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventServiceSceneFresh(EventServiceScenesFresh event) {
        String sku = event.sku;
        String skuCur = info.sku;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventServiceSceneFresh() sku = " + sku + " ; skuCur = " + skuCur + " ; uiTypeBle = " + uiTypeBle + " ; uiTypeIot = " + uiTypeIot);
        }
        if (!TextUtils.isEmpty(sku) && sku.equals(skuCur) && (uiTypeBle == ui_type_normal || uiTypeIot == ui_type_normal)) {
            /*检查是否有服务器配置场景*/
            EventSceneCheck4BleIotV1.sendEventSceneCheck4BleIotV1(info);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventChangeScenes(EventChangeScenes event) {
        String sku = event.sku;
        String skuCur = info.sku;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventChangeScenes() sku = " + sku + " ; skuCur = " + skuCur);
        }
        if (!TextUtils.isEmpty(sku) && sku.equals(skuCur) && (uiTypeBle == ui_type_normal || uiTypeIot == ui_type_normal)) {
            /*检查是否有服务器配置场景*/
            EventSceneCheck4BleIotV1.sendEventSceneCheck4BleIotV1(info);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventOtaPrepareOp(EventOtaPrepareOp event) {
        boolean opCommEnable = bleOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventOtaPrepareOp() opCommEnable = " + opCommEnable);
        }
        if (opCommEnable) {
            OtaPrepareController otaPrepareController = new OtaPrepareController();
            bleOp.executeOp(otaPrepareController);
        } else {
            OtaFlagV2.getInstance.onOtaPrepare(false);
        }
    }

    /**
     * 注册diy的输出-v1版本
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApplyGraffiti(EventDiyApplyV2 event) {
        boolean applyDiyV3 = applyDiyV3(event.getDiyGraffiti());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyGraffiti() applyDiyV3 = " + applyDiyV3);
        }
        if (!applyDiyV3) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventColorStrip(EventColorStrip event) {
        /*色条设置颜色模式*/
        if (bleOp.isOpCommEnable() || iotOp.isOpCommEnable()) {
            int size = event.colors.colorSet.length;
            boolean subModeColorPiece10 = Support.isSubModeColorPiece10(info.goodsType);
            boolean subModeColorPiece12 = Support.isSubModeColorPiece12(info.goodsType);
            boolean subModeColorPiece20 = Support.isSubModeColorPiece20(info.goodsType);
            boolean subModeColorPiece18 = Support.isSubModeColorPiece18(info.goodsType);
            boolean supportChangeColorPiece = Support.goodsTypeH612x(info.goodsType);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventColorStrip()");
            }
            if (supportChangeColorPiece) {
                SafeLog.Companion.i(TAG, () -> "onEventColorStrip() supportChangeColorPiece");
            } else if (subModeColorPiece20) {
                if (size != 20) {
                    //不支持
                    if (LogInfra.openLog()) {
                        LogInfra.Log.e(TAG, "颜色数和灯段数不等：" + size);
                    }
                    return;
                }
            } else if (subModeColorPiece12) {
                if (size != 12) {
                    //不支持
                    if (LogInfra.openLog()) {
                        LogInfra.Log.e(TAG, "颜色数和灯段数不等：" + size);
                    }
                    return;
                }
            } else if (subModeColorPiece10) {
                if (size != 10) {
                    //不支持
                    if (LogInfra.openLog()) {
                        LogInfra.Log.e(TAG, "颜色数和灯段数不等：" + size);
                    }
                    return;
                }
            } else if (subModeColorPiece18) {
                if (size != 18) {
                    //不支持
                    if (LogInfra.openLog()) {
                        LogInfra.Log.e(TAG, "颜色数和灯段数不等：" + size);
                    }
                    return;
                }
            } else {
                if (size != 15) {
                    //不支持
                    if (LogInfra.openLog()) {
                        LogInfra.Log.e(TAG, "颜色数和灯段数不等：" + size);
                    }
                    return;
                }
            }
            showLoading();
            int subModeColorVersion = -1;
            if (ui_type_normal == uiTypeBle) {
                subModeColorVersion = Support.supportPartBrightness(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, "");
            } else if (ui_type_normal == uiTypeIot) {
                subModeColorVersion = Support.supportPartBrightness(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, ext.wifiSoftVersion);
            }
            boolean supportColorStripMulti = Support.supportColorStripMulti(info.goodsType, info.pactType, info.pactCode);
            LogInfra.Log.i(TAG, "onEventColorStrip() supportColorStripMulti = " + supportColorStripMulti);
            if (supportColorStripMulti) {
                Colors colors = event.colors;
                MultipleColorStripControllerV1 stripControllerV1 = new MultipleColorStripControllerV1(colors);
                if (bleOp.isOpCommEnable()) {
                    bleOp.executeMultiOpV1(stripControllerV1);
                } else {
                    CmdPtReal cmdPtReal = CmdPtReal.makeColorStripCmdPtReal(stripControllerV1, new ICmdPtRealOpResult() {
                        @Override
                        public void fail() {
                            EventLightStripResult.sendEventLightStripResult(false);
                        }

                        @Override
                        public void scu() {
                            Support.makeColorStripMode(info, colors);
                            EventLightStripResult.sendEventLightStripResult(true);
                        }
                    });
                    iotOp.writeCmd(cmdPtReal);
                }
                return;
            }

            AbsSingleController[] modeControllers;
            if (subModeColorVersion == 0) {
                modeControllers = SubModeColor.makeSubModeColor(event.colors);
            } else {
                if (subModeColorPiece20) {
                    modeControllers = SubModeColorV6.makeSubModeColor(event.colors);
                } else if (subModeColorPiece12) {
                    modeControllers = SubModeColorV4.makeSubModeColor(event.colors);
                } else if (subModeColorPiece10) {
                    modeControllers = SubModeColorV3.makeSubModeColor(event.colors);
                } else {
                    modeControllers = SubModeColorV2.makeSubModeColor(event.colors);
                }
            }
            if (bleOp.isOpCommEnable()) {
                bleOp.executeOp(modeControllers);
            } else {
                if (modeControllers != null) {
                    List<byte[]> bytes = new ArrayList<>();
                    for (AbsSingleController absSingleController : modeControllers) {
                        bytes.add(absSingleController.getValue());
                    }
                    CmdPtReal cmdPtReal = new CmdPtReal(bytes);
                    iotOp.writeCmd(cmdPtReal);
                }
            }
        }
    }
    /*新DIY交互补充代码逻辑*/

    private void checkDiyModeInfo(SubModeNewDiy diy) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyModeInfo()");
        }
        if (diy == null) return;
        /*刷新在模式中展示的DIY列表*/
        diy.setDiyGroups(curDiyGroups);
        /*获取当前选中的diyValueKey*/
        int diyCode = diy.getDiyCode();
        int newVersion = Support.getDiyVersion(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard);
        String lastDiyApplyKey = Diy.getLastDiyApplyKey(newVersion, info.goodsType, info.ic, info.sku, diyCode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyMode() lastDiyApplyKey = " + lastDiyApplyKey);
        }
        diy.setDiyValueKey(lastDiyApplyKey);
    }

    private void checkDiyMode(AbsMode mode) {
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeNewDiy) {
            checkDiyModeInfo((SubModeNewDiy) subMode);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEvent2AcDiyGroup(Event2AcDiyGroup event) {
        int icNum = info.ic;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEvent2AcDiyGroup icNum = " + icNum);
        }
        if (Support.goodsTypeH612x(info.goodsType) && info instanceof BleIotInfoV1) {
            ArrayList<Integer> subIcList = new ArrayList<>();
            subIcList.add(info.ic_sub_1);
            subIcList.add(info.ic_sub_2);
            AcDiyGroup.jump2DiyGroupAcIn(ac, info.sku, info.goodsType, getDiySupport(), info.ic_sub_1 + info.ic_sub_2, subIcList, true, false);
        } else {
            AcDiyGroup.jump2DiyGroupAc(ac, info.sku, info.goodsType, getDiySupport(), icNum, true);
        }
    }

    private DiySupportV1 getDiySupport() {
        boolean supportGraffitiInSeries = Support.supportGraffitiInSeries(info.goodsType);
        return Diy.getDiySupport(supportGraffitiInSeries ? 3 : 2);
    }

    private DiyValue applyingDiyValue;

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApplyInModeShowing(EventDiyApply4InModeShowing event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing()");
        }
        modeUI.setModeChanged(true);
        DiyProtocol diyProtocol = event.getDiyProtocol();
        if (diyProtocol != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV0 = applyDiyV0(diyProtocol);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV0 = " + applyDiyV0);
            }
            if (applyDiyV0) {
                showLoading();
            }
            return;
        }

        DiyTemplate diyTemplate = event.getDiyTemplate();
        if (diyTemplate != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV2 = applyDiyV2(diyTemplate);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV2 = " + applyDiyV2);
            }
            if (applyDiyV2) {
                showLoading();
            }
            return;
        }

        DiyGraffitiV2 diyGraffitiV2 = event.getDiyGraffitiV2();
        if (diyGraffitiV2 != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV3 = applyDiyV3(diyGraffitiV2);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV3 = " + applyDiyV3);
            }
            if (applyDiyV3) {
                showLoading();
            }
            return;
        }

        DiyStudio diyStudio = event.getDiyStudio();
        if (diyStudio != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV4 = applyDiyV4(diyStudio);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV4 = " + applyDiyV4);
            }
            if (applyDiyV4) {
                showLoading();
            }
            return;
        }

        DiyAi diyAi = event.getDiyAi();
        if (diyAi != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV5 = applyDiyV5(diyAi);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV5 = " + applyDiyV5);
            }
            if (applyDiyV5) {
                showLoading();
            }
        }

    }

    private boolean applyDiyV0(@NonNull DiyProtocol diyProtocol) {
        if (bleOp.isOpCommEnable()) {
            MultipleDiyControllerV1 multipleDiyController = new MultipleDiyControllerV1(diyProtocol);
            bleOp.executeMultiOpV1(multipleDiyController);
            return true;
        } else if (iotOp.isOpCommEnable()) {
            CmdPtReal cmdPtReal = CmdPtReal.getDiyCmdPtV1(diyProtocol);
            iotOp.writeCmd(cmdPtReal);
            return true;
        }
        return false;
    }

    private boolean applyDiyV2(@NonNull DiyTemplate diyTemplate) {
        if (bleOp.isOpCommEnable()) {
            int diyCode = diyTemplate.diyCode;
            int templateCode = diyTemplate.scenesCode;
            String effectStr = diyTemplate.effectStr;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV2() diyCode = " + diyCode + " ; templateCode = " + templateCode + " ; effectStr = " + effectStr);
            }
            AbsMultipleControllerV1 diyTemplateController = ScenesOp.parseDiyTemplate(templateCode, diyCode, effectStr);
            bleOp.executeMultiOpV1(diyTemplateController);
            return true;
        } else if (iotOp.isOpCommEnable()) {
            int diyCode = diyTemplate.diyCode;
            int templateCode = diyTemplate.scenesCode;
            String effectStr = diyTemplate.effectStr;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyTemplate() diyCode = " + diyCode + " ; templateCode = " + templateCode + " ; effectStr = " + effectStr);
            }
            AbsMultipleControllerV14DiyTemplate diyTemplateController = ScenesOp.parseDiyTemplate(templateCode, diyCode, effectStr);
            CmdPtReal cmdPt = CmdPtReal.getDiyTemplateCmdPt(diyTemplateController);
            this.diyCode = diyCode;
            this.diyTemplateCode = templateCode;
            iotOp.writeCmd(cmdPt);
            return true;
        }
        return false;
    }

    private boolean applyDiyV3(@NonNull DiyGraffitiV2 diyGraffitiV2) {
        if (bleOp.isOpCommEnable()) {
            bleOp.executeMultiOpV1(Support.makeGraffitiController(info.goodsType, diyGraffitiV2));
            return true;
        } else if (iotOp.isOpCommEnable()) {
            CmdPtReal cmdPt = CmdPtReal.getDiyCmdPt4DiyGraffitiV2(info.goodsType, diyGraffitiV2);
            int commandPacketNum = cmdPt.getCommandSize();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyGraffiti() commandPacketNum = " + commandPacketNum);
            }
            if (commandPacketNum > 19) {
                toast(R.string.b2light_ble_disconnect_hint);
                EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
                return true;
            }
            iotOp.writeCmd(cmdPt);
            return true;
        }
        return false;
    }

    private boolean applyDiyV4(@NonNull DiyStudio diyStudio) {
        if (bleOp.isOpCommEnable()) {
            int scenesCode = diyStudio.scenesCode;
            String effectStr = diyStudio.effectStr;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV4() scenesCode = " + scenesCode + " ; effectStr = " + effectStr);
            }
            boolean supportRgbicV1 = Support.isSupportRgbicV1(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, ext.wifiSoftVersion);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV4() supportRgbicV1 = " + supportRgbicV1);
            }
            AbsMultipleControllerV14DiyTemplate multi4Scenes = diyStudio.toMulti4Scenes4RgbicV1(supportRgbicV1 ? 1 : 0);
            if (multi4Scenes != null) {
                bleOp.executeMultiOpV1(multi4Scenes);
                return true;
            }
        } else if (iotOp.isOpCommEnable()) {
            int diyCode = diyStudio.diyCode;
            int scenesCode = diyStudio.scenesCode;
            String effectStr = diyStudio.effectStr;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV4() diyCode = " + diyCode + " ; scenesCode = " + scenesCode + " ; effectStr = " + effectStr);
            }
            boolean supportRgbicV1 = Support.isSupportRgbicV1(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, ext.wifiSoftVersion);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV4() supportRgbicV1 = " + supportRgbicV1);
            }
            AbsMultipleControllerV14DiyTemplate multi4Scenes = diyStudio.toMulti4Scenes4RgbicV1(supportRgbicV1 ? 1 : 0);
            if (multi4Scenes != null) {
                CmdPtReal cmdPt = CmdPtReal.getDiyTemplateCmdPt(multi4Scenes);
                this.diyCode = diyCode;
                this.diyTemplateCode = scenesCode;
                iotOp.writeCmd(cmdPt);
                return true;
            }
        }
        return false;
    }

    private boolean applyDiyV5(@NonNull DiyAi diyAi) {
        Command4PtReal ptReal = diyAi.command4PtReal;
        if (bleOp.isOpCommEnable()) {
            bleOp.executeMultiple4PtReal(PtRealController.makePtRealController(ptReal.opCommands, diyAi.diyCode, diyAi.scenesCode));
            return true;
        } else if (iotOp.isOpCommEnable()) {
            diyCode = diyAi.diyCode;
            diyTemplateCode = diyAi.scenesCode;
            CmdPtReal ptRealCmd = CmdPtReal.getPtRealCmd(ptReal.getCommands4IotPtReal());
            iotOp.writeCmd(ptRealCmd);
            return true;
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN, priority = 100)
    public void onEventDiyApplyResult(EventDiyApplyResult event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyResult() result = " + result + " ; diyCode = " + diyCode);
        }
        if (applyingDiyValue != null && applyingDiyValue.diyCode == diyCode) {
            if (result) {
                String diyValueKey = applyingDiyValue.getDiyValueKey();
                /*记录上次应用的DIY*/
                LastDiyConfig.read().saveLastDiyValueKey(info.sku, diyValueKey, diyCode, applyingDiyValue.effectCode);
            } else {
                toast(R.string.b2light_diy_apply_fail);
                hideLoading();
            }
        }
        applyingDiyValue = null;
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventDiyModeShowingChange(EventDiyModeShowingChange event) {
        List<DiyGroup> diyShortcuts = DiyShortcutManger.getDiyShortcuts(info.sku, info.goodsType, info.ic, getDiySupport());
        curDiyGroups.clear();
        if (diyShortcuts != null && !diyShortcuts.isEmpty()) {
            curDiyGroups.addAll(diyShortcuts);
        }
        handler.post(new CaughtRunnable() {
            @Override
            protected void runSafe() {
                checkUi();
            }
        });
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyModeShowingChange() curDiyGroups.size = " + curDiyGroups.size());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSwitchMicPickUpType(EventSwitchMicPickUpType event) {
        isSwitchMicPickUpType = true;
        modeUI.switchMicPickUpMode(event.isMicMode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onMicSetRgbController(MicSetRgbController controller) {
        if (bleOp.isOpCommEnable()) {
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_updating)) return;/*升级中，忽略mic发送逻辑*/
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_rebooting))
                return;/*重启过程中，忽略mic发送逻辑*/
            if (controller.isInitMode) {
                EventBleStatus.sendEvent(EventBleStatus.BleStatus.read_info_over);
            }
            if (!controller.sendByColorMode) {
                bleOp.executeExtOp(controller);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onRealtimeColorChangeEvent(RealtimeColorChangeEvent event) {
        if (bleOp.isOpCommEnable()) {
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_updating)) return;/*升级中，忽略mic发送逻辑*/
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_rebooting))
                return;/*重启过程中，忽略mic发送逻辑*/
            if (!Util4ColorRealtime.supportColorRealtime(info.sku, info.versionSoft, info.versionHard))
                return;
            AbsMicFragmentV4.SupportMicStatus micStatus = Support.getMicStatus(info.sku, info.versionSoft, info.goodsType);
            int controllerType = Util4ColorRealtime.getControllerType(micStatus, event.rgbicPartChoose);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onRealtimeColorChangeEvent() controllerType = " + controllerType);
            }
            if (controllerType == Util4ColorRealtime.CONTROLLER_TYPE_NO_SUPPORT) return;
            ISubMode subMode = event.subMode;
            int color = event.color;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onRealtimeColorChangeEvent() subMode = " + subMode + " ; color = " + color);
            }
            AbsSingleController controller;
            if (controllerType == Util4ColorRealtime.CONTROLLER_TYPE_COLOR) {
                Mode mode = new Mode();
                mode.subMode = subMode;
                controller = new ModeController(mode);
            } else {
                controller = new MicSetRgbController(ColorUtils.getRgbBytes(color));
            }
            bleOp.executeExtOp(controller);
        }
    }

    /*DIY引导逻辑*/

    private void hideDiyGuide() {
        if (hadShowGuide) {
            hadShowGuide = false;
            GuideDialog.hideDialog(TAG);
        }
    }

    private boolean hadDiyGuide = false;
    private boolean hadShowGuide;

    private void checkDiyGuide() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyGuide() hadDiyGuide = " + hadDiyGuide);
        }
        if (hadDiyGuide) return;
        if (modeUI != null) {
            hadDiyGuide = true;
            hadShowGuide = true;
            Util4Diy.checkShowDiyGuide4ModeNum(ac, TAG, modeUI.getModeNum());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSetMultiMusicEffect(EventSetMultiMusicEffect event) {
        int sensitivity = event.sensitivity;
        modeUI.setModeChanged(true);
        AbsNewMusicEffect newMusicEffect = event.newMusicEffect;
        int musicCode = newMusicEffect.getMusicCode();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNewMusicEffect() sensitivity = " + sensitivity + " ; newMusicEffect.musicCode = " + musicCode);
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            MultipleController4Music multipleController4Music = new MultipleController4Music(sensitivity, newMusicEffect);
            bleOp.executeMultiOpV2(multipleController4Music);
            return;
        }
        if (iotOp.isOpCommEnable()) {
            showLoading();
            MultipleController4Music multipleController4Music = new MultipleController4Music(sensitivity, newMusicEffect);
            CmdPtReal cmdPt = CmdPtReal.getMultiNewMusicMode(multipleController4Music);
            iotOp.writeCmd(cmdPt);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEvent2ConnectInDetail(Event2ConnectInDetail event) {
        boolean supportFeast = supportFeast();
        LogInfra.Log.i(TAG, "onEvent2ConnectInDetail() supportFeast = " + supportFeast);
        if (!supportFeast) return;
        info.reconnectInDetail = true;
        bleOp.enforceStepReconnect();
        startOpComm();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventIcNotify(EventIcNotify event) {
        int ic = event.ic;
        LogInfra.Log.i(TAG, "onEventIcNotify() ic = " + ic);
        updateIc(ic);
    }

    private void updateIc(int ic) {
        LogInfra.Log.i(TAG, "updateIc() ic = " + ic + " ; info.ic = " + info.ic);
        boolean icChange = info.ic != ic && ic > 0;
        if (icChange) {
            /*上报ic信息*/
            DeviceIcRequest request = new DeviceIcRequest(String.valueOf(System.currentTimeMillis()), info.sku, info.device, ic);
            Cache.get(IDeviceNet.class).updateDeviceIc(request).enqueue(new Network.IHCallBack<>(request));
            info.ic = ic;
            checkUi();
        }
        EventOpFreshIcResult.sendEventOpFreshIcResult(EventOpFreshIcResult.RESULT_TYPE_FRESH_OP_RESULT, true);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventCheckSnapshotEnable(EventCheckSnapshotEnable event) {
        LogInfra.Log.i(TAG, "onEventCheckSnapshotEnable()");
        SnapshotDataUtil.getInstance(ext).dealData(modeUI, info);
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventApplySnapshotCmd(EventApplySnapshotCmd event) {
        SnapshotCmdM.sendMsg(bleOp.isOpCommEnable(), iotOp.isOpCommEnable(), BleComm.serviceUuid, BleComm.characteristicUuid, info.topic, event.snapshotMode);
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventPowerOffMemory(Event4PowerOfMemoryOp event) {
        if (!event.isSame(info.sku, info.device)) return;
        if (bleOp.isOpCommEnable()) {
            OnOffMemoryController controller = new OnOffMemoryController(event.getMemory(), true);
            bleOp.executeOp(controller);
        } else if (iotOp.isOpCommEnable()) {
            OnOffMemoryController controller = new OnOffMemoryController(event.getMemory(), true);
            CmdPtReal cmdPtReal = CmdPtReal.makeCmdPtRealWithCallBack(controller, new ICmdPtRealOpResult() {
                @Override
                public void fail() {
                    Event4PowerOfMemoryOpResult.sendFail(event.getSku(), event.getDevice());
                }

                @Override
                public void scu() {
                    info.powerOffMemoryValue = event.getMemory();
                    Event4PowerOfMemoryOpResult.sendSuc(event.getSku(), event.getDevice(), event.getMemory());
                }
            });
            iotOp.writeCmd(cmdPtReal);
        } else {
            Event4PowerOfMemoryOpResult.sendFail(event.getSku(), event.getDevice());
        }
    }

    @NonNull
    @Override
    public FilterSceneInfo4BleIot getFilterSceneInfo() {
        return new FilterSceneInfo4BleIot(info.sku, info.versionSoft, info.versionHard, uiTypeBle == ui_type_normal, ext.wifiSoftVersion, ext.wifiHardVersion, null, null);
    }


    @NonNull
    @Override
    public AbsDeviceInfo getDeviceInfo() {
        return info;
    }

    @Override
    public int getBrightness() {
        return ext.brightness;
    }

    @Override
    public AbsSingleController getColorTemController4Ble(int kelvin) {
        int temColor = com.govee.base2home.Constant.getTemColorByKelvin(kelvin)[2];
        return Comm.makeColorTemController4BleComm(info.sku, info.goodsType, info.pactType, info.pactCode, temColor);
    }

    @Override
    public void onColorTemAndBrightnessChangeCallback(int kelvin, int brightness) {
        ext.brightness = brightness;
        if (isColorMode(info.mode)) {
            //SubModeColorV2 H70A1/2/3
            SubModeColorV2 subModeColorV2 = new SubModeColorV2();
            int ctRgb = com.govee.base2home.Constant.getTemColorByKelvin(kelvin)[2];
            subModeColorV2.setTemRgbAndKelvin(ctRgb, kelvin);
            /*色温设置-整条灯带都是同一个色温值*/
            Arrays.fill(subModeColorV2.ctlLight, true);
            Mode mode = new Mode();
            mode.subMode = subModeColorV2;
            info.mode = mode;
        }
        checkUi();
    }

    @Override
    public AbsCmd getColorTemController4Iot(int kelvin) {
        int temColor = com.govee.base2home.Constant.getTemColorByKelvin(kelvin)[2];
        return Comm.makeColorTemCmd4IotComm(info.sku, info.goodsType, info.pactType, info.pactCode, temColor);
    }

    @NonNull
    @Override
    public AbsBle getBle() {
        return bleOp.getBle();
    }

    @Override
    public AbsIotOpV1 getIotOp() {
        return iotOp;
    }

    //612x裁剪校准
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventCheckIC(EventCutCaliIC event) {
        int type = event.getType();
        int channel = event.getChannel();
        int icPos = event.getIcPos();
        LogInfra.Log.i(TAG, "onEventCheckIC() type = " + type + " channel = " + channel + " icPos = " + icPos);
        int controllerSize = type == 4 ? 2 : 1;//确认裁剪 ic数可能改变 再读一次ic
        AbsSingleController[] controllers = new AbsSingleController[controllerSize];
        controllers[0] = new CheckIcController(type, channel, icPos);
        if (type == 4) {
            controllers[1] = new ReadIcController();
        }
        if (bleOp.isOpCommEnable()) {
            bleOp.executeOp(controllers);
        }
    }
}