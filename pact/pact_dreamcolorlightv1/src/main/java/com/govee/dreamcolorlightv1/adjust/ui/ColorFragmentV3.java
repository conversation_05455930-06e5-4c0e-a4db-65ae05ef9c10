package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.ui.R;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.v1.AbsColorFragmentV13;

import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.ui.Cons;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.EventBus;

import java.util.Arrays;

import androidx.annotation.NonNull;

/**
 * Create by hey on 2021/2/5
 * $
 */
public class ColorFragmentV3 extends AbsColorFragmentV13 {
    private SubModeColorV2 subModeColor = new SubModeColorV2();

    @Override
    protected void init() {
        super.init();
        updateUi();
    }

    @Override
    protected void colorChange(int type, int color, int kevin, boolean[] checks, int brightness) {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        if (color == 0 && kevin == 0) {
            subModeColor.brightness = brightness;
        } else if (kevin == 0) {
            subModeColor.rgb = color;
        } else {
            subModeColor.kelvin = kevin;
            subModeColor.ctRgb = color;
            subModeColor.rgb = ColorUtils.toWhite();
        }
        subModeColor.ctlLight = checks;
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.mode_click_color_ + UtilColor.colorName(color));
        /*统计颜色变更来源*/
        AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.getColorFromTypeStr(type));
        AnalyticsRecorder.getInstance().recordUseCount(ParamKey.color_mode, ParamFixedValue.times);
        if (type == Cons.color_from_type_wcBar && kevin > 0) {
            /*统计颜色色温k值*/
            AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.temperature_ + kevin);
        }
    }

    private void updateUi() {
        if (isViewInflateOk()) {
            int opType = subModeColor.opType;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "updateUi() opType = " + opType);
            }
            if (opType == SubModeColorV2.op_type_ui_no_fresh) return;
            if (opType == SubModeColorV2.op_type_gradual) {
                subModeColor.opType = SubModeColorV2.op_type_ui_no_fresh;
                return;
            }
            subModeColor.opType = SubModeColorV2.op_type_ui_no_fresh;
            int rgb = subModeColor.rgb;
            int ctRgb = subModeColor.ctRgb;
            int brightness = subModeColor.brightness;
            /*当前处于设置分段颜色逻辑*/
            if ((subModeColor.rgb == 0 && ColorUtils.isColorTem(ctRgb)) || (subModeColor.rgb == ColorUtils.toWhite() && ColorUtils.isColorTem(ctRgb))) {
                updateSelectedBulbColor(subModeColor.ctRgb);
            } else if (rgb != ColorUtils.toColor(0, 0, 0) && rgb != 0 && rgb != ResUtil.getColor(R.color.black)) {
                updateSelectedBulbColor(rgb);
            } else if (brightness == 0 && subModeColor.brightnessSet == null) {
                updateSelectedBulbColor(ResUtil.getColor(R.color.ui_light_style_1));
            }
            if (brightness != 0) {
                updateSelectedBulbBrightness(brightness);
            }
            int[] rgbSet = subModeColor.rgbSet;
            if (rgbSet != null && rgbSet.length > 0) {
                /*当前处于设置全部颜色逻辑*/
                updateBulbUI(rgbSet);
            }
            int[] brightnessSet = subModeColor.brightnessSet;
            if (brightnessSet != null && brightnessSet.length > 0) {
                updateBulbBrightnessUI(brightnessSet);
            }
            if (ColorUtils.isColorTem(rgb)) ctRgb = rgb;/*wifi透传色温*/
            /*更新色块和色条颜色*/
            boolean colorTem = ColorUtils.isColorTem(ctRgb);
            if (colorTem) {
                setColorWithTemColor(ColorUtils.toWhite(), ctRgb);
            } else {
                setColorWithTemColor(rgb, 0);
            }
        }
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof SubModeColorV2) {
            ((SubModeColorV2) subMode).checkAnalytic4SubModeUse(getSku());
            subModeColor = (SubModeColorV2) subMode;
            updateUi();
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    protected boolean supportRealTimeColor() {
        /*支持实时颜色设置*/
        return true;
    }

    @NonNull
    @Override
    protected ISubMode makePartChooseSubMode(boolean[] check, int color) {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        subModeColor.rgb = color;
        subModeColor.ctlLight = check;
        return subModeColor;
    }

    @Override
    public ISubMode getCompleteSubMode() {
        SubModeColor subMode = new SubModeColor();
        int[] rgbArray = Arrays.copyOf(rgb, bulb_num_max);
        for (int i : rgbArray) {
            if (i == 0) {
                return null;
            }
        }
        int[] brightnessArray;
        if (brightness == null) {
            brightnessArray = new int[bulb_num_max];
            for (int i = 0; i < bulb_num_max; i++) {
                brightnessArray[i] = 100;
            }
        } else {
            brightnessArray = Arrays.copyOf(brightness, bulb_num_max);
        }
        subMode.rgbSet = rgbArray;
        subMode.gradual = subModeColor.gradual;
        subMode.brightnessSet = brightnessArray;
        return subMode;
    }
}
