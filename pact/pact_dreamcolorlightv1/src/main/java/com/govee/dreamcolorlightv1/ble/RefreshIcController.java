package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsOnlyWriteSingleController;

/**
 * Create by lins<PERSON><PERSON> on 3/4/21
 */
public class RefreshIcController extends AbsOnlyWriteSingleController {

    @Override
    protected boolean writeResult(boolean writeSuc) {
        EventCheckIc.sendSuc(isWrite(), getCommandType(), getProType());
        return true;
    }

    @Override
    protected byte[] translateWrite() {
        return null;
    }

    @Override
    protected void fail() {
        EventCheckIc.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.SINGLE_WRITE_CHECK_IC;
    }
}