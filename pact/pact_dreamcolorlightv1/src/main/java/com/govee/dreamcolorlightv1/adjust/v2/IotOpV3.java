package com.govee.dreamcolorlightv1.adjust.v2;

import androidx.annotation.NonNull;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.pact.iot.AbsIotOpV1;
import com.govee.dreamcolorlightv1.iot.Cmd;
import com.govee.dreamcolorlightv1.iot.CmdStatus;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/7/25
 * iot操作op$
 */
public class IotOpV3 extends AbsIotOpV1 {
    @Override
    protected String getCmd4Pt() {
        return Cmd.ptReal;
    }

    @NonNull
    @Override
    protected AbsCmd getCmd4DeviceInfo() {
        return new CmdStatus();
    }

    @NonNull
    @Override
    protected String getAutoStatusCmd() {
        return Cmd.status;
    }

    @Override
    protected String getReadCmdParserKey(String cmd) {
        return Cmd.getCmdReadParseKey(cmd);
    }
}