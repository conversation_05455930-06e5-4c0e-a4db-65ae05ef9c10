package com.govee.dreamcolorlightv1.adjust.v2;

import com.govee.base2home.pact.Protocol;
import com.govee.base2light.pact.iot.IPactResult4Iot;
import com.govee.dreamcolorlightv1.pact.Support;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022/5/12
 * v3版本的iot协议$
 */
class IotPactV3 extends IotPactV2 {
    IotPactV3(IPactResult4Iot iPactResult) {
        super(iPactResult);
    }

    @Override
    protected boolean isSupportPact(int pactType, int pactCode) {
        for (Protocol pro : Support.supportProtocolsV8) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        for (Protocol pro : Support.supportProtocolsV8_1) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        for (Protocol pro : Support.supportProtocolsV8_2) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        for (Protocol pro : Support.supportProtocolsV8_2_1) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        for (Protocol pro : Support.supportProtocolsV8_3) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }
}