package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2022/6/29
 * event-读取ic
 */
public class EventReadIcV2 extends AbsControllerEvent {
    public int sectionNum1;
    public int sectionNum2;
    public int ic1;
    public int ic2;

    protected EventReadIcV2(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    public static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventReadIcV2(false, write, commandType, proType));
    }

    public static void sendSuc(boolean write, byte commandType, byte proType, int sectionNum1, int sectionNum2, int ic1, int ic2) {
        EventReadIcV2 event = new EventReadIcV2(true, write, commandType, proType);
        event.sectionNum1 = sectionNum1;
        event.sectionNum2 = sectionNum2;
        event.ic1 = ic1;
        event.ic2 = ic2;
        EventBus.getDefault().post(event);
    }
}