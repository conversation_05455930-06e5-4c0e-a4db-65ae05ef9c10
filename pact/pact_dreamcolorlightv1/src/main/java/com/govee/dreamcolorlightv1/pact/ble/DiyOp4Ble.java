package com.govee.dreamcolorlightv1.pact.ble;

import com.govee.dreamcolorlightv1.pact.Support;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/8/13
 * $
 */
public class DiyOp4Ble extends AbsDiyOp4Ble {
    @Override
    protected int[] supportGoodsType() {
        return Support.effect4OpBleGoodsTypes;
    }

    private DiyOp4Ble() {
    }

    private static class Builder {
        private static final DiyOp4Ble instance = new DiyOp4Ble();
    }

    public static DiyOp4Ble op = Builder.instance;
}