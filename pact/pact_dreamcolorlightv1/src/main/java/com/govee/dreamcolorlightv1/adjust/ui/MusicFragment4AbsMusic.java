package com.govee.dreamcolorlightv1.adjust.ui;

import android.text.TextUtils;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.sku.SkuIcM;
import com.govee.base2light.ble.IMusicEffectStatic;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.OldMusicEffect;
import com.govee.base2light.light.v1.AbsNewMusicFragment;
import com.govee.base2light.light.v1.absmusic.AbsMusicFragment;
import com.govee.base2light.light.v1.absmusic.DefaultMusicOp;
import com.govee.base2light.light.v1.absmusic.IMusicMode;
import com.govee.base2light.light.v1.absmusic.IMusicMode4New;
import com.govee.base2light.light.v1.absmusic.IMusicMode4Old;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV3;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.SafeLog;

import org.greenrobot.eventbus.EventBus;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2021/5/5
 * 新音乐模式$
 * 支持抽象音乐
 */
public class MusicFragment4AbsMusic extends AbsMusicFragment {
    private SubModeMusicV3 subModeMusic = new SubModeMusicV3();
    private List<AbsNewMusicFragment.SubMusicMode> subMusicModeList;

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_music;
    }

    protected int getIcNum() {
        int icNum = subModeMusic.getIcNum();
        if (icNum > 0) return icNum;
        return SkuIcM.getInstance().getDefIcNum(getSku());
    }

    @Override
    public ISubMode getCompleteSubMode() {
        SubModeMusicV3 subModeMusic = this.subModeMusic.copy();

        boolean oldSubMusic = !this.subModeMusic.isNewMusic;
        if (oldSubMusic) {
            OldMusicEffect oldMusicEffect = OldMusicEffect.read4OldMusic(getSku(), getDevice(), this.subModeMusic.getMusicCode(), this.subModeMusic.sensitivity);
            subModeMusic.oldMusicEffectChange(oldMusicEffect);
        } else {
            subModeMusic.setSensitivity(this.subModeMusic.getSensitivity());
            subModeMusic.musicEffect = AbsNewMusicEffect.getLocalNewMusicEffect(getSku(), getDevice(), this.subModeMusic.getMusicCode(), getIcNum());
        }
        return subModeMusic;
    }

    @NonNull
    @Override
    public AbsMode getMode() {
        return new Mode();
    }

    @Nullable
    protected AbsNewMusicFragment.SubMusicMode querySubMusicModeByMusicCode(int subMusicCode) {
        if (getIMusicMode4Old() != null) {
            List<AbsNewMusicFragment.SubMusicMode> items = getIMusicMode4Old().getMusicEffectList();
            for (AbsNewMusicFragment.SubMusicMode item : items) {
                if (item.musicCode == subMusicCode) return item;
            }
        }
        return null;
    }

    protected void analyticSubModeDetail(String paramValuePre, @Nullable String subModeDetailStr) {
        if (TextUtils.isEmpty(subModeDetailStr)) return;
        AnalyticsRecorder.getInstance().recordUseCount(getSku(), paramValuePre + subModeDetailStr);
    }

    @Override
    public byte[] getSubModeTypeArray() {
        return new byte[]{BleProtocol.sub_mode_new_music, BleProtocolConstants.sub_mode_abs_music};
    }

    @NonNull
    @Override
    public IMusicMode getIMusicMode() {
        return new IMusicMode() {
            @Nullable
            @Override
            public IMusicMode4New getIMusicMode4New() {
                return null;
            }

            @Override
            public IMusicMode4Old getIMusicMode4Old() {
                return new IMusicMode4Old() {
                    @Override
                    public boolean supportAnalyticSubModeDetail() {
                        return true;
                    }

                    @Override
                    public void onSensitivityChange(int sensitivity) {
                        SubModeMusicV3 copy = subModeMusic.copy();
                        copy.setSensitivity(sensitivity);
                        Mode mode = new Mode();
                        mode.subMode = copy;
                        EventBus.getDefault().post(mode);
                    }

                    @Override
                    public int getSensitivity() {
                        return subModeMusic.getSensitivity();
                    }

                    @NonNull
                    @Override
                    public List<AbsNewMusicFragment.SubMusicMode> getMusicEffectList() {
                        if (subMusicModeList == null) {
                            subMusicModeList = DefaultMusicOp.INSTANCE.makeRgbicSubMusicModes(IMusicEffectStatic.single_value_sub_energy, IMusicEffectStatic.single_value_sub_rhythm, IMusicEffectStatic.single_value_sub_specturm, IMusicEffectStatic.single_value_sub_scroll);
                        }
                        return subMusicModeList;
                    }

                    @Override
                    public int getCurSubMusicCode() {
                        return subModeMusic.getMusicCode();
                    }

                    @Override
                    public void updateDefaultSubMode(@NonNull ISubMode subMode) {
                        if (subMode instanceof SubModeMusicV3) {
                            boolean checkAnalytic4SubModeUse = ((SubModeMusicV3) subMode).checkAnalytic4SubModeUse(getSku());
                            subModeMusic = (SubModeMusicV3) subMode;
                            boolean newMusicCode = Support.isNewMusicCode(subModeMusic.getMusicCode());
                            if (!newMusicCode) {
                                /*旧音乐模式-需要存储到OldMusicEffect*/
                                saveOldMusic(subModeMusic.getSensitivity(), subModeMusic.isAutoColor(), subModeMusic.isDynamic(), subModeMusic.getRgb());
                            }
                            if (checkAnalytic4SubModeUse) {
                                SafeLog.i(TAG, () -> "updateSubMode() analyticSubModeDetail");
                                AbsNewMusicFragment.SubMusicMode subMusicMode = querySubMusicModeByMusicCode(getCurSubMusicCode());
                                if (subMusicMode != null) {
                                    analyticSubModeDetail(ParamFixedValue.mode_use_music_, subMusicMode.getAnalyticSubModeStr());
                                }
                            }
                        }
                    }

                    @Override
                    public void showOldSubMusicEditDialog(@NonNull AbsNewMusicFragment.SubMusicMode musicMode) {
                        BleIotInfo bleIotInfo = getViewModel().getBleIotInfo();
                        int musicCode = musicMode.musicCode;
                        if (musicCode == IMusicEffectStatic.single_value_sub_rhythm) {
                            DefaultMusicOp.INSTANCE.showOldMusicEditV1(getActivity(), bleIotInfo.sku, bleIotInfo.device, musicMode, getSensitivity(), subModeMusic.isAutoColor(), subModeMusic.getRgb(), subModeMusic.isDynamic(), oldMusicEffect -> {
                                oldMusicParamsChange(oldMusicEffect);
                                return null;
                            });
                        } else {
                            DefaultMusicOp.INSTANCE.showOldMusicEditV0(getActivity(), bleIotInfo.sku, bleIotInfo.device, musicMode, getSensitivity(), subModeMusic.isAutoColor(), subModeMusic.getRgb(), subModeMusic.isDynamic(), oldMusicEffect -> {
                                oldMusicParamsChange(oldMusicEffect);
                                return null;
                            });
                        }
                    }

                    @Override
                    public void oldMusicParamsChange(@Nullable OldMusicEffect oldMusicEffect) {
                        if (oldMusicEffect == null) return;
                        SubModeMusicV3 copy = subModeMusic.copy();
                        copy.oldMusicEffectChange(oldMusicEffect);
                        Mode mode = new Mode();
                        mode.subMode = copy;
                        EventBus.getDefault().post(mode);
                    }

                    @Override
                    public int getLocalDefaultMusicCode() {
                        SubModeMusicV3 subModeMusicV3 = new SubModeMusicV3();
                        subModeMusicV3.loadLocal();
                        return subModeMusicV3.getMusicCode();
                    }
                };
            }
        };
    }
}