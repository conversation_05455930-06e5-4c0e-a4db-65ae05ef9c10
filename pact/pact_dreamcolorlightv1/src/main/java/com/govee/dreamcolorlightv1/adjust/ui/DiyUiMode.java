package com.govee.dreamcolorlightv1.adjust.ui;

import android.os.Bundle;

import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsDiyUiMode;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.govee.ui.R;

/**
 * Create by xieyingwu on 2/25/21
 * diy的模式Ui$
 */
public class DiyUiMode extends AbsDiyUiMode {
    private final int goodsType;

    public DiyUiMode(String sku, int goodsType) {
        super(sku);
        this.goodsType = goodsType;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_diy_mini, R.mipmap.new_control_light_btb_mode_diy_mini_press};
    }

    @Override
    public AbsModeFragment getUiFragment() {
        DiyFragment fragment = new DiyFragment();
        Bundle args = new Bundle();
        fragment.makeArguments(args, getSku(), goodsType);
        return fragment;
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_new_diy;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeNewDiy subModeNewDiy = new SubModeNewDiy();
        subModeNewDiy.loadLocal();
        return subModeNewDiy;
    }
}
