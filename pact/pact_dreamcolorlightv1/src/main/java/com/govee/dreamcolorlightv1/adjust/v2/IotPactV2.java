package com.govee.dreamcolorlightv1.adjust.v2;

import com.govee.base2home.pact.Protocol;
import com.govee.base2light.pact.iot.AbsIotPact;
import com.govee.base2light.pact.iot.IPactResult4Iot;
import com.govee.dreamcolorlightv1.iot.Cmd;
import com.govee.dreamcolorlightv1.pact.Support;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-11
 * v2版本的iot协议$
 */
public class IotPactV2 extends AbsIotPact {

    public IotPactV2(IPactResult4Iot iPactResult) {
        super(iPactResult);
    }

    @Override
    protected String getPactCmd() {
        return Cmd.online;
    }

    @Override
    protected boolean isSupportPact(int pactType, int pactCode) {
        for (Protocol pro : Support.supportProtocolsV2) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        /*BK*/
        for (Protocol pro : Support.supportProtocolsV5) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        /*ble+wifi - frk - 不支持设备校验*/
        for (Protocol pro : Support.supportProtocolsV5_1) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        /*ble+wifi - bk -支持设备校验*/
        for (Protocol pro : Support.supportProtocolsV7) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        //616c/d/e
        for (Protocol pro : Support.supportProtocolsV10) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        //612x
        for (Protocol pro : Support.supportProtocolsV11) {
            if (pro.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    @Override
    protected int getCmdVersion() {
        return 1;
    }
}