package com.govee.dreamcolorlightv1.adjust.setting;

import com.govee.ui.R;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import com.govee.base2home.custom.LoadingDialog;
import com.govee.base2home.device.DeviceM;
import com.govee.base2home.device.net.Config4IcSettings;
import com.govee.base2home.device.net.Event4NotifyGuideUpdate;
import com.govee.base2home.device.net.Guide;
import com.govee.base2home.ota.OtaType;
import com.govee.base2home.pact.support.OldDreamColorUtil;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2home.update.download.CheckVersion;
import com.govee.base2home.util.ClickUtil;
import com.govee.base2light.Constant;
import com.govee.base2light.ac.effect.EventTryConnectDeviceResult;
import com.govee.base2light.ac.update.OtaUpdateAcV1;
import com.govee.base2light.ac.update.OtaUpdateAcV2;
import com.govee.base2light.ac.update.OtaUpdateAcV3;
import com.govee.base2light.lowBlueLightControl.EventLowBlueLight;
import com.govee.base2light.lowBlueLightControl.EventLowBlueLightResult;
import com.govee.base2light.lowBlueLightControl.LowBlueLightBean;
import com.govee.base2light.lowBlueLightControl.LowBlueLightConfig;
import com.govee.base2light.wlanControl.WlanControlOperator;
import com.govee.ble.event.EventBleConnect;
import com.govee.dreamcolorlightv1.ConsV1;

import com.govee.dreamcolorlightv1.add.v2.WifiChooseAc;
import com.govee.dreamcolorlightv1.adjust.v2.EventOpFreshIc;
import com.govee.dreamcolorlightv1.adjust.v2.EventOpFreshIcResult;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.matter.Event4MatterBleOp;
import com.govee.matter.MatterManager;
import com.govee.matter.guide.Activity4MatterGuide;
import com.govee.matter.pact.MtPairProtocol;
import com.govee.ui.ac.AbsWifiBleSettingAcV3;
import com.govee.ui.component.OpItemWithHintView;
import com.govee.ui.component.WlanControlView;
import com.govee.ui.dialog.Dialog4LowBlueLight;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;
import com.ihoment.base2app.util.JumpUtil;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2022/7/27
 * ble+wifi设置页
 * 设置页支持ota升级入口
 */
public class SettingBleWifiAcV1 extends AbsWifiBleSettingAcV3 {
    private static final String intent_ac_key_goodsType = "intent_ac_key_goodsType";
    private static final String intent_ac_key_support_ic_fresh = "intent_ac_key_support_ic_fresh";
    private static final String intent_ac_key_adjust_connect_suc = "intent_ac_key_adjust_connect_suc";

    private int goodsType;
    private boolean adjustConnectSuc = false;
    private boolean supportIcFresh;

    private OpItemWithHintView opItemWithHintView;
    private boolean inRefreshIc = false;
    private String topic;
    private LowBlueLightBean config;
    private Dialog4LowBlueLight dialog4LowBlueLight;
    private boolean isBlueLightOpen;
    private int blueLightValue;
    private View pllLowBlueLightContainer;
    private View vLowBlueContainerDivider;

    /**
     * 跳转到支持设置页ota
     *
     * @param context
     * @param supportDeviceLock
     * @param goodsType
     * @param sku
     * @param device
     * @param deviceName
     * @param wifiMac
     * @param bleAddress
     * @param versionHard
     * @param versionSoft
     * @param checkVersion
     */
    public static void jump2SettingBleWifiAcV1(Context context, boolean supportDeviceLock, int goodsType, String sku, String device, String deviceName, String wifiMac, String bleAddress, String versionHard, String versionSoft, CheckVersion checkVersion, boolean supportIcFresh, boolean connectSuc, String topic, boolean blueLightOpen, int blueLightValue) {
        int[] wifiInputLimit = Support.getWifiInputLimit();
        Bundle bundle = makeAcBundle(supportDeviceLock, sku, device, deviceName, 22, wifiMac, bleAddress, wifiInputLimit[0], wifiInputLimit[1], versionHard, versionSoft, checkVersion);
        bundle.putInt(intent_ac_key_goodsType, goodsType);
        bundle.putBoolean(intent_ac_key_adjust_connect_suc, connectSuc);
        bundle.putBoolean(intent_ac_key_support_ic_fresh, supportIcFresh);
        bundle.putString(Constant.intent_ac_key_device_topic, topic);
        bundle.putBoolean(ConsV1.intent_ac_adjust_blue_light_open, blueLightOpen);
        bundle.putInt(ConsV1.intent_ac_adjust_blue_light_value, blueLightValue);
        JumpUtil.jump(context, SettingBleWifiAcV1.class, bundle);
    }

    /**
     * 跳转到支持设置页ota-->增加wifi版本信息(支持matter功能的设备用到)
     *
     * @param context
     * @param supportDeviceLock
     * @param goodsType
     * @param sku
     * @param device
     * @param deviceName
     * @param wifiMac
     * @param bleAddress
     * @param versionHard
     * @param versionSoft
     * @param checkVersion
     */
    public static void jump2SettingBleWifiAcV1(Context context, boolean supportDeviceLock, int goodsType, String sku, String device, String deviceName, String wifiMac, String bleAddress, String versionHard, String versionSoft, String wifiVersionHard, String wifiVersionSoft, CheckVersion checkVersion, boolean supportIcFresh, boolean connectSuc, String topic, boolean blueLightOpen, int blueLightValue) {
        int[] wifiInputLimit = Support.getWifiInputLimit();
        Bundle bundle = makeAcBundle(supportDeviceLock, sku, device, deviceName, 22, wifiMac, bleAddress, wifiInputLimit[0], wifiInputLimit[1], versionHard, versionSoft, checkVersion);
        bundle.putInt(intent_ac_key_goodsType, goodsType);
        bundle.putBoolean(intent_ac_key_adjust_connect_suc, connectSuc);
        bundle.putBoolean(intent_ac_key_support_ic_fresh, supportIcFresh);
        bundle.putString(Constant.intent_ac_key_device_topic, topic);
        bundle.putString(ConsV1.intent_ac_adjust_versionHard_4_wifi, wifiVersionHard);
        bundle.putString(ConsV1.intent_ac_adjust_versionSoft_4_wifi, wifiVersionSoft);
        bundle.putBoolean(ConsV1.intent_ac_adjust_blue_light_open, blueLightOpen);
        bundle.putInt(ConsV1.intent_ac_adjust_blue_light_value, blueLightValue);
        JumpUtil.jump(context, SettingBleWifiAcV1.class, bundle);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        goodsType = intent.getIntExtra(intent_ac_key_goodsType, 0);
        adjustConnectSuc = intent.getBooleanExtra(intent_ac_key_adjust_connect_suc, false);
        supportIcFresh = intent.getBooleanExtra(intent_ac_key_support_ic_fresh, false);
        topic = getIntent().getStringExtra(Constant.intent_ac_key_device_topic);
        wifiHv = getIntent().getStringExtra(ConsV1.intent_ac_adjust_versionHard_4_wifi);
        if (TextUtils.isEmpty(wifiHv)) {
            wifiHv = "";
        }
        wifiSv = getIntent().getStringExtra(ConsV1.intent_ac_adjust_versionSoft_4_wifi);
        if (TextUtils.isEmpty(wifiSv)) {
            wifiSv = "";
        }
        checkOpItemHint();
        checkSupportWlanControl();
        if (supportIcFresh) {
            DeviceM.getInstance.requestIcGuide(sku, goodsType);
        }
        if (TextUtils.isEmpty(wifiMac)) {
            wifiMac = "";
        }
        pllLowBlueLightContainer = findViewById(com.govee.dreamcolorlightv1.R.id.low_blue_light_container);
        vLowBlueContainerDivider = findViewById(com.govee.dreamcolorlightv1.R.id.v_low_blue_container_divider);
        setLowBlueLightClick();
        checkLowBlueLight();
    }

    private void checkLowBlueLight() {
        boolean supportLowBlueLight = LowBlueLightConfig.Companion.checkSkuSupport(goodsType, sku, versionSoft);
        pllLowBlueLightContainer.setVisibility(supportLowBlueLight ? View.VISIBLE : View.GONE);
        vLowBlueContainerDivider.setVisibility(supportLowBlueLight ? View.VISIBLE : View.GONE);
        if (supportLowBlueLight) {
            config = LowBlueLightConfig.Companion.getConfig(goodsType);
            isBlueLightOpen = getIntent().getBooleanExtra(ConsV1.intent_ac_adjust_blue_light_open, config.getDefaultOpen());
            blueLightValue = getIntent().getIntExtra(ConsV1.intent_ac_adjust_blue_light_value, config.getDefaultValue());
        }
    }

    private void setLowBlueLightClick() {
        if (pllLowBlueLightContainer != null) {
            pllLowBlueLightContainer.setOnClickListener(view -> {
                if (ClickUtil.getInstance.clickQuick()) {
                    return;
                }
                onClickLowBlueLight();
            });
        }
    }

    private void checkOpItemHint() {
        opItemWithHintView = findViewById(com.govee.dreamcolorlightv1.R.id.op_item_with_hint);
        if (opItemWithHintView != null) {
            opItemWithHintView.setOpClickListener(this::sendOpComm);
            opItemWithHintView.makeOpItemConfig(R.mipmap.new_sensor_setting_icon_shuaxin, ResUtil.getString(R.string.label_4_fresh_light_piece), ResUtil.getString(R.string.refresh));
            opItemHintUi();
        }

    }

    private void sendOpComm() {
        LogInfra.Log.i(TAG, "sendOpComm() adjustConnectSuc = " + adjustConnectSuc);
        if (adjustConnectSuc) {
            EventOpFreshIc.sendEventOpFreshIc();
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
        }
    }

    private void opItemHintUi() {
        LogInfra.Log.i(TAG, "opItemHintUi() supportIcFresh = " + supportIcFresh);
        if (opItemWithHintView != null) {
            opItemWithHintView.setVisibility(supportIcFresh ? View.VISIBLE : View.GONE);
            if (supportIcFresh) {
                Guide guide = Config4IcSettings.read().getGuide(sku, goodsType);
                if (guide == null) {
                    String popTitleStr = ResUtil.getString(R.string.label_4_fresh_light_piece);
                    String popDesStr = ResUtil.getString(R.string.hint_4_fresh_light_piece);
                    opItemWithHintView.makePopHint(popTitleStr, popDesStr);
                    opItemWithHintView.makeGuide(-1, "");
                } else {
                    String popTitleStr = ResUtil.getString(R.string.label_4_fresh_light_piece);
                    String popDes = guide.getPopDes();
                    if (popDes == null || TextUtils.isEmpty(popDes)) {
                        popDes = ResUtil.getString(R.string.hint_4_fresh_light_piece);
                    }
                    opItemWithHintView.makePopHint(popTitleStr, popDes);
                    opItemWithHintView.makeGuide(R.mipmap.new_setting_icon_segments_guide, guide.getGuideUrl());
                }
            }
        }
    }

    @Override
    protected void toUpdateAc() {
        int defSkuRes = ThemeM.getDefSkuRes(sku, "");
        boolean bkOta = OtaType.isBKOtaV1(versionHard);
        boolean frkOtaV1 = OtaType.isFRKOtaV1(versionHard);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "toUpdateAc() bkOta = " + bkOta + " ; frkOtaV1 = " + frkOtaV1);
        }
        if (frkOtaV1) {
            OtaUpdateAcV3.jump2OtaUpdateAcV3(this, sku, deviceName, versionSoft, defSkuRes, checkVersion);
        } else if (bkOta) {
            OtaUpdateAcV2.jump2OtaUpdateAcV2(this, sku, deviceName, versionSoft, defSkuRes, checkVersion);
        } else {
            OtaUpdateAcV1.jump2OtaUpdateAcV1(this, sku, deviceName, versionSoft, defSkuRes, checkVersion);
        }
    }

    @Override
    protected void jump2WifiSettingAc() {
        WifiChooseAc.jump2wifiChooseAcByChangeWifi(this, goodsType, sku, device, deviceName, "", bleAddress, versionHard, versionSoft, wifiHv, wifiSv, wifiMac);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventAdjustConnectResult(EventTryConnectDeviceResult event) {
        int connectStatus = event.connectStatus;
        LogInfra.Log.i(TAG, "onEventAdjustConnectResult() connectStatus = " + connectStatus);
        adjustConnectSuc = connectStatus == EventTryConnectDeviceResult.connect_status_suc;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventOpFreshIcResult(EventOpFreshIcResult event) {
        int resultType = event.getResultType();
        boolean result = event.isResult();
        LogInfra.Log.i(TAG, "onEventOpFreshIcResult() resultType = " + resultType + " ; result = " + result);
        if (resultType == EventOpFreshIcResult.RESULT_TYPE_FRESH_OP) {
            inRefreshIc = result;
            if (result) {
                showLoading4RefreshIc();
            } else {
                hideLoading4RefreshIc();
            }
            return;
        }

        if (resultType == EventOpFreshIcResult.RESULT_TYPE_FRESH_OP_RESULT) {
            if (inRefreshIc) {
                toast(R.string.hint_fresh_done);
            }
            inRefreshIc = false;
            hideLoading4RefreshIc();
            return;
        }

        if (resultType == EventOpFreshIcResult.RESULT_TYPE_SUPPORT) {
            supportIcFresh = result;
            if (supportIcFresh) {
                DeviceM.getInstance.requestIcGuide(sku, goodsType);
            }
            opItemHintUi();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEvent4NotifyGuideUpdate(Event4NotifyGuideUpdate event) {
        LogInfra.Log.i(TAG, "onEvent4NotifyGuideUpdate()");
        opItemHintUi();
    }

    private void showLoading4RefreshIc() {
        LoadingDialog.createDialog(this, com.ihoment.base2app.R.style.DialogDim, 10 * 1000, () -> {
            inRefreshIc = false;
            toast(R.string.hint_fresh_fail);
        }).setEventKey("onRefreshIc").show();
    }

    private void hideLoading4RefreshIc() {
        LoadingDialog.hideDialog("onRefreshIc");
    }

    private void checkSupportWlanControl() {
        WlanControlView wlanControlView = findViewById(com.govee.dreamcolorlightv1.R.id.wlan_control);
        if (wlanControlView != null) {
            wlanControlView.checkSupport(new WlanControlOperator(goodsType, sku, device, topic));
        }
    }

    /**
     * ============================================matter新增============================================
     */
    @Override
    protected void skipToMatterGuide() {
        //将ble操作类作为粘性事件发送给Matter导航页面
        EventBus.getDefault().removeAllStickyEvents();
        Event4MatterBleOp.Companion.postBleOp(Ble.getInstance);
        Bundle argument = new Bundle();
        argument.putString(OldDreamColorUtil.intent_ac_adjust_sku, sku);
        argument.putString(OldDreamColorUtil.intent_ac_adjust_device, device);
        argument.putString(OldDreamColorUtil.intent_ac_adjust_wifiMac, wifiMac);
        argument.putString(OldDreamColorUtil.intent_ac_adjust_bleAddress, bleAddress);
        JumpUtil.jump(this, Activity4MatterGuide.class, argument);
    }

    private boolean hasLoadMatterInfo = false;
    private boolean isSupportMatter = false;
    private String wifiHv = "";
    private String wifiSv = "";
    private boolean isResume4User = false;

    @Override
    protected void onResume() {
        super.onResume();
        isResume4User = true;
        getMatterInfo();
    }

    @Override
    protected void onPause() {
        super.onPause();
        isResume4User = false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onBtConnectState(EventBleConnect event) {
        if (isResume4User && event.connectSuc()) {
            getMatterInfo();
        }
    }

    private void getMatterInfo() {
        if (hasLoadMatterInfo) {
            return;
        }
        SafeLog.Companion.i("xiaobing", () -> "SettingBleWifiAcV1--getMatterInfo-->...");
        if (MtPairProtocol.INSTANCE.supportMatter(goodsType, sku, versionSoft, versionHard, wifiSv, wifiHv)) {
            //修改标识位
            hasLoadMatterInfo = true;
            //获取matter相关信息
            MatterManager.Companion.instance().getMtInfo4Setting(Ble.getInstance, sku, device, wifiMac, new MatterManager.OnMtInfo4SettingListener() {

                @Override
                public void getBaseInfo(boolean isSupportMatter, boolean hasPairNet, boolean hasAddedEcology) {
                    SettingBleWifiAcV1.this.isSupportMatter = isSupportMatter;
                }

                @Override
                public void finish() {
                    if (SettingBleWifiAcV1.this.isSupportMatter) {
                        if (pllMatterContainer != null) {
                            pllMatterContainer.setVisibility(View.VISIBLE);
                        }
                        if (vMatterContainerDivider != null) {
                            vMatterContainerDivider.setVisibility(View.VISIBLE);
                        }
                    }
                }
            });
        }
    }

    /**
     * ============================================低蓝光 新增============================================
     */

    private void onClickLowBlueLight() {
        if (config != null) {
            dialog4LowBlueLight = Dialog4LowBlueLight.Companion.createDialog(this, config.getMax(), config.getMin(), sku);
            dialog4LowBlueLight.setOnOpChange((aBoolean, integer) -> {
                showLoading();
                EventLowBlueLight.Companion.sendEvent(aBoolean, integer);
                return null;
            });
            dialog4LowBlueLight.setOnOpReset(open -> {
                showLoading();
                EventLowBlueLight.Companion.sendEvent(open, config.getDefaultValue());
                return null;
            });
            dialog4LowBlueLight.setData(isBlueLightOpen, blueLightValue);
            dialog4LowBlueLight.show();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventLowBlueLightResult(EventLowBlueLightResult event) {
        SafeLog.Companion.i(TAG, () -> "onEventLowBlueLightResult open=" + event.getSwitch());
        boolean result = event.isResult();
        if (result) {
            isBlueLightOpen = event.getSwitch();
            blueLightValue = event.getBlue();
        }
        if (dialog4LowBlueLight != null && dialog4LowBlueLight.isShowing()) {
            dialog4LowBlueLight.setData(isBlueLightOpen, blueLightValue);
        }
        hideLoading();
    }

    @Override
    protected int getLayout() {
        return com.govee.dreamcolorlightv1.R.layout.dreamcolorv1_ac_wifi_ble_setting;
    }
}