package com.govee.dreamcolorlightv1.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.EventDiyTemplate4NewScenes;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/8/12
 * op diy操作应用-ble+iot$
 */
public class OpDiyCommDialog4SquareBleIotV1 extends AbsOpCommDialog4BleIotV2 {
    private final AbsMultipleControllerV14DiyTemplate diyTemplate;
    private final CmdPt cmdPt;
    private final CmdPtReal cmdPtReal;
    private final boolean isBk;

    protected OpDiyCommDialog4SquareBleIotV1(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull AbsMultipleControllerV14DiyTemplate diyTemplate, boolean isBk, int type) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_DIY, type);
        this.diyTemplate = diyTemplate;
        this.isBk = isBk;
        cmdPt = CmdPt.getDiyTemplateCmdPt(diyTemplate);
        cmdPtReal = CmdPtReal.getDiyTemplateCmdPt(diyTemplate);
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull AbsMultipleControllerV14DiyTemplate diyTemplate, boolean isBk, int type) {
        new OpDiyCommDialog4SquareBleIotV1(context, bleAddress, bleName, topic, sku, device, diyTemplate, isBk, type).show();
    }

    @Override
    protected @androidx.annotation.Nullable
    AbsCmd getOpCmd() {
        return isBk ? cmdPtReal : cmdPt;
    }

    @Override
    protected void bleOping() {
        getBle().sendMultipleControllerV1(diyTemplate);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyTemplate4NewScenes(EventDiyTemplate4NewScenes event) {
        boolean result = event.isResult();
        int scenesCode = event.scenesCode;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiy() result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            Mode mode = new Mode();
            SubModeScenes subMode = new SubModeScenes();
            subMode.setEffect(scenesCode);
            mode.subMode = subMode;
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            getBle().clearControllers();
            updateBleResult(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateBleResult(result);
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}