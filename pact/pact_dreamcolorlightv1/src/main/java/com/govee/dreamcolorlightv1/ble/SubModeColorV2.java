package com.govee.dreamcolorlightv1.ble;

import androidx.annotation.NonNull;

import com.govee.base2home.Constant;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.colortemp.base.ISubModeColorTem;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * Create by hey on 2021/2/5
 * $
 */
public class SubModeColorV2 extends AbsSubMode4Analytic implements ISubModeColorTem {
    public static final int op_type_ui_no_fresh = -1;
    public static final int op_type_gradual = 1;

    public int rgb = 0;/*默认颜色-红色*/
    public int gradual = 0;/*默认不开启渐变*/

    public static int len = 15;/*颜色模式分段长度  H61A9->18段*/
    public boolean[] ctlLight = new boolean[len];
    public int[] rgbSet;

    public int[] brightnessSet;
    public int brightness;

    public int kelvin;
    public int ctRgb;

    public int opType;

    public int group;

    /**
     * 用于真色温设备-读取模式是返回色温值
     */
    public transient int realKelvinByRead = 0;

    @Override
    public void loadLocal() {
    }

    @Override
    public void saveLocal() {
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_color_v2;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr = UtilColor.colorName(rgb);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_color, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        this.gradual = BleUtil.getUnsignedByte(validBytes[0]);
        this.realKelvinByRead = BleUtil.getSignedInt(new byte[]{validBytes[1], validBytes[2]}, true);
        this.kelvin = this.realKelvinByRead;
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] bytes = new byte[17];
        bytes[0] = subModeCommandType();
        if (this.brightness == 0 && this.brightnessSet == null) {
            bytes[1] = (byte) 0x01;
            int[] rgb = ColorUtils.getRgb(this.rgb);
            bytes[2] = (byte) rgb[0];
            bytes[3] = (byte) rgb[1];
            bytes[4] = (byte) rgb[2];
            if (this.kelvin != 0) {
                byte[] signedBytesFor2 = BleUtil.getSignedBytesFor2(kelvin, true);
                bytes[5] = signedBytesFor2[0];
                bytes[6] = signedBytesFor2[1];
                int[] ctRgb = ColorUtils.getRgb(this.ctRgb);
                bytes[7] = (byte) ctRgb[0];
                bytes[8] = (byte) ctRgb[1];
                bytes[9] = (byte) ctRgb[2];
            }
            byte[] selectPosBytes = BleUtil.makeBytes4SelectPosByOneBit(ctlLight);
            System.arraycopy(selectPosBytes, 0, bytes, 10, selectPosBytes.length);
        } else if (this.brightnessSet != null && group == 0) {
            bytes[1] = (byte) 0x03;
            for (int i = 0; i < brightnessSet.length; i++) {
                bytes[i + 2] = (byte) brightnessSet[i];
            }
        } else if (this.brightnessSet != null) {
            bytes[1] = (byte) 0x03;
            if (group == 1) {
                bytes[2] = (byte) 0x01;
                for (int i = 0; i < 14; i++) {
                    bytes[i + 3] = (byte) brightnessSet[i];
                }
            } else {
                bytes[2] = (byte) 0x02;
                int index = 3;
                for (int i = 14; i < brightnessSet.length; i++) {
                    bytes[index] = (byte) brightnessSet[i];
                    index++;
                }
            }
        } else {
            bytes[1] = (byte) 0x02;
            bytes[2] = (byte) this.brightness;
            byte[] selectPosBytes = BleUtil.makeBytes4SelectPosByOneBit(ctlLight);
            System.arraycopy(selectPosBytes, 0, bytes, 3, selectPosBytes.length);
        }
        return bytes;
    }

    public int getRealRgb() {
        if (kelvin > 0) return ctRgb;
        return rgb;
    }

    public void setRgb(int rgb) {
        this.rgb = rgb;
        this.ctRgb = 0;
        this.kelvin = 0;
    }

    public void setTemRgbAndKelvin(int temRgb, int kelvin) {
        this.rgb = 0;
        this.ctRgb = temRgb;
        this.kelvin = kelvin;
    }

    public static SubModeColorV2 makeSubModeColor(int color) {
        SubModeColorV2 subModeColor = new SubModeColorV2();
        subModeColor.rgb = color;
        subModeColor.kelvin = 0;
        subModeColor.ctRgb = 0;
        int length = subModeColor.ctlLight.length;
        int[] rgbSet = new int[length];
        for (int i = 0; i < length; i++) {
            subModeColor.ctlLight[i] = true;
            rgbSet[i] = color;
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }

    public static boolean isSetMode2Color(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opByte == BleProtocolConstants.SINGLE_MODE && modeType == BleProtocol.sub_mode_color_v2;
        }
        return false;
    }

    public static AbsSingleController[] makeSubModeColor(int[] colors, boolean isFade, boolean hadFadeController) {
        if (colors.length != len) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        int fadeSize = hadFadeController ? 1 : 0;
        AbsSingleController[] modeControllers = new AbsSingleController[hashMap.size() + fadeSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.rgb = color;
            subModeColor.kelvin = 0;
            subModeColor.ctRgb = 0;
            subModeColor.ctlLight = new boolean[len];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode mode = new Mode();
            mode.subMode = subModeColor;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            modeControllers[i] = new Gradual4BleWifiController(isFade);
        }
        return modeControllers;
    }

    public static void parsePosColor(byte[] setPosColorBytes, @NonNull SubModeColorV2 subModeColor) {
        if (setPosColorBytes == null || setPosColorBytes.length != 20) return;
        if (subModeColor.rgbSet == null) {
            subModeColor.rgbSet = new int[subModeColor.ctlLight.length];
        }
        int rgb = ColorUtils.toColor(setPosColorBytes[4], setPosColorBytes[5], setPosColorBytes[6]);
        int kelvin = BleUtil.convertTwoBytesToShort(setPosColorBytes[7], setPosColorBytes[8]);
        int temRgb = ColorUtils.toColor(setPosColorBytes[9], setPosColorBytes[10], setPosColorBytes[11]);
        int rgbValue;
        if (kelvin > 0) {
            rgbValue = temRgb;
        } else {
            rgbValue = rgb;
        }

        /*从低位bit开始描述*/
        boolean[] low8Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[12]);
        for (int i = 0; i < low8Set.length; i++) {
            if (low8Set[i]) {
                subModeColor.rgbSet[i] = rgbValue;
            }
        }
        int index = 8;
        boolean[] high5Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[13]);
        for (int i = 0; i < high5Set.length - 1; i++) {
            if (high5Set[i]) {
                subModeColor.rgbSet[index] = rgbValue;
            }
            index++;
        }
    }

    public static SubModeColorV2 parseSubModeColor4Write(byte[] validBytes) {
        if (LogInfra.openLog()) {
            LogInfra.Log.d("parseSubModeColor4Write", "validBytes = " + BleUtil.bytesToHexString(validBytes));
        }
        if (validBytes[0] == 0x01) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            int rgb = ColorUtils.toColor(validBytes[1], validBytes[2], validBytes[3]);
            int kelvin = BleUtil.convertTwoBytesToShort(validBytes[4], validBytes[5]);
            int temRgb = ColorUtils.toColor(validBytes[6], validBytes[7], validBytes[8]);
            subModeColor.rgb = rgb;
            subModeColor.ctRgb = temRgb;
            subModeColor.kelvin = kelvin;

            boolean[] ctlLight = new boolean[len];
            /*前8盏灯的选中状态*/
            boolean[] group1Value8 = BleUtil.parseBytes4Bit(validBytes[9]);
            boolean[] group2Value8 = BleUtil.parseBytes4Bit(validBytes[10]);
            System.arraycopy(group1Value8, 0, ctlLight, 0, group1Value8.length);
            if (len == 15) {
                System.arraycopy(group2Value8, 0, ctlLight, 8, ctlLight.length - group1Value8.length);
            } else if (len == 18) {
                System.arraycopy(group2Value8, 0, ctlLight, 8, group2Value8.length);
                boolean[] group3Value8 = BleUtil.parseBytes4Bit(validBytes[11]);
                System.arraycopy(group3Value8, 0, ctlLight, 16, ctlLight.length - group1Value8.length - group2Value8.length);
            }
            subModeColor.ctlLight = ctlLight;
            return subModeColor;
        } else if (validBytes[0] == 0x02) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.brightness = BleUtil.getUnsignedByte(validBytes[1]);
            boolean[] ctlLight = new boolean[len];
            /*前8盏灯的选中状态*/
            boolean[] group1Value8 = BleUtil.parseBytes4Bit(validBytes[2]);
            boolean[] group2Value8 = BleUtil.parseBytes4Bit(validBytes[3]);
            System.arraycopy(group1Value8, 0, ctlLight, 0, group1Value8.length);
            if (len == 15) {
                System.arraycopy(group2Value8, 0, ctlLight, 8, ctlLight.length - group1Value8.length);
            } else if (len == 18) {
                System.arraycopy(group2Value8, 0, ctlLight, 8, group2Value8.length);
                boolean[] group3Value8 = BleUtil.parseBytes4Bit(validBytes[4]);
                System.arraycopy(group3Value8, 0, ctlLight, 16, ctlLight.length - group1Value8.length - group2Value8.length);
            }
            subModeColor.ctlLight = ctlLight;
            return subModeColor;
        }
        return null;
    }

    public boolean isSelectAll() {
        if (ctlLight != null) {
            for (boolean b : ctlLight) {
                if (!b) return false;
            }
            return true;
        }
        return false;
    }

    public static SubModeColorV2 parseSubModeColor2New(@NonNull SubModeColor subModeColor) {
        SubModeColorV2 subModeColorV2 = new SubModeColorV2();
        subModeColorV2.ctlLight = subModeColor.ctlLight;
        subModeColorV2.rgb = subModeColor.rgb;
        subModeColorV2.rgbSet = subModeColor.rgbSet;
        return subModeColorV2;
    }

    public static AbsSingleController[] makeSubModeColor(Colors colorStrip) {
        int[] colors = colorStrip.colorSet;
        if (colors.length != len) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        boolean hasBrightness = colorStrip.brightnessSet != null && colorStrip.brightnessSet.length == len;
        AbsSingleController[] modeControllers = new AbsSingleController[hasBrightness ? hashMap.size() + 1 : hashMap.size()];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[len];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hasBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hasBrightness) {
            if (len == 18) {
                return makeSubModeColor(colorStrip, modeControllers, i);
            }
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.brightnessSet = colorStrip.brightnessSet;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i] = modeController;
        }
        return modeControllers;
    }

    private static AbsSingleController[] makeSubModeColor(Colors colorStrip, AbsSingleController[] modeControllers, int i) {
        AbsSingleController[] modeNewControllers = new AbsSingleController[modeControllers.length + 1];
        System.arraycopy(modeControllers, 0, modeNewControllers, 0, modeControllers.length);
        for (int index = 1; index <= 2; index++) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.brightnessSet = colorStrip.brightnessSet;
            subModeColor.group = index;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (index == 2) {
                mode.isLastController = true;
            }
            ModeController modeController = new ModeController(mode);
            modeNewControllers[i++] = modeController;
        }
        return modeNewControllers;
    }

    public static AbsSingleController[] makeSubModeColor(int[] colors, int[] brightnessArray, boolean isFade, boolean isTelinkBle, boolean hadFadeController) {
        if (colors == null || colors.length != len) return null;
        HashMap<Integer, List<Integer>> hashMap = new HashMap<>();
        for (int i = 0; i < colors.length; i++) {
            int color = colors[i];
            List<Integer> pos = hashMap.get(color);
            if (pos == null) {
                pos = new ArrayList<>();
            }
            pos.add(i);
            hashMap.put(color, pos);
        }
        int fadeSize = hadFadeController ? 1 : 0;
        boolean hadBrightness = brightnessArray != null && brightnessArray.length == len;
        int controllerSize = hadBrightness ? (hashMap.size() + 1 + fadeSize) : (hashMap.size() + fadeSize);
        AbsSingleController[] modeControllers = new AbsSingleController[controllerSize];
        int i = 0;
        for (Integer color : hashMap.keySet()) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.rgb = color;
            subModeColor.ctlLight = new boolean[len];
            List<Integer> poss = hashMap.get(color);
            if (poss == null || poss.size() == 0) continue;
            for (Integer pos : poss) {
                subModeColor.ctlLight[pos] = true;
            }
            subModeColor.rgbSet = colors;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            if (i == hashMap.size() - 1 && !hadBrightness) mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadBrightness) {
            SubModeColorV2 subModeColor = new SubModeColorV2();
            subModeColor.brightnessSet = brightnessArray;
            Mode4ColorStrip mode = new Mode4ColorStrip();
            mode.subMode = subModeColor;
            mode.isLastController = true;
            ModeController modeController = new ModeController(mode);
            modeControllers[i++] = modeController;
        }
        if (hadFadeController) {
            if (isTelinkBle) {
                modeControllers[i] = new GradualController(isFade);
            } else {
                modeControllers[i] = new Gradual4BleWifiController(isFade);
            }
        }
        return modeControllers;
    }

    public static boolean isSetMode2Color4ColorEffect(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opByte = original20Bytes[1];
            byte modeType = original20Bytes[2];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opByte == BleProtocolConstants.SINGLE_MODE && modeType == BleProtocol.sub_mode_color_v2;
        }
        return false;
    }

    public static void parsePosColorWithBrightness(byte[] setPosColorBytes, @NonNull SubModeColorV2 subModeColor) {
        if (setPosColorBytes == null || setPosColorBytes.length != 20) return;
        if (subModeColor.rgbSet == null) {
            subModeColor.rgbSet = new int[subModeColor.ctlLight.length];
        }
        if (setPosColorBytes[3] == 0x01) {
            int rgb = ColorUtils.toColor(setPosColorBytes[4], setPosColorBytes[5], setPosColorBytes[6]);
            int kelvin = BleUtil.convertTwoBytesToShort(setPosColorBytes[7], setPosColorBytes[8]);
            int temRgb = ColorUtils.toColor(setPosColorBytes[9], setPosColorBytes[10], setPosColorBytes[11]);
            int rgbValue;
            if (kelvin > 0) {
                rgbValue = temRgb;
            } else {
                rgbValue = rgb;
            }
            /*从低位bit开始描述*/
            boolean[] low8Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[12]);
            for (int i = 0; i < low8Set.length; i++) {
                if (low8Set[i]) {
                    subModeColor.rgbSet[i] = rgbValue;
                }
            }
            int index = 8;
            if (subModeColor.ctlLight.length == 18) {
                boolean[] rgbSet = BleUtil.parseBytes4BitReverse(setPosColorBytes[13]);
                for (boolean value : rgbSet) {
                    if (value) {
                        subModeColor.rgbSet[index] = rgbValue;
                    }
                    index++;
                }
                rgbSet = BleUtil.parseBytes4BitReverse(setPosColorBytes[14]);
                for (boolean value : rgbSet) {
                    if (value) {
                        subModeColor.rgbSet[index] = rgbValue;
                    }
                    index++;
                }
            } else {
                boolean[] high5Set = BleUtil.parseBytes4BitReverse(setPosColorBytes[13]);
                for (int i = 0; i < high5Set.length - 1; i++) {
                    if (high5Set[i]) {
                        subModeColor.rgbSet[index] = rgbValue;
                    }
                    index++;
                }
            }
        } else if (setPosColorBytes[3] == 0x03) {
            if (subModeColor.brightnessSet == null) {
                subModeColor.brightnessSet = new int[subModeColor.ctlLight.length];
            }
            if (subModeColor.ctlLight.length == 18) {
                int groupByte = BleUtil.getUnsignedByte(setPosColorBytes[4]);
                int[] brightnessArray;
                if (groupByte == 1) {
                    brightnessArray = new int[14];
                    subModeColor.brightnessSet = new int[14];
                } else {
                    brightnessArray = new int[4];
                }
                for (int i = 0; i < brightnessArray.length; i++) {
                    brightnessArray[i] = BleUtil.getUnsignedByte(setPosColorBytes[i + 5]);
                }
                if (groupByte == 1) {
                    subModeColor.brightnessSet = brightnessArray;
                } else {
                    int[] brightnessNewArray = new int[subModeColor.ctlLight.length];
                    System.arraycopy(subModeColor.brightnessSet, 0, brightnessNewArray, 0, subModeColor.brightnessSet.length);
                    System.arraycopy(brightnessArray, 0, brightnessNewArray, subModeColor.brightnessSet.length, brightnessArray.length);
                    subModeColor.brightnessSet = brightnessNewArray;
                }
                return;
            }

            int[] brightnessArray = new int[subModeColor.ctlLight.length];
            for (int i = 0; i < subModeColor.ctlLight.length; i++) {
                brightnessArray[i] = BleUtil.getUnsignedByte(setPosColorBytes[i + 4]);
            }
            subModeColor.brightnessSet = brightnessArray;
        }
    }

    public static SubModeColorV2 makeSubModeColor4Group(Colors colors) {
        SubModeColorV2 subModeColorV2 = new SubModeColorV2();
        subModeColorV2.rgbSet = colors.colorSet;
        subModeColorV2.brightnessSet = colors.brightnessSet;
        return subModeColorV2;
    }

    @NonNull
    @Override
    protected String analyticParamValue4ModeUse() {
        return ParamFixedValue.mode_use_color;
    }

    public static boolean isAllColorTem(int[] rgbSet, int len) {
        if (rgbSet == null || rgbSet.length == 0) return false;
        int index = 0;
        for (int rgb : rgbSet) {
            if (!Constant.isColorTemp(rgb)) return false;
            index++;
            if (index == len) break;
        }
        return true;
    }

    @Override
    public int getColorTemKelvin() {
        if (kelvin > 0) return kelvin;
        return getColorTemKelvinByRgbSet(rgbSet);
    }
}