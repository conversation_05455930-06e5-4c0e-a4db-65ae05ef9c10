package com.govee.dreamcolorlightv1.adjust;

import com.alibaba.android.arouter.utils.TextUtils;
import com.govee.base2home.Constant;
import com.govee.base2home.scenes.builder.CmdBuilder;
import com.govee.base2home.sku.Colors;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v2.DiyTemplate;
import com.govee.base2light.ac.diy.v2.DiyValue;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyShortcutManger;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ac.music.MusicApplyM;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultipleColorStripControllerV1;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.controller.MusicComposeController;
import com.govee.base2light.ble.controller.SubModeAbsMusic;
import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.light.v1.absmusic.AbsMusicFragment;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.rhythm.RhyRule;
import com.govee.base2light.snapshot.BaseSnapshotDataUtil;
import com.govee.base2light.snapshot.EventGeneralSnapshotCmd;
import com.govee.base2light.snapshot.SnapshotMode;
import com.govee.dreamcolorlightv1.adjust.v2.ExtV3;
import com.govee.dreamcolorlightv1.ble.Gradual4BleWifiController;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeColorV3;
import com.govee.dreamcolorlightv1.ble.SubModeColorV4;
import com.govee.dreamcolorlightv1.ble.SubModeColorV6;
import com.govee.dreamcolorlightv1.ble.SubModeMusic;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV1;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV2;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV3;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.govee.dreamcolorlightv1.iot.CmdBrightness;
import com.govee.dreamcolorlightv1.iot.CmdPtReal;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 组装UI界面当前模式的指令集合（ble + iot）
 */
public class SnapshotDataUtil extends BaseSnapshotDataUtil {
    ExtV3 ext;

    private static class Builder {
        public final static SnapshotDataUtil instance = new SnapshotDataUtil();
    }

    public static SnapshotDataUtil getInstance(ExtV3 ext) {
        Builder.instance.ext = ext;
        return Builder.instance;
    }

    private static final String TAG = "SnapshotDataM";

    public void dealData(AbsMode4UIV1 modeUI, BleIotInfo info) {
        super.dealData(modeUI, info);
        //模式没有改变过
        if (!checkEnable(modeUI)) {
            return;
        }

        //组装指令
        List<SnapshotMode.CmdMode> cmds = new ArrayList<>();

        //亮度指令
        CmdBrightness cmdBrightness = new CmdBrightness(ext.brightness);
        SnapshotMode.CmdMode cmdModeBrightness = new SnapshotMode.CmdMode();
        cmdModeBrightness.cmdType = RhyRule.op_type_snapshot_switch;
        cmdModeBrightness.bleCmds = makeBlueMsg(new CmdPtReal(new BrightnessController(ext.brightness)).getCommand());
        cmdModeBrightness.iotCmd = CmdBuilder.makeCmdStr(cmdBrightness);
        cmds.add(cmdModeBrightness);

        ISubMode iSubMode = info.mode.subMode;
        if (iSubMode instanceof SubModeMusicV3) {
            recordAddMusicSnapshot();
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            SubModeMusicV3 subModeMusic = (SubModeMusicV3) modeUI.getCurCompleteMode();
            CmdPtReal cmdPtReal;
            if (subModeMusic.isNewMusic) {
                //当前音乐模式的颜色多包
                MultipleController4Music musicController = new MultipleController4Music((byte) subModeMusic.getSensitivity(), subModeMusic.musicEffect);
                cmdPtReal = CmdPtReal.getMultiNewMusicMode(musicController);
            } else {
                Mode mode = new Mode();
                mode.subMode = iSubMode;
                ModeController controller = new ModeController(mode);
                cmdPtReal = new CmdPtReal(controller);
            }
            cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
            cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);

            cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode);
        } else if (iSubMode instanceof SubModeMusicV2) {
            recordAddMusicSnapshot();
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            SubModeMusicV2 subModeMusic = (SubModeMusicV2) modeUI.getCurCompleteMode();
            CmdPtReal cmdPtReal;
            if (subModeMusic.isNewMusic) {
                //当前音乐模式的颜色多包
                MultipleController4Music musicController = new MultipleController4Music((byte) subModeMusic.getSensitivity(), subModeMusic.musicEffect);
                cmdPtReal = CmdPtReal.getMultiNewMusicMode(musicController);
            } else {
                Mode mode = new Mode();
                mode.subMode = iSubMode;
                ModeController controller = new ModeController(mode);
                cmdPtReal = new CmdPtReal(controller);
            }
            cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
            cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);

            cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode);
        } else if (iSubMode instanceof SubModeMusic || iSubMode instanceof SubModeMusicV1) {
            recordAddMusicSnapshot();
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            CmdPtReal cmdPtReal;

            Mode mode = new Mode();
            mode.subMode = iSubMode;
            ModeController controller = new ModeController(mode);
            cmdPtReal = new CmdPtReal(controller);
            cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
            cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);

            cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode);
        } else if (iSubMode instanceof SubModeColorV6) {
            /*20段灯带*/
            recordAddColorSnapshot();
            //颜色模式需要从ColorFragment拿取对应的色条
            iSubMode = modeUI.getCurCompleteMode();
            int gradual = ((SubModeColor) iSubMode).gradual;
            int[] rgbSet = ((SubModeColor) iSubMode).rgbSet;
            int[] brightnessSet = ((SubModeColor) iSubMode).brightnessSet;
            Colors color = new Colors(rgbSet, brightnessSet, "");
            List<byte[]> bytes = SubModeColorV6.makeSnapshotBytes(color, gradual);
            CmdPtReal cmdPtReal = new CmdPtReal(bytes);
            SnapshotMode.CmdMode cmdMode2 = new SnapshotMode.CmdMode();
            cmdMode2.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
            cmdMode2.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
            cmdMode2.cmdType = RhyRule.op_type_snapshot_mode_2;
            cmds.add(cmdMode2);
        } else if (iSubMode instanceof SubModeColor
                || iSubMode instanceof SubModeColorV2
                || iSubMode instanceof SubModeColorV3
                || iSubMode instanceof SubModeColorV4) {
            recordAddColorSnapshot();
            //颜色模式需要从ColorFragment拿取对应的色条
            iSubMode = modeUI.getCurCompleteMode();
            int[] rgbSet;
            int[] brightnessSet;
            int gradual;
            Colors color;
            AbsSingleController[] modeControllers;
            int subModeColorVersion = Support.supportPartBrightness(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, "");
            gradual = ((SubModeColor) iSubMode).gradual;
            rgbSet = ((SubModeColor) iSubMode).rgbSet;
            brightnessSet = ((SubModeColor) iSubMode).brightnessSet;
            color = new Colors(rgbSet, brightnessSet, "");

            if (subModeColorVersion == 0) {
                modeControllers = SubModeColor.makeSubModeColor(color);
            } else {
                if (Support.isColorTemRealDevice(info.goodsType)) {
                    boolean allColorTem = SubModeColorV2.isAllColorTem(rgbSet, 15);
                    if (allColorTem) {
                        SubModeColorV2 subModeColorV2 = new SubModeColorV2();
                        subModeColorV2.ctlLight = new boolean[15];
                        Arrays.fill(subModeColorV2.ctlLight, true);
                        subModeColorV2.kelvin = Constant.getColorTemKelvin(rgbSet[0]);
                        subModeColorV2.ctRgb = rgbSet[0];
                        Mode mode = new Mode();
                        mode.subMode = subModeColorV2;
                        ModeController modeController = new ModeController(mode);
                        modeControllers = new AbsSingleController[]{modeController};
                    } else {
                        modeControllers = SubModeColorV4.makeSubModeColorV2(color);
                    }
                } else {
                    if (Support.isGoodsTypeH61A9(info.goodsType)) {
                        modeControllers = SubModeColorV2.makeSubModeColor(color);
                    } else {
                        modeControllers = SubModeColorV4.makeSubModeColorV2(color);
                    }
                }
            }
            if (gradual != -1) {
                Gradual4BleWifiController gradualController = new Gradual4BleWifiController(gradual == 1);
                SnapshotMode.CmdMode cmdMode3 = new SnapshotMode.CmdMode();
                CmdPtReal cmdPtRealGradual = new CmdPtReal(gradualController);
                cmdMode3.bleCmds = makeBlueMsg(cmdPtRealGradual.getCommand());
                cmdMode3.iotCmd = CmdBuilder.makeCmdStr(cmdPtRealGradual);
                cmdMode3.cmdType = RhyRule.op_type_snapshot_mode_1;
                cmds.add(cmdMode3);
            }
            if (modeControllers == null) {
                SafeLog.e(TAG, "modeControllers is null ; please check colorMode transform controller method!");
                return;
            }
            boolean supportColorStripMulti = Support.supportColorStripMulti(info.goodsType, info.pactType, info.pactCode);
            LogInfra.Log.i(TAG, "onEventColorStrip() supportColorStripMulti = " + supportColorStripMulti);
            if (supportColorStripMulti) {
                MultipleColorStripControllerV1 stripControllerV1 = new MultipleColorStripControllerV1(color);
                SubModeColorV2 subModeColorV2 = new SubModeColorV2();
                Mode mode = new Mode();
                mode.subMode = subModeColorV2;
                ModeController modeController = new ModeController(mode);
                CmdPtReal cmdPtReal = CmdPtReal.makeColorStripCmdPtRealV2(stripControllerV1, modeController);
                SnapshotMode.CmdMode cmdMode2 = new SnapshotMode.CmdMode();
                cmdMode2.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
                cmdMode2.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
                cmdMode2.cmdType = RhyRule.op_type_snapshot_mode_2;
                cmds.add(cmdMode2);
            } else {
                //色条与亮度
                SnapshotMode.CmdMode cmdMode2 = new SnapshotMode.CmdMode();
                List<byte[]> bytes = new ArrayList<>();
                for (AbsSingleController absSingleController : modeControllers) {
                    bytes.add(absSingleController.getValue());
                }
                CmdPtReal cmdPtReal = new CmdPtReal(bytes);
                cmdMode2.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
                cmdMode2.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
                cmdMode2.cmdType = RhyRule.op_type_snapshot_mode_2;
                cmds.add(cmdMode2);
            }

        } else if (iSubMode instanceof SubModeScenes) {
            recordAddSceneSnapshot();
            Mode mode = new Mode();
            mode.subMode = iSubMode;
            ModeController controller = new ModeController(mode);
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            CmdPtReal cmdPtReal = new CmdPtReal(controller);
            AbsMultipleControllerV14Scenes newScenesMode = Support.is2NewScenesMode(info.sku, info.device, mode, 1);
            if (newScenesMode != null) {
                cmdPtReal = CmdPtReal.getNewScenesCmdPtReal(newScenesMode);
            }
            cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
            cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
            cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
            cmds.add(cmdMode);

        } else if (iSubMode instanceof SubModeNewDiy) {
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            CmdPtReal cmdPtReal = getDiyCmdPtReal((SubModeNewDiy) iSubMode, info);
            if (cmdPtReal != null) {
                cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
                cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
                cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
                cmds.add(cmdMode);
            } else {
                LogInfra.Log.e(TAG, " 无法获取快照diy参数-解析失败");
            }
        } else if (iSubMode instanceof SubModeAbsMusic && !TextUtils.isEmpty(AbsMusicFragment.Companion.getCurMusicStr())) {
            SnapshotMode.CmdMode cmdMode = new SnapshotMode.CmdMode();
            int musicCode = ((SubModeAbsMusic) iSubMode).getMusicCode();
            MusicComposeController controller = MusicApplyM.INSTANCE.createAbsMusicController(AbsMusicFragment.Companion.getCurMusicStr(), musicCode);
            if (controller != null) {
                CmdPtReal cmdPtReal = new CmdPtReal(controller.getAllBytes4Command());
                cmdMode.bleCmds = makeBlueMsg(cmdPtReal.getCommand());
                cmdMode.iotCmd = CmdBuilder.makeCmdStr(cmdPtReal);
                cmdMode.cmdType = RhyRule.op_type_snapshot_mode_1;
                cmds.add(cmdMode);
            }
        }
        EventGeneralSnapshotCmd.sendEvent(cmds, ext.wifiSoftVersion, ext.wifiHardVersion);
    }

    private CmdPtReal getDiyCmdPtReal(SubModeNewDiy modeNewDiy, BleIotInfo info) {

        String diyValueKey = modeNewDiy.getDiyValueKey();
        boolean supportGraffitiInSeries = Support.supportGraffitiInSeries(info.goodsType);
        DiySupportV1 diySupport = Diy.getDiySupport(supportGraffitiInSeries ? 3 : 2);
        DiyValue diyValue = DiyShortcutManger.queryLastApplyDiy(info.sku, info.goodsType, diySupport, info.ic, diyValueKey, false);

        DiyGraffitiV2 highColor = getHighColor(info, modeNewDiy.getDiyCode());
        if (highColor != null) {
            return CmdPtReal.getDiyCmdPt4DiyGraffitiV2(info.goodsType, highColor);
        }
        recordAddDiySnapshot();
        boolean aiEffect = diyValue.isAiDiy();
        if (aiEffect) {
            DiyAi diyAi = diyValue.toDiyAi();
            if (diyAi != null) {
                return CmdPtReal.getPtRealCmd(diyAi.command4PtReal.getCommands4IotPtReal());
            }
        }
        boolean studioDiy = diyValue.isStudioDiyEffect();
        if (studioDiy) {
            DiyStudio diyStudio = diyValue.toDiyStudio4Apply();
            if (diyStudio != null && diyStudio.isValid()) {
                boolean supportRgbicV1 = Support.isSupportRgbicV1(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, info.wifiSoftVersion);
                AbsMultipleControllerV14DiyTemplate controllerV14DiyTemplate = diyStudio.toMulti4Scenes4RgbicV1(supportRgbicV1 ? 1 : 0);
                return CmdPtReal.getDiyTemplateCmdPt(controllerV14DiyTemplate);
            }
        }


        DiyProtocol diyProtocol = diyValue.toDiyProtocol4Apply();
        if (diyProtocol != null) {
            return CmdPtReal.getDiyCmdPtV1(diyProtocol);
        }
        DiyGraffitiV2 diyGraffitiV2 = diyValue.toDiyGraffiti4Apply4Rgbic();
        if (diyGraffitiV2 != null) {
            return CmdPtReal.getDiyCmdPt4DiyGraffitiV2(info.goodsType, diyGraffitiV2);
        }
        DiyTemplate diyTemplate = diyValue.toDiyTemplate();
        if (diyTemplate != null) {
            AbsMultipleControllerV14DiyTemplate diyTemplateController = ScenesOp.parseDiyTemplateV2(diyTemplate.scenesCode, diyTemplate.diyCode, diyTemplate.effectStr);
            if (null == diyTemplateController) {
                diyTemplateController = ScenesOp.parseDiyTemplate(diyTemplate.scenesCode, diyTemplate.diyCode, diyTemplate.effectStr);
            }
            return CmdPtReal.getDiyTemplateCmdPt(diyTemplateController);
        }
        return null;
    }


}