package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsParamsSubMode;
import com.govee.base2light.ble.controller.ISubMode;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2021/5/12
 * 音乐模式的参数支持subMode$
 */
public class ParamsSubMode4Music extends AbsParamsSubMode {
    private static final String TAG = "ParamsSubMode4Music";

    @Override
    public ISubMode toSupportSubMode(int version) {
        if (version == 1) {
            SubModeMusicV2 subModeMusicV2 = new SubModeMusicV2();
            subModeMusicV2.parse(validBytes);
            return subModeMusicV2;
        }
        SubModeMusic subModeMusic = new SubModeMusic();
        subModeMusic.parse(validBytes);
        return subModeMusic;
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_music;
    }
}