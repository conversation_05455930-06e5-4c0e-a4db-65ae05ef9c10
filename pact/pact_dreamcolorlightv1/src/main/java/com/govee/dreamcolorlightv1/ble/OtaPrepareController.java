package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsOnlyWriteSingleController;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 12/1/20
 * 准备ota-$
 */
public class OtaPrepareController extends AbsOnlyWriteSingleController {
    @Override
    protected boolean writeResult(boolean writeSuc) {
        EventOtaPrepare.sendWriteResult(writeSuc, getCommandType(), getProType());
        return true;
    }

    @Override
    protected byte[] translateWrite() {
        return null;
    }

    @Override
    protected void fail() {
        EventOtaPrepare.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.SINGLE_OTA_PREPARE;
    }
}