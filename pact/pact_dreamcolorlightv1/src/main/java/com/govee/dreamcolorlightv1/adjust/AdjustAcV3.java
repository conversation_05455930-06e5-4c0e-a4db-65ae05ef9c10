package com.govee.dreamcolorlightv1.adjust;

import android.content.Intent;
import android.os.Bundle;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.sku.SkuIcM;
import com.govee.base2light.aieffect.ui.AILightUI;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.scenes.ScenesM;
import com.govee.base2light.pact.AbsPactAdjustAc4Function;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.IFrame;
import com.govee.base2light.pact.IFrameResult;
import com.govee.dreamcolorlightv1.ConsV1;
import com.govee.dreamcolorlightv1.adjust.v2.FrameV3;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JsonUtil;
import com.liveeventbus.LiveEventBus;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2022/5/12
 * 针对rgbic支持主盛宴的高端设备-详情页
 * BleBroadcastProcessorV5使用了该详情页，路径较长，无法梳理出是否还在使用，暂不删除
 */
public class AdjustAcV3 extends AbsPactAdjustAc4Function {
    @NonNull
    @Override
    protected IFrame makeFrame(IFrameResult iFrameResult, BleIotInfo bleIotInfo) {
        return new FrameV3(iFrameResult, bleIotInfo);
    }

    @NonNull
    @Override
    protected BleIotInfo makeInfoFromIntent(Intent intent) {
        String sku = intent.getStringExtra(ConsV1.intent_ac_adjust_sku);
        String device = intent.getStringExtra(ConsV1.intent_ac_adjust_device);
        String spec = intent.getStringExtra(ConsV1.intent_ac_adjust_spec);
        String deviceName = intent.getStringExtra(ConsV1.intent_ac_adjust_deviceName);
        int goodsType = intent.getIntExtra(ConsV1.intent_ac_adjust_goodsType, GoodsType.GOODES_TYPE_NO_SUPPORT);
        String bleAddress = intent.getStringExtra(ConsV1.intent_ac_adjust_bleAddress);
        String bleName = intent.getStringExtra(ConsV1.intent_ac_adjust_bleName);
        String wifiMac = intent.getStringExtra(ConsV1.intent_ac_adjust_wifiMac);
        String topic = intent.getStringExtra(ConsV1.intent_ac_adjust_topic);
        String versionHard = intent.getStringExtra(ConsV1.intent_ac_adjust_versionHard);
        String versionSoft = intent.getStringExtra(ConsV1.intent_ac_adjust_versionSoft);
        String versionHard4Wifi = intent.getStringExtra(ConsV1.intent_ac_adjust_versionHard_4_wifi);
        String versionSoft4Wifi = intent.getStringExtra(ConsV1.intent_ac_adjust_versionSoft_4_wifi);
        int ic = intent.getIntExtra(ConsV1.intent_ac_adjust_ic, 0);
        if (ic == 0) {
            ic = SkuIcM.getInstance().getDefIcNum(sku);
        }
        if (Support.supportGraffitiInSeries(goodsType)) {
            ic = ic * 2;
            LogInfra.Log.i(TAG, "makeInfoFromIntent() supportGraffitiInSeries --> ic = " + ic);
        }
        int pactType = intent.getIntExtra(ConsV1.intent_ac_adjust_pactType, 0);
        int pactCode = intent.getIntExtra(ConsV1.intent_ac_adjust_pactCode, 0);
        BleIotInfo bleIotInfo = new BleIotInfo(sku, goodsType, device, spec, deviceName, bleName, bleAddress, wifiMac, topic, versionHard);
        bleIotInfo.pactType = pactType;
        bleIotInfo.pactCode = pactCode;
        bleIotInfo.versionSoft = versionSoft;
        bleIotInfo.ic = ic;
        bleIotInfo.wifiHardVersion = versionHard4Wifi;
        bleIotInfo.wifiSoftVersion = versionSoft4Wifi;
        return bleIotInfo;
    }

    private boolean isInAIForeground = true;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        LiveEventBus.get(AILightUI.AI_DIALOG_STATUS).observe(this, o -> {
            if (o instanceof Boolean) {
                isInAIForeground = (boolean) o;
                if (isInAIForeground && !inFail) {
                    showSnapshotFloatBar(info.open && info.ic > 0);
                }
            }
        });

    }

    @Override
    protected void calculateHeight() {
        super.calculateHeight();
        if (Support.supportAiLight(info.goodsType, info.sku)) {
            /*405原本marginTop + AI灯效高度（90）*/
            connectUI.setCloseImgTopMargin(495);
        }
    }

    @Override
    protected int[] getDefHeaderRes() {
        return Support.getDefHeaderRes4BleWifi(info.goodsType, info.sku);
    }

    @Override
    protected boolean needCheckDefHeaderRes() {
        return Support.needCheckHeaderRes4BleWifi(info.sku);
    }

    @Override
    protected String getTag() {
        return "AdjustAcV3-DreamColorLightV1";
    }

    @Override
    protected boolean supportDeviceLock() {
        return Support.supportDeviceLock(info.goodsType, info.sku, info.pactType, info.pactCode);
    }

    @Override
    protected int getScenesEffectVersion() {
        return ScenesM.version_scenes_effect_v1;
    }

    @Override
    protected int[] supportScenesOp() {
        if (info == null) return null;
        return Support.getSupportScenesOpSet(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard);
    }

    @Override
    protected void afterInit() {
        boolean support = Support.supportRecommendedGraphic(info.sku);
        if (support) {
            boolean recommendGraphicStatus = RecommendGraphicManager.getInstance().getRecommendGraphicStatus(info.sku, info.bleAddress);
            if (!recommendGraphicStatus) {
                RecommendGraphicManager.getInstance().showDialog(this, info.sku, info.bleAddress, true);
            }
        }
    }

    @Override
    public boolean supportJump2FeastAc() {
        return true;
    }

    @NonNull
    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    /**
     * 用于释放ble标识
     *
     * @return
     */
    @NonNull
    @Override
    public String[] getBleOpTag() {
        return new String[]{
                "BleOpV3"
        };
    }

    @Override
    protected void inNormal() {
        super.inNormal();
        inFail = false;
        if (isInAIForeground) showSnapshotFloatBar(info.open);
    }

    @Override
    protected boolean isShowVersion() {
        return false;
    }

    @Override
    protected boolean needShowVersionFlag() {
        return true;
    }

    @Override
    public boolean supportFeast() {
        return true;
    }

    @Override
    protected boolean supportInAcDestoryAnalyticMode() {
        return false;
    }

    @Override
    public void initViewModel() {
        BleIotInfo musicInfo = (BleIotInfo) JsonUtil.deepCopy(info);
        if (musicInfo != null && Support.supportGraffitiInSeries(musicInfo.goodsType)) {
            musicInfo.ic /= 2;
            getMusicViewModel().doInit(musicInfo);
        } else {
            super.initViewModel();
        }
    }
}