package com.govee.dreamcolorlightv1.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultipleDiy;
import com.govee.base2light.ble.controller.MultipleDiyController;
import com.govee.base2light.ble.controller.MultipleDiyControllerV1;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2020/8/12
 * op diy操作应用-ble+iot$
 */
public class OpDiyCommDialog4SquareBleIot extends AbsOpCommDialog4BleIotV2 {
    private final DiyProtocol diyProtocol;
    private final CmdPt cmdPt;
    private final CmdPtReal cmdPtReal;
    private final boolean isBk;

    protected OpDiyCommDialog4SquareBleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull DiyProtocol diyProtocol, boolean isBk, int type) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_DIY, type);
        this.diyProtocol = diyProtocol;
        this.isBk = isBk;
        cmdPt = CmdPt.getDiyCmdPt(diyProtocol);
        cmdPtReal = CmdPtReal.getDiyCmdPtV1(diyProtocol);
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull DiyProtocol diyProtocol, boolean isBk, int type) {
        new OpDiyCommDialog4SquareBleIot(context, bleAddress, bleName, topic, sku, device, diyProtocol, isBk, type).show();
    }

    @Nullable
    @Override
    protected AbsCmd getOpCmd() {
        return isBk ? cmdPtReal : cmdPt;
    }

    @Override
    protected void bleOping() {
        if (isBk) {
            MultipleDiyControllerV1 multipleDiyControllerV1 = new MultipleDiyControllerV1(diyProtocol);
            getBle().sendMultipleControllerV1(multipleDiyControllerV1);
        } else {
            MultipleDiyController multipleDiyController = new MultipleDiyController(diyProtocol);
            getBle().sendMultipleController(multipleDiyController);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleDiy(EventMultipleDiy event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiy() result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(event.getDiyCode());
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            getBle().clearControllers();
            updateBleResult(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateBleResult(result);
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}