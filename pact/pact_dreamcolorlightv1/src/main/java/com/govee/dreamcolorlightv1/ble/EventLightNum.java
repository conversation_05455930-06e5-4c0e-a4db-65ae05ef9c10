package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by lins<PERSON>ong on 2019-09-10
 * 灯串数量的通知event
 */
public class EventLightNum extends AbsControllerEvent {
    private int num;

    private EventLightNum(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    public static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventLightNum(false, write, commandType, proType));
    }

    public static void sendSuc(boolean write, byte commandType, byte proType, int num) {
        EventLightNum eventBulbNum = new EventLightNum(true, write, commandType, proType);
        eventBulbNum.num = num;
        EventBus.getDefault().post(eventBulbNum);
    }

    public int getNum() {
        return num;
    }
}