package com.govee.dreamcolorlightv1.pact.bleiot;

import com.govee.dreamcolorlightv1.pact.Support;

/**
 * Create by <PERSON><PERSON>ying<PERSON> on 2020/8/13
 * $
 */
public class DiyOp4BleIot extends AbsDiyOp4BleIot {
    @Override
    protected int[] supportGoodsType() {
        return Support.effect4OpBleWifiGoodsTypes;
    }

    private DiyOp4BleIot() {
    }

    private static class Builder {
        private static final DiyOp4BleIot instance = new DiyOp4BleIot();
    }

    public static DiyOp4BleIot op = Builder.instance;
}