package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by hey on 2021/2/5
 * $
 */
public class EventBulbStringColorV3 extends AbsC<PERSON>rollerEvent {
    public BulbGroupColorV3 groupColor;

    protected EventBulbStringColorV3(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    public static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventBulbStringColorV3(false, write, commandType, proType));
    }

    public static void sendSuc(boolean write, byte commandType, byte proType, BulbGroupColorV3 groupColor) {
        EventBulbStringColorV3 event = new EventBulbStringColorV3(true, write, commandType, proType);
        event.groupColor = groupColor;
        EventBus.getDefault().post(event);
    }
}