package com.govee.dreamcolorlightv1.iot;

import com.govee.base2home.util.Encode;
import com.ihoment.base2app.infra.LogInfra;

import java.util.List;

import androidx.annotation.Keep;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-08-02
 * cmd=pt的解析处理
 */
@Keep
public class ResultPt {
    private static final String TAG = "ResultPt";
    String opcode;
    List<String> sleepValue;
    List<String> wakeupValue;
    List<String> modeValue;
    List<String> timerValue;
    List<String> gradualValue;

    public String getOpcode() {
        return opcode;
    }

    public List<String> getSleepValue() {
        return sleepValue;
    }

    public List<String> getWakeupValue() {
        return wakeupValue;
    }

    public List<String> getModeValue() {
        return modeValue;
    }

    public List<String> getTimerValue() {
        return timerValue;
    }

    /**
     * 透传bytes数据包;第一包都是定义的单包控制指令
     *
     * @return
     */
    public byte[] getOpBytes() {
        List<String> value = null;
        int index = 0;
        if (CmdPt.pt_op_mode.equals(opcode)) {
            value = modeValue;
            if (value != null) {
                /*模式包在最后一包*/
                int size = value.size();
                if (size >= 1) {
                    index = size - 1;
                }
            }
        } else if (CmdPt.pt_op_sleep.equals(opcode)) {
            value = sleepValue;
        } else if (CmdPt.pt_op_wakeup.equals(opcode)) {
            value = wakeupValue;
        } else if (CmdPt.pt_op_timer.equals(opcode)) {
            value = timerValue;
        } else if (CmdPt.pt_op_gradual.equals(opcode)) {
            value = gradualValue;
        }
        if (value == null || value.isEmpty()) return null;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getOpBytes() index = " + index);
        }
        return Encode.decryByBase64(value.get(index));
    }

    /**
     * 获取颜色模式下的球泡串色值
     *
     * @return
     */
    public int[] getOpMode4SubModeColorEffectRgbSet() {
        if (CmdPt.pt_op_mode.equals(opcode)) {
            return CmdStatusV0.parseColorModeRgbSet(modeValue);
        }
        return null;
    }
}