package com.govee.dreamcolorlightv1.ble;


import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BleProtocolConstants;

/**
 * Create by lins<PERSON><PERSON> on 2019-09-10
 * 开启渐变
 */
public class Gradual4BleWifiController extends AbsSingleController {
    private int gradual;

    public Gradual4BleWifiController(boolean gradualOpen) {
        super(true);
        this.gradual = gradualOpen ? 1 : 0;
    }

    public Gradual4BleWifiController() {
        super(false);
    }

    @Override
    protected boolean writeResult(boolean writeSuc) {
        EventGradual.sendSuc(isWrite(), getCommandType(), getProType(), gradual);
        return true;
    }

    @Override
    protected byte[] translateWrite() {
        return new byte[]{(byte) gradual};
    }

    @Override
    protected void fail() {
        EventGradual.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.SINGLE_GRADUAL_CHANGE_4_WIFI_BLE;
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        int gradual = validBytes[0];
        EventGradual.sendSuc(isWrite(), getCommandType(), getProType(), gradual);
        return true;
    }

    /**
     * 解析渐变值
     *
     * @param original20Bytes
     * @return -1表明解析错误
     */
    public static int parseGradual(byte[] original20Bytes) {
        if (original20Bytes == null || original20Bytes.length != 20) return -1;
        byte[] validBytes = BleUtil.parseValidBleBytes(original20Bytes);
        return validBytes[0];
    }

    public static boolean isWriteGradualController(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opType = original20Bytes[1];
            return opTypeByte == BleProtocolConstants.SINGLE_WRITE && opType == BleProtocol.SINGLE_GRADUAL_CHANGE_4_WIFI_BLE;
        }
        return false;
    }

    public static boolean isReadGradualController(byte[] original20Bytes) {
        if (original20Bytes != null && original20Bytes.length == 20) {
            byte opTypeByte = original20Bytes[0];
            byte opType = original20Bytes[1];
            return opTypeByte == BleProtocolConstants.SINGLE_READ && opType == BleProtocol.SINGLE_GRADUAL_CHANGE_4_WIFI_BLE;
        }
        return false;
    }
}