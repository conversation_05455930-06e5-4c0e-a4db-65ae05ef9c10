package com.govee.dreamcolorlightv1.adjust.ui;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2light.ble.controller.AbsSubMode4Analytic;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.IMicModeV2;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.AppUtil;

/**
 * Create by DengFei on 2021/3/1
 * dj+动感+柔和+灵敏度 mic 模式
 */
public class MicFragment extends AbsMicFragmentV4 {
    private static final String TAG = "MicFragment";

    @Override
    protected void init() {
        super.init();
        updateUi();
    }

    private void updateUi() {
        if (isViewInflateOk()) {
            boolean auto = isAutoColor();
            int rgb = getRgb();
            updateAutoSwitch(auto);

            if (auto) {
                showColorArea(false);
            } else {
                showColorArea(true);
                setColor(rgb);
            }
            setMicColor(auto ? IMicModeV2.invalid_rgb_color : rgb);

            int sensitivity = getSensitivity();
            updateSensitivity(sensitivity);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "updateUi()----isAutoColor:" + auto + ",rgb:" + rgb + "----sensitivity:" + sensitivity);
            }
        }
    }

    @Override
    protected void sendMicMode() {
        super.sendMicMode();
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.mic_mode, ParamFixedValue.times);
    }

    @Override
    public int marginTop() {
        return AppUtil.getScreenWidth() * 37 / 750;
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        if (subMode instanceof AbsSubMode4Analytic) {
            boolean checkAnalytic4SubModeUse = ((AbsSubMode4Analytic) subMode).checkAnalytic4SubModeUse(getSku());
            if (checkAnalytic4SubModeUse) {
                AnalyticsRecorder.getInstance().recordUseCount(getSku(), ParamFixedValue.mode_use_music_mic);
            }
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_music;
    }
}