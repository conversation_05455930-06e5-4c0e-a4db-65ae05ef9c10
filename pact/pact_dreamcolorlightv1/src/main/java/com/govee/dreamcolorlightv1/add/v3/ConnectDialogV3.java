package com.govee.dreamcolorlightv1.add.v3;

import android.bluetooth.BluetoothDevice;
import android.content.Context;

import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.AbsConnectDialog4Secret;
import com.govee.dreamcolorlightv1.add.AddInfo;
import com.govee.dreamcolorlightv1.ble.Ble;

import androidx.annotation.NonNull;

/**
 * Create by xieying<PERSON> on 2021/6/10
 * $
 */
public class ConnectDialogV3 extends AbsConnectDialog4Secret {
    private final AddInfo addInfo;

    protected ConnectDialogV3(Context context, BluetoothDevice device, @NonNull AddInfo addInfo) {
        super(context, device, addInfo.sku);
        this.addInfo = addInfo;
    }

    public static void showDialog(Context context, @NonNull AddInfo addInfo, BluetoothDevice bluetoothDevice) {
        new ConnectDialogV3(context, bluetoothDevice, addInfo).show();
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected void jumpToPairAc() {
        PairAc.jump2PairAc(context, addInfo, device);
    }
}