package com.govee.dreamcolorlightv1.adjust.v2;

import com.govee.base2home.pact.Protocol;
import com.govee.base2light.pact.ble.IPactResult4Ble;
import com.govee.dreamcolorlightv1.pact.Support;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2022/5/12
 * ble协议判断-v3
 */
class BlePactV3 extends BlePactV2 {
    BlePactV3(IPactResult4Ble iPactResult) {
        super(iPactResult);
    }

    @Override
    protected String getTag() {
        return "BlePactV3";
    }

    @Override
    protected boolean isSupportProtocol(Protocol protocol) {
        if (protocol == null) return false;
        for (Protocol pro : Support.supportProtocolsV8) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        for (Protocol pro : Support.supportProtocolsV8_1) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        for (Protocol pro : Support.supportProtocolsV8_2) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        for (Protocol pro : Support.supportProtocolsV8_2_1) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        for (Protocol pro : Support.supportProtocolsV8_3) {
            if (pro.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        return false;
    }
}