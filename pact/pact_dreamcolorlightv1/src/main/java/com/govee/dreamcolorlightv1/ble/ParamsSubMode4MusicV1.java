package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsParamsSubMode;
import com.govee.base2light.ble.controller.ISubMode;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2021/5/12
 * 音乐模式的参数支持subMode$
 */
public class ParamsSubMode4MusicV1 extends AbsParamsSubMode {
    private static final String TAG = "ParamsSubMode4Music";

    @Override
    public ISubMode toSupportSubMode(int version) {
        if (version == 1) {
            SubModeMusicV3 subModeMusicV3 = new SubModeMusicV3();
            subModeMusicV3.parse(validBytes);
            return subModeMusicV3;
        }
        SubModeMusicV1 subModeMusic = new SubModeMusicV1();
        subModeMusic.parse(validBytes);
        return subModeMusic;
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_new_music;
    }
}