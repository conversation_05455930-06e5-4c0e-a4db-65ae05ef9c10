package com.govee.dreamcolorlightv1.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsMultipleControllerV1;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventMultiNewDiyGraffiti;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/8/12
 * op diy操作应用-ble+iot$
 */
public class OpDiyCommDialog4SquareBleIotV2 extends AbsOpCommDialog4BleIotV2 {
    private final AbsCmd absCmd;
    private final AbsMultipleControllerV1 controllerV1;

    protected OpDiyCommDialog4SquareBleIotV2(int goodsType, Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull DiyGraffitiV2 diyGraffiti, int type) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_DIY, type);
        controllerV1 = Support.makeGraffitiController(goodsType, diyGraffiti);
        absCmd = CmdPtReal.getDiyCmdPt4DiyGraffitiV2(goodsType, diyGraffiti);
    }

    public static void showDialog(int goodsType, Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull DiyGraffitiV2 diyGraffiti, int type) {
        new OpDiyCommDialog4SquareBleIotV2(goodsType, context, bleAddress, bleName, topic, sku, device, diyGraffiti, type).show();
    }

    @Override
    protected @androidx.annotation.Nullable
    AbsCmd getOpCmd() {
        return absCmd;
    }

    @Override
    protected void bleOping() {
        if (controllerV1 != null) {
            getBle().sendMultipleControllerV1(controllerV1);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultipleDiyGraffiti(EventMultiNewDiyGraffiti event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultipleDiyGraffiti() result = " + result);
        }
        if (result) {
            getBle().onMultipleControllerOk(event);
            Mode mode = new Mode();
            mode.subMode = new SubModeNewDiy(event.getDiyCode());
            ModeController modeController = new ModeController(mode);
            getBle().startController(modeController);
        } else {
            getBle().clearControllers();
            updateBleResult(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateBleResult(result);
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}