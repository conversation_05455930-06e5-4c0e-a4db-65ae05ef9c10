package com.govee.dreamcolorlightv1.add.v2;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;

import com.alibaba.android.arouter.facade.annotation.Route;
import com.govee.base2home.config.Base2homeConfig;
import com.govee.base2home.constant.PathBaseHome;
import com.govee.base2home.main.tab.EventTabDefault;
import com.govee.base2home.pact.support.OldDreamColorUtil;
import com.govee.base2home.sku.SkuIcM;
import com.govee.base2home.util.SchemeUtils;
import com.govee.base2light.ac.AbsBleWifiChooseActivity;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.kt.comm.DefInfo;
import com.govee.base2light.neonlight.recommend.ConstJumpKey;
import com.govee.base2light.pact.newdetail.ShareVM;
import com.govee.base2light.pact.newdetail.Vm4NewDetailAcShare;
import com.govee.ble.BleController;
import com.govee.dreamcolorlightv1.ConsV1;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.util.JumpUtil;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/3/6
 * wifi选择界面$
 */
@Route(path = PathBaseHome.URL_DREAM_COLOR_LIGHT_V1_WIFI)
public class WifiChooseAc extends AbsBleWifiChooseActivity {

    private boolean bleDisconnectCloseAc;
    private boolean supportBack;
    private int goodsType;
    private String secretCode;
    private int ic;

    /**
     * 跳转到wifi选择页面-添加流程
     *
     * @param ac
     * @param addInfo
     */
    public static void jump2wifiChooseAcByAdd(Activity ac, @NonNull AddInfoV2 addInfo) {
        int[] wifiInputLimit = Support.getWifiInputLimit();
        String adjustAcClassName = "";
        if (!TextUtils.isEmpty(addInfo.adjustAcClassName)) {
            adjustAcClassName = addInfo.adjustAcClassName;
        }
        Bundle jumpBundle = getJumpBundle(addInfo.sku, addInfo.device, addInfo.bleName, addInfo.bleAddress, addInfo.deviceName, adjustAcClassName, wifiInputLimit[0], wifiInputLimit[1], addInfo.versionHard, addInfo.versionSoft);
        if (jumpBundle == null) return;
        jumpBundle.putBoolean(ConstJumpKey.intent_ac_key_ble_disconnect_close_ac, true);
        jumpBundle.putBoolean(ConstJumpKey.intent_ac_key_support_back, false);
        jumpBundle.putBoolean(ConstJumpKey.intent_ac_key_support_skip, true);
        jumpBundle.putInt(ConstJumpKey.intent_ac_adjust_goodsType, addInfo.goodsType);
        jumpBundle.putInt(intent_ac_pacttype, addInfo.pactType);
        jumpBundle.putInt(intent_ac_pactcode, addInfo.pactCode);
        jumpBundle.putString(ConstJumpKey.intent_ac_adjust_secret, addInfo.secretCode);
        jumpBundle.putString(ConstJumpKey.intent_ac_adjust_versionSoft_4_wifi, addInfo.wifiSoftVersion);
        jumpBundle.putString(ConstJumpKey.intent_ac_adjust_versionHard_4_wifi, addInfo.wifiHardVersion);
        jumpBundle.putString(OldDreamColorUtil.intent_ac_adjust_wifiMac, addInfo.wifiMac);
        if (addInfo.icNum > 0) {
            jumpBundle.putInt(ConstJumpKey.intent_ac_adjust_ic, addInfo.icNum);
        }
        JumpUtil.jumpWithBundle(ac, WifiChooseAc.class, true, jumpBundle);
    }

    /**
     * 跳转到wifi选择界面-修改wifi
     *
     * @param goodsType
     * @param context
     * @param sku
     * @param device
     * @param bleName
     * @param bleAddress
     */
    public static void jump2wifiChooseAcByChangeWifi(Context context, int goodsType, String sku, String device, String deviceName, String bleName, String bleAddress, String versionHard) {
        int[] wifiInputLimit = Support.getWifiInputLimit();
        Bundle jumpBundle = getJumpBundle(sku, device, bleName, bleAddress, deviceName, "", wifiInputLimit[0], wifiInputLimit[1], versionHard, "");
        if (jumpBundle == null) return;
        jumpBundle.putBoolean(ConstJumpKey.intent_ac_key_ble_disconnect_close_ac, false);
        jumpBundle.putBoolean(ConstJumpKey.intent_ac_key_support_back, true);
        jumpBundle.putBoolean(ConstJumpKey.intent_ac_key_support_skip, false);
        jumpBundle.putInt(ConsV1.intent_ac_adjust_goodsType, goodsType);
        JumpUtil.jump(context, WifiChooseAc.class, jumpBundle);
    }

    /**
     * 跳转到wifi选择界面-修改wifi
     * 备注：添加参数,supportMatter
     *
     * @param goodsType
     * @param context
     * @param sku
     * @param device
     * @param bleName
     * @param bleAddress
     */
    public static void jump2wifiChooseAcByChangeWifi(Context context, int goodsType, String sku, String device, String deviceName, String bleName, String bleAddress, String bleHv, String bleSv, String wifiHv, String wifiSv, String wifiMac) {
        int[] wifiInputLimit = Support.getWifiInputLimit();
        Bundle jumpBundle = getJumpBundle(sku, device, bleName, bleAddress, deviceName, "", wifiInputLimit[0], wifiInputLimit[1], bleHv, bleSv);
        if (jumpBundle == null) return;
        jumpBundle.putBoolean(ConstJumpKey.intent_ac_key_ble_disconnect_close_ac, false);
        jumpBundle.putBoolean(ConstJumpKey.intent_ac_key_support_back, true);
        jumpBundle.putBoolean(ConstJumpKey.intent_ac_key_support_skip, false);
        jumpBundle.putInt(ConsV1.intent_ac_adjust_goodsType, goodsType);
        jumpBundle.putString(ConsV1.intent_ac_adjust_versionHard_4_wifi, wifiHv);
        jumpBundle.putString(ConsV1.intent_ac_adjust_versionSoft_4_wifi, wifiSv);
        jumpBundle.putString(OldDreamColorUtil.intent_ac_adjust_wifiMac, wifiMac);
        JumpUtil.jump(context, WifiChooseAc.class, jumpBundle);
    }

    @Override
    protected void doCheckPermissionPre() {
        super.doCheckPermissionPre();
        Intent intent = getIntent();
        goodsType = intent.getIntExtra(ConsV1.intent_ac_adjust_goodsType, 0);
        bleDisconnectCloseAc = intent.getBooleanExtra(ConstJumpKey.intent_ac_key_ble_disconnect_close_ac, false);
        supportBack = intent.getBooleanExtra(ConstJumpKey.intent_ac_key_support_back, false);
        secretCode = intent.getStringExtra(ConsV1.intent_ac_adjust_secret);
        ic = intent.getIntExtra(ConsV1.intent_ac_adjust_ic, 0);
        boolean supportSkip = intent.getBooleanExtra(ConstJumpKey.intent_ac_key_support_skip, false);
        updateBackAndSkip(supportBack, supportSkip);
    }

    private void toJumpAdjustAc() {
        if (BaseApplication.getBaseApplication().isInBackground()) return;/*若应用处于后台，则不进行界面跳转*/
        /*关闭蓝牙*/
        getBle().stopHeart();
        BleController.getInstance().toBtClose();
        /*通知Tab更改为Default*/
        EventTabDefault.sendEventTabDefault();
        /*跳转到详情页*/
        Class<?> mainAcClass = Base2homeConfig.getConfig().getMainAcClass();
        Bundle bundle = new Bundle();
        DefInfo defInfo = new DefInfo();
        defInfo.setGoodsType(goodsType);
        defInfo.setSku(sku);
        defInfo.setDevice(device);
        defInfo.setSpec("");
        defInfo.setDeviceName(deviceName);
        defInfo.setBleAddress(bleAddress);
        defInfo.setBleName(bleName);
        defInfo.setVersionSoft(versionSoft);
        defInfo.setVersionHard(versionHard);
        defInfo.setPactType(pactType);
        defInfo.setPactCode(pactCode);
        defInfo.setIc(ic > 0 ? ic : SkuIcM.getInstance().getDefIcNum(sku));
        if (secretCode != null) {
            defInfo.setSecretCode(secretCode);
        }
        if (wifiMac != null) {
            defInfo.setWifiMac(wifiMac);
        }
        if (wifiSv != null) {
            defInfo.setWifiSoftVersion(wifiSv);
        }
        if (wifiHv != null) {
            defInfo.setWifiHardVersion(wifiHv);
        }
        BaseApplication.getBaseApplication().finishOther(mainAcClass);//解决三星手机视频播放页重启bug
        bundle.putParcelable("intent_ac_key_info", defInfo);
        SchemeUtils.jump(this, PathBaseHome.URL_RGBIC_NEW_DETAIL, bundle);
    }

    @Override
    protected boolean bleDisconnectCloseAc() {
        return bleDisconnectCloseAc;
    }

    @Override
    protected void toCloseAc() {
        if (changeWifiRemindDialog != null) {
            changeWifiRemindDialog.hide();
        }
        if (supportBack) {
            finish();
        } else {
            /*跳转到adjustAc*/
            toJumpAdjustAc();
        }
    }

    @Override
    protected int runModeVersion() {
        return Support.check2WifiDeviceRunModeVersion();
    }

    @Override
    protected AbsBle getBle() {
        Vm4NewDetailAcShare shareVm = Vm4NewDetailAcShare.Companion.createShareVm4NewDetail(sku, device, this, false);
        ShareVM opVm = shareVm.getOpVm();
        if (opVm != null) {
            return opVm.queryBleOp();
        }
        return Ble.getInstance;
    }

    @Override
    protected boolean isCanBack() {
        return supportBack;
    }

    @Override
    protected void toSkip() {
        toCloseAc();
    }

    @Override
    protected boolean supportDynamicApi() {
        return Support.supportDynamicApi(goodsType, sku);
    }
}