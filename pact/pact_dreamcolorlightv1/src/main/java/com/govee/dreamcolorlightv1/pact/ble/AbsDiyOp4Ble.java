package com.govee.dreamcolorlightv1.pact.ble;

import android.content.Context;

import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.pact.Command4PtReal;
import com.govee.base2home.pact.PtRealCommands;
import com.govee.base2home.pact.support.OldDreamColorUtil;
import com.govee.base2home.pact.support.OldRgbicBkUtil;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2home.sku.Colors;
import com.govee.base2home.sku.SkuIcM;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.DiyShare;
import com.govee.base2light.ac.diy.IColorOp;
import com.govee.base2light.ac.diy.IDiyOp;
import com.govee.base2light.ac.diy.IRoomOp4Ble;
import com.govee.base2light.ac.diy.IRoomOp4Iot;
import com.govee.base2light.ac.diy.ISceneOp;
import com.govee.base2light.ac.diy.ISmartRoomOp;
import com.govee.base2light.ac.diy.ISwitchAndBrightnessOp;
import com.govee.base2light.ac.diy.local.ShareDiy;
import com.govee.base2light.ac.diy.v1.EffectCodes;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.HeartController;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.dreamcolorlightv1.adjust.Diy;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.ble.BleComm;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.OpColorCommDialog4Ble;
import com.govee.dreamcolorlightv1.ble.OpColorCommDialog4SquareBle;
import com.govee.dreamcolorlightv1.ble.OpDiyCommDialog4Ble;
import com.govee.dreamcolorlightv1.ble.OpDiyCommDialog4BleV1;
import com.govee.dreamcolorlightv1.ble.OpDiyCommDialog4BleV2;
import com.govee.dreamcolorlightv1.ble.OpDiyCommDialog4SquareBle;
import com.govee.dreamcolorlightv1.ble.OpDiyCommDialog4SquareBleV1;
import com.govee.dreamcolorlightv1.ble.OpDiyCommDialog4SquareBleV2;
import com.govee.dreamcolorlightv1.ble.OpSceneCommDialog4Ble;
import com.govee.dreamcolorlightv1.ble.OpSceneCommDialog4SquareBle;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.pact.Comm;
import com.govee.dreamcolorlightv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JsonUtil;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/8/12
 * diy的op操作$
 */
abstract class AbsDiyOp4Ble implements IDiyOp, IColorOp, ISceneOp, ISmartRoomOp, ISwitchAndBrightnessOp {
    private static final String TAG = "AbsDiyOp4Ble";

    @Override
    public String getKey() {
        StringBuilder sb = new StringBuilder();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            sb.append(goodsType).append("_");
        }
        return sb.toString();
    }

    /**
     * 支持的goodsType
     *
     * @return
     */
    protected abstract int[] supportGoodsType();

    @Override
    public boolean support(@NonNull AbsDevice absDevice, int effectType) {
        /*旧款已配置了goodsType的rgbic升级的bk*/
        boolean rgbicBk = OldRgbicBkUtil.isRgbicBk4Ble(absDevice.getSku(), absDevice.getPactType(), absDevice.getPactCode());
        if (rgbicBk) return true;
        /*旧幻彩升级的sku-ble款*/
        boolean bleRgbic4OldSku = OldDreamColorUtil.checkSupportNewDreamColorFuc(absDevice.getSku(), absDevice.getVersionSoft());
        if (bleRgbic4OldSku) return true;
        int curGoodsType = absDevice.getGoodsType();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) return true;
        }
        return false;
    }

    @Override
    public boolean supportDiyEffect(@NonNull AbsDevice absDevice, int[] effectCodes) {
        int diyVersion = Support.getDiyVersion(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "supportDiyEffect() diyVersion = " + diyVersion + " ; effectCodes = " + Arrays.toString(effectCodes));
        }
        EffectCodes effectCodesCur = Diy.getDiySupport(diyVersion).effectCodes;
        if (effectCodesCur != null) {
            return effectCodesCur.supportDiyEffect(effectCodes);
        }
        return false;
    }

    @Override
    public boolean applyDiyEffect(Context context, @NonNull AbsDevice absDevice, @NonNull DiyShare diyShare, boolean needConnect) {
        return applyDiyEffect(context, absDevice, diyShare.effectStr, diyShare.effectCodes, diyShare.type, needConnect);
    }

    private boolean applyDiyEffect(Context context, @NonNull AbsDevice absDevice, String effectStr, int[] effectCodes, int type, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        DiyProtocol diyProtocol = ShareDiy.parseDiyProtocol(effectStr);
        if (diyProtocol != null) {

            boolean bkProtocol = Support.isBkProtocol(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode());
            if (needConnect) {
                OpDiyCommDialog4Ble.showDialog(context, ext.address, ext.bleName, diyProtocol, bkProtocol, type);
            } else {
                OpDiyCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, diyProtocol, bkProtocol, type);
            }
            return true;
        }
        DiyGraffitiV2 diyGraffiti = ShareDiy.parseDiyGraffiti4Rgbic(effectStr);
        if (diyGraffiti != null) {
            int ic = ext.ic;
            if (ic <= 0) {
                ic = SkuIcM.getInstance().getDefIcNum(absDevice.getSku());
            }
            boolean support = diyGraffiti.checkDiyValue4RgbicGraffiti(ic);
            if (!support) return false;
            if (needConnect) {
                OpDiyCommDialog4BleV2.showDialog(context, ext.address, ext.bleName, diyGraffiti, type);
            } else {
                OpDiyCommDialog4SquareBleV2.showDialog(context, ext.address, ext.bleName, diyGraffiti, type);
            }
            return true;
        }
        AbsMultipleControllerV14DiyTemplate shareDiyTemplate = ScenesOp.isShareDiyTemplate(effectCodes, effectStr);
        if (shareDiyTemplate != null) {
            if (needConnect) {
                OpDiyCommDialog4BleV1.showDialog(context, ext.address, ext.bleName, shareDiyTemplate, type);
            } else {
                OpDiyCommDialog4SquareBleV1.showDialog(context, ext.address, ext.bleName, shareDiyTemplate, type);
            }
            return true;
        }
        /*来自Govee Studio*/
        int diyVersion = Support.getDiyVersion(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyDiyEffect() diyVersion = " + diyVersion);
        }
        int rgbicVersion = diyVersion >= 2 ? 1 : 0;
        AbsMultipleControllerV14DiyTemplate shareDiyStudio = ScenesOp.isShareDiyStudio4Rgbic(rgbicVersion, effectCodes, effectStr);
        if (shareDiyStudio != null) {
            if (needConnect) {
                OpDiyCommDialog4BleV1.showDialog(context, ext.address, ext.bleName, shareDiyStudio, type);
            } else {
                OpDiyCommDialog4SquareBleV1.showDialog(context, ext.address, ext.bleName, shareDiyStudio, type);
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean supportColorEffect(@NonNull AbsDevice absDevice, @NonNull EffectData.ColorEffect colorEffect) {
        int size = colorEffect.colorSet.length;
        return size == 1 || size == 15;
    }

    @Override
    public boolean applyColorEffect(Context context, @NonNull AbsDevice absDevice, @NonNull EffectData.ColorEffect colorEffect, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        boolean bkProtocol = Support.isBkProtocol(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode());
        boolean supportSubModeColor4PartBrightness = Support.supportPartBrightness4Ble(absDevice.getVersionSoft(), absDevice.getVersionHard());
        boolean telinkBle = Support.isTelinkBle(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode());
        if (needConnect) {
            OpColorCommDialog4Ble.showDialog(context, ext.address, ext.bleName, colorEffect, bkProtocol, supportSubModeColor4PartBrightness, telinkBle);
        } else {
            OpColorCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, colorEffect, bkProtocol, supportSubModeColor4PartBrightness, telinkBle);
        }
        return true;
    }

    @Override
    public boolean supportSceneEffect(@NonNull AbsDevice absDevice, @NonNull EffectData.ShareEffect shareEffect) {
        int parseVersion = shareEffect.parseVersion;
        if (parseVersion == EffectData.ShareEffect.parser_version_diy) {
            return supportDiyEffect(absDevice, shareEffect.effectCodes);
        }
        if (!shareEffect.isValidEffect()) return false;
        int version = Support.getDiyVersion(absDevice.getSku(), absDevice.getGoodsType(),
                absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
        if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgb || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v0) {
            return version >= 1;
        } else if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v1) {
            return version >= 2;
        }
        return false;
    }

    @Override
    public boolean applySceneEffect(Context context, @NonNull AbsDevice absDevice, @NonNull EffectData.ShareEffect shareEffect, boolean needConnect) {
        int parseVersion = shareEffect.parseVersion;
        if (parseVersion == EffectData.ShareEffect.parser_version_diy) {
            return applyDiyEffect(context, absDevice, shareEffect.effectStr, shareEffect.effectCodes, DiyShare.type_recommend, needConnect);
        }
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        if (parseVersion == EffectData.ShareEffect.parser_version_scenes_rgb
                || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v0
                || parseVersion == EffectData.ShareEffect.parser_version_scenes_rgbic_v1) {
            if (needConnect) {
                OpSceneCommDialog4Ble.showDialog(context, ext.address, ext.bleName, shareEffect);
            } else {
                OpSceneCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, shareEffect);
            }
            return true;
        }
        return false;
    }

    @Override
    public boolean applySwitch(Context context, @NonNull AbsDevice absDevice, boolean on) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        OpSceneCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, on);
        return true;
    }

    @Override
    public boolean applySwitch(Context context, @NonNull AbsDevice absDevice, boolean on, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpSceneCommDialog4Ble.showDialog(context, ext.address, ext.bleName, on);
        } else {
            OpSceneCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, on);
        }
        return true;
    }

    @Override
    public boolean applyBrightness(Context context, @NonNull AbsDevice absDevice, int brightness4Percent) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        OpSceneCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, absDevice, brightness4Percent);
        return true;
    }

    @Override
    public boolean applyBrightness(Context context, @NonNull AbsDevice absDevice, int brightness4Percent, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpSceneCommDialog4Ble.showDialog(context, ext.address, ext.bleName, absDevice, brightness4Percent);
        } else {
            OpSceneCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, absDevice, brightness4Percent);
        }
        return true;
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    public EffectCodes getSupportDiyEffectCode(@NonNull AbsDevice absDevice) {
        int diyVersion = Support.getDiyVersion(absDevice.getSku(), absDevice.getGoodsType(), absDevice.getPactType(), absDevice.getPactCode(),
                absDevice.getVersionSoft(), absDevice.getVersionHard());
        return Diy.getDiySupport(diyVersion).effectCodes;
    }

    @Override
    public int supportRandomColorSize(AbsDevice absDevice) {
        if (Support.isSubModeColorPiece10(absDevice.getGoodsType())) return 10;
        return 15;
    }

    @Override
    public int[] supportScenesOpSet(AbsDevice absDevice) {
        int goodsType = absDevice.getGoodsType();
        if (goodsType == 0) {
            goodsType = OldDreamColorUtil.parseOldDreamDeviceNewGoodsType(absDevice.getSku());
        }
        goodsType = Math.max(0, goodsType);
        return Support.getSupportScenesOpSet(absDevice.getSku(), goodsType, absDevice.getPactType(), absDevice.getPactCode(), absDevice.getVersionSoft(), absDevice.getVersionHard());
    }

    @Override
    public boolean applyPtControllers(Context context, @NonNull AbsDevice absDevice, int type, @NonNull Command4PtReal ptReal, boolean needConnect) {
        boolean invalid = ptReal.isInvalid();
        if (invalid) return false;
        boolean supportBleOp = ptReal.supportBleOp();
        List<PtRealCommands> commands = ptReal.opCommands;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "applyPtControllers() supportBleOp = " + supportBleOp);
        }
        if (!supportBleOp) return false;
        PtRealController ptRealController = PtRealController.makePtRealController(commands, -1, -1);
        if (ptRealController == null) {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "applyPtControllers() 透传指令解析失败");
            }
            return false;
        }
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext == null) return false;
        if (needConnect) {
            OpDiyCommDialog4Ble.showDialog(context, ext.address, ext.bleName, type, ptRealController);
        } else {
            OpDiyCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, type, ptRealController);
        }
        return true;
    }

    /*smartRoom*/

    @Override
    public boolean supportBle() {
        return true;
    }

    @Override
    public boolean supportWifi() {
        return false;
    }

    @Override
    public boolean supportDevice(DeviceModel deviceModel) {
        String sku = deviceModel.getSku();
        int pactType = deviceModel.pactType;
        int pactCode = deviceModel.pactCode;
        String versionSoft = deviceModel.versionSoft;
        int curGoodsType = deviceModel.getGoodsType();
        /*旧款已配置了goodsType的rgbic升级的bk*/
        boolean rgbicBk = OldRgbicBkUtil.isRgbicBk4Ble(sku, pactType, pactCode);
        if (rgbicBk) return true;
        /*旧幻彩升级的sku-ble款*/
        boolean bleRgbic4OldSku = OldDreamColorUtil.checkSupportNewDreamColorFuc(sku, versionSoft);
        if (bleRgbic4OldSku) return true;
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) return true;
        }
        return false;
    }

    @Override
    public int supportColorSize(DeviceModel model) {
        if (Support.isSubModeColorPiece10(model.getGoodsType())) return 10;
        return 15;
    }

    @Override
    public IRoomOp4Ble getRoomOp() {
        return new IRoomOp4Ble() {
            @Override
            public byte[] heartBytes() {
                return new HeartController().getValue();
            }

            @Override
            public UUID getServiceUUID() {
                return BleComm.serviceUuid;
            }

            @Override
            public UUID getCharacteristicUUID() {
                return BleComm.characteristicUuid;
            }

            @Override
            public byte[] makeSwitchOpBytes(DeviceModel deviceModel, boolean on) {
                return new SwitchController(on).getValue();
            }

            @Override
            public byte[] makeBrightnessOpBytes(DeviceModel deviceModel, int brightnessPercent) {
                return Comm.makeBrightnessController4BleComm(deviceModel, brightnessPercent).getValue();
            }

            @Override
            public List<byte[]> makeSetColorOpBytes(DeviceModel deviceModel, int[] colors) {
                List<byte[]> setColorBytes = new ArrayList<>();
                Colors colors1 = new Colors();
                colors1.colorSet = colors;
                boolean bkProtocol = Support.isBkProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                AbsSingleController[] controllers;
                if (bkProtocol) {
                    controllers = SubModeColorV2.makeSubModeColor(colors1);
                } else {
                    controllers = SubModeColor.makeSubModeColor(colors1);
                }
                if (controllers != null) {
                    for (AbsSingleController controller : controllers) {
                        byte[] value = controller.getValue();
                        setColorBytes.add(value);
                    }
                }
                return setColorBytes;
            }

            @Override
            public List<byte[]> makeSetColorTemOpBytes(DeviceModel deviceModel, int[] kelvin, int[] temColor) {
                return makeSetColorOpBytes(deviceModel, temColor);
            }

            @Override
            public byte[] makeSetColorOpBytes(DeviceModel deviceModel, int color) {
                return Comm.makeColorController4BleComm(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, color).getValue();
            }

            @Override
            public byte[] makeSetColorTemOpBytes(DeviceModel deviceModel, int kelvin, int temColor) {
                return Comm.makeColorTemController4BleComm(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode, temColor).getValue();
            }

            @Override
            public int supportMic(DeviceModel deviceModel) {
                AbsMicFragmentV4.SupportMicStatus micStatus = Support.getMicStatus(deviceModel.getSku(), deviceModel.versionSoft, deviceModel.getGoodsType());
                if (AbsMicFragmentV4.SupportMicStatus.support_new_order.equals(micStatus))
                    return support_mic_new;
                if (AbsMicFragmentV4.SupportMicStatus.support_color_order.equals(micStatus))
                    return support_mic_rgb;
                return support_mic_no;
            }

            @Override
            public byte[] makeMicBytesByRgb(DeviceModel deviceModel, int rgb) {
                Mode mode = new Mode();
                boolean bkProtocol = Support.isBkProtocol(deviceModel.getSku(), deviceModel.getGoodsType(), deviceModel.pactType, deviceModel.pactCode);
                if (bkProtocol) {
                    mode.subMode = SubModeColorV2.makeSubModeColor(rgb);
                } else {
                    mode.subMode = SubModeColor.makeSubModeColor(rgb);
                }
                return new ModeController(mode).getValue();
            }
        };
    }

    @Override
    public IRoomOp4Iot getRoomOp4Iot() {
        return null;
    }

    /*smartRoom*/
}