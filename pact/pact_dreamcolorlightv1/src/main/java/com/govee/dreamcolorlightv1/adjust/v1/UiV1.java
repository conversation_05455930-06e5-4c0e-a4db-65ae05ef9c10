package com.govee.dreamcolorlightv1.adjust.v1;

import com.govee.ui.R;
import android.app.Activity;
import android.bluetooth.BluetoothDevice;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.view.View;
import android.widget.RelativeLayout;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.custom.LoadingDialog;
import com.govee.base2home.event.EventBleStatus;
import com.govee.base2home.guide.GuideDialog;
import com.govee.base2home.pact.Command4PtReal;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2home.util.Encode;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.Constant;
import com.govee.base2light.ac.adjust.EventSleepUpdate;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.TimerInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ac.club.EventColorStrip;
import com.govee.base2light.ac.diy.DiyGraffitiV2;
import com.govee.base2light.ac.diy.DiyProtocol;
import com.govee.base2light.ac.diy.EventDiyApply;
import com.govee.base2light.ac.diy.EventDiyApplyResult;
import com.govee.base2light.ac.diy.EventDiyApplyV2;
import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ac.diy.v1.EventDiyEffectOp;
import com.govee.base2light.ac.diy.v2.DiyOpM;
import com.govee.base2light.ac.diy.v2.DiyTemplate;
import com.govee.base2light.ac.diy.v2.DiyValue;
import com.govee.base2light.ac.diy.v2.LastDiyConfig;
import com.govee.base2light.ac.diy.v3.AcDiyGroup;
import com.govee.base2light.ac.diy.v3.DiyAi;
import com.govee.base2light.ac.diy.v3.DiyGroup;
import com.govee.base2light.ac.diy.v3.DiyShortcutManger;
import com.govee.base2light.ac.diy.v3.DiyStudio;
import com.govee.base2light.ac.diy.v3.Event2AcDiyGroup;
import com.govee.base2light.ac.diy.v3.EventDiyApply4InModeShowing;
import com.govee.base2light.ac.diy.v3.EventDiyModeShowingChange;
import com.govee.base2light.ac.diy.v3.Util4Diy;
import com.govee.base2light.ac.effect.EffectAc;
import com.govee.base2light.ac.effect.EffectEvent;
import com.govee.base2light.ac.effect.EventEffectSquareOpResult;
import com.govee.base2light.ac.effect.EventScenesEffect;
import com.govee.base2light.ac.timer.NewShowTimerAcV2;
import com.govee.base2light.ac.timer.NewSleepSetEvent;
import com.govee.base2light.ac.timer.NewTimerSetEvent;
import com.govee.base2light.ac.timer.NewWakeupSetEvent;
import com.govee.base2light.ac.timer.SleepFailEvent;
import com.govee.base2light.ac.timer.TimerFailEvent;
import com.govee.base2light.ac.timer.UpdateTimeEvent;
import com.govee.base2light.ac.timer.WakeupFailEvent;
import com.govee.base2light.ac.update.OtaUpdateAcV1;
import com.govee.base2light.ble.ScenesOp;
import com.govee.base2light.ble.ScenesRgb;
import com.govee.base2light.ble.ScenesRgbIC;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.AbsMultipleControllerV1;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14DiyTemplate;
import com.govee.base2light.ble.controller.AbsMultipleControllerV14Scenes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.AutoTimeController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.MultiDiyGraffitiController;
import com.govee.base2light.ble.controller.MultipleController4Music;
import com.govee.base2light.ble.controller.MultipleDiyController;
import com.govee.base2light.ble.controller.PtRealController;
import com.govee.base2light.ble.controller.SleepController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.controller.SyncTimeController;
import com.govee.base2light.ble.controller.SyncTimeInfo;
import com.govee.base2light.ble.controller.WakeUpController;
import com.govee.base2light.ble.mic.controller.EventSwitchMicPickUpType;
import com.govee.base2light.ble.mic.controller.MicSetRgbController;
import com.govee.base2light.ble.music.AbsNewMusicEffect;
import com.govee.base2light.ble.music.EventSetMultiMusicEffect;
import com.govee.base2light.ble.scenes.CategoryV1;
import com.govee.base2light.ble.scenes.EditScenesAc;
import com.govee.base2light.ble.scenes.EventChangeScenes;
import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.light.EventServiceScenesFresh;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.light.v1.RealtimeColorChangeEvent;
import com.govee.base2light.pact.BleInfo;
import com.govee.base2light.pact.EventSceneCheck4BleV1;
import com.govee.base2light.pact.IUiResult4Ble;
import com.govee.base2light.pact.ble.IBleOpResult;
import com.govee.base2light.ui.mode.ChangeModeEvent;
import com.govee.base2light.util.Util4ColorRealtime;
import com.govee.base2light.util.UtilFlag;

import com.govee.dreamcolorlightv1.adjust.Diy;
import com.govee.dreamcolorlightv1.ble.BleProtocol;
import com.govee.dreamcolorlightv1.ble.EventChangeGradual;
import com.govee.dreamcolorlightv1.ble.EventChangeLimit;
import com.govee.dreamcolorlightv1.ble.GradualController;
import com.govee.dreamcolorlightv1.ble.LimitController;
import com.govee.dreamcolorlightv1.ble.Mode;
import com.govee.dreamcolorlightv1.ble.ModeController;
import com.govee.dreamcolorlightv1.ble.SubModeColor;
import com.govee.dreamcolorlightv1.ble.SubModeColorV2;
import com.govee.dreamcolorlightv1.ble.SubModeMusic;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV1;
import com.govee.dreamcolorlightv1.ble.SubModeMusicV2;
import com.govee.dreamcolorlightv1.ble.SubModeNewDiy;
import com.govee.dreamcolorlightv1.ble.SubModeScenes;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.home.account.config.AccountConfig;
import com.govee.ui.component.BrightnessUI;
import com.govee.ui.component.EffectUI;
import com.govee.ui.component.NewTimerUI;
import com.govee.ui.dialog.HintDialog1;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.CaughtRunnable;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.ResUtil;
import com.ihoment.base2app.util.ToastUtil;
import com.zhy.android.percent.support.PercentRelativeLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2020/3/17
 * ui-v1版本$
 */
class UiV1 implements IUi4ExtInfo {
    private static final String TAG = "UiV1";

    private static final int ui_type_def = -1;
    private static final int ui_type_normal = 1;
    private static final int ui_type_fail = 2;

    private final IUiResult4Ble uiResult;
    private final BleInfo info;
    private final ExtV1 ext = new ExtV1();
    private final BleOpV1 bleOp;

    private Activity ac;
    private boolean layoutSuc;/*布局是否已初始化*/
    private boolean destroy;
    private int curUiType = ui_type_def;

    private NewTimerUI timerUI;
    private BrightnessUI brightnessUI;
    private EffectUI effectUI;
    private AbsMode4UIV1 modeUI;
    private final List<DiyGroup> curDiyGroups = new ArrayList<>();
    private final Handler handler = new Handler(Looper.getMainLooper());

    private boolean isSwitchMicPickUpType;

    private final IBleOpResult opResult = new IBleOpResult() {
        @Override
        public void onOffChange() {
            showLoading();
        }

        @Override
        public void infoOver() {
            curUiType = ui_type_normal;
            hideLoading();
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_suc);
            checkUi();
        }

        @Override
        public void noConnect() {
            curUiType = ui_type_fail;
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_fail);
            checkUi();
        }

        @Override
        public void bleWrite(byte proCommandType, boolean result) {
            hideLoading();
            if (result && proCommandType == BleProtocol.SINGLE_GRADUAL_CHANGE) {
                /*若当前就是颜色模式，则提示当前仅仅设置渐变逻辑*/
                subModeColor2Gradual();
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(result ? EventEffectSquareOpResult.result_op_suc : EventEffectSquareOpResult.result_op_fail);
            checkUi();
        }
    };

    private void subModeColor2Gradual() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeColorV2) {
            ((SubModeColorV2) subMode).opType = SubModeColorV2.op_type_gradual;
        }
        if (subMode instanceof SubModeColor) {
            ((SubModeColor) subMode).opType = SubModeColor.op_type_gradual;
        }
    }

    public UiV1(IUiResult4Ble uiResult, BleInfo info) {
        this.uiResult = uiResult;
        this.info = info;
        bleOp = new BleOpV1(info, ext);
    }

    private void registerEvent(boolean register) {
        if (register) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this);
            }
        } else {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        }
    }

    @Override
    public boolean isSupportProtocol(int pactType, int pactCode) {
        List<Protocol> supportProtocolsV1 = Support.supportProtocolsV1;
        for (Protocol protocol : supportProtocolsV1) {
            if (protocol.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    @Override
    public void destroy() {
        if (!destroy) {
            destroy = true;
            hideLoading();
            registerEvent(false);
            bleOp.destroy();
            ac = null;
            if (timerUI != null) timerUI.onDestroy();
            if (brightnessUI != null) brightnessUI.onDestroy();
            if (modeUI != null) modeUI.onDestroy();
            if (effectUI != null) effectUI.onDestroy();
            hideDiyGuide();
        }
    }

    @Override
    public void onOffChange() {
        if (bleOp.isOpCommEnable()) {
            showLoading();
            int delay = 0;
            if (modeUI.isMicMode()) {
                delay = 100;
            }
            handler.postDelayed(new CaughtRunnable() {
                @Override
                protected void runSafe() {
                    SwitchController switchController = new SwitchController(!info.open);
                    bleOp.executeOp(switchController);
                }
            }, delay);
        }
    }

    @Override
    public void toUpdateAc(@NonNull AppCompatActivity ac) {
        if (info.canUpdate() && bleOp.isOpCommEnable()) {
            int defSkuRes = ThemeM.getDefSkuRes(info.sku, info.spec);
            OtaUpdateAcV1.jump2OtaUpdateAcV1(ac, info.sku, info.deviceName, info.versionSoft, defSkuRes, info.checkVersion);
        }
    }

    @Override
    public void layout(@NonNull AppCompatActivity ac, @NonNull PercentRelativeLayout contentParent, int headerId) {
        /*注册事件*/
        registerEvent(true);
        checkMicMode();
        this.ac = ac;
        if (!layoutSuc) {
            layoutSuc = true;
            /*初始化ui组件*/
            int[] ids = new int[]{100, 101, 102, 103, 104, 105, 106};
            effectUI = new EffectUI(ac);
            View fucViewEffect = effectUI.getFucView();
            fucViewEffect.setId(ids[4]);
            addViewMargin(contentParent, fucViewEffect, headerId, effectUI.getWidth(), effectUI.getHeight(), AppUtil.getScreenWidth() * 16 / 750);
            /*添加布局-定时*/
            timerUI = new NewTimerUI(ac);
            View fucViewTimer = timerUI.getFucView();
            fucViewTimer.setId(ids[0]);
            addViewMargin(contentParent, fucViewTimer, fucViewEffect.getId(), timerUI.getWidth(), timerUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
            /*添加布局-亮度*/
            int[] brightnessRange = Support.getBrightnessRange(info.sku, info.versionHard);
            brightnessUI = new BrightnessUI(ac, brightnessRange[1], brightnessRange[0], true);
            View fucViewBrightness = brightnessUI.getFucView();
            fucViewBrightness.setId(ids[1]);
            addViewMargin(contentParent, fucViewBrightness, fucViewTimer.getId(), brightnessUI.getWidth(), brightnessUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
            /*添加布局-mode*/
            modeUI = new ModeUiV1(ac, info.sku, info.device, info.goodsType, info.ic, getDiySupport());
            View fucViewMode = modeUI.getFucView();
            fucViewMode.setId(ids[3]);
            addViewMargin(contentParent, fucViewMode, fucViewBrightness.getId(), modeUI.getWidth(), modeUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
        }
        bleOp.setOpResult(opResult);
        /*开始进行op通信*/
        startOpComm();
        /*通知刷新DIY模式*/
        EventDiyModeShowingChange.sendEventDiyModeShowingChange();
    }

    private void startOpComm() {
        /*ble通信处理器*/
        boolean opCommEnableBle = bleOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "startOpComm() opCommEnableBle = " + opCommEnableBle);
        }
        /*若当前不可直接通信，则尝试进行ble通信*/
        if (!opCommEnableBle) {
            curUiType = ui_type_def;
            BluetoothDevice bluetoothDevice = null;
            if (uiResult != null) bluetoothDevice = uiResult.getCurBluetoothDevice();
            bleOp.beOpComm(bluetoothDevice);
        }
        checkUi();
    }

    @Override
    public void uiLost() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "uiLost() layoutSuc = " + layoutSuc + " ; curUiType = " + curUiType);
        }
        if (layoutSuc && (curUiType == ui_type_normal)) {
            curUiType = ui_type_fail;
            checkUi(false);
        }
        hideDiyGuide();
    }

    private void checkMicMode() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i("MicFragment", "checkMicMode sku device：" + info.sku + info.device);
        }
        String versionSoft = info.versionSoft;
        Support.isSupportMicByPhone(info.sku, info.device, versionSoft, info.goodsType);
    }

    private void checkUi() {
        checkUi(true);
    }

    private void checkUi(boolean reportResult) {
        if (destroy) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkUi() curUiType = " + curUiType);
        }
        if (curUiType == ui_type_fail) {
            /*通信失败*/
            timerUI.hide();
            effectUI.hide();
            brightnessUI.hide();
            modeUI.hide();
            /*断开连接-重置通信Op*/
            bleOp.destroy();
            hideLoading();
            hideDiyGuide();
            if (uiResult != null && reportResult) uiResult.uiResult(IUiResult4Ble.ui_result_fail);
        } else if (curUiType == ui_type_normal) {
            /*通信正常*/
            hideLoading();
            boolean open = info.open;
            timerUI.show();
            effectUI.show();
            if (open) {
                brightnessUI.show();
                brightnessUI.updateBrightness(checkBrightnessEnable(), ext.brightness);
                if (isSwitchMicPickUpType) {
                    AbsMicFragmentV4.saveMicModeByPhone(info.sku, info.device);
                }
                checkMusicMode();
                checkDiyStudio();
                checkDiyTemplate();
                checkDiyMode(info.mode);
                checkDiyGuide();
                checkColorMode(info.mode);
                changeColorMode(info.mode);
                checkMicMode();
                checkScenesMode();
                analytic4ModeUse(info.mode);
                modeUI.show();
                modeUI.setMode(info.mode);
            } else {
                hideDiyGuide();
                brightnessUI.hide();
                modeUI.hide();
            }
            if (uiResult != null && reportResult) uiResult.uiResult(IUiResult4Ble.ui_result_suc);
        } else {
            /*通信中*/
            timerUI.hide();
            brightnessUI.hide();
            modeUI.hide();
            effectUI.hide();
            hideDiyGuide();
            if (uiResult != null && reportResult) uiResult.uiResult(IUiResult4Ble.ui_result_ing);
        }
        isSwitchMicPickUpType = false;
    }

    /*标志位-是否统计了mode的使用;进去一次仅统计一次*/
    private boolean hadAnalyticModeUse = false;

    private void analytic4ModeUse(AbsMode mode) {
        if (hadAnalyticModeUse) return;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode == null) return;
        hadAnalyticModeUse = true;
        subMode.setAnalyticType(ISubMode.ANALYTIC_TYPE_MODE_USE_DETAIL);
    }

    private void checkMusicMode() {
        boolean supportMultiNewMusic4Ble = Support.supportMultiNewMusic4Ble(info.sku, info.versionSoft);
        int musicModeVersion = supportMultiNewMusic4Ble ? 1 : 0;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkMusicMode() supportMultiNewMusic4Ble = " + supportMultiNewMusic4Ble + " ; musicModeVersion = " + musicModeVersion);
        }
        /*更换对应的uiMode*/
        if (modeUI instanceof ModeUiV1) {
            ((ModeUiV1) modeUI).changeMusicMode(musicModeVersion);
        }
        /*设置默认音乐模式*/
        AbsMode mode = info.mode;
        if (mode instanceof Mode) {
            ((Mode) mode).changeMusicMode4MultiNewMusic4Telink(musicModeVersion);
        }
    }

    private void changeColorMode(AbsMode mode) {
        int subModeColorVersion = Support.supportPartBrightness(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, "");
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "changeColorMode() subModeColorVersion = " + subModeColorVersion);
        }
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subModeColorVersion > 0 && subMode instanceof SubModeColor) {
                /*转化成新的颜色模式*/
                mode.subMode = SubModeColorV2.parseSubModeColor2New((SubModeColor) subMode);
                if (bleOp.isOpCommEnable()) {
                    bleOp.readPartColor();
                }
            }
        }
        if (modeUI instanceof ModeUiV1) {
            ((ModeUiV1) modeUI).changeColorMode(subModeColorVersion, info.ic, getDiySupport());
        }
    }

    private void checkDiyStudio() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenes) {
            int scenesCode = ((SubModeScenes) subMode).getEffect();
            boolean scenes4DiyStudio = ScenesOp.isScenes4DiyStudio(scenesCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkDiyStudio() scenesCode = " + scenesCode + " ; scenes4DiyStudio = " + scenes4DiyStudio);
            }
            if (scenes4DiyStudio) {
                /*来自于Studio的DIY-切换成DIY模式*/
                int diyCode = DiyOpM.getInstance.getDiyCode4DiyTemplate(info.sku, scenesCode);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "checkDiyStudio() diyCode = " + diyCode);
                }
                mode.subMode = new SubModeNewDiy(diyCode);
            }
        }
    }

    private void checkDiyTemplate() {
        AbsMode mode = info.mode;
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeScenes) {
            int scenesCode = ((SubModeScenes) subMode).getEffect();
            boolean scenes4DiyTemplate = ScenesOp.isScenes4DiyTemplate(scenesCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkDiyTemplate() scenes4DiyTemplate " + scenes4DiyTemplate + " ; scenesCode = " + scenesCode);
            }
            if (scenes4DiyTemplate) {
                /*当前是diy模版效果；将模式切换长diy模式*/
                int diyCode = DiyOpM.getInstance.getDiyCode4DiyTemplate(info.sku, scenesCode);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "checkDiyTemplate() diyCode = " + diyCode);
                }
                mode.subMode = new SubModeNewDiy(diyCode);
            }
        }
    }

    private void checkScenesMode() {
        if (modeUI instanceof ModeUiV1) {
            int newVersion = getNewVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkScenesMode() newVersion = " + newVersion);
            }
            ((ModeUiV1) modeUI).setScenesModeVersion(newVersion);
        }
    }

    private int getNewVersion() {
        return Support.newVersion4Ble(info.sku, info.goodsType, info.pactCode, info.versionSoft, info.versionHard);
    }

    private boolean checkBrightnessEnable() {
        AbsMode mode = info.mode;
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeScenes) {
                int effect = ((SubModeScenes) subMode).getEffect();
                int newVersion = getNewVersion();
                if (newVersion == 1) {
                    boolean[] brightnessChangeEnable = ScenesRgb.brightnessChangeEnable(effect);
                    if (brightnessChangeEnable[0]) {
                        return brightnessChangeEnable[1];
                    }
                }
                return Support.supportScenesBrightnessOp(info.pactType, info.pactCode, info.goodsType, effect, info.sku);
            }
        }
        return true;
    }

    private void checkColorMode(AbsMode mode) {
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeColor) {
                ((SubModeColor) subMode).gradual = ext.gradual;
            } else if (subMode instanceof SubModeColorV2) {
                ((SubModeColorV2) subMode).gradual = ext.gradual;
            }
        }
    }

    private void showLoading() {
        LoadingDialog.createDialog(ac, com.ihoment.base2app.R.style.DialogDim, 60 * 1000).setEventKey(TAG).show();
    }

    private void hideLoading() {
        LoadingDialog.hideDialog(TAG);
    }

    private void toast(@StringRes int strRes) {
        ToastUtil.getInstance().toast(strRes);
    }

    private void addViewMargin(PercentRelativeLayout contentParent, View subView, int belowId, int width, int height, int marginTop) {
        PercentRelativeLayout.LayoutParams lp = getLP(width, height);
        lp.addRule(RelativeLayout.BELOW, belowId);
        lp.topMargin = marginTop;
        lp.bottomMargin = 0;
        contentParent.addView(subView, lp);
    }

    private PercentRelativeLayout.LayoutParams getLP(int width, int height) {
        return new PercentRelativeLayout.LayoutParams(width, height);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onBrightnessEndEvent(BrightnessUI.EventBrightnessClickEvent event) {
        int brightness = event.brightness;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onBrightnessEndEvent() brightness = " + brightness);
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            BrightnessController controller = new BrightnessController(brightness);
            bleOp.executeOp(controller);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onModeV1Event(Mode mode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onModeV1Event()");
        }
        changeMode(mode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeModeEvent(ChangeModeEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent()");
        }
        /*若是采用颜色指令进行mic逻辑发送的，需要在切换模式时将模式设置成颜色模式的纯色*/
        if (!Support.supportPartBrightness4Ble(info.versionSoft, info.versionHard)) {
            checkMicByColorMode(event);
        }
        ISubMode subMode = event.getSubMode();
        boolean change2NewMultiMusicCode = change2NewMultiMusicMode(subMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent() change2NewMultiMusicCode = " + change2NewMultiMusicCode);
        }
        if (change2NewMultiMusicCode) return;
        boolean changeDiyMode = changeDiyMode(subMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent() changeDiyMode = " + changeDiyMode);
        }
        if (changeDiyMode) return;
        /*检测场景模式下的效果是否支持*/
        ISubMode subModeNew = Support.checkScenesModeEffect(subMode, info.sku, info.goodsType, info.pactType, info.pactCode, getNewVersion(), bleOp.isOpCommEnable(), info.versionSoft, info.versionHard, false, "", "");
        if (subModeNew != null) {
            subMode = subModeNew;
        }
        Mode mode = new Mode();
        mode.subMode = subMode;
        changeMode(mode);
        /*统计场景使用次数*/
        if (subMode instanceof SubModeScenes) {
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.scene_mode);
        } else if (subMode instanceof SubModeColor || subMode instanceof SubModeColorV2) {
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.color_mode);
        } else if (subMode instanceof SubModeMusic || subMode instanceof SubModeMusicV1) {
            AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.music_mode);
        }
    }

    private boolean change2NewMultiMusicMode(ISubMode subMode) {
        if (subMode instanceof SubModeMusicV2) {
            int musicCode = ((SubModeMusicV2) subMode).getMusicCode();
            int sensitivity = ((SubModeMusicV2) subMode).getSensitivity();
            boolean newMusicCode = Support.isNewMusicCode(musicCode);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "change2NewMultiMusicMode() newMusicCode = " + newMusicCode);
            }
            if (newMusicCode) {
                /*若当前是新的音乐模式-则需要先发送新的多包参数*/
                boolean setLocalNewMusicMode = AbsNewMusicEffect.setLocalNewMusicMode(info.sku, info.device, musicCode, sensitivity);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "change2NewMultiMusicMode() setLocalNewMusicMode = " + setLocalNewMusicMode);
                }
                AnalyticsRecorder.getInstance().recordUseCountTimes(ParamKey.music_mode);
                return true;
            }
        }
        return false;
    }

    /**
     * 离开 通过颜色模式实现的手机拾音模式之前，从新设置灯带的颜色为纯色
     */
    private void checkMicByColorMode(ChangeModeEvent event) {
        if (bleOp.isOpCommEnable()) {
            int color = event.getColor();
            if (color != 0) {
                Mode mode = new Mode();
                mode.subMode = SubModeColor.makeSubModeColor(color);
                mode.subMode.saveLocal();
                if (event.getSubMode() instanceof SubModeColor) {
                    event.setSubMode(mode.subMode);
                }
                ModeController modeController = new ModeController(mode);
                bleOp.executeExtOp(modeController);
            }
        }
    }

    private boolean changeDiyMode(ISubMode subMode) {
        if (subMode instanceof SubModeNewDiy) {
            boolean hadToken = AccountConfig.read().isHadToken();
            String diyValueKey = ((SubModeNewDiy) subMode).getDiyValueKey();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "changeDiyMode() diyValueKey = " + diyValueKey + " ; hadToken = " + hadToken);
            }
            DiyValue diyValue = DiyShortcutManger.queryLastApplyDiy(info.sku, info.goodsType, getDiySupport(), info.ic, diyValueKey, true);
            Util4Diy.toApplyDiy(diyValue);
            return true;
        }
        return false;
    }

    private void changeMode(Mode mode) {
        if (bleOp.isOpCommEnable()) {
            showLoading();
            AbsMultipleControllerV14Scenes newScenesMode = Support.is2NewScenesMode(info.sku, info.device, mode, getNewVersion());
            if (LogInfra.openLog()) {
                LogInfra.Log.i("MicFragment", "newScenesMode：" + (newScenesMode != null));
            }
            if (newScenesMode != null) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "changeMode() AbsMultipleControllerV1");
                }
                bleOp.executeMultiOpV1(newScenesMode);
            } else {
                ModeController controller = new ModeController(mode);
                bleOp.executeOp(controller);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventScenesEffect(EventScenesEffect effect) {
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "onEventScenesEffect");
        }
        CategoryV1.SceneV1 sceneV1 = effect.sceneV1;
        int pos = effect.pos;
        if (bleOp.isOpCommEnable()) {
            boolean result = scenesEffect4Ble(sceneV1, pos);
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(result ? EventEffectSquareOpResult.result_op_support : EventEffectSquareOpResult.result_op_no_support, EventEffectSquareOpResult.status_ble_no_support);
        } else {
            /*当前设备未连接*/
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_unable);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventEditScenes(EffectEvent.EventEditScenes event) {
        String sku = event.sku;
        int categoryId = event.categoryId;
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "onEventEditScenes:" + sku + "---categoryId:" + categoryId);
        }
        if (!info.sku.equals(sku)) return;
        EditScenesAc.jump2EditScenesAc(ac, info.sku, info.device, info.goodsType, categoryId, true, false);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeScenes(CategoryV1.SceneV1 scene) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeScenes()");
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            int sceneType = scene.getSceneType(0, info.sku);
            int sceneCode = scene.getSceneCode(0, info.sku);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onChangeScenes() sceneType = " + sceneType + " ; scene.sceneCode = " + sceneCode);
            }
            if (!scenesEffect4Ble(scene, 0)) {
                HintDialog1.createHintDialog1(ac, ResUtil.getString(R.string.b2light_scenes_ble_not_support), ResUtil.getString(R.string.hint_done_got_it)).show();
                hideLoading();
            }
        }
    }

    /**
     * ble 发送配置场景
     *
     * @param sceneV1 sceneV1
     * @param pos     pos
     * @return boolean
     */
    private boolean scenesEffect4Ble(CategoryV1.SceneV1 sceneV1, int pos) {
        int cmdVersion = sceneV1.getCmdVersion(info.sku, pos);
        int sceneType = sceneV1.getSceneType(pos, info.sku);
        int sceneCode = sceneV1.getSceneCode(pos, info.sku);
        if (!Support.checkShareEffectVersion4Ble(sceneType, cmdVersion, info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard)) {
            return false;
        }
        String sceneEffectStr = sceneV1.getScenesParam(info.sku, pos);
        if (sceneType == ScenesOp.scene_type_static) {
            /*静态场景效果*/
            SubModeScenes subModeScenes = new SubModeScenes();
            subModeScenes.setEffect(sceneCode);
            Mode mode = new Mode();
            mode.subMode = subModeScenes;
            ModeController controller = new ModeController(mode);
            ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeOp(controller));
            return true;
        }
        if (sceneType == ScenesOp.scene_type_rgb) {
            /*rgb效果*/
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgb(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeMultiOpV1(controllerV14Scenes));
            } else {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "onChangeScenes() rgb指令解析出错!");
                }
                return false;
            }
            return true;
        }
        if (sceneType == ScenesOp.scene_type_rgbic) {
            /*rgbic效果*/
            int[] rgbicProtocolVersionBytes = ScenesRgbIC.isRGBICProtocolVersionBytes(Encode.decryByBase64(sceneEffectStr));
            if (rgbicProtocolVersionBytes[0] == 0) return false;
            int parseVersion = rgbicProtocolVersionBytes[1];
            if (parseVersion != cmdVersion) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "版本不一致  parseVersion:" + parseVersion + "----cmdVersion:" + cmdVersion);
                }
                return false;
            }
            AbsMultipleControllerV14Scenes controllerV14Scenes = ScenesOp.parserScenes4Rgbic(sceneCode, sceneEffectStr);
            if (controllerV14Scenes != null) {
                ScenesOp.toSetScenes(ac, sceneV1, pos, () -> bleOp.executeMultiOpV1(controllerV14Scenes));
            } else {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "onChangeScenes() rgbic指令解析出错!");
                }
                return false;
            }
            return true;
        }
        return false;
    }

    /**
     * diy效果发生改变操作
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyEffectOp(EventDiyEffectOp event) {
        int opType = event.opType;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyEffectOp() opType = " + opType);
        }
        checkDiyMode(info.mode);
    }

    /**
     * 注册diy的输出
     *
     * @param event event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApply(EventDiyApply event) {
        boolean applyDiyV0 = applyDiyV0(event.getDiyProtocol());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApply() applyDiyV0 = " + applyDiyV0);
        }
        if (!applyDiyV0) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    /**
     * diy模版
     *
     * @param diyTemplate
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyTemplate(DiyTemplate diyTemplate) {
        boolean applyDiyV2 = applyDiyV2(diyTemplate);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyTemplate() applyDiyV2 = " + applyDiyV2);
        }
        if (!applyDiyV2) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    /**
     * 来自于Studio的 DIY模版
     *
     * @param diyStudio
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyStudio(DiyStudio diyStudio) {
        boolean applyDiyV4 = applyDiyV4(diyStudio);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyStudio() applyDiyV4 = " + applyDiyV4);
        }
        if (!applyDiyV4) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyAi(DiyAi diyAi) {
        boolean applyDiyV5 = applyDiyV5(diyAi);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyAi() applyDiyV5 = " + applyDiyV5);
        }
        if (!applyDiyV5) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }

    /**
     * 进入定时界面
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewTimerClickEvent(NewTimerUI.NewTimerClickEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewTimerClickEvent()");
        }
        NewShowTimerAcV2.jump2NewShowTimerAc(ac, info.sku, info.device, ext.timerInfo1, ext.timerInfo2, ext.wakeUpInfo, ext.sleepInfo, info.isSupportIot());
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onUpdateTimeEvent(UpdateTimeEvent event) {
        LogInfra.Log.i(TAG, "onUpdateTimeEvent");
        readTimer();
    }

    private void readTimer() {
        if (bleOp.isOpCommEnable()) {
            SyncTimeInfo info = SyncTimeInfo.getCurrentTime();
            AbsSingleController[] controllers = new AbsSingleController[]{new SyncTimeController(info.hour, info.minute, info.second, info.week), new AutoTimeController(0), new AutoTimeController(1), new WakeUpController(), new SleepController()};
            bleOp.executeOp(controllers);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewTimerSetEvent(NewTimerSetEvent event) {
        TimerInfo info = event.getInfo();
        int group = event.getGroup();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "group = " + group + " ; info.str = " + info.getDisplayTimeStr() + " ; open = " + info.isOpen());
        }
        if (bleOp.isOpCommEnable()) {
            info.check();
            AutoTimeController autoTimeController = new AutoTimeController(group, info.isOpen(), info.openHour, info.openMin, info.closeHour, info.closeMin, info.repeat);
            bleOp.executeOp(autoTimeController);
        } else {
            TimerFailEvent.sendTimerFailEvent(false);
            toast(R.string.b2light_aal_light_connect_label_error);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onWakeSetEvent(NewWakeupSetEvent event) {
        WakeUpInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onWakeSetEvent:" + info.toString());
        }
        if (bleOp.isOpCommEnable()) {
            info.check();
            WakeUpController wakeUpController = new WakeUpController(info.enable, info.endBri, info.wakeHour, info.wakeMin, info.repeat, info.wakeTime);
            bleOp.executeOp(wakeUpController);
        } else {
            WakeupFailEvent.sendWakeupFailEvent(false);
            toast(R.string.b2light_aal_light_connect_label_error);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSleepSetEvent(NewSleepSetEvent event) {
        SleepInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onSleepSetEvent:" + info.toString());
        }
        if (bleOp.isOpCommEnable()) {
            boolean open = event.isOpen();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onSleepSetEvent() open = " + open);
            }
            /*若当前是开启睡眠模式，但设备已处于关闭状态*/
            if (open && !this.info.open) {
                SleepFailEvent.sendSleepFailEvent(false);
                toast(R.string.b2light_please_open_the_light);
                return;
            }
            info.check();
            SleepController sleepController = new SleepController(info.enable, info.startBri, info.closeTime, info.closeTime);
            bleOp.executeOp(sleepController);
        } else {
            SleepFailEvent.sendSleepFailEvent(false);
            toast(R.string.b2light_aal_light_connect_label_error);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSleepTimeSub(EventSleepUpdate event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onSleepTimeSub()");
        }
        if (ext.sleepInfo != null && ext.sleepInfo.isOn()) {
            SleepInfo info = event.getInfo();
            info.check();
            ext.sleepInfo = info;
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventChangeGradual(EventChangeGradual event) {
        boolean open = event.open;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventChangeGradual() open = " + open);
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            GradualController gradualController = new GradualController(open);
            bleOp.executeOp(gradualController);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventChangeLimit(EventChangeLimit event) {
        if (bleOp.isOpCommEnable()) {
            boolean limitOpen = event.limitOpen;
            LimitController limitController = new LimitController(limitOpen);
            bleOp.executeOp(limitController);
        }
    }

    @Override
    public boolean limitOpen() {
        return ext.limitOpen;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEffectClickEvent(EffectUI.EffectClickEvent event) {
        boolean isSupportScenesLib = getNewVersion() == 1;
        int[] protocolSet = null;
        if (isSupportScenesLib) {
            protocolSet = Support.getSupportScenesOpSet(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard);
        }
        EffectAc.jump2EffectAc(ac, info.goodsType, info.sku, info.device, protocolSet, true, false, isSupportScenesLib, info.versionSoft, info.versionHard);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventColorEffect(EffectData.ColorEffect colorEffect) {
        /*设置颜色模式*/
        if (bleOp.isOpCommEnable()) {
            boolean singleColor = colorEffect.isSingleColor();
            int size = colorEffect.colorSet.length;
            if (size != 1 && size != 15) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "颜色数量不支持：" + size);
                }
                EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_no_support);
                return;
            }
            Mode mode = new Mode();

            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onMicSetRgbController rgb:" + JsonUtil.toJson(colorEffect));
            }
            if (singleColor) {
                mode.subMode = SubModeColor.makeSubModeColor(colorEffect.colorSet[0]);
                changeMode(mode);
            } else {
                AbsSingleController[] controllers = SubModeColor.makeSubModeColor(colorEffect.colorSet, colorEffect.isFade(), !colorEffect.noFadeController());
                if (controllers == null) {
                    //不支持
                    EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_no_support);
                    return;
                }
                bleOp.executeOp(controllers);
            }
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_support);
        } else {
            /*当前设备未连接*/
            EventEffectSquareOpResult.sendEventEffectSquareOpResult(EventEffectSquareOpResult.result_op_unable);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventServiceSceneFresh(EventServiceScenesFresh event) {
        String sku = event.sku;
        String skuCur = info.sku;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventServiceSceneFresh() sku = " + sku + " ; skuCur = " + skuCur + " ; uiTypeBle = " + curUiType);
        }
        if (!TextUtils.isEmpty(sku) && sku.equals(skuCur) && curUiType == ui_type_normal) {
            /*检查是否有服务器配置场景*/
            EventSceneCheck4BleV1.sendEventSceneCheck4BleV1(info);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventChangeScenes(EventChangeScenes event) {
        String sku = event.sku;
        String skuCur = info.sku;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventChangeScenes() sku = " + sku + " ; skuCur = " + skuCur);
        }
        if (!TextUtils.isEmpty(sku) && sku.equals(skuCur) && curUiType == ui_type_normal) {
            /*检查是否有服务器配置场景*/
            EventSceneCheck4BleV1.sendEventSceneCheck4BleV1(info);
        }
    }

    /**
     * 注册diy的输出-v1版本
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApplyGraffiti(EventDiyApplyV2 event) {
        boolean applyDiyV3 = applyDiyV3(event.getDiyGraffiti());
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyGraffiti() applyDiyV3 = " + applyDiyV3);
        }
        if (!applyDiyV3) {
            toast(R.string.b2light_aal_light_connect_label_error);
            EventDiyApplyResult.sendEventDiyApplyResultNoConnect();
        }
    }


    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventColorStrip(EventColorStrip event) {
        /*色条设置颜色模式*/
        if (bleOp.isOpCommEnable()) {
            int size = event.colors.colorSet.length;
            if (size != 15) {
                //不支持
                if (LogInfra.openLog()) {
                    LogInfra.Log.e(TAG, "颜色数和灯段数不等：" + size);
                }
                return;
            }
            showLoading();
            AbsSingleController[] modeControllers;
            boolean supportPartBrightness = Support.supportPartBrightness4Ble(info.versionSoft, info.versionHard);
            if (!supportPartBrightness) {
                modeControllers = SubModeColor.makeSubModeColor(event.colors);
            } else {
                modeControllers = SubModeColorV2.makeSubModeColor(event.colors);
            }
            if (bleOp.isOpCommEnable()) {
                bleOp.executeOp(modeControllers);
            }
        }
    }

    /*新DIY交互补充代码逻辑*/

    private void checkDiyModeInfo(SubModeNewDiy diy) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyModeInfo()");
        }
        if (diy == null) return;
        /*刷新在模式中展示的DIY列表*/
        diy.setDiyGroups(curDiyGroups);
        /*获取当前选中的diyValueKey*/
        int diyCode = diy.getDiyCode();
        int newVersion = Support.getDiyVersion(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyModeInfo() newVersion = " + newVersion);
        }
        String lastDiyApplyKey = Diy.getLastDiyApplyKey(newVersion, info.goodsType, info.ic, info.sku, diyCode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyMode() lastDiyApplyKey = " + lastDiyApplyKey + " ; diyCode = " + diyCode);
        }
        diy.setDiyValueKey(lastDiyApplyKey);
    }

    private void checkDiyMode(AbsMode mode) {
        if (mode == null) return;
        ISubMode subMode = mode.subMode;
        if (subMode instanceof SubModeNewDiy) {
            checkDiyModeInfo((SubModeNewDiy) subMode);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEvent2AcDiyGroup(Event2AcDiyGroup event) {
        int icNum = info.ic;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEvent2AcDiyGroup icNum = " + icNum);
        }
        AcDiyGroup.jump2DiyGroupAc(ac, info.sku, info.goodsType, getDiySupport(), icNum, true);
    }

    private DiySupportV1 getDiySupport() {
        int newVersion = Support.getDiyVersion(info.sku, info.goodsType, info.pactType, info.pactCode, info.versionSoft, info.versionHard);
        return Diy.getDiySupport(newVersion);
    }

    private DiyValue applyingDiyValue;

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventDiyApplyInModeShowing(EventDiyApply4InModeShowing event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing()");
        }
        DiyProtocol diyProtocol = event.getDiyProtocol();
        if (diyProtocol != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV0 = applyDiyV0(diyProtocol);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV0 = " + applyDiyV0);
            }
            if (applyDiyV0) {
                showLoading();
            }
            return;
        }
        DiyTemplate diyTemplate = event.getDiyTemplate();
        if (diyTemplate != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV2 = applyDiyV2(diyTemplate);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV2 = " + applyDiyV2);
            }
            if (applyDiyV2) {
                showLoading();
            }
            return;
        }
        DiyGraffitiV2 diyGraffitiV2 = event.getDiyGraffitiV2();
        if (diyGraffitiV2 != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV3 = applyDiyV3(diyGraffitiV2);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV3 = " + applyDiyV3);
            }
            if (applyDiyV3) {
                showLoading();
            }
            return;
        }

        DiyStudio diyStudio = event.getDiyStudio();
        if (diyStudio != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV4 = applyDiyV4(diyStudio);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV4 = " + applyDiyV4);
            }
            if (applyDiyV4) {
                showLoading();
            }
            return;
        }

        DiyAi diyAi = event.getDiyAi();
        if (diyAi != null) {
            applyingDiyValue = event.getDiyValue();
            boolean applyDiyV5 = applyDiyV5(diyAi);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyApplyInModeShowing() applyDiyV5 = " + applyDiyV5);
            }
            if (applyDiyV5) {
                showLoading();
            }
        }

    }

    private boolean applyDiyV0(@NonNull DiyProtocol diyProtocol) {
        if (bleOp.isOpCommEnable()) {
            MultipleDiyController multipleDiyController = new MultipleDiyController(diyProtocol);
            bleOp.executeMultiOp(multipleDiyController);
            return true;
        }
        return false;
    }

    private boolean applyDiyV2(@NonNull DiyTemplate diyTemplate) {
        if (bleOp.isOpCommEnable()) {
            int diyCode = diyTemplate.diyCode;
            int templateCode = diyTemplate.scenesCode;
            String effectStr = diyTemplate.effectStr;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDiyTemplate() diyCode = " + diyCode + " ; templateCode = " + templateCode + " ; effectStr = " + effectStr);
            }
            AbsMultipleControllerV1 diyTemplateController = ScenesOp.parseDiyTemplate(templateCode, diyCode, effectStr);
            bleOp.executeMultiOpV1(diyTemplateController);
            return true;
        }
        return false;
    }

    private boolean applyDiyV3(@NonNull DiyGraffitiV2 diyGraffitiV2) {
        if (bleOp.isOpCommEnable()) {
            MultiDiyGraffitiController controller = new MultiDiyGraffitiController(diyGraffitiV2.getDiyCode(), diyGraffitiV2.getEffectBytes());
            bleOp.executeMultiOpV1(controller);
            return true;
        }
        return false;
    }

    private boolean applyDiyV5(@NonNull DiyAi diyAi) {
        Command4PtReal ptReal = diyAi.command4PtReal;
        if (bleOp.isOpCommEnable()) {
            bleOp.executeMultiple4PtReal(PtRealController.makePtRealController(ptReal.opCommands, diyAi.diyCode, diyAi.scenesCode));
            return true;
        }
        return false;
    }

    private boolean applyDiyV4(@NonNull DiyStudio diyStudio) {
        if (bleOp.isOpCommEnable()) {
            boolean supportRgbicV1 = Support.isSupportRgbicV1(info.goodsType, info.pactType, info.pactCode, info.sku, info.versionSoft, info.versionHard, null);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "applyDiyV4() supportRgbicV1 = " + supportRgbicV1);
            }
            AbsMultipleControllerV14DiyTemplate multi4Scenes = diyStudio.toMulti4Scenes4RgbicV1(supportRgbicV1 ? 1 : 0);
            if (multi4Scenes != null) {
                bleOp.executeMultiOpV1(multi4Scenes);
                return true;
            }
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN, priority = 100)
    public void onEventDiyApplyResult(EventDiyApplyResult event) {
        int diyCode = event.getDiyCode();
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyApplyResult() result = " + result + " ; diyCode = " + diyCode);
        }
        if (applyingDiyValue != null && applyingDiyValue.diyCode == diyCode) {
            if (result) {
                String diyValueKey = applyingDiyValue.getDiyValueKey();
                /*记录上次应用的DIY*/
                LastDiyConfig.read().saveLastDiyValueKey(info.sku, diyValueKey, diyCode, applyingDiyValue.effectCode);
            } else {
                toast(R.string.b2light_diy_apply_fail);
                hideLoading();
            }
        }
        applyingDiyValue = null;
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    public void onEventDiyModeShowingChange(EventDiyModeShowingChange event) {
        List<DiyGroup> diyShortcuts = DiyShortcutManger.getDiyShortcuts(info.sku, info.goodsType, info.ic, getDiySupport());
        curDiyGroups.clear();
        if (diyShortcuts != null && !diyShortcuts.isEmpty()) {
            curDiyGroups.addAll(diyShortcuts);
        }
        handler.post(new CaughtRunnable() {
            @Override
            protected void runSafe() {
                checkUi();
            }
        });
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDiyModeShowingChange() curDiyGroups.size = " + curDiyGroups.size());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSwitchMicPickUpType(EventSwitchMicPickUpType event) {
        isSwitchMicPickUpType = true;
        modeUI.switchMicPickUpMode(event.isMicMode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onMicSetRgbController(MicSetRgbController controller) {
        if (bleOp.isOpCommEnable()) {
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_updating)) return;/*升级中，忽略mic发送逻辑*/
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_rebooting))
                return;/*重启过程中，忽略mic发送逻辑*/
            if (controller.isInitMode) {
                EventBleStatus.sendEvent(EventBleStatus.BleStatus.read_info_over);
            }
            if (controller.sendByColorMode) {
                Mode mode = new Mode();
                int color = ColorUtils.toColor(controller.data[0], controller.data[1], controller.data[2]);
                mode.subMode = SubModeColor.makeSubModeColor(color);

                ModeController modeController = new ModeController(mode);
                bleOp.executeExtOp(modeController);
            } else {
                bleOp.executeExtOp(controller);
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onRealtimeColorChangeEvent(RealtimeColorChangeEvent event) {
        if (bleOp.isOpCommEnable()) {
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_updating)) return;/*升级中，忽略mic发送逻辑*/
            if (UtilFlag.getInstance.getFlag(Constant.key_flag_rebooting))
                return;/*重启过程中，忽略mic发送逻辑*/
            if (!Util4ColorRealtime.supportColorRealtime(info.sku, info.versionSoft, info.versionHard))
                return;
            AbsMicFragmentV4.SupportMicStatus micStatus = Support.getMicStatus(info.sku, info.versionSoft, info.goodsType);
            int controllerType = Util4ColorRealtime.getControllerType(micStatus, event.rgbicPartChoose);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onRealtimeColorChangeEvent() controllerType = " + controllerType);
            }
            if (controllerType == Util4ColorRealtime.CONTROLLER_TYPE_NO_SUPPORT) return;
            ISubMode subMode = event.subMode;
            int color = event.color;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onRealtimeColorChangeEvent() subMode = " + subMode + " ; color = " + color);
            }
            AbsSingleController controller;
            if (controllerType == Util4ColorRealtime.CONTROLLER_TYPE_COLOR) {
                Mode mode = new Mode();
                mode.subMode = subMode;
                controller = new ModeController(mode);
            } else {
                controller = new MicSetRgbController(ColorUtils.getRgbBytes(color));
            }
            bleOp.executeExtOp(controller);
        }
    }

    /*DIY引导逻辑*/

    private void hideDiyGuide() {
        if (hadShowGuide) {
            hadShowGuide = false;
            GuideDialog.hideDialog(TAG);
        }
    }

    private boolean hadDiyGuide = false;
    private boolean hadShowGuide;

    private void checkDiyGuide() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkDiyGuide() hadDiyGuide = " + hadDiyGuide);
        }
        if (hadDiyGuide) return;
        if (modeUI != null) {
            hadDiyGuide = true;
            hadShowGuide = true;
            Util4Diy.checkShowDiyGuide4ModeNum(ac, TAG, modeUI.getModeNum());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSetMultiMusicEffect(EventSetMultiMusicEffect event) {
        int sensitivity = event.sensitivity;
        AbsNewMusicEffect newMusicEffect = event.newMusicEffect;
        int musicCode = newMusicEffect.getMusicCode();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNewMusicEffect() sensitivity = " + sensitivity + " ; newMusicEffect.musicCode = " + musicCode);
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            MultipleController4Music multipleController4Music = new MultipleController4Music(sensitivity, newMusicEffect);
            bleOp.executeMultiOpV2(multipleController4Music);
        }
    }
}
