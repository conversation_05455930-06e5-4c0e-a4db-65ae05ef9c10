package com.govee.dreamcolorlightv1.adjust.v1;

import com.govee.base2light.ac.diy.v1.DiySupportV1;
import com.govee.base2light.ble.v1.AbsMode4UIV1;
import com.govee.base2light.light.v1.AbsMicFragmentV4;
import com.govee.base2light.ui.mode.IUiMode;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiMode;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiModeV1;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiModeV2;
import com.govee.dreamcolorlightv1.adjust.ui.ColorUiModeV3;
import com.govee.dreamcolorlightv1.adjust.ui.DiyUiMode;
import com.govee.dreamcolorlightv1.adjust.ui.MicUiModeV1;
import com.govee.dreamcolorlightv1.adjust.ui.MicUiModeV3;
import com.govee.dreamcolorlightv1.adjust.ui.MusicUiModeV1;
import com.govee.dreamcolorlightv1.adjust.ui.MusicUiModeV3;
import com.govee.dreamcolorlightv1.adjust.ui.ScenesUiModeV1;
import com.ihoment.base2app.infra.LogInfra;

import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2019-07-22
 * mode ui
 */
class ModeUiV3 extends AbsMode4UIV1 {
    private static final String TAG = "ModeUiV3";

    public ModeUiV3(AppCompatActivity ac, String sku, String device, int goodsType, int ic, DiySupportV1 diySupportV1) {
        super(ac, sku, device, goodsType, ic, diySupportV1);
    }

    public void checkMusicVersion(int version) {
        boolean isMic = AbsMicFragmentV4.isMicModeByPhone(sku, device);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkMusicVersion() isMic = " + isMic + " ; version = " + version);
        }
        IUiMode positionUiMode = getPositionUiMode(0);
        if (version == 0) {
            boolean isMicV0 = positionUiMode instanceof MicUiModeV1;
            boolean isMusicV0 = positionUiMode instanceof MusicUiModeV1;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicVersion() isMicV0 = " + isMicV0 + " ; isMusicV0 = " + isMusicV0);
            }
            if (isMic && !isMicV0) {
                changePositionUiMode(0, new MicUiModeV1(sku, device));
                return;
            }
            if (!isMic && !isMusicV0) {
                changePositionUiMode(0, new MusicUiModeV1(sku, device));
            }
            return;
        }

        if (version == 1) {
            boolean isMicV1 = positionUiMode instanceof MicUiModeV3;
            boolean isMusicV1 = positionUiMode instanceof MusicUiModeV3;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "checkMusicVersion() isMicV1 = " + isMicV1 + " ; isMusicV1 = " + isMusicV1);
            }
            if (isMic && !isMicV1) {
                changePositionUiMode(0, new MicUiModeV3(ac, sku, device));
                return;
            }
            if (!isMic && !isMusicV1) {
                changePositionUiMode(0, new MusicUiModeV3(sku, device));
            }
        }

    }

    @Override
    protected IUiMode getMode4() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i("MicFragment", "ModeUiV3 device:" + device);
        }
        return new DiyUiMode(sku, goodsType);
    }

    @Override
    protected IUiMode getMode3() {
        return new ScenesUiModeV1(sku, device, goodsType, true);
    }

    @Override
    protected IUiMode getMode2() {
        return new ColorUiModeV1(goodsType, sku, device, ic, diySupportV1);
    }

    @Override
    protected IUiMode getMode1() {
        return new MusicUiModeV1(sku, device);
    }

    @Override
    protected String getTAG() {
        return "ModeUiV3";
    }

    /**
     * 更改颜色模式
     * <p>0:旧颜色模式-0x0b<p/>
     * <p>2:新颜色模式-0x15-支持分段控制亮度<p/>
     *
     * @param subModeColorVersion
     */
    public void changeColorMode(int subModeColorVersion, int ic, DiySupportV1 diySupportV1) {
        IUiMode positionUiMode = getPositionUiMode(1);
        int curColorUiModeVersion = getCurColorUiModeVersion(positionUiMode);
        if (LogInfra.openLog()) {
            LogInfra.Log.i("ModeUiV3", "changeColorMode() subModeColorVersion = " + subModeColorVersion + " ; curColorUiModeVersion = " + curColorUiModeVersion);
        }
        if (curColorUiModeVersion > -1 && curColorUiModeVersion != subModeColorVersion) {
            /*需要进行颜色模式ui界面转化*/
            if (subModeColorVersion == 0) {
                changePositionUiMode(1, new ColorUiMode(goodsType, sku, device, ic, diySupportV1));
                updateSupportHighColor(diySupportV1);
            } else if (subModeColorVersion == 2) {
                changePositionUiMode(1, new ColorUiModeV2(goodsType, sku, device, ic, diySupportV1));
                updateSupportHighColor(diySupportV1);
            } else if (subModeColorVersion == 3) {
                changePositionUiMode(1, new ColorUiModeV3(goodsType, sku, device, ic, diySupportV1));
                updateSupportHighColor(diySupportV1);
            }
        }
    }

    private int getCurColorUiModeVersion(IUiMode positionUiMode) {
        if (positionUiMode instanceof ColorUiMode) return 0;
        if (positionUiMode instanceof ColorUiModeV1) return 1;
        if (positionUiMode instanceof ColorUiModeV2) return 2;
        if (positionUiMode instanceof ColorUiModeV3) return 3;
        return -1;
    }

    @Override
    protected boolean showHighColorContainer() {
        if (diySupportV1 != null) {
            return isSupportHighColor(diySupportV1);
        }
        return false;
    }
}