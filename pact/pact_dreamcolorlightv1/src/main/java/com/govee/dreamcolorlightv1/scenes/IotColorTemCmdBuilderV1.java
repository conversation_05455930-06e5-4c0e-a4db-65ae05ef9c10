package com.govee.dreamcolorlightv1.scenes;

import com.govee.base2home.scenes.BaseCmd;
import com.govee.base2home.scenes.ICmd;
import com.govee.base2home.scenes.builder.model.ColorTempModel;
import com.govee.dreamcolorlightv1.pact.Comm;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-18
 * iot-色温控制$
 */
public class IotColorTemCmdBuilderV1 extends AbsIotCmdBuilderV1<ColorTempModel> {
    @Override
    public ICmd createCmd(ColorTempModel colorTempModel) {
        return new BaseCmd() {
            @Override
            public String getIotCmd() {
                return makeCmdStr(Comm.makeColorTemCmd4IotComm(colorTempModel.sku, colorTempModel.goodsType, colorTempModel.pactType, colorTempModel.pactCode, colorTempModel.temColor));
            }
        };
    }
}