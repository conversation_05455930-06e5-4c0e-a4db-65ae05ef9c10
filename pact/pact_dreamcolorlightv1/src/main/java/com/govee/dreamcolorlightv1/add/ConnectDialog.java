package com.govee.dreamcolorlightv1.add;

import android.bluetooth.BluetoothDevice;
import android.content.Context;

import com.govee.base2home.ac.ActivityMgr;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.custom.AbsConnectDialog;
import com.govee.base2home.device.net.DeviceBindResponse;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.main.OfflineDeviceListConfig;
import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.support.OldDreamColorUtil;
import com.govee.base2home.pact.support.OldRgbicBkUtil;
import com.govee.base2home.util.UuidV1;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.EventHardVersion;
import com.govee.base2light.ble.controller.EventIcNum;
import com.govee.base2light.ble.controller.EventSn;
import com.govee.base2light.ble.controller.EventSoftVersion;
import com.govee.base2light.ble.controller.HardVersionController;
import com.govee.base2light.ble.controller.IcNumController;
import com.govee.base2light.ble.controller.SnController;
import com.govee.base2light.ble.controller.SoftVersionController;
import com.govee.ble.BleController;
import com.govee.ble.event.EventBleConnect;
import com.govee.dreamcolorlightv1.ble.Ble;
import com.govee.dreamcolorlightv1.pact.Support;
import com.govee.home.account.config.AccountConfig;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.network.ErrorResponse;
import com.ihoment.base2app.util.JsonUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Create by xieyingwu on 2020-02-12
 * 连接弹窗$
 */
public class ConnectDialog extends AbsConnectDialog {
    private final AddInfo addInfo;

    private ConnectDialog(Context context, BluetoothDevice device, AddInfo addInfo) {
        super(context, device);
        this.addInfo = addInfo;
        /*注册*/
        getBle().registerEvent(true, this.getClass().getName());
    }

    public static ConnectDialog createDialog(Context context, BluetoothDevice device, AddInfo addInfo) {
        return new ConnectDialog(context, device, addInfo);
    }

    private AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected void toConnectBle() {
        boolean connectBle = getBle().connectBle(device, true);
        if (!connectBle) {
            bluetoothClose();
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBleConnect(EventBleConnect event) {
        boolean connectSuc = event.connectSuc();
        LogInfra.Log.i(TAG, "onEventBleConnect() connectSuc = " + connectSuc);
        if (connectSuc) {
            /*统计蓝牙连接成功*/
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.ble_connect_suc, addInfo.sku);
            /*连接成功*/
            sendMsg();
        } else {
            /*统计蓝牙连接失败*/
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.ble_connect_fail, addInfo.sku);
            uiDisconnect();
        }
    }

    protected void sendMsg() {
        AbsSingleController[] controllers;
        boolean supportBindWithIcNum = Support.supportBindWithIcNum(addInfo.goodsType, addInfo.pactType, addInfo.pactCode, addInfo.sku);
        LogInfra.Log.i(TAG, "sendMsg() supportBindWithIcNum = " + supportBindWithIcNum);
        if (supportBindWithIcNum) {
            controllers = new AbsSingleController[]{
                    new SoftVersionController(),
                    new HardVersionController(),
                    new IcNumController(),
                    new SnController()
            };
        } else {
            controllers = new AbsSingleController[]{
                    new SoftVersionController(),
                    new HardVersionController(),
                    new SnController()
            };
        }
        getBle().startController(controllers);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSoftVersionEvent(EventSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "softVersion = " + softVersion);
            }
            addInfo.versionSoft = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onHardVersionEvent(EventHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "hardVersion = " + hardVersion);
            }
            addInfo.versionHard = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onIcNumEvent(EventIcNum event) {
        if (event.isResult()) {
            int icNum = event.icNum;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onIcNumEvent() icNum = " + icNum);
            }
            if (icNum > 0) {
                addInfo.icNum = icNum;
            }
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onSnEvent(EventSn event) {
        if (event.isResult()) {
            String device = event.getUuid();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "device = " + device);
            }
            addInfo.device = device;
        }
        getBle().controllerEvent(event);

        if (event.isResult()) {
            /*构建absDevice对象，用于绑定设备*/
            DeviceExtMode deviceExt = new DeviceExtMode();
            deviceExt.setLastDeviceData("{}");
            deviceExt.setDeviceSettings(makeDeviceSettingsJsonStr());
            int goodsType = addInfo.goodsType;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onSnEvent() addInfo.sku = " + addInfo.sku + " ; goodsType = " + goodsType);
            }
            goodsType = OldRgbicBkUtil.goodsTypeCheck(addInfo.sku, goodsType);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onSnEvent() goodsTypeCheck---> goodsType = " + goodsType);
            }
            if (OldDreamColorUtil.supportOldDreamColorUpdateSkuArray.contains(addInfo.sku)) {
                /*属于旧幻彩升级的sku；goodsType=0*/
                goodsType = GoodsType.GOODES_TYPE_NO_SUPPORT;
            }
            boolean isOldSku = UuidV1.needMakeDeviceIdSkuList.contains(addInfo.sku);
            /*若是需要手动生成uuid的设备sku，则uuid规则需要跟之前一致*/
            if (isOldSku) {
                AccountConfig accountConfig = AccountConfig.read();
                if (accountConfig.isHadToken()) {
                    /*若已登录，则device需要重新生成*/
                    String uuid = UuidV1.getUuid(accountConfig.getAccountId(), addInfo.bleName);
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "onSnEvent() 生成新的 uuid = " + uuid);
                    }
                    addInfo.device = uuid;
                }
            }
            absDevice = new AbsDevice(addInfo.device, addInfo.sku, addInfo.versionHard, addInfo.versionSoft, addInfo.deviceName, goodsType, deviceExt);
            /*读取完成，绑定设备-检测设备锁*/
            if (isOldSku) {
                /*旧设备的device是跟随账号的,无需校验设备锁*/
                bindDeviceStep();
            } else {
                checkDeviceLock(addInfo.sku, addInfo.device);
            }
        }
    }

    @Override
    protected void retryCheckDeviceLock() {
        checkDeviceLock(addInfo.sku, addInfo.device);
    }

    @Override
    protected void bindDeviceStep() {
        AccountConfig accountConfig = AccountConfig.read();
        boolean login = accountConfig.isHadToken();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "stepBindDevice() login = " + login);
        }
        if (login) {
            bindDevice(absDevice);
        } else {
            OfflineDeviceListConfig.read().addOfflineDevice(absDevice);
            afterBindSuc();
        }
    }

    private String makeDeviceSettingsJsonStr() {
        BindExt bindExt;
        if (addInfo.icNum > 0) {
            bindExt = new BindExt_1(addInfo.pactType, addInfo.pactCode, addInfo.icNum);
        } else {
            bindExt = new BindExt(addInfo.pactType, addInfo.pactCode);
        }
        bindExt.deviceName = addInfo.deviceName;
        bindExt.bleName = addInfo.bleName;
        bindExt.address = addInfo.bleAddress;
        return JsonUtil.toJson(bindExt);
    }

    @Override
    protected void bluetoothClose() {
        closeBle();
        /*关闭弹窗*/
        hide();
    }

    @Override
    protected void bindError(ErrorResponse response) {
        beBindError(true);
    }

    @Override
    protected void bindSuc(DeviceBindResponse response) {
        afterBindSuc();
    }

    private void afterBindSuc() {
        /*隐藏弹窗*/
        hide();
        /*断开蓝牙*/
        getBle().stopHeart();
        BleController.getInstance().toBtClose();
        /*关闭其他Ac，除了主界面*/
        ActivityMgr.getInstance().finishAllExceptMain();
        /*跳转到名称设置界面*/
        DeviceNameAc.jump2DeviceNameAc(context, addInfo);
        context = null;
    }

    @Override
    protected void toDisconnectBle() {
        closeBle();
    }

    private void closeBle() {
        getBle().stopHeart();
        BleController.getInstance().toBtClose();
        getBle().registerEvent(false, this.getClass().getName());
        getBle().release();
    }
}