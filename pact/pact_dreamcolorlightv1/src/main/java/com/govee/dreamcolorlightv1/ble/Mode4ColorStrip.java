package com.govee.dreamcolorlightv1.ble;

import com.govee.base2light.ble.controller.AbsMode;

/**
 * Create by hey on 2021/3/9
 * $ 判断
 */
public class Mode4ColorStrip extends AbsMode {
    public boolean isLastController;

    @Override
    protected void parseSubMode(byte subModeType, byte[] subModeValidBytes) {
        if (subModeType == BleProtocol.sub_mode_music) {
            subMode = new SubModeMusic();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_music) {
            subMode = new SubModeMusicV1();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_scenes) {
            subMode = new SubModeScenes();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_new_diy) {
            subMode = new SubModeNewDiy();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_color_v2) {
            subMode = new SubModeColorV2();
            subMode.parse(subModeValidBytes);
        } else {
            /*默认都按照颜色模式解析*/
            subMode = new SubModeColor();
            subMode.parse(subModeValidBytes);
        }
    }
}