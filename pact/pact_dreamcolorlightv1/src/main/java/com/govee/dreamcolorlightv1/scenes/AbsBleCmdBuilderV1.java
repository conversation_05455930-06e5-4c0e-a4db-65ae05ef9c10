package com.govee.dreamcolorlightv1.scenes;

import com.govee.base2home.pact.support.OldDreamColorUtil;
import com.govee.base2home.pact.support.OldRgbicBkUtil;
import com.govee.base2home.scenes.builder.BaseCmdModel;
import com.govee.base2home.scenes.builder.BleCmdBuilder;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.dreamcolorlightv1.ble.BleComm;
import com.govee.dreamcolorlightv1.pact.Support;

import java.util.UUID;

/**
 * Create by xieyingwu on 2019-12-18
 * ble-抽象cmd构造器v1版本$
 */
public abstract class AbsBleCmdBuilderV1<T extends BaseCmdModel> extends BleCmdBuilder<T> {
    @Override
    public boolean needCheckDeviceModel() {
        return true;
    }

    @Override
    public boolean checkSupport(DeviceModel model) {
        String key = model.getKey();
        if (model.getGoodsType() > 0) {
            /*配置了goodsType的旧sku升级BK逻辑*/
            if (OldRgbicBkUtil.isRgbicBk4Ble(model.getSku(), model.pactType, model.pactCode)) {
                return true;
            }
            /*支持goodsType*/
            return checkSupportKeys(key);
        }
        /*判断是否是升级幻彩设备，则采用新项目进行支持*/
        return OldDreamColorUtil.checkSupportNewDreamColor4OldSku(model.getSku(), model.versionSoft, model.versionHard);
    }

    @Override
    public String[] getSupportKeys() {
        return Support.supportBleV1GoodsSet;
    }

    @Override
    public UUID getServiceUUID(String key) {
        return BleComm.serviceUuid;
    }

    @Override
    public UUID getCharacteristicUUID(String key) {
        return BleComm.characteristicUuid;
    }
}