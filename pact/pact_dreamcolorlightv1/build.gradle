apply plugin: 'com.android.library'
apply plugin: 'com.jakewharton.butterknife'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-kapt'

android {
    namespace 'com.govee.dreamcolorlightv1'
    dataBinding {
        enabled = true
    }

    compileSdk COMPILE_SDK_VERSION

    defaultConfig {
        minSdkVersion MIN_SDK_VERSION
        targetSdkVersion TARGET_SDK_VERSION

        //ARouter配置-->用于java编写页面
        javaCompileOptions {
            annotationProcessorOptions {
                arguments = [
                        AROUTER_MODULE_NAME: project.getName(),
                        MODULE_NAME        : project.getName()
                ]
            }
        }
        //ARouter配置-->用于kotlin编写页面
        kapt {
            //ARouter配置
            arguments {
                arg("AROUTER_MODULE_NAME", project.getName())
            }
        }
    }

    buildTypes {
        release {
            isDefault = RELEASE_BUILD_VARIANT_IS_DEFAULT
            minifyEnabled false
            consumerProguardFiles = ['proguard-rules.pro']
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        debug {
            isDefault = !RELEASE_BUILD_VARIANT_IS_DEFAULT
            minifyEnabled false
            consumerProguardFiles = ['proguard-rules.pro']
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    resourcePrefix 'dreamcolorv1_'
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation project(':base2light')
    implementation project(':barcode')
    implementation project(':pact:pact_rgbiclight')
    /*butterknife注解实现*/
    kapt 'com.jakewharton:butterknife-compiler:' + BUTTERKNIFE_VERSION
    /*ARouter相关配置*/
    //noinspection DependencyNotationArgument
    implementation rootProject.ext.sdk.arouter_api
    //noinspection DependencyNotationArgument
    kapt rootProject.ext.sdk.arouter_compiler
    // AppLifecycle
    implementation 'com.govee.lifecycle:lifecycle-api:' + lifecycle_api_version
    kapt 'com.govee.lifecycle:lifecycle-compiler:' + lifecycle_compiler_version
}
