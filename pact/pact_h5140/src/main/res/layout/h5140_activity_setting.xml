<?xml version="1.0" encoding="utf-8"?>
<com.zhy.android.percent.support.PercentRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ac_container"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/ui_bg_color_style_1"
    tools:ignore="MissingDefaultResource"
    >

    <TextView
        android:id="@+id/top_flag"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        />

    <TextView
        android:id="@+id/title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignTop="@+id/btn_back"
        android:layout_alignBottom="@+id/btn_back"
        android:gravity="center"
        android:text="@string/h5072_ads_title"
        android:textColor="@color/font_style_105_textColor"
        android:textSize="@dimen/font_style_105_textSize"
        />

    <ImageView
        android:id="@+id/btn_back"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_below="@+id/top_flag"
        android:background="@drawable/component_btn_style_23"
        android:contentDescription="@null"
        android:padding="5dp"
        android:src="@mipmap/new_sensor_setting_icon_arrow_left"
        app:layout_heightPercent="9.0667%w"
        app:layout_marginLeftPercent="3.2%w"
        app:layout_marginStartPercent="3.2%w"
        app:layout_marginTopPercent="8.1333%w"
        app:layout_widthPercent="9.0667%w"
        />

    <com.govee.base2home.custom.ScrollableNestedScrollView
        android:id="@+id/scroll_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/btn_back"
        app:layout_marginTopPercent="2.8%w"
        >

        <com.zhy.android.percent.support.PercentLinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            >

            <TextView
                android:id="@+id/device_info_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:gravity="center_vertical"
                android:lines="1"
                android:text="@string/device_info_label"
                android:textColor="@color/font_style_70_1_textColor"
                android:textSize="@dimen/font_style_70_1_textSize"
                app:layout_marginTopPercent="5.0667%w"
                app:layout_minHeightPercent="5.3333%w"
                app:layout_paddingLeftPercent="6.1333%w"
                app:layout_paddingRightPercent="6.1333%w"
                />

            <!-- 修改名称-->
            <com.zhy.android.percent.support.PercentRelativeLayout
                android:id="@+id/device_name_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/device_info_label"
                android:layout_centerHorizontal="true"
                android:layout_marginHorizontal="13dp"
                android:background="@drawable/component_bg_style_4"
                app:layout_marginTopPercent="2.6667%w"
                >

                <TextView
                    android:id="@+id/device_name_label"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/device_info_label"
                    android:gravity="center_vertical"
                    android:lines="1"
                    android:text="@string/device_name_hint"
                    android:textColor="@color/font_style_70_2_textColor"
                    android:textSize="@dimen/font_style_70_2_textSize"
                    app:layout_marginTopPercent="4.0115%w"
                    app:layout_minHeightPercent="5.7307%w"
                    app:layout_paddingLeftPercent="4.2980%w"
                    app:layout_paddingRightPercent="4.2980%w"
                    />

                <com.govee.base2home.custom.ClearEditText
                    android:id="@+id/device_name_edit"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@+id/device_name_label"
                    android:layout_toStartOf="@+id/btn_device_name_cancel"
                    android:gravity="center_vertical"
                    android:inputType="text"
                    android:lines="1"
                    android:textColor="@color/font_style_70_3_textColor"
                    android:textColorHint="@color/font_style_28_4_textColor"
                    android:textSize="@dimen/font_style_70_3_textSize"
                    app:cet_clear_drawable="@drawable/component_btn_close"
                    app:cet_clear_drawable_height="27dp"
                    app:cet_clear_drawable_width="27dp"
                    app:cet_input_limit="22"
                    app:layout_marginBottomPercent="4.0115%w"
                    app:layout_marginEndPercent="2.8653%w"
                    app:layout_marginRightPercent="2.8653%w"
                    app:layout_marginTopPercent="1.4327%w"
                    app:layout_minHeightPercent="7.7364%w"
                    app:layout_paddingLeftPercent="4.2980%w"
                    />

                <TextView
                    android:id="@+id/btn_device_name_done"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_alignParentEnd="true"
                    android:gravity="center"
                    android:text="@string/done"
                    android:textColor="@color/component_text_color_70_7_8"
                    android:textSize="@dimen/font_style_70_8_textSize"
                    app:layout_heightPercent="11.4613%w"
                    app:layout_marginEndPercent="2.8653%w"
                    app:layout_marginRightPercent="2.8653%w"
                    app:layout_marginTopPercent="9.3123%w"
                    app:layout_minWidthPercent="14.3266%w"
                    />

                <TextView
                    android:id="@+id/btn_device_name_cancel"
                    android:layout_width="wrap_content"
                    android:layout_height="0dp"
                    android:layout_alignTop="@+id/btn_device_name_done"
                    android:layout_toStartOf="@+id/btn_device_name_done"
                    android:gravity="center"
                    android:text="@string/cancel"
                    android:textColor="@color/component_text_color_70_7_8"
                    android:textSize="@dimen/font_style_70_8_textSize"
                    app:layout_heightPercent="11.4613%w"
                    app:layout_marginEndPercent="2.8653%w"
                    app:layout_marginRightPercent="2.8653%w"
                    app:layout_minWidthPercent="14.3266%w"
                    />
            </com.zhy.android.percent.support.PercentRelativeLayout>

            <TextView
                android:id="@+id/other_info_label"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/device_name_container"
                android:layout_marginBottom="10dp"
                android:gravity="center_vertical"
                android:lines="1"
                android:text="@string/label_4_product_setting"
                android:textColor="@color/font_style_70_2_textColor"
                android:textSize="@dimen/font_style_70_2_textSize"
                app:layout_marginTopPercent="4.8%w"
                app:layout_minHeightPercent="5.3333%w"
                app:layout_paddingLeftPercent="6.1333%w"
                app:layout_paddingRightPercent="6.1333%w"
                />


            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="13dp"
                android:background="@drawable/component_bg_style_4"
                android:orientation="vertical"
                >

                <!--wifi设置-->
                <com.zhy.android.percent.support.PercentLinearLayout
                    android:id="@+id/wifi_container"
                    android:layout_width="match_parent"
                    android:layout_height="70dp"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="visible"
                    >

                    <ImageView
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginStart="7dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_sensor_setting_icon_wifi_blue"
                        app:layout_heightPercent="16%w"
                        app:layout_widthPercent="13.3333%w"
                        />

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="match_parent"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="@string/wifi_setting_label"
                        android:textColor="@color/font_style_70_4_textColor"
                        android:textSize="@dimen/font_style_70_4_textSize"
                        app:layout_paddingLeftPercent="1.8667%w"
                        />

                    <ImageView
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginEnd="10dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_update_list_arrow"
                        app:layout_heightPercent="8%w"
                        app:layout_widthPercent="8%w"
                        />

                </com.zhy.android.percent.support.PercentLinearLayout>

                <include
                    android:id="@+id/gapLine1"
                    layout="@layout/h5140_gap_line"
                    android:visibility="gone"
                    />

                <!--温度单位-->
                <LinearLayout
                    android:id="@+id/llUnit"
                    android:layout_width="match_parent"
                    android:layout_height="70dp"
                    android:alpha="0.5"
                    android:gravity="center_vertical"
                    android:orientation="horizontal"
                    android:visibility="visible"
                    tools:visibility="gone"
                    >

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="7dp"
                        android:contentDescription="@null"
                        android:scaleType="fitCenter"
                        android:src="@mipmap/new_sensor_setting_icon_temper_unites"
                        />

                    <TextView
                        android:id="@+id/tv_temp_unit_text_4_5106_s"
                        android:layout_width="0dp"
                        android:layout_height="70dp"
                        android:layout_marginStart="7dp"
                        android:layout_weight="1"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxLines="2"
                        android:text="@string/h5072_tem_unit"
                        android:textColor="@color/font_style_70_4_textColor"
                        android:textSize="@dimen/font_style_70_4_textSize"
                        />

                    <ImageView
                        android:id="@+id/ivTuValueIcon45106S"
                        android:layout_width="82dp"
                        android:layout_height="30dp"
                        android:layout_marginEnd="10dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_sensor_setting_switch_fahrenheit"
                        />
                </LinearLayout>

                <include
                    android:id="@+id/gapLine2"
                    layout="@layout/h5140_gap_line"
                    android:visibility="gone"
                    tools:visibility="visible"
                    />

                <!--报警方式-->
                <com.govee.h5140.widget.H5140ThWarnSettingViewNew
                    android:id="@+id/thWarnSettingViewNew"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="visible"
                    tools:visibility="gone"
                    />

                <include
                    android:id="@+id/gapLine3"
                    layout="@layout/h5140_gap_line"
                    android:visibility="gone"
                    tools:visibility="visible"
                    />

                <!--C02浓度报警-->
                <com.govee.h5140.detail.setting.ThCo2AlarmRangeView
                    android:id="@+id/tarvPm25AlarmRange"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="gone"
                    />

                <include
                    android:id="@+id/gapLine4"
                    layout="@layout/h5140_gap_line"
                    android:visibility="gone"
                    tools:visibility="visible"
                    />

                <!--温度报警-->
                <com.govee.ui.component.THTemAlarmRangeViewV2
                    android:id="@+id/tem_alarm_range"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="gone"
                    />

                <include
                    android:id="@+id/gapLine5"
                    layout="@layout/h5140_gap_line"
                    android:visibility="gone"
                    tools:visibility="visible"
                    />

                <!--湿度报警-->
                <com.govee.ui.component.THHumAlarmRangeViewV2
                    android:id="@+id/hum_alarm_range"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    tools:visibility="gone"
                    />

                <!-- 报警间隔-->
                <LinearLayout
                    android:id="@+id/llAlarmInterval"
                    android:layout_width="match_parent"
                    android:layout_height="70dp"
                    android:orientation="horizontal"
                    android:visibility="visible"
                    tools:visibility="visible"
                    >

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:src="@mipmap/new_setting_icon_time_set"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:gravity="center_vertical"
                        android:text="@string/h5140_co2_alear_interval"
                        android:textColor="@color/font_style_81_1_textColor"
                        android:textSize="15sp"
                        />

                    <ImageView
                        android:id="@+id/tvCo2AlarmInterval"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:src="@mipmap/new_light_icon_diy_auto_what"
                        />

                    <Space
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        />

                    <TextView
                        android:id="@+id/tvAlarmIntervalTime"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:textColor="@color/font_style_5_1_textColor"
                        tools:text="30min"
                        android:textSize="14sp"
                        />

                    <ImageView
                        android:id="@+id/ivCo2AlarmInterval"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginEnd="10dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_update_list_arrow"
                        />
                </LinearLayout>

                <include
                    android:id="@+id/gapLine61"
                    layout="@layout/h5140_gap_line"
                    android:visibility="visible"
                    tools:visibility="visible"
                    />

                <!-- 勿扰模式-->
                <LinearLayout
                    android:id="@+id/llNotDisturbMode"
                    android:layout_width="match_parent"
                    android:layout_height="70dp"
                    android:orientation="horizontal"
                    android:visibility="visible"
                    tools:visibility="visible"
                    >

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:src="@mipmap/new_sensor_setting_icon_mianrao"
                        />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:gravity="center_vertical"
                        android:text="@string/h7180_disturb_mode"
                        android:textColor="@color/font_style_81_1_textColor"
                        android:textSize="15sp"
                        />

                    <ImageView
                        android:id="@+id/ivNotDisturbModeTips"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:src="@mipmap/new_light_icon_diy_auto_what"
                        />

                    <Space
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        />


                    <FrameLayout
                        android:id="@+id/flDnDTime"
                        android:layout_gravity="center_vertical"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content">

                        <com.govee.shape.view.ShapeTextView
                            app:shape_solidColor="@color/ui_bg_style_48"
                            app:shape_radius="13dp"
                            android:layout_gravity="center_vertical"
                            android:gravity="center_vertical"
                            android:paddingEnd="21dp"
                            android:paddingStart="16dp"
                            android:id="@+id/tvNotDisturbModeTime"
                            android:layout_width="wrap_content"
                            android:layout_height="24dp"
                            android:textColor="@color/font_style_5_1_textColor"
                            tools:text="22:00-06:00"
                            />

                        <ImageView
                            android:layout_gravity="end"
                            android:layout_width="30dp"
                            android:layout_height="30dp"
                            android:src="@mipmap/new_diy_btn_color_list_arrow"/>
                    </FrameLayout>



                    <ImageView
                        android:id="@+id/ivNotDisturbMode"
                        android:layout_width="49dp"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:contentDescription="@null"
                        android:src="@drawable/component_btn_switch"
                        />

                </LinearLayout>

                <include
                    android:id="@+id/gapLine62"
                    layout="@layout/h5140_gap_line"
                    android:visibility="visible"
                    tools:visibility="visible"
                    />
                <!--C02浓度通知-->
                <LinearLayout
                    android:id="@+id/llCo2"
                    android:layout_width="match_parent"
                    android:layout_height="70dp"
                    android:orientation="horizontal"
                    android:visibility="gone"
                    tools:visibility="visible"
                    >

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:src="@mipmap/new_sensor_setting_icon_co2_notice"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:ellipsize="end"
                        android:gravity="center_vertical"
                        android:maxWidth="180dp"
                        android:maxLines="2"
                        android:text="@string/h5140_co2_density_notify"
                        android:textColor="@color/font_style_81_1_textColor"
                        android:textSize="15sp"
                        />

                    <ImageView
                        android:id="@+id/tvCo2DensityQuest"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:src="@mipmap/new_light_icon_diy_auto_what"
                        />

                    <Space
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        />

                    <ImageView
                        android:id="@+id/ivCo2DensitySwitch"
                        android:layout_width="49dp"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:contentDescription="@null"
                        android:src="@drawable/component_btn_switch"
                        />
                </LinearLayout>

                <include
                    android:id="@+id/gapLine63"
                    layout="@layout/h5140_gap_line"
                    android:visibility="visible"
                    tools:visibility="visible"
                    />

                <!-- 二氧化碳浓度等级-->
                <LinearLayout
                    android:id="@+id/llCo2LevelSet"
                    android:layout_width="match_parent"
                    android:layout_height="70dp"
                    android:alpha="0.5"
                    android:orientation="horizontal"
                    android:visibility="visible"
                    tools:visibility="visible"
                    >

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:src="@mipmap/new_sensor_setting_icon_co2_grade"
                        />


                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:gravity="center_vertical"
                        android:text="@string/h5140_co2_density"
                        android:textColor="@color/font_style_81_1_textColor"
                        android:textSize="15sp"
                        />

                    <ImageView
                        android:id="@+id/tvCo2GradeQuest"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:src="@mipmap/new_light_icon_diy_auto_what"
                        />

                    <Space
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        />

                    <ImageView
                        android:id="@+id/ivCo2GradeArrow"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="10dp"
                        android:layout_marginEnd="10dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_update_list_arrow"
                        />
                </LinearLayout>

                <include
                    android:id="@+id/gapLine7"
                    layout="@layout/h5140_gap_line"
                    android:visibility="gone"
                    tools:visibility="visible"
                    />

                <!-- 校准 -->
                <LinearLayout
                    android:id="@+id/llJiaoZhun"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="5dp"
                    android:visibility="gone"
                    tools:visibility="visible"
                    >

                    <ImageView
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="7dp"
                        android:src="@mipmap/new_sensor_setting_icon_shoudongdang"
                        />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:gravity="center_vertical"
                        android:text="@string/h5072_calibration_label"
                        android:textColor="@color/font_style_81_1_textColor"
                        android:textSize="15sp"
                        />

                    <ImageView
                        android:id="@+id/tvCalibrationQuestion"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="7dp"
                        android:src="@mipmap/new_light_icon_diy_auto_what"
                        />
                </LinearLayout>

                <!-- co2校准 -->
                <LinearLayout
                    android:id="@+id/tvCo2Calibration"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="6dp"
                    android:visibility="gone"
                    tools:visibility="visible"
                    >

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_vertical"
                        android:layout_marginStart="20dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="@string/h5140_c02_jiaozhun"
                        android:textColor="@color/font_style_81_1_textColor"
                        android:textSize="15sp"
                        />

                    <ImageView
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginEnd="10dp"
                        android:src="@mipmap/new_update_list_arrow"
                        />

                </LinearLayout>

                <!-- 温湿度校准 -->
                <com.govee.h5140.detail.setting.ThpCalibrationView
                    android:id="@+id/calibration"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_marginTopPercent="4.8%w"
                    tools:visibility="visible"
                    />

                <include
                    android:id="@+id/gapLine8"
                    layout="@layout/h5140_gap_line"
                    android:visibility="gone"
                    tools:visibility="visible"
                    />

                <!-- 屏幕显示设置 -->
                <com.govee.h5140.detail.setting.ThpOtherSetViewV2
                    android:id="@+id/tosvOtherSetV2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_marginTopPercent="4.8%w"
                    tools:visibility="visible"
                    />
            </LinearLayout>

            <TextView
                android:id="@+id/tvOther"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/prl_operated_device_container"
                android:layout_marginStart="23dp"
                android:layout_marginTop="18dp"
                android:text="@string/bbq_preset_others"
                android:textColor="@color/font_style_70_1_textColor"
                android:textSize="@dimen/font_style_70_1_textSize"
                />

            <com.govee.ui.component.GuideView
                android:id="@+id/gv_guide"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tvOther"
                android:layout_marginHorizontal="13dp"
                android:layout_marginTop="10dp"
                android:background="@drawable/component_bg_style_4"
                android:visibility="visible"
                />

            <TextView
                android:id="@+id/tvDeviceInfo"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_below="@+id/gv_guide"
                android:layout_marginStart="23dp"
                android:layout_marginTop="18dp"
                android:text="@string/device_info_label"
                android:textColor="@color/font_style_70_1_textColor"
                android:textSize="@dimen/font_style_70_1_textSize"
                />

            <include
                android:id="@+id/common"
                layout="@layout/h5140_setting_common_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/tvDeviceInfo"
                android:layout_marginTop="10dp"
                />

            <TextView
                android:id="@+id/tv_disconnect_hint"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_below="@+id/common"
                android:gravity="center"
                android:text="@string/h5072_not_found_devices"
                android:textColor="@color/font_style_28_1_textColor"
                android:textSize="@dimen/font_style_28_1_textSize"
                android:visibility="gone"
                app:layout_marginBottomPercent="9.8667%w"
                app:layout_marginTopPercent="9.8667%w"
                app:layout_paddingLeftPercent="6.1333%w"
                app:layout_paddingRightPercent="6.1333%w"
                />

            <TextView
                android:id="@+id/btn_delete_inside"
                style="@style/compoent_btn_style_1"
                android:layout_below="@+id/tv_disconnect_hint"
                android:layout_centerHorizontal="true"
                android:layout_gravity="center_horizontal"
                android:background="@drawable/component_btn_style_6"
                android:text="@string/text_delete_device"
                android:textColor="@color/ui_btn_style_6_1_text_color"
                android:textSize="@dimen/ui_btn_style_6_1_text_size"
                app:layout_marginTopPercent="8%w"
                app:layout_minWidthPercent="54.1333%w"
                app:layout_paddingLeftPercent="5.7333%w"
                app:layout_paddingRightPercent="5.7333%w"
                />
        </com.zhy.android.percent.support.PercentLinearLayout>
    </com.govee.base2home.custom.ScrollableNestedScrollView>

    <ProgressBar
        android:id="@+id/searching"
        style="@style/ProgressBarCycle"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_centerInParent="true"
        android:visibility="gone"
        app:layout_heightPercent="10.6667%w"
        app:layout_widthPercent="10.6667%w"
        />
</com.zhy.android.percent.support.PercentRelativeLayout>