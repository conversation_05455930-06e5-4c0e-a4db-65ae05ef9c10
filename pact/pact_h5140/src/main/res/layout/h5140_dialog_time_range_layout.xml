<?xml version="1.0" encoding="utf-8"?>
<layout>

    <com.zhy.android.percent.support.PercentRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/component_bg_style_2"
        >

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:text="@string/time_select_dialog_title"
            android:textColor="@color/font_style_36_1_textColor"
            android:textSize="@dimen/font_style_36_1_textSize"
            app:layout_marginBottomPercent="8.3582%w"
            app:layout_marginTopPercent="11.3433%w"
            app:layout_minHeightPercent="6.7164%w"
            app:layout_paddingLeftPercent="9.7015%w"
            app:layout_paddingRightPercent="9.7015%w"
            />

        <LinearLayout
            android:layout_below="@id/title"
            android:id="@+id/llStartTime"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            >

            <TextView
                android:id="@+id/tvStartTimeDes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:text="@string/open_time_label"
                />

            <Space
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1"
                />

            <TextView
                android:id="@+id/tvStartTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="30dp"
                android:text="00:00"
                />
        </LinearLayout>

        <LinearLayout
            android:layout_below="@id/llStartTime"
            android:id="@+id/llEndTime"
            android:layout_width="match_parent"
            android:layout_height="50dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            >

            <TextView
                android:id="@+id/tvEndTimeDes"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="30dp"
                android:text="@string/close_time_label"
                />

            <Space
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_weight="1"
                />

            <TextView
                android:id="@+id/tvEndTime"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="30dp"
                android:text="00:00"
                />
        </LinearLayout>

        <TextView
            android:id="@+id/center_flag"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_centerHorizontal="true"
            />

        <TextView
            android:id="@+id/center_bg_left"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_alignStart="@+id/hour_picker"
            android:layout_alignTop="@+id/center_line"
            android:layout_alignEnd="@+id/min_picker"
            android:layout_alignBottom="@+id/center_line"
            android:background="@drawable/component_bg_choose_time_showing"
            app:layout_widthPercent="40.7463%w"
            />


        <TextView
            android:id="@+id/center_line"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_alignStart="@+id/hour_picker"
            android:layout_alignTop="@+id/hour_picker"
            android:layout_alignEnd="@+id/min_picker"
            android:gravity="center"
            android:text="@string/b2light_time_separated"
            android:textColor="@color/font_style_36_3_textColor"
            android:textSize="@dimen/font_style_36_3_textSize"
            app:layout_heightPercent="10.7463%w"
            app:layout_marginTopPercent="21.4925%w"
            />

        <com.aigestudio.wheelpicker.WheelPicker
            android:id="@+id/am_picker"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="gone"
            app:wheel_data="@array/time_format_am"
            app:wheel_selected_item_position="0"
            app:wheel_visible_item_count="5"
            />

        <cn.carbswang.android.numberpickerview.library.NumberPickerView
            android:id="@+id/hour_picker"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_below="@+id/llEndTime"
            android:layout_toStartOf="@+id/center_flag"
            android:contentDescription="@null"
            app:layout_heightPercent="53.7313%w"
            app:layout_marginBottomPercent="8.9552%w"
            app:layout_widthPercent="40.7463%w"
            app:npv_DividerColor="@color/ui_split_line_style_9"
            app:npv_ItemPaddingVertical="14dp"
            app:npv_RespondChangeOnDetached="false"
            app:npv_ShowCount="5"
            app:npv_ShowDivider="true"
            app:npv_TextArray="@array/hour_display"
            app:npv_TextColorNormal="@color/font_style_36_2_textColor"
            app:npv_TextColorSelected="@color/font_style_36_3_textColor"
            app:npv_TextSizeNormal="@dimen/font_style_36_2_textSize"
            app:npv_TextSizeSelected="@dimen/font_style_36_3_textSize"
            app:npv_WrapSelectorWheel="true"
            />

        <cn.carbswang.android.numberpickerview.library.NumberPickerView
            android:id="@+id/min_picker"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_below="@+id/llEndTime"
            android:layout_toEndOf="@+id/center_flag"
            android:contentDescription="@null"
            app:layout_heightPercent="53.7313%w"
            app:layout_marginBottomPercent="8.9552%w"
            app:layout_widthPercent="40.7463%w"
            app:npv_DividerColor="@color/ui_split_line_style_9"
            app:npv_ItemPaddingVertical="14dp"
            app:npv_RespondChangeOnDetached="false"
            app:npv_ShowCount="5"
            app:npv_ShowDivider="true"
            app:npv_TextArray="@array/minute_display"
            app:npv_TextColorNormal="@color/font_style_36_2_textColor"
            app:npv_TextColorSelected="@color/font_style_36_3_textColor"
            app:npv_TextSizeNormal="@dimen/font_style_36_2_textSize"
            app:npv_TextSizeSelected="@dimen/font_style_36_3_textSize"
            app:npv_WrapSelectorWheel="true"
            />

        <TextView
            android:id="@+id/btn_cancel"
            style="@style/compoent_btn_style_2"
            android:layout_below="@+id/hour_picker"
            android:background="@drawable/component_btn_style_5"
            android:text="@string/cancel"
            android:textColor="@color/ui_btn_style_5_1_text_color"
            android:textSize="@dimen/ui_btn_style_5_1_text_size"
            app:layout_marginLeftPercent="5.5224%w"
            app:layout_marginStartPercent="5.5224%w"
            app:layout_widthPercent="44.1791%w"
            />

        <TextView
            android:id="@+id/btn_done"
            style="@style/compoent_btn_style_2"
            android:layout_below="@+id/min_picker"
            android:layout_alignParentEnd="true"
            android:background="@drawable/component_btn_style_3"
            android:text="@string/done"
            android:textColor="@color/ui_btn_style_3_1_text_color"
            android:textSize="@dimen/ui_btn_style_3_1_text_size"
            app:layout_heightPercent="19.403%w"
            app:layout_marginEndPercent="5.5224%w"
            app:layout_marginRightPercent="5.5224%w"
            app:layout_widthPercent="44.1791%w"
            />
    </com.zhy.android.percent.support.PercentRelativeLayout>
</layout>
