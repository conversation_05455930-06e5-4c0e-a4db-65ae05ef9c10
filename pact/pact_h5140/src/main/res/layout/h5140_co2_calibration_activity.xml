<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:id="@+id/ac_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/ui_bg_color_style_2"
        >

        <View
            android:id="@+id/topFlag"
            android:layout_width="0dp"
            android:layout_height="1px"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            android:id="@+id/tv4Title"
            android:layout_width="0dp"
            android:layout_height="34dp"
            android:layout_marginTop="30.5dp"
            android:gravity="center"
            android:text="@string/h5072_calibration_label"
            android:textColor="@color/font_style_105_textColor"
            android:textSize="@dimen/font_style_105_textSize"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/topFlag"
            />

        <ImageView
            android:id="@+id/iv4Back"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="12dp"
            android:background="@drawable/component_btn_style_23"
            android:contentDescription="@null"
            android:padding="5dp"
            android:src="@mipmap/new_sensor_setting_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="@+id/tv4Title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv4Title"
            />

        <ImageView
            android:id="@+id/ivIcon"
            android:layout_width="175dp"
            android:layout_height="150dp"
            android:layout_marginTop="45dp"
            android:src="@mipmap/h5140_pics_co2_calibration"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/iv4Back"
            />

        <TextView
            android:id="@+id/tvNotic"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="26dp"
            android:layout_marginTop="30dp"
            android:text="@string/h8121_pre_cool_content_4"
            android:textColor="@color/font_style_243_3_textColor"
            android:textSize="14sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/ivIcon"
            />

        <TextView
            android:id="@+id/tvTips1"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="23dp"
            android:layout_marginTop="8dp"
            android:text="@string/h5140_co2_tips_1"
            android:textColor="@color/font_style_4_1_textColor"
            android:textSize="14sp"
            app:layout_constraintTop_toBottomOf="@+id/tvNotic"
            />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="23dp"
            android:layout_marginTop="16dp"
            android:text="@string/h5140_co2_tips_2"
            android:textColor="@color/font_style_4_1_textColor"
            android:textSize="14sp"
            app:layout_constraintTop_toBottomOf="@+id/tvTips1"
            />

        <TextView
            android:id="@+id/tvReadNote"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:text="@string/h5140_please_read_note"
            android:textColor="@color/font_style_5_1_textColor"
            app:layout_constraintBottom_toTopOf="@+id/btnDone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            />

        <TextView
            android:id="@+id/btnDone"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:layout_marginHorizontal="23dp"
            android:layout_marginBottom="20dp"
            android:alpha="0.5"
            android:background="@drawable/component_btn_style_3"
            android:ellipsize="end"
            android:enabled="false"
            android:gravity="center"
            android:lines="2"
            android:paddingHorizontal="15dp"
            android:paddingBottom="20dp"
            android:text="@string/h5140_please_read_note"
            android:textColor="@color/ui_btn_style_3_1_text_color"
            android:textSize="@dimen/ui_btn_style_3_1_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_marginEndPercent="5.5224%w"
            app:layout_marginRightPercent="5.5224%w"
            app:layout_widthPercent="44.1791%w"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
