<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    tools:ignore="ResourceName">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/component_bg_style_2">

        <TextView
            android:id="@+id/tv_title_4_volume_gear"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="38dp"
            android:gravity="center"
            android:paddingBottom="12dp"
            android:text="@string/h5140_alarm_interval"
            android:textColor="@color/font_style_16_1_textColor"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_remind_text_4_volume_gear"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center_vertical"
            android:paddingTop="2dp"
            android:text=""
            android:textColor="@color/font_style_11_2_textColor"
            android:textSize="12sp"
            android:visibility="gone"
            app:layout_constraintTop_toBottomOf="@+id/tv_title_4_volume_gear" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="36dp"
            android:background="@drawable/h512x_selected_time_bg"
            android:gravity="center_vertical"
            android:text=""
            android:textColor="@color/font_style_36_7_textColor"
            android:textSize="@dimen/font_style_36_7_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/wpValue4VolumeGear"
            app:layout_constraintTop_toTopOf="@+id/wpValue4VolumeGear" />

        <com.aigestudio.wheelpicker.WheelPicker
            android:id="@+id/wpValue4VolumeGear"
            android:layout_width="match_parent"
            android:layout_height="184dp"
            android:layout_marginTop="16dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_remind_text_4_volume_gear"
            app:wheel_cyclic="false"
            app:wheel_indicator_color="@color/font_style_36_3_textColor"
            app:wheel_indicator_size="0.5dp"
            app:wheel_item_align="center"
            app:wheel_item_text_color="@color/font_style_36_2_textColor"
            app:wheel_item_text_size="16sp"
            app:wheel_selected_item_position="3"
            app:wheel_selected_item_text_color="@color/font_style_36_3_textColor"
            app:wheel_visible_item_count="5" />

        <TextView
            android:id="@+id/tv_cancel_4_volume_gear"
            android:layout_width="148dp"
            android:layout_height="55dp"
            android:layout_marginStart="24dp"
            android:layout_marginTop="30dp"
            android:background="@drawable/component_btn_style_5_1"
            android:gravity="center"
            android:paddingBottom="10dp"
            android:text="@string/cancel"
            android:textColor="@color/ui_btn_style_5_1_text_color"
            android:textSize="@dimen/ui_btn_style_3_1_text_size"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/wpValue4VolumeGear" />

        <TextView
            android:id="@+id/tv_sure_4_volume_gear"
            android:layout_width="148dp"
            android:layout_height="55dp"
            android:layout_marginEnd="24dp"
            android:background="@drawable/component_btn_style_3_1"
            android:gravity="center"
            android:paddingBottom="10dp"
            android:text="@string/confirm"
            android:textColor="@color/ui_btn_style_3_1_text_color"
            android:textSize="@dimen/ui_btn_style_3_1_text_size"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tv_cancel_4_volume_gear" />

        <View
            android:layout_width="match_parent"
            android:layout_height="10dp"
            app:layout_constraintTop_toBottomOf="@+id/tv_cancel_4_volume_gear" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
