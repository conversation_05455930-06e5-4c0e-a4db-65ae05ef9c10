package com.govee.h5140.item

import com.govee.base2home.pact.BleUtil
import com.govee.base2home.pact.BleUtil.getUnsignedInt
import com.govee.h5140.iotop.IotParseUtils

/**
 * <AUTHOR>
 * @date created on 2022/5/25
 * @description sku=5106(带pm2.5显示的温湿度计)的广播解析工具类
 */
class ThpBroadcastUtil {

    companion object {

        private const val TAG = "ThpBroadcastUtil"

        /**
         * 解析h5140的广播包数据
         */
        fun parseH5140Broadcast(broadcastData: ByteArray): IntArray {
            val map = BleUtil.parseBleAdvertisement(broadcastData)
            var checkTag = true
            // 检查标志位 0x03 0x88 0xEC
            map.forEach { (key: Int?, value: ByteArray?) ->
                if (key == 3 && value!!.size == 3) {
                    if (value[0] == BleUtil.ble_broadcast_service_flag && value[1] == BleUtil.ble_govee_flag[0] && value[2] == BleUtil.ble_govee_flag[1]) {
                        checkTag = true
                    }
                }
            }
            for (key in map.keys) {
                val value = map[key]
                if (key == 11 && value != null && value.size == 11 && value[0] == BleUtil.ble_broadcast_self_filling_flag) {
                    // 解析最低4位为整数值
                    val pactType = getUnsignedInt(value[2], value[3])
                    val pactCode = BleUtil.getUnsignedByte(value[4])
                    val temHumByteArray = ByteArray(3)
                    temHumByteArray[0] = value[5]
                    temHumByteArray[1] = value[6]
                    temHumByteArray[2] = value[7]
                    val temHum = BleUtil.parseThValue(temHumByteArray)// 解析温湿度
                    val co2 = IotParseUtils.parse2ByteToInt(value[8], value[9])
                    val result = intArrayOf(pactType, pactCode, temHum[0], temHum[1], co2)
                    return result
                }
            }
            return intArrayOf(5)
        }
    }
}