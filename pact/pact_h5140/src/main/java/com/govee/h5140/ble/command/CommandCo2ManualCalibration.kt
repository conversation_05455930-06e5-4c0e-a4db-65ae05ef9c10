package com.govee.h5140.ble.command

import com.govee.base2newth.AbsSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventCo2ManualCalibration

/**
 * 二氧化碳手动校准
 */
class CommandCo2ManualCalibration : AbsSingleController {

    companion object {
        private const val TAG = "CommandCo2ManualCalibration"
    }

    /**
     * 写操作
     */
    constructor(isWrite: Boolean) : super(isWrite) {

    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray? {
        return byteArrayOf()
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventCo2ManualCalibration.sendWriteResult(suc,commandType,proType)
        return true
    }

    override fun fail() {
        EventCo2ManualCalibration.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_CO2_ADJUST
    }

    override fun parseValidBytes(validBytes: ByteArray?): Boolean {
        EventCo2ManualCalibration.sendSuc(isWrite, commandType, proType)
        return true
    }
}