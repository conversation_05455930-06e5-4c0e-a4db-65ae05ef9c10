package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsOnlyReadSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventSoftVersion

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取设备软件版本的指令，并解析其回复
 */
class CommandSoftVersion : AbsOnlyReadSingleController() {
    override fun fail() {
        EventSoftVersion.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_SOFT_VERSION
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val softVersion = BleUtil.getStrData(validBytes)
        EventSoftVersion.sendSuc(isWrite, commandType, proType, softVersion)
        return true
    }
}