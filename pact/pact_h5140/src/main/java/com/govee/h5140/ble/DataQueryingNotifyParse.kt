package com.govee.h5140.ble

import com.govee.base2newth.AbsNotifyParse
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.base2newth.data.controller.EventDataQuerying

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 5106设备数据查询中主动上报解析器
 */
class DataQueryingNotifyParse(private val serviceUuid: String, private val characteristicUuid: String) : AbsNotifyParse() {
    override fun checkCharacteristic(serviceUUID: String, characteristicUUID: String): Boolean {
        return serviceUuid == serviceUUID && characteristicUuid == characteristicUUID
    }

    override fun getSubCommandType(): Byte {
        return BleProtocol.value_notify_data_querying
    }

    override fun parseBytes(bytes17: ByteArray) {
        EventDataQuerying.sendEventDataQuerying()
    }
}