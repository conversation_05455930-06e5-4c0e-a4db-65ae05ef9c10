package com.govee.h5140.detail.square

import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.govee.base2home.Constant4L5
import com.govee.base2newth.chart.TimeUtil
import com.govee.base2newth.data.ExtV1
import com.govee.base2newth.db.DbController
import com.govee.base2newth.db.TemHum
import com.govee.ui.R
import com.govee.util.SkuUtil
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit

/**
 * Create by yu on 2022/8/31
 *
 */
class SquareViewModel : ViewModel() {

    val TAG = SquareViewModel::class.simpleName


    //intent接收参数
    var ext: ExtV1? = null

    var thDataLiveData = MutableLiveData<List<TemHum>>()

    // 时间筛选条件数据源
    val menuTimeList = listOf<String>(
        ResUtil.getString(R.string.screen_time_hour),
        ResUtil.getString(R.string.screen_time_day),
        ResUtil.getString(R.string.screen_time_month)
    )

    // 值筛选条件数据源
    val menuValueList = listOf<String>(
        ResUtil.getString(R.string.screen_value_max),
        ResUtil.getString(R.string.screen_value_min),
        ResUtil.getString(R.string.screen_value_average)
    )

    // 当前选中的筛选字符串
    var selectMenuTimeStr = menuTimeList[0]
    var selectMenuValueStr = menuValueList[0]

    /**
     * 筛选时间范围
     */
    val currentTimeMillis = TimeUtil.getCorrectHour(System.currentTimeMillis())

    //默认为近7个小时
    var startTimeStamp = currentTimeMillis - TimeUnit.HOURS.toMillis(6)
    var endTimeStamp = currentTimeMillis

    fun queryAllData(sku: String, device: String, startTime: Long, endTime: Long) {
        if (sku == Constant4L5.H5140) {
            queryH5140AllData(sku, device, startTime, endTime)
            return
        }
        viewModelScope.launch(Dispatchers.IO) {
            val list = DbController.queryAllDataEqual(
                sku,
                device,
                startTime,
                endTime + TimeUnit.HOURS.toMillis(1)
            )
            var array: Array<Int>
            val temCali = ext?.temCali ?: 0
            val humCali = ext?.humCali ?: 0
            list?.forEach {
                array = SkuUtil.calculationDewP2Vpd(it.tem, it.hum, temCali, humCali)
                it.tem += temCali
                it.hum += humCali
                it.dewPoint = array[0]
                it.vpd = array[1]
            }
            thDataLiveData.postValue(list)
        }
    }

    private fun queryH5140AllData(sku: String, device: String, startTime: Long, endTime: Long) {
        viewModelScope.launch(Dispatchers.IO) {
            try {
                val list = DbController.queryAllData4ThpEqual(
                    sku,
                    device,
                    startTime,
                    endTime + TimeUnit.HOURS.toMillis(1)
                )
                var array: Array<Int>
                val temCali = ext?.temCali ?: 0
                val humCali = ext?.humCali ?: 0
                list?.forEach {
                    array = SkuUtil.calculationDewP2Vpd(it.tem, it.hum, temCali, humCali)
                    it.tem += temCali
                    it.hum += humCali
                    it.dewPoint = array[0]
                    it.vpd = array[1]
                }
                //val filterList = list.filter { it.pm25 != 65535 }
                val finalList = mutableListOf<TemHum>()
                list.forEach {
                    val temHum = TemHum()
                    temHum.tem = it.tem
                    temHum.hum = it.hum
                    temHum.time = it.time
                    temHum.from = it.from
                    temHum.dewPoint = it.dewPoint
                    temHum.vpd = it.vpd
                    temHum.pm25 = it.pm25
                    finalList.add(temHum)
                }
                thDataLiveData.postValue(finalList)
            } catch (e: Exception) {
                e.printStackTrace()
            }
        }
    }
}
