package com.govee.h5140.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.widget.SeekBar
import android.widget.SeekBar.OnSeekBarChangeListener
import android.widget.TextView
import com.govee.base2kt.ext.clickDelay
import com.govee.ble.BleController
import com.govee.h5140.databinding.H5140Co2LevelSettingDialogBinding
import com.govee.h5140.detail.setting.Activity4Setting
import com.govee.h5140.widget.TransparentSeekBar
import com.ihoment.base2app.dialog.BaseDialog
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.ToastUtil
import kotlin.math.max
import kotlin.math.min

/**
 * @author：YangQi.Chen
 * @date：2025/4/11 上午11:10
 * @description：Co2浓度等级设置
 */
class Co2LevelSettingDialog(context: Context, private val minGrade: Int, private val maxGrade: Int) : BaseDialog(context) {
    private lateinit var binding: H5140Co2LevelSettingDialogBinding
    private var minLevel = MIN_VALUE
    private var maxLevel = MAX_VALUE

    init {
        changeDialogOutside(false)
        immersionMode()
    }

    companion object {
        private const val MIN_VALUE = 400
        private const val MAX_VALUE = 2000
        private const val DEFAULT_MIN = 1000
        private const val DEFAULT_MAX = 1400
    }

    @SuppressLint("SetTextI18n")
    override fun getLayoutView(): View {
        binding = H5140Co2LevelSettingDialogBinding.inflate(LayoutInflater.from(context))
        binding.btnCancel.clickDelay { hide() }
        binding.btnDone.clickDelay {
            val bleConnect = BleController.getInstance().isConnected
            val iotOnline = Activity4Setting.iotOnline //Iot.getInstance.isConnected()
            if (!bleConnect && !iotOnline) {
                ToastUtil.getInstance().toast(com.govee.ui.R.string.h5140_toast_connect_device)
                return@clickDelay
            }
            mSetLevelListener?.onSetLevel(minLevel, maxLevel)
            hide()
        }
        binding.tvRestoreLevel.clickDelay {
            setRange(DEFAULT_MIN, DEFAULT_MAX)
        }
        initSeekBar()
        minLevel = minGrade
        maxLevel = maxGrade

        val standStr = ResUtil.getString(com.govee.ui.R.string.h5140_co2_standard)
        binding.tvLevel1Tips.text = "（${standStr}：400-1000）"
        binding.tvLevel2Tips.text = "（${standStr}：1000-1400）"
        binding.tvLevel3Tips.text = "（${standStr}：≥1400）"
        return binding.root
    }

    /**
     * 设置范围
     */
    fun setRange(minRang: Int, maxRange: Int) {
        binding.seekBarRight.progress = min(maxRange, 2000) / 100
        Handler(Looper.getMainLooper()).postDelayed({
            binding.seekBarLeft.progress = max(minRang, 400) / 100
            updateProgressText(binding.tvMinLevel, binding.seekBarLeft)
            updateProgressText(binding.tvMaxLevel, binding.seekBarRight)
        }, 20)
    }

    @SuppressLint("SetTextI18n")
    private fun initSeekBar() {
        binding.run {
            seekBarLeft.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
                override fun onProgressChanged(p0: SeekBar?, progress: Int, p2: Boolean) {
                    if (progress >= seekBarRight.progress) {
                        seekBarLeft.progress = progress - 1
                    }
                    updateProgressText(tvMinLevel, seekBarLeft)

                    Handler(Looper.getMainLooper()).postDelayed({
                        minLevel = seekBarLeft.progress * 100
                    }, 100)
                }

                override fun onStartTrackingTouch(p0: SeekBar?) {
                }

                override fun onStopTrackingTouch(p0: SeekBar?) {
                }
            })
            seekBarRight.setOnSeekBarChangeListener(object : OnSeekBarChangeListener {
                override fun onProgressChanged(p0: SeekBar?, progress: Int, p2: Boolean) {
                    if (progress <= seekBarLeft.progress) {
                        seekBarRight.progress = progress + 1
                    }
                    updateProgressText(tvMaxLevel, seekBarRight)
                    Handler(Looper.getMainLooper()).postDelayed({
                        maxLevel = seekBarRight.progress * 100
                    }, 100)
                }

                override fun onStartTrackingTouch(p0: SeekBar?) {
                }

                override fun onStopTrackingTouch(p0: SeekBar?) {
                }
            })
        }
    }

    @SuppressLint("SetTextI18n")
    fun updateProgressText(
        textView: TextView,
        seekBar: TransparentSeekBar
    ) {
        val targetX = seekBar.getThumbPosX()
        // 计算TextView的宽度
        val textWidth = textView.width
        // 计算TextView需要设置的left位置（使其中点对齐targetX）
        val newLeft = targetX - textWidth / 2
        textView.translationX = max(0, newLeft).toFloat()
        checkTvPosition()
        textView.text = (seekBar.progress * 100).toString()
        // 更新底部文案范围
        updateRangeTv()
    }

    @SuppressLint("SetTextI18n")
    fun updateRangeTv() {
        val maxLevel: Int = binding.seekBarRight.progress * 100
        val minLevel: Int = binding.seekBarLeft.progress * 100
        binding.tvLevel1.text = "${ResUtil.getString(com.govee.ui.R.string.h5140_co2_superior)} 400-${minLevel}"
        binding.tvLevel2.text = "${ResUtil.getString(com.govee.ui.R.string.h5140_co2_good)} ${minLevel}-${maxLevel}"
        binding.tvLevel3.text = "${ResUtil.getString(com.govee.ui.R.string.h5140_co2_poor)} ≥${maxLevel}"
    }

    /**
     * 检查两个TextView是否重叠
     */
    private fun checkTvPosition() {
        val tvMaxLeft = binding.tvMaxLevel.translationX
        val tvMinRight = binding.tvMinLevel.translationX + binding.tvMinLevel.width
        if (tvMaxLeft < tvMinRight) {
            binding.tvMaxLevel.translationX = tvMinRight
        }
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth() * 335 / 375
    }

    private var mSetLevelListener: OnSetLevelListener? = null

    fun setOnSetLevelListener(listener: OnSetLevelListener) {
        this.mSetLevelListener = listener
    }

    interface OnSetLevelListener {
        fun onSetLevel(minRange: Int, maxRange: Int)
    }
}