package com.govee.h5140.iotop

/**
 * <AUTHOR>
 * @date created on 2022/06/15
 * @description 5106设备iot指令相关常量
 */
object Cmd5140 {

    const val status = "status"
    const val ptReal = "ptReal"

    const val parse_json_op = "op"
    const val parse_json_data = "data"

    fun getCmdReadParseKey(cmd: String): String {
        return if ((status == cmd)) {
            parse_json_op
        } else parse_json_data
    }
}