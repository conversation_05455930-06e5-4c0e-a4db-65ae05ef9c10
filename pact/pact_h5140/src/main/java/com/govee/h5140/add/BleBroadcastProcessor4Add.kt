package com.govee.h5140.add

import android.app.Activity
import com.govee.base2home.main.choose.BaseBleDeviceModel
import com.govee.base2home.main.choose.BaseBleProcessor
import com.govee.base2home.sku.DefaultDeviceNameUtil
import com.govee.base2kt.ext.toJson
import com.govee.h5140.item.Support
import com.ihoment.base2app.infra.SafeLog

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 通过广播包中的goodsType,协议类型等判断是否对该设备的支持，并进行后续处理
 */
class BleBroadcastProcessor4Add : BaseBleProcessor() {

    /**
     * 添加设备的ProductAc中点击SkuItem的点击事件的最终处理处
     */
    override fun onItemClick(
        activity: Activity,
        model: BaseBleDeviceModel,
        singleSku: Boolean
    ): Boolean {
        val goodsType = model.goodsType
        val protocol = model.protocol
        //是否支持协议处理;需要登录检测
        if (Support.supportPact(goodsType, protocol)) {
            val checkLogin = checkLogin(activity)
            if (checkLogin) {
                return true
            }
            val addInfo = AddInfo(goodsType, model.sku, model.bleName, model.device.address).apply {
                protocol?.let {
                    pactType = it.pactType
                    pactCode = it.pactCode
                }
            }
            addInfo.deviceName = "Air Quality Monitor"
            addInfo.deviceName =
                DefaultDeviceNameUtil.getSkuDefaultName(model.sku, addInfo.deviceName)
            //dialog，在调用show()方法后会触发onShow()回调方法，连接指令在此处调用
            ConnectDialog.createDialog(activity, model.device, addInfo).show()
            return true
        }
        return false
    }
}