package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读写屏幕显示pm2.5or时间的事件
 */
class EventDisplay : AbsControllerEvent {

    constructor(result: <PERSON><PERSON><PERSON>, write: Boolean, commandType: Byte, proType: Byte) : super(result, write, commandType, proType, !write)

    /**
     * 显示pm2.5or时间
     */
    var sdType = 0

    companion object {
        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte, sdType: Int) {
            val event = EventDisplay(result, true, commandType, proType)
            event.sdType = sdType
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventDisplay(false, write, commandType, proType))
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, sdType: Int) {
            val event = EventDisplay(true, write, commandType, proType)
            event.sdType = sdType
            EventBus.getDefault().post(event)
        }
    }
}