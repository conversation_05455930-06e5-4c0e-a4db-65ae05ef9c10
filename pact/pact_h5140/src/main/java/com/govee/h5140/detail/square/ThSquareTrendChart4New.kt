package com.govee.h5140.detail.square

import android.annotation.SuppressLint
import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.graphics.Rect
import android.text.TextUtils
import android.util.AttributeSet
import android.view.MotionEvent
import android.view.View
import android.view.ViewParent
import androidx.core.graphics.toColorInt
import com.govee.base2home.util.DisplayUtils
import com.govee.base2home.util.NumberUtil
import com.govee.base2home.util.StrUtil
import com.govee.base2home.util.TimeFormatM
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.IChartView
import com.govee.base2newth.chart.IndicatorDrawable
import com.govee.base2newth.chart.IntervalType
import com.govee.base2newth.chart.TimeUtil
import com.govee.base2newth.chart.square.ISquareYTestChangeListener
import com.govee.base2newth.chart.square.SquareChartPM25Controller
import com.govee.base2newth.chart.square.newsquare.SquareChartController4New
import com.govee.base2newth.db.TemHum
import com.govee.ui.R
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.skinv2.ISkinView
import com.ihoment.base2app.util.ResUtil
import com.tk.mediapicker.utils.DensityUtil
import kotlin.math.abs
import kotlin.math.roundToInt

/**
 * Create by yu on 2022/8/25
 *  柱状图
 */
@SuppressLint("CustomViewStyleable")
class ThSquareTrendChart4New @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) :
    View(context, attrs, defStyleAttr), IChartView, ISkinView {

    companion object {
        val INVALID_POINT: Int = -1 /*无效的坐标点的值*/
    }

    /*自定义属性*/
    private var baseLineColor: Int = -0x45413f /*范围基线的颜色*/
    private var baseLineWidth = 1 /*范围基线的高度*/
    private var desTextColor = -0x9b9b9c /*x轴/y轴文本颜色*/
    private var desUnSelectTextColor = -0x9b9b9c /*x轴/y轴文本颜色*/
    private var desTextSize = 12 /*x轴/y轴文本大小*/
    private var defTextStr = "" /*默认数据未准备安成的字符串 如 --.-*/
    private var pointInTimeTextColor = -0x1 /*时间点文本颜色*/
    private var pointInTimeTextSize = 12 /*时间点文本大小*/
    private var squareDefColor = -0xff5319 /*矩形图默认颜色*/
    private var squareSelColor = -0x66210b /*矩形图选中颜色*/

    private var squareRadius = 12 /*矩形宽度*/
    private var deviationClickWidth = 12 /*点击范围的偏移量*/
    private var rangeTextVerticalSpace = 8/*时间范围文字和X轴文字的间距*/
    private var rangeTextColor = -0x9b9b9c/*时间范围文字颜色*/
    private var rangeTextSize = 14 /*时间范围文字大小*/
    private var curveLineColor = -0xc55801 /*曲线/矩形颜色*/
    private var curveLineWidth = 1 /*曲线/矩形的宽度*/
    private var intervalTextVerticalSpace = 4 /*X轴文字距bottombaseline的间距*/
    var textSuffix = "" /*文本单位后缀 如°C*/
    var chartModelType = TemHum.Type.tem //  tem, hum, dewP, vpd, pm25

    private var intervalLineWidth = 1 /*指示线宽度*/
    private var pointInTimeTextHeight = 0 // 指示器文字高度
    private var pointInTimeTextBgDrawable: IndicatorDrawable/*指示器drawable*/
    private var pointInTimeTextPaddingTop = 4 /*指示器垂直top方向padding*/
    private var pointInTimeTextPaddingBottom = 4 /*指示器垂直bottom方向padding*/
    private var pointInTimeTextPaddingHorizontal = 3 /*指示器水平方向padding*/

    private var maxShowValue = 0f  // 根据数据计算下的最大值
    private var minShowValue = 0f  // 根据数据计算下的最小值
    var maxDefYValue = 150f /*无数据情况下默认最大值*/
    var minDefYValue = 0f    /*无数据情况下默认最小值*/

    /*Paint操作属性*/
    private var baseLinePaint: Paint // 基线画笔
    private var desTextPaint: Paint // x轴y轴字体画笔
    private var squareLinePaint: Paint // 曲线/矩形画笔
    private var pointInTimeTextPaint: Paint // 点击指示器画笔
    private var rangeTextPaint: Paint // 时间范围字体画笔
    private var curveLinePaint: Paint // pm25背景画笔

    /*绘制相关属性*/
    private val VERTICAL_OFFSET = 3 /*垂直方向偏移量*/
    private val HORIZONTAL_OFFSET = 0 /*水平方向左边偏移量*/
    private val DIVISION_LINE_COUNT = 2 /*Y轴分割线的数量（这里的线不包含顶部基线和底步基线）*/
    private val subscriptHeight = 10 /*指示标的高度*/
    private val subscriptVertical = 3 /*指示标距离基线的距离*/
    private val MIN_VAlUE_HEIGTH = DisplayUtils.dp2px(context, 1f) /*最低值显示默认高度*/

    /**
     * 字体相关
     */
    private var desTextHeight = 0//Y轴和X轴字体高度大小
    private var desTextBaseline = 0 //X轴字体基线大小
    private var rangeTextHeight = 0//范围描述文字字体高度大小（可滚动的时间轴）
    private var rangTextBaseline = 0 //范围描述文字字体基线大小（可滚动的时间轴）

    /*计算结果值*/
    private var baselineX = 0 // 基线的X坐标
    private var baselinesInterval = 0 // 俩条基线之间的长度
    private var topBaselineY = 0 // 顶部基线的Y坐标
    private var bottomBaselineY = 0 // 底部基线的Y坐标
    private var pointSelectX = 0 // 当前选中指针位置X坐标
    private var pointInTimeStr = "" //指针位置文本

    private var chart: SquareChartController4New? = null
    private var squareYTestChange: ISquareYTestChangeListener? = null

    private var caliValue = 0  /*校准值*/
    private var isFahOpen = false   /*华氏度和摄氏度切换开关*/
    private var pointValue = 2 /*Y轴坐标 默认保留小数点后两位*/
    private var maxMinAvgEqual = false /*最大值最小值平均值相同*/
    private var pointTimeStamp = 0L  /*点击点所在的时间*/

    protected var co2MinLevel = 400
    private var co2MaxLevel = 600

    private val pm25Colors = intArrayOf(
        //绿
        "#2601CC00".toColorInt(),
        //黄
        "#26FFDB3A".toColorInt(),
        //红
        "#26FF3434".toColorInt(),
    )

    init {
        /*开启软件加速，则绘制Shape虚线不会在4.0手机上显示为实线*/
        this.setLayerType(LAYER_TYPE_SOFTWARE, null)

        val ta =
            getContext().obtainStyledAttributes(attrs, com.govee.base2home.R.styleable.ThTrendChart)
        baseLineColor = ta.getColor(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_baseLineColor,
            baseLineColor
        )
        baseLineWidth = ta.getDimensionPixelSize(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_baseLineWidth,
            baseLineWidth
        )

        desTextColor = ta.getColor(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_desTextColor,
            desTextColor
        )
        desUnSelectTextColor = ta.getColor(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_desUnSelectTextColor,
            desUnSelectTextColor
        )

        desTextSize = ta.getDimensionPixelSize(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_desTextSize,
            desTextSize
        )

        val textSuffixCS =
            ta.getText(com.govee.base2home.R.styleable.ThTrendChart_thChart_textSuffix)
        textSuffix = textSuffixCS?.toString() ?: ""
        val defTextStrCS =
            ta.getText(com.govee.base2home.R.styleable.ThTrendChart_thChart_defTextStr)
        defTextStr = defTextStrCS?.toString() ?: ""

        curveLineColor = ta.getColor(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_curveLineColor,
            curveLineColor
        )
        curveLineWidth = ta.getDimensionPixelSize(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_curveLineWidth,
            curveLineWidth
        )

        squareDefColor = ta.getColor(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_squareColor,
            squareDefColor
        )
        squareSelColor = ta.getColor(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_squareSelectColor,
            squareSelColor
        )
        squareRadius = ta.getDimensionPixelSize(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_squareRadius,
            squareRadius
        )

        pointInTimeTextColor = ta.getColor(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_pointInTimeTextColor,
            pointInTimeTextColor
        )
        pointInTimeTextSize = ta.getDimensionPixelSize(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_pointInTimeTextSize,
            pointInTimeTextSize
        )
        pointInTimeTextPaddingTop = ta.getDimensionPixelSize(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_pointInTimeTextPaddingTop,
            pointInTimeTextPaddingTop
        )
        pointInTimeTextPaddingBottom = pointInTimeTextPaddingTop
        pointInTimeTextPaddingHorizontal = ta.getDimensionPixelSize(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_pointInTimeTextPaddingHorizontal,
            pointInTimeTextPaddingHorizontal
        )

        intervalLineWidth = ta.getDimensionPixelSize(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_intervalLineWidth,
            intervalLineWidth
        )
        intervalTextVerticalSpace = ta.getDimensionPixelSize(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_intervalTextVerticalSpace,
            intervalTextVerticalSpace
        )
        rangeTextVerticalSpace = ta.getDimensionPixelSize(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_rangeTextVerticalSpace,
            rangeTextVerticalSpace
        )

        rangeTextColor = ta.getColor(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_rangeColor,
            rangeTextColor
        )
        rangeTextSize = ta.getDimensionPixelSize(
            com.govee.base2home.R.styleable.ThTrendChart_thChart_rangeTextSize,
            rangeTextSize
        )
        ta.recycle()

        //pm2.5的色块颜色
        pm25Colors[0] = ResUtil.getColor(R.color.ui_color_block_style_48_color_green)
        pm25Colors[1] = ResUtil.getColor(R.color.ui_color_block_style_48_color_yellow)
        pm25Colors[2] = ResUtil.getColor(R.color.ui_color_block_style_48_color_red)

        baselinesInterval = DensityUtil.dp2px(context, 140.0f)
        val dashWidth = DensityUtil.dp2px(context, 2.5f) * 1.0f
        val dashGap = DensityUtil.dp2px(context, 1.5f) * 1.0f
        /**
         * 初始化画笔
         */
        baseLinePaint = Paint(Paint.ANTI_ALIAS_FLAG)
        baseLinePaint.color = baseLineColor
        baseLinePaint.strokeWidth = baseLineWidth.toFloat()
        baseLinePaint.setPathEffect(DashPathEffect(floatArrayOf(dashWidth, dashGap), 0.0f))

        desTextPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        desTextPaint.flags = Paint.FAKE_BOLD_TEXT_FLAG
        desTextPaint.color = desTextColor
        desTextPaint.textSize = desTextSize.toFloat()

        rangeTextPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        rangeTextPaint.flags = Paint.FAKE_BOLD_TEXT_FLAG
        rangeTextPaint.color = rangeTextColor
        rangeTextPaint.textSize = rangeTextSize.toFloat()

        squareLinePaint = Paint(Paint.ANTI_ALIAS_FLAG)
        squareLinePaint.style = Paint.Style.FILL
        squareLinePaint.color = squareDefColor
        squareLinePaint.strokeWidth = curveLineWidth.toFloat()
        squareLinePaint.setPathEffect(DashPathEffect(floatArrayOf(dashWidth, dashGap), 0.0f))

        pointInTimeTextPaint = Paint(Paint.ANTI_ALIAS_FLAG)
        pointInTimeTextPaint.color = pointInTimeTextColor
        pointInTimeTextPaint.textSize = pointInTimeTextSize.toFloat()

        curveLinePaint = Paint(Paint.ANTI_ALIAS_FLAG)
        curveLinePaint.style = Paint.Style.STROKE
        curveLinePaint.color = curveLineColor
        curveLinePaint.strokeWidth = curveLineWidth.toFloat()

        val fmi4Des: Paint.FontMetrics = desTextPaint.fontMetrics
        desTextHeight = (fmi4Des.bottom - fmi4Des.top).toInt()
        desTextBaseline = (desTextHeight / 2 + (fmi4Des.descent - fmi4Des.ascent) / 2 - fmi4Des.bottom).toInt()

        val fmi4Range: Paint.FontMetrics = rangeTextPaint.fontMetrics
        rangeTextHeight = (fmi4Range.bottom - fmi4Range.top).toInt()
        rangTextBaseline = (rangeTextHeight / 2 + (fmi4Range.descent - fmi4Range.ascent) / 2 - fmi4Des.bottom).toInt()

        val fmi4PointTime = pointInTimeTextPaint.fontMetrics
        pointInTimeTextHeight = (fmi4PointTime.descent - fmi4PointTime.ascent).toInt()
        pointInTimeTextBgDrawable = IndicatorDrawable()

        //基线X轴起点 =  横方向偏移量
        baselineX = HORIZONTAL_OFFSET
        /*计算topBaseLine的位置*/
        //顶部基线Y轴坐标 = 垂直方向偏移量 + 最大值文字的paddingTop + 最大值文字的paddingBootom + 文字高度 +指示标的高度 +指示标距离基线的距离
        topBaselineY =
            VERTICAL_OFFSET + pointInTimeTextPaddingTop + pointInTimeTextPaddingBottom + pointInTimeTextHeight + subscriptHeight + subscriptVertical
    }

    /**
     * 对外提供设置属性方法 start
     */
    fun setChart(chart: SquareChartController4New) {
        this.chart = chart
        this.chart?.associatedView(this)
    }

    fun setSquareYTestChangeListener(squareYTestChange: ISquareYTestChangeListener) {
        this.squareYTestChange = squareYTestChange
    }

    fun updateCali(caliValue: Int) {
        this.caliValue = caliValue
    }

    fun setChangeValueShow(isFahOpen: Boolean, textSuffix: String) {
        this.isFahOpen = isFahOpen
        this.textSuffix = textSuffix
        invalidate()
    }

    fun setPointValue(pointValue: Int) {
        this.pointValue = pointValue
    }

    fun setDefMaxValueAndMinValue(minV: Float, maxV: Float) {
        this.maxDefYValue = maxV
        this.minDefYValue = minV
    }

    /**
     * 绘制pm2.5色块
     */
    private val rect: Rect = Rect()

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        chart?.initChartWidth(width, baselinesInterval, topBaselineY)
        initShowMaxValueMinValue()
        initPointInTimeX()
        //pm2.5需绘制色块
        if (chartModelType == TemHum.Type.pm25) {
            pm25LevelBlock(canvas)
        }
        /*绘制TopBaseLine*/
        drawTopBaseLine(canvas)
        /*绘制Y轴中间线*/
        drawCenterLines(canvas)
        /*绘制X轴坐标值*/
        drawIntervalLines(canvas)
        /*绘制柱状图*/
        drawSquare(canvas)
        /*绘制BottomBaseLine*/
        drawBottomBaseLine(canvas)
        /*绘制PointInTime*/
        drawPointInTime(canvas)
        squareYTestChange?.apply { invalidateView() }
    }

    private fun getBottomBaselineY(): Int {
        return topBaselineY + baselinesInterval
    }

    private fun initShowMaxValueMinValue() {
        if (chart == null) {
            return
        }
        val isLookupData = chart!!.isLookupData
        maxShowValue = chart!!.getShowValue(chartModelType)[0]
        minShowValue = chart!!.getShowValue(chartModelType)[1]
        maxShowValue = calculationYValue(if (isLookupData) maxShowValue else maxDefYValue)
        minShowValue = calculationYValue(if (isLookupData) minShowValue else minDefYValue)
        maxMinAvgEqual = maxShowValue == minShowValue
    }

    /**
     *  绘制选中指示器
     */
    private fun initPointInTimeX() {
        pointInTimeStr = getPointInTimeStr()
        if (TextUtils.isEmpty(pointInTimeStr)) {
            /*表明无最近可查看数据*/
            setPointTimeStamp(INVALID_POINT, 0)
            return
        }
        val filterList = chart?.timeLineSet?.filter {
            it.timeStamp == pointTimeStamp
        }
        if (filterList.isNullOrEmpty()) {
            setPointTimeStamp(INVALID_POINT, 0)
            return
        }
        val pointInTimeX = filterList.first().x
        if (pointInTimeX < 0 || pointInTimeX > width) {
            /*不在可见范围内;取消时间戳有效*/
            setPointTimeStamp(INVALID_POINT, 0)
            return
        }
        pointSelectX = pointInTimeX
    }

    private fun drawPointInTime(canvas: Canvas) {
        if (pointInTimeStr.isEmpty() || pointSelectX == INVALID_POINT) return
        // 文字宽度
        val measureText = (pointInTimeTextPaint.measureText(pointInTimeStr) + 0.5f).toInt()
        var indicatorW = measureText + pointInTimeTextPaddingHorizontal * 2
        indicatorW = Math.max(70, indicatorW)
        val indicatorH = pointInTimeTextHeight + pointInTimeTextPaddingTop + pointInTimeTextPaddingBottom + subscriptHeight
        val subscriptWidth = 20
        var subscriptStartX = indicatorW / 2 - subscriptWidth / 2
        /*绘制指示文本*/
        pointInTimeTextBgDrawable.setColor(curveLineColor)
        pointInTimeTextBgDrawable.setSubscriptWH(subscriptWidth, subscriptHeight)
        pointInTimeTextBgDrawable.setRoundRadius(12)
        var left = pointSelectX - indicatorW / 2
        if (left <= 0) {
            /*表明滑动到了最左端；需要移动subscriptStartX位置*/
            subscriptStartX = Math.max(0, subscriptStartX - Math.abs(left))
            left = 0
        } else {
            val leftMax = width - indicatorW
            if (left >= leftMax) {
                /*表明滑动到了最右端;需要移动subscriptStartX位置*/
                subscriptStartX = Math.min(
                    indicatorW - subscriptWidth,
                    subscriptStartX + Math.abs(left - leftMax)
                )
                left = leftMax
            }
        }
        // 获取坐标点集合
        val curvePathPointsFirst = chart?.getCurvePathPoints(chartModelType)
        var yPoint = VERTICAL_OFFSET
        if (curvePathPointsFirst != null) {
            run loop@{
                curvePathPointsFirst.forEach {
                    if (it.x == pointSelectX) {
                        yPoint = it.y - VERTICAL_OFFSET - indicatorH
                        return@loop
                    }
                }
            }
        }

        val top = yPoint
        val right = left + indicatorW
        val bottom = top + indicatorH
        var startX = pointSelectX
        startX = Math.max(intervalLineWidth, startX)
        startX = Math.min(width - intervalLineWidth, startX)
        var offsetValue = 0
        var indicatorType = IndicatorDrawable.IndicatorType.center
        if (pointSelectX < 10) {
            indicatorType = IndicatorDrawable.IndicatorType.left
            offsetValue = startX
        } else if (pointSelectX > width - 10) {
            indicatorType = IndicatorDrawable.IndicatorType.right
            offsetValue = 10 - (width - startX)
        }

        pointInTimeTextBgDrawable.setTextField(
            pointInTimeStr,
            pointInTimeTextSize,
            pointInTimeTextColor,
            pointInTimeTextPaddingTop
        )
        pointInTimeTextBgDrawable.setSubscriptStartX(subscriptStartX)
        pointInTimeTextBgDrawable.setBounds(left, top, right, bottom)
        pointInTimeTextBgDrawable.setIndicatorType(indicatorType, offsetValue)
        pointInTimeTextBgDrawable.draw(canvas)
    }

    /**
     * 获取当前触碰点坐标的文本描述
     */
    private fun getPointInTimeStr(): String {
        if (pointTimeStamp == 0L) {
            return ""
        }
        val chartModel = chart?.getPointValue(pointTimeStamp, chartModelType) ?: return ""
        val timeStamp = chartModel.timeStamp
        val value = chartModel.value
        val validValue = when (chartModelType) {
            TemHum.Type.tem -> {
                NumberUtil.getTemValue(isFahOpen, value, caliValue)
            }

            TemHum.Type.pm25 -> {
                value.toFloat()
            }

            TemHum.Type.dewP -> {
                NumberUtil.getTemValue(isFahOpen, value, 0)
            }

            TemHum.Type.vpd -> {
                NumberUtil.toFloatValueBy100(value)
            }

            else -> {
                NumberUtil.getHumValue(value, caliValue)
            }
        }
        /*判断是否在预警范围内*/
        val valueStr: String = getValueStr(validValue)

        var timeStampStr = ""
        when (chart?.intervalType) {
            IntervalType.hour_1_hour -> {
                timeStampStr = ResUtil.getStringFormat(
                    R.string.time_point_hour,
                    TimeFormatM.getInstance().formatTimeTo5HM(timeStamp),
                    TimeFormatM.getInstance().formatTimeToHMYMDFor24(TimeUtil.getRightTimeStampBy1Hour(timeStamp))
                )
            }

            IntervalType.week_1_day -> {
                timeStampStr = TimeFormatM.getInstance().formatTimeToYMD(timeStamp)
            }

            IntervalType.year_1_month -> {
                timeStampStr = TimeFormatM.getInstance().formatTimeTo5YM(timeStamp)
            }

            else -> {}
        }
        return "$valueStr $timeStampStr"
    }

    private fun getValueStr(value: Float): String {
        return if (pointValue > 0) {
            StrUtil.subZeroAndDot(NumberUtil.getValidFloatByPoint(value, pointValue).toString()) + textSuffix
        } else {
            StrUtil.subZeroAndDot(value.toInt().toString()) + textSuffix
        }
    }


    /**
     * 绘制X轴坐标值
     */
    private fun drawIntervalLines(canvas: Canvas) {
        val timeLineSet = chart?.timeLineSet
        if (timeLineSet.isNullOrEmpty()) return
        val desTextStrArr = getTimeStampStr(timeLineSet.peek()?.timeStamp ?: 0).split("_")
        var desTextNum = 0
        for (s in desTextStrArr) {
            if (!TextUtils.isEmpty(s)) {
                desTextNum++
            }
        }
        if (desTextNum == 0) {
            return
        }
        val startTextY = bottomBaselineY + intervalTextVerticalSpace + desTextBaseline
        val rangeStartTextY =
            bottomBaselineY + intervalTextVerticalSpace + desTextHeight * desTextNum + rangeTextVerticalSpace + rangTextBaseline
        var firstRangeText = ""
        var nextRangeText = ""
        var firstRangeTextRightX = 0
        var showFirst = true
        rangeTextPaint.textAlign = Paint.Align.LEFT
        timeLineSet.forEachIndexed { index, timeLine ->
            val x = timeLine.x
            val timeStamp = timeLine.timeStamp
            val str = getTimeStampStr(timeStamp).split("_")
            val startX = (x - (desTextPaint.measureText(str[0]) / 2)).toInt()
            desTextPaint.color =
                if (chart?.timeStampListByEmpty?.contains(timeStamp) == true) desUnSelectTextColor else desTextColor
            canvas.drawText(str[0], startX.toFloat(), startTextY.toFloat(), desTextPaint)
            if (str.size > 1 && desTextNum > 1) {
                val startX_1 = x - (desTextPaint.measureText(str[1]) / 2)
                val startY_1: Float = (startTextY + desTextHeight) * 1.0f
                canvas.drawText(str[1], startX_1, startY_1, desTextPaint)
            }
            if (index == 0) {
                firstRangeText = getFirstRangeTimeStampStr(timeStamp)
                firstRangeTextRightX = rangeTextPaint.measureText(firstRangeText).toInt()
            } else {
                nextRangeText = getRangeTimeStampStr(timeStamp)
                if (nextRangeText.isNotEmpty()) {
                    val startRangeX = (x - (rangeTextPaint.measureText(nextRangeText) / 2)).toInt()
                    if (startRangeX < firstRangeTextRightX) showFirst = false
                    canvas.drawText(
                        nextRangeText,
                        startRangeX.toFloat(),
                        rangeStartTextY.toFloat(),
                        rangeTextPaint
                    )
                }
            }
        }
        rangeTextPaint.textAlign = Paint.Align.LEFT
        if (showFirst) canvas.drawText(
            firstRangeText,
            0f,
            rangeStartTextY.toFloat(),
            rangeTextPaint
        )
    }

    /**
     * 时间戳转X轴标准字符串
     */
    private fun getTimeStampStr(timeStamp: Long): String {
        val str = when (chart?.intervalType) {
            IntervalType.hour_1_hour -> TimeFormatM.getInstance().formatTimeToHM4V1(timeStamp, true)
            IntervalType.week_1_day -> TimeFormatM.getInstance().formatTimeToD(timeStamp)
            IntervalType.year_1_month -> TimeUtil.getChartTimeByMonth(timeStamp)
            else -> {
                ""
            }
        }
        return str
    }

    /**
     * 获取开始时间的时间戳字符串
     */
    private fun getFirstRangeTimeStampStr(timeStamp: Long): String {
        val time = when (chart?.intervalType) {
            IntervalType.hour_1_hour -> getRangeTimeStampStr(TimeUtil.getCorrectDay(timeStamp))
            IntervalType.week_1_day -> getRangeTimeStampStr(TimeUtil.getCorrectMonth(timeStamp))
            IntervalType.year_1_month -> getRangeTimeStampStr(TimeUtil.getCorrectYear(timeStamp))
            else -> {
                ""
            }
        }
        return time
    }


    private fun getRangeTimeStampStr(timeStamp: Long): String {
        val intervalType = chart?.intervalType
        var str = ""
        if (intervalType == IntervalType.hour_1_hour && TimeUtil.isDay(timeStamp)) {
            str = TimeFormatM.getInstance().formatTimeToMD(timeStamp)
        } else if (intervalType == IntervalType.week_1_day && TimeUtil.isMonth(timeStamp)) {
            str = TimeFormatM.getInstance().formatTimeToYM(timeStamp)
        } else if (intervalType == IntervalType.year_1_month && TimeUtil.isYear(timeStamp)) {
            str = TimeFormatM.getInstance().formatTimeToY(timeStamp)
        }
        return str
    }

    /**
     * 绘制中间俩条线
     */
    private fun drawCenterLines(canvas: Canvas) {
        val dividerStrArray = arrayOf("", "")
        val stopX = width - HORIZONTAL_OFFSET
        // y轴分割线坐标
        val dividerYArray = arrayOf(INVALID_POINT.toFloat(), INVALID_POINT.toFloat())
        val yValueArray = FloatArray(DIVISION_LINE_COUNT)

        // 最大最小值相同情况不需要绘制中间间隔线和显示Y轴值
        if (!maxMinAvgEqual) {
            // 算出单条线的高度
            val singleLineHeight = baselinesInterval / (DIVISION_LINE_COUNT + 1)

            // y轴分割线之间的间距
            val dividerInterval = Math.abs(maxShowValue - minShowValue) / (DIVISION_LINE_COUNT + 1)
            val decimal = pointValue
            for (i in 1..DIVISION_LINE_COUNT) {

                // 分割线Y坐标
                val topStart = (topBaselineY + singleLineHeight * i).toFloat()
                // 计算Y轴值
                yValueArray[i - 1] = handlerAccuracy(maxShowValue - dividerInterval * i, decimal)
                if (chart?.isLookupData == true) {
                    // 画Y轴分割线
                    canvas.drawLine(
                        baselineX.toFloat(),
                        topStart,
                        stopX.toFloat(),
                        topStart,
                        baseLinePaint
                    )
                }
                dividerYArray[i - 1] = topStart
            }
            handleDividerPoint(yValueArray, decimal, dividerInterval)
            //  给Y轴str赋值
            yValueArray.forEachIndexed { index, value ->
                dividerStrArray[index] = "${formatYStr(value)}$textSuffix"
            }
        }
        /*绘制maxStr*/
        val maxStr = "${formatYStr(maxShowValue)}$textSuffix"
        /*绘制minStr*/
        val minStr = if (maxMinAvgEqual) "" else "${formatYStr(minShowValue)}$textSuffix"
        squareYTestChange?.apply {
            pointChange(
                topBaselineY.toFloat(),
                bottomBaselineY.toFloat(),
                dividerYArray[0],
                dividerYArray[1],
            )
            textChange(
                maxStr,
                minStr,
                dividerStrArray[0],
                dividerStrArray[1],
            )
        }
    }


    /**
     * 特殊情况：由于y轴取值为四舍五入，所有可能出现精度问题，这边针对此情况做下特殊处理
     */
    private fun handleDividerPoint(
        yValueArray: FloatArray,
        decimal: Int,
        dividerInterval: Float
    ) {
        val mDecimal = decimal + 1
        var isSpecial = false
        run loop@{
            yValueArray.forEachIndexed { index, fl ->
                if (fl == maxShowValue || fl == maxShowValue || (index > 0 && fl == yValueArray[index - 1])) {
                    isSpecial = true
                    return@loop
                }
            }
        }
        if (isSpecial) {
            for (i in 1..DIVISION_LINE_COUNT) {
                yValueArray[i - 1] = handlerAccuracy(maxShowValue - dividerInterval * i, mDecimal)
            }
        }
        if (isSpecial && mDecimal <= 3) handleDividerPoint(yValueArray, mDecimal, dividerInterval)
    }

    private fun formatYStr(minShowValue: Float) = StrUtil.subZeroAndDot(minShowValue.toString())


    private fun calculationYValue(v: Float, decimal: Int = pointValue): Float {
        var value = v
        val caliValueF = NumberUtil.toFloatValueBy100(caliValue)
        value = NumberUtil.getValidFloatValue(value + caliValueF)
        if (chartModelType == TemHum.Type.hum || chartModelType == TemHum.Type.vpd) {
            /*湿度、VPD最小值为0%*/
            value = Math.max(0f, value)
        }
        if (chartModelType == TemHum.Type.hum) {
            /*湿度最大值为99.9%*/
            value = Math.min(99.9f, value)
        }
        value = if (isFahOpen) NumberUtil.getFahValue(value) else value
        return handlerAccuracy(value, decimal)
    }

    private fun handlerAccuracy(value: Float, decimal: Int): Float {
        return if (decimal > 0) NumberUtil.getValidFloatByPoint(value, decimal) else value.toInt().toFloat()
    }

    private fun drawTopBaseLine(canvas: Canvas) {
        if (chart?.isLookupData == false) return
        /*绘制顶部baseLine*/
        val stopX = width - HORIZONTAL_OFFSET
        canvas.drawLine(
            baselineX.toFloat(),
            topBaselineY.toFloat(),
            stopX.toFloat(),
            topBaselineY.toFloat(),
            baseLinePaint
        )
    }


    private fun drawBottomBaseLine(canvas: Canvas) {
        /*绘制底部baseLine*/
        val startX = baselineX
        val stopX = width - HORIZONTAL_OFFSET
        baseLinePaint.xfermode = null
        baseLinePaint.strokeWidth = baseLineWidth.toFloat()
        canvas.drawLine(
            startX.toFloat(),
            bottomBaselineY.toFloat(),
            stopX.toFloat(),
            bottomBaselineY.toFloat(),
            baseLinePaint
        )
    }

    private fun drawSquare(canvas: Canvas) {
        if (chart == null) {
            return
        }
        if (!chart!!.dataPrepared() || chart!!.noData()) return
        // 获取坐标点集合
        val curvePathPointsFirst = chart!!.getCurvePathPoints(chartModelType)
        if (curvePathPointsFirst.isNullOrEmpty()) return
        // 坐标点集合
        /**
         * 最大最小平均值相同的特殊情况，更新所有坐标点Y坐标在可绘制图标最中间点
         */
        if (maxMinAvgEqual) {
            /*更新坐标点y轴坐标值*/
            for (curvePathPoint in curvePathPointsFirst) {
                //不是无效数据点才进行赋值
                if (curvePathPoint.y != INVALID_POINT) {
                    curvePathPoint.y = topBaselineY
                }
            }
        }
        var yPonit = 0
        var minHeigth = 0
        curvePathPointsFirst.forEach {
            if (it.y != INVALID_POINT && it.y <= bottomBaselineY) {
                squareLinePaint.color =
                    if (pointSelectX == it.x && pointInTimeStr.isNotEmpty()) squareSelColor else squareDefColor
                //最低值显示灰色
                minHeigth = bottomBaselineY - MIN_VAlUE_HEIGTH
                yPonit = if (it.y > minHeigth) minHeigth else it.y
                //left, top, right, bottom
                canvas.drawRect(
                    it.x.toFloat() - squareRadius,
                    yPonit.toFloat(),
                    (it.x + squareRadius).toFloat(),
                    bottomBaselineY.toFloat(),
                    squareLinePaint
                )
            }
        }
    }

    /**
     * 触摸操作包含流程
     * 1.单击显示隐藏某一条数据
     * 2.左右移动进行数据查看
     * 3.手势缩放进行数据日期间隔调整
     * @param event event
     * @return boolean
     */
    @SuppressLint("ClickableViewAccessibility")
    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (chart == null) {
            return true
        }
        val result = chart!!.onTouchEvent(this, event)
        if (!result) {
            parent.requestDisallowInterceptTouchEvent(false)
            return super.onTouchEvent(event)
        }
        return true
    }

    override fun isEnabledView(): Boolean {
        return isEnabled
    }

    override fun getParentView(): ViewParent {
        return parent
    }

    override fun getPointTimeStamp(): Long {
        return pointTimeStamp
    }

    override fun setPointTimeStamp(x: Int, pointTimeStamp: Long) {
        val timeLineSet = chart?.timeLineSet
        var times = 0L
        if (!timeLineSet.isNullOrEmpty() && x > 0) {
            run loop@{
                timeLineSet.forEach {
                    val startX = it.x - squareRadius - deviationClickWidth
                    val endX = it.x + squareRadius + deviationClickWidth
                    if (x in startX..endX) {
                        times = it.timeStamp
                        return@loop
                    }
                }
            }
        }
        //  取消当前
        if (times == 0L) pointSelectX = INVALID_POINT
        this.pointTimeStamp = times
    }

    override fun invalidateView() {
        invalidate()
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        //设置控件的测量高度
        bottomBaselineY = getBottomBaselineY()
        val desTexLineNum = if (chart?.intervalType == IntervalType.hour_1_hour) {
            if (TimeFormatM.getInstance().is24Hours(false)) {
                1
            } else {
                2
            }
        } else {
            1
        }
        val measuredHeight =
            bottomBaselineY + intervalTextVerticalSpace + desTextHeight * desTexLineNum + rangeTextVerticalSpace + rangeTextHeight
        //设置控件的测量宽高
        setMeasuredDimension(widthMeasureSpec, measuredHeight)
    }

    private fun pm25LevelBlock(canvas: Canvas) {
        if (chart == null) return
        if (!chart!!.isLookupData) return
        val levelArray = getPm25Level() ?: return
        curveLinePaint.style = Paint.Style.FILL
        if (levelArray[0] == 5) {
            //黄色区块
            curveLinePaint.color = pm25Colors[2]
            rect.set(0, topBaselineY, width, bottomBaselineY)
            canvas.drawRect(rect, curveLinePaint)
        } else if (levelArray[0] == 6) {
            val level2 = levelArray[1]
            //黄色区块
            curveLinePaint.color = pm25Colors[2]
            rect.set(0, topBaselineY, width, level2)
            canvas.drawRect(rect, curveLinePaint)
            //蓝色区块
            curveLinePaint.color = pm25Colors[1]
            rect.set(0, level2, width, bottomBaselineY)
            canvas.drawRect(rect, curveLinePaint)
        } else if (levelArray[0] == 7) {
            val level2 = levelArray[2]
            val level1 = levelArray[1]
            //黄色区块
            curveLinePaint.color = pm25Colors[2]
            rect.set(0, topBaselineY, width, level2)
            canvas.drawRect(rect, curveLinePaint)
            //蓝色区块
            curveLinePaint.color = pm25Colors[1]
            rect.set(0, level2, width, level1)
            canvas.drawRect(rect, curveLinePaint)
            //绿色区块
            curveLinePaint.color = pm25Colors[0]
            rect.set(0, level1, width, bottomBaselineY)
            canvas.drawRect(rect, curveLinePaint)
        } else if (levelArray[0] == 8) {
            //蓝色区块
            curveLinePaint.color = pm25Colors[1]
            rect.set(0, topBaselineY, width, bottomBaselineY)
            canvas.drawRect(rect, curveLinePaint)
        } else if (levelArray[0] == 9) {
            val level1 = levelArray[1]
            //蓝色区块
            curveLinePaint.color = pm25Colors[1]
            rect.set(0, topBaselineY, width, level1)
            canvas.drawRect(rect, curveLinePaint)
            //绿色区块
            curveLinePaint.color = pm25Colors[0]
            rect.set(0, level1, width, bottomBaselineY)
            canvas.drawRect(rect, curveLinePaint)
        } else if (levelArray[0] == 10) {
            //绿色区块
            curveLinePaint.color = pm25Colors[0]
            rect.set(0, topBaselineY, width, bottomBaselineY)
            canvas.drawRect(rect, curveLinePaint)
        }
    }

    fun updateWarn(min: Int, max: Int) {
        this.co2MinLevel = min
        this.co2MaxLevel = max
        invalidate()
    }

    /**
     * pm2.5曲线的分颜色绘制判断
     *
     * @return 数组：第1位表示类型，后边跟分界点的坐标y值
     */
    private fun getPm25Level(): IntArray? {
        //0<value<35：绿；35<=value<75:蓝色；75<=value<115:黄色；115<=value:红色
        val minValue = minShowValue
        val maxValue = maxShowValue
        val level1 = co2MinLevel
        val level2 = co2MaxLevel
        if (level1 <= 1 || level2 <= 2) {
            return null
        }
        SafeLog.d("getPm25Level") { "minValue=$minValue,maxValue=$maxValue,level1=$level1,level2=$level2" }
        //最大范围
        val offset = 0
        val rangeValue: Float = abs((maxValue - minValue).toFloat()) * 1f
        //有多种组合（4累加->10种）显示,以最大值所在层级逐层判断
        if (maxValue >= level2) {
            if (minValue >= level2) {
                //曲线全为红色（类型5）
                return intArrayOf(5)
            } else if (minValue >= level1) {
                //曲线为红色、黄两色（类型6）
                val percentLevel2: Float = abs((maxValue - level2).toFloat()) / rangeValue
                val level2Y: Int = (percentLevel2 * baselinesInterval).roundToInt() + topBaselineY
                return intArrayOf(6, level2Y + offset)
            } else {
                //曲线为绿、红色、红色三色（类型7）
                val percentLevel2: Float = abs((maxValue - level2).toFloat()) / rangeValue
                val percentLevel1: Float = abs((maxValue - level1).toFloat()) / rangeValue
                val level2Y: Int = (percentLevel2 * baselinesInterval).roundToInt() + topBaselineY
                val level1Y: Int = (percentLevel1 * baselinesInterval).roundToInt() + topBaselineY
                return intArrayOf(7, level1Y + offset, level2Y + offset)
            }
        } else if (maxValue >= level1) {
            if (minValue >= level1) {
                //曲线全为黄色（类型8）
                return intArrayOf(8)
            } else {
                //曲线为绿、黄两色（类型9）
                val percentLevel1: Float = abs((maxValue - level1).toFloat()) / rangeValue
                val level1Y: Int = (percentLevel1 * baselinesInterval).roundToInt() + topBaselineY
                return intArrayOf(9, level1Y + offset)
            }
        } else {
            //曲线全为绿色（类型10）
            return intArrayOf(10)
        }
    }

    override fun applySkin(attrMap: HashMap<String, Int>) {
        for (s in attrMap.keys) {
            val id = attrMap[s]!!
            when (s) {
                "thChart_baseLineColor" -> baseLineColor = ResUtil.getColor(id)
                "thChart_desTextColor" -> desTextColor = ResUtil.getColor(id)
                "thChart_desUnSelectTextColor" -> desUnSelectTextColor = ResUtil.getColor(id)
                "thChart_curveLineColor" -> curveLineColor = ResUtil.getColor(id)
                "thChart_squareColor" -> squareDefColor = ResUtil.getColor(id)
                "thChart_squareSelectColor" -> squareSelColor = ResUtil.getColor(id)
                "thChart_pointInTimeTextColor" -> pointInTimeTextColor = ResUtil.getColor(id)
                "thChart_rangeColor" -> rangeTextColor = ResUtil.getColor(id)
            }
        }
        /**
         * 初始化画笔
         */
        baseLinePaint.color = baseLineColor

        desTextPaint.color = desTextColor

        rangeTextPaint.color = rangeTextColor

        squareLinePaint.color = squareDefColor

        pointInTimeTextPaint.color = pointInTimeTextColor

        curveLinePaint.color = curveLineColor

        //pm2.5的色块颜色
        pm25Colors[0] = ResUtil.getColor(R.color.ui_color_block_style_48_color_green)
        pm25Colors[1] = ResUtil.getColor(R.color.ui_color_block_style_48_color_yellow)
        pm25Colors[2] = ResUtil.getColor(R.color.ui_color_block_style_48_color_red)

        invalidate()
    }
}
