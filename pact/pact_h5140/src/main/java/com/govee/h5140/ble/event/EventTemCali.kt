package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取温度校准值的事件
 */
class EventTemCali(result: Boolean, write: <PERSON><PERSON>an, commandType: Byte, proType: Byte) :
        AbsControllerEvent(result, write, commandType, proType, !write) {

    //温度校准值
    var temCali = 0

    companion object {
        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, temCali: Int) {
            val event = EventTemCali(true, write, commandType, proType)
            event.temCali = temCali
            EventBus.getDefault().post(event)
        }

        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte, temCali: Int) {
            val event = EventTemCali(result, true, commandType, proType)
            event.temCali = temCali
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventTemCali(false, write, commandType, proType))
        }
    }
}