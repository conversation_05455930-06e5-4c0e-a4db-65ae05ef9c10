package com.govee.h5140.detail.square

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import com.govee.base2newth.chart.square.SquareChartPM25Controller
import com.govee.base2newth.chart.square.newsquare.SquareChartController4New
import com.govee.base2newth.chart.square.newsquare.ThSquareYTextView4New
import com.govee.base2newth.db.TemHum

/**
 * Create by yu on 2022/8/26
 *  矩形viewgroup
 */
@SuppressLint("CustomViewStyleable")
class ThSquareView4New @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0)
    : ConstraintLayout(context, attrs, defStyleAttr) {

    private val mTvEmpty: TextView
    private val mChart: ThSquareTrendChart4New
    private val mLeftY: ThSquareYTextView4New

    init {
        inflate(context, com.govee.h5140.R.layout.h5140_squareview_4_new, this)
        mTvEmpty = findViewById(com.govee.base2newth.R.id.tv_empty)
        mChart = findViewById(com.govee.base2newth.R.id.chart)
        mLeftY = findViewById(com.govee.base2newth.R.id.left_y)
        val ta = getContext().obtainStyledAttributes(attrs, com.govee.base2home.R.styleable.ThTrendChart)
        val textSuffixCS =
            ta.getText(com.govee.base2home.R.styleable.ThTrendChart_thChart_textSuffix)
        mChart.textSuffix = textSuffixCS?.toString() ?: ""
        val chartType: Int =
            ta.getInt(com.govee.base2home.R.styleable.ThTrendChart_thChart_tc_chart_type, 0)
        mChart.chartModelType = TemHum.Type.entries.toTypedArray()[chartType]
        ta.recycle()
    }

    fun setChart(iChart: SquareChartController4New) {
        mChart.setChart(iChart)
        mChart.setSquareYTestChangeListener(mLeftY)
    }

    fun getSquareChart(): ThSquareTrendChart4New {
        return mChart
    }

    fun setEmptyUI(visibility: Int) {
        mTvEmpty.visibility = visibility
    }
}