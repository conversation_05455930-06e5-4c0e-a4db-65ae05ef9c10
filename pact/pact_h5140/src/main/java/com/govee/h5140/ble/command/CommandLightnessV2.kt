package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventLightnessV2
import com.govee.h5140.ble.event.EventLightnessV2.LightnessV2
import com.ihoment.base2app.infra.LogInfra

/**
 * <AUTHOR>
 * @date created on 2022/9/20
 * @description 从5106设备读取/写入屏幕亮度的指令，并解析其回复
 */
class CommandLightnessV2 : AbsSingleController {

    companion object {
        private const val TAG = "CommandLightnessV2"
    }

    private var lightnessV2: LightnessV2? = null

    /**
     * 写操作
     */
    constructor(lightnessV2: LightnessV2) : super(true) {
        this.lightnessV2 = lightnessV2
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray {
        var sendData = byteArrayOf()
        if (lightnessV2 != null) {
            sendData = ByteArray(12)
            val daytimeByteArr = BleUtil.hexStr2Byte(lightnessV2!!.dayTimeRange)
            System.arraycopy(daytimeByteArr, 0, sendData, 0, daytimeByteArr.size)
            sendData[4] = lightnessV2!!.dayLtValue.toByte()
            sendData[5] = lightnessV2!!.dayLtOpen.toByte()
            val nighttimeByteArr = BleUtil.hexStr2Byte(lightnessV2!!.nightTimeRange)
            System.arraycopy(nighttimeByteArr, 0, sendData, 6, nighttimeByteArr.size)
            sendData[10] = lightnessV2!!.nightLtValue.toByte()
            sendData[11] = lightnessV2!!.nightLtOpen.toByte()
        }
        LogInfra.Log.i("xiaobing", "发送的亮度信息数据为-->${BleUtil.bytesToHexString(sendData)}")
        return sendData;
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        return if (lightnessV2 == null) {
            false
        } else {
            EventLightnessV2.sendWriteResult(suc, commandType, proType, lightnessV2!!)
            true
        }
    }

    override fun fail() {
        EventLightnessV2.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_DISPLAY_LIGHTNESS_V2
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        //解析读取到的数据
        val lightnessV2 = LightnessV2()
        val hexStr = BleUtil.bytesToHexString(validBytes).replace("0x", "").replace(" ", "")
        lightnessV2.dayTimeRange = hexStr.substring(0, 8)
        lightnessV2.dayLtValue = BleUtil.hex2Int(hexStr.substring(8, 10), true)
        lightnessV2.dayLtOpen = BleUtil.hex2Int(hexStr.substring(10, 12), true)
        lightnessV2.nightTimeRange = hexStr.substring(12, 20)
        lightnessV2.nightLtValue = BleUtil.hex2Int(hexStr.substring(20, 22), true)
        lightnessV2.nightLtOpen = BleUtil.hex2Int(hexStr.substring(22, 24), true)
        EventLightnessV2.sendSuc(isWrite, commandType, proType, lightnessV2)
        return true
    }
}