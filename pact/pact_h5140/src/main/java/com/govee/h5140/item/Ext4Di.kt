package com.govee.h5140.item

import android.text.TextUtils
import androidx.annotation.Keep

/**
 * <AUTHOR>
 * @date created on 2022/6/2
 * @description 首页5106设备item的扩展参数
 */
@Keep
class Ext4Di {

    var address: String = ""
    var bleName: String = ""
    var deviceName: String = ""
    var pm25Min = 0
    var pm25Max = 0
    var pm25Warning = false
    var temMin = 0
    var temMax = 0
    var temWarning = false
    var humMin = 0
    var humMax = 0
    var humWarning = false
    var temCali = 0
    var humCali = 0
    var wifiMac: String = ""
    var topic = ""
    private var airQualityOnOff = ""

    fun getAiqQualityOnOff(): Boolean {
        //默认值为开
        if (airQualityOnOff.uppercase() == "TRUE" || airQualityOnOff == "1" || TextUtils.isEmpty(
                airQualityOnOff
            )
        ) {
            return true
        }
        return false
    }
}