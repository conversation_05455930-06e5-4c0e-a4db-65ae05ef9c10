package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsOnlyReadSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventHardVersion

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取设备硬件版本的指令，并解析其回复
 */
class CommandHardVersion : AbsOnlyReadSingleController() {
    override fun fail() {
        EventHardVersion.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_HARD_VERSION
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val hardVersion = BleUtil.getStrData(validBytes)
        EventHardVersion.sendSuc(isWrite, commandType, proType, hardVersion)
        return true
    }
}