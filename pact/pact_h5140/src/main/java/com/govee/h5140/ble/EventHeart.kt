package com.govee.h5140.ble

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 心跳包中的有效数据传输事件
 */
class EventHeart : AbsControllerEvent {

    constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) : super(result, write, commandType, proType, false)

    var tem = 0
    var hum = 0
    var pm25 = 0

    companion object {
        fun sendSuc(write: <PERSON>olean, commandType: Byte, proType: Byte, tem: Int, hum: Int, pm25: Int) {
            val event = EventHeart(true, write, commandType, proType)
            event.tem = tem
            event.hum = hum
            event.pm25 = pm25
            EventBus.getDefault().post(event)
        }
    }
}