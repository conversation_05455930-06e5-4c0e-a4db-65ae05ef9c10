package com.govee.h5140.add

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.govee.base2home.Constant
import com.govee.base2home.device.AbsDeviceNameAcV2
import com.govee.base2home.push.EventDeviceListFresh
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.config.WarnRange
import com.govee.ble.event.BTStatusEvent
import com.govee.ble.event.EventBleConnect
import com.govee.h5140.Constants5140
import com.govee.h5140.net.INet
import com.govee.h5140.net.RequestSettingName
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.Cache
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.network.BaseResponse
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.util.JumpUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 给绑定设备命名页面
 */
class Activity4NameDevice : AbsDeviceNameAcV2() {

    private var addInfo: AddInfo? = null
    private var inSet = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setNeedUploadRate(false)
    }

    override fun chooseIntervalMinutes(minutes: Int) {
    }

    override fun getIntervalMinutes(): IntArray {
        return intArrayOf(10, 30, 60)
    }

    override fun toSaveDeviceName(deviceName: String) {
        addInfo!!.deviceName = deviceName
        showLoading()
        inSet = true
        toUpdateDeviceInfo()
    }

    override fun initParams(intent: Intent) {
        addInfo = intent.getParcelableExtra(Constants5140.intent_ac_addInfo)
        makeWarnRange()?.run {
            WarnConfig.read().updateWarningRange(this, false)
        }
    }

    private fun makeWarnRange(): WarnRange? {
        addInfo?.let { ext4St ->
            val warnRange = WarnRange()
            warnRange.sku = addInfo?.sku
            warnRange.device = ext4St.deviceId
            warnRange.bleAddress = ext4St.bleAddress
            warnRange.pm25Min = ext4St.co2Min
            warnRange.pm25Max = ext4St.co2Max
            warnRange.pm25Warning = ext4St.co2Warning
            warnRange.temMin = ext4St.temMin
            warnRange.temMax = ext4St.temMax
            warnRange.temWarning = ext4St.temWarning
            warnRange.humMin = ext4St.humMin
            warnRange.humMax = ext4St.humMax
            warnRange.humWarning = ext4St.humWarning
            warnRange.temCali = ext4St.temCalibration
            warnRange.humCali = ext4St.humCalibration
            return warnRange
        }
        return null
    }

    override fun doSkip() {
        toJumpWifiSetting()
    }

    override fun onSaveDeviceNameSuc(newDeviceName: String) {
        if (LogInfra.openLog()) {
            //wifi设备无需本地存储设备名
            LogInfra.Log.i(TAG, "onSaveDeviceNameSuc() newDeviceName = $newDeviceName")
        }
    }

    override fun getDevice(): String {
        return addInfo!!.deviceId
    }

    override fun getSku(): String {
        return addInfo!!.sku
    }

    override fun getDeviceName(): String {
        return addInfo!!.deviceName
    }

    private fun toUpdateDeviceInfo() {
        val requestSettingNameAndRate = RequestSettingName(
            transactions.createTransaction(),
            addInfo!!.sku,
            addInfo!!.deviceId,
            addInfo!!.deviceName
        )
        Cache.get(INet::class.java).updateSettingNameAndRate(requestSettingNameAndRate)
            .enqueue(IHCallBack(requestSettingNameAndRate))
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponseSettingNameAndRate(response: BaseResponse) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onResponseSettingNameAndRate()")
        }
        inSet = false
        changeNameAndUploadRateSuc()
    }

    private fun changeNameAndUploadRateSuc() {
        toJumpWifiSetting()
        //关闭loading
        hideLoading()
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        //禁用返回键
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onBTStatusEvent(event: BTStatusEvent) {
        val btOpen = event.isBtOpen
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onBTStatusEvent() btOpen = $btOpen")
        }
        if (!btOpen) {
            toJumpWifiSetting()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventBleConnect(event: EventBleConnect) {
        val connectSuc = event.connectSuc()
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBleConnect() connectSuc = $connectSuc")
        }
        if (!connectSuc) {
            toJumpWifiSetting()
        }
    }

    override fun onRequestError(error: ErrorResponse) {
        super.onRequestError(error)
        val request = error.request
        if (request is RequestSettingName) {
            hideLoading()
        }
        inSet = false
    }

    private fun toJumpWifiSetting() {
        //处于后台则不跳转ac
        if (BaseApplication.getBaseApplication().isInBackground) {
            return
        }
        //刷新设备列表
        EventDeviceListFresh.sendEventDeviceListFresh(true)
        //取消EventBus注册
        unRegisterEventBus()
        //跳转到wifi设置界面
        Activity4WifiChoose.jump2WifiChooseAc4Add(
            this,
            true,
            addInfo!!.goodsType,
            addInfo!!.sku,
            addInfo!!.deviceId,
            addInfo!!.deviceName,
            addInfo!!.bleAddress,
            Constant.wifi_input_limit_ssid_new,
            Constant.wifi_input_limit_password_new,
            addInfo!!.temCalibration,
            addInfo!!.humCalibration,
            addInfo!!.wifiMac,
            addInfo!!.versionSoft,
            addInfo!!.versionHard,
            "",
        )
    }

    companion object {
        /**
         * 跳转到设备命名页
         */
        fun jump2DeviceNameAc(context: Context?, addInfo: AddInfo?) {
            val bundle = Bundle()
            bundle.putParcelable(Constants5140.intent_ac_addInfo, addInfo)
            JumpUtil.jump(context, Activity4NameDevice::class.java, bundle)
        }
    }
}