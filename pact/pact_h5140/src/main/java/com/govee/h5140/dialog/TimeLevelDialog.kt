package com.govee.h5140.dialog

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import com.govee.base2home.util.ClickUtil
import com.govee.base2newth.other.VolumeLeveView
import com.govee.h5140.databinding.H5140TimeLevelDialogBinding
import com.govee.ui.R
import com.ihoment.base2app.dialog.BaseEventDialog
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.ResUtil

/**
 * <AUTHOR>
 * @date created on 2025/01/03
 * @description 音量档位选择
 */
class TimeLevelDialog private constructor(context: Context) : BaseEventDialog(context), OnClickListener {

    /**
     * 选值的数据集
     */
    private val volumeGearsList by lazy {
        ArrayList<Int>().apply {
            for (i in 0..3) {
                this.add(i)
            }
        }
    }
    private var onSelectVolumeListener: VolumeLeveView.OnSelectVolumeListener? = null
    private var selectedVolumeGear: Int = 0

    private lateinit var svBinding: H5140TimeLevelDialogBinding

    init {
        changeDialogOutside(false)
        immersionMode()
    }

    companion object {
        fun createDialog(context: Context): TimeLevelDialog {
            return TimeLevelDialog(context)
        }

        fun timeLevel(timeLevel: Int): Int {
            if (timeLevel == 0) return 10
            if (timeLevel == 1) return 30
            if (timeLevel == 2) return 60
            if (timeLevel == 3) return 120
            return 120
        }

        var timeLevels = arrayOf(10, 30, 60, 120)
    }

    override fun changeDialogOutside(isCanClick: Boolean) {
        super.changeDialogOutside(isCanClick)
        dialog.setCancelable(isCanClick)
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getLayoutView(): View {
        svBinding = H5140TimeLevelDialogBinding.inflate(LayoutInflater.from(context))
        svBinding.tvCancel4VolumeGear.setOnClickListener(this)
        svBinding.tvSure4VolumeGear.setOnClickListener(this)
        svBinding.wpValue4VolumeGear.selectedItemPosition = 4
        svBinding.wpValue4VolumeGear.setOnItemSelectedListener { _, _, position ->
            this.selectedVolumeGear = volumeGearsList[position]
        }
        return svBinding.root
    }

    /**
     * 显示音量档位的设置弹窗
     */
    fun toSetVolume(curVolumeLevel: Int?, onSelectVolumeListener: VolumeLeveView.OnSelectVolumeListener) {
        curVolumeLevel?.let {
            this.selectedVolumeGear = it
        }
        this.onSelectVolumeListener = onSelectVolumeListener
        //设置选择齿轮数据
        val disValuesShowStrList = ArrayList<String>()
        for (value in volumeGearsList) {
            disValuesShowStrList.add(ResUtil.getStringFormat(R.string.minute_pattern, timeLevels[value]))
        }
        svBinding.wpValue4VolumeGear.data = disValuesShowStrList
        if (selectedVolumeGear < 0) {
            selectedVolumeGear = 0
        }
        if (selectedVolumeGear > 3) {
            selectedVolumeGear = 3
        }
        svBinding.wpValue4VolumeGear.selectedItemPosition = selectedVolumeGear
        show()
    }

    override fun onClick(v: View?) {
        if (ClickUtil.getInstance.clickQuick()) {
            return
        }
        v?.let {
            when (it) {
                svBinding.tvCancel4VolumeGear -> {
                    hide()
                }

                svBinding.tvSure4VolumeGear -> {
                    hide()
                    onSelectVolumeListener?.onSelectVolumeLevel(selectedVolumeGear)
                }

                else -> {}
            }
        }
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth() * 335 / 375
    }
}
