package com.govee.h5140.iotop

import com.govee.base2home.iot.AbsCmd
import com.govee.base2light.pact.iot.AbsIotOpV1

/**
 * <AUTHOR>
 * @date created on 2022/06/15
 * @description 5106设备iot操作管理类
 */
class IotOpManager : AbsIotOpV1() {

    override fun getCmd4Pt(): String {
        return Cmd5140.ptReal
    }

    override fun getCmd4DeviceInfo(): AbsCmd {
        return CmdStatus()
    }

    override fun getAutoStatusCmd(): String {
        return Cmd5140.status
    }

    override fun getReadCmdParserKey(cmd: String): String {
        return Cmd5140.getCmdReadParseKey(cmd)
    }
}