package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsOnlyReadSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventDeviceId.Companion.sendFail
import com.govee.h5140.ble.event.EventDeviceId.Companion.sendSuc

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取deviceId的指令，并解析其回复
 */
class CommandDeviceId : AbsOnlyReadSingleController() {

    override fun fail() {
        sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_DEVICE_ID
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        //address倒置
        val addressBytes = ByteArray(8)
        System.arraycopy(validBytes, 0, addressBytes, 0, addressBytes.size)
        var deviceId = BleUtil.toAddressBytes(addressBytes, false)
        if (deviceId.startsWith("00:00:")) {
            //需要移除掉前面的无效字段
            deviceId = deviceId.replaceFirst("00:00:".toRegex(), "")
        }
        sendSuc(isWrite, commandType, proType, deviceId)
        return true
    }
}