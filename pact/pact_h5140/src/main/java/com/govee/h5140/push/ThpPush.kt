package com.govee.h5140.push

import android.app.Notification
import com.govee.base2home.push.IPush
import com.govee.base2home.push.PushUtil
import com.govee.base2kt.ext.toJson
import com.govee.h5140.Constants5140
import com.govee.h5140.item.Support.supportPush
import com.govee.push.NotificationConfig
import com.govee.push.NotifyManager
import com.govee.push.PushData
import com.govee.push.event.NotificationClickEvent
import com.govee.ui.R
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.ResUtil

/**
 * <AUTHOR>
 * @date created on 2022/6/10
 * @description 5106推送消息的监听及操作类
 */
class ThpPush : IPush {

    override fun checkPushData(sku: String, goodType: Int, messageType: String): Boolean {
        return supportPush(goodType, sku)
    }

    override fun showPushData(pushData: PushData) {
        val data = pushData.data
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "showPush() data = ${data.toJson()}")
        }
        val msg = JsonUtil.fromJson(data, Msg::class.java)
        if (msg == null) {
            SafeLog.d(TAG) { "h5140解析推送内容失败！" }
        }
        val title = msg.deviceName
        val content = msg.message
        val notifyId = PushUtil.getNotifyId()
        val channelId = Constants5140.CHANNEL_ID
        val channelStr = ResUtil.getString(R.string.channel_id_warning_string)
        val iconRes = R.mipmap.new_icon_push
        val config =
            NotificationConfig(title, content, iconRes, true, false, false, Notification.DEFAULT_ALL, channelId, channelStr)
        NotifyManager.getInstance().showNotify(BaseApplication.getContext(), notifyId, config)
    }

    override fun checkConsumePush(event: NotificationClickEvent): Boolean {
        return false
    }

    companion object {
        private val TAG = ThpPush::class.java.simpleName
    }
}
