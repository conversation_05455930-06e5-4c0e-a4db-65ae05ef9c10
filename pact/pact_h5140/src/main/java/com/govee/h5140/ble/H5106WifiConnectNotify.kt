package com.govee.h5140.ble

import com.govee.base2newth.WifiConnectNotify

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description wifi连接状态通知
 */
class H5106WifiConnectNotify : WifiConnectNotify {

    private val serviceUuid: String
    private val characteristicUuid: String

    constructor(serviceUuid: String, characteristicUuid: String, sku: String) : super(sku) {
        this.serviceUuid = serviceUuid
        this.characteristicUuid = characteristicUuid
    }

    override fun checkCharacteristic(serviceUUID: String, characteristicUUID: String): Boolean {
        return serviceUuid == serviceUUID && characteristicUuid == characteristicUUID
    }
}