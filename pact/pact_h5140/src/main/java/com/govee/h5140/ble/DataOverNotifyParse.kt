package com.govee.h5140.ble

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsNotifyParse
import com.govee.base2newth.data.controller.BleProtocol
import com.govee.base2newth.data.controller.EventDataOver

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 5106设备数据读取完成的主动上报解析器
 */
class DataOverNotifyParse(private val serviceUuid: String, private val characteristicUuid: String) : AbsNotifyParse() {

    override fun getSubCommandType(): Byte {
        return BleProtocol.value_notify_data_over
    }

    override fun checkCharacteristic(serviceUUID: String, characteristicUUID: String): Boolean {
        return serviceUuid == serviceUUID && characteristicUuid == characteristicUUID
    }

    override fun parseBytes(bytes17: ByteArray) {
        val packsBytes = ByteArray(2)
        System.arraycopy(bytes17, 0, packsBytes, 0, 2)
        val packs = BleUtil.getSignedInt(packsBytes, true)
        EventDataOver.sendEventDataOver(packs)
    }
}