package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventTemWarning

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读/写温度预警信息的指令，并解析其回复
 */
class CommandTemWarning : AbsSingleController {

    companion object {
        private const val TAG = "CommandTemWarning"
    }

    /**
     * 是否开启预警
     */
    private var openWarning = false
    private var minTem = 0
    private var maxTem = 0

    /**
     * 写操作
     */
    constructor(openWarning: Boolean, minTem: Int, maxTem: Int) : super(true) {
        this.openWarning = openWarning
        this.minTem = minTem
        this.maxTem = maxTem
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray {
        val openWarningValue = if (openWarning) 1.toByte() else 0.toByte()
        val minTempValues = BleUtil.getSignedBytesFor2(minTem, false)
        val maxTempValues = BleUtil.getSignedBytesFor2(maxTem, false)
        val byteArr = byteArrayOf(openWarningValue, minTempValues[0], minTempValues[1], maxTempValues[0], maxTempValues[1])
        return byteArr
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventTemWarning.sendWriteResult(suc, commandType, proType, openWarning, minTem, maxTem)
        return true
    }

    override fun fail() {
        EventTemWarning.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_TEM_WARNING
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val openWarning = validBytes[0].toInt() == 1
        val minTem = BleUtil.convertTwoBytesToShort(validBytes[1], validBytes[2])
        val maxTem = BleUtil.convertTwoBytesToShort(validBytes[3], validBytes[4])
        EventTemWarning.sendSuc(isWrite, commandType, proType, openWarning, minTem, maxTem)
        return true
    }
}