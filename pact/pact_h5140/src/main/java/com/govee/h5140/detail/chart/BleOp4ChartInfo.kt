package com.govee.h5140.detail.chart

import android.os.Handler
import android.os.Looper
import android.os.Message
import android.util.Log
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ext.toJson
import com.govee.base2light.ble.ota.v3.OtaOpV3
import com.govee.base2newth.AbsSingleController
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.data.Co2LevelInfo
import com.govee.base2newth.data.Co2WarnInfo
import com.govee.base2newth.data.DataConfig
import com.govee.base2newth.data.DataTimeSet
import com.govee.base2newth.data.H5140C02WarnConfig
import com.govee.base2newth.data.IBleOp4Detail
import com.govee.base2newth.data.IBleOp4DetailResult
import com.govee.base2newth.data.IBleOpData
import com.govee.base2newth.db.DbController
import com.govee.ble.BleController
import com.govee.ble.event.BTStatusEvent
import com.govee.ble.event.EventBleConnect
import com.govee.h5140.Constants5140
import com.govee.h5140.ble.ThpBleCommManager
import com.govee.h5140.ble.ThpBleCommManager.Companion.getInstance
import com.govee.h5140.ble.command.CommandCo2SetGrade
import com.govee.h5140.ble.command.CommandCo2Warning
import com.govee.h5140.ble.command.CommandHardVersion
import com.govee.h5140.ble.command.CommandHumCali
import com.govee.h5140.ble.command.CommandSoftVersion
import com.govee.h5140.ble.command.CommandSyncTime
import com.govee.h5140.ble.command.CommandTemCali
import com.govee.h5140.ble.command.CommandTemUnit
import com.govee.h5140.ble.command.CommandTimeZone
import com.govee.h5140.ble.event.EventCo2SetGrade
import com.govee.h5140.ble.event.EventCo2Warning
import com.govee.h5140.ble.event.EventHardVersion
import com.govee.h5140.ble.event.EventHumCali
import com.govee.h5140.ble.event.EventSoftVersion
import com.govee.h5140.ble.event.EventSyncTime
import com.govee.h5140.ble.event.EventTemCali
import com.govee.h5140.ble.event.EventTimeZone
import com.govee.thnew.ble.controller.Controller4WifiHv
import com.govee.thnew.ble.controller.Controller4WifiSv
import com.govee.thnew.ble.event.Event4WifiHv
import com.govee.thnew.ble.event.Event4WifiSv
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.CaughtRunnable
import com.ihoment.base2app.util.ThreadPoolUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2022/06/01
 * @description 5106图表信息页面ble操作类
 */
class BleOp4ChartInfo : IBleOp4Detail {

    private val ext: Ext4Ci
    private var opResult: IBleOp4DetailResult?
    private var iBleOpOver: IBleOpOver?
    private val handler: Handler
    private var inComm = false
    private var destroy = false
    private var step: Int

    companion object {
        private const val TAG = "BleOp4ChartInfo"
        private const val what_check_step = 1000
        private const val step_ble_def = -1
        private const val step_ble_connecting = 1
        private const val step_ble_disconnected = 2
        private const val step_ble_unable = 3
        private const val step_ble_reading_device_info = 4
        private const val step_ble_info_over = 5
        private const val step_ble_loading_data = 6
        var is_load_all_data = false
    }

    constructor(ext: Ext4Ci, opResult: IBleOp4DetailResult?, iBleOpOver: IBleOpOver?) {
        this.ext = ext
        this.opResult = opResult
        this.iBleOpOver = iBleOpOver
        this.handler = object : Handler(Looper.getMainLooper()) {
            override fun handleMessage(msg: Message) {
                super.handleMessage(msg)
                val what = msg.what
                doWhat(what)
            }
        }
        this.step = step_ble_def
    }

    val thBle: ThpBleCommManager
        get() = getInstance()

    private fun doWhat(what: Int) {
        if (what == what_check_step) {
            checkStep()
        }
    }

    private fun checkStep() {
        if (step == step_ble_disconnected) {
            if (opResult != null) {
                opResult!!.bleDisconnect()
            }
        } else if (step == step_ble_unable) {
            if (opResult != null) {
                opResult!!.bleUnable()
            }
        } else if (step == step_ble_info_over) {
            if (opResult != null) {
                opResult!!.infoOver()
            }
        } else if (step == step_ble_loading_data) {
            if (opResult != null) {
                opResult!!.dataLoading()
            }
        } else {
            if (opResult != null) {
                opResult!!.inBleComming()
            }
        }
    }

    override fun onDestroy() {
        if (!destroy) {
            registerEvent(false)
            destroy = true
            opResult = null
            iBleOpOver = null
            //关闭蓝牙
            BleController.getInstance().toBtClose()
        }
    }

    override fun connectBle() {
        SafeLog.d("test_ble") { "---------->connectBle" }
        if (BaseApplication.getBaseApplication().isInBackground) {
            //若当前处于后台，则不尝试连接
            updateStep(step_ble_disconnected)
            return
        }
        if (AbsThpBleData.isLoadThcd4Compare) {
            return
        }
        //非通信状态下，不处理
        if (!inComm) {
            return
        }
        //设备已连接，不需要再连接一次
        if (BleController.getInstance().isConnected && ext.bleAddress == BleController.getInstance().connectedBleAddress) {
            EventBleConnect.sendEventBleConnect(
                EventBleConnect.Type.ble_connect_suc,
                ext.bleAddress,
                0
            )
            return
        }
        val connectBle = thBle.connectBle(ext.bleAddress)
        SafeLog.d("test_ble") { "---------->connectBle=$connectBle" }
        if (connectBle) {
            updateStep(step_ble_connecting)
        } else {
            updateStep(step_ble_unable)
        }
    }

    private fun updateStep(step: Int) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "updateStep() step = $step")
        }
        this.step = step
        handler.sendEmptyMessage(what_check_step)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventBleConnect(event: EventBleConnect) {
        val connectSuc = event.connectSuc() && event.address == ext.bleAddress
        SafeLog.i(TAG, "onEventBleConnect() connectSuc = $connectSuc,isLoadThcd4Compare=${AbsThpBleData.isLoadThcd4Compare}")
        if (connectSuc) {
            if (AbsThpBleData.isLoadThcd4Compare) {
                return
            }
            updateStep(step_ble_reading_device_info)
            SafeLog.i(TAG, "startExtControllers()")
            thBle.startExtControllers(false, *waitingControllers)
        } else {
            updateStep(step_ble_disconnected)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventBTStatus(event: BTStatusEvent) {
        val btOpen = event.isBtOpen
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBTStatus() btOpen = $btOpen")
        }
        if (btOpen) {
            //蓝牙重新打开;重新尝试连接设备
            connectBle()
        } else {
            updateStep(step_ble_unable)
        }
    }

    private val waitingControllers: Array<AbsSingleController>
        get() = arrayOf(
            CommandSyncTime(),
            CommandTimeZone(),
            CommandHardVersion(),
            CommandSoftVersion(),
            CommandTemCali(),
            CommandCo2Warning(),
            CommandCo2SetGrade(),
            Controller4WifiSv(),
            Controller4WifiHv(),
            CommandHumCali(),
        )

    override fun inComm(canComm: Boolean) {
        inComm = canComm
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "inComm() step = $step ; canComm = $canComm")
        }
        registerEvent(canComm)
        if (!canComm) {
            //若当前处于不可操作,则通知界面
            if (iBleOpOver != null) {
                iBleOpOver!!.bleOpOver()
            }
        }
    }

    private fun registerEvent(register: Boolean) {
        if (register) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this)
            }
        } else {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventWifiSoftVersion(event: Event4WifiSv) {
        if (event.isResult) {
            val wifiSoftVersion: String = event.wifiSoftVersion
            SafeLog.i(TAG, "onEventWifiSoftVersion() mac = $wifiSoftVersion")
            ext.versionWifiSoft = event.wifiSoftVersion
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventWifiHardVersion(event: Event4WifiHv) {
        if (event.isResult) {
            val wifiHardVersion: String = event.wifiHardVersion
            SafeLog.i(TAG, "onEventWifiHardVersion() mac = $wifiHardVersion")
            ext.versionWifiHard = event.wifiHardVersion
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSyncTime(event: EventSyncTime) {
        val result = event.isResult
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSyncTime() result = $result")
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTimeZone(event: EventTimeZone) {
        val result = event.isResult
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventTimeZone() result = $result")
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventHardVersion(event: EventHardVersion) {
        val result = event.isResult
        if (result) {
            val hardVersion = event.hardVersion
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventHardVersion() hardVersion = $hardVersion")
            }
            ext.versionHard = hardVersion
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSoftVersion(event: EventSoftVersion) {
        val result = event.isResult
        if (result) {
            val softVersion = event.softVersion
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventSoftVersion() softVersion = $softVersion")
            }
            ext.versionSoft = softVersion
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTemCali(event: EventTemCali) {
        val result = event.isResult
        if (result) {
            val temCali = event.temCali
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventTemCali() temCali = $temCali")
            }
            ext.temCali = temCali
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventCo2Warning(event: EventCo2Warning) {
        val result = event.isResult
        if (result) {
            SafeLog.i(TAG, "EventCo2Warning() event = ${event.toJson()}")
            val warning = WarnConfig.read().queryWarningRangeByKey(ext.sku, ext.device)
            warning.co2Min = if (event.minCo2 < 400) 400 else event.minCo2
            warning.co2Max = event.maxCo2
            warning.co2Warning = event.openWarning
            WarnConfig.read().updateWarningRange(warning, false)
            H5140C02WarnConfig.updateCo2WarnInfo(
                ext.sku,
                ext.device,
                Co2WarnInfo(event.minCo2, event.maxCo2, event.openWarning, event.alarmInterval)
            )
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventCo2GradeLevel(event: EventCo2SetGrade) {
        val result = event.isResult
        if (result) {
            H5140C02WarnConfig.updateCo2LevelInfo(ext.sku, ext.device, Co2LevelInfo(event.minLevel, event.maxLevel))
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventHumCali(event: EventHumCali) {
        val result = event.isResult
        if (result) {
            val humCali = event.humCali
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventHumCali() humCali = $humCali")
            }
            ext.humCali = humCali
        }
        //再去读取温度单位和新版本亮度信息
        thBle.startExtControllers(true, CommandTemUnit())
        //从设备读写数据完毕
        infoOver()
        thBle.controllerEvent(event)
    }

    private fun infoOver() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "infoOver()")
            SafeLog.i("temp_h5140_chart") { "1---------->>读取其他信息完成，准备读取图表数据" }
        }
        updateStep(step_ble_info_over)
    }

    override fun checkBleLoadData() {
        SafeLog.i("temp_h5140_chart") { "2---------->>开始读取图表数据" }
        val connected = BleController.getInstance().isConnected
        SafeLog.i(TAG, "loadData() step = $step ; inComm = $inComm ; connected = $connected")
        if (connected) {
            val inDataOping = step == step_ble_loading_data
            if (inDataOping) {
                SafeLog.i("temp_h5140_chart") { "checkBleLoadData() inDataOping = $inDataOping" }
                return
            }
            updateStep(step_ble_loading_data)
            //读取数据
            ThreadPoolUtil.getThreadPool().execute(object : CaughtRunnable() {
                override fun runSafe() {
                    startReadData()
                }
            })
        } else {
            if (inComm) {
                connectBle()
            } else {
                if (iBleOpOver != null) {
                    iBleOpOver!!.ignoreBleReadData()
                }
            }
        }
    }

    /**
     * run in background thread
     */
    private fun startReadData() {
        //依据本地数据库数据，获取待查询的数据时间段
        val lastDataClearTime = DataConfig.read().getLastDataClearTime(ext.sku, ext.device)
        SafeLog.i("temp_h5140_chart") {
            "3---------->>lastDataClearTime=${
                TimeFormatM.getInstance().formatTimeToHMYMD(lastDataClearTime)
            }"
        }
        var dataTimeSet = DbController.queryDataTimeH5140(
            ext.sku,
            ext.device,
            lastDataClearTime,
            Constants5140.max_times_to_read_from_device
        )
        if (OtaOpV3.getInstance().inOta()) {
            Log.i(TAG, "startReadData: inOta return")
            return
        }
        if (is_load_all_data) {
            SafeLog.i("temp_h5140_chart") { "拉取全部数据，is_load_all_data=true" }
            dataTimeSet = DataTimeSet().apply {
                minStartTime = ThConsV1.min_valid_time_2022_01
                maxEndTime = System.currentTimeMillis()
            }
            is_load_all_data = false
        }
        if (dataTimeSet.minStartTime < ThConsV1.min_valid_time_2022_01) {
            dataTimeSet.minStartTime = ThConsV1.min_valid_time_2022_01
        }
        dataTimeSet.waitingDataSize = (dataTimeSet.maxEndTime - dataTimeSet.minStartTime + 1).toInt()
        SafeLog.i("temp_h5140_chart") {
            "3---------->>minStartTime=${
                TimeFormatM.getInstance().formatTimeToHMYMD(dataTimeSet.minStartTime)
            }"
        }
        SafeLog.i("temp_h5140_chart") {
            "3---------->>maxStartTime=${
                TimeFormatM.getInstance().formatTimeToHMYMD(dataTimeSet.maxEndTime)
            }"
        }
        SafeLog.i("temp_h5140_chart") { "3---------->>waitingDataSize=${dataTimeSet.waitingDataSize}" }
        opData.startDataOp(ext.sku, ext.device, dataTimeSet)
    }

    private val opData: IBleOpData
        get() = thBle.dataComm as IBleOpData

    override fun inBleing(): Boolean {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "inBleing() step = $step")
        }
        if (!inComm) {
            return false
        }
        if (step == step_ble_disconnected) {
            return false
        }
        return if (step == step_ble_unable) {
            false
        } else step != step_ble_info_over
    }

    override fun bleDataOver() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "bleDataOver() step = $step")
        }
        if (step == step_ble_loading_data) {
            step = step_ble_info_over
        }
    }
}