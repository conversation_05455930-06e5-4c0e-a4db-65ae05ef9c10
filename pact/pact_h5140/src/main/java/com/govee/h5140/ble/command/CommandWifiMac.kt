package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsOnlyReadSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventWifiMac

/**
 * <AUTHOR>
 * @date created on 2022/5/30
 * @description 从5106设备读取wifi mac的指令，并解析其回复
 */
class CommandWifiMac : AbsOnlyReadSingleController {

    private var commandType = BleProtocol.VALUE_SINGLE_WIFI_MAC

    constructor(commandType: Byte) {
        this.commandType = commandType
    }

    constructor() {}

    override fun fail() {
        EventWifiMac.sendFail(isWrite, getCommandType(), proType)
    }

    override fun getCommandType(): Byte {
        return commandType
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val macBytes = ByteArray(6)
        System.arraycopy(validBytes, 0, macBytes, 0, macBytes.size)
        val checkWifiMacValid = checkWifiMacValid(macBytes)
        if (checkWifiMacValid) {
            val mac = BleUtil.toAddressBytes(macBytes, true)
            EventWifiMac.sendSuc(isWrite, getCommandType(), proType, mac)
        } else {
            EventWifiMac.sendFail(isWrite, getCommandType(), proType)
        }
        return true
    }

    private fun checkWifiMacValid(macBytes: ByteArray): Boolean {
        //若mac地址字段值全部是0时，表明当前读取的mac是无效的
        var valid = false
        for (macByte in macBytes) {
            if (macByte.toInt() != 0) {
                valid = true
                break
            }
        }
        return valid
    }
}