package com.govee.h5140.detail

import android.app.Activity
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2newth.net.DataExportRequest
import com.govee.base2newth.net.DataExportResponse
import com.govee.base2newth.net.IThNet
import com.govee.ui.R
import com.govee.ui.ac.AbsExportDataAc
import com.ihoment.base2app.Cache
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2022/06/01
 * @description 5106设备导出数据页面
 */
class Activity4ExportData : AbsExportDataAc() {

    companion object {
        private val intervalMinutes = arrayOf(
            1, 15, 30, 60, 120,
            240, 360, 480, 720
        )

        /**
         * 跳转到数据导出界面
         */
        fun jump2ExportData(ac: Activity?, sku: String?, device: String?) {
            val bundle = makeAcBundle(sku, device)
            JumpUtil.jumpWithBundle(ac, Activity4ExportData::class.java, bundle)
        }
    }

    override fun getIntervalMintues(): Array<Int> {
        return intervalMinutes
    }

    override fun getExportOtherData(): String {
        return ResUtil.getString(R.string.h5106_export_other_th_data)

    }

    override fun applyExportData(email: List<String>, interval: Int, startTimeMills: Long, endTimeMills: Long, chooseOther: Boolean) {
        showExportDialog()
        var fahOpen = true
        DeviceListConfig.read().getDeviceByKey(sku, device)?.let {
            fahOpen =
                TemUnitConfig.read().isTemUnitFah(it.sku, it.device, it.versionHard, it.versionSoft)
        }
        val request = DataExportRequest(transactions.createTransaction(), email, interval, startTimeMills, endTimeMills, device, sku, fahOpen, chooseOther)
        Cache.get(IThNet::class.java).dataExport(request).enqueue(IHCallBack(request))
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onDataExportResponse(response: DataExportResponse) {
        if (!transactions.isMyTransaction(response)) return
        val request = response.getRequest()
        val exportAll = request.isExportAll
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onDataExportResponse() exportAll = $exportAll")
        }
        if (exportAll) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.export_all_data, ParamFixedValue.success)
        }
        toast(response.message)
        hideExportDialog()
    }

    override fun onErrorResponse(response: ErrorResponse) {
        super.onErrorResponse(response)
        hideExportDialog()
    }

    override fun supportTimerExport(): Boolean {
        return true
    }
}
