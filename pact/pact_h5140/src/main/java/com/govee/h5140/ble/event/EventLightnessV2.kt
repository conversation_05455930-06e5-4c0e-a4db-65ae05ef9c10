package com.govee.h5140.ble.event

import androidx.annotation.Keep
import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读写屏幕亮度的事件
 */
class EventLightnessV2 : AbsControllerEvent {

    constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) : super(
        result,
        write,
        commandType,
        proType,
        !write
    )

    /**
     * 亮度等级值
     */
    var lightnessV2: LightnessV2? = null

    companion object {
        fun sendWriteResult(
            result: Boolean,
            commandType: Byte,
            proType: Byte,
            lightnessV2: LightnessV2
        ) {
            val event = EventLightnessV2(result, true, commandType, proType)
            event.lightnessV2 = lightnessV2
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventLightnessV2(false, write, commandType, proType))
        }

        fun sendSuc(write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte, lightnessV2: LightnessV2) {
            val event = EventLightnessV2(true, write, commandType, proType)
            event.lightnessV2 = lightnessV2
            EventBus.getDefault().post(event)
        }
    }

    @Keep
    class LightnessV2 {
        /**
         * 白天时间范围
         */
        var dayTimeRange = "0700123b"

        /**
         * 白天亮度值
         * 备注：1(弱)-->7(强)
         */
        var dayLtValue = 1

        /**
         * 白天是否显示亮度
         * 备注：0->关闭，1->开启
         */
        var dayLtOpen = 0

        /**
         * 黑夜时间范围
         * 备注：白+黑=24h
         */
        var nightTimeRange = "1300063b"

        /**
         * 黑夜亮度值
         * 备注：1(弱)-->7(强)
         */
        var nightLtValue = 1

        /**
         * 黑夜是否显示亮度
         * 备注：0->关闭，1->开启
         */
        var nightLtOpen = 0
    }
}