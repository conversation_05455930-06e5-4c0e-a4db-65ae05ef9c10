package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import com.govee.h5140.Constants5140
import com.ihoment.base2app.infra.SafeLog
import org.greenrobot.eventbus.EventBus

/**
 * 从5140设备读取Co2报警等级相关信息的事件
 */
class EventCo2SetGrade(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, !write) {

    var minLevel = Constants5140.CO2_GRADE_MIN
    var maxLevel = Constants5140.CO2_GRADE_MAX

    companion object {

        fun sendWriteResult(
            result: Bo<PERSON>an,
            commandType: Byte,
            proType: Byte,
            minLevel: Int,
            maxLevel: Int
        ) {
            val event = EventCo2SetGrade(result, true, commandType, proType)
            event.minLevel = minLevel
            event.maxLevel = maxLevel
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventCo2SetGrade(false, write, commandType, proType))
        }

        fun sendSuc(
            write: Boolean,
            commandType: Byte,
            proType: Byte,
            minLevel: Int,
            maxLevel: Int
        ) {
            val event = EventCo2SetGrade(true, write, commandType, proType)
            event.minLevel = minLevel
            event.maxLevel = maxLevel
            EventBus.getDefault().post(event)
        }
    }

}