package com.govee.h5140.ble.command

import com.govee.base2newth.AbsSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventLightness
import com.ihoment.base2app.infra.LogInfra

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取/写入屏幕亮度的指令，并解析其回复
 */
class CommandLightness : AbsSingleController {

    companion object {
        private const val TAG = "CommandLightness"
        const val WEAK = 0x01
        const val HIGH = 0x07
    }

    private var slLevel = WEAK

    /**
     * 写操作
     */
    constructor(lightnessLevel: Int) : super(true) {
        this.slLevel = checkSlLevel(lightnessLevel)
    }

    private fun checkSlLevel(slLevel: Int): Int {
        var realLevel = slLevel
        if (slLevel < WEAK) {
            realLevel = WEAK
            LogInfra.Log.e(TAG, "checkSlLevel() level is not in range! level = $slLevel")
        } else if (slLevel > HIGH) {
            realLevel = HIGH
            LogInfra.Log.e(TAG, "checkSlLevel() level is not in range! level = $slLevel")
        }
        return realLevel
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray {
        return byteArrayOf(slLevel.toByte())
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventLightness.sendWriteResult(suc, commandType, proType, slLevel)
        return true
    }

    override fun fail() {
        EventLightness.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_DISPLAY_LIGHTNESS
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        EventLightness.sendSuc(isWrite, commandType, proType, validBytes[0].toInt())
        return true
    }
}