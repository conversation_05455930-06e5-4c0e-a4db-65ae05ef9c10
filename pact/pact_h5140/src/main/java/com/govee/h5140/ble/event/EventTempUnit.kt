package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/9/20
 * @description 从5106设备读取温度显示单位的事件
 */
class EventTempUnit(result: Bo<PERSON>an, write: Boolean, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, !write) {

    /**
     * 0为摄氏度，1为华氏度
     */
    var temUnit: Int = 0

    /**
     * 是否当前event只做刷新（从sp保存值里面读取），用于分享设备设置页
     */
    var onlyRefreshFromSp = false

    companion object {
        /**
         * 用来做从sp读取的刷新操作
         */
        fun sendRefreshEvent() {
            EventBus.getDefault().post(EventTempUnit(true, true, 0, 0).apply {
                onlyRefreshFromSp = true
            })
        }

        fun sendSuc(write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte, tempUnit: Int) {
            val event = EventTempUnit(true, write, commandType, proType)
            event.temUnit = tempUnit
            EventBus.getDefault().post(event)
        }

        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte, tempUnit: Int) {
            val event = EventTempUnit(result, true, commandType, proType)
            event.temUnit = tempUnit
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventTempUnit(false, write, commandType, proType))
        }
    }
}