package com.govee.h5140.detail.setting

import androidx.annotation.Keep
import com.govee.h5140.Constants5140
import com.govee.h5140.ble.event.EventLightnessV2

/**
 * <AUTHOR>
 * @date created on 2022/06/01
 * @description 5106设备设置页所需的扩展实体类
 */
@Keep
class Ext4St {

    var goodsType = 0
    var sku: String = ""
    var device: String = ""
    var deviceName: String = ""
    var address: String = ""
    var versionSoft: String = ""
    var versionHard: String = ""

    //pm2.5预警相关
    var co2AlarmMin = Constants5140.CO2_MIN_VALUE
    var co2AlarmMax = Constants5140.CO2_MAX_VALUE
    var co2AlarmSwitch = false
    var alarmInterval = 10 //报警间隔
    var airQualityOnOff = false

    //温湿度相关
    var temMin = Constants5140.TEM_MIN_VALUE * 100
    var temMax = Constants5140.TEM_MAX_VALUE * 100
    var temWarning = false
    var humMin = Constants5140.HUM_MIN_VALUE * 100
    var humMax = Constants5140.HUM_MAX_VALUE * 100
    var humWarning = false
    var temCali = 0
    var humCali = 0

    //其他设置相关(屏幕展示，时间制式等)
    var sdType = 0
    var tfType = 0
    var slLevel = 0

    //5.3.1迭代，多出的属性
    //温度单位
    var tuType = 0

    //屏幕亮度相关
    var slV2: EventLightnessV2.LightnessV2? = null

    // 蜂鸣开关
    var soundSwitch = false
    // 蜂鸣音量大小
    var soundLevel = 0

    var co2GradeMin = Constants5140.CO2_GRADE_MIN
    var co2GradeMax = Constants5140.CO2_GRADE_MAX

    var co2NotifySwitch = false

    // 勿扰模式
    var dndIsOpen = false
    var dndStartHour = 22
    var dndStartMinute = 0
    var dndEndHour = 6
    var dndEndMinute = 0
}