package com.govee.h5140.widget

import android.app.Activity
import android.app.NotificationManager
import android.content.Context
import android.content.Intent
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.text.TextUtils
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.FrameLayout
import com.govee.base2home.Constant4L5
import com.govee.base2home.account.AccountEvent.ApplyCodeEvent
import com.govee.base2home.account.LoginActivity
import com.govee.base2home.account.VerifyEmailActivity
import com.govee.base2home.account.VerifyEmailActivity.Companion.jump2VerifyEmailAc
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.community.msg.AddMailRequest
import com.govee.base2home.community.msg.AddMailResponse
import com.govee.base2home.community.msg.DelMailRequest
import com.govee.base2home.community.msg.EmailDeleteResponse
import com.govee.base2home.community.msg.EmailModel
import com.govee.base2home.community.msg.IMsgNet
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.main.OfflineDeviceListConfig
import com.govee.base2home.main.about.SpanTextView
import com.govee.base2home.util.ClickUtil
import com.govee.base2home.util.StrUtil
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.visible
import com.govee.base2newth.deviceitem.Ext4Gw
import com.govee.base2newth.net.IThNet
import com.govee.base2newth.net.Request4DeviceBindEmail
import com.govee.base2newth.net.Request4DeviceEmails
import com.govee.base2newth.net.Request4GetSettings
import com.govee.base2newth.net.Request4Setting
import com.govee.base2newth.net.Response4DeviceBindEmail
import com.govee.base2newth.net.Response4DeviceEmails
import com.govee.base2newth.net.Response4GetSettings
import com.govee.base2newth.other.Dialog4SelVolumeLevel
import com.govee.base2newth.other.VolumeLeveView.OnSelectVolumeListener
import com.govee.h5140.Constants5140
import com.govee.h5140.databinding.H5140WarnSettingNewBinding
import com.govee.h5140.detail.setting.Ext4St
import com.govee.h5140.dialog.WarnConfirmDialog
import com.govee.home.account.config.AccountConfig
import com.govee.ui.R
import com.govee.ui.adapter.ExportEmailListAdapter
import com.govee.ui.component.RequestStatusUI
import com.govee.ui.component.export.EmailExportListUI
import com.govee.ui.dialog.ConfirmDialog
import com.govee.ui.dialog.ConfirmDialogV3
import com.govee.ui.event.EmailOpeateEvent
import com.govee.ui.event.TimerExportDelEvent
import com.govee.ui.event.TimerExportDelEvent.TimerStatus
import com.govee.util.recordUseCount
import com.ihoment.base2app.Cache
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.ext.setVisibility
import com.ihoment.base2app.ext.toast
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.NetUtil
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.ToastUtil
import com.tk.mediapicker.utils.DensityUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2024/9/25
 * @description 温湿度计报警方式设置
 */
class H5140ThWarnSettingViewNew @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    companion object {
        /**
         * 是否正在设置通知权限
         */
        var isSettingNotify = false
            private set
    }

    private val binding: H5140WarnSettingNewBinding
    private val transactions by lazy {
        Transactions()
    }
    private var sku = ""
    private var device = ""
    private var notificationChannel = ""

    //当前要删除的邮箱
    private var deleteEmailModel: EmailModel? = null

    //当前删除的item是否定时导出的最后一个邮箱
    private var timerStatus = TimerStatus.UNCONTAIN

    private var cacheSettings: Request4Setting? = null
    private var firstBind = true

    init {
        binding = H5140WarnSettingNewBinding.inflate(LayoutInflater.from(context), this, true)
        EventBus.getDefault().takeIf { !it.isRegistered(this) }?.register(this)
        //设置点击监听
        setListener()
    }

    fun getSoundLevelView(): View {
        return binding.llSetSound
    }

    fun getTvSoundAlarmTime(): View {
        return binding.tvSoundAlarmTime
    }

    /**
     * 初始化信息
     * 备注：必须在其他方法调用前调用
     */
    fun initDeviceInfo(sku: String, device: String, ext4St: Ext4St) {
        this.sku = sku
        this.device = device
        soundSwitch = ext4St.soundSwitch
        soundLevel = ext4St.soundLevel
        //普通告警(未登录则本地控制，登录则用服务端数据)
        cacheSettings = getLocalSettings()
        getClosePushRemindText()
        notificationChannel = Constants5140.CHANNEL_ID
        //普通告警
        binding.ivCommonAlarmSwitch4ThWarn.isSelected = cacheSettings?.normalPushOnOff.isTrue()
        setNotificationSettings(cacheSettings)
        //邮箱告警(邮箱报警需登录且绑定网关才能使用)
        binding.eeluReceiveEmail4ThWarn.init4BeforeUse(ExportEmailListAdapter.Type.ALARM)
        binding.eeluReceiveEmail4ThWarn.initView(context)
        updateEmailStatusUi(cacheSettings?.emailWarningOnOff == true)
        updateSoundUi()
        if (!AccountConfig.read().isHadToken) {
            return
        }
        if (firstBind) {
            requestDeviceEmails(true)
            firstBind = false
        }
        //更新服务端的漏水告警信息
        getSettings()
        binding.llSetSound.visible()
    }

    private fun updateSoundUi() {
        binding.tvSoundAlarmTime.text = getSoundLevelText(soundLevel)
        binding.ivSoundAlarmSwitch.isSelected = soundSwitch
        Handler(Looper.getMainLooper()).postDelayed({
            binding.tvSoundAlarmTime.setVisibility(binding.ivSoundAlarmSwitch.isSelected)
        }, 100)
    }

    private fun getSoundLevelText(level: Int): String {
        val textResId = when (level) {
            0 -> {
                com.govee.ui.R.string.h5043_volume_mute
            }

            1 -> {
                com.govee.ui.R.string.h721214_low
            }

            2 -> {
                com.govee.ui.R.string.h721214_medium
            }

            else -> {
                com.govee.ui.R.string.h721214_high
            }
        }
        return ResUtil.getString(textResId)
    }

    /**
     * 设置背景等信息
     */
    fun setBackgroundInfo(needBackGround: Boolean = true, needBottomDivider: Boolean) {

    }

    /**
     * 其他功能控件
     */
    fun addOtherContent(otherContentView: View) {
        binding.flOtherContentContainer4ThWarn.removeAllViews()
        val lp = LayoutParams(LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT)
        binding.flOtherContentContainer4ThWarn.addView(otherContentView, lp)
        binding.flOtherContentContainer4ThWarn.visibility = VISIBLE
        binding.spOtherContentPadding4ThWarn.visibility = VISIBLE
    }

    /**
     * 获取缓存的settings信息
     * 备注:卡片页通用设备
     */
    private fun getLocalSettings(): Request4Setting? {
        if (AccountConfig.read().isHadToken) {
            DeviceListConfig.read().getDeviceByKey(sku, device)?.let {
                val setting = JsonUtil.fromJson(it.deviceExt.deviceSettings, Request4Setting::class.java)
                return setting
            }
        } else {
            OfflineDeviceListConfig.read().getDevices(sku, device)?.let {
                JsonUtil.fromJson(it.deviceExt.deviceSettings, Request4Setting::class.java)
                    ?.let { setting ->
                        return setting.apply {
                            //默认为打开
                            if (normalPushOnOff == null) {
                                normalPushOnOff = true
                            }
                        }
                    }
            }
        }
        return null
    }

    fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == VerifyEmailActivity.key_request_code_verify_code_ac && resultCode == Activity.RESULT_OK && data != null) {
            val type = data.getIntExtra(VerifyEmailActivity.key_verity_type, 0)
            val account = data.getStringExtra(VerifyEmailActivity.key_verity_email)
            if (account == null || TextUtils.isEmpty(account)) {
                return
            }
            val newEmail = account.trim { it <= ' ' }
            if (TextUtils.isEmpty(newEmail)) {
                ToastUtil.getInstance().toast(R.string.invalid_input)
                return
            }
            //请求接口
            if (type == VerifyEmailActivity.type_notify_email_add) {
                //添加新邮箱，验证完成后回来
                showLoading()
                val request = AddMailRequest(transactions.createTransaction(), newEmail)
                Cache.get(IMsgNet::class.java).addNotificationEMail(request)
                    .enqueue(IHCallBack(request))
            }
        }
    }

    private var soundSwitch = false
    private var soundLevel = 0

    private fun setListener() {
        binding.tvSoundAlarmTime.clickDelay {
            showChoseSoundSizeDialog(soundLevel)
        }
        binding.tvSoundAlarmQuest.clickDelay {
            showSoundTipsDialog()
        }
        binding.ivSoundAlarmSwitch.clickDelay {
            mSetSoundLevelListener?.onSoundLevelChange(!soundSwitch, soundLevel)
        }
        binding.ivCommonAlarmSwitch4ThWarn.clickDelay {
            val isAppRemind = !binding.ivCommonAlarmSwitch4ThWarn.isSelected
            if (AccountConfig.read().isHadToken) {
                showLoading()
                val request = Request4Setting(transactions.createTransaction(), sku, device).apply {
                    normalPushOnOff = isAppRemind
                }
                Cache.get(IThNet::class.java).updateSetting(request).enqueue(IHCallBack(request))
            } else {
                OfflineDeviceListConfig.read().getDevices(sku, device)?.let {
                    JsonUtil.parseToHashmap(it.deviceExt.deviceSettings)?.let { settingMap ->
                        //更新本地数据
                        settingMap["normalPushOnOff"] = isAppRemind
                        it.deviceExt.deviceSettings = JsonUtil.toJson(settingMap)
                        OfflineDeviceListConfig.read().addOfflineDevice(it)
                        //更新显示
                        binding.ivCommonAlarmSwitch4ThWarn.isSelected = isAppRemind
                        cacheSettings?.apply {
                            normalPushOnOff = isAppRemind
                        }
                        setNotificationSettings(cacheSettings)
                    }
                }
            }
            //埋点
            recordUseCount(
                sku,
                if (isAppRemind) ParamFixedValue.turn_on_leak_water_warn_normal else ParamFixedValue.turn_off_leak_water_warn_normal
            )
        }
        binding.tvImportantLevel4OffWarn.clickDelay {
            val intent = Intent(Settings.ACTION_CHANNEL_NOTIFICATION_SETTINGS).apply {
                putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
                putExtra(Settings.EXTRA_CHANNEL_ID, notificationChannel)
            }
            context.startActivity(intent)
            //进入系统通知权限设置
            isSettingNotify = true
        }
        binding.ivEmailAlarmSwitch4ThWarn.clickDelay {
            //登录才能进行相关操作
            if (!AccountConfig.read().isHadToken) {
                ConfirmDialogV3.showConfirmDialog(
                    context,
                    R.string.login_first_label,
                    R.string.cancel,
                    R.string.to_login_now
                ) { LoginActivity.jump2LoginAc(context, "", false) }
                return@clickDelay
            }
            //纯蓝牙设备须要绑定网关才可使用
            if (Constant4L5.isH5151SubSku(sku)) {
                DeviceListConfig.read().getDeviceByKey(sku, device)?.let {
                    val isBindGw = JsonUtil.fromJson(it.deviceExt.deviceSettings, Ext4Gw::class.java)
                        ?.isBindGateway() ?: false
                    if (!isBindGw) {
                        toast(ResUtil.getString(R.string.th_bind_gateway_hint))
                        return@clickDelay
                    }
                }
            }
            showLoading()
            val isEmailOpen = !binding.ivEmailAlarmSwitch4ThWarn.isSelected
            val request = Request4Setting(transactions.createTransaction(), sku, device).apply {
                emailWarningOnOff = isEmailOpen
            }
            Cache.get(IThNet::class.java).updateSetting(request).enqueue(IHCallBack(request))
            //埋点
            recordUseCount(
                sku,
                if (isEmailOpen) ParamFixedValue.turn_on_leak_water_warn_email else ParamFixedValue.turn_off_leak_water_warn_email
            )
        }
        binding.eeluReceiveEmail4ThWarn.clickItemListener = { emailModel ->
            val hasSelectedEmails = binding.eeluReceiveEmail4ThWarn.getSelectEmails()
            val newSelectEmails = arrayListOf<EmailModel>()
            if (emailModel.isSelect) {
                newSelectEmails.apply {
                    addAll(hasSelectedEmails)
                    add(emailModel)
                }
            } else {
                for (email in hasSelectedEmails) {
                    if (email.emailId != emailModel.emailId) {
                        newSelectEmails.add(email)
                    }
                }
            }
            showLoading()
            val emailIds = arrayListOf<Int>()
            for (email in newSelectEmails) {
                emailIds.add(email.emailId)
            }
            val request = Request4DeviceBindEmail(
                transactions.createTransaction(),
                sku,
                device,
                emailIds.toIntArray()
            )
            Cache.get(IThNet::class.java).deviceBindEmail(request).enqueue(IHCallBack(request))
        }
        binding.eeluReceiveEmail4ThWarn.onExportEmailUIListener =
            object : EmailExportListUI.OnExportEmailUIListener {
                override fun addClick(datas: MutableList<EmailModel>) {
                    jump2VerifyEmailAc(
                        context as Activity,
                        VerifyEmailActivity.type_notify_email_add,
                        "",
                        datas,
                        hideCloseIcon = true
                    )
                }

                override fun retryRequest() {
                    requestDeviceEmails(true)
                }

                override fun delete(
                    position: Int,
                    emails: EmailModel,
                    datas: MutableList<EmailModel>
                ) {
                    timerStatus = TimerExportDelEvent.isCurrentTimerEndEmail(null, emails.email)
                    ConfirmDialog.showConfirmDialog(
                        context,
                        ResUtil.getStringFormat(
                            R.string.email_delete_dialog_content,
                            if (!TextUtils.isEmpty(emails.email)) emails.email else ""
                        ),
                        ResUtil.getString(R.string.no),
                        ResUtil.getString(R.string.yes)
                    ) {
                        showLoading()
                        deleteEmailModel = emails
                        val request =
                            DelMailRequest(transactions.createTransaction(), emails.emailId)
                        Cache.get(IMsgNet::class.java).deleteNotificationEMail(emails.emailId)
                            .enqueue(IHCallBack(request))
                    }
                }

                override fun verifyEmail(position: Int, emails: EmailModel) {
                    jump2VerifyEmailAc(
                        context as Activity,
                        VerifyEmailActivity.type_verify_account,
                        emails.email
                    )
                }
            }
    }

    private fun showSoundTipsDialog() {
        WarnConfirmDialog.showConfirmDialog(
            context,
            ResUtil.getString(R.string.b2light_hint_title),
            ResUtil.getString(R.string.h5140_sound_push_tips),
            ResUtil.getString(R.string.dbgw_i_know),
        )
    }

    private val selectVolumeLevelDialog by lazy {
        Dialog4SelVolumeLevel.createDialog(getContext())
    }

    private fun showChoseSoundSizeDialog(soundLevel: Int) {
        selectVolumeLevelDialog.toSetVolume(soundLevel, object : OnSelectVolumeListener {
            override fun onSelectVolumeLevel(selectVolumeGear: Int) {
                val switch = binding.ivSoundAlarmSwitch.isSelected
                //onSelVolumeLevelListener?.onSelectVolumeLevel(selectVolumeGear)
                mSetSoundLevelListener?.onSoundLevelChange(switch, selectVolumeGear)
            }
        })
    }

    private fun updateEmailStatusUi(isEmailOpen: Boolean) {
        binding.ivEmailAlarmSwitch4ThWarn.isSelected = isEmailOpen
        val bottomPaddingLp = binding.spBottomPadding4ThWarn.layoutParams
        bottomPaddingLp.height = if (isEmailOpen) 0 else DensityUtil.dp2px(context, 17.5f)
        binding.spBottomPadding4ThWarn.layoutParams = bottomPaddingLp
        binding.eeluReceiveEmail4ThWarn.setVisibility(isEmailOpen)
    }

    /**
     * 获取邮箱列表数据
     */
    private fun requestDeviceEmails(needLoading: Boolean) {
        if (needLoading) {
            binding.eeluReceiveEmail4ThWarn.requestStatusChange(RequestStatusUI.Status.LOADING)
        }
        val request = Request4DeviceEmails(transactions.createTransaction())
        Cache.get(IThNet::class.java).getDeviceEmails(sku, device).enqueue(IHCallBack(request))
    }

    /**
     * 获取设备的相关开关状态
     */
    private fun getSettings() {
        val request = Request4GetSettings(transactions.createTransaction(), sku, device)
        Cache.get(IThNet::class.java).getSettings(request).enqueue(IHCallBack(request))
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponse4UpdateSettings(response: Response4GetSettings) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        hideLoading()
        response.settings?.let {
            DeviceListConfig.read().updateLocalSettings(sku, device, it)
            JsonUtil.fromJson(it, Request4Setting::class.java)?.let { setting ->
                //更新普通告警ui
                binding.ivCommonAlarmSwitch4ThWarn.isSelected = setting.normalPushOnOff.isTrue()
                cacheSettings = setting
                setNotificationSettings(setting)
                //更新邮箱报警ui
                updateEmailStatusUi(setting.emailWarningOnOff.isTrue())

            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponse4DeviceEmails(response: Response4DeviceEmails) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        hideLoading()
        response.data?.emails?.let {
            binding.eeluReceiveEmail4ThWarn.setAccountEmailVerityStatus(true)
            binding.eeluReceiveEmail4ThWarn.requestStatusChange(RequestStatusUI.Status.SUCCESS)
            val emails: ArrayList<EmailModel> = arrayListOf()
            for (deviceEmail in it) {
                emails.add(EmailModel().apply {
                    emailId = deviceEmail.emailId
                    email = deviceEmail.email
                    isSelect = deviceEmail.selected == 1
                    isCheck = true
                })
            }
            binding.eeluReceiveEmail4ThWarn.setRequestSuccessData(emails)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponse4AddEmail(response: AddMailResponse) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        val addMailRequest = response.request as AddMailRequest
        val email = addMailRequest.mail
        val id = response.emailId
        val addEmailModel = EmailModel(id, email, true)
        EmailOpeateEvent.sendAdd(addEmailModel)
        hideLoading()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponse4DeleteEmail(response: EmailDeleteResponse?) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        EmailOpeateEvent.sendDelete(deleteEmailModel)
        TimerExportDelEvent.sendEvent(deleteEmailModel!!.email, timerStatus)
        hideLoading()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponse4BindEmail(response: Response4DeviceBindEmail?) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        if (response?.request is Request4DeviceBindEmail) {
            requestDeviceEmails(false)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onErrorResponse(response: ErrorResponse) {
        val request = response.request
        when (request) {
            is Request4DeviceEmails -> {
                //请求失败
                hideLoading()
                binding.eeluReceiveEmail4ThWarn.requestStatusChange(RequestStatusUI.Status.FAIL)
            }

            is AddMailRequest,
            is DelMailRequest,
            is Request4Setting,
            is Request4DeviceBindEmail -> {
                hideLoading()
                //无网络提示
                if (!NetUtil.isNetworkAvailable(context)) {
                    toast(R.string.network_anomaly)
                }
            }

            else -> {}
        }
    }

    /**
     * 发送验证码回调
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onApplyCodeEvent(event: ApplyCodeEvent) {
        if (event.result) {
            if (!StrUtil.isSameStr(AccountConfig.read().email, event.email)) {
                return
            }
            binding.eeluReceiveEmail4ThWarn.setAccountEmailVerityStatus(true)
        }
    }

    private fun setNotificationSettings(settings: Request4Setting?) {
        if (settings == null) {
            binding.tvImportantLevel4OffWarn.setVisibility(false)
            binding.stvAppPushOpenRemind4OfflineWarn.setVisibility(false)
        } else {
            isNotificationEnabled()?.let {
                binding.tvImportantLevel4OffWarn.setVisibility(it.first)
                binding.tvImportantLevel4OffWarn.text = it.second
                binding.spAppPushTopPadding.setVisibility(settings.normalPushOnOff.isTrue() && !it.first)
                binding.stvAppPushOpenRemind4OfflineWarn.setVisibility(settings.normalPushOnOff.isTrue() && !it.first)
            } ?: run {
                binding.tvImportantLevel4OffWarn.setVisibility(false)
                binding.stvAppPushOpenRemind4OfflineWarn.setVisibility(false)
                binding.spAppPushTopPadding.setVisibility(false)
            }
        }
    }

    /**
     * 检查通知开关状态
     */
    private fun isNotificationEnabled(): Pair<Boolean, String>? {
        context.getSystemService(Context.NOTIFICATION_SERVICE)?.let {
            val notificationManager = it as NotificationManager
            val channel = notificationManager.getNotificationChannel(notificationChannel)
            if (channel != null) {
                val enable = channel.importance != NotificationManager.IMPORTANCE_NONE
                val levelStr = when (channel.importance) {
                    NotificationManager.IMPORTANCE_HIGH -> {
                        ResUtil.getString(R.string.text_4_app_push_level_max)
                    }

                    NotificationManager.IMPORTANCE_DEFAULT -> {
                        ResUtil.getString(R.string.text_4_app_push_level_high)
                    }

                    NotificationManager.IMPORTANCE_LOW -> {
                        ResUtil.getString(R.string.text_4_app_push_level_mid)
                    }

                    NotificationManager.IMPORTANCE_MIN -> {
                        ResUtil.getString(R.string.text_4_app_push_level_low)
                    }

                    else -> {
                        ""
                    }
                }
                return Pair(enable, levelStr)
            } else {
                return null
            }
        }
        return null
    }

    fun onResume() {
        setNotificationSettings(cacheSettings)
        //权限设置结束
        isSettingNotify = false
    }

    /**
     * 系统已关闭通知的提示
     **/
    private fun getClosePushRemindText() {
        val spanModels: MutableList<SpanTextView.BaseSpanModel> = mutableListOf()
        val splitText1 = ResUtil.getString(R.string.connect_wifi_done)
        val remindText = "${ResUtil.getString(R.string.text_4_app_push_remind_2)}, $splitText1"

        val split1FirstIndex = remindText.indexOf(splitText1)
        val remindTextRv = remindText.replaceFirst(splitText1, "=")
        val remindTextSp = remindTextRv.split("=")
        var endIndex = 0
        for (s in remindTextSp) {
            endIndex += s.length
            when (endIndex) {
                split1FirstIndex -> {
                    val textSpanModel1 = SpanTextView.TextSpanModel()
                    textSpanModel1.content = s
                    textSpanModel1.textColor = ResUtil.getColor(R.color.ui_flag_style_45_textColor)
                    spanModels.add(textSpanModel1)

                    val clickSpanModel = SpanTextView.ClickSpanModel()
                    clickSpanModel.content = splitText1
                    clickSpanModel.textColor = ResUtil.getColor(R.color.font_style_50_3_textColor)
                    spanModels.add(clickSpanModel)
                    endIndex += splitText1.length
                }

                else -> {
                    val textSpanModel5 = SpanTextView.TextSpanModel()
                    textSpanModel5.content = s
                    textSpanModel5.textColor = ResUtil.getColor(R.color.ui_flag_style_45_textColor)
                    spanModels.add(textSpanModel5)
                }
            }
        }
        binding.stvAppPushOpenRemind4OfflineWarn.setText(spanModels) {
            if (ClickUtil.getInstance.clickQuick()) {
                return@setText
            }
            val intent = Intent(Settings.ACTION_CHANNEL_NOTIFICATION_SETTINGS).apply {
                putExtra(Settings.EXTRA_APP_PACKAGE, context.packageName)
                putExtra(Settings.EXTRA_CHANNEL_ID, notificationChannel)
            }
            context.startActivity(intent)
        }
    }

    private fun showLoading() {
        LoadingDialog.createDialog(context, com.ihoment.base2app.R.style.DialogDim)
            .setEventKey(this::class.java.name).show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(this::class.java.name)
    }

    override fun onDetachedFromWindow() {
        EventBus.getDefault().takeIf { it.isRegistered(this) }?.unregister(this)
        super.onDetachedFromWindow()
    }

    private var mSetSoundLevelListener: OnSetSoundLevelListener? = null

    fun setOnSetSoundLevelListener(listener: OnSetSoundLevelListener) {
        mSetSoundLevelListener = listener
    }

    interface OnSetSoundLevelListener {
        fun onSoundLevelChange(switch: Boolean, level: Int);
    }
}