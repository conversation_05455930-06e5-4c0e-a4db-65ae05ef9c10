package com.govee.h5140.iotop

import androidx.annotation.Keep
import com.govee.base2home.iot.AbsCmd
import com.govee.base2home.util.Encode
import com.govee.base2newth.AbsSingleController

/**
 * <AUTHOR>
 * @date created on 2022/06/15
 * @description 5106设备iot指令-->ptReal指令
 */
@Keep
class CmdPtReal : AbsCmd {

    private val command: MutableList<String> = ArrayList()

    var controller: AbsSingleController
    var sendTimeMills = 0L

    override fun getCmd(): String {
        return Cmd5140.ptReal
    }

    constructor(controller: AbsSingleController, timeMills: Long) {
        val bytes = controller.value
        val valueStr = Encode.encryptByBase64(bytes)
        command.add(valueStr)
        this.controller = controller
        this.sendTimeMills = timeMills
    }

    fun getCommand(): List<String> {
        return command
    }
}