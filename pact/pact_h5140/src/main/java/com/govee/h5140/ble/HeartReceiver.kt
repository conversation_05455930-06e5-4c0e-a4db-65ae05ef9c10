package com.govee.h5140.ble

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsOnlyReadSingleController

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 心跳包接收/解析器
 */
class HeartReceiver() : AbsOnlyReadSingleController() {

    override fun fail() {
        //心跳包；失败不处理
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_BLE_HEART
    }

    /**
     * 解析读取到心跳包中的数据(tem+hum+pm,每个值两个字节)
     */
    override fun parseValidBytes(validBytes: ByteArray): Bo<PERSON>an {
        if (BleUtil.bytesToHexString(validBytes).startsWith("0xff 0xff 0xff 0xff")) {
            val pm25Bytes = ByteArray(2)
            System.arraycopy(validBytes, 4, pm25Bytes, 0, pm25Bytes.size)
            val pm25 = BleUtil.getSignedInt(pm25Bytes, true)
            EventHeart.sendSuc(isWrite, commandType, proType, 0, 0, pm25)
            return true
        }
        val temBytes = ByteArray(2)
        System.arraycopy(validBytes, 0, temBytes, 0, temBytes.size)
        val tem = BleUtil.getSignedShort(temBytes[0], temBytes[1]).toInt()
        val humBytes = ByteArray(2)
        System.arraycopy(validBytes, 2, humBytes, 0, humBytes.size)
        val hum = BleUtil.getSignedInt(humBytes, true)
        val pm25Bytes = ByteArray(2)
        System.arraycopy(validBytes, 4, pm25Bytes, 0, pm25Bytes.size)
        val pm25 = BleUtil.getSignedInt(pm25Bytes, true)
        EventHeart.sendSuc(isWrite, commandType, proType, tem, hum, pm25)
        return true
    }
}