package com.govee.h5140.detail.chart

import androidx.annotation.Keep

/**
 * <AUTHOR>
 * @date created on 2022/06/01
 * @description 5106设备图表信息页所需的扩展实体类
 */
@Keep
class Ext4Ci {

    var goodsType: Int
    var sku: String
    var device: String
    var deviceName: String
    var bleAddress: String
    var temCali: Int
    var humCali: Int

    var versionSoft: String = ""
    var versionHard: String = ""
    var versionWifiSoft: String = ""
    var versionWifiHard: String = ""
    var wifiMac: String = ""

    constructor(
            goodsType: Int,
            sku: String,
            device: String,
            deviceName: String,
            bleAddress: String,
            temCali: Int,
            humCali: Int,
    ) {
        this.goodsType = goodsType
        this.sku = sku
        this.device = device
        this.deviceName = deviceName
        this.bleAddress = bleAddress
        this.temCali = temCali
        this.humCali = humCali
    }
}