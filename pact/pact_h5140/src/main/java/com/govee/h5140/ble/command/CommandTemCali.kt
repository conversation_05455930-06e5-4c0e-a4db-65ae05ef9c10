package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsSingleController
import com.govee.h5140.Constants5140
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventTemCali
import com.ihoment.base2app.infra.LogInfra

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取/写入温度校准值的指令，并解析其回复
 */
class CommandTemCali : AbsSingleController {

    companion object {
        private const val TAG = "CommandTemCali"
    }

    /**
     * 温度校准值*100
     */
    private var temCali = 0

    /**
     * 写操作
     */
    constructor(temCali: Int) : super(true) {
        this.temCali = checkTemCali(temCali)
    }

    private fun checkTemCali(temCali: Int): Int {
        var realTemCali = temCali
        val minTemCali = (Constants5140.TEM_CALI_MIN_CEL * 100).toInt()
        val maxTemCali = (Constants5140.TEM_CALI_MAX_CEL * 100).toInt()
        if (LogInfra.openLog() && (realTemCali < minTemCali || realTemCali > maxTemCali)) {
            LogInfra.Log.e(TAG, "checkTemCali() temCali is not in range!! temCali = $realTemCali")
        }
        realTemCali = realTemCali.coerceAtLeast(minTemCali)
        realTemCali = realTemCali.coerceAtMost(maxTemCali)
        return realTemCali
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray {
        return BleUtil.getSignedBytesFor2(temCali, false)
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventTemCali.sendWriteResult(suc, commandType, proType, temCali)
        return true
    }

    override fun fail() {
        EventTemCali.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_TEM_CALI
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        var temCali = BleUtil.getSignedShort(validBytes[1], validBytes[0]).toInt()
        temCali = checkTemCali(temCali)
        EventTemCali.sendSuc(isWrite, commandType, proType, temCali)
        return true
    }
}