package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventAirNotify
import com.ihoment.base2app.infra.SafeLog

/**
 * @author：Yang<PERSON>i.Chen
 * @date：2025/5/22 下午3:30
 * @description：
 */
class CommandAirNotify : AbsSingleController {

    private var switch = false

    /**
     * 写操作
     */
    constructor(switch: Boolean) : super(true) {
        this.switch = switch
    }

    /**
     * 读操作
     */
    constructor() : super(false) {

    }

    override fun translateWrite(): ByteArray {
        val switchByte = if (switch) 0x01.toByte() else 0x00.toByte()
        return byteArrayOf(0x01.toByte(), switchByte)
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        SafeLog.d("CommandAirNotify"){"onWriteResult switch=$switch"}
        EventAirNotify.sendWriteResult(suc, commandType, proType, switch)
        return true
    }

    override fun translateRead(): ByteArray {
        return byteArrayOf(0x01.toByte())
    }

    override fun fail() {
        EventAirNotify.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_AIR_QUALITY_NOTICE
    }

    override fun parseValidBytes(validBytes: ByteArray?): Boolean {
        if (validBytes == null || validBytes.size < 2) {
            return false
        }
        val switch = validBytes[1].toInt() == 1
        SafeLog.d("CommandAirNotify"){"parseValidBytes validBytes=${BleUtil.bytesToHexString(validBytes)}"}
        EventAirNotify.sendSuc(isWrite, commandType, proType, switch)
        return true
    }
}