package com.govee.h5140.ble.command

import com.govee.base2newth.AbsOnlyWriteSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventSyncTime
import com.govee.h5140.ble.event.EventSyncTime.Companion.sendWriteResult

/**
 * <AUTHOR>
 * @date created on 2022/6/20
 * @description 向5106设备发送上传最后一包数据的指令，并解析其回复（仅iot通讯时需要）
 */
class CommandUploadLastData : AbsOnlyWriteSingleController() {

    override fun translateWrite(): ByteArray {
        return byteArrayOf(1)
    }

    override fun onWriteResult(suc: <PERSON>olean): Boolean {
        sendWriteResult(suc, commandType, proType)
        return true
    }

    override fun fail() {
        EventSyncTime.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_UPLOAD_LAST_DATA
    }
}