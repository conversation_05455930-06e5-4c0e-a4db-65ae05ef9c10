package com.govee.h5140.item

import android.bluetooth.BluetoothDevice
import android.content.Context
import android.os.Handler
import android.os.Looper
import com.govee.base2home.Constant4L5
import com.govee.base2home.iot.Cmd
import com.govee.base2home.iot.Iot
import com.govee.base2home.iot.protype.v2.IotMsgV2
import com.govee.base2home.main.AbsDevice
import com.govee.base2home.pact.Protocol
import com.govee.base2home.util.TemUtil
import com.govee.base2light.synctriggers.h5106.Command4H5106
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.data.THMemoryUtil
import com.govee.base2newth.deviceitem.AbsModel4ThBleIot
import com.govee.base2newth.deviceitem.AbsThItem4BleWithPm
import com.govee.base2newth.other.DevicePactCheckOp
import com.govee.h5140.detail.chart.Activity4CharInfo
import com.govee.h5140.iotop.Cmd5140
import com.govee.h5140.iotop.IotParseUtils
import com.govee.kt.ui.device.DevicesOp
import com.govee.kt.ui.device.IDeviceItem
import com.govee.kt.ui.device.IRegister4DeviceItem
import com.govee.kt.ui.device.ISubLib
import com.govee.kt.ui.device.Item
import com.govee.kt.ui.device.base.IModel
import com.ihoment.base2app.util.CaughtRunnable
import com.ihoment.base2app.util.JsonUtil

object Register4Item : IRegister4DeviceItem {
    override fun register() {
        /*注册Holder-注册过一次即可*/
        DevicesOp.registerDeviceHolder(H5140Holder::class.java) {
            H5140Holder()
        }
        DevicesOp.register(mutableListOf<ISubLib>().apply {
            add(SubLib())
        })
    }
}

class DeviceItem(device: AbsDevice) : AbsThItem4BleWithPm(device) {

    override fun hadPm25(): Boolean {
        return false
    }

    override fun hadCo2(): Boolean {
        return true
    }

    override fun fahValueSet(): IntArray {
        return intArrayOf(
            TemUtil.getTemF(ThConsV1.TEM_MIN_VALUE_H5106 * 1.0f),
            TemUtil.getTemF(ThConsV1.TEM_MAX_VALUE_H5106 * 1.0f)
        )
    }

    override fun celValueSet(): IntArray {
        return intArrayOf(Constant4L5.TEM_MIN_VALUE_H5140, Constant4L5.TEM_MAX_VALUE_H5140)
    }

    override fun parseScanRecord(scanRecord: ByteArray, bt: BluetoothDevice): Boolean {
        val model = iModel as? Model ?: return false
        model.parseBcInfo(scanRecord, bt.address)
        return true
    }

    override fun makeModel(): IModel = Model(device)

    override fun checkIotOnline() {
        val model = iModel as? AbsModel4ThBleIot
        handler.removeCallbacksAndMessages(null)
        model?.checkIotOnline()
    }

    override fun stopIotTimer4Status() {
        //无需停止iot的定时检测（温湿度计类型设备的实时数据可能在首页的多个fragment展示）
        handler.removeCallbacks(checkIotOnlineRunnable)
        handler.post(checkIotOnlineRunnable)
    }

    private val handler = Handler(Looper.getMainLooper())

    private val checkIotOnlineRunnable by lazy {
        object : CaughtRunnable() {
            override fun runSafe() {
                val model = iModel as? AbsModel4ThBleIot
                model?.checkIotOnline()
                //轮询
                handler.removeCallbacks(this)
                handler.postDelayed(this, 60 * 1000L)
            }
        }
    }

    override fun quickIotOnlineCheck() {
        val model = iModel as? AbsModel4ThBleIot
        model?.quickIotOnlineCheck()
    }

    override fun removed(deleted: Boolean) {
        super.removed(deleted)
        handler.removeCallbacksAndMessages(null)
    }
}

class Model(device: AbsDevice) : AbsModel4ThBleIot(device) {

    private var lastProtocol: Protocol? = null

    init {
        THMemoryUtil.getInstance().setAirQsOpen(device.key, ext.getAiqQualityOnOff())
    }

    override fun syncDevice(newDevice: AbsDevice) {
        super.syncDevice(newDevice)
        device.parsePact()
        if (device.pactType <= 0 || device.pactCode <= 0) {
            lastProtocol?.let {
                device.pactType = it.pactType
                device.pactCode = it.pactCode
            }
        }
        THMemoryUtil.getInstance().setAirQsOpen(device.key, ext.getAiqQualityOnOff())
    }

    override fun jump2Adjust(context: Context) {
//        /**注册App版本过低的响应类*/
//        if (!Support.supportPact(device.goodsType, Protocol(device.pactType, device.pactCode))) {
//            AppOlderDialog.showAppOlderDialogWithErrorMsg(context, DevicePactCheckOp.appOldReasonMsg(device)) {}
//            AnalyticsRecorder.getInstance().recordUseCount(device.sku, ParamFixedValue.device_into_detail_excetion)
//            return
//        }
        Activity4CharInfo.jump2AdjustAc(
            context,
            device.goodsType,
            device.sku,
            device.device,
            device. deviceName,
            ext.address,
            ext.temCali,
            ext.humCali,
            ext.wifiMac?: "",
            device.versionSoft,
            device.versionHard,
            ext.topic,
        )
    }

    override fun removed(deleted: Boolean) {
    }

    override fun sendOnlineIotCommand() {
        Iot.getInstance.read(ext.topic, createIotTransaction(Cmd.status), Cmd.status, 0)
    }

    override fun quickIotOnlineCheck() {
        Iot.getInstance.read(ext.topic, createIotTransaction(Cmd.online), Cmd.online, 0)
    }

    /**
     * 解析蓝牙广播
     */
    fun parseBcInfo(scanRecord: ByteArray, bleAddress: String) {
        // 从广播中解析protocol信息
        DevicePactCheckOp.checkPact4Bc(device, scanRecord, bleAddress) {
            it?.let { curProtocol ->
                device.run {
                    pactType = curProtocol.pactType
                    pactCode = curProtocol.pactCode
                }
                //记录最新的协议
                lastProtocol = curProtocol
            }
        }
        // 从广播中解析
        ThpBroadcastUtil.parseH5140Broadcast(scanRecord)?.let {
            lastData.run {
                this.tem = it[2]
                this.hum = it[3]
                this.co2 = it[4]
                this.lastTime = System.currentTimeMillis()
            }
        }
    }

    override fun parseIotV2(msg: IotMsgV2, jsonStr: String) {
        val btInRange = btInRange()
        /*蓝牙在附近时，不去解析iot status包中的数据，只获取蓝牙广播中的数据*/
        if (btInRange) return
        device.run {
            pactType = msg.pactType
            pactCode = msg.pactCode
        }
        DevicePactCheckOp.checkPact4Iot(device, Protocol(msg.pactType, msg.pactCode).apply {
            //记录最新的协议
            lastProtocol = this
        })
        try {
            if (Cmd.status != msg.cmd || jsonStr.isBlank()) {
                return
            }
            val opJsonStr = IotParseUtils.getJsonObjectStr(jsonStr, Cmd5140.parse_json_op)
            val op = JsonUtil.fromJson(opJsonStr, Command4H5106::class.java)
            if (op == null || op.command.isNullOrEmpty()) {
                return
            }
            IotParseUtils.getDeviceInfo(
                device.sku,
                device.device,
                op.command!!
            )?.run {
                /*同步设备相关信息*/
                ext.let {
                    it.co2Warning = this.co2Warning
                    it.co2Max = this.co2Max
                    it.co2Min = this.co2Min
                    it.temWarning = this.temWarning
                    it.temCali = this.temCali
                    it.temMax = this.temMax
                    it.temMin = this.temMin
                    it.humWarning = this.humWarning
                    it.humCali = this.humCali
                    it.humMax = this.humMax
                    it.humMin = this.humMin
                }
                lastData.let {
                    //设备实时数据
                    it.co2 = this.co2
                    it.tem = this.tem
                    it.hum = this.hum
                    it.lastTime = System.currentTimeMillis()
                }
            }
        } catch (e: Exception) {
        }
    }

    override fun isWifi(): Boolean {
        return true
    }
}

class SubLib : ISubLib {
    override fun transform(absDevice: AbsDevice): IDeviceItem {
        return DeviceItem(absDevice)
    }

    override fun keys(): MutableList<String> {
        return mutableListOf<String>().apply {
            val deviceItemGoodsTypes = Support.deviceItemGoodsTypes
            for (goodsType in deviceItemGoodsTypes) {
                add(goodsType.toString())
            }
        }
    }

    override fun itemTypes(): MutableList<Int> {
        return Item.itemTypes29()
    }
}