package com.govee.h5140.detail.setting;

import android.content.Context;
import android.text.TextUtils;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.TextView;

import com.aigestudio.wheelpicker.WheelPicker;
import com.govee.base2home.util.ClickUtil;
import com.govee.h5140.databinding.H5140DialogTimeRangeLayoutBinding;
import com.ihoment.base2app.dialog.BaseEventDialog;
import com.ihoment.base2app.infra.SafeLog;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.ResUtil;

import cn.carbswang.android.numberpickerview.library.NumberPickerView;

/**
 * Create by xieyingwu on 2020-01-06
 * 时间选择弹窗v2版本$
 * modify by xiaobing
 */
public class Dialog4SelectTimeRange extends BaseEventDialog implements View.OnClickListener {

    private DoneListener doneListener;

    private int startHour;
    private int startMinute;
    private int endHour;
    private int endMinute;
    public static boolean is24Hours = true;
    private boolean isDoneHide;
    private H5140DialogTimeRangeLayoutBinding binding;

    TextView titleTv;
    NumberPickerView hourPicker;
    NumberPickerView minPicker;
    WheelPicker amPicker;
    TextView btnCancelTv;
    TextView btnDoneTv;

    private boolean isChoseStartTime = true;

    private Dialog4SelectTimeRange(Context context,
                                   int startHour,
                                   int startMinute,
                                   int endHour,
                                   int endMinute,
                                   String title,
                                   DoneListener doneListener) {
        super(context);
        immersionMode();
        ignoreBackPressed();
        this.startHour = startHour;
        this.startMinute = startMinute;
        this.endHour = endHour;
        this.endMinute = endMinute;
        this.doneListener = doneListener;
        initView();
        titleTv.setText(title);
        changeDialogOutside(false);
        if (is24Hours) {
            hourPicker.setMaxValue(23);
        } else {
            hourPicker.setMaxValue(11);
        }
        hourPicker.setOnValueChangedListener((picker, oldVal, newVal) -> updateHour(newVal));
        minPicker.setMaxValue(59);
        minPicker.setOnValueChangedListener((picker, oldVal, newVal) -> updateMin(newVal));
        //设置监听
        btnCancelTv.setOnClickListener(this);
        btnDoneTv.setOnClickListener(this);
        binding.llStartTime.setOnClickListener(v -> {
            isChoseStartTime = true;
            SafeLog.d("Dialog4SelectTimeRange", () -> "1------startHour=" + startHour + ",startMinute=" + startMinute);
            hourPicker.setValue(this.startHour);
            minPicker.setValue(this.startMinute);
            ResUtil.setTextColor(binding.tvStartTimeDes, com.govee.ui.R.color.font_style_3_2_textColor);
            ResUtil.setTextColor(binding.tvStartTime, com.govee.ui.R.color.font_style_3_2_textColor);
            ResUtil.setTextColor(binding.tvEndTimeDes, com.govee.ui.R.color.font_style_5_1_textColor);
            ResUtil.setTextColor(binding.tvEndTime, com.govee.ui.R.color.font_style_5_1_textColor);
        });
        binding.llEndTime.setOnClickListener(v -> {
            isChoseStartTime = false;
            SafeLog.d("Dialog4SelectTimeRange", () -> "2------startHour=" + startHour + ",startMinute=" + startMinute);
            hourPicker.setValue(this.endHour);
            minPicker.setValue(this.endMinute);
            ResUtil.setTextColor(binding.tvStartTimeDes, com.govee.ui.R.color.font_style_5_1_textColor);
            ResUtil.setTextColor(binding.tvStartTime, com.govee.ui.R.color.font_style_5_1_textColor);
            ResUtil.setTextColor(binding.tvEndTimeDes, com.govee.ui.R.color.font_style_3_2_textColor);
            ResUtil.setTextColor(binding.tvEndTime, com.govee.ui.R.color.font_style_3_2_textColor);
        });
    }

    private void initView() {
        titleTv = binding.title;
        hourPicker = binding.hourPicker;
        minPicker = binding.minPicker;
        amPicker = binding.amPicker;
        btnCancelTv = binding.btnCancel;
        btnDoneTv = binding.btnDone;
    }

    /**
     * 24小时制的小时、分钟数
     *
     * @param context
     * @param startHour
     * @param startMinute
     * @param title
     * @param doneHide
     * @param doneListener
     */
    public static void showTimeDialogV2(Context context,
                                        int startHour,
                                        int startMinute,
                                        int endHour,
                                        int endMinute,
                                        String title,
                                        boolean doneHide,
                                        DoneListener doneListener) {
        Dialog4SelectTimeRange timeDialogV2 = new Dialog4SelectTimeRange(context, startHour, startMinute, endHour, endMinute, title, doneListener);
        timeDialogV2.setDoneHide(doneHide);
        timeDialogV2.show();
    }

    public void setDoneHide(boolean doneHide) {
        isDoneHide = doneHide;
    }

    @Override
    protected void dialogOnShow() {
        super.dialogOnShow();
        hourPicker.setValue(startHour);
        minPicker.setValue(startMinute);
        binding.tvStartTime.setText(getTimeStr(startHour, startMinute));
        binding.tvEndTime.setText(getTimeStr(endHour, endMinute));
    }

    private void updateMin(int newMinute) {
        if (isChoseStartTime) {
            startMinute = newMinute;
            binding.tvStartTime.setText(getTimeStr(startHour, startMinute));
        } else {
            endMinute = newMinute;
            binding.tvEndTime.setText(getTimeStr(endHour, endMinute));
        }
    }

    private void updateHour(int newHour) {
        if (isChoseStartTime) {
            startHour = newHour;
            binding.tvStartTime.setText(getTimeStr(startHour, startMinute));
        } else {
            endHour = newHour;
            binding.tvEndTime.setText(getTimeStr(endHour, endMinute));
        }
    }

    private String getTimeStr(int hour, int minute) {
        String hourStr;
        String minuteStr;
        if (hour == 0) {
            hourStr = "00";
        } else if (hour < 10) {
            hourStr = "0" + hour;
        } else {
            hourStr = String.valueOf(hour);
        }
        if (minute == 0) {
            minuteStr = "00";
        } else if (minute < 10) {
            minuteStr = "0" + minute;
        } else {
            minuteStr = String.valueOf(minute);
        }
        return hourStr + ":" + minuteStr;
    }

    @Override
    public void hide() {
        doneListener = null;
        super.hide();
        context = null;
    }

    @Override
    protected int getLayout() {
        return -1;
    }

    @Override
    protected View getLayoutView() {
        binding = H5140DialogTimeRangeLayoutBinding.inflate(LayoutInflater.from(context));
        return binding.getRoot();
    }

    public static void hideDialog(String eventKey) {
        if (TextUtils.isEmpty(eventKey)) {
            eventKey = Dialog4SelectTimeRange.class.getName();
        }
        DialogHideEvent.sendDialogHideEvent(eventKey);
    }

    @Override
    public void onClick(View v) {
        if (ClickUtil.getInstance.clickQuick()) {
            return;
        }
        if (v == btnCancelTv) {
            hide();
        } else if (v == btnDoneTv) {
            if (doneListener != null) {
                doneListener.onDone(startHour, startMinute, endHour, endMinute);
            }
            if (isDoneHide) {
                hide();
            }
        }
    }

    public interface DoneListener {
        void onDone(int startHour, int startMinute, int endHour, int endMinute);
    }

    @Override
    protected int getWidth() {
        return AppUtil.getScreenWidth() * 335 / 375;
    }
}