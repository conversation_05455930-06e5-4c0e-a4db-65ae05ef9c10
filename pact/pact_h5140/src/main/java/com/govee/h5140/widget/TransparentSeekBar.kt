package com.govee.h5140.widget

import android.content.Context
import android.util.AttributeSet
import android.view.MotionEvent
import androidx.appcompat.widget.AppCompatSeekBar

/**
 * @author：YangQi.Chen
 * @date：2025/4/11 上午11:51
 * @description：
 */
class TransparentSeekBar @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = androidx.appcompat.R.attr.seekBarStyle
) : AppCompatSeekBar(context, attrs, defStyleAttr) {

    override fun onTouchEvent(event: MotionEvent): Boolean {
        if (!isEnabled) return false

        // 只处理滑块附近的触摸事件
        val x = event.x
        val thumbBounds = thumb.bounds
        val thumbPos = paddingLeft + thumbBounds.centerX()

        // 计算触摸点与滑块中心的距离
        val touchSlop = thumbBounds.width() / 2f  // 一半滑块宽度作为触摸区域

        return when (event.action) {
            MotionEvent.ACTION_DOWN -> {
                // 只有在滑块附近才处理事件
                Math.abs(x - thumbPos) <= touchSlop && super.onTouchEvent(event)
            }
            MotionEvent.ACTION_MOVE -> {
                // 移动时总是处理事件（即使离开初始触摸区域）
                super.onTouchEvent(event)
            }
            else -> {
                super.onTouchEvent(event)
            }
        }
    }

    /**
     * 获取thumb x坐标
     */
    fun getThumbPosX(): Int {
        val thumbBounds = thumb.bounds
        val thumbPos = paddingLeft + thumbBounds.centerX()
        return thumbPos
    }
}