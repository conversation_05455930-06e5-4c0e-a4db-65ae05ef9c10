package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventTempUnit
import com.ihoment.base2app.infra.SafeLog

/**
 * <AUTHOR>
 * @date created on 2022/9/20
 * @description 从5106设备读取设备温度显示单位的指令，并解析其回复
 */
class CommandTemUnit : AbsSingleController {

    companion object {
        const val CEN = 0x00
        const val FAH = 0X01
    }

    /**
     * 温度单位类型
     */
    private var tuType = FAH

    /**
     * 写操作
     */
    constructor(tuType: Int) : super(true) {
        this.tuType = tuType
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray {
        return byteArrayOf(tuType.toByte())
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventTempUnit.sendWriteResult(suc, commandType, proType, tuType)
        return true
    }

    override fun fail() {
        EventTempUnit.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_TEM_UNIT
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val tempUnit = validBytes[0].toInt()
        EventTempUnit.sendSuc(isWrite, commandType, proType, tempUnit)
        return true
    }
}