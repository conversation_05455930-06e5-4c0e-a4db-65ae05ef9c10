//package com.govee.h5140.linkage
//
//import android.annotation.SuppressLint
//import android.content.Context
//import android.view.LayoutInflater
//import android.view.View
//import android.widget.ImageView
//import android.widget.SeekBar
//import android.widget.TextView
//import androidx.constraintlayout.widget.ConstraintLayout
//import androidx.viewbinding.ViewBinding
//import com.govee.base2home.common.BaseRvAdapter
//import com.govee.base2home.common.BaseRvHolder
//import com.govee.base2home.scenes.model.DeviceModel
//import com.govee.base2light.databinding.ThLinkageTriggerActionItemBinding
//import com.govee.base2light.reform4dbgw.ThTriggerSeekBar
//import com.govee.base2light.rhythm.RhyRule
//import com.govee.base2light.rhythm.model.AbsTriggerModel
//import com.govee.h5140.Constants5140
//import com.govee.h5140.databinding.H5140LinkagePm25ItemBinding
//import com.ihoment.base2app.infra.LogInfra
//import com.ihoment.base2app.util.ResUtil
//import kotlin.math.roundToInt
//
///**
// * <AUTHOR>
// * @date created on 2022/9/9
// * @description H5106的联动触发动作的列表适配器
// */
//class Adapter4ThpTrigger : BaseRvAdapter<AbsTriggerModel> {
//
//    companion object {
//        private const val MAX_TEM_VALUE = 50.0F
//        private const val MIN_TEM_VALUE = -10.0F
//        private const val MAX_HUM_VALUE = 100.0F
//        private const val MIN_HUM_VALUE = 0.0F
//
//        //item的viewType类型
//        private const val IS_PM25_VIEW_TYPE = 1
//        private const val IS_TH_VIEW_TYPE = 2
//    }
//
//    private var deviceModel: DeviceModel
//
//    constructor(
//        context: Context,
//        data: ArrayList<AbsTriggerModel>,
//        deviceModel: DeviceModel,
//    ) : super(
//        context,
//        data
//    ) {
//        this.deviceModel = deviceModel
//    }
//
//    private var selectedIndex = 0
//
//    @SuppressLint("NotifyDataSetChanged")
//    fun setSelectedIndex(selectedIndex: Int) {
//        this.selectedIndex = selectedIndex
//        this.notifyDataSetChanged()
//    }
//
//    override fun getItemViewType(position: Int): Int {
//        val model: AbsTriggerModel = data[position]
//        return if (model.opCmdValue == RhyRule.op_type_air_quality) IS_PM25_VIEW_TYPE else IS_TH_VIEW_TYPE
//    }
//
//    override fun getItemViewBinding(viewType: Int): ViewBinding {
//        return when (viewType) {
//            IS_PM25_VIEW_TYPE -> {
//                H5140LinkagePm25ItemBinding.inflate(LayoutInflater.from(context))
//            }
//            else -> {
//                ThLinkageTriggerActionItemBinding.inflate(LayoutInflater.from(context))
//            }
//        }
//    }
//
//    override fun setItem(h: BaseRvHolder, entity: AbsTriggerModel, position: Int) {
//        when (h.itemViewType) {
//            IS_PM25_VIEW_TYPE -> {
//                setPm25Item(h, entity, position)
//            }
//            else -> {
//                setThItem(h, entity, position)
//            }
//        }
//    }
//
//    /**
//     * 设置pm2.5item
//     */
//    private fun setPm25Item(h: BaseRvHolder, entity: AbsTriggerModel, position: Int) {
//        val pm25Item = H5140LinkagePm25ItemBinding.bind(h.itemView)
//        //数据适配
//        pm25Item.ivSelectIcon4Pm25Trigger.isSelected = selectedIndex == position
//        pm25Item.clContentContainer4PmTrigger.isSelected = selectedIndex == position
//        pm25Item.vCenterPadding2Pm25Trigger.visibility =
//            if (selectedIndex == position) View.GONE else View.VISIBLE
//        pm25Item.clCmdValueContainer4Pm25Trigger.visibility =
//            if (selectedIndex == position) View.VISIBLE else View.GONE
//        pm25Item.ivTriggerIcon4Pm25Trigger.setImageDrawable(ResUtil.getDrawable(entity.iconId))
//        pm25Item.tvTriggerName4Pm25Trigger.text = context.resources.getString(entity.labelResId)
//        //设置已选中值
//        pm25Item.sbValue4Pm25Trigger.progress = entity.opValue
//        selectPm25Level(pm25Item, position)
//        //设置选择监听
//        pm25Item.tvAq14Pm25Trigger.setOnClickListener {
//            pm25Item.sbValue4Pm25Trigger.progress = 0
//            data[position].opValue = Constants5140.EXCELLENT
//            resetPm25Level(pm25Item)
//            pm25Item.tvAq14Pm25Trigger.isSelected = true
//        }
//        pm25Item.tvAq24Pm25Trigger.setOnClickListener {
//            pm25Item.sbValue4Pm25Trigger.progress = 1
//            data[position].opValue = Constants5140.GOOD
//            resetPm25Level(pm25Item)
//            pm25Item.tvAq24Pm25Trigger.isSelected = true
//        }
//        pm25Item.tvAq34Pm25Trigger.setOnClickListener {
//            pm25Item.sbValue4Pm25Trigger.progress = 2
//            data[position].opValue = Constants5140.MODERATE
//            resetPm25Level(pm25Item)
//            pm25Item.tvAq34Pm25Trigger.isSelected = true
//        }
//        pm25Item.tvAq44Pm25Trigger.setOnClickListener {
//            pm25Item.sbValue4Pm25Trigger.progress = 3
//            data[position].opValue = Constants5140.POOR
//            resetPm25Level(pm25Item)
//            pm25Item.tvAq44Pm25Trigger.isSelected = true
//        }
//        pm25Item.sbValue4Pm25Trigger.setOnSeekBarChangeListener(object :
//            SeekBar.OnSeekBarChangeListener {
//            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
//            }
//
//            override fun onStartTrackingTouch(seekBar: SeekBar?) {
//            }
//
//            override fun onStopTrackingTouch(seekBar: SeekBar?) {
//                if (seekBar == null) {
//                    return
//                }
//                data[position].opValue = when (seekBar.progress) {
//                    0 -> {
//                        Constants5140.EXCELLENT
//                    }
//                    1 -> {
//                        Constants5140.GOOD
//                    }
//                    2 -> {
//                        Constants5140.MODERATE
//                    }
//                    else -> {
//                        Constants5140.POOR
//                    }
//                }
//                selectPm25Level(pm25Item, position)
//            }
//        })
//    }
//
//    /**
//     * 选中pm2.5等级
//     */
//    private fun selectPm25Level(
//        binding: H5140LinkagePm25ItemBinding,
//        itemIndex: Int,
//    ) {
//        resetPm25Level(binding)
//        when (data[itemIndex].opValue) {
//            Constants5140.EXCELLENT -> {
//                binding.tvAq14Pm25Trigger.isSelected = true
//            }
//            Constants5140.GOOD -> {
//                binding.tvAq24Pm25Trigger.isSelected = true
//            }
//            Constants5140.MODERATE -> {
//                binding.tvAq34Pm25Trigger.isSelected = true
//            }
//            else -> {
//                binding.tvAq44Pm25Trigger.isSelected = true
//            }
//        }
//    }
//
//    /**
//     * 文案颜色重置
//     */
//    private fun resetPm25Level(binding: H5140LinkagePm25ItemBinding) {
//        binding.tvAq14Pm25Trigger.isSelected = false
//        binding.tvAq24Pm25Trigger.isSelected = false
//        binding.tvAq34Pm25Trigger.isSelected = false
//        binding.tvAq44Pm25Trigger.isSelected = false
//    }
//
//    /**
//     * 设置温湿度item
//     */
//    private fun setThItem(h: BaseRvHolder, entity: AbsTriggerModel, position: Int) {
//        val vCenterPadding: View =
//            h.getView(com.govee.base2light.R.id.v_center_padding_2_th_trigger) as View
//        val clCmdValueContainer: ConstraintLayout =
//            h.getView(com.govee.base2light.R.id.cl_cmd_value_container_4_th_trigger) as ConstraintLayout
//        val clContentContainer: ConstraintLayout =
//            h.getView(com.govee.base2light.R.id.cl_content_container_4_th_trigger) as ConstraintLayout
//        val ivSelectIcon4ThLinkage: ImageView =
//            h.getView(com.govee.base2light.R.id.iv_select_icon_4_th_trigger) as ImageView
//        val ivTriggerIcon: ImageView =
//            h.getView(com.govee.base2light.R.id.iv_trigger_icon_4_th_trigger) as ImageView
//        val ivReduceIcon: ImageView =
//            h.getView(com.govee.base2light.R.id.iv_reduce_icon_4_th_trigger) as ImageView
//        val ivAddIcon: ImageView =
//            h.getView(com.govee.base2light.R.id.iv_add_icon_4_th_trigger) as ImageView
//        val tvTriggerName: TextView =
//            h.getView(com.govee.base2light.R.id.tv_trigger_name_4_th_trigger) as TextView
//        val sbTriggerValue: ThTriggerSeekBar =
//            h.getView(com.govee.base2light.R.id.sb_trigger_value_4_th_trigger) as ThTriggerSeekBar
//        val tvMaxValue: TextView =
//            h.getView(com.govee.base2light.R.id.tv_show_max_value_4_th_trigger) as TextView
//        val tvMinValue: TextView =
//            h.getView(com.govee.base2light.R.id.tv_show_min_value_4_th_trigger) as TextView
//        //适配数据
//        sbTriggerValue.setSku(
//            deviceModel.sku,
//            deviceModel.device,
//            deviceModel.versionHard,
//            deviceModel.versionSoft
//        )
//        ivSelectIcon4ThLinkage.isSelected = selectedIndex == position
//        clContentContainer.isSelected = selectedIndex == position
//        vCenterPadding.visibility = if (selectedIndex == position) View.GONE else View.VISIBLE
//        clCmdValueContainer.visibility = if (selectedIndex == position) View.VISIBLE else View.GONE
//        ivTriggerIcon.setImageDrawable(ResUtil.getDrawable(entity.iconId))
//        tvTriggerName.text = context.resources.getString(entity.labelResId)
//        when (entity.opCmdValue) {
//            RhyRule.op_type_tem_high, RhyRule.op_type_tem_low -> {
//                sbTriggerValue.triggerType = ThTriggerSeekBar.TEM
//                val currentTypeValue = entity.opValue / 100.0f
//                sbTriggerValue.setInfo(
//                    MAX_TEM_VALUE,
//                    MIN_TEM_VALUE, currentTypeValue
//                )
//                tvMaxValue.text = sbTriggerValue.maxShowValueStr
//                tvMinValue.text = sbTriggerValue.minShowValueStr
//            }
//            RhyRule.op_type_hum_high, RhyRule.op_type_hum_low -> {
//                sbTriggerValue.triggerType = ThTriggerSeekBar.HUM
//                var currentTypeValue = entity.opValue / 100.0f
//                if (currentTypeValue >= 99.0f) {
//                    currentTypeValue = 100.0f
//                }
//                sbTriggerValue.setInfo(
//                    MAX_HUM_VALUE,
//                    MIN_HUM_VALUE, currentTypeValue
//                )
//                tvMaxValue.text = sbTriggerValue.maxShowValueStr
//                tvMinValue.text = sbTriggerValue.minShowValueStr
//            }
//            else -> {}
//        }
//        sbTriggerValue.setListener { progressValue ->
//            when (entity.opCmdValue) {
//                RhyRule.op_type_tem_high, RhyRule.op_type_tem_low -> {
//                    //toInt()是舍去小数位取整，而roundToInt()才是四舍五入取整
//                    //!!!这里有个看来怪异的现象：progressValue打印除出来的值为33.6，但是progressValue * 100得出的值却是3259.99998
//                    val temOpValue = (progressValue * 100).roundToInt()
//                    LogInfra.Log.i("xiaobing", "要保存的温度值为-->$temOpValue")
//                    data[position].opValue = temOpValue
//                    //切换两边加/减按钮ui
//                    if (temOpValue >= (MAX_TEM_VALUE * 100).roundToInt()) {
//                        //值达到最大
//                        ivAddIcon.alpha = 0.3f
//                        ivAddIcon.isEnabled = false
//                        ivReduceIcon.alpha = 1.0f
//                        ivReduceIcon.isEnabled = true
//                    } else if (temOpValue <= (MIN_TEM_VALUE * 100).roundToInt()) {
//                        //值达到最小
//                        ivAddIcon.alpha = 1.0f
//                        ivAddIcon.isEnabled = true
//                        ivReduceIcon.alpha = 0.3f
//                        ivReduceIcon.isEnabled = false
//                    } else {
//                        ivAddIcon.alpha = 1.0f
//                        ivAddIcon.isEnabled = true
//                        ivReduceIcon.alpha = 1.0f
//                        ivReduceIcon.isEnabled = true
//                    }
//                }
//                RhyRule.op_type_hum_high, RhyRule.op_type_hum_low -> {
//                    var currentValue = progressValue
//                    val humCurrentValue = (currentValue * 100).roundToInt()
//                    //切换两边加/减按钮ui
//                    if (humCurrentValue >= (MAX_HUM_VALUE * 100).roundToInt()) {
//                        //值达到最大
//                        ivAddIcon.alpha = 0.3f
//                        ivAddIcon.isEnabled = false
//                        ivReduceIcon.alpha = 1.0f
//                        ivReduceIcon.isEnabled = true
//                    } else if (humCurrentValue <= (MIN_HUM_VALUE * 100).roundToInt()) {
//                        //值达到最小
//                        ivAddIcon.alpha = 1.0f
//                        ivAddIcon.isEnabled = true
//                        ivReduceIcon.alpha = 0.3f
//                        ivReduceIcon.isEnabled = false
//                    } else {
//                        ivAddIcon.alpha = 1.0f
//                        ivAddIcon.isEnabled = true
//                        ivReduceIcon.alpha = 1.0f
//                        ivReduceIcon.isEnabled = true
//                    }
//                    //湿度值最大使用值为99，但显示100
//                    if (progressValue >= MAX_HUM_VALUE) {
//                        currentValue = 99.0f
//                    }
//                    data[position].opValue = (currentValue * 100).roundToInt()
//                    LogInfra.Log.i("xiaobing", "要保存的湿度值为-->${data[position].opValue}")
//                }
//                else -> {}
//            }
//        }
//        ivReduceIcon.setOnClickListener {
//            sbTriggerValue.changeCurrentValue(false)
//        }
//        ivAddIcon.setOnClickListener {
//            sbTriggerValue.changeCurrentValue(true)
//        }
//    }
//}