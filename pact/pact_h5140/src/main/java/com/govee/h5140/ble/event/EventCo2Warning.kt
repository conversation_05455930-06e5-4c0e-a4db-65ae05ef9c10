package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/06/09
 * @description 从5106设备读取Co2预警相关信息的事件
 */
class EventCo2Warning(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, !write) {

    var openWarning = false
    var minCo2 = 400
    var maxCo2 = 5000
    var alarmInterval = 10

    companion object {
        fun sendWriteResult(
            result: Boolean,
            commandType: Byte,
            proType: Byte,
            openWarning: Boolean,
            minHum: Int,
            maxHum: Int,
            alarmInterval: Int
        ) {
            val event = EventCo2Warning(result, true, commandType, proType)
            event.openWarning = openWarning
            event.minCo2 = minHum
            event.maxCo2 = maxHum
            event.alarmInterval = alarmInterval
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventCo2Warning(false, write, commandType, proType))
        }

        fun sendSuc(
            write: Boolean,
            commandType: Byte,
            proType: Byte,
            openWarning: Boolean,
            minPm25: Int,
            maxPm25: Int,
            alarmInterval: Int
        ) {
            val event = EventCo2Warning(true, write, commandType, proType)
            event.openWarning = openWarning
            event.minCo2 = minPm25
            event.maxCo2 = maxPm25
            event.alarmInterval = alarmInterval
            EventBus.getDefault().post(event)
        }
    }
}