package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsSingleController
import com.govee.h5140.Constants5140
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventHumCali
import com.ihoment.base2app.infra.LogInfra

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取/写入湿度校准值的指令，并解析其回复
 */
class CommandHumCali : AbsSingleController {

    companion object {
        private const val TAG = "CommandHumCali"
    }

    /**
     * 湿度校准值*100
     */
    private var humCali = 0

    /**
     * 写操作
     */
    constructor(humCali: Int) : super(true) {
        this.humCali = checkHumCali(humCali)
    }

    private fun checkHumCali(humCali: Int): Int {
        var realHumCali = humCali
        val minHumCali = (Constants5140.HUM_CALI_MIN * 100).toInt()
        val maxHumCali = (Constants5140.HUM_CALI_MAX * 100).toInt()
        if (LogInfra.openLog() && (realHumCali < minHumCali || realHumCali > maxHumCali)) {
            LogInfra.Log.e(TAG, "checkHumCali() humCali is not in range!! humCali = $realHumCali")
        }
        realHumCali = realHumCali.coerceAtLeast(minHumCali)
        realHumCali = realHumCali.coerceAtMost(maxHumCali)
        return realHumCali
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray {
        return BleUtil.getSignedBytesFor2(humCali, false)
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventHumCali.sendWriteResult(suc, commandType, proType, humCali)
        return true
    }

    override fun fail() {
        EventHumCali.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_HUM_CALI
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        var humCali = BleUtil.convertTwoBytesToShort(validBytes[0], validBytes[1])
        humCali = checkHumCali(humCali)
        EventHumCali.sendSuc(isWrite, commandType, proType, humCali)
        return true
    }
}