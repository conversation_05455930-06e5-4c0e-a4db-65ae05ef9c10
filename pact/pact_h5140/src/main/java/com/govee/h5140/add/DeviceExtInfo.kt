package com.govee.h5140.add

import androidx.annotation.Keep
import com.govee.h5140.Constants5140

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 5106设备所拥有的相关信息
 * 备注：这个需转化为json上传服务器，故该类不能混淆
 */
@Keep
class DeviceExtInfo {

    var deviceName: String? = null
    var bleSoftVersion: String? = null
    var bleHardVersion: String? = null
    var bleName: String? = null
    var address: String? = null
    var pactType = -100
    var pactCode = -100

    //温度范围-起始*100
    var temMin: Int = Constants5140.TEM_MIN_VALUE * 100

    //温度范围-结束*100
    var temMax: Int = Constants5140.TEM_MAX_VALUE * 100

    //温度预警是否开启
    var temWarning = false

    //湿度范围-起始*100
    var humMin = Constants5140.HUM_MIN_VALUE * 100

    //湿度范围-结束*100
    var humMax = Constants5140.HUM_MAX_VALUE * 100

    //湿度预警是否开启
    var humWarning = false

    //湿度预警阙值*100
    var humCali = 0

    //温度预警阙值*100
    var temCali = 0

    //pm2.5范围-起始
    var co2Min = Constants5140.CO2_MIN_VALUE

    //pm2.5范围-结束
    var co2Max = Constants5140.CO2_MAX_VALUE
    var co2Warning = false

    //空气质量开关默认为开
    var airQualityOnOff = 1

    var wifiMac: String? = null

    var co2LevelLower = 1000

    var co2LevelUpper = 1400
}