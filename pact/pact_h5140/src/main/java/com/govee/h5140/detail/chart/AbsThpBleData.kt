package com.govee.h5140.detail.chart

import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.os.Message
import android.text.TextUtils
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.util.CaughtRunnable
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ext.toJson
import com.govee.base2newth.AbsSingleController
import com.govee.base2newth.IControllerComm
import com.govee.base2newth.IDataComm
import com.govee.base2newth.chart.TimeUtil
import com.govee.base2newth.data.DataTimeSet
import com.govee.base2newth.data.EventDataProgress
import com.govee.base2newth.data.EventDataResult
import com.govee.base2newth.data.IBleOpData
import com.govee.base2newth.data.controller.ControllerHeartPrepare
import com.govee.base2newth.data.controller.ControllerHeartTimeRange4H5140
import com.govee.base2newth.data.controller.EventDataOver
import com.govee.base2newth.data.controller.EventDataQuerying
import com.govee.base2newth.data.controller.EventHeartPrepare
import com.govee.base2newth.data.controller.EventHeartStopSend
import com.govee.base2newth.data.controller.EventHeartTimeRange
import com.govee.base2newth.db.DbController
import com.govee.base2newth.db.TemHumPm
import com.govee.h5140.Constants5140
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JsonUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.util.UUID
import kotlin.math.min

/**
 * <AUTHOR>
 * @date created on 2022/06/07
 * @description 5106设备从设备端获取图表信息相关的处理器
 */
abstract class AbsThpBleData : IDataComm, IBleOpData {

    private var handlerThread: HandlerThread? = null
    private var subHandler: Handler? = null
    private var inDataComm = false
    private var sku: String? = null
    private var device: String? = null
    private var progress = 0
    private var all = 0
    private var dataTimeSet: DataTimeSet? = null
    private var curTime: Long = 0
    private var curStartTime = 0
    private var curEndTime = 0
    private var commFailTimes = 0

    companion object {
        private const val TAG = "AbsThpDataComm"
        private const val serviceUuidStr = "494e5445-4c4c-495f-524f-434b535f4857"
        private const val characteristicUuidStr = "494e5445-4c4c-495f-524f-434b535f2015"
        val serviceUUID = UUID.fromString(serviceUuidStr)
        val characteristicUUID = UUID.fromString(characteristicUuidStr)

        /**
         * 10s通信超时
         */
        private const val delay_countdown_comm_mills = (10 * 1000).toLong()
        private const val what_countdown_overtime = 100
        private const val def_max_command_retry_times = 5

        /**
         * 是否已处于数据对比页面
         */
        var isLoadThcd4Compare = false
    }

    private val finishRunnable: FinishRunnable = FinishRunnable(true)

    private val mainHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            val what = msg.what
            doWhat(what)
        }
    }
    private var nextTime: DataTimeSet.Time? = null
    private val runReadDataAgain: CaughtRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            prepareReadData(false)
        }
    }

    private inner class FinishRunnable(private var readOver: Boolean) : CaughtRunnable() {
        override fun runSafe() {
            if (LogInfra.openLog()) {
                LogInfra.Log.w(TAG, "FinishRunnable readOver1 = $readOver")
            }
            //通知数据读取结果
            EventDataResult.sendEventDataResult(readOver)
            //释放资源
            release()
        }

        fun setReadOver(readOver: Boolean) {
            if (LogInfra.openLog()) {
                LogInfra.Log.w(TAG, "FinishRunnable readOver2 = $readOver")
            }
            this.readOver = readOver
        }
    }

    private inner class ReceiveTemHumRunnable : CaughtRunnable {
        var curTime: Int
        var startTime: Int
        var endTime: Int
        var value: ByteArray

        constructor(curTime: Int, startTime: Int, endTime: Int, value: ByteArray) : super() {
            this.curTime = curTime
            this.startTime = startTime
            this.endTime = endTime
            this.value = value
        }

        override fun runSafe() {
            doParse(curTime, startTime, endTime, value)
        }
    }

    private fun doParse(curTime: Int, startTime: Int, endTime: Int, values: ByteArray) {
        SafeLog.i(
            "parse_h5140", "doParse() curTime = $curTime ; startTime = $startTime ; endTime = $endTime ; value2HexString = " + BleUtil.bytesToHexString(values)
        )
        val timeOffset = ByteArray(2)
        System.arraycopy(values, 0, timeOffset, 0, timeOffset.size)
        val thpList: MutableList<TemHumPm> = ArrayList()
        var timeOffsetMinutes = BleUtil.getSignedInt(timeOffset, true)
        LogInfra.Log.w("parse_h5140", "timeOffsetMinutes = $timeOffsetMinutes")
        val thpBytes = getTHPBytes(values)
        for (thByte in thpBytes) {
            val time = curTime - timeOffsetMinutes
            timeOffsetMinutes--
            if (isInvalidTHPValue(thByte)) {//无效数据
                LogInfra.Log.w("parse_h5140", "---------->无效数据")
                continue
            }
            if (time > endTime || time < startTime) {//数据不在读取范围内
                LogInfra.Log.w("parse_h5140", "---------->无效数据，数据不在读取范围内")
                continue
            }
            //解析温湿度数据
            val thpValue = com.govee.base2newth.chart.BleUtil.parseThpValue(thByte) ?: continue
            val tem = thpValue[0]
            val hum = thpValue[1]
            val pm25 = thpValue[2]
            SafeLog.i("parse_h5140_value", "time = $time ; tem = $tem ; hum = $hum ; co2 =$pm25")
            val thp = TemHumPm(
                tem,
                hum,
                pm25,
                com.govee.base2newth.chart.BleUtil.getValidTimeStamp(time),
                TemHumPm.FROM_TYPE_DEVICE
            )
            //加入有效数据集合
            thpList.add(thp)
            progress++
        }
        //存储有效数据
        if (thpList.isNotEmpty()) {
            DbController.insertDeviceData4Thp(sku, device, thpList)
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parseThData() all = $all ; progress = $progress")
        }
        EventDataProgress.sendEventDataProgress(all, progress)
    }

    /**
     * 根据协议截取温度、湿度、pm2.5的数据端段(其中也可能存在无效数据，全用0xff填充)
     *
     * @param value
     * @return
     */
    private fun getTHPBytes(value: ByteArray): Array<ByteArray> {
        val thpBytes = Array(3) { ByteArray(5) }
        var srcPos = 2
        for (thByte in thpBytes) {
            System.arraycopy(value, srcPos, thByte, 0, thByte.size)
            srcPos += 5
        }
        return thpBytes
    }

    /**
     * 判断截取取数据段中的无效数据
     *
     * @param thpValueBytes 5个字节 温湿度无效时全为ff,pm2.5的值无效时也全为ff,但两者不一定同时都为ff，因为温湿度和pm2.5不是同一个sensor测量的
     * @return
     */
    private fun isInvalidTHPValue(thpValueBytes: ByteArray): Boolean {
        var result = true
        val hexStr = BleUtil.bytesToHexString(thpValueBytes).replace("0x", "").replace(" ", "")
        if (hexStr.substring(0, 6) != "ffffff" && hexStr.substring(6, 10) != "ffff") {
            result = false
        }
        return result
    }

    private fun doWhat(what: Int) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "doWhat() what = $what")
        }
        if (what == what_countdown_overtime) {
            val finishRunnable: FinishRunnable = FinishRunnable(true)
            subHandler!!.post(finishRunnable)
        }
    }

    override fun inDataComm(): Boolean {
        return inDataComm
    }

    override fun release() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "release()")
        }
        inDataComm = false
        registerEvent(false)
        if (subHandler != null) {
            subHandler!!.removeCallbacksAndMessages(null)
        }
        if (handlerThread != null) {
            handlerThread!!.quitSafely()
        }
        mainHandler.removeCallbacksAndMessages(null)
    }

    override fun serviceUUID(): UUID {
        return serviceUUID
    }

    override fun characteristicUUID(): UUID {
        return characteristicUUID
    }

    override fun isSelfComm(
        serviceUuid: String,
        characteristicUuid: String,
        values: ByteArray
    ): Boolean {
        return inDataComm && serviceUuidStr == serviceUuid && characteristicUuidStr == characteristicUuid
    }

    override fun parse(
        serviceUuid: String,
        characteristicUuid: String,
        values: ByteArray
    ): Boolean {
        if (inDataComm) {
            //查询数据中；重置倒计时
            doHandler(doAtOnce = false, readOver = false)
            val receiveTemHumRunnable = ReceiveTemHumRunnable(
                com.govee.base2newth.chart.BleUtil.getTimeStamp(curTime),
                curStartTime,
                curEndTime,
                values
            )
            SafeLog.d("parse_h5140") { "------->>>>parse解析图表数据" }
            subHandler!!.post(receiveTemHumRunnable)
        } else {
            SafeLog.d("parse_h5140") { "------->>>>h5140 parse---->" }
        }
        return true
    }

    override fun parsePriority(): Int {
        return Constants5140.COMM_PARSE_PRIORITY_DATA
    }

    override fun startDataOp(sku: String, device: String, dataTimeSet: DataTimeSet) {
        if (TextUtils.isEmpty(sku) || TextUtils.isEmpty(device)) {
            return
        }
        registerEvent(true)
        this.sku = sku
        this.device = device
        progress = 0
        this.dataTimeSet = dataTimeSet
        handlerThread = HandlerThread("ThDataCommBleWifiV1", Thread.MAX_PRIORITY)
        handlerThread!!.start()
        subHandler = Handler(handlerThread!!.looper)
        curTime = System.currentTimeMillis()
        SafeLog.i("temp_h5140_chart") { "4---------->>通知设备准备读取数据" }
        //通知设备准备读取数据
        prepareReadData(true)
    }

    protected abstract val controllerComm: IControllerComm

    protected abstract fun sendCmd(controller: AbsSingleController)

    private fun prepareReadData(readNext: Boolean) {
        if (readNext) {
            nextTime = dataTimeSet!!.nextTime
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "prepareReadData nextTime = " + JsonUtil.toJson(nextTime))
        }
        /*if (nextTime == null) {
            //读取完成
            doHandler(doAtOnce = true, readOver = true)
            return
        }*/
        val controllerHeartPrepare = ControllerHeartPrepare()
        SafeLog.i("temp_h5140_chart") { "4---------->>发送controllerHeartPrepare指令 0x02" }
        sendCmd(controllerHeartPrepare)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventHeartPrepare(event: EventHeartPrepare) {
        if (isLoadThcd4Compare) {
            return
        }
        val result = event.isResult
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventHeartPrepare() result = $result")
        }
        controllerComm.controllerEvent(event)
        SafeLog.i("temp_h5140_chart") { "5---------->>0x02发送成功result=$result" }
        if (result) {
            commFailTimes = 0
            //准备完成；开始进行数据读取
            inDataComm = true
            transport()
        } else if (commFailTimes < def_max_command_retry_times) {
            readDataAgain()
        } else {
            doHandler(true, false)
        }
    }

    private fun readDataAgain() {
        commFailTimes++
        subHandler!!.removeCallbacks(runReadDataAgain)
        subHandler!!.postDelayed(runReadDataAgain, 4000)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventDataQuerying(event: EventDataQuerying?) {
        if (isLoadThcd4Compare) {
            return
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDataQuerying()")
        }
        //查询数据中；重置倒计时
        doHandler(doAtOnce = false, readOver = false)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventDataOver(event: EventDataOver) {
        if (isLoadThcd4Compare) {
            return
        }
        val packageNums = event.packageNums
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDataOver() packageNums = $packageNums")
        }
        //移除超时
        doHandler(doAtOnce = false, readOver = false)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventStopHeart(event: EventHeartStopSend?) {
        if (isLoadThcd4Compare) {
            return
        }
        release()
    }

    private fun transport() {
        //记录待读取时间段范围
        SafeLog.d("temp_h5140_chart") { "------>dataTimeSet=${dataTimeSet?.toJson()}" }
        val endTime = System.currentTimeMillis()
        val minTimeStamp = System.currentTimeMillis() - TimeUtil.getMills(Constants5140.max_times_to_read_from_device)
        val startTime = dataTimeSet?.minStartTime ?: minTimeStamp
        SafeLog.d("temp_h5140_chart") { "startTime=${TimeFormatM.getInstance().formatTimeToHMYMD(startTime)}" }
        val cont = ControllerHeartTimeRange4H5140(startTime / 1000, endTime / 1000)
        SafeLog.i("temp_h5140_chart") { "4---------->>图表数据第一步，发送读取时间范围ControllerHeartTimeRange指令0x01" }
        curStartTime = com.govee.base2newth.chart.BleUtil.getTimeStamp(startTime)
        curEndTime = com.govee.base2newth.chart.BleUtil.getTimeStamp(endTime)
        all = min(curEndTime - curStartTime, 60 * 24 * 20)// 最多读取20天数据
        sendCmd(cont)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventHeartTimeRange(event: EventHeartTimeRange) {
        if (isLoadThcd4Compare) {
            return
        }
        val result = event.isResult
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventHeartTimeRange() result = $result")
        }
        SafeLog.i("temp_h5140_chart") { "5---------->>ControllerHeartTimeRange指令0x01，结果result=$result" }
        controllerComm.controllerEvent(event)
        if (result) {
            commFailTimes = 0
            doHandler(false, false)
        } else {
            doHandler(true, false)
        }
    }

    private fun doHandler(doAtOnce: Boolean, readOver: Boolean) {
        subHandler!!.removeCallbacks(finishRunnable)
        finishRunnable.setReadOver(true)
        subHandler!!.postDelayed(finishRunnable, if (doAtOnce) 10 else delay_countdown_comm_mills)
    }

    private fun getOffset(millsStart: Long, millsEnd: Long, millsCur: Long): IntArray {
        val startMinutes = com.govee.base2newth.chart.BleUtil.getTimeStamp(millsStart)
        val endMinutes = com.govee.base2newth.chart.BleUtil.getTimeStamp(millsEnd)
        val curMinutes = com.govee.base2newth.chart.BleUtil.getTimeStamp(millsCur)
        val result = IntArray(2)
        result[0] = (curMinutes - startMinutes).coerceAtLeast(0)
        result[1] = (curMinutes - endMinutes).coerceAtLeast(0)
        return result
    }

    private fun registerEvent(register: Boolean) {
        if (register) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this)
            }
        } else {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this)
            }
        }
    }
}