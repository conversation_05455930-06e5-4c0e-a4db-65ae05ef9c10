package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * @author：<PERSON><PERSON><PERSON>.Chen
 * @date：2025/7/28 下午6:22
 * @description：
 */
class EventNoRemindMode(result: Bo<PERSON>an, write: Boolean, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, !write) {

    var isOpen = false

    var startHour = 0

    var startMinute = 0

    var endHour = 0

    var endMinute = 0

    companion object {
        fun sendSuc(
            write: Boolean, commandType: Byte, proType: Byte,
            isOpen: Boolean,
            startHour: Int,
            startMinute: Int,
            endHour: Int,
            endMinute: Int
        ) {
            val event = EventNoRemindMode(true, write, commandType, proType)
            event.isOpen = isOpen
            event.startHour = startHour
            event.startMinute = startMinute
            event.endHour = endHour
            event.endMinute = endMinute
            EventBus.getDefault().post(event)
        }

        fun sendWriteResult(
            result: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte,
            isOpen: Boolean,
            startHour: Int,
            startMinute: Int,
            endHour: Int,
            endMinute: Int
        ) {
            val event = EventNoRemindMode(result, true, commandType, proType)
            event.isOpen = isOpen
            event.startHour = startHour
            event.startMinute = startMinute
            event.endHour = endHour
            event.endMinute = endMinute
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventNoRemindMode(false, write, commandType, proType))
        }
    }
}