package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * @author：Yang<PERSON><PERSON>.Chen
 * @date：2025/5/22 下午3:30
 * @description：
 */
class EventAirNotify(result: <PERSON><PERSON><PERSON>, write: Bo<PERSON>an, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, !write) {

    var switch = false

    companion object {

        /**
         * 用来做从sp读取的刷新操作
         */
        fun sendRefreshEvent() {
            EventBus.getDefault().post(EventTempUnit(true, true, 0, 0).apply {
                onlyRefreshFromSp = true
            })
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte,switch: Boolean) {
            val event = EventAirNotify(true, write, commandType, proType)
            event.switch = switch
            EventBus.getDefault().post(event)
        }

        fun sendWriteResult(result: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte, switch: Boolean) {
            val event = EventAirNotify(result, true, commandType, proType)
            event.switch = switch
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventAirNotify(false, write, commandType, proType))
        }

    }

}