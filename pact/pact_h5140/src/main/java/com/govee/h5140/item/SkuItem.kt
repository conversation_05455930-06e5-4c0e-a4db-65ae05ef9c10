package com.govee.h5140.item

import android.content.Context
import android.content.Intent
import android.os.Bundle
import com.govee.base2home.main.choose.BaseBleDeviceChooseActivity
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.sku.AbsSkuItem
import com.govee.base2home.theme.ThemeM
import com.govee.h5140.Constants5140
import com.govee.h5140.add.PairGuideActivity
import com.govee.ui.R

/**
 * <AUTHOR>
 * @date created on 2022/5/25
 * @description 添加5106设备时点击skuItem的具体操作类
 */
class SkuItem() : AbsSkuItem() {

    override fun getGoodsType(): Int {
        return GoodsType.GOODS_TYPE_VALUE_H5140
    }

    override fun needLogin(): Boolean {
        return true
    }

    override fun forceGpsOpen(): Boolean {
        return false
    }

    override fun getDefIcon(): Int {
        return ThemeM.getDefSkuRes(sku)
    }

    override fun getSku(): String? {
        return if (product != null) product.sku else ""
    }

    override fun needBle(): <PERSON><PERSON><PERSON> {
        return true
    }

    override fun click(context: Context?) {
        val bundle = Bundle()
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_NEED_LOCATION, true)
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG, true)
        bundle.putString(BaseBleDeviceChooseActivity.INTENT_KEY_SKU_NAME, sku)
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG_HINT, true)
        bundle.putInt(
            BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG_HINT_DES,
            R.string.h5074_more_device_5074_hint_des
        )
        bundle.putInt(
            BaseBleDeviceChooseActivity.INTENT_KEY_DEVICE_FLASHING_RSSI_VALUE,
            Constants5140.BLE_SIGNAL_COMPARE_VALUE
        )
        //加入信号强度偏移量
        bundle.putInt(
            BaseBleDeviceChooseActivity.INTENT_KEY_FILTER_DIFF,
            Constants5140.BLE_SIGNAL_COMPARE_OFFSET_VALUE
        )
        //加入蓝牙名称过滤
        //设备蓝牙名称没有H，不能传入H5140,否则会造成扫描不到设备
        bundle.putString(BaseBleDeviceChooseActivity.INTENT_KEY_FILTER_BLE_NAME, "5140")
        val intent = Intent(context, PairGuideActivity::class.java)
        intent.putExtras(bundle)
        context?.startActivity(intent)
    }
}
