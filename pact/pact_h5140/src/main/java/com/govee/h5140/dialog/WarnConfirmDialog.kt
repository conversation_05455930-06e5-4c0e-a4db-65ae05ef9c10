package com.govee.h5140.dialog

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import com.govee.base2kt.ext.clickDelay
import com.govee.h5140.databinding.H5140DialogConfirmDialogBinding
import com.ihoment.base2app.dialog.BaseDialog
import com.ihoment.base2app.util.AppUtil

/**
 * @author：YangQi.Chen
 * @date：2025/4/27 上午10:21
 * @description：
 */
class WarnConfirmDialog(context: Context) : BaseDialog(context) {

    private lateinit var binding: H5140DialogConfirmDialogBinding

    init {
        initUI()
    }

    private fun initUI() {
        binding.btnDone.clickDelay {
            hide()
        }
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth() * 335 / 375;
    }

    override fun getLayoutView(): View {
        binding = H5140DialogConfirmDialogBinding.inflate(LayoutInflater.from(context))
        return binding.root
    }

    public override fun changeDialogOutside(isCanClick: Boolean) {
        super.changeDialogOutside(isCanClick)
    }

    fun setTitle(title: String) {
        binding.title.text = title
    }

    fun setContent(content: String) {
        binding.content.text = content
    }

    companion object {
        fun showConfirmDialog(context: Context, title: String, content: String, btnText: String) {
            val dialog = WarnConfirmDialog(context)
            dialog.setTitle(title)
            dialog.setContent(content)
            dialog.setBtnText(btnText)
            dialog.show()
        }
    }

    fun setBtnText(btnText: String) {
        binding.btnDone.text = btnText
    }
}