package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/6/2
 * @description 向5106设备发送清除数据指令后，将结果通过EventBus向外发布
 */
class EventClearData : AbsControllerEvent {

    constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON>ole<PERSON>, commandType: Byte, proType: Byte) : super(result, write, commandType, proType, !write)

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventClearData(false, write, commandType, proType))
        }

        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte) {
            val event = EventClearData(result, true, commandType, proType)
            EventBus.getDefault().post(event)
        }
    }
}