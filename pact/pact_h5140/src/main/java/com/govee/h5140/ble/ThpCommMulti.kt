package com.govee.h5140.ble

import com.govee.base2newth.AbsMultiBleComm
import java.util.UUID
import com.govee.h5140.Constants5140

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 5106设备ble多包指令通讯器
 */
class ThpCommMulti : AbsMultiBleComm() {

    companion object {
        private const val serviceUuidStr = "494e5445-4c4c-495f-524f-434b535f4857"
        private const val characteristicUuid = "494e5445-4c4c-495f-524f-434b535f2011"
        private val serviceUUID = UUID.fromString(serviceUuidStr)
        private val characteristicUUID = UUID.fromString(characteristicUuid)
    }

    override fun serviceUUID(): UUID {
        return serviceUUID
    }

    override fun characteristicUUID(): UUID {
        return characteristicUUID
    }

    override fun parsePriority(): Int {
        return Constants5140.COMM_PARSE_PRIORITY_THP
    }
}