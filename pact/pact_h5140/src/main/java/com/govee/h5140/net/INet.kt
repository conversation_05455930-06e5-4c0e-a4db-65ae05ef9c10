package com.govee.h5140.net

import com.govee.base2home.UrlConstants
import com.ihoment.base2app.network.BaseResponse
import retrofit2.Call
import retrofit2.http.Body
import retrofit2.http.POST

/**
 * <AUTHOR>
 * @date created on 2022/5/31
 * @description 网络请求接口相关
 */
interface INet {

    @POST(UrlConstants.DEVICE_SETTING)
    fun updateSettingNameAndRate(@Body request: RequestSettingName): Call<BaseResponse>

    @POST(UrlConstants.DEVICE_SETTING)
    fun updateSetting(@Body request: RequestSetting): Call<BaseResponse>

    @POST(UrlConstants.DEVICE_SETTING)
    fun updatePm25Warning(@Body request: RequestPm25Warning): Call<BaseResponse>

    @POST(UrlConstants.DEVICE_SETTING)
    fun updateAirQsSetting(@Body request: RequestAirQs): Call<ResponseAirQs>
}