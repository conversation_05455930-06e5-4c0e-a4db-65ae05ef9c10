package com.govee.h5140.item

import android.os.Bundle
import com.alibaba.android.arouter.facade.annotation.Route
import com.govee.base2home.constant.PathBaseHome
import com.govee.base2home.main.AbsDevice
import com.govee.base2newth.deviceitem.Ext
import com.govee.h5140.Constants5140.intent_ac_bleAddress
import com.govee.h5140.Constants5140.intent_ac_device
import com.govee.h5140.Constants5140.intent_ac_deviceName
import com.govee.h5140.Constants5140.intent_ac_goodsType
import com.govee.h5140.Constants5140.intent_ac_hard_version
import com.govee.h5140.Constants5140.intent_ac_hum_cali
import com.govee.h5140.Constants5140.intent_ac_key_wifi_mac
import com.govee.h5140.Constants5140.intent_ac_sku
import com.govee.h5140.Constants5140.intent_ac_soft_version
import com.govee.h5140.Constants5140.intent_ac_tem_cali
import com.govee.h5140.Constants5140.intent_ac_topic
import com.govee.h5140.detail.chart.Activity4CharInfo
import com.govee.widget.manager.IWidgetJumpService
import com.ihoment.base2app.util.JsonUtil

@Route(path = PathBaseHome.URL_WIDGET_SERVICE_H5140)
class WidgetJumpServiceImp : IWidgetJumpService {
    override fun combineBundle(device: AbsDevice): Bundle {
        var ext: Ext = Ext()
        device.deviceExt?.run {
            ext = JsonUtil.fromJson(this.deviceSettings, Ext::class.java) ?: Ext()
        }
        val bundle = Bundle()
        bundle.putInt(intent_ac_goodsType, device.goodsType)
        bundle.putString(intent_ac_sku, device.sku)
        bundle.putString(intent_ac_device, device.device)
        bundle.putString(intent_ac_deviceName, device.deviceName)
        bundle.putString(intent_ac_bleAddress, ext.address)
        bundle.putInt(intent_ac_tem_cali, ext.temCali)
        bundle.putInt(intent_ac_hum_cali, ext.humCali)
        bundle.putString(intent_ac_key_wifi_mac, ext.wifiMac)
        bundle.putString(intent_ac_soft_version, device.versionSoft)
        bundle.putString(intent_ac_hard_version, device.versionHard)
        bundle.putString(intent_ac_topic, ext.topic)
        return bundle
    }

    override fun supportAcClassName(deviceInfo: AbsDevice): String? {
        return Activity4CharInfo::class.java.name
    }
}