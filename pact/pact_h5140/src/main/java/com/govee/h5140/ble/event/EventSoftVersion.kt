package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取设备软件版本号的事件
 */
class EventSoftVersion(result: <PERSON><PERSON>an, write: <PERSON><PERSON>an, commandType: Byte, proType: Byte) :
        AbsControllerEvent(result, write, commandType, proType, !write) {

    var softVersion: String = ""

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventSoftVersion(false, write, commandType, proType))
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, softVersion: String) {
            val event = EventSoftVersion(true, write, commandType, proType)
            event.softVersion = softVersion
            EventBus.getDefault().post(event)
        }
    }
}