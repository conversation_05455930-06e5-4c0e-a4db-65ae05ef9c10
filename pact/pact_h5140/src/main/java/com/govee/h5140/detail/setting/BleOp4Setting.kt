package com.govee.h5140.detail.setting

import android.os.Handler
import android.os.Looper
import android.os.Message
import com.govee.base2home.temUnit.TemperatureUnitType
import com.govee.base2newth.AbsSingleController
import com.govee.base2newth.AbsThBle
import com.govee.base2newth.data.Co2LevelInfo
import com.govee.base2newth.data.Co2WarnInfo
import com.govee.base2newth.data.H5140C02WarnConfig
import com.govee.base2newth.data.THMemoryUtil
import com.govee.base2newth.setting.IBleOp
import com.govee.base2newth.setting.IBleOpResult
import com.govee.ble.BleController
import com.govee.ble.event.BTStatusEvent
import com.govee.ble.event.EventBleConnect
import com.govee.h5140.ble.ThpBleCommManager.Companion.getInstance
import com.govee.h5140.ble.command.CommandAirNotify
import com.govee.h5140.ble.command.CommandCo2SetGrade
import com.govee.h5140.ble.command.CommandCo2Warning
import com.govee.h5140.ble.command.CommandDisplay
import com.govee.h5140.ble.command.CommandHardVersion
import com.govee.h5140.ble.command.CommandHumCali
import com.govee.h5140.ble.command.CommandHumWarning
import com.govee.h5140.ble.command.CommandLightnessV2
import com.govee.h5140.ble.command.CommandSoftVersion
import com.govee.h5140.ble.command.CommandSoundLevel
import com.govee.h5140.ble.command.CommandSyncTime
import com.govee.h5140.ble.command.CommandTemCali
import com.govee.h5140.ble.command.CommandTemUnit
import com.govee.h5140.ble.command.CommandTemWarning
import com.govee.h5140.ble.command.CommandTimeFormat
import com.govee.h5140.ble.command.CommandTimeZone
import com.govee.h5140.ble.event.EventAirNotify
import com.govee.h5140.ble.event.EventClearData
import com.govee.h5140.ble.event.EventCo2ManualCalibration
import com.govee.h5140.ble.event.EventCo2SetGrade
import com.govee.h5140.ble.event.EventCo2Warning
import com.govee.h5140.ble.event.EventDisplay
import com.govee.h5140.ble.event.EventHardVersion
import com.govee.h5140.ble.event.EventHumCali
import com.govee.h5140.ble.event.EventHumWarning
import com.govee.h5140.ble.event.EventLightness
import com.govee.h5140.ble.event.EventLightnessV2
import com.govee.h5140.ble.event.EventNoRemindMode
import com.govee.h5140.ble.event.EventSoftVersion
import com.govee.h5140.ble.event.EventSoundLevel
import com.govee.h5140.ble.event.EventSyncTime
import com.govee.h5140.ble.event.EventTemCali
import com.govee.h5140.ble.event.EventTemWarning
import com.govee.h5140.ble.event.EventTempUnit
import com.govee.h5140.ble.event.EventTimeFormat
import com.govee.h5140.ble.event.EventTimeZone
import com.govee.h5140.ble.event.HindLoadingEvent
import com.govee.h5140.detail.chart.H5140TempUnitHelper
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.ToastUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2022/06/01
 * @description 5106设备设置页的ble操作类
 */
internal class BleOp4Setting : IBleOp {

    private val ext4St: Ext4St
    private var opResult: IBleOpResult?
    private val handler: Handler
    private var step: Int
    private var destroy = false
    private val waitingControllers: Array<AbsSingleController>

    companion object {
        private const val TAG = "BleOp4Setting"
        private const val what_check_step = 1000
        private const val step_ble_def = -1
        private const val step_ble_connecting = 1
        private const val step_ble_disconnected = 2
        private const val step_ble_unable = 3
        private const val step_ble_reading_device_info = 4
        private const val step_ble_info_over = 5
    }

    constructor(ext4St: Ext4St, opResult: IBleOpResult?) {
        this.ext4St = ext4St
        this.opResult = opResult
        registerEvent(true)
        this.handler = object : Handler(Looper.getMainLooper()) {
            override fun handleMessage(msg: Message) {
                super.handleMessage(msg)
                val what = msg.what
                doWhat(what)
            }
        }
        this.step = step_ble_def
        this.waitingControllers = arrayOf(
            CommandSyncTime(),
            CommandTimeZone(),
            CommandHardVersion(),
            CommandSoftVersion(),
            CommandCo2Warning(),
            CommandTemWarning(),
            CommandHumWarning(),
            CommandDisplay(),
            CommandTimeFormat(),
            CommandAirNotify(),
            //CommandLightness(),
            CommandLightnessV2(),
            CommandTemCali(),
            CommandHumCali(),
            CommandSoundLevel(),
            CommandCo2SetGrade(),
            CommandTemUnit()
        )
    }

    private val thBle: AbsThBle
        get() = getInstance()

    private fun doWhat(what: Int) {
        if (what == what_check_step) {
            checkStep()
        }
    }

    private fun checkStep() {
        if (step == step_ble_disconnected) {
            if (opResult != null) {
                opResult!!.bleDisconnect()
            }
        } else if (step == step_ble_unable) {
            if (opResult != null) {
                opResult!!.bleUnable()
            }
        } else if (step == step_ble_info_over) {
            if (opResult != null) {
                opResult!!.infoOver()
            }
        } else {
            if (opResult != null) {
                opResult!!.bleConnecting()
            }
        }
    }

    private fun registerEvent(register: Boolean) {
        if (register) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this)
            }
        } else {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this)
            }
        }
    }

    override fun onDestroy() {
        if (!destroy) {
            registerEvent(false)
            destroy = true
            opResult = null
        }
    }

    override fun sendController(vararg controllers: AbsSingleController) {
        thBle.startControllers(*controllers)
    }

    override fun sendExtController(cleanPre: Boolean, vararg controllers: AbsSingleController?) {
        thBle.startExtControllers(cleanPre, *controllers)
    }

    override fun connectBle() {
        //详情页已连接，不需要在连接一次
        if (BleController.getInstance().isConnected && ext4St.address == BleController.getInstance().connectedBleAddress) {
            EventBleConnect.sendEventBleConnect(
                EventBleConnect.Type.ble_connect_suc,
                ext4St.address,
                0
            )
            return
        }
        val connectBle = thBle.connectBle(ext4St.address)
        if (connectBle) {
            updateStep(step_ble_connecting)
        } else {
            updateStep(step_ble_unable)
        }
    }

    private fun updateStep(step: Int) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "updateStep() step = $step")
        }
        this.step = step
        handler.sendEmptyMessage(what_check_step)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventBleConnect(event: EventBleConnect) {
        val connectSuc = event.connectSuc()
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBleConnect() connectSuc = $connectSuc")
        }
        if (connectSuc) {
            updateStep(step_ble_reading_device_info)
            thBle.startExtControllers(true, *waitingControllers)
        } else {
            updateStep(step_ble_disconnected)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventBTStatus(event: BTStatusEvent) {
        val btOpen = event.isBtOpen
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBTStatus() btOpen = $btOpen")
        }
        if (btOpen) {
            //蓝牙重新打开;重新尝试连接设备
            connectBle()
        } else {
            updateStep(step_ble_unable)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSyncTime(event: EventSyncTime) {
        val result = event.isResult
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSyncTime() result = $result")
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTimeZone(event: EventTimeZone) {
        val result = event.isResult
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventTimeZone() result = $result")
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventHardVersion(event: EventHardVersion) {
        val result = event.isResult
        if (result) {
            val hardVersion = event.hardVersion
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventHardVersion() hardVersion = $hardVersion")
            }
            ext4St.versionHard = hardVersion
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSoftVersion(event: EventSoftVersion) {
        val result = event.isResult
        if (result) {
            val softVersion = event.softVersion
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventSoftVersion() softVersion = $softVersion")
            }
            ext4St.versionSoft = softVersion
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSoundLevel(event: EventSoundLevel) {
        EventBus.getDefault().post(HindLoadingEvent())
        val result = event.isResult
        if (result) {
            val switch = event.switch
            val level = event.level
            ext4St.soundLevel = level
            ext4St.soundSwitch = switch
            LogInfra.Log.i(TAG, "EventSoundLevel() switch = $switch,level = $level")
        }
        if (event.isWrite) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
            if (result) {
                ToastUtil.getInstance().toast(com.govee.ui.R.string.bbq_presettem_successful)
            } else {
                ToastUtil.getInstance().toast(com.govee.ui.R.string.bbq_presettem_failed)
            }
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventCo2GradeLevel(event: EventCo2SetGrade) {
        val result = event.isResult
        if (result) {
            val minLevel = event.minLevel
            val maxLevel = event.maxLevel
            ext4St.co2GradeMin = minLevel
            ext4St.co2GradeMax = maxLevel
        }
        if (event.isWrite) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
            if (result) {
                ToastUtil.getInstance().toast(com.govee.ui.R.string.h5140_set_success)
            } else {
                ToastUtil.getInstance().toast(com.govee.ui.R.string.h5140_set_fail)
            }
            H5140C02WarnConfig.updateCo2LevelInfo(ext4St.sku, ext4St.device, Co2LevelInfo(event.minLevel, event.maxLevel))
        }
        thBle.controllerEvent(event)
        //再去读取温度单位和新版本亮度信息
        thBle.startExtControllers(true, CommandTemUnit())
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventAirNotify(event: EventAirNotify) {
        val result = event.isResult
        if (result) {
            SafeLog.d(TAG) { "发送空气质量指令成功,${event.switch}" }
            THMemoryUtil.getInstance().setAirQsOpen(ext4St.sku + "_" + ext4St.device, event.switch)
        }
        if (event.isWrite) {
            opResult?.writeResult(result, event.commandType)
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventCo2ManualCalibration(event: EventCo2ManualCalibration) {
        if (event.isWrite) {
            if (event.isResult) {
                ToastUtil.getInstance().toast(com.govee.ui.R.string.h5140_set_success)
                Co2CalibrationActivity.cacheAdjustTime(ext4St.sku, ext4St.device)
            } else {
                ToastUtil.getInstance().toast(com.govee.ui.R.string.h5140_set_fail)
            }
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventPm25Warning(event: EventCo2Warning) {
        val result = event.isResult
        val write = event.isWrite
        if (result) {
            val openWarning = event.openWarning
            val minCo2 = event.minCo2
            val maxCo2 = event.maxCo2
            val alarmInterval = event.alarmInterval
            H5140C02WarnConfig.updateCo2WarnInfo(
                ext4St.sku,
                ext4St.device,
                Co2WarnInfo(event.minCo2, event.maxCo2, event.openWarning, event.alarmInterval)
            )
            ext4St.co2AlarmMin = minCo2
            ext4St.co2AlarmMax = maxCo2
            ext4St.co2AlarmSwitch = openWarning
            ext4St.alarmInterval = alarmInterval
        }
        if (write) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventDNDMode(event: EventNoRemindMode){
        val result = event.isResult
        val write = event.isWrite
        if (result){
            ext4St.dndIsOpen = event.isOpen
            ext4St.dndStartHour = event.startHour
            ext4St.dndStartMinute = event.startMinute
            ext4St.dndEndHour = event.endHour
            ext4St.dndEndMinute = event.endMinute
        }
        if (write){
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTemWarning(event: EventTemWarning) {
        val result = event.isResult
        val write = event.isWrite
        if (result) {
            val openWarning = event.openWarning
            val minTem = event.minTem
            val maxTem = event.maxTem
            if (LogInfra.openLog()) {
                LogInfra.Log.i(
                    TAG,
                    "onEventTemWarning() openWarning = $openWarning ; minTem = $minTem ; maxTem = $maxTem"
                )
            }
            ext4St.temMin = minTem
            ext4St.temMax = maxTem
            ext4St.temWarning = openWarning
        }
        if (write) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventHumWarning(event: EventHumWarning) {
        val result = event.isResult
        val write = event.isWrite
        if (result) {
            val openWarning = event.openWarning
            val minHum = event.minHum
            val maxHum = event.maxHum
            if (LogInfra.openLog()) {
                LogInfra.Log.i(
                    TAG,
                    "onEventHumWarning() openWarning = $openWarning ; minHum = $minHum ; maxHum = $maxHum"
                )
            }
            ext4St.humMin = minHum
            ext4St.humMax = maxHum
            ext4St.humWarning = openWarning
        }
        if (write) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventScreenDisplay(event: EventDisplay) {
        val result = event.isResult
        val write = event.isWrite
        if (result) {
            val sdType = event.sdType
            if (LogInfra.openLog()) {
                LogInfra.Log.i("test_screen", "onEventScreenDisplay() sdType = $sdType")
            }
            ext4St.sdType = sdType
        }
        if (write) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTimeFormat(event: EventTimeFormat) {
        val result = event.isResult
        val write = event.isWrite
        if (result) {
            val tfType = event.tfType
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventTimeFormat() tfType = $tfType")
            }
            ext4St.tfType = tfType
        }
        if (write) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventScreenLightness(event: EventLightness) {
        val result = event.isResult
        val write = event.isWrite
        if (result) {
            val slLevel = event.slLevel
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventScreenLightness() slLevel = $slLevel")
            }
            ext4St.slLevel = slLevel
        }
        if (write) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTemCali(event: EventTemCali) {
        val result = event.isResult
        val write = event.isWrite
        if (result) {
            val temCali = event.temCali
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventTemCali() temCali = $temCali")
            }
            ext4St.temCali = temCali
        }
        if (write) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventHumCali(event: EventHumCali) {
        val result = event.isResult
        val write = event.isWrite
        if (result) {
            val humCali = event.humCali
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventHumCali() humCali = $humCali")
            }
            ext4St.humCali = humCali
        }
        if (write) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
        }
        thBle.controllerEvent(event)
    }

    /**
     * 5.3.1迭代功能，新增读写温度单位以及新的屏幕亮度显示
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTempUnit(event: EventTempUnit) {
        val result = event.isResult
        val write = event.isWrite
        if (result) {
            val tuType = event.temUnit
            ext4St.tuType = tuType
            if (tuType == 1) {
                H5140TempUnitHelper.setTemUnit(ext4St.sku, ext4St.device, TemperatureUnitType.Fahrenheit)
            } else {
                H5140TempUnitHelper.setTemUnit(ext4St.sku, ext4St.device, TemperatureUnitType.Celsius)
            }
            SafeLog.d("onEventTempUnit"){"读取温度单位成功，设置页读取信息结束infoOver()"}
            infoOver()
        }
        if (write) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
        }
        thBle.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventScreenLightnessV2(event: EventLightnessV2) {
        val result = event.isResult
        val write = event.isWrite
        if (result) {
            val slInfo = event.lightnessV2
            if (LogInfra.openLog()) {
                LogInfra.Log.i(
                    TAG,
                    "onEventScreenLightnessV2() slInfo = ${JsonUtil.toJson(slInfo)}"
                )
            }
            ext4St.slV2 = slInfo
        }
        if (write) {
            if (opResult != null) {
                opResult!!.writeResult(result, event.commandType)
            }
        }
        thBle.controllerEvent(event)
    }

    /**
     * 所有数据读取完毕时调用
     */
    private fun infoOver() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "infoOver()")
        }
        updateStep(step_ble_info_over)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventClearData(event: EventClearData) {
        val result: Boolean = event.isResult
        val write: Boolean = event.isWrite
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventClearData() write = $write ; result = $result")
        }
        if (write) {
            if (opResult != null) opResult!!.writeResult(result, event.commandType)
        }
        thBle.controllerEvent(event)
    }
}