package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取湿度校准值的事件
 */
class EventHumCali(result: Boolean, write: Boolean, commandType: Byte, proType: Byte) :
        AbsControllerEvent(result, write, commandType, proType, !write) {

    //湿度校准值*100
    var humCali = 0

    companion object {
        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte, humCali: Int) {
            val event = EventHumCali(result, true, commandType, proType)
            event.humCali = humCali
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventHumCali(false, write, commandType, proType))
        }

        fun sendSuc(write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte, humCali: Int) {
            val event = EventHumCali(true, write, commandType, proType)
            event.humCali = humCali
            EventBus.getDefault().post(event)
        }
    }
}