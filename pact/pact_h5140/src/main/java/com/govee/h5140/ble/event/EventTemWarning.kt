package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取温度预警相关信息的事件
 */
class EventTemWarning(result: Boolean, write: Boolean, commandType: Byte, proType: Byte) :
        AbsControllerEvent(result, write, commandType, proType, !write) {

    //是否开启预警
    var openWarning = false

    //最低预警值*100
    var minTem = 0

    //最大预警值*100
    var maxTem = 0

    companion object {
        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte, openWarning: Boolean, minTem: Int, maxTem: Int) {
            val event = EventTemWarning(result, true, commandType, proType)
            event.openWarning = openWarning
            event.minTem = minTem
            event.maxTem = maxTem
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventTemWarning(false, write, commandType, proType))
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, openWarning: Boolean, minTem: Int, maxTem: Int) {
            val event = EventTemWarning(true, write, commandType, proType)
            event.openWarning = openWarning
            event.minTem = minTem
            event.maxTem = maxTem
            EventBus.getDefault().post(event)
        }
    }
}