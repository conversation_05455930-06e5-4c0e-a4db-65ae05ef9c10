package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/6/9
 * @description 从5106设备读/写设备时间显示的事件
 */
class EventTimeFormat : AbsControllerEvent {

    constructor(result: <PERSON>olean, write: Boolean, commandType: Byte, proType: Byte) : super(result, write, commandType, proType, !write)

    /**
     * 显示12小时制or24小时制
     */
    var tfType = 0

    companion object {
        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte, tfType: Int) {
            val event = EventTimeFormat(result, true, commandType, proType)
            event.tfType = tfType
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventTimeFormat(false, write, commandType, proType))
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, tfType: Int) {
            val event = EventTimeFormat(true, write, commandType, proType)
            event.tfType = tfType
            EventBus.getDefault().post(event)
        }
    }
}