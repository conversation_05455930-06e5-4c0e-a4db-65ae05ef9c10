package com.govee.h5140.ble.command

import com.govee.base2newth.AbsSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventNoRemindMode

/**
 * @author：YangQi.Chen
 * @date：2025/7/28 下午6:11
 * @description：勿扰模式
 */
class CommandNoRemindMode : AbsSingleController {

    private var isOpen = false

    private var startHour = 0

    private var startMinute = 0

    private var endHour = 0

    private var endMinute = 0

    /**
     * 写操作
     */
    constructor(isOpen: Boolean, startHour: Int, startMinute: Int, endHour: Int, endMinute: Int) : super(true) {
        this.isOpen = isOpen
        this.startMinute = startMinute
        this.startHour = startHour
        this.endHour = endHour
        this.endMinute = endMinute
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_WU_RAO_MODE
    }

    override fun parseValidBytes(validBytes: ByteArray?): Boolean {
        if (validBytes == null || validBytes.size < 2) {
            return false
        }
        val isOpen = validBytes[0].toInt() == 1
        val startHour = validBytes[1].toInt()
        val startMinute = validBytes[2].toInt()
        val endHour = validBytes[3].toInt()
        val endMinute = validBytes[4].toInt()
        EventNoRemindMode.sendSuc(isWrite, commandType, proType, isOpen, startHour, startMinute, endHour, endMinute)
        return true
    }

    override fun translateWrite(): ByteArray {
        val switchByte = if (isOpen) 0x01.toByte() else 0x00.toByte()
        return byteArrayOf(switchByte, startHour.toByte(), startMinute.toByte(), endHour.toByte(), endMinute.toByte())
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventNoRemindMode.sendWriteResult(suc, commandType, proType, isOpen, startHour, startMinute, endHour, endMinute)
        return true
    }

    override fun fail() {
        EventNoRemindMode.sendFail(isWrite, commandType, proType)
    }
}