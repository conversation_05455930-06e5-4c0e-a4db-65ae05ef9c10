package com.govee.h5140.iotop

import androidx.annotation.Keep
import com.govee.h5140.Constants5140
import com.govee.h5140.ble.event.EventLightnessV2

/**
 * <AUTHOR>
 * @date created on 2022/6/15
 * @description 5106iot心跳(status)指令中的信息实体类
 */
@Keep
class IotHeartInfo {

    var co2 = 0
    var co2Warning = false
    var co2Min = Constants5140.CO2_MIN_VALUE
    var co2Max = Constants5140.CO2_MAX_VALUE
    var alarmInterval = 10

    var tem = 0
    var temCali = 0
    var temWarning = false
    var temMin = Constants5140.TEM_MIN_VALUE * 100
    var temMax = Constants5140.TEM_MAX_VALUE * 100

    var hum = 0
    var humCali = 0
    var humWarning = false
    var humMin = Constants5140.HUM_MIN_VALUE * 100
    var humMax = Constants5140.HUM_MAX_VALUE * 100

    //其他设置相关(屏幕展示，时间制式，屏幕亮度,温度单位等)
    var sdType = 0
    var tfType = 0
    var slLevel = 0

    /**
     * 温度单位 0：摄氏度，1：华氏度
     */
    var tuType = 1

    /**
     * 蜂鸣声音
     */
    var soundLevel = 0
    var soundSwitch = false

    var slV2: EventLightnessV2.LightnessV2? = null

    var co2GradeMin = Constants5140.CO2_GRADE_MIN
    var co2GradeMax = Constants5140.CO2_GRADE_MAX

    /**
     * co2浓度通知
     */
    var co2NotifySwitch = false

    // 勿扰模式
    var dndIsOpen = false
    var dndStartHour = 22
    var dndStartMinute = 0
    var dndEndHour = 6
    var dndEndMinute = 0
}