package com.govee.h5140.add

import android.app.Activity
import android.bluetooth.BluetoothDevice
import android.content.Context
import com.govee.base2newth.AbsThBle
import com.govee.base2newth.bbq.ble.BleProtocol
import com.govee.base2newth.ui.ThConnectDialog4Secret
import com.govee.h5140.ble.ThpBleCommManager

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 正在连接中的弹窗
 */
class ConnectDialog private constructor(context: Context, device: BluetoothDevice, private val addInfo: AddInfo) :
        ThConnectDialog4Secret(context, device, addInfo.sku) {

    companion object {
        fun createDialog(context: Context, device: BluetoothDevice, addInfo: AddInfo): ConnectDialog {
            return ConnectDialog(context, device, addInfo)
        }
    }

    init {
        ble.inStep4AddDevice(true)
    }

    override fun getBle(): AbsThBle {
        return ThpBleCommManager.getInstance()
    }

    override fun getCommandType(): Byte {
        return BleProtocol.value_th_check_secret_code
    }

    /**
     * 跳转到配对页面
     */
    override fun jumpToPairAc() {
        Activity4Pair.jump2PairAc(context as Activity, addInfo, device)
    }
}