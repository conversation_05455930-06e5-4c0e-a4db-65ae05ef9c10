package com.govee.h5140.ble.command

import com.govee.base2newth.AbsOnlyWriteSingleController
import com.govee.h5140.add.TimeZoneUtils
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventSyncTime.Companion.sendWriteResult
import com.govee.h5140.ble.event.EventTimeZone

/**
 * <AUTHOR>
 * @date created on 2022/6/30
 * @description 向5106设备同步手机时间时区的指令，并解析其回复
 * 备注：这个是向设备写入，用33指令
 */
class CommandTimeZone : AbsOnlyWriteSingleController() {

    override fun translateWrite(): ByteArray {
        val timeZoneInfo = TimeZoneUtils.getOffsetTime()
        return byteArrayOf(timeZoneInfo[0].toByte(), timeZoneInfo[1].toByte())
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        sendWriteResult(suc, commandType, proType)
        return true
    }

    override fun fail() {
        EventTimeZone.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_TIME_ZONE
    }
}