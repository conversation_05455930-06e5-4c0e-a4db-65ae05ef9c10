package com.govee.h5140.detail.setting

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.SystemClock
import android.text.TextUtils
import android.view.View
import androidx.lifecycle.lifecycleScope
import com.govee.base2home.Constant
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.config.Base2homeConfig
import com.govee.base2home.config.DeviceRoomOrderConfig
import com.govee.base2home.device.DeviceM
import com.govee.base2home.device.EventDeviceTopic
import com.govee.base2home.device.unbind.UnUnBindMV1
import com.govee.base2home.iot.AbsCmd
import com.govee.base2home.iot.share.ShareController
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.main.OfflineDeviceListConfig
import com.govee.base2home.main.tab.EventTabDefault
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.temUnit.TemperatureUnitType
import com.govee.base2home.theme.ThemeM
import com.govee.base2home.update.IUpdateNet
import com.govee.base2home.update.download.CheckVersion
import com.govee.base2home.update.net.DeviceUpdateRequest
import com.govee.base2home.update.net.DeviceUpdateResponse
import com.govee.base2home.util.NumberUtil
import com.govee.base2home.util.TemUtil
import com.govee.base2kt.ext.gone
import com.govee.base2kt.ext.toJson
import com.govee.base2kt.ext.visible
import com.govee.base2light.ble.ota.v3.OtaOpV3
import com.govee.base2light.pact.iot.IIotOpResultV1
import com.govee.base2light.synctriggers.h5106.Command4H5106
import com.govee.base2light.synctriggers.h5106.SaveTriggersManager
import com.govee.base2newth.AbsSingleController
import com.govee.base2newth.ThUtil
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.config.WarnRange
import com.govee.base2newth.data.Co2LevelInfo
import com.govee.base2newth.data.Co2WarnInfo
import com.govee.base2newth.data.DataConfig
import com.govee.base2newth.data.H5140C02WarnConfig
import com.govee.base2newth.data.THMemoryUtil
import com.govee.base2newth.data.TaskLinkConfig
import com.govee.base2newth.db.DbController
import com.govee.base2newth.net.IThNet
import com.govee.base2newth.net.Request4GetSettings
import com.govee.base2newth.net.Request4Setting
import com.govee.base2newth.net.RequestClearData
import com.govee.base2newth.net.RequestHum
import com.govee.base2newth.net.RequestName
import com.govee.base2newth.net.RequestTem
import com.govee.base2newth.net.Response4GetSettings
import com.govee.base2newth.net.ResponseClearData
import com.govee.base2newth.net.ResponseName
import com.govee.base2newth.other.ClearThDataUtils
import com.govee.base2newth.other.ClearThDataUtils.clearByPeriodType4Thp
import com.govee.base2newth.other.ClearThDataUtils.getThpDataCacheMemory
import com.govee.base2newth.other.Dialog4LoadAtd
import com.govee.base2newth.other.Event4HasClearCache
import com.govee.base2newth.other.Event4HasClearCache.Companion.sendEvent
import com.govee.base2newth.other.Event4LoadAllData
import com.govee.base2newth.other.Event4LoadAllData.Companion.sendEvent
import com.govee.base2newth.setting.EventAfterClearDataSuc
import com.govee.base2newth.setting.EventNameChange
import com.govee.base2newth.setting.EventNewVersion
import com.govee.base2newth.setting.IBleOpResult
import com.govee.base2newth.update.UpdateSucEvent
import com.govee.ble.BleController
import com.govee.h5140.Constants5140
import com.govee.h5140.add.Activity4WifiChoose.Companion.jump2WifiChooseAc4ChangeWifi
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.EventHeart
import com.govee.h5140.ble.H5140HeartSender
import com.govee.h5140.ble.ThpBleCommManager.Companion.getInstance
import com.govee.h5140.ble.command.CommandAirNotify
import com.govee.h5140.ble.command.CommandClearData
import com.govee.h5140.ble.command.CommandCo2ManualCalibration
import com.govee.h5140.ble.command.CommandCo2SetGrade
import com.govee.h5140.ble.command.CommandCo2Warning
import com.govee.h5140.ble.command.CommandDisplay
import com.govee.h5140.ble.command.CommandHumCali
import com.govee.h5140.ble.command.CommandHumWarning
import com.govee.h5140.ble.command.CommandLightnessV2
import com.govee.h5140.ble.command.CommandNoRemindMode
import com.govee.h5140.ble.command.CommandSoundLevel
import com.govee.h5140.ble.command.CommandTemCali
import com.govee.h5140.ble.command.CommandTemUnit
import com.govee.h5140.ble.command.CommandTemWarning
import com.govee.h5140.ble.command.CommandTimeFormat
import com.govee.h5140.ble.event.AbsCmdBean
import com.govee.h5140.ble.event.EventLightnessV2
import com.govee.h5140.ble.event.EventTempUnit
import com.govee.h5140.ble.event.HindLoadingEvent
import com.govee.h5140.detail.chart.Activity4CharInfo
import com.govee.h5140.detail.chart.BleOp4ChartInfo
import com.govee.h5140.detail.chart.H5140TempUnitHelper
import com.govee.h5140.dialog.Co2LevelSettingDialog
import com.govee.h5140.dialog.Co2LevelSettingDialog.OnSetLevelListener
import com.govee.h5140.iotop.Cmd5140
import com.govee.h5140.iotop.CmdPtReal
import com.govee.h5140.iotop.CmdStatus
import com.govee.h5140.iotop.IotHeartInfo
import com.govee.h5140.iotop.IotOpManager
import com.govee.h5140.iotop.IotParseUtils
import com.govee.h5140.net.INet
import com.govee.h5140.net.RequestAirQs
import com.govee.h5140.net.RequestPm25Warning
import com.govee.h5140.net.RequestSetting
import com.govee.h5140.net.ResponseAirQs
import com.govee.h5140.update.Activity4DeviceUpdate
import com.govee.home.account.config.AccountConfig
import com.govee.shared.CommSharedSettingsAc
import com.govee.ui.R
import com.govee.ui.dialog.HintDialog1
import com.ihoment.base2app.Cache
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.util.CaughtRunnable
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.ToastUtil
import com.kk.taurus.playerbase.utils.NetworkUtils
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode


/**
 * <AUTHOR>
 * @date created on 2022/06/01
 * @description 5106设备的信息设置页面
 */
class Activity4Setting : AbsThpBleWifiSettingAc() {

    private var fahOpen = false
    private var realTem = 0f
    private var realHum = 0f
    private var checkVersion: CheckVersion? = null
    private lateinit var ext4St: Ext4St
    private val mHandler = Handler(Looper.getMainLooper())

    /**
     * 标记是否清除了数据
     */
    private var clearDataSuc = false

    /**
     * 是否需要通知用户操作蓝牙
     */
    private var needNotifyUserOpBluetooth = false
    private var bleOp: BleOp4Setting? = null
    private var lifeCyclePause = false

    private var softVersion = ""
    private var hardVersion = ""
    private var topic = ""

    /**
     * 记录首次加载设备数据是否已经完成
     */
    private var lastIotCmdType: Byte = -1

    /**
     * iot指令连续发送相关
     */
    private var lastIotCmdTimeMills = 0L

    /**
     * 发送的指令值，即：command部分的值
     */
    private var sendIotCmds = ArrayList<CmdPtReal>()

    @Volatile
    private var iotSendOver = true

    //5.9.0-->拉取全部数据的加载弹窗
    private val loadAtdDialog by lazy {
        Dialog4LoadAtd.Companion.createDialog(this)
    }

    companion object {
        var iotOnline = false

        //重连间隔时间
        private const val RECONNECT_INTERVAL_TIME = 60 * 1000L

        //连续点击时，iot指令的间隔时间
//        private const val IOT_CMD_INTERVAL_TIME = 2 * 1000L
        private const val IOT_CMD_INTERVAL_TIME = 0 * 1000L

        /**
         * 跳转到设置界面
         */
        fun jump2SettingAc(
            ac: Activity,
            goodsType: Int,
            sku: String,
            device: String,
            bleAddress: String,
            deviceName: String,
            wifiMac: String,
            softVersion: String,
            hardVersion: String,
            topic: String,
        ) {
            val bundle = Bundle()
            bundle.putInt(Constants5140.intent_ac_goodsType, goodsType)
            bundle.putString(Constants5140.intent_ac_sku, sku)
            bundle.putString(Constants5140.intent_ac_device, device)
            bundle.putString(Constants5140.intent_ac_deviceName, deviceName)
            bundle.putString(Constants5140.intent_ac_bleAddress, bleAddress)
            bundle.putString(Constants5140.intent_ac_key_wifi_mac, wifiMac)
            bundle.putString(Constants5140.intent_ac_soft_version, softVersion)
            bundle.putString(Constants5140.intent_ac_hard_version, hardVersion)
            bundle.putString(Constants5140.intent_ac_topic, topic)

            val shareDev = ShareController.queryIsShareDevice(sku, device)
            if (shareDev != null) {
                CommSharedSettingsAc.jump2Activity(
                    ac,
                    sku,
                    device,
                    topic,
                    bleAddress,
                    softVersion,
                    hardVersion,
                    vm = SharedSettingVM()
                )
                return
            }
            JumpUtil.jumpWithBundle(ac, Activity4Setting::class.java, bundle)
        }
    }

    override fun parseIntentParams(intent: Intent) {
        val goodsType =
            intent.getIntExtra(Constants5140.intent_ac_goodsType, GoodsType.GOODES_TYPE_NO_SUPPORT)
        val sku = intent.getStringExtra(Constants5140.intent_ac_sku)
        val device = intent.getStringExtra(Constants5140.intent_ac_device)
        val deviceName = intent.getStringExtra(Constants5140.intent_ac_deviceName)
        val bleAddress = intent.getStringExtra(Constants5140.intent_ac_bleAddress)
        val wifiMac = intent.getStringExtra(Constants5140.intent_ac_key_wifi_mac)
        softVersion = intent.getStringExtra(Constants5140.intent_ac_soft_version)!!
        hardVersion = intent.getStringExtra(Constants5140.intent_ac_hard_version)!!
        topic = intent.getStringExtra(Constants5140.intent_ac_topic)!!
        ext4St = Ext4St()
        ext4St.goodsType = goodsType
        ext4St.sku = sku!!
        ext4St.device = device!!
        ext4St.deviceName = deviceName!!
        ext4St.address = bleAddress!!
        ext4St.versionSoft = softVersion
        ext4St.versionHard = hardVersion
        ext4St.slV2 = slInfo
        ext4St.airQualityOnOff =
            THMemoryUtil.getInstance().isAirQsOpen(ext4St.sku + "_" + ext4St.device)
        fahOpen =
            TemUnitConfig.read().isTemUnitFah(sku, device, ext4St.versionHard, ext4St.versionSoft)
        iUnBindM = UnUnBindMV1(this, sku, device, false)
        bleOp = BleOp4Setting(ext4St, object : IBleOpResult {
            override fun bleUnable() {
                //蓝牙不可用，则尝试iot通讯
                initIot()
            }

            override fun bleDisconnect() {
                //蓝牙断开，也尝试iot通讯
                initIot()
                //自然断连的情况下，每隔一段时间就尝试去重连
                mHandler.removeCallbacks(reconnectBleRunnable)
                mHandler.postDelayed(reconnectBleRunnable, RECONNECT_INTERVAL_TIME)
            }

            override fun bleConnecting() {
                SafeLog.d("init_iot") { "bleConnecting......" }
                mHandler.removeCallbacks(reconnectBleRunnable)
                mHandler.removeCallbacks(mIotDisconnectRunnable)
                // updateStep(step_ble_scanning)
            }

            override fun infoOver() {
                updateUi()
            }

            override fun writeResult(result: Boolean, subCommandType: Byte) {
                SafeLog.d(TAG) { "更新设置页UI" }
                if (subCommandType == BleProtocol.VALUE_CLEAR_DATA) {
                    //删除数据
                    if (result) {
                        SafeLog.d("onEventClearData") { "------------>蓝牙删除全部数据" }
                        clearServiceData()
                        // 删除本地数据
                        lifecycleScope.launch(Dispatchers.IO) {
                            DbController.clearThpCache(sku, device, System.currentTimeMillis())
                        }
                    } else {
                        toast(R.string.h5072_reset_device_fail)
                    }
                } else {
                    if (!result) {
                        toast(R.string.h5072_update_settings_fail)
                    } else {
                        updateSetting2Service(subCommandType)
                    }
                    updateSettingUi(false)
                }
            }

            override fun realThUpdate(tem: Int, hum: Int) {
                updateRealTh(tem, hum)
            }
        })
        showWifiMac(wifiMac)
        updateVersion(softVersion, false)
        updateHardVersion(hardVersion)
        //设置图表数据缓存大小
        setThDataCacheMemory()
        if (!BleController.getInstance().isConnected) {
            SafeLog.d("init_iot") { "蓝牙未连接，使用iot连接读取数据" }
            initIot()
        }
    }

    /**
     * 设置图表数据占用存储大小
     */
    private fun setThDataCacheMemory() {
        mHandler.post {
            binding.common.tvCacheMemory.text = getThpDataCacheMemory(skuStr, device)
        }
    }

    override fun loadAllThData() {
        super.loadAllThData()
        loadAtdDialog.show()
        BleOp4ChartInfo.is_load_all_data = true
        sendEvent(Event4LoadAllData.LOAD_TH_DATA_TO_RESET_TIME, false)
        Activity4CharInfo.needRefreshChart = true
    }

    override fun clearDataByType(selectType: Int) {
        super.clearDataByType(selectType)
        Activity4CharInfo.needRefreshChart = true
        showLoading()
        clearByPeriodType4Thp(skuStr, device, selectType)
        mHandler.postDelayed(object : CaughtRunnable() {
            override fun runSafe() {
                setThDataCacheMemory()
                hideLoading()
            }
        }, 1000)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4LoadAldFinish(event: Event4LoadAllData) {
        if (event.step == Event4LoadAllData.LOAD_TH_DATA_FINISH) {
            loadAtdDialog.finish(event.result)
            mHandler.postDelayed(object : CaughtRunnable() {
                override fun runSafe() {
                    setThDataCacheMemory()
                    loadAtdDialog.hide()
                }
            }, ClearThDataUtils.REFRESH_TH_WAIT_TIME)
        }
    }

    /**
     * 蓝牙断连后，每隔一段时间就去尝试重连
     */
    private val reconnectBleRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            //重现连接设备
            bleOp?.connectBle()
        }
    }

    /**
     * 更新设置页面
     */
    fun updateUi() {
        //更新ui
        updateStep(step_ble_suc)
        updateSettingUi(true)
        //更新预警范围
        WarnConfig.read().updateWarningRange(makeWarnRange(), false)

        //上报设备信息
        toSyncSettingInfo()
        //检测升级
        toCheckVersion()
    }

    override fun updateStep(step: Int) {
        if (step != step_ble_suc) {
            checkVersion = null
        }
        super.updateStep(step)
    }

    private fun updateSetting2Service(subCommandType: Byte) {
        var needUpdateWarnConfig = false
        when (subCommandType) {
            BleProtocol.VALUE_PM25_WARNING -> {
                needUpdateWarnConfig = true
                //更新pm2.5预警信息
                val requestPm25 = RequestPm25Warning(
                    transactions.createTransaction(),
                    ext4St.sku,
                    ext4St.device,
                    ext4St.co2AlarmMin,
                    ext4St.co2AlarmMax,
                    ext4St.co2AlarmSwitch,
                )
                Cache.get(INet::class.java).updatePm25Warning(requestPm25)
                    .enqueue(IHCallBack(requestPm25))
            }

            BleProtocol.VALUE_TEM_CALI, BleProtocol.VALUE_TEM_WARNING -> {
                needUpdateWarnConfig = true
                //更新温度预警信息
                val requestTem = RequestTem(
                    transactions.createTransaction(),
                    ext4St.sku,
                    ext4St.device,
                    ext4St.temMin,
                    ext4St.temMax,
                    ext4St.temWarning,
                    ext4St.temCali
                )
                Cache.get(IThNet::class.java).updateDeviceTemConfig(requestTem)
                    .enqueue(IHCallBack(requestTem))
            }

            BleProtocol.VALUE_HUM_CALI, BleProtocol.VALUE_HUM_WARNING -> {
                needUpdateWarnConfig = true
                //更新湿度预警信息
                val requestHum = RequestHum(
                    transactions.createTransaction(),
                    ext4St.sku,
                    ext4St.device,
                    ext4St.humMin,
                    ext4St.humMax,
                    ext4St.humWarning,
                    ext4St.humCali
                )
                Cache.get(IThNet::class.java).updateDeviceHumConfig(requestHum)
                    .enqueue(IHCallBack(requestHum))
            }
        }
        if (needUpdateWarnConfig) {
            //更新预警范围
            WarnConfig.read().updateWarningRange(makeWarnRange(), true)
        }
    }

    override fun onResume() {
        super.onResume()
        (getInstance().heart as H5140HeartSender).setNeedParseHeartPackage(true)
        if (needNotifyUserOpBluetooth) {
            needNotifyUserOpBluetooth = false
            //升级成功后，提示用户可能需要开关一下蓝牙进行重新连接
            HintDialog1.showHintDialog1(
                this,
                ResUtil.getString(R.string.h5072_notify_user_operation_ble),
                ResUtil.getString(R.string.done)
            )
        }
        if (lifeCyclePause) {
            //重新回到设置页，若蓝牙未连接，则尝试连接蓝牙
            if (isMustPermissionGranted && !BleController.getInstance().isConnected) {
                bleOp!!.connectBle()
            }
        }
        lifeCyclePause = false
        getSettings()
    }

    override fun onPause() {
        super.onPause()
        if (!OtaOpV3.getInstance().inOta()) {
            (getInstance().heart as H5140HeartSender).setNeedParseHeartPackage(false)
        }
        lifeCyclePause = true
        //存在iot指令未发送则立即发送并
        if (sendIotCmds.isNotEmpty()) {
            sendIotCmd(sendIotCmds[sendIotCmds.lastIndex], System.currentTimeMillis())
            sendIotCmds.clear()
            mHandler.removeCallbacks(sendIotCmdRunnable)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onHeartEvent(event: EventHeart) {
        val result = event.isResult
        if (result) {
            val tem = event.tem
            val hum = event.hum
            if (LogInfra.openLog()) {
                LogInfra.Log.i(
                    this.TAG,
                    "onHeartEvent() tem = $tem ; hum = $hum ; pm2.5 = ${event.pm25}"
                )
            }
            updateRealTh(tem, hum)
            updateTemWarnUI()
        }
    }

    private var temp100Cache = 0
    private var hum100Cache = 0

    private fun updateRealTh(tem: Int, hum: Int) {
        if (isDestroy) {
            return
        }
        this.temp100Cache = tem
        this.hum100Cache = hum

        realTem = NumberUtil.getTemValue(isFahOpen, tem, 0)
        realHum = NumberUtil.toFloatValueBy100(hum)
        //更新校准控件
        val temCali = NumberUtil.getValidTemCali(ext4St.temCali, isFahOpen)
        val humCali = NumberUtil.getValidHumCali(ext4St.humCali)
        binding.calibration.updateCalibration(realTem, temCali, realHum, humCali, hum > 0)
    }

    private fun clearServiceData() {
        showLoading()
        val requestClearData =
            RequestClearData(transactions.createTransaction(), ext4St.sku, ext4St.device)
        Cache.get(IThNet::class.java).clearData(requestClearData)
            .enqueue(IHCallBack(requestClearData))
    }

    private fun toSyncSettingInfo() {
        H5140C02WarnConfig.getCo2WarnInfo(ext4St.sku, ext4St.device).let {
            ext4St.co2AlarmMin = -200
            ext4St.co2AlarmMax = it.co2Max
            ext4St.co2AlarmSwitch = it.openWarn
        }
        val requestSetting = RequestSetting(transactions.createTransaction(), ext4St)
        Cache.get(INet::class.java).updateSetting(requestSetting)
            .enqueue(IHCallBack(requestSetting))
    }

    private fun toCheckVersion() {
        val request = DeviceUpdateRequest(
            transactions.createTransaction(),
            ext4St.versionSoft,
            ext4St.versionHard,
            ext4St.sku,
            ext4St.device,
            ""
        )
        Cache.get(IUpdateNet::class.java).checkUpdate(request).enqueue(IHCallBack(request))
    }

    private fun makeWarnRange(): WarnRange {
        val warnRange = WarnRange()
        warnRange.sku = ext4St.sku
        warnRange.device = ext4St.device
        warnRange.bleAddress = ext4St.address
        warnRange.co2Min = ext4St.co2AlarmMin
        warnRange.co2Max = ext4St.co2AlarmMax
        warnRange.co2Warning = ext4St.co2AlarmSwitch
        warnRange.temMin = ext4St.temMin
        warnRange.temMax = ext4St.temMax
        warnRange.temWarning = ext4St.temWarning
        warnRange.humMin = ext4St.humMin
        warnRange.humMax = ext4St.humMax
        warnRange.humWarning = ext4St.humWarning
        warnRange.temCali = ext4St.temCali
        warnRange.humCali = ext4St.humCali
        return warnRange
    }

    private fun updateSettingUi(updateDeviceNameUi: Boolean) {
        if (isDestroy) {
            return
        }
        if (updateDeviceNameUi) {
            //更新设备名
            updateDeviceNameUi()
        }
        //更新pm2.5预警
        val co2AlarmRange = intArrayOf(
            ext4St.co2AlarmMin,
            ext4St.co2AlarmMax
        )
        //binding.taqsvAirQaSwitch.updateAqRemind(ext4St.airQualityOnOff)
        binding.tarvPm25AlarmRange.updateCo2Alarm(co2AlarmRange, ext4St.co2AlarmSwitch)
        updateAlarmInterval()
        //更新温度预警
        updateTemWarnUI()
        //更新湿度预警
        val humAlarmRange = intArrayOf(
            NumberUtil.getValueUpward(ext4St.humMin, true),
            NumberUtil.getValueUpward(ext4St.humMax, false)
        )
        binding.humAlarmRange.updateHumAlram(humAlarmRange, ext4St.humWarning)
        updateTuShow(ext4St.tuType)// 更新温度单位
        binding.tosvOtherSetV2.updateSdShow(ext4St.sdType)
        binding.tosvOtherSetV2.updateTfShow(ext4St.tfType, false)
        binding.tosvOtherSetV2.updateSlInfo(ext4St.slV2)
        binding.thWarnSettingViewNew.initDeviceInfo(skuStr, device, ext4St)
        //更新校准
        val temCali = NumberUtil.getValidTemCali(ext4St.temCali, isFahOpen)
        if (LogInfra.openLog()) {
            LogInfra.Log.i(
                this.TAG,
                "updateSettingUi() deviceSettings.temCali = " + ext4St.temCali + " ; temCali = " + temCali
            )
        }
        val humCali = NumberUtil.toFloatValueBy100(ext4St.humCali)
        binding.calibration.updateCalibration(realTem, temCali, realHum, humCali, realHum != 0f)
        //更新版本
        if (ext4St.versionSoft.isNotEmpty()) {
            updateVersion(ext4St.versionSoft, hadNewVersion())
        }
        if (ext4St.versionHard.isNotEmpty()) {
            updateHardVersion(ext4St.versionHard)
        }
        val isAirQsOpen = THMemoryUtil.getInstance().isAirQsOpen(ext4St.sku + "_" + ext4St.device)
        binding.ivCo2DensitySwitch.isSelected = isAirQsOpen
        updateDNDUi()
    }

    private fun updateDNDUi() {
        if (ext4St.dndIsOpen) {
            binding.tvNotDisturbModeTime.visible()
        } else {
            binding.tvNotDisturbModeTime.gone()
        }
        val text = getTimeStr(ext4St.dndStartHour) + ":" + getTimeStr(ext4St.dndStartMinute) + "-" +
                getTimeStr(ext4St.dndEndHour) + ":" + getTimeStr(ext4St.dndEndMinute)
        binding.tvNotDisturbModeTime.text = text
        binding.ivNotDisturbMode.isSelected = ext4St.dndIsOpen
    }

    /**
     * 更新报警间隔
     */
    private fun updateAlarmInterval() {
        H5140C02WarnConfig.getCo2WarnInfo(ext4St.sku, ext4St.device).let {
            binding.tvAlarmIntervalTime.text = ResUtil.getStringFormat(R.string.minute_pattern, it.alarmInterval)
        }
    }

    private fun updateTemWarnUI() {
        binding.temAlarmRange.setTemRange(isFahOpen, temRange)
        val temAlarmRange = IntArray(2)
        if (isFahOpen) {
            temAlarmRange[0] = ThUtil.temRangeCel2Fah4Showing(ext4St.temMin)
            temAlarmRange[1] = ThUtil.temRangeCel2Fah4Showing(ext4St.temMax)
        } else {
            temAlarmRange[0] = ThUtil.temRangeCel2Cel4Showing(ext4St.temMin)
            temAlarmRange[1] = ThUtil.temRangeCel2Cel4Showing(ext4St.temMax)
        }
        binding.temAlarmRange.updateTemAlarm(temAlarmRange, ext4St.temWarning)
    }

    override fun onErrorResponse(response: ErrorResponse) {
        super.onErrorResponse(response)
        if (response.request is RequestTem || response.request is RequestHum) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(this.TAG, "RequestSetting onErrorResponse")
            }
            saveInfoToLocal()
        }
    }

    override fun updateVersion(versionSoft: String, hadNewVersion: Boolean) {
        super.updateVersion(versionSoft, hadNewVersion)
        binding.common.versionShowing.visibility =
            if (hadNewVersion) View.INVISIBLE else View.VISIBLE
    }

    /**
     * 没网或者调接口失败就把信息保存到本地，同步到卡片界面
     */
    private fun saveInfoToLocal() {
        val changeDevice = DeviceRoomOrderConfig.read()
            .changeDevice(ext4St.sku, ext4St.device, JsonUtil.toJson(ext4St))
        if (LogInfra.openLog()) {
            LogInfra.Log.i(this.TAG, "saveInfoToLocal = $changeDevice")
        }
    }

    override fun getDevice(): String {
        return ext4St.device
    }

    override fun sendTempUnit(tuType: Int) {
        val commTuType = CommandTemUnit(tuType)
        toSendController(commTuType)
    }

    override fun sendSoundLevel(sw: Boolean, level: Int) {
        val commSoundLevel = CommandSoundLevel(sw, level)
        toSendController(commSoundLevel)
        showLoading()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun hideLoadingEvent(hindLoading: HindLoadingEvent) {
        hideLoading()
    }

    override fun sendCo2GradeLevel(minLevel: Int, maxLevel: Int) {
        val command = CommandCo2SetGrade(minLevel, maxLevel)
        toSendController(command)
        Activity4CharInfo.needRefreshChart = true
    }

    override fun sendManualCalibration() {
        val command = CommandCo2ManualCalibration(true)
        toSendController(command)
    }

    override fun sendScreenDisplay(sdType: Int) {
        val commSdType = CommandDisplay(sdType)
        toSendController(commSdType)
    }

    override fun sendTimeFormat(tfType: Int) {
        val commTfType = CommandTimeFormat(tfType)
        toSendController(commTfType)
    }

    override fun getExt4Str(): Ext4St {
        return ext4St
    }

    override fun sendScreenLightness(slType: Int) {
//        ext4St.slLevel = slType
//        val commSlType = CommandLightness(slType)
//        toSendController(commSlType)
    }

    override fun showCo2LevelSettingDialog() {
        val dialog = Co2LevelSettingDialog(this, ext4St.co2GradeMin, ext4St.co2GradeMax)
        dialog.setCanceledOnTouchOutside(false)
        dialog.setOnSetLevelListener(object : OnSetLevelListener {
            override fun onSetLevel(minRange: Int, maxRange: Int) {
                sendCo2GradeLevel(minRange, maxRange)
            }
        })
        dialog.show()
        dialog.setRange(ext4St.co2GradeMin, ext4St.co2GradeMax)
    }

    override fun sendScreenLightness(slInfo: EventLightnessV2.LightnessV2) {
        ext4St.slV2 = slInfo
        val commSlInfo = CommandLightnessV2(slInfo)
        toSendController(commSlInfo)
    }

    override fun sendHumCali(humCali: Float) {
        val humCali100 = NumberUtil.toIntValueBy100(humCali)
        val controllerHumCali = CommandHumCali(humCali100)
        toSendController(controllerHumCali)
    }

    override fun sendTemCali(fahOpen: Boolean, temCali: Float) {
        val temCali100 = NumberUtil.getValidTemCali(temCali, fahOpen)
        SafeLog.i("test_temp_cali", "sendTemCali() temCali = $temCali ; temCali100 = $temCali100")
        val controllerTemCali = CommandTemCali(temCali100)
        toSendController(controllerTemCali)
    }

    override fun getHumCaliRange(): FloatArray {
        return floatArrayOf(Constants5140.HUM_CALI_MIN, Constants5140.HUM_CALI_MAX)
    }

    override fun getTemCaliRange(): FloatArray {
        return if (isFahOpen) floatArrayOf(
            Constants5140.TEM_CALI_MIN_FAH,
            Constants5140.TEM_CALI_MAX_FAH
        ) else floatArrayOf(Constants5140.TEM_CALI_MIN_CEL, Constants5140.TEM_CALI_MAX_CEL)
    }

    override fun sendPm25Alarm(minAlarmPm25: Int, maxAlarmPm25: Int, alarmOn: Boolean) {
        val pm25Warning = CommandCo2Warning(alarmOn, minAlarmPm25, maxAlarmPm25, ext4St.alarmInterval)
        toSendController(pm25Warning)
        Activity4CharInfo.needRefreshChart = true
    }

    override fun sendAlarmInterval(alarmInterval: Int) {
        val alarmIntervalCmd = CommandCo2Warning(ext4St.co2AlarmSwitch, ext4St.co2AlarmMin, ext4St.co2AlarmMax, alarmInterval)
        toSendController(alarmIntervalCmd)
        Activity4CharInfo.needRefreshChart = true
    }

    override fun sendAirQs(open: Boolean) {
        val requestAirQs = RequestAirQs(
            transactions.createTransaction(),
            ext4St.sku,
            ext4St.device,
            open,
        )
        Cache.get(INet::class.java).updateAirQsSetting(requestAirQs).enqueue(IHCallBack(requestAirQs))
        val control = CommandAirNotify(open)
        toSendController(control)
    }

    override fun sendDNDMode(isOpen: Boolean, startHour: Int, startMinute: Int, endHour: Int, endMinute: Int) {
        val cmd = CommandNoRemindMode(isOpen, startHour, startMinute, endHour, endMinute)
        toSendController(cmd)
        Activity4CharInfo.needRefreshChart = true
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponseAirQs(response: ResponseAirQs) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(this.TAG, "onResponseAirQs()")
        }
        ext4St.airQualityOnOff = response.getRequest().airQualityOnOff == 1
        THMemoryUtil.getInstance().setAirQsOpen(ext4St.sku + "_" + ext4St.device, ext4St.airQualityOnOff)
        updateSettingUi(false)
    }

    override fun getCo25Range(): IntArray {
        return intArrayOf(Constants5140.CO2_MIN_VALUE, Constants5140.CO2_MAX_VALUE)
    }

    override fun getAlarmInterval(): Int {
        return ext4St.alarmInterval
    }

    override fun getSlInfo(): EventLightnessV2.LightnessV2 {
        if (ext4St.slV2 == null) {
            ext4St.slV2 = EventLightnessV2.LightnessV2()
        }
        return ext4St.slV2!!
    }

    override fun isFahOpen(): Boolean {
        return TemUnitConfig.read().isTemUnitFah(skuStr, device)
    }

    override fun sendHumAlarm(minAlarmHum: Int, maxAlarmHum: Int, alarmOn: Boolean) {
        val controllerHumWarning = CommandHumWarning(alarmOn, minAlarmHum * 100, maxAlarmHum * 100)
        toSendController(controllerHumWarning)
    }

    override fun getHumRange(): IntArray {
        return intArrayOf(Constants5140.HUM_MIN_VALUE, Constants5140.HUM_MAX_VALUE)
    }

    override fun sendTemAlarm(minAlarmTem: Int, maxAlarmTem: Int, alarmOn: Boolean) {
        val minTem: Int
        val maxTem: Int
        if (isFahOpen) {
            minTem = ThUtil.temRangeFah2Cel4Setting(minAlarmTem)
            maxTem = ThUtil.temRangeFah2Cel4Setting(maxAlarmTem)
        } else {
            minTem = minAlarmTem * 100
            maxTem = maxAlarmTem * 100
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(this.TAG, "sendTemAlarm() minTem = $minTem ; maxTem = $maxTem")
        }
        val controllerTemWarning = CommandTemWarning(alarmOn, minTem, maxTem)
        toSendController(controllerTemWarning)
    }

    override fun getTemRange(): IntArray {
        return if (isFahOpen)
            intArrayOf(
                TemUtil.getTemF(Constant4L5.TEM_MIN_VALUE_H5140 * 1.0f),
                TemUtil.getTemF(Constant4L5.TEM_MAX_VALUE_H5140 * 1.0f)
            ) else
            intArrayOf(Constant4L5.TEM_MIN_VALUE_H5140, Constant4L5.TEM_MAX_VALUE_H5140)
    }

    override fun getSkuStr(): String {
        return ext4St.sku
    }

    override fun toSaveDeviceName(newDeviceName: String) {
        showLoading()
        val requestName =
            RequestName(transactions.createTransaction(), ext4St.sku, ext4St.device, newDeviceName)
        Cache.get(IThNet::class.java).updateDeviceName(requestName).enqueue(IHCallBack(requestName))
    }

    override fun getDeviceName(): String {
        return ext4St.deviceName
    }

    override fun toJump2ChangeWifi() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(this.TAG, "toJump2ChangeWifi()")
        }
        jump2WifiChooseAc4ChangeWifi(
            this,
            false,
            ext4St.goodsType,
            ext4St.sku,
            ext4St.address,
            Constant.wifi_input_limit_ssid_new,
            Constant.wifi_input_limit_password_new
        )
    }

    override fun getDeleteDataHintStr(): String {
        return ResUtil.getString(R.string.h5072_dialog_reset_des)
    }

    override fun toUpdateAc() {
        checkVersion!!.curVersionSoft = ext4St.versionSoft
        Activity4DeviceUpdate.jump2OtaUpdateAcV3(
            this,
            ext4St.sku,
            ext4St.deviceName,
            ext4St.versionSoft,
            ThemeM.getDefSkuRes(ext4St.sku),
            checkVersion!!
        )
    }

    override fun hadNewVersion(): Boolean {
        return checkVersion != null
    }

    override fun toDeleteData() {
        Activity4CharInfo.needRefreshChart = true
        val controllerClearData = CommandClearData()
        toSendController(controllerClearData)
        Activity4CharInfo.deleteAllData = true
    }

    override fun toDeleteDevice() {
        //请求接口的加载loading和载入数据的加载loading非同一个，显示一个即可
        if (binding.searching.visibility == View.VISIBLE) {
            binding.searching.visibility = View.GONE
        }
        H5140C02WarnConfig.updateCo2WarnInfo(ext4St.sku, ext4St.device, Co2WarnInfo())
        H5140C02WarnConfig.updateCo2LevelInfo(ext4St.sku, ext4St.device, Co2LevelInfo())
        //H5106删除联动，连同删除设备上存储的triggers
        if (iUnBindM is UnUnBindMV1) {
            (iUnBindM as UnUnBindMV1).showLoading()
            val saveTriggersManager = SaveTriggersManager()
            saveTriggersManager.clearTriggers(
                ext4St.sku,
                ext4St.device,
                topic,
                object : SaveTriggersManager.OnSaveTriggersListener {
                    override fun onSaveTriggers(saveSuccess: Boolean) {
                        (iUnBindM as UnUnBindMV1).needLoading = false
                        iUnBindM.unbindDevice()
                        saveTriggersManager.release()
                    }
                })
        }
    }

    override fun getDeleteDeviceHintStr(): String {
        return ResUtil.getString(R.string.dialog_unbind_label)
    }

    override fun toRecycler() {
        bleOp!!.onDestroy()
        if (iotOp != null) {
            iotOp!!.destroy()
        }
        HintDialog1.hideDialog()
        if (clearDataSuc) {
            clearDataSuc = false
            EventAfterClearDataSuc.sendEventClearData()
        }
    }

    override fun toConnectBle() {
        bleOp!!.connectBle()
    }

    private var sendCo2Cmd: String = ""

    /**
     * 送送控制指令（ble+iot）
     */
    private fun toSendController(controller: AbsSingleController) {
        val connected = BleController.getInstance().isConnected
        if (connected) {
            SafeLog.d("ble_send") { "使用蓝牙发送" }
            binding.calibration.setIotCommunication(false)
            bleOp!!.sendController(controller)
        } else {
            SafeLog.d("ble_send") { "iot" }
            if (!isTotCommunication) {
                return
            }
            SafeLog.d("ble_send") { "使用iot发送" }
            //!!是否为ble蓝牙控制，iot控制时需有loading弹窗
            showLoading()
            binding.calibration.setIotCommunication(true)
            iotSendOver = false
            //发送iot写入指令，则将iot心跳向后顺延
            startIotHeart()
            //iot处于通讯中，则通过iot发送指令
            //对于快速点击的指令需额外处理：同一指令2s内的只发送最后一次的设置
            val currentTimeMills = System.currentTimeMillis()
            val writeCmd = CmdPtReal(controller, currentTimeMills)
            sendIotCmds.add(writeCmd)
            if (sendIotCmds.size > 1) {
                val lastIotCmd = sendIotCmds[sendIotCmds.lastIndex - 1]
                val isSameIotCmdType = lastIotCmd.controller.commandType == controller.commandType
                //当前指令类型和上一条类型不一致，则立即发送上一次的指令
                if (!isSameIotCmdType) {
                    sendIotCmd(lastIotCmd, lastIotCmd.sendTimeMills)
                }
            }
            mHandler.removeCallbacks(sendIotCmdRunnable)
            mHandler.postDelayed(sendIotCmdRunnable, IOT_CMD_INTERVAL_TIME)
        }
    }

    private fun sendIotCmd(writeCmd: CmdPtReal, sendMills: Long) {
        //将指令集收集起来,快速连续发送可能只会回复最后一条
        iotOp?.writeCmd(writeCmd)
        //重置iot相关标志位
        lastIotCmdTimeMills = sendMills
        lastIotCmdType = writeCmd.controller.commandType
    }

    private val sendIotCmdRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            if (sendIotCmds.isEmpty()) {
                return
            }
            val lastWriteCmd = sendIotCmds[sendIotCmds.lastIndex]
            sendIotCmd(lastWriteCmd, lastWriteCmd.sendTimeMills)
            //发送了最后一条，标志着发送结束
            sendIotCmds.clear()
            iotSendOver = true
        }
    }

    /**
     * 温度单位切换
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTempUnit(event: EventTempUnit) {
        synchronized(this) {
            val result = event.isResult
            if (result) {
                val tuType = event.temUnit
                ext4St.tuType = event.temUnit
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventTempUnit() temUnit = $tuType")
                }
                val isFahOpen = tuType == 1
                if (this.fahOpen != isFahOpen) {
                    //有温度单位相关的视图需更新ui
                    this.fahOpen = isFahOpen
                    binding.temAlarmRange.setTemRange(fahOpen, temRange)
                    binding.calibration.setRange(fahOpen, temCaliRange, humCaliRange)
                    updateRealTh(temp100Cache, hum100Cache)
                    updateTemWarnUI()
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponseDeviceName(response: ResponseName) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(this.TAG, "onResponseDeviceName()")
        }
        mHandler.postDelayed({
            ext4St.deviceName = response.getRequest().deviceName
            updateDeviceNameUi()
            EventNameChange.sendEventNameChange(ext4St.deviceName)
            hideLoading()
        }, 500)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponseDeviceUpdate(response: DeviceUpdateResponse) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        val checkVersion = response.checkVersion
        val needUpdate = checkVersion.isNeedUpdate
        if (needUpdate) {
            this.checkVersion = checkVersion
        } else {
            this.checkVersion = null
        }
        EventNewVersion.sendEventNewVersion(hadNewVersion())
        updateVersion(ext4St.versionSoft, hadNewVersion())
        //弹升级提示窗
        if (this.checkVersion != null) {
            showUpdateHintDialog(ext4St.device, checkVersion)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onUpdateSucEvent(event: UpdateSucEvent?) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(this.TAG, "onUpdateSucEvent()")
        }
        needNotifyUserOpBluetooth = true
        checkVersion = null
        EventNewVersion.sendEventNewVersion(hadNewVersion())
        updateVersion(ext4St.versionSoft, hadNewVersion())
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onResponseClearData(response: ResponseClearData) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        val request = response.getRequest()
        val sku = request.sku
        val device = request.device
        clearDataSuc = true
        //统计删除数据次数
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.delete_data, sku)
        //清除加载任务
        TaskLinkConfig.read(ext4St.sku, ext4St.device).clear()
        //清除本地数据库
        DbController.delete(sku, device, false)
        sendEvent(Event4HasClearCache.DELETE_ALL)
        setThDataCacheMemory()
        //更新拉取数据本地存储
        DataConfig.read()
            .updateLastDataClearTime(ext4St.sku, ext4St.device, System.currentTimeMillis())
        //延时2秒，等待设备删除数据
        SystemClock.sleep((2 * 1000).toLong())
        runOnUiThread(object : CaughtRunnable() {
            override fun runSafe() {
                hideLoading()
                toast(response.message)
            }
        })
    }

    private fun beUnbindSuc(msg: String) {
        toast(msg)
        unRegisterEventBus()
        //统计删除设备
        AnalyticsRecorder.getInstance()
            .recordTimes(EventKey.use_count, ParamKey.delete_device, ext4St.sku)
        //删除数据加载配置
        TaskLinkConfig.read(ext4St.sku, ext4St.deviceName).clear()
        DataConfig.read().removeLastClearTime(ext4St.sku, ext4St.device)
        //回退到主界面
        EventTabDefault.sendEventTabDefault()
        val bundle = Bundle()
        bundle.putString(Constant.intent_ac_key_sku, ext4St.sku)
        bundle.putString(Constant.intent_ac_key_unbind, ext4St.device)
        val config = Base2homeConfig.getConfig()
        val functionAcClass = config.mainAcClass
        JumpUtil.jumpWithBundle(this, functionAcClass, true, bundle)
        //断开蓝牙
        BleController.getInstance().toBtClose()
    }

    override fun onRequestError(error: ErrorResponse) {
        val request = error.request
        var needShowError = false
        if (request is RequestName) {
            hideLoading()
            needShowError = true
        }
        if (needShowError) {
            super.onRequestError(error)
        }
    }

    override fun onUnbindSuc(sku: String, device: String, msg: String) {
        beUnbindSuc(msg)
    }

    override fun onDestroy() {
        iotOp?.destroy()
        mHandler.removeCallbacksAndMessages(null)
        super.onDestroy()
    }

    //=============================================iot相关===========================================
    private var iotOp: IotOpManager? = null
    private var hasStartIotHeart = false

    /**
     * iot通讯是否处于活跃中（即心跳是否存在）
     * 备注：优先进行ble通讯，在ble通讯受阻的情况下才进行iot通讯，ble下有wifi也会维持iot心跳，为了ble断开后，拉起iot通讯
     */
    private var isTotCommunication = false

    /**
     *iot相关初始化
     */
    private fun initIot() {
        SafeLog.d("init_iot") { "initIot-------->" }
        if (!NetworkUtils.isNetConnected(this)) {
            getIotInfoFail()
            return
        }
        mHandler.removeCallbacks(reconnectIotRunnable)
        mHandler.removeCallbacks(iotHeartRunnable)
        this.isTotCommunication = true
        if (iotOp == null) {
            iotOp = IotOpManager()
            iotOp!!.setOpResult(iotOpResult)
        }
        if (topic.isEmpty()) {
            DeviceM.getInstance.getDeviceTopic(ext4St.sku, ext4St.device)
        } else {
            startIotComm()
        }
    }

    /**
     * iot获取设备信息失败，展示对应ui
     */
    private fun getIotInfoFail() {
        isIotConnect = false
        //在蓝牙未连接的情况下才视为断开
        if (!BleController.getInstance().isConnected) {
            updateStep(step_ble_disconnect)
            //过一段时间后尝试重连
            mHandler.removeCallbacks(reconnectIotRunnable)
            mHandler.postDelayed(reconnectIotRunnable, RECONNECT_INTERVAL_TIME)
        }
    }

    private val reconnectIotRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            initIot()
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onDeviceTopicResponse(topicEvent: EventDeviceTopic) {
        if (ext4St.sku == topicEvent.sku && ext4St.device == topicEvent.device) {
            topic = topicEvent.topic
        }
        //开始iot心跳
        if (!TextUtils.isEmpty(topic)) {
            startIotComm()
            //获取主题失败
        } else {
            getIotInfoFail()
        }
    }

    /**
     *开始iot通讯
     */
    private fun startIotComm() {
        try {
            iotOp!!.stopIotStep()
            SafeLog.d("init_iot") { "开始status指令-------->" }
            iotOp!!.beOpComm(ext4St.sku, ext4St.device, topic)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private val iotHeartRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            SafeLog.d("init_iot") { "发送了status指令" }
            iotOp?.readCmd(CmdStatus())
            //发送了status指令后一定时间内没有回复就认为iot掉线
            mHandler.removeCallbacks(mIotDisconnectRunnable)
            mHandler.postDelayed(mIotDisconnectRunnable, Constants5140.IOT_RESPONSE_TIME_OUT)
            //下一次心跳
            startIotHeart()
        }
    }

    private val mIotDisconnectRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            hideLoading()
            SafeLog.d("init_iot") { "发送了status指令，但是10秒内没有回复，认为iot掉线" }
            isIotConnect = false
            updateStep(step_ble_disconnect)
        }
    }

    /**
     * 通过iot心跳读到设备信息后，更新设置页信息
     */
    private fun setExt(deviceInfo: IotHeartInfo) {
        ext4St.temWarning = deviceInfo.temWarning
        ext4St.temCali = deviceInfo.temCali
        ext4St.temMax = deviceInfo.temMax
        ext4St.temMin = deviceInfo.temMin
        ext4St.humWarning = deviceInfo.humWarning
        ext4St.humCali = deviceInfo.humCali
        ext4St.humMax = deviceInfo.humMax
        ext4St.humMin = deviceInfo.humMin
        ext4St.co2AlarmSwitch = deviceInfo.co2Warning
        ext4St.alarmInterval = deviceInfo.alarmInterval
        ext4St.co2AlarmMax = deviceInfo.co2Max
        ext4St.co2AlarmMin = deviceInfo.co2Min
        ext4St.sdType = deviceInfo.sdType
        ext4St.tfType = deviceInfo.tfType
        ext4St.slLevel = deviceInfo.slLevel
        ext4St.tuType = deviceInfo.tuType
        ext4St.slV2 = deviceInfo.slV2
        ext4St.soundLevel = deviceInfo.soundLevel
        ext4St.soundSwitch = deviceInfo.soundSwitch
        ext4St.co2GradeMin = deviceInfo.co2GradeMin
        ext4St.co2GradeMax = deviceInfo.co2GradeMax
        ext4St.co2NotifySwitch = deviceInfo.co2NotifySwitch
        ext4St.dndIsOpen = deviceInfo.dndIsOpen
        ext4St.dndStartHour = deviceInfo.dndStartHour
        ext4St.dndStartMinute = deviceInfo.dndStartMinute
        ext4St.dndEndHour = deviceInfo.dndEndHour
        ext4St.dndEndMinute = deviceInfo.dndEndMinute
        SafeLog.i(TAG, "cmdOnline-->setExt(),ext4St=${ext4St.toJson()}")

        //相关更新设置
        updateStep(step_ble_suc)
        updateSettingUi(false)
        WarnConfig.read().updateWarningRange(makeWarnRange(), false)
        updateRealTh(deviceInfo.tem, deviceInfo.hum)
        updateTemWarnUI()
    }

    /**
     * iot操作回调接口实现
     */
    private val iotOpResult = object : IIotOpResultV1 {
        override fun noConnectIot() {
            LogInfra.Log.i("iotOpResult", "noConnectIot")
            iotOnline = false
            getIotInfoFail()
        }

        override fun cmdWriteFail(overtime: Boolean, absCmd: AbsCmd?) {
            LogInfra.Log.i("iotOpResult", "cmdWriteFail,${absCmd?.toJson()}")
            hideLoading()
            parseIotCmdFail(absCmd)
        }

        override fun cmdWriteSuc(absCmd: AbsCmd?) {
            LogInfra.Log.i("iotOpResult", "cmdWriteSuc=${absCmd?.toJson()}")
        }

        override fun cmdRead(cmd: String?, cmdJsonStr: String?) {
            LogInfra.Log.i("iotOpResult", "cmdRead")
        }

        /**
         * CmdStatus回复时的回调方法
         */
        override fun cmdOnline(
            softVersion: String?,
            cmdJsonStr: String?,
            wifiSoftVersion: String?,
        ) {
            LogInfra.Log.i("iotOpResult", "cmdOnline")
            iotOnline = true
            val needParseStatus = !BleController.getInstance().isConnected && !TextUtils.isEmpty(cmdJsonStr) && iotSendOver
            if (!needParseStatus) {
                mHandler.removeCallbacks(mIotDisconnectRunnable)
                return
            }
            LogInfra.Log.i(TAG, "cmdOnline-->$cmdJsonStr")
            //解析op字段内参数
            val opJsonStr = IotParseUtils.getJsonObjectStr(cmdJsonStr!!, Cmd5140.parse_json_op)
            val op = JsonUtil.fromJson(opJsonStr, Command4H5106::class.java)
            if (op != null && !op.command.isNullOrEmpty()) {
                val deviceInfo = IotParseUtils.getDeviceInfo(
                    ext4St.sku,
                    ext4St.device,
                    op.command!!
                )
                if (deviceInfo != null) {
                    LogInfra.Log.i(TAG, "cmdOnline-->deviceInfo=${deviceInfo.toJson()}")
                    mHandler.removeCallbacks(mIotDisconnectRunnable)
                    //设置设备相关信息
                    if (binding.temAlarmRange.visibility != View.VISIBLE) {
                        updateStep(step_ble_scanning)
                        mHandler.postDelayed(object : CaughtRunnable() {
                            override fun runSafe() {
                                getIotData(deviceInfo)
                            }
                        }, 1000)
                    } else {
                        getIotData(deviceInfo)
                    }
                } else {
                    getIotInfoFail()
                }
            } else {
                getIotInfoFail()
            }
        }


        /**
         * CmdPtReal指令回复时的回调方法
         */
        override fun cmdWriteSuc4Pt(absCmd: AbsCmd, cmdJsonStr: String?) {
            iotOnline = true
            //重置心跳
            startIotHeart()
            LogInfra.Log.i(TAG, "cmdWriteSuc4Pt-->${cmdJsonStr}")
            hideLoading()
            if (absCmd is CmdPtReal) {
                val commandByteArray = IotParseUtils.getCommandByteArray(absCmd.getCommand())
                val deviceInfo = IotParseUtils.parseIotData(commandByteArray, ext4St.sku, ext4St.device)
                if (deviceInfo.tuType == 1) {
                    H5140TempUnitHelper.setTemUnit(skuStr, device, TemperatureUnitType.Fahrenheit)
                } else {
                    H5140TempUnitHelper.setTemUnit(skuStr, device, TemperatureUnitType.Celsius)
                }
                setExt(deviceInfo)
                commandByteArray?.forEach {
                    val commandType = it[1]
                    LogInfra.Log.i(TAG, "commandType-->${BleUtil.byteToHexString(commandType)}")
                    updateSetting2Service(commandType)
                    setPtResponse(it)
                }
            }
        }
    }

    private fun parseIotCmdFail(absCmd: AbsCmd?) {
        absCmd?.let {
            val absCmdBean = JsonUtil.fromJson(absCmd.toJson(), AbsCmdBean::class.java)
            val commands = IotParseUtils.getCommandByteArray(absCmdBean.command)
            commands?.forEach { command ->
                SafeLog.d("parseIotCmdFail") { "command=${BleUtil.bytesToHexString(command)}" }
                val commandType = command[1]
                if (commandType == BleProtocol.VALUE_CO2_GRADE || commandType == BleProtocol.VALUE_CO2_ADJUST) {
                    ToastUtil.getInstance().toast(com.govee.ui.R.string.h5140_set_fail)
                }
            }
        }
    }

    private fun setPtResponse(command: ByteArray) {
        SafeLog.d("setPtResponse") { "command=${BleUtil.bytesToHexString(command)}" }
        val commandType = command[1]
        if (commandType == BleProtocol.VALUE_CO2_GRADE || commandType == BleProtocol.VALUE_CO2_ADJUST) {
            ToastUtil.getInstance().toast(com.govee.ui.R.string.h5140_set_success)
        }
        if (commandType == BleProtocol.VALUE_CLEAR_DATA) {
            SafeLog.d("onEventClearData") { "------------>IoT删除全部数据" }
            clearServiceData()
            // 删除本地数据
            DbController.clearThpCache(ext4St.sku, device, 0)
        }
        if (commandType == BleProtocol.VALUE_PM25_WARNING) {
            H5140C02WarnConfig.updateCo2WarnInfo(
                ext4St.sku,
                ext4St.device,
                Co2WarnInfo(ext4St.co2AlarmMin, ext4St.co2AlarmMax, ext4St.co2AlarmSwitch, ext4St.alarmInterval)
            )
        }
        if (commandType == BleProtocol.VALUE_CO2_GRADE) {
            H5140C02WarnConfig.updateCo2LevelInfo(ext4St.sku, ext4St.device, Co2LevelInfo(ext4St.co2GradeMin, ext4St.co2GradeMax))
        }
        if (commandType == BleProtocol.VALUE_TEM_UNIT) {
            binding.temAlarmRange.setTemRange(isFahOpen, temRange)
            binding.calibration.setRange(isFahOpen, temCaliRange, humCaliRange)
            updateRealTh(temp100Cache, hum100Cache)
            updateTemWarnUI()
        }
    }

    /**
     * 处理获取到的iot数据
     */
    private fun getIotData(deviceInfo: IotHeartInfo) {
        SafeLog.i(TAG, "cmdOnline-->getIotData()")
        isIotConnect = true
        setExt(deviceInfo)
        //收到第一包status数据就意味着已经开启了iot通讯
        if (!hasStartIotHeart) {
            hasStartIotHeart = true
            //开始第一次iot心跳
            startIotHeart()
        }
    }

    /**
     * 当单iot连接时，间隔一定时间发送status指令作为心跳
     */
    private fun startIotHeart() {
        mHandler.removeCallbacks(iotHeartRunnable)
        mHandler.postDelayed(iotHeartRunnable, Constants5140.IOT_HEART_INTERVAL_TIME)
    }

    override fun getLocalSettings(): Request4Setting? {
        if (AccountConfig.read().isHadToken) {
            DeviceListConfig.read().getDeviceByKey(ext4St.sku, device)?.let {
                JsonUtil.fromJson(it.deviceExt.deviceSettings, Request4Setting::class.java)
                    ?.let { setting ->
                        return setting
                    }
            }
        } else {
            OfflineDeviceListConfig.read().getDevices(ext4St.sku, device)?.let {
                JsonUtil.fromJson(it.deviceExt.deviceSettings, Request4Setting::class.java)
                    ?.let { setting ->
                        return setting.apply {
                            //默认为打开
                            if (normalPushOnOff == null) {
                                normalPushOnOff = true
                            }
                            if (airQualityOnOff == null) {
                                airQualityOnOff = 1
                            }
                        }
                    }
            }
        }
        return null
    }

    private fun getSettings() {
        val request = Request4GetSettings(transactions.createTransaction(), skuStr, device)
        Cache.get(IThNet::class.java).getSettings(request).enqueue(IHCallBack(request))
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponse4UpdateSettings(response: Response4GetSettings) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        response.settings?.let {
            DeviceListConfig.read().updateLocalSettings(skuStr, device, it)
            //binding.thWarnSettingViewNew.initDeviceInfo(skuStr, device, ext4St)
        }
    }
}
