package com.govee.h5140.ble

import com.govee.base2home.Constant4L5
import com.govee.base2newth.AbsNotifyComm
import com.govee.base2newth.WifiConnectNotify
import com.govee.h5140.Constants5140
import java.util.UUID

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 5106设备ble读取设备主动上报的通讯器
 */
class ThpCommNotify : AbsNotifyComm() {

    companion object {
        private const val serviceUuidStr = "494e5445-4c4c-495f-524f-434b535f4857"

        /**
         * 读取温湿度数据
         */
        private const val characteristicUuidDataStr = "494e5445-4c4c-495f-524f-434b535f2014"

        /**
         * 通用指令
         */
        private const val characteristicUuidCommStr = "494e5445-4c4c-495f-524f-434b535f2011"
        private val serviceUUID = UUID.fromString(serviceUuidStr)
        private val characteristicUUID = UUID.fromString(characteristicUuidDataStr)
    }

    init {
        //加入数据读取完成解析器
        notifyParseList.add(DataOverNotifyParse(serviceUuidStr, characteristicUuidDataStr))
        //加入数据读取中解析器
        notifyParseList.add(DataQueryingNotifyParse(serviceUuidStr, characteristicUuidDataStr))
    }

    override fun parsePriority(): Int {
        return Constants5140.COMM_PARSE_PRIORITY_NOTIFY
    }

    override fun getWifiConnectNotify(): WifiConnectNotify {
        return H5106WifiConnectNotify(serviceUuidStr, characteristicUuidCommStr, Constant4L5.H5106)
    }

    override fun serviceUUID(): UUID {
        return serviceUUID
    }

    override fun characteristicUUID(): UUID {
        return characteristicUUID
    }

    override fun isSelfComm(
        serviceUuid: String,
        characteristicUuid: String,
        values: ByteArray
    ): Boolean {
        return if (values.size > 2) {
            values[0] == BleProtocol.NOTIFY_COMM
        } else false
    }
}