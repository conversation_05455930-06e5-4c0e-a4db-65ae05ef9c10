//package com.govee.h5140.linkage
//
//import com.govee.ui.R
//import androidx.appcompat.app.AppCompatActivity
//import com.govee.base2home.scenes.model.DeviceModel
//import com.govee.base2light.rhythm.RhyRule
//import com.govee.base2light.rhythm.event.EventSelectedChanged
//import com.govee.base2light.rhythm.model.AbsTriggerModel
//import com.govee.base2light.rhythm.ui.AbsEffectUI
//import com.govee.h5140.Constants5140
//import com.zhy.android.percent.support.PercentRelativeLayout
//
///**
// * <AUTHOR>
// * @date created on 2022/9/9
// * @description H5106联动的触发动作列表ui
// */
//class ThpLinkageEffectUI : AbsEffectUI() {
//
//    private var hasSelectedIndex = 0
//
//    override fun needUsePushTriggerUi(): Boolean = true
//
//    override fun layoutPushTrigger(
//        ac: AppCompatActivity,
//        parent: PercentRelativeLayout,
//        deviceModel: DeviceModel,
//        rhyRule: RhyRule?,
//        confirmListener: OnConfirmListener?
//    ) {
//        super.layoutPushTrigger(ac, parent, deviceModel, rhyRule, confirmListener)
//        //初始数据
//        val triggerModels =
//            arrayListOf(
//                AbsTriggerModel(
//                    R.mipmap.new_control_icon_mini_pm25,
//                    R.string.h5106_pm25_trigger_name,
//                    RhyRule.op_type_air_quality,
//                    Constants5140.POOR
//                ),
//                AbsTriggerModel(
//                    R.mipmap.new_rhythm_icon_mini_temperature_high,
//                    R.string.dbgw_th_linkage_tem_high,
//                    RhyRule.op_type_tem_high,
//                    2500
//                ),
//                AbsTriggerModel(
//                    R.mipmap.new_rhythm_icon_mini_temperature_low,
//                    R.string.dbgw_th_linkage_tem_low,
//                    RhyRule.op_type_tem_low,
//                    2500
//                ),
//                AbsTriggerModel(
//                    R.mipmap.new_rhythm_icon_mini_humidity_high,
//                    R.string.dbgw_th_linkage_hum_high,
//                    RhyRule.op_type_hum_high,
//                    8000
//                ),
//                AbsTriggerModel(
//                    R.mipmap.new_rhythm_icon_mini_humidity_low,
//                    R.string.dbgw_th_linkage_hum_low,
//                    RhyRule.op_type_hum_low,
//                    8000
//                ),
//            )
//        if (rhyRule != null) {
//            //in为前闭，后闭区间，until为前闭后开区间
//            for (i in 0..triggerModels.lastIndex) {
//                val thValue = rhyRule.getThValue(triggerModels[i].opCmdValue)
//                if (thValue != RhyRule.ThObj.USELESS_VALUE) {
//                    triggerModels[i].opValue = thValue
//                    hasSelectedIndex = i
//                    break
//                }
//            }
//        }
//        val rhythmTriggerListUI =
//            object : ThpTriggerListUI(ac, deviceModel, this@ThpLinkageEffectUI, triggerModels) {}
//        rhythmTriggerListUI.mSelectedPosition = hasSelectedIndex
//        rhythmTriggerListUI.run {
//            uiList.add(this)
//        }
//        autoAddViewAndApplyRule(parent, rhyRule)
//        EventSelectedChanged.sendEvent(true)
//    }
//}
