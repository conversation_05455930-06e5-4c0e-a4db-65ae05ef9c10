package com.govee.h5140.ble.command

import com.govee.base2newth.AbsSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventTimeFormat

/**
 * <AUTHOR>
 * @date created on 2022/06/09
 * @description 从5106设备读取/写入设备时间显示格式(12h/24h)的指令，并解析其回复
 */
class CommandTimeFormat : AbsSingleController {

    companion object {
        const val H12 = 0x00
        const val H24 = 0X01
    }

    private var tfType = H12

    /**
     * 写操作
     */
    constructor(tfType: Int) : super(true) {
        this.tfType = checkTfType(tfType)
    }

    private fun checkTfType(tuType: Int): Int {
        var realWay = tuType
        if (tuType != 0 && tuType != 1) {
            realWay = H12
        }
        return realWay
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray {
        return byteArrayOf(tfType.toByte())
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventTimeFormat.sendWriteResult(suc, commandType, proType, tfType)
        return true
    }

    override fun fail() {
        EventTimeFormat.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_TIME_FORMAT
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        EventTimeFormat.sendSuc(isWrite, commandType, proType, validBytes[0].toInt())
        return true
    }
}