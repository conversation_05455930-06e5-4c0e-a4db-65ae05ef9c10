package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import com.ihoment.base2app.infra.SafeLog
import org.greenrobot.eventbus.EventBus

class EventCo2ManualCalibration (result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, !write) {

    companion object {
        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte) {
            val event = EventCo2ManualCalibration(result, true, commandType, proType)
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventCo2ManualCalibration(false, write, commandType, proType))
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte) {
            val event = EventCo2ManualCalibration(true, write, commandType, proType)
            EventBus.getDefault().post(event)
        }
    }
}