package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventSoundLevel
import com.ihoment.base2app.infra.SafeLog

class CommandSoundLevel : AbsSingleController {

    companion object {
        const val LEVEL_0 = 0x00// 静音
        const val LEVEL_1 = 0x01// 低
        const val LEVEL_2 = 0x02// 中
        const val LEVEL_3 = 0x03// 高
    }

    private var level: Int = LEVEL_0
    private var switch = false

    /**
     * 写操作
     */
    constructor(switch: Boolean, level: Int) : super(true) {
        this.switch = switch
        this.level = level
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray? {
        val switchByte = if (switch) 0x01.toByte() else 0x00.toByte()
        return byteArrayOf(switchByte, level.toByte())
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventSoundLevel.sendWriteResult(suc, commandType, proType, switch, level)
        return true
    }

    override fun fail() {
        EventSoundLevel.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_SOUND_LEVEL
    }

    override fun parseValidBytes(validBytes: ByteArray?): Boolean {
        if (validBytes == null || validBytes.size < 2) {
            return false
        }
        val switch = validBytes[0].toInt() != 0
        val level = validBytes[1].toInt()
        EventSoundLevel.sendSuc(isWrite, commandType, proType, switch, level)
        return true
    }
}