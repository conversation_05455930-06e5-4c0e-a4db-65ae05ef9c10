package com.govee.h5140.ble

import com.govee.base2home.pact.BleUtil
import com.govee.base2light.ble.ota.v3.OtaOpV3
import com.govee.base2newth.AbsControllerEvent
import com.govee.base2newth.AbsHeartComm
import com.govee.base2newth.AbsSingleController
import com.govee.base2newth.BleThProtocol
import com.govee.base2newth.IController
import com.govee.base2newth.IControllerComm
import com.govee.ble.BleController
import com.govee.h5140.Constants5140
import com.govee.h5140.detail.chart.AbsThpBleData
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import java.util.UUID
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * / **
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 心跳包下发处理器
 */
class H5140HeartSender : AbsHeartComm(), IControllerComm {

    private var heartController: HeartReceiver? = null
    private val controllers: ConcurrentLinkedQueue<IController> = ConcurrentLinkedQueue<IController>()
    private var commFailTimes = 0
    private val maxCommandRetryTimes = 5
    private var needParse = false

    companion object {
        private const val TAG = "HeartSender"
        private const val serviceUuidStr = "494e5445-4c4c-495f-524f-434b535f4857"
        private const val characteristicUuidStr = "494e5445-4c4c-495f-524f-434b535f2014"
        private val serviceUUID: UUID = UUID.fromString(serviceUuidStr)
        private val characteristicUUID: UUID = UUID.fromString(characteristicUuidStr)
    }

    override fun heartCommOvertime(proType: Byte, proCommandType: Byte) {
        //心跳包超时处理逻辑
        val controller: IController = controllers.peek()
        if (controller.proType == proType && controller.commandType == proCommandType) {
            controller.onResult(false, null)
        }
    }

    override fun getHeartBytes(): ByteArray {
        if (heartController == null) {
            heartController = HeartReceiver()
        }
        return heartController!!.value
    }

    override fun isCanSendHeart(): Boolean {
        //多包通信时；不处理心跳
        val inMultiComm: Boolean = ThpBleCommManager.getInstance().inMultiComm()
        if (inMultiComm) return false
        //ota时不处理心跳
        val inOta: Boolean = OtaOpV3.getInstance().inOta()
        return !inOta && !AbsThpBleData.isLoadThcd4Compare
    }

    override fun serviceUUID(): UUID {
        return serviceUUID
    }

    override fun characteristicUUID(): UUID {
        return characteristicUUID
    }

    override fun isSelfComm(serviceUuid: String, characteristicUuid: String, values: ByteArray): Boolean {
        if (serviceUuidStr != serviceUuid) return false
        if (characteristicUuidStr != characteristicUuid) return false
        return if (values.size >= 2) {
            values[0] == BleThProtocol.SINGLE_READ || values[0] == BleThProtocol.SINGLE_WRITE
        } else false
    }

    override fun parse(serviceUuid: String, characteristicUuid: String, values: ByteArray): Boolean {
        SafeLog.d(TAG) { "parse-->>>$needParse" }
        return if (needParse) {
            parseHeartBytes(values)
        } else {
            false
        }
    }

    override fun parsePriority(): Int {
        return Constants5140.COMM_PARSE_PRIORITY_HEART
    }

    fun setNeedParseHeartPackage(need: Boolean) {
        SafeLog.d(TAG) { "setNeedParseHeartPackage-->>>$need" }
        this.needParse = need
    }

    /**
     * 解析心跳包
     */
    private fun parseHeartBytes(values: ByteArray): Boolean {
        val proType = values[0]
        val proSubCommandType = values[1]
        if (proType == BleThProtocol.SINGLE_READ) {
            //心跳包
            if (heartController != null) {
                heartController!!.onResult(true, values)
            }
            return true
        } else if (proType == BleThProtocol.SINGLE_WRITE) {
            val peek: IController = controllers.peek()
            val isSame = peek.isSameController(proType, proSubCommandType)
            SafeLog.d(TAG){"parseHeartBytes-------->isSame=$isSame"}
            if (peek.isSameController(proType, proSubCommandType)) {
                val result = peek.onResult(true, values)
                SafeLog.d(TAG){"parseHeartBytes-------->result=$result"}
                if (result) {
                    controllers.remove(peek)
                }
                return true
            }
        }
        return false
    }

    /**
     * 启动任务
     */
    override fun startController(vararg iControllers: AbsSingleController) {
        controllers.clear()
        controllers.addAll(listOf(*iControllers))
        next()
    }

    private operator fun next() {
        if (controllers.isEmpty()) {
            heartComm.commOver()
            return
        }
        if (!BleController.getInstance().isConnected) return
        val controller: IController = controllers.peek()
        sendController(controller)
    }


    override fun clearControllers() {
        controllers.clear()
        heartComm.commOver()
    }

    override fun controllerEvent(event: AbsControllerEvent) {
        val result = event.isResult
        val write = event.isWrite
        val proType = event.proType
        val commandType = event.commandType
        val commandRetry = event.commandRetry()
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "controllerEvent() proType = " + BleUtil.byteToHexString(proType) + " ; commandType = " + BleUtil.byteToHexString(commandType) + " ; commandRetry = " + commandRetry + " ; write = " + write + " ; result = " + result)
        }
        if (result) {
            commFailTimes = 0
        } else if (commandRetry) {
            commFailTimes++
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "commFailTimes = $commFailTimes")
            }
            if (commFailTimes > maxCommandRetryTimes) {
                commFailTimes = 0
                //同时失败，需要重新连接蓝牙
                BleController.getInstance().disconnectBleAndNotify()
                return
            }
        }
        //操作失败，且指令不需要重试，则判断当前待执行的指令是否是相同指令，若是，则移除指令
        if (!result && !commandRetry) {
            val controller: IController = controllers.peek()
            if (controller.isSameController(proType, commandType)) {
                controllers.remove(controller)
            }
        }
        //执行下一个
        next()
    }
}