package com.govee.h5140.detail.setting

import com.govee.ui.R
import androidx.lifecycle.MutableLiveData
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.temUnit.TemperatureUnitType
import com.govee.base_h71xx.base.utils.toast
import com.govee.ctlchannel.core.ConnectStatus
import com.govee.ctlchannel.core.SourceType
import com.govee.ctlchannel.message.Gmsg4NewthController
import com.govee.ctlchannel.message.ParserAdapter
import com.govee.ctlchannel.resp.parseResult
import com.govee.ctlchannel.send
import com.govee.h5140.ble.command.CommandTemUnit
import com.govee.h5140.ble.event.EventTempUnit
import com.govee.h5140.detail.chart.AbsThpBleData
import com.govee.h5140.detail.chart.H5140TempUnitHelper
import com.govee.shared.model.BaseUiSyncModel
import com.govee.shared.model.TempUnitModel
import com.govee.shared.viewmodel.CommSharedVm
import com.govee.ui.component.SettingItemView
import com.ihoment.base2app.ext.isTrue
import com.ihoment.base2app.util.ResUtil
import java.util.UUID

/**
 * <AUTHOR>
 * @Date 2023/5/18 11:01
 * @Description
 * 将部分业务抽离到vm中处理（）
 */
class SharedSettingVM : CommSharedVm() {
    /**单位状态触发回调*/
    private val triggerUnitAction: (Boolean) -> Unit = {
        sendTempUnit(
            if (it) {
                CommandTemUnit.FAH
            } else {
                CommandTemUnit.CEN
            }
        )
    }

    /**单位状态刷新*/
    private val unitStatusLiveData = MutableLiveData<Boolean>()

    override fun initEvent() {
        showLoading()
        val fahOpen = TemUnitConfig.read().isTemUnitFah(sku, device, hardVersion, softVersion)
        unitStatusLiveData.postValue(fahOpen)
        if (!bleAddress.isNullOrEmpty() && !topic.isNullOrEmpty()) {
            createClient(
                bleAddress = bleAddress!!,
                serviceUuid = AbsThpBleData.serviceUUID,
                characteristicUuid = UUID.fromString("494e5445-4c4c-495f-524f-434b535f2011"),
                topic = topic!!,
                sku = sku,
                device = device,
                connectRightNow = true
            )
        }
        client?.mMergeStatusCall = {
            if (it == ConnectStatus.CONNECT_SUC) {
                sendTempUnit(null)//读指令
            } else if (it == ConnectStatus.CONNECT_FAILED) {
                closeLoading()
            }
        }
    }

    /**
     * 构建item列表
     * @return List<IModel>
     */
    override fun createItemList(): MutableList<BaseUiSyncModel> {
        val list = super.createItemList()
        val unitStatusModel = SettingItemView.CommonSwitchModel(
            commonIconResId = R.mipmap.new_sensor_setting_icon_temper_unites, commonLabelResId = R.string.h5072_tem_unit, showTempUnitSwich = true
        )
        list.add(0, TempUnitModel(unitStatusModel, triggerUnitAction, unitStatusLiveData))
        return list
    }


    /**
     * 发送温度切换
     * @param tuType Int
     */
    private fun sendTempUnit(tuType: Int? = null) {
        val isFahOpen = tuType == 1
        val commTuType = if (tuType != null) {
            showLoading()
            CommandTemUnit(tuType)
        } else {//读指令
            CommandTemUnit()
        }
        if (client == null) {
            return
        }
        scopeLaunch {
            val msg = Gmsg4NewthController(commTuType, object : ParserAdapter<Boolean>() {
                override suspend fun transObj4Ble(
                    validBytes: ByteArray,
                    proType: Byte,
                    cmdType: Byte,
                    sourceType: SourceType
                ): Boolean {
                    return validBytes[0].toInt() == 1
                }
            })
            msg.send(client!!).parseResult(fail = {
                if (tuType != null) {
                    unitStatusLiveData.postValue(!isFahOpen)//取反结果
                    toast(ResUtil.getString(R.string.bbq_presettem_failed))
                }
                closeLoading()
            }, suc = {
                if (tuType != null) {
                    val unitType = if (tuType == CommandTemUnit.FAH) {
                        TemperatureUnitType.Fahrenheit
                    } else {
                        TemperatureUnitType.Celsius
                    }
                    H5140TempUnitHelper.setTemUnit(sku, device, unitType)
                    EventTempUnit.sendRefreshEvent()
                    unitStatusLiveData.postValue(isFahOpen)
                    toast(ResUtil.getString(R.string.bbq_presettem_successful))
                } else {
                    unitStatusLiveData.postValue(it.isTrue())
                }
                closeLoading()
            })
        }
    }

    override fun onCleared() {
        super.onCleared()
        client?.close()
        client = null
    }
}
