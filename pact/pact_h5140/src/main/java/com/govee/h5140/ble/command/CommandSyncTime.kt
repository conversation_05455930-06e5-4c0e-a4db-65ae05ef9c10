package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsOnlyWriteSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventSyncTime
import com.govee.h5140.ble.event.EventSyncTime.Companion.sendWriteResult

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 向5106设备同步手机时间的指令，并解析其回复
 * 备注：这个是向设备写入，用33指令
 */
class CommandSyncTime : AbsOnlyWriteSingleController() {

    override fun translateWrite(): ByteArray {
        val timeStamp = (System.currentTimeMillis() / 1000).toInt()
        return BleUtil.getSignedBytesFor4(timeStamp, false)
    }

    override fun onWriteResult(suc: Boolean): Bo<PERSON>an {
        sendWriteResult(suc, commandType, proType)
        return true
    }

    override fun fail() {
        EventSyncTime.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_SYNC_TIME
    }
}