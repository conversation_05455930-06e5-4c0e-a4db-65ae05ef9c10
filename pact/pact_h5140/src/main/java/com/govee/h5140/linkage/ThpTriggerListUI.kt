//package com.govee.h5140.linkage
//
//import android.view.View
//import androidx.appcompat.app.AppCompatActivity
//import androidx.recyclerview.widget.LinearLayoutManager
//import androidx.recyclerview.widget.RecyclerView
//import com.govee.base2home.common.BaseRvAdapter
//import com.govee.base2home.iot.AbsCmd
//import com.govee.base2home.iot.Write
//import com.govee.base2home.scenes.model.DeviceModel
//import com.govee.base2light.rhythm.RhyRule
//import com.govee.base2light.rhythm.model.AbsTriggerModel
//import com.govee.base2light.rhythm.ui.AbsRhythmUI
//
///**
// * <AUTHOR>
// * @date created on 2022/9/9
// * @description H5106的联动添加触发条件时的触发item
// */
//open class ThpTriggerListUI : AbsRhythmUI {
//
//    private lateinit var ac: AppCompatActivity
//    private lateinit var deviceModel: DeviceModel
//    private lateinit var selectChangeListener: OnSelectChangeListener
//    private val triggerModels: ArrayList<AbsTriggerModel>
//    private val layoutRes: Int
//
//    constructor(
//        ac: AppCompatActivity,
//        deviceModel: DeviceModel,
//        selectChangeListener: OnSelectChangeListener,
//        triggerModels: ArrayList<AbsTriggerModel>,
//        layoutRes: Int = com.govee.base2light.R.layout.b2light_layout_rhythm_switch_list
//    ) : super(ac, layoutRes, deviceModel, selectChangeListener) {
//        this.ac = ac
//        this.deviceModel = deviceModel
//        this.selectChangeListener = selectChangeListener
//        this.triggerModels = triggerModels
//        this.layoutRes = layoutRes
//        //初始化
//        init()
//    }
//
//    private var mRhyRule: RhyRule? = null
//    var mRecyclerView: RecyclerView? = null
//    private var mSelectTriggerModel: AbsTriggerModel? = null
//
//    //默认选中场景
//    var mSelectedPosition = 0
//    private lateinit var thpTriggerAdapter: Adapter4ThpTrigger
//
//    private fun init() {
//        mRecyclerView =
//            fucView.findViewById(com.govee.base2light.R.id.rootRecyclerView) as RecyclerView?
//        mRecyclerView?.layoutManager = LinearLayoutManager(ac)
//        thpTriggerAdapter = Adapter4ThpTrigger(ac, triggerModels, deviceModel)
//        mRecyclerView?.adapter = thpTriggerAdapter
//        mSelectTriggerModel = triggerModels[0]
//        //设置item点击监听
//        thpTriggerAdapter.setOnRvItemClickListener(object : BaseRvAdapter.OnRvItemClickListener {
//            override fun onItemClick(v: View, position: Int) {
//                thpTriggerAdapter.setSelectedIndex(position)
//                mSelectedPosition = position
//                mSelectTriggerModel = triggerModels[position]
//            }
//        })
//        //默认选中第一个
//        updateSelect(true)
//    }
//
//    /**
//     * 保存待应用指令
//     */
//    override fun inflateApplyRule(rhyRule: RhyRule?) {
//        if (rhyRule == null || mSelectTriggerModel == null) {
//            return
//        }
//        val absCmd = makeRuleCmd()
//        val iotMsg = if (absCmd == null) "" else Write.getWriteMsg4Transaction(absCmd)
//        val blueMsg = null
//        when (val opCmdValue = mSelectTriggerModel!!.opCmdValue) {
//            RhyRule.op_type_air_quality -> {
//                rhyRule.addThTriggerRule(RhyRule.op_type_air_quality, mSelectTriggerModel!!.opValue)
//            }
//            RhyRule.op_type_tem_high -> {
//                rhyRule.addThTriggerRule(RhyRule.op_type_tem_high, mSelectTriggerModel!!.opValue)
//            }
//            RhyRule.op_type_tem_low -> {
//                rhyRule.addThTriggerRule(RhyRule.op_type_tem_low, mSelectTriggerModel!!.opValue)
//            }
//            RhyRule.op_type_hum_high -> {
//                rhyRule.addThTriggerRule(RhyRule.op_type_hum_high, mSelectTriggerModel!!.opValue)
//            }
//            RhyRule.op_type_hum_low -> {
//                rhyRule.addThTriggerRule(RhyRule.op_type_hum_low, mSelectTriggerModel!!.opValue)
//            }
//            else -> {
//                rhyRule.addEmptyJsonRule(opCmdValue, blueMsg, iotMsg)
//            }
//        }
//    }
//
//    override fun applyRhythmRule(rhyRule: RhyRule?) {
//        this.mRhyRule = rhyRule
//        if (rhyRule == null) {
//            return
//        }
//        updateSelect(true)
//        var index = 0
//        for (trigger in triggerModels) {
//            if (rhyRule.containsEmptyJsonRule(trigger.opCmdValue)) {
//                mSelectTriggerModel = trigger
//                mSelectedPosition = index
//                thpTriggerAdapter.setSelectedIndex(index)
//                break
//            }
//            index++
//        }
//    }
//
//    private fun makeRuleCmd(): AbsCmd? {
//        return mSelectTriggerModel?.makeRuleCmd()
//    }
//
//    override fun updateUI() {
//    }
//
//    override fun isSingleSelect(): Boolean {
//        return true
//    }
//
//    override fun canCancelSelect(): Boolean {
//        return false
//    }
//}