package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 向5106设备写入手机时间的事件
 */
class EventSyncTime(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) :
        AbsControllerEvent(result, write, commandType, proType, true) {

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventSyncTime(false, write, commandType, proType))
        }

        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte) {
            val event = EventSyncTime(result, true, commandType, proType)
            EventBus.getDefault().post(event)
        }
    }
}