package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsSingleController
import com.govee.h5140.Constants5140
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventCo2Warning
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog

/**
 * <AUTHOR>
 * @date created on 2022/06/09
 * @description 从5106设备读/写pm2.5预警信息的指令，并解析其回复
 */
class CommandCo2Warning : AbsSingleController {

    companion object {
        private const val TAG = "CommandPm25Warning"
    }

    /**
     * 是否开启预警
     */
    private var openWarning = false
    private var minCo2 = Constants5140.CO2_MIN_VALUE
    private var maxCo2 = Constants5140.CO2_MAX_VALUE
    private var alarmInterval = 10

    /**
     * 写操作
     * @param alarmInterval:报警间隔的分钟数
     */
    constructor(openWarning: Boolean, minHum: Int, maxHum: Int, alarmInterval: Int) : super(true) {
        this.minCo2 = checkCo2(minHum)
        this.maxCo2 = checkCo2(maxHum)
        this.openWarning = openWarning
        this.alarmInterval = alarmInterval
    }

    private fun checkCo2(co2: Int): Int {
        var realCo2 = co2
        val minCo2 = Constants5140.CO2_MIN_VALUE
        val maxCo2 = Constants5140.CO2_MAX_VALUE
        if (LogInfra.openLog() && (realCo2 < minCo2 || realCo2 > maxCo2)) {
            LogInfra.Log.e(TAG, "checkCo2() co2 is not in range! pm2.5 = $realCo2")
        }
        realCo2 = realCo2.coerceAtLeast(minCo2)
        realCo2 = realCo2.coerceAtMost(maxCo2)
        return realCo2
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray {
        val openWarningValue = if (openWarning) 1.toByte() else 0.toByte()
        val minPm25Values = BleUtil.getSignedBytesFor2(minCo2, false)
        val maxPm25Values = BleUtil.getSignedBytesFor2(maxCo2, false)
        val alarmInterval = alarmInterval.toByte()
        return byteArrayOf(
            openWarningValue,
            minPm25Values[0],
            minPm25Values[1],
            maxPm25Values[0],
            maxPm25Values[1],
            alarmInterval
        )
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventCo2Warning.sendWriteResult(suc, commandType, proType, openWarning, minCo2, maxCo2, alarmInterval)
        return true
    }

    override fun fail() {
        EventCo2Warning.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_PM25_WARNING
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        SafeLog.d("AbsThBle") { "parseValidBytes co2 waring=${BleUtil.bytesToHexString(validBytes)}" }
        val openWarning = validBytes[0].toInt() == 1
        var minPm25 = BleUtil.getSignedShort(validBytes[1], validBytes[2]).toInt()
        minPm25 = checkCo2(minPm25)
        var maxPm25 = BleUtil.convertTwoBytesToShort(validBytes[3], validBytes[4])
        maxPm25 = checkCo2(maxPm25)
        val alarmInterval = validBytes[5].toInt()
        EventCo2Warning.sendSuc(isWrite, commandType, proType, openWarning, minPm25, maxPm25, alarmInterval)
        return true
    }
}