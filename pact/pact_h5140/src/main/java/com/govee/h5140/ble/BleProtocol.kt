package com.govee.h5140.ble

/**
 * / **
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 5106设备读/写功能协议类
 */
object BleProtocol {

    const val VALUE_BLE_HEART = 0x01.toByte()
    const val VALUE_TEM_UNIT = 0x02.toByte()
    const val VALUE_HUM_WARNING = 0x03.toByte()
    const val VALUE_TEM_WARNING = 0x04.toByte()
    const val VALUE_HUM_CALI = 0x06.toByte()
    const val VALUE_TEM_CALI = 0x07.toByte()
    const val VALUE_DEVICE_ID = 0x0c.toByte()
    const val VALUE_SOFT_VERSION = 0x0e.toByte()
    const val VALUE_HARD_VERSION = 0x0d.toByte()
    const val VALUE_SYNC_TIME = 0x10.toByte()
    const val VALUE_CLEAR_DATA = 0x11.toByte()
    const val VALUE_SWITCH_DISPLAY = 0x12.toByte()
    const val VALUE_DISPLAY_LIGHTNESS = 0x13.toByte()
    const val VALUE_SINGLE_WIFI_MAC = 0x14.toByte()
    const val VALUE_TIME_ZONE = 0x15.toByte()
    const val VALUE_PM25_WARNING = 0x16.toByte()
    const val VALUE_TIME_FORMAT = 0x17.toByte()
    const val VALUE_UPLOAD_LAST_DATA = 0x18.toByte()
    const val VALUE_DISPLAY_LIGHTNESS_V2 = 0x19.toByte()
    const val NOTIFY_COMM = 0xee.toByte()
    const val VALUE_SOUND_LEVEL = 0x1b.toByte()
    const val VALUE_CO2_ADJUST = 0x1c.toByte()
    const val VALUE_CO2_GRADE = 0x1d.toByte()
    const val VALUE_TH_CO2_DATA = 0x0a.toByte()
    const val VALUE_AIR_QUALITY_NOTICE = 0x1f.toByte()
    const val VALUE_WU_RAO_MODE = 0x1e.toByte()
}