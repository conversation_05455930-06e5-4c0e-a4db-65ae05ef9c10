package com.govee.h5140

import android.os.Bundle
import com.govee.base2home.Constant4L5
import com.govee.base2newth.ThConsV1

/**
 * <AUTHOR>
 * @date created on 2022/5/25
 * @description pact_h5140相关常量
 */
object Constants5140 {

    const val CHANNEL_ID = "Thermometer with co2"

    const val BLE_SIGNAL_COMPARE_VALUE = -40
    const val BLE_SIGNAL_COMPARE_OFFSET_VALUE = 5

    /**
     * 温度(摄氏度)范围极值
     */
    const val TEM_MIN_VALUE = ThConsV1.TEM_MIN_VALUE_H5106
    const val TEM_MAX_VALUE = ThConsV1.TEM_MAX_VALUE_H5106

    /**
     * 湿度百分比氛围极值
     */
    const val HUM_MIN_VALUE = 0
    const val HUM_MAX_VALUE = 100

    /**
     * co2范围极值
     */
    const val CO2_MIN_VALUE = -200
    const val CO2_MAX_VALUE = 5000

    const val CO2_GRADE_MIN = 1100
    const val CO2_GRADE_MAX = 1800

    /**
     * 通信解析的优先级：
     * dataComm优先级最高(主要为图表数据相关)
     * notifyComm优先级其次
     * thpComm优先级其次
     * heartComm优先级其次
     */
    const val COMM_PARSE_PRIORITY_HEART = 1
    const val COMM_PARSE_PRIORITY_THP = 2
    const val COMM_PARSE_PRIORITY_NOTIFY = 3
    const val COMM_PARSE_PRIORITY_DATA = 4

    /**
     * 温度校准相关
     */
    const val TEM_CALI_MIN_CEL = -3f
    const val TEM_CALI_MAX_CEL = 3f
    const val TEM_CALI_MIN_FAH = -5.4f
    const val TEM_CALI_MAX_FAH = 5.4f

    /**
     * 湿度校准相关
     */
    const val HUM_CALI_MIN = -20f
    const val HUM_CALI_MAX = 20f

    const val intent_ac_addInfo = "intent_ac_addInfo"

    /**
     *传入图表信息页面的相关信息的key
     */
    const val intent_ac_goodsType = "intent_ac_goodsType"
    const val intent_ac_sku = "intent_ac_sku"
    const val intent_ac_device = "intent_ac_device"
    const val intent_ac_deviceName = "intent_ac_deviceName"
    const val intent_ac_bleAddress = "intent_ac_bleAddress"
    const val intent_ac_topic = "intent_ac_topic"
    const val intent_ac_tem_cali = "intent_ac_tem_cali"
    const val intent_ac_hum_cali = "intent_ac_hum_cali"
    const val intent_ac_soft_version = "intent_ac_soft_version"
    const val intent_ac_hard_version = "intent_ac_hard_version"
    const val intent_ac_key_wifi_mac = "intent_ac_key_wifi_mac"

    /**
     * 从设备读取的数据的时长
     */
    const val max_times_to_read_from_device = 20 * 24 * 60

    @JvmStatic
    fun makeAcIntentBundle(
        goodsType: Int,
        sku: String,
        device: String,
        deviceName: String,
        bleAddress: String,
        temCali: Int,
        humCali: Int,
        wifiMac: String,
        softVersion: String,
        hardVersion: String,
        topic: String,
    ): Bundle {
        val bundle = Bundle()
        bundle.putInt(intent_ac_goodsType, goodsType)
        bundle.putString(intent_ac_sku, sku)
        bundle.putString(intent_ac_device, device)
        bundle.putString(intent_ac_deviceName, deviceName)
        bundle.putString(intent_ac_bleAddress, bleAddress)
        bundle.putInt(intent_ac_tem_cali, temCali)
        bundle.putInt(intent_ac_hum_cali, humCali)
        bundle.putString(intent_ac_key_wifi_mac, wifiMac)
        bundle.putString(intent_ac_soft_version, softVersion)
        bundle.putString(intent_ac_hard_version, hardVersion)
        bundle.putString(intent_ac_topic, topic)
        return bundle
    }

    /**
     * 空气质量等级
     */
    const val EXCELLENT = Constant4L5.EXCELLENT
    const val GOOD = Constant4L5.GOOD
    const val MODERATE = Constant4L5.MODERATE
    const val POOR = Constant4L5.POOR

    const val IOT_HEART_INTERVAL_TIME = 30 * 1000L
    const val IOT_RESPONSE_TIME_OUT = 10 * 1000L
    const val IOT_CONNECT_TIME_OUT = 60 * 1000L
}