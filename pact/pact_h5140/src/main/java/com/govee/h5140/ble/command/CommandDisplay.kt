package com.govee.h5140.ble.command

import com.govee.base2newth.AbsSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventDisplay

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取/写入屏幕显示(时间/pm2.5)的指令，并解析其回复
 */
class CommandDisplay : AbsSingleController {

    companion object {
        const val PM25 = 0X00
        const val TIME = 0x01
    }

    private var sdType = TIME

    /**
     * 写操作
     */
    constructor(sdType: Int) : super(true) {
        this.sdType = checkSdType(sdType)
    }

    private fun checkSdType(sdType: Int): Int {
        var realWay = sdType
        if (sdType != 0 && sdType != 1) {
            realWay = TIME
        }
        return realWay
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray {
        return byteArrayOf(sdType.toByte())
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventDisplay.sendWriteResult(suc, commandType, proType, sdType)
        return true
    }

    override fun fail() {
        EventDisplay.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_SWITCH_DISPLAY
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        EventDisplay.sendSuc(isWrite, commandType, proType, validBytes[0].toInt())
        return true
    }
}