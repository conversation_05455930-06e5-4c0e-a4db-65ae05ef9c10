package com.govee.h5140.item

import android.content.Context
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.Group
import androidx.databinding.DataBindingUtil
import com.chad.library.adapter.base.viewholder.BaseViewHolder
import com.govee.base2kt.ext.gone
import com.govee.base2kt.ext.invisibleByBoolean
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2newth.databinding.ComponentH5140DeviceItemBigBinding
import com.govee.base2newth.databinding.ComponentH5140DeviceItemSmallBinding
import com.govee.base2newth.deviceitem.Item3
import com.govee.base2newth.deviceitem.ThNewStyleUIConvert
import com.govee.kt.loadSkuIcon
import com.govee.kt.ui.device.Adapter4DeviceList
import com.govee.kt.ui.device.CardStyle
import com.govee.kt.ui.device.CommonViewResId
import com.govee.kt.ui.device.IDeviceHolder
import com.govee.kt.ui.device.IDeviceItem
import com.govee.kt.ui.device.Item
import com.govee.kt.ui.device.ItemType
import com.govee.kt.ui.device.SpanSize
import com.govee.kt.ui.device.util.DeviceItemOfflineUtils
import com.govee.kt.ui.device.util.DeviceListEditManager
import com.govee.utils.GlobalStateUtils
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil

/**
 * @author：YangQi.Chen
 * @date：2025/4/9 上午11:02
 * @description：
 */
class H5140Holder : IDeviceHolder {

    override fun itemTypes(): MutableList<ItemType> {
        return arrayListOf(
            ItemType(
                Item.ITEM_TYPE_29_SMALL,
                com.govee.base2newth.R.layout.component_h5140_device_item_small,
                CardStyle.CLASSICS,
                SpanSize.SMALL
            ),
            ItemType(
                Item.ITEM_TYPE_29_BIG,
                com.govee.base2newth.R.layout.component_h5140_device_item_big,
                CardStyle.CLASSICS,
                SpanSize.BIG
            ),
            ItemType(
                Item.ITEM_TYPE_H5140_SMALL_NEW,
                com.govee.base2newth.R.layout.device_item_layout_th_small_new,
                CardStyle.NEWS,
                SpanSize.SMALL
            ),
            ItemType(
                Item.ITEM_TYPE_H5140_BIG_NEW,
                com.govee.base2newth.R.layout.device_item_layout_th_big_new,
                CardStyle.NEWS,
                SpanSize.BIG
            ),
        )
    }

    override fun childClickIds(): MutableList<Int> {
        return mutableListOf<Int>().apply {
            add(com.govee.base2newth.R.id.ivQa)
        }
    }

    override fun getCommonViewResId(holder: BaseViewHolder): CommonViewResId {
        return CommonViewResId(
            ivRemoveCardResId = com.govee.base2newth.R.id.ivRemove,
            ivChangeCardTypeResId = com.govee.base2newth.R.id.ivChangeSize,
        )
    }

    override fun covert(context: Context?, adapterDeviceList: Adapter4DeviceList, holder: BaseViewHolder, item: IDeviceItem): Boolean {
        val isEditMode = DeviceListEditManager.isEditState()
        return when (holder.itemViewType) {
            Item.ITEM_TYPE_29_SMALL -> {
                parseItemTypeSmall(context, holder, item, isEditMode)
            }

            Item.ITEM_TYPE_29_BIG -> {
                parseItemTypeBig(context, holder, item, isEditMode)
            }

            Item.ITEM_TYPE_H5140_SMALL_NEW -> {
                ThNewStyleUIConvert.parseItemTypeSmall(
                    context,
                    holder,
                    item,
                    isEditMode
                ) { binding, _ ->
                    binding.ivBattery.gone()
                }
            }

            Item.ITEM_TYPE_H5140_BIG_NEW -> {
                ThNewStyleUIConvert.parseItemTypeBig(
                    context,
                    holder,
                    item,
                    isEditMode
                ) { binding, _ ->
                    binding.ivBattery.gone()
                }
            }

            else -> {
                SafeLog.e("SensorHolder") { "covert() itemType = ${item.itemType} 未匹配到" }
                false
            }
        }
    }

    private fun parseItemTypeBig(
        context: Context?,
        holder: BaseViewHolder,
        item: IDeviceItem,
        isEdit: Boolean,
    ): Boolean {
        val item3 = item.item4Ui as? Item3
        return item3?.run {
            val binding = DataBindingUtil.bind<ComponentH5140DeviceItemBigBinding>(holder.itemView) ?: return false
            binding.icShareFlag.root.visibleByBoolean(item3.share == 1)
            uiPartFresh(
                context, item, this,
                binding.ivQa,
                binding.ivTopFlag,
                binding.orderGroup,
                binding.tvOrder,
                binding.tvName,
                binding.ivBT,
                binding.ivWifi,
                binding.ivBattery,
                binding.tvHint,
                binding.ivIcon,
                binding.groupTimeMills,
                binding.tvTimeMills,
                isNormalMode = !isEdit
            )
            /*温度值*/
            binding.rangeScale4Tem.update(
                this.range4Tem.range4Warn,
                this.range4Tem.range4WarnStr,
                this.range4Tem.curPercent,
                this.getFlagColor(this.temWarning),
                this.range4Tem.showFlag
            )
            binding.tvTemValue.text = this.temStr
            binding.tvTemValue.setTextColor(this.getColor(this.temWarning))
            /*湿度值*/
            binding.rangeScale4Hum.update(
                this.rang4Hum.range4Warn,
                this.rang4Hum.range4WarnStr,
                this.rang4Hum.curPercent,
                this.getFlagColor(this.humWarning),
                this.rang4Hum.showFlag
            )
            binding.tvHumValue.text = this.humStr
            binding.tvHumValue.setTextColor(this.getColor(this.humWarning))
            /*co2值*/
            binding.rangeScale4Co2.update(
                this.rangeCo2.range4Warn,
                this.rangeCo2.range4WarnStr,
                this.rangeCo2.curPercent,
                this.getFlagColor(this.co2Warning),
                this.rangeCo2.showFlag
            )
            binding.tvCo2Value.text = this.co2Str
            binding.tvCo2Value.setTextColor(this.getColor(this.co2Warning))
            true
        } ?: false
    }

    private fun parseItemTypeSmall(
        context: Context?,
        holder: BaseViewHolder,
        item: IDeviceItem,
        isEdit: Boolean,
    ): Boolean {
        val item3 = item.item4Ui as? Item3
        return item3?.run {
            val binding = DataBindingUtil.bind<ComponentH5140DeviceItemSmallBinding>(holder.itemView) ?: return false
            binding.icShareFlag.root.visibleByBoolean(item3.share == 1)

            uiPartFresh(
                context, item, this,
                binding.ivQa,
                binding.ivTopFlag,
                binding.orderGroup,
                binding.tvOrder,
                binding.tvName,
                binding.ivBT,
                binding.ivWifi,
                binding.ivBattery,
                binding.tvHint,
                binding.ivIcon,
                binding.groupTimeMills,
                binding.tvTimeMills,
                isNormalMode = !isEdit
            )
            /*温度值*/
            binding.ivTemIcon.setImageDrawable(ResUtil.getDrawable(this.temIconRes()))
            binding.tvTemValue.text = this.temStr
            binding.tvTemValue.setTextColor(this.getColor(this.temWarning, true))
            /*湿度值*/
            binding.ivHumIcon.setImageDrawable(ResUtil.getDrawable(this.humIconRes()))
            binding.tvHumValue.text = this.humStr
            binding.tvHumValue.setTextColor(this.getColor(this.humWarning, true))
            /* 根据CO2浓度显示不同的颜色 */
            binding.ivC02Icon.setImageDrawable(ResUtil.getDrawable(this.co2IconRes()))
            binding.tvC02Value.text = this.co2Str
            binding.tvC02Value.setTextColor(this.getColor(this.co2Warning, true))
            true
        } ?: false
    }

    private fun uiPartFresh(
        context: Context?,
        item: IDeviceItem,
        item3: Item3,
        ivQa: ImageView,
        ivTopFlag: ImageView,
        orderGroup: Group,
        tvOrder: TextView,
        tvName: TextView,
        ivBT: ImageView,
        ivWifi: ImageView,
        ivBattery: ImageView,
        tvHint: TextView,
        ivIcon: ImageView,
        timeMillsGroup: Group,
        tvTimeMills: TextView,
        isNormalMode: Boolean
    ) {
        /*调查问卷*/
        ivQa.visibleByBoolean(item.qaVis && isNormalMode)
        /*flag图标*/
        val flagVis = item3.flagVis()
        ivTopFlag.visibleByBoolean(flagVis)
        if (flagVis) {
            ivTopFlag.setImageDrawable(ResUtil.getDrawable(item3.flagIconResId()))
        }
        /*order显示*/
        val orderVis = item3.orderVis()
        orderGroup.visibleByBoolean(orderVis)
        if (orderVis) {
            tvOrder.textSize = if (item3.orderValue.length > 1) 10f else 12f
            tvOrder.text = item3.orderValue
        }
        /*设备名称*/
        tvName.text = item3.deviceName
        /*蓝牙图标*/
        ivBT.visibleByBoolean((item3.bleVis ?: false) && isNormalMode && GlobalStateUtils.btSwitchIsOpen)
        /*Wi-Fi图标*/
        val wifiGone = !item3.wifiVis() && !DeviceItemOfflineUtils.offlineOverTime()
        val wifiVis = !wifiGone
        ivWifi.visibleByBoolean(wifiVis && isNormalMode)
        if (wifiVis) {
            ivWifi.setImageDrawable(ResUtil.getDrawable(item3.wifiIconRes()))
        }
        /*电池图标(H5140 是直连电源的，没有电源图标)*/
        val batteryVis = false
        ivBattery.visibleByBoolean(batteryVis)
        if (batteryVis) {
            ivBattery.setImageDrawable(ResUtil.getDrawable(item3.batteryResId))
        }
        /*提示文案*/
        val hintVis = item3.hintVis()
        tvHint.visibleByBoolean(hintVis)
        if (hintVis) {
            tvHint.text = item3.hintStr
        }
        /*时间显示*/
        val timeStrVis = item3.timeStrVis()
        timeMillsGroup.invisibleByBoolean(!timeStrVis)
        if (timeStrVis) {
            tvTimeMills.text = item3.timeMillStr
        }
        /*icon图标*/
        ivIcon.loadSkuIcon(context, item3.sku, item3.spec, item3.defSkuRes, item3.skuUrl)
    }

}