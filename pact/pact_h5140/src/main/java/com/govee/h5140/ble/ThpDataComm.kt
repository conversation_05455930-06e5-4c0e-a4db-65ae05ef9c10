package com.govee.h5140.ble

import com.govee.base2light.ble.ota.v3.OtaOpV3
import com.govee.base2newth.AbsSingleController
import com.govee.base2newth.IControllerComm
import com.govee.h5140.detail.chart.AbsThpBleData

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 5106设备ble数据通讯器
 */
class ThpDataComm : AbsThpBleData() {

    override val controllerComm: IControllerComm
        get() = ThpBleCommManager.getInstance().heart as H5140HeartSender

    override fun sendCmd(controller: AbsSingleController) {
        if (OtaOpV3.getInstance().inOta()) {
            release()
            return
        }
        controllerComm.startController(controller)
    }
}