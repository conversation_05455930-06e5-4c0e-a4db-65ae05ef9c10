package com.govee.h5140.add

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import com.govee.base2home.Constant
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.analytics.service.Wifi
import com.govee.base2home.config.Base2homeConfig
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.main.tab.EventTabDefault
import com.govee.base2home.pact.GoodsType
import com.govee.base2newth.AbsThBle
import com.govee.base2newth.EventNotifyWifiConnect
import com.govee.base2newth.EventWifi
import com.govee.ble.BleController
import com.govee.h5140.Constants5140
import com.govee.h5140.ble.ThpBleCommManager.Companion.getInstance
import com.govee.h5140.detail.chart.Activity4CharInfo
import com.govee.ui.R
import com.govee.ui.ac.AbsBleWifiChooseAc
import com.govee.ui.dialog.WifiErrorReasonHintDialog
import com.govee.ui.dialog.WifiErrorSimpleHintDialog
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.util.JumpUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2022/5/31
 * @description 给5106设备配网页面
 */
class Activity4WifiChoose : AbsBleWifiChooseAc() {

    private var goodsType = 0
    private var sku: String? = null
    private var device: String? = null
    private var deviceName: String? = null
    private var bleAddress: String? = null
    private var wifiMac: String? = null
    private var wifiInputLimitSsid = 0
    private var wifiInputLimitPassword = 0
    private var fromType = 0
    private var temCali = 0
    private var humCali = 0

    private var softVersion = ""
    private var hardVersion = ""
    private var topic = ""

    companion object {
        private const val intent_ac_key_wifi_input_limit_ssid =
                "intent_ac_key_wifi_input_limit_ssid"
        private const val intent_ac_key_wifi_input_limit_password =
                "intent_ac_key_wifi_input_limit_password"
        private const val intent_ac_key_from_type = "intent_ac_key_from_type"
        private const val from_type_add = 1
        private const val from_type_change_wifi = 2

        /**
         * 跳转到wifi选择界面-添加设备流程（设置wifi名称页面调用）
         */
        fun jump2WifiChooseAc4Add(
                ac: Activity?,
                closeCurAc: Boolean,
                goodsType: Int,
                sku: String?,
                device: String?,
                deviceName: String?,
                bleAddress: String?,
                wifiInputLimitSsid: Int,
                wifiInputLimitPassword: Int,
                temCali: Int,
                humCali: Int,
                wifiMac: String,
                softVersion: String,
                hardVersion: String,
                topic: String,
        ) {
            val bundle = Bundle()
            bundle.putInt(Constants5140.intent_ac_goodsType, goodsType)
            bundle.putString(Constants5140.intent_ac_sku, sku)
            bundle.putString(Constants5140.intent_ac_device, device)
            bundle.putString(Constants5140.intent_ac_deviceName, deviceName)
            bundle.putString(Constants5140.intent_ac_bleAddress, bleAddress)
            bundle.putInt(Constants5140.intent_ac_tem_cali, temCali)
            bundle.putInt(Constants5140.intent_ac_hum_cali, humCali)
            bundle.putInt(intent_ac_key_wifi_input_limit_ssid, wifiInputLimitSsid)
            bundle.putInt(intent_ac_key_wifi_input_limit_password, wifiInputLimitPassword)
            bundle.putInt(intent_ac_key_from_type, from_type_add)
            bundle.putString(Constants5140.intent_ac_key_wifi_mac, wifiMac)
            bundle.putString(Constants5140.intent_ac_soft_version, softVersion)
            bundle.putString(Constants5140.intent_ac_hard_version, hardVersion)
            bundle.putString(Constants5140.intent_ac_topic, topic)
            JumpUtil.jumpWithBundle(ac, Activity4WifiChoose::class.java, closeCurAc, bundle)
        }

        /**
         * 跳转到wifi选择界面-修改wifi流程（设置页面调用）
         */
        fun jump2WifiChooseAc4ChangeWifi(
                ac: Activity?,
                closeCurAc: Boolean,
                goodsType: Int,
                sku: String?,
                bleAddress: String?,
                wifiInputLimitSsid: Int,
                wifiInputLimitPassword: Int
        ) {
            val bundle = Bundle()
            bundle.putInt(Constants5140.intent_ac_goodsType, goodsType)
            bundle.putString(Constants5140.intent_ac_sku, sku)
            bundle.putString(Constants5140.intent_ac_bleAddress, bleAddress)
            bundle.putInt(intent_ac_key_wifi_input_limit_ssid, wifiInputLimitSsid)
            bundle.putInt(intent_ac_key_wifi_input_limit_password, wifiInputLimitPassword)
            bundle.putInt(intent_ac_key_from_type, from_type_change_wifi)
            JumpUtil.jumpWithBundle(ac, Activity4WifiChoose::class.java, closeCurAc, bundle)
        }
    }

    private val thBle: AbsThBle
        get() = getInstance()

    override fun toRecycler() {
        //无回收逻辑
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "toRecycler()")
        }
    }

    override fun getWifiInputLimit(): IntArray {
        return intArrayOf(wifiInputLimitSsid, wifiInputLimitPassword)
    }

    override fun getSku(): String {
        return sku!!
    }

    override fun parserIntentParams(intent: Intent) {
        fromType = intent.getIntExtra(intent_ac_key_from_type, from_type_add)
        sku = intent.getStringExtra(Constants5140.intent_ac_sku)
        goodsType =
                intent.getIntExtra(Constants5140.intent_ac_goodsType, GoodsType.GOODES_TYPE_NO_SUPPORT)
        bleAddress = intent.getStringExtra(Constants5140.intent_ac_bleAddress)
        wifiMac = intent.getStringExtra(Constants5140.intent_ac_key_wifi_mac)
        wifiInputLimitSsid = intent.getIntExtra(intent_ac_key_wifi_input_limit_ssid, 31)
        wifiInputLimitPassword = intent.getIntExtra(intent_ac_key_wifi_input_limit_password, 31)
        if (fromType == from_type_add) {
            temCali = intent.getIntExtra(Constants5140.intent_ac_tem_cali, 0)
            humCali = intent.getIntExtra(Constants5140.intent_ac_hum_cali, 0)
            device = intent.getStringExtra(Constants5140.intent_ac_device)
            deviceName = intent.getStringExtra(Constants5140.intent_ac_deviceName)

            softVersion = intent.getStringExtra(Constants5140.intent_ac_soft_version)!!
            hardVersion = intent.getStringExtra(Constants5140.intent_ac_hard_version)!!
            topic = intent.getStringExtra(Constants5140.intent_ac_topic)!!
        }
        updateBackAndSkip(fromType == from_type_change_wifi, fromType == from_type_add)
    }

    override fun toSkip() {
        toDeviceDetail()
    }

    override fun onResumeBleClose() {
        if (fromType == from_type_add) {
            //添加流程，若蓝牙断开，则直接关闭界面
            toCloseAc()
        } else {
            uiType = UIType.ble_unable
            updateUI()
        }
    }

    override fun onResumeBleConnected(connected: Boolean) {
        if (fromType == from_type_add) {
            if (!connected) {
                //添加流程，若蓝牙断开，则直接关闭界面
                toCloseAc()
            } else {
                updateUI()
            }
        } else {
            if (!connected) {
                uiType = UIType.ble_disconnect
                toReconnectBle()
            }
            updateUI()
        }
    }

    override fun btConnectResult(connectSuc: Boolean) {
        if (fromType == from_type_add) {
            if (!connectSuc) {
                btClose4CloseAc()
            }
        } else {
            uiType = if (!connectSuc) {
                if (BleController.getInstance().isBlueToothOpen) UIType.ble_disconnect else UIType.ble_unable
            } else {
                UIType.wifi_choose_normal
            }
            updateUI()
        }
    }

    override fun btStatusChange(btOpen: Boolean) {
        if (fromType == from_type_add) {
            if (!btOpen) {
                btClose4CloseAc()
            }
        } else {
            if (btOpen) {
                val inBackground = BaseApplication.getBaseApplication().isInBackground
                //后台不允许连接蓝牙
                if (inBackground) return
                showLoading()
                toReconnectBle()
            } else {
                uiType = UIType.ble_unable
                updateUI()
            }
        }
    }

    override fun toCloseAc() {
        val inBackground = BaseApplication.getBaseApplication().isInBackground
        //若退至后台，则不允许跳转
        if (inBackground) return
        if (fromType == from_type_change_wifi) {
            //从设置界面进入，直接退出界面即可
            finish()
        } else {
            //配对进入，退到设备详情界面
            toDeviceDetail()
        }
    }

    private fun toDeviceDetail() {
        //跳转到设备详情页面
        //通知Tab更改为Default
        EventTabDefault.sendEventTabDefault()
        val bundle = Constants5140.makeAcIntentBundle(
                goodsType,
                sku!!,
                device!!,
                deviceName!!,
                bleAddress!!,
                temCali,
                humCali,
                wifiMac!!,
                softVersion,
                hardVersion,
                topic,
        )
        val functionAcClass = Base2homeConfig.getConfig().mainAcClass;
        BaseApplication.getBaseApplication()
                .finishOtherAndStartNewAc(functionAcClass, Activity4CharInfo::class.java, bundle)
    }

    override fun bleSendWifiInfo(wifiName: String, password: String) {
        val multiWifiController = Command5140Wifi(wifiName, password)
        thBle.startMultiControllers(multiWifiController)
    }

    private var hadRecord = false

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventWifi(event: EventWifi) {
        val result = event.isResult
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventWifi() result = $result")
        }
        if (result) {
            //等待wifi连接，超时60s
            //提示wifi连接超时
            LoadingDialog.createDialog(this, (60 * 1000).toLong()) { recordWifiSetOvertime() }
                    .setEventKey(TAG).show()
            toast(R.string.temhum_wifi_setting_suc)
        } else {
            hideLoading()
            toast(R.string.temhum_wifi_setting_fail)
        }
        thBle.multiControllerEvent(event)
    }

    private fun recordWifiSetOvertime() {
        //隐藏dialog
        hideLoading()
        hadRecord = true
        if (isDestroyed) return
        val errorCodeStr = sku + "_" + positionErrorCode + "_" + Constant.error_code_timeout
        recordWifiSetFailReason(errorCodeStr)
        //超时错误弹窗
        WifiErrorSimpleHintDialog.createDialog(this, errorCodeStr).show()
    }

    private fun recordWifiSetFailReason(errorCode: String) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "recordWifiSetFailReason() errorCode = $errorCode")
        }
        val values = HashMap<String, Any>()
        values[ParamKey.reason] = errorCode
        AnalyticsRecorder.getInstance().recordMaps(EventKey.wifi_set_fail, values)
        //统计wifi设置成功/失败
        Wifi.recordWifi(false)
    }

    private val positionErrorCode: Int
        get() = if (fromType == from_type_change_wifi) Constant.error_code_position_change_wifi else Constant.error_code_position_add_device

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventNotifyWifiConnect(event: EventNotifyWifiConnect) {
        val connectSuc = event.connectSuc
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNotifyWifiConnect() connectSuc = $connectSuc")
        }
        hideLoading()
        if (connectSuc) {
            toast(R.string.temhum_wifi_connect_suc)
            //统计记录wifi设置成功次数
            recordWifiSetTimes(true)
            //连接wifi成功，退出界面
            toCloseAc()
        } else {
            if (!hadRecord) {
                hadRecord = true
                //统计记录wifi设置失败次数
                recordWifiSetTimes(false)
            }
            //统计wifi设置错误码
            val errorCode = event.errorCode
            val systemErrorCode = event.systemErrorCode
            val positionErrorCode = positionErrorCode
            val errorCodeStr =
                    sku + "_" + positionErrorCode + "_" + errorCode + "_" + systemErrorCode
            recordWifiSetFailReason(errorCodeStr)
            //依据错误码；提示不同的错误提示弹框
            if (!Constant.knownErrorCodeArray.contains(errorCode)) {
                //未知错误
                WifiErrorSimpleHintDialog.createDialog(this, errorCodeStr).show()
            } else {
                //已知错误
                WifiErrorReasonHintDialog.createDialog(this, sku, errorCodeStr, errorCode).show()
            }
        }
    }

    override fun toReconnectBle() {
        val inBackground = BaseApplication.getBaseApplication().isInBackground
        //后台不允许连接蓝牙
        if (inBackground) return
        val connectBle = thBle.connectBle(bleAddress)
        if (!connectBle) {
            uiType = UIType.ble_unable
            updateUI()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        if (fromType == from_type_change_wifi) {
            super.onBackPressed()
        }
    }
}
