package com.govee.h5140.item

import com.govee.base2home.pact.GoodsType
import com.govee.base2home.pact.Pact
import com.govee.base2home.pact.Protocol
import com.govee.base2home.theme.ThemeM
import com.govee.base2newth.ThConsV1

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description
 */
object Support {

    private const val H5140 = "H5140"

    var supportProtocols: MutableList<Protocol> = ArrayList()

    /**
     * item的goodsType集合
     */
    val deviceItemGoodsTypes = intArrayOf(GoodsType.GOODS_TYPE_VALUE_H5140)

    /**
     * 添加5140支持的协议
     */
    fun addSupportPact() {
        val pact = Pact.getInstance
        val protocol = GoodsType.beProtocol(
                GoodsType.PACT_TYPE_VALUE_H5140,
                GoodsType.PACT_CODE_VALUE_H5140
        )
        supportProtocols.add(protocol)
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H5140, protocol)

        GoodsType.beProtocol(
            GoodsType.PACT_TYPE_VALUE_H5140,
            GoodsType.PACT_CODE_VALUE_H5140_2
        ).let {
            supportProtocols.add(it)
            pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H5140, it)
        }

        GoodsType.beProtocol(
            GoodsType.PACT_TYPE_VALUE_H5140_2,
            GoodsType.PACT_CODE_VALUE_H5140
        ).let {
            supportProtocols.add(it)
            pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_H5140, it)
        }

        //初始化默认sku图
        ThemeM.getInstance.addDefSkuRes(H5140, com.govee.ui.R.mipmap.add_list_type_device_5140)
    }

    /**
     * 判断协议是否支持
     */
    fun supportPact(goodsType: Int, protocol: Protocol?): Boolean {
        if (protocol == null) return false
        val pactType = protocol.pactType
        val pactCode = protocol.pactCode
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_H5140 && supportProtocols.isNotEmpty()) {
            for (pro in supportProtocols) {
                if (pro.isSameProtocol(pactType, pactCode)) return true
            }
        }
        return false
    }

    @JvmStatic
    fun getMinValidTimeMills(goodsType: Int): Long {
        return ThConsV1.min_valid_time_2022_01
    }

    @JvmStatic
    fun supportPush(goodsType: Int,sku:String): Boolean {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_H5140 || sku == H5140
    }
}