package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读写屏幕亮度的事件
 */
class EventLightness : AbsControllerEvent {

    constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON>olean, commandType: Byte, proType: Byte) : super(result, write, commandType, proType, !write)

    /**
     * 亮度等级值
     */
    var slLevel = 0

    companion object {
        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte, slLevel: Int) {
            val event = EventLightness(result, true, commandType, proType)
            event.slLevel = slLevel
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventLightness(false, write, commandType, proType))
        }

        fun sendSuc(write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte, slLevel: Int) {
            val event = EventLightness(true, write, commandType, proType)
            event.slLevel = slLevel
            EventBus.getDefault().post(event)
        }
    }
}