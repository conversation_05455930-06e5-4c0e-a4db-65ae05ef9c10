package com.govee.h5140.iotop

import com.google.gson.Gson
import com.govee.base2home.Constant4L5
import com.govee.base2home.pact.BleUtil
import com.govee.base2home.temUnit.TemperatureUnitType
import com.govee.base2home.util.Encode
import com.govee.base2home.util.NumberUtil
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.data.Co2LevelInfo
import com.govee.base2newth.data.Co2WarnInfo
import com.govee.base2newth.data.H5140C02WarnConfig
import com.govee.base2newth.data.THMemoryUtil
import com.govee.h5140.Constants5140
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventLightnessV2.LightnessV2
import com.govee.h5140.detail.chart.H5140TempUnitHelper
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import org.json.JSONObject

/**
 * <AUTHOR>
 * @date created on 2022/6/15
 * @description h5140 iot指令解析工具类
 */
object IotParseUtils {
    const val TAG = "IotParseUtils"
    private val gson = Gson()
    private var cacheIotHeartInfoMap: MutableMap<String, IotHeartInfo> = mutableMapOf()

    /**
     * 解析command,获取设备信息
     */
    fun getDeviceInfo(
        sku: String,
        device: String,
        commands: List<String>,
    ): IotHeartInfo? {
        val ptByteArray = getCommandByteArray(commands)
        if (ptByteArray.isNullOrEmpty()) {
            return null
        }
        val deviceInfo = parseIotData(ptByteArray, sku, device)
        if (deviceInfo.tuType == 1) {
            H5140TempUnitHelper.setTemUnit(sku, device, TemperatureUnitType.Fahrenheit)
        } else {
            H5140TempUnitHelper.setTemUnit(sku, device, TemperatureUnitType.Celsius)
        }
        return deviceInfo
    }

    fun parseIotData(ptByteArray: MutableList<ByteArray>?, sku: String, device: String): IotHeartInfo {
        val cacheIotHeartInfo = cacheIotHeartInfoMap[sku + "_" + device] ?: IotHeartInfo()
        if (ptByteArray.isNullOrEmpty()) {
            return cacheIotHeartInfo
        }
        SafeLog.d("parse_iot_pt") { "\n\n----->" }
        ptByteArray.forEachIndexed { index, validBytes ->
            SafeLog.d("parse_iot_pt") { "index=$index,bytes=${BleUtil.bytesToHexString(validBytes)}" }
            val commandType = validBytes[1]
            when (commandType) {
                BleProtocol.VALUE_BLE_HEART, BleProtocol.VALUE_TH_CO2_DATA -> {// 0x01，心跳
                    cacheIotHeartInfo.tem = BleUtil.getSignedShort(validBytes[3], validBytes[2]).toInt()
                    cacheIotHeartInfo.hum = getHumValidRangeValue(parse2ByteToIntV2(validBytes[4], validBytes[5]))
                    cacheIotHeartInfo.co2 = parse2ByteToIntV2(validBytes[6], validBytes[7])
                }

                BleProtocol.VALUE_TEM_UNIT -> {// 0x02，温度单位
                    val tempUnit = validBytes[2].toInt()
                    cacheIotHeartInfo.tuType = tempUnit
                }

                BleProtocol.VALUE_HUM_WARNING -> {// 0x03，湿度预警范围
                    val humWarning = validBytes[2].toInt() == 1
                    val minHum = checkHum(BleUtil.convertTwoBytesToShort(validBytes[3], validBytes[4]))
                    val maxHum = checkHum(BleUtil.convertTwoBytesToShort(validBytes[5], validBytes[6]))
                    cacheIotHeartInfo.humMin = getHumValidRangeValue(minHum)
                    cacheIotHeartInfo.humMax = getHumValidRangeValue(maxHum)
                    cacheIotHeartInfo.humWarning = humWarning
                }

                BleProtocol.VALUE_TEM_WARNING -> {// 0x04，温度预警范围
                    val openWarning = validBytes[2].toInt() == 1
                    val tempMin = BleUtil.convertTwoBytesToShort(validBytes[3], validBytes[4])
                    val tempMax = BleUtil.convertTwoBytesToShort(validBytes[5], validBytes[6])
                    cacheIotHeartInfo.temMin = tempMin
                    cacheIotHeartInfo.temMax = tempMax
                    cacheIotHeartInfo.temWarning = openWarning
                }

                BleProtocol.VALUE_HUM_CALI -> {// 0x06，湿度校准值
                    var humCali = BleUtil.getSignedShort(validBytes[3], validBytes[2]).toInt()
                    humCali = checkHumCali(humCali)
                    cacheIotHeartInfo.humCali = humCali
                }

                BleProtocol.VALUE_TEM_CALI -> {// 0x07，温度校准值
                    var temCali = BleUtil.getSignedShort(validBytes[3], validBytes[2]).toInt()
                    temCali = checkTemCali(temCali)
                    cacheIotHeartInfo.temCali = temCali
                }

                BleProtocol.VALUE_SWITCH_DISPLAY -> {// 0x12，切换显示页面
                    cacheIotHeartInfo.sdType = validBytes[2].toInt()
                }

                BleProtocol.VALUE_DISPLAY_LIGHTNESS -> {// 0x13 亮度调节
                    cacheIotHeartInfo.slLevel = validBytes[2].toInt()
                }

                BleProtocol.VALUE_PM25_WARNING -> {// 0x16 Co2报警范围
                    val openWarning = validBytes[2].toInt() == 1
                    var minCo2 = BleUtil.convertTwoBytesToShort(validBytes[3], validBytes[4])
                    minCo2 = checkCo2(minCo2)
                    var maxCo2 = BleUtil.convertTwoBytesToShort(validBytes[5], validBytes[6])
                    val alarmInterval = validBytes[7].toInt()
                    SafeLog.d("updateCo2WarnInfo") { "validBytes[7].toInt() = $alarmInterval" }
                    maxCo2 = checkCo2(maxCo2)
                    cacheIotHeartInfo.co2Warning = openWarning
                    cacheIotHeartInfo.co2Min = minCo2
                    cacheIotHeartInfo.co2Max = maxCo2
                    cacheIotHeartInfo.alarmInterval = alarmInterval
                    //if (minCo2 != Constants5140.CO2_MIN_VALUE || maxCo2 != Constants5140.CO2_MAX_VALUE) {
                    val warning = WarnConfig.read().queryWarningRangeByKey(sku, device)
                    warning.co2Min = if (minCo2 < 400) 400 else minCo2
                    warning.co2Max = maxCo2
                    warning.co2Warning = openWarning
                    WarnConfig.read().updateWarningRange(warning, false)
                    H5140C02WarnConfig.updateCo2WarnInfo(sku, device, Co2WarnInfo(minCo2, maxCo2, openWarning, alarmInterval))
                    //}
                }

                BleProtocol.VALUE_TIME_FORMAT -> {// 0x17 时间类型 0x01 24小时制
                    cacheIotHeartInfo.tfType = validBytes[2].toInt()
                }

                BleProtocol.VALUE_DISPLAY_LIGHTNESS_V2 -> {// 0x19 显示设置
                    val lightnessV2 = LightnessV2()
                    val newArray = validBytes.drop(2).toByteArray()
                    val hexStr = BleUtil.bytesToHexString(newArray).replace("0x", "").replace(" ", "")
                    lightnessV2.dayTimeRange = hexStr.substring(0, 8)
                    lightnessV2.dayLtValue = BleUtil.hex2Int(hexStr.substring(8, 10), true)
                    lightnessV2.dayLtOpen = BleUtil.hex2Int(hexStr.substring(10, 12), true)
                    lightnessV2.nightTimeRange = hexStr.substring(12, 20)
                    lightnessV2.nightLtValue = BleUtil.hex2Int(hexStr.substring(20, 22), true)
                    lightnessV2.nightLtOpen = BleUtil.hex2Int(hexStr.substring(22, 24), true)
                    cacheIotHeartInfo.slV2 = lightnessV2
                }

                BleProtocol.VALUE_SOUND_LEVEL -> {// 0x1b蜂鸣器报警音量
                    val switch = validBytes[2].toInt() != 0
                    val level = validBytes[3].toInt()
                    cacheIotHeartInfo.soundLevel = level
                    cacheIotHeartInfo.soundSwitch = switch
                }

                BleProtocol.VALUE_CO2_GRADE -> {// 0x1d Co2浓度等级设置
                    val minLevel = BleUtil.convertTwoBytesToShort(validBytes[2], validBytes[3])
                    val maxLevel = BleUtil.convertTwoBytesToShort(validBytes[4], validBytes[5])
                    cacheIotHeartInfo.co2GradeMin = minLevel
                    cacheIotHeartInfo.co2GradeMax = maxLevel
                    H5140C02WarnConfig.updateCo2LevelInfo(sku, device, Co2LevelInfo(minLevel, maxLevel))
                }

                BleProtocol.VALUE_AIR_QUALITY_NOTICE -> {// 0x1f co2浓度通知开关
                    val switch = validBytes[3].toInt() == 1
                    cacheIotHeartInfo.co2NotifySwitch = switch
                    THMemoryUtil.getInstance().setAirQsOpen(sku + "_" + device, switch)
                }

                BleProtocol.VALUE_WU_RAO_MODE -> {
                    val isOpen = validBytes[2].toInt() == 1
                    val startHour = validBytes[3].toInt()
                    val startMinute = validBytes[4].toInt()
                    val endHour = validBytes[5].toInt()
                    val endMinute = validBytes[6].toInt()
                    cacheIotHeartInfo.dndIsOpen = isOpen
                    cacheIotHeartInfo.dndStartHour = startHour
                    cacheIotHeartInfo.dndStartMinute = startMinute
                    cacheIotHeartInfo.dndEndHour = endHour
                    cacheIotHeartInfo.dndEndMinute = endMinute
                }
            }
        }
        LogInfra.Log.i(TAG, "获取到的设备信息为-->${gson.toJson(cacheIotHeartInfo)}")
        cacheIotHeartInfoMap[sku + "_" + device] = cacheIotHeartInfo
        return cacheIotHeartInfo
    }

    private fun getTemValidRangeValue(value: Int): Int {
        return NumberUtil.getValidRangeValue(
            value,
            Constant4L5.TEM_MIN_VALUE_H5140 * 100,
            Constant4L5.TEM_MAX_VALUE_H5140 * 100
        )
    }

    private fun getHumValidRangeValue(value: Int): Int {
        return NumberUtil.getValidRangeValue(
            value,
            Constants5140.HUM_MIN_VALUE * 100,
            Constants5140.HUM_MAX_VALUE * 100
        )
    }

    /**
     * 把op转字节数组
     */
    fun getCommandByteArray(commands: List<String>?): MutableList<ByteArray>? {
        if (commands == null) {
            return null
        }
        val ptBytes: MutableList<ByteArray> = ArrayList()
        for (command in commands) {
            val commandBytes = Encode.decryByBase64(command)
            if (commandBytes != null) {
                ptBytes.add(commandBytes)
            }
        }
        if (ptBytes.isEmpty()) {
            return null
        }
        return ptBytes
    }

    /**
     * 获取status指令回复中的信息
     */
    fun getCommandHexStr(commands: List<String>): String? {
        val ptBytes: MutableList<ByteArray> = ArrayList()
        for (command in commands) {
            val commandBytes = Encode.decryByBase64(command)
            if (commandBytes != null) {
                ptBytes.add(commandBytes)
            }
        }
        if (ptBytes.isEmpty()) {
            return null
        }
        val validData = ByteArray(ptBytes.size * 20)
        for ((index, item) in ptBytes.withIndex()) {
            System.arraycopy(item, 0, validData, 20 * index, item.size)
        }
        val commandHexStr = BleUtil.bytesToHexString(validData)
        LogInfra.Log.i(TAG, "command解析->HexStr==$commandHexStr")
        //去除" "和"0x"
        return commandHexStr.replace("0x", "").replace(" ", "")
    }

    /**
     * 提取json中的某个字段的json
     */
    fun getJsonObjectStr(jsonStr: String, key: String): String {
        try {
            val jsonObject = JSONObject(jsonStr)
            val `object` = jsonObject.getJSONObject(key)
            return `object`.toString()
        } catch (e: Exception) {
            e.printStackTrace()
        }
        return ""
    }

    private fun checkHum(hum: Int): Int {
        var readHum = hum
        val minHum = Constants5140.HUM_MIN_VALUE * 100
        val maxHum = Constants5140.HUM_MAX_VALUE * 100
        readHum = readHum.coerceAtLeast(minHum)
        readHum = readHum.coerceAtMost(maxHum)
        return readHum
    }

    private fun checkTem(tem: Int): Int {
        var realTem = tem
        val minTem = Constants5140.TEM_MIN_VALUE * 100
        val maxTem = Constants5140.TEM_MAX_VALUE * 100
        realTem = realTem.coerceAtLeast(minTem)
        realTem = realTem.coerceAtMost(maxTem)
        return realTem
    }

    private fun checkHumCali(humCali: Int): Int {
        var realHumCali = humCali
        val minHumCali = (Constants5140.HUM_CALI_MIN * 100).toInt()
        val maxHumCali = (Constants5140.HUM_CALI_MAX * 100).toInt()
        realHumCali = realHumCali.coerceAtLeast(minHumCali)
        realHumCali = realHumCali.coerceAtMost(maxHumCali)
        return realHumCali
    }

    private fun checkTemCali(temCali: Int): Int {
        var realTemCali = temCali
        val minTemCali = (Constants5140.TEM_CALI_MIN_CEL * 100).toInt()
        val maxTemCali = (Constants5140.TEM_CALI_MAX_CEL * 100).toInt()
        realTemCali = realTemCali.coerceAtLeast(minTemCali)
        realTemCali = realTemCali.coerceAtMost(maxTemCali)
        return realTemCali
    }

    private fun checkCo2(co2: Int): Int {
        var realCo2 = co2
        val minPm25 = Constants5140.CO2_MIN_VALUE
        val maxPm25 = Constants5140.CO2_MAX_VALUE
        realCo2 = realCo2.coerceAtLeast(minPm25)
        realCo2 = realCo2.coerceAtMost(maxPm25)
        return realCo2
    }

    /**
     * 2个字节转int,兼容负数的情况，高位在前低位在后
     */
    fun parse2ByteToInt(byte1: Byte, byte2: Byte): Int {
        return ((byte1.toInt() and 0xFF) shl 8) or (byte2.toInt() and 0xFF)
    }

    /**
     * 2个字节转int,兼容负数的情况，低位在前高位在后
     */
    fun parse2ByteToIntV2(byte1: Byte, byte2: Byte): Int {
        return ((byte2.toInt() and 0xFF) shl 8) or (byte1.toInt() and 0xFF)
    }

    fun twoBytesToInt(byte1: Byte, byte2: Byte, bigEndian: Boolean = true): Int {
        val high = if (bigEndian) byte1.toInt() and 0xFF else byte2.toInt() and 0xFF
        val low = if (bigEndian) byte2.toInt() and 0xFF else byte1.toInt() and 0xFF
        return (high shl 8) or low
    }
}