package com.govee.h5140.add

import android.animation.ValueAnimator
import android.os.Bundle
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import com.govee.base2home.Constant4L5
import com.govee.base2home.main.choose.BaseBleDeviceChooseActivity
import com.govee.base2home.support.SupManager
import com.govee.base2home.support.UiConfig
import com.govee.base2kt.ac.AbsAc
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.setSpannable
import com.govee.ble.event.EventBleBroadcastListenerTrigger
import com.govee.h5140.databinding.H5140ActivityPairGuideBinding
import com.govee.ui.R
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil

class PairGuideActivity : AbsAc<H5140ActivityPairGuideBinding>() {
    private lateinit var tvGuideConfirm: TextView
    private var supManager: SupManager? = null
    private val animator = ValueAnimator.ofFloat(0f, 1f).apply {
        duration = 1000 // 完整周期（显示+隐藏）的时间
        repeatCount = ValueAnimator.INFINITE // 无限循环
        repeatMode = ValueAnimator.RESTART
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        tvGuideConfirm = findViewById(com.govee.h5140.R.id.tvGuideConfirm)
        val tvStep1 = findViewById<TextView>(com.govee.h5140.R.id.tvStep1)
        tvStep1.setSpannable(
            ResUtil.getString(R.string.h7129_pair_guide_step1),
            ResUtil.getColor(R.color.font_style_44_6_textColor),
            ResUtil.getString(R.string.app_first_add)
        )
        val tvStep2 = findViewById<TextView>(com.govee.h5140.R.id.tvStep2)
        val secondStr =  ResUtil.getStringFormat(R.string.h7126_pair_guide_step_s, "3")
        val text2 = ResUtil.getStringFormat(
            R.string.h7129_pair_guide_step2_s,
            secondStr
        )
        tvStep2.setSpannable(
            text2,
            ResUtil.getColor(R.color.font_style_44_6_textColor),
            secondStr
        )
        findViewById<ImageView>(com.govee.h5140.R.id.back).clickDelay {
            finish()
        }
        tvGuideConfirm.clickDelay {
            JumpUtil.jump(this, BaseBleDeviceChooseActivity::class.java, intent.extras)
            //进入蓝牙扫描界面；需要关闭主界面蓝牙广播
            EventBleBroadcastListenerTrigger.sendEventBleBroadcastListenerTrigger(false)
            finish()
        }

        // 根据sku区分不同的图片资源
        viewBinding.let {
            setDrawable(it.ivStep1, com.govee.h5140.R.mipmap.connect_pics_wifi_h5140_01)
            setDrawable(it.ivStep2, com.govee.h5140.R.mipmap.connect_pics_wifi_h5140_02)
        }

        supManager = SupManager(this, UiConfig.getConfig4InputContent(), Constant4L5.H5140)
        supManager!!.show()

        animator.addUpdateListener { animation ->
            val value = animation.animatedValue as Float
            viewBinding.ivWifiIcon.alpha = if (value < 0.5f) 1f else 0f // 前半段显示，后半段隐藏
        }
    }

    override fun onResume() {
        super.onResume()
        animator.start()
    }

    override fun onPause() {
        super.onPause()
        animator.cancel()
        viewBinding.ivWifiIcon.alpha = 1f // 停止时恢复显示
    }

    private fun setDrawable(img: ImageView?, resId: Int) {
        img?.setImageDrawable(ContextCompat.getDrawable(this, resId))
    }

    /**
     * 获取当前Ac的布局的xml布局content的布局id
     *
     * @return id
     */
    override fun getAcContentRootViewId(): Int = com.govee.h5140.R.id.ac_container
    override fun adapterInsetViewId(): Int = com.govee.h5140.R.id.top_flag

    override fun layoutId(): Int = com.govee.h5140.R.layout.h5140_activity_pair_guide
}