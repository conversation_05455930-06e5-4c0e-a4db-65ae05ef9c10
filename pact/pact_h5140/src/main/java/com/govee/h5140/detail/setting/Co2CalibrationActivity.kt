package com.govee.h5140.detail.setting

import android.annotation.SuppressLint
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.os.Message
import com.govee.base2kt.ac.AbsAc
import com.govee.base2kt.ext.clickDelay
import com.govee.ble.BleController
import com.govee.cache.GlobalCache
import com.govee.cache.key.SmartCacheKey
import com.govee.h5140.databinding.H5140Co2CalibrationActivityBinding
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.ToastUtil

/**
 * @author：YangQi.Chen
 * @date：2025/4/11 下午4:40
 * @description：Co2手动校准页面
 */
class Co2CalibrationActivity : AbsAc<H5140Co2CalibrationActivityBinding>() {

    private var countDownSecond = 3
    private val isIotConnect by lazy { intent.getBooleanExtra(INTENT_KEY_IS_IOT_CONNECT, false) }
    private val mSku by lazy { intent.getStringExtra(INTENT_KEY_SKU) ?: "" }
    private val mDevice by lazy { intent.getStringExtra(INTENT_KEY_DEVICE) ?: "" }

    private val mHandler = object : Handler(Looper.getMainLooper()) {
        @SuppressLint("SetTextI18n")
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            if (isDestroyed) {
                return
            }
            if (countDownSecond <= 0) {
                removeCallbacksAndMessages(null)
                viewBinding.btnDone.isEnabled = true
                viewBinding.btnDone.alpha = 1f
                viewBinding.btnDone.text = getString(com.govee.ui.R.string.h5072_calibration_label)
            } else {
                sendEmptyMessageDelayed(1, 1000)
                val secondStr = ResUtil.getStringFormat(com.govee.ui.R.string.app_send_code_delay, countDownSecond)
                viewBinding.btnDone.text = "${getString(com.govee.ui.R.string.h5140_please_read_note)}（$secondStr）"
                countDownSecond--
            }
        }
    }

    companion object {
        const val INTENT_KEY_IS_IOT_CONNECT = "intent_key_is_iot_connect"
        const val INTENT_KEY_SKU = "intent_key_sku"
        const val INTENT_KEY_DEVICE = "intent_key_device"

        /**
         * 缓存手动校准的时间
         */
        fun cacheAdjustTime(sku: String, device: String) {
            val key = "${sku}_${device}_co2_calibration_time"
            val smartKey = SmartCacheKey.sku(sku, key)
            SafeLog.d("Co2CalibrationActivity") { "$sku $device cacheAdjustTime=${System.currentTimeMillis()}" }
            GlobalCache.build().set(smartKey, System.currentTimeMillis())
        }

        fun getAdjustTime(sku: String, device: String): Long {
            val key = "${sku}_${device}_co2_calibration_time"
            val smartKey = SmartCacheKey.sku(sku, key)
            SafeLog.d("Co2CalibrationActivity") { "$sku $device getAdjustTime=${GlobalCache.build().getLong(smartKey, -1L)}" }
            return GlobalCache.build().getLong(smartKey, -1L)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        viewBinding.iv4Back.clickDelay {
            finish()
        }
        viewBinding.btnDone.clickDelay {
            // 判断连接状态，再进行校准
            val isBleConnected = BleController.getInstance().isConnected
            if (isBleConnected || isIotConnect) {
                setResult(RESULT_OK)
                cacheAdjustTime(mSku,mDevice)
                finish()
            } else {
                ToastUtil.getInstance().toast(com.govee.ui.R.string.h5140_toast_connect_device)
            }
        }

        val disTime = System.currentTimeMillis() - getAdjustTime(mSku, mDevice)
        SafeLog.d(TAG) { "disTime=$disTime" }
        if (disTime >= 60 * 5 * 1000) {
            mHandler.sendEmptyMessage(1)
        } else {
            viewBinding.btnDone.text = ResUtil.getString(com.govee.ui.R.string.h5140_calibration)
            Handler(Looper.getMainLooper()).postDelayed({
                if (!isDestroyed) {
                    viewBinding.btnDone.text = getString(com.govee.ui.R.string.h5072_calibration_label)
                    viewBinding.btnDone.alpha = 1f
                }
            }, disTime)
        }
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.h5140.R.id.ac_container
    }

    override fun adapterInsetViewId(): Int {
        return com.govee.h5140.R.id.topFlag
    }

    override fun layoutId(): Int {
        return com.govee.h5140.R.layout.h5140_co2_calibration_activity
    }
}