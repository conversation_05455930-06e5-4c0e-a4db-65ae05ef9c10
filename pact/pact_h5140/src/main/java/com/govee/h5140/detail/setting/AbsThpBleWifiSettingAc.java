package com.govee.h5140.detail.setting;

import static com.govee.h5140.detail.setting.Co2CalibrationActivity.INTENT_KEY_DEVICE;
import static com.govee.h5140.detail.setting.Co2CalibrationActivity.INTENT_KEY_IS_IOT_CONNECT;
import static com.govee.h5140.detail.setting.Co2CalibrationActivity.INTENT_KEY_SKU;

import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;

import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.annotation.Nullable;

import com.govee.base2home.BaseRPEventActivity;
import com.govee.base2home.BuildConfig;
import com.govee.base2home.Constant;
import com.govee.base2home.custom.LoadingDialog;
import com.govee.base2home.device.EventSmartHomeResult;
import com.govee.base2home.device.Guide;
import com.govee.base2home.device.ISkuAccessoryServiceKt;
import com.govee.base2home.device.SmartHomeGuideM;
import com.govee.base2home.device.unbind.BaseUnUnBindM;
import com.govee.base2home.device.unbind.IUnBindM;
import com.govee.base2home.support.SupManager;
import com.govee.base2home.support.UiConfig;
import com.govee.base2home.temUnit.TemUnitConfig;
import com.govee.base2home.update.UpdateHint4VersionConfig;
import com.govee.base2home.update.download.CheckVersion;
import com.govee.base2home.util.ClickUtil;
import com.govee.base2home.util.InputUtil;
import com.govee.base2home.util.IntroUtils;
import com.govee.base2home.util.StrUtil;
import com.govee.base2newth.net.Request4Setting;
import com.govee.ble.BleController;
import com.govee.h5140.ble.command.CommandTemUnit;
import com.govee.h5140.ble.event.EventLightnessV2;
import com.govee.h5140.databinding.H5140ActivitySettingBinding;
import com.govee.h5140.dialog.TimeLevelDialog;
import com.govee.h5140.widget.H5140ThWarnSettingViewNew;
import com.govee.ui.R;
import com.govee.ui.dialog.BleUpdateHintDialog;
import com.govee.ui.dialog.ConfirmDialog;
import com.govee.ui.dialog.ConfirmDialogV5;
import com.govee.ui.dialog.Dialog4ClearThDataSelPeriod;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.ResUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

/**
 * <AUTHOR>
 * @date created on 2022/06/07
 * @description 5106设备设置页的基础类
 */
public abstract class AbsThpBleWifiSettingAc extends BaseRPEventActivity implements BaseUnUnBindM.OnUnbindListener, View.OnClickListener {

    protected static final int step_ble_suc = 1;
    protected static final int step_ble_scanning = 2;
    protected static final int step_ble_disconnect = 3;
    private SupManager supManager;
    private boolean inEditing;
    private boolean destroy;
    /**
     * 是否正在显示ble升级提示弹窗
     */
    protected boolean showingBleUpdateHintDialog;
    protected IUnBindM iUnBindM;
    //5.9.0新增
    private Dialog4ClearThDataSelPeriod selPeriodDialog;

    protected H5140ActivitySettingBinding binding;
    protected boolean isIotConnect = false;

    private Dialog4SelectTimeRange timeRangeDialog;

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        destroy = false;
        super.onCreate(savedInstanceState);
        //设置点击事件
        binding.btnBack.setOnClickListener(this);
        binding.deviceNameEdit.setOnClickListener(this);
        binding.btnDeviceNameDone.setOnClickListener(this);
        binding.btnDeviceNameCancel.setOnClickListener(this);
        binding.acContainer.setOnClickListener(this);
        binding.btnDeleteInside.setOnClickListener(this);
        binding.wifiContainer.setOnClickListener(this);
        binding.gvGuide.setLineVis(false);
        binding.gvGuide.setBottomLineVis(false);
        binding.common.vLoadAllData.setOnClickListener(this);
        binding.common.deleteDataContainer.setOnClickListener(this);
        binding.common.versionContainer.setOnClickListener(this);
        //初始化弹窗
        selPeriodDialog = Dialog4ClearThDataSelPeriod.Companion.createDialog(this, getSkuStr());
        binding.llCo2LevelSet.setOnClickListener(this);
        binding.tvCo2Calibration.setOnClickListener(this);
        binding.ivTuValueIcon45106S.setOnClickListener(this);
        binding.tvCo2DensityQuest.setOnClickListener(this);
        binding.tvCo2GradeQuest.setOnClickListener(this);
        binding.ivCo2DensitySwitch.setOnClickListener(this);
        binding.tvCalibrationQuestion.setOnClickListener(this);
        startBForResult = registerForActivityResult(
                new ActivityResultContracts.StartActivityForResult(),
                result -> {
                    if (result.getResultCode() == RESULT_OK) {
                        sendManualCalibration();
                    }
                }
        );
        binding.ivCo2AlarmInterval.setOnClickListener(this);
        binding.tvAlarmIntervalTime.setOnClickListener(this);
        binding.tvNotDisturbModeTime.setOnClickListener(this);
        mTimeLevelDialog = TimeLevelDialog.Companion.createDialog(this);
        binding.tvCo2AlarmInterval.setOnClickListener(this);
        binding.ivNotDisturbModeTips.setOnClickListener(this);
        binding.ivNotDisturbMode.setOnClickListener(this);
    }

    @Override
    protected void onDestroy() {
        destroy();
        super.onDestroy();
    }

    @Override
    protected int getLayout() {
        return -1;
    }

    @Override
    public boolean bindView(int layoutId) {
        binding = H5140ActivitySettingBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        return true;
    }

    private int tuType = CommandTemUnit.FAH;

    /**
     * 设备温度单位发生变化修改ui,供外部调用的方法
     */
    public void updateTuShow(int tuType) {
        this.tuType = tuType;
        boolean isFah = TemUnitConfig.read().isTemUnitFah(getSkuStr(), getDevice());
        if (!isFah) {
            binding.ivTuValueIcon45106S.setImageDrawable(ResUtil.getDrawable(R.mipmap.new_sensor_setting_switch_celsius));
        } else {
            binding.ivTuValueIcon45106S.setImageDrawable(ResUtil.getDrawable(R.mipmap.new_sensor_setting_switch_fahrenheit));
        }
    }

    protected void showWifiMac(String wifiMac) {
        if (TextUtils.isEmpty(wifiMac)) {
            return;
        }
        binding.common.containerWifiMac.setVisibility(View.VISIBLE);
        binding.common.lineWifiMac.setVisibility(View.VISIBLE);
        binding.common.macShowing.setText(wifiMac);
    }

    @Override
    protected int getAcContentRootViewId() {
        return binding.acContainer.getId();
    }

    @Override
    protected void doCheckPermissionPre() {
        super.doCheckPermissionPre();
        Intent intent = getIntent();
        parseIntentParams(intent);
        //更新命名提示语Hint字体
        String sensorNameHintDes = ResUtil.getStringFormat(R.string.setting_device_name_hint, String.valueOf(getDeviceNameInputLimit()));
        StrUtil.setEditHint(binding.deviceNameEdit, Constant.HINT_EDIT_TEXT_SIZE, sensorNameHintDes);
        InputUtil.hideIMEByDone(binding.deviceNameEdit);
        binding.deviceNameEdit.setText(getDeviceName());
        binding.common.tvModel.setText(getSkuStr());
        updateEditing(false);
        //空气质量开关
        /*binding.taqsvAirQaSwitch.setListener(new ThpAirQaSwitchView.ThpAirAaListener() {
            @Override
            public void onClickAqIntro() {
                Dialog4AirQuality.createDialog(AbsThpBleWifiSettingAc.this).show();
            }

            @Override
            public void onClickAqSwitch(boolean open) {
                sendAirQs(open);
            }
        });*/
        //pm2.5相关
        binding.tarvPm25AlarmRange.setListener(new ThCo2AlarmRangeView.ThPm25AlarmRangeListener() {

            @Override
            public void pmCo2AlarmChange(int minAlarmPm25, int maxAlarmPm25, boolean alarmOn) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "pm25AlarmChange() minAlarmPm25 = " + minAlarmPm25 + " ; maxAlarmPm25 = " + maxAlarmPm25 + " ; alarmOn = " + alarmOn);
                }
                sendPm25Alarm(minAlarmPm25, maxAlarmPm25, alarmOn);
            }
        });
        //温度报警相关
        binding.temAlarmRange.setNeedRealResult(false);
        binding.temAlarmRange.setTemRange(isFahOpen(), getTemRange());
        binding.temAlarmRange.setListener((minAlarmTem, maxAlarmTem, alarmOn) -> {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "temAlarmChange() minAlarmTem = " + minAlarmTem + " ; maxAlarmTem = " + maxAlarmTem + " ; alarmOn = " + alarmOn);
            }
            sendTemAlarm(minAlarmTem, maxAlarmTem, alarmOn);
        });
        //湿度报警相关
        binding.humAlarmRange.setNeedRealResult(false);
        binding.humAlarmRange.setHumRange(getHumRange());
        binding.humAlarmRange.setListener((minAlarmHum, maxAlarmHum, alarmOn) -> {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "humAlarmChange() minAlarmHum = " + minAlarmHum + " ; maxAlarmHum = " + maxAlarmHum + " alarmOn = " + alarmOn);
            }
            sendHumAlarm(minAlarmHum, maxAlarmHum, alarmOn);
        });
        //校准相关
        binding.calibration.setDeviceInfo(getSkuStr());
        binding.calibration.setRange(isFahOpen(), getTemCaliRange(), getHumCaliRange());
        binding.calibration.setListener(new ThpCalibrationView.THCalibrationListener() {
            @Override
            public void onTemCali(boolean fahOpen, float temCali) {
                sendTemCali(fahOpen, temCali);
            }

            @Override
            public void onHumCali(float humCali) {
                sendHumCali(humCali);
            }
        });
        //其他相关设置的回调监听
        binding.tosvOtherSetV2.setListener(new ThpOtherSetViewV2.ThOtherSetListener() {

            @Override
            public void onChangeTu(int tuType) {

            }

            @Override
            public void onChangeSd(int sdType) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onChangeSd() sdType = " + sdType);
                }
                sendScreenDisplay(sdType);
            }

            @Override
            public void onClickTfIntro() {
                String title = getResources().getString(R.string.h5106_time_inaccuracy);
                String content = getResources().getString(R.string.h5106_time_format_introduction);
                Dialog4CommonIntro.createDialog(AbsThpBleWifiSettingAc.this).onShow(title, content);
            }

            @Override
            public void onChangeTf(int tfType) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onChangeTf() tfType = " + tfType);
                }
                sendTimeFormat(tfType);
            }

            @Override
            public void onChangeSl(EventLightnessV2.LightnessV2 slInfo) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onChangeSl() slInfo = " + JsonUtil.toJson(slInfo));
                }
                sendScreenLightness(slInfo);
            }

            @Override
            public void onClickSlShowIntro() {
                String title = getResources().getString(R.string.h7121_display);
                String content = getResources().getString(R.string.h5106_show_intro_text);
                Dialog4CommonIntro.createDialog(AbsThpBleWifiSettingAc.this).onShow(title, content);
            }
        });
        binding.thWarnSettingViewNew.setOnSetSoundLevelListener(new H5140ThWarnSettingViewNew.OnSetSoundLevelListener() {
            @Override
            public void onSoundLevelChange(boolean b, int level) {
                if (!isConnected()) {
                    return;
                }
                sendSoundLevel(b, level);
            }
        });
        supManager = new SupManager(this, UiConfig.getConfig4Title(), getSkuStr());
        supManager.show();
        updateStep(step_ble_scanning);
        if (supportGuide()) {
            binding.gvGuide.setLineVis(true);
            //更新引导
            updateGuide(true);
        }
        //更多玩法推荐
        binding.common.recommendPlaysView.bindData(this, getSkuStr(), null);

        //适配insetTop
        adaptationInsetTop(com.govee.h5140.R.id.top_flag, AppUtil.getScreenWidth() * 61 / 750);

        insertSkuShopAccessory();
    }

    /**
     * 将配件shop的view组件插入到指定view的上方，这里是插入到电池ui-item的下方
     * takecare:注意ui改动带来的适配问题
     */
    private void insertSkuShopAccessory() {
        try {
            ISkuAccessoryServiceKt.insertSkuShopAccessory(TAG, getSkuStr(), binding.common.bottomOther, this, true, false, null, 0);
        } catch (Exception e) {
            if (BuildConfig.DEBUG) {
                e.printStackTrace();
            }
            SafeLog.e(TAG, e);
        }
    }

    /**
     * 获取device
     *
     * @return
     */
    protected abstract String getDevice();

    /**
     * 是否支持guide
     *
     * @return
     */
    protected boolean supportGuide() {
        return true;
    }

    private void updateGuide(boolean checkSku) {
        if (isDestroy()) {
            return;
        }
        List<Guide> guides = SmartHomeGuideM.getInstance.querySkuGuide(checkSku, getSkuStr());
        binding.gvGuide.setGuide(getSkuStr(), guides);
        binding.tvOther.setVisibility(View.VISIBLE);
        if (guides == null || guides.isEmpty()) {
            //不支持引导
            binding.tvOther.setVisibility(View.GONE);
            binding.gvGuide.setVisibility(View.GONE);
            //更新扫描页坐标参数
        } else {
            //支持引导
            binding.otherInfoLabel.setVisibility(View.VISIBLE);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventSmartHomeResult(EventSmartHomeResult event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSmartHomeResult()");
        }
        if (supportGuide()) {
            updateGuide(false);
        }
    }

    /**
     * 发送温度单位
     *
     * @param tuType
     */
    protected abstract void sendTempUnit(int tuType);

    /**
     * 设置蜂鸣音量
     *
     * @param level 0-3级别
     */
    protected abstract void sendSoundLevel(boolean sw, int level);

    /**
     * 设置co2浓度等级
     *
     * @param minLevel
     * @param maxLevel
     */
    protected abstract void sendCo2GradeLevel(int minLevel, int maxLevel);

    /**
     * 手动校准Co2
     */
    protected abstract void sendManualCalibration();

    /**
     * 发送屏幕展示设置
     *
     * @param sdType
     */
    protected abstract void sendScreenDisplay(int sdType);

    /**
     * 发送时间制式设置
     *
     * @param tfType
     */
    protected abstract void sendTimeFormat(int tfType);

    /**
     * 发送屏幕亮度显示设置
     *
     * @param slType
     */
    protected abstract void sendScreenLightness(int slType);

    /**
     * 发送屏幕亮度显示设置
     * 备注：5.3.1版本使用
     *
     * @param slInfo
     */
    protected abstract void sendScreenLightness(EventLightnessV2.LightnessV2 slInfo);

    /**
     * 发送湿度校准值
     *
     * @param humCali
     */
    protected abstract void sendHumCali(float humCali);

    /**
     * 发送温度校准值
     *
     * @param fahOpen
     * @param temCali
     */
    protected abstract void sendTemCali(boolean fahOpen, float temCali);

    /**
     * 湿度校准值范围
     *
     * @return
     */
    protected abstract float[] getHumCaliRange();

    /**
     * 温度校准值范围
     *
     * @return
     */
    protected abstract float[] getTemCaliRange();

    /**
     * 发送pm2.5的预警范围和预警开关
     *
     * @param minAlarmPm25
     * @param maxAlarmPm25
     * @param alarmOn
     */
    protected abstract void sendPm25Alarm(int minAlarmPm25, int maxAlarmPm25, boolean alarmOn);

    /**
     * 设备报警间隔
     *
     * @param alarmInterval 分钟数
     */
    protected abstract void sendAlarmInterval(int alarmInterval);

    /**
     * 发送空气指令推送开关状态
     *
     * @param open
     */
    protected abstract void sendAirQs(boolean open);

    /**
     * Pm2.5的预警范围
     *
     * @return
     */
    protected abstract int[] getCo25Range();

    /**
     * 报警间隔
     */
    protected abstract int getAlarmInterval();

    /**
     * 获取屏幕亮度信息
     */
    protected abstract EventLightnessV2.LightnessV2 getSlInfo();

    /**
     * 是否华氏度单位展示
     *
     * @return
     */
    protected abstract boolean isFahOpen();

    /**
     * 发送温度预警范围和预警开关
     *
     * @param minAlarmTem
     * @param maxAlarmTem
     * @param alarmOn
     */
    protected abstract void sendTemAlarm(int minAlarmTem, int maxAlarmTem, boolean alarmOn);

    /**
     * 展示co2浓度等级弹窗
     */
    protected abstract void showCo2LevelSettingDialog();

    /**
     * 温度预警范围
     *
     * @return
     */
    protected abstract int[] getTemRange();

    /**
     * 发送湿度预警范围和预警开关
     *
     * @param minAlarmHum
     * @param maxAlarmHum
     * @param alarmOn
     */
    protected abstract void sendHumAlarm(int minAlarmHum, int maxAlarmHum, boolean alarmOn);

    /**
     * 湿度的预警范围
     *
     * @return
     */
    protected abstract int[] getHumRange();

    /**
     * 设置勿扰模式
     *
     * @param isOpen
     * @param startHour
     * @param startMinute
     * @param endHour
     * @param endMinute
     */
    protected abstract void sendDNDMode(boolean isOpen, int startHour, int startMinute, int endHour, int endMinute);

    protected abstract Ext4St getExt4Str();

    /**
     * sku
     *
     * @return
     */
    protected abstract String getSkuStr();

    protected int getGoodsType() {
        return 0;
    }

    private void updateEditing(boolean editing) {
        if (isDestroy()) {
            return;
        }
        binding.btnDeviceNameDone.setVisibility(editing ? View.VISIBLE : View.GONE);
        binding.btnDeviceNameCancel.setVisibility(editing ? View.VISIBLE : View.GONE);
        if (editing) {
            //编辑中
            StrUtil.setEditOneLine(binding.deviceNameEdit);
            binding.deviceNameEdit.setFocusable(true);
            binding.deviceNameEdit.setFocusableInTouchMode(true);
            if (!inEditing) {
                StrUtil.setSelectionEnd(binding.deviceNameEdit);
            }
            inEditing = true;
        } else {
            //显示中
            StrUtil.setEditMultiLine(binding.deviceNameEdit, 2);
            binding.deviceNameEdit.setFocusableInTouchMode(false);
            binding.deviceNameEdit.setFocusable(false);
            inEditing = false;
        }
    }

    protected void updateDeviceNameUi() {
        if (isDestroy()) {
            return;
        }
        binding.deviceNameEdit.setText(getDeviceName());
        updateEditing(false);
        InputUtil.hideInputMethod(binding.deviceNameEdit);
    }

    /**
     * 更新设备名称
     *
     * @param newDeviceName
     */
    protected abstract void toSaveDeviceName(String newDeviceName);

    /**
     * 获取设备名
     *
     * @return
     */
    protected abstract String getDeviceName();

    /**
     * 设备名称长度限制
     *
     * @return
     */
    protected int getDeviceNameInputLimit() {
        return 22;
    }

    /**
     * 解析传递的参数
     *
     * @param intent
     */
    protected abstract void parseIntentParams(Intent intent);

    /**
     * 跳转到更改wifi
     */
    protected abstract void toJump2ChangeWifi();

    /**
     * 删除数据的提示语
     *
     * @return
     */
    protected abstract String getDeleteDataHintStr();

    /**
     * 跳转到升级界面
     */
    protected abstract void toUpdateAc();

    /**
     * 是否有新版本
     */
    protected abstract boolean hadNewVersion();

    /**
     * 删除数据
     */
    protected abstract void toDeleteData();

    /**
     * 删除设备
     */
    protected abstract void toDeleteDevice();

    /**
     * 删除设备的提示语
     *
     * @return
     */
    protected abstract String getDeleteDeviceHintStr();

    protected void showLoading() {
        LoadingDialog.createDialog(this, 60 * 1000, null).setEventKey(TAG).show();
    }

    protected void hideLoading() {
        LoadingDialog.hideDialog(TAG);
    }

    @Override
    public void finish() {
        destroy();
        super.finish();
    }

    @Override
    public void onBackPressed() {
        destroy();
        super.onBackPressed();
    }

    private void destroy() {
        if (!destroy) {
            destroy = true;
            hideLoading();
            //关闭升级提示弹窗
            BleUpdateHintDialog.hideDialog(this.getClass().getName());
            ConfirmDialog.hideDialog();
            if (supManager != null) {
                supManager.onDestroy();
            }
            if (iUnBindM != null) {
                iUnBindM.onDestroy();
            }
            toRecycler();
        }
    }

    /**
     * 回收操作
     */
    protected abstract void toRecycler();

    /**
     * 更新step
     */
    protected void updateStep(int step) {
        if (isDestroy()) {
            return;
        }
        if (step != step_ble_suc) {
            closeBleUpdateHint();
        }
        if (step == step_ble_scanning) {
            binding.searching.setVisibility(View.VISIBLE);
        } else if (step == step_ble_disconnect) {
            binding.searching.setVisibility(View.GONE);
            binding.gapLine1.getRoot().setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.gapLine2.getRoot().setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.gapLine3.getRoot().setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.gapLine4.getRoot().setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.gapLine5.getRoot().setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.gapLine7.getRoot().setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.gapLine8.getRoot().setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.llUnit.setAlpha(isConnected() ? 1f : 0.5f);
            binding.tarvPm25AlarmRange.setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.temAlarmRange.setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.humAlarmRange.setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.llCo2LevelSet.setAlpha(isConnected() ? 1f : 0.5f);
            binding.llJiaoZhun.setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.tvCo2Calibration.setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.calibration.setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.tosvOtherSetV2.setVisibility(isConnected() ? View.VISIBLE : View.GONE);
            binding.thWarnSettingViewNew.setVisibility(View.VISIBLE);
            binding.thWarnSettingViewNew.getSoundLevelView().setAlpha(0.5f);
            binding.thWarnSettingViewNew.getTvSoundAlarmTime().setVisibility(View.GONE);
            binding.llCo2.setVisibility(View.GONE);
            binding.common.deleteDataContainer.setVisibility(View.GONE);
            binding.common.vLoadAllData.setVisibility(View.GONE);
            binding.llAlarmInterval.setAlpha(isConnected() ? 1f : 0.5f);
            binding.llNotDisturbMode.setAlpha(isConnected() ? 1f : 0.5f);
        } else if (step == step_ble_suc) {
            binding.searching.setVisibility(View.GONE);
            binding.gapLine1.getRoot().setVisibility(View.VISIBLE);
            binding.gapLine2.getRoot().setVisibility(View.VISIBLE);
            binding.gapLine3.getRoot().setVisibility(View.VISIBLE);
            binding.gapLine4.getRoot().setVisibility(View.VISIBLE);
            binding.gapLine5.getRoot().setVisibility(View.VISIBLE);
            binding.gapLine7.getRoot().setVisibility(View.VISIBLE);
            binding.gapLine8.getRoot().setVisibility(View.VISIBLE);
            binding.llUnit.setAlpha(1f);
            binding.thWarnSettingViewNew.setVisibility(View.VISIBLE);
            binding.thWarnSettingViewNew.getSoundLevelView().setAlpha(1f);
            binding.tarvPm25AlarmRange.setVisibility(View.VISIBLE);
            binding.temAlarmRange.setVisibility(View.VISIBLE);
            binding.humAlarmRange.setVisibility(View.VISIBLE);
            binding.llCo2.setVisibility(View.VISIBLE);
            binding.llCo2LevelSet.setAlpha(1f);
            binding.llJiaoZhun.setVisibility(View.VISIBLE);
            binding.tvCo2Calibration.setVisibility(View.VISIBLE);
            binding.calibration.setVisibility(View.VISIBLE);
            binding.tosvOtherSetV2.setVisibility(View.VISIBLE);
            binding.common.deleteDataContainer.setVisibility(View.VISIBLE);
            binding.common.vLoadAllData.setVisibility(View.VISIBLE);
            binding.llAlarmInterval.setAlpha(1f);
            binding.llNotDisturbMode.setAlpha(1f);
        }
    }

    private boolean isConnected() {
        return isIotConnect || BleController.getInstance().isConnected();
    }

    /**
     * 更新版本信息
     *
     * @param versionSoft
     * @param hadNewVersion
     */
    protected void updateVersion(String versionSoft, boolean hadNewVersion) {
        if (isDestroy()) {
            return;
        }
        binding.common.versionShowing.setText(versionSoft);
        binding.common.versionFlag.setVisibility(hadNewVersion ? View.VISIBLE : View.GONE);
        binding.common.versionArrow.setVisibility(hadNewVersion ? View.VISIBLE : View.GONE);
        if (!hadNewVersion) {
            closeBleUpdateHint();
        }
    }

    /**
     * 更新硬件版本
     *
     * @param versionHard
     */
    protected void updateHardVersion(String versionHard) {
        if (isDestroy()) {
            return;
        }
        binding.common.tvHardVersion.setText(versionHard);
    }

    @Override
    protected void onRPPermissionGranted() {
        super.onRPPermissionGranted();
        updateStep(step_ble_scanning);
        toConnectBle();
    }

    /**
     * 连接蓝牙设备
     */
    protected abstract void toConnectBle();

    protected void showUpdateHintDialog(String device, CheckVersion checkVersion) {
        if (checkVersion == null) {
            return;
        }
        String sku = getSkuStr();
        String newSoftVersion = checkVersion.getVersionSoft();
        String newHardVersion = checkVersion.getVersionHard();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventShowUpdateHintDialog() sku = " + sku + " ; device = " + device);
            LogInfra.Log.i(TAG, "onEventShowUpdateHintDialog() newSoftVersion = " + newSoftVersion + " ; newHardVersion = " + newHardVersion);
        }
        if (TextUtils.isEmpty(sku) || TextUtils.isEmpty(device) || TextUtils.isEmpty(newSoftVersion) || TextUtils.isEmpty(newHardVersion)) {
            return;
        }
        boolean needShowUpdateHint = UpdateHint4VersionConfig.read().needShowUpdateHint(sku, device, newSoftVersion, newHardVersion);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventShowUpdateHintDialog() needShowUpdateHint = " + needShowUpdateHint);
        }
        if (!needShowUpdateHint) {
            return;
        }
        //记录升级提示
        UpdateHint4VersionConfig.read().recordUpdateHint(sku, device, newSoftVersion, newHardVersion);
        showingBleUpdateHintDialog = true;
        BleUpdateHintDialog.showDialog(this, sku, this::toUpdateAc, this.getClass().getName());
    }

    protected void closeBleUpdateHint() {
        if (showingBleUpdateHintDialog) {
            showingBleUpdateHintDialog = false;
            //关闭升级提示弹窗
            BleUpdateHintDialog.hideDialog(this.getClass().getName());
        }
    }

    @Override
    public void onClick(View v) {
        if (v == binding.btnBack) {
            v.setEnabled(false);
            finish();
        } else if (v == binding.deviceNameEdit) {
            if (ClickUtil.getInstance.clickQuick()) {
                return;
            }
            binding.deviceNameEdit.setFocusable(true);
            binding.deviceNameEdit.setFocusableInTouchMode(true);
            binding.deviceNameEdit.requestFocus();
            InputUtil.showInputMethod(binding.deviceNameEdit);
            updateEditing(true);
        } else if (v == binding.btnDeviceNameDone) {
            if (ClickUtil.getInstance.clickQuick()) {
                return;
            }
            String newDeviceName = StrUtil.trim4String(binding.deviceNameEdit.getText().toString());
            //空字符则提示输入名称
            if (TextUtils.isEmpty(newDeviceName)) {
                toast(R.string.name_lights_hint);
                return;
            }
            boolean isValidName = StrUtil.isInputCheck(newDeviceName, getDeviceNameInputLimit());
            if (isValidName) {
                toSaveDeviceName(newDeviceName);
            } else {
                toast(R.string.invalid_input);
            }
        } else if (v == binding.btnDeviceNameCancel) {
            if (ClickUtil.getInstance.clickQuick()) {
                return;
            }
            updateDeviceNameUi();
        } else if (v == binding.acContainer) {
            if (ClickUtil.getInstance.clickQuick()) {
                return;
            }
            InputUtil.hideInputMethod(binding.deviceNameEdit);
        } else if (v == binding.btnDeleteInside) {
            if (ClickUtil.getInstance.clickQuick()) {
                return;
            }
            ConfirmDialog.showConfirmDialog(this, getDeleteDeviceHintStr(), ResUtil.getString(R.string.no), ResUtil.getString(R.string.yes), this::toDeleteDevice);
        } else if (v == binding.wifiContainer) {
            if (ClickUtil.getInstance.clickQuick()) {
                return;
            }
            toJump2ChangeWifi();
        } else if (v == binding.common.vLoadAllData) {
            if (ClickUtil.getInstance.clickQuick()) {
                return;
            }
            loadAllThData();
        } else if (v == binding.common.deleteDataContainer) {
            if (ClickUtil.getInstance.clickQuick()) {
                return;
            }
            selPeriodDialog.show(getGoodsType(), getSkuStr(), selPeriodType -> {
                if (selPeriodType == 4) {
                    ConfirmDialog.showConfirmDialog(this, getDeleteDataHintStr(), ResUtil.getString(R.string.no), ResUtil.getString(R.string.yes), this::toDeleteData);
                } else {
                    clearDataByType(selPeriodType);
                }
            }, true);
        } else if (v == binding.common.versionContainer) {
            if (ClickUtil.getInstance.clickQuick()) {
                return;
            }
            if (hadNewVersion()) {
                if (!BleController.getInstance().isBlueToothOpen()) {
                    toast(getString(R.string.bluetooth_unable_des));
                    return;
                }
                toUpdateAc();
            }
        } else if (v == binding.llCo2LevelSet) {// 设置二氧化碳浓度等级
            if (!isConnected()) {
                return;
            }
            showCo2LevelSettingDialog();
        } else if (v == binding.tvCo2GradeQuest) {// Co2浓度等级弹窗
            showCo2GradeTipsDialog();
        } else if (v == binding.tvCo2Calibration) {// C02校准
            startCo2ManualCalibration();
        } else if (v == binding.ivTuValueIcon45106S) {// 设置温度单位
            if (!isConnected()) {
                return;
            }
            //温度单位切换
            this.tuType = this.tuType == CommandTemUnit.CEN ? CommandTemUnit.FAH : CommandTemUnit.CEN;
            sendTempUnit(tuType);
        } else if (v == binding.tvCo2DensityQuest) {// Co2浓度通知提示弹窗
            showCo2LevelTipsDialog();
        } else if (v == binding.ivCo2DensitySwitch) {// Co2浓度通知请求
            boolean onSwitch = !binding.ivCo2DensitySwitch.isSelected();
            sendAirQs(onSwitch);
        } else if (v == binding.tvCalibrationQuestion) {
            IntroUtils.INSTANCE.showCalibrationIntro(this, getSkuStr());
        } else if (v == binding.ivCo2AlarmInterval || v == binding.tvAlarmIntervalTime) {
            // 报警间隔
            if (binding.llAlarmInterval.getAlpha() != 1) {
                return;
            }
            int currLevel = 0;
            for (int i = 0; i < TimeLevelDialog.Companion.getTimeLevels().length; i++) {
                if (getAlarmInterval() == TimeLevelDialog.Companion.getTimeLevels()[i]) {
                    currLevel = i;
                }
            }
            mTimeLevelDialog.toSetVolume(currLevel, selectVolumeGear -> {
                int minute = TimeLevelDialog.Companion.getTimeLevels()[selectVolumeGear];
                String text = ResUtil.getStringFormat(R.string.minute_pattern, minute);
                binding.tvAlarmIntervalTime.setText(text);
                sendAlarmInterval(minute);
            });
        } else if (v == binding.tvNotDisturbModeTime) {
            // 勿扰模式
            if (binding.llNotDisturbMode.getAlpha() != 1) {
                return;
            }
            Ext4St ext4St = getExt4Str();
            Dialog4SelectTimeRange.showTimeDialogV2(this,
                    ext4St.getDndStartHour(),
                    ext4St.getDndStartMinute(),
                    ext4St.getDndEndHour(),
                    ext4St.getDndEndMinute(),
                    getString(R.string.time_select_dialog_title),
                    true, (startHour, startMinute, endHour, endMinute) -> {
                        String result = getTimeStr(startHour) + ":" + getTimeStr(startMinute) + "-" + getTimeStr(endHour) + ":" + getTimeStr(endMinute);
                        binding.tvNotDisturbModeTime.setText(result);
                        sendDNDMode(true, startHour, startMinute, endHour, endMinute);
                    });
        } else if (v == binding.ivNotDisturbMode) {
            if (binding.llNotDisturbMode.getAlpha() != 1) {
                return;
            }
            if (getExt4Str().getDndIsOpen()) {
                binding.flDnDTime.setVisibility(View.VISIBLE);
            } else {
                binding.flDnDTime.setVisibility(View.GONE);
            }
            if (binding.ivNotDisturbMode.isSelected()) {
                binding.ivNotDisturbMode.setSelected(false);
            } else {
                binding.ivNotDisturbMode.setSelected(true);
                Ext4St ext4St = getExt4Str();
                Dialog4SelectTimeRange.showTimeDialogV2(this,
                        ext4St.getDndStartHour(),
                        ext4St.getDndStartMinute(),
                        ext4St.getDndEndHour(),
                        ext4St.getDndEndMinute(),
                        getString(R.string.time_select_dialog_title),
                        true, (startHour, startMinute, endHour, endMinute) -> {
                            String result = getTimeStr(startHour) + ":" + getTimeStr(startMinute) + "-" + getTimeStr(endHour) + ":" + getTimeStr(endMinute);
                            binding.tvNotDisturbModeTime.setText(result);
                            sendDNDMode(true, startHour, startMinute, endHour, endMinute);
                        });
            }
        } else if (v == binding.tvCo2AlarmInterval) {
            showAlarmIntervalTipsDialog();
        } else if (v == binding.ivNotDisturbModeTips) {
            showWuYaoModeDialog();
        }
    }

    public String getTimeStr(int time) {
        String str;
        if (time == 0) {
            str = "00";
        } else if (time < 10) {
            str = "0" + time;
        } else {
            str = String.valueOf(time);
        }
        return str;
    }

    private TimeLevelDialog mTimeLevelDialog;

    private ActivityResultLauncher<Intent> startBForResult;

    private void startCo2ManualCalibration() {
        Intent intent = new Intent(this, Co2CalibrationActivity.class);
        intent.putExtra(INTENT_KEY_SKU, getSkuStr());
        intent.putExtra(INTENT_KEY_DEVICE, getDevice());
        intent.putExtra(INTENT_KEY_IS_IOT_CONNECT, isIotConnect);
        startBForResult.launch(intent);
    }

    private void showCo2GradeTipsDialog() {
        ConfirmDialogV5.showConfirmDialog(
                this,
                ResUtil.getString(R.string.b2light_hint_title),
                ResUtil.getString(com.govee.ui.R.string.h5140_co2_grade_dialog_tips),
                ResUtil.getString(R.string.dbgw_i_know),
                () -> {
                });
    }

    private void showCo2LevelTipsDialog() {
        ConfirmDialogV5.showConfirmDialog(
                this,
                ResUtil.getString(R.string.b2light_hint_title),
                ResUtil.getString(com.govee.ui.R.string.h5140_co2_levle_push_tips),
                ResUtil.getString(R.string.dbgw_i_know),
                () -> {
                });
    }

    private void showAlarmIntervalTipsDialog() {
        ConfirmDialogV5.showConfirmDialog(
                this,
                ResUtil.getString(R.string.h5140_alarm_interval),
                ResUtil.getString(com.govee.ui.R.string.h5140_alarm_interval_tips),
                ResUtil.getString(R.string.dbgw_i_know),
                () -> {
                });
    }

    private void showWuYaoModeDialog() {
        ConfirmDialogV5.showConfirmDialog(
                this,
                ResUtil.getString(R.string.h5140_not_disturb_mode),
                ResUtil.getString(com.govee.ui.R.string.h5140_not_d9sturb_mode_tips),
                ResUtil.getString(R.string.dbgw_i_know),
                () -> {
                });
    }

    /**
     * 拉取全部数据(子类重写实现功能)
     */
    public void loadAllThData() {
    }

    /**
     * 按数据段类型清理本地数据库数据(子类重写实现功能)
     *
     * @param selectType
     */
    public void clearDataByType(int selectType) {
    }

    @Override
    protected boolean needLocationPre4iBeaconDevice() {
        return true;
    }

    protected Request4Setting getLocalSettings() {
        return null;
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        binding.thWarnSettingViewNew.onActivityResult(requestCode, resultCode, data);
        //binding.twsvWarnSettingNew4ThSetting.onActivityResult(requestCode, resultCode, data)
    }
}