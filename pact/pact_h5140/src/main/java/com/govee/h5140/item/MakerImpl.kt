package com.govee.h5140.item

import com.govee.base2home.sku.IMaker
import com.govee.base2home.sku.ISkuItem

/**
 * <AUTHOR>
 * @date created on 2022/5/25
 * @description 添加设备时点击5106相关skuItem的操作实现类
 */
class MakerImpl : IMaker {

    private val makers: MutableList<ISkuItem> = ArrayList()

    constructor() {
        makers.add(SkuItem())
    }

    override fun getSupportMakers(): List<ISkuItem> {
        return makers
    }
}