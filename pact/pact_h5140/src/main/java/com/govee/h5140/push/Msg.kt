package com.govee.h5140.push

import android.text.TextUtils
import androidx.annotation.Keep

/**
 * <AUTHOR>
 * @date created on 2022/6/10
 * @description 5106推送消息的实体类
 */
@Keep
class Msg {

    var deviceName: String = ""
        private set
    var message: String = ""
        private set

    constructor() {}

    constructor(contentTitle: String, message: String) {
        this.deviceName = contentTitle
        this.message = message
    }

    val isInvalidMsg: Boolean
        get() = TextUtils.isEmpty(deviceName) || TextUtils.isEmpty(message)
}