package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取deviceId的指令后，将读取结果通过EventBus向外发布
 */
class EventDeviceId : AbsControllerEvent {

    constructor(result: <PERSON><PERSON><PERSON>, write: <PERSON>olean, commandType: Byte, proType: Byte) : super(result, write, commandType, proType, !write)

    var deviceId: String? = null

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventDeviceId(false, write, commandType, proType))
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, deviceId: String?) {
            val event = EventDeviceId(true, write, commandType, proType)
            event.deviceId = deviceId
            EventBus.getDefault().post(event)
        }
    }
}