package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取湿度预警相关信息的事件
 */
class EventHumWarning(result: <PERSON>olean, write: <PERSON>olean, commandType: Byte, proType: Byte) :
        AbsControllerEvent(result, write, commandType, proType, !write) {

    var openWarning = false
    var minHum = 0
    var maxHum = 0

    companion object {
        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte, openWarning: Boolean, minHum: Int, maxHum: Int) {
            val event = EventHumWarning(result, true, commandType, proType)
            event.openWarning = openWarning
            event.minHum = minHum
            event.maxHum = maxHum
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: <PERSON>ole<PERSON>, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventHumWarning(false, write, commandType, proType))
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, openWarning: Boolean, minHum: Int, maxHum: Int) {
            val event = EventHumWarning(true, write, commandType, proType)
            event.openWarning = openWarning
            event.minHum = minHum
            event.maxHum = maxHum
            EventBus.getDefault().post(event)
        }
    }
}