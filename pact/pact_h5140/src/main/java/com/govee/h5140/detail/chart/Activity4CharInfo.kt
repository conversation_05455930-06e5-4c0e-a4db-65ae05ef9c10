package com.govee.h5140.detail.chart

import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.HandlerThread
import android.os.Looper
import android.os.Message
import android.text.TextUtils
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import androidx.activity.viewModels
import androidx.lifecycle.ViewModelProvider
import com.bumptech.glide.Glide
import com.google.android.gms.common.util.CollectionUtils
import com.govee.base2home.BaseRPNetActivity
import com.govee.base2home.Constant
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.device.DeviceM
import com.govee.base2home.device.EventDeviceTopic
import com.govee.base2home.iot.AbsCmd
import com.govee.base2home.iot.Cmd4Status
import com.govee.base2home.iot.Iot
import com.govee.base2home.iot.IotConnectEvent
import com.govee.base2home.iot.IotTransactions
import com.govee.base2home.iot.protype.v2.IotMsgEventV2
import com.govee.base2home.iot.protype.v2.IotMsgV2
import com.govee.base2home.iot.share.ShareController
import com.govee.base2home.main.DeviceListConfig
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.reform4dbgw.router.RouterRuler
import com.govee.base2home.sku.ShopGuideM
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.temUnit.TemperatureUnitType
import com.govee.base2home.theme.ThemeM
import com.govee.base2home.ui.showguide.ShowGuideView
import com.govee.base2home.update.IUpdateNet
import com.govee.base2home.update.UpdateHint4VersionConfig
import com.govee.base2home.update.download.CheckVersion
import com.govee.base2home.update.net.DeviceUpdateRequest
import com.govee.base2home.update.net.DeviceUpdateResponse
import com.govee.base2home.util.ClickUtil
import com.govee.base2home.util.IntroUtils
import com.govee.base2home.util.StrUtil
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.gone
import com.govee.base2kt.ext.setVisibility
import com.govee.base2kt.ext.toJson
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.base2kt.utils.IntentUtils
import com.govee.base2light.ac.diy.v1.EventDiyShowLoading.Companion.showLoading
import com.govee.base2light.pact.iot.IIotOpResultV1
import com.govee.base2light.synctriggers.h5106.Command4H5106
import com.govee.base2newth.AbsSingleController
import com.govee.base2newth.ThConsV1
import com.govee.base2newth.chart.ChartController4Thp
import com.govee.base2newth.chart.ChartVisibleConfig
import com.govee.base2newth.chart.DataGroup
import com.govee.base2newth.chart.IntervalType
import com.govee.base2newth.chart.TimeUtil
import com.govee.base2newth.chart.newchart.ChartController4New
import com.govee.base2newth.chart.newchart.ChartController4New.ChartListener
import com.govee.base2newth.chart.newchart.ConfigParams
import com.govee.base2newth.chart.newchart.DbDataVm4New
import com.govee.base2newth.chart.newchart.Event4ChangeTemUnit
import com.govee.base2newth.chart.order.Event4ChangeChartOrder
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.data.EventDataProgress
import com.govee.base2newth.data.EventDataResult
import com.govee.base2newth.data.H5140C02WarnConfig
import com.govee.base2newth.data.IBleOp4DetailResult
import com.govee.base2newth.data.IServiceDataOp
import com.govee.base2newth.data.ServiceDataOp4Thp
import com.govee.base2newth.data.THMemoryUtil
import com.govee.base2newth.data.event.DetailFinishEvent
import com.govee.base2newth.db.DbController
import com.govee.base2newth.db.DbDataViewMode4Thp
import com.govee.base2newth.db.TemHumPm
import com.govee.base2newth.deviceitem.LastData
import com.govee.base2newth.net.IThNet
import com.govee.base2newth.net.RequestUpdateLatestData
import com.govee.base2newth.net.ResponseUpdateLatestData
import com.govee.base2newth.other.Config4DeviceChartOrder
import com.govee.base2newth.other.Config4LastThValue
import com.govee.base2newth.other.Event4HasClearCache
import com.govee.base2newth.other.Event4LoadAllData
import com.govee.base2newth.other.Event4LoadAllData.Companion.sendEvent
import com.govee.base2newth.other.SyncTemUnitUtil.syncTemUnit
import com.govee.base2newth.setting.EventAfterClearDataSuc
import com.govee.base2newth.setting.EventNameChange
import com.govee.base2newth.setting.EventNewVersion
import com.govee.base2newth.update.UpdateSucEvent
import com.govee.bind.SafeBindMgr
import com.govee.bind.bean.ConfirmGidReq.Companion.createConfirmGidReq
import com.govee.ble.BleController
import com.govee.h5140.Constants5140
import com.govee.h5140.Constants5140.makeAcIntentBundle
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.EventHeart
import com.govee.h5140.ble.H5140HeartSender
import com.govee.h5140.ble.ThpBleCommManager
import com.govee.h5140.ble.ThpBleCommManager.Companion.getInstance
import com.govee.h5140.ble.command.CommandTemUnit
import com.govee.h5140.ble.command.CommandUploadLastData
import com.govee.h5140.ble.event.EventSetTemUnit
import com.govee.h5140.ble.event.EventTempUnit
import com.govee.h5140.ble.event.Jump2SettingEvent
import com.govee.h5140.ble.event.UpdateThCo2Event
import com.govee.h5140.databinding.H5140ActivityChartInfoBinding
import com.govee.h5140.databinding.H5140ThnewChartNew4Co2Binding
import com.govee.h5140.detail.Activity4ExportData
import com.govee.h5140.detail.setting.Activity4Setting
import com.govee.h5140.detail.square.Ac4ThSquareChartNew
import com.govee.h5140.iotop.Cmd5140
import com.govee.h5140.iotop.CmdPtReal
import com.govee.h5140.iotop.IotOpManager
import com.govee.h5140.iotop.IotParseUtils
import com.govee.h5140.item.Support.getMinValidTimeMills
import com.govee.h5140.update.Activity4DeviceUpdate
import com.govee.kt.hideLoading
import com.govee.mvvm.network.NetworkUtil
import com.govee.thnew.add.AddInfo
import com.govee.thnew.ble.event.ReleaseVMEvent
import com.govee.thnew.databinding.ThnewChartNew4DewPointBinding
import com.govee.thnew.databinding.ThnewChartNew4HumBinding
import com.govee.thnew.databinding.ThnewChartNew4TemBinding
import com.govee.thnew.databinding.ThnewChartNew4VpdBinding
import com.govee.thnew.gidsafe.GidSafeManager
import com.govee.thnew.ui.Vm4ThOpManager
import com.govee.thnew.ui.compare.Vm4MultiCompare
import com.govee.ui.R
import com.govee.ui.dialog.BleUpdateHintDialog
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.Cache
import com.ihoment.base2app.broadcast.Event4DynamicBroadcastReceiver
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.infra.SafeLog.Companion.i
import com.ihoment.base2app.network.ErrorResponse
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.network.Transactions
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.CaughtRunnable
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import com.ihoment.base2app.util.ThreadPoolUtil
import com.kk.taurus.playerbase.utils.NetworkUtils
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2022/06/01
 * @description 5106设备图表信息页
 */
class Activity4CharInfo : BaseRPNetActivity(), ShopGuideM.OnShowListener, View.OnClickListener {

    private val binding by lazy { H5140ActivityChartInfoBinding.inflate(layoutInflater) }

    private val newHumBinding by lazy {
        ThnewChartNew4HumBinding.inflate(layoutInflater)
    }
    private val newCo2Binding by lazy {
        H5140ThnewChartNew4Co2Binding.inflate(layoutInflater)
    }
    private val newTemBinding by lazy {
        ThnewChartNew4TemBinding.inflate(layoutInflater)
    }

    private val newDpBinding by lazy {
        ThnewChartNew4DewPointBinding.inflate(layoutInflater)
    }
    private val newVpdBinding by lazy {
        ThnewChartNew4VpdBinding.inflate(layoutInflater)
    }

    /**
     * 图表展示控制类
     */
    private lateinit var chartOp4Th: ChartController4New

    /**
     * 数据展示的vm
     */
    private val vm4ChartData: DbDataVm4New by viewModels()

    private val vm4Mc by lazy {
        ViewModelProvider(this)[Vm4MultiCompare::class.java].apply {
            init(this@Activity4CharInfo, GoodsType.GOODS_TYPE_VALUE_H5140, sku, device, bleAddress)
        }
    }

    /**
     * appBarLayout的实时滑动距离
     */
    @Volatile
    private var ablVerticalOffset = 0

    private var shopGuideM: ShopGuideM? = null
    private var device: String = ""
    private var sku: String = ""
    private var bleAddress: String = ""
    private var deviceName: String = ""
    private lateinit var viewMode: DbDataViewMode4Thp
    private val lastDataList: MutableList<TemHumPm> = ArrayList()
    private lateinit var ext: Ext4Ci
    private var destroy = false

    /**
     * dataGroup不为null表明处于默认标签下
     */
    private var dataGroup: DataGroup? = null
    private var chartOp: ChartController4Thp? = null
    private var handlerThread: HandlerThread? = null
    private var backgroundHandler: Handler? = null
    private lateinit var bleOp4Detail: BleOp4ChartInfo
    private lateinit var serviceData: IServiceDataOp

    /**
     * 标志位；是否已经展示过本地数据
     */
    private var hadShowLocal = false

    /**
     * 图表数据是否准备中
     */
    private var lastValidDataLength: Long = 0
    private var lastDataTime: Long = 0
    private var lastSyncTime: Long = 0

    /**
     * 下拉刷新参数
     */
    private var needShowFresh = false
    private var lastStartMoveY = 0
    private var lastDownY = 0
    private var fullFreshShow = false

    /**
     * 是否正在显示ble升级提示弹窗
     */
    private var showingBleUpdateHintDialog = false
    private var checkVersion: CheckVersion? = null
    private var waitingShowUpdateHintDialog = false
    private var topic: String = ""

    private var softVersion = ""
    private var hardVersion = ""

    //5.9.0-->实时信息展示相关
    private var refreshTime = 0L
    private var realTem = 0
    private var realHum = 0
    private var realCo2 = 0
    private var initPageTime = 0L

    private val mHandler: Handler = object : Handler(Looper.getMainLooper()) {
        override fun handleMessage(msg: Message) {
            super.handleMessage(msg)
            val what = msg.what
            doWhat(what)
        }
    }

    companion object {
        private const val VERTICAL_DIR_PULL_DOWN = 120

        /**
         * 设置超时时间
         */
        private const val TIME_OUT_MILLS = 60 * 1000L

        /**
         * 刷新界面的高度占屏幕宽度百分比
         */
        private const val FRESH_HEIGHT_PERCENT_SCREEN_WIDTH = 0.137f
        private const val what_update_sync_time = 100
        private const val what_check_fresh_full_showing_type = 101
        private const val what_check_ble_load_data = 102
        var needRefreshChart = true
        var deleteAllData = true

        /**
         * 跳转到控制页
         */
        fun jump2AdjustAc(
            context: Context,
            goodsType: Int,
            sku: String,
            device: String,
            deviceName: String,
            bleAddress: String,
            temCali: Int,
            humCali: Int,
            wifiMac: String,
            softVersion: String,
            hardVersion: String,
            topic: String,
        ) {
            val bundle = makeAcIntentBundle(
                goodsType,
                sku,
                device,
                deviceName,
                bleAddress,
                temCali,
                humCali,
                wifiMac,
                softVersion,
                hardVersion,
                topic,
            )
            JumpUtil.jump(context, Activity4CharInfo::class.java, bundle)
        }
    }

    override fun bindView(layoutId: Int): Boolean {
        setContentView(binding.root)
        return true
    }

    private fun initBindExtData() {
        val addInfo = AddInfo()
        addInfo.sku = sku
        addInfo.device = device
        addInfo.goodsType = ext.goodsType
        addInfo.deviceName = deviceName
        addInfo.address = ext.bleAddress
        addInfo.bleSoftVersion = softVersion
        addInfo.bleHardVersion = hardVersion
        i("getDeviceInfo") { "initData->bindExt=${JsonUtil.toJson(addInfo)}" }
        Vm4ThOpManager.init(addInfo, null, Vm4ThOpManager.INIT_FROM_OLD_DETAIL)
    }

    private var bleConnectState = false

    override fun onCreate(savedInstanceState: Bundle?) {
        //ViewBinding
        super.onCreate(savedInstanceState)
        handlerThread = HandlerThread(TAG)
        handlerThread!!.start()
        backgroundHandler = Handler(handlerThread!!.looper)
        //将内容控件根据顺序添加至壳控件中
        changeChartOrder()
        //获取上个页面传递过来的信息
        val intent = intent
        val goodsType =
            intent.getIntExtra(Constants5140.intent_ac_goodsType, GoodsType.GOODES_TYPE_NO_SUPPORT)
        sku = intent.getStringExtra(Constants5140.intent_ac_sku)!!
        device = intent.getStringExtra(Constants5140.intent_ac_device)!!
        deviceName = intent.getStringExtra(Constants5140.intent_ac_deviceName)!!
        bleAddress = intent.getStringExtra(Constants5140.intent_ac_bleAddress)!!
        val wifiMac = intent.getStringExtra(Constants5140.intent_ac_key_wifi_mac)
        val temCali = intent.getIntExtra(Constants5140.intent_ac_tem_cali, 0)
        val humCali = intent.getIntExtra(Constants5140.intent_ac_hum_cali, 0)
        softVersion = intent.getStringExtra(Constants5140.intent_ac_soft_version)!!
        hardVersion = intent.getStringExtra(Constants5140.intent_ac_hard_version)!!
        ext = Ext4Ci(goodsType, sku, device, deviceName, bleAddress, temCali, humCali)
        ext.wifiMac = wifiMac!!
        ext.versionSoft = softVersion
        ext.versionHard = hardVersion
        topic = intent.getStringExtra(Constants5140.intent_ac_topic)!!
        THMemoryUtil.getInstance().curDeviceBleAddress = bleAddress
        //统计进入详情页
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.into_detail, sku)
        //适配insetTop
        adaptationInsetTop(com.govee.h5140.R.id.top_flag, AppUtil.getScreenWidth() * 61 / 750)
        //构建数据库表
        DbController.getInstance.createDbTable(sku, device)
        //构建详情页ble通信
        SafeLog.d(TAG) { "ext=${ext.toJson()}" }
        bleOp4Detail = BleOp4ChartInfo(ext, object : IBleOp4DetailResult {
            override fun bleUnable() {
                SafeLog.d(TAG) { "------->bleUnable" }
                bleConnectState = false
                THMemoryUtil.getInstance().isTrendChartReading = false
                doCheckDataStep()
                checkBleSwitchState()
            }

            override fun bleDisconnect() {
                SafeLog.d(TAG) { "------->bleDisconnect" }
                bleConnectState = false
                THMemoryUtil.getInstance().isTrendChartReading = false
                doCheckDataStep()
                //自然断连的情况下，每隔一段时间就尝试去重连
                mHandler.removeCallbacks(reconnectBleRunnable)
                mHandler.postDelayed(reconnectBleRunnable, TIME_OUT_MILLS)
            }

            override fun inBleComming() {
                SafeLog.d(TAG) { "------->inBleComming" }
                bleConnectState = true
            }

            override fun dataLoading() {
                SafeLog.d(TAG) { "------->dataLoading" }
                bleConnectState = true
                mHandler.removeCallbacks(reconnectBleRunnable)
                mHandler.sendEmptyMessage(what_check_fresh_full_showing_type)
            }

            override fun infoOver() {
                SafeLog.d(TAG) { "------->infoOver" }
                bleConnectState = true
                //检测是否有升级
                toCheckVersion()
                SafeLog.d(TAG) { "读取设备信息完毕，ext=${ext.toJson()}" }
                if (fullFreshShow) {
                    //若当前已拉取完服务器数据，则读取ble数据
                    mHandler.sendEmptyMessage(what_check_ble_load_data)
                }
                DeviceListConfig.read().getDeviceByKey(sku, device)?.let {
                    SafeBindMgr.checkDetailPageGid(
                        GidSafeManager(ble),
                        createConfirmGidReq(it, ext.versionHard, ext.versionSoft)
                    )
                }
            }

            override fun showChart() {
                dataOver(hadReadBleData = false, readOver = false)
                (getInstance().heart as H5140HeartSender).setNeedParseHeartPackage(false)
            }
        }, object : IBleOpOver {
            override fun bleOpOver() {
                THMemoryUtil.getInstance().isTrendChartReading = false
                if (fullFreshShow) {
                    //若当前已拉取完服务器数据，则读取ble数据
                    mHandler.sendEmptyMessage(what_check_ble_load_data)
                }
            }

            override fun ignoreBleReadData() {
                THMemoryUtil.getInstance().isTrendChartReading = false
                doCheckDataStep()
            }
        })
        (bleOp4Detail as BleOp4ChartInfo).inComm(true)
        //构建服务器数据加载
        serviceData = ServiceDataOp4Thp(this, ext.sku, ext.device, getMinValidTimeMills(goodsType))
        (serviceData as ServiceDataOp4Thp).setOpResult { allOver: Boolean ->
            //服务器数据加载完成，尝试通过蓝牙加载数据
            mHandler.sendEmptyMessage(what_check_ble_load_data)
            if (!isBluetoothEnabled(this)) {//
                SafeLog.d("temp_h5140_chart") { "如果蓝牙没打开，则直接刷新UI" }
                doCheckDataStep()
                updateFresh(false)
            }
        }
        EventBus.getDefault().postSticky(serviceData)
        //初始化ui
        updateTitleTv()
        updateTemUnit(TemUnitConfig.read().isTemUnitFah(sku, device))
        updateSyncTime()
        initViewMode()
        initChart()
        //请求权限
        requestBtRp()
        shopGuideM = ShopGuideM(this, sku, device, this)
        ShowGuideView.addShowGuide(this@Activity4CharInfo, binding.btnGuide, sku, device)
        setListener()
        val isShareDev = ShareController.queryIfSharedDev(sku, device)
        binding.tvExportData4ThDetail.visibleByBoolean(!isShareDev)
        if (isShareDev) {
            binding.clExportContainer4ThDetail.background = null
            binding.clExportContainer4ThDetail.isEnabled = false
        }
        binding.thstvContainer4H5106Detail.setOnSwitchTimeListener { isToNext ->
            if (chartOp != null) {
                chartOp!!.setOneStage(isToNext)
            }
        }
        //初始化实时温湿度信息展示控件
        startIotHeart()
        binding.thRealInfo.init(sku, device)
        initPageTime = System.currentTimeMillis()
    }

    private val temUnitListener = object : H5140TempUnitHelper.TemUnitChangeListener {
        override fun temUnitChange() {
            if (isDestroy) {
                return
            }
            val isFah = TemUnitConfig.read().isTemUnitFah(sku, device)
            updateTemUnit(isFah)
            syncTemUnit(Transactions().createTransaction(), sku, isFah)
        }
    }

    private fun setListener() {
        binding.btnBack.setOnClickListener(this)
        binding.btnChart.setOnClickListener(this)
        binding.btnSetting.setOnClickListener(this)
        binding.groupHour.setOnClickListener(this)
        binding.groupDay.setOnClickListener(this)
        binding.groupWeek.setOnClickListener(this)
        binding.groupMonth.setOnClickListener(this)
        binding.groupYear.setOnClickListener(this)
        binding.btnGuide.setOnClickListener(this)
        binding.btnGuideClose.setOnClickListener(this)
        binding.clExportContainer4ThDetail.setOnClickListener(this)
        binding.tvHistoryCompare4ThDetail.setOnClickListener(this)
        binding.tvMultiDeviceCompare4ThDetail.setOnClickListener(this)
        binding.ablTopContentContainer.addOnOffsetChangedListener { _, verticalOffset ->
            ablVerticalOffset = verticalOffset
        }
        newDpBinding.ivDpSwitch4ThDetailNew.clickDelay { dpSwitch() }
        newDpBinding.ivDpSwitch14ThDetailNew.clickDelay { dpSwitch() }
        newVpdBinding.ivVpdSwitch4ThDetailNew.clickDelay { vpdSwitch() }
        newVpdBinding.ivVpdSwitch14ThDetailNew.clickDelay { vpdSwitch() }

        newDpBinding.ivDpIntroIcon4ThDetailNew.setOnClickListener(this)
        newDpBinding.ivDpIntroIcon14ThDetailNew.setOnClickListener(this)
        newVpdBinding.ivVpdIntroIcon4ThDetailNew.setOnClickListener(this)
        newVpdBinding.ivVpdIntroIcon14ThDetailNew.setOnClickListener(this)
        H5140TempUnitHelper.addListener(temUnitListener)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4LoadAllDataStart(event: Event4LoadAllData) {
        if (event.step == Event4LoadAllData.LOAD_TH_DATA_TO_START) {
            loadServiceData()
            if (!NetworkUtil.isNetworkAvailable(BaseApplication.getContext())) {
                sendEvent(Event4LoadAllData.LOAD_TH_DATA_FINISH, true)
                return
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4ClearCache(event: Event4HasClearCache) {
        showChart()
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onHeartEvent(event: EventHeart) {
        refreshTime = System.currentTimeMillis()
        realTem = event.tem
        realHum = event.hum
        realCo2 = event.pm25
        runOnUiThread(object : CaughtRunnable() {
            override fun runSafe() {
                EventBus.getDefault().post(UpdateThCo2Event(realTem, realHum, realCo2, refreshTime))
                binding.thRealInfo.updateRealInfo(refreshTime, realTem, realHum, realCo2)
            }
        })
    }

    /**
     * 蓝牙断连后，每隔一段时间就去尝试重连
     */
    private val reconnectBleRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            //重现连接设备
            ble.connectBle(bleAddress)
        }
    }

    @Volatile
    private var isResume = false

    override fun onResume() {
        super.onResume()
        isResume = true
        refreshDewPView()
        refreshVpdView()
        ble.inStep4Details(true)
        bleOp4Detail.inComm(true)
        //更新图表的参数配置
        val warnRange = WarnConfig.read().queryWarningRangeByKey(ext.sku, ext.device)
        if (warnRange != null) {
            ext.humCali = warnRange.humCali
            ext.temCali = warnRange.temCali
            newCo2Binding.ttcPm25Chart4ThDetailNew.updateWarn(
                warnRange.co2Min,
                warnRange.co2Max
            )
            newTemBinding.ttcTemChart4ThDetailNew.updateWarn(warnRange.temMin, warnRange.temMax)
            newHumBinding.ttcHumChart4ThDetailNew.updateWarn(warnRange.humMin, warnRange.humMax)
        } else {
            newCo2Binding.ttcPm25Chart4ThDetailNew.updateWarn(
                ThConsV1.CO2_MIN_VALUE,
                ThConsV1.CO2_MAX_VALUE
            )
            newTemBinding.ttcTemChart4ThDetailNew.updateWarn(
                Constants5140.TEM_MIN_VALUE * 100,
                Constants5140.TEM_MAX_VALUE * 100
            )
            newHumBinding.ttcHumChart4ThDetailNew.updateWarn(
                Constants5140.HUM_MIN_VALUE * 100,
                Constants5140.HUM_MAX_VALUE * 100
            )
        }
        //newTemBinding.ttcTemChart4ThDetailNew.updateCali(ext.temCali)
        //newHumBinding.ttcHumChart4ThDetailNew.updateCali(ext.humCali)
        viewMode.setCali(ext.temCali, ext.humCali)
        shopGuideM!!.requestGuide()
        checkBleSwitchState()
        try {
            SafeLog.d(TAG) { "onResume needRefreshChart=$needRefreshChart,deleteAllData=$deleteAllData" }
            if (needRefreshChart && ::viewMode.isInitialized) {
                needRefreshChart = false
                // 触发刷新UI
                val data = viewMode.thpDataLiveData.value
                if (data != null) {
                    viewMode.thpDataLiveData.postValue(data)
                }
            }
            if (deleteAllData && ::viewMode.isInitialized) {
                clearAllDate()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
        needShowFresh = false
        deleteAllData = false
        BleOp4ChartInfo.is_load_all_data = false
    }

    private fun clearAllDate() {
        viewMode.thpDataLiveData.postValue(emptyList())
        val hashmap = kotlin.collections.HashMap<Int, ArrayList<TemHumPm>>()
        hashmap[1] = arrayListOf()
        val startTime = System.currentTimeMillis() - ThConsV1.ONE_DAY_MILLIS
        val endTime = System.currentTimeMillis()
        val timeRangePair = Pair(startTime, endTime)
        chartOp4Th.updateRealData(hashmap, timeRangePair, 0) {
            H5140C02WarnConfig.getCo2WarnInfo(ext.sku, ext.device).let { warnRange ->
                newCo2Binding.ttcPm25Chart4ThDetailNew.updateWarn(
                    warnRange.co2Min,
                    warnRange.co2Max
                )
            }
            H5140C02WarnConfig.getCo2LevelInfo(ext.sku, ext.device).let { co2LevelInfo ->
                newCo2Binding.ttcPm25Chart4ThDetailNew.updateCo2Level(
                    co2LevelInfo.minLevel,
                    co2LevelInfo.maxLevel
                )
            }
            newCo2Binding.ttcPm25Chart4ThDetailNew.isCo2()
            val intervalType = binding.spvChartSelectPeriod4ThDetail.intervalType ?: IntervalType.hour_15_min
            chartOp4Th.setIntervalType(intervalType)
        }
    }

    override fun onPause() {
        //保存最新的实时温湿度值
        Config4LastThValue.getConfig().updateLastTh(sku, device, LastData().apply {
            tem = realTem
            hum = realHum
            pm = realCo2
            lastTime = refreshTime
        })
        super.onPause()
    }

    override fun onStop() {
        super.onStop()
        isResume = false
    }

    private val ble: ThpBleCommManager
        get() = getInstance()

    override fun onShow(show: Boolean, imgUrl: String, showClose: Boolean) {
        binding.btnGuide.visibility = if (show) View.VISIBLE else View.GONE
        binding.btnGuideClose.visibility = if (showClose) View.VISIBLE else View.GONE
        if (show) {
            Glide.with(this).load(imgUrl).into(binding.btnGuide)
        }
    }

    private fun updateSettingStatus() {
        if (isDestroy) {
            return
        }
        if (waitingShowUpdateHintDialog) {
            showUpdateHintDialog(checkVersion)
        }
    }

    private fun initViewMode() {
        viewMode = ViewModelProvider(this)[DbDataViewMode4Thp::class.java]
        lifecycle.addObserver(viewMode)
        viewMode.thpDataLiveData.observe(this) { thps: List<TemHumPm> ->
            if (!CollectionUtils.isEmpty(thps)) {
                val size = thps.size
                //上报服务器最新一条温湿度数据
                val thp = thps[size - 1]
                lastSyncTime = thp.time
                updateLatestThpData(thp)
                //更新同步时数据时间
                mHandler.sendEmptyMessage(what_update_sync_time)
                chartOp!!.updateRealData(thps)
                binding.btnChart.changeVisibilityState(View.VISIBLE)
                val hashmap = kotlin.collections.HashMap<Int, ArrayList<TemHumPm>>()
                hashmap[1] = thps as ArrayList
                val startTime = hashmap[1]?.get(0)?.time ?: (System.currentTimeMillis() - ThConsV1.ONE_DAY_MILLIS)
                val endTime = hashmap[1]?.get(size - 1)?.time ?: System.currentTimeMillis()
                val timeRangePair = Pair(startTime, endTime)
                chartOp4Th.updateRealData(hashmap, timeRangePair, 0) {
                    H5140C02WarnConfig.getCo2WarnInfo(ext.sku, ext.device).let { warnRange ->
                        SafeLog.d("temp_h5140_chart__") { "warnRange----->${ext.sku} ${ext.device} co2=${warnRange.co2Min} ${warnRange.co2Max}" }
                        newCo2Binding.ttcPm25Chart4ThDetailNew.updateWarn(
                            warnRange.co2Min,
                            warnRange.co2Max
                        )
                    }
                    H5140C02WarnConfig.getCo2LevelInfo(ext.sku, ext.device).let { co2LevelInfo ->
                        SafeLog.d("temp_h5140_chart__") { "co2LevelInfo----->${ext.sku} ${ext.device} co2=${co2LevelInfo.minLevel} ${co2LevelInfo.maxLevel}" }
                        newCo2Binding.ttcPm25Chart4ThDetailNew.updateCo2Level(
                            co2LevelInfo.minLevel,
                            co2LevelInfo.maxLevel
                        )
                    }
                    newCo2Binding.ttcPm25Chart4ThDetailNew.isCo2()
                    val intervalType = binding.spvChartSelectPeriod4ThDetail.intervalType ?: IntervalType.hour_15_min
                    chartOp4Th.setIntervalType(intervalType)
                }
            }
            mHandler.sendEmptyMessage(what_check_fresh_full_showing_type)
        }
    }

    private fun initChart() {
        dataGroup = DataGroup.hour
        toChooseSelectedGroup(com.govee.h5140.R.id.group_hour)
        val chartController = ChartController4Thp(this)
        chartController.setChartListener(object : ChartController4Thp.ChartListener {
            override fun beScale() {
                dataGroup = null
                toChooseSelectedGroup(-1)
            }

            override fun timeChange(
                startTimeStamp: Long,
                endTimeStamp: Long,
                intervalType: IntervalType,
            ) {
                runOnUiThread(object : CaughtRunnable() {
                    override fun runSafe() {
                        updateTime(startTimeStamp, endTimeStamp, intervalType)
                    }
                })
            }
        })
        chartOp = chartController
        val configParams = ConfigParams()
        configParams.goodsType = GoodsType.GOODS_TYPE_VALUE_H5140
        configParams.sku = sku
        configParams.device = device
        configParams.bleAddress = bleAddress
        configParams.bleSv = softVersion
        configParams.bleHv = hardVersion
        configParams.isSupportScale = true
        configParams.isForNewDetail = true
        configParams.needFill = true
        chartOp4Th = ChartController4New(this, configParams)
        newHumBinding.ttcHumChart4ThDetailNew.setChart(chartOp4Th)
        newTemBinding.ttcTemChart4ThDetailNew.setChart(chartOp4Th)
        newCo2Binding.ttcPm25Chart4ThDetailNew.setChart(chartOp4Th)
        newDpBinding.ttcDpChart4ThDetailNew.setChart(chartOp4Th)
        newVpdBinding.ttcVpdChart4ThDetailNew.setChart(chartOp4Th)
        chartOp4Th.setChartListener(object : ChartListener {
            override fun beScale() {
                binding.spvChartSelectPeriod4ThDetail.clearSelect()
            }

            override fun timeChange(
                startTimeStamp: Long,
                endTimeStamp: Long,
                intervalType: IntervalType
            ) {
                updateTime(startTimeStamp, endTimeStamp, intervalType)
            }

            override fun hasDrawnChart() {

            }
        })
        binding.spvChartSelectPeriod4ThDetail.setSelectListener(sku) { type, dataGroup ->
            if (type != null) {
                chartOp4Th.setIntervalType(type)
            }
        }
        binding.thstvContainer4ThDetail.setOnSwitchTimeListener(
            GoodsType.GOODS_TYPE_VALUE_H5140,
            sku,
            device,
            -1
        ) { isToNext ->
            chartOp4Th.setOneStage(isToNext)
        }
        newTemBinding.ivTemUnitIcon4ThDetailNew.gone()
        Handler(Looper.getMainLooper()).postDelayed({
            binding.spvChartSelectPeriod4ThDetail.updateDataGroupChoose(DataGroup.hour)
        }, 100)
    }

    private fun refreshVpdView() {
        val selected = ChartVisibleConfig.read().getVisible(sku, ChartVisibleConfig.CHART_TYPE_VPD)
        newVpdBinding.gpVpdHideChartIds4ThDetailNew.setVisibility(!selected)
        newVpdBinding.ivVpdSwitch14ThDetailNew.isSelected = selected
        newVpdBinding.gpVpdShowChartIds4ThDetailNew.setVisibility(selected)
        newVpdBinding.ivVpdSwitch4ThDetailNew.isSelected = selected
    }

    private fun refreshDewPView() {
        val selected =
            ChartVisibleConfig.read().getVisible(sku, ChartVisibleConfig.CHART_TYPE_DEW_P)
        newDpBinding.gpDpHideChartIds4ThDetailNew.setVisibility(!selected)
        newDpBinding.ivDpSwitch14ThDetailNew.isSelected = selected
        newDpBinding.gpDpShowChartIds4ThDetailNew.setVisibility(selected)
        newDpBinding.ivDpSwitch4ThDetailNew.isSelected = selected
    }

    override fun onDestroy() {
        destroy()
        if (shopGuideM != null) {
            shopGuideM!!.onDestroy()
        }
        EventBus.getDefault().removeStickyEvent(serviceData)
        H5140TempUnitHelper.removeListener()
        super.onDestroy()
    }

    private fun doWhat(what: Int) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "doWhat() what = $what")
        }
        when (what) {
            what_update_sync_time -> {
                updateSyncTime()
            }

            what_check_fresh_full_showing_type -> {
                checkFreshFull()
            }

            what_check_ble_load_data -> {
                checkBleLoadData()
            }
        }
    }

    private fun checkBleLoadData() {
        val inServicing = serviceData.inServicing()
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkBleLoadData() inServicing = $inServicing")
        }
        if (!inServicing && !isUploadLastPck) {
            //ble加载数据准备阶段的两条指令在心跳特征中
            (getInstance().heart as H5140HeartSender).setNeedParseHeartPackage(true)
            bleOp4Detail.checkBleLoadData()
        }
    }

    private fun doCheckDataStep() {
        val inServicing = serviceData.inServicing()
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "doCheckDataStep() inServicing = $inServicing")
        }
        if (!inServicing && !isUploadLastPck) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "doCheckDataStep()")
            }
            dataOver(hadReadBleData = false, readOver = false)
        }
    }

    private fun toCheckVersion() {
        val request = DeviceUpdateRequest(
            transactions.createTransaction(),
            ext.versionSoft,
            ext.versionHard,
            ext.sku,
            ext.device,
            ""
        )
        Cache.get(IUpdateNet::class.java).checkUpdate(request).enqueue(IHCallBack(request))
    }

    override fun statusBarLightMode(): Boolean {
        return false
    }

    override fun getAcContentRootViewId(): Int {
        return com.govee.h5140.R.id.ac_container
    }

    override fun getLayout(): Int {
        return -1
    }

    private fun toSettingAc() {
        bleOp4Detail.inComm(false)
        //跳转到设置界面
        Activity4Setting.jump2SettingAc(
            this,
            ext.goodsType,
            ext.sku,
            ext.device,
            ext.bleAddress,
            ext.deviceName,
            ext.wifiMac,
            ext.versionSoft,
            ext.versionHard,
            topic,
        )
    }

    private fun updateTitleTv() {
        if (isDestroy) {
            return
        }
        binding.title.text = ext.deviceName
    }

    private fun getIntervalTypeByDataGroup(group: DataGroup): IntervalType {
        var type = IntervalType.hour_15_min
        when (group) {
            DataGroup.year -> {
                type = IntervalType.year_1_month
            }

            DataGroup.month -> {
                type = IntervalType.month_7_day
            }

            DataGroup.week -> {
                type = IntervalType.week_1_day
            }

            DataGroup.day -> {
                type = IntervalType.day_8_hour
            }

            DataGroup.hour -> {
                type = IntervalType.hour_15_min
            }

            else -> {}
        }
        return type
    }

    private fun updateTemUnit(fahOpen: Boolean) {
        if (isDestroy) {
            return
        }
        newTemBinding.ivTemUnitIcon4ThDetailNew.setImageDrawable(ResUtil.getDrawable(if (fahOpen) R.mipmap.new_sensor_setting_switch_fahrenheit else R.mipmap.new_sensor_setting_switch_celsius))
        newTemBinding.ttcTemChart4ThDetailNew.setChangeValueShow(
            fahOpen,
            if (fahOpen) getString(R.string.tem_unit_fah) else getString(R.string.tem_unit_cel)
        )
        newDpBinding.ttcDpChart4ThDetailNew.setChangeValueShow(
            fahOpen,
            if (fahOpen) getString(R.string.tem_unit_fah) else getString(R.string.tem_unit_cel)
        )
    }

    private fun toChooseSelectedGroup(id: Int) {
        if (isDestroy) {
            return
        }
        binding.groupHour.isSelected = id == com.govee.h5140.R.id.group_hour
        binding.groupDay.isSelected = id == com.govee.h5140.R.id.group_day
        binding.groupWeek.isSelected = id == com.govee.h5140.R.id.group_week
        binding.groupMonth.isSelected = id == com.govee.h5140.R.id.group_month
        binding.groupYear.isSelected = id == com.govee.h5140.R.id.group_year
        var tabValue: String? = null
        when (id) {
            com.govee.h5140.R.id.group_hour -> {
                tabValue = ParamFixedValue.chart_tab_hour
            }

            com.govee.h5140.R.id.group_day -> {
                tabValue = ParamFixedValue.chart_tab_day
            }

            com.govee.h5140.R.id.group_week -> {
                tabValue = ParamFixedValue.chart_tab_week
            }

            com.govee.h5140.R.id.group_month -> {
                tabValue = ParamFixedValue.chart_tab_month
            }

            com.govee.h5140.R.id.group_year -> {
                tabValue = ParamFixedValue.chart_tab_year
            }
        }
        if (tabValue != null) {
            val paramsKey = ext.sku + "_" + tabValue
            AnalyticsRecorder.getInstance()
                .recordTimes(EventKey.use_count, ParamKey.chart_tab, paramsKey)
        }
    }

    private fun updateSyncTime() {
        if (isDestroy) {
            return
        }
        var str = ""
        if (lastSyncTime > 0) {
            val syncTimeStr = TimeFormatM.getInstance().formatTimeToHMYMD(lastSyncTime)
            str = String.format(ResUtil.getString(R.string.h5072_last_sync_time), syncTimeStr)
        }
        binding.tvSyncTime4ThDetail.text = str
        binding.tvSyncTime4ThDetail.setVisibility(!TextUtils.isEmpty(str))
    }

    override fun dispatchTouchEvent(ev: MotionEvent): Boolean {
        if (fullFreshShow || isDestroy) {
            return super.dispatchTouchEvent(ev)
        }
        val action = ev.action
        if (MotionEvent.ACTION_DOWN == action) {
            lastDownY = ev.rawY.toInt()
        } else if (MotionEvent.ACTION_MOVE == action) {
            val scrollY = binding.scrollContainer.scrollY
            val rawY = ev.rawY.toInt()
            //向下滑动，才需要做逻辑处理
            val pullDown = rawY - lastDownY > VERTICAL_DIR_PULL_DOWN
            val ablRecover = ablVerticalOffset == 0
            if (scrollY == 0 && !needShowFresh && pullDown && ablRecover) {
                lastStartMoveY = rawY
                needShowFresh = true
            }
            if (needShowFresh) {
                updateFreshHeight(rawY)
                return true
            }
        } else if ((MotionEvent.ACTION_UP == action || MotionEvent.ACTION_CANCEL == action) && needShowFresh) {
            checkFreshHeight()
            needShowFresh = false
            lastDownY = 0
        }
        return super.dispatchTouchEvent(ev)
    }

    private fun updateFreshHeight(rawY: Int) {
        if (isDestroy) {
            return
        }
        binding.run {
            var moreDis: Int = rawY - lastStartMoveY
            moreDis = 0.coerceAtLeast(moreDis)
            binding.llcRefreshContainer4ThDetail.layoutParams?.let {
                it.height = moreDis
                binding.llcRefreshContainer4ThDetail.layoutParams = it
            }
        }
    }

    private fun checkFreshHeight() {
        if (isDestroy) {
            return
        }
        binding.run {
            val maxHeight: Int =
                (AppUtil.getScreenWidth() * ThConsV1.FRESH_HEIGHT_PERCENT_SCREEN_WIDTH).toInt()
            val lp: ViewGroup.LayoutParams = llcRefreshContainer4ThDetail.layoutParams
            val height = lp.height
            //展示/隐藏
            updateFresh(height >= maxHeight)
        }
    }

    private fun updateFresh(fullFreshShow: Boolean) {
        if (isDestroy) {
            return
        }
        binding.let { vb ->
            <EMAIL> = fullFreshShow
            vb.pbRefreshCycle4ThDetail.setVisibility(true)
            val lp: ViewGroup.LayoutParams = vb.llcRefreshContainer4ThDetail.layoutParams
            if (fullFreshShow) {
                lp.height =
                    (AppUtil.getScreenWidth() * ThConsV1.FRESH_HEIGHT_PERCENT_SCREEN_WIDTH).toInt()
            } else {
                lp.height = 0
            }
            vb.llcRefreshContainer4ThDetail.layoutParams = lp
            if (fullFreshShow) {
                //开始加载数据:先服务端，再同步设备端的
                //Vm4ThOpManager.instance()?.loadThcdManager?.toLoadThcdBySingle(this@Activity4CharInfo)
            } else {
                vb.tvRefreshDes4ThDetail.setText(R.string.fresh_des_loading)
            }
        }
        updateSettingStatus()
        if (fullFreshShow) {
            //从服务器加载数据
            loadDataFromService()
            //默认先加载本地数据
            if (!hadShowLocal) {
                hadShowLocal = true
                showChart()
            }
        } else {
            updateSync4ServiceDes()
        }
    }

    private val refreshTimeOutRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            //更新图表
            showChart()
            updateFresh(false)
        }
    }

    private fun loadDataFromService() {
        SafeLog.d("h5140_chart_iot") { "---------->>loadDataFromService()" }
        //在iot的状态下，在加载服务器数据时，需先发送指令给设备上其上报最近不足一次频率时间的数据
        if (!BleController.getInstance().isConnected && NetworkUtils.isNetConnected(this)) {
            toSendLastPack()
        } else {
            loadServiceData()
        }
    }

    override fun onErrorResponse(response: ErrorResponse?) {
        super.onErrorResponse(response)
        if (response != null && response.isNetworkBroken) {
            isUploadLastPck = false
        }
    }

    private fun loadServiceData() {
        //查询本地数据记录时间和条目
        ThreadPoolUtil.getThreadPool().execute(object : CaughtRunnable() {
            override fun runSafe() {
                lastValidDataLength = DbController.queryValidDataLength4Thp(ext.sku, ext.device)
                lastDataTime = DbController.queryLastValidTime4Thp(ext.sku, ext.device)
                LogInfra.Log.i(
                    TAG,
                    "lastValidDataLength = $lastValidDataLength ; lastDataTime = $lastDataTime"
                )
            }
        })
        //调用服务器接口
        serviceData.loadServiceData()
    }

    private fun showChart() {
        if (isDestroy) {
            return
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "showChart()")
        }
        backgroundHandler!!.removeCallbacks(chartRunnable)
        backgroundHandler!!.postDelayed(chartRunnable, 500)
    }

    override fun finish() {
        destroy()
        super.finish()
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        destroy()
        super.onBackPressed()
    }

    override fun isDestroy(): Boolean {
        return super.isDestroy() || destroy
    }

    private fun destroy() {
        if (!destroy) {
            destroy = true
            chartOp?.destroy()
            mHandler.removeCallbacksAndMessages(null)
            backgroundHandler!!.removeCallbacksAndMessages(null)
            THMemoryUtil.getInstance().isTrendChartReading = false
            //取消event注册
            unRegisterEventBus()
            serviceData.destroy()
            bleOp4Detail.onDestroy()
            ble.clearDataComm()
            ble.inStep4Details(false)
            //断开蓝牙
            BleController.getInstance().toBtClose()
            val quitSafely = handlerThread!!.quitSafely()
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "destroy() quitSafely = $quitSafely")
            }
        }
    }

    override fun onRPPermissionGranted() {
        super.onRPPermissionGranted()
        updateFresh(true)
        //获取权限后，尝试连接蓝牙
        bleOp4Detail.connectBle()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEventNameChange(event: EventNameChange) {
        val deviceName = event.deviceName
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNameChange()")
        }
        ext.deviceName = deviceName
        updateTitleTv()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun onEvent(event: DetailFinishEvent) {
        if (StrUtil.isSameStr(event.sku, sku) && StrUtil.isSameStr(event.deviceId, device)) {
            finish()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventDataProgress(event: EventDataProgress) {
        var all = event.all
        all = 1.coerceAtLeast(all)
        var progress = event.progress
        progress = 0.coerceAtLeast(progress)
        progress = progress.coerceAtMost(all)
        val percent = progress * 100 / all
        i(TAG, "onEventDataProgress() all = $all ; progress = $progress ; percent = $percent")
        updateSyncDes(percent)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventDataResult(event: EventDataResult) {
        val readOver = event.readOver
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventDataResult() readOver = $readOver")
        }
        bleOp4Detail.bleDataOver()
        dataOver(readOver, true)
    }

    private fun dataOver(hadReadBleData: Boolean, readOver: Boolean) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "dataOver() readOver = $readOver,hadReadBleData=$hadReadBleData")
        }
        fullFreshShow = false
        THMemoryUtil.getInstance().isTrendChartReading = false
        //数据读取完成；则触发上传逻辑
        serviceData.uploadData()
        if (hadReadBleData) {
            //数据读取完毕，发送事件给设置页
            sendEvent(Event4LoadAllData.LOAD_TH_DATA_FINISH, readOver)
        }
        showChart()
        //统计同步蓝牙数据成功失败次数
        AnalyticsRecorder.getInstance().recordTimes(
            EventKey.use_count,
            ext.sku,
            if (readOver) ParamFixedValue.data_device_suc else ParamFixedValue.data_device_fail
        )
        if (!readOver) {
            checkFreshFull()
            if (hadReadBleData && isResume) {
                toast(R.string.h5072_read_ble_data_fail)
            }
            return
        }
        updateSyncDes(100)
        ThreadPoolUtil.getThreadPool().execute(object : CaughtRunnable() {
            override fun runSafe() {
                val validDataLength = DbController.queryValidDataLength4Thp(ext.sku, ext.device)
                val dataTime = DbController.queryLastValidTime4Thp(ext.sku, ext.device)
                val toastRes: Int =
                    if (dataTime > lastDataTime || validDataLength > lastValidDataLength) {
                        R.string.hint_fresh_done
                    } else {
                        R.string.temhum_data_sync_suc_no_new
                    }
                if (isResume) {
                    toastInThread(toastRes)
                }
            }
        })
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponseDeviceUpdate(response: DeviceUpdateResponse) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        val checkVersion = response.checkVersion
        val versionSoft = response.getRequest().versionSoft
        val needUpdate = checkVersion.isNeedUpdate
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onResponseDeviceUpdate() needUpdate = $needUpdate")
        }
        ShareController.queryIfSharedCall(sku, device) { isShared ->
            if (isShared) {
                SafeLog.w(TAG) { "当前被分享设备不支持升级" }
                return@queryIfSharedCall
            }
            updateVersion(needUpdate)
            if (needUpdate) {
                checkVersion.curVersionSoft = versionSoft
                showUpdateHintDialog(checkVersion)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventNewVersion(event: EventNewVersion) {
        val newVersion = event.newVersion
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNewVersion() newVersion = $newVersion")
        }
        updateVersion(newVersion)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onUpdateSucEvent(event: UpdateSucEvent?) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onUpdateSucEvent()")
        }
        updateVersion(false)
    }

    private fun updateVersion(newVersion: Boolean) {
        if (!newVersion) {
            closeBleUpdateHint()
        }
        binding.versionFlag.visibility = if (newVersion) View.VISIBLE else View.GONE
    }

    private fun updateSyncDes(percent: Int) {
        var percentStr = "$percent%"
        percentStr = String.format(getString(R.string.h5072_fresh_des_syncing), percentStr)
        binding.tvRefreshDes4ThDetail.text = percentStr
        if (percent >= 100) {
            mHandler.postDelayed({ updateFresh(false) }, 3000)
        }
    }

    private fun updateSync4ServiceDes() {
        binding.tvRefreshDes4ThDetail.setText(R.string.fresh_des_loading)
    }

    private fun updateTime(startTimeStamp: Long, endTimeStamp: Long, intervalType: IntervalType) {
        var startTimeStampU = startTimeStamp
        var endTimeStampU = endTimeStamp
        if (isDestroyed) {
            return
        }
        if (dataGroup != null) {/*在默认标签下；需要格式时间起点的十分钟一致*/
            val calibration = TimeUtil.calibration(startTimeStampU, endTimeStampU, dataGroup)
            startTimeStampU = calibration[0]
            endTimeStampU = calibration[1]
        }
        val strByMDHMStart: String
        val strByMDHMEnd: String
        if (IntervalType.year_1_month == intervalType) {
            strByMDHMStart = TimeFormatM.getInstance().formatTimeToYM(startTimeStampU)
            strByMDHMEnd = TimeFormatM.getInstance().formatTimeToYM(endTimeStampU)
        } else {
            strByMDHMStart = TimeFormatM.getInstance().formatTimeToHMMD(startTimeStampU)
            strByMDHMEnd = TimeFormatM.getInstance().formatTimeToHMMD(endTimeStampU)
        }
        newTemBinding.tvTemStartTime4ThDetailNew.text = strByMDHMStart
        newHumBinding.tvHumStartTime4ThDetailNew.text = strByMDHMStart
        newCo2Binding.tvPm25StartTime4ThDetailNew.text = strByMDHMStart
        newDpBinding.tvDpStartTime4ThDetailNew.text = strByMDHMStart
        newVpdBinding.tvVpdStartTime4ThDetailNew.text = strByMDHMStart

        newTemBinding.tvTemEndTime4ThDetailNew.text = strByMDHMEnd
        newHumBinding.tvHumEndTime4ThDetailNew.text = strByMDHMEnd
        newCo2Binding.tvPm25EndTime4ThDetailNew.text = strByMDHMEnd
        newDpBinding.tvDpEndTime4ThDetailNew.text = strByMDHMEnd
        newVpdBinding.tvVpdEndTime4ThDetailNew.text = strByMDHMEnd
    }

    private fun checkFreshFull() {
        if (isDestroy) {
            return
        }
        val inBleing = bleOp4Detail.inBleing()
        val inServicing = serviceData.inServicing()
        if (inBleing || inServicing || isUploadLastPck) {
            //加载蓝牙数据中或加载服务器数据中，不隐藏刷新
            return
        }
        updateFresh(false)
    }

    private var firstShowChart = true

    private val chartRunnable: Runnable = object : CaughtRunnable() {
        override fun runSafe() {
            if (isDestroy) {
                return
            }
            val curTimeMs = System.currentTimeMillis()
            val validMinTimeMills = if (firstShowChart) {
                firstShowChart = false
                //为快速展示出图表，首次展示只查询一个星期的数据
                curTimeMs - ThConsV1.ONE_WEEK_MILLIS
            } else {
                getMinValidTimeMills(ext.goodsType)
            }
            val validMaxTimeMills =
                System.currentTimeMillis() + ThConsV1.flag_time_mills_one_month /*用户系统时间往后1个月以后数据认为无效*/
            if (LogInfra.openLog()) {
                LogInfra.Log.i(
                    TAG,
                    "runSafe() validMinTimeMills = " + TimeFormatM.getInstance()
                        .formatTimeToHMYMD(validMinTimeMills) + " ； validMaxTimeMills = " + TimeFormatM.getInstance()
                        .formatTimeToHMYMD(validMaxTimeMills)
                )
            }
            val datas =
                DbController.queryAllData4Thp(sku, device, validMinTimeMills, validMaxTimeMills)
            //.filter { it.pm25 != 65535 }
            if (lastDataList.isNotEmpty() && datas.isNotEmpty()) {
                if (lastDataList.size == datas.size && lastDataList[0].time == datas[0].time && lastDataList[lastDataList.size - 1].time == datas[datas.size - 1].time) {
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "数据没有变化")
                    }
                    mHandler.sendEmptyMessage(what_update_sync_time)
                    mHandler.sendEmptyMessage(what_check_fresh_full_showing_type)
                    return
                }
            }
            lastDataList.clear()
            lastDataList.addAll(datas)
            //更新同步时间
            val size = datas.size
            if (size > 1) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(
                        TAG,
                        " 最新一条时间：" + TimeFormatM.getInstance()
                            .formatTimeToHMYMD(datas[size - 1].time)
                    )
                }
            }
            if (size == 0) {
                //清除最新数据时间戳
                lastSyncTime = 0
                chartOp!!.updateRealData(datas)
                if (!CollectionUtils.isEmpty(datas)) binding.btnChart.changeVisibilityState(View.VISIBLE)
                //更新同步时数据时间
                mHandler.sendEmptyMessage(what_update_sync_time)
                mHandler.sendEmptyMessage(what_check_fresh_full_showing_type)
            } else {
                mHandler.post {
                    viewMode.setThpData(datas, true)
                }
            }
        }
    }


    /**
     * 更新最后一条数据
     */
    private fun updateLatestThpData(thp: TemHumPm) {
        val datas = thp.tem.toString() + "," + thp.hum + "," + thp.pm25 + "," + thp.time
        val requestUpdateLatestData =
            RequestUpdateLatestData(transactions.createTransaction(), ext.sku, ext.device, datas)
        Cache.get(IThNet::class.java).updateLatesData(requestUpdateLatestData)
            .enqueue(IHCallBack(requestUpdateLatestData))
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onResponseUpdateLatest(response: ResponseUpdateLatestData?) {
        if (!transactions.isMyTransaction(response)) {
            return
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onResponseUpdateLatest()")
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventAfterClearDataSuc(event: EventAfterClearDataSuc?) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventAfterClearDataSuc()")
        }
        showChart()
    }

    /**
     * 温度单位切换
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTempUnit(event: EventTempUnit) {
        synchronized(this) {
            if (event.onlyRefreshFromSp) {
                updateTemUnit(TemUnitConfig.read().isTemUnitFah(sku, device))
                return@synchronized
            }
            val result = event.isResult
            if (result) {
                val tuType = event.temUnit
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventTempUnit() temUnit = $tuType")
                }
                //获取本地保存的温度单位
                val tuTypeCache = TemUnitConfig.read().getTemUnit(sku, device).ordinal
                //不一致则将设备端的数据保存值本地，并更新显示
                //设置页中切换单位，也会回调到此处,故本地保存在此处统一处理
                if (tuType != tuTypeCache) {
                    val isFahOpen = tuType == 1
                    H5140TempUnitHelper.setTemUnit(
                        ext.sku,
                        ext.device,
                        if (isFahOpen) TemperatureUnitType.Fahrenheit else TemperatureUnitType.Celsius
                    )
                    updateTemUnit(isFahOpen)
                }
            }
            getInstance().controllerEvent(event)
        }
    }

    /**
     * 在数据对比页面切换温度单位
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4ChangeTempUnitFromCompare(event: Event4ChangeTemUnit) {
        if (event.sku == sku && event.device == device) {
            EventTempUnit.sendRefreshEvent()
        }
    }

    private fun showUpdateHintDialog(checkVersion: CheckVersion?) {
        if (checkVersion == null) {
            return
        }
        val sku = ext.sku
        val device = ext.device
        val newSoftVersion = checkVersion.versionSoft
        val newHardVersion = checkVersion.versionHard
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventShowUpdateHintDialog() sku = $sku ; device = $device")
            LogInfra.Log.i(
                TAG,
                "onEventShowUpdateHintDialog() newSoftVersion = $newSoftVersion ; newHardVersion = $newHardVersion"
            )
        }
        if (TextUtils.isEmpty(sku) || TextUtils.isEmpty(device) || TextUtils.isEmpty(newSoftVersion) || TextUtils.isEmpty(
                newHardVersion
            )
        ) {
            return
        }
        val needShowUpdateHint = UpdateHint4VersionConfig.read()
            .needShowUpdateHint(sku, device, newSoftVersion, newHardVersion)
        if (LogInfra.openLog()) {
            LogInfra.Log.i(
                TAG,
                "onEventShowUpdateHintDialog() needShowUpdateHint = $needShowUpdateHint"
            )
        }
        if (!needShowUpdateHint) {
            return
        }
        if (fullFreshShow) {
            //若当前正处于加载数据，则延时弹窗
            if (LogInfra.openLog()) {
                LogInfra.Log.w(TAG, "showUpdateHintDialog() 正处于加载数据ing，延时弹窗")
            }
            this.checkVersion = checkVersion
            waitingShowUpdateHintDialog = true
            return
        }
        //记录升级提示
        UpdateHint4VersionConfig.read()
            .recordUpdateHint(sku, device, newSoftVersion, newHardVersion)
        showingBleUpdateHintDialog = true
        BleUpdateHintDialog.showDialog(this, sku, { toUpdateAc(checkVersion) }, this.javaClass.name)
    }

    private fun toUpdateAc(checkVersion: CheckVersion?) {
        if (checkVersion == null) {
            return
        }
        //进入升级界面；不响应eventBus;通知数据同步关闭
        bleOp4Detail.inComm(false)
        Activity4DeviceUpdate.jump2OtaUpdateAcV3(
            this,
            ext.sku,
            ext.deviceName,
            ext.versionSoft,
            ThemeM.getDefSkuRes(ext.sku),
            checkVersion
        )
    }

    private fun closeBleUpdateHint() {
        if (showingBleUpdateHintDialog) {
            showingBleUpdateHintDialog = false
            //关闭升级提示弹窗
            BleUpdateHintDialog.hideDialog(this.javaClass.name)
        }
        waitingShowUpdateHintDialog = false
    }

    /**
     * 点击事件集中处理处
     */
    override fun onClick(v: View) {
        if (ClickUtil.getInstance.clickQuick()) {
            return
        }
        //点击返回
        if (v === binding.btnBack) {
            v.setEnabled(false)
            finish()
            //点击柱状图按钮
        } else if (v === binding.btnChart) {
            bleOp4Detail.inComm(false)
            initBindExtData()
            JumpUtil.jump(this, Ac4ThSquareChartNew::class.java, Bundle().apply {
                val bindExt = IntentUtils.parseParcelable<AddInfo>(intent, ThConsV1.KEY_4_ADD_INFO)
                    ?: AddInfo()
                this.putParcelable(ThConsV1.KEY_4_ADD_INFO, bindExt)
            })
            //点击设置按钮
        } else if (v === binding.btnSetting) {
            toSettingAc()
            //温度华氏度/摄氏度切换(5.3.1后入口已隐藏)
        } else if (v === binding.btnGuide) {
            if (shopGuideM != null) {
                shopGuideM!!.clickGuide()
            }
        } else if (v === binding.btnGuideClose) {
            if (shopGuideM != null) {
                shopGuideM!!.clickGuideClose()
            }
        } else if (v === newDpBinding.ivDpIntroIcon4ThDetailNew || v === newDpBinding.ivDpIntroIcon14ThDetailNew) {
            IntroUtils.showDewPointIntro(this, ext.sku)
            //点击vpd介绍icon
        } else if (v === newVpdBinding.ivVpdIntroIcon4ThDetailNew || v === newVpdBinding.ivVpdIntroIcon14ThDetailNew) {
            IntroUtils.showVpdIntro(this, ext.sku)
            //数据导出
        } else if (v === binding.clExportContainer4ThDetail) {
            Activity4ExportData.jump2ExportData(this, ext.sku, ext.device)
        } else if (v === binding.tvHistoryCompare4ThDetail) {
            initViewMode()
            //须主动断开蓝牙
            BleController.getInstance().disconnectBleAndNotify()
            //跳转至历史数据对比页面
            AbsThpBleData.isLoadThcd4Compare = true
            RouterRuler.instance.startToThcd4SingleCompare(this@Activity4CharInfo, Bundle().apply {
                putString(Constant.intent_ac_key_setting_str, JsonUtil.toJson(ext))
            })
        } else if (v === binding.tvMultiDeviceCompare4ThDetail) {
            initViewMode()
            //须主动断开蓝牙
            BleController.getInstance().disconnectBleAndNotify()
            //跳转至多设备数据对比页面
            AbsThpBleData.isLoadThcd4Compare = true
            RouterRuler.instance.startToThcd4MultiCompare(this@Activity4CharInfo, Bundle().apply {
                putString(Constant.intent_ac_key_setting_str, JsonUtil.toJson(ext))
            })
        } else {
            //切换时间段显示
            val id = v.id
            var dataGroup: DataGroup? = null
            when (id) {
                com.govee.h5140.R.id.group_hour -> {
                    dataGroup = DataGroup.hour
                }

                com.govee.h5140.R.id.group_day -> {
                    dataGroup = DataGroup.day
                }

                com.govee.h5140.R.id.group_week -> {
                    dataGroup = DataGroup.week
                }

                com.govee.h5140.R.id.group_month -> {
                    dataGroup = DataGroup.month
                }

                com.govee.h5140.R.id.group_year -> {
                    dataGroup = DataGroup.year
                }
            }
            if (dataGroup == null) {
                return
            }
            if (this.dataGroup == dataGroup) {
                return
            }
            this.dataGroup = dataGroup
            toChooseSelectedGroup(id)
            val intervalType = getIntervalTypeByDataGroup(dataGroup)
            chartOp!!.intervalType = intervalType
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == RouterRuler.REQUEST_CODE_4_RETURN_FROM_COMPARE) {
            mHandler.postDelayed(object : CaughtRunnable() {
                override fun runSafe() {
                    AbsThpBleData.isLoadThcd4Compare = false
                    bleOp4Detail.connectBle()
                }
            }, 1 * 1000)
        }
    }

    //=========================================iot相关===========================================
    private val iotTransactions: IotTransactions by lazy {
        IotTransactions()
    }

    @Volatile
    private var isUploadLastPckStart = false

    @Volatile
    private var isUploadLastPck = false

    /**
     * 检查iot状态
     */
    private fun iotCheck() {
        if (TextUtils.isEmpty(topic)) {
            DeviceM.getInstance.getDeviceTopic(sku, device)
            mHandler.removeCallbacks(connectIotTimeOutRunnable)
            mHandler.postDelayed(connectIotTimeOutRunnable, Constants5140.IOT_CONNECT_TIME_OUT)
            return
        }
        if (Iot.getInstance.isConnected) {
            sendIotOp()
        } else {
            Iot.getInstance.toConnectIot()
            mHandler.removeCallbacks(connectIotTimeOutRunnable)
            mHandler.postDelayed(connectIotTimeOutRunnable, Constants5140.IOT_CONNECT_TIME_OUT)
        }
    }

    /**
     * 通过iot上传最后一包数据
     */
    private fun toSendLastPack() {
        isUploadLastPckStart = true
        iotCheck()
    }

    /**
     * 开始iot心跳
     */
    private fun startIotHeart() {
        iotCheck()
    }

    /**
     * 根据类型进行iot通讯
     */
    private fun sendIotOp() {
        if (isUploadLastPckStart) {
            isUploadLastPckStart = false
            isUploadLastPck = true
            Iot.getInstance.write(
                topic,
                iotTransactions.createTransaction(false),
                CmdPtReal(CommandUploadLastData(), System.currentTimeMillis())
            )
            mHandler.removeCallbacks(uploadTimeOutRunnable)
            mHandler.postDelayed(uploadTimeOutRunnable, Constants5140.IOT_RESPONSE_TIME_OUT)
        }
        mHandler.removeCallbacks(iotHeartRunnable)
        mHandler.postDelayed(iotHeartRunnable, 0)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onIotConnect(iotConnectEvent: IotConnectEvent) {
        if (Iot.getInstance.isConnected) {
            mHandler.removeCallbacks(connectIotTimeOutRunnable)
            sendIotOp()
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onDeviceTopicResponse(topicEvent: EventDeviceTopic) {
        if (sku == topicEvent.sku && device == topicEvent.device) {
            topic = topicEvent.topic
        }
        //获取到主题后再次尝试iot检测
        if (!TextUtils.isEmpty(topic)) {
            mHandler.removeCallbacks(connectIotTimeOutRunnable)
            iotCheck()
        }
    }

    private var connectIotTimeOutRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            if (isUploadLastPck) {
                if (BleController.getInstance().isConnected) {
                    bleOp4Detail.checkBleLoadData()
                } else {
                    loadServiceData()
                }
                isUploadLastPck = false
            }
        }
    }

    private var uploadTimeOutRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            isUploadLastPck = false
            loadServiceData()
        }
    }

    /**
     * iot读写操作的回调事件
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onIotMsgEventV2(iotEvent: IotMsgEventV2) {
        val msg: IotMsgV2 = iotEvent.msgV2
        if (!msg.isSameDevice(sku, device)) {
            return
        }
        ext.versionSoft = iotEvent.msgV2.softVersion
        if (msg.cmd == Cmd5140.status && !TextUtils.isEmpty(iotEvent.jsonStr)) {
            parseIotStatus(iotEvent.jsonStr)
        }
        val needParseIot =
            msg.cmd == Cmd5140.ptReal && !TextUtils.isEmpty(iotEvent.jsonStr) && isResume
        if (!needParseIot) {
            return
        }
        val opJsonStr = IotParseUtils.getJsonObjectStr(iotEvent.jsonStr, Cmd5140.parse_json_op)
        val op = JsonUtil.fromJson(opJsonStr, Command4H5106::class.java)
        if (op != null && !op.command.isNullOrEmpty()) {
            val commandHexStr = IotParseUtils.getCommandHexStr(op.command!!)
            if (TextUtils.isEmpty(commandHexStr)) {
                return
            }
            val commandType = commandHexStr!!.substring(2, 4).toInt(16).toByte()
            val result = commandHexStr.substring(4, 6).toInt(16) == 0
            if (commandType == BleProtocol.VALUE_UPLOAD_LAST_DATA) {
                if (result) {
                    mHandler.removeCallbacks(uploadTimeOutRunnable)
                    //设备端上传完毕后，app端略做延迟去加载
                    mHandler.postDelayed({
                        loadServiceData()
                    }, 1000)
                }
                isUploadLastPck = false
            }
        }
    }

    /**
     * 解析status
     */
    private fun parseIotStatus(cmdJsonStr: String) {
        //解析op字段内参数
        val opJsonStr = IotParseUtils.getJsonObjectStr(cmdJsonStr, Cmd5140.parse_json_op)
        val op = JsonUtil.fromJson(opJsonStr, Command4H5106::class.java)
        if (op != null && !op.command.isNullOrEmpty()) {
            IotParseUtils.getDeviceInfo(
                ext.sku,
                ext.device,
                op.command!!
            )?.run {
                refreshTime = System.currentTimeMillis()
                realTem = this.tem
                realHum = this.hum
                realCo2 = this.co2
                EventBus.getDefault().post(UpdateThCo2Event(realTem, realHum, realCo2, refreshTime))
                binding.thRealInfo.updateRealInfo(refreshTime, realTem, realHum, realCo2)
            }
        }
    }

    override fun needLocationPre4iBeaconDevice(): Boolean {
        return true
    }

    private val iotHeartRunnable = object : CaughtRunnable() {
        override fun runSafe() {
            if (isResume) {
                val statusCmd = Cmd4Status()
                Iot.getInstance.read(
                    topic,
                    iotTransactions.createTransaction(true),
                    statusCmd.cmd,
                    statusCmd.cmdVersion
                )
            }
            //准备下一次心跳
            mHandler.removeCallbacks(this)
            mHandler.postDelayed(this, Constants5140.IOT_HEART_INTERVAL_TIME)
        }
    }

    /**
     * 根据图表顺序展示图表
     */
    private fun changeChartOrder(orderList: ArrayList<String>? = null) {
        val defaultList = arrayListOf(
            ThConsV1.CO2,
            ThConsV1.TEM,
            ThConsV1.HUM,
            ThConsV1.DP,
            ThConsV1.VPD
        )
        val list = if (orderList.isNullOrEmpty()) {
            Config4DeviceChartOrder.getConfig().getChartOrder(sku, device) ?: defaultList
        } else {
            orderList
        }
        if (!list.contains(ThConsV1.CO2)) {
            Config4DeviceChartOrder.getConfig().updateChartOrder(sku, device, defaultList)
        }
        binding.flContent1Container.removeAllViews()
        binding.flContent2Container.removeAllViews()
        binding.flContent3Container.removeAllViews()
        binding.flContent4Container.removeAllViews()
        binding.flContent5Container.removeAllViews()
        list.forEachIndexed { index, orderType ->
            fillToContainer(index, orderType)
        }
    }

    private fun fillToContainer(order: Int, type: String) {
        //排序填充
        val container = when (order) {
            0 -> binding.flContent1Container
            1 -> binding.flContent2Container
            2 -> binding.flContent3Container
            3 -> binding.flContent4Container
            4 -> binding.flContent5Container
            else -> {
                binding.flTestHumContainer
            }
        }
        when (type) {
            ThConsV1.TEM -> {
                container.addView(newTemBinding.root)
            }

            ThConsV1.HUM -> {
                container.addView(newHumBinding.root)
            }

            ThConsV1.DP -> {
                container.addView(newDpBinding.root)
            }

            ThConsV1.VPD -> {
                container.addView(newVpdBinding.root)
            }

            ThConsV1.CO2 -> {
                container.addView(newCo2Binding.root)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4ChangeChartOrder(event: Event4ChangeChartOrder) {
        val list = event.chartOrder
        val finalList = if (list.isEmpty() || list.size < 5) {
            arrayListOf(
                ThConsV1.CO2,
                ThConsV1.TEM,
                ThConsV1.HUM,
                ThConsV1.DP,
                ThConsV1.VPD
            )
        } else {
            list
        }
        changeChartOrder(finalList)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onReleaseVmEvent(event: ReleaseVMEvent) {
        Vm4ThOpManager.instance()?.release(Vm4ThOpManager.INIT_FROM_OLD_DETAIL)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun jump2SettingAc(event: Jump2SettingEvent) {
        binding.btnSetting.performClick()
    }

    /**
     * 监听蓝牙开关状态
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onBleSwitchStateChange(event: Event4DynamicBroadcastReceiver) {
        checkBleSwitchState()
    }

    private fun checkBleSwitchState() {
        /*
        val btOpen = isBluetoothEnabled(this)
        if (btOpen) {
            binding.nhvBleEnable4ThDetail.hide()
        } else {
            binding.nhvBleEnable4ThDetail.updateHint(
                HintLabel(
                    ResUtil.getString(R.string.h5072_bluetooth_unable_detail_main_des),
                    NotifyHintView.hint_type_notification_showing
                )
            )
        }
        */
    }

    /**
     * 送送控制指令（ble+iot）
     */
    private fun toSendController(controller: AbsSingleController) {
        val connected = BleController.getInstance().isConnected
        if (connected) {
            SafeLog.d("ble_send") { "使用蓝牙发送" }
            bleOp4Detail.thBle.startControllers(controller)
        } else {
            SafeLog.d("ble_send") { "使用iot发送" }
            //!!是否为ble蓝牙控制，iot控制时需有loading弹窗
            showLoading(true)
            val writeCmd = CmdPtReal(controller, System.currentTimeMillis())
            IotOpManager().writeCmd(writeCmd)
            IotOpManager().setOpResult(object : IIotOpResultV1 {
                override fun noConnectIot() {

                }

                override fun cmdWriteFail(overtime: Boolean, absCmd: AbsCmd?) {

                }

                override fun cmdWriteSuc(absCmd: AbsCmd?) {

                }

                override fun cmdRead(cmd: String?, cmdJsonStr: String?) {

                }

                override fun cmdOnline(
                    softVersion: String?,
                    cmdJsonStr: String?,
                    wifiSoftVersion: String?
                ) {

                }

                override fun cmdWriteSuc4Pt(absCmd: AbsCmd?, cmdJsonStr: String?) {

                }
            })
            Handler(Looper.getMainLooper()).postDelayed({
                hideLoading()
            }, 1000)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun toSendTempUnit(event: EventSetTemUnit) {
        //温度单位切换
        val tuType = if (event.isFah) CommandTemUnit.FAH else CommandTemUnit.CEN
        val command = CommandTemUnit(tuType)
        toSendController(command)
    }

    private fun sendIotCmd(writeCmd: CmdPtReal, sendMills: Long) {
        //将指令集收集起来,快速连续发送可能只会回复最后一条
        IotOpManager().writeCmd(writeCmd)
        //重置iot相关标志位
    }

    private fun dpSwitch() {
        val selected: Boolean = !newDpBinding.ivDpSwitch4ThDetailNew.isSelected
        ChartVisibleConfig.read().setVisible(sku, ChartVisibleConfig.CHART_TYPE_DEW_P, selected)
        refreshDewPView()
    }

    private fun vpdSwitch() {
        val selected: Boolean = !newVpdBinding.ivVpdSwitch4ThDetailNew.isSelected
        ChartVisibleConfig.read().setVisible(sku, ChartVisibleConfig.CHART_TYPE_VPD, selected)
        refreshVpdView()
    }

    val bluetoothAdapter: BluetoothAdapter by lazy {
        val bluetoothManager: BluetoothManager =
            getSystemService(BLUETOOTH_SERVICE) as BluetoothManager
        bluetoothManager.adapter
    }

    fun isBluetoothEnabled(context: Context): Boolean {
        return bluetoothAdapter.isEnabled == true
    }
}