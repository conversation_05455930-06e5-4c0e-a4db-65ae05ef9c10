package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读取设备硬件版本号的事件
 */
class EventHardVersion(result: Boolean, write: <PERSON>olean, commandType: Byte, proType: Byte) :
        AbsControllerEvent(result, write, commandType, proType, !write) {

    var hardVersion: String = ""

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventHardVersion(false, write, commandType, proType))
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, hardVersion: String) {
            val event = EventHardVersion(true, write, commandType, proType)
            event.hardVersion = hardVersion
            EventBus.getDefault().post(event)
        }
    }
}