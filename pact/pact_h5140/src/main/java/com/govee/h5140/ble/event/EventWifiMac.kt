package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/30
 * @description 从5106设备读取设wifi mac的事件
 */
class EventWifiMac(result: Boolean, write: <PERSON>olean, commandType: Byte, proType: Byte) :
        AbsControllerEvent(result, write, commandType, proType, false) {

    var mac = ""

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventWifiMac(false, write, commandType, proType))
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, mac: String) {
            val event = EventWifiMac(true, write, commandType, proType)
            event.mac = mac
            EventBus.getDefault().post(event)
        }
    }
}