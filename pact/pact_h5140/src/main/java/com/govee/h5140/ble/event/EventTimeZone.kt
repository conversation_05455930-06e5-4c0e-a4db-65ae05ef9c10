package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/6/30
 * @description 向5106设备写入手机时间时区的事件
 */
class EventTimeZone(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, true) {

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventTimeZone(false, write, commandType, proType))
        }

        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte) {
            val event = EventTimeZone(result, true, commandType, proType)
            EventBus.getDefault().post(event)
        }
    }
}