package com.govee.h5140.add

import android.app.Activity
import android.bluetooth.BluetoothDevice
import android.os.Bundle
import android.view.View
import android.view.ViewGroup.MarginLayoutParams
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.AlphaAnimation
import android.view.animation.Animation
import android.widget.ImageView
import com.govee.base2home.ac.ActivityMgr
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.device.IDeviceNet
import com.govee.base2home.device.net.DeviceBindRequest
import com.govee.base2home.device.net.DeviceBindResponse
import com.govee.base2home.main.AbsDevice
import com.govee.base2home.main.DeviceExtMode
import com.govee.base2home.pact.GoodsType
import com.govee.base2newth.AbsThBle
import com.govee.base2newth.bbq.config.SecretKeyConfig
import com.govee.base2newth.data.Co2LevelInfo
import com.govee.base2newth.data.Co2WarnInfo
import com.govee.base2newth.data.H5140C02WarnConfig
import com.govee.base2newth.ui.AbsThPairAc4Secret
import com.govee.bind.SafeBindMgr
import com.govee.bind.bean.ConfirmGidReq
import com.govee.h5140.Constants5140
import com.govee.h5140.ble.ThpBleCommManager.Companion.getInstance
import com.govee.h5140.ble.command.CommandAirNotify
import com.govee.h5140.ble.command.CommandCo2SetGrade
import com.govee.h5140.ble.command.CommandCo2Warning
import com.govee.h5140.ble.command.CommandDeviceId
import com.govee.h5140.ble.command.CommandHardVersion
import com.govee.h5140.ble.command.CommandHumCali
import com.govee.h5140.ble.command.CommandHumWarning
import com.govee.h5140.ble.command.CommandSoftVersion
import com.govee.h5140.ble.command.CommandSyncTime
import com.govee.h5140.ble.command.CommandTemCali
import com.govee.h5140.ble.command.CommandTemWarning
import com.govee.h5140.ble.command.CommandTimeZone
import com.govee.h5140.ble.command.CommandWifiMac
import com.govee.h5140.ble.event.EventAirNotify
import com.govee.h5140.ble.event.EventCo2SetGrade
import com.govee.h5140.ble.event.EventCo2Warning
import com.govee.h5140.ble.event.EventDeviceId
import com.govee.h5140.ble.event.EventHardVersion
import com.govee.h5140.ble.event.EventHumCali
import com.govee.h5140.ble.event.EventHumWarning
import com.govee.h5140.ble.event.EventSoftVersion
import com.govee.h5140.ble.event.EventSyncTime
import com.govee.h5140.ble.event.EventTemCali
import com.govee.h5140.ble.event.EventTemWarning
import com.govee.h5140.ble.event.EventTimeZone
import com.govee.h5140.ble.event.EventWifiMac
import com.govee.thnew.ble.controller.Controller4WifiHv
import com.govee.thnew.ble.controller.Controller4WifiSv
import com.govee.thnew.ble.event.Event4WifiHv
import com.govee.thnew.ble.event.Event4WifiSv
import com.govee.thnew.gidsafe.BindDevServer4th
import com.govee.thnew.gidsafe.GidSafeManager
import com.govee.ui.R
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.Cache
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.infra.SafeLog.Companion.d
import com.ihoment.base2app.network.Network.IHCallBack
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.JsonUtil
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 5106设备与手机配对页面
 */
class Activity4Pair : AbsThPairAc4Secret() {

    private var addInfo: AddInfo? = null
    private var absDevice: AbsDevice? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        findViewById<ImageView>(com.govee.base2home.R.id.iv_pair_indicator_icon)?.run {
            if (this.layoutParams is MarginLayoutParams) {
                val marLp = this.layoutParams as MarginLayoutParams
                marLp.leftMargin = (AppUtil.getScreenWidth() * (70 / 375.0f)).toInt()
                marLp.topMargin = (AppUtil.getScreenWidth() * (180 / 375.0f)).toInt()
                this.layoutParams = marLp
            }
            ResUtil.setImageResource(
                this,
                com.govee.ui.R.mipmap.new_add_pics_sensor_set_5106_light
            )
            this.startAnimation(generateAnimation())
            this.visibility = View.VISIBLE
        }
    }

    override fun readDeviceInfo() {
        val controllers = arrayOf(
            CommandTimeZone(),
            CommandSyncTime(),
            CommandHardVersion(),
            CommandSoftVersion(),
            CommandCo2SetGrade(),
            CommandCo2Warning(),
            CommandAirNotify(),
            CommandTemWarning(),
            CommandHumWarning(),
            CommandTemCali(),
            CommandHumCali(),
            CommandWifiMac(),
            Controller4WifiSv(),
            Controller4WifiHv(),
            CommandDeviceId()
        )
        ble.startControllers(*controllers)
    }

    override fun doCheckPermissionPre() {
        val intent = intent
        addInfo = intent.getParcelableExtra(intent_ac_key_addInfo)
        super.doCheckPermissionPre()
    }

    override fun finishAc() {
        finish()
    }

    override fun getBle(): AbsThBle {
        return getInstance()
    }

    override fun getTipsStr(): String {
        return ResUtil.getString(R.string.h5140_pair_device_tips)
    }

    override fun getPairIconRes(): Int {
        return com.govee.ui.R.mipmap.new_add_pics_sensor_set_5140
    }

    override fun rebind() {
        bindDeviceStep2()
    }

    private fun toBindDevice() {
        val deviceExt = DeviceExtInfo()
        addInfo?.let {
            deviceExt.deviceName = it.deviceName
            deviceExt.bleSoftVersion = it.versionSoft
            deviceExt.bleHardVersion = it.versionHard
            deviceExt.pactType = it.pactType
            deviceExt.pactCode = it.pactCode
            deviceExt.bleName = it.bleName
            deviceExt.address = it.bleAddress
            deviceExt.co2Min = it.co2Min
            deviceExt.co2Max = it.co2Max
            deviceExt.co2Warning = it.co2Warning
            deviceExt.airQualityOnOff = it.airQualityOnOff
            deviceExt.temMin = it.temMin
            deviceExt.temMax = it.temMax
            deviceExt.temWarning = it.temWarning
            deviceExt.humMin = it.humMin
            deviceExt.humMax = it.humMax
            deviceExt.humWarning = it.humWarning
            deviceExt.temCali = it.temCalibration
            deviceExt.humCali = it.humCalibration
            deviceExt.wifiMac = it.wifiMac
        }
        val deviceExtMode = DeviceExtMode("{}", JsonUtil.toJson(deviceExt))
        absDevice = AbsDevice(
            addInfo?.deviceId ?: "",
            addInfo?.sku ?: "",
            addInfo?.versionHard ?: "",
            addInfo?.versionSoft ?: "",
            addInfo?.deviceName ?: "",
            addInfo?.goodsType ?: GoodsType.GOODES_TYPE_NO_SUPPORT,
            deviceExtMode
        )
        bindDeviceStep2()
    }

    override fun saveSecretKey(secretCode: String) {
        //读操作；获取成功密钥，存储密钥
        addInfo!!.secretCode = secretCode
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "secretCode = " + addInfo!!.secretCode)
        }
        val address = bluetoothDevice.address
        SecretKeyConfig.read().saveSecretKey(address, addInfo!!.secretCode)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSyncTime(event: EventSyncTime) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSyncTime()")
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTimeZone(event: EventTimeZone) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventTimeZone()")
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventHardVersion(event: EventHardVersion) {
        if (event.isResult) {
            val hardVersion: String = event.hardVersion
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventHardVersion() hardVersion = $hardVersion")
            }
            addInfo!!.versionHard = hardVersion
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventSoftVersion(event: EventSoftVersion) {
        if (event.isResult) {
            val softVersion: String = event.softVersion
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventSoftVersion() softVersion = $softVersion")
            }
            addInfo!!.versionSoft = softVersion
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventPm25Warning(event: EventCo2Warning) {
        if (event.isResult) {
            val openWarning: Boolean = event.openWarning
            val minCo2: Int = event.minCo2
            val maxCo2: Int = event.maxCo2
            if (LogInfra.openLog()) {
                LogInfra.Log.i(
                    TAG,
                    "onEventPm25Warning() openWarning = $openWarning ; minCo2 = $minCo2 ; maxCo2 = $maxCo2"
                )
            }
            addInfo!!.co2Warning = openWarning
            addInfo!!.co2Min = Constants5140.CO2_MIN_VALUE
            addInfo!!.co2Max = maxCo2
            addInfo!!.alarmInterval = event.alarmInterval
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventCo2GradeLevel(event: EventCo2SetGrade) {
        if (event.isResult) {
            addInfo!!.co2LevelLower = event.minLevel
            addInfo!!.co2LevelUpper = event.maxLevel
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTemWarning(event: EventTemWarning) {
        if (event.isResult) {
            val openWarning: Boolean = event.openWarning
            val minTem: Int = event.minTem
            val maxTem: Int = event.maxTem
            if (LogInfra.openLog()) {
                LogInfra.Log.i(
                    TAG,
                    "onEventTemWarning() openWarning = $openWarning ; minTem = $minTem ; maxTem = $maxTem"
                )
            }
            addInfo!!.temWarning = openWarning
            addInfo!!.temMin = minTem
            addInfo!!.temMax = maxTem
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventHumWarning(event: EventHumWarning) {
        if (event.isResult) {
            val openWarning: Boolean = event.openWarning
            val minHum: Int = event.minHum
            val maxHum: Int = event.maxHum
            if (LogInfra.openLog()) {
                LogInfra.Log.i(
                    TAG,
                    "onEventHumWarning() openWarning = $openWarning ; minHum = $minHum ; maxHum = $maxHum"
                )
            }
            addInfo!!.humWarning = openWarning
            addInfo!!.humMin = minHum
            addInfo!!.humMax = maxHum
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventTemCali(event: EventTemCali) {
        if (event.isResult) {
            val temCali: Int = event.temCali
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventTemCali() temCali = $temCali")
            }
            addInfo!!.temCalibration = temCali
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventHumCali(event: EventHumCali) {
        if (event.isResult) {
            val humCali: Int = event.humCali
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventHumCali() humCali = $humCali")
            }
            addInfo!!.humCalibration = humCali
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onWifiMacEvent(event: EventWifiMac) {
        if (event.isResult) {
            val mac: String = event.mac
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onWifiMacEvent() mac = $mac")
            }
            addInfo!!.wifiMac = mac
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventWifiSoftVersion(event: Event4WifiSv) {
        if (event.isResult) {
            val wifiSoftVersion: String = event.wifiSoftVersion
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWifiSoftVersion() mac = $wifiSoftVersion")
            }
            addInfo!!.wifiSoftVersion = wifiSoftVersion
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventWifiHardVersion(event: Event4WifiHv) {
        if (event.isResult) {
            val wifiHardVersion: String = event.wifiHardVersion
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWifiHardVersion() mac = $wifiHardVersion")
            }
            addInfo!!.wifiHardVersion = wifiHardVersion
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventAirNotify(event: EventAirNotify) {
        val result = event.isResult
        if (result) {
            addInfo!!.airQualityOnOff = if (event.switch) 1 else 0
        }
        ble.controllerEvent(event)
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventDeviceId(event: EventDeviceId) {
        val result = event.isResult
        if (result) {
            val deviceId = event.deviceId
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventDeviceId() deviceId = $deviceId")
            }
            addInfo!!.deviceId = deviceId!!
            toBindDevice()
        }
        ble.controllerEvent(event)
    }

    private fun bindDevice(absDevice: AbsDevice?) {
        SafeLog.i(TAG, "SecretKeyController上传 :" + JsonUtil.toJson(absDevice))
        d("onDevice4BindThV1") { "使用v1绑定设备" }
        val request = DeviceBindRequest(transactions.createTransaction(), absDevice)
        Cache.get(IDeviceNet::class.java).bindDevice(request).enqueue(IHCallBack(request))
    }

    private fun bindDeviceStep2() {
        val thNewAddInfo = com.govee.thnew.add.AddInfo()
        addInfo?.let {
            thNewAddInfo.sku = it.sku
            thNewAddInfo.device = it.deviceId
            thNewAddInfo.goodsType = it.goodsType
            thNewAddInfo.deviceName = it.deviceName
            thNewAddInfo.bleName = it.bleName
            thNewAddInfo.address = it.bleAddress
            thNewAddInfo.bleSoftVersion = it.versionSoft
            thNewAddInfo.bleHardVersion = it.versionHard
            thNewAddInfo.wifiMac = it.wifiMac
            thNewAddInfo.pactType = it.pactType
            thNewAddInfo.pactCode = it.pactCode
            thNewAddInfo.secretCode = it.secretCode
            thNewAddInfo.wifiSoftVersion = it.wifiSoftVersion
            thNewAddInfo.wifiHardVersion = it.wifiHardVersion
            thNewAddInfo.airQualityOnOff = it.airQualityOnOff
            thNewAddInfo.co2LevelLower = it.co2LevelLower
            thNewAddInfo.co2LevelUpper = it.co2LevelUpper
            thNewAddInfo.co2Min = it.co2Min
            thNewAddInfo.co2Max = it.co2Max
            thNewAddInfo.co2Warning = it.co2Warning
        }
        val bindImpl = BindDevServer4th(this, absDevice!!, thNewAddInfo, { bindResult, _ ->
            if (bindResult) {
                SafeLog.d(TAG) { "绑定成功!" }
                absDevice?.let {
                    H5140C02WarnConfig.updateCo2WarnInfo(it.sku, it.device, Co2WarnInfo())
                    H5140C02WarnConfig.updateCo2LevelInfo(it.sku, it.device, Co2LevelInfo())
                }
                beBindSuc()
            } else {
                //关闭连接弹窗
                SafeLog.e(TAG) { "绑定失败!" }
            }
        })
        SafeLog.e(TAG) { "开始gid校验" }
        SafeBindMgr.bindDeviceGidCheck(
            GidSafeManager(ble),
            ConfirmGidReq.createConfirmGidReq(absDevice!!, thNewAddInfo.wifiHardVersion, thNewAddInfo.wifiSoftVersion),
            thNewAddInfo.wifiMac,
            bindImpl
        ) {
            SafeLog.e(TAG) { "bindDeviceGidCheck 结束!" }
            bindDevice(absDevice)
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onDevice4BindThV1(response: DeviceBindResponse) {
        d("onDevice4BindThV1") { "v1绑定设备成功1" }
        if (!transactions.isMyTransaction(response)) return
        d("onDevice4BindThV1") { "v1绑定设备成功2" }
        val request = response.getRequest()
        //统计绑定成功
        AnalyticsRecorder.getInstance().recordTimes(EventKey.bind_success, ParamKey.sku, request.sku)
        //设置设备名称为默认名称
        beBindSuc()
        toast(response.message)
    }

    override fun beBindSuc() {
        val inBackground = BaseApplication.getBaseApplication().isInBackground
        if (inBackground) {
            //应用处于后台，则不跳转
            return
        }
        ActivityMgr.getInstance().finishAllExceptMain()
        destroy()
        Activity4NameDevice.jump2DeviceNameAc(this, addInfo)
    }

    companion object {
        private const val intent_ac_key_addInfo = "intent_ac_key_addInfo"
        private const val ssid_hint_flashing_time_mills: Long = 1000
        fun jump2PairAc(activity: Activity?, addInfo: AddInfo, bluetoothDevice: BluetoothDevice?) {
            val bundle = makeAcBundle(addInfo.sku, bluetoothDevice)
            bundle.putParcelable(intent_ac_key_addInfo, addInfo)
            JumpUtil.jump(activity, Activity4Pair::class.java, bundle)
        }
    }

    private fun generateAnimation(): Animation {
        val startAlpha = ResUtil.getInt(R.integer.font_style_51_3_alpha) * 1.0f / 100
        val endAlpha = ResUtil.getInt(R.integer.font_style_51_4_alpha) * 1.0f / 100
        val animation: Animation = AlphaAnimation(startAlpha, endAlpha)
        animation.repeatCount = Animation.INFINITE
        animation.duration = ssid_hint_flashing_time_mills
        animation.interpolator = AccelerateDecelerateInterpolator()
        return animation
    }
}