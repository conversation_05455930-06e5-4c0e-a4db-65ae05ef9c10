package com.govee.h5140.widget

import android.annotation.SuppressLint
import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import com.govee.base2home.Constant4L5
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.temUnit.EventThWidgetDataChanged
import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.util.NumberUtil
import com.govee.base2home.util.StrUtil
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ext.gone
import com.govee.base2kt.ext.setVisibility
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.other.Config4LastThValue
import com.govee.h5140.databinding.H5140Layout4ThTopRealInfoViewNewBinding
import com.govee.ui.R
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import kotlin.math.max

/**
 * <AUTHOR>
 * @date created on 2024/3/29
 * @description 温湿度计=->顶部实时信息展示控件(新版)
 */
class Co2ThTopRealInfoView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : FrameLayout(context, attrs, defStyleAttr) {

    private var binding: H5140Layout4ThTopRealInfoViewNewBinding
    private var infoType = THP_TYPE
    private var sku: String = ""
    private var device: String = ""

    //缓存上一次时间值
    private var cacheLastTime = -1L
    private var cacheTem = 0
    private var cacheHum = 0
    private var cacheCo2 = 0

    init {
        EventBus.getDefault().register(this)
        binding = H5140Layout4ThTopRealInfoViewNewBinding.inflate(
            LayoutInflater.from(context),
            this,
            true
        )
    }

    companion object {
        /**
         * 信息类型
         */
        private const val THP_TYPE = 1
    }

    /**
     * 初始化信息
     */
    fun init(sku: String, device: String) {
        this.sku = sku
        this.device = device
        infoType = THP_TYPE
        Config4LastThValue.Companion.getConfig().getLastTh(sku, device)?.let {
            updateRealInfo(it.lastTime, it.tem, it.hum, it.pm)
        }
    }

    /**
     * 更新实时信息
     * 备注：在这之前要先调用init方法
     */
    fun updateRealInfo(time: Long, tem: Int, hum: Int, co2: Int) {
        //存在部分老sku,心跳读不到温湿度值，此时tem==0,hum==0,这个情况过滤掉
        if ((tem == 0 && hum == 0) || (!isValidTime(time))) {
            cacheLastTime = if (isValidTime(time) && time > cacheLastTime) {
                time
            } else {
                0
            }
            cacheTem = 0
            cacheHum = 0
            cacheCo2 = 0
        } else {
            if (isValidTime(time) && time > cacheLastTime) {
                cacheLastTime = time
                cacheTem = tem
                cacheHum = hum
                cacheCo2 = co2
            }
        }
        refreshRealInfo()
    }

    private fun isValidTime(time: Long): Boolean {
        //H5106的时间取的为本地的当前时间，故这里的时间比较时，将当前时间滞后10s
        return time > 0 && time < (System.currentTimeMillis() + 10 * 1000L)
    }

    @SuppressLint("SetTextI18n")
    private fun refreshRealInfo() {
        when (infoType) {
            THP_TYPE -> {
                binding.tvTemValueV24ThDtri.text = getTemRealStr(cacheTem, cacheHum).second
                binding.tvTemUnitV24ThDtri.text = getTemRealStr(cacheTem, cacheHum).third
                binding.tvHumValueV24ThDtri.text = getHumRealStr(cacheHum).second
                binding.tvPm25ValueV24ThDtri.text = getCo2RealStr(cacheCo2).second
            }
        }
        //无效数据时，时间亦不显示
        val updateTimeLabel = if (isValidTime(cacheLastTime) && cacheHum > 0) {
            ResUtil.getStringFormat(
                R.string.h5072_last_sync_time,
                TimeFormatM.getInstance().formatTimeToHMMD(cacheLastTime)
            )
        } else {
            ResUtil.getStringFormat(R.string.h5072_last_sync_time, "--")
        }
        binding.tvRefreshTimeValue4ThDtri.text = updateTimeLabel
    }

    private fun getTemRealStr(tem: Int, hum: Int): Triple<Boolean, String, String> {
        val warningRang = WarnConfig.read().queryWarningRangeByKey(sku, device)
        val temUnitStr = if (isFahOpen()) StrUtil.getTemUnitFah() else StrUtil.getTemUnitCel()
        return if (hum <= 0 || warningRang == null) {
            val curTemStr = ResUtil.getString(R.string.h5072_chart_text_def)
            Triple(false, curTemStr, temUnitStr)
        } else {
            val realTemValue = NumberUtil.getTemValue(isFahOpen(), tem, warningRang.temCali)
            val realTemStr = NumberUtil.getValidFloatByOnePoint(realTemValue).toString()
            val isWarningTem =
                realTemValue < warningRang.temMin || realTemValue > warningRang.temMax
            Triple(isWarningTem, realTemStr, temUnitStr)
        }
    }

    private fun getHumRealStr(hum: Int): Pair<Boolean, String> {
        val warningRang = WarnConfig.read().queryWarningRangeByKey(sku, device)
        return if (hum <= 0 || warningRang == null) {
            val realHumStr = ResUtil.getString(R.string.h5072_chart_text_def)
            Pair(false, realHumStr)
        } else {
            val realHumValue = NumberUtil.getHumValue(hum, warningRang.humCali)
            val realHumStr = NumberUtil.getValidFloatByOnePoint(realHumValue).toString()
            val isWarningHum =
                realHumValue < warningRang.humMin || realHumValue > warningRang.humMax
            Pair(isWarningHum, realHumStr)
        }
    }

    private fun getCo2RealStr(co2: Int): Pair<Boolean, String> {
        val str = if (co2 <= 0 || co2 == 65535) {
            "--"
        } else {
            co2.toString()
        }
        return Pair(false, str)
    }

    private fun isFahOpen(): Boolean {
        return TemUnitConfig.read().isTemUnitFah(sku, device)
    }

    /**
     * 修改温度单位、校准温度值等操作引起的刷新实时信息值
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventThWidgetDataChanged(event: EventThWidgetDataChanged) {
        refreshRealInfo()
    }

    override fun onDetachedFromWindow() {
        EventBus.getDefault().unregister(this)
        super.onDetachedFromWindow()
    }
}
