package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import com.govee.h5140.ble.command.CommandSoundLevel.Companion.LEVEL_0
import org.greenrobot.eventbus.EventBus

class EventSoundLevel(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, !write) {

    var switch: Boolean = false

    var level : Int = LEVEL_0

    companion object{

        /**
         * 用来做从sp读取的刷新操作
         */
        fun sendRefreshEvent() {
            EventBus.getDefault().post(EventTempUnit(true, true, 0, 0).apply {
                onlyRefreshFromSp = true
            })
        }

        fun sendSuc(write: Boolean, commandType: Byte, proType: Byte, switch: Boolean, level: Int) {
            val event = EventSoundLevel(true, write, commandType, proType)
            event.level = level
            event.switch = switch
            EventBus.getDefault().post(event)
        }

        fun sendWriteResult(result: Boolean, commandType: Byte, proType: Byte,  switch: Boolean, level: Int) {
            val event = EventSoundLevel(result, true, commandType, proType)
            event.level = level
            event.switch = switch
            EventBus.getDefault().post(event)
        }

        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventSoundLevel(false, write, commandType, proType))
        }

    }

}