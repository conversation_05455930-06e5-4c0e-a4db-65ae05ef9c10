package com.govee.h5140.add

import java.util.*

/**
 * <AUTHOR>
 * @date created on 2022/5/31
 * @description 时区获取工具类
 */
object TimeZoneUtils {

    /**
     * 获取地区所在时区
     * @param isStrFormat true:字符形式，false:数组形式
     */
    fun getGmtTimeZone(isStrFormat: Boolean): Any {
        val tz = TimeZone.getDefault()
        return if (isStrFormat) {
            createGmtOffsetString(
                includeGmt = true,
                includeMinuteSeparator = true,
                offsetMillis = tz.rawOffset
            )
        } else {
            createGmtOffsetArray(tz.rawOffset)
        }
    }

    /**
     * 获取字符串形式的时区信息,如：GMT +08:00
     */
    private fun createGmtOffsetString(
        includeGmt: Boolean,
        includeMinuteSeparator: Boolean,
        offsetMillis: Int
    ): String {
        var offsetMinutes = offsetMillis / 60000
        var sign = '+'
        if (offsetMinutes < 0) {
            sign = '-'
            offsetMinutes = -offsetMinutes
        }
        val builder = StringBuilder(9)
        if (includeGmt) {
            builder.append("GMT")
        }
        builder.append(sign)
        appendNumber(builder, 2, offsetMinutes / 60)
        if (includeMinuteSeparator) {
            builder.append(':')
        }
        appendNumber(builder, 2, offsetMinutes % 60)
        return builder.toString()
    }

    /**
     * 生成int数组形式的时区信息，如：{-8,0}
     */
    private fun createGmtOffsetArray(offsetMillis: Int): Array<Int> {
        var hour = 0
        var minute = 0

        var offsetMinutes = offsetMillis / 60000
        var sign = '+'
        if (offsetMinutes < 0) {
            sign = '-'
            offsetMinutes = -offsetMinutes
        }
        //小时偏移
        val hourStr = StringBuilder()
        hourStr.append(sign)
        hour = appendNumber(hourStr, 2, offsetMinutes / 60).toString().toInt()
        //分钟偏移
        minute = appendNumber(StringBuilder(), 2, offsetMinutes % 60).toString().toInt()
        return arrayOf(hour, minute)
    }

    private fun appendNumber(builder: StringBuilder, count: Int, value: Int): StringBuilder {
        val string = value.toString()
        for (i in 0 until count - string.length) {
            builder.append('0')
        }
        builder.append(string)
        return builder
    }

    /**
     * 获取地区时间相对0时区的时间偏移量
     */
    fun getOffsetTime(): IntArray {
        val offsetMinutes =
            TimeZone.getDefault().getOffset(System.currentTimeMillis()) / (60 * 1000)
        val hour = offsetMinutes / 60
        val minute = offsetMinutes % 60
        return intArrayOf(hour, minute)
    }
}