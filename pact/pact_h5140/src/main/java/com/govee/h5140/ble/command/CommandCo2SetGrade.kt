package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsSingleController
import com.govee.h5140.Constants5140
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventCo2SetGrade
import com.ihoment.base2app.infra.SafeLog

class CommandCo2SetGrade : AbsSingleController {

    companion object {
        private const val TAG = "CommandCo2SetGrade"
    }

    private var minLevel = Constants5140.CO2_GRADE_MIN
    private var maxLevel = Constants5140.CO2_GRADE_MAX

    constructor(min: Int, max: Int) : super(true) {
        this.minLevel = min
        this.maxLevel = max
    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray? {
        val minLevelByteArr = BleUtil.getSignedBytesFor2(minLevel, false)
        val maxLevelByteArr = BleUtil.getSignedBytesFor2(maxLevel, false)
        return byteArrayOf(
            minLevelByteArr[0],
            minLevelByteArr[1],
            maxLevelByteArr[0],
            maxLevelByteArr[1]
        )
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventCo2SetGrade.sendWriteResult(suc, commandType, proType, minLevel, maxLevel)
        return true
    }

    override fun fail() {
        EventCo2SetGrade.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_CO2_GRADE
    }

    override fun parseValidBytes(validBytes: ByteArray?): Boolean {
        SafeLog.d(TAG) { "parseValidBytes(),validBytes=${BleUtil.bytesToHexString(validBytes)}" }
        if (validBytes == null) {
            return true
        }
        minLevel = BleUtil.convertTwoBytesToShort(validBytes[0], validBytes[1])
        maxLevel = BleUtil.convertTwoBytesToShort(validBytes[2], validBytes[3])
        EventCo2SetGrade.sendSuc(isWrite, commandType, proType, minLevel, maxLevel)
        return true
    }
}