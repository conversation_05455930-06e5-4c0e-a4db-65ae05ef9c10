package com.govee.h5140.add

import android.text.TextUtils
import com.govee.base2home.Constant
import com.govee.base2home.pact.BleUtil
import com.govee.base2light.ble.controller.EventDynamicApiSupport
import com.govee.base2newth.AbsMultiController
import com.govee.base2newth.BleThProtocol
import com.govee.base2newth.EventWifi
import com.ihoment.base2app.config.RunMode
import com.ihoment.base2app.infra.LogInfra

/**
 * Create by xieyingwu on 2020/3/18
 * wifi指令$
 */
class Command5140Wifi @JvmOverloads constructor(
    ssid: String,
    password: String?,
    runModeV: Int = 0
) : AbsMultiController() {

    companion object {
        private const val TAG = "MultiWifiController"
        private const val SSID = 0x01.toByte() /*设置ssid*/
        private const val PASSWORD = 0x02.toByte() /*设置password*/
        private const val ZONE: Byte = 0x04 //设置时区
        private const val FLAG = 0x03.toByte() /*通知发送完成*/
        private const val LENGTH_TYPE = 0xff.toByte() /*长度定义*/
        private const val DYNAMIC_DOMAIN = 0x05.toByte()
        private const val validBytes = 15 /*15个有效字节*/
    }

    init {
        multiBytes.clear()
        /*ssid包*/
        makeSsidPackages(ssid)
        makePasswordPackages(password)
        makeZonePackages()
        makeFlagPackages(runModeV)
    }

    /**
     * 生成时区指令包
     */
    private fun makeZonePackages() {
        val timeZoneInfo = TimeZoneUtils.getOffsetTime()
        val result = ByteArray(20)
        result[0] = proType
        result[1] = commandType
        result[2] = ZONE
        result[3] = timeZoneInfo[0].toByte()
        result[4] = timeZoneInfo[1].toByte()
        //生成校验位
        result[19] = BleUtil.getBCC(result, 19)
        multiBytes.add(result)
    }

    /**
     * 生成动态域名指令包
     */
    private fun makeDynamicDomain() {
        val domain = dynamicApi()
        val bytes = domain.toByteArray()
        val length = bytes.size
        //生成域名长度包
        val lengthBytes = ByteArray(2)
        lengthBytes[0] = LENGTH_TYPE
        lengthBytes[1] = length.toByte()
        val domainBytesLength = getSsidBytes(lengthBytes)
        multiBytes.add(domainBytesLength)
        //生成域名有效数据包
        var lastPacksLength = length % validBytes
        val packs = length / validBytes + if (lastPacksLength == 0) 0 else 1
        if (lastPacksLength == 0) {
            //表明最后一个包也是15个有效字节
            lastPacksLength = validBytes
        }
        var srcPos = 0
        for (i in 1..packs) {
            val packsBytes = ByteArray(16)
            packsBytes[0] = i.toByte()
            if (i == packs) {
                System.arraycopy(bytes, srcPos, packsBytes, 1, lastPacksLength)
            } else {
                System.arraycopy(bytes, srcPos, packsBytes, 1,
                    validBytes
                )
            }
            srcPos += validBytes
            val domainBytes = getDomainBytes(packsBytes)
            multiBytes.add(domainBytes)
        }
    }

    private fun getDomainBytes(nextBytes: ByteArray): ByteArray {
        val result = ByteArray(20)
        result[0] = proType
        result[1] = commandType
        result[2] = DYNAMIC_DOMAIN
        val length = nextBytes.size
        System.arraycopy(nextBytes, 0, result, 3, length)
        result[19] = BleUtil.getBCC(result, 19)
        return result
    }

    private fun dynamicApi(): String {
        val isQaEnv = RunMode.getRunModel().number != RunMode.ci.number
        return EventDynamicApiSupport.getDynamicApi(if (isQaEnv) EventDynamicApiSupport.api_support_http else EventDynamicApiSupport.api_support_http)
    }

    private fun makeFlagPackages(runModeV: Int) {
        val flagBytes = getFlagBytes(runModeV)
        multiBytes.add(flagBytes)
    }

    private fun makePasswordPackages(wifiPassword: String?) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "makePasswordPackages() wifiPassword = " + wifiPassword)
        }
        if (TextUtils.isEmpty(wifiPassword)) {
            /*无需密码*/
            /*生成password长度*/
            val lenghtBytes = ByteArray(2)
            lenghtBytes[0] = LENGTH_TYPE
            val passwordBytesLength = getPasswordBytes(lenghtBytes)
            multiBytes.add(passwordBytesLength)
        } else {
            /*需要密码*/
            val bytes = wifiPassword!!.toByteArray()
            val length = bytes.size
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makePasswordPackages() length = " + length)
            }
            /*生成password长度*/
            val lenghtBytes = ByteArray(2)
            lenghtBytes[0] = LENGTH_TYPE
            lenghtBytes[1] = length.toByte()
            val passwordBytesLength = getPasswordBytes(lenghtBytes)
            multiBytes.add(passwordBytesLength)
            /*生成有效数据包*/
            var lastPacksLength: Int = length % validBytes
            val packs: Int = length / validBytes + (if (lastPacksLength == 0) 0 else 1)
            if (lastPacksLength == 0) {
                /*表明最后一个包也是15个有效字节*/
                lastPacksLength = validBytes
            }
            var srcPos = 0
            for (i in 1..packs) {
                val packsBytes = ByteArray(16)
                packsBytes[0] = i.toByte()
                if (i == packs) {
                    System.arraycopy(bytes, srcPos, packsBytes, 1, lastPacksLength)
                } else {
                    System.arraycopy(bytes, srcPos, packsBytes, 1, validBytes)
                }
                srcPos += validBytes
                val passwordBytes = getPasswordBytes(packsBytes)
                multiBytes.add(passwordBytes)
            }
        }
    }

    private fun makeSsidPackages(wifiName: String) {
        val bytes = wifiName.toByteArray()
        val length = bytes.size
        if (LogInfra.openLog()) {
            LogInfra.Log.i(
                TAG,
                "makeSsidPackages()wifiName = " + wifiName + " ; length = " + length
            )
        }
        /*生成ssid长度*/
        val lenghtBytes = ByteArray(2)
        lenghtBytes[0] = LENGTH_TYPE
        lenghtBytes[1] = length.toByte()
        val ssidBytesLength = getSsidBytes(lenghtBytes)
        multiBytes.add(ssidBytesLength)
        /*生成有效数据包*/
        var lastPacksLength: Int = length % validBytes
        val packs: Int = length / validBytes + (if (lastPacksLength == 0) 0 else 1)
        if (lastPacksLength == 0) {
            /*表明最后一个包也是15个有效字节*/
            lastPacksLength = validBytes
        }
        var srcPos = 0
        for (i in 1..packs) {
            val packsBytes = ByteArray(16)
            packsBytes[0] = i.toByte()
            if (i == packs) {
                System.arraycopy(bytes, srcPos, packsBytes, 1, lastPacksLength)
            } else {
                System.arraycopy(bytes, srcPos, packsBytes, 1, validBytes)
            }
            srcPos += validBytes
            val ssidBytes = getSsidBytes(packsBytes)
            multiBytes.add(ssidBytes)
        }
    }

    private fun getSsidBytes(nextBytes: ByteArray): ByteArray {
        val result = ByteArray(20)
        result[0] = getProType()
        result[1] = getCommandType()
        result[2] = SSID
        val length = nextBytes.size
        System.arraycopy(nextBytes, 0, result, 3, length)
        result[19] = BleUtil.getBCC(result, 19)
        return result
    }

    private fun getPasswordBytes(nextBytes: ByteArray): ByteArray {
        val result = ByteArray(20)
        result[0] = getProType()
        result[1] = getCommandType()
        result[2] = PASSWORD
        val length = nextBytes.size
        System.arraycopy(nextBytes, 0, result, 3, length)
        result[19] = BleUtil.getBCC(result, 19)
        return result
    }

    private fun getFlagBytes(runModeV: Int): ByteArray {
        val result = ByteArray(20)
        result[0] = getProType()
        result[1] = getCommandType()
        result[2] = FLAG
        val runModeByte =
            (if (runModeV == 0) Constant.getRunModeV0() else Constant.getRunModeV1()).toByte()
        result[3] = runModeByte
        result[19] = BleUtil.getBCC(result, 19)
        return result
    }

    override fun suc() {
        EventWifi.sendSuc(isWrite(), getCommandType(), getProType())
    }

    override fun fail() {
        EventWifi.sendFail(isWrite(), getCommandType(), getProType())
    }

    override fun getCommandType(): Byte {
        return BleThProtocol.value_multi_wifi
    }
}