package com.govee.h5140.widget

import android.content.Context
import android.graphics.Canvas
import android.util.AttributeSet
import android.view.View
import androidx.compose.ui.graphics.Paint
import androidx.compose.ui.graphics.PaintingStyle
import androidx.core.content.ContextCompat

/**
 * @author：YangQi.Chen
 * @date：2025/4/11 下午2:27
 * @description：
 */
class SeekBarBgView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = androidx.appcompat.R.attr.seekBarStyle
) : View(context, attrs, defStyleAttr) {
    private var mWidth = 0
    private var mHeight = 0
    private val greedPaint = Paint()

    init {
        greedPaint.style = PaintingStyle.Stroke
        greedPaint.isAntiAlias = true
        //greedPaint.color =
    }

    override fun onMeasure(widthMeasureSpec: Int, heightMeasureSpec: Int) {
        super.onMeasure(widthMeasureSpec, heightMeasureSpec)
        mWidth = measuredWidth
        mHeight = measuredHeight
    }

    override fun onSizeChanged(w: Int, h: Int, oldw: Int, oldh: Int) {
        super.onSizeChanged(w, h, oldw, oldh)
        mWidth = measuredWidth
        mHeight = measuredHeight
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        mWidth = measuredWidth
        mHeight = measuredHeight


    }
}