package com.govee.h5140.detail.chart

import com.govee.base2home.temUnit.TemUnitConfig
import com.govee.base2home.temUnit.TemperatureUnitType

/**
 * @author：YangQi.Chen
 * @date：2025/5/13 下午7:13
 * @description：h5140监听温度变化，上报服务器
 */
object H5140TempUnitHelper {
    var temUnit: TemperatureUnitType = TemperatureUnitType.Fahrenheit

    private var listener: TemUnitChangeListener? = null

    fun addListener(listener: TemUnitChangeListener) {
        this.listener = listener
    }

    fun removeListener() {
        this.listener = null
    }

    fun setTemUnit(sku: String, device: String, hewUnit: TemperatureUnitType) {
        val oldUnit = TemUnitConfig.read().getTemUnit(sku, device)
        if (hewUnit == oldUnit) {
            return
        }
        TemUnitConfig.read().setTemUnit(sku, device, hewUnit)
        listener?.temUnitChange()
    }

    interface TemUnitChangeListener {
        fun temUnitChange()
    }
}