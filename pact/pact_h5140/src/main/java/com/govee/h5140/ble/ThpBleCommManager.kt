package com.govee.h5140.ble

import com.govee.base2newth.AbsBleComm
import com.govee.base2newth.AbsHeartComm
import com.govee.base2newth.AbsMultiBleComm
import com.govee.base2newth.AbsNotifyComm
import com.govee.base2newth.AbsThBle
import com.govee.base2newth.IDataComm
import com.govee.base2newth.IOta
import com.govee.ble.event.EventBleConnect
import com.govee.h5140.detail.chart.AbsThpBleData

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 5140蓝牙通讯解析器管理类
 */
class ThpBleCommManager private constructor() : AbsThBle() {

    private object Builder {
        @JvmStatic
        val instance = ThpBleCommManager()
    }

    companion object {
        fun getInstance(): ThpBleCommManager {
            return Builder.instance
        }
    }

    public override fun getDataComm(): IDataComm {
        if (dataComm == null) {
            return ThpDataComm()
        }
        return dataComm
    }

    override fun getMultiBleComm(): AbsMultiBleComm {
        if (multiBleComm == null) {
            return ThpCommMulti()
        }
        return multiBleComm
    }

    override fun getNotifyComm(): AbsNotifyComm {
        if (notifyComm == null) {
            return ThpCommNotify()
        }
        return notifyComm
    }

    override fun getOta(): IOta? {
        return null
    }

    public override fun getHeart(): AbsHeartComm {
        if (heartComm == null) {
            return H5140HeartSender()
        }
        return heartComm
    }

    override fun getBleComm(): AbsBleComm {
        if (bleComm == null) {
            return ThpCommSingle()
        }
        return bleComm
    }

    override fun onBleConnectEvent(event: EventBleConnect?) {
        if (AbsThpBleData.isLoadThcd4Compare) {
            return
        }
        super.onBleConnectEvent(event)
    }
}