package com.govee.h5140.ble

import android.bluetooth.BluetoothGatt
import android.bluetooth.BluetoothGattCharacteristic
import com.govee.base2light.ble.ota.v3.OtaOpV3
import com.govee.base2newth.AbsThBle
import com.govee.base2newth.AbsThGattCallbackImp
import com.govee.h5140.ble.ThpBleCommManager.Companion.getInstance

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 5106设备ble 协议回调实现类(主要对ota升级和普通通讯作区分处理)
 */
class ThpGattCallbackImp : AbsThGattCallbackImp() {

    override fun getThBle(): AbsThBle {
        return getInstance()
    }

    override fun callCharacteristicChanged(
        gatt: BluetoothGatt,
        characteristic: BluetoothGattCharacteristic
    ) {
        if (OtaOpV3.getInstance().inOta()) {
            OtaOpV3.getInstance().onCharacteristicChanged(characteristic)
        } else {
            super.callCharacteristicChanged(gatt, characteristic)
        }
    }

    override fun callCharacteristicWrite(
        gatt: BluetoothGatt,
        characteristic: BluetoothGattCharacteristic,
        value: ByteArray,
        status: Int
    ) {
        if (OtaOpV3.getInstance().inOta()) {
            OtaOpV3.getInstance().onCharacteristicWrite(characteristic, status)
        } else {
            super.callCharacteristicWrite(gatt, characteristic, value, status)
        }
    }

    override fun onMtuChanged(gatt: BluetoothGatt, mtu: Int, status: Int) {
        if (OtaOpV3.getInstance().inOta()) {
            OtaOpV3.getInstance().onMtuChanged(mtu, status)
        } else {
            super.onMtuChanged(gatt, mtu, status)
        }
    }
}