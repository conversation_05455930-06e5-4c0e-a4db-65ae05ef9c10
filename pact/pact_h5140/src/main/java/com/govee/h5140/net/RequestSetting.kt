package com.govee.h5140.net

import com.govee.h5140.detail.setting.Ext4St
import com.ihoment.base2app.network.BaseRequest

/**
 * <AUTHOR>
 * @date created on 2022/5/31
 * @description 5106设备在设置页面同步设备信息的请求体
 */
class RequestSetting(transaction: String?, ext4St: Ext4St) : BaseRequest(transaction) {

    var sku: String? = ext4St.sku
    var device: String? = ext4St.device
    var co2Min: Int = ext4St.co2AlarmMin
    var co2Max: Int = ext4St.co2AlarmMax
    var co2Warning: Boolean = ext4St.co2AlarmSwitch
    var temMin: Int = ext4St.temMin
    var temMax: Int = ext4St.temMax
    var temWarning: Boolean = ext4St.temWarning
    var temCali: Int = ext4St.temCali
    var humMin: Int = ext4St.humMin
    var humMax: Int = ext4St.humMax
    var humWarning: Boolean = ext4St.humWarning
    var humCali: Int = ext4St.humCali
}