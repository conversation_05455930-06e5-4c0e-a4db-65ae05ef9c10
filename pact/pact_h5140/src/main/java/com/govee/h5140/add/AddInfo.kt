package com.govee.h5140.add

import android.os.Parcel
import android.os.Parcelable
import androidx.annotation.Keep
import com.govee.h5140.Constants5140

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 添加/绑定设备时的相关信息类
 */
@Keep
class AddInfo() : Parcelable {

    var sku: String = ""
    var deviceId: String = ""
    var goodsType = 0
    var deviceName: String = ""
    var versionSoft: String = ""
    var versionHard: String = ""
    var pactType = -100
    var pactCode = -100

    //co22.5预警相关
    var co2Min = -200
    var co2Max = 5000
    var co2Warning = false

    //空气指令开关默认为开
    var airQualityOnOff = 1
    var co2LevelLower = 1000
    var co2LevelUpper = 1400

    var humMin = 0
    var humMax = 0
    var humWarning = false
    var temMin = 0
    var temMax = 0
    var temWarning = false

    var humCalibration = 0
    var temCalibration = 0

    var bleAddress: String = ""
    var bleName: String = ""

    var secretCode: String = ""
    var wifiMac: String = ""
    var wifiSoftVersion = ""
    var wifiHardVersion = ""

    var alarmInterval = 10

//    var dndIsOpen = false
//    var dndStartHour = 22
//    var dndStartMinute = 0
//    var dndEndHour = 6
//    var dndEntMinute = 0

    constructor(parcel: Parcel) : this() {
        sku = parcel.readString().toString()
        deviceId = parcel.readString().toString()
        goodsType = parcel.readInt()
        deviceName = parcel.readString().toString()
        versionSoft = parcel.readString().toString()
        versionHard = parcel.readString().toString()
        pactType = parcel.readInt()
        pactCode = parcel.readInt()
        co2Min = parcel.readInt()
        co2Max = parcel.readInt()
        co2Warning = parcel.readByte() != 0.toByte()
        airQualityOnOff = parcel.readInt()
        co2LevelLower = parcel.readInt()
        co2LevelUpper = parcel.readInt()
        humMin = parcel.readInt()
        humMax = parcel.readInt()
        humWarning = parcel.readByte() != 0.toByte()
        temMin = parcel.readInt()
        temMax = parcel.readInt()
        temWarning = parcel.readByte() != 0.toByte()
        temCalibration = parcel.readInt()
        humCalibration = parcel.readInt()
        bleAddress = parcel.readString().toString()
        bleName = parcel.readString().toString()
        secretCode = parcel.readString().toString()
        wifiMac = parcel.readString().toString()
        wifiSoftVersion = parcel.readString().toString()
        wifiHardVersion = parcel.readString().toString()
        alarmInterval = parcel.readInt()
    }

    constructor(goodsType: Int, sku: String, bleName: String, address: String) : this() {
        this.goodsType = goodsType
        this.sku = sku
        this.bleName = bleName
        this.bleAddress = address
        this.temMin = Constants5140.TEM_MIN_VALUE * 100
        this.temMax = Constants5140.TEM_MAX_VALUE * 100
        this.humMin = Constants5140.HUM_MIN_VALUE * 100
        this.humMax = Constants5140.HUM_MAX_VALUE * 100
        this.co2Min = Constants5140.CO2_MIN_VALUE
        this.co2Max = Constants5140.CO2_MAX_VALUE
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {
        parcel.writeString(sku)
        parcel.writeString(deviceId)
        parcel.writeInt(goodsType)
        parcel.writeString(deviceName)
        parcel.writeString(versionSoft)
        parcel.writeString(versionHard)
        parcel.writeInt(pactType)
        parcel.writeInt(pactCode)
        parcel.writeInt(co2Min)
        parcel.writeInt(co2Max)
        parcel.writeByte(if (co2Warning) 1 else 0)
        parcel.writeInt(airQualityOnOff)
        parcel.writeInt(co2LevelLower)
        parcel.writeInt(co2LevelUpper)
        parcel.writeInt(humMin)
        parcel.writeInt(humMax)
        parcel.writeByte(if (humWarning) 1 else 0)
        parcel.writeInt(temMin)
        parcel.writeInt(temMax)
        parcel.writeByte(if (temWarning) 1 else 0)
        parcel.writeInt(temCalibration)
        parcel.writeInt(humCalibration)
        parcel.writeString(bleAddress)
        parcel.writeString(bleName)
        parcel.writeString(secretCode)
        parcel.writeString(wifiMac)
        parcel.writeString(wifiSoftVersion)
        parcel.writeString(wifiHardVersion)
        parcel.writeInt(alarmInterval)

    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<AddInfo> {
        override fun createFromParcel(parcel: Parcel): AddInfo {
            return AddInfo(parcel)
        }

        override fun newArray(size: Int): Array<AddInfo?> {
            return arrayOfNulls(size)
        }
    }
}