package com.govee.h5140.ble.command

import com.govee.base2newth.AbsOnlyWriteSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventClearData.Companion.sendWriteResult
import com.govee.h5140.ble.event.EventClearData.Companion.sendFail

/**
 * <AUTHOR>
 * @date created on 2022/6/2
 * @description 5106设备设置页面，发送清除数据的指令
 */
class CommandClearData : AbsOnlyWriteSingleController() {

    override fun translateWrite(): ByteArray? {
        return null
    }

    override fun onWriteResult(suc: Boolean): Bo<PERSON>an {
        sendWriteResult(suc, commandType, proType)
        return true
    }

    override fun fail() {
        sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_CLEAR_DATA
    }
}