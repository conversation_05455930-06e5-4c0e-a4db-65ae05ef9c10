package com.govee.h5140.ble.command

import com.govee.base2home.pact.BleUtil
import com.govee.base2newth.AbsSingleController
import com.govee.h5140.ble.BleProtocol
import com.govee.h5140.ble.event.EventHumWarning

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 从5106设备读/写湿度预警信息的指令，并解析其回复
 */
class CommandHumWarning : AbsSingleController {

    companion object {
        private const val TAG = "CommandHumWarning"
    }

    /**
     * 是否开启预警
     */
    private var openWarning = false
    private var minHum = 0
    private var maxHum = 0

    /**
     * 写操作
     */
    constructor(openWarning: Boolean, minHum: Int, maxHum: Int) : super(true) {
        this.minHum = minHum
        this.maxHum = maxHum
        this.openWarning = openWarning
    }

//    private fun checkHum(hum: Int): Int {
//        var hum = hum
//        val minHum = Constants5140.HUM_MIN_VALUE * 100
//        val maxHum = Constants5140.HUM_MAX_VALUE * 100
//        if (LogInfra.openLog() && (hum < minHum || hum > maxHum)) {
//            LogInfra.Log.e(TAG, "checkHum() hum is not in range! hum = $hum")
//        }
//        hum = hum.coerceAtLeast(minHum)
//        hum = hum.coerceAtMost(maxHum)
//        return hum
//    }

    /**
     * 读操作
     */
    constructor() : super(false) {}

    override fun translateWrite(): ByteArray {
        val openWarningValue = if (openWarning) 1.toByte() else 0.toByte()
        val minHumValues = BleUtil.getSignedBytesFor2(minHum, false)
        val maxHumValues = BleUtil.getSignedBytesFor2(maxHum, false)
        return byteArrayOf(openWarningValue, minHumValues[0], minHumValues[1], maxHumValues[0], maxHumValues[1])
    }

    override fun onWriteResult(suc: Boolean): Boolean {
        EventHumWarning.sendWriteResult(suc, commandType, proType, openWarning, minHum, maxHum)
        return true
    }

    override fun fail() {
        EventHumWarning.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleProtocol.VALUE_HUM_WARNING
    }

    override fun parseValidBytes(validBytes: ByteArray): Boolean {
        val openWarning = validBytes[0].toInt() == 1
        val minHum = BleUtil.convertTwoBytesToShort(validBytes[1], validBytes[2])
        val maxHum = BleUtil.convertTwoBytesToShort(validBytes[3], validBytes[4])
        EventHumWarning.sendSuc(isWrite, commandType, proType, openWarning, minHum, maxHum)
        return true
    }
}