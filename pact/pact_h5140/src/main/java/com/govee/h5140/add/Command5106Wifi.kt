package com.govee.h5140.add

import android.text.TextUtils
import com.govee.base2home.Constant
import com.govee.base2home.pact.BleUtil
import com.govee.base2light.ble.controller.EventDynamicApiSupport
import com.govee.base2newth.AbsMultiController
import com.govee.base2newth.BleThProtocol
import com.govee.base2newth.EventWifi
import com.govee.h5140.add.TimeZoneUtils.getGmtTimeZone
import com.govee.h5140.add.TimeZoneUtils.getOffsetTime
import com.ihoment.base2app.config.RunMode
import com.ihoment.base2app.infra.LogInfra

/**
 * <AUTHOR>
 * @date created on 2022/5/31
 * @description h5106设备配网相关指令控制器
 */
class Command5106Wifi @JvmOverloads constructor(ssid: String, password: String) :
    AbsMultiController() {

    companion object {
        private const val TAG = "Command5106Wifi"

        //设置ssid
        private const val SSID = 0x01.toByte()

        //设置password
        private const val PASSWORD = 0x02.toByte()

        //设置时区
        private const val ZONE = 0x04.toByte()

        //设置动态域名
        private const val DYNAMIC_DOMAIN = 0x05.toByte()

        //通知发送完成
        private const val FLAG = 0x03.toByte()

        //长度定义包指令标志
        private const val LENGTH_TYPE = 0xff.toByte()

        //有效字节长度
        private const val validBytes = 15
    }

    init {
        multiBytes.clear()
        makeSsidPackages(ssid)
        makePasswordPackages(password)
        makeZonePackages()
        makeDynamicDomain()
        makeFlagPackages()
    }

    /**
     * 生成ssid(wifi名)指令包
     *
     * @param wifiName
     */
    private fun makeSsidPackages(wifiName: String) {
        val bytes = wifiName.toByteArray()
        val length = bytes.size
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "makeSsidPackages()wifiName = $wifiName ; length = $length")
        }
        //生成ssid长度
        val lengthBytes = ByteArray(2)
        lengthBytes[0] = LENGTH_TYPE
        lengthBytes[1] = length.toByte()
        val ssidBytesLength = getSsidBytes(lengthBytes)
        multiBytes.add(ssidBytesLength)
        //生成有效数据包
        var lastPacksLength = length % validBytes
        val packs = length / validBytes + if (lastPacksLength == 0) 0 else 1
        if (lastPacksLength == 0) {
            //表明最后一个包也是15个有效字节
            lastPacksLength = validBytes
        }
        var srcPos = 0
        for (i in 1..packs) {
            val packsBytes = ByteArray(16)
            packsBytes[0] = i.toByte()
            if (i == packs) {
                System.arraycopy(bytes, srcPos, packsBytes, 1, lastPacksLength)
            } else {
                System.arraycopy(bytes, srcPos, packsBytes, 1, validBytes)
            }
            srcPos += validBytes
            val ssidBytes = getSsidBytes(packsBytes)
            multiBytes.add(ssidBytes)
        }
    }

    /**
     * 生成密码指令包
     *
     * @param wifiPassword
     */
    private fun makePasswordPackages(wifiPassword: String) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "makePasswordPackages() wifiPassword = $wifiPassword")
        }
        if (TextUtils.isEmpty(wifiPassword)) {
            //无需密码
            //生成password长度
            val lengthBytes = ByteArray(2)
            lengthBytes[0] = LENGTH_TYPE
            val passwordBytesLength = getPasswordBytes(lengthBytes)
            multiBytes.add(passwordBytesLength)
        } else {
            //需要密码
            val bytes = wifiPassword.toByteArray()
            val length = bytes.size
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makePasswordPackages() length = $length")
            }
            //生成password长度
            val lengthBytes = ByteArray(2)
            lengthBytes[0] = LENGTH_TYPE
            lengthBytes[1] = length.toByte()
            val passwordBytesLength = getPasswordBytes(lengthBytes)
            multiBytes.add(passwordBytesLength)
            //生成有效数据包
            var lastPacksLength = length % validBytes
            val packs = length / validBytes + if (lastPacksLength == 0) 0 else 1
            if (lastPacksLength == 0) {
                //表明最后一个包也是15个有效字节
                lastPacksLength = validBytes
            }
            var srcPos = 0
            for (i in 1..packs) {
                val packsBytes = ByteArray(16)
                packsBytes[0] = i.toByte()
                if (i == packs) {
                    System.arraycopy(bytes, srcPos, packsBytes, 1, lastPacksLength)
                } else {
                    System.arraycopy(bytes, srcPos, packsBytes, 1, validBytes)
                }
                srcPos += validBytes
                val passwordBytes = getPasswordBytes(packsBytes)
                multiBytes.add(passwordBytes)
            }
        }
    }

    /**
     * 生成时区指令包
     */
    private fun makeZonePackages() {
        val timeZoneInfo = getOffsetTime()
        LogInfra.Log.i(
            TAG,
            "相对0时区的时间偏移量为-->${timeZoneInfo[0]}:${timeZoneInfo[1]}，地区所在gmt时区信息为->${
                getGmtTimeZone(true)
            }"
        )
        val result = ByteArray(20)
        result[0] = proType
        result[1] = commandType
        result[2] = ZONE
        result[3] = timeZoneInfo[0].toByte()
        result[4] = timeZoneInfo[1].toByte()
        //生成校验位
        result[19] = BleUtil.getBCC(result, 19)
        multiBytes.add(result)
    }

    /**
     * 生成动态域名指令包
     */
    private fun makeDynamicDomain() {
        val domain = dynamicApi()
        val bytes = domain.toByteArray()
        val length = bytes.size
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "makeDynamicDomain()dynamicDomain = $domain ; length = $length")
        }
        //生成域名长度包
        val lengthBytes = ByteArray(2)
        lengthBytes[0] = LENGTH_TYPE
        lengthBytes[1] = length.toByte()
        val domainBytesLength = getSsidBytes(lengthBytes)
        multiBytes.add(domainBytesLength)
        //生成域名有效数据包
        var lastPacksLength = length % validBytes
        val packs = length / validBytes + if (lastPacksLength == 0) 0 else 1
        if (lastPacksLength == 0) {
            //表明最后一个包也是15个有效字节
            lastPacksLength = validBytes
        }
        var srcPos = 0
        for (i in 1..packs) {
            val packsBytes = ByteArray(16)
            packsBytes[0] = i.toByte()
            if (i == packs) {
                System.arraycopy(bytes, srcPos, packsBytes, 1, lastPacksLength)
            } else {
                System.arraycopy(bytes, srcPos, packsBytes, 1, validBytes)
            }
            srcPos += validBytes
            val domainBytes = getDomainBytes(packsBytes)
            multiBytes.add(domainBytes)
        }
    }

    /**
     * 生成完成标志指令包
     */
    private fun makeFlagPackages() {
        val runModeValue = if (RunMode.getRunModel().number != RunMode.ci.number) 0 else 1
        val flagBytes = getFlagBytes(runModeValue)
        multiBytes.add(flagBytes)
    }

    private fun getSsidBytes(nextBytes: ByteArray): ByteArray {
        val result = ByteArray(20)
        result[0] = proType
        result[1] = commandType
        result[2] = SSID
        val length = nextBytes.size
        System.arraycopy(nextBytes, 0, result, 3, length)
        result[19] = BleUtil.getBCC(result, 19)
        return result
    }

    private fun getPasswordBytes(nextBytes: ByteArray): ByteArray {
        val result = ByteArray(20)
        result[0] = proType
        result[1] = commandType
        result[2] = PASSWORD
        val length = nextBytes.size
        System.arraycopy(nextBytes, 0, result, 3, length)
        result[19] = BleUtil.getBCC(result, 19)
        return result
    }

    private fun getDomainBytes(nextBytes: ByteArray): ByteArray {
        val result = ByteArray(20)
        result[0] = proType
        result[1] = commandType
        result[2] = DYNAMIC_DOMAIN
        val length = nextBytes.size
        System.arraycopy(nextBytes, 0, result, 3, length)
        result[19] = BleUtil.getBCC(result, 19)
        return result
    }

    private fun getFlagBytes(runModeV: Int): ByteArray {
        val result = ByteArray(20)
        result[0] = proType
        result[1] = commandType
        result[2] = FLAG
        val runModeByte =
            (if (runModeV == 0) Constant.getRunModeV0() else Constant.getRunModeV1()).toByte()
        result[3] = runModeByte
        result[19] = BleUtil.getBCC(result, 19)
        return result
    }

    override fun suc() {
        EventWifi.sendSuc(isWrite, commandType, proType)
    }

    override fun fail() {
        EventWifi.sendFail(isWrite, commandType, proType)
    }

    override fun getCommandType(): Byte {
        return BleThProtocol.value_multi_wifi
    }

    private fun dynamicApi(): String {
        val isQaEnv = RunMode.getRunModel().number != RunMode.ci.number
        return EventDynamicApiSupport.getDynamicApi(if (isQaEnv) EventDynamicApiSupport.api_support_http else EventDynamicApiSupport.api_support_http)
    }
}