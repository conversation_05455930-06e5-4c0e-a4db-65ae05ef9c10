package com.govee.h5140

import com.govee.base2home.main.choose.BleProcessorManager
import com.govee.base2home.push.PushM
import com.govee.base2home.sku.ModelMaker
import com.govee.base2newth.config.WarnConfig
import com.govee.base2newth.data.EventSyncOverInBackground
import com.govee.base2newth.data.THMemoryUtil
import com.govee.h5140.add.BleBroadcastProcessor4Add
import com.govee.h5140.item.MakerImpl
import com.govee.h5140.item.Register4Item
import com.govee.h5140.item.Support
import com.govee.h5140.push.ThpPush
import com.govee.lifecycle.annotation.AppLifecycle
import com.ihoment.base2app.IApplication
import com.ihoment.base2app.infra.LogInfra
import com.ihoment.base2app.infra.SafeLog
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2022/5/24
 * @description sku-->5106设备的初始化类
 */
@AppLifecycle
class H5140ApplicationImp() : IApplication {

    private val TAG = "H5140ApplicationImp"

    private fun registerEvent(register: Boolean) {
        if (register) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this)
            }
        } else {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this)
            }
        }
    }

    override fun create() {
        //注册event
        registerEvent(true)
        //item的构建
        Register4Item.register()
        ModelMaker.getInstance().addMaker(MakerImpl())
        //发现设备后操作逻辑
        BleProcessorManager.getInstance().addProcessor(BleBroadcastProcessor4Add())
        //添加默认的支持产品类型sku
        Support.addSupportPact()
        //开启push监听
        PushM.getInstance().addPushM(ThpPush())
        //对联动触发的支持(5.4.0功能)
        //RhythmOpM.getInstance.registerRhythmOp(ThpLinkageBuilder())
        //联动执行动作需过滤(触发设备和执行设备为同一个请求接口)
        //RhythmSupport.addFilterOnClickAndTriggerActions(Constant4L5.H5140)
        //LinkageTriggerFilter.addFilter(ThpTriggerListUI::class.java)
    }

    override fun close() {
        //取消注册event
        registerEvent(false)
    }

    override fun app2Background() {
        //退至后台断开蓝牙的目的是为了节省设备电量，但该设备是直连电源设备，故无须此操作
    }

    override fun onDevicesList() {
        THMemoryUtil.getInstance().curDeviceBleAddress = ""
    }

    override fun onLogout() {
        //退出登录；清除预警记录
        WarnConfig.read().clearWarning()
    }

    @Subscribe(threadMode = ThreadMode.ASYNC)
    fun onEventSyncOverInBackground(event: EventSyncOverInBackground?) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSyncOverInBackground()")
        }
        app2Background()
    }
}