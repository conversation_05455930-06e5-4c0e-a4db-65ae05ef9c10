package com.govee.h5140.ble

import com.govee.base2newth.AbsBleComm
import com.govee.base2newth.AbsThGattCallbackImp
import com.govee.base2newth.BleThProtocol
import com.govee.base2newth.chart.BleUtil
import com.govee.base2newth.one2multi.AbProtocol
import com.govee.h5140.Constants5140
import com.ihoment.base2app.infra.SafeLog
import java.util.*

/**
 * <AUTHOR>
 * @date created on 2022/5/26
 * @description 5106设备ble单包指令通讯器
 */
class ThpCommSingle : AbsBleComm() {

    /**
     * 注意：如果不加companion object的话，可能调用时还未被初始化，这个跟java类中变量的初始化是有明显区别的
     */
    companion object {
        private const val serviceUuidStr = "494e5445-4c4c-495f-524f-434b535f4857"
        private const val characteristicUuidStr = "494e5445-4c4c-495f-524f-434b535f2011"
        private val serviceUUID = UUID.fromString(serviceUuidStr)
        private val characteristicUUID = UUID.fromString(characteristicUuidStr)
    }

    override fun serviceUUID(): UUID {
        return serviceUUID
    }

    override fun characteristicUUID(): UUID {
        return characteristicUUID
    }

    override fun isSelfComm(serviceUuid: String?, characteristicUuid: String?, values: ByteArray?): Boolean {
        val result = serviceUuidStr == serviceUuid && characteristicUuidStr == characteristicUuid
        SafeLog.d("ThpCommSingle") { "------>>>11 result=" + result }
        SafeLog.d("ThpCommSingle"){"------>>>22,"+com.govee.base2home.pact.BleUtil.bytesToHexString(values)}
        return if (result && values != null && values.size > 2) {
            val proType = values[0]
            val isAbComm = proType == AbProtocol.PRO_TYPE
            val isCommonPro = values[0] == BleThProtocol.SINGLE_READ || values[0] == BleThProtocol.SINGLE_WRITE
            //是ab指令或通用的单包指令
            isAbComm || isCommonPro
        } else result
    }

    override fun parsePriority(): Int {
        return Constants5140.COMM_PARSE_PRIORITY_THP
    }

    override fun thGattCallback(): AbsThGattCallbackImp {
        return ThpGattCallbackImp()
    }
}