package com.govee.h5140.ble.event

import com.govee.base2newth.AbsControllerEvent
import org.greenrobot.eventbus.EventBus

/**
 * <AUTHOR>
 * @date created on 2022/5/27
 * @description 向5106设备写入上传最后一包数据的事件
 */
class EventUploadLastData(result: <PERSON><PERSON><PERSON>, write: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) :
    AbsControllerEvent(result, write, commandType, proType, true) {

    companion object {
        fun sendFail(write: Boolean, commandType: Byte, proType: Byte) {
            EventBus.getDefault().post(EventUploadLastData(false, write, commandType, proType))
        }

        fun sendWriteResult(result: <PERSON><PERSON><PERSON>, commandType: Byte, proType: Byte) {
            val event = EventUploadLastData(result, true, commandType, proType)
            EventBus.getDefault().post(event)
        }
    }
}