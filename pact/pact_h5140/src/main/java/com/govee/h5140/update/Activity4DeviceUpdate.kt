package com.govee.h5140.update

import android.content.Context
import androidx.annotation.DrawableRes
import com.govee.base2home.update.download.CheckVersion
import com.govee.base2light.ac.update.OtaUpdateAcV3
import com.govee.base2light.ble.ota.v3.OtaOpV3
import com.govee.base2newth.data.controller.ControllerStopSend
import com.govee.base2newth.data.controller.EventHeartStopSend
import com.govee.h5140.ble.H5140HeartSender
import com.govee.h5140.ble.ThpBleCommManager.Companion.getInstance
import com.ihoment.base2app.util.JumpUtil
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * <AUTHOR>
 * @date created on 2022/6/10
 * @description 5106固件（富芮坤芯片）升级页面
 */
class Activity4DeviceUpdate : OtaUpdateAcV3() {

    override fun prepareOta() {
        (getInstance().heart as H5140HeartSender).setNeedParseHeartPackage(true)
        (getInstance().heart as H5140HeartSender).startController(ControllerStopSend())
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEventStopHeart(event: EventHeartStopSend) {
        OtaOpV3.getInstance().onOtaPrepare(event.isResult)
        (getInstance().heart as H5140HeartSender).controllerEvent(event)
    }

    companion object {
        fun jump2OtaUpdateAcV3(
            context: Context?,
            sku: String,
            deviceName: String?,
            softVersion: String?,
            @DrawableRes skuPlaceholderRes: Int,
            checkVersion: CheckVersion
        ) {
            val bundle =
                makeAcBundle(sku, "", deviceName, softVersion, skuPlaceholderRes, checkVersion)
            JumpUtil.jumpWithBundle(context, Activity4DeviceUpdate::class.java, bundle)
        }
    }
}