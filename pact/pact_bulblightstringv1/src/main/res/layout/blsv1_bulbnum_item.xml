<?xml version="1.0" encoding="utf-8"?>
<com.zhy.android.percent.support.PercentRelativeLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    >

    <TextView
        android:id="@+id/tv_bulb_num"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerVertical="true"
        android:gravity="center_vertical"
        android:textColor="@color/font_style_16_2_textColor"
        android:textSize="@dimen/font_style_16_2_textSize"
        app:layout_minHeightPercent="9.0667%sw"
        app:layout_paddingLeftPercent="8.2667%sw"
        app:layout_paddingRightPercent="8.2667%sw"
        />

    <ImageView
        android:id="@+id/iv_bulb_choose"
        android:layout_width="27dp"
        android:layout_height="27dp"
        android:layout_alignParentEnd="true"
        android:layout_centerVertical="true"
        android:contentDescription="@null"
        android:src="@mipmap/new_public_popup_icon_choose_1"
        app:layout_marginEndPercent="8.0267%sw"
        app:layout_marginRightPercent="8.0267%sw" />
</com.zhy.android.percent.support.PercentRelativeLayout>