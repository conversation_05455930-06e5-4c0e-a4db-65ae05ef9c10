<manifest
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.govee.bulblightstringv1"
    tools:ignore="DiscouragedApi"
    >
    <application>
        <activity
            android:name=".add.v1.DeviceNameAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.AdjustAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.setting.SettingAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.v1.BulbNumSetAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.AdjustAcV2"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.v2.DeviceNameAcV2"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.v2.BulbNumSetAcV2"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".add.v2.WifiChooseAc"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
        <activity
            android:name=".adjust.setting.SettingAcV2"
            android:configChanges="uiMode"
            android:exported="false"
            android:screenOrientation="portrait"
            />
    </application>
</manifest>
