package com.govee.bulblightstringv1.adjust.setting;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-02-13
 * 通知bulb的串数变更的event$
 */
public class EventBulbSettingResult {
    public boolean bulbStringEnable;
    public int maxStringNum;
    public int oneStringNum;
    public int stringNum;

    private EventBulbSettingResult() {
    }

    public static void sendEventBulbSetting(boolean bulbStringEnable, int maxStringNum, int oneStringNum, int stringNum) {
        EventBulbSettingResult event = new EventBulbSettingResult();
        event.bulbStringEnable = bulbStringEnable;
        event.maxStringNum = maxStringNum;
        event.oneStringNum = oneStringNum;
        event.stringNum = stringNum;
        EventBus.getDefault().post(event);
    }

}