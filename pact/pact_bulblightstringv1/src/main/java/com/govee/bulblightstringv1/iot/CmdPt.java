package com.govee.bulblightstringv1.iot;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.util.Encode;
import com.govee.base2light.ble.BleUtil;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Keep;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2019-07-22
 * cmd=pt
 */
@Keep
public class CmdPt extends AbsCmd {
    public static final String pt_op_timer = "timer";
    public static final String pt_op_sleep = "sleep";
    public static final String pt_op_wakeup = "wakeup";
    public static final String pt_op_mode = "mode";

    private static final String TAG = "CmdPt";
    private String opcode;
    private List<String> value = new ArrayList<>();

    /**
     * 单包的透传
     *
     * @param op    op
     * @param bytes bytes
     */
    public CmdPt(String op, byte[] bytes) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "op = " + op + " ; bytesHexString = " + BleUtil.bytesToHexString(bytes));
        }
        this.opcode = op;
        String valueStr = Encode.encryptByBase64(bytes);
        value.add(valueStr);
    }

    /**
     * 多包的透传
     *
     * @param op            op
     * @param multipleBytes multipleBytes
     */
    public CmdPt(String op, List<byte[]> multipleBytes) {
        this.opcode = op;
        if (multipleBytes != null) {
            for (byte[] bytes : multipleBytes) {
                String valueStr = Encode.encryptByBase64(bytes);
                value.add(valueStr);
            }
        }
    }

    public String getOp() {
        return opcode;
    }

    @Override
    public String getCmd() {
        return Cmd.pt;
    }
}