package com.govee.bulblightstringv1.ble;

import com.govee.base2light.ble.comm.AbsNotify;
import com.govee.base2light.ble.comm.AbsNotifyParse;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Create by linshicong on 2019-04-30
 * h7022 被动通知
 */
public class BleNotifyComm extends AbsNotify {

    @Override
    protected List<AbsNotifyParse> getSupportNotify() {
        List<AbsNotifyParse> list = new ArrayList<>();
        list.add(new WifiNotifyParse());
        return list;
    }

    @Override
    public UUID getServiceUuid() {
        return BleComm.serviceUuid;
    }

}