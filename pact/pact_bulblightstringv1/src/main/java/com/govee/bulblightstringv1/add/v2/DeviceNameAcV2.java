package com.govee.bulblightstringv1.add.v2;

import android.content.Context;
import android.content.Intent;
import android.os.Bundle;

import com.govee.base2home.device.AbsDeviceNameAcV1;
import com.govee.bulblightstringv1.ConsV1;
import com.govee.bulblightstringv1.pact.Support;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.util.JumpUtil;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/3/6
 * 设备命名页v2$
 */
public class DeviceNameAcV2 extends AbsDeviceNameAcV1 {
    private AddInfoV2 addInfo;

    /**
     * 跳转到设备命名页
     *
     * @param context
     * @param addInfo
     */
    public static void jump2DeviceNameAcV2(Context context, @NonNull AddInfoV2 addInfo) {
        Bundle bundle = new Bundle();
        bundle.putParcelable(ConsV1.intent_ac_key_addInfo, addInfo);
        JumpUtil.jumpWithBundle(context, DeviceNameAcV2.class, bundle);
    }

    @Override
    protected void initParams(Intent intent) {
        addInfo = intent.getParcelableExtra(ConsV1.intent_ac_key_addInfo);
    }

    @Override
    protected void doSkip() {
        if (BaseApplication.getBaseApplication().isInBackground()) return;/*若应用处于后台，则不进行界面跳转*/
        /*跳转到球泡串设置界面*/
        int[] bulbStringSetting = Support.getBulbStringSetting(addInfo.goodsType, addInfo.pactType, addInfo.pactCode);
        BulbNumSetAcV2.jump2bulbNumSettingAcV2(this, bulbStringSetting[0], bulbStringSetting[1], addInfo);
    }

    @Override
    protected void onSaveDeviceNameSuc(String newDeviceName) {
        addInfo.deviceName = newDeviceName;
        doSkip();
    }

    @Override
    protected String getDevice() {
        return addInfo.device;
    }

    @Override
    protected String getSku() {
        return addInfo.sku;
    }

    @Override
    protected String getDeviceName() {
        return addInfo.deviceName;
    }
}