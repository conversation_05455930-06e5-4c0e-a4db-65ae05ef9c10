package com.govee.bulblightstringv1.pact.ble;

import android.content.Context;

import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.scenes.model.DeviceModel;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ac.diy.DiyShare;
import com.govee.base2light.ac.diy.GroupCmdEntry;
import com.govee.base2light.ac.diy.IColorOp;
import com.govee.base2light.ac.diy.IDiyOp;
import com.govee.base2light.ac.diy.IRoomOp4Ble;
import com.govee.base2light.ac.diy.IRoomOp4Iot;
import com.govee.base2light.ac.diy.ISmartRoomOp;
import com.govee.base2light.ac.diy.v1.EffectCodes;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.HeartController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.util.NumUtil;
import com.govee.bulblightstringv1.ble.Ble;
import com.govee.bulblightstringv1.ble.BleComm;
import com.govee.bulblightstringv1.ble.Mode;
import com.govee.bulblightstringv1.ble.ModeController;
import com.govee.bulblightstringv1.ble.OpColorCommDialog4Ble;
import com.govee.bulblightstringv1.ble.OpColorCommDialog4SquareBle;
import com.govee.bulblightstringv1.ble.SubModeColor;
import com.govee.bulblightstringv1.pact.Comm;
import com.ihoment.base2app.util.JsonUtil;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/8/12
 * color的op操作$
 */
abstract class AbsEffectOp4Ble implements IColorOp, ISmartRoomOp, IDiyOp {

    @Override
    public String getKey() {
        StringBuilder sb = new StringBuilder();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            sb.append(goodsType).append("_");
        }
        return sb.toString();
    }

    /**
     * 支持的goodsType
     *
     * @return int
     */
    protected abstract int[] supportGoodsType();

    @Override
    public boolean support(@NonNull AbsDevice absDevice, int effectType) {
        int curGoodsType = absDevice.getGoodsType();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) return true;
        }
        return false;
    }

    @Override
    public boolean supportColorEffect(@NonNull AbsDevice absDevice, @NonNull EffectData.ColorEffect colorEffect) {
        return colorEffect.isSingleColor();
    }

    @Override
    public boolean applyColorEffect(Context context, @NonNull AbsDevice absDevice, @NonNull EffectData.ColorEffect colorEffect, boolean needConnect) {
        DeviceExtMode deviceExt = absDevice.getDeviceExt();
        String extSettingJson = deviceExt.getDeviceSettings();
        BleExt ext = JsonUtil.fromJson(extSettingJson, BleExt.class);
        if (ext != null) {
            if (needConnect) {
                OpColorCommDialog4Ble.showDialog(context, ext.address, ext.bleName, colorEffect);
            } else {
                OpColorCommDialog4SquareBle.showDialog(context, ext.address, ext.bleName, colorEffect);
            }
            return true;
        }
        return false;
    }
    /*smartRoom*/

    @Override
    public boolean supportWifi() {
        return false;
    }

    @Override
    public boolean supportBle() {
        return true;
    }

    @Override
    public int supportColorSize(DeviceModel model) {
        return 1;
    }

    @Override
    public boolean supportDevice(DeviceModel deviceModel) {
        int curGoodsType = deviceModel.getGoodsType();
        int[] goodsTypes = supportGoodsType();
        for (int goodsType : goodsTypes) {
            if (goodsType == curGoodsType) return true;
        }
        return false;
    }

    @Override
    public IRoomOp4Iot getRoomOp4Iot() {
        return null;
    }

    @Override
    public IRoomOp4Ble getRoomOp() {
        return new IRoomOp4Ble() {
            @Override
            public UUID getServiceUUID() {
                return BleComm.serviceUuid;
            }

            @Override
            public UUID getCharacteristicUUID() {
                return BleComm.characteristicUuid;
            }

            @Override
            public byte[] heartBytes() {
                return new HeartController().getValue();
            }

            @Override
            public byte[] makeSwitchOpBytes(DeviceModel deviceModel, boolean on) {
                return new SwitchController(on).getValue();
            }

            @Override
            public byte[] makeBrightnessOpBytes(DeviceModel deviceModel, int brightnessPercent) {
                int brightness = NumUtil.calculateProgress(100, 1, brightnessPercent);
                return new BrightnessController(brightness).getValue();
            }

            @Override
            public List<byte[]> makeSetColorOpBytes(DeviceModel deviceModel, int[] colors) {
                SubModeColor subModeColor = SubModeColor.makeSubModeColor(colors[0]);
                Mode mode = new Mode();
                mode.subMode = subModeColor;
                ModeController modeController = new ModeController(mode);
                List<byte[]> bytes = new ArrayList<>();
                bytes.add(modeController.getValue());
                return bytes;
            }

            @Override
            public List<byte[]> makeSetColorTemOpBytes(DeviceModel deviceModel, int[] kelvin, int[] temColor) {
                return makeSetColorOpBytes(deviceModel, temColor);
            }

            @Override
            public byte[] makeSetColorOpBytes(DeviceModel deviceModel, int color) {
                return Comm.makeColorController4BleComm(color).getValue();
            }

            @Override
            public byte[] makeSetColorTemOpBytes(DeviceModel deviceModel, int kelvin, int temColor) {
                return Comm.makeColorTemController4BleComm(temColor).getValue();
            }

            @Override
            public int supportMic(DeviceModel deviceModel) {
                return support_mic_no;
            }

            @Override
            public byte[] makeMicBytesByRgb(DeviceModel deviceModel, int rgb) {
                return null;
            }
        };
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    public EffectCodes getSupportDiyEffectCode(@NonNull AbsDevice absDevice) {
        return null;
    }

    @Override
    public boolean supportDiyEffect(@NonNull AbsDevice absDevice, int[] effectCodes) {
        return false;
    }

    @Override
    public boolean applyDiyEffect(Context context, @NonNull AbsDevice absDevice, @NonNull DiyShare diyShare, boolean needConnect) {
        return false;
    }

    @Override
    public GroupCmdEntry getGroupCmdEntry(@NonNull AbsDevice absDevice, @NonNull EffectData.ShareEffect shareEffect) {
        return null;
    }

    /*smartRoom*/
}