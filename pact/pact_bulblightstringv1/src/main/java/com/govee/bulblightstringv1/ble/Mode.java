package com.govee.bulblightstringv1.ble;

import com.govee.base2light.ble.controller.AbsMode;


/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-03
 * 模式
 */
public class Mode extends AbsMode {
    @Override
    public void parseSubMode(byte subModeType, byte[] subModeValidBytes) {
        if (subModeType == BleProtocol.sub_mode_music) {
            subMode = new SubModeMusic();
            subMode.parse(subModeValidBytes);
        } else if (subModeType == BleProtocol.sub_mode_scenes) {
            subMode = new SubModeScenes();
            subMode.parse(subModeValidBytes);
        } else {
            subMode = new SubModeColor();
            subMode.parse(subModeValidBytes);
        }
    }
}