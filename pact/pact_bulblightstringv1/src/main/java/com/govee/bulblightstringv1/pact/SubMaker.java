package com.govee.bulblightstringv1.pact;

import com.govee.base2home.sku.IMaker;
import com.govee.base2home.sku.ISkuItem;
import com.govee.bulblightstringv1.pact.ble.V1BleSkuItem;
import com.govee.bulblightstringv1.pact.bleiot.V1BleIotSkuItem;

import java.util.ArrayList;
import java.util.List;


/**
 * Create by xieyingwu on 2019-12-10
 * maker
 */
public class SubMaker implements IMaker {
    private List<ISkuItem> makers = new ArrayList<>();

    public SubMaker() {
        makers.add(new V1BleSkuItem());
        makers.add(new V1BleIotSkuItem());
    }

    @Override
    public List<ISkuItem> getSupportMakers() {
        return makers;
    }
}