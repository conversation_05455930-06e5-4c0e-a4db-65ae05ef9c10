package com.govee.bulblightstringv1.pact.bleiot;

import com.govee.bulblightstringv1.pact.Support;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/8/12
 * $
 */
public class EffectOp4BleIot extends AbsEffectOp4BleIot {
    @Override
    protected int[] supportGoodsType() {
        return Support.effect4bleWifiOpGoodsTypeArray;
    }

    private EffectOp4BleIot() {
    }

    private static class Builder {
        private static final EffectOp4BleIot instance = new EffectOp4BleIot();
    }

    public static EffectOp4BleIot getInstance = Builder.instance;
}