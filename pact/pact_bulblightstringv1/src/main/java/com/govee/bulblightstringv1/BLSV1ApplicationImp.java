package com.govee.bulblightstringv1;

import com.govee.base2home.main.choose.BleProcessorManager;
import com.govee.base2home.scenes.CmdBuilderManager;
import com.govee.base2home.sku.ModelMaker;
import com.govee.bulblightstringv1.add.v1.BleBroadcastProcessorV1;
import com.govee.bulblightstringv1.add.v2.BleBroadcastProcessorV2;
import com.govee.bulblightstringv1.pact.Register4Item;
import com.govee.bulblightstringv1.pact.SubMaker;
import com.govee.bulblightstringv1.pact.Support;
import com.govee.bulblightstringv1.scenes.BleBrightnessBuilderV1;
import com.govee.bulblightstringv1.scenes.BleColorCmdBuilderV1;
import com.govee.bulblightstringv1.scenes.BleColorTemCmdBuilderV1;
import com.govee.bulblightstringv1.scenes.BleHeartCmdBuilderV1;
import com.govee.bulblightstringv1.scenes.BleSwitchCmdBuilderV1;
import com.govee.bulblightstringv1.scenes.IotBrightnessCmdBuilderV1;
import com.govee.bulblightstringv1.scenes.IotColorCmdBuilderV1;
import com.govee.bulblightstringv1.scenes.IotColorTemCmdBuilderV1;
import com.govee.bulblightstringv1.scenes.IotSwitchCmdBuilderV1;
import com.govee.lifecycle.annotation.AppLifecycle;
import com.ihoment.base2app.IApplication;

/**
 * Create by xieyingwu on 2020-02-12
 * ApplicationImp$
 */
@AppLifecycle
public class BLSV1ApplicationImp implements IApplication {
    @Override
    public void create() {
        /*item的构建*/
        Register4Item.INSTANCE.register();
        ModelMaker.getInstance().addMaker(new SubMaker());
        /*发现设备后操作逻辑*/
        BleProcessorManager.getInstance().addProcessor(new BleBroadcastProcessorV1());
        BleProcessorManager.getInstance().addProcessor(new BleBroadcastProcessorV2());
        /*添加默认的支持产品类型sku*/
        Support.addSupportPact();
        /*scenes场景ble支持*/
        CmdBuilderManager.getInstance().registerBleCmdBuilder(
                new BleBrightnessBuilderV1(),
                new BleColorCmdBuilderV1(),
                new BleColorTemCmdBuilderV1(),
                new BleHeartCmdBuilderV1(),
                new BleSwitchCmdBuilderV1()
        );
        /*scenes场景iot支持*/
        CmdBuilderManager.getInstance().registerCmdBuilder(
                new IotSwitchCmdBuilderV1(),
                new IotBrightnessCmdBuilderV1(),
                new IotColorCmdBuilderV1(),
                new IotColorTemCmdBuilderV1()
        );
        DetailConfig.INSTANCE.addConfig();
    }
}