package com.govee.bulblightstringv1.adjust.v1;


import com.govee.ui.R;
import android.app.Activity;
import android.bluetooth.BluetoothDevice;
import android.os.Bundle;
import android.view.View;
import android.widget.RelativeLayout;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.custom.LoadingDialog;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2light.ac.adjust.EventSleepUpdate;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.timer.NewShowTimerAcV1;
import com.govee.base2light.ac.timer.NewSleepSetEvent;
import com.govee.base2light.ac.timer.NewTimerSetEventV1;
import com.govee.base2light.ac.timer.NewWakeupSetEvent;
import com.govee.base2light.ac.timer.SleepFailEvent;
import com.govee.base2light.ac.timer.TimerResultEvent;
import com.govee.base2light.ac.timer.UpdateTimeEvent;
import com.govee.base2light.ac.timer.WakeupFailEvent;
import com.govee.base2light.ac.update.OtaUpdateAcV1;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.NewTimerV1;
import com.govee.base2light.ble.controller.NewTimerV1Controller;
import com.govee.base2light.ble.controller.SleepController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.controller.SyncTimeController;
import com.govee.base2light.ble.controller.SyncTimeInfo;
import com.govee.base2light.ble.controller.WakeUpController;
import com.govee.base2light.ble.v1.AbsMode3UIV1;
import com.govee.base2light.pact.BleInfo;
import com.govee.base2light.pact.IUiResult4Ble;
import com.govee.base2light.pact.ble.IBleOpResult;
import com.govee.base2light.ui.mode.ChangeModeEvent;
import com.govee.bulblightstringv1.ConsV1;

import com.govee.bulblightstringv1.adjust.IUi4ExtInfo;
import com.govee.bulblightstringv1.adjust.setting.EventBulbSettingResult;
import com.govee.bulblightstringv1.adjust.setting.EventChangeBulb;
import com.govee.bulblightstringv1.ble.BleProtocol;
import com.govee.bulblightstringv1.ble.BulbNumController;
import com.govee.bulblightstringv1.ble.Mode;
import com.govee.bulblightstringv1.ble.ModeController;
import com.govee.bulblightstringv1.ble.MultiSceneModel;
import com.govee.bulblightstringv1.ble.MultipleScenesController;
import com.govee.bulblightstringv1.ble.SubModeColor;
import com.govee.bulblightstringv1.ble.SubModeMusic;
import com.govee.bulblightstringv1.ble.SubModeScenes;
import com.govee.bulblightstringv1.pact.Support;
import com.govee.ui.component.BrightnessUI;
import com.govee.ui.component.NewTimerUI;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.ToastUtil;
import com.zhy.android.percent.support.PercentRelativeLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2020-02-12
 * ui-v1版本$
 */
class UiV1 implements IUi4ExtInfo {
    private static final String TAG = "UiV1";

    private static final int ui_type_def = -1;
    private static final int ui_type_normal = 1;
    private static final int ui_type_fail = 2;

    private final IUiResult4Ble uiResult;
    private final BleInfo info;
    private final ExtV1 ext = new ExtV1();
    private final BleOpV1 bleOp;

    private Activity ac;
    private boolean layoutSuc;/*布局是否已初始化*/
    private boolean destory;
    private int curUiType = ui_type_def;

    private NewTimerUI timerUI;
    private BrightnessUI brightnessUI;
    private AbsMode3UIV1 modeUI;
    private final IBleOpResult opResult = new IBleOpResult() {
        @Override
        public void onOffChange() {
            showLoading();
        }

        @Override
        public void infoOver() {
            curUiType = ui_type_normal;
            hideLoading();
            checkUi();
        }

        @Override
        public void noConnect() {
            curUiType = ui_type_fail;
            checkUi();
        }

        @Override
        public void bleWrite(byte proCommandType, boolean result) {
            hideLoading();
            checkUi();
        }
    };

    public UiV1(IUiResult4Ble uiResult, BleInfo info) {
        this.uiResult = uiResult;
        this.info = info;
        bleOp = new BleOpV1(info, ext);
    }

    @Override
    public int getStringNum() {
        return ext.bulbNum;
    }

    private void registerEvent(boolean register) {
        if (register) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this);
            }
        } else {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        }
    }

    @Override
    public boolean isSupportProtocol(int pactType, int pactCode) {
        List<Protocol> supportProtocolsV1 = Support.supportProtocolsV1;
        for (Protocol protocol : supportProtocolsV1) {
            if (protocol.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    @Override
    public void destroy() {
        if (!destory) {
            destory = true;
            registerEvent(false);
            bleOp.destroy();
            ac = null;
            if (timerUI != null) timerUI.onDestroy();
            if (brightnessUI != null) brightnessUI.onDestroy();
            if (modeUI != null) modeUI.onDestroy();
            hideLoading();
        }
    }

    @Override
    public void onOffChange() {
        if (bleOp.isOpCommEnable()) {
            showLoading();
            SwitchController switchController = new SwitchController(!info.open);
            bleOp.executeOp(switchController);
        }
    }

    @Override
    public void layout(@NonNull AppCompatActivity ac, @NonNull PercentRelativeLayout contentParent, int headerId) {
        /*注册事件*/
        registerEvent(true);
        this.ac = ac;
        if (!layoutSuc) {
            layoutSuc = true;
            /*初始化ui组件*/
            int[] ids = new int[]{100, 101, 102, 103, 104, 105, 106};
            /*添加布局-定时*/
            timerUI = new NewTimerUI(ac);
            View fucViewTimer = timerUI.getFucView();
            fucViewTimer.setId(ids[0]);
            addViewMargin(contentParent, fucViewTimer, headerId, timerUI.getWidth(), timerUI.getHeight(), AppUtil.getScreenWidth() * 16 / 750);
            /*添加布局-亮度*/
            brightnessUI = new BrightnessUI(ac, 100, 1, false);
            View fucViewBrightness = brightnessUI.getFucView();
            fucViewBrightness.setId(ids[1]);
            addViewMargin(contentParent, fucViewBrightness, fucViewTimer.getId(), brightnessUI.getWidth(), brightnessUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
            /*添加布局-mode*/
            modeUI = new ModeUiV1(ac, info.sku, this::makeArguments);
            View fucViewMode = modeUI.getFucView();
            fucViewMode.setId(ids[3]);
            addViewMargin(contentParent, fucViewMode, fucViewBrightness.getId(), modeUI.getWidth(), modeUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
        }
        bleOp.setOpResult(opResult);
        /*开始进行op通信*/
        startOpComm();
    }

    @Override
    public void toUpdateAc(@NonNull AppCompatActivity ac) {
        if (info.canUpdate() && bleOp.isOpCommEnable()) {
            int defSkuRes = ThemeM.getDefSkuRes(info.sku, info.spec);
            OtaUpdateAcV1.jump2OtaUpdateAcV1(ac, info.sku, info.deviceName, info.versionSoft, defSkuRes, info.checkVersion);
        }
    }

    @Override
    public void uiLost() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "uiLost() layoutSuc = " + layoutSuc + " ; curUiType = " + curUiType);
        }
        if (layoutSuc && (curUiType == ui_type_normal)) {
            curUiType = ui_type_fail;
            checkUi(false);
        }
    }

    private void startOpComm() {
        /*ble通信处理器*/
        boolean opCommEnableBle = bleOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "startOpComm() opCommEnableBle = " + opCommEnableBle);
        }
        /*若当前不可直接通信，则尝试进行ble通信*/
        if (!opCommEnableBle) {
            curUiType = ui_type_def;
            BluetoothDevice bluetoothDevice = null;
            if (uiResult != null) bluetoothDevice = uiResult.getCurBluetoothDevice();
            bleOp.beOpComm(bluetoothDevice);
        }
        checkUi();
    }

    private void checkUi() {
        checkUi(true);
    }

    private void checkUi(boolean reportResult) {
        if (destory) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkUi() curUiType = " + curUiType);
        }
        if (curUiType == ui_type_fail) {
            /*通信失败*/
            timerUI.hide();
            brightnessUI.hide();
            modeUI.hide();
            hideLoading();
            /*连接断开-重置op*/
            bleOp.destroy();
            if (uiResult != null && reportResult) uiResult.uiResult(IUiResult4Ble.ui_result_fail);
        } else if (curUiType == ui_type_normal) {
            /*通信正常*/
            hideLoading();
            timerUI.show();
            boolean open = info.open;
            if (open) {
                brightnessUI.show();
                brightnessUI.updateBrightness(true, ext.brightness);
                modeUI.show();
                checkSubMode();
                modeUI.setMode(info.mode);
            } else {
                brightnessUI.hide();
                modeUI.hide();
            }
            if (uiResult != null && reportResult) uiResult.uiResult(IUiResult4Ble.ui_result_suc);
        } else {
            /*通信中*/
            timerUI.hide();
            brightnessUI.hide();
            modeUI.hide();
            if (uiResult != null && reportResult) uiResult.uiResult(IUiResult4Ble.ui_result_ing);
        }
    }

    private void checkSubMode() {
        AbsMode mode = info.mode;
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeColor) {
                int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
                ((SubModeColor) subMode).bulbStringNum = bulbStringSetting[1] * ext.bulbNum;
            }
        }
    }

    private Bundle makeArguments(byte subMode) {
        if (subMode == BleProtocol.sub_mode_scenes) {
            Bundle bundle = new Bundle();
            bundle.putInt(ConsV1.arguments_key_scenes_color_num, 12);
            return bundle;
        }
        if (subMode == BleProtocol.sub_mode_color) {
            Bundle bundle = new Bundle();
            int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
            int bulbStringNum = bulbStringSetting[1] * ext.bulbNum;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeArguments() bulbStringNum = " + bulbStringNum);
            }
            bundle.putInt(ConsV1.arguments_key_color_bulb_string_num, bulbStringNum);
            return bundle;
        }
        return null;
    }

    private void showLoading() {
        LoadingDialog.createDialog(ac, com.ihoment.base2app.R.style.DialogDim, 60 * 1000).setEventKey(TAG).show();
    }

    private void hideLoading() {
        LoadingDialog.hideDialog(TAG);
    }

    private void toast(@StringRes int strRes) {
        ToastUtil.getInstance().toast(strRes);
    }

    private void addViewMargin(PercentRelativeLayout contentParent, View subView, int belowId, int width, int height, int marginTop) {
        PercentRelativeLayout.LayoutParams lp = getLP(width, height);
        lp.addRule(RelativeLayout.BELOW, belowId);
        lp.topMargin = marginTop;
        lp.bottomMargin = 0;
        contentParent.addView(subView, lp);
    }

    private PercentRelativeLayout.LayoutParams getLP(int width, int height) {
        return new PercentRelativeLayout.LayoutParams(width, height);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onClickNewTimer(NewTimerUI.NewTimerClickEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onClickNewTimer()");
        }
        NewShowTimerAcV1.jump2NewShowTimerAcV1(ac, info, ext.wakeUpInfo, ext.sleepInfo, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onBrightnessEndEvent(BrightnessUI.EventBrightnessClickEvent event) {
        int brightness = event.brightness;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onBrightnessEndEvent() brightness = " + brightness);
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            BrightnessController controller = new BrightnessController(brightness);
            bleOp.executeOp(controller);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewTimerSetEventV1(NewTimerSetEventV1 event) {
        int group = event.getGroup();
        NewTimerV1 info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewTimerSetEventV1() group = " + group);
        }
        if (bleOp.isOpCommEnable()) {
            info.check();
            NewTimerV1Controller controller = new NewTimerV1Controller(group, NewTimerV1.toTimer(info));
            bleOp.executeOp(controller);
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            TimerResultEvent.sendTimerResultEventFail(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewWakeupSetEvent(NewWakeupSetEvent event) {
        WakeUpInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewWakeupSetEvent() info = " + info.toString());
        }
        if (bleOp.isOpCommEnable()) {
            info.check();
            WakeUpController wakeupModeController = new WakeUpController(info.enable, info.endBri, info.wakeHour, info.wakeMin, info.repeat, info.wakeTime);
            bleOp.executeOp(wakeupModeController);
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            WakeupFailEvent.sendWakeupFailEvent(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNewSleepSetEvent(NewSleepSetEvent event) {
        SleepInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewSleepSetEvent() info = " + info.toString());
        }
        if (bleOp.isOpCommEnable()) {
            /*若当前是开启睡眠模式，但设备未开启，则提示用户打开灯*/
            if (event.isOpen() && !this.info.open) {
                toast(R.string.b2light_please_open_the_light);
                SleepFailEvent.sendSleepFailEvent(false);
            } else {
                info.check();
                SleepController sleepModeController = new SleepController(info.enable, info.startBri, info.closeTime, info.closeTime);
                bleOp.executeOp(sleepModeController);
            }
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            SleepFailEvent.sendSleepFailEvent(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onModeV1Event(Mode mode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onModeV1Event()");
        }
        changeMode(mode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeModeEvent(ChangeModeEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent()");
        }
        ISubMode subMode = event.getSubMode();
        Mode mode = new Mode();
        mode.subMode = subMode;
        changeMode(mode);
        /*统计场景使用次数*/
        if (subMode instanceof SubModeScenes) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, ParamFixedValue.times);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, "scene_" + ((SubModeScenes) subMode).type);
        } else if (subMode instanceof SubModeColor) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.color_mode, ParamFixedValue.times);
        } else if (subMode instanceof SubModeMusic) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
        }
    }

    private void changeMode(Mode mode) {
        if (bleOp.isOpCommEnable()) {
            showLoading();
            ModeController controller = new ModeController(mode);
            bleOp.executeOp(controller);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onUpdateTimeEvent(UpdateTimeEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onUpdateTimeEvent()");
        }
        readTimer();
    }

    private void readTimer() {
        /*重新读取定时信息*/
        if (bleOp.isOpCommEnable()) {
            SyncTimeInfo info = SyncTimeInfo.getCurrentTime();
            AbsSingleController[] controllers = new AbsSingleController[]{
                    new SyncTimeController(info.hour, info.minute, info.second, info.week),
                    new NewTimerV1Controller(0xFF),
                    new WakeUpController(),
                    new SleepController(),
            };
            bleOp.executeOp(controllers);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSleepUpdate(EventSleepUpdate event) {
        SleepInfo sleepInfo = event.getInfo();
        sleepInfo.check();
        ext.sleepInfo = sleepInfo;
        /*重新读取定时信息*/
        readTimer();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventChangeBulb(EventChangeBulb event) {
        int stringNum = event.stringNum;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventChangeBulb() stringNum = " + stringNum);
        }
        if (bleOp.isOpCommEnable()) {
            BulbNumController bulbNumController = new BulbNumController(stringNum);
            bleOp.executeOp(bulbNumController);
        } else {
            int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
            EventBulbSettingResult.sendEventBulbSetting(false, bulbStringSetting[0], bulbStringSetting[1], ext.bulbNum);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onMultiSceneModel(MultiSceneModel sceneModel) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onMultiSceneModel()");
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            MultipleScenesController multipleScenesController = new MultipleScenesController(sceneModel);
            bleOp.executeMultiOp(multipleScenesController);
        }
    }
}
