package com.govee.bulblightstringv1.add.v2;

import android.app.Activity;

import com.govee.base2home.main.choose.BaseBleDeviceModel;
import com.govee.base2home.main.choose.BaseBleProcessor;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.sku.DefaultDeviceNameUtil;
import com.govee.bulblightstringv1.pact.Support;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2019-11-27
 * 蓝牙广播处理器$
 */
public class BleBroadcastProcessorV2 extends BaseBleProcessor {
    @Override
    public boolean onItemClick(Activity activity, BaseBleDeviceModel model, boolean singleSku) {
        int goodsType = model.goodsType;
        Protocol protocol = model.protocol;
        /*是否支持协议处理;需要登录检测*/
        if (Support.supportPactV2(goodsType, protocol)) {
            boolean checkLogin = checkLogin(activity);
            if (checkLogin) {
                return true;
            }
            AddInfoV2 addInfo = new AddInfoV2();
            addInfo.sku = model.getSku();
            addInfo.goodsType = goodsType;
            addInfo.pactType = protocol.pactType;
            addInfo.pactCode = protocol.pactCode;
            String deviceName = model.getDeviceName();
            deviceName = DefaultDeviceNameUtil.INSTANCE.getSkuDefaultName(model.getSku(), deviceName);
            addInfo.deviceName = deviceName;
            addInfo.bleName = model.getBleName();
            addInfo.bleAddress = model.getDevice().getAddress();
            ConnectDialogV2.createDialog(activity, model.getDevice(), addInfo).show();
            return true;
        }
        return false;
    }
}