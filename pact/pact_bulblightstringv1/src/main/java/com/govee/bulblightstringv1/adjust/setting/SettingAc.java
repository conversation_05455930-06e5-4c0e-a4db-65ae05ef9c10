package com.govee.bulblightstringv1.adjust.setting;

import android.content.Context;
import android.os.Bundle;

import com.govee.bulblightstringv1.add.BulbNumSettingDialog;
import com.govee.ui.ac.AbsBleBulbSettingAcV1;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JumpUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Create by xieyingwu on 2020-02-13
 * SettingAc$
 */
public class SettingAc extends AbsBleBulbSettingAcV1 {

    /**
     * 跳转到设置界面
     *
     * @param context
     * @param sku
     * @param device
     * @param deviceName
     * @param deviceNameInputLimit
     * @param bulbStringEnable
     * @param maxStringNum
     * @param oneStringNum
     * @param stringNum
     */
    public static void jump2SettingAc(Context context, String sku, String device, String deviceName, int deviceNameInputLimit, boolean bulbStringEnable, int maxStringNum, int oneStringNum, int stringNum, String versionHard) {
        stringNum = Math.max(1, stringNum);
        Bundle bundle = makeAcBundle(true, sku, device, deviceName, deviceNameInputLimit, bulbStringEnable, maxStringNum, stringNum, oneStringNum, versionHard);
        JumpUtil.jumpWithBundle(context, SettingAc.class, bundle);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBulbSetting(EventBulbSettingResult event) {
        hideLoading();
        int maxStringNum = event.maxStringNum;
        int oneStringNum = event.oneStringNum;
        int stringNum = event.stringNum;
        boolean bulbStringEnable = event.bulbStringEnable;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBulbSetting() bulbStringEnable = " + bulbStringEnable + " ; maxStringNum = " + maxStringNum + " ; oneStringNum = " + oneStringNum + " ; stringNum = " + stringNum);
        }
        updateBulbStringUi(bulbStringEnable, maxStringNum, oneStringNum, stringNum);
        BulbNumSettingDialog.hideDialog();
    }

    @Override
    protected void execRecycle() {
        super.execRecycle();
        BulbNumSettingDialog.hideDialog();
    }

    @Override
    protected void changeBulbSetting(int bulbMaxStringNum, int bulbOneStringNum, int bulbStringNum) {
        BulbNumSettingDialog.createDialog(this, bulbStringNum, bulbMaxStringNum, bulbOneStringNum, stringNum -> {
            showLoading();
            EventChangeBulb.sendEventChangeBulb(stringNum);
        }).show();
    }
}