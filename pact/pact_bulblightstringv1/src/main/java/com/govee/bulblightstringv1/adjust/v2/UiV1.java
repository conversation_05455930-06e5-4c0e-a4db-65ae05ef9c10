package com.govee.bulblightstringv1.adjust.v2;


import com.govee.ui.R;
import android.app.Activity;
import android.bluetooth.BluetoothDevice;
import android.os.Bundle;
import android.text.TextUtils;
import android.util.SparseArray;
import android.view.View;
import android.widget.RelativeLayout;

import com.govee.base2home.Constant;
import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.custom.LoadingDialog;
import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ac.adjust.EventSleepUpdate;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.timer.NewShowTimerAcV1;
import com.govee.base2light.ac.timer.NewSleepSetEvent;
import com.govee.base2light.ac.timer.NewTimerSetEventV1;
import com.govee.base2light.ac.timer.NewWakeupSetEvent;
import com.govee.base2light.ac.timer.SleepFailEvent;
import com.govee.base2light.ac.timer.SleepSucEvent;
import com.govee.base2light.ac.timer.Timer;
import com.govee.base2light.ac.timer.TimerResultEvent;
import com.govee.base2light.ac.timer.UpdateTimeEvent;
import com.govee.base2light.ac.timer.WakeupFailEvent;
import com.govee.base2light.ac.timer.WakeupSucEvent;
import com.govee.base2light.ac.update.OtaUpdateAcV1;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.comm.MultipleBleBytes;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.NewTimerV1;
import com.govee.base2light.ble.controller.NewTimerV1Controller;
import com.govee.base2light.ble.controller.SleepController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.controller.SyncTimeController;
import com.govee.base2light.ble.controller.SyncTimeInfo;
import com.govee.base2light.ble.controller.WakeUpController;
import com.govee.base2light.ble.v1.AbsMode3UIV1;
import com.govee.base2light.iot.Color;
import com.govee.base2light.iot.ResultBrightness;
import com.govee.base2light.iot.ResultColor;
import com.govee.base2light.iot.ResultColorTem;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.FilterSceneInfo4BleIot;
import com.govee.base2light.pact.IUi4BleIot;
import com.govee.base2light.pact.IUiResult4BleIot;
import com.govee.base2light.pact.ble.IBleOpResult;
import com.govee.base2light.pact.iot.IIotOpResult;
import com.govee.base2light.ui.mode.ChangeModeEvent;
import com.govee.bulblightstringv1.ConsV1;

import com.govee.bulblightstringv1.adjust.IUi4ExtInfo;
import com.govee.bulblightstringv1.adjust.setting.EventBulbSettingResult;
import com.govee.bulblightstringv1.adjust.setting.EventChangeBulb;
import com.govee.bulblightstringv1.adjust.v1.ModeUiV1;
import com.govee.bulblightstringv1.ble.BleProtocol;
import com.govee.bulblightstringv1.ble.BulbNumController;
import com.govee.bulblightstringv1.ble.Mode;
import com.govee.bulblightstringv1.ble.ModeController;
import com.govee.bulblightstringv1.ble.MultiSceneModel;
import com.govee.bulblightstringv1.ble.MultipleScenesController;
import com.govee.bulblightstringv1.ble.ScenesConfig;
import com.govee.bulblightstringv1.ble.SubModeColor;
import com.govee.bulblightstringv1.ble.SubModeMusic;
import com.govee.bulblightstringv1.ble.SubModeScenes;
import com.govee.bulblightstringv1.iot.Cmd;
import com.govee.bulblightstringv1.iot.CmdBrightness;
import com.govee.bulblightstringv1.iot.CmdBulb;
import com.govee.bulblightstringv1.iot.CmdPt;
import com.govee.bulblightstringv1.iot.CmdStatus;
import com.govee.bulblightstringv1.iot.CmdStatusV0;
import com.govee.bulblightstringv1.iot.CmdString;
import com.govee.bulblightstringv1.iot.CmdTurn;
import com.govee.bulblightstringv1.iot.ResultBulb;
import com.govee.bulblightstringv1.iot.ResultPt;
import com.govee.bulblightstringv1.iot.ResultString;
import com.govee.bulblightstringv1.pact.Support;
import com.govee.ui.Cons;
import com.govee.ui.component.BrightnessUI;
import com.govee.ui.component.NewTimerUI;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.StreamUtil;
import com.ihoment.base2app.util.ToastUtil;
import com.zhy.android.percent.support.PercentRelativeLayout;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.annotation.StringRes;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2020/3/5
 * ui-v1版本$
 */
class UiV1 implements IUi4ExtInfo, IUi4BleIot {
    private static final String TAG = "UiV1";

    private static final int ui_type_def = -1;
    private static final int ui_type_normal = 1;
    private static final int ui_type_fail = 2;

    private static final int max_retry_connect_device_times = 2;

    private final IUiResult4BleIot uiResult;
    private final BleIotInfo info;
    private final ExtV1 ext = new ExtV1();
    private final BleOpV2 bleOp;
    private final IotOpV2 iotOp;

    private Activity ac;
    private boolean layoutSuc;/*布局是否已初始化*/
    private boolean destory;
    private int uiTypeBle = ui_type_def;
    private int uiTypeIot = ui_type_def;

    private NewTimerUI timerUI;
    private BrightnessUI brightnessUI;
    private AbsMode3UIV1 modeUI;

    private int retryConnectDeviceTimes;/*尝试连接设备次数*/

    private final IBleOpResult bleOpResult = new IBleOpResult() {
        @Override
        public void onOffChange() {
            showLoading();
        }

        @Override
        public void infoOver() {
            uiTypeBle = ui_type_normal;
            checkUi();
        }

        @Override
        public void noConnect() {
            uiTypeBle = ui_type_fail;
            /*蓝牙断开，需要检测iot是否可用*/
            bleUnable2CheckIot();
            checkUi();
        }

        @Override
        public void bleWrite(byte proCommandType, boolean result) {
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "bleWrite() proCommandType = " + proCommandType + " ; result = " + result);
            }
            checkUi();
        }
    };

    private void bleUnable2CheckIot() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "bleUnable2CheckIot() uiTypeIot = " + uiTypeIot + " ; retryConnectDeviceTimes = " + retryConnectDeviceTimes);
        }
        if (uiTypeIot != ui_type_normal) {
            if (retryConnectDeviceTimes < max_retry_connect_device_times) {
                retryConnectDeviceTimes++;
                if (iotOp.isOpCommEnable()) {
                    iotOp.readCmd(new CmdStatus());
                } else {
                    uiTypeIot = ui_type_def;
                    iotOp.beOpComm(info.sku, info.device, info.topic);
                }
            }
        }
    }

    private final IIotOpResult iotOpResult = new IIotOpResult() {
        @Override
        public void noConnectIot() {
            uiTypeIot = ui_type_fail;
            /*iot断开，检测蓝牙是否可用*/
            iotUnable2CheckBle();
            checkUi();
        }

        @Override
        public void cmdWriteFail(boolean overtime, AbsCmd absCmd) {
            String cmd = absCmd.getCmd();
            if (Cmd.pt.equals(cmd)) {
                CmdPt cmdPt = (CmdPt) absCmd;
                String op = cmdPt.getOp();
                iotOpPtFail(op);
            }
            if (overtime) {
                uiTypeIot = ui_type_fail;
                /*iot断开，检测蓝牙是否可用*/
                iotUnable2CheckBle();
            }
            checkUi();
        }

        @Override
        public void cmdWriteSuc(AbsCmd absCmd) {
            checkUi();
        }

        @Override
        public void cmdRead(String cmd, String cmdJsonStr) {
            if (uiTypeBle == ui_type_normal) return;/*蓝牙通信可用，忽略iot通信信息*/
            if (Cmd.brightness.equals(cmd)) {
                /*解析亮度*/
                ResultBrightness resultBrightness = JsonUtil.fromJson(cmdJsonStr, ResultBrightness.class);
                if (resultBrightness != null) {
                    ext.brightness = resultBrightness.getBrightness();
                }
            } else if (Cmd.pt.equals(cmd)) {
                /*解析透传*/
                ResultPt resultPt = JsonUtil.fromJson(cmdJsonStr, ResultPt.class);
                parseResultPt(resultPt);
            } else if (Cmd.bulb.equals(cmd)) {
                /*解析球泡串颜色*/
                ResultBulb resultBulb = JsonUtil.fromJson(cmdJsonStr, ResultBulb.class);
                if (resultBulb != null) {
                    List<String> value = resultBulb.value;
                    SubModeColor subModeColor = CmdStatusV0.parseBulbColorRgbSet(value);
                    Mode mode = new Mode();
                    mode.subMode = subModeColor;
                    info.mode = mode;
                }
            } else if (Cmd.string.equals(cmd)) {
                /*解析球泡串数量*/
                ResultString resultString = JsonUtil.fromJson(cmdJsonStr, ResultString.class);
                if (resultString != null) {
                    int stringNum = resultString.stringNum;
                    if (stringNum > 0) {
                        ext.bulbNum = stringNum;
                        int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
                        EventBulbSettingResult.sendEventBulbSetting(true, bulbStringSetting[0], bulbStringSetting[1], ext.bulbNum);
                    }
                }
            } else if (Cmd.color_4_alexa.equals(cmd)) {
                ResultColor resultColor = JsonUtil.fromJson(cmdJsonStr, ResultColor.class);
                if (resultColor != null) {
                    int color = resultColor.toColor();
                    if (color != 0) {
                        int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
                        int bulbStringNum = bulbStringSetting[1] * ext.bulbNum;
                        SubModeColor subModeColor = SubModeColor.makeSubModeColor4SetAllBulb(bulbStringNum, color);
                        Mode mode = new Mode();
                        mode.subMode = subModeColor;
                        info.mode = mode;
                    }
                }
            } else if (Cmd.color_tem_4_alexa.equals(cmd)) {
                ResultColorTem resultColorTem = JsonUtil.fromJson(cmdJsonStr, ResultColorTem.class);
                if (resultColorTem != null) {
                    /*切换成颜色模式*/
                    int colorValue;
                    int colorTemInKelvin = resultColorTem.getColorTemInKelvin();
                    int[] temColorByKelvin = Constant.getTemColorByKelvin(colorTemInKelvin);
                    if (temColorByKelvin[0] == 1) {
                        colorValue = temColorByKelvin[2];
                    } else {
                        Color color = resultColorTem.getColor();
                        colorValue = color.toColor();
                    }
                    if (colorValue != 0) {
                        int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
                        int bulbStringNum = bulbStringSetting[1] * ext.bulbNum;
                        SubModeColor subModeColor = SubModeColor.makeSubModeColor4SetAllBulb(bulbStringNum, colorValue);
                        Mode mode = new Mode();
                        mode.subMode = subModeColor;
                        info.mode = mode;
                    }
                }
            }
            checkUi();
        }

        @Override
        public void cmdOnline(String softVersion, String cmdJsonStr, String wifiSoftVersion) {
            if (!TextUtils.isEmpty(wifiSoftVersion)) {
                ext.wifiSoftVersion = wifiSoftVersion;
            }
            CmdStatusV0 statusV0 = CmdStatusV0.parseJson(softVersion, cmdJsonStr);
            uiTypeIot = statusV0 == null ? ui_type_fail : ui_type_normal;
            if (uiTypeBle == ui_type_normal) return;/*蓝牙通信可用，忽略iot通信信息*/
            if (statusV0 == null) {
                /*iot断开，检测蓝牙是否可用*/
                iotUnable2CheckBle();
            } else {
                info.open = statusV0.on;
                ext.brightness = statusV0.brightness;
                ext.bulbNum = statusV0.stringNum;

                info.versionSoft = statusV0.softVersion;
                info.mode = statusV0.mode;

                ext.timer1 = statusV0.timer1;
                ext.timer2 = statusV0.timer2;
                ext.timer3 = statusV0.timer3;
                ext.timer4 = statusV0.timer4;
                ext.wakeUpInfo = statusV0.wakeUpInfo;
                ext.sleepInfo = statusV0.sleepInfo;
                /*通知事件*/
                TimerResultEvent.sendTimerResultEvent(false, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
                SleepSucEvent.sendSleepSucEvent(false, ext.sleepInfo);
                WakeupSucEvent.sendWakeUpSucEvent(false, ext.wakeUpInfo);
            }
            checkUi();
        }
    };

    private void iotUnable2CheckBle() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "iotUnable2CheckBle() uiTypeBle = " + uiTypeBle + " ; retryConnectDeviceTimes = " + retryConnectDeviceTimes);
        }
        if (uiTypeBle != ui_type_normal) {
            if (retryConnectDeviceTimes < max_retry_connect_device_times) {
                retryConnectDeviceTimes++;
                boolean opCommEnable = bleOp.isOpCommEnable();
                if (!opCommEnable) {
                    uiTypeBle = ui_type_def;
                    BluetoothDevice bluetoothDevice = null;
                    if (uiResult != null) bluetoothDevice = uiResult.getCurBluetoothDevice();
                    bleOp.beOpComm(bluetoothDevice);
                }
            }

        }
    }

    private void parseResultPt(ResultPt resultPt) {
        if (resultPt == null) return;
        String opcode = resultPt.getOpcode();
        byte[] opBytes = resultPt.getOpBytes();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parseResultPt() opcode = " + opcode + " ; bytesHexString = " + BleUtil.bytesToHexString(opBytes));
        }
        byte[] validBytes = BleUtil.parseValidBleBytes(opBytes);
        if (CmdPt.pt_op_wakeup.equals(opcode)) {
            /*解析唤醒透传信息*/
            ext.wakeUpInfo = WakeUpInfo.parseBytes(validBytes);
            WakeupSucEvent.sendWakeUpSucEvent(true, ext.wakeUpInfo);
        } else if (CmdPt.pt_op_sleep.equals(opcode)) {
            /*解析睡眠透传信息*/
            ext.sleepInfo = SleepInfo.parseBytes(validBytes);
            SleepSucEvent.sendSleepSucEvent(true, ext.sleepInfo);
        } else if (CmdPt.pt_op_timer.equals(opcode)) {
            /*解析定时透传信息*/
            SparseArray<Timer> timers = NewTimerV1.parseBytes(validBytes);
            int size = timers.size();
            for (int i = 0; i < size; i++) {
                int group = timers.keyAt(i);
                Timer timer = timers.valueAt(i);
                if (group == 0) {
                    ext.timer1 = timer;
                } else if (group == 1) {
                    ext.timer2 = timer;
                } else if (group == 2) {
                    ext.timer3 = timer;
                } else if (group == 3) {
                    ext.timer4 = timer;
                }
            }
            TimerResultEvent.sendTimerResultEvent(true, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
        } else if (CmdPt.pt_op_mode.equals(opcode)) {
            /*解析模式透传信息*/
            Mode mode = new Mode();
            mode.parse(validBytes);
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeColor) {
                /*颜色模式;需要解析球泡串的各个球泡的色值*/
                ((SubModeColor) subMode).rgbSet = resultPt.getOpMode4SubModeColorEffectRgbSet();
            }
            info.mode = mode;
        }
    }

    private void iotOpPtFail(String op) {
        if (CmdPt.pt_op_timer.equals(op)) {
            /*定时操作失败*/
            TimerResultEvent.sendTimerResultEventFail(true);
        } else if (CmdPt.pt_op_sleep.equals(op)) {
            /*睡眠操作失败*/
            SleepFailEvent.sendSleepFailEvent(true);
        } else if (CmdPt.pt_op_wakeup.equals(op)) {
            /*唤醒操作失败*/
            WakeupFailEvent.sendWakeupFailEvent(true);
        }
    }

    public UiV1(IUiResult4BleIot uiResult, BleIotInfo info) {
        this.uiResult = uiResult;
        this.info = info;
        bleOp = new BleOpV2(info, ext);
        iotOp = new IotOpV2();
    }

    private void registerEvent(boolean register) {
        if (register) {
            if (!EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().register(this);
            }
        } else {
            if (EventBus.getDefault().isRegistered(this)) {
                EventBus.getDefault().unregister(this);
            }
        }
    }

    @Override
    public boolean isSupportProtocol(int pactType, int pactCode) {
        List<Protocol> supportProtocolsV1 = Support.supportProtocolsV2;
        for (Protocol protocol : supportProtocolsV1) {
            if (protocol.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }

    @Override
    public void destroy() {
        if (!destory) {
            destory = true;
            registerEvent(false);
            bleOp.destroy();
            iotOp.destroy();
            hideLoading();
            ac = null;
            if (timerUI != null) timerUI.onDestroy();
            if (brightnessUI != null) brightnessUI.onDestroy();
            if (modeUI != null) modeUI.onDestroy();
        }
    }

    @Override
    public void onOffChange() {
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            showLoading();
            SwitchController switchController = new SwitchController(!info.open);
            bleOp.executeOp(switchController);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            CmdTurn cmdTurn = new CmdTurn(!info.open);
            iotOp.writeCmd(cmdTurn);
        }
    }

    @Override
    public void toUpdateAc(@NonNull AppCompatActivity ac) {
        if (info.canUpdate() && bleOp.isOpCommEnable()) {
            int defSkuRes = ThemeM.getDefSkuRes(info.sku, info.spec);
            OtaUpdateAcV1.jump2OtaUpdateAcV1(ac, info.sku, info.deviceName, info.versionSoft, defSkuRes, info.checkVersion);
        }
    }

    @Override
    public void layout(@NonNull AppCompatActivity ac, @NonNull PercentRelativeLayout contentParent, int headerId) {
        /*注册事件*/
        registerEvent(true);
        this.ac = ac;
        if (!layoutSuc) {
            layoutSuc = true;
            /*初始化ui组件*/
            int[] ids = new int[]{100, 101, 102, 103, 104, 105, 106};
            /*添加布局-定时*/
            timerUI = new NewTimerUI(ac);
            View fucViewTimer = timerUI.getFucView();
            fucViewTimer.setId(ids[0]);
            addViewMargin(contentParent, fucViewTimer, headerId, timerUI.getWidth(), timerUI.getHeight(), AppUtil.getScreenWidth() * 16 / 750);
            /*添加布局-亮度*/
            brightnessUI = new BrightnessUI(ac, 100, 1, false);
            View fucViewBrightness = brightnessUI.getFucView();
            fucViewBrightness.setId(ids[1]);
            addViewMargin(contentParent, fucViewBrightness, fucViewTimer.getId(), brightnessUI.getWidth(), brightnessUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
            /*添加布局-mode*/
            modeUI = new ModeUiV1(ac, info.sku, this::makeArguments);
            View fucViewMode = modeUI.getFucView();
            fucViewMode.setId(ids[3]);
            addViewMargin(contentParent, fucViewMode, fucViewBrightness.getId(), modeUI.getWidth(), modeUI.getHeight(), AppUtil.getScreenWidth() * 5 / 375);
        }
        bleOp.setOpResult(bleOpResult);
        iotOp.setOpResult(iotOpResult);
        /*开始进行op通信*/
        startOpComm();
    }

    private void startOpComm() {
        /*ble通信处理器*/
        boolean opCommEnableBle = bleOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "startOpComm() opCommEnableBle = " + opCommEnableBle);
        }
        if (!opCommEnableBle) {
            uiTypeBle = ui_type_def;
            BluetoothDevice bluetoothDevice = null;
            if (uiResult != null) bluetoothDevice = uiResult.getCurBluetoothDevice();
            bleOp.beOpComm(bluetoothDevice);
        }
        /*iot通信处理器*/
        boolean opCommEnableIot = iotOp.isOpCommEnable();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "startOpComm() opCommEnableIot = " + opCommEnableIot);
        }
        if (!opCommEnableIot) {
            uiTypeIot = ui_type_def;
            iotOp.beOpComm(info.sku, info.device, info.topic);
        }
        /*刷新ui*/
        checkUi();
    }

    @Override
    public void uiLost() {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "uiLost() layoutSuc = " + layoutSuc + " ; uiTypeIot = " + uiTypeIot + " ; uiTypeBle = " + uiTypeBle);
        }
        if (layoutSuc && (uiTypeIot == ui_type_normal || uiTypeBle == ui_type_normal)) {
            uiTypeBle = ui_type_fail;
            uiTypeIot = ui_type_fail;
            checkUi(false);
        }
    }

    private void checkUi() {
        checkUi(true);
    }

    private void checkUi(boolean reportResult) {
        if (destory) return;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "checkUi() uiTypeBle = " + uiTypeBle + " ; uiTypeIot = " + uiTypeIot);
        }
        if (ui_type_normal == uiTypeBle || ui_type_normal == uiTypeIot) {
            /*重置连接次数*/
            retryConnectDeviceTimes = 0;
            /*通信成功*/
            hideLoading();
            timerUI.show();
            info.headerOpType = ui_type_normal == uiTypeBle ? Cons.op_type_ble : Cons.op_type_iot;
            boolean open = info.open;
            if (open) {
                brightnessUI.show();
                brightnessUI.updateBrightness(true, ext.brightness);
                modeUI.show();
                checkSubMode();
                modeUI.setMode(info.mode);
            } else {
                brightnessUI.hide();
                modeUI.hide();
            }

            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_suc);
        } else if (ui_type_fail == uiTypeBle && ui_type_fail == uiTypeIot) {
            /*重置连接次数*/
            retryConnectDeviceTimes = 0;
            /*通信失败*/
            hideLoading();
            timerUI.hide();
            brightnessUI.hide();
            modeUI.hide();
            hideLoading();
            /*断开连接-重置通信Op*/
            iotOp.destroy();
            bleOp.destroy();
            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_fail);
        } else {
            hideLoading();
            /*通信中*/
            timerUI.hide();
            brightnessUI.hide();
            modeUI.hide();
            if (uiResult != null && reportResult)
                uiResult.uiResult(getBleOpType(), getIotOpType(), IUiResult4BleIot.ui_result_ing);
        }
    }

    private int getIotOpType() {
        return getOpType(uiTypeIot);
    }

    private int getBleOpType() {
        return getOpType(uiTypeBle);
    }

    private int getOpType(int step) {
        if (step == ui_type_fail) return IUiResult4BleIot.op_type_fail;
        if (step == ui_type_normal) return IUiResult4BleIot.op_type_suc;
        return IUiResult4BleIot.op_type_ing;
    }

    private void checkSubMode() {
        AbsMode mode = info.mode;
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeColor) {
                int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
                ((SubModeColor) subMode).bulbStringNum = bulbStringSetting[1] * ext.bulbNum;
            }
        }
    }

    private Bundle makeArguments(byte subMode) {
        if (subMode == BleProtocol.sub_mode_scenes) {
            Bundle bundle = new Bundle();
            bundle.putInt(ConsV1.arguments_key_scenes_color_num, 12);
            return bundle;
        }
        if (subMode == BleProtocol.sub_mode_color) {
            Bundle bundle = new Bundle();
            int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
            int bulbStringNum = bulbStringSetting[1] * ext.bulbNum;
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "makeArguments() bulbStringNum = " + bulbStringNum);
            }
            bundle.putInt(ConsV1.arguments_key_color_bulb_string_num, bulbStringNum);
            return bundle;
        }
        return null;
    }

    private void showLoading() {
        LoadingDialog.createDialog(ac, com.ihoment.base2app.R.style.DialogDim, 60 * 1000).setEventKey(TAG).show();
    }

    private void hideLoading() {
        LoadingDialog.hideDialog(TAG);
    }

    private void toast(@StringRes int strRes) {
        ToastUtil.getInstance().toast(strRes);
    }

    private void addViewMargin(PercentRelativeLayout contentParent, View subView, int belowId, int width, int height, int marginTop) {
        PercentRelativeLayout.LayoutParams lp = getLP(width, height);
        lp.addRule(RelativeLayout.BELOW, belowId);
        lp.topMargin = marginTop;
        lp.bottomMargin = 0;
        contentParent.addView(subView, lp);
    }

    private PercentRelativeLayout.LayoutParams getLP(int width, int height) {
        return new PercentRelativeLayout.LayoutParams(width, height);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onClickNewTimer(NewTimerUI.NewTimerClickEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onClickNewTimer()");
        }
        NewShowTimerAcV1.jump2NewShowTimerAcV1(ac, info, ext.wakeUpInfo, ext.sleepInfo, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onBrightnessEndEvent(BrightnessUI.EventBrightnessClickEvent event) {
        int brightness = event.brightness;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onBrightnessEndEvent() brightness = " + brightness);
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            showLoading();
            BrightnessController controller = new BrightnessController(brightness);
            bleOp.executeOp(controller);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            CmdBrightness cmdBrightness = new CmdBrightness(brightness);
            iotOp.writeCmd(cmdBrightness);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewTimerSetEventV1(NewTimerSetEventV1 event) {
        int group = event.getGroup();
        NewTimerV1 info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewTimerSetEventV1() group = " + group);
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            info.check();
            NewTimerV1Controller controller = new NewTimerV1Controller(group, NewTimerV1.toTimer(info));
            bleOp.executeOp(controller);
        } else if (iotOp.isOpCommEnable()) {
            info.check();
            NewTimerV1Controller controller = new NewTimerV1Controller(group, NewTimerV1.toTimer(info));
            CmdPt cmdPt = new CmdPt(CmdPt.pt_op_timer, controller.getValue());
            iotOp.writeCmd(cmdPt);
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            TimerResultEvent.sendTimerResultEventFail(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onNewWakeupSetEvent(NewWakeupSetEvent event) {
        WakeUpInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewWakeupSetEvent() info = " + info.toString());
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            info.check();
            WakeUpController wakeupModeController = new WakeUpController(info.enable, info.endBri, info.wakeHour, info.wakeMin, info.repeat, info.wakeTime);
            bleOp.executeOp(wakeupModeController);
        } else if (iotOp.isOpCommEnable()) {
            info.check();
            WakeUpController wakeupModeController = new WakeUpController(info.enable, info.endBri, info.wakeHour, info.wakeMin, info.repeat, info.wakeTime);
            CmdPt cmdPt = new CmdPt(CmdPt.pt_op_wakeup, wakeupModeController.getValue());
            iotOp.writeCmd(cmdPt);
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            WakeupFailEvent.sendWakeupFailEvent(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onNewSleepSetEvent(NewSleepSetEvent event) {
        SleepInfo info = event.getInfo();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onNewSleepSetEvent() info = " + info.toString());
        }
        /*蓝牙指令优先*/
        if (bleOp.isOpCommEnable()) {
            /*若当前是开启睡眠模式，但设备未开启，则提示用户打开灯*/
            if (event.isOpen() && !this.info.open) {
                toast(R.string.b2light_please_open_the_light);
                SleepFailEvent.sendSleepFailEvent(false);
            } else {
                info.check();
                SleepController sleepModeController = new SleepController(info.enable, info.startBri, info.closeTime, info.closeTime);
                bleOp.executeOp(sleepModeController);
            }
        } else if (iotOp.isOpCommEnable()) {
            /*若当前是开启睡眠模式，但设备未开启，则提示用户打开灯*/
            if (event.isOpen() && !this.info.open) {
                toast(R.string.b2light_please_open_the_light);
                SleepFailEvent.sendSleepFailEvent(false);
            } else {
                info.check();
                SleepController sleepModeController = new SleepController(info.enable, info.startBri, info.closeTime, info.closeTime);
                CmdPt cmdPt = new CmdPt(CmdPt.pt_op_sleep, sleepModeController.getValue());
                iotOp.writeCmd(cmdPt);
            }
        } else {
            toast(R.string.b2light_aal_light_connect_label_error);
            SleepFailEvent.sendSleepFailEvent(false);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onModeV1Event(Mode mode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onModeV1Event()");
        }
        changeMode(mode);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onChangeModeEvent(ChangeModeEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onChangeModeEvent()");
        }
        ISubMode subMode = event.getSubMode();
        Mode mode = new Mode();
        mode.subMode = subMode;
        changeMode(mode);
        /*统计场景使用次数*/
        if (subMode instanceof SubModeScenes) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, ParamFixedValue.times);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, "scene_" + ((SubModeScenes) subMode).type);
        } else if (subMode instanceof SubModeColor) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.color_mode, ParamFixedValue.times);
        } else if (subMode instanceof SubModeMusic) {
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
        }
    }

    private void changeMode(Mode mode) {
        if (bleOp.isOpCommEnable()) {
            showLoading();
            ModeController controller = new ModeController(mode);
            bleOp.executeOp(controller);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            if (isSubScenesMode(mode)) {
                /*场景模式；透传信息需要带上效果*/
                iotOpSubScenesMode(mode);
            } else if (isSubColorMode(mode)) {
                /*颜色模式；走cmd=bulb指令*/
                iopOpSubColorMode(mode);
            } else {
                /*音乐模式；透传模式信息即可*/
                ModeController modeController = new ModeController(mode);
                CmdPt cmdPt = new CmdPt(CmdPt.pt_op_mode, modeController.getValue());
                iotOp.writeCmd(cmdPt);
            }
        }
    }

    private void iopOpSubColorMode(Mode mode) {
        ModeController modeController = new ModeController(mode);
        byte[] value = modeController.getValue();
        CmdBulb cmdBulb = new CmdBulb(value);
        iotOp.writeCmd(cmdBulb);
    }

    private void iotOpSubScenesMode(Mode mode) {
        ModeController modeController = new ModeController(mode);
        ISubMode subMode = mode.subMode;
        CmdPt cmdPt;
        if (subMode instanceof SubModeScenes) {
            int type = ((SubModeScenes) subMode).type;
            ScenesConfig.ScenesExt localExt = ScenesConfig.getLocalExt(type);
            if (localExt == null) {
                cmdPt = new CmdPt(CmdPt.pt_op_mode, modeController.getValue());
            } else {
                /*设置场景模式+效果*/
                MultiSceneModel multiSceneModel = new MultiSceneModel();
                multiSceneModel.code = type + 1;
                multiSceneModel.effectType = type;
                multiSceneModel.speed = localExt.speed;
                int[] colors = localExt.color;
                if (colors != null && colors.length > 0) {
                    ByteArrayOutputStream baos = new ByteArrayOutputStream();
                    for (int color : colors) {
                        int[] rgb = ColorUtils.getRgb(color);
                        byte[] bytes = new byte[]{(byte) rgb[0], (byte) rgb[1], (byte) rgb[2]};
                        baos.write(bytes, 0, 3);
                    }
                    multiSceneModel.colors = baos.toByteArray();
                    StreamUtil.closeStream(baos);
                }
                List<byte[]> modeBytes = new ArrayList<>();
                /*先加入场景模式bytes*/
                modeBytes.add(modeController.getValue());
                /*再加入场景效果bytes*/
                MultipleScenesController multipleScenesController = new MultipleScenesController(multiSceneModel);
                List<byte[]> scenesEffectBytes = MultipleBleBytes.getMultipleWriteBytes(multipleScenesController);
                modeBytes.addAll(scenesEffectBytes);
                cmdPt = new CmdPt(CmdPt.pt_op_mode, modeBytes);
            }
        } else {
            cmdPt = new CmdPt(CmdPt.pt_op_mode, modeController.getValue());
        }
        iotOp.writeCmd(cmdPt);
    }

    private boolean isSubScenesMode(Mode mode) {
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode != null) {
                return subMode.subModeCommandType() == BleProtocol.sub_mode_scenes;
            }
        }
        return false;
    }

    private boolean isSubColorMode(Mode mode) {
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode != null) {
                return subMode.subModeCommandType() == BleProtocol.sub_mode_color;
            }
        }
        return false;
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onUpdateTimeEvent(UpdateTimeEvent event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onUpdateTimeEvent()");
        }
        readTimer();
    }

    private void readTimer() {
        /*重新读取定时信息*/
        if (bleOp.isOpCommEnable()) {
            SyncTimeInfo info = SyncTimeInfo.getCurrentTime();
            AbsSingleController[] controllers = new AbsSingleController[]{
                    new SyncTimeController(info.hour, info.minute, info.second, info.week),
                    new NewTimerV1Controller(0xFF),
                    new WakeUpController(),
                    new SleepController(),
            };
            bleOp.executeOp(controllers);
        } else if (iotOp.isOpCommEnable()) {
            iotOp.readCmd(new CmdStatus());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSleepUpdate(EventSleepUpdate event) {
        SleepInfo sleepInfo = event.getInfo();
        sleepInfo.check();
        ext.sleepInfo = sleepInfo;
        /*重新读取定时信息*/
        readTimer();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventChangeBulb(EventChangeBulb event) {
        int stringNum = event.stringNum;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventChangeBulb() stringNum = " + stringNum);
        }
        if (bleOp.isOpCommEnable()) {
            BulbNumController bulbNumController = new BulbNumController(stringNum);
            bleOp.executeOp(bulbNumController);
        } else if (iotOp.isOpCommEnable()) {
            CmdString cmdString = new CmdString(stringNum);
            iotOp.writeCmd(cmdString);
        } else {
            int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
            EventBulbSettingResult.sendEventBulbSetting(false, bulbStringSetting[0], bulbStringSetting[1], event.stringNum);
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onMultiSceneModel(MultiSceneModel sceneModel) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onMultiSceneModel()");
        }
        if (bleOp.isOpCommEnable()) {
            showLoading();
            MultipleScenesController multipleScenesController = new MultipleScenesController(sceneModel);
            bleOp.executeMultiOp(multipleScenesController);
        } else if (iotOp.isOpCommEnable()) {
            showLoading();
            List<byte[]> modeBytes = new ArrayList<>();
            /*先加入场景模式bytes*/
            SubModeScenes subModeScenes = new SubModeScenes();
            subModeScenes.type = sceneModel.effectType;
            Mode mode = new Mode();
            mode.subMode = subModeScenes;
            ModeController modeController = new ModeController(mode);
            modeBytes.add(modeController.getValue());
            /*再加入场景效果bytes*/
            MultipleScenesController multipleScenesController = new MultipleScenesController(sceneModel);
            List<byte[]> scenesEffectBytes = MultipleBleBytes.getMultipleWriteBytes(multipleScenesController);
            modeBytes.addAll(scenesEffectBytes);
            CmdPt cmdPt = new CmdPt(CmdPt.pt_op_mode, modeBytes);
            iotOp.writeCmd(cmdPt);
        }
    }

    @Override
    public int getStringNum() {
        return ext.bulbNum;
    }

    @NonNull
    @Override
    public FilterSceneInfo4BleIot getFilterSceneInfo() {
        return new FilterSceneInfo4BleIot(info.sku, info.versionSoft, info.versionHard, uiTypeBle == ui_type_normal, ext.wifiSoftVersion, ext.wifiHardVersion, null, null);
    }

}
