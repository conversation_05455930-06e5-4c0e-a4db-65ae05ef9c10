package com.govee.bulblightstringv1.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by lins<PERSON><PERSON> on 2019-04-29
 * H7022场景多包通知事件
 */
public class EventMultiScenes extends AbsControllerEvent {
    private int code;

    private EventMultiScenes(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, false);
    }

    static void sendFail(boolean write, byte commandType, byte proType, int code) {
        EventMultiScenes event = new EventMultiScenes(false, write, commandType, proType);
        event.code = code;
        EventBus.getDefault().post(event);
    }

    static void sendWriteResult(boolean result, byte commandType, byte proType, int code) {
        EventMultiScenes event = new EventMultiScenes(result, true, commandType, proType);
        event.code = code;
        EventBus.getDefault().post(event);
    }

    public int getCode() {
        return code;
    }
}