package com.govee.bulblightstringv1.ble;

import com.govee.base2light.ble.controller.AbsSingleController;

/**
 * Create by lins<PERSON><PERSON> on 2019-04-28
 * 球泡灯串数量 单包协议
 */
public class BulbNumController extends AbsSingleController {
    private int num;

    /**
     * 读操作
     */
    public BulbNumController() {
        super(false);
    }

    /**
     * 写操作
     *
     * @param num 球泡灯串数量
     */
    public BulbNumController(int num) {
        super(true);
        this.num = num;
    }

    @Override
    protected boolean writeResult(boolean writeSuc) {
        EventBulbNum.sendWriteResult(isWrite(), getCommandType(), getProType(), num);
        return true;
    }

    @Override
    protected byte[] translateWrite() {
        return new byte[]{(byte) num};
    }

    @Override
    protected void fail() {
        EventBulbNum.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.SINGLE_BULB_NUM;
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        num = validBytes[0];
        EventBulbNum.sendSuc(isWrite(), getCommandType(), getProType(), num);
        return true;
    }
}