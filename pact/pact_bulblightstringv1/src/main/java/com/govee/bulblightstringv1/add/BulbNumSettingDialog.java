package com.govee.bulblightstringv1.add;

import com.govee.ui.R;
import android.content.Context;
import android.graphics.Rect;
import android.view.View;
import android.widget.ImageView;
import android.widget.TextView;



import com.ihoment.base2app.adapter.BaseListAdapter;
import com.ihoment.base2app.dialog.BaseEventDialog;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.ResUtil;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;
import butterknife.BindView;

/**
 * Create by linshicong on 2019/3/1
 * 设置灯串数量弹窗
 */
public class BulbNumSettingDialog extends BaseEventDialog {
    @BindView(com.govee.bulblightstringv1.R2.id.rv_list)
    RecyclerView rvList;
    private OnChooseListener onChooseListener;
    private int maxStringNum;/*最大串数*/
    private int oneStringNum;/*单串的球泡数*/
    private int defChooseStringNum;/*默认选中的串数*/

    public static BulbNumSettingDialog createDialog(Context context, int defChooseStringNum, int maxStringNum, int oneStringNum, OnChooseListener listener) {
        return new BulbNumSettingDialog(context, defChooseStringNum, maxStringNum, oneStringNum, listener);
    }

    /**
     * @param context
     * @param defChooseStringNum 默认选中的串数
     * @param maxStringNum       最大串数
     * @param oneStringNum       单串的球泡数
     * @param listener
     */
    private BulbNumSettingDialog(Context context, int defChooseStringNum, int maxStringNum, int oneStringNum, OnChooseListener listener) {
        super(context);
        this.maxStringNum = Math.max(maxStringNum, 1);
        this.defChooseStringNum = Math.min(Math.max(defChooseStringNum, 1), this.maxStringNum);
        this.oneStringNum = oneStringNum;
        this.onChooseListener = listener;
        initUI();
    }

    private void initUI() {
        BulbNumAdapter adapter = new BulbNumAdapter();
        adapter.setItems(getList());
        rvList.addItemDecoration(new RecyclerView.ItemDecoration() {
            @Override
            public void getItemOffsets(@NonNull Rect outRect, @NonNull View view, @NonNull RecyclerView parent, @NonNull RecyclerView.State state) {
                outRect.bottom = AppUtil.getScreenWidth() * 25 / 750;
            }
        });
        adapter.setClickItemCallback((pos, item, v) -> {
            if (onChooseListener != null) {
                onChooseListener.onChoose(item.stringNum);
            }
            hide();
        });
        rvList.setLayoutManager(new LinearLayoutManager(context));
        rvList.setAdapter(adapter);
    }

    @Override
    public void hide() {
        super.hide();
        onChooseListener = null;
        context = null;
    }

    private List<BulbNumBean> getList() {
        List<BulbNumBean> list = new ArrayList<>();
        for (int i = 1; i <= maxStringNum; i++) {
            BulbNumBean bulbNumBean = new BulbNumBean(ResUtil.getStringFormat(R.string.bulb_unit, i),
                    ResUtil.getStringFormat(R.string.bulb_num, i * oneStringNum),
                    i);
            list.add(bulbNumBean);
        }
        return list;
    }

    @Override
    protected int getLayout() {
        return com.govee.bulblightstringv1.R.layout.blsv1_dialog_bulbnum_setting;
    }

    @Override
    protected int getWidth() {
        return AppUtil.getScreenWidth() * 335 / 375;
    }

    public class BulbNumAdapter extends BaseListAdapter<BulbNumBean> {
        private TextView tvBulbNum;
        private ImageView ivChoose;

        @Override
        protected int getLayout(int viewType) {
            return com.govee.bulblightstringv1.R.layout.blsv1_bulbnum_item;
        }

        @Override
        protected RecyclerView.ViewHolder createViewHolder(View v, int viewType) {
            return new BulbNumHolder(v);
        }

        class BulbNumHolder extends ListItemViewHolder<BulbNumBean> {

            BulbNumHolder(View v) {
                super(v, true, false);
                tvBulbNum = v.findViewById(com.govee.bulblightstringv1.R.id.tv_bulb_num);
                ivChoose = v.findViewById(com.govee.bulblightstringv1.R.id.iv_bulb_choose);
            }

            @Override
            protected void bind(BulbNumBean item, int position) {
                String bulbNum = item.num +
                        "&#160; &#160;" +
                        "<font color='#979797'>" +
                        item.sum +
                        "</font>";
                tvBulbNum.setText(ResUtil.fromHtml(bulbNum));
                ivChoose.setVisibility(defChooseStringNum == item.stringNum ? View.VISIBLE : View.GONE);
            }
        }
    }

    static class BulbNumBean {
        private String num;
        private String sum;
        private int stringNum;

        BulbNumBean(String num, String sum, int stringNum) {
            this.num = num;
            this.sum = sum;
            this.stringNum = stringNum;
        }
    }

    public interface OnChooseListener {
        void onChoose(int stringNum);
    }

    public static void hideDialog() {
        DialogHideEvent.sendDialogHideEvent(BulbNumSettingDialog.class.getName());
    }
}