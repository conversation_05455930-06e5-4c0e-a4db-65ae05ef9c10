package com.govee.bulblightstringv1.adjust.v2;

import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.timer.SleepSucEvent;
import com.govee.base2light.ac.timer.Timer;
import com.govee.base2light.ac.timer.TimerResultEvent;
import com.govee.base2light.ac.timer.WakeupSucEvent;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.AbsMode;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.EventBrightness;
import com.govee.base2light.ble.controller.EventHardVersion;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.ble.controller.EventNewTimeV1;
import com.govee.base2light.ble.controller.EventSleep;
import com.govee.base2light.ble.controller.EventSoftVersion;
import com.govee.base2light.ble.controller.EventSyncTime;
import com.govee.base2light.ble.controller.EventWakeUp;
import com.govee.base2light.ble.controller.EventWifiHardVersion;
import com.govee.base2light.ble.controller.EventWifiMac;
import com.govee.base2light.ble.controller.EventWifiSoftVersion;
import com.govee.base2light.ble.controller.HardVersionController;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.ble.controller.NewTimerV1;
import com.govee.base2light.ble.controller.NewTimerV1Controller;
import com.govee.base2light.ble.controller.SleepController;
import com.govee.base2light.ble.controller.SoftVersionController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.ble.controller.SyncTimeController;
import com.govee.base2light.ble.controller.SyncTimeInfo;
import com.govee.base2light.ble.controller.WakeUpController;
import com.govee.base2light.ble.controller.WifiHardVersionController;
import com.govee.base2light.ble.controller.WifiMacController;
import com.govee.base2light.ble.controller.WifiSoftVersionController;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.ble.AbsBleOp;
import com.govee.bulblightstringv1.adjust.setting.EventBulbSettingResult;
import com.govee.bulblightstringv1.ble.Ble;
import com.govee.bulblightstringv1.ble.BleProtocol;
import com.govee.bulblightstringv1.ble.BulbGroupColor;
import com.govee.bulblightstringv1.ble.BulbNumController;
import com.govee.bulblightstringv1.ble.BulbStringColorController;
import com.govee.bulblightstringv1.ble.EventBulbNum;
import com.govee.bulblightstringv1.ble.EventBulbStringColor;
import com.govee.bulblightstringv1.ble.EventMultiScenes;
import com.govee.bulblightstringv1.ble.Mode;
import com.govee.bulblightstringv1.ble.ModeController;
import com.govee.bulblightstringv1.ble.MultiSceneModel;
import com.govee.bulblightstringv1.ble.ScenesConfig;
import com.govee.bulblightstringv1.ble.SubModeColor;
import com.govee.bulblightstringv1.ble.SubModeScenes;
import com.govee.bulblightstringv1.pact.Support;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.StreamUtil;

import org.greenrobot.eventbus.EventBus;
import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.io.ByteArrayOutputStream;
import java.util.Arrays;
import java.util.List;

/**
 * Create by xieyingwu on 2020-02-12
 * ble-op控制v1版本$
 */
class BleOpV2 extends AbsBleOp {
    private static final String TAG = "BleOpV2";

    private final ExtV1 ext;

    public BleOpV2(BleIotInfo info, ExtV1 ext) {
        super(info);
        this.ext = ext;
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @Override
    public AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected void onOffChangeReadingInfo() {
        SyncTimeInfo info = SyncTimeInfo.getCurrentTime();
        AbsSingleController[] controllers = new AbsSingleController[]{
                new SoftVersionController(),
                new HardVersionController(),
                new SyncTimeController(info.hour, info.minute, info.second, info.week),
                new NewTimerV1Controller(0xFF),
                new WakeUpController(),
                new SleepController(),
                new BulbNumController(),
                new BrightnessController(),
                new SwitchController(),
                new ModeController()
        };
        getBle().startController(controllers);
    }

    @Nullable
    @Override
    protected AbsSingleController getReadModeController() {
        return new ModeController();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWifiSoftVersion(EventWifiSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWifiSoftVersion() softVersion = " + softVersion);
            }
            if (!TextUtils.isEmpty(softVersion)) {
                ext.wifiSoftVersion = softVersion;
            }
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWifiHardVersion(EventWifiHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWifiHardVersion() hardVersion = " + hardVersion);
            }
            if (!TextUtils.isEmpty(hardVersion)) {
                /*判断是否需要上报wifi版本信息*/
                boolean needReportWifiVersion = TextUtils.isEmpty(ext.wifiSoftVersion) || TextUtils.isEmpty(ext.wifiHardVersion);
                ext.wifiHardVersion = hardVersion;
                if (needReportWifiVersion) {
                    String curWifiSoftVersion = ext.wifiSoftVersion;
                    String curWifiHardVersion = ext.wifiHardVersion;
                    reportWifiVersion(curWifiSoftVersion, curWifiHardVersion);
                }
            }
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWifiMac(EventWifiMac event) {
        if (event.isResult()) {
            String mac = event.getMac();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventWifiMac()");
            }
            if (!TextUtils.isEmpty(mac)) {
                /*判断是否需要上报wifiMac*/
                boolean needReportWifiMac = TextUtils.isEmpty(info.wifiMac);
                info.wifiMac = mac;
                if (needReportWifiMac) {
                    reportWifiMac(mac);
                }
            }
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSoftVersion(EventSoftVersion event) {
        if (event.isResult()) {
            String softVersion = event.getSoftVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventSoftVersion() softVersion = " + softVersion);
            }
            info.versionSoft = softVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventHardVersion(EventHardVersion event) {
        if (event.isResult()) {
            String hardVersion = event.getHardVersion();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventHardVersion() hardVersion = " + hardVersion);
            }
            info.versionHard = hardVersion;
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSyncTime(EventSyncTime event) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSyncTime()");
        }
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBrightness(EventBrightness event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBrightness() write = " + write + " ; result = " + result);
        }
        if (result) {
            int brightness = event.getBrightness();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventBrightness() brightness = " + brightness);
            }
            ext.brightness = brightness;
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.effect_apply_all_times, ParamFixedValue.times);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBulbNum(EventBulbNum event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBulbNum() write = " + write + " ; result = " + result);
        }
        if (result) {
            int num = event.getNum();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventBulbNum() num = " + num);
            }
            ext.bulbNum = num;
        }
        if (result) {
            int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
            EventBulbSettingResult.sendEventBulbSetting(true, bulbStringSetting[0], bulbStringSetting[1], ext.bulbNum);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventNewTimerV1(EventNewTimeV1 event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventNewTimerV1()  write = " + write + " ; result = " + result);
        }
        if (result) {
            int group = event.getGroup();
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventNewTimerV1() group = " + group);
            }
            List<Timer> timers = event.getTimers();
            if (group == 0xFF) {
                ext.timer1 = timers.get(0);
                ext.timer2 = timers.get(1);
                ext.timer3 = timers.get(2);
                ext.timer4 = timers.get(3);
            } else {
                Timer timer = timers.get(0);
                if (group == 0) {
                    ext.timer1 = timer;
                } else if (group == 1) {
                    ext.timer2 = timer;
                } else if (group == 2) {
                    ext.timer3 = timer;
                } else if (group == 3) {
                    ext.timer4 = timer;
                }
            }
            TimerResultEvent.sendTimerResultEvent(write, NewTimerV1.fromTimer(ext.timer1), NewTimerV1.fromTimer(ext.timer2), NewTimerV1.fromTimer(ext.timer3), NewTimerV1.fromTimer(ext.timer4));
        } else if (write) {
            TimerResultEvent.sendTimerResultEventFail(true);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventWakeUp(EventWakeUp event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventWakeUp()  write = " + write + " ; result = " + result);
        }
        if (result) {
            WakeUpInfo wakeUpInfo = new WakeUpInfo();
            wakeUpInfo.enable = event.getEnable();
            wakeUpInfo.endBri = event.getEndBri();
            wakeUpInfo.wakeHour = event.getWakeHour();
            wakeUpInfo.wakeMin = event.getWakeMin();
            wakeUpInfo.wakeTime = event.getWakeTime();
            wakeUpInfo.repeat = event.getRepeat();
            wakeUpInfo.check();
            ext.wakeUpInfo = wakeUpInfo;
            WakeupSucEvent.sendWakeUpSucEvent(write, wakeUpInfo);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventSleep(EventSleep event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventSleep()  write = " + write + " ; result = " + result);
        }
        if (result) {
            SleepInfo sleepInfo = new SleepInfo();
            sleepInfo.enable = event.getEnable();
            sleepInfo.startBri = event.getStartBri();
            sleepInfo.closeTime = event.getCloseTime();
            sleepInfo.curTime = event.getCurTime();
            sleepInfo.check();
            ext.sleepInfo = sleepInfo;
            SleepSucEvent.sendSleepSucEvent(write, sleepInfo);
        }
        if (write) {
            opResult.bleWrite(event.getCommandType(), result);
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() write = " + write + " ; result = " + result);
        }
        /*标记是否是由其他模式切换到颜色模式*/
        boolean mode2Color = false;
        /*标记是否是由其他模式切换到场景模式*/
        boolean mode2Scenes = false;
        if (result) {
            AbsMode mode = event.getMode();
            boolean isColorSubMode = mode.subMode.subModeCommandType() == BleProtocol.sub_mode_color;
            if (isColorSubMode) {
                mode2Color = true;
                AbsMode lastMode = info.mode;
                if (lastMode != null) {
                    ISubMode subMode = lastMode.subMode;
                    if (subMode != null) {
                        mode2Color = subMode.subModeCommandType() != BleProtocol.sub_mode_color;
                    }
                }
            }
            boolean isScenesSubMode = mode.subMode.subModeCommandType() == BleProtocol.sub_mode_scenes;
            if (isScenesSubMode) {
                mode2Scenes = true;
                AbsMode lastMode = info.mode;
                if (lastMode != null) {
                    ISubMode subMode = lastMode.subMode;
                    if (subMode != null) {
                        mode2Scenes = subMode.subModeCommandType() != BleProtocol.sub_mode_scenes;
                    }
                }
            }
            info.mode = mode;
        }
        if (write) {
            if (result) {
                boolean bleWriteResult = true;
                if (mode2Color) {
                    bleWriteResult = false;
                    /*读取球泡串颜色*/
                    readBulbStringColor();
                }
                if (bleWriteResult) {
                    opResult.bleWrite(event.getCommandType(), true);
                }
                if (mode2Scenes) {
                    /*设置场景效果*/
                    sendScenesEffects();
                }
            } else {
                opResult.bleWrite(event.getCommandType(), false);
            }
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.effect_apply_all_times, ParamFixedValue.times);
            AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.adjust_effect_apply_time, ParamFixedValue.times);
        } else {
            if (result) {
                byte subModeCommandType = info.mode.subMode.subModeCommandType();
                if (subModeCommandType == BleProtocol.sub_mode_color) {
                    /*颜色模式；需要获取球泡串的色值*/
                    readBulbStringColor();
                } else {
                    /*其他模式，信息读取完成*/
                    infoOver();
                }
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    private void sendScenesEffects() {
        AbsMode mode = info.mode;
        if (mode != null) {
            ISubMode subMode = mode.subMode;
            if (subMode instanceof SubModeScenes) {
                int type = ((SubModeScenes) subMode).type;
                ScenesConfig.ScenesExt localExt = ScenesConfig.getLocalExt(type);
                if (localExt != null) {
                    MultiSceneModel multiSceneModel = new MultiSceneModel();
                    multiSceneModel.code = type + 1;
                    multiSceneModel.effectType = type;
                    multiSceneModel.speed = localExt.speed;
                    int[] colors = localExt.color;
                    if (colors != null && colors.length > 0) {
                        ByteArrayOutputStream baos = new ByteArrayOutputStream();
                        for (int color : colors) {
                            int[] rgb = ColorUtils.getRgb(color);
                            byte[] bytes = new byte[]{(byte) rgb[0], (byte) rgb[1], (byte) rgb[2]};
                            baos.write(bytes, 0, 3);
                        }
                        multiSceneModel.colors = baos.toByteArray();
                        StreamUtil.closeStream(baos);
                    }
                    EventBus.getDefault().post(multiSceneModel);
                }
            }
        }
    }

    private void readBulbStringColor() {
        int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
        int maxGroup = bulbStringMaxNum / 4 + (bulbStringMaxNum % 4 == 0 ? 0 : 1);
        AbsSingleController[] controllers = new AbsSingleController[maxGroup];
        for (int i = 0; i < maxGroup; i++) {
            controllers[i] = new BulbStringColorController(i + 1);
        }
        getBle().addController(controllers);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMultiScenes(EventMultiScenes event) {
        boolean result = event.isResult();
        boolean write = event.isWrite();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMultiScenes() write = " + write + " ; result = " + result + " ; code = " + event.getCode());
        }
        if (write) {
            if (result) {
                SubModeScenes subMode2Scenes = new SubModeScenes();
                subMode2Scenes.type = event.getCode() - 1;
                Mode mode = new Mode();
                mode.subMode = subMode2Scenes;
                info.mode = mode;
            }
            opResult.bleWrite(event.getCommandType(), result);
        }
        getBle().onMultipleControllerOk(event);
    }

    private int[] lastRgbSet;

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBulbStringColor(EventBulbStringColor event) {
        boolean result = event.isResult();
        if (result) {
            BulbGroupColor groupColor = event.groupColor;
            int group = groupColor.group;
            int bulbStringMaxNum = Support.getBulbStringMaxNum(info.goodsType, info.pactType, info.pactCode);
            int maxGroup = bulbStringMaxNum / 4 + (bulbStringMaxNum % 4 == 0 ? 0 : 1);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "onEventBulbStringColor() bulbStringMaxNum = " + bulbStringMaxNum + " ; maxGroup = " + maxGroup + " ； group = " + group);
            }
            if (group == 1) {
                lastRgbSet = new int[maxGroup * 4];
            }
            int[] rgb = groupColor.rgb;
            int destPos = Math.max(group - 1, 0) * 4;
            System.arraycopy(rgb, 0, lastRgbSet, destPos, rgb.length);
            if (group == maxGroup) {
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "onEventBulbStringColor() lastRgbSet = " + Arrays.toString(lastRgbSet));
                }
                /*读取球泡串颜色完成*/
                SubModeColor subModeColor = new SubModeColor();
                subModeColor.rgbSet = lastRgbSet;
                Mode mode = new Mode();
                mode.subMode = subModeColor;
                info.mode = mode;
                /*信息读取完成*/
                infoOver();
            }
        }
        /*检测指令*/
        getBle().controllerEvent(event);
    }

    @Override
    protected void readExtDeviceInfoAfterInfoOver() {
        AbsSingleController[] singleControllers = new AbsSingleController[]{
                new WifiSoftVersionController(),
                new WifiHardVersionController(),
                new WifiMacController(),
        };
        getBle().addController(singleControllers);
    }
}