package com.govee.bulblightstringv1.pact;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.Pact;
import com.govee.base2home.pact.Protocol;
import com.govee.base2home.theme.ThemeM;
import com.govee.base2light.ac.diy.DiyM;
import com.govee.bulblightstringv1.pact.ble.EffectOp4Ble;
import com.govee.bulblightstringv1.pact.bleiot.EffectOp4BleIot;
import com.govee.ui.R;
import com.ihoment.base2app.infra.LogInfra;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by xieyingwu on 2020-02-12
 * pact支持类$
 */
public final class Support {
    private static final String TAG = "Support";

    private Support() {
    }

    private static final String H7002 = "H7002";
    private static final String H7005 = "H7005";

    public static List<Protocol> supportProtocolsV1 = new ArrayList<>();

    public static List<Protocol> supportProtocolsV2 = new ArrayList<>();

    public static void addSupportPact() {
        Pact pact = Pact.getInstance;
        Protocol protocolBle1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_BULB_LIGHT_STRING_BLE_V1_1, GoodsType.PACT_CODE_4_BULB_LIGHT_STRING_BLE_V1_1_1);
        Protocol protocolBle2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_BULB_LIGHT_STRING_BLE_V1_2, GoodsType.PACT_CODE_4_BULB_LIGHT_STRING_BLE_V1_1_1);
        Protocol protocolBle3 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_BULB_LIGHT_STRING_BLE_V1_3, GoodsType.PACT_CODE_4_BULB_LIGHT_STRING_BLE_V1_1_1);
        supportProtocolsV1.add(protocolBle1);
        supportProtocolsV1.add(protocolBle2);
        supportProtocolsV1.add(protocolBle3);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1, protocolBle1);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1, protocolBle2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1, protocolBle3);

        Protocol protocolBleIot1 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_BULB_LIGHT_STRING_BLE_IOT_V1_1, GoodsType.PACT_CODE_4_BULB_LIGHT_STRING_BLE_IOT_V1_1);
        Protocol protocolBleIot2 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_BULB_LIGHT_STRING_BLE_IOT_V1_2, GoodsType.PACT_CODE_4_BULB_LIGHT_STRING_BLE_IOT_V1_1);
        Protocol protocolBleIot3 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_BULB_LIGHT_STRING_BLE_IOT_V1_3, GoodsType.PACT_CODE_4_BULB_LIGHT_STRING_BLE_IOT_V1_1);
        Protocol protocolBleIot4 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_BULB_LIGHT_STRING_BLE_IOT_V1_4, GoodsType.PACT_CODE_4_BULB_LIGHT_STRING_BLE_IOT_V1_1);
        Protocol protocolBleIot5 = GoodsType.beProtocol(GoodsType.PACT_TYPE_4_BULB_LIGHT_STRING_BLE_IOT_V1_5, GoodsType.PACT_CODE_4_BULB_LIGHT_STRING_BLE_IOT_V1_1);
        supportProtocolsV2.add(protocolBleIot1);
        supportProtocolsV2.add(protocolBleIot2);
        supportProtocolsV2.add(protocolBleIot3);
        supportProtocolsV2.add(protocolBleIot4);
        supportProtocolsV2.add(protocolBleIot5);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1, protocolBleIot1);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1, protocolBleIot2);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1, protocolBleIot3);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1, protocolBleIot4);
        pact.addProtocol(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1, protocolBleIot5);

        /*初始化支持的默认sku图*/
        ThemeM.getInstance.addDefSkuRes(H7002, com.govee.bulblightstringv1.R.mipmap.add_list_type_device_7002);
        ThemeM.getInstance.addDefSkuRes(H7005, com.govee.bulblightstringv1.R.mipmap.add_list_type_device_7005);

        /*注册diyOp*/
        DiyM.getInstance.addDiyOp(EffectOp4Ble.getInstance);
        /*注册diyOp*/
        DiyM.getInstance.addDiyOp(EffectOp4BleIot.getInstance);
    }

    public static boolean isSupportWifi(int goodsType) {
        for (int type : effect4bleWifiOpGoodsTypeArray) {
            if (type == goodsType) return true;
        }
        return false;
    }

    /**
     * 设备列表的goodsType集合-ble
     */
    public static final int[] deviceItemGoodsTypes4Ble = {
            GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1
    };
    /**
     * 设备列表的goodsType集合-ble+iot
     */
    public static final int[] deviceItemGoodsTypes4BleIot = {
            GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1,
    };

    /**
     * 针对灯效op操作的ble的goodsType
     */
    public static final int[] effect4bleOpGoodsTypeArray = {
            GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1,
    };
    /**
     * 针对灯效op操作的ble+wifi的goodsType
     */
    public static final int[] effect4bleWifiOpGoodsTypeArray = {
            GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1,
    };

    public static boolean supportPactV1(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1 && !supportProtocolsV1.isEmpty()) {
            for (Protocol pro : supportProtocolsV1) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    public static boolean supportPactV2(int goodsType, Protocol protocol) {
        if (protocol == null) return false;
        int pactType = protocol.pactType;
        int pactCode = protocol.pactCode;
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1 && !supportProtocolsV2.isEmpty()) {
            for (Protocol pro : supportProtocolsV2) {
                if (pro.isSameProtocol(pactType, pactCode)) return true;
            }
        }
        return false;
    }

    /**
     * 支持ble的goodsType集合
     */
    public static final String[] supportBleGoodsTypeSet = new String[]{
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1),
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1)
    };

    /**
     * 支持iot的goodsType集合
     */
    public static final String[] supportIotGoodsTypeSet = new String[]{
            String.valueOf(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1)
    };

    /**
     * 依据协议类型和code码获取bulb的最大串数和单串球泡个数
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return int[2];[0]最大串数-[1]单串球泡个数
     */
    public static int[] getBulbStringSetting(int goodsType, int pactType, int pactCode) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "getBulbStringSetting() goodsType = " + goodsType + " ; pactType = " + pactType + " ; pactCode = " + pactCode);
        }
        return new int[]{2, 15};
    }

    /**
     * 获取对应的球泡最大个数
     *
     * @param goodsType
     * @param pactType
     * @param pactCode
     * @return
     */
    public static int getBulbStringMaxNum(int goodsType, int pactType, int pactCode) {
        int[] bulbStringSetting = getBulbStringSetting(goodsType, pactType, pactCode);
        return bulbStringSetting[0] * bulbStringSetting[1];
    }

    /**
     * 获取wifi输入限制
     *
     * @return [2];[0]ssidInputLimit;[1]passwordInputLimit
     */
    public static int[] getWifiInputLimit() {
        return new int[]{32, 64};
    }

    public static int[] getDefHeaderRes(int goodsType) {
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1) {
            return new int[]{R.mipmap.new_light_title_7002_on, R.mipmap.new_light_title_7002_off, R.mipmap.new_light_title_7002_off};
        }
        if (goodsType == GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1) {
            return new int[]{R.mipmap.new_light_title_7022_alexa_on, R.mipmap.new_light_title_7022_alexa_off, R.mipmap.new_light_title_7022_alexa_off};
        }
        return null;
    }

    /**
     * 检测给到wifi设备的当前服务器域名版本值
     *
     * @param goodsType
     * @param sku
     * @return
     */
    public static int check2WifiDeviceRunModeVersion(int goodsType, String sku) {
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "check2WifiDeviceRunModeVersion() goodsType = " + goodsType + " ; sku = " + sku);
        }
        return 0;/*因之前已经出去了旧款sku因此默认使用v0*/
    }
}
