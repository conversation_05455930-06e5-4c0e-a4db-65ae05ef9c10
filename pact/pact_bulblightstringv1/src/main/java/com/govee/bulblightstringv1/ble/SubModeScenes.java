package com.govee.bulblightstringv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.ModeStr;
import com.ihoment.base2app.infra.StorageInfra;


/**
 * Create by x<PERSON><PERSON><PERSON> on 2019-12-03
 * 场景模式$
 */
public class SubModeScenes implements ISubMode {
    public int type = BleProtocol.value_sub_mode_scenes_illumination;

    @Override
    public void loadLocal() {
        Type type = StorageInfra.get(Type.class);
        if (type != null) {
            this.type = type.type;
        }
    }

    public static class Type {
        int type;
    }

    @Override
    public void saveLocal() {
        Type value = new Type();
        value.type = type;
        StorageInfra.put(value);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_scenes;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr = ModeStr.getBulbScenesSubModeStr(type);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_scenes, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        int diy = BleUtil.getUnsignedByte(validBytes[0]);
        if (diy > 0) {
            type = Math.max(0, diy - 1);
        }
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] values = new byte[17];
        values[0] = BleProtocol.sub_mode_scenes;
        values[1] = (byte) (type + 1);
        return values;
    }
}