package com.govee.bulblightstringv1.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-03-03
 * 球泡串颜色event$
 */
public class EventBulbStringColor extends AbsControllerEvent {
    public BulbGroupColor groupColor;

    protected EventBulbStringColor(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    public static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventBulbStringColor(false, write, commandType, proType));
    }

    public static void sendSuc(boolean write, byte commandType, byte proType, BulbGroupColor groupColor) {
        EventBulbStringColor event = new EventBulbStringColor(true, write, commandType, proType);
        event.groupColor = groupColor;
        EventBus.getDefault().post(event);
    }
}