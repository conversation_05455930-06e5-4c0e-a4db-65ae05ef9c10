package com.govee.bulblightstringv1.iot;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2home.util.Encode;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by <PERSON><PERSON>ying<PERSON> on 2020/3/11
 * 球泡颜色设置cmd$
 */
public class CmdBulb extends AbsCmd {
    List<String> value = new ArrayList<>();

    public CmdBulb(byte[] bytes) {
        String valueStr = Encode.encryptByBase64(bytes);
        value.add(valueStr);
    }

    @Override
    public String getCmd() {
        return Cmd.bulb;
    }

}