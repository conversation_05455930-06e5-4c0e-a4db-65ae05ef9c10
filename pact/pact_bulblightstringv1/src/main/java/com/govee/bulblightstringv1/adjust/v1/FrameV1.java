package com.govee.bulblightstringv1.adjust.v1;

import com.govee.base2light.pact.AbsFrameBle;
import com.govee.base2light.pact.BleInfo;
import com.govee.base2light.pact.IFrameResult;
import com.govee.base2light.pact.IUi;
import com.govee.base2light.pact.IUiResult4Ble;
import com.govee.base2light.pact.ble.AbsBlePact;
import com.govee.base2light.pact.ble.IPactResult4Ble;
import com.govee.bulblightstringv1.adjust.IUi4ExtInfo;
import com.govee.bulblightstringv1.adjust.setting.SettingAc;
import com.govee.bulblightstringv1.pact.Support;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2020-02-12
 * v1版的框架实现-ble$
 * <p>1.协议判断<p/>
 * <p>2.构建ui布局<p/>
 * <p>3.协议控制<p/>
 */
public class FrameV1 extends AbsFrameBle {

    public FrameV1(IFrameResult frameResult, BleInfo info) {
        super(frameResult, info);
    }

    @Override
    protected AbsBlePact makeBlePact(IPactResult4Ble iPactResult4Ble) {
        return new BlePactV1(iPactResult4Ble);
    }

    @Override
    protected List<IUi> getSupportUiList(IUiResult4Ble iUiResult4Ble, BleInfo bleInfo) {
        List<IUi> uiList = new ArrayList<>();
        uiList.add(new UiV1(iUiResult4Ble, bleInfo));
        return uiList;
    }

    @Override
    public void toSettingAc(@NonNull AppCompatActivity ac) {
        if (isDestroy()) return;
        boolean bulbEnable = isUiSuc();
        int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
        SettingAc.jump2SettingAc(ac, info.sku, info.device, info.deviceName, 22, bulbEnable, bulbStringSetting[0], bulbStringSetting[1], getBulbNum(), info.versionHard);
    }

    protected int getBulbNum() {
        if (curUi instanceof IUi4ExtInfo) {
            return ((IUi4ExtInfo) curUi).getStringNum();
        }
        return 1;
    }
}