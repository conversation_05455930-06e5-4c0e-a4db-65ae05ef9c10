package com.govee.bulblightstringv1.ble;


/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-02-12
 * protocol
 */
public interface BleProtocol {
    /**
     * 子模式-音乐
     */
    byte sub_mode_music = (byte) 0x0e;

    /**
     * 音乐效果-动感
     */
    byte value_sub_mode_music_power = 0x00;

    /**
     * 音乐效果-柔和
     */
    byte value_sub_mode_music_soft = 0x01;

    /**
     * 场景多包
     */
    byte MULTI_SCENE = (byte) 0x01;

    /**
     * 子模式-场景
     */
    byte sub_mode_scenes = (byte) 0x09;

    /**
     * 子模式-颜色
     */
    byte sub_mode_color = (byte) 0x0b;

    /**
     * 场景效果-照明
     */
    int value_sub_mode_scenes_illumination = 0;

    /**
     * 场景效果-渐变
     */
    int value_sub_mode_scenes_fade = 1;

    /**
     * 场景效果-雨滴
     */
    int value_sub_mode_scenes_raindrops = 2;

    /**
     * 场景效果-炫彩
     */
    int value_sub_mode_scenes_colorful = 3;

    /**
     * 场景效果-跑马灯
     */
    int value_sub_mode_scenes_marquee = 4;

    /**
     * 场景效果-眨眼
     */
    int value_sub_mode_scenes_blinking = 5;

    /**
     * 场景效果-雪花
     */
    int value_sub_mode_scenes_snow = 6;

    /**
     * 场景效果-星空
     */
    int value_sub_mode_scenes_sky = 7;

    /**
     * 单包-灯串数量
     */
    byte SINGLE_BULB_NUM = (byte) 0x0f;

    /**
     * 读取球泡串颜色
     */
    byte SINGLE_BULB_COLOR = (byte) 0xa2;

}