package com.govee.bulblightstringv1.adjust.ui;

import com.govee.ui.R;
import android.os.Bundle;

import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsColorUiMode;
import com.govee.base2light.ui.mode.IArguments;

import com.govee.bulblightstringv1.ble.BleProtocol;
import com.govee.bulblightstringv1.ble.SubModeColor;

/**
 * Create by xieyingwu on 2019-07-22
 * color ui mode
 */
public class ColorUiMode extends AbsColorUiMode {
    private IArguments arguments;

    public ColorUiMode(String sku, IArguments arguments) {
        super(sku);
        this.arguments = arguments;
    }

    @Override
    public AbsModeFragment getUiFragment() {
        ColorFragment fragment = new ColorFragment();
        Bundle args = null;
        if (arguments != null) {
            args = this.arguments.arguments(getSubModeType());
        }
        fragment.addExtArguments(args, getSku());
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_color, R.mipmap.new_control_light_btb_mode_color_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.loadLocal();
        return subModeColor;
    }
}
