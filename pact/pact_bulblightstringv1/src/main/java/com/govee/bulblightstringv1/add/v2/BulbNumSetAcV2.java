package com.govee.bulblightstringv1.add.v2;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import com.govee.bulblightstringv1.ConsV1;
import com.govee.bulblightstringv1.add.BulbNumSettingDialog;
import com.govee.bulblightstringv1.ble.Ble;
import com.govee.bulblightstringv1.ble.BulbNumController;
import com.govee.bulblightstringv1.ble.EventBulbNum;
import com.govee.ui.ac.AbsBleBulbNumSetAc;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JumpUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020/3/6
 * 球泡串设置Ac-v2$
 */
public class BulbNumSetAcV2 extends AbsBleBulbNumSetAc {
    private static final String TAG = "BulbNumSetAcV2";
    private static final int max_retry_set_bulb_num_times = 1;
    private AddInfoV2 addInfo;
    private int failTimes;

    /**
     * 跳转到球泡串设置界面
     *
     * @param ac
     * @param stringMaxNum
     * @param oneStringNum
     * @param addInfo
     */
    public static void jump2bulbNumSettingAcV2(Activity ac, int stringMaxNum, int oneStringNum, @NonNull AddInfoV2 addInfo) {
        Bundle bundle = makeAcBundle(stringMaxNum, oneStringNum);
        bundle.putParcelable(ConsV1.intent_ac_key_addInfo, addInfo);
        JumpUtil.jumpWithBundle(ac, BulbNumSetAcV2.class, true, bundle);
    }


    @Override
    protected void getExtParams(Intent intent) {
        addInfo = intent.getParcelableExtra(ConsV1.intent_ac_key_addInfo);
    }

    @Override
    protected String getSku() {
        return addInfo.sku;
    }

    @Override
    protected void showBulbNumChooseDialog(int curStringNum, int stringMaxNum, int oneStringNum) {
        BulbNumSettingDialog.createDialog(this, curStringNum, stringMaxNum, oneStringNum, this::chooseBulbNum).show();
    }

    private void chooseBulbNum(int stringNum) {
        this.curStringNum = stringNum;
        updateBulbStringNum(curStringNum, oneStringNum);
    }

    @Override
    protected void setBulbNum(int stringNum) {
        showLoading();
        BulbNumController bulbNumController = new BulbNumController(stringNum);
        Ble.getInstance.startController(bulbNumController);
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventBulbNum(EventBulbNum event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBulbNum() result = " + result);
        }
        if (!result) {
            /*若写操作失败，且失败尝试次数未达到上限，则尝试一次*/
            failTimes++;
            if (failTimes <= max_retry_set_bulb_num_times) {
                setBulbNum(curStringNum);
                return;
            }
        }
        failTimes = 0;
        hideLoading();
        if (result) {
            /*设置完成；关闭界面*/
            closeAc();
        }
    }

    @Override
    protected void closeAc() {
        if (BaseApplication.getBaseApplication().isInBackground()) return;/*若应用处于后台，则不进行界面跳转*/
        hideLoading();
        BulbNumSettingDialog.hideDialog();
        /*跳转到wifi设置页*/
        WifiChooseAc.jump2wifiChooseAcByAdd(this, addInfo);
    }

    @Override
    protected boolean ignoreBackPressed() {
        return true;
    }
}