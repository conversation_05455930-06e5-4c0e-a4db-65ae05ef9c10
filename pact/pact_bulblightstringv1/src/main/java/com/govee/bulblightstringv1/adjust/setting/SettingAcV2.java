package com.govee.bulblightstringv1.adjust.setting;

import android.app.Activity;
import android.os.Bundle;

import com.govee.bulblightstringv1.add.BulbNumSettingDialog;
import com.govee.bulblightstringv1.add.v2.WifiChooseAc;
import com.govee.bulblightstringv1.pact.Support;
import com.govee.ui.ac.AbsBleWifiBulbSettingAc;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JumpUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

/**
 * Create by xieyingwu on 2020/3/9
 * 设置页-ble+wifi$
 */
public class SettingAcV2 extends AbsBleWifiBulbSettingAc {

    /**
     * 跳转到设置界面v2
     *
     * @param ac
     * @param goodsType
     * @param sku
     * @param device
     * @param deviceName
     * @param deviceNameInputLimit
     * @param bulbStringEnable
     * @param maxStringNum
     * @param oneStringNum
     * @param stringNum
     * @param wifiMac
     * @param bleAddress
     */
    public static void jump2SettingAcV2(Activity ac, int goodsType, String sku, String device, String deviceName, int deviceNameInputLimit, boolean bulbStringEnable, int maxStringNum, int oneStringNum, int stringNum, String wifiMac, String bleAddress, String bleName, String versionHard) {
        stringNum = Math.max(1, stringNum);
        int[] wifiInputLimit = Support.getWifiInputLimit();
        Bundle bundle = makeAcBundleV1(true, goodsType, sku, device, deviceName, deviceNameInputLimit, bulbStringEnable, maxStringNum, stringNum, oneStringNum, wifiMac, wifiInputLimit[0], wifiInputLimit[1], bleAddress, bleName, versionHard);
        JumpUtil.jumpWithBundle(ac, SettingAcV2.class, bundle);
    }

    @Override
    protected void jump2WifiSettingAc() {
        WifiChooseAc.jump2wifiChooseAcByChangeWifi(this, goodsType, sku, device, deviceName, bleName, bleAddress, versionHard);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventBulbSetting(EventBulbSettingResult event) {
        hideLoading();
        int maxStringNum = event.maxStringNum;
        int oneStringNum = event.oneStringNum;
        int stringNum = event.stringNum;
        boolean bulbStringEnable = event.bulbStringEnable;
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBulbSetting() bulbStringEnable = " + bulbStringEnable + " ; maxStringNum = " + maxStringNum + " ; oneStringNum = " + oneStringNum + " ; stringNum = " + stringNum);
        }
        updateBulbStringUi(bulbStringEnable, maxStringNum, oneStringNum, stringNum);
        BulbNumSettingDialog.hideDialog();
    }

    @Override
    protected void execRecycle() {
        super.execRecycle();
        BulbNumSettingDialog.hideDialog();
    }

    @Override
    protected void changeBulbSetting(int bulbMaxStringNum, int bulbOneStringNum, int bulbStringNum) {
        BulbNumSettingDialog.createDialog(this, bulbStringNum, bulbMaxStringNum, bulbOneStringNum, stringNum -> {
            showLoading();
            EventChangeBulb.sendEventChangeBulb(stringNum);
        }).show();
    }
}