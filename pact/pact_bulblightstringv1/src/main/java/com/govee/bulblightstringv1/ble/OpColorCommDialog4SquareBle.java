package com.govee.bulblightstringv1.ble;

import android.content.Context;

import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.light.AbsOpCommDialog4BleV2;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by linshicong on 1/28/21
 * op color操作应用$
 */
public class OpColorCommDialog4SquareBle extends AbsOpCommDialog4BleV2 {
    private final EffectData.ColorEffect colorEffect;

    protected OpColorCommDialog4SquareBle(Context context, String bleAddress, String bleName, @NonNull EffectData.ColorEffect colorEffect) {
        super(context, bleAddress, bleName, EFFECT_TYPE_COLOR, -1);
        this.colorEffect = colorEffect;
    }

    @Override
    protected void bleOping() {
        SubModeColor subModeColor = SubModeColor.makeSubModeColor(colorEffect.colorSet[0]);
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        ModeController modeController = new ModeController(mode);
        getBle().startController(modeController);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        getBle().controllerEvent(event);
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    public static void showDialog(Context context, String bleAddress, String bleName, @NonNull EffectData.ColorEffect colorEffect) {
        new OpColorCommDialog4SquareBle(context, bleAddress, bleName, colorEffect).show();
    }
}