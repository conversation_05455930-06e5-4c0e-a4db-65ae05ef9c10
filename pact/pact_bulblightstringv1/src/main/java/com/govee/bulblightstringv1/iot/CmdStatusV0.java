package com.govee.bulblightstringv1.iot;

import android.text.TextUtils;
import android.util.SparseArray;

import com.govee.base2home.util.Encode;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ac.AbsIotManagerV1;
import com.govee.base2light.ac.adjust.SleepInfo;
import com.govee.base2light.ac.adjust.WakeUpInfo;
import com.govee.base2light.ac.timer.Timer;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.BleProtocolConstants;
import com.govee.base2light.ble.controller.NewTimerV1;
import com.govee.bulblightstringv1.ble.BleProtocol;
import com.govee.bulblightstringv1.ble.BulbGroupColor;
import com.govee.bulblightstringv1.ble.Mode;
import com.govee.bulblightstringv1.ble.SubModeColor;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JsonUtil;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.Keep;

/**
 * Create by xieyingwu on 2020/3/5
 * cmd=status的iotV0版本$
 */
public class CmdStatusV0 {
    private static final String TAG = "CmdStatusV0";

    public boolean on;/*开关*/
    public int stringNum;/*球泡串数*/
    public int brightness;/*亮度*/

    public Mode mode;/*模式*/
    public String softVersion;/*蓝牙软件版本*/

    public Timer timer1 = new Timer();
    public Timer timer2 = new Timer();
    public Timer timer3 = new Timer();
    public Timer timer4 = new Timer();
    public WakeUpInfo wakeUpInfo = new WakeUpInfo();
    public SleepInfo sleepInfo = new SleepInfo();

    public static CmdStatusV0 parseJson(String softVersion, String json) {
        if (TextUtils.isEmpty(json)) return null;
        CmdStatusV0 statusV0 = new CmdStatusV0();
        statusV0.softVersion = softVersion;
        String stateJsonStr = AbsIotManagerV1.getJsonObjectStr(json, Cmd.parse_json_key_state);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "stateJsonStr = " + stateJsonStr);
        }
        /*解析state字段内参数*/
        State state = JsonUtil.fromJson(stateJsonStr, State.class);
        boolean isColorSubMode = false;
        if (state != null) {
            statusV0.on = state.onOff == 1;
            statusV0.brightness = state.brightness;
            statusV0.stringNum = state.stringNum;
            isColorSubMode = state.mode == BleProtocol.sub_mode_color;
        }
        /*解析op字段内参数*/
        String opJsonStr = AbsIotManagerV1.getJsonObjectStr(json, Cmd.parse_json_op);
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "opJsonStr = " + opJsonStr);
        }
        ResultPt op = JsonUtil.fromJson(opJsonStr, ResultPt.class);
        if (op != null) {
            /*timer*/
            List<String> timerValueSet = op.getTimerValue();
            if (timerValueSet != null && !timerValueSet.isEmpty()) {
                String bleBase64BytesStr = timerValueSet.get(0);
                byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "parseJson() timer originalBytes = " + BleUtil.bytesToHexString(originalBytes));
                }
                if (originalBytes != null && originalBytes.length == 20) {
                    byte[] validBytes = BleUtil.parseValidBleBytes(originalBytes);
                    SparseArray<Timer> timers = NewTimerV1.parseBytes(validBytes);
                    if (timers.size() != 0) {
                        for (int i = 0; i < timers.size(); i++) {
                            int group = timers.keyAt(i);
                            Timer timer = timers.valueAt(i);
                            if (group == 0) {
                                statusV0.timer1 = timer;
                            } else if (group == 1) {
                                statusV0.timer2 = timer;
                            } else if (group == 2) {
                                statusV0.timer3 = timer;
                            } else if (group == 3) {
                                statusV0.timer4 = timer;
                            }
                        }
                    }
                }
            }
            /*sleep*/
            List<String> sleepValueSet = op.getSleepValue();
            if (sleepValueSet != null && !sleepValueSet.isEmpty()) {
                String bleBase64BytesStr = sleepValueSet.get(0);
                byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "parseJson() sleep originalBytes = " + BleUtil.bytesToHexString(originalBytes));
                }
                if (originalBytes != null && originalBytes.length == 20) {
                    byte[] validBytes = BleUtil.parseValidBleBytes(originalBytes);
                    statusV0.sleepInfo = SleepInfo.parseBytes(validBytes);
                }
            }
            /*wakeup*/
            List<String> wakeupValueSet = op.getWakeupValue();
            if (wakeupValueSet != null && !wakeupValueSet.isEmpty()) {
                String bleBase64BytesStr = wakeupValueSet.get(0);
                byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
                if (LogInfra.openLog()) {
                    LogInfra.Log.i(TAG, "parseJson() wakeup originalBytes = " + BleUtil.bytesToHexString(originalBytes));
                }
                if (originalBytes != null && originalBytes.length == 20) {
                    byte[] validBytes = BleUtil.parseValidBleBytes(originalBytes);
                    statusV0.wakeUpInfo = WakeUpInfo.parseBytes(validBytes);
                }
            }
            /*mode*/
            List<String> modeValueSet = op.getModeValue();
            if (modeValueSet != null && !modeValueSet.isEmpty()) {
                if (isColorSubMode) {
                    SubModeColor subModeColor = new SubModeColor();
                    subModeColor.rgbSet = parseColorModeRgbSet(modeValueSet);
                    Mode mode = new Mode();
                    mode.subMode = subModeColor;
                    statusV0.mode = mode;
                } else {
                    String bleBase64BytesStr = modeValueSet.get(0);
                    byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
                    if (LogInfra.openLog()) {
                        LogInfra.Log.i(TAG, "parseJson() modeValue originalBytes = " + BleUtil.bytesToHexString(originalBytes));
                    }
                    if (originalBytes != null && originalBytes.length == 20) {
                        byte[] valid17Bytes = BleUtil.parseValidBleBytes(originalBytes);
                        Mode mode = new Mode();
                        mode.parse(valid17Bytes);
                        statusV0.mode = mode;
                    }
                }
            }
        }
        return statusV0;
    }

    /**
     * 解析模式里面的球泡颜色
     *
     * @param modeValueSet
     * @return
     */
    public static int[] parseColorModeRgbSet(List<String> modeValueSet) {
        if (modeValueSet == null || modeValueSet.isEmpty()) return null;
        /*颜色模式需要单独解析;第一包不是颜色数据包*/
        int size = modeValueSet.size();
        List<BulbGroupColor> groupColors = new ArrayList<>();
        for (int i = 1; i < size; i++) {
            String bleBase64BytesStr = modeValueSet.get(i);
            byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "parseColorModeRgbSet() group = " + i + " ; colorMode originalBytes = " + BleUtil.bytesToHexString(originalBytes));
            }
            if (originalBytes != null && originalBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(originalBytes);
                BulbGroupColor bulbGroupColor = BulbGroupColor.parseBytes(valid17Bytes);
                groupColors.add(bulbGroupColor);
            }
        }
        int[] rgbSet = null;
        if (!groupColors.isEmpty()) {
            int sizeGroup = groupColors.size();
            rgbSet = new int[sizeGroup * 4];
            for (int i = 0; i < sizeGroup; i++) {
                int destPos = i * 4;
                BulbGroupColor bulbGroupColor = groupColors.get(i);
                int[] rgb = bulbGroupColor.rgb;
                System.arraycopy(rgb, 0, rgbSet, destPos, rgb.length);
            }
        }
        return rgbSet;
    }

    public static SubModeColor parseBulbColorRgbSet(List<String> bulbValueSet) {
        SubModeColor subModeColor = new SubModeColor();
        if (bulbValueSet == null || bulbValueSet.isEmpty()) return subModeColor;
        String firstValueStr = bulbValueSet.get(0);
        byte[] firstOriginalBytes = Encode.decryByBase64(firstValueStr);
        /*判断第一包是模式包还是数据包*/
        boolean firstBytesIsData = false;
        if (firstOriginalBytes != null && firstOriginalBytes.length == 20) {
            firstBytesIsData = firstOriginalBytes[1] == BleProtocol.SINGLE_BULB_COLOR;
            if (firstOriginalBytes[0] == BleProtocolConstants.SINGLE_WRITE && firstOriginalBytes[1] == BleProtocolConstants.SINGLE_MODE && firstOriginalBytes[2] == BleProtocol.sub_mode_color) {
                int r = BleUtil.getUnsignedByte(firstOriginalBytes[3]);
                int g = BleUtil.getUnsignedByte(firstOriginalBytes[4]);
                int b = BleUtil.getUnsignedByte(firstOriginalBytes[5]);
                subModeColor.rgb = ColorUtils.toColor(r, g, b);
            }
        }
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "parseBulbColorRgbSet() firstBytesIsData = " + firstBytesIsData + " ; firstOriginalBytes = " + BleUtil.bytesToHexString(firstOriginalBytes));
        }
        int size = bulbValueSet.size();
        int fromIndex = firstBytesIsData ? 0 : 1;
        List<BulbGroupColor> groupColors = new ArrayList<>();
        for (int i = fromIndex; i < size; i++) {
            String bleBase64BytesStr = bulbValueSet.get(i);
            byte[] originalBytes = Encode.decryByBase64(bleBase64BytesStr);
            if (LogInfra.openLog()) {
                LogInfra.Log.i(TAG, "parseBulbColorRgbSet() group = " + i + " ; colorMode originalBytes = " + BleUtil.bytesToHexString(originalBytes));
            }
            if (originalBytes != null && originalBytes.length == 20) {
                byte[] valid17Bytes = BleUtil.parseValidBleBytes(originalBytes);
                BulbGroupColor bulbGroupColor = BulbGroupColor.parseBytes(valid17Bytes);
                groupColors.add(bulbGroupColor);
            }
        }
        int[] rgbSet = null;
        if (!groupColors.isEmpty()) {
            int sizeGroup = groupColors.size();
            rgbSet = new int[sizeGroup * 4];
            for (int i = 0; i < sizeGroup; i++) {
                int destPos = i * 4;
                BulbGroupColor bulbGroupColor = groupColors.get(i);
                int[] rgb = bulbGroupColor.rgb;
                System.arraycopy(rgb, 0, rgbSet, destPos, rgb.length);
            }
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }

    @Keep
    static class State {
        public int onOff;
        public int brightness;
        public int stringNum;
        public int mode;
    }
}