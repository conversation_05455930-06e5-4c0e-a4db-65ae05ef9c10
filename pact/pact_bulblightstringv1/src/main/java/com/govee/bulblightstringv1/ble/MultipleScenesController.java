package com.govee.bulblightstringv1.ble;

import com.govee.base2light.ble.controller.AbsMultipleController;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2019-12-02
 * 场景多包协议$
 */
public class MultipleScenesController extends AbsMultipleController {
    private int scenesCode;
    private byte[] writeBytes;

    /**
     * 写操作
     *
     * @param scenesCode
     * @param writeBytes
     */
    public MultipleScenesController(int scenesCode, byte[] writeBytes) {
        super(true);
        this.scenesCode = scenesCode;
        this.writeBytes = writeBytes;
    }

    /**
     * 写操作
     *
     * @param sceneModel
     */
    public MultipleScenesController(MultiSceneModel sceneModel) {
        super(true);
        /*scenesCode+scenesValue+subScenes(暂无)+speed+colorLen+colorRgb*3*/
        byte[] colors = sceneModel.colors;
        int colorLen = 0;
        if (colors != null && colors.length > 0) {
            colorLen = colors.length;
        }
        writeBytes = new byte[5 + colorLen];
        writeBytes[0] = (byte) sceneModel.code;
        writeBytes[1] = (byte) sceneModel.effectType;
        writeBytes[2] = 0x00;
        writeBytes[3] = (byte) sceneModel.speed;
        writeBytes[4] = (byte) colorLen;
        if (colorLen > 0) {
            System.arraycopy(colors, 0, writeBytes, 5, colorLen);
        }
        scenesCode = sceneModel.code;
    }

    @Override
    public AbsMultipleController copy(String bleAddress) {
        MultipleScenesController scenesController = new MultipleScenesController(scenesCode, writeBytes);
        scenesController.setBleAddress(bleAddress);
        return scenesController;
    }

    @Override
    protected boolean writeResult(boolean writeSuc) {
        EventMultiScenes.sendWriteResult(writeSuc, getCommandType(), getProType(), scenesCode);
        return true;
    }

    @Override
    protected byte[] getWriteValues() {
        return writeBytes;
    }

    @Override
    protected void fail() {
        EventMultiScenes.sendFail(isWrite(), getCommandType(), getProType(), scenesCode);
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.MULTI_SCENE;
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        return true;
    }
}