package com.govee.bulblightstringv1.adjust;

import android.content.Intent;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2light.pact.AbsPactAdjustAc4Ble;
import com.govee.base2light.pact.BleInfo;
import com.govee.base2light.pact.IFrame;
import com.govee.base2light.pact.IFrameResult;
import com.govee.bulblightstringv1.ConsV1;
import com.govee.bulblightstringv1.adjust.v1.FrameV1;
import com.govee.bulblightstringv1.pact.Support;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020-02-12
 * 控制页-仅支持ble$
 */
public class AdjustAc extends AbsPactAdjustAc4Ble {

    @Override
    protected boolean supportDeviceLock() {
        return true;
    }

    @NonNull
    @Override
    protected IFrame makeFrame(IFrameResult iFrameResult, BleInfo bleInfo) {
        /*依据对应的bleInfo的goodsType进行框架选择*/
        return new FrameV1(iFrameResult, bleInfo);
    }

    @NonNull
    @Override
    protected BleInfo makeInfoFromIntent(Intent intent) {
        String sku = intent.getStringExtra(ConsV1.intent_ac_adjust_sku);
        String device = intent.getStringExtra(ConsV1.intent_ac_adjust_device);
        String spec = intent.getStringExtra(ConsV1.intent_ac_adjust_spec);
        String deviceName = intent.getStringExtra(ConsV1.intent_ac_adjust_deviceName);
        int goodsType = intent.getIntExtra(ConsV1.intent_ac_adjust_goodsType, GoodsType.GOODES_TYPE_NO_SUPPORT);
        String bleAddress = intent.getStringExtra(ConsV1.intent_ac_adjust_bleAddress);
        String bleName = intent.getStringExtra(ConsV1.intent_ac_adjust_bleName);
        String versionSoft = intent.getStringExtra(ConsV1.intent_ac_adjust_version_soft);
        String versionHard = intent.getStringExtra(ConsV1.intent_ac_adjust_version_hard);
        BleInfo bleInfo = new BleInfo(sku, goodsType, device, spec, deviceName, bleName, bleAddress);
        bleInfo.versionSoft = versionSoft;
        bleInfo.versionHard = versionHard;
        return bleInfo;
    }

    @Override
    protected int[] getDefHeaderRes() {
        return Support.getDefHeaderRes(info.goodsType);
    }

    @Override
    protected String getTag() {
        return "AdjustAc";
    }
}