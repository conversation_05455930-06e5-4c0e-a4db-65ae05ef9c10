package com.govee.bulblightstringv1.adjust.v1;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2home.pact.Protocol;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.pact.ble.AbsBlePact;
import com.govee.base2light.pact.ble.IPactResult4Ble;
import com.govee.bulblightstringv1.ble.Ble;
import com.govee.bulblightstringv1.pact.Support;

import androidx.annotation.NonNull;

/**
 * Create by xie<PERSON><PERSON> on 2019-12-16
 * v1版本的蓝牙协议$
 */
public class BlePactV1 extends AbsBlePact {
    private static final String TAG = "BlePactV1";

    public BlePactV1(IPactResult4Ble iPactResult) {
        super(iPactResult);
    }

    @Override
    protected String getTag() {
        return TAG;
    }

    @NonNull
    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }

    @Override
    protected boolean isSupportProtocol(Protocol protocol) {
        if (protocol == null) return false;
        for (Protocol v1Protocol : Support.supportProtocolsV1) {
            if (v1Protocol.isSameProtocol(protocol.pactType, protocol.pactCode)) return true;
        }
        return false;
    }

    @Override
    protected Protocol parseBleBroadcastProtocol(int goodsType, byte[] scanRecord) {
        return GoodsType.parseBleBroadcastPactInfo(goodsType, scanRecord);
    }
}