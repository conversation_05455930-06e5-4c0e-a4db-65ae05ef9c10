package com.govee.bulblightstringv1.adjust.ui;

import android.os.Bundle;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.ScenesHint;
import com.govee.base2light.light.v1.AbsScenesFragmentV3;
import com.govee.bulblightstringv1.ConsV1;
import com.govee.bulblightstringv1.ble.BleProtocol;
import com.govee.bulblightstringv1.ble.MultiSceneModel;
import com.govee.bulblightstringv1.ble.ScenesConfig;
import com.govee.bulblightstringv1.ble.SubModeScenes;
import com.govee.bulblightstringv1.pact.Scenes4BleRhythm;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.StreamUtil;

import org.greenrobot.eventbus.EventBus;

import java.io.ByteArrayOutputStream;
import java.util.List;

/**
 * Create by xieyingwu on 2020-02-13
 * 场景$
 */
public class ScenesFragment extends AbsScenesFragmentV3 {

    private SubModeScenes subModeScenes = new SubModeScenes();
    private ScenesConfig.ScenesExt curExt;
    private ScenesConfig.ScenesExt newExt;

    @Override
    public int marginTop() {
        return AppUtil.getScreenWidth() * 37 / 750;
    }

    @Override
    protected void init() {
        super.init();
        updateUi();
    }

    private void updateUi() {
        if (isViewInflateOk()) {
            /*刷新scenes*/
            int type = subModeScenes.type;
            updateSelectedScenes(type);
            /*刷新额外属性*/
            boolean supportExt = type != BleProtocol.value_sub_mode_scenes_illumination;
            supportExt(supportExt);
            if (supportExt) {
                if (curExt == null) {
                    curExt = ScenesConfig.getDefExt(type);
                }
                ScenesConfig.ScenesExt ext = curExt.copy();
                int[] colors = ext.color;
                /*刷新颜色列表*/
                updateColorList(colors);
                /*刷新速度条*/
                updateSpeed(ext.speed);
            }
        }
    }


    @Override
    protected void updateSubMode(ISubMode subMode) {
        subModeScenes = (SubModeScenes) subMode;
        /*不支持ext参数*/
        if (subModeScenes.type == BleProtocol.value_sub_mode_scenes_illumination) {
            curExt = null;
            newExt = null;
        } else {
            if (newExt == null || newExt.isInvalid()) {
                /*表明非设置扩展属性，获取本地存储的扩展属性*/
                ScenesConfig.ScenesExt ext = ScenesConfig.read().getExt(subModeScenes.type);
                if (ext == null || ext.isInvalid()) {
                    ext = ScenesConfig.getDefExt(subModeScenes.type);
                }
                curExt = ext.copy();
            } else {
                /*保存当前设置的扩展属性*/
                ScenesConfig.read().updateScenesExt(subModeScenes.type, newExt);
                curExt = newExt.copy();
                newExt = null;
            }
        }
        updateUi();
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_scenes;
    }

    @Override
    protected int getMaxColorNum() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            return arguments.getInt(ConsV1.arguments_key_scenes_color_num, 12);
        }
        return 12;
    }

    @Override
    protected void resetExtParams() {
        apply(ScenesConfig.getDefExt(subModeScenes.type), subModeScenes.type);
    }

    @Override
    protected void applyExtParams() {
        ScenesConfig.ScenesExt ext = new ScenesConfig.ScenesExt();
        ext.speed = getSpeed();
        ext.color = getColors();
        apply(ext, subModeScenes.type);
    }

    private void apply(ScenesConfig.ScenesExt ext, int effectType) {
        MultiSceneModel sceneModel = new MultiSceneModel();
        sceneModel.code = effectType + 1;
        sceneModel.effectType = effectType;
        if (effectType != BleProtocol.value_sub_mode_scenes_illumination) {
            if (ext == null || ext.isInvalid()) {
                ext = ScenesConfig.getDefExt(effectType);
            }
            this.newExt = ext.copy();
            sceneModel.speed = ext.speed;
            int[] colors = ext.color;
            if (colors != null && colors.length > 0) {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                for (int color : colors) {
                    int[] rgb = ColorUtils.getRgb(color);
                    byte[] bytes = new byte[]{(byte) rgb[0], (byte) rgb[1], (byte) rgb[2]};
                    baos.write(bytes, 0, 3);
                }
                sceneModel.colors = baos.toByteArray();
                StreamUtil.closeStream(baos);
            }
        } else {
            this.newExt = null;
        }
        EventBus.getDefault().post(sceneModel);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, ParamFixedValue.times);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.scene_mode, "scene_" + sceneModel.effectType);
    }

    @Override
    protected void toSendScenesMode(ScenesItem item) {
        int type = item.scenesValue;
        if (subModeScenes.type == type) return;
        apply(ScenesConfig.getLocalExt(type), type);
    }

    @Override
    protected List<ScenesItem> getSupportItems() {
        return Scenes4BleRhythm.rhythm.getSupportItems();
    }

    @Override
    protected ScenesHint getScenesHint(int scenesValue) {
        return null;
    }
}