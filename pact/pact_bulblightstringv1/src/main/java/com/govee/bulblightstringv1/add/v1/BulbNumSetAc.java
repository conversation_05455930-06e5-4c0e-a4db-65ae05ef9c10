package com.govee.bulblightstringv1.add.v1;

import android.app.Activity;
import android.content.Intent;
import android.os.Bundle;

import com.govee.base2home.config.Base2homeConfig;
import com.govee.base2home.main.tab.EventTabDefault;
import com.govee.base2light.ble.AbsBle;
import com.govee.ble.BleController;
import com.govee.bulblightstringv1.ConsV1;
import com.govee.bulblightstringv1.add.BulbNumSettingDialog;
import com.govee.bulblightstringv1.adjust.AdjustAc;
import com.govee.bulblightstringv1.ble.Ble;
import com.govee.bulblightstringv1.ble.BulbNumController;
import com.govee.bulblightstringv1.ble.EventBulbNum;
import com.govee.ui.ac.AbsBleBulbNumSetAc;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.util.JumpUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import androidx.annotation.NonNull;

/**
 * Create by xieyingwu on 2020-02-13
 * 球泡串设置Ac$
 */
public class BulbNumSetAc extends AbsBleBulbNumSetAc {
    private static final String TAG = "BulbNumSetAc";
    private static final int max_retry_set_bulb_num_times = 1;
    private AddInfo addInfo;
    private int failTimes;

    /**
     * 跳转到球泡串设置界面
     *
     * @param ac
     * @param stringMaxNum
     * @param oneStringNum
     * @param addInfo
     */
    public static void jump2BulbNumSettingAc(Activity ac, int stringMaxNum, int oneStringNum, @NonNull AddInfo addInfo) {
        Bundle bundle = makeAcBundle(stringMaxNum, oneStringNum);
        bundle.putParcelable(ConsV1.intent_ac_key_addInfo, addInfo);
        JumpUtil.jumpWithBundle(ac, BulbNumSetAc.class, true, bundle);
    }

    @Override
    protected void getExtParams(Intent intent) {
        addInfo = intent.getParcelableExtra(ConsV1.intent_ac_key_addInfo);
    }

    @Override
    protected String getSku() {
        return addInfo.sku;
    }

    @Override
    protected void showBulbNumChooseDialog(int curStringNum, int stringMaxNum, int oneStringNum) {
        BulbNumSettingDialog.createDialog(this, curStringNum, stringMaxNum, oneStringNum, this::chooseBulbNum).show();
    }

    private void chooseBulbNum(int stringNum) {
        this.curStringNum = stringNum;
        updateBulbStringNum(curStringNum, oneStringNum);
    }

    @Override
    protected void setBulbNum(int stringNum) {
        showLoading();
        BulbNumController bulbNumController = new BulbNumController(stringNum);
        getBle().startController(bulbNumController);
    }

    private AbsBle getBle() {
        return Ble.getInstance;
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    public void onEventBulbNum(EventBulbNum event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventBulbNum() result = " + result);
        }
        getBle().controllerEvent(event);
        if (!result) {
            /*若写操作失败，且失败尝试次数未达到上限，则尝试一次*/
            failTimes++;
            if (failTimes <= max_retry_set_bulb_num_times) {
                setBulbNum(curStringNum);
                return;
            }
        }
        failTimes = 0;
        hideLoading();
        if (result) {
            /*设置完成；关闭界面*/
            closeAc();
        }
    }

    @Override
    protected void closeAc() {
        if (BaseApplication.getBaseApplication().isInBackground()) return;/*若应用处于后台，则不进行界面跳转*/
        hideLoading();
        BulbNumSettingDialog.hideDialog();
        /*关闭蓝牙*/
        Ble.getInstance.stopHeart();
        BleController.getInstance().toBtClose();
        /*通知Tab更改为Default*/
        EventTabDefault.sendEventTabDefault();
        /*跳转到详情页*/
        Class<?> mainAcClass = Base2homeConfig.getConfig().getMainAcClass();
        Bundle bundle = new Bundle();
        bundle.putString(ConsV1.intent_ac_adjust_sku, getSku());
        bundle.putString(ConsV1.intent_ac_adjust_device, addInfo.device);
        bundle.putString(ConsV1.intent_ac_adjust_spec, "");
        bundle.putInt(ConsV1.intent_ac_adjust_goodsType, addInfo.goodsType);
        bundle.putString(ConsV1.intent_ac_adjust_deviceName, addInfo.deviceName);
        bundle.putString(ConsV1.intent_ac_adjust_bleAddress, addInfo.bleAddress);
        bundle.putString(ConsV1.intent_ac_adjust_bleName, addInfo.bleName);
        bundle.putString(ConsV1.intent_ac_adjust_version_hard, addInfo.versionHard);
        bundle.putString(ConsV1.intent_ac_adjust_version_soft, addInfo.versionSoft);
        BaseApplication.getBaseApplication().finishOtherAndStartNewAc(mainAcClass, AdjustAc.class, bundle);
    }

    @Override
    protected boolean ignoreBackPressed() {
        return true;
    }
}