package com.govee.bulblightstringv1.pact.ble;

import com.govee.base2home.theme.ThemeM;
import com.govee.base2light.light.AbsBleSku;

/**
 * Create by <PERSON><PERSON>yingwu on 2019-12-10
 * ble-sku的item操作$
 */
public abstract class AbsBleSkuItem extends AbsBleSku {
    @Override
    public int getDefIcon() {
        return ThemeM.getDefSkuRes(getSku());
    }

    @Override
    public String getSku() {
        return product == null ? "" : product.sku;
    }

    @Override
    public int getGoodsType() {
        return curGoodsType();
    }

    protected abstract int curGoodsType();
}