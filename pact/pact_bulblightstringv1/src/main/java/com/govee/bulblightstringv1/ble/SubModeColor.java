package com.govee.bulblightstringv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.controller.ISubMode;

import androidx.annotation.Keep;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-02
 * 颜色模式$
 */
@Keep
public class SubModeColor implements ISubMode {
    public int rgb;
    public boolean[] ctlLight = new boolean[8 * 10];
    public int[] rgbSet;
    public int bulbStringNum;

    @Override
    public void loadLocal() {
    }

    @Override
    public void saveLocal() {
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_color;
    }

    @Override
    public String getAnalyticModeName() {
        String subModeStr = UtilColor.colorName(rgb);
        return ParamFixedValue.beAnalyticModeName(ParamFixedValue.mode_color, subModeStr);
    }

    @Override
    public void parse(byte[] validBytes) {
        /*只读取到模式-颜色值需要单独读取*/
    }

    @Override
    public byte[] getWriteBytes() {
        int bulbLenBytes = ctlLight.length / 8 + (ctlLight.length % 8 == 0 ? 0 : 1);
        boolean[] bulbChoose = new boolean[8 * bulbLenBytes];
        System.arraycopy(ctlLight, 0, bulbChoose, 0, ctlLight.length);
        byte[] values = new byte[4 + bulbLenBytes];
        values[0] = subModeCommandType();
        int[] rgb = ColorUtils.getRgb(this.rgb);
        values[1] = (byte) rgb[0];
        values[2] = (byte) rgb[1];
        values[3] = (byte) rgb[2];
        boolean[] chooseSet = new boolean[8];
        int temp = 1;
        int index = 4;
        for (int i = 0; i < bulbLenBytes; i++) {
            values[index] = 0x00;
            System.arraycopy(bulbChoose, i * 8, chooseSet, 0, 8);
            for (boolean b : chooseSet) {
                if (b) {
                    values[index] = (byte) (values[index] | temp);
                }
                temp = temp << 1;
            }
            temp = 1;
            index = index + 1;
        }
        return values;
    }

    public static SubModeColor makeSubModeColor(int color) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgb = color;
        int length = subModeColor.ctlLight.length;
        for (int i = 0; i < length; i++) {
            subModeColor.ctlLight[i] = true;
        }
        return subModeColor;
    }

    public static SubModeColor makeSubModeColor4SetAllBulb(int bulbNum, int color) {
        SubModeColor subModeColor = new SubModeColor();
        int[] rgbSet = new int[bulbNum];
        for (int i = 0; i < bulbNum; i++) {
            rgbSet[i] = color;
        }
        subModeColor.rgbSet = rgbSet;
        return subModeColor;
    }
}