package com.govee.bulblightstringv1.pact;

import com.govee.ui.R;
import com.govee.base2light.light.IScenes4BleRhythm;
import com.govee.base2light.light.v1.AbsScenesFragmentV1;

import com.govee.bulblightstringv1.ble.BleProtocol;
import com.ihoment.base2app.util.ResUtil;

import java.util.ArrayList;
import java.util.List;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2021/10/22
 * 静态场景描述$
 */
public class Scenes4BleRhythm implements IScenes4BleRhythm {
    private Scenes4BleRhythm() {
    }

    private static class Builder {
        private static Scenes4BleRhythm instance = new Scenes4BleRhythm();
    }

    public static Scenes4BleRhythm rhythm = Builder.instance;

    private static int[] defRes = {
            R.mipmap.new_light_btn_scenes_bright,
            R.mipmap.new_light_btn_scenes_fade,
            R.mipmap.new_light_btn_scenes_raindrop,
            R.mipmap.new_light_btn_scenes_colorful,
            R.mipmap.new_light_btn_scenes_running,
            R.mipmap.new_light_btn_scenes_blinking,
            R.mipmap.new_light_btn_scenes_snow,
            R.mipmap.new_light_btn_scenes_sky
    };
    private static int[] selectedRes = {
            R.mipmap.new_light_btn_scenes_bright_press,
            R.mipmap.new_light_btn_scenes_fade_press,
            R.mipmap.new_light_btn_scenes_raindrop_press,
            R.mipmap.new_light_btn_scenes_colorful_press,
            R.mipmap.new_light_btn_scenes_running_press,
            R.mipmap.new_light_btn_scenes_blinking_press,
            R.mipmap.new_light_btn_scenes_snow_press,
            R.mipmap.new_light_btn_scenes_sky_press
    };

    private static int[] str = {
            R.string.b2light_scenes_illumination,
            R.string.scenes_fade,
            R.string.b2light_scenes_raindrop,
            R.string.b2light_scenes_colorful,
            R.string.b2light_diy_marquee,
            R.string.b2light_bulb_scenes_blinking,
            R.string.b2light_scenes_snow,
            R.string.b2light_scenes_sky
    };
    private static int[] scenesType = {
            BleProtocol.value_sub_mode_scenes_illumination,
            BleProtocol.value_sub_mode_scenes_fade,
            BleProtocol.value_sub_mode_scenes_raindrops,
            BleProtocol.value_sub_mode_scenes_colorful,
            BleProtocol.value_sub_mode_scenes_marquee,
            BleProtocol.value_sub_mode_scenes_blinking,
            BleProtocol.value_sub_mode_scenes_snow,
            BleProtocol.value_sub_mode_scenes_sky
    };

    @Override
    public int[] strSet() {
        return str;
    }

    @Override
    public int[] scenesEffectSet() {
        return scenesType;
    }

    @Override
    public int[] defResSet() {
        return defRes;
    }

    @Override
    public int[] selectedResSet() {
        return selectedRes;
    }

    public List<AbsScenesFragmentV1.ScenesItem> getSupportItems() {
        List<AbsScenesFragmentV1.ScenesItem> scenesItems = new ArrayList<>();
        int length = defRes.length;
        for (int i = 0; i < length; i++) {
            AbsScenesFragmentV1.ScenesItem scenesItem = new AbsScenesFragmentV1.ScenesItem();
            scenesItem.defRes = defRes[i];
            scenesItem.selectRes = selectedRes[i];
            scenesItem.valueStr = ResUtil.getString(str[i]);
            scenesItem.scenesValue = scenesType[i];
            scenesItem.strRes=str[i];
            scenesItems.add(scenesItem);
        }
        return scenesItems;
    }
}
