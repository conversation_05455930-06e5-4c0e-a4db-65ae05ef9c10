package com.govee.bulblightstringv1.adjust.ui;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.v1.AbsMusicNoIcFragmentV1;
import com.govee.bulblightstringv1.ble.BleProtocol;
import com.govee.bulblightstringv1.ble.Mode;
import com.govee.bulblightstringv1.ble.SubModeMusic;
import com.ihoment.base2app.util.AppUtil;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by xieying<PERSON> on 2019-07-23
 * 音乐模式ui布局
 */
public class MusicFragment extends AbsMusicNoIcFragmentV1 {
    private SubModeMusic subModeMusic = new SubModeMusic();

    @Override
    protected void init() {
        super.init();
        /*防止fragment视图还未加载就已传递了模式信息；因此在init完成时也需要刷新ui*/
        updateUi();
    }

    @Override
    protected void onSensitivityChange(int sensitivity) {
        SubModeMusic copy = subModeMusic.copy();
        copy.setSensitivity(sensitivity);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
    }

    @Override
    protected void autoColorChange() {
        SubModeMusic copy = subModeMusic.copy();
        copy.setAuto(!copy.isAuto());
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
    }

    @Override
    protected void onColorTempChange(int colorTemp) {
        SubModeMusic copy = subModeMusic.copy();
        copy.setRgb(colorTemp);
        copy.setAuto(false);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
    }

    @Override
    protected boolean supportCameraGetColor() {
        return false;
    }

    @Override
    protected boolean supportColorTemBar() {
        return false;
    }

    @Override
    protected void onColorChange(int type, int color) {
        SubModeMusic copy = subModeMusic.copy();
        copy.setRgb(color);
        copy.setAuto(false);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
    }

    @Override
    protected void chooseEffect(Effect effect) {
        int effectValue;
        if (Effect.soft.equals(effect)) {
            effectValue = BleProtocol.value_sub_mode_music_soft;
        } else {
            effectValue = BleProtocol.value_sub_mode_music_power;
        }
        if (subModeMusic.getEffect() == effectValue) return;
        SubModeMusic copy = subModeMusic.copy();
        copy.setEffect(effectValue);
        Mode mode = new Mode();
        mode.subMode = copy;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.music_mode, ParamFixedValue.times);
    }


    @Override
    public int marginTop() {
        return AppUtil.getScreenWidth() * 37 / 750;
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        this.subModeMusic = (SubModeMusic) subMode;
        updateUi();
    }

    private void updateUi() {
        if (isViewInflateOk()) {
            boolean auto = subModeMusic.isAuto();
            updateAutoSwitch(auto);
            updateSensitivity(subModeMusic.getSensitivity());
            updateEffect(subModeMusic.getEffect() == BleProtocol.value_sub_mode_music_power ? Effect.power : Effect.soft);
            if (auto) {
                showColorArea(false);
            } else {
                showColorArea(true);
                int rgb = subModeMusic.getRgb();
                setColor(rgb);
            }
        }
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_music;
    }
}