package com.govee.bulblightstringv1.adjust.ui;

import com.govee.ui.R;
import android.os.Bundle;

import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsScenesUiMode;
import com.govee.base2light.ui.mode.IArguments;

import com.govee.bulblightstringv1.ble.BleProtocol;
import com.govee.bulblightstringv1.ble.SubModeScenes;

/**
 * Create by xieyingwu on 2019-07-22
 * scenes ui mode
 */
public class ScenesUiMode extends AbsScenesUiMode {
    private IArguments arguments;

    public ScenesUiMode(String sku, IArguments arguments) {
        super(sku);
        this.arguments = arguments;
    }

    @Override
    public AbsModeFragment getUiFragment() {
        ScenesFragment fragment = new ScenesFragment();
        Bundle args = null;
        if (arguments != null) {
            args = this.arguments.arguments(getSubModeType());
        }
        fragment.addExtArguments(args, getSku());
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_scene, R.mipmap.new_control_light_btb_mode_scene_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_scenes;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.loadLocal();
        return subModeScenes;
    }
}
