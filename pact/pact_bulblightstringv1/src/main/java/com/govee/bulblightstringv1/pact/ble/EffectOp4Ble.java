package com.govee.bulblightstringv1.pact.ble;

import com.govee.bulblightstringv1.pact.Support;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020/8/12
 * $
 */
public class EffectOp4Ble extends AbsEffectOp4Ble {
    @Override
    protected int[] supportGoodsType() {
        return Support.effect4bleOpGoodsTypeArray;
    }

    private EffectOp4Ble() {
    }

    private static class Builder {
        private static final EffectOp4Ble instance = new EffectOp4Ble();
    }

    public static EffectOp4Ble getInstance = Builder.instance;
}