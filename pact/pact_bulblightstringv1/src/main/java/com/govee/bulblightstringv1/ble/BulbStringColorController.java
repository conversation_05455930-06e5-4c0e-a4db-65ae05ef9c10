package com.govee.bulblightstringv1.ble;

import com.govee.base2light.ble.controller.AbsOnlyReadSingleController;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2020-03-03
 * 球泡串颜色-controller$
 */
public class BulbStringColorController extends AbsOnlyReadSingleController {
    private int group;

    public BulbStringColorController(int group) {
        this.group = group;
    }

    @Override
    protected void fail() {
        EventBulbStringColor.sendFail(isWrite(), getCommandType(), getProType());
    }

    @Override
    public byte getCommandType() {
        return BleProtocol.SINGLE_BULB_COLOR;
    }

    @Override
    protected byte[] translateRead() {
        return new byte[]{(byte) group};
    }

    @Override
    public boolean parseValidBytes(byte[] validBytes) {
        BulbGroupColor bulbGroupColor = BulbGroupColor.parseBytes(validBytes);
        EventBulbStringColor.sendSuc(isWrite(), getCommandType(), getProType(), bulbGroupColor);
        return true;
    }
}