package com.govee.bulblightstringv1.adjust.v2;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.pact.iot.AbsIotOp;
import com.govee.bulblightstringv1.iot.Cmd;
import com.govee.bulblightstringv1.iot.CmdStatus;

import androidx.annotation.NonNull;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020/7/25
 * iot操作op$
 */
class IotOpV2 extends AbsIotOp {
    @NonNull
    @Override
    protected AbsCmd getCmd4DeviceInfo() {
        return new CmdStatus();
    }

    @NonNull
    @Override
    protected String getAutoStatusCmd() {
        return Cmd.status;
    }

    @Override
    protected String getReadCmdParserKey(String cmd) {
        return Cmd.getCmdReadParseKey(cmd);
    }
}