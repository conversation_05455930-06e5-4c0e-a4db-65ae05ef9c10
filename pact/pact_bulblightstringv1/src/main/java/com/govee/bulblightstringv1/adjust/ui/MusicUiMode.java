package com.govee.bulblightstringv1.adjust.ui;

import com.govee.ui.R;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.AbsModeFragment;
import com.govee.base2light.ui.mode.AbsMusicNoIcUiMode;

import com.govee.bulblightstringv1.ble.BleProtocol;
import com.govee.bulblightstringv1.ble.SubModeMusic;


/**
 * Create by xieyingwu on 2019-07-22
 * music ui mode
 */
public class MusicUiMode extends AbsMusicNoIcUiMode {
    public MusicUiMode(String sku) {
        super(sku);
    }

    @Override
    public AbsModeFragment getUiFragment() {
        MusicFragment fragment = new MusicFragment();
        fragment.makeArguments(getSku());
        return fragment;
    }

    @Override
    public int[] modeIcons() {
        return new int[]{R.mipmap.new_control_light_btb_mode_music, R.mipmap.new_control_light_btb_mode_music_press};
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_music;
    }

    @Override
    public ISubMode getSubMode() {
        SubModeMusic subModeMusic = new SubModeMusic();
        subModeMusic.loadLocal();
        return subModeMusic;
    }
}
