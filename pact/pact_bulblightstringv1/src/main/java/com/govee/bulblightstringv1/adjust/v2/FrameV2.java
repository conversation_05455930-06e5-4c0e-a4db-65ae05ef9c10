package com.govee.bulblightstringv1.adjust.v2;

import com.govee.base2light.pact.AbsFrameBleIot;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.IFrameResult;
import com.govee.base2light.pact.IUi;
import com.govee.base2light.pact.IUiResult4BleIot;
import com.govee.base2light.pact.ble.AbsBlePact;
import com.govee.base2light.pact.ble.IPactResult4Ble;
import com.govee.base2light.pact.iot.AbsIotPact;
import com.govee.base2light.pact.iot.IPactResult4Iot;
import com.govee.bulblightstringv1.adjust.IUi4ExtInfo;
import com.govee.bulblightstringv1.adjust.setting.SettingAcV2;
import com.govee.bulblightstringv1.pact.Support;

import java.util.ArrayList;
import java.util.List;

import androidx.annotation.NonNull;
import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2020/3/5
 * v2版的框架实现-ble+iot$
 */
public class FrameV2 extends AbsFrameBleIot {

    public FrameV2(IFrameResult frameResult, BleIotInfo info) {
        super(frameResult, info);
    }

    @Override
    protected List<IUi> getSupportUiList(IUiResult4BleIot iUiResult4BleIot, BleIotInfo bleIotInfo) {
        List<IUi> uiList = new ArrayList<>();
        uiList.add(new UiV1(iUiResult4BleIot, bleIotInfo));
        return uiList;
    }

    @Override
    protected AbsBlePact makeBlePact(IPactResult4Ble iPactResult4Ble) {
        return new BlePactV2(iPactResult4Ble);
    }

    @Override
    protected AbsIotPact makeIotPact(IPactResult4Iot iPactResult4Iot) {
        return new IotPactV2(iPactResult4Iot);
    }

    @Override
    public void toSettingAc(@NonNull AppCompatActivity ac) {
        if (isDestroy()) return;
        boolean bulbEnable = isUiSuc();
        int[] bulbStringSetting = Support.getBulbStringSetting(info.goodsType, info.pactType, info.pactCode);
        SettingAcV2.jump2SettingAcV2(ac,
                info.goodsType,
                info.sku,
                info.device,
                info.deviceName,
                22,
                bulbEnable,
                bulbStringSetting[0],
                bulbStringSetting[1],
                getBulbNum(),
                info.wifiMac,
                info.bleAddress,
                info.bleName,
                info.versionHard);
    }

    protected int getBulbNum() {
        if (curUi instanceof IUi4ExtInfo) {
            return ((IUi4ExtInfo) curUi).getStringNum();
        }
        return 1;
    }
}