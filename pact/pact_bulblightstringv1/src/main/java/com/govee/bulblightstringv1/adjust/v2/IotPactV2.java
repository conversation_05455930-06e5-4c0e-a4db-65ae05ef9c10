package com.govee.bulblightstringv1.adjust.v2;

import com.govee.base2home.pact.Protocol;
import com.govee.base2light.pact.iot.AbsIotPact;
import com.govee.base2light.pact.iot.IPactResult4Iot;
import com.govee.bulblightstringv1.iot.Cmd;
import com.govee.bulblightstringv1.pact.Support;

import java.util.List;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-11
 * v2版本的iot协议$
 */
class IotPactV2 extends AbsIotPact {

    public IotPactV2(IPactResult4Iot iPactResult) {
        super(iPactResult);
    }

    @Override
    protected String getPactCmd() {
        return Cmd.online;
    }

    @Override
    protected boolean isSupportPact(int pactType, int pactCode) {
        List<Protocol> supportV2Protocols = Support.supportProtocolsV2;
        for (Protocol v2Protocol : supportV2Protocols) {
            if (v2Protocol.isSameProtocol(pactType, pactCode)) return true;
        }
        return false;
    }
}