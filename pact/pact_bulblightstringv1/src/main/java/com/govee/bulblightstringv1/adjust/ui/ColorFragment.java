package com.govee.bulblightstringv1.adjust.ui;

import com.govee.ui.R;
import android.os.Bundle;

import com.govee.base2home.analytics.AnalyticsRecorder;
import com.govee.base2home.analytics.EventKey;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.analytics.ParamKey;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.controller.ISubMode;
import com.govee.base2light.light.v1.AbsColorFragmentV2;
import com.govee.bulblightstringv1.ConsV1;

import com.govee.bulblightstringv1.ble.BleProtocol;
import com.govee.bulblightstringv1.ble.Mode;
import com.govee.bulblightstringv1.ble.SubModeColor;
import com.ihoment.base2app.util.AppUtil;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2020-02-13
 * fragment-颜色$
 */
public class ColorFragment extends AbsColorFragmentV2 {
    SubModeColor subModeColor = new SubModeColor();

    @Override
    protected int getBulbNum() {
        Bundle arguments = getArguments();
        if (arguments != null) {
            return arguments.getInt(ConsV1.arguments_key_color_bulb_string_num, 15);
        }
        return 15;
    }

    @Override
    protected boolean supportRecommendColor() {
        return false;
    }

    @Override
    protected int getBulbSpanCount() {
        return 5;
    }

    @Override
    protected void init() {
        super.init();
        updateUi();
    }

    @Override
    protected void onColorTempChange(int colorTemp) {
        if (isSetColorEnable()) {
            setBulbColor(colorTemp);
        } else {
            toast(R.string.h612526_ctrlight_empty);
        }
    }

    @Override
    protected boolean supportCameraGetColor() {
        return true;
    }

    @Override
    protected boolean supportColorTemBar() {
        return true;
    }

    @Override
    protected void onColorChange(int type, int color) {
        if (isSetColorEnable()) {
            setBulbColor(color);
        } else {
            toast(R.string.h612526_ctrlight_empty);
        }
    }

    private void setBulbColor(int color) {
        boolean[] selectBulb = bulbString.getSelectBulb();
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgb = color;
        if (selectBulb != null && selectBulb.length > 0) {
            boolean[] ctlLight = subModeColor.ctlLight;
            int length = Math.min(selectBulb.length, ctlLight.length);
            System.arraycopy(selectBulb, 0, ctlLight, 0, length);
        }
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        EventBus.getDefault().post(mode);
        AnalyticsRecorder.getInstance().recordTimes(EventKey.use_count, ParamKey.color_mode, ParamFixedValue.times);
    }

    private void updateUi() {
        if (isViewInflateOk()) {
            bulbString.updateBulbNum(subModeColor.bulbStringNum);
            int rgb = subModeColor.rgb;
            if (rgb != ColorUtils.toColor(0, 0, 0) && rgb != 0) {
                /*当前处于设置选中球泡串颜色*/
                bulbString.updateSelectBulbColor(rgb);
                updateSelectBulbColor(rgb);
            }
            int[] rgbSet = subModeColor.rgbSet;
            if (rgbSet != null && rgbSet.length > 0) {
                /*当前处于设置所有球泡串颜色*/
                bulbString.updateAllBulbColor(rgbSet);
                updateAllBulbColor(rgbSet);
            }
            /*更新色块和色条颜色*/
            boolean colorTem = ColorUtils.isColorTem(subModeColor.rgb);
            if (colorTem) {
                setColorWithTemColor(ColorUtils.toWhite(), subModeColor.rgb);
            } else {
                setColorWithTemColor(subModeColor.rgb, 0);
            }
        }
    }

    @Override
    public int marginTop() {
        return AppUtil.getScreenWidth() * 20 / 375;
    }

    @Override
    protected void updateSubMode(ISubMode subMode) {
        subModeColor = (SubModeColor) subMode;
        updateUi();
    }

    @Override
    public byte getSubModeType() {
        return BleProtocol.sub_mode_color;
    }
}
