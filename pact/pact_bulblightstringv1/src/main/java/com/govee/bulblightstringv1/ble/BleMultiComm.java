package com.govee.bulblightstringv1.ble;

import com.govee.base2light.ble.comm.AbsMultipleBleComm;

import java.util.UUID;

/**
 * Create by linshicong on 2019-04-30
 * h7022多包协议解析
 */
public class BleMultiComm extends AbsMultipleBleComm {
    private static final UUID serviceUuid = UUID.fromString("00010203-0405-0607-0809-0a0b0c0d1910");
    private static final UUID characteristicUuid = UUID.fromString("00010203-0405-0607-0809-0a0b0c0d2b11");

    @Override
    public UUID getServiceUuid() {
        return serviceUuid;
    }

    @Override
    public UUID getCharacteristicUuid() {
        return characteristicUuid;
    }
}