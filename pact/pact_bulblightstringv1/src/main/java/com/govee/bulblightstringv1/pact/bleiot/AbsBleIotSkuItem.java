package com.govee.bulblightstringv1.pact.bleiot;

import com.govee.base2home.theme.ThemeM;
import com.govee.base2light.light.AbsBleWifiSku;

/**
 * Create by xieyingwu on 2020/3/6
 * ble+iot组合v1版的sku的item$
 */
public abstract class AbsBleIotSkuItem extends AbsBleWifiSku {
    @Override
    public int getDefIcon() {
        return ThemeM.getDefSkuRes(getSku());
    }

    @Override
    public String getSku() {
        return product == null ? "" : product.sku;
    }

    @Override
    public int getGoodsType() {
        return curGoodsType();
    }

    /**
     * 当前支持的goodType
     *
     * @return
     */
    protected abstract int curGoodsType();
}