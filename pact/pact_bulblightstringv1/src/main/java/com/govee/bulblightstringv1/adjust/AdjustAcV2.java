package com.govee.bulblightstringv1.adjust;

import android.content.Intent;

import com.govee.base2home.pact.GoodsType;
import com.govee.base2light.pact.AbsPactAdjustAc4BleIot;
import com.govee.base2light.pact.BleIotInfo;
import com.govee.base2light.pact.IFrame;
import com.govee.base2light.pact.IFrameResult;
import com.govee.bulblightstringv1.ConsV1;
import com.govee.bulblightstringv1.adjust.v2.FrameV2;
import com.govee.bulblightstringv1.pact.Support;

import androidx.annotation.NonNull;

/**
 * Create by xie<PERSON><PERSON> on 2020/3/5
 * 控制页-v2版本-支持ble+iot$
 */
public class AdjustAcV2 extends AbsPactAdjustAc4BleIot {

    @Override
    protected boolean supportDeviceLock() {
        return true;
    }

    @NonNull
    @Override
    protected IFrame makeFrame(IFrameResult iFrameResult, BleIotInfo bleIotInfo) {
        return new FrameV2(iFrameResult, bleIotInfo);
    }

    @NonNull
    @Override
    protected BleIotInfo makeInfoFromIntent(Intent intent) {
        String sku = intent.getStringExtra(ConsV1.intent_ac_adjust_sku);
        String device = intent.getStringExtra(ConsV1.intent_ac_adjust_device);
        String spec = intent.getStringExtra(ConsV1.intent_ac_adjust_spec);
        String deviceName = intent.getStringExtra(ConsV1.intent_ac_adjust_deviceName);
        int goodsType = intent.getIntExtra(ConsV1.intent_ac_adjust_goodsType, GoodsType.GOODES_TYPE_NO_SUPPORT);
        String bleAddress = intent.getStringExtra(ConsV1.intent_ac_adjust_bleAddress);
        String bleName = intent.getStringExtra(ConsV1.intent_ac_adjust_bleName);
        String wifiMac = intent.getStringExtra(ConsV1.intent_ac_adjust_wifiMac);
        String topic = intent.getStringExtra(ConsV1.intent_ac_adjust_topic);
        String versionHard = intent.getStringExtra(ConsV1.intent_ac_adjust_version_hard);
        String versionSoft = intent.getStringExtra(ConsV1.intent_ac_adjust_version_soft);
        BleIotInfo bleIotInfo = new BleIotInfo(sku, goodsType, device, spec, deviceName, bleName, bleAddress, wifiMac, topic, versionHard);
        bleIotInfo.versionSoft = versionSoft;
        return bleIotInfo;
    }

    @Override
    protected int[] getDefHeaderRes() {
        return Support.getDefHeaderRes(info.goodsType);
    }

    @Override
    protected String getTag() {
        return "AdjustAcV2";
    }
}