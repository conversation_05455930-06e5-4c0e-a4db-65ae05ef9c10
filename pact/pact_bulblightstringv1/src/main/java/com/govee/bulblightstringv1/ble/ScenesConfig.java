package com.govee.bulblightstringv1.ble;

import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.ihoment.base2app.infra.AbsConfig;
import com.ihoment.base2app.infra.StorageInfra;

import java.util.HashMap;

import androidx.annotation.Keep;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-12-02
 * 场景配置$
 */
public class ScenesConfig extends AbsConfig {
    private HashMap<Integer, ScenesExt> scenesMap = new HashMap<>();

    @Override
    protected void initDefaultAttrs() {
    }

    public static ScenesConfig read() {
        ScenesConfig config = StorageInfra.get(ScenesConfig.class);
        if (config == null) {
            config = new ScenesConfig();
            config.writeDef();
        }
        return config;
    }

    public void updateScenesExt(int scenesType, ScenesExt ext) {
        scenesMap.put(scenesType, ext);
        writeDef();
    }

    public ScenesExt getExt(int scenesType) {
        return scenesMap.get(scenesType);
    }

    @Keep
    public static class ScenesExt {
        public int speed;
        public int[] color;

        public ScenesExt copy() {
            ScenesExt ext = new ScenesExt();
            ext.color = color;
            ext.speed = speed;
            return ext;
        }

        public boolean isInvalid() {
            return color == null;
        }
    }

    public static ScenesConfig.ScenesExt getLocalExt(int type) {
        if (type == BleProtocol.value_sub_mode_scenes_illumination) return null;
        ScenesConfig.ScenesExt ext = ScenesConfig.read().getExt(type);
        if (ext == null || ext.isInvalid()) {
            ext = getDefExt(type);
        }
        return ext;
    }

    public static ScenesConfig.ScenesExt getDefExt(int type) {
        int[] defColors;
        if (type == BleProtocol.value_sub_mode_scenes_snow) {
            defColors = new int[]{ColorUtils.toColor(255, 255, 255)};
        } else {
            defColors = new int[]{ColorUtils.toColor(255, 0, 0), ColorUtils.toColor(0, 0, 255), ColorUtils.toColor(255, 255, 0), ColorUtils.toColor(0, 255, 0)};
        }
        int defSpeed = 50;
        ScenesConfig.ScenesExt ext = new ScenesConfig.ScenesExt();
        ext.speed = defSpeed;
        ext.color = defColors;
        return ext;
    }
}