package com.govee.bulblightstringv1.adjust.v1;

import com.govee.base2light.ble.v1.AbsMode3UIV1;
import com.govee.base2light.ui.mode.IArguments;
import com.govee.base2light.ui.mode.IUiMode;
import com.govee.bulblightstringv1.adjust.ui.ColorUiMode;
import com.govee.bulblightstringv1.adjust.ui.MusicUiMode;
import com.govee.bulblightstringv1.adjust.ui.ScenesUiMode;

import androidx.appcompat.app.AppCompatActivity;

/**
 * Create by xieyingwu on 2020-02-12
 * mode ui-v1版本$
 */
public class ModeUiV1 extends AbsMode3UIV1 {

    public ModeUiV1(AppCompatActivity ac, String sku, IArguments arguments) {
        super(ac, arguments, sku);
    }

    @Override
    protected IUiMode getModeRight() {
        return new ScenesUiMode(sku, arguments);
    }

    @Override
    protected IUiMode getModeCenter() {
        return new ColorUiMode(sku, arguments);
    }

    @Override
    protected IUiMode getModeLeft() {
        return new MusicUiMode(sku);
    }

    @Override
    protected String getTAG() {
        return "ModeUiV1";
    }
}