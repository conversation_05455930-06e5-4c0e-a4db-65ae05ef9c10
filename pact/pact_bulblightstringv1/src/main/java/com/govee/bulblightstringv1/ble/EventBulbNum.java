package com.govee.bulblightstringv1.ble;

import com.govee.base2light.ble.controller.AbsControllerEvent;

import org.greenrobot.eventbus.EventBus;

/**
 * Create by lins<PERSON>ong on 2019-04-28
 * 球泡灯数量的通知event
 */
public class EventBulbNum extends AbsControllerEvent {
    private int num;

    private EventBulbNum(boolean result, boolean write, byte commandType, byte proType) {
        super(result, write, commandType, proType, !write);
    }

    static void sendFail(boolean write, byte commandType, byte proType) {
        EventBus.getDefault().post(new EventBulbNum(false, write, commandType, proType));
    }

    static void sendSuc(boolean write, byte commandType, byte proType, int num) {
        EventBulbNum eventBulbNum = new EventBulbNum(true, write, commandType, proType);
        eventBulbNum.num = num;
        EventBus.getDefault().post(eventBulbNum);
    }

    static void sendWriteResult(boolean result, byte commandType, byte proType, int num) {
        EventBulbNum eventBulbNum = new EventBulbNum(result, true, commandType, proType);
        eventBulbNum.num = num;
        EventBus.getDefault().post(eventBulbNum);
    }

    public int getNum() {
        return num;
    }
}