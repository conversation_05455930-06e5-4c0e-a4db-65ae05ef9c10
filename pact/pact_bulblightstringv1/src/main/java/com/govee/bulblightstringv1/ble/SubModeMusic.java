package com.govee.bulblightstringv1.ble;

import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.util.UtilColor;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.controller.ISubMode;
import com.ihoment.base2app.infra.StorageInfra;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-04-19
 * 音乐模式
 */
public class SubModeMusic implements ISubMode {
    private static final byte auto_type_open = 0;
    private static final byte auto_type_close = 1;
    private static final int min_sensitivity = 0;
    private static final int max_sensitivity = 99;
    private int effect = BleProtocol.value_sub_mode_music_power;/*效果*/
    private int sensitivity = max_sensitivity;/*灵敏度*/
    private int rgb = 0xFFFF0000;/*默认颜色-红色*/
    private boolean auto;/*自动颜色跳动*/

    @Override
    public void loadLocal() {
        SubModeMusic subModeMusic = StorageInfra.get(SubModeMusic.class);
        if (subModeMusic == null) return;
        this.sensitivity = subModeMusic.sensitivity;
        this.rgb = subModeMusic.rgb;
        this.auto = subModeMusic.auto;
        this.effect = subModeMusic.effect;
    }

    @Override
    public void saveLocal() {
        StorageInfra.put(this);
    }

    @Override
    public byte subModeCommandType() {
        return BleProtocol.sub_mode_music;
    }

    @Override
    public String getAnalyticModeName() {
        return ParamFixedValue.mode_music;
    }

    @Override
    public void parse(byte[] validBytes) {
        this.effect = BleUtil.getUnsignedByte(validBytes[0]);
        int sensitivity = BleUtil.getUnsignedByte(validBytes[1]);
        this.sensitivity = Math.max(min_sensitivity, Math.min(sensitivity, max_sensitivity));
        this.auto = validBytes[2] == auto_type_open;
        /*非有颜色字段*/
        if (!this.auto) {
            int r = BleUtil.getUnsignedByte(validBytes[3]);
            int g = BleUtil.getUnsignedByte(validBytes[4]);
            int b = BleUtil.getUnsignedByte(validBytes[5]);
            rgb = ColorUtils.toColor(r, g, b);
        }
    }

    @Override
    public byte[] getWriteBytes() {
        byte[] bytes;
        if (!auto) {
            bytes = new byte[7];
            bytes[0] = subModeCommandType();
            bytes[1] = (byte) effect;
            bytes[2] = (byte) sensitivity;
            bytes[3] = autoType(auto);
            int[] rgb = ColorUtils.getRgb(this.rgb);
            bytes[4] = (byte) rgb[0];
            bytes[5] = (byte) rgb[1];
            bytes[6] = (byte) rgb[2];
        } else {
            bytes = new byte[4];
            bytes[0] = subModeCommandType();
            bytes[1] = (byte) effect;
            bytes[2] = (byte) sensitivity;
            bytes[3] = autoType(auto);
        }
        return bytes;
    }

    private byte autoType(boolean auto) {
        return auto ? auto_type_open : auto_type_close;
    }

    public int getRgb() {
        return rgb;
    }

    public int getSensitivity() {
        return sensitivity;
    }

    public void setSensitivity(int sensitivity) {
        this.sensitivity = Math.max(min_sensitivity, Math.min(sensitivity, max_sensitivity));
    }

    public void setAuto(boolean auto) {
        this.auto = auto;
    }

    public boolean isAuto() {
        return auto;
    }

    public void setRgb(int rgb) {
        this.rgb = rgb;
    }

    public void setEffect(int effect) {
        this.effect = effect;
    }

    public int getEffect() {
        return effect;
    }

    public SubModeMusic copy() {
        SubModeMusic subModeMusic = new SubModeMusic();
        subModeMusic.sensitivity = this.sensitivity;
        subModeMusic.auto = this.auto;
        subModeMusic.rgb = this.rgb;
        subModeMusic.effect = this.effect;
        return subModeMusic;
    }
}