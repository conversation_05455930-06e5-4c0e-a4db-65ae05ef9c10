package com.govee.bulblightstringv1.pact;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.ble.BleUtil;
import com.govee.base2light.ble.comm.MultipleBleBytes;
import com.govee.base2light.ble.controller.AbsSingleController;
import com.govee.base2light.ble.controller.BrightnessController;
import com.govee.base2light.ble.controller.HeartController;
import com.govee.base2light.ble.controller.SwitchController;
import com.govee.base2light.util.NumUtil;
import com.govee.bulblightstringv1.ble.Mode;
import com.govee.bulblightstringv1.ble.ModeController;
import com.govee.bulblightstringv1.ble.MultiSceneModel;
import com.govee.bulblightstringv1.ble.MultipleScenesController;
import com.govee.bulblightstringv1.ble.ScenesConfig;
import com.govee.bulblightstringv1.ble.SubModeColor;
import com.govee.bulblightstringv1.ble.SubModeScenes;
import com.govee.bulblightstringv1.iot.CmdBrightness;
import com.govee.bulblightstringv1.iot.CmdBulb;
import com.govee.bulblightstringv1.iot.CmdPt;
import com.govee.bulblightstringv1.iot.CmdTurn;
import com.ihoment.base2app.util.StreamUtil;

import java.io.ByteArrayOutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * Create by xieyingwu on 2021/10/22
 * 指令生成类$
 */
public final class Comm {
    private Comm() {
    }

    /**
     * 生成亮度指令-ble
     *
     * @param brightness
     * @return
     */
    public static AbsSingleController makeBrightnessController4BleComm(int brightness) {
        int newBrightness = NumUtil.calculateProgress(100, 1, brightness);
        return new BrightnessController(newBrightness);
    }

    /**
     * 生成亮度指令-iot
     *
     * @param brightness
     * @return
     */
    public static AbsCmd makeBrightnessCmd4IotComm(int brightness) {
        return new CmdBrightness(brightness);
    }

    /**
     * 生成颜色指令-ble
     *
     * @param color
     * @return
     */
    public static AbsSingleController makeColorController4BleComm(int color) {
        SubModeColor subModeColor = SubModeColor.makeSubModeColor(color);
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        return new ModeController(mode);
    }

    /**
     * 生成颜色指令-iot
     *
     * @param color
     * @return
     */
    public static AbsCmd makeColorCmd4IotComm(int color) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgb = color;
        Arrays.fill(subModeColor.ctlLight, true);
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        ModeController modeController = new ModeController(mode);
        return new CmdBulb(modeController.getValue());
    }

    /**
     * 生成色温指令-ble
     *
     * @param temColor
     * @return
     */
    public static AbsSingleController makeColorTemController4BleComm(int temColor) {
        SubModeColor subModeColor = SubModeColor.makeSubModeColor(temColor);
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        return new ModeController(mode);
    }

    /**
     * 生成色温指令-iot
     *
     * @param color
     * @return
     */
    public static AbsCmd makeColorTemCmd4IotComm(int color) {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgb = color;
        Arrays.fill(subModeColor.ctlLight, true);
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        ModeController modeController = new ModeController(mode);
        return new CmdBulb(modeController.getValue());
    }

    /**
     * 生成心跳指令-ble
     *
     * @return
     */
    public static AbsSingleController makeHeartController4BleComm() {
        return new HeartController();
    }

    /**
     * 生成开关指令-ble
     *
     * @param open
     * @return
     */
    public static AbsSingleController makeSwitchController4BleComm(boolean open) {
        return new SwitchController(open);
    }

    /**
     * 生成开关指令-iot
     *
     * @param open
     * @return
     */
    public static AbsCmd makeSwitchCmd4IotComm(boolean open) {
        return new CmdTurn(open);
    }

    /**
     * 生成场景指令-iot
     *
     * @param goodsType
     * @param code
     * @return
     */
    public static AbsCmd makeScene4IotComm(int goodsType, int code) {
        /*H7005项目已停止，设置场景暂时按照单包设置*/
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.type = code;
        Mode mode = new Mode();
        mode.subMode = subModeScenes;
        ModeController modeController = new ModeController(mode);
        ScenesConfig.ScenesExt ext = ScenesConfig.getLocalExt(subModeScenes.type);
        if (ext == null) {
            return new CmdPt(CmdPt.pt_op_mode, modeController.getValue());
        }
        /*设置场景模式+效果*/
        MultiSceneModel multiSceneModel = new MultiSceneModel();
        multiSceneModel.code = code + 1;
        multiSceneModel.effectType = code;
        multiSceneModel.speed = ext.speed;
        int[] colors = ext.color;
        if (colors != null && colors.length > 0) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            for (int color : colors) {
                int[] rgb = ColorUtils.getRgb(color);
                byte[] bytes = new byte[]{(byte) rgb[0], (byte) rgb[1], (byte) rgb[2]};
                baos.write(bytes, 0, 3);
            }
            multiSceneModel.colors = baos.toByteArray();
            StreamUtil.closeStream(baos);
        }
        List<byte[]> modeBytes = new ArrayList<>();
        /*先加入场景模式bytes*/
        modeBytes.add(modeController.getValue());
        /*再加入场景效果bytes*/
        MultipleScenesController multipleScenesController = new MultipleScenesController(multiSceneModel);
        List<byte[]> scenesEffectBytes = MultipleBleBytes.getMultipleWriteBytes(multipleScenesController);
        modeBytes.addAll(scenesEffectBytes);
        return new CmdPt(CmdPt.pt_op_mode, modeBytes);
    }

    /**
     * 生成场景蓝牙指令集合-ble
     *
     * @param goodsType
     * @param code
     * @return
     */
    public static List<String> makeScene4BleComm(int goodsType, int code) {
        SubModeScenes subModeScenes = new SubModeScenes();
        subModeScenes.type = code;
        List<byte[]> bytes = new ArrayList<>();
        ScenesConfig.ScenesExt ext = ScenesConfig.getLocalExt(subModeScenes.type);
        if (ext == null) {
            MultiSceneModel sceneModel = new MultiSceneModel();
            sceneModel.code = code + 1;
            sceneModel.effectType = code;
            MultipleScenesController multipleScenesController = new MultipleScenesController(sceneModel);
            List<byte[]> scenesEffectBytes = MultipleBleBytes.getMultipleWriteBytes(multipleScenesController);
            bytes.addAll(scenesEffectBytes);
        } else {
            /*设置场景模式+效果*/
            MultiSceneModel multiSceneModel = new MultiSceneModel();
            multiSceneModel.code = code + 1;
            multiSceneModel.effectType = code;
            multiSceneModel.speed = ext.speed;
            int[] colors = ext.color;
            if (colors != null && colors.length > 0) {
                ByteArrayOutputStream baos = new ByteArrayOutputStream();
                for (int color : colors) {
                    int[] rgb = ColorUtils.getRgb(color);
                    byte[] bytesCur = new byte[]{(byte) rgb[0], (byte) rgb[1], (byte) rgb[2]};
                    baos.write(bytesCur, 0, 3);
                }
                multiSceneModel.colors = baos.toByteArray();
                StreamUtil.closeStream(baos);
            }
            /*再加入场景效果bytes*/
            MultipleScenesController multipleScenesController = new MultipleScenesController(multiSceneModel);
            List<byte[]> scenesEffectBytes = MultipleBleBytes.getMultipleWriteBytes(multipleScenesController);
            bytes.addAll(scenesEffectBytes);
        }
        return BleUtil.makeBleComm2Str(bytes);
    }
}