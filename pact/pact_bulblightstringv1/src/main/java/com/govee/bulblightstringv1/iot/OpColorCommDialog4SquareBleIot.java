package com.govee.bulblightstringv1.iot;

import android.content.Context;

import com.govee.base2home.iot.AbsCmd;
import com.govee.base2light.ac.club.EffectData;
import com.govee.base2light.ble.AbsBle;
import com.govee.base2light.ble.controller.EventMode;
import com.govee.base2light.light.AbsOpCommDialog4BleIotV2;
import com.govee.bulblightstringv1.ble.Ble;
import com.govee.bulblightstringv1.ble.Mode;
import com.govee.bulblightstringv1.ble.ModeController;
import com.govee.bulblightstringv1.ble.SubModeColor;
import com.ihoment.base2app.infra.LogInfra;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.Arrays;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by linshicong on 1/28/21
 * op color操作应用$
 */
public class OpColorCommDialog4SquareBleIot extends AbsOpCommDialog4BleIotV2 {
    private final EffectData.ColorEffect colorEffect;
    private final CmdBulb cmdBulb;

    protected OpColorCommDialog4SquareBleIot(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect) {
        super(context, bleAddress, bleName, topic, sku, device, EFFECT_TYPE_COLOR, -1);
        this.colorEffect = colorEffect;
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgb = colorEffect.colorSet[0];
        Arrays.fill(subModeColor.ctlLight, true);
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        ModeController modeController = new ModeController(mode);
        cmdBulb = new CmdBulb(modeController.getValue());
    }

    public static void showDialog(Context context, String bleAddress, String bleName, String topic, String sku, String device, @NonNull EffectData.ColorEffect colorEffect) {
        new OpColorCommDialog4SquareBleIot(context, bleAddress, bleName, topic, sku, device, colorEffect).show();
    }

    @Override
    protected @Nullable
    AbsCmd getOpCmd() {
        return cmdBulb;
    }


    @Override
    protected void bleOping() {
        SubModeColor subModeColor = new SubModeColor();
        subModeColor.rgb = colorEffect.colorSet[0];
        Arrays.fill(subModeColor.ctlLight, true);
        Mode mode = new Mode();
        mode.subMode = subModeColor;
        ModeController modeController = new ModeController(mode);
        getBle().startController(modeController);
    }


    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEventMode(EventMode event) {
        boolean result = event.isResult();
        if (LogInfra.openLog()) {
            LogInfra.Log.i(TAG, "onEventMode() result = " + result);
        }
        updateBleResult(result);
        hide();
    }

    @Override
    protected AbsBle getBle() {
        return Ble.getInstance;
    }
}