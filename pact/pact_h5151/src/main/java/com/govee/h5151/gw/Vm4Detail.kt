package com.govee.h5151.gw

import android.text.TextUtils
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleEventObserver
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.MutableLiveData
import com.govee.base2home.Constant4L5
import com.govee.base2home.event.NameUpdateEvent
import com.govee.base2home.iot.Cmd
import com.govee.base2home.iot.Cmd4Status
import com.govee.base2home.iot.protype.v2.IotMsgV2
import com.govee.base2home.main.AbsDevice
import com.govee.base2home.reform4dbgw.view.Event4ChangeGwForSub
import com.govee.base2light.ble.AbsBle
import com.govee.base2light.ble.controller.IControllerNoEvent
import com.govee.base2light.kt.ble.Compose4DefRead
import com.govee.base2light.kt.ble.Compose4DefWrite
import com.govee.base2light.kt.ble.Compose4DefWrite4Multi
import com.govee.base2light.kt.comm.AbsViewMode4OpWithOtaCheck
import com.govee.ble.BleController
import com.govee.ble.event.EventBleBroadcastListenerTrigger
import com.govee.ble.event.EventBleConnect
import com.govee.ble.event.ScanEvent
import com.govee.h5151.ble.Ble
import com.govee.h5151.ble.controller.Compose4ReadGatewayInfo
import com.govee.h5151.ble.controller.Controller4DataReset
import com.govee.h5151.ble.controller.Controller4LightSwitch
import com.govee.h5151.ble.controller.Controller4SbDelayPushTime
import com.govee.h5151.ble.controller.Controller4SubDeviceTemHumRange
import com.govee.h5151.ble.controller.Controller4SubDeviceUuids
import com.govee.h5151.gw.net.netService
import com.govee.kt.ui.device.Event4GwSubBleStatusUpdate
import com.govee.kt.ui.device.GwH5151Op
import com.govee.kt.ui.device.base.GwIotOnline
import com.govee.mvvm.ext.request
import com.ihoment.base2app.infra.SafeLog
import kotlinx.coroutines.Dispatchers
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/**
 * Create by xieyingwu on 2023/6/26
 * ViewModel详情页
 */
class Vm4Detail : AbsViewMode4OpWithOtaCheck<DeviceInfo>(), LifecycleEventObserver {
    companion object {
        const val ui_type_fail = 0
        const val ui_type_loading = 1
        const val ui_type_suc = 2
        const val ui_type_suc_empty = 3

        const val WHAT_TRY_SCAN_SUB_DEVICES = 20000
        const val WHAT_TIMER_FRESH_ITEM = 20001
    }

    /*ui类型*/
    val uiType: MutableLiveData<Int?> = MutableLiveData()

    /*子设备列表*/
    val ld4SubDevices: MutableLiveData<MutableList<AbsDevice>?> = MutableLiveData()

    var gwLightSwitchOn: Boolean = false
        private set

    /*是否存在需要扫描的子设备*/
    private var hadBleScanSubDevice: Boolean = false

    /*是否存在需要显示Wi-Fi图标的子设备*/
    private var hadWifiStatusShowingSubDevice: Boolean = false

    /*是否可见*/
    private var vis: Boolean = false

    private var needPauseBleConnect = false

    fun init(deviceInfo: DeviceInfo) {
        this.deviceInfo = deviceInfo
        ble(true)
        registerEventBus(true)
    }

    /**
     * 查询子设备列表信息
     */
    fun querySubDevices(firstRequest: Boolean = false) {
        val sku = deviceInfo?.sku ?: ""
        val device = deviceInfo?.device ?: ""
        SafeLog.i(TAG) { "querySubDevices() sku = $sku ; firstRequest = $firstRequest" }
        if (sku.isEmpty() || device.isEmpty()) return
        if (firstRequest) {
            uiType.postValue(ui_type_loading)
        }
        request({
            netService.subDevices(sku, device)
        }, success = {
            val empty = it.isEmpty()
            SafeLog.i(TAG) { "querySubDevices() empty = $empty" }
            ld4SubDevices.postValue(it)
            val uiTypeValue = if (empty) ui_type_suc_empty else ui_type_suc
            uiType.postValue(uiTypeValue)
            val gwKey = deviceInfo?.key()
            val syncLinkModels = GwH5151Op.syncLinkModels(gwKey, it)
            SafeLog.i(TAG) { "querySubDevices() syncLinkModels = ${syncLinkModels.contentToString()}" }
            this.hadBleScanSubDevice = syncLinkModels[0]
            this.hadWifiStatusShowingSubDevice = syncLinkModels[1]
            tryScanSubDevices()
            tryStartTimer(empty)
        }, error = {
            if (firstRequest) {
                uiType.postValue(ui_type_fail)
            }
        }, sucDispatcher = Dispatchers.IO)
    }

    private fun tryStartTimer(empty: Boolean) {
        SafeLog.i(TAG) { "tryStartTimer() " }
        if (empty) return
        doHandler(WHAT_TIMER_FRESH_ITEM, 500L)
    }

    override val ble: AbsBle
        get() = Ble.getInstance

    override fun doConnect() {
        if (needPauseBleConnect) {
            return
        }
        super.doConnect()
    }

    override fun onBtConnect(event: EventBleConnect) {
        if (event.address != deviceInfo?.bleAddress) {
            return
        }
//        SafeLog.i("xiaobing") { "ViewMode4Adjust--onBtConnect-->bleAddress->${event.address},result->${event.connectSuc()}" }
        super.onBtConnect(event)
    }

    override fun connectBleSuc() {
        scopeLaunch(Dispatchers.IO) {
            SafeLog.i(TAG) { "connectBleSuc() " }
            afterBleConnectSuc4ReadInfo(Compose4ReadGatewayInfo(resultSuc = {
                changeBleStatus(connect_status_suc)
                changeLightSwitch(it.lightSwitch)
                compareUpdateBleVersion(it.versionSoft, it.versionHard)
                compareUpdateWifiVersion(it.wifiVersionSoft, it.wifiVersionHard, it.wifiMac)
                checkOta()
            }, resultFail = {
                changeBleStatus(connect_status_fail)
            }))
        }
    }

    override fun changeBleStatus(status: Int) {
        super.changeBleStatus(status)
        if (isBleConnectSuc()) {/*蓝牙连接成功-尝试启动子设备扫描*/
            EventBleBroadcastListenerTrigger.sendEventBleBroadcastListenerTrigger(true)
        } else if (isBleConnectFail()) {
            versionUpdateDialogShowing.postValue(false)/*尝试启动子设备扫描*/
            EventBleBroadcastListenerTrigger.sendEventBleBroadcastListenerTrigger(true)
        }
        tryScanSubDevices()
    }

    override fun doWhat(what: Int): Boolean {
        val result = super.doWhat(what)
        if (result) return true
        return when (what) {
            WHAT_TRY_SCAN_SUB_DEVICES -> {
                SafeLog.i(TAG) { "doWhat() WHAT_TRY_SCAN_SUB_DEVICES-->hadBleScanSubDevice = $hadBleScanSubDevice" }
                if (hadBleScanSubDevice && (isBleConnectSuc() || isBleConnectFail())) {
                    EventBleBroadcastListenerTrigger.sendEvent4EnforceStartScan()
                }
                true
            }

            WHAT_TIMER_FRESH_ITEM -> {
                timerItemFresh()
                true
            }

            else -> {
                false
            }
        }
    }

    private fun timerItemFresh() {
        SafeLog.i(TAG) { "timerItemFresh() vis = $vis" }
        ld4SubDevices.value?.run {
            if (this.isEmpty()) return/*发送iot指令-读取status指令*/
            tryReadCmd(Cmd4Status())/*通知UI刷新*/
            ld4SubDevices.postValue(this)/*进行下一次定时*/
            if (vis) {
                doHandler(WHAT_TIMER_FRESH_ITEM, 60 * 1000L)
            }/*尝试共用一份iot检测逻辑*/
            GwH5151Op.iotCheckOnline4Gw(deviceInfo?.key())
        }
    }

    private fun tryScanSubDevices() {
        doHandler(WHAT_TRY_SCAN_SUB_DEVICES, 500L)
    }

    private fun changeLightSwitch(switchOn: Boolean) {
        SafeLog.i(TAG) { "changeLightSwitch() switchOn = $switchOn" }
        this.gwLightSwitchOn = switchOn
        deviceInfo?.run {
            Event4ResultGwLightSwitch.sendSuc(sku, device, switchOn)
        }
    }

    override fun parseIotMsg4Online(msgV2: IotMsgV2, jsonStr: String?) {
        if (jsonStr.isNullOrEmpty()) {
            return
        }
        deviceInfo?.run {
            if (!TextUtils.isEmpty(msgV2.softVersion) && (TextUtils.isEmpty(this.versionSoft) || msgV2.softVersion > this.versionSoft)) {
                this.versionSoft = msgV2.softVersion
            }
            if (!TextUtils.isEmpty(msgV2.wifiSoftVersion) && (TextUtils.isEmpty(this.wifiSoftVersion) || msgV2.wifiSoftVersion > this.wifiSoftVersion)) {
                this.wifiSoftVersion = msgV2.wifiSoftVersion
            }
        }
        deviceInfo?.key()?.also { gwKey ->
            GwIotOnline.queryGwIotOnline(gwKey).lastIotOnlineTimeMills = System.currentTimeMillis()
            if (hadWifiStatusShowingSubDevice && msgV2.cmd == Cmd.status) {
                val wifiOnlineCheck = GwH5151Op.wifiOnlineCheck(gwKey, msgV2, jsonStr)
                SafeLog.i(TAG) { "parseIotMsg4Online() wifiOnlineCheck = $wifiOnlineCheck" }
                if (wifiOnlineCheck) {
                    ld4SubDevices.value?.run {
                        ld4SubDevices.postValue(this)
                    }
                }
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4NameUpdate(event: NameUpdateEvent) {
        deviceInfo?.let {
            if (event.isSameDevice(it.sku, it.device)) {
                deviceName.postValue(event.newDeviceName)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4BindSubDevice(event: Event4BindSubDevice) {
        deviceInfo?.run {
            if (this.key() == event.key()) {
                val devices = event.devices
                SafeLog.i(TAG) { "onEvent4BindSubDevice() devices.size = ${devices.size}" }
                val controllers = mutableListOf<IControllerNoEvent>().apply {
                    add(Controller4SubDeviceUuids.makeController(devices))
                    add(Controller4SubDeviceTemHumRange.makeController(devices))
                    //是否有防误报警（延时报警）
                    var hasDptSku = false
                    for (device in devices) {
                        if (Constant4L5.isRefrigeratorTh(device.goodsType, device.sku)) {
                            hasDptSku = true
                            break
                        }
                    }
                    if (hasDptSku) {
                        add(Controller4SbDelayPushTime.makeController(devices))
                    }
                }
                val controller4Multi =
                    Compose4DefWrite4Multi.makeWriteController4Multi(controllers) {
                        SafeLog.i(TAG) { "onEvent4BindSubDevice() bindSuc? = $it" }
                        Event4BindSubDeviceResult.sendEvent(this.sku, this.device, it)
                    }
                doCommController(controller4Multi)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4GwLightSwitchOp(event: Event4GwLightSwitchOp) {
        deviceInfo?.run {
            if (this.key() == event.key()) {
                val switchOn = event.switchOn
                SafeLog.i(TAG) { "onEvent4GwLightSwitchOp() switchOn = $switchOn" }
                val controller = Compose4DefWrite.makeWriteController(
                    Controller4LightSwitch.makeController4Write(switchOn)
                ) {
                    SafeLog.i(TAG) { "onEvent4GwLightSwitchOp() opResult = $it" }
                    if (it) {
                        changeLightSwitch(switchOn)
                    } else {
                        Event4ResultGwLightSwitch.sendFail(sku, device)
                    }
                }
                doCommController(controller)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4GwSubBleStatusUpdate(event: Event4GwSubBleStatusUpdate) {
        val oneDeviceUpdate = event.isOneDeviceUpdate()
        SafeLog.i(TAG) { "onEvent4GwSubBleStatusUpdate() oneDeviceUpdate = $oneDeviceUpdate" }
        ld4SubDevices.value?.run {
            if (oneDeviceUpdate) {
                val oneDeviceKey = event.oneDeviceKey
                if (!oneDeviceKey.isNullOrEmpty()) {
                    SafeLog.i(TAG) { "onEvent4GwSubBleStatusUpdate() oneDeviceKey = $oneDeviceKey" }
                    var hadChange = false
                    for (absDevice in this) {
                        if (absDevice.key == oneDeviceKey) {
                            hadChange = true
                            break
                        }
                    }
                    SafeLog.i(TAG) { "onEvent4GwSubBleStatusUpdate() hadChange = $hadChange" }
                    if (hadChange) {
                        ld4SubDevices.postValue(this)
                    }
                }
            } else {
                ld4SubDevices.postValue(this)
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun onEvent4UnbindGw(event: Event4UnbindGw) {
        deviceInfo?.run {
            if (key() == event.key()) {
                SafeLog.i(TAG) { "onEvent4UnbindGw() 解绑网关-通知网关清除数据" }
                doCommController(Compose4DefWrite.makeWriteController(Controller4DataReset.makeController4DataReset()))
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4SubDeviceChange(event: Event4SubDeviceChange) {
        SafeLog.i(TAG) { "onEvent4SubDeviceChange() " }
        deviceInfo?.run {
            if (this.key() == event.key()) {
                GwH5151Op.iotStatusReset(this.key())
            }
        }
    }

    override fun deviceConnectResult(suc: Boolean) {
        super.deviceConnectResult(suc)
        deviceInfo?.run {
            Event4GwConnectStatus.sendEvent(this.sku, this.device, suc)
        }
    }

    override fun changeIotStatus(status: Int) {
        val iotConnectSuc4Pre = isIotConnectSuc()
        super.changeIotStatus(status)/*若iot状态由未连接到连接成功-则需要主动读取一下网关的指示灯状态;因为网关的status指令中不包含指示灯信息*/
        if (!iotConnectSuc4Pre && isIotConnectSuc()) {
            SafeLog.i(TAG) { "changeIotStatus() 读取iot下的网关指示灯状态" }
            val controller =
                Compose4DefRead.makeController(Controller4LightSwitch.makeController4Read().apply {
                    this.readParserListenerLambda = {
                        changeLightSwitch(it[0] == 0x01.toByte())
                    }
                })
            val ptReal = makePtRealNeedParseJsonStr(controller)
            sendWriteCmd(ptReal)
        }
    }

    override fun onScanEvent(event: ScanEvent) {
        if (needPauseBleConnect) {
            return
        }
        super.onScanEvent(event)
    }

    /**
     * 类H5151网关功能-->添加新设备到帐号下
     */
    @Subscribe(threadMode = ThreadMode.BACKGROUND)
    fun onEvent4AddNewSubDevice(event: Event4AddSubToAccount) {
        needPauseBleConnect = event.isStart
        if (event.isStart) {
            //断开蓝牙
            if (BleController.getInstance().connectedBleAddress == deviceInfo?.bleAddress) {
                BleController.getInstance().disconnectBleAndNotify()
                ble.curConnectBleAddress = ""
            }
        } else {
            if (BleController.getInstance().connectedBleAddress != deviceInfo?.bleAddress) {
                doConnect()
            }
        }
    }

    /**
     * 子设备切换网关绑定
     *
     * @param event
     */
    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    fun event4GwHasUpdate(event: Event4ChangeGwForSub) {
        ld4SubDevices.value?.let {
            for (subDev in it) {
                if (event.isSameSubDev(subDev.sku, subDev.device)) {
                    querySubDevices()
                    return
                }
            }
        }
    }

    /**
     * gw网关cmd-status仅包含子设备信息
     */
    override fun cmdStatusHadAllInfo(): Boolean {
        return false
    }

    override fun onStateChanged(source: LifecycleOwner, event: Lifecycle.Event) {
        if (Lifecycle.Event.ON_STOP == event) {
            vis = false
            onStop()
        } else if (Lifecycle.Event.ON_RESUME == event) {
            vis = true
        }
    }

    private fun onStop() {
        SafeLog.i(TAG) { "onStop()" }/*停止定时检测*/
        handler.removeMessages(WHAT_TIMER_FRESH_ITEM)/*保存上一次子设备Wi-Fi状态*/
        val iotStopTimer4Status = GwH5151Op.iotStopTimer4Status(deviceInfo?.key())
        SafeLog.i(TAG) { "onStop() iotStopTimer4Status = $iotStopTimer4Status" }
    }

    override fun otaCheckType(): Int = ota_check_normal
}