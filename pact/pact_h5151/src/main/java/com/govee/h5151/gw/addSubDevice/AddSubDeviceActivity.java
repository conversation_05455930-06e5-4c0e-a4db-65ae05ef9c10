package com.govee.h5151.gw.addSubDevice;

import static com.govee.util.RecordUtilsKt.recordUseCount;

import android.annotation.SuppressLint;
import android.content.Context;
import android.content.Intent;
import android.os.Bundle;
import android.text.TextUtils;
import android.view.View;
import android.widget.TextView;

import androidx.annotation.Nullable;
import androidx.recyclerview.widget.GridLayoutManager;
import androidx.recyclerview.widget.LinearLayoutManager;
import androidx.recyclerview.widget.RecyclerView;

import com.govee.base2home.AbsNetActivity;
import com.govee.base2home.Constant;
import com.govee.base2home.Constant4L5;
import com.govee.base2home.analytics.ParamFixedValue;
import com.govee.base2home.config.OrderConfig;
import com.govee.base2home.custom.DragSortRecyclerView;
import com.govee.base2home.custom.LoadingDialog;
import com.govee.base2home.custom.NetFailFreshViewV1;
import com.govee.base2home.device.IDeviceNet;
import com.govee.base2home.device.net.GwSubDeviceListRequest;
import com.govee.base2home.device.net.GwSubDeviceListResponse;
import com.govee.base2home.h5.WebActivity;
import com.govee.base2home.main.AbsDevice;
import com.govee.base2home.main.DeviceExtMode;
import com.govee.base2home.main.DeviceListConfig;
import com.govee.base2home.main.choose.BaseBleDeviceChooseActivity;
import com.govee.base2home.push.EventDeviceListFresh;
import com.govee.base2home.util.ClickUtil;
import com.govee.base2newth.ThConsV1;
import com.govee.base2newth.deviceitem.Ext4Gw;
import com.govee.base2newth.net.GwBindSubDeviceRequest;
import com.govee.base2newth.net.GwBindSubDeviceResponse;
import com.govee.base2newth.net.GwUpdateBindDeviceRequest;
import com.govee.base2newth.net.GwUpdateBindDeviceResponse;
import com.govee.ble.BleController;
import com.govee.ble.event.EventBleBroadcastListenerTrigger;
import com.govee.h5151.ConsV1;
import com.govee.h5151.gw.Event4AddSubToAccount;
import com.govee.h5151.gw.Event4BindSubDevice;
import com.govee.h5151.gw.Event4BindSubDeviceResult;
import com.govee.h5151.gw.Event4SubDeviceChange;
import com.govee.h5151.gw.net.IGwNet;
import com.govee.kt.ui.device.GwH5151Op;
import com.govee.ui.R;
import com.govee.ui.dialog.ConfirmDialog;
import com.govee.ui.dialog.ConfirmDialogV5;
import com.govee.ui.dialog.HintDialog1;
import com.ihoment.base2app.Cache;
import com.ihoment.base2app.infra.SafeLog;
import com.ihoment.base2app.network.BaseRequest;
import com.ihoment.base2app.network.ErrorResponse;
import com.ihoment.base2app.network.Network;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.CaughtRunnable;
import com.ihoment.base2app.util.JsonUtil;
import com.ihoment.base2app.util.JumpUtil;
import com.ihoment.base2app.util.NetUtil;
import com.ihoment.base2app.util.ResUtil;
import com.ihoment.base2app.util.ToastUtil;

import org.greenrobot.eventbus.Subscribe;
import org.greenrobot.eventbus.ThreadMode;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArrayList;

import butterknife.BindView;
import butterknife.OnClick;

/**
 * Create by DengFei on 2020/11/16
 **/
@SuppressLint("NonConstantResourceId")
public class AddSubDeviceActivity extends AbsNetActivity {

    @BindView(com.govee.h5151.R2.id.rcDeviceScenes)
    RecyclerView rvDeviceScenes;
    @BindView(com.govee.h5151.R2.id.tvTypeTag)
    TextView tvTypeTag;
    @BindView(com.govee.h5151.R2.id.tvAddedTag)
    TextView tvAddedTag;
    @BindView(com.govee.h5151.R2.id.tvAddedNum)
    TextView tvAddedNum;
    @BindView(com.govee.h5151.R2.id.tvNoDevice)
    TextView tvNoDevice;
    @BindView(com.govee.h5151.R2.id.net_fail_fresh)
    NetFailFreshViewV1 netFailFreshViewV1;
    @BindView(com.govee.h5151.R2.id.rcAddedDevicesScenes)
    DragSortRecyclerView rvAddedDevicesScenes;

    private WaitAddSubDeviceAdapter mWaitAddAdapter;
    //从服务器拿到的所有类H5151网关(H5151/H5042/H5043/H5044)的子设备
    private volatile List<AbsDevice> mAllWidgetData = null;
    //从服务器拿到的所有类H5151网关(H5151/H5042/H5043/H5044)子设备的key的集合
    private final ConcurrentHashMap<String, AbsDevice> allWidgetDeviceMaps = new ConcurrentHashMap<>();
    //显示在待添加列表的
    private final List<AbsDevice> mWaitAddShowData = new ArrayList<>();

    private AddedSubDeviceAdapter mAddedAdapter;
    private final List<AbsDevice> mAddedWidgetList = new ArrayList<>();
    //从服务器拿到的已添加列表
    private List<GwBindSubDeviceResponse.Device> mAddedList = null;
    //修改数据后是否已经保存
    private boolean isSaving = true;
    private String gwSku;
    private String gwDevice;
    private String gwBleSv = "";
    private String gwBleHv = "";

    @Nullable
    private List<GwBindSubDeviceResponse.Device> wait4BindSubDevices;
    private Dialog4LvRemind lvRemindDialog;

    private final Map<String, Integer> mOrderMap = new HashMap<>();
    private final ArrayList<String> supportSkus = new ArrayList<>();

    /**
     * 跳转到添加类H5151网关子设备页
     */
    public static void jump2AddSubDeviceAc(Context context, String gwSku, String gwDevice, String gwBleSv, String gwBleHv) {
        Bundle bundle = new Bundle();
        bundle.putString(Constant.intent_ac_key_sku, gwSku);
        bundle.putString(Constant.intent_ac_key_device, gwDevice);
        bundle.putString(ConsV1.intent_ac_adjust_versionSoft, gwBleSv);
        bundle.putString(ConsV1.intent_ac_adjust_versionHard, gwBleHv);
        JumpUtil.jumpWithBundle(context, AddSubDeviceActivity.class, bundle);
    }

    @Override
    protected void onCreate(@Nullable Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        Intent intent = getIntent();
        gwSku = intent.getStringExtra(Constant.intent_ac_key_sku);
        gwDevice = intent.getStringExtra(Constant.intent_ac_key_device);
        gwBleSv = intent.getStringExtra(ConsV1.intent_ac_adjust_versionSoft);
        gwBleHv = intent.getStringExtra(ConsV1.intent_ac_adjust_versionHard);
        initView();
        getBindDeviceData();
        //兼容insetTop
        adaptationInsetTop(com.govee.h5151.R.id.top_flag, AppUtil.getScreenWidth() * 61 / 750);
        lvRemindDialog = Dialog4LvRemind.Companion.createDialog(this);
    }

    private boolean isBackFromAddNew = false;

    @Override
    protected void onResume() {
        super.onResume();
        if (isBackFromAddNew) {
            Event4AddSubToAccount.Companion.sendEvent(false);
            isBackFromAddNew = false;
            getBindDeviceData();
        }
    }

    private void initView() {
        tvAddedTag.setText(getString(R.string.widget_added_devices));
        tvTypeTag.setText(getString(R.string.widget_available_devices));
        netFailFreshViewV1.setListener(() -> {
            if (!NetUtil.isNetworkAvailable(this)) {
                toast(R.string.network_anomaly);
                return;
            }
            getBindDeviceData();
        });
        initAddedRecyclerView();
        initWaitAddRecyclerView();
    }

    private synchronized void toUpdateDevices() {
        updateUI(ui_type_suc);
        if (mAllWidgetData == null || mAddedList == null) {
            return;
        }
        if (mAddedAdapter.getItems().isEmpty()) {
            for (GwBindSubDeviceResponse.Device device : mAddedList) {
                AbsDevice absDevice = allWidgetDeviceMaps.get(device.getKey());
                if (absDevice != null) {
                    mAddedWidgetList.add(absDevice);
                }
            }
            notifyChangedAdded();
        }
        mWaitAddShowData.clear();
        mWaitAddShowData.addAll(mAllWidgetData);
        initOrderData();
        mWaitAddAdapter.setNewData(removeAddedItems(mWaitAddShowData));
        LoadingDialog.hideDialog();
    }

    /**
     * 缓存列表排序的数据,从已添加列表点删除，需要还原到待添加列表对应的位置。
     */
    private synchronized void initOrderData() {
        mOrderMap.clear();
        if (mAllWidgetData == null || mAllWidgetData.isEmpty()) {
            return;
        }
        for (int i = 0; i < mAllWidgetData.size(); i++) {
            AbsDevice itemModel = mAllWidgetData.get(i);
            itemModel.setPosition(i);
            mOrderMap.put(itemModel.getKey(), i);
        }
    }

    /**
     * 已添加的不显示在待添加列表，要删除对应的项
     */
    private List<AbsDevice> removeAddedItems(List<AbsDevice> modelList) {
        List<AbsDevice> rtList = new ArrayList<>();
        List<String> addedWidgetIds = new ArrayList<>();
        if (mAddedAdapter.getItems() != null) {
            for (AbsDevice itemModel : mAddedAdapter.getItems()) {
                addedWidgetIds.add(itemModel.getKey());
            }
        } else if (mAddedList.isEmpty()) {
            return modelList;
        }
        for (int i = 0; i < modelList.size(); i++) {
            AbsDevice itemModel = modelList.get(i);
            if (!addedWidgetIds.contains(itemModel.getKey())) {
                rtList.add(itemModel);
            } else {
                modelList.remove(i);
                i--;
            }
        }
        return rtList;
    }

    /**
     * 初始化待添加的列表
     */
    private void initWaitAddRecyclerView() {
        mWaitAddAdapter = new WaitAddSubDeviceAdapter(false, new WaitAddSubDeviceAdapter.OnEventListener() {
            @Override
            public boolean onClickAdd(AbsDevice itemModel) {
                //H5044支持添加32个温湿度计设备(H5112+类H5151网关支持的子设备)
                if (Constant4L5.H5044.equals(gwSku)) {
                    int h5112SubNum = 0;
                    for (AbsDevice absDevice : DeviceListConfig.read().getAllDeviceList()) {
                        if (Constant4L5.H5112.equals(absDevice.getSku())) {
                            Ext4Gw ext4Gw = JsonUtil.fromJson(absDevice.getDeviceExt().getDeviceSettings(), Ext4Gw.class);
                            if (ext4Gw != null && ext4Gw.getGatewayInfo() != null) {
                                if (!TextUtils.isEmpty(gwSku) && !TextUtils.isEmpty(gwDevice)) {
                                    if ((gwSku + "_" + gwDevice).equals(ext4Gw.getGatewayInfo().key())) {
                                        h5112SubNum++;
                                    }
                                }
                            }
                        }
                    }
                    if (mAddedWidgetList.size() >= ThConsV1.MAX_NUM_4_BIND_H5151_SUB_V1) {
                        HintDialog1.showHintDialog1(AddSubDeviceActivity.this, ResUtil.getString(R.string.h5044_text_4_max_th_add_remind_1), ResUtil.getString(R.string.hint_done_got_it));
                        //统计
                        recordUseCount(Constant4L5.H5044, ParamFixedValue.th_show_over_limit_dialog);
                        return false;
                    }
                    if ((mAddedWidgetList.size() + h5112SubNum) >= ThConsV1.MAX_NUM_4_H5044_BIND_TH_SUB) {
                        HintDialog1.showHintDialog1(AddSubDeviceActivity.this, ResUtil.getString(R.string.h5044_text_4_max_th_add_remind), ResUtil.getString(R.string.hint_done_got_it));
                        //统计
                        recordUseCount(Constant4L5.H5044, ParamFixedValue.th_show_over_limit_dialog);
                        return false;
                    }
                } else {
                    if (mAddedWidgetList.size() >= ConsV1.MAX_NUM_4_BIND_H5151_SUB) {
                        HintDialog1.showHintDialog1(AddSubDeviceActivity.this, ResUtil.getString(R.string.gateway_num_limit), ResUtil.getString(R.string.hint_done_got_it));
                        return false;
                    }
                }
                //子设备支持单值告警，但网关不支持单值告警需弹窗确认提示
                if (Constant4L5.supportSingleAlarm(itemModel.getGoodsType(), itemModel.getSku(), itemModel.getVersionSoft(), itemModel.getVersionHard())) {
                    if (!Constant4L5.supportSingleAlarm4Gw(0, gwSku, gwBleSv, gwBleHv)) {
                        String title = ResUtil.getString(R.string.b2light_hint_title);
                        String content = ResUtil.getStringFormat(R.string.text_4_single_alarm_4_gw_remind);
                        String sureText = ResUtil.getString(R.string.hint_done_got_it);
                        ConfirmDialogV5.showConfirmDialog(AddSubDeviceActivity.this, title, content, sureText, null);
                        return false;
                    }
                }
                addToWidget(itemModel);
                return true;
            }

            @Override
            public void onClickItem() {
                lvRemindDialog.show();
            }
        });
        LinearLayoutManager llm = new LinearLayoutManager(this);
        rvDeviceScenes.setLayoutManager(llm);
        rvDeviceScenes.setAdapter(mWaitAddAdapter);
        mWaitAddAdapter.setNewData(removeAddedItems(mWaitAddShowData));
    }

    /**
     * 添加到添加列表中去
     */
    private void addToWidget(AbsDevice itemModel) {
        isSaving = false;
        mWaitAddShowData.remove(itemModel);
        mAddedWidgetList.add(itemModel);
        //已添加列表滚到对应的位置
        if (mAddedWidgetList.size() > 1) {
            rvAddedDevicesScenes.smoothScrollToPosition(mAddedWidgetList.size() - 1);
        }
        notifyChangedAdded();
    }

    /**
     * 初始化已添加的列表
     */
    private void initAddedRecyclerView() {
        mAddedAdapter = new AddedSubDeviceAdapter(new AddedSubDeviceAdapter.OnEventListener() {
            @Override
            public void onDelete(AbsDevice AbsDevice) {
                isSaving = false;
                mAddedWidgetList.remove(AbsDevice);
                insertWaitAddList(AbsDevice);
                mWaitAddAdapter.setNewData(removeAddedItems(mWaitAddShowData));
            }

            @Override
            public void onAddedNumChanged(int addedNum) {
                String addedNumText = " （" + addedNum + "/" + ConsV1.MAX_NUM_4_BIND_H5151_SUB + "）";
                if (Constant4L5.H5044.equals(gwSku)) {
                    addedNumText = "";
                }
                tvAddedNum.setText(addedNumText);
                rvAddedDevicesScenes.setEnableDragNum(addedNum);
            }
        });
        GridLayoutManager gridLayoutManager = new GridLayoutManager(this, 4);
        rvAddedDevicesScenes.setLayoutManager(gridLayoutManager);
        rvAddedDevicesScenes.setOnDragListener(new DragSortRecyclerView.OnDragListener() {
            @Override
            public void onStart() {
            }

            @Override
            public void onStop() {
                isSaving = false;
            }
        });
        rvAddedDevicesScenes.setItemType(1);
        rvAddedDevicesScenes.setAdapter(mAddedAdapter);
        notifyChangedAdded();
    }

    private void notifyChangedAdded() {
        tvNoDevice.setVisibility(mAddedWidgetList.isEmpty() ? View.VISIBLE : View.GONE);
        mAddedAdapter.setNewData(mAddedWidgetList);
    }

    /**
     * 已添加列表点删除，插入到待添加列表
     */
    private void insertWaitAddList(AbsDevice itemModel) {
        //在整个队列中的位置
        Integer position = mOrderMap.get(itemModel.getKey());
        if (null == position) {
            return;
        }
        itemModel.setPosition(position);
        boolean haveAdded = false;
        for (AbsDevice item : mWaitAddShowData) {
            if (item.getPosition() > position) {
                int indexOf = mWaitAddShowData.indexOf(item);
                mWaitAddShowData.add(indexOf, itemModel);
                haveAdded = true;
                break;
            }
        }
        if (!haveAdded) {
            mWaitAddShowData.add(itemModel);
        }
    }

    private void getBindDeviceData() {
        LoadingDialog.createDialog(this, com.ihoment.base2app.R.style.DialogDim, 20 * 1000).show();
        GwBindSubDeviceRequest request = new GwBindSubDeviceRequest(transactions.createTransaction(), gwSku, gwDevice);
        Cache.get(IGwNet.class).getBindSubDevice(gwSku, gwDevice).enqueue(new Network.IHCallBack<>(request));
        //获取网关设备列表
        GwSubDeviceListRequest gwSubDeviceRequest = new GwSubDeviceListRequest(transactions.createTransaction(), gwSku, gwDevice);
        Cache.get(IDeviceNet.class).gwSubDeviceList(gwSubDeviceRequest).enqueue(new Network.IHCallBack<>(gwSubDeviceRequest));
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onGwSubDeviceResponse(GwBindSubDeviceResponse response) {
        if (!transactions.isMyTransaction(response)) return;
        LoadingDialog.hideDialog();
        List<GwBindSubDeviceResponse.Device> list = response.getData();
        mAddedList = Objects.requireNonNullElseGet(list, ArrayList::new);
        toUpdateDevices();
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onGwUpdateDeviceResponse(GwUpdateBindDeviceResponse response) {
        if (!transactions.isMyTransaction(response)) return;
        List<GwBindSubDeviceResponse.Device> subDevices = response.getRequest().subDevices;
        boolean hadSubDevice = subDevices != null && !subDevices.isEmpty();
        SafeLog.Companion.i(TAG, () -> "AddSubDeviceActivity--onGwUpdateDeviceResponse-->hadSubDevice = " + hadSubDevice);
        Event4SubDeviceChange.sendEvent(gwSku, gwDevice);
        //延时2000，等指令发送成功再销毁当前界面
        getWindow().getDecorView().postDelayed(new CaughtRunnable() {
            @Override
            protected void runSafe() {
                LoadingDialog.hideDialog(TAG);
                ToastUtil.getInstance().toast(getString(R.string.h5026_save_pic_gallery_suc));
                /*记录标识位-网关子设备变更过*/
                GwH5151Op.gwSubDeviceOp(gwSku + "_" + gwDevice, hadSubDevice);
                finish();
            }
        }, 2000);
    }

    @Override
    protected int getAcContentRootViewId() {
        return com.govee.h5151.R.id.ac_container;
    }

    @Override
    protected int getLayout() {
        return com.govee.h5151.R.layout.h5151_activity_add_sub_device;
    }

    @OnClick({com.govee.h5151.R2.id.tvHowAddSubDevice, com.govee.h5151.R2.id.ivHowAddSubDevice})
    public void onClickHowAddSubDevice() {
        if (ClickUtil.getInstance.clickQuick()) return;
        //登录，跳转到添加子设备介绍的H5页面
        String url = Constant.getH5RemoteControlUrl(gwSku);
        WebActivity.jump2WebAc(this, ResUtil.getString(R.string.how_to_add_sub_device), url);
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onGwSubDevicesListResponse(GwSubDeviceListResponse response) {
        if (!transactions.isMyTransaction(response) || response.data == null) {
            return;
        }
        allWidgetDeviceMaps.clear();
        mAllWidgetData = new CopyOnWriteArrayList<>();

        supportSkus.clear();
        supportSkus.addAll(response.data.supportSkus);
        List<AbsDevice> subDevices = OrderConfig.config().sortByAllDeviceSort(convertAbsDevices(response.data.subDevices));
        if (subDevices != null && !subDevices.isEmpty()) {
            //读取排序记录，重新排序
            for (AbsDevice device : subDevices) {
                if (isH5151SubDevices(device.getSku())) {
                    mAllWidgetData.add(device);
                    allWidgetDeviceMaps.put(device.getKey(), device);
                }
            }
        }
        toUpdateDevices();
    }

    private boolean isH5151SubDevices(String sku) {
        if (supportSkus == null || supportSkus.isEmpty()) {
            return false;
        }
        for (String supportSku : supportSkus) {
            if (TextUtils.isEmpty(supportSku)) {
                return false;
            }
            if (supportSku.equals(sku)) {
                return true;
            }
        }
        return false;
    }

    private ArrayList<AbsDevice> convertAbsDevices(List<GwSubDeviceListResponse.GwSubDevice> subDevices) {
        if (subDevices == null || subDevices.isEmpty()) {
            return null;
        }
        ArrayList<AbsDevice> devices = new ArrayList<>();
        for (GwSubDeviceListResponse.GwSubDevice subDevice : subDevices) {
            AbsDevice device = new AbsDevice();
            device.setSku(subDevice.sku);
            device.setDevice(subDevice.device);
            device.setDeviceName(subDevice.deviceName);
            device.setVersionHard(subDevice.versionHard);
            device.setVersionSoft(subDevice.versionSoft);
            DeviceExtMode mode = new DeviceExtMode();
            mode.setDeviceSettings(TextUtils.isEmpty(subDevice.settings) ? "{}" : subDevice.settings);
            mode.setLastDeviceData("{}");
            device.setDeviceExt(mode);
            devices.add(device);
        }
        return devices;
    }

    @Override
    protected void onErrorResponse(ErrorResponse response) {
        super.onErrorResponse(response);
        BaseRequest request = response.request;
        LoadingDialog.hideDialog();
        if (request instanceof GwUpdateBindDeviceRequest) {
            toast(R.string.h512x_bind_failure);
        } else {
            updateUI(ui_type_fail);
        }
    }

    private static final int ui_type_loading = 101;
    private static final int ui_type_fail = 102;
    private static final int ui_type_suc = 103;

    private void updateUI(int uiType) {
        switch (uiType) {
            case ui_type_loading:
                netFailFreshViewV1.beLoading();
                break;
            case ui_type_fail:
                netFailFreshViewV1.beFail();
                break;
            case ui_type_suc:
                netFailFreshViewV1.beHide();
                break;
            default:
        }
    }

    @OnClick(com.govee.h5151.R2.id.btn_ok)
    public void onBtnOk(View view) {
        if (isTooQuickClick(view.getId())) return;
        submitSubDeviceInfo();
    }

    private void submitSubDeviceInfo() {
        List<AbsDevice> newDeviceModelList = mAddedAdapter.getItems();
        List<GwBindSubDeviceResponse.Device> addedList = new ArrayList<>();
        try {
            for (int i = 0; i < newDeviceModelList.size(); i++) {
                AbsDevice itemModel = newDeviceModelList.get(i);
                if (itemModel == null || TextUtils.isEmpty(itemModel.getSku())) {
                    newDeviceModelList.remove(i);
                    i--;
                    continue;
                }
                addedList.add(new GwBindSubDeviceResponse.Device(itemModel.getSku(), itemModel.getDevice()));
            }
            //先给设备发送指令，设备接收成功后，再通知服务器
            LoadingDialog.createDialog(this, com.ihoment.base2app.R.style.DialogDim, 30 * 1000).setEventKey(TAG).show();
            wait4BindSubDevices = addedList;
            Event4BindSubDevice.sendEvent(gwSku, gwDevice, newDeviceModelList);
        } catch (Exception e) {
            wait4BindSubDevices = null;
            SafeLog.Companion.i(TAG, () -> "AddSubDeviceActivity--submitSubDeviceInfo-->set Widget exception" + e.getMessage());
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN_ORDERED)
    public void onEvent4BindSubDeviceResult(Event4BindSubDeviceResult event) {
        if (wait4BindSubDevices == null) return;
        boolean same = event.isSame(gwSku, gwDevice);
        boolean result = event.getResult();
        SafeLog.i(TAG, () -> "onEvent4BindSubDeviceResult() same = " + same + " ; result = " + result);
        if (!same) return;
        if (result) {
            updateBindDevice(wait4BindSubDevices);
        } else {
            SafeLog.Companion.i(TAG, () -> "AddSubDeviceActivity--onEvent4BindSubDeviceResult-->添加失败!");
            LoadingDialog.hideDialog(TAG);
            toast(R.string.b2light_save_fail);
        }
    }

    @OnClick({com.govee.h5151.R2.id.tvToAddNew})
    public void onToAddNew(View view) {
        if (isTooQuickClick(view.getId())) return;
        Event4AddSubToAccount.Companion.sendEvent(true);
        BleController.getInstance().disconnectBleAndNotify();
        toBleScanAc();
    }

    private void toBleScanAc() {
        Bundle bundle = new Bundle();
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG, true);
        if (supportSkus == null || supportSkus.isEmpty()) {
            bundle.putString(BaseBleDeviceChooseActivity.INTENT_KEY_SKU_NAME, Constant4L5.H5100);
        } else {
            bundle.putStringArray(BaseBleDeviceChooseActivity.INTENT_KEY_SKU_ARRAY, supportSkus.toArray(new String[]{}));
        }
        bundle.putBoolean(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG_HINT, true);
        bundle.putInt(BaseBleDeviceChooseActivity.INTENT_KEY_MORE_DEVICE_MSG_HINT_DES, R.string.h5074_more_device_5074_hint_des);
        bundle.putInt(BaseBleDeviceChooseActivity.INTENT_KEY_DEVICE_FLASHING_RSSI_VALUE, ConsV1.ble_ssid_compare_value);
        JumpUtil.jump(this, BaseBleDeviceChooseActivity.class, bundle);
        //进入蓝牙扫描界面；需要关闭主界面蓝牙广播
        EventBleBroadcastListenerTrigger.sendEventBleBroadcastListenerTrigger(false);
        //修改标识位
        isBackFromAddNew = true;
    }

    private void updateBindDevice(List<GwBindSubDeviceResponse.Device> addedList) {
        GwUpdateBindDeviceRequest request = new GwUpdateBindDeviceRequest(transactions.createTransaction(), addedList, gwSku, gwDevice);
        Cache.get(IGwNet.class).updateBindSubDevice(request).enqueue(new Network.IHCallBack<>(request));
    }

    private synchronized void finishCheck() {
        if (!isSaving) {
            ConfirmDialog.createConfirmDialog(this, ResUtil.getString(R.string.app_save_all_change), ResUtil.getString(R.string.app_not_save), ResUtil.getString(R.string.app_share_diy_save), new ConfirmDialog.ClickListener() {
                @Override
                public void doDone() {
                    submitSubDeviceInfo();
                }

                @Override
                public void doCancel() {
                    finish();
                }
            }).show();
        } else {
            finish();
        }
    }

    @OnClick(com.govee.h5151.R2.id.btn_back)
    public void onBtnBack(View view) {
        if (isTooQuickClick(view.getId())) return;
        finishCheck();
    }

    @SuppressLint("MissingSuperCall")
    @Override
    public void onBackPressed() {
        finishCheck();
    }

    @Override
    public void finish() {
        LoadingDialog.hideDialog();
        LoadingDialog.hideDialog(TAG);
        //刷新首页设备列表(添加新设备，添加/删除子设备都会引起设备列表对应子设备的相关数据变化)
        EventDeviceListFresh.sendEventDeviceListFresh(true);
        super.finish();
    }

    @Override
    protected boolean statusBarLightMode() {
        return false;
    }
}