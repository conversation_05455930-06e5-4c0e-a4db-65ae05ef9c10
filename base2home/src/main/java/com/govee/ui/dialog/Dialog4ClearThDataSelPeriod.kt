package com.govee.ui.dialog

import android.annotation.SuppressLint
import android.content.Context
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.View.OnClickListener
import android.widget.TextView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewbinding.ViewBinding
import com.govee.base2home.Constant4L5
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.common.BaseRvAdapter
import com.govee.base2home.common.BaseRvHolder
import com.govee.base2home.databinding.Layout4CdSelPeriodItemBinding
import com.govee.base2home.databinding.Layout4ClearDataSelPeriodDiaBinding
import com.govee.base2home.util.ClickUtil
import com.govee.base2home.util.recordUseCnt
import com.govee.base2kt.ext.setVisibility
import com.govee.ble.BleController
import com.govee.mvvm.network.NetworkUtil
import com.govee.ui.R
import com.ihoment.base2app.dialog.BaseEventDialog
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.ResUtil

/**
 * <AUTHOR>
 * @date created on 2023/09/18
 * @description H5127_选择距离的弹窗
 */
class Dialog4ClearThDataSelPeriod private constructor(
    context: Context,
    private var sku: String,
    private var isClearAll: Boolean = true
) : BaseEventDialog(context), OnClickListener {

    private lateinit var binding: Layout4ClearDataSelPeriodDiaBinding
    private var onSelectedListener: OnSelectedListener? = null
    private lateinit var periodList: ArrayList<String>
    private lateinit var selPeriodTypeAdapter: Adapter4SelectPeriod

    init {
        changeDialogOutside(true)
        immersionMode()
        updatePosition(0, 0, Gravity.BOTTOM)
    }

    companion object {
        fun createDialog(context: Context, sku: String): Dialog4ClearThDataSelPeriod {
            return Dialog4ClearThDataSelPeriod(context, sku, true)
        }

        fun createDialog(context: Context, sku: String, isClearAll: Boolean): Dialog4ClearThDataSelPeriod {
            return Dialog4ClearThDataSelPeriod(context, sku, isClearAll)
        }
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getLayoutView(): View {
        binding = Layout4ClearDataSelPeriodDiaBinding.inflate(LayoutInflater.from(context))
        binding.rvRemainPeriodContainer.layoutManager = LinearLayoutManager(context)
        initData()
        selPeriodTypeAdapter = Adapter4SelectPeriod(context, periodList)
        binding.rvRemainPeriodContainer.adapter = selPeriodTypeAdapter
        selPeriodTypeAdapter.setOnRvItemClickListener(object : BaseRvAdapter.OnRvItemClickListener {
            override fun onItemClick(v: View, position: Int) {
                onSelectedListener?.run {
                    hide()
                    onSure(position)
                    //统计
                    when (position) {
                        0 -> {
                            recordUseCnt(sku, ParamFixedValue.click_keep_data_7)
                        }

                        1 -> {
                            recordUseCnt(sku, ParamFixedValue.click_keep_data_1_month)
                        }

                        2 -> {
                            recordUseCnt(sku, ParamFixedValue.click_keep_data_3_month)
                        }

                        else -> {
                            recordUseCnt(sku, ParamFixedValue.click_keep_data_6_month)
                        }
                    }
                }
            }
        })
        binding.tvCancel.setOnClickListener(this)
        return binding.root
    }

    private fun initData() {
        periodList = ArrayList<String>().apply {
            add(ResUtil.getString(R.string.text_4_remain_7_days))
            add(ResUtil.getString(R.string.text_4_remain_1_month))
            add(ResUtil.getString(R.string.text_4_remain_3_month))
            add(ResUtil.getString(R.string.text_4_remain_half_year))
            if (isClearAll) {
                add(ResUtil.getString(R.string.text_4_clear_all))
            }
        }
    }

    /**
     * 选中设置的回调监听
     */
    interface OnSelectedListener {
        fun onSure(selPeriodType: Int)
    }

    fun show(goodsType: Int, sku: String, onSelectedListener: OnSelectedListener, isConnected: Boolean = true) {
        this.onSelectedListener = onSelectedListener
        selPeriodTypeAdapter.setDeviceInfo(goodsType, sku, isConnected)
        show()
    }

    /**
     * 忽略设备信息,不用 dialog 内部触发链接
     */
    fun show(onSelectedListener: OnSelectedListener, ignoreDeviceInfo: Boolean = true) {
        this.onSelectedListener = onSelectedListener
        selPeriodTypeAdapter.setIgnoreDeviceInfo(ignoreDeviceInfo)
        show()
    }

    override fun onClick(v: View?) {
        if (ClickUtil.getInstance.clickQuick()) {
            return
        }
        v?.let {
            when (it) {
                binding.tvCancel -> {
                    hide()
                }

                else -> {}
            }
        }
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth() * 343 / 375
    }
}

/**
 * 上面弹传中列表的适配器
 */
internal class Adapter4SelectPeriod(context: Context, data: ArrayList<String>) : BaseRvAdapter<String>(context, data) {

    private var goodsType = 0
    private var sku = ""
    private var isConnected = true
    private var ignoreDeviceInfo = false

    @SuppressLint("NotifyDataSetChanged")
    fun setDeviceInfo(goodsType: Int, sku: String, isConnected: Boolean) {
        this.goodsType = goodsType
        this.sku = sku
        this.isConnected = isConnected
        notifyDataSetChanged()
    }

    @SuppressLint("NotifyDataSetChanged")
    fun setIgnoreDeviceInfo(ignoreDeviceInfo: Boolean) {
        this.ignoreDeviceInfo = ignoreDeviceInfo
        notifyDataSetChanged()
    }

    override fun getItemViewBinding(viewType: Int): ViewBinding {
        return Layout4CdSelPeriodItemBinding.inflate(LayoutInflater.from(context))
    }

    override fun setItem(h: BaseRvHolder, entity: String, position: Int) {
        //删除全部需要蓝牙连接
        if (!ignoreDeviceInfo) {
            if (position == data.lastIndex) {
                val canClearAll = if (Constant4L5.canWifiClearAll(goodsType, sku)) {
                    NetworkUtil.isNetworkAvailable(context) && isConnected
                } else {
                    BleController.getInstance().isConnected && Constant4L5.needClearDeviceData(
                        goodsType,
                        sku
                    ) && isConnected
                }
                h.itemView.alpha = if (canClearAll) 1f else 0.4f
                h.itemView.isEnabled = canClearAll
            } else {
                h.itemView.alpha = 1f
                h.itemView.isEnabled = true
            }
        } else {
            h.itemView.alpha = 1f
            h.itemView.isEnabled = true
        }
        //其他设置
        h.itemView.findViewById<TextView>(com.govee.base2home.R.id.tv_remain_period_text)?.run {
            text = entity
        }
        h.itemView.findViewById<View>(com.govee.base2home.R.id.v_bottom_divider_line)?.run {
            setVisibility(position != data.lastIndex)
        }
    }
}