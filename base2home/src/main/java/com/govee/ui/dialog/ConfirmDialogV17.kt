package com.govee.ui.dialog

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import androidx.annotation.DrawableRes
import androidx.constraintlayout.widget.ConstraintLayout
import com.bumptech.glide.Glide
import com.govee.app.imageloader.config.GImageParams
import com.govee.base2home.databinding.DialogConfirmDialogV17Binding
import com.govee.base2home.util.ClickUtil
import com.govee.base2kt.ext.dp4Int
import com.govee.ui.R
import com.ihoment.base2app.dialog.BaseDialog
import com.ihoment.base2app.glide.ImageResConfigManager
import com.ihoment.base2app.glide.ImageResConfigManager.ImgSuffix
import com.ihoment.base2app.util.AppUtil
import com.ihoment.base2app.util.ResUtil

/**
 * Create by DengFei on 2022/11/4
 * 文字在图片上面、按钮
 */
class ConfirmDialogV17 private constructor(context: Context?) : BaseDialog(context) {
    private var mBinding: DialogConfirmDialogV17Binding? = null

    private var doneListener: DoneListener? = null
    private var confirmListener: ConfirmListener? = null

    init {
        immersionMode()
        ignoreBackPressed()
        changeDialogOutside(false)
        initListener()
    }

    override fun getLayoutView(): View? {
        mBinding = DialogConfirmDialogV17Binding.inflate(LayoutInflater.from(context))
        return mBinding?.root
    }

    private fun initListener() {
        mBinding?.ivClose?.setOnClickListener {
            if (ClickUtil.getInstance.clickQuick()) return@setOnClickListener
            hide()
        }
        mBinding?.tvConfirm?.setOnClickListener {
            if (ClickUtil.getInstance.clickQuick()) return@setOnClickListener
            doneListener?.topBtnClick()
            confirmListener?.onBtnKnowClick()
            hide()
        }
        mBinding?.tvCancel?.setOnClickListener {
            if (ClickUtil.getInstance.clickQuick()) return@setOnClickListener
            doneListener?.bottomBtnClick()
            hide()
        }
    }

    private fun loadImg4Config(imgUrl: String, suffix: ImgSuffix) {
        ImageResConfigManager.loadImage(
            mBinding?.ivImg,
            name = imgUrl,
            suffix = suffix,
            gImageParams = GImageParams.build().placeholder(R.color.ui_bg_color_style_1)
        )
    }

    private fun loadImg(@DrawableRes imgSrcId: Int) {
        mBinding?.ivImg?.run {
            val localDrawable = ResUtil.getDrawable(imgSrcId)
            Glide.with(context)
                .load(localDrawable)
                .into(this)
        }
    }

    fun setBottomTopMargin(topMargin: Int) {
        mBinding?.tvConfirm?.let {
            val layoutParams = it.layoutParams as ConstraintLayout.LayoutParams
            layoutParams.topMargin = topMargin
            it.layoutParams = layoutParams
        }
    }

    override fun hide() {
        doneListener = null
        super.hide()
    }

    override fun getLayout(): Int {
        return -1
    }

    override fun getWidth(): Int {
        return AppUtil.getScreenWidth() * 335 / 375
    }

    interface DoneListener {
        fun topBtnClick()
        fun bottomBtnClick()
    }

    interface ConfirmListener {
        fun onBtnKnowClick()
    }

    companion object {
        fun showDialog(
            context: Context,
            contentStr: String?,
            @DrawableRes imgSrcId: Int,
            doneListener: ConfirmListener?
        ) {
            val dialog = ConfirmDialogV17(context).apply {
                mBinding?.tvContent?.text = contentStr
                loadImg(imgSrcId)
                confirmListener = doneListener
            }
            dialog.show()
        }

        fun showDialog(
            context: Context,
            contentStr: String?,
            isCenter: Boolean,
            @DrawableRes imgSrcId: Int,
            doneListener: ConfirmListener?
        ) {
            val dialog = ConfirmDialogV17(context).apply {
                mBinding?.tvContent?.text = contentStr
                mBinding?.tvContent?.textAlignment =
                    if (isCenter) View.TEXT_ALIGNMENT_CENTER else View.TEXT_ALIGNMENT_TEXT_START
                loadImg(imgSrcId)
                confirmListener = doneListener
            }
            dialog.show()
        }

        fun showDialog(
            context: Context,
            contentStr: String?,
            imageSize: Pair<Int, Int>,
            configImageName: String,
            suffix: ImgSuffix = ImgSuffix.PNG,
            doneListener: ConfirmListener?
        ) {
            val dialog = ConfirmDialogV17(context).apply {
                mBinding?.tvContent?.text = contentStr
                mBinding?.tvContent?.let { updateDesGravity(it) }
                val layoutParams = mBinding?.ivImg?.layoutParams as? ConstraintLayout.LayoutParams
                layoutParams?.width = imageSize.first
                layoutParams?.height = imageSize.second
                mBinding?.ivImg?.layoutParams = layoutParams
                setBottomTopMargin(30.dp4Int)
                loadImg4Config(configImageName, suffix)
                confirmListener = doneListener
            }
            dialog.show()
        }
    }
}
