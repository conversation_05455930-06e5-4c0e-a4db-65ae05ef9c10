package com.govee.kt.ui.device

import androidx.annotation.Keep
import androidx.annotation.WorkerThread
import com.govee.base2home.main.AbsDevice
import com.govee.base2home.pact.GoodsType
import com.govee.base2home.theme.ThemeM
import com.govee.device.ExtDeviceInfo
import com.govee.device.ExtDeviceInfoManager
import com.govee.kt.ui.device.base.BleExt
import com.govee.kt.ui.device.base.IModel
import com.govee.kt.ui.device.util.DeviceListEditManager
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JsonUtil
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.CopyOnWriteArrayList

/**
 * Create By xieyingwu on 2022/11/3
 * <p>设备操作类</p>
 */
object DevicesOp {
    const val TAG = "DevicesOp"

    /*设备ui-Holder集合*/
    val deviceHolders by lazy {
        mutableListOf<IDeviceHolder>().apply {
            add(DefHolder())
            add(LightHolder())
            add(SecurityHolderV1())
            add(WarningDeviceHolder())
            add(BabyMonitorHolder())
            add(BabyMonitorHolderV1())
        }
    }
    private val deviceHolderClassHashSet by lazy {
        hashSetOf<String>().apply {
            add(DefHolder::class.java.name)
            add(LightHolder::class.java.name)
            add(SecurityHolderV1::class.java.name)
            add(WarningDeviceHolder::class.java.name)
            add(BabyMonitorHolder::class.java.name)
            add(BabyMonitorHolderV1::class.java.name)
        }
    }
    private val lastDeviceTransform = DeviceTransform().apply {
        itemChangeListener = {
            gwSubItemMap.clear()
            gwSubItemMap4BleBroadcast.clear()
            tempNoSupportDevicesBleAddressMaps.clear()
            for (item in it) {
                /*记录网关子设备*/
                if (item is IGwSubItem) {
                    val key4Gw = item.key4Gw()
                    if (key4Gw.isEmpty()) continue
                    SafeLog.i(TAG) { "deviceTransform() 网关子设备" }
                    val list = (gwSubItemMap[key4Gw] ?: mutableListOf()).apply {
                        add(item)
                    }
                    gwSubItemMap[key4Gw] = list
                    val key4GwBleBroadcast = item.key4GwBleBroadcast()
                    if (key4GwBleBroadcast.isNotEmpty()) {
                        gwSubItemMap4BleBroadcast[key4GwBleBroadcast] = key4Gw
                    }
                    /*是H5151网关的子设备*/
                    if (item is IGwSubItem4H5151) {
                        GwH5151Op.itemListSyncWifiStatusModel(key4Gw, item.device)
                    }
                } else if (item is DefNoSupportDeviceItem) {
                    item.device.deviceExt?.deviceSettings?.let { json ->
                        JsonUtil.fromJson(json, BleExt::class.java)?.address?.also { bleAddress ->
                            if (bleAddress.isNotEmpty()) {
                                SafeLog.i(TAG) { "deviceTransform() 不支持item的蓝牙信息 key = ${item.device.key} ; bleAddress = $bleAddress" }
                                tempNoSupportDevicesBleAddressMaps[bleAddress] = item.device.sku
                            }
                        }
                    }
                }
            }
            SafeLog.w(TAG) { "itemChangeListener() gw.size = ${gwSubItemMap.size}" }
        }
    }
    private val deviceModeMap = hashMapOf<String, IModel>()

    /*网关关联的子设备集合*/
    val gwSubItemMap = ConcurrentHashMap<String, MutableList<IDeviceItem>>()/*key=sku_device*/
    private val gwSubItemMap4BleBroadcast = ConcurrentHashMap<String, String>()/*key=sku_address,value=sku_device*/

    /*记录不支持设备的临时的蓝牙信息-用于针对pfd模块未下载时，用户能够重新扫描到已添加的设备*/
    private val tempNoSupportDevicesBleAddressMaps = ConcurrentHashMap<String, String>()/*key=address;value=sku*/

    fun gwSubItemMapKey(bleBroadcastKey: String): String? {
        return gwSubItemMap4BleBroadcast[bleBroadcastKey]
    }

    /**
     * 是否是不支持设备列表中的蓝牙设备
     */
    fun isDeviceList4NoSupportItem(sku: String?, bleAddress: String?): Boolean {
        if (sku.isNullOrEmpty() || bleAddress.isNullOrEmpty()) return false
        return tempNoSupportDevicesBleAddressMaps[bleAddress] == sku
    }

    init {
        /*默认加入不支持设备的卡片支持*/
        register(mutableListOf<ISubLib>().apply {
            this.add(DefNoSupportLib())
        })
    }

    /**
     * 注册ui刷新的Holder
     */
    fun registerDeviceHolder(deviceHolderClass: Class<*>, generate: () -> IDeviceHolder) {
        val className = deviceHolderClass.name
        if (deviceHolderClassHashSet.contains(className)) return
        deviceHolderClassHashSet.add(className)
        SafeLog.w(TAG) { "registerDeviceHolder() $className" }
        deviceHolders.add(generate.invoke())
    }

    /**
     * 查询对应的[IDeviceHolder]
     * @param itemType Adapter的ui样式值
     */
    fun findDeviceHolder(itemType: Int): IDeviceHolder? {
        for (holder in deviceHolders) {
            if (holder.isSameItemType(itemType)) return holder
        }
        return null
    }

    fun queryMode(device: AbsDevice): IModel? {
        return deviceModeMap[device.key]
    }

    fun recordMode(device: AbsDevice, mode: IModel) {
        deviceModeMap[device.key] = mode
    }

    fun deletedMode(device: AbsDevice) {
        deviceModeMap.remove(device.key)
    }

    fun clearLastDevice() {
        lastDeviceTransform.removed()
    }

    /**
     * 注册
     */
    fun register(subLibs: MutableList<ISubLib>) {
        for (subLib in subLibs) {
            /*注册Holder*/
            HolderConfig.registerHolder(subLib.keys(), subLib.itemTypes())
            /*注册subLib*/
            SubConfig.registerSubLib(subLib)
        }
    }

    /**
     * 设备列表数据转变--数据操作转换-计算量较大，在子线程内处理
     */
    @WorkerThread
    @Synchronized
    fun deviceTransform(
        absDevices: MutableList<AbsDevice>?, resetLastItems: Boolean = false
    ): MutableList<IDeviceItem> {
        SafeLog.w(TAG) { "deviceTransform，absDevices: ${absDevices?.size} lastDeviceTransform:$lastDeviceTransform ; resetLastItems = $resetLastItems" }
        if (absDevices.isNullOrEmpty()) {
            gwSubItemMap.clear()
            gwSubItemMap4BleBroadcast.clear()
            /*移除之前构建的item信息*/
            return lastDeviceTransform.apply { removed() }.deviceItems
        }
        /*开始依据设备信息进行模型转换*/
        val newItems = mutableListOf<IDeviceItem>()
        val newItemsHash = ConcurrentHashMap<String, IDeviceItem>()
        makeCurNewDeviceItemList(resetLastItems, absDevices, newItems, newItemsHash)
        lastDeviceTransform.compareDif(newItems, newItemsHash)
        return lastDeviceTransform.deviceItems
    }

    private fun makeCurNewDeviceItemList(
        resetLastItems: Boolean,
        absDevices: MutableList<AbsDevice>,
        newItems: MutableList<IDeviceItem>,
        newItemsHash: ConcurrentHashMap<String, IDeviceItem>
    ) {
        val temDevices = mutableListOf<AbsDevice>().apply { addAll(absDevices) }
        val iterator = temDevices.iterator()
        val mainDeviceItemMap = hashMapOf<String, IDeviceItem>()
        val subDeviceItemMap = hashMapOf<String, IMultiItem>()
        SafeLog.i(TAG) { "makeCurNewDeviceItemList() resetLastItems = $resetLastItems" }
        while (iterator.hasNext()) {
            val device = iterator.next()
            /*先查询是否已构建item*/
            val key = device.key
            val item = lastDeviceTransform.deviceItemsHash[key]
            var newDeviceItem: IDeviceItem? = null
            SafeLog.i(TAG) { "makeCurNewDeviceItemList() key = $key ; item = $item" }
            if (item != null) {
                /**若之前已经记录过该设备，并且该设备是被标记成不支持设备类型，并且当前就是需要重置列表，则需要判断是否已重新加载了新的支持类型（动态模块安装成功了的场景）*/
                if (resetLastItems && item is DefNoSupportDeviceItem) {
                    parse2DeviceItem(device)?.let { newDeviceItemCur ->
                        val isNoSupportDeviceItem = newDeviceItemCur is DefNoSupportDeviceItem
                        SafeLog.i(TAG) { "makeCurNewDeviceItemList() isNoSupportDeviceItem = $isNoSupportDeviceItem" }
                        /**表面动态模块加载成功，该设备的类型需要重新赋值*/
                        if (!isNoSupportDeviceItem) {
                            newDeviceItem = newDeviceItemCur
                        }
                    }
                }
                /**旧业务，还是保留之前的item对象*/
                if (newDeviceItem == null) {
                    item.updateDevice(device)
                    /*记录主从设备*/
                    if (item is IMultiItem) {
                        val order = item.order()
                        if (order == 0) {
                            mainDeviceItemMap[item.key4Multi()] = item
                        } else {
                            subDeviceItemMap[item.key4Multi()] = item
                        }
                    }
                    newItems.add(item)
                    newItemsHash[key] = item
                    continue
                }
            }
            /**若之前已经解析到了新的item对象，则直接复用，否则重新生成，若为null则表明当前设备不需要在设备列表展示*/
            val deviceItem = (newDeviceItem ?: parse2DeviceItem(device)) ?: continue
            /*解析服务器配置的sku图和头图信息*/
            parseExtResource(device)
            if (!DeviceListEditManager.isEditState()) {
                deviceItem.bigItem = ExtDeviceInfoManager.getCardSize(device.sku, device.device) == ExtDeviceInfo.CARD_SIZE_BIG
            }
            val itemType = queryItemType(deviceItem.bigItem, device.sku, device.goodsType)
            SafeLog.i(TAG) { "makeCurNewDeviceItemList deviceTransform() sku = ${device.sku} ; goodsType = ${device.goodsType} ;  bigItem = ${deviceItem.bigItem} ;  itemType = $itemType ; isEditState = ${DeviceListEditManager.isEditState()}" }
            /*记录主从设备*/
            if (deviceItem is IMultiItem) {
                val order = deviceItem.order()
                if (order == 0) {
                    mainDeviceItemMap[deviceItem.key4Multi()] = deviceItem
                } else {
                    subDeviceItemMap[deviceItem.key4Multi()] = deviceItem
                }
            }
            newItems.add(deviceItem)
            newItemsHash[key] = deviceItem
        }
        /*关联主从设备*/
        for (key4Multi in subDeviceItemMap.keys) {
            val item = mainDeviceItemMap[key4Multi] ?: continue
            val subItem = subDeviceItemMap[key4Multi]
            subItem?.run {
                /*关联主设备*/
                linkItem(item)
                /*主设备关联从设备*/
                val mainMultiItem = item as? IMultiItem
                val subDeviceItem = this as? IDeviceItem
                subDeviceItem?.let {
                    mainMultiItem?.linkItem(it)
                }
            }
        }
    }

    private fun parseExtResource(device: AbsDevice) {
        val extResources = device.deviceExt?.extResources ?: return
        if (extResources.isNotEmpty()) {
            JsonUtil.fromJson(extResources, ExtResource::class.java)?.run {
                ThemeM.getInstance.makeSkuResource(
                    device.sku,
                    device.spec,
                    this.skuUrl,
                    this.headOnImg,
                    this.headOffImg,
                    this.headOnImgNew,
                    this.headOffImgNew
                )
            }
        }
    }

    private fun queryItemType(bigItem: Boolean, sku: String, goodsType: Int): Int {
        val key = if (goodsType == GoodsType.GOODES_TYPE_NO_SUPPORT) sku else goodsType.toString()
        val itemTypeList = HolderConfig.key4ItemTypeMap[key]
        if (itemTypeList.isNullOrEmpty()) {
            /*若未匹配到-则表明该设备类型当前APP版本不支持*/
            return if (DeviceListEditManager.isClassicStyle()) {
                if (bigItem) Item.ITEM_TYPE_0_BIG else Item.ITEM_TYPE_0_SMALL
            } else {
                if (bigItem) Item.ITEM_TYPE_0_BIG_NEW else Item.ITEM_TYPE_0_SMALL_NEW
            }
        }
        val pos = (itemTypeList.size - 1).coerceAtMost(if (bigItem) 1 else 0)
        return itemTypeList[pos]
    }

    /**
     * 转化成对应的IDeviceItem信息
     * @return [IDeviceItem]该字段若为null-则表明当前设备不需要显示在设备列表上
     */
    private fun parse2DeviceItem(device: AbsDevice): IDeviceItem? {
        return SubConfig.querySubLib(device).transform(device)
    }
}

/**
 * Create By xieyingwu on 2022/11/4
 *
 * 辅助类-用于记录相关设备转化的信息
 */
internal class DeviceTransform {
    val deviceItems = CopyOnWriteArrayList<IDeviceItem>()
    var deviceItemsHash = ConcurrentHashMap<String, IDeviceItem>()
    var itemChangeListener: ((items: MutableList<IDeviceItem>) -> Unit)? = null

    fun removed() {
        deviceItems.forEach { item ->
            item.removed(true)
            DevicesOp.deletedMode(item.device)
        }
        deviceItems.clear()
        deviceItemsHash.clear()
    }

    internal fun compareDif(
        newItems: MutableList<IDeviceItem>, newItemsHash: ConcurrentHashMap<String, IDeviceItem>
    ) {
        deviceItemsHash.clear()
        deviceItemsHash = newItemsHash
        val keys = mutableListOf<String>().apply { addAll(newItemsHash.keys) }
        val empty = keys.isEmpty()
        SafeLog.w("DeviceTransform") { "compareDif() empty = $empty" }
        for (item in deviceItems) {
            if (empty) {
                item.removed(true)
                DevicesOp.deletedMode(item.device)
                SafeLog.w("DeviceTransform") { "compareDif() removed - ${item.deviceInfoStr()}" }
                continue
            }
            if (!keys.remove(item.device.key)) {
                item.removed(true)
                DevicesOp.deletedMode(item.device)
                SafeLog.w("DeviceTransform") { "compareDif() removed - ${item.deviceInfoStr()}" }
            }
        }
        deviceItems.clear()
        deviceItems.addAll(newItems)
        itemChangeListener?.invoke(deviceItems)
    }

}

/**
 * Create By xieyingwu on 2022/11/22
 *
 * 扩展资源
 */
@Keep
internal class ExtResource {
    var headOffImg: String? = ""
    var headOffImgNew: String? = ""
    var headOnImg: String? = ""
    var headOnImgNew: String? = ""
    var skuUrl: String? = ""
}
