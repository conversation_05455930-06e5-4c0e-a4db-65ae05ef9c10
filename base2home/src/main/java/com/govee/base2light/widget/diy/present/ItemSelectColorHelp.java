package com.govee.base2light.widget.diy.present;

import android.content.Context;
import android.graphics.drawable.Drawable;
import android.graphics.drawable.GradientDrawable;
import android.util.AttributeSet;
import android.view.View;
import android.widget.ImageView;
import android.widget.RelativeLayout;
import android.widget.TextView;

import com.govee.base2home.Constant;
import com.govee.base2home.color.IColorChoose;
import com.govee.base2home.color.IColorTemp;
import com.govee.base2home.color.IPalette;
import com.govee.base2home.color.PaletteDialogNew;
import com.govee.base2home.color.PaletteInterfaceKt;
import com.govee.base2kt.utils.ColorUtils;
import com.govee.base2light.following.net.OneParamCallback;
import com.govee.ui.R;
import com.ihoment.base2app.infra.SafeLog;
import com.ihoment.base2app.util.ResUtil;

import androidx.annotation.NonNull;
import androidx.appcompat.widget.AppCompatImageView;

/**
 * create by chenshun on 2022/8/30
 * Describe:ItemSelectColorView管理类
 */
public class ItemSelectColorHelp {

    private Context context;
    private RelativeLayout colorSelectLayout;
    private AppCompatImageView colorView;//颜色view
    private View refreshColorView;//刷新颜色按钮
    private TextView colorText;

    public int DEFAULT_COLOR = 0xFFFF9829;
    private int color = DEFAULT_COLOR;
    private String sku = "";
    private boolean colourLess;//默认不支持无色

    private OneParamCallback<Integer> valueChangeCallback;

    //默认色温
    private int[] defaultTemRange = new int[]{Constant.color_temperature_min_kelvin, Constant.color_temperature_max_kelvin};

    public void initView(AttributeSet attrs, Context context, RelativeLayout colorSelectLayout, AppCompatImageView colorView, View refreshColorView, TextView colorText) {
        this.context = context;
        this.colorSelectLayout = colorSelectLayout;
        this.colorView = colorView;
        this.refreshColorView = refreshColorView;
        this.colorText = colorText;

        if (colorSelectLayout == null || colorView == null || refreshColorView == null) {
            return;
        }

        initData(attrs, context);
        setViews();
        setListener();
    }

    private void initData(AttributeSet attrs, Context context) {
    }

    private void setViews() {

    }

    private void showColorSelectUI(boolean show) {
        if (refreshColorView != null) {
            refreshColorView.setVisibility(show ? View.VISIBLE : View.GONE);
        }

        if (colorText != null) {
            colorText.setVisibility(show ? View.GONE : View.VISIBLE);
        }

        if (colorView != null) {
            colorView.setVisibility(show ? View.VISIBLE : View.GONE);
        }
    }

    private void setListener() {
        if (colorSelectLayout != null) {
            IColorChoose iColorChoose = new IColorChoose() {
                @Override
                public boolean isSupportChooseMultiColors() {
                    return false;
                }

                @Override
                public void chooseColor(@NonNull int[] newColors) {
                    //用户重新选择颜色
                    changeColor(newColors[0]);
                    showColorSelectUI(true);
                }

                @Override
                public void chooseColorRealTime(int color, boolean isFirstDown) {
                    //空
                }
            };
            IColorTemp iColorTemp;
            if (defaultTemRange != null) {
                iColorTemp = new IColorTemp() {
                    @Override
                    public boolean canClick() {
                        return true;
                    }

                    @NonNull
                    @Override
                    public int[] getColorTempRange() {
                        return defaultTemRange;
                    }

                    @Override
                    public void chooseColorTem(int colorTem) {
                        int[] colorT = Constant.getTemColorByKelvin(colorTem, defaultTemRange);
                        int[] color = new int[]{colorT[2]};
                        iColorChoose.chooseColor(color);
                    }
                };
            } else {
                iColorTemp = null;
            }
            colorSelectLayout.setOnClickListener(v -> {
                IPalette iPalette = PaletteInterfaceKt.getIPalette(sku, null, colourLess, iColorChoose, null, iColorTemp, true);
                SafeLog.Companion.i("ItemSelectColorHelp", () -> "iPalette = " + iPalette);
                PaletteDialogNew.Companion.createDialog(context, iPalette, color, true).show();
            });
        }

        if (refreshColorView != null) {
            refreshColorView.setOnClickListener(v -> {
                //刷新颜色为默认颜色
                if (valueChangeCallback != null) {
                    valueChangeCallback.callback(0);
                }
                color = 0;
                showColorSelectUI(false);
            });
        }
    }

    public void resetDefaultColor(int defaultColor) {
        this.DEFAULT_COLOR = defaultColor;
    }

    public void setColor(int color) {
        if (color == 0) {
            color = DEFAULT_COLOR;
            showColorSelectUI(false);
        } else {
            showColorSelectUI(true);
        }

        this.color = color;
        setColorPiece(colorView, color);
    }

    public void setSku(String sku) {
        this.sku = sku;
    }

    public void setValueChangeCallback(OneParamCallback<Integer> valueChangeCallback) {
        this.valueChangeCallback = valueChangeCallback;
    }

    private void changeColor(int newColor) {
        color = newColor;
        if (valueChangeCallback != null) {
            valueChangeCallback.callback(color);
        }

        setColorPiece(colorView, color);
    }

    public void setSupportColourLess(boolean colourLess) {
        this.colourLess = colourLess;
    }

    public static void setColorPiece(ImageView iv, int color) {
        if (iv == null) return;
        boolean noColor = ColorUtils.isNoColor(color);
        if (noColor) {
            iv.setImageDrawable(ResUtil.getDrawable(R.mipmap.new_diy_btn_bg_color_un));
            return;
        }
        Drawable drawable = ResUtil.getDrawable(R.drawable.component_color_round_13);
        if (drawable instanceof GradientDrawable) {
            ((GradientDrawable) drawable).setColor(color);
            boolean nearWhiteColor = ColorUtils.isNearWhiteColor(color);
            if (nearWhiteColor) {
                drawable = ResUtil.getDrawable(R.drawable.component_color_round_12);
                ((GradientDrawable) drawable).setStroke(1, ResUtil.getColor(R.color.ui_color_block_normal_style_2_stroke));
            }
            iv.setImageDrawable(drawable);
        }
    }

    public void setDefaultTemRange(int[] defaultTemRange) {
        if (defaultTemRange == null || defaultTemRange.length < 2) {
            this.defaultTemRange = null;
            return;
        }

        this.defaultTemRange = defaultTemRange;
    }
}
