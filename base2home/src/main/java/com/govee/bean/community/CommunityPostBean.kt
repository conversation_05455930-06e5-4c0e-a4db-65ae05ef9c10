package com.govee.bean.community

import android.os.Parcelable
import android.text.TextUtils
import androidx.annotation.Keep
import com.govee.base2home.community.post.CircleTag
import com.govee.base2home.community.post.Img
import com.govee.base2home.community.post.Post
import com.govee.base2home.translate.TranslateBean
import com.govee.base2kt.utils.ColorUtils
import com.govee.base2kt.utils.NumberUtils
import com.govee.home.account.config.AccountConfig
import com.govee.ui.R
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.DeviceUtil
import com.ihoment.base2app.util.ResUtil
import kotlinx.parcelize.Parcelize

/**
 *     author  : sinrow
 *     time    : 2025/3/6
 *     version : 1.0.0
 *     desc    : 社区改版普通帖子 bean 类集合
 */

@Keep
data class CommunityDiscovery(
    val projectBanner: ProjectBanner?,
    val activityBanner: ActivityBanner?,
    val topics: MutableList<Topic>?,
    val rotateTopics: CircleTopic?,
    val postings: MutableList<Posting>,
    val nextCursors: NextCursors?
)

@Keep
data class ProjectBanner(
    val bannerId: Int?,
    val imgUrl: String?,
    val gifUrl: String?,
    val skipInfo: SkipInfo?,
) {
    fun loadImgUrl(): String {
        return if (gifUrl.isNullOrEmpty()) imgUrl ?: "" else gifUrl
    }
}

@Keep
data class ActivityBanner(
    val bannerId: Int?,
    val imgUrl: String?,
    val gifUrl: String?,
    val skipInfo: SkipInfo?
) {
    fun loadImgUrl(): String {
        return if (gifUrl.isNullOrEmpty()) imgUrl ?: "" else gifUrl
    }
}

@Keep
data class CircleTopic(
    var startTime: Long,//开始时间
    var endTime: Long,//结束时间
    var duration: Int,//持续时间
    val topics: MutableList<MutableList<Topic>>?//轮播话题
)

@Keep
data class CommunityCircles(
    val circles: MutableList<CommunityCircle>,
)

@Keep
data class Posting(
    val lastPostId: Long?,
    var postId: Long?,
    var postType: Int?,//贴子类型 1普通贴 2投票贴 3视频贴 4banner 5问答帖子
    val publisher: Int?,//发帖人 1自己 2官方 3其他人
    var title: String?, //标题
    var content: String?,//贴子内容
    var imgUrls: MutableList<Img>?,
    val coverUrl: String?,//封面url
    val h5Url: String?,//H5跳转链接
    val videoInfo: CommunityVideo?,
    var voteInfo: Vote?,
    val userInfo: CommunityUser?,
    var circle: CircleTag?,
    var topicInfo: Topic?,
    var commentNum: Int?,//评论数量
    var isLike: Boolean?,//是否点赞
    var likeNum: Int?,//点赞数
    var highQuality: Boolean?,//是否高质量贴
    var isTop: Boolean?,//是否置顶
    val bannerInfo: CommunityBanner?,
    var extraParams: String?,
    var createTime: Long,
    //是否有新的回复，显示红点
    var replyState: Boolean?,
    //翻译文本
    var translateStatus: Boolean = false,
    var translateText: ArrayList<TranslateBean>?,
    val questionStatus: Int?,//问答状态，1:已回答；0:未回答
    val sorts: MutableList<String>? = null,// 搜索页面独有的字段
) {
    companion object {
        fun makeDef(postId: Long?, postType: Int? = null): Posting {
            return Posting(
                null,
                postId,
                postType,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                null,
                0L,
                null,
                false,
                null,
                0,
            )
        }
    }

    var isVisTopTag = true /*是否显示置顶*/
    var isVisSendByMeTag = true /*是否显示我发布的标识*/

    /*用于是否显示话题标题内容*/
    var isVisibilityTopicTag: Boolean = true

    fun isVideoType(): Boolean {
        return postType == 3
    }

    fun isBannerType(): Boolean {
        return postType == 4
    }

    fun isQuestionType(): Boolean {
        return postType == 5
    }

    fun isVoteType(): Boolean {
        return postType == 2
    }

    /**
     * 是否正在等待提问
     */
    fun isWaitQuestion(): Boolean {
        return isQuestionType() && questionStatus == 0
    }

    fun sortNewPosting(next: Posting): Boolean {
        if (next.isBannerType()) {
            return false
        } else if (next.isVideoType()) {
            if (videoInfo?.videoId == next.videoInfo?.videoId) {
                SafeLog.i("CommonPostAdapter") { "clearRepeatNewPosting  videoId = ${next.videoInfo?.videoId}" }
                return true
            }
        } else if (postId == next.postId && postType == next.postType) {
            SafeLog.i("CommonPostAdapter") { "clearRepeatNewPosting  postId = ${next.postId}  postType = ${next.postType}" }
            return true
        }
        return false
    }

    /**
     * 是否是官方帖子
     */
    fun isOfficial(): Boolean {
        return publisher == 2
    }

    fun isHighQuality(): Boolean {
        return highQuality == true
    }

    fun isTop(): Boolean {
        return isTop == true
    }

    fun isOwn(): Boolean {
        return publisher == 1
    }

    /**
     *  H5 的 postType 类型: 0：普通帖，1：投票帖
     *  重新映射到帖子类型
     *  <p>
     *
     *  </p>
     */
    fun syncPostTypeByH5UpdateJs(postType: Int, voteInfo: com.govee.base2home.community.bean.Vote?): Boolean {
        var isChange = false
        when (postType) {
            0 -> {
                isChange = this.postType != 1
                this.postType = 1
            }

            1 -> {
                voteInfo?.let {
                    this.voteInfo = Vote.syncOldVote(voteInfo)
                }

                isChange = this.postType != 2
                this.postType = 2
            }
        }
        return isChange
    }

    fun syncH5PostingInfo(posting: Posting) {
        posting.isLike?.let {
            this.isLike = posting.isLike
        }
        posting.likeNum?.let {
            this.likeNum = posting.likeNum
        }
        posting.commentNum?.let {
            this.commentNum = posting.commentNum
        }
        posting.extraParams?.let {
            this.extraParams = posting.extraParams
        }
    }

    fun syncLikeStatus(isLike: Boolean, likeNum: Int, likeNumValid: Boolean): Boolean {
        val videoType = isVideoType()
        if (videoType) {
            return videoInfo?.let {
                it.liked = if (isLike) 1 else 0
                val newLikeTimes =
                    if (likeNumValid) likeNum else if (isLike) it.likeTimes + 1 else it.likeTimes - 1
                it.likeTimes = 0.coerceAtLeast(newLikeTimes)
                true
            } ?: false
        }
        this.isLike = isLike
        val lastLikeNum = this.likeNum ?: 0
        val newLikeNum: Int =
            if (likeNumValid) likeNum else if (isLike) lastLikeNum + 1 else lastLikeNum - 1
        this.likeNum = 0.coerceAtLeast(newLikeNum)
        return true
    }
}

@Keep
data class CommunityBanner(
    val bannerId: Int,
    val imgUrl: String,
    val gifUrl: String,
    val skipInfo: SkipInfo
) {
    fun getUrl(): String {
        return if (!TextUtils.isEmpty(gifUrl)) gifUrl else imgUrl
    }
}

@Keep
data class CommunityCircle(
    val circleId: Int,
    val circleName: String,
    val circleType: Int,
    val coverInfo: CoverInfo,
    val memberNum: Int,
    var isJoined: Boolean,
    val tipInfo: TipInfo?,
    val circleIcon: String?,
)

@Keep
data class TipInfo(
    val tip: String?,
    val type: Int //0默认xx人关注 1有人评论 2新增帖子 3关注的人关注 4设备
)

@Keep
data class CircleRankList(
    val identity: String,
    val identityType: Int,
    val headUrl: String?,
    val avatarUrl: String?,
    val nickName: String,
    val avatarTagUrl: MutableList<String>,
    val isDefaultUser: Boolean,
    val official: Boolean,
    val tagUrlV2: MutableList<TagUrlV2>
)

@Keep
data class TagUrlV2(
    val darkUrl: String,
    val lightUrl: String,
    val isHolidaySkin: Int
)

@Keep
data class CoverInfo(
    val darkColor: Int,
    val lightColor: Int,
    val coverImg: String
) {
    fun getColor(): Int {
        return if (ResUtil.curLightMode()) {
            lightColor
        } else {
            darkColor
        }
    }
}

@Keep
data class Topic(
    val topicId: Int,// 话题ID
    var discussionNum: Int?,// 话题用户参与人数
    var topicHot: Int?,//话题热度
    var topicAttr: Int?,//话题类型：1官方，2其他'
    val topicName: String?,
    val skipInfo: SkipInfo?,
) {
    fun jump2Browser(): Boolean {
        return skipInfo?.skip2Browser() ?: false
    }

    fun jump2H5(): Boolean {
        return skipInfo?.skip2H5() ?: false
    }
}

@Keep
data class SkipInfo(
    val redirectUrl: String?,
    val skipWay: String?,
    val title: String?,
    var type: Int = 0
) {
    val skip_way_webview = "webview"
    val skip_way_browser = "browser"

    fun skip2H5(): Boolean {
        return "webview" == skipWay
    }

    fun skip2H5V2(): Boolean {
        return "webView" == skipWay
    }

    fun skip2Browser(): Boolean {
        return "browser" == skipWay
    }
}

@Keep
data class Vote(
    val questionId: Int,
    val startTime: Long,
    val endTime: Long,
    var content: String,
    val type: Int,/*投票类型：1单选 2多选*/
    val voteOptions: MutableList<VoteOption>?,
) {
    companion object {
        fun syncOldVote(old: com.govee.base2home.community.bean.Vote): Vote {
            var temp: MutableList<VoteOption>? = null
            old.voteOptions?.let {
                temp = mutableListOf()
                it.forEach { option ->
                    temp?.add(VoteOption(option.questionOptionId, option.imgUrl, option.videoUrl, option.content, option.num, option.percent, option.voted))
                }
            }
            return Vote(old.questionId, old.startTime, old.endTime, old.content, old.type, temp)
        }
    }
}

@Keep
data class VoteOption(
    val questionOptionId: Int,//选项id
    val imgUrl: String,//选项图片链接
    val videoUrl: String?,//(5.9新增)视频url
    var content: String,//选项内容，旧服务器字段
    val num: Int, //多少人投
    val percent: Float,//百分比
    var voted: Boolean //本人是否投此选项
)

@Keep
data class CommunityCircleMore(
    val myFollowCircles: MutableList<CommunityCircle>?,
    val recommendedCircles: MutableList<CommunityCircle>?,
)

@Keep
data class CommunityPostings(
    val postings: MutableList<Posting>?,
    val nextCursors: NextCursors? = null
)

@Keep
data class MyPostings(
    val lastVideoId: Int?, //下一次的传参
    //下一次的传参
    val lastPostId: Int?, val lastTime: Long?, val postings: MutableList<Posting>?
)

@Keep
data class OtherTopics(val last: Boolean? = false, val history: Boolean? = false)

@Keep
data class CircleTopics(
    val dataList: MutableList<Topic>?,
    private val total: Int,
    val pageNum: Int,
    val pageSize: Int,
    var otherTopic: OtherTopics?
) {
    fun isEmpty(): Boolean {
        return total <= 0 || dataList.isNullOrEmpty()
    }

    fun totalPageNum(onePageNum: Int): Int {
        return NumberUtils.calculatePageNums(total, onePageNum)
    }

    fun firstPageTopicNum(onePageNum: Int): Int {
        if (total >= onePageNum) return onePageNum
        return total
    }
}

@Keep
data class TopicDetails(
    var isJoined: Boolean?,
    val topicId: Int,
    var topicName: String?,
    var discussionNum: Int?,//参与人数
    var topicDes: String?,
    val bgColor: BgColor?,
    val postings: MutableList<Posting>?,
    val topicAttr: Int?,//话题类型：1官方，2其他',
    var circleTag: CircleTag?,
    var editState: Boolean?,//是否支持修改true支持 false不支持
    val topicCreator: TopicCreator?,
    val nextCursors: NextCursors? = null
)

@Keep
data class BgColor(
    val darkColor: String?,
    val lightColor: String?
) {
    fun getBgColor(): Int {
        if (lightColor.isNullOrEmpty() || darkColor.isNullOrEmpty()) {
            return ResUtil.getColor(R.color.ui_bg_color_style_9)
        }
        val lightMode = ResUtil.curLightMode()
        val split: List<String> = if (lightMode) {
            lightColor.split(",")
        } else {
            darkColor.split(",")
        }
        if (split.size == 3) {
            return ColorUtils.toColor(split[0].toInt(), split[1].toInt(), split[2].toInt())
        }
        return ResUtil.getColor(R.color.ui_bg_color_style_9)
    }
}

@Keep
data class CircleDetails(
    val des: String?,
    private val hasCircle: Boolean,
    val circleId: Int,
    val circleName: String?,
    val circleType: Int,
    val coverInfo: CoverInfo?,
    var memberNum: Int,
    var postCount: Int,
    var isJoined: Boolean,
    val tip: String?,//圈子动态
//    val rankList: MutableList<CircleRankList>?,
    val lastCircleId: Int,
    val rotateTopics: CircleTopic?,//圈子--轮播话题列表
    val topics: MutableList<Topic>?,//圈子--话题列表
    val questions: MutableList<Question>?,//圈子内的邀请回答
    val postings: MutableList<Posting>?,
    val nextCursors: NextCursors?
) {

    fun circleHadClose(): Boolean {
//        if (circleType == 1) return hasCircle
//        return !hasCircle
        return hasCircle
    }

    /**
     * 加入或退出圈子操作
     */
    fun op4JoinCircle(join: Boolean): Boolean {
        return if (join && !isJoined) {
            isJoined = true
            memberNum++
            true
        } else if (!join && isJoined) {
            isJoined = false
            memberNum--
            true
        } else {
            false
        }
    }
}

@Keep
data class FollowingUser(
    val identity: String,
    val identityType: Int,
    val myIdentity: String,
    var myIdentityType: Int,
    var subscribe: Int,//1关注，0取消关注
)

@Keep
data class LikeUserResult(
    var spotNum: Int
)

@Keep
data class VideoLikeState(
    val videoId: Int,
    val state: Int,
    var list: Boolean
)

@Keep
data class Request4JoinCircle(val circleId: Int)

@Keep
data class CircleDetail4Roadmap(
    val des: String,
    val circleId: Int,
    val circleName: String,
    val circleType: Int,
    val coverInfo: CoverInfo?,
    var memberNum: Int,
    var postCount: Int,
    var isJoined: Boolean,
    var hasCircle: Boolean,
    val tipInfo: TipInfo?,
    val categories: MutableList<RoadmapCategory>?,
    val rotateTopics: CircleTopic?,//圈子--轮播话题列表
    val topics: MutableList<Topic>?,//圈子--话题列表
    val questions: MutableList<Question>?,//圈子内的邀请回答
) {
    fun toCircleDetail(): CircleDetails {
        val featureList = categories?.filter { it.type == 4 }
        var nextCursors: NextCursors? = null
        var postings: MutableList<Posting>? = null
        if (!featureList.isNullOrEmpty()) {
            nextCursors = featureList[0].queryCursorsDTO
            postings = featureList[0].postings
        }
        return CircleDetails(
            des,
            hasCircle,
            circleId,
            circleName,
            circleType,
            coverInfo,
            memberNum,
            postCount,
            isJoined,
            "",
            -1,
            rotateTopics,
            topics,
            questions,
            postings,
            nextCursors
        )
    }
}

@Keep
@Parcelize
data class Roadmap(
    val title: String,
    val roadMapId: Int,
    val sort: Int,
    var readTimes: Int,
    var likeTimes: Int,
    var liked: Int,
    val releaseDes: String?,
    var h5Url: String?,
    val templateId: Int,
    var commentTotal: Int,
    val isCircleTop: Int?
) : Parcelable {
    fun isLiked(): Boolean {
        return liked == 1
    }

    /**
     * 变更点赞状态以及关联的点赞数
     */
    fun changeLiked() {
        if (liked == 1) {
            liked = 0
            likeTimes--
        } else if (liked == 0) {
            liked = 1
            likeTimes++
        }
        likeTimes = 0.coerceAtLeast(likeTimes)
    }
}

@Keep
data class RoadmapCategory(
    val title: String,
    val type: Int,
    val dataType: Int,
    val postings: MutableList<Posting>?,
    val roadmaps: MutableList<Roadmap>?,
    val queryCursorsDTO: NextCursors?
) {
    fun toRoadmapList(): RoadmapList {
        return RoadmapList(roadmaps).apply {
            this.type = <EMAIL>
        }
    }
}

@Keep
data class RoadmapList(val roadmaps: MutableList<Roadmap>?) {
    @Transient
    var firstPage: Boolean = false
    var type: Int = -1
}

@Keep
data class Request4RoadmapLike(val state: Int, val roadMapId: Int, val templateId: Int) {
    val client: String = DeviceUtil.getDeviceUuid(BaseApplication.getContext()) ?: ""
    val accountId: Int = AccountConfig.read().accountId
}

@Keep
data class CoEntryNew(val new: Boolean, val blockStatus: Int, val content: String)
/*新增字段 blockStatus：0未被拉黑；1被拉黑*/
/*新增字段 content：共创上新内容*/

@Keep
data class SearchTopicResponse(var topics: MutableList<SearchTopic>)

@Keep
data class SearchTopic(val topicId: Int, val topicName: String, var skipInfo: SkipInfo?)


@Keep
data class Data4SearchAll(
    val postings: MutableList<Posting>?,
    val topics: MutableList<Topic4Search>?,
    val users: MutableList<User4Search>?,
    val sorts: MutableList<String>?
)

@Keep
data class CommunitySearchPostings(
    val postings: MutableList<Posting>?,
    val sorts: MutableList<String>?
)

@Keep
data class User4SearchList(val users: MutableList<User4Search>?, val sorts: MutableList<String>?)

@Keep
data class User4Search(
    val id: Int,
    val identityType: Int,
    val identity: String,
    val avatar: String?,
    val nickName: String?,
    val followTotal: Int?,
    var fansTotal: Int,
    var followed: Int,
    val self: Int
)

@Keep
data class Topic4SearchList(val topics: MutableList<Topic4Search>?, val sorts: MutableList<String>?)

@Keep
data class Topic4Search(
    val id: Int,
    val title: String?,
    val hotNum: Int,
    val redictUrl: String?,
    val skipType: String?,
) {
    fun skip2H5(): Boolean {
        return "webview" == skipType
    }

    fun skip2Browser(): Boolean {
        return "browser" == skipType
    }
}

@Keep
data class AssociationBean(val association: MutableList<String>?)

@Keep
data class FriendDynamicPost(
    val postId: Int?,
    val videoId: Int?,
    val postings: MutableList<Posting>?
)

/**
 * 邀请我回答
 */
@Keep
data class CircleInviteAnswer(
    val pageNum: Int,
    val pageSize: Int,
    val questions: List<Question>?,
    val total: Int
)

@Keep
data class Question(
    val questionId: Int,
    val questionTitle: String?,
    val questionUser: QuestionUser?
)

@Keep
data class QuestionUser(
    val headPortrait: String?,
    val identity: String?,
    val nickName: String?,
    val identityType: Int?
)

@Keep
data class NextCursors(
    val createTime: Long?,
    val evalId: Long?,
    val evalScore: Long?,
    val postId: Long?,
    val postScore: Long?,
    val questionId: Long?,
    val questionScore: Long?,
    val replyTime: Long?
)

@Keep
data class VideoList(
    val data: List<CommunityVideo>?
)

@Keep
data class CommunityCircleAll(
    var postings: MutableList<Posting>?,//圈子帖子列表
    val questionBanners: MutableList<Question>?,//邀请我回答banner
    val questions: MutableList<Question>?,//圈子内的邀请回答
    var myFollowCircles: MutableList<CommunityCircle>?,//关注圈子
    var recommendedCircles: MutableList<CommunityCircle>?,//推荐圈子
    val rotateTopics: CircleTopic?,//圈子--轮播话题列表
    val topics: MutableList<Topic>?,//圈子--话题列表
    var nextCursors: NextCursors? = null,
    val categories: MutableList<RoadmapCategory>?,
)

@Keep
data class SaveMyCircleRequest(
    val circleSorts: List<CircleSort>
)

@Keep
data class CircleSort(
    val circleId: Int,
    val sort: Int
)

@Keep
data class TopicList(
    val topics: List<Topic>?,
    val cursors: TopicCursors?
)

@Keep
data class TopicCreator(
    val headPortrait: String,
    val identity: String,
    val identityType: Int,
    val nickName: String,
    val isCreator: Boolean
)

@Keep
data class FollowUserData(
    val postings: List<Post>? = null,
    val videos: List<CommunityVideo>? = null,
    val latestPostTime: Long = 0,
    val latestVideoTime: Long = 0,
    val latestPictureTime: Long = 0,
    val user: CommunityUser? = null,
    val users: List<CommunityUser>? = null, //关注列表
    val followers: List<CommunityUser>? = null, //粉丝列表
    val postingNewReplyTime: Long = 0, //我参与的帖子最新的回复时间
    val videoNewReplyTime: Long = 0, //我的视频最新回复时间
    val fansTotal: Int = 0,//粉丝总数
    val followedTotal: Int = 0, //关注总数
)

@Keep
data class RedDotsData(
    val latestPostTime: Long = 0,
    val latestVideoTime: Long = 0,
    val latestPictureTime: Long = 0,
    val postingNewReplyTime: Long = 0, //我参与的帖子最新的回复时间
    val videoNewReplyTime: Long = 0, //我的视频最新回复时间
    val myPostingReplyTime: Long = 0, //我发布的帖子最新回复时间
    val myCommentReplyTime: Long = 0, //我评论的帖子最新回复时间
)

@Keep
data class FollowUserRecommendData(
    val referUsers: List<CommunityUser>?
)

@Keep
data class TopicCursors(
    val topicId: Int?,
    val userTopicId: Int?
)

@Keep
data class CheckTopicStatus(
    val topicName: String,
    val des: String,
    val circleId: Int
)



