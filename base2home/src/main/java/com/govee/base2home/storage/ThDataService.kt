package com.govee.base2home.storage

import com.alibaba.android.arouter.facade.template.IProvider

/**
 * author  : sinrow
 * time    : 2024/6/13
 * version : 1.0.0
 * desc    : 温湿度数据服务接口，提供温湿度设备数据获取和清理功能。
 */
interface ThDataService : IProvider {

    /**
     * 获取温湿度设备数据缓存大小
     * @param sku 设备SKU
     * @param device 设备地址
     * @return 数据大小
     */
    fun getThDataCacheMemory(sku: String, device: String): Long

    /**
     * 根据时间段清理温湿度设备数据
     * @param sku 设备SKU
     * @param device 设备地址
     * @param periodType 温湿度计删除删除数据——>保留时间段类型 @see com.govee.base2newth.ThConsV1.REMIND_7_DAYS
     */
    fun clearByPeriodType(sku: String, device: String, periodType: Int)

    /**
     * 获取全部温湿度计设备数据大小
     */
    fun getThAllDataSize(): Long

    /**
     * 清除非温湿度计数据
     */
    fun clearNonThData()
}