package com.govee.base2home.storage

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.govee.base2home.storage.model.AppMemoryOverviewModel
import com.govee.base2home.storage.model.CacheDataModel
import com.govee.base2home.storage.model.DeviceDataModel
import com.govee.mvvm.base.viewmodel.BaseViewModel
import com.govee.ui.R
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil
import kotlinx.coroutines.launch

/**
 * author  : sinrow
 * time    : 2025/07/17
 * version : 1.0.0
 * desc    : 存储管理ViewModel
 */
class StorageManagementViewModel : BaseViewModel() {
    override var TAG = "STORAGE_MGR"

    // 应用内存概览
    private val _memoryOverview = MutableLiveData<AppMemoryOverviewModel>()
    val memoryOverview: LiveData<AppMemoryOverviewModel> = _memoryOverview

    // 临时缓存数据
    private val _tempCacheData = MutableLiveData<List<CacheDataModel>>()
    val tempCacheData: LiveData<List<CacheDataModel>> = _tempCacheData

    // 温湿度设备数据

    private val _thDeviceData = MutableLiveData<List<DeviceDataModel>>()
    val thDeviceData: LiveData<List<DeviceDataModel>> = _thDeviceData

    // 系统数据
    private val _systemData = MutableLiveData<List<CacheDataModel>>()
    val systemData: LiveData<List<CacheDataModel>> = _systemData

    // 清理结果
    private val _clearResult = MutableLiveData<Boolean>()
    val clearResult: LiveData<Boolean> = _clearResult

    // 自动清理开关状态
    private val _autoCleanEnabled = MutableLiveData<Boolean>()
    val autoCleanEnabled: LiveData<Boolean> = _autoCleanEnabled

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // 错误信息
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage
    private val _toastMsg = MutableLiveData<String>()
    val toastMsg: LiveData<String> = _toastMsg

    init {
        loadAutoCleanStatus()
    }

    /**
     * 验证数据一致性
     */
    private fun validateDataConsistency(
        memoryOverview: AppMemoryOverviewModel,
        tempCacheSize: Long,
        thDeviceSize: Long,
        systemData: List<CacheDataModel>
    ) {
        val totalSize = memoryOverview.totalSize
        val systemDataSize = systemData.sumOf { it.size }
        val calculatedSize = tempCacheSize + thDeviceSize + systemDataSize

        SafeLog.i(TAG) {
            "数据一致性验证: " +
                "总大小=${FileStorageUtils.formatSize(totalSize)}, " +
                "临时缓存=${FileStorageUtils.formatSize(tempCacheSize)}, " +
                "温湿度设备=${FileStorageUtils.formatSize(thDeviceSize)}, " +
                "系统数据=${FileStorageUtils.formatSize(systemDataSize)}, " +
                "计算总和=${FileStorageUtils.formatSize(calculatedSize)}"
        }

        if (totalSize == calculatedSize) {
            SafeLog.i(TAG) { "✓ 数据一致性验证通过：总大小 = 临时缓存 + 温湿度设备 + 系统数据" }
        } else {
            val difference = totalSize - calculatedSize
            SafeLog.e(TAG) {
                "✗ 数据一致性验证失败：总大小 ≠ 分项总和，差异=${FileStorageUtils.formatSize(kotlin.math.abs(difference))}"
            }
            // 抛出异常，强制修复数据不一致问题
            throw IllegalStateException(
                "数据一致性验证失败：总大小(${FileStorageUtils.formatSize(totalSize)}) ≠ " +
                    "分项总和(${FileStorageUtils.formatSize(calculatedSize)})，" +
                    "差异=${FileStorageUtils.formatSize(kotlin.math.abs(difference))}"
            )
        }
    }

    /**
     * 加载所有数据
     */
    fun loadAllData() {
        SafeLog.i(TAG) { "开始加载存储管理所有数据" }
        viewModelScope.launch {
            try {
                _isLoading.value = true

                // 开始新的数据加载会话，清除旧缓存
                StorageManagerService.startNewDataSession()

                SafeLog.i(TAG) { "正在并行加载数据：内存概览、临时缓存、温湿度设备、系统数据" }

                // 先获取基础数据，避免重复调用
                val memoryOverview = StorageManagerService.getAppMemoryOverview()
                val thDevices = StorageManagerService.getThDeviceData()

                // 计算温湿度计设备总占用大小
                val thDeviceTotalSize = thDevices.sumOf { it.size }
                SafeLog.i(TAG) { "温湿度设备总数据大小: ${FileStorageUtils.formatSize(thDeviceTotalSize)}" }

                // 获取全部温湿度数据大小（只调用一次）
                val allThDataSize = StorageManagerService.getAllThDataSize()
                SafeLog.i(TAG) { "全部温湿度数据大小: ${FileStorageUtils.formatSize(allThDataSize)}" }

                // 获取临时缓存数据
                val tempCache = StorageManagerService.getTempCacheData()

                // 计算临时缓存总大小，传递给系统数据计算
                val tempCacheSize = tempCache.sumOf { it.size }
                // 传递已计算的数据，确保数据一致性
                val systemData = StorageManagerService.getSystemData(
                    memoryOverview.totalSize,
                    tempCacheSize,
                    thDeviceTotalSize
                )

                // 数据一致性验证
                validateDataConsistency(memoryOverview, tempCacheSize, thDeviceTotalSize, systemData)

                _memoryOverview.value = memoryOverview
                _tempCacheData.value = tempCache
                _thDeviceData.value = thDevices
                _systemData.value = systemData

                // 将温湿度设备数据保存到缓存中，供ThDeviceDataManagementActivity使用
                ThDeviceDataCache.updateDeviceList(thDevices)

                SafeLog.i(TAG) {
                    "存储管理数据加载完成：内存总大小=${FileStorageUtils.formatSize(memoryOverview.totalSize)}, 临时缓存=${tempCache.size}项(${
                        FileStorageUtils.formatSize(
                            tempCache.sumOf { it.size })
                    }), 温湿度设备=${thDevices.size}个(${FileStorageUtils.formatSize(thDevices.sumOf { it.size })}), 系统数据=${systemData.size}项(${
                        FileStorageUtils.formatSize(
                            systemData.sumOf { it.size })
                    })"
                }
            } catch (e: Exception) {
                SafeLog.e(TAG) { "loadAllData error: ${e.message}" }
                _errorMessage.value = "加载数据失败：${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 刷新内存概览
     */
    fun refreshMemoryOverview() {
        viewModelScope.launch {
            try {
                val overview = StorageManagerService.getAppMemoryOverview()
                _memoryOverview.value = overview
            } catch (e: Exception) {
                SafeLog.e(TAG) { "refreshMemoryOverview error: ${e.message}" }
                _errorMessage.value = "刷新内存概览失败"
            }
        }
    }

    /**
     * 刷新临时缓存数据
     */
    fun refreshTempCacheData() {
        viewModelScope.launch {
            try {
                val tempCache = StorageManagerService.getTempCacheData()
                _tempCacheData.value = tempCache
            } catch (e: Exception) {
                SafeLog.e(TAG) { "refreshTempCacheData error: ${e.message}" }
                _errorMessage.value = "刷新缓存数据失败"
            }
        }
    }

    /**
     * 清理临时缓存
     * @param isAuto 是否是自动清理开关按钮触发
     */
    fun clearTempCache(isAuto: Boolean) {
        viewModelScope.launch {
            try {
                val curTempData = getTempCacheTotalSize()
                if (!isAuto) {
                    _isLoading.value = true
                }
                val success = StorageManagerService.clearTempCache(isAuto = isAuto)
                if (!isAuto) {
                    _clearResult.value = success
                }

                if (success) {
                    // 刷新数据
                    refreshMemoryOverview()
                    refreshTempCacheData()
                }
                if (isAuto) {
                    val tempCache = StorageManagerService.getTempCacheData()
                    val tempData = tempCache.sumOf { it.size }
                    val value = curTempData - tempData
                    if (value > 0) {
                        val formatSize = FileStorageUtils.formatSize(value)
                        _toastMsg.value = ResUtil.getStringFormat(R.string.base2home_storage_auto_clean_toast, formatSize)
                    }
                }
            } catch (e: Exception) {
                SafeLog.e(TAG) { "clearTempCache error: ${e.message}" }
                _clearResult.value = false
                _errorMessage.value = "清理缓存失败：${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 设置自动清理开关
     */
    fun setAutoCleanEnabled(enabled: Boolean) {
        try {
            StorageManagerService.setAutoCleanEnabled(enabled)
            _autoCleanEnabled.value = enabled
        } catch (e: Exception) {
            SafeLog.e(TAG) { "setAutoCleanEnabled error: ${e.message}" }
            _errorMessage.value = "设置自动清理失败"
        }
    }

    /**
     * 加载自动清理状态
     */
    private fun loadAutoCleanStatus() {
        try {
            val enabled = StorageManagerService.isAutoCleanEnabled()
            _autoCleanEnabled.value = enabled
        } catch (e: Exception) {
            SafeLog.e(TAG) { "loadAutoCleanStatus error: ${e.message}" }
            _autoCleanEnabled.value = false
        }
    }

    /**
     * 获取临时缓存总大小
     */
    fun getTempCacheTotalSize(): Long {
        return _tempCacheData.value?.sumOf { it.size } ?: 0L
    }

    /**
     * 获取温湿度设备数据总大小
     */
    fun getThDeviceDataTotalSize(): Long {
        return _thDeviceData.value?.sumOf { it.size } ?: 0L
    }

    /**
     * 检查临时缓存是否可清理（大于0字节）
     */
    fun isTempCacheClearable(): Boolean {
        val totalSize = getTempCacheTotalSize()
        return totalSize > 0
    }

    /**
     * 检查温湿度设备数据是否可管理（至少有一个设备数据大于0MB）
     */
    fun isThDeviceDataManageable(): Boolean {
        return _thDeviceData.value?.any { FileStorageUtils.isGreaterThanZeroMB(it.size) } == true
    }

    /**
     * 刷新温湿度设备数据（不显示全局 loading）
     * 同时刷新相关的缓存和系统数据，因为清理设备数据会影响整个存储状况
     */
    fun refreshThDeviceData() {
        SafeLog.i(TAG) { "开始刷新温湿度设备数据及相关存储数据" }
        viewModelScope.launch {
            try {
                // 开始新的数据加载会话，清除旧缓存
                StorageManagerService.startNewDataSession()

                // 从缓存获取更新后的设备列表
                val thDevices = ThDeviceDataCache.deviceList ?: emptyList()

                // 刷新内存概览
                val newMemoryOverview = StorageManagerService.getAppMemoryOverview()

                // 重新计算温湿度设备总大小
                val thDeviceTotalSize = thDevices.sumOf { it.size }
                // 刷新临时缓存数据（因为数据库缓存大小会变化）
                val tempCache = StorageManagerService.getTempCacheData()

                // 刷新系统数据（因为温湿度数据变化会影响系统数据计算）
                val tempCacheSize = tempCache.sumOf { it.size }
                val systemData = StorageManagerService.getSystemData(newMemoryOverview.totalSize, tempCacheSize, thDeviceTotalSize)

                _memoryOverview.value = newMemoryOverview
                _systemData.value = systemData
                _tempCacheData.value = tempCache
                _thDeviceData.value = thDevices

                SafeLog.i(TAG) {
                    "温湿度设备数据及相关存储数据刷新完成：设备数量=${thDevices.size}, 设备总大小=${
                        FileStorageUtils.formatSize(
                            thDeviceTotalSize
                        )
                    }"
                }
            } catch (e: Exception) {
                SafeLog.e(TAG) { "refreshThDeviceData error: ${e.message}" }
                _errorMessage.value = "刷新温湿度设备数据失败"
            }
        }
    }
}
