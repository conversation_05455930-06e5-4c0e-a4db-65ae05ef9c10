package com.govee.base2home.storage.model


/**
 * 缓存数据模型
 */
data class CacheDataModel(
    val type: CacheType,
    val name: String,
    val size: Long, // 字节
    val paths: List<String>
)

/**
 * 缓存类型枚举
 */
enum class CacheType {
    VIDEO_CACHE,        // 视频缓存
    IMAGE_CACHE,        // 图片缓存
    WEB_RESOURCE,       // Web资源缓存
    DATABASE_CACHE,     // 数据库缓存
    DYNAMIC_SO,         // 动态SO库
    SYSTEM_FUNCTION,    // 系统功能
    TH_DEVICE_DATA,    // 历史温湿度计数据
}

/**
 * 设备数据模型
 */
data class DeviceDataModel(
    val sku: String,
    val device: String,
    val deviceName: String,
    val size: Long, // 字节
    val isSelected: Boolean = false,
    val spec: String = ""
)

/**
 * 应用内存概览模型
 */
data class AppMemoryOverviewModel(
    val totalSize: Long, // 总占用空间（字节）
    val deviceTotalStorage: Long, // 设备总存储空间（字节）
    val percentage: Double // 占设备总存储空间的百分比
)