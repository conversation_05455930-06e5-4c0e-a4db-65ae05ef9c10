package com.govee.base2home.storage.adapter

import com.chad.library.adapter.base.BaseQuickAdapter
import com.chad.library.adapter.base.viewholder.BaseDataBindingHolder
import com.govee.base2home.databinding.ItemStorageThDeviceDataBinding
import com.govee.base2home.storage.FileStorageUtils
import com.govee.base2home.storage.model.DeviceDataModel
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.visibleOrInvisible
import com.govee.kt.loadSkuIcon
import com.govee.ui.R
import com.ihoment.base2app.util.ResUtil

/**
 * author  : sinrow
 * time    : 2025/07/17
 * version : 1.0.0
 * desc    : 设备数据适配器，用于设备数据列表的展示和交互。
 */
class StorageThDeviceDataAdapter(
    private val onDeviceClick: (DeviceDataModel) -> Unit
) : BaseQuickAdapter<DeviceDataModel, BaseDataBindingHolder<ItemStorageThDeviceDataBinding>>(com.govee.base2home.R.layout.item_storage_th_device_data) {

    override fun convert(holder: BaseDataBindingHolder<ItemStorageThDeviceDataBinding>, item: DeviceDataModel) {
        val binding = holder.dataBinding ?: return
        binding.run {
            // 设备名称
            tvDeviceName.text = item.deviceName
            // 设备数据大小
            val sizeText = FileStorageUtils.formatSize(item.size)
            tvDataSize.text = sizeText
            // 设备图标
            ivDeviceIcon.loadSkuIcon(context, item.sku, item.spec)

            // 复选框状态
            ivSelectIcon.setImageDrawable(ResUtil.getDrawable(if (item.isSelected) R.mipmap.new_public_icon_choose_type_choose else R.mipmap.new_public_icon_choose_type_unchoose))
            // 0数据设备不可选择
            val isSelectable = FileStorageUtils.isGreaterThanZeroMB(item.size)
            ivSelectIcon.visibleOrInvisible(isSelectable)
            // 点击事件
            if (isSelectable) {
                root.clickDelay {
                    onDeviceClick(item)
                }
                ivSelectIcon.setOnClickListener {
                    onDeviceClick(item)
                }
            } else {
                root.setOnClickListener(null)
                ivSelectIcon.setOnClickListener(null)
            }
            viewLine.visibleOrInvisible(data.size > 1)
        }
    }
}
