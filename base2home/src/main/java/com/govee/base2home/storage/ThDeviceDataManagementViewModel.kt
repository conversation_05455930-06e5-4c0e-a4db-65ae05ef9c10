package com.govee.base2home.storage

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.govee.base2home.storage.model.DeviceDataModel
import com.govee.mvvm.base.viewmodel.BaseViewModel
import com.ihoment.base2app.infra.SafeLog
import kotlinx.coroutines.launch

/**
 * author  : sinrow
 * time    : 2025/07/17
 * version : 1.0.0
 * desc    : 温湿度计设备数据管理ViewModel
 */
class ThDeviceDataManagementViewModel : BaseViewModel() {
    override var TAG = "STORAGE_MGR"

    // 设备列表
    private val _deviceList = MutableLiveData<List<DeviceDataModel>>()
    val deviceList: LiveData<List<DeviceDataModel>> = _deviceList

    // 选中的设备列表
    private val _selectedDevices = MutableLiveData<List<DeviceDataModel>>()
    val selectedDevices: LiveData<List<DeviceDataModel>> = _selectedDevices

    // 是否全选状态
    private val _isAllSelected = MutableLiveData<Boolean>()
    val isAllSelected: LiveData<Boolean> = _isAllSelected

    // 清理结果
    private val _clearResult = MutableLiveData<Boolean>()
    val clearResult: LiveData<Boolean> = _clearResult

    // 加载状态
    private val _isLoading = MutableLiveData<Boolean>()
    val isLoading: LiveData<Boolean> = _isLoading

    // 错误信息
    private val _errorMessage = MutableLiveData<String>()
    val errorMessage: LiveData<String> = _errorMessage

    private val selectedDeviceSet = mutableSetOf<String>()

    /**
     * 切换设备选中状态
     */
    fun toggleDeviceSelection(device: DeviceDataModel) {
        // 0数据设备不可选择
        if (!FileStorageUtils.isGreaterThanZeroMB(device.size)) {
            return
        }

        val deviceKey = "${device.sku}_${device.device}"
        if (selectedDeviceSet.contains(deviceKey)) {
            selectedDeviceSet.remove(deviceKey)
        } else {
            selectedDeviceSet.add(deviceKey)
        }
        updateSelectedDevices()
        updateAllSelectedStatus()
    }

    /**
     * 全选/取消全选
     */
    fun toggleSelectAll() {
        val devices = _deviceList.value ?: return
        val selectableDevices = devices.filter { FileStorageUtils.isGreaterThanZeroMB(it.size) }

        if (_isAllSelected.value == true) {
            // 取消全选
            selectedDeviceSet.clear()
        } else {
            // 全选
            selectedDeviceSet.clear()
            selectableDevices.forEach { device ->
                selectedDeviceSet.add("${device.sku}_${device.device}")
            }
        }
        updateSelectedDevices()
        updateAllSelectedStatus()
    }

    /**
     * 清理选中设备的数据
     */
    fun clearSelectedDevicesData(selPeriodType: Int) {
        val selected = _selectedDevices.value
        if (selected.isNullOrEmpty()) {
            _errorMessage.value = "请选择要清理的设备"
            return
        }
        if (selPeriodType == -1) {
            _errorMessage.value = "请选择要保留的数据类型"
            return
        }
        SafeLog.i(TAG) { "clearSelectedDevicesData() selPeriodType = $selPeriodType" }
        SafeLog.i(TAG) { "clearSelectedDevicesData() selected = ${selected.map { it.device }}" }
        viewModelScope.launch {
            try {
                _isLoading.value = true
                val success = StorageManagerService.clearThDeviceData(selected, selPeriodType)
                _clearResult.value = success

                if (success) {
                    selectedDeviceSet.clear()
                    // 清理成功后重新加载数据
                    val updatedDevices = StorageManagerService.getThDeviceData()
                    _deviceList.value = updatedDevices
                    updateSelectedDevices()
                    updateAllSelectedStatus()

                    // 更新缓存并通知主页面刷新
                    ThDeviceDataCache.updateDeviceList(updatedDevices, notify = true)
                    SafeLog.i(TAG) { "清理成功，已更新缓存并通知主页面刷新，设备数量=${updatedDevices.size}" }
                }
            } catch (e: Exception) {
                SafeLog.e(TAG) { "clearSelectedDevicesData error: ${e.message}" }
                _clearResult.value = false
                _errorMessage.value = "清理设备数据失败：${e.message}"
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * 更新选中设备列表
     */
    private fun updateSelectedDevices() {
        val devices = _deviceList.value ?: return
        val selected = devices.filter { device ->
            selectedDeviceSet.contains("${device.sku}_${device.device}")
        }.map { it.copy(isSelected = true) }
        _selectedDevices.value = selected

        // 触发设备列表更新以刷新UI选中状态
        _deviceList.value = devices
    }

    /**
     * 更新全选状态
     */
    private fun updateAllSelectedStatus() {
        val devices = _deviceList.value ?: return
        val selectableDevices = devices.filter { FileStorageUtils.isGreaterThanZeroMB(it.size) }
        val selectedCount = _selectedDevices.value?.size ?: 0
        _isAllSelected.value = selectableDevices.isNotEmpty() && selectedCount == selectableDevices.size
    }

    /**
     * 获取选中设备数量
     */
    fun getSelectedDeviceCount(): Int {
        return _selectedDevices.value?.size ?: 0
    }

    /**
     * 检查是否有选中的设备
     */
    fun hasSelectedDevices(): Boolean {
        return getSelectedDeviceCount() > 0
    }

    /**
     * 获取可选择的设备数量
     */
    fun getSelectableDeviceCount(): Int {
        val devices = _deviceList.value ?: return 0
        return devices.count { FileStorageUtils.isGreaterThanZeroMB(it.size) }
    }

    /**
     * 检查设备是否被选中
     */
    fun isDeviceSelected(device: DeviceDataModel): Boolean {
        return selectedDeviceSet.contains("${device.sku}_${device.device}")
    }

    /**
     * 获取设备列表（包含选中状态）
     */
    fun getDeviceListWithSelection(): List<DeviceDataModel> {
        val devices = _deviceList.value ?: return emptyList()
        return devices.map { device ->
            device.copy(isSelected = isDeviceSelected(device))
        }
    }

    fun setDeviceList(list: List<DeviceDataModel>) {
        _deviceList.value = list
    }

    /**
     * 检查所有设备数据是否都为0
     */
    fun isAllDeviceDataEmpty(): Boolean {
        val deviceList = _deviceList.value ?: return false
        if (deviceList.isEmpty()) return false

        val hasAnyDataGreaterThanZero = deviceList.any { FileStorageUtils.isGreaterThanZeroMB(it.size) }
        return !hasAnyDataGreaterThanZero
    }
}
