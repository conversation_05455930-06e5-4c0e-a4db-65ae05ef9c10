package com.govee.base2home.storage

import android.os.Environment
import android.os.StatFs
import androidx.annotation.WorkerThread
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.infra.SafeLog
import java.io.File
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * author  : sinrow
 * time    : 2025/07/17
 * version : 1.0.0
 * desc    : 文件存储工具类，提供文件大小计算、目录清理、大小格式化、存储路径管理等工具方法。
 */
object FileStorageUtils {
    private const val TAG = "STORAGE_MGR"

    /**
     * 获取指定目录的文件大小（字节）
     * @param path 目录路径
     * @return 目录下所有文件的总大小（字节）
     */
    @WorkerThread
    fun getDirectorySize(path: String): Long {
        return try {
            val file = File(path)
            if (!file.exists()) {
                SafeLog.i(TAG) { "目录不存在: $path" }
                0L
            } else {
                val size = calculateDirectorySize(file)
                size
            }
        } catch (e: Exception) {
            SafeLog.e(TAG) { "getDirectorySize error: ${e.message}" }
            0L
        }
    }

    /**
     * 递归计算目录大小
     */
    private fun calculateDirectorySize(file: File): Long {
        var size = 0L
        try {
            if (file.isFile) {
                size = file.length()
            } else if (file.isDirectory) {
                val files = file.listFiles()
                if (files != null) {
                    for (childFile in files) {
                        size += calculateDirectorySize(childFile)
                    }
                }
            }
        } catch (e: Exception) {
            SafeLog.e(TAG) { "calculateDirectorySize error: ${e.message}" }
        }
        return size
    }

    /**
     * 获取多个目录的总大小
     */
    @WorkerThread
    fun getMultipleDirectoriesSize(paths: List<String>): Long {
        var totalSize = 0L
        for (path in paths) {
            totalSize += getDirectorySize(path)
        }
        return totalSize
    }

    /**
     * 删除指定目录下的所有文件
     */
    @WorkerThread
    fun clearDirectory(path: String): Boolean {
        SafeLog.i(TAG) { "开始清除目录: $path" }
        return try {
            val file = File(path)
            if (file.exists() && file.isDirectory) {
                val success = deleteDirectoryContents(file)
                SafeLog.i(TAG) { "目录清除完成: $path, 成功: $success" }
                success
            } else {
                SafeLog.i(TAG) { "目录不存在或不是目录: $path, 无需清除" }
                true
            }
        } catch (e: Exception) {
            SafeLog.e(TAG) { "clearDirectory error: ${e.message}" }
            false
        }
    }

    /**
     * 递归删除目录内容
     */
    private fun deleteDirectoryContents(directory: File): Boolean {
        SafeLog.i(TAG) { "开始递归删除目录内容: ${directory.absolutePath}" }
        try {
            val files = directory.listFiles()
            if (files != null) {
                for (file in files) {
                    if (file.isDirectory) {
                        deleteDirectoryContents(file)
                        file.delete()
                    } else {
                        file.delete()
                    }
                }
            }
            SafeLog.i(TAG) { "目录内容删除完成: ${directory.absolutePath}" }
            return true
        } catch (e: Exception) {
            SafeLog.e(TAG) { "deleteDirectoryContents error: ${e.message}" }
            return false
        }
    }

    /**
     * 获取设备总存储空间
     */
    fun getDeviceTotalStorage(): Long {
        return try {
            val stat = StatFs(Environment.getDataDirectory().path)
            val totalBytes = stat.blockCountLong * stat.blockSizeLong
            totalBytes
        } catch (e: Exception) {
            SafeLog.e(TAG) { "getDeviceTotalStorage error: ${e.message}" }
            0L
        }
    }

    /**
     * 格式化文件大小显示
     * @param sizeInBytes 文件大小（字节）
     * @return 格式化后的大小字符串，最小单位MB，适用时显示GB，保留2位小数
     */
    fun formatSize(sizeInBytes: Long): String {
        if (sizeInBytes <= 0) {
            return "0.00 MB"
        }
        // 使用 BigDecimal 避免精度损失
        val bytes = BigDecimal(sizeInBytes)
        val gbThreshold = BigDecimal(1024 * 1024 * 1024)
        val mbDivisor = BigDecimal(1024 * 1024)

        return if (bytes >= gbThreshold) {
            // 大于等于1GB，显示GB
            val sizeInGB = bytes.divide(gbThreshold, 2, RoundingMode.HALF_UP)
            "${sizeInGB.toPlainString()} GB"
        } else {
            // 小于1GB，显示MB
            val sizeInMB = bytes.divide(mbDivisor, 2, RoundingMode.HALF_UP)
            "${sizeInMB.toPlainString()} MB"
        }
    }

    /**
     * 获取应用数据目录路径
     */
    fun getAppDataPath(): String {
        val appDataPath = BaseApplication.getContext().filesDir.parent ?: ""
        return appDataPath
    }

    /**
     * 获取外部缓存目录路径
     */
    fun getExternalCachePath(): String {
        val externalCachePath = BaseApplication.getContext().externalCacheDir?.absolutePath ?: ""
        return externalCachePath
    }

    /**
     * 获取外部文件目录路径
     */
    fun getExternalFilesPath(): String {
        val externalFilesPath = BaseApplication.getContext().getExternalFilesDir(null)?.absolutePath ?: ""
        return externalFilesPath
    }

    /**
     * 计算百分比
     * @param size 当前大小（字节）
     * @param total 总大小（字节）
     * @return 百分比值
     */
    fun calculatePercentage(size: Long, total: Long): Double {
        SafeLog.i(TAG) { "开始计算百分比: size=${formatSize(size)}, total=${formatSize(total)}" }
        val percentage = if (total > 0) {
            // 使用 BigDecimal 避免精度损失
            val sizeDecimal = BigDecimal(size)
            val totalDecimal = BigDecimal(total)
            val hundred = BigDecimal(100)
            val result = sizeDecimal.divide(totalDecimal, 4, RoundingMode.HALF_UP)
                .multiply(hundred)
            result.toDouble()
        } else {
            0.0
        }
        SafeLog.i(TAG) { "百分比计算完成: ${"%.2f".format(percentage)}%" }
        return percentage
    }


    /**
     * 检查大小是否大于0MB（四舍五入后）
     * @param bytes 字节数
     * @return 是否大于0MB
     */
    fun isGreaterThanZeroMB(bytes: Long): Boolean {
        if (bytes <= 0) return false

        // 使用 BigDecimal 避免精度损失
        val bytesDecimal = BigDecimal(bytes)
        val mbDivisor = BigDecimal(1024 * 1024)
        val sizeInMB = bytesDecimal.divide(mbDivisor, 2, RoundingMode.HALF_UP)
        return sizeInMB > BigDecimal.ZERO
    }

    /**
     * 删除目录下所有超过指定天数的文件，并打印被删除文件的全路径
     * @param path 目录路径
     * @param expire 到期时间 ms
     * @return 实际删除的文件数
     */
    fun clearFilesOlderThanDays(path: String, expire: Long): Int {
        val dir = File(path)
        if (!dir.exists() || !dir.isDirectory) return 0
        val now = System.currentTimeMillis()
        var deletedCount = 0
        dir.listFiles()?.forEach { file ->
            try {
                if (file.isFile && (now - file.lastModified() > expire)) {
                    val deleted = file.delete()
                    if (deleted) {
                        deletedCount++
                        SafeLog.i(TAG) { "已删除超期缓存文件: ${file.absolutePath}" }
                    } else {
                        SafeLog.e(TAG) { "删除超期缓存文件失败: ${file.absolutePath}" }
                    }
                }
            } catch (e: Exception) {
                SafeLog.e(TAG) { "清理超期缓存文件异常: ${file.absolutePath}, err=${e.message}" }
            }
        }
        return deletedCount
    }
}
