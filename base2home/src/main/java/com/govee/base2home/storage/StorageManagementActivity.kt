package com.govee.base2home.storage

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import com.govee.base2home.databinding.ActivityCacheManagementBinding
import com.govee.base2home.storage.model.AppMemoryOverviewModel
import com.govee.base2home.storage.model.CacheDataModel
import com.govee.base2home.storage.model.DeviceDataModel
import com.govee.base2kt.ac.AbsAc
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.clickWithTrigger
import com.govee.base2kt.ext.toast
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.ui.R
import com.govee.ui.dialog.ConfirmDialog
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil

/**
 * author  : sinrow
 * time    : 2025/07/17
 * version : 1.0.0
 * desc    : 存储管理主界面，提供应用内存概览、缓存管理、设备数据管理等功能入口。
 */
class StorageManagementActivity : AbsAc<ActivityCacheManagementBinding>() {
    override var TAG = "STORAGE_MGR"

    private val viewModel: StorageManagementViewModel by lazy {
        ViewModelProvider(this)[StorageManagementViewModel::class.java]
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        initObserver()
        initClickListeners()
        loadData()
        ThDeviceDataCache.deviceListChanged.observe(this) {
            SafeLog.i(TAG) { "收到温湿度设备数据变化通知，开始刷新主页面数据" }
            viewModel.refreshThDeviceData()
        }
    }

    override fun onResume() {
        super.onResume()
        // 从设备管理页面返回时刷新数据，启动新的数据会话
        SafeLog.i(TAG) { "页面恢复，重新加载数据" }
        viewModel.loadAllData()
    }

    override fun getAcContentRootViewId(): Int = com.govee.base2home.R.id.ac_container
    override fun adapterInsetViewId(): Int = com.govee.base2home.R.id.top_flag
    override fun layoutId(): Int = com.govee.base2home.R.layout.activity_cache_management


    private fun initObserver() {
        // 观察内存概览数据
        viewModel.memoryOverview.observe(this) { overview ->
            updateMemoryOverview(overview)
        }

        // 观察临时缓存数据
        viewModel.tempCacheData.observe(this) { cacheList ->
            updateTempCacheData(cacheList)
        }

        // 观察温湿度设备数据
        viewModel.thDeviceData.observe(this) { deviceList ->
            updateThDeviceData(deviceList)
        }

        // 观察系统数据
        viewModel.systemData.observe(this) { systemList ->
            updateSystemData(systemList)
        }

        // 观察清理结果
        viewModel.clearResult.observe(this) { success ->
            if (success) {
                toast(ResUtil.getString(R.string.base2home_management_clear_suc))
            } else {
                toast(ResUtil.getString(R.string.base2home_management_clear_fail))
            }
        }

        // 观察自动清理开关状态
        viewModel.autoCleanEnabled.observe(this) { enabled ->
            viewBinding.ivAutoClean.isSelected = enabled
        }

        // 观察加载状态
        viewModel.isLoading.observe(this) { isLoading ->
            viewBinding.llContent.visibleByBoolean(!isLoading)
            viewBinding.progressBar.visibleByBoolean(isLoading)
            viewBinding.tvLoadingHint.visibleByBoolean(isLoading)
        }

        // 观察错误信息
        viewModel.errorMessage.observe(this) { message ->
            if (message.isNotEmpty()) {
                SafeLog.e(TAG) { "initObserver() errorMessage = $message" }
            }
        }
        // 观察toast消息
        viewModel.toastMsg.observe(this) { message ->
            if (message.isNotEmpty()) {
                toast(message)
                SafeLog.i(TAG) { "initObserver() 观察toast消息 = $message" }
            }
        }
    }

    private fun initClickListeners() {
        viewBinding.run {
            // 返回按钮
            back.clickDelay {
                finish()
            }

            // 清理缓存按钮
            btnClearCache.clickDelay {
                val isClearable = viewModel.isTempCacheClearable()
                if (isClearable) {
                    showClearCacheConfirmDialog()
                } else {
                    toast(R.string.base2home_storage_no_data_to_clean)
                }
            }

            // 自动清理开关
            ivAutoClean.clickWithTrigger {
                val selected = ivAutoClean.isSelected
                viewModel.setAutoCleanEnabled(!selected)
                if (!selected) {
                    viewModel.clearTempCache(true)
                }
            }

            // 管理设备数据按钮
            tvManageDeviceData.clickDelay {
                val isManageable = viewModel.isThDeviceDataManageable()
                if (isManageable) {
                    JumpUtil.jump(this@StorageManagementActivity, ThDeviceDataManagementActivity::class.java)
                } else {
                    toast(R.string.base2home_storage_no_data_to_clean)
                }
            }
        }
    }

    private fun loadData() {
        viewModel.loadAllData()
    }

    private fun updateMemoryOverview(overview: AppMemoryOverviewModel) {
        viewBinding.run {
            tvTotalSize.text = FileStorageUtils.formatSize(overview.totalSize)
            val percentageText =
                ResUtil.getStringFormat(R.string.base2home_storage_percentage, "${"%.2f".format(overview.percentage)}%")
            tvStoragePercentage.text = percentageText
        }
    }

    private fun updateTempCacheData(cacheList: List<CacheDataModel>) {
        val totalSize = cacheList.sumOf { it.size }
        val sizeText = FileStorageUtils.formatSize(totalSize)
        viewBinding.tvTempCacheSize.text = sizeText

        // 更新清理按钮状态
        val isClearable = viewModel.isTempCacheClearable()
        viewBinding.btnClearCache.alpha = if (isClearable) 1.0f else 0.5f
    }

    private fun updateThDeviceData(deviceList: List<DeviceDataModel>) {
        val hasDevices = deviceList.isNotEmpty()
        viewBinding.containerThDeviceData.visibleByBoolean(hasDevices)
        viewBinding.viewTHLine.visibleByBoolean(hasDevices)

        if (hasDevices) {
            val totalSize = viewModel.getThDeviceDataTotalSize()
            val sizeText = FileStorageUtils.formatSize(totalSize)
            viewBinding.tvThDeviceCacheSize.text = sizeText

            // 更新管理按钮状态
            val isManageable = viewModel.isThDeviceDataManageable()
            viewBinding.tvManageDeviceData.alpha = if (isManageable) 1.0f else 0.5f
        }
    }

    private fun updateSystemData(systemList: List<CacheDataModel>) {
        val totalSize = systemList.sumOf { it.size }
        val sizeText = FileStorageUtils.formatSize(totalSize)
        viewBinding.tvSystemDataSize.text = sizeText
    }

    private fun showClearCacheConfirmDialog() {
        val contentStr = ResUtil.getString(R.string.base2home_management_clear_data_all)
        val cancelStr = ResUtil.getString(R.string.cancel)
        val doneStr = ResUtil.getString(R.string.confirm)
        ConfirmDialog.showConfirmDialog(this, contentStr, cancelStr, doneStr) {
            viewModel.clearTempCache(false)
        }
    }

    override fun finish() {
        ThDeviceDataCache.clear()
        super.finish()
    }
}
