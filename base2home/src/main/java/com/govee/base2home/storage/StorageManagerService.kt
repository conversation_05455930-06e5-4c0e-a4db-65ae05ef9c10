package com.govee.base2home.storage

import androidx.annotation.WorkerThread
import com.govee.base2home.Constant4L5
import com.govee.base2home.community.video.club.VideoUtils
import com.govee.base2home.constant.PathBaseHome
import com.govee.base2home.main.DeviceList
import com.govee.base2home.sku.WifiPswConfig
import com.govee.base2home.storage.model.AppMemoryOverviewModel
import com.govee.base2home.storage.model.CacheDataModel
import com.govee.base2home.storage.model.CacheType
import com.govee.base2home.storage.model.DeviceDataModel
import com.govee.base2home.util.ExoPlayerCacheUtils
import com.govee.base2home.util.GlideCacheUtil
import com.govee.base2home.util.SchemeUtils
import com.govee.cache.GlobalCache
import com.govee.cache.key.SmartCacheKey
import com.govee.home.account.config.AccountConfig
import com.ihoment.base2app.BaseApplication
import com.ihoment.base2app.infra.SafeLog
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * author  : sinrow
 * time    : 2025/07/17
 * version : 1.0.0
 * desc    : 存储管理服务类，负责所有存储相关的业务逻辑，包括缓存数据获取、清理、自动清理设置、温湿度设备数据处理等。
 */
object StorageManagerService {
    private const val TAG = "STORAGE_MGR"

    // SharedPreferences键名
    private const val SP_AUTO_CLEAN_ENABLED = "auto_clean_cache_enabled"
    private const val SP_AUTO_CLEAN_TIMESTAMP = "auto_clean_cache_timestamp"

    /**
     * 自动清理缓存的过期（毫秒级），七天
     */
    //private const val SEVEN_DAYS_MILLIS = 7 * 24 * 60 * 60 * 1000L
    private const val SEVEN_DAYS_MILLIS = 2 * 60 * 1000L// 临时（方便测试）

    // 会话级缓存机制 - 仅在单次数据加载中有效，避免清理操作后的数据不一致
    private var currentDataSession = 0L
    private var sessionCache = mutableMapOf<String, Long>() // path -> size

    // 存储路径常量 - 统一管理所有存储路径，方便维护和修改
    private object StoragePaths {
        // 基础路径
        val appDataPath: String get() = FileStorageUtils.getAppDataPath()
        val externalCachePath: String get() = FileStorageUtils.getExternalCachePath()
        val externalFilesPath: String get() = FileStorageUtils.getExternalFilesPath()

        // 视频缓存路径
        val videoCachePaths: List<String>
            get() = listOf(
                "$externalCachePath/video-cache",
                "$externalCachePath/media_cache",
                "$externalFilesPath/Movies"
            )

        // 图片缓存路径
        val imageCachePaths: List<String>
            get() = listOf(
                "$appDataPath/cache/image_manager_disk_cache",
                "$appDataPath/cache/glide_cache"
            )

        // Web资源缓存路径
        val webCachePaths: List<String>
            get() = listOf(
                "$appDataPath/cache/WebView"
            )

        // 数据库缓存路径
        val databasePaths: List<String>
            get() = listOf(
                "$appDataPath/files/objectbox"
            )

        // 动态SO库路径
        val soPaths: List<String>
            get() = listOf(
                "$appDataPath/files/GNative"
            )

        // Web优化资源路径
        val webOptPaths: List<String>
            get() = listOf(
                "$appDataPath/app_web_resources"
            )

        // 系统功能路径
        val systemPaths: List<String>
            get() = listOf(
                "$appDataPath/files/prodexdir"
            )

        // 单个清理路径
        val webViewCacheDir: String get() = "$appDataPath/cache/WebView"
    }

    /**
     * 开始新的数据加载会话
     * 每次用户进入页面或刷新数据时调用，清除旧缓存
     */
    fun startNewDataSession() {
        currentDataSession = System.currentTimeMillis()
        sessionCache.clear()
        SafeLog.i(TAG) { "开始新的数据加载会话: $currentDataSession" }
    }

    /**
     * 清除缓存（在清理操作后调用）
     * 确保清理操作后的数据是最新的
     */
    fun invalidateCache() {
        sessionCache.clear()
        SafeLog.i(TAG) { "缓存已失效，清理操作后数据将重新计算" }
    }

    /**
     * 获取目录大小（会话级缓存）
     * 仅在同一个数据加载会话中缓存，避免清理操作后的数据不一致
     */
    private fun getSessionCachedDirectorySize(path: String): Long {
        return sessionCache[path] ?: run {
            val size = FileStorageUtils.getDirectorySize(path)
            sessionCache[path] = size
            SafeLog.d(TAG) { "计算目录大小并缓存: $path -> ${FileStorageUtils.formatSize(size)}" }
            size
        }
    }

    /**
     * 获取多个目录的总大小（会话级缓存）
     */
    private fun getSessionCachedMultipleDirectoriesSize(paths: List<String>): Long {
        return paths.sumOf { getSessionCachedDirectorySize(it) }
    }

    /**
     * 获取应用内存概览
     * @return AppMemoryOverviewModel 应用内存占用概览数据
     */
    @WorkerThread
    suspend fun getAppMemoryOverview(): AppMemoryOverviewModel = withContext(Dispatchers.IO) {
        SafeLog.i(TAG) { "开始获取应用内存概览" }

        // 计算总占用空间
        val totalSize = FileStorageUtils.getDirectorySize(StoragePaths.appDataPath) +
            FileStorageUtils.getDirectorySize(StoragePaths.externalCachePath) +
            FileStorageUtils.getDirectorySize(StoragePaths.externalFilesPath)

        // 获取设备总存储空间
        val deviceTotalStorage = FileStorageUtils.getDeviceTotalStorage()

        // 计算百分比
        val percentage = FileStorageUtils.calculatePercentage(totalSize, deviceTotalStorage)
        SafeLog.i(TAG) {
            "应用内存概览获取完成: totalSize=${FileStorageUtils.formatSize(totalSize)}, deviceTotalStorage=${
                FileStorageUtils.formatSize(
                    deviceTotalStorage
                )
            }, percentage=${"%.2f".format(percentage)}%"
        }
        AppMemoryOverviewModel(totalSize, deviceTotalStorage, percentage)
    }

    /**
     * 获取临时缓存数据
     */
    @WorkerThread
    suspend fun getTempCacheData(): List<CacheDataModel> = withContext(Dispatchers.IO) {

        SafeLog.i(TAG) { "getTempCacheData() thDeviceTotalSize = $thDeviceTotalSize" }
        val cacheList = mutableListOf<CacheDataModel>()

        // 视频缓存
        val videoSize = FileStorageUtils.getMultipleDirectoriesSize(StoragePaths.videoCachePaths)
        cacheList.add(CacheDataModel(CacheType.VIDEO_CACHE, "视频缓存", videoSize, StoragePaths.videoCachePaths))

        // 图片缓存
        val imageSize = FileStorageUtils.getMultipleDirectoriesSize(StoragePaths.imageCachePaths)
        cacheList.add(CacheDataModel(CacheType.IMAGE_CACHE, "图片缓存", imageSize, StoragePaths.imageCachePaths))

        // Web资源缓存（排除H5优化资源）
        val webSize = FileStorageUtils.getMultipleDirectoriesSize(StoragePaths.webCachePaths)
        cacheList.add(CacheDataModel(CacheType.WEB_RESOURCE, "Web资源缓存", webSize, StoragePaths.webCachePaths))

        // 数据库缓存（排除当前用户温湿度数据、其他温湿度数据和基础开销）
        val dbSize = FileStorageUtils.getMultipleDirectoriesSize(StoragePaths.databasePaths)

        // 计算当前用户的温湿度数据大小
        val currentUserThSize = getThDeviceTotalSize()
        // 计算全部温湿度数据大小
        val allThDataSize = getAllThDataSize()
        val otherThDataSize = maxOf(0L, allThDataSize - currentUserThSize)

        val clearableDbCacheSize = maxOf(0L, dbSize - currentUserThSize - otherThDataSize)

        SafeLog.i(TAG) {
            "数据库缓存计算: 总大小=${FileStorageUtils.formatSize(dbSize)}, 当前用户温湿度=${
                FileStorageUtils.formatSize(
                    currentUserThSize
                )
            }, 其他温湿度数据=${FileStorageUtils.formatSize(otherThDataSize)} 可清理=${FileStorageUtils.formatSize(clearableDbCacheSize)}"
        }

        if (clearableDbCacheSize > 0) {
            cacheList.add(CacheDataModel(CacheType.DATABASE_CACHE, "数据库缓存", clearableDbCacheSize, StoragePaths.databasePaths))
        }

        cacheList
    }

    /**
     * 获取临时缓存数据（完全优化版本：使用传入的温湿度设备总大小和全部温湿度数据大小，避免所有重复获取）
     */
    @WorkerThread
    suspend fun getTempCacheDataOptimized(thDeviceTotalSize: Long, allThDataSize: Long): List<CacheDataModel> =
        withContext(Dispatchers.IO) {

            SafeLog.i(TAG) { "getTempCacheDataOptimized() 使用传入的数据，避免重复获取" }
            val cacheList = mutableListOf<CacheDataModel>()

            // 视频缓存（使用会话级缓存）
            val videoSize = getSessionCachedMultipleDirectoriesSize(StoragePaths.videoCachePaths)
            cacheList.add(CacheDataModel(CacheType.VIDEO_CACHE, "视频缓存", videoSize, StoragePaths.videoCachePaths))

            // 图片缓存（使用会话级缓存）
            val imageSize = getSessionCachedMultipleDirectoriesSize(StoragePaths.imageCachePaths)
            cacheList.add(CacheDataModel(CacheType.IMAGE_CACHE, "图片缓存", imageSize, StoragePaths.imageCachePaths))

            // Web资源缓存（使用会话级缓存）
            val webSize = getSessionCachedMultipleDirectoriesSize(StoragePaths.webCachePaths)
            cacheList.add(CacheDataModel(CacheType.WEB_RESOURCE, "Web资源缓存", webSize, StoragePaths.webCachePaths))

            // 数据库缓存（排除当前用户温湿度数据、其他温湿度数据和基础开销）
            val dbSize = FileStorageUtils.getMultipleDirectoriesSize(StoragePaths.databasePaths)

            // 使用传入的数据，避免重复获取
            val currentUserThSize = thDeviceTotalSize
            val otherThDataSize = maxOf(0L, allThDataSize - currentUserThSize)

            val clearableDbCacheSize = maxOf(0L, dbSize - currentUserThSize - otherThDataSize)

            SafeLog.i(TAG) {
                "数据库缓存计算: 总大小=${FileStorageUtils.formatSize(dbSize)}, 当前用户温湿度=${
                    FileStorageUtils.formatSize(
                        currentUserThSize
                    )
                }, 其他温湿度数据=${FileStorageUtils.formatSize(otherThDataSize)} 可清理=${
                    FileStorageUtils.formatSize(
                        clearableDbCacheSize
                    )
                }"
            }
            if (clearableDbCacheSize > 0) {
                cacheList.add(CacheDataModel(CacheType.DATABASE_CACHE, "数据库缓存", clearableDbCacheSize, StoragePaths.databasePaths))
            }
            cacheList
        }

    /**
     * 获取系统数据（确保数据一致性）
     * 系统数据 = 总大小 - 临时缓存 - 当前用户温湿度设备数据
     * @param totalAppSize 应用总大小
     * @param tempCacheSize 临时缓存总大小
     * @param currentUserThSize 当前用户温湿度设备数据大小
     */
    @WorkerThread
    suspend fun getSystemData(
        totalAppSize: Long,
        tempCacheSize: Long,
        currentUserThSize: Long
    ): List<CacheDataModel> = withContext(Dispatchers.IO) {
        val cacheList = mutableListOf<CacheDataModel>()

        // 计算系统数据总大小：确保数据一致性
        // 系统数据 = 总大小 - 临时缓存 - 当前用户温湿度设备数据
        val systemDataTotalSize = totalAppSize - tempCacheSize - currentUserThSize

        SafeLog.i(TAG) {
            "系统数据计算: 总大小=${FileStorageUtils.formatSize(totalAppSize)}, " +
                    "临时缓存=${FileStorageUtils.formatSize(tempCacheSize)}, " +
                    "当前用户温湿度=${FileStorageUtils.formatSize(currentUserThSize)}, " +
                    "系统数据=${FileStorageUtils.formatSize(systemDataTotalSize)}"
        }

        // 获取各个系统数据组件的实际大小（用于详细显示）
        val soSize = getSessionCachedMultipleDirectoriesSize(StoragePaths.soPaths)
        val webOptSize = getSessionCachedMultipleDirectoriesSize(StoragePaths.webOptPaths)
        val systemFunctionSize = getSessionCachedMultipleDirectoriesSize(StoragePaths.systemPaths)

        // 计算其他温湿度数据大小
        val allThDataSize = getAllThDataSize()
        val otherThDataSize = maxOf(0L, allThDataSize - currentUserThSize)

        // 计算核心应用数据大小（确保总和一致）
        val knownSystemDataSize = soSize + webOptSize + systemFunctionSize + otherThDataSize
        val coreAppDataSize = maxOf(0L, systemDataTotalSize - knownSystemDataSize)

        SafeLog.i(TAG) {
            "系统数据详细: SO库=${FileStorageUtils.formatSize(soSize)}, " +
                    "Web优化=${FileStorageUtils.formatSize(webOptSize)}, " +
                    "系统功能=${FileStorageUtils.formatSize(systemFunctionSize)}, " +
                    "其他温湿度=${FileStorageUtils.formatSize(otherThDataSize)}, " +
                    "核心应用=${FileStorageUtils.formatSize(coreAppDataSize)}, " +
                    "系统数据总计=${FileStorageUtils.formatSize(systemDataTotalSize)}"
        }

        // 添加各个系统数据项
        cacheList.add(CacheDataModel(CacheType.DYNAMIC_SO, "动态SO库", soSize, StoragePaths.soPaths))
        cacheList.add(CacheDataModel(CacheType.WEB_RESOURCE, "Web优化资源", webOptSize, StoragePaths.webOptPaths))
        cacheList.add(CacheDataModel(CacheType.SYSTEM_FUNCTION, "系统功能", systemFunctionSize, StoragePaths.systemPaths))

        if (otherThDataSize > 0) {
            val isHadToken = AccountConfig.read().isHadToken
            val description = if (!isHadToken) {
                "历史账号的温湿度数据（不可清除）"
            } else {
                "其他账号的温湿度数据（不可清除）"
            }
            cacheList.add(CacheDataModel(CacheType.TH_DEVICE_DATA, "历史温湿度数据", otherThDataSize, listOf(description)))
        }

        if (coreAppDataSize > 0L) {
            cacheList.add(
                CacheDataModel(
                    CacheType.SYSTEM_FUNCTION,
                    "核心应用数据",
                    coreAppDataSize,
                    listOf("数据库、配置文件等（不可清除）")
                )
            )
        }
        cacheList
    }

    /**
     * 获取系统数据（完全优化版本：使用传入的数据，避免所有重复获取）
     * @param totalAppSize 应用总大小
     * @param tempCacheSize 临时缓存总大小
     * @param thDeviceTotalSize 温湿度设备总大小
     * @param allThDataSize 全部温湿度数据大小
     */
    @WorkerThread
    suspend fun getSystemDataOptimized(
        totalAppSize: Long = 0L,
        tempCacheSize: Long = 0L,
        thDeviceTotalSize: Long,
        allThDataSize: Long
    ): List<CacheDataModel> = withContext(Dispatchers.IO) {
        val cacheList = mutableListOf<CacheDataModel>()

        // 动态SO库
        val soSize = FileStorageUtils.getMultipleDirectoriesSize(StoragePaths.soPaths)
        cacheList.add(CacheDataModel(CacheType.DYNAMIC_SO, "动态SO库", soSize, StoragePaths.soPaths))

        // Web优化资源
        val webOptSize = FileStorageUtils.getMultipleDirectoriesSize(StoragePaths.webOptPaths)
        cacheList.add(CacheDataModel(CacheType.WEB_RESOURCE, "Web优化资源", webOptSize, StoragePaths.webOptPaths))

        // 系统功能
        val systemSize = FileStorageUtils.getMultipleDirectoriesSize(StoragePaths.systemPaths)
        cacheList.add(CacheDataModel(CacheType.SYSTEM_FUNCTION, "系统功能", systemSize, StoragePaths.systemPaths))

        // 使用传入的数据，避免重复获取
        val currentUserThSize = thDeviceTotalSize
        val otherThDataSize = maxOf(0L, allThDataSize - currentUserThSize)

        SafeLog.i(TAG) {
            "温湿度数据统计: 全部数据=${FileStorageUtils.formatSize(allThDataSize)}, 当前用户设备=${
                FileStorageUtils.formatSize(
                    currentUserThSize
                )
            }, 其他数据=${FileStorageUtils.formatSize(otherThDataSize)}"
        }

        if (otherThDataSize > 0) {
            val isHadToken = AccountConfig.read().isHadToken
            val description = if (!isHadToken) {
                "历史账号的温湿度数据（不可清除）"
            } else {
                "其他账号的温湿度数据（不可清除）"
            }
            cacheList.add(CacheDataModel(CacheType.SYSTEM_FUNCTION, "历史温湿度数据", otherThDataSize, listOf(description)))
        }

        // 核心应用数据（数据库、配置文件等，不可清除）
        // 只有在提供了总大小和缓存大小时才计算差值
        if (totalAppSize > 0L && tempCacheSize >= 0L) {
            val currentSystemDataSize = soSize + webOptSize + systemSize + otherThDataSize
            val coreAppDataSize = totalAppSize - tempCacheSize - currentUserThSize - currentSystemDataSize
            if (coreAppDataSize > 0L) {
                SafeLog.i(TAG) {
                    "核心应用数据计算: 总大小=${FileStorageUtils.formatSize(totalAppSize)}, 临时缓存=${
                        FileStorageUtils.formatSize(
                            tempCacheSize
                        )
                    }, 已统计系统数据=${FileStorageUtils.formatSize(currentSystemDataSize)}, 核心数据=${
                        FileStorageUtils.formatSize(
                            coreAppDataSize
                        )
                    }"
                }
                cacheList.add(
                    CacheDataModel(
                        CacheType.SYSTEM_FUNCTION,
                        "核心应用数据",
                        coreAppDataSize,
                        listOf("数据库、配置文件等（不可清除）")
                    )
                )
            }
        }
        cacheList
    }


    /**
     * 获取温湿度设备数据
     */
    @WorkerThread
    suspend fun getThDeviceData(): List<DeviceDataModel> = withContext(Dispatchers.IO) {
        SafeLog.i(TAG) { "开始获取温湿度设备数据" }
        val deviceList = mutableListOf<DeviceDataModel>()

        try {

            // 获取当前账户的所有设备
            val allDevices = DeviceList.getDevices()
            SafeLog.i(TAG) { "获取到所有设备数量: ${allDevices.size}" }

            // 筛选温湿度设备
            var thDeviceCount = 0
            for (device in allDevices) {
                if (isTemperatureHumidityDevice(device.sku)) {
                    thDeviceCount++
                    SafeLog.i(TAG) { "找到温湿度设备: sku=${device.sku}, device=${device.device}, name=${device.deviceName}" }

                    val dataSize = getThDeviceDataSize(device.sku, device.device)
                    SafeLog.i(TAG) { "设备数据大小: sku=${device.sku}, size=${FileStorageUtils.formatSize(dataSize)}" }

                    /*sku的icon图片加载*/
                    deviceList.add(
                        DeviceDataModel(
                            sku = device.sku,
                            device = device.device,
                            deviceName = device.deviceName ?: "未知设备",
                            size = dataSize,
                            spec = device.spec
                        )
                    )
                }
            }

            SafeLog.i(TAG) { "温湿度设备筛选完成: 总设备数=${allDevices.size}, 温湿度设备数=${thDeviceCount}" }
            if (thDeviceCount > 0) {
                // 按内存大小排序（从大到小）
                deviceList.sortByDescending { it.size }
                SafeLog.i(TAG) { "温湿度设备数据排序完成，按大小从大到小排列" }
            }
        } catch (e: Exception) {
            SafeLog.e(TAG) { "getThDeviceData error: ${e.message}" }
        }

        SafeLog.i(TAG) { "温湿度设备数据获取完成: 返回设备数=${deviceList.size}" }
        deviceList
    }

    /**
     * 检查是否为温湿度设备
     */
    private fun isTemperatureHumidityDevice(sku: String): Boolean {
        return Constant4L5.supportAllThList().contains(sku)
    }

    /**
     * 获取温湿度设备数据大小
     */
    @WorkerThread
    private fun getThDeviceDataSize(sku: String, device: String): Long {
        return try {
            // 通过 ARouter 获取温湿度数据服务
            val thDataService = SchemeUtils.getService<ThDataService>(PathBaseHome.URL_TH_DATA_SERVICE)
            val memorySizeStr = thDataService?.getThDataCacheMemory(sku, device) ?: "0 MB"
            SafeLog.i(TAG) { "获取温湿度设备数据大小: sku=$sku, device=$device, size=$memorySizeStr" }
            // 解析内存大小字符串，转换为字节
            parseMemorySizeToBytes(memorySizeStr)
        } catch (e: Exception) {
            SafeLog.e(TAG) { "getThDeviceDataSize error: ${e.message}" }
            0L
        }
    }

    /**
     * 解析内存大小字符串为字节数
     */
    private fun parseMemorySizeToBytes(sizeStr: String): Long {
        return try {
            when {
                sizeStr.contains("GB") -> {
                    val value = sizeStr.replace("GB", "").trim().toDouble()
                    (value * 1024 * 1024 * 1024).toLong()
                }

                sizeStr.contains("MB") -> {
                    val value = sizeStr.replace("MB", "").trim().toDouble()
                    (value * 1024 * 1024).toLong()
                }

                sizeStr.contains("KB") -> {
                    val value = sizeStr.replace("KB", "").trim().toDouble()
                    (value * 1024).toLong()
                }

                else -> 0L
            }
        } catch (e: Exception) {
            SafeLog.e(TAG) { "parseMemorySizeToBytes() 解析错误 ${e.message}" }
            0L
        }
    }

    /**
     * 获取温湿度设备总数据大小
     */
    suspend fun getThDeviceTotalSize(): Long = withContext(Dispatchers.IO) {
        var totalSize = 0L
        try {
            val thDeviceList = getThDeviceData()
            totalSize = thDeviceList.sumOf { it.size }
            SafeLog.i(TAG) { "温湿度设备总数据大小: ${FileStorageUtils.formatSize(totalSize)}" }
        } catch (e: Exception) {
            SafeLog.e(TAG) { "getThDeviceTotalSize error: ${e.message}" }
        }
        totalSize
    }

    /**
     * 获取数据库中全部温湿度数据大小（按照clearNonThData逻辑计算所有温湿度设备数据总和）
     */
    suspend fun getAllThDataSize(): Long = withContext(Dispatchers.IO) {
        var totalSize = 0L
        try {
            val thDataService = SchemeUtils.getService<ThDataService>(PathBaseHome.URL_TH_DATA_SERVICE)
            if (thDataService != null) {
                // 使用温湿度数据服务获取所有温湿度设备数据大小总和
                totalSize = thDataService.getThAllDataSize()
                SafeLog.i(TAG) { "所有温湿度设备数据大小总和: ${FileStorageUtils.formatSize(totalSize)}" }
            }
        } catch (e: Exception) {
            SafeLog.e(TAG) { "getAllThDataSize error: ${e.message}" }
        }
        totalSize
    }

    /**
     * 清理临时缓存
     */
    @WorkerThread
    suspend fun clearTempCache(isAuto: Boolean = false): Boolean = withContext(Dispatchers.IO) {
        try {
            // 清理视频缓存
            VideoUtils.cleanVideoCacheDir()
            ExoPlayerCacheUtils.clearCacheFile()

            // 清理图片缓存
            val context = BaseApplication.getContext()
            GlideCacheUtil.clearImageDiskCache(context)

            if (!isAuto) {
                // 清理Web缓存
                FileStorageUtils.clearDirectory(StoragePaths.webViewCacheDir)

                // 清理数据库缓存（排除温湿度数据）
                // 注意：这里需要谨慎处理，避免删除重要数据

                val thDataService = SchemeUtils.getService<ThDataService>(PathBaseHome.URL_TH_DATA_SERVICE)
                thDataService?.clearNonThData()

                // 清空Wi-Fi存储的秘密
                WifiPswConfig.read().clearAll()
            }

            // 清理操作完成后，失效缓存确保数据一致性
            invalidateCache()
            SafeLog.i(TAG) { "临时缓存清理完成${if (isAuto) "(自动)" else ""}，缓存已失效" }

            true
        } catch (e: Exception) {
            SafeLog.e(TAG) { "clearTempCache error: ${e.message}" }
            false
        }
    }

    /**
     * 清理指定设备的温湿度数据
     */
    @WorkerThread
    suspend fun clearThDeviceData(devices: List<DeviceDataModel>, selPeriodType: Int): Boolean = withContext(Dispatchers.IO) {
        try {
            // 通过 ARouter 获取温湿度数据服务
            val service = SchemeUtils.getService<ThDataService>(PathBaseHome.URL_TH_DATA_SERVICE)
            if (service != null) {
                for (device in devices) {
                    service.clearByPeriodType(device.sku, device.device, selPeriodType)
                }
            } else {
                SafeLog.e(TAG) { "clearThDeviceData() ThDataService err 获取温湿度数据服务失败" }
            }

            // 清理操作完成后，失效缓存确保数据一致性
            invalidateCache()
            SafeLog.i(TAG) { "温湿度设备数据清理完成，缓存已失效" }

            true
        } catch (e: Exception) {
            SafeLog.e(TAG) { "clearThDeviceData error: ${e.message}" }
            false
        }
    }

    /**
     * 设置7天自动清理缓存开关
     */
    fun setAutoCleanEnabled(enabled: Boolean) {
        SafeLog.i(TAG) { "setAutoCleanEnabled() enabled = ${if (enabled) "设置-开启自动清理开关" else "设置-关闭自动清理开关"}" }
        GlobalCache.build().set(SmartCacheKey.caches(SP_AUTO_CLEAN_ENABLED), enabled)
        if (enabled) {
            // 记录当前时间戳
            GlobalCache.build().set(SmartCacheKey.caches(SP_AUTO_CLEAN_TIMESTAMP), System.currentTimeMillis())
        } else {
            // 清除时间戳
            GlobalCache.build().remove(SmartCacheKey.caches(SP_AUTO_CLEAN_TIMESTAMP))
        }
    }

    /**
     * 获取7天自动清理缓存开关状态
     */
    fun isAutoCleanEnabled(): Boolean {
        val auto = GlobalCache.build().getBoolean(SmartCacheKey.caches(SP_AUTO_CLEAN_ENABLED)) ?: false
        SafeLog.i(TAG) { "isAutoCleanEnabled() ${if (auto) "读取-自动清理已开启" else "读取-自动清理已关闭"}" }
        return auto
    }

    /**
     * 检查并执行自动清理（每次进入app都清除超过7天的视频/图片缓存文件）
     * 只清理超期文件，保留最新数据，提升存储利用率。
     * 清理范围：视频缓存、图片缓存。
     * 日志详细，异常不影响主流程。
     */
    @WorkerThread
    suspend fun checkAndExecuteAutoClean() = withContext(Dispatchers.IO) {
        if (!isAutoCleanEnabled()) {
            SafeLog.i(TAG) { "自动清理未开启，跳过自动清理流程" }
            return@withContext
        }
        val videoCachePaths = listOf(
            FileStorageUtils.getExternalCachePath() + "/video-cache",
            FileStorageUtils.getExternalCachePath() + "/media_cache",
        )
        val imageCachePaths = listOf(
            FileStorageUtils.getAppDataPath() + "/cache/image_manager_disk_cache",
            FileStorageUtils.getAppDataPath() + "/cache/glide_cache"
        )
        SafeLog.i(TAG) { "自动清理检测开始:\n 视频缓存路径: $videoCachePaths, \n 图片缓存路径: $imageCachePaths" }
        var totalDeleted = 0
        // 清理视频缓存
        for (path in videoCachePaths) {
            try {
                val deleted = FileStorageUtils.clearFilesOlderThanDays(path, SEVEN_DAYS_MILLIS)
                totalDeleted += deleted
                SafeLog.i(TAG) { "[AutoClean] 视频缓存目录: $path, 删除文件数: ${if (deleted == 0) "无过期文件" else deleted}" }
            } catch (e: Exception) {
                SafeLog.e(TAG) { "[AutoClean] 清理视频缓存异常: $path, err=${e.message}" }
            }
        }
        // 清理图片缓存
        for (path in imageCachePaths) {
            try {
                val deleted = FileStorageUtils.clearFilesOlderThanDays(path, SEVEN_DAYS_MILLIS)
                totalDeleted += deleted
                SafeLog.i(TAG) { "[AutoClean] 图片缓存目录: $path, 删除文件数: ${if (deleted == 0) "无过期文件" else deleted}" }
            } catch (e: Exception) {
                SafeLog.e(TAG) { "[AutoClean] 清理图片缓存异常: $path, err=${e.message}" }
            }
        }
        SafeLog.i(TAG) { "自动清理完成，总共删除超期缓存文件数: $totalDeleted" }
    }
}
