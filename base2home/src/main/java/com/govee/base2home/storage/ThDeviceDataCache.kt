package com.govee.base2home.storage

import androidx.lifecycle.MutableLiveData
import com.govee.base2home.storage.model.DeviceDataModel
import com.ihoment.base2app.infra.SafeLog

/**
 * author  : sinrow
 * time    : 2025/07/17
 * version : 1.0.0
 * desc    : 温湿度计设备数据缓存类
 */
object ThDeviceDataCache {
    var deviceList: List<DeviceDataModel>? = null
    private var _deviceListChanged: MutableLiveData<Unit>? = null

    val deviceListChanged: MutableLiveData<Unit>
        get() {
            if (_deviceListChanged == null) {
                _deviceListChanged = MutableLiveData<Unit>()
            }
            return _deviceListChanged!!
        }

    fun updateDeviceList(list: List<DeviceDataModel>, notify: Boolean = false) {
        deviceList = list
        if (notify) {
            SafeLog.i("ThDeviceDataCache") { "发送设备列表变化通知，设备数量=${list.size}" }
            deviceListChanged.postValue(Unit)
        }
    }

    fun clear() {
        _deviceListChanged = null
        deviceList = null
    }
} 