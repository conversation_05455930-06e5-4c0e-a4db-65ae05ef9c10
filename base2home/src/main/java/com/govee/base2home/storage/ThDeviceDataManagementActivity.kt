package com.govee.base2home.storage

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import androidx.recyclerview.widget.LinearLayoutManager
import com.govee.base2home.databinding.ActivityDeviceDataManagementBinding
import com.govee.base2home.storage.adapter.StorageThDeviceDataAdapter
import com.govee.base2home.storage.model.DeviceDataModel
import com.govee.base2kt.ac.AbsAc
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.toast
import com.govee.ui.R
import com.govee.ui.dialog.Dialog4ClearThDataSelPeriod
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.ResUtil

/**
 * author  : sinrow
 * time    : 2025/07/17
 * version : 1.0.0
 * desc    : 温湿度计设备本地数据管理界面，提供设备数据的展示、选择、清理等功能。
 */
class ThDeviceDataManagementActivity : AbsAc<ActivityDeviceDataManagementBinding>() {
    override var TAG = "STORAGE_MGR"

    private val viewModel: ThDeviceDataManagementViewModel by lazy {
        ViewModelProvider(this)[ThDeviceDataManagementViewModel::class.java]
    }

    private lateinit var deviceAdapter: StorageThDeviceDataAdapter

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        SafeLog.i(TAG) { "DeviceDataManagementActivity onCreate 启动" }
        initRecyclerView()
        initObserver()
        initClickListeners()
        val thDeviceList = ThDeviceDataCache.deviceList ?: emptyList()
        viewModel.setDeviceList(thDeviceList)
    }

    override fun getAcContentRootViewId(): Int = com.govee.base2home.R.id.ac_container

    override fun adapterInsetViewId(): Int = com.govee.base2home.R.id.top_flag

    override fun layoutId(): Int = com.govee.base2home.R.layout.activity_device_data_management

    private fun initRecyclerView() {
        deviceAdapter = StorageThDeviceDataAdapter { device ->
            viewModel.toggleDeviceSelection(device)
        }

        viewBinding.rvDeviceList.apply {
            layoutManager = LinearLayoutManager(this@ThDeviceDataManagementActivity)
            adapter = deviceAdapter
        }
    }

    private fun initObserver() {
        // 观察设备列表
        viewModel.deviceList.observe(this) { deviceList ->
            updateDeviceList(deviceList)
        }

        // 观察选中设备
        viewModel.selectedDevices.observe(this) { selectedDevices ->
            updateSelectedInfo(selectedDevices)
        }

        // 观察全选状态
        viewModel.isAllSelected.observe(this) { isAllSelected ->
            updateSelectAllButton(isAllSelected)
        }

        // 观察清理结果
        viewModel.clearResult.observe(this) { success ->
            if (success) {
                toast(R.string.base2home_management_clear_suc)

                // 检查是否所有设备数据都为0，如果是则退出页面
                if (viewModel.isAllDeviceDataEmpty()) {
                    SafeLog.i(TAG) { "所有温湿度设备数据已清理完成，退出页面" }
                    finish()
                }
            } else {
                toast(R.string.base2home_management_clear_fail)
            }
        }

        // 观察错误信息
        viewModel.errorMessage.observe(this) { message ->
            if (message.isNotEmpty()) {
                SafeLog.e(TAG) { "initObserver() errorMessage = $message" }
            }
        }
    }

    private fun initClickListeners() {
        viewBinding.run {
            // 返回按钮
            back.clickDelay {
                finish()
            }

            // 全选/取消全选按钮
            tvSelectAll.clickDelay {
                viewModel.toggleSelectAll()
            }

            // 清理数据按钮
            tvClearData.clickDelay {
                showCleanPeriodDialog()
            }
        }
    }

    private fun updateDeviceList(deviceList: List<DeviceDataModel>) {
        SafeLog.i(TAG) { "updateDeviceList() deviceList =${deviceList.size}" }

        // 获取包含选中状态的设备列表
        val deviceListWithSelection = viewModel.getDeviceListWithSelection()
        deviceAdapter.setList(deviceListWithSelection)

        // 检查是否有设备数据
        if (deviceList.isEmpty()) {
            SafeLog.i(TAG) { "温湿度设备列表为空，显示空状态" }
            // 这里可以添加空状态显示逻辑
        } else {
            SafeLog.i(TAG) { "温湿度设备列表更新完成，设备数量=${deviceList.size}" }
        }
        updateClearButton()
    }

    private fun updateSelectedInfo(selectedDevices: List<DeviceDataModel>) {
        val selectedCount = selectedDevices.size

        val infoText = if (selectedCount > 0) {
            ResUtil.getString(R.string.b2light_unselect_all)
        } else {
            ResUtil.getString(R.string.b2light_select_all)
        }

        viewBinding.tvSelectAll.text = infoText

        updateClearButton()
    }

    private fun updateClearButton() {
        // 更新清理按钮状态
        val hasSelected = viewModel.hasSelectedDevices()
        viewBinding.tvClearData.isEnabled = hasSelected
        viewBinding.tvClearData.alpha = if (hasSelected) 1.0f else 0.5f
    }

    private fun updateSelectAllButton(isAllSelected: Boolean) {
        val selectableCount = viewModel.getSelectableDeviceCount()
        val infoText = if (selectableCount > 0 && isAllSelected) {
            ResUtil.getString(R.string.b2light_unselect_all)
        } else {
            ResUtil.getString(R.string.b2light_select_all)
        }
        viewBinding.tvSelectAll.text = infoText
    }

    private fun showCleanPeriodDialog() {
        val selectedCount = viewModel.getSelectedDeviceCount()
        if (selectedCount == 0) {
            SafeLog.e(TAG) { "showCleanPeriodDialog() 请选择要清理的设备" }
            return
        }

        val selPeriodDialog = Dialog4ClearThDataSelPeriod.createDialog(this, "", false)
        selPeriodDialog.show(object : Dialog4ClearThDataSelPeriod.OnSelectedListener {
            override fun onSure(selPeriodType: Int) {
                viewModel.clearSelectedDevicesData(selPeriodType)
            }
        })
    }
}
