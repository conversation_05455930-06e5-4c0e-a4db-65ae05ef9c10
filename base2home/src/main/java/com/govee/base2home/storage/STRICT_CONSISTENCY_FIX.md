# 严格数据一致性修复方案

## 问题要求

**必须严格满足：内存总大小 = 临时缓存 + 温湿度设备 + 系统数据**

不允许任何差异，不接受容差验证。

## 根本解决方案

### 1. 修改系统数据计算逻辑

**修复前的问题：**
```kotlin
// 错误方式：独立计算各个系统数据组件
val soSize = getDirectorySize(soPaths)
val webOptSize = getDirectorySize(webOptPaths)
val systemSize = getDirectorySize(systemPaths)
val systemDataTotal = soSize + webOptSize + systemSize // 可能与总大小不一致
```

**修复后的正确方式：**
```kotlin
// 正确方式：系统数据 = 总大小 - 临时缓存 - 温湿度设备
val systemDataTotalSize = totalAppSize - tempCacheSize - currentUserThSize

// 各组件大小仅用于详细显示，不影响总和
val soSize = getDirectorySize(soPaths)
val webOptSize = getDirectorySize(webOptPaths)
val systemFunctionSize = getDirectorySize(systemPaths)
val otherThDataSize = getAllThDataSize() - currentUserThSize
val knownSystemDataSize = soSize + webOptSize + systemFunctionSize + otherThDataSize
val coreAppDataSize = maxOf(0L, systemDataTotalSize - knownSystemDataSize)
```

### 2. 会话级缓存机制

**解决用户清理操作后的数据不一致问题：**

```kotlin
// 会话级缓存 - 仅在单次数据加载中有效
private var currentDataSession = 0L
private var sessionCache = mutableMapOf<String, Long>()

fun startNewDataSession() {
    currentDataSession = System.currentTimeMillis()
    sessionCache.clear()
}

fun invalidateCache() {
    sessionCache.clear() // 清理操作后立即失效缓存
}
```

**使用场景：**
- 用户进入页面：`startNewDataSession()`
- 用户清理缓存：`invalidateCache()`
- 页面恢复：`startNewDataSession()`

### 3. 严格的数据一致性验证

```kotlin
private fun validateDataConsistency(
    memoryOverview: AppMemoryOverviewModel,
    tempCacheSize: Long,
    thDeviceSize: Long,
    systemData: List<CacheDataModel>
) {
    val totalSize = memoryOverview.totalSize
    val systemDataSize = systemData.sumOf { it.size }
    val calculatedSize = tempCacheSize + thDeviceSize + systemDataSize

    if (totalSize == calculatedSize) {
        SafeLog.i(TAG) { "✓ 数据一致性验证通过" }
    } else {
        val difference = totalSize - calculatedSize
        SafeLog.e(TAG) { "✗ 数据一致性验证失败" }
        // 抛出异常，强制修复问题
        throw IllegalStateException("数据一致性验证失败：差异=${difference}")
    }
}
```

## 实现细节

### 1. StorageManagerService 修改

```kotlin
suspend fun getSystemData(
    totalAppSize: Long, 
    tempCacheSize: Long,
    currentUserThSize: Long
): List<CacheDataModel> {
    // 系统数据总大小 = 总大小 - 临时缓存 - 当前用户温湿度设备
    val systemDataTotalSize = totalAppSize - tempCacheSize - currentUserThSize
    
    // 获取各组件实际大小（用于详细显示）
    val soSize = getSessionCachedDirectorySize(soPaths)
    val webOptSize = getSessionCachedDirectorySize(webOptPaths)
    val systemFunctionSize = getSessionCachedDirectorySize(systemPaths)
    val otherThDataSize = getAllThDataSize() - currentUserThSize
    
    // 计算核心应用数据（确保总和一致）
    val knownSystemDataSize = soSize + webOptSize + systemFunctionSize + otherThDataSize
    val coreAppDataSize = maxOf(0L, systemDataTotalSize - knownSystemDataSize)
    
    // 返回系统数据列表，总和 = systemDataTotalSize
    return listOf(...)
}
```

### 2. 清理操作后缓存失效

```kotlin
suspend fun clearTempCache(): Boolean {
    try {
        // 执行清理操作
        VideoUtils.cleanVideoCacheDir()
        GlideCacheUtil.clearImageDiskCache(context)
        // ...
        
        // 清理完成后立即失效缓存
        invalidateCache()
        return true
    } catch (e: Exception) {
        return false
    }
}
```

### 3. ViewModel 数据加载

```kotlin
fun loadAllData() {
    viewModelScope.launch {
        // 开始新的数据加载会话
        StorageManagerService.startNewDataSession()
        
        val memoryOverview = StorageManagerService.getAppMemoryOverview()
        val tempCache = StorageManagerService.getTempCacheData()
        val thDevices = StorageManagerService.getThDeviceData()
        
        // 使用已计算的数据确保一致性
        val systemData = StorageManagerService.getSystemData(
            memoryOverview.totalSize,
            tempCache.sumOf { it.size },
            thDevices.sumOf { it.size }
        )
        
        // 严格验证数据一致性
        validateDataConsistency(memoryOverview, tempCacheSize, thDeviceSize, systemData)
    }
}
```

## 数学保证

通过以下方式确保等式恒成立：

1. **系统数据计算**：`systemData = totalSize - tempCache - thDevice`
2. **验证等式**：`totalSize = tempCache + thDevice + systemData`
3. **代入验证**：`totalSize = tempCache + thDevice + (totalSize - tempCache - thDevice) = totalSize` ✓

## 测试验证

```kotlin
@Test
fun testStrictDataConsistency() {
    val totalSize = 1000L * 1024 * 1024 // 1000MB
    val tempCacheSize = 300L * 1024 * 1024 // 300MB
    val thDeviceSize = 200L * 1024 * 1024 // 200MB
    
    // 系统数据 = 总大小 - 其他部分
    val systemSize = totalSize - tempCacheSize - thDeviceSize // 500MB
    
    // 验证等式严格成立
    assertEquals(totalSize, tempCacheSize + thDeviceSize + systemSize)
}
```

## 修复效果

1. **数学保证**：通过计算逻辑确保等式恒成立
2. **缓存一致性**：会话级缓存避免清理操作后的数据不一致
3. **严格验证**：抛出异常强制修复任何不一致问题
4. **用户体验**：UI显示的数据始终准确一致

这个方案从根本上解决了数据不一致问题，确保 **内存总大小 = 临时缓存 + 温湿度设备 + 系统数据** 严格成立。
