package com.govee.base2home.pfd

import com.govee.base2home.pact.GoodsType
import java.util.concurrent.ConcurrentLinkedQueue

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2024/1/16
 * 动态模块配置类
 */
internal class Config private constructor() {
    companion object {
        /**
         * 构建动态模块配置
         */
        val pfdModules by lazy {
            ConcurrentLinkedQueue<Pfd>().also { modules ->
                //<editor-fold desc="V5.6.10版本新增动态模块">
                /**fea_h5026*/
                modules.add(
                    Pfd.makePfd4Sku(
                        mutableListOf<String>().also {
                            it.add("H5026")
                        },
                        "fea_h5026",
                        "com.govee.h5026.H5026ModuleInitImp"
                    )
                )
                /**pfdv1*/
                modules.add(
                    Pfd.makePfd4Sku(
                        mutableListOf<String>().also {
                            it.add("H6165")
                            it.add("H6166")
                            it.add("H7024")
                            it.add("H7318")
                            it.add("H7001")
                            it.add("H7023")
                            it.add("H7310")
                            it.add("H7312")
                            it.add("H7317")
                        },
                        "pfdv1",
                        "com.govee.pfdv1.PfdV1ModuleInitImp"
                    )
                )
                /**fea_doorbell*/
                modules.add(
                    Pfd.makePfd4Sku(
                        mutableListOf<String>().also {
                            it.add("H7205")
                            it.add("H7212")
                            it.add("H7214")
                        },
                        "fea_doorbell",
                        "com.govee.doorbell.DoorbellModuleInitImp"
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V5.7.00版本新增动态模块">
                /**pact_h7111*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_CIRCULATION_FAN_BLE_IOT_DESKTOP_AIR_V1)
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_IOT_TOWER_FAN_V1)
                            it.add(GoodsType.GOODS_TYPE_VALUE_TOWER_FAN_BLE_IOT)
                            it.add(GoodsType.GOODS_TYPE_VALUE_4_H7106)
                            it.add(GoodsType.GOODS_TYPE_VALUE_4_H7105)
                            it.add(GoodsType.GOODS_TYPE_VALUE_4_H7107)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7112)
                            it.add(GoodsType.GOODS_TYPE_VALUE_4_H7103)
                        },
                        "pact_h7111",
                        "com.govee.h7111.H7111ModuleInitImp"
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V5.8.00版本新增动态模块">
                /**skipv1*/
                modules.add(
                    Pfd.makePfd4Sku(
                        mutableListOf<String>().also {
                            it.add("H5020")
                        },
                        "skipv1",
                        "com.govee.skipv1.SkipModuleInitImp",
                        true
                    )
                )
                /**scalev1*/
                modules.add(
                    Pfd.makePfd4Sku(
                        mutableListOf<String>().also {
                            it.add("H5010")
                        },
                        "scalev1",
                        "com.govee.scalev1.ScaleModuleInitImp",
                        true
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V5.9.00版本新增动态模块">
                /**pact_h7180*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_152)
                        },
                        "pact_h7180",
                        "com.govee.h7180.H7180ModuleInitImp",
                        true
                    )
                )
                /**pact_h7170*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_GOOSE_NECK_POT)
                            it.add(GoodsType.GOODS_TYPE_VALUE_GOOSE_NECK_POT_V2)
                            it.add(GoodsType.GOODS_TYPE_VALUE_177)
                            it.add(GoodsType.GOODS_TYPE_VALUE_168)
                        },
                        "pact_h7170",
                        "com.govee.h7170.H7170ModuleInitImp",
                        true
                    )
                )

                //</editor-fold>

                //<editor-fold desc="V6.0.00版本新增动态模块">
                /**pact_h6020*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6022)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6020)
                        },
                        "pact_h6020",
                        "com.govee.h6020.H6020ModuleInitImp"
                    )
                )
                /**pact_h7129*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7129)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7128)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7124)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H712C)
                        },
                        "pact_h7129",
                        "com.govee.h7129.H7129ModuleInitImp"
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V6.0.10版本新增动态模块">
                /**pact_h7184*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7184)
                        },
                        "pact_h7184",
                        "com.govee.h7184.H7184ModuleInitImp",
                        true
                    )
                )
                /**pact_h7150*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_DEHUMIDIFIER_V1)
                            it.add(GoodsType.GOODS_TYPE_VALUE_DEHUMIDIFIER_V2)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7152)
                            /**V6.7.0版本*/
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7153)
                        },
                        "pact_h7150",
                        "com.govee.h7150.H7150ModuleInitImp"
                    )
                )
                /**pact_h7161*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_146)
                            it.add(GoodsType.GOODS_TYPE_VALUE_189)
                        },
                        "pact_h7161",
                        "com.govee.h7161.H7161ModuleInitImp", true
                    )
                )
                /**pact_h7172*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_IC_MAKER)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H717D)
                        },
                        "pact_h7172",
                        "com.govee.h7172.H7172ModuleInitImp",
                        true
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V6.1.0版本新增动态模块">
                /**pact_h70b3*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H70B3)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H70B4)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H70B5)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6800)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6810)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6811)
                            /*V7.0.1*/
                            it.add(GoodsType.GOODS_TYPE_VALUE_H70B6)
                        },
                        "pact_h70b3",
                        "com.govee.h70b3.H70b3ModuleInitImpl"
                    )
                )
                /**pact_h7130*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            // 7130/713A/7131/7132/713B/7135/713C/7133/7134/7136/713D/7137/713E
                            it.add(GoodsType.GOODS_TYPE_VALUE_HEATER)
                            it.add(GoodsType.GOODS_TYPE_VALUE_h713A)
                            it.add(GoodsType.GOODS_TYPE_VALUE_HEATER_7132)
                            it.add(GoodsType.GOODS_TYPE_VALUE_h713B)
                            it.add(GoodsType.GOODS_TYPE_VALUE_HEATER_7135)
                            it.add(GoodsType.GOODS_TYPE_VALUE_h713C)
                            it.add(GoodsType.GOODS_TYPE_VALUE_HEATER_7133)
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H7134)
                            it.add(GoodsType.GOODS_TYPE_VALUE_HEATER_H7136)
                            it.add(GoodsType.GOODS_TYPE_VALUE_HEATER_H713D)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7137)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H713E)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7138)
                        },
                        "pact_h7130",
                        "com.govee.h7130.H7130ModuleInitImp"
                    )
                )

                /**pact_h6061*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT)
                            it.add(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_V2)
                            it.add(GoodsType.GOODS_TYPE_VALUE_CUBE_LIGHT_606A)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6063)
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_TRIANGLE)
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_LAMP_Y)
                            /**V6.2.3版本*/
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6069)
                        },
                        "pact_h6061",
                        "com.govee.cubelight.H6061ModuleInitImp"
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V6.1.2版本新增动态模块">
                /**pact_h7149*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7149)
                        },
                        "pact_h7149",
                        "com.govee.h7149.H7149ModuleInitImp"
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V6.2.0版本新增动态模块">
                /**pact_h70dx*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H70D1)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H70D2)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H70D3)
                        },
                        "pact_h70dx",
                        "com.govee.h70dx.H70dxModuleInitImpl"
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V6.2.3版本新增动态模块">
                /**pact_h6840*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6840)
                            /*V6.7.2*/
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6841)
                        },
                        "pact_h6840",
                        "com.govee.h6840.H6840ModuleInitImpl"
                    )
                )
                /**lib_recognition - 识别模块 -需要单独触发式下载-以sku值=lib_recognition*/
                modules.add(
                    Pfd.makePfd4Sku(
                        mutableListOf<String>().also {
                            it.add("lib_recognition")
                        },
                        "lib_recognition",
                        "com.govee.ndk.RecognitionModuleInitImp"
                    )
                )
                /**pact_h6609*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_PC_LIGHT)
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H70C1_H70C2)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H70C4_5_6_7_8_9)
                        },
                        "pact_h6609",
                        "com.govee.pact_h6609.H6609ModuleInitImpl"
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V6.2.4版本新增动态模块">
                /**pact_h6047*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_GAME_FEAST)
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6042)
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6043)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6039)
                            /**V6.5.0版本*/
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6038)
                            /**V6.6.0版本*/
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6048)
                        },
                        "pact_h6047",
                        "com.govee.h6047.H6047ModuleInitImpl"
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V6.3.0版本新增动态模块">
                /**pact_h7120*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        //包含7120、7121、7122、7123、7126、7127
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_AIR_CLEANER_V4)
                            it.add(GoodsType.GOODS_TYPE_VALUE_AIR_CLEANER)
                            it.add(GoodsType.GOODS_TYPE_VALUE_AIR_CLEANER_V2)
                            it.add(GoodsType.GOODS_TYPE_VALUE_AIR_CLEANER_V3)
                            it.add(GoodsType.GOODS_TYPE_VALUE_160)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7127)
                        },
                        "pact_h7120",
                        "com.govee.h7120.H7120ModuleInitImp"
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V6.3.10版本新增动态模块">
                /**pact_h6630*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6630)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H6631)
                        },
                        "pact_h6630",
                        "com.govee.h6630.H6630ModuleInitImp"
                    )
                )
                //</editor-fold>

                // <editor-fold desc="V6.5.00版本新增动态模块">
                /**pact_h7086*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7086)
                        },
                        "pact_h7086",
                        "com.govee.h7086.H7086ModuleInitImp"
                    )
                )
                //</editor-fold>

                // <editor-fold desc="V6.6.00版本新增动态模块">
                /**pact_bulblightv3*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_BLE_WIFI_V1)
                            it.add(GoodsType.GOODS_TYPE_VALUE_BULB)
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_RGB_FLUSH_LIGHT_PANEL)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H601EF)
                            /*V6.7.2*/
                            it.add(GoodsType.GOODS_TYPE_VALUE_H8015)
                        },
                        "pact_bulblightv3",
                        "com.govee.bulblightv3.BulblightV3ModuleInitImp"
                    )
                )
                /**pact_h7160*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER_V3)
                        },
                        "pact_h7160",
                        "com.govee.h7160.H7160ModuleInitImp"
                    )
                )
                /**pact_h60B1*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H60B1)
                        },
                        "pact_h60b1",
                        "com.govee.h60b1.H60b1ModuleInitImp"
                    )
                )
                /**pact_h7140*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER_V6)    //7140
                            it.add(GoodsType.GOODS_TYPE_VALUE_H714E)            //714E
                            it.add(GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER)       //7141
                            it.add(GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER_V2)    //7142
                            it.add(GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER_V4)    //7143
                            it.add(GoodsType.GOODS_TYPE_VALUE_HUMIDIFIER_170)    //7145
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7147)    //7147
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7148)    //7148
                        },
                        "pact_h7140",
                        "com.govee.h7140.H7140ModuleInitImp"
                    )
                )
                /**pact_straightfloorlamp*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1)
                            it.add(GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H60B2)
                        },
                        "pact_straightfloorlamp",
                        "com.govee.straightfloorlamp.H6072ModuleInitImp"
                    )
                )
                /**pact_h60ax*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A0)
                            it.add(GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_H60A1)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H60A4)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H60A6)
                            /*V6.7.4*/
                            it.add(GoodsType.GOODS_TYPE_VALUE_H60C1)
                        },
                        "pact_h60ax",
                        "com.govee.pact_h60a0.H60axModuleInitImp"
                    )
                )
                /**pact_h60b0*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H60B0)
                        },
                        "pact_h60b0",
                        "com.govee.h60b0.H60B0ModuleInitImpl"
                    )
                )
                //</editor-fold>

                // <editor-fold desc="V6.6.20版本新增动态模块">
                /**pact_h7056*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7056)
                        },
                        "pact_h7056",
                        "com.govee.h7056.H7056ModuleInitImp"
                    )
                )
                /**pact_h7075*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7075)
                            it.add(GoodsType.GOODS_TYPE_VALUE_WALL_LAMP_H7078)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7072)
                            it.add(GoodsType.GOODS_TYPE_VALUE_H7076)
                            /*V7.0.1*/
                            it.add(GoodsType.GOODS_TYPE_VALUE_H707ABC)
                        },
                        "pact_h7075",
                        "com.govee.h7075.H7075ModuleInitImp"
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V6.7.10版本新增动态模块">
                /**pact_iceMachine 制冰机*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf<Int>().also {
                            it.add(GoodsType.GOODS_TYPE_VALUE_H8121)
                            /*V6.7.4*/
                            it.add(GoodsType.GOODS_TYPE_VALUE_H8120)
                        },
                        "pact_iceMachine",
                        "com.govee.ice.IceModuleInitImp", true
                    )
                )
                //</editor-fold>

                // <editor-fold desc="V6.7.20版本新增动态模块">
                /**pact_h7050*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf(
                            GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT,
                            GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT,
                            GoodsType.GOODS_TYPE_VALUE_CRUEL_WALL_LIGHT,
                            GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_FLOOD_LIGHT,
                            GoodsType.GOODS_TYPE_VALUE_PATH_LIGHT_V2,
                            GoodsType.GOODS_TYPE_VALUE_FLOOD_LIGHT_V2,
                            GoodsType.GOODS_TYPE_VALUE_SHOOT_LIGHT_V1,
                            GoodsType.GOODS_TYPE_VALUE_LED_PATH_LIGHTS,
                            GoodsType.GOODS_TYPE_VALUE_H6088,
                            GoodsType.GOODS_TYPE_VALUE_H7066,
                            GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7052,
                            GoodsType.GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7053,
                            GoodsType.GOODS_TYPE_VALUE_H7063,
                            GoodsType.GOODS_TYPE_VALUE_H7037,
                            GoodsType.GOODS_TYPE_VALUE_H7057,
                            GoodsType.GOODS_TYPE_VALUE_H7058,
                            GoodsType.GOODS_TYPE_VALUE_H7093,
                            GoodsType.GOODS_TYPE_VALUE_H7094,
                            GoodsType.GOODS_TYPE_VALUE_H7087,
                            GoodsType.GOODS_TYPE_VALUE_H8066
                        ),
                        "pact_h7050",
                        "com.govee.h7050.H7050ModuleInitImp"
                    )
                )
                //</editor-fold>

                //<editor-fold desc="V7.0.0版本新增动态模块">
                /**pact_h7006*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf(
                            GoodsType.GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V2,//H7010
                            GoodsType.GOODS_TYPE_VALUE_BLE_BALL_LIGHT,//H7019
                            GoodsType.GOODS_TYPE_VALUE_WARM_WHITE_BULB_LIGHT,//H7012
                            GoodsType.GOODS_TYPE_VALUE_WARM_WHITE_BULB_LIGHT_V2 //H7031
                        ),
                        "pact_h7006",
                        "com.govee.bulblightstringv2.H7006ModuleInitImpl"
                    )
                )
                /**pact_h7040*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf(GoodsType.GOODS_TYPE_VALUE_CAMPING_LIGHT_BLE_V1)//h7040
                        ,
                        "pact_h7040",
                        "com.govee.h7040.H7040ModuleInitImpl"
                    )
                )
                /**pact_h7080*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf(
                            GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT,//B7080
                            GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_8,//B7081
                            GoodsType.GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_6,//B7082
                        ),
                        "pact_h7080",
                        "com.govee.h7080.H7080ModuleInitImpl"
                    )
                )
                /**pact_h6099*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf(
                            GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6099,
                            GoodsType.GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098,
                            GoodsType.GOODS_TYPE_VALUE_H605A,
                            GoodsType.GOODS_TYPE_VALUE_H6097,
                            /*V7.0.2*/
                            GoodsType.GOODS_TYPE_VALUE_H6690,
                            GoodsType.GOODS_TYPE_VALUE_H66A0
                        ),
                        "pact_h6099",
                        "com.govee.pact_h6099.H6099ModuleInitImpl"
                    )
                )
                /**pact_h6092*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf(
                            GoodsType.GOODS_TYPE_VALUE_h6092,
                            GoodsType.GOODS_TYPE_VALUE_H6093,
                            GoodsType.GOODS_TYPE_VALUE_H7070,
                            /*V7.0.0*/
                            GoodsType.GOODS_TYPE_VALUE_H6094,
                            GoodsType.GOODS_TYPE_VALUE_H6095,
                            GoodsType.GOODS_TYPE_VALUE_H609D,
                            /*V7.0.2*/
                            GoodsType.GOODS_TYPE_VALUE_H7073,
                            /*V7.0.3*/
                            GoodsType.GOODS_TYPE_VALUE_H7071
                        ),
                        "pact_h6092",
                        "com.govee.h6092.H6092ModuleInitImpl"
                    )
                )
                /**pact_h6091*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf(
                            GoodsType.GOODS_TYPE_VALUE_START_SKY_LIGHT
                        ),
                        "pact_h6091",
                        "com.govee.h6091.H6091ModuleInitImpl"
                    )
                )
                /**pact_h6079*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf(
                            GoodsType.GOODS_TYPE_VALUE_HEATER_h6079,
                            GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C //h807C

                        ),
                        "pact_h6079",
                        "com.govee.h6079.H6079ModuleInitImpl"
                    )
                )
                /**pact_h6071*/
                modules.add(
                    Pfd.makePfd4GoodsType(
                        mutableListOf(
                            GoodsType.GOODS_TYPE_VALUE_GOOSENECK_FLOOR_LAMP_BLE_WIFI_V1,//h6071
                            GoodsType.GOODS_TYPE_VALUE_UPGLOW_FLOOR_LAMP_WW_BLE_WIFI_V1 //h6075
                        ),
                        "pact_h6071",
                        "com.govee.pact_h6071.H6071ModuleInitImpl"
                    )
                )
                /**pact_h6062*/
                modules.add(
                    Pfd.makePfd4GoodsType(

                        mutableListOf(
                            GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT,//h6062
                            GoodsType.GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2, //h610A
                            GoodsType.GOODS_TYPE_VALUE_MUSIC_LIGHT_BAR//h610B
                        ),
                        "pact_h6062",
                        "com.govee.striplight.H6062ModuleInitImpl"
                    )
                )

                //</editor-fold>

            }
        }
    }
}