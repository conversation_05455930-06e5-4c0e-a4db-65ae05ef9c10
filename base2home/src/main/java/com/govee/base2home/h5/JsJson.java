package com.govee.base2home.h5;

import android.text.TextUtils;

import com.govee.base2home.main.user.SavvyUserManager;
import com.govee.base2home.sku.LightEffect;
import com.govee.base2home.util.TimeFormatM;
import com.govee.base2home.vip.VipM;
import com.govee.home.account.config.AccountConfig;
import com.govee.ui.skin.SkinManager;
import com.ihoment.base2app.BaseApplication;
import com.ihoment.base2app.config.RunMode;
import com.ihoment.base2app.skinv2.ThemeConfig;
import com.ihoment.base2app.util.AppUtil;
import com.ihoment.base2app.util.DeviceUtil;
import com.tk.mediapicker.media.IMediaResult;

import org.greenrobot.eventbus.EventBus;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import androidx.annotation.Keep;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;

/**
 * Create by xieyingwu on 2019-06-18
 * 定义js通信解析的对象信息
 */
public class JsJson {
    private JsJson() {
    }

    /**
     * 获取手机新的js格式信息
     *
     * @return
     * @throws JSONException
     */
    public static JSONObject phoneInfo() throws JSONException {
        JSONObject result = new JSONObject();
        result.put("brand", DeviceUtil.getDeviceBrand());
        result.put("model", DeviceUtil.getDeviceModel());
        result.put("product", DeviceUtil.getDeviceProduct());
        result.put("sysVersion", String.valueOf(DeviceUtil.getVersionRelease()));
        return result;
    }

    /**
     * 获取用户信息的js格式信息
     *
     * @return userInfo json
     * @throws JSONException JSONException
     */
    public static JSONObject userInfo() throws JSONException {
        AccountConfig accountConfig = AccountConfig.read();
        String token = "";
        try {
            token = accountConfig.getToken();
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONObject result = new JSONObject();
        result.put("client", DeviceUtil.getDeviceUuid(BaseApplication.getContext()));
        result.put("accountId", accountConfig.getAccountId());
        result.put("accountEmail", accountConfig.getEmail());
        result.put("language", AppUtil.getLocalLanguageNew());
        result.put("timezone", TimeZone.getDefault().getID());
        result.put("country", AppUtil.getLocalCountryCode());
        result.put("hour24", TimeFormatM.getInstance().is24Hours(false));
        result.put("savvyUser", SavvyUserManager.getInstance.isSavvyUser());
        result.put("appVersion", AppUtil.getVersionName(BaseApplication.getContext()));
        result.put("integral", VipM.getInstance.getPoints());
        result.put("lightMode", SkinManager.getInstance.isLightMode());
        result.put("accountToken", token);
        if (RunMode.isQaInnerMode() || RunMode.isQaMode()) {
            result.put("envId", RunMode.isQaInnerMode() ? 5 : 0);
        }
        /*节日皮肤开关-后续头像功能可能要用到，先保留，但目前交互没有定义，统一赋值false*/
        result.put("festivalSkin", false);
        result.put("themeId", ThemeConfig.Companion.read().getLastApplyThemeId().getFirst());  //对应主题皮肤id
        return result;
    }

    public static JSONObject lightMode() throws JSONException {
        JSONObject result = new JSONObject();
        result.put("lightMode", SkinManager.getInstance.isLightMode());
        return result;
    }

    public static JSONObject removeCommunity(int type, long id) throws JSONException {
        JSONObject result = new JSONObject();
        result.put("type", type);
        result.put("removeId", id);
        return result;
    }

    public static JSONObject notifySearchObj(String reqParams) throws JSONException {
        JSONObject result = new JSONObject();
        result.put("searchObj", reqParams);
        return result;
    }

    /**
     * 获取ime的高度
     *
     * @param h h
     * @return imeH json
     * @throws JSONException JSONException
     */
    public static JSONObject imeHeight(int h) throws JSONException {
        JSONObject result = new JSONObject();
        result.put("imeH", h);
        return result;
    }

    /**
     * 获取用户是否添加设备的js格式信息
     *
     * @return deviceAdd json
     * @throws JSONException JSONException
     */
    public static JSONObject deviceAdd() throws JSONException {
        JSONObject result = new JSONObject();
        result.put("deviceAdd", com.govee.base2home.main.DeviceList.hadDeviceAdd());
        return result;
    }

    /**
     * 是否是SavvyUser
     *
     * @param isSavvyUser isSavvyUser
     * @return savvyUser json
     * @throws JSONException JSONException
     */
    public static JSONObject beSavvyUser(boolean isSavvyUser) throws JSONException {
        JSONObject result = new JSONObject();
        result.put("isSavvyUser", isSavvyUser);
        return result;
    }

    /**
     * 构建插入的diy视频
     *
     * @param diyList 待插入的diy视频集合
     * @return
     * @throws JSONException
     */
    public static JSONObject insertDiy(@NonNull List<Diy> diyList) throws JSONException {
        JSONObject jsonObject = new JSONObject();
        JSONArray jsonArray = new JSONArray();
        for (Diy diy : diyList) {
            jsonArray.put(diy.toJsonObject());
        }
        jsonObject.put("diys", jsonArray);
        return jsonObject;
    }

    /**
     * 确认弹框回调
     *
     * @param confirm
     * @return
     * @throws JSONException
     */
    public static JSONObject showConfirmDialog(boolean confirm) throws JSONException {
        JSONObject result = new JSONObject();
        result.put("state", confirm);
        return result;
    }

    /**
     * 按键操作-app与js通信的json解析对象
     */
    @Keep
    public static class KeyResult {
        public String key;/*按键类型*/
        public boolean result;/*js是否需要消耗*/
    }

    /**
     * 跳转操作-app与js通信的json解析对象
     */
    @Keep
    public static class LinkInfo {
        public String redirectUrl;
        public String skipWay;
        public String title;
        public int type;
        /*4.7.0-针对通用半屏弹窗扩展的新参数*/
        public static final int CLOSE_TYPE_BACK = 0;
        public static final int CLOSE_TYPE_CLOSE = 1;
        public int closeType;/*关闭样式类型*/
        public float height;/*半屏样式高度占最大高度的比例*/
    }

    /**
     * code码信息-app与js通信的json解析对象
     */
    @Keep
    public static class CodeInfo {
        public String code;
    }

    /**
     * 分享信息-app与js通信的json解析对象
     */
    @Keep
    public static class ShareInfo {
        public String iconUrl;//图片连接
        public String title;//标题
        public String content;//内容
        public String url;/*分享的url*/
    }

    /**
     * 跳转app主界面tab的信息-app与js通信的json解析对象
     */
    @Keep
    public static class LinkAppPage {
        public String page;
        public int pageId;
        public String title;
        public boolean close;
        public int activityId;
        public int type;
        public String site;
        /*4.7.0*/
        public int circleType;/*圈子类型*/
        public int circleId;/*圈子ID*/
        /*5.1.2*/
        public int gId;/*群组ID-第一版本-指的是音乐盛宴的ID*/

        public int topicId;/*话题ID*/
        public int executeType;/*创建自动执行跳转类型*/
    }

    /**
     * 播放视频的信息-app与js通信的json解析对象
     */
    @Keep
    public static class Video {
        public String videoUrl;
    }

    /**
     * 申请上传media
     */
    @Keep
    public static class UploadMedia {
        public String type;
        public int maxNum;
        public int times = -1;
    }

    /**
     * 兑换积分商品
     */
    @Keep
    public static class RedeemWares {
        public int waresId;
        public int points;
    }

    /**
     * 关闭分享页面
     */
    @Keep
    public static class ShareInfoClose {
        public boolean result;
    }

    /**
     * media的信息
     */
    public static class MediaInfo {
        private static final int status_create_task = 0;
        private static final int status_update_progress = 1;
        private static final int status_upload_fail = 2;
        private static final int status_upload_suc = 3;

        private static final int type_img = 1;
        private static final int type_video = 2;
        private static final int type_gif = 3;

        public long code;
        public String showUrl;
        public int type;

        public String playUrl;
        public String imgUrl;
        private final int[] statusSet = new int[]{status_create_task, status_create_task};
        private final int[] percentSet = new int[]{0, 0};/*img+video*/

        void videoSuc(String cloudUrl) {
            this.playUrl = cloudUrl;
            statusSet[1] = status_upload_suc;
        }

        void videoFail() {
            statusSet[1] = status_upload_fail;
        }

        public void imgFail() {
            statusSet[0] = status_upload_fail;
        }

        public void imageSuc(String cloudUrl) {
            this.imgUrl = cloudUrl;
            statusSet[0] = status_upload_suc;
        }

        boolean isSuc() {
            if (type == IMediaResult.media_type_video) {
                return statusSet[0] == status_upload_suc && statusSet[1] == status_upload_suc;
            } else {
                return statusSet[0] == status_upload_suc;
            }
        }

        boolean isFail() {
            if (type == IMediaResult.media_type_img) {
                return statusSet[0] == status_upload_fail;
            } else {
                return statusSet[0] == status_upload_fail || statusSet[1] == status_upload_fail;
            }
        }

        void videoPercent(int percent) {
            if (statusSet[0] == status_create_task) statusSet[0] = status_update_progress;
            percentSet[1] = percent;
        }

        public void imgPercent(int percent) {
            if (statusSet[0] == status_create_task) statusSet[0] = status_update_progress;
            percentSet[0] = percent;
        }

        void resetStatus() {
            if (type == IMediaResult.media_type_img || type == IMediaResult.media_type_gif) {
                statusSet[0] = status_update_progress;
            } else {
                if (statusSet[0] == status_upload_fail) statusSet[0] = status_update_progress;
                if (statusSet[1] == status_upload_fail) statusSet[1] = status_update_progress;
            }
        }

        public JSONObject toJSONObject() {
            JSONObject result = null;
            try {
                result = new JSONObject();
                int status = getStatus();
                result.put("status", status);
                result.put("code", code);
                result.put("progress", getPercent());
                int type = getType();
                result.put("type", type);
                String showUrl = status == status_create_task ? this.showUrl : "";/*只有当status=status_create_task该字段有效*/
                result.put("showUrl", showUrl);
                JSONArray jsonArray = new JSONArray();
                /*只有当status=status_upload_suc时，该字段才赋值*/
                if (status == status_upload_suc) {
                    if (this.type == IMediaResult.media_type_img || this.type == IMediaResult.media_type_gif) {
                        jsonArray.put(imgUrl);
                    } else if (this.type == IMediaResult.media_type_video) {
                        jsonArray.put(imgUrl);
                        jsonArray.put(playUrl);
                    }
                }
                result.put("resultUrl", jsonArray);
            } catch (Exception e) {
                e.printStackTrace();
            }
            return result;
        }

        private int getPercent() {
            if (type == IMediaResult.media_type_video) {
                return (percentSet[0] + percentSet[1]) / 2;
            } else {
                return percentSet[0];
            }
        }

        private int getType() {
            if (type == IMediaResult.media_type_img) return type_img;
            if (type == IMediaResult.media_type_video) return type_video;
            if (type == IMediaResult.media_type_gif) return type_gif;
            return type_img;
        }

        private int getStatus() {
            if (type == IMediaResult.media_type_img || type == IMediaResult.media_type_gif) {
                return statusSet[0];
            } else {
                /*createTask*/
                if (statusSet[0] == status_create_task) return status_create_task;
                /*uploadSuc*/
                if (statusSet[0] == status_upload_suc && statusSet[1] == status_upload_suc)
                    return status_upload_suc;
                /*uploadFail*/
                if (statusSet[0] == status_upload_fail || statusSet[1] == status_upload_fail)
                    return status_upload_fail;
                /*updateProgress*/
                return status_update_progress;
            }
        }
    }

    /**
     * 更改上传media的状态
     */
    @Keep
    public static class ChangeUploadMedia {
        static final int type_status_re_upload = 1;
        static final int type_status_delete_task = 2;
        public int type;
        public int code;
    }

    /**
     * 帖子字段刷新
     */
    @Keep
    public static class PostPushUpdate {
        public Long postId;
        public int thumbNumber;
        public int commentsNumber;
        public int glanceNumber;
        public boolean thumbState;
        public List<Map<String, String>> extraParams;//额外参数,json数组格式[{"type":"1","a":"a"}]
    }

    //更新帖子类型
    public static class UpdatePostType {
        public static final String TYPE_COLLECTION = "1";
    }

    /**
     * @see UpdatePostType
     * 1:收藏
     */
    public static final String KEY_TYPE = "type";
    //收藏状态 1：已收藏 0:未收藏
    public static final String KEY_COLLECTION_STATE = "collectionState";
    //收藏数量
    public static final String KEY_COLLECTION_NUMBER = "collectionNumber";

    /**
     * h5统计
     */
    @Keep
    public static class Analytics {
        public String paramKey;
        public String paramValue;
    }

    @Keep
    public static class PushUpdate {
        public int glanceNumber;
        public int thumbNumber;
        public boolean thumbState;
        public int commentsNumber;
    }

    @Keep
    public static class Diy {
        public int videoId;
        public String thumbnailUrl;
        public String videoUrl;
        public int type;
        public String gifUrl;
        public boolean isGifGiy;

        public Diy(int videoId, String thumbnailUrl, String videoUrl, int type, String gifUrl, boolean isGifGiy) {
            this.videoId = videoId;
            this.thumbnailUrl = thumbnailUrl;
            this.videoUrl = videoUrl;
            this.type = type;
            this.gifUrl = gifUrl;
            this.isGifGiy = isGifGiy;
        }

        public JSONObject toJsonObject() throws JSONException {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("videoId", videoId);
            jsonObject.put("thumbnailUrl", thumbnailUrl);
            jsonObject.put("videoUrl", videoUrl);
            jsonObject.put("type", type);
            jsonObject.put("gifUrl", gifUrl);
            jsonObject.put("isGifDiy", isGifGiy);
            return jsonObject;
        }
    }

    @Keep
    public static class MyColors {
        public List<Color> myColor;

        public boolean isValid() {
            return myColor != null && !myColor.isEmpty();
        }

        @Keep
        public static class Color {
            public int[] colorSet;
            public int[] brightnessSet;
        }
    }

    @Keep
    public static class InsertDiy {
        public int maxNum;
        public boolean showWorkShop;
    }

    @Keep
    public static class InertMyColor {
        public int maxNum;
        public int colorBlockNum; //色块数目
        public int colorBarNum; //色条数目
        // 0 只用到maxNum，不需要拆分色块色条
        // 1 拆分色块色条
        public int type;
    }

    @Keep
    public static class PlayDiy {
        public static final int TYPE_DIY_VIDEO = 1;/*diy视频*/
        public static final int TYPE_UPLOAD_VIDEO = 0;/*普通上传视频*/
        public static final int TYPE_WORKSHOP_VIDEO = 2;//工坊视频
        public static final int TYPE_DEF = -1;/*按照之前的条件处理*/
        public int videoId;
        public Integer type = TYPE_DEF;


        public boolean isDiyVideo() {
            return type == null || type == TYPE_DEF || type == TYPE_DIY_VIDEO;
        }
    }

    @Keep
    public static class ShareIcon {
        public boolean result;
    }

    @Keep
    public static class WatchUser {
        public String identity;
        public int identityType;
    }

    @Keep
    public static class EffectApply4Device {
        public String deviceId;
        public List<EffectValue> effectValues = new ArrayList<>();

        public static void sendEvent(EffectApply4Device event) {
            EventBus.getDefault().post(event);
        }

        public boolean isValid() {
            return !TextUtils.isEmpty(deviceId) && effectValues != null && !effectValues.isEmpty();
        }

        public boolean isAbsMusicType() {
            try {
                return effectValues.size() == 1 && effectValues.get(0).effect.parseVersion == 7;
            } catch (Exception e) {
                return false;
            }
        }
    }

    @Keep
    public static class EffectChooseApply {
        public List<EffectValue> effectValues;
    }

    @Keep
    public static class EffectValue {
        public int priority;
        public List<String> supportSkus;
        public Effect effect;

        @Keep
        public static class Effect {
            public int parseVersion;
            public String effectStr;
            public int[] effectCodes;
            public int effectIcNum;
        }
    }


    @Keep
    public static class EffectApply {
        public int parseVersion;
        public String effectStr;
        public int[] effectCodes;
        public int type;//默认0走以前的逻辑。1默认走引导页应用效果的逻辑
    }

    @Keep
    public static class ColorApply {
        public int[] colorSet;
        public int[] brightnessSet;
    }

    @Keep
    public static class ApplyState {
        public static final int NO_SUPPORT = 1;
        boolean state;
        public int support = 0;
    }

    @Keep
    public static class ClickRightButton {
        public boolean disabled;
    }

    @Keep
    public static class Title {
        public String title;
        public String subTitle;
    }

    @Keep
    public static class BarAction {
        public boolean visible;
        public boolean disabled;
        public String text;
        public String icon;
        public String iconPress;
    }

    @Keep
    public static class PassThroughInfo {
        public String data;
        public long requestCode;
    }

    @Keep
    public static class EffectVersions {
        public int[] parseVersion;

        public boolean isValid() {
            return parseVersion != null && parseVersion.length > 0;
        }
    }

    @Keep
    public static class EffectDevice {
        public String deviceId;
        public String deviceName;
        public String sku;

        public EffectDevice(String deviceId, String deviceName, String sku) {
            this.deviceId = deviceId;
            this.deviceName = deviceName;
            this.sku = sku;
        }
    }

    public static class EffectDeviceListCallback {
        public List<EffectDevice> deviceList;
    }

    @Keep
    public static class DownloadFile {
        public String fileUrl;
        public String fileExtension;
    }

    @Keep
    public static class Report {
        int reportId;
        int reportType;
        int extCommunalId; // reportType = 12 时生效 ,app 端需要把此参数同步传给服务器
    }

    /**
     * 复购，更新优惠卷
     */
    @Keep
    public static class UpdateCouponInfo {
        public String code;
        public String itemId;
        public int codeStatus;
    }


    /**
     * 删除帖子、评论
     */
    @Keep
    public static class ApplyToRemoveCommunity {
        public static final int type_post = 1;
        public static final int type_comment = 2;
        public int type;
        public long removeId;
    }

    public static final String callback_key_removeCommunity = "removeCommunity";/*上报删除id*/

    /**
     * 保存颜色操作js数据结构
     */
    @Keep
    public static class SaveColorOp {
        public List<NewColor> newColors;

        @Keep
        public static class NewColor {
            public int[] colorSet;
            public int[] brightnessSet;
        }
    }

    @Keep
    public static class CircleState {
        public int circleId;
        public boolean state;
    }

    @Keep
    public static class DefImeOp {
        public int opVersion = 0;
        private final int reportImeH = 0;
        private final int scrollEnable = 0;

        public boolean reportImeH() {
            return reportImeH == 1;
        }

        public boolean scrollImeH() {
            return scrollEnable == 1;
        }
    }

    /**
     * 设置/取消 AI图片
     */
    @Keep
    public static class AiImgSet {
        private static final int mode_type_set_ai = 1;
        private static final int mode_type_cancel_ai = 0;
        public String url;
        public int mode = mode_type_cancel_ai;

        public boolean isCancelAi() {
            return mode == mode_type_cancel_ai;
        }

        public boolean isSetAi() {
            return mode == mode_type_set_ai;
        }
    }

    /**
     * ai图片效果应用
     */
    @Keep
    public static class AiImgApplyEffect {
        public String url;

        public static class EventAiImgApplyEffectResult {
            public boolean result;

            private EventAiImgApplyEffectResult() {
            }

            public static void sendEventAiImgApplyEffectResult(boolean result) {
                EventAiImgApplyEffectResult event = new EventAiImgApplyEffectResult();
                event.result = result;
                EventBus.getDefault().post(event);
            }
        }
    }

    @Keep
    public static class ApplyAudioRecord {
        private static final int type_request_token = 0;
        private static final int type_start_record = 1;
        private static final int type_end_record = 2;
        private static final int type_cancel_record = 3;
        private final int type = -1;
        private int duration;/*单位s*/
        private List<String> languages; /*语言转文字语音集*/

        public boolean isStartAudioRecord() {
            return type == type_start_record;
        }

        public boolean isEndAudioRecord() {
            return type == type_end_record;
        }

        public boolean isCancelAudioRecord() {
            return type == type_cancel_record;
        }

        public boolean isRequestToken() {
            return type == type_request_token;
        }

        public long getDurationMills() {
            return duration * 1000L;
        }

        public List<String> getLanguages() {
            return languages;
        }

        public void setLanguages(List<String> languages) {
            this.languages = languages;
        }
    }

    @Keep
    public static class NotifyCircleStateFromH5 {
        public int circleId;
        public int circleType;
        public boolean state;
    }

    @Keep
    public static class LinkBrowserFilter {
        public List<String> insideList;
        public List<String> outsideList;

        /**
         * 是否是有效配置
         *
         * @return
         */
        public boolean isValid() {
            checkListValid();
            if (insideList != null && !insideList.isEmpty()) return true;
            return outsideList != null && !outsideList.isEmpty();
        }

        private void checkListValid() {
            if (insideList != null && !insideList.isEmpty()) {
                Iterator<String> iterator = insideList.iterator();
                while (iterator.hasNext()) {
                    String next = iterator.next();
                    if (TextUtils.isEmpty(next)) {
                        iterator.remove();
                    }
                }
            }
            if (outsideList != null && !outsideList.isEmpty()) {
                Iterator<String> iterator = outsideList.iterator();
                while (iterator.hasNext()) {
                    String next = iterator.next();
                    if (TextUtils.isEmpty(next)) {
                        iterator.remove();
                    }
                }
            }
        }
    }

    /**
     * 跳转盛宴
     */
    @Keep
    public static class LinkDreamViewPage {
        private static final int TYPE_TO_NEW_CREATE_DREAMVIEW = 1;
        private static final int TYPE_INTO_DETAIL = 2;

        private static final int DREAM_VIEW_TYPE_4_MUSIC = 1;
        private static final int DREAM_VIEW_TYPE_4_VIDEO = 2;
        private static final int DREAM_VIEW_TYPE_4_IMMERSIVE = 3;

        public int gId = -1;
        public int goodsType = 0;
        public int gType = DREAM_VIEW_TYPE_4_MUSIC;
        private final int type = TYPE_TO_NEW_CREATE_DREAMVIEW;

        public String sku;
        public int pactType;
        public int pactCode;
        public String bleVersionSoft;
        public String bleVersionHard;

        public boolean isToNewCreateDreamView() {
            return type == TYPE_TO_NEW_CREATE_DREAMVIEW;
        }

        public boolean isIntoDetail() {
            return type == TYPE_INTO_DETAIL;
        }

        public boolean isDreamView4Music() {
            return gType == DREAM_VIEW_TYPE_4_MUSIC;
        }

        public boolean isDreamView4Video() {
            return gType == DREAM_VIEW_TYPE_4_VIDEO;
        }

        public boolean isDreamView4Immersive() {
            return gType == DREAM_VIEW_TYPE_4_IMMERSIVE;
        }
    }

    @Keep
    public static class State4Boolean {
        public boolean state;
    }

    @Keep
    public static class Page {
        public String pageId;
    }

    @Keep
    public static class CloseOtherPage {
        /*默认关闭自身页面*/
        public boolean closeSelf = true;
        public List<String> pageIds;
    }

    @Keep
    public static class Market {
        public String market;
    }

    @Keep
    public static class DeviceControl {
        public String deviceId;
        public String deviceName;
        public Cmd cmd;
    }

    @Keep
    public static class DevicePage {
        public String deviceId;
        public String deviceName;
    }

    @Keep
    public static class DeviceList {
        public List<String> deviceIdList;
        public boolean supportIot = false;
    }


    @Keep
    public static class GroupProxy {
        public List<CmdValue> cmdList;
    }

    @Keep
    public static class CmdValue {
        public String deviceId;
        public int type;//表示是否配置指令，1-配置指令  2-透传指令（A5、B1、B2、B5）
        public byte[][] cmd;


        public boolean isConfigurationType() {
            return type == 1;
        }

        public byte[][] getCmdBytes() {
            if (cmd == null) return null;
            return cmd;
        }
    }


    @Keep
    public static class GroupEffectList {
        public List<EffectApply4Device> effectList;

        public List<LightEffect.ChooseApplyEffect> getChooseApplyEffectList() {
            if (effectList == null) return null;
            List<LightEffect.ChooseApplyEffect> chooseApplyEffects = new ArrayList<>();
            for (EffectApply4Device effectApply4Device : effectList) {
                if (effectApply4Device.isValid()) {// 有效
                    List<JsJson.EffectValue> effectValues = effectApply4Device.effectValues;
                    LightEffect.ChooseApplyEffect chooseApplyEffect = new LightEffect.ChooseApplyEffect();
                    chooseApplyEffect.deviceId = effectApply4Device.deviceId;
                    chooseApplyEffect.isAbsMusicType = effectApply4Device.isAbsMusicType();
                    for (int i = 0; i < effectValues.size(); i++) {
                        JsJson.EffectValue.Effect effect = effectValues.get(i).effect;
                        LightEffect.EffectValue effectValue = new LightEffect.EffectValue();
                        LightEffect.ShareEffect shareEffect = new LightEffect.ShareEffect();
                        shareEffect.parseVersion = effect.parseVersion;
                        shareEffect.effectStr = effect.effectStr;
                        shareEffect.effectCodes = effect.effectCodes;
                        /*标记来源为H5*/
                        shareEffect.source = 2;

                        effectValue.effect = shareEffect;
                        effectValue.priority = effectValues.get(i).priority;
                        effectValue.supportSkus = effectValues.get(i).supportSkus;
                        chooseApplyEffect.effectValues.add(effectValue);
                    }
                    chooseApplyEffects.add(chooseApplyEffect);
                }
            }
            return chooseApplyEffects;
        }
    }

    @Keep

    public static class GroupEffect {
        public String deviceId;
        public List<EffectValue> effectValues;
    }

    @Keep
    public static class Cmd {
        public String name;
        public String value;
        public int turn;
        public int[] colorSet;
        public int brightness;
        public static final int turnOn = 1;

        public boolean isSwitchType() {
            return TextUtils.equals(name, "turn");
        }

        public boolean isColorType() {
            return TextUtils.equals(name, "color");
        }

        public boolean isBrightnessType() {
            return TextUtils.equals(name, "brightness");
        }

        public boolean isTurnOn() {
            return turn == turnOn;
        }

    }

    @Keep
    public static class Sku {
        public String sku = "";
    }

    @Keep
    public static class H5Loacation {
        public String city;
        public String country;
        @Nullable
        public String countryCode;

        public H5Loacation(String city, String country, @Nullable String countryCode) {
            this.city = city;
            this.country = country;
            this.countryCode = countryCode;
        }
    }

    @Keep
    public static class EncryptData {
        public String data = "";
    }


    @Keep
    public static class ClickRightButtonMulti {
        public int currentIndex;
    }
}