package com.govee.base2home.analytics;

import android.text.TextUtils;

/**
 * Create by x<PERSON><PERSON><PERSON> on 2018/11/9
 * 统计固定的value
 */
public class ParamFixedValue {
    private ParamFixedValue() {
    }

    public static final String bt_switch = "switch";
    public static final String deals_show = "deals_show";
    public static final String contact_us = "contact_us";
    public static final String contact_us_wifi_fail = "contact_us_wifi_fail";
    public static final String suggestion = "suggestion";
    public static final String rapid_replacement = "rapid_replacement";
    public static final String order_commit = "order_commit";
    public static final String replacement_commit = "replacement_commit";
    public static final String account_5052 = "5052_account";
    public static final String no_account_5052 = "5052_no_account";
    public static final String account_5074 = "5074_account";
    public static final String no_account_5074 = "5074_no_account";
    public static final String account_5072 = "5072_account";
    public static final String no_account_5072 = "5072_no_account";
    public static final String account_5075 = "5075_account";
    public static final String account_5104 = "5104_account";
    public static final String account_5105 = "5105_account";
    public static final String account_5108 = "5108_account";
    public static final String account_5010 = "5010_account";
    public static final String no_account_5010 = "5010_no_account";
    public static final String no_account_5075 = "5075_no_account";
    public static final String no_account_5104 = "5104_no_account";
    public static final String no_account_5105 = "5105_no_account";
    public static final String no_account_5108 = "5108_no_account";
    public static final String sum_code = "sum_code";
    public static final String sum_jump = "sum_jump";
    public static final String list_group_switch = "list_group_switch";
    public static final String group_page_switch = "group_page_switch";

    public static String getAccountRecordValue(String sku, boolean useAccount) {
        if (useAccount) return sku + "_account";
        return sku + "_no_account";
    }

    public static String beDealId(long id) {
        return "deal_" + id;
    }

    public static String beNewArrivalsId(long id) {
        return "new_arrivals_" + id;
    }

    public static final String beef_1 = "beef_1";
    public static final String beef_3 = "beef_3";
    public static final String beef_5 = "beef_5";
    public static final String beef_7 = "beef_7";
    public static final String beef_10 = "beef_10";
    public static final String chicken = "chicken";
    public static final String fish = "fish";
    public static final String lamb_1 = "lamb_1";
    public static final String lamb_3 = "lamb_3";
    public static final String lamb_5 = "lamb_5";
    public static final String lamb_7 = "lamb_7";
    public static final String lamb_10 = "lamb_10";
    public static final String pork = "pork";
    public static final String turkey = "turkey";
    public static final String bbq_others = "others";
    public static final String refuse = "refuse";
    public static final String community_contact_us = "community_contact_us";
    public static final String community_fast_back = "community_fast_back";
    public static final String app_open = "app_open";
    public static final String feedback = "feedback";
    public static final String my_profile = "my_profile";
    public static final String post_review = "post_review";
    public static final String feedback_review = "feedback_review";
    public static final String rgb = "rgb";
    public static final String onoff = "onoff";
    public static final String defend = "defend";
    public static final String ble = "ble";
    public static final String circadian_rhythm = "circadian_rhythm";
    public static final String same_model = "same_model";
    public static final String rgb_color = "rgb_color";
    public static final String ble_color = "ble_color";
    public static final String rgb_brightness = "rgb_brightness";
    public static final String ble_brightness = "ble_brightness";

    public static final String my_device = "my_device";
    public static final String savvy_user = "savvy_user";
    public static final String community = "community";
    /*2.2.0*/
    public static final String issue = "issue";
    public static final String fast_back = "fast_back";
    public static final String scan_around_ble = "scan_around_ble";
    public static final String search = "search";
    public static final String no_result = "no_result";
    public static final String result_sku = "result_sku";
    public static final String result_name = "result_name";
    public static final String result_group = "result_group";
    public static final String result_category = "result_category";

    /*2.4.0*/
    public static final String connect_time = "connect_time";
    public static final String connectFail = "connectFail";
    public static final String transportFail = "transportFail";
    public static final String bindFail = "bindFail";
    public static final String networkAnomaly = "networkAnomaly";
    public static final String wificheckFail = "wificheckFail";
    public static final String apDisconnect = "apDisconnect";
    public static final String notSameDevice = "notSameDevice";
    public static final String apIpEmpty = "ipEmpty";


    /*数据类*/
    public static final String data_service_suc = "data_service_suc";
    public static final String data_service_fail = "data_service_fail";
    public static final String data_device_suc = "data_device_suc";
    public static final String data_device_fail = "data_device_fail";
    public static final String data_upload_suc = "data_upload_suc";
    public static final String data_upload_fail = "data_upload_fail";

    /*模式类*/
    public static final String mode_music = "music";
    public static final String mode_game = "game";
    public static final String mode_color = "color";
    public static final String mode_scenes = "scenes";
    public static final String mode_video = "mode_video";
    public static final String mode_diy = "DIY";

    public static final String other = "other";
    public static final String red = "red";
    public static final String orange = "orange";
    public static final String yellow = "yellow";
    public static final String green = "green";
    public static final String blue = "blue";
    public static final String indigo = "indigo";
    public static final String violet = "violet";
    public static final String white = "white";
    public static final String transparent = "transparent";

    public static final String color_from_type_piece = "piece";
    public static final String color_from_type_bar = "bar";
    public static final String color_from_type_wcBar = "wcBar";
    public static final String color_from_type_wheel = "wheel";
    public static final String color_from_type_camera = "camera";

    public static final String chart_tab_hour = "hour";
    public static final String chart_tab_day = "day";
    public static final String chart_tab_week = "week";
    public static final String chart_tab_month = "month";
    public static final String chart_tab_year = "year";
    public static final String click_motion_mode = "click_motion_mode";
    public static final String click_motion_scene_turn_on = "click_motion_scene_turn_on";
    public static final String click_motion_scene_turn_off = "click_motion_scene_turn_off";
    public static final String motion_scene_num = "motion_scene_num";
    public static final String click_motion_brightness = "click_motion_brightness";
    public static final String click_motion_brightness_turn_on = "click_motion_brightness_turn_on";
    public static final String click_motion_brightness_turn_off = "click_motion_brightness_turn_off";
    public static final String click_guide_set_yandex = "click_guide_set_yandex";

    public static String getColorFromTypeStr(int colorFromType) {
        String s = "mode_color_type_";
        if (colorFromType == 1) return s + color_from_type_piece;
        if (colorFromType == 2) return s + color_from_type_bar;
        if (colorFromType == 3) return s + color_from_type_wcBar;
        if (colorFromType == 4) return s + color_from_type_wheel;
        if (colorFromType == 5) return s + color_from_type_camera;
        return "mode_color_type_unknown";
    }

    public static final String dynamic = "dynamic";
    public static final String soft = "soft";

    public static String beAnalyticModeName(String mode, String subMode) {
        if (TextUtils.isEmpty(subMode)) {
            return mode;
        }
        return mode + "_" + subMode;
    }

    public static String beModeUse(String analyticModeName) {
        return "mode_use_" + analyticModeName;
    }

    public static String beModeClick(String analyticModeName) {
        return "mode_click_" + analyticModeName;
    }

    public static String beLullaySongPlay(int songId) {
        return "lullaby_song_" + songId;
    }

    public static String bePairFail(String reason) {
        return "pair_fail_" + reason;
    }

    /*通话类*/
    public static final String call_leave_message_listen = "call_leave_message_listen";
    public static final String call_answer = "call_answer";
    public static final String call_abnormal_disconnect = "call_abnormal_disconnect";
    public static final String call_network_poor = "call_network_poor";

    /*图片类*/

    public static final String img = "img";
    public static final String img_fail = "img_fail";
    public static final String img_suc = "img_suc";
    public static final String img_fail_socket = "img_fail_socket";
    public static final String img_suc_socket = "img_suc_socket";

    /*检测连接类*/
    public static final String link_error_gw = "link_error_gw";
    public static final String link_error_433 = "link_error_433";
    public static final String link_error_device = "link_error_device";
    public static final String link_click_check = "link_click_check";

    /*监听类*/
    public static final String listen_into_page = "listen_into_page";
    public static final String listen_fail = "listen_fail";
    public static final String listen_someone_using = "listen_someone_using";
    public static final String listen_abnormal_disconnect = "listen_abnormal_disconnect";
    public static final String listen_click_speak = "listen_click_speak";

    /*摇篮曲类*/
    public static final String lullaby_cycle = "lullaby_cycle";
    public static final String lullaby_delay_stop = "lullaby_delay_stop";
    public static final String night_light_onOff = "night_light_onOff";

    public static String lullabySong(long id) {
        return "lullaby_song_" + id;
    }

    public static final String call = "call";

    public static final String times = "times";

    public static final String near = "near";

    /*2.5.0*/
    public static final String creations = "creations";
    public static final String forum = "forum";
    public static final String roadmap = "roadmap";


    public static final String light = "light";
    public static final String dark = "dark";
    public static final String system = "system";


    public static final String device = "device";
    public static final String scenes = "scenes";


    public static String beModeApply(String analyticModeName) {
        return "mode_" + analyticModeName;
    }

    public static String beDrag(String analyticModeName) {
        return "drag_" + analyticModeName;
    }

    /*3.5.0*/
    public static final String firstAgree = "firstAgree";
    public static final String disAgree = "disAgree";
    public static final String disagreeAndExit = "disagreeAndExit";
    public static final String secAgree = "secAgree";

    public static String agreementUser(String type) {
        return "time_" + type;
    }


    public static final String userAgreement = "userAgreement";
    public static final String privatePolicy = "privatePolicy";

    public static String agreementDialogJump(String type) {
        return "jump_dialog_" + type;
    }

    public static String agreementAboutUsJump(String type) {
        return "jump_about_" + type;
    }

    public static String agreementLoginJump(String type) {
        return "jump_login_" + type;
    }

    public static String agreementRegisterJump(String type) {
        return "jump_register_" + type;
    }

    public static String testingclubProductJump(int videoId, String site) {
        return videoId + "_" + site;
    }

    public static final String show = "show";
    public static final String now = "now";
    public static final String next = "next";
    public static final String close = "close";
    public static final String like = "like";
    public static final String dislike = "dislike";
    public static final String video_list_play = "video_list_play";
    public static final String video_detail_play = "video_detail_play";
    public static final String op_like_list = "op_like_list";
    public static final String op_like_detail = "op_like_detail";

    public static final String solid = "solid";
    public static final String piece = "piece";
    public static final String mood = "mood";

    public static String paletteMoodStr(String moodStr) {
        if (TextUtils.isEmpty(moodStr)) return "";
        return "palette_" + moodStr;
    }

    public static String pieceMoodStr(String moodStr) {
        if (TextUtils.isEmpty(moodStr)) return "";
        return "piece_" + moodStr;
    }

    public static String applyLightSquare(boolean isSuc, int effectId) {
        if (isSuc) {
            return "apply_suc_" + effectId;
        } else {
            return "apply_fail_" + effectId;
        }
    }

    public static final String diy = "diy";
    public static final String color = "color";
    public static final String mycolor = "mycolor";
    public static final String apply = "apply";

    public static String saveMyColor(String sku, String scenes) {
        return sku + "_" + scenes;
    }

    public static String diy_apply_colorful = "diy_apply_colorful";
    public static String diy_apply_colorfulsky = "diy_apply_colorfulsky";
    public static String diy_apply_colorfulmeteor = "diy_apply_colorfulmeteor";
    public static String diy_apply_colorfulmeteorshower = "diy_apply_colorfulmeteorshower";
    public static String diy_apply_shine = "diy_apply_shine";
    public static String diy_apply_bloom = "diy_apply_bloom";
    public static String diy_apply_stack = "diy_apply_bloom";

    /*3.9.0*/
    public static final String home_page = "home_page";
    public static final String success = "success";
    public static final String fail = "fail";
    public static final String choose = "choose";
    public static final String unchoose = "unchoose";
    /*3.9.1*/
    public static final String bug_retrofit = "bug_retrofit";/*定位线上crash，retrofit的接口请求场景列表报NullException*/
    /*4.0.0*/
    public static final String bug_retrofit_v1 = "bug_retrofit_v1";/*定位线上crash，retrofit的接口请求场景列表报NullException*/
    public static final String bug_drawable_error = "bug_drawable_error";/*定位线上crash，使用ResUtil.getDrawable出现drawable异常场景*/
    public static final String apply_suc = "apply_suc";
    public static final String use_time = "use_time";
    public static final String into_my_focus = "into_my_focus";
    public static final String into_edit_my_scenes = "into_edit_my_scenes";
    public static final String edit_category_name = "edit_category_name";
    public static final String edit_select_all = "edit_select_all";

    public static String getColorLib4SaveTimes(long subCategoryId, int r, int g, int b) {
        return "save_" + subCategoryId + "_" + r + "_" + g + "_" + b;
    }

    public static String getColorLib4ApplySucTimes(long subCategoryId, int r, int g, int b) {
        return "apply_suc_" + subCategoryId + "_" + r + "_" + g + "_" + b;
    }

    public static String getLightLib4ApplySucTimes(String sku, long effectId) {
        return "apply_suc_" + sku + "_" + effectId;
    }

    public static String getLightLib4LastApplySucTimes(String sku, long effectId) {
        return "last_apply_suc_" + sku + "_" + effectId;
    }


    /*4.1.0*/
    public static final String studio = "studio";
    public static final String recommend_times = "recommend_times";
    public static final String effect_apply_times = "effect_apply_times";
    public static final String effect_apply_failed = "effect_apply_failed";
    public static final String color_apply_times = "color_apply_times";
    public static final String color_apply_failed = "color_apply_failed";
    public static final String theme_times = "theme_times";
    public static final String apply_times = "apply_times";
    public static final String apply_success = "apply_success";
    public static final String effect = "effect";
    public static final String theme = "theme";
    public static final String detail_page_apply = "detail_page_apply";
    public static final String diyMode = "diyMode";
    public static final String diy_page_apply = "diy_page_apply";
    public static final String color_lib = "color_lib";
    public static final String recommend = "recommend";
    public static final String bar_apply = "bar_apply";
    public static final String block_apply = "block_apply";
    public static final String click = "click";
    public static final String cancel_success = "cancel_success";
    public static final String cancel_failed = "cancel_failed";
    public static final String edit_category_sort = "edit_category_sort";
    public static final String save = "save";
    public static final String sub_effect_apply = "sub_effect_apply";
    public static final String sub_effect_save = "sub_effect_save";
    public static final String phone = "phone";
    public static final String group = "group";
    public static final String all = "all";
    public static final String part = "part";

    public static String getEffectApplyStr(int pageId, int effectId) {
        return "effect_apply_" + pageId + "_" + effectId;
    }

    public static String getEffectApply4Sku(String sku) {
        return "effect_apply_" + sku;
    }

    public static String getColorSaveStr(int pageId, int effectId) {
        return "color_save_" + pageId + "_" + effectId;
    }

    public static String getColorApplyStr(int pageId, int effectId) {
        return "color_apply_" + pageId + "_" + effectId;
    }

    public static String getColorApply4Sku(String sku) {
        return "color_apply_" + sku;
    }

    public static String getThemeIdStr(int themeId) {
        return "theme_" + themeId;
    }

    public static String getApplyVideoStr(int themeId) {
        return "apply_" + themeId;
    }

    public static String getSegmentBrightnessStr(String sku) {
        return "click_" + sku;
    }


    /*4.3.0*/
    public static String recommend_click_color = "click_color";
    public static String recommend_click_diy = "click_diy";
    public static String recommend_apply_color = "apply_color";
    public static String recommend_apply_diy = "apply_diy";
    public static String recommend_save_color = "save_color";
    public static String recommend_save_diy = "save_diy";
    public static String click_tab_times = "click_tab_times";

    public static String getScenesLibApplyStr(int id) {
        return "apply_effect_" + id;
    }

    /*4.4.0*/
    public static String shop = "mall";
    public static String banner = "banner";
    public static String blind_box = "blind_box";
    public static String seckill = "seckill";
    public static String suit = "suit";
    public static String flag_product = "flag_product";
    public static String single_product = "single_product";
    public static String article = "article";
    public static String go_to_shop = "go_to_shop";
    public static String video_to_shop = "video_to_shop";
    public static String box_single_click = "box_single_click";
    public static String suit_single_click = "suit_single_click";
    public static String suit_one_shop = "suit_one_shop";
    public static String rgbic = "rgbic";

    public static String getLastSoundEffectApplyStr(int effectId) {
        return "effect_" + effectId;
    }

    /*4.5.0*/
    public static String schemes = "schemes";
    public static String simple = "simple";
    public static String professor = "professor";
    public static String on = "on";
    public static String off = "off";
    public static String my_plan_apply_times = "my_plan_apply_times";
    public static String recommend_plan_apply_times = "recommend_plan_apply_times";
    public static String ai_times = "ai_times";
    public static String random_color_times = "random_color_times";
    public static String device_op_times = "device_op_times";

    public static String mode_gear = "mode_gear";
    public static String mode_gear_pre = "mode_gear_";
    public static String mode_sleep = "mode_sleep";
    public static String mode_sleep_pre = "mode_sleep_";
    public static String mode_auto = "mode_auto";
    public static String mode_nature_wind = "mode_nature_wind";
    public static String mode_nature_wind_pre = "mode_nature_wind_";
    public static String mode_storm = "mode_storm";
    public static String mode_custom = "mode_custom";
    public static String mode_dry_clothes = "mode_dry_clothes";
    public static String mode_auto_comfortable_on = "mode_auto_comfortable_on";
    public static String mode_auto_comfortable_off = "mode_auto_comfortable_off";
    public static String mode_auto_close_on = "mode_auto_close_on";
    public static String mode_auto_close_off = "mode_auto_close_off";
    public static String mode_auto_efficient_on = "mode_auto_efficient_on";
    public static String mode_auto_efficient_off = "mode_auto_efficient_off";
    public static String mode_auto_is_eco_on = "mode_auto_is_eco_on";
    public static String mode_auto_is_eco_off = "mode_auto_is_eco_off";
    public static String indicator_light_on = "indicator_light_on";
    public static String indicator_light_off = "indicator_light_off";
    public static String indicator_light_forever = "indicator_light_forever";
    public static String indicator_light_period = "indicator_light_period";
    public static String delay_off = "delay_off";
    public static String schedule_timer = "schedule_timer";
    public static String lock_on = "lock_on";
    public static String lock_off = "lock_off";
    public static String constant_temperature_on = "constant_temperature_on";
    public static String constant_temperature_off = "constant_temperature_off";
    public static String auto_stop_on = "auto_stop_on";
    public static String auto_stop_off = "auto_stop_off";
    public static String rotate_on = "rotate_on";
    public static String rotate_off = "rotate_off";
    public static String mode_custom_1 = "mode_custom_1";
    public static String mode_custom_2 = "mode_custom_2";
    public static String mode_custom_3 = "mode_custom_3";
    public static String uvc_on = "uvc_on";
    public static String uvc_off = "uvc_off";
    public static String night_light_on = "night_light_on";
    public static String night_light_off = "night_light_off";
    public static String bind_thermometer_suc = "bind_thermometer_suc";
    public static final String bind_airquality_times = "bind_airquality_times";
    public static String not_disturb_on = "not_disturb_on";
    public static String not_disturb_off = "not_disturb_off";
    public static String not_disturb_forever = "not_disturb_forever";
    public static String not_disturb_period = "not_disturb_period";
    public static String filter_life_click = "filter_life_click";
    public static String filter_life_reset = "filter_life_reset";

    /*4.8*/
    public static String show_update_dialog = "show_update_dialog";
    public static String into_existing_feast = "into_existing_feast";
    public static String create_new_feast = "create_new_feast";
    public static String h5 = "h5";
    public static String type_app = "type_app";
    public static String complementary = "complementary";
    public static String triangle = "triangle";
    public static String similar = "similar";
    public static String rectangle = "rectangle";
    public static String split = "split";
    public static String color_ring = "color_ring";
    public static String color_value = "color_value";
    public static String Itten_color = "Itten_color";
    public static String gradient_color = "gradient_color";
    public static String HEX = "HEX";
    public static String RGB = "RGB";
    public static String HSV = "HSV";

    /*4.9.1 H7170*/
    public static String keep_warm_duration_ = "keep_warm_duration_";
    public static String mode_ = "mode_";
    public static String timer_page = "timer_page";
    public static String switch_on = "switch_on";
    public static String switch_off = "switch_off";
    public static String timing_start = "timing_start";
    public static String timing_cancel = "timing_cancel";
    public static String keep_warm_on = "keep_warm_on";
    public static String keep_warm_off = "keep_warm_off";
    public static String timer_page_start = "timer_page_start";
    public static String timer_detail_start = "timer_detail_start";
    /*5.1.1*/
    /*h7132*/
    public static final String mode_fan = "mode_fan";/*（风扇模式）选择风扇模式次数*/
    public static final String night_light_brightness_use = "night_light_brightness_use"; /*亮度调节次数*/
    public static final String night_light_mode_color_use = "night_light_mode_color_use";/*颜色模式使用次数*/
    public static final String night_light_mode_scenes_use = "night_light_mode_scenes_use";  /*场景模式使用次数*/
    public static final String night_light_mode_default_use = "night_light_mode_default_use";  /*默认场景模式使用次数*/
    public static final String night_light_scenes_use_s = "night_light_scenes_use_";  /*场景模式场景选择次数*/
    public static final String night_light_mode_diy_use = "night_light_mode_diy_use"; /*DIY模式使用次数*/
    /*5.1 H7172*/
    public static String cancel_alert_confirm = "cancel_alert_confirm";
    public static String cancel_alert_cancel = "cancel_alert_cancel";
    public static String clean_on = "clean_on";
    public static String clean_off = "clean_off";

    /*5.3.0*/
    public static String feedback_no_device = "feedback_no_device";
    public static String feedback_all_success = "feedback_all_success";
    public static String feedback_exist_fail = "feedback_exist_fail";
    public static String times_show = "times_show";
    public static String times_one_click_add = "times_one_click_add";
    public static String times_cancel = "times_cancel";
    public static String feast_type_music = "feast_type_music";
    public static String feast_type_movie = "feast_type_movie";
    public static String feast_type_scene = "feast_type_scene";
    public static String feast_mode_music = "feast_mode_music";
    public static String feast_mode_movie = "feast_mode_movie";
    public static String model_ai_music = "model_ai_music";
    public static String model_light_shadow_show = "model_light_shadow_show";
    public static String model_carnival = "model_carnival";
    public static String ai_music_switch_on = "ai_music_switch_on";
    public static String ai_music_switch_off = "ai_music_switch_off";
    public static String jump_to_create_suc = "jump_to_create_suc";
    public static String tips_sign_in = "tips_sign_in";
    public static String on_2_off = "on_2_off";
    public static String off_2_on = "off_2_on";
    public static String tips_to_update = "tips_to_update";
    public static String mall = "mall";
    public static String environment = "environment";
    public static String click_widget_manage_oneclick = "click_widget_manage_oneclick";
    public static String click_widget_manage_feast = "click_widget_manage_feast";
    public static String click_widget_manage_group = "click_widget_manage_group";
    public static String interrupt = "interrupt";
    public static String interrupt_time_ = "interrupt_time_";
    public static String interrupt_ = "interrupt_";
    public static String fails = "fails";
    public static String fail_device_all_ = "fail_device_all_";
    public static String fail_device_ = "fail_device_";
    public static String add = "add";
    public static String detail = "detail";
    public static String manage_group = "manage_group";
    public static String more = "more";
    public static String voice_guide = "voice_guide";
    public static String batch_manage = "batch_manage";
    public static String apply_all = "apply_all";
    public static String apply_movie = "apply_movie_on";
    public static String apply_movie_off = "apply_movie_off";
    public static String apply_music = "apply_music_on";
    public static String apply_music_off = "apply_music_off";
    public static String apply_scene = "apply_scene_on";
    public static String apply_scene_off = "apply_scene_off";
    public static String recommand_movie = "recommand_movie";
    public static String recommand_music = "recommand_music";
    public static String introduce = "introduce";
    public static String apply_base = "apply_base_on";
    public static String apply_base_off = "apply_base_off";
    public static String apply_same = "apply_same_on";
    public static String apply_same_off = "apply_same_off";
    public static String apply_on = "apply_on";
    public static String apply_off = "apply_off";
    public static String apply_pure_color = "apply_pure_color";
    public static String apply_colorful = "apply_colorful";
    public static String week = "week";
    public static String time_detail = "time_detail";
    public static String less = "less";
    public static String create_effect = "create_effect";
    public static String share = "share";
    public static String tips_text = "tips_text";
    public static String tips_video = "tips_video";
    public static String tips_login = "tips_login";
    public static String tips_su = "tips_su";
    public static String tips_manage_device = "tips_manage_device";
    public static String suc = "suc";
    public static String room = "room";
    public static String sync = "sync";
    public static String sync_2_oneclick = "sync_2_oneclick";
    public static String sync_2_schedule = "sync_2_schedule";
    public static String preview_ = "preview_";
    public static String interrupt_id_ = "interrupt_id_";
    public static String interrupt_device_ = "interrupt_device_";
    public static String preview_fail = "preview_fail";
    public static String preview_suc_ = "preview_suc_";
    public static String preview_suc = "preview_suc";
    public static String setting = "setting";
    public static String shopping_mall = "shopping_mall";
    public static String my_message = "my_message";
    public static String label_nfc = "label_nfc";
    public static String music = "music";
    public static String video = "video";
    public static String color_high = "color_high";
    public static String scene = "scene";
    public static String read = "read";
    public static String sub_read = "sub_read";
    public static String sub_click = "sub_click";
    public static String again = "again";
    public static String add_light = "add_light";
    public static String light_all = "light_all";
    public static String light_subsection = "light_subsection";
    public static String video_mode_all = "video_mode_all";
    public static String video_mode_part = "video_mode_part";
    public static String video_mode_light_all = "video_mode_light_all";
    public static String video_mode_light_part = "video_mode_light_part";
    public static String video_mode_voice_off_2_on = "video_mode_voice_off_2_on";
    public static String video_mode_voice_on_2_off = "video_mode_voice_on_2_off";

    public static final String bug_select_effect = "bug_select_effect";/*定位线上crash，单例中的数据被回收报NullException*/

    public static final String fog = "fog";
    public static final String big = "big";
    public static final String small = "small";

    public static final String mode_use_video = "mode_use_video";
    public static final String mode_use_video_ = "mode_use_video_";
    public static final String mode_click_video_ = "mode_click_video_";
    //音乐模式相关
    public static final String mode_click_music = "mode_click_music";
    public static final String mode_use_music = "mode_use_music";
    public static final String mode_click_music_ = "mode_click_music_";
    public static final String mode_use_music_ = "mode_use_music_";
    public static final String mode_use_music_mic = "mode_use_music_mic";

    public static final String click_pickup_by_phone = "click_pickup_by_phone";
    public static final String click_pickup_by_device = "click_pickup_by_device";
    public static final String click_pickup_by_headset = "click_pickup_by_headset";
    public static final String click_pickup_by_hdmi = "click_pickup_by_hdmi";
    public static final String mode_click_color = "mode_click_color";
    public static final String mode_use_color = "mode_use_color";
    public static final String mode_click_color_ = "mode_click_color_";
    public static final String mode_use_color_ = "mode_use_color_";
    public static final String mode_click_scenes = "mode_click_scenes";
    public static final String mode_use_scenes = "mode_use_scenes";
    public static final String mode_use_scenes_ = "mode_use_scenes_";
    public static final String mode_click_scenes_ = "mode_click_scenes_";
    public static final String mode_click_DIY = "mode_click_DIY";
    public static final String mode_use_DIY = "mode_use_DIY";
    public static final String duration = "duration";
    public static final String exposure = "exposure";
    public static final String automation = "automation";
    public static final String panel = "panel";
    public static final String click_club_default = "click_club_default";
    public static final String click_club_reply_time = "click_club_reply_time";
    public static final String click_club_send_time = "click_club_send_time";
    public static final String click_club_mypartake = "click_club_mypartake";

    public static final String Default = "Default";
    public static final String max = "max";
    public static final String mic_mode = "mic_mode";
    public static final String switch_mode = "switch_mode";
    public static final String input_name = "input_name";
    public static final String click_club_window_join = "click_club_window_join";
    public static final String click_club_window_cancel = "click_club_window_cancel";

    public static final String tab_duration_level_1 = "1-10s";
    public static final String tab_duration_level_2 = "11-30s";
    public static final String tab_duration_level_3 = "31-60s";
    public static final String tab_duration_level_4 = "1-3min";
    public static final String tab_duration_level_5 = "3-5min";
    public static final String tab_duration_level_6 = "5-10min";
    public static final String tab_duration_level_7 = "11-15min";
    public static final String tab_duration_level_8 = "m15min";
    public static final String temperature_ = "temperature_";

    public static final String mode_click_mic = "mode_click_mic";
    public static final String mode_use_mic = "mode_use_mic";

    public static final String click_entry = "click_entry";
    public static final String click_people = "click_people";
    public static final String hint_unsupport = "hint_unsupport";
    public static final String click_create = "click_create";
    public static final String click_detail_create = "click_detail_create";
    public static final String sku_detail = "sku_detail";
    public static final String mode_click_game = "mode_click_game";

    public static final String mode_use_game = "mode_use_game";
    public static final String mode_use_game_ = "mode_use_game_";
    public static final String mode_click_game_ = "mode_click_game_";

    public static final String mode_dark_turn_off = "mode_dark_turn_off";
    public static final String mode_dark_turn_on = "mode_dark_turn_on";
    public static final String mode_dark_low_bright = "mode_dark_low_bright";
    public static final String mode_dark_same_color = "mode_dark_same_color";
    public static final String mode_relative_bright = "mode_relative_bright";
    public static final String yes = "yes";
    public static final String no = "no";
    public static final String open = "open";
    public static final String rice_mode = "rice_mode";

    public static final String weighing_mode = "weighing_mode";
    public static final String slowstew_mode = "slowstew_mode";
    public static final String saute_mode = "saute_mode";
    public static final String diy_mode = "diy_mode";
    public static final String steam_mode = "steam_mode";
    public static final String keepwarm_mode = "keepwarm_mode";
    public static final String rice_start = "rice_start";
    public static final String rice_timing_start = "rice_timing_start";
    public static final String rice_stop = "rice_stop";
    public static final String rice_taste_soft = "rice_taste_soft";
    public static final String rice_taste_hard = "rice_taste_hard";

    public static final String diy_timing_stop = "diy_timing_stop";
    public static final String menu_step_start = "menu_step_start";
    public static final String keepwarm_start = "keepwarm_start";
    public static final String menu_step_stop = "menu_step_stop";
    public static final String diy_start = "diy_start";
    public static final String rice_timing_stop = "rice_timing_stop";
    public static final String diy_timing_start = "diy_timing_start";
    public static final String diy_stop = "diy_stop";
    public static final String slowstew_start = "slowstew_start";
    public static final String slowstew_timing_start = "slowstew_timing_start";
    public static final String slowstew_stop = "slowstew_stop";
    public static final String slowstew_timing_stop = "slowstew_timing_stop";
    public static final String steam_start = "steam_start";
    public static final String steam_stop = "steam_stop";
    public static final String steam_timing_start = "steam_timing_start";
    public static final String steam_timing_stop = "steam_timing_stop";
    public static final String saute_start = "saute_start";

    public static final String square = "square";
    public static final String square_edit_list = "square_edit_list";
    public static final String homepage = "homepage";
    public static final String click_speed_gear_ = "click_speed_gear_";

    /* 5.6 */
    /**
     * 城市反编码失败（有网条件）
     */
    public static final String bug_geocoder_failed = "bug_geocoder_failed";
    public static final String bug_geocoder_empty = "bug_geocoder_empty";
    /**
     * 经纬度定位失败
     */
    public static final String bug_location_failed = "bug_location_failed";
    /*V5.6.0*/
    public static final String add_sub_device_click = "add_sub_device_click";
    public static final String over_max_bind_sub_device = "over_max_bind_sub_device";
    public static final String pair_sub_device_guide_click = "pair_sub_device_guide_click";
    public static final String gateway_sub_device_click = "gateway_sub_device_click";
    public static final String gateway_click = "gateway_click";
    public static final String read_leak_message_click = "read_leak_message_click";
    public static final String gateway_light_switch_off_click = "gateway_light_switch_off_click";
    public static final String gateway_light_switch_on_click = "gateway_light_switch_on_click";

    /*V5.6.20*/
    //视频模式 ai编辑
    public static final String click_ai_switch_on_ = "click_ai_switch_on_";
    public static final String click_ai_switch_off_ = "click_ai_switch_off_";
    public static final String click_ai_mode = "click_ai_mode";
    public static final String click_ai_mode_ = click_ai_mode + "_";
    //盛宴 ai编辑
    public static final String click_feast_ai_switch_on_ = "click_feast_ai_switch_on_";
    public static final String click_feast_ai_switch_off_ = "click_feast_ai_switch_off_";
    public static final String click_feast_ai_mode = "click_feast_ai_mode";
    public static final String click_feast_ai_mode_ = click_feast_ai_mode + "_";

    public static final String click_ai_effect_edit = "click_ai_effect_edit";
    public static final String click_ai_effect_edit_ = click_ai_effect_edit + "_";
    public static final String click_ai_effect_edit_reset = "click_ai_effect_edit_reset";
    public static final String click_ai_effect_edit_apply_ = "click_ai_effect_edit_apply_";
    /*V5.7*/
    public static final String show_loading_sku_resource = "show_loading_sku_resource";
    public static final String click_resource_dialog_cancel = "click_resource_dialog_cancel";
    public static final String click_resource_dialog_retry = "click_resource_dialog_retry";
    public static final String click_sku_card_load_resource = "click_sku_card_load_resource";
    public static final String click_nearly_device_load_resource = "click_nearly_device_load_resource";
    public static final String click_product_scan_device_load_source = "click_product_scan_device_load_source";
    public static final String click_product_sku_card_load_source = "click_product_sku_card_load_source";

    public static final String entrance = "entrance";
    public static final String accept = "accept";
    public static final String delete_share = "delete_share";
    public static final String delete_accept = "delete_accept";
    public static final String temp_unit = "temp_unit";

    public static final String click_energy_conservation_on = "click_energy_conservation_on";
    public static final String click_no_worries_night_on = "click_no_worries_night_on";
    public static final String click_no_worries_night_off = "click_no_worries_night_off";
    public static final String click_without_disturbing_on = "click_without_disturbing_on";
    public static final String click_without_disturbing_off = "click_without_disturbing_off";
    public static final String click_energy_conservation_off = "click_energy_conservation_off";
    public static final String click_sound_on = "click_sound_on";
    public static final String click_sound_off = "click_sound_off";
    public static final String click_indicator_lamp_on = "click_indicator_lamp_on";
    public static final String click_indicator_lamp_off = "click_indicator_lamp_off";
    public static final String click_preset = "click_preset";
    public static final String click_bluetooth_speaker_guide = "click_bluetooth_speaker_guide";
    public static final String click_camera_calibration = "click_camera_calibration";
    public static final String click_light_calibration = "click_light_calibration";
    public static final String push_on = "push_on";
    public static final String push_off = "push_off";
    //7133摆叶开关
    public static final String swing_on = "swing_on";
    public static final String swing_off = "swing_off";
    // 摆叶选择低速次数
    public static final String swing_speed_low = "swing_speed_low";
    //摆叶选择中速次数
    public static final String swing_speed_middle = "swing_speed_middle";
    //摆叶选择高速次数
    public static final String swing_speed_high = "swing_speed_high";
    //      摆叶选择左侧
    public static final String swing_angle_left_top = "swing_angle_left_top";
    //摆叶选择右侧
    public static final String swing_angle_right_bottom = "swing_angle_right_bottom";
    //      摆叶选择全部
    public static final String swing_angle_all = "swing_angle_all";

    public static final String on_ = "on_";
    public static final String off_ = "off_";
    /**
     * 蓝牙快速连接功能回滚
     */
    public static final String bug_blefast_rollback = "v3_rollback_";
    /**
     * 蓝牙快速连接功能异常版本信息
     */
    public static final String bug_blefast_error_version = "ble_error_version_";
    /**
     * 蓝牙快速连接功能 版本解析报错
     */
    public static final String bug_blefast_error_parse = "_error_parse_";
    public static final String bug_aes_sys_error = "bug_aes_sys_error";
    public static final String bug_aes_sys_empty = "bug_aes_sys_empty";
    public static final String bug_aes_native_not_right = "bug_aes_native_not_right";
    public static final String bug_aes_native_error = "bug_aes_native_error";

    public static final String add_music_feast = "add_music_feast";
    public static final String add_movie_feast = "add_movie_feast";
    public static final String add_music_feast_device = "add_music_feast_device";
    public static final String add_movie_feast_device = "add_movie_feast_device";
    public static final String add_scene_feast_device = "add_scene_feast_device";

    public static final String iot = "iot";
    public static final String my_zoom = "my_zoom";
    public static final String gateway_node_device = "gateway_node_device";
    public static final String gateway_sensor_device = "gateway_sensor_device";
    /*V5.8.02*/
    public static final String mode_use_op = "mode_use_op";
    public static final String mode_click_op = "mode_click_op";
    public static final String click_planet_hint = "click_planet_hint";
    public static final String click_backgroundLight_hint = "click_backgroundLight_hint";
    public static final String planet_2_off = "planet_2_off";
    public static final String planet_2_on = "planet_2_on";
    public static final String starLayer_2_off = "starLayer_2_off";
    public static final String starLayer_2_on = "starLayer_2_on";
    public static final String backgroundLight_2_off = "backgroundLight_2_off";
    public static final String backgroundLight_2_on = "backgroundLight_2_on";
    public static final String click_playList = "click_playList";
    public static final String click_autoClose = "click_autoClose";
    public static final String whiteNoise = "whiteNoise";
    public static final String doNotDisturb_2_off = "doNotDisturb_2_off";
    public static final String doNotDisturb_2_on = "doNotDisturb_2_on";
    public static final String autoClose_2_off = "autoClose_2_off";
    public static final String autoClose_2_on = "autoClose_2_on";
    public static final String click_whiteNoise_ = "click_whiteNoise_";

    /*V5.8.3*/
    public static final String mode_use_interaction = "mode_use_interaction";
    public static final String mode_use_interaction_ = "mode_use_interaction_";

    public static final String mode_click_interaction = "mode_click_interaction";

    public static final String mode_click_interaction_ = "mode_click_interaction_";

    public static final String mode_use_interaction_sound_ = "mode_use_interaction_sound_";

    public static final String mode_use_interaction_game_ = "mode_use_interaction_game_";

    public static final String mode_use_interaction_circle = "mode_use_interaction_circle";

    public static final String mode_use_interaction_game = "mode_use_interaction_game";

    public static final String tab_mood = "tab_mood";
    public static final String tab_light = "tab_light";
    public static final String brightness_bar = "brightness_bar";
    public static final String tem_bar = "tem_bar";
    public static final String scene_question = "scene_question";
    public static final String add_scene = "add_scene";

    public static final String save_scene = "save_scene";
    public static final String edit = "edit";
    public static final String movie = "movie";
    public static final String before_bedtime = "before_bedtime";
    public static final String relaxing = "relaxing";
    public static final String get_up = "get_up";
    public static final String reading = "reading";
    public static final String game = "game";
    public static final String working = "working";
    public static final String outdoors = "outdoors";
    public static final String turn_on = "turn_on";
    public static final String turn_off = "turn_off";

    public static final String deal_later = "deal_later";
    public static final String one_key_turn_on = "one_key_turn_on";
    public static final String push_show = "push_show";

    public static final String production_activity = "production_activity";
    public static final String function_new = "function_new";
    public static final String activity_relation = "activity_relation";

    public static final String click_recommend_set = "click_recommend_set";
    public static final String click_recommend_question = "click_recommend_question";
    public static final String click_recommend_effect = "click_recommend_effect";

    public static final String click_recommend_effect_on = "click_recommend_effect_on";
    public static final String click_recommend_effect_off = "click_recommend_effect_off";
    public static final String click_recommend_effect_more = "click_recommend_effect_more";
    public static final String click_online_picture_lib = "click_online_picture_lib";
    public static final String click_picture_ = "click_picture_";
    public static final String click_album = "click_album";
    public static final String click_select_picture = "click_select_picture";

    public static final String select_gif = "select_gif";
    public static final String click_picture_import = "click_picture_import";
    public static final String click_picture_import_optimize = "click_picture_import_optimize";
    public static final String click_draw_util = "click_draw_util";
    public static final String show_gif_too_big = "show_gif_too_big";
    public static final String show_graffiti_too_large = "show_graffiti_too_large";
    public static final String click_save_graffiti = "click_save_graffiti";
    public static final String click_chart_enter = "click_chart_enter";
    public static final String click_download_data_7 = "click_download_data_7";
    public static final String click_download_data_1_month = "click_download_data_1_month";
    public static final String click_download_data_3_month = "click_download_data_3_month";

    public static final String click_download_data_6_month = "click_download_data_6_month";
    public static final String click_download_data_all = "click_download_data_all";
    public static final String click_download_data_cancel = "click_download_data_cancel";
    public static final String click_keep_data_7 = "click_keep_data_7";
    public static final String click_keep_data_1_month = "click_keep_data_1_month";
    public static final String click_keep_data_3_month = "click_keep_data_3_month";
    public static final String click_keep_data_6_month = "click_keep_data_6_month";
    public static final String click_turn_on_device_lock = "click_turn_on_device_lock";
    public static final String click_turn_off_device_lock = "click_turn_off_device_lock";

    public static final String click_auto_photo_recognition = "click_auto_photo_recognition";
    public static final String click_recommend_photo_recognition = "click_recommend_photo_recognition";
    public static final String click_recognition_again = "click_recognition_again";
    public static final String click_manual_connection = "click_manual_connection";
    public static final String use_manual_connection = "use_manual_connection";
    public static final String click_auto_recognition_next = "click_auto_recognition_next";
    public static final String click_manual_connection_next = "click_manual_connection_next";

    public static final String click_switch = "click_switch";
    public static final String click_chart_show = "click_chart_show";
    public static final String click_chart_hide = "click_chart_hide";
    public static final String click_timing = "click_timing";
    public static final String click_delay_on = "click_delay_on";
    public static final String click_delay_off = "click_delay_off";
    public static final String click_timing_monitor = "click_timing_monitor";
    public static final String click_safelock = "click_safelock";
    public static final String click_displaylight = "click_displaylight";
    public static final String click_more_data = "click_more_data";
    public static final String chart_detail_energy = "chart_detail_energy";
    public static final String chart_detail_duration = "chart_detail_duration";
    public static final String chart_detail_today = "chart_detail_today";
    public static final String chart_detail_7days = "chart_detail_7days";
    public static final String chart_detail_30days = "chart_detail_30days";
    public static final String chart_detail_1year = "chart_detail_1year";
    public static final String chart_detail_export = "chart_detail_export";
    public static final String chart_detail_clear = "chart_detail_clear";
    public static final String export_detail_click_export = "export_detail_click_export";
    public static final String export_detail_click_send_email = "export_detail_click_send_email";
    public static final String export_detail_click_add_email = "export_detail_click_add_email";
    public static final String timing_monitor_start = "timing_monitor_start";
    public static final String timing_monitor_alert_no = "timing_monitor_alert_no";
    public static final String timing_monitor_alert_yes = "timing_monitor_alert_yes";
    public static final String timing_monitor_cancel = "timing_monitor_cancel";
    public static final String timing_monitor_cancel_alert_no = "timing_monitor_cancel_alert_no";
    public static final String timing_monitor_cancel_alert_yes = "timing_monitor_cancel_alert_yes";
    //色温模块
    public static final String click_save_color_tem = "click_save_color_tem";
    public static final String click_save_color_tem_scene = "click_save_color_tem_scene";
    public static final String click_color_tem = "click_color_tem";
    /*V6.0.0*/
    public static String rotate_angle = "rotate_angle";
    public static String rotate_left = "rotate_left";
    public static String rotate_right = "rotate_right";
    /*rotate_angle_n(n代表具体档位值)*/
    public static String rotate_angle_pre = "rotate_angle_";
    public static String rotate_angle_other = "rotate_angle_other";
    public static final String into_detail = "into_detail";
    public static final String work_mode_once = "work_mode_once";
    public static final String work_mode_cycle = "work_mode_cycle";
    public static final String work_mode_clean = "work_mode_clean";
    public static final String work_cancel = "work_cancel";
    public static final String chart_detail = "chart_detail";
    public static final String inner_light_on = "inner_light_on";
    public static final String inner_light_off = "inner_light_off";
    public static final String timer_add = "timer_add";
    public static final String timer_save = "timer_save";

    public static final String click_brightness = "click_brightness";
    public static final String click_shortcuts = "click_shortcuts";
    public static final String click_shortcuts_workshop = "click_shortcuts_workshop";
    public static final String click_shortcuts_timer = "click_shortcuts_timer";
    public static final String click_shortcuts_effect_lib = "click_shortcuts_effect_lib";
    public static final String click_shortcuts_auto_play = "click_shortcuts_auto_play";

    public static final String click_shortcuts_material_lib = "click_shortcuts_material_lib";
    public static final String click_mode_ai = "click_mode_ai";
    public static final String click_shortcuts_snapshot = "click_shortcuts_snapshot";
    public static final String click_mode_video = "click_mode_video";
    public static final String click_mode_lighting = "click_mode_lighting";
    public static final String click_mode_color = "click_mode_color";
    public static final String click_mode_scene = "click_mode_scene";
    public static final String click_mode_graffiti = "click_mode_graffiti";
    public static final String click_color_treasure_box_quick = "click_color_treasure_box_quick";
    public static final String click_color_random_quick = "click_color_random_quick";

    public static final String expose_shortcuts_timer = "expose_shortcuts_timer";
    public static final String expose_shortcuts_auto_play = "expose_shortcuts_auto_play";
    public static final String expose_shortcuts_snapshot = "expose_shortcuts_snapshot";
    public static final String expose_shortcuts_effect_lib = "expose_shortcuts_effect_lib";
    public static final String expose_shortcuts_workshop = "expose_shortcuts_workshop";
    public static final String expose_shortcuts_material_lib = "expose_shortcuts_material_lib";
    public static final String color_random_quick_show = "color_random_quick_show";
    public static final String color_treasure_box_quick_show = "color_treasure_box_quick_show";

    public static final String click_mode_more = "click_mode_more";
    public static final String click_mode_music = "click_mode_music";
    public static final String click_mode_diy = "click_mode_diy";
    public static final String send_cmd_video = "send_cmd_video";
    public static final String send_cmd_lighting = "send_cmd_lighting";
    public static final String send_cmd_color = "send_cmd_color";
    public static final String send_cmd_scene = "send_cmd_scene";
    public static final String send_cmd_graffiti = "send_cmd_graffiti";
    public static final String send_cmd_ai_image = "send_cmd_ai_image";
    public static final String send_cmd_more = "send_cmd_more";
    public static final String send_cmd_share = "send_cmd_share";
    public static final String send_cmd_diy = "send_cmd_diy";
    public static final String send_cmd_music = "send_cmd_music";
    public static final String send_cmd_display = "send_cmd_display";
    public static final String send_cmd_carousel = "send_cmd_carousel";
    public static final String use_mode_video = "use_mode_video";
    public static final String use_mode_color = "use_mode_color";
    public static final String use_mode_scene = "use_mode_scene";
    public static final String use_mode_share = "use_mode_share";
    public static final String use_mode_music = "use_mode_music";
    public static final String use_mode_diy = "use_mode_diy";
    public static final String use_mode_graffiti = "use_mode_graffiti";
    public static final String use_mode_display = "use_mode_display";
    public static final String use_mode_carousel = "use_mode_carousel";
    public static final String click_video_start = "click_video_start";
    public static final String click_mic_start = "click_mic_start";

    public static final String turn_2_intact = "turn_2_intact";
    public static final String turn_2_segment = "turn_2_segment";
    public static final String turn_on_gradient = "turn_on_gradient";
    public static final String turn_off_gradient = "turn_off_gradient";
    public static final String click_select_all = "click_select_all";
    public static final String click_select_no = "click_select_no";
    public static final String clcik_relative_brightness = "clcik_relative_brightness";
    public static final String click_color_colortem = "click_color_colortem";
    public static final String click_default_color_block = "click_default_color_block";
    public static final String expose_color_disc_default = "expose_color_disc_default";
    public static final String send_cmd_color_disc = "send_cmd_color_disc";

    public static final String expose_color_disc_emotion = "expose_color_disc_emotion";
    public static final String send_cmd_color_disc_emotion = "send_cmd_color_disc_emotion";
    public static final String expose_color_block_emotion = "expose_color_block_emotion";
    public static final String send_cmd_color_block_emotion = "send_cmd_color_block_emotion";
    public static final String expose_my_color = "expose_my_color";
    public static final String expose_colortem = "expose_colortem";
    public static final String click_my_color_bar = "click_my_color_bar";
    public static final String click_my_color_block = "click_my_color_block";
    public static final String click_edit_my_color = "click_edit_my_color";
    public static final String click_more_my_color = "click_more_my_color";
    public static final String _color = "_color";
    public static final String _diy = "_diy";
    public static final String click_general_recommend = "click_general_recommend";

    public static final String click_tem_bar = "click_tem_bar";
    public static final String click_color_tem_scene_question = "click_color_tem_scene_question";
    public static final String click_color_tem_add_scene = "click_color_tem_add_scene";
    public static final String click_color_tem_save_scene = "click_color_tem_save_scene";
    public static final String click_color_tem_sort = "click_color_tem_sort";
    public static final String click_color_tem_edit = "click_color_tem_edit";
    public static final String click_color_tem_ = "click_color_tem_";

    public static final String expose_work_shop = "expose_work_shop";
    public static final String click_work_shop = "click_work_shop";
    public static final String expose_ai_image = "expose_ai_image";
    public static final String click_ai_image = "click_ai_image";
    public static final String expose_ai_robot = "expose_ai_robot";
    public static final String click_ai_robot = "click_ai_robot";
    public static final String click_music_sub_mode = "click_music_sub_mode";
    public static final String use_music_sub_mode_id = "use_music_sub_mode_id";
    public static final String click_music_tab_my = "click_music_tab_my";
    public static final String click_music_create_add_in_detail = "click_music_create_add_in_detail";
    public static final String click_music_create_item = "click_music_create_item";
    public static final String click_music_create_share = "click_music_create_share";

    public static final String click_music_create_copy = "click_music_create_copy";
    public static final String click_phone_pickup = "click_phone_pickup";
    public static final String click_device_pickup = "click_device_pickup";
    public static final String click_headset_pickup = "click_headset_pickup";
    public static final String click_hdmi_pickup = "click_hdmi_pickup";
    public static final String click_scene_item_ = "click_scene_item_";
    public static final String use_scene_item_ = "use_scene_item_";
    public static final String click_scene_direct = "click_scene_direct";
    public static final String click_item_detail = "click_item_detail";

    public static final String click_more_timer = "click_more_timer";
    public static final String click_timer_awake = "click_timer_awake";
    public static final String click_timer_awake_on = "click_timer_awake_on";
    public static final String click_timer_awake_off = "click_timer_awake_off";
    public static final String click_timer_sleep = "click_timer_sleep";
    public static final String click_timer_sleep_on = "click_timer_sleep_on";

    public static final String click_timer_sleep_off = "click_timer_sleep_off";
    public static final String click_timer_delay_off = "click_timer_delay_off";
    public static final String click_timer_detail = "click_timer_detail";
    public static final String timer_ui = "timer_ui";
    public static final String click_timer_2_on = "click_timer_2_on";
    public static final String click_timer_2_off = "click_timer_2_off";
    public static final String expose_more_work_shop = "expose_more_work_shop";
    public static final String expose_more_material_lib = "expose_more_material_lib";
    public static final String click_more_work_shop = "click_more_work_shop";
    public static final String click_more_material_lib = "click_more_material_lib";
    public static final String expose_more_effect_lib = "expose_more_effect_lib";

    public static final String click_more_action_unit = "click_more_action_unit";
    public static final String click_more_rhythm = "click_more_rhythm";
    public static final String expose_more_action_unit = "expose_more_action_unit";
    public static final String click_more_feast_scene = "click_more_feast_scene";
    public static final String expose_more_one_click = "expose_more_one_click";
    public static final String click_more_one_click = "click_more_one_click";
    public static final String expose_more_rhythm = "expose_more_rhythm";
    public static final String expose_more_feast_movie = "expose_more_feast_movie";
    public static final String expose_more_feast_music = "expose_more_feast_music";
    public static final String expose_more_feast_scene = "expose_more_feast_scene";
    public static final String click_more_feast_movie = "click_more_feast_movie";
    public static final String click_more_feast_music = "click_more_feast_music";
    public static final String click_more_effect_lib = "click_more_effect_lib";
    public static final String expose_more_snapshot = "expose_more_snapshot";
    public static final String click_more_snapshot = "click_more_snapshot";
    public static final String expose_more_autoplay = "expose_more_autoplay";
    public static final String click_more_autoplay_off = "click_more_autoplay_off";
    public static final String click_more_autoplay_on = "click_more_autoplay_on";
    public static final String click_more_autoplay = "click_more_autoplay";

    public static final String click_save_music_snapshot = "click_save_music_snapshot";
    public static final String click_save_video_snapshot = "click_save_video_snapshot";
    public static final String click_save_scene_snapshot = "click_save_scene_snapshot";
    public static final String click_save_low_color_snapshot = "click_save_low_color_snapshot";

    public static final String click_save_all_color_snapshot = "click_save_all_color_snapshot";
    public static final String click_save_part_color_snapshot = "click_save_part_color_snapshot";
    public static final String click_save_graffiti_snapshot = "click_save_graffiti_snapshot";
    public static final String click_save_diy_snapshot = "click_save_diy_snapshot";
    public static final String click_save_game_snapshot = "click_save_game_snapshot";
    public static final String click_save_AI_snapshot = "click_save_AI_snapshot";
    public static final String click_save_operate_snapshot = "click_save_operate_snapshot";
    public static final String hide_all_function = "hide_all_function";
    public static final String expose_status_off = "expose_status_off";
    public static final String click_set_layout = "click_set_layout";
    public static final String click_layout_2_default = "click_layout_2_default";
    public static final String click_layout_2_diy = "click_layout_2_diy";
    public static final String click_layout_diy_detail = "click_layout_diy_detail";
    public static final String show_hint_mode_at_least_one = "show_hint_mode_at_least_one";
    public static final String click_music_create_unsupport = "click_music_create_unsupport";
    public static final String click_music_create = "click_music_create";
    public static final String click_music_create_add_in_squar = "click_music_create_add_in_squar";
    public static final String click_alexa = "click_alexa";
    public static final String click_google_assistant = "click_google_assistant";

    public static final String click_color_cali_unit_ = "click_color_cali_unit_";
    public static final String show_share_hint_dialog = "show_share_hint_dialog";
    public static final String show_hint_in_share = "show_hint_in_share";
    public static final String show_hint_remove_share = "show_hint_remove_share";
    public static final String show_signal_detection_page = "show_signal_detection_page";
    public static final String valid_signal_times = "valid_signal_times";
    public static final String turn_2_off_black_screen = "turn_2_off_black screen";
    public static final String turn_2_on_black_screen = "turn_2_on_black screen";

    public static final String turn_2_off_black_border = "turn_2_off_black border";
    public static final String turn_2_on_black_border = "turn_2_on_black border";
    public static final String click_play_more = "click_play_more";
    public static final String click_snapshot_sort_name = "click_snapshot_sort_name";
    public static final String click_snapshot_sort_time = "click_snapshot_sort_time";
    public static final String click_watch_short_cuts = "click_watch_short_cuts";
    public static final String click_effect_image = "click_effect_image";
    public static final String expose_effect_image = "expose_effect_image";
    public static final String click_effect_robot = "click_effect_robot";
    public static final String expose_effect_robot = "expose_effect_robot";

    public static final String click_question_vpd = "click_question_vpd";
    public static final String click_question_dp = "click_question_dp";
    public static final String click_question_cali = "click_question_cali";
    public static final String click_set_remote_check = "click_set_remote_check";
    public static final String click_set_action_unit = "click_set_action_unit";
    public static final String click_set_more = "click_set_more";
    public static final String click_set_less = "click_set_less";
    public static final String click_set_range = "click_set_range";
    public static final String click_color_tem_id_ = "click_color_tem_id_";
    public static final String skip = "skip";

    /*V6.0.1*/

    public static final String click_music_pickup_auto = "click_music_pickup_auto";
    public static final String click_music_pickup_auto_effect_light = "click_music_pickup_auto_effect_light";
    public static final String click_music_pickup_auto_effect_base = "click_music_pickup_auto_effect_base";
    public static final String click_diy_all = "click_diy_all";
    public static final String click_diy_auto_mode = "click_diy_auto_mode";
    public static final String click_cancel_single_make_ice = "click_cancel_single_make_ice";

    public static final String click_cancel_cycle_make_ice = "click_cancel_cycle_make_ice";
    public static final String click_cancel_clean = "click_cancel_clean";
    public static final String click_output_ice_weight = "click_output_ice_weight";
    public static final String click_makeup_ice_time = "click_makeup_ice_time";
    public static final String click_log = "click_log";
    public static final String click_time_7_days = "click_time_7_days";
    public static final String click_time_30_days = "click_time_30_days";
    public static final String click_time_x_month = "click_time_x_month";
    public static final String click_output_ice_weight_discrepancy = "click_output_ice_weight_discrepancy";
    public static final String click_set_data_func = "click_set_data_func";
    public static final String click_set_clean_hint_time = "click_set_clean_hint_time";
    public static final String click_kitchen_waste_turn_on = "click_kitchen_waste_turn_on";
    public static final String click_save_turn_on = "click_save_turn_on";
    public static final String click_start_clean = "click_start_clean";


    public static final String click_activated_carbon_lifespan = "click_activated_carbon_lifespan";
    public static final String click_add_pre_time = "click_add_pre_time";
    public static final String click_modify_pre_time = "click_modify_pre_time";
    public static final String modify_activated_carbon_lifespan = "modify_activated_carbon_lifespan";
    public static final String click_waste_treatment = "click_waste_treatment";

    public static final String click_less_type_alarm_value = "click_less_type_alarm_value";
    public static final String click_more_type_alarm_value = "click_more_type_alarm_value";
    public static final String click_range_type_alarm_value = "click_range_type_alarm_value";
    public static final String click_hide_wifi_on = "click_hide_wifi_on2off";
    public static final String click_hide_wifi_off = "click_hide_wifi_off2on";
    public static final String click_safe_choose = "click_safe_choose";
    public static final String click_auto_diy_light = "click_auto_diy_light";
    public static final String click_auto_diy_base = "click_auto_diy_base";
    public static final String save_diy_in_auto = "save_diy_in_auto";
    public static final String save_diy_in_all = "save_diy_in_all";

    /*V6.1.0*/
    public static final String click_draw_util_xx = "click_draw_util_xx";
    public static final String set_colourless_awake = "set_colourless_awake";
    public static final String set_colourless_sleep = "set_colourless_sleep";
    public static final String to_binding_gateway = "to_binding_gateway";
    public static final String no_handle_gateway = "no_handle_gateway";
    public static final String click_notice_bind = "click_notice_bind";
    public static final String click_notice_no = "click_notice_no";
    public static final String turn_on_low_blue_light = "turn_on_low_blue_light";
    public static final String turn_off_low_blue_light = "turn_off_low_blue_light";
    public static final String click_detail_chart_edit = "click_detail_chart_edit";
    public static final String first_is_temp_chart = "first_is_temp_chart";
    public static final String first_is_th_chart = "first_is_th_chart";
    public static final String first_is_dp_chart = "first_is_dp_chart";
    public static final String first_is_vpd_chart = "first_is_vpd_chart";
    public static final String second_is_temp_chart = "second_is_temp_chart";
    public static final String second_is_th_chart = "second_is_th_chart";
    public static final String second_is_dp_chart = "second_is_dp_chart";
    public static final String second_is_vpd_chart = "second_is_vpd_chart";
    public static final String click_multi_data_compare = "click_multi_data_compare";
    public static final String user_two_device_compare = "user_two_device_compare";
    public static final String user_three_device_compare = "user_three_device_compare";

    public static final String click_sleep_mode_question = "click_sleep_mode_question";
    public static final String click_edit = "click_edit";
    public static final String click_name_add = "click_name_add";
    public static final String click_name_modify = "click_name_modify";
    public static final String set_curve_hours_pre = "set_curve_hours_";
    public static final String get_recommend_curve = "get_recommend_curve";
    public static final String turn_2_part = "turn_2_part";
    public static final String turn_2_all = "turn_2_all";
    public static final String click_scene_edit = "click_scene_edit";
    public static final String click_hand_movement = "click_hand_movement";
    public static final String use_auto_movement = "use_auto_movement";
    public static final String click_save_scene = "click_save_scene";
    public static final String save_scene_suc = "save_scene_suc";
    public static final String use_part_scene = "use_part_scene";
    public static final String use_all_scene = "use_all_scene";
    public static final String click_scene_tab_my = "click_scene_tab_my";
    public static final String apply_my_scene = "apply_my_scene";
    public static final String click_my_scene_edit = "click_my_scene_edit";
    public static final String click_tab_user_share = "click_tab_user_share";
    public static final String apply_user_share_scene = "apply_user_share_scene";
    public static final String save_user_share_scene = "save_user_share_scene";
    public static final String detail_my_share_scene = "detail_my_share_scene";
    public static final String click_cancel_share_scene = "click_cancel_share_scene";
    public static final String show_cancel_share_scene_dialog = "show_cancel_share_scene_dialog";

    public static final String post = "post";
    public static final String topic = "topic";
    public static final String user = "user";
    public static final String related_to_more = "related_to_more";
    public static final String nearly_year = "nearly_year";
    public static final String only_follow_user = "only_follow_user";
    public static final String discuss_more = "discuss_more";
    public static final String like_more = "like_more";
    public static final String publish_time = "publish_time";
    public static final String comment_more = "comment_more";
    public static final String no_limit = "no_limit";
    public static final String nearly_day = "nearly_day";
    public static final String nearly_week = "nearly_week";
    public static final String nearly_month = "nearly_month";
    public static final String only_govee = "only_govee";
    private static final String hide_wifi_connect_fail = "hide_wifi_connect_fail";
    private static final String hide_wifi_connect_suc = "hide_wifi_connect_suc";

    private static final String None = "None";
    private static final String WEP = "WEP";
    private static final String WPA_WPA2_personal = "WPA/WPA2-personal";
    private static final String WPA3_personal = "WPA3-personal";
    public static final String click_save_illumination_snapshot = "click_save_illumination_snapshot";

    public static final String click_auto_identify_confirm = "click_auto_identify_confirm";
    public static final String auto_identify_fail = "auto_identify_fail";
    public static final String click_auto_identify_draw_shape = "click_auto_identify_draw_shape";
    public static final String click_draw_shape_confirm = "click_draw_shape_confirm";
    public static final String work_mode_large = "work_mode_large";
    public static final String work_mode_medium = "work_mode_medium";
    public static final String work_mode_small = "work_mode_small";

    //设备详情页-自动模式-预设按钮
    public static final String automode_scene_recommend = "automode_scene_recommend";
    //预设弹窗-关闭按钮
    public static final String automode_scene_recommend_close = "automode_scene_recommend_close";
    //预设弹窗-应用按钮
    public static final String automode_scene_recommend_apply = "automode_scene_recommend_apply";
    //预设弹窗-我要反馈按钮
    public static final String automode_scene_recommend_feedback = "automode_scene_recommend_feedback";
    //预设弹窗-tab按钮
    public static final String automode_scene_recommend_tab_s = "automode_scene_recommend_tab_";
    //评分反馈弹窗-提交按钮
    public static final String automode_scene_recommend_feedback_submit = "automode_scene_recommend_feedback_submit";
    //评分反馈弹窗-关闭按钮
    public static final String automode_scene_recommend_feedback_close = "automode_scene_recommend_feedback_close";

    //6.0.30
    public static final String click_pressure_linkage = "click_pressure_linkage";
    public static final String click_pressure_chart_time_picker = "click_pressure_chart_time_picker";
    public static final String click_pressure_log_more = "click_pressure_log_more";
    public static final String click_pressure_setting_gateway = "click_pressure_setting_gateway";
    public static final String click_pressure_setting_press = "click_pressure_setting_press";
    public static final String click_pressure_setting_delete_log = "click_pressure_setting_delete_log";
    public static final String click_pressure_setting_battery_info = "click_pressure_setting_battery_info";
    public static final String cancel = "cancel";
    public static final String confirm = "confirm";
    public static final String click_pressure_setting_reset = "click_pressure_setting_reset";
    public static final String delay_off_on = "delay_off_on";
    public static final String delay_off_off = "delay_off_off";

    public static final String iot_apply_success = "iot_apply_success";
    public static final String iot_apply_fail = "iot_apply_fail";
    public static final String ble_apply_success = "ble_apply_success";
    public static final String ble_apply_fail = "ble_apply_fail";
    public static final String layer_nums_1 = "single_layer";
    public static final String layer_nums_2_4 = "2_4_layer";
    public static final String layer_nums_5_7 = "5_7_layer";
    public static final String layer_nums_8_10 = "8_10_layer";
    public static final String iot_apply = "IOT";
    public static final String ble_apply = "BLE";
    public static final String click_mode_gear_1 = "click_mode_gear_1";
    public static final String click_mode_gear_2 = "click_mode_gear_2";
    public static final String click_mode_gear_3 = "click_mode_gear_3";
    public static final String click_mode_auto_keep = "click_mode_auto_keep";
    public static final String click_mode_auto_autostop = "click_mode_auto_autostop";
    public static final String click_mode_auto_gear_x = "click_mode_auto_gear_";
    public static final String click_save_snapshot = "click_save_snapshot";
    public static final String click_mode_share = "click_mode_share";
    public static final String click_mode_display = "click_mode_display";
    public static final String click_mode_carousel = "click_mode_carousel";

    /****************** V6.1.1     ******************/
    public static final String click_shortcuts_material_library = "click_shortcuts_material_library";
    public static final String expose_more_material_library = "expose_more_material_library";
    public static final String click_more_material_library = "click_more_material_library";
    public static final String apply_material_library = "apply_material_library";
    public static final String click_save_compound_effect = "click_save_compound_effect";
    public static final String expose_compound_detail = "expose_compound_detail";
    public static final String click_material_library_in_graffiti = "click_material_library_in_graffiti";
    public static final String merge_material_ = "merge_material_";
    public static final String click_material_in_graffiti_ = "click_material_in_graffiti_";
    public static final String mode_turbo = "mode_turbo";
    public static final String click_mode_auto_gear = "click_mode_auto_gear";
    public static final String click_matter_set = "click_matter_set";
    public static final String click_manual_gear_set = "click_manual_gear_set";

    public static final String apply_compound_material_ = "apply_compound_material_";
    public static final String compound_preview_ = "compound_preview_";

    public static final String click_graffiti_material_ = "click_graffiti_material_";
    public static final String click_list_material_ = "click_list_material_";
    public static final String click_compount_material_ = "click_compount_material_";
    public static final String compound_save_ = "compound_save_";
    public static final String compound_save_suc = "compound_save_suc";
    public static final String apply_library_material_ = "apply_library_material_";
    //61D2/D3/D4/D5 V6.1.2统计
    public static final String click_manual_draw = "click_manual_draw";
    public static final String click_finish_in_manual_draw = "click_finish_in_manual_draw";
    public static final String click_finish_in_photo_recognize = "click_finish_in_photo_recognize";
    public static final String click_on_draw_assit = "click_on_draw_assit";
    public static final String click_off_draw_assit = "click_off_draw_assit";

    //H7149加湿器 V6.1.2统计
    public static final String click_manual_gear = "click_manual_gear";
    public static final String click_auto_gear = "click_auto_gear";
    public static final String click_fog_grade_ = "click_fog_grade_";
    public static final String click_comfort_sleep = "click_comfort_sleep";
    public static final String click_target_th = "click_target_th";

    public static final String click_target_th_low_on = "click_target_th_low_on";
    public static final String click_target_th_low_off = "click_target_th_low_off";
    public static final String click_target_vpd = "click_target_vpd";
    public static final String click_target_hum_in_80_90 = "click_target_hum_in_80_90";
    public static final String click_preset_ = "click_preset_";
    public static final String click_preset_apply_ = "click_preset_apply_";
    public static final String click_horticultural_plant_auto = "click_horticultural_plant_auto";
    public static final String apply_horticultural_plant_auto = "apply_horticultural_plant_auto";

    public static final String click_th_unlock_on = "click_th_unlock_on";
    public static final String click_th_unlock_off = "click_th_unlock_off";
    public static final String click_target_vpd_on = "click_target_vpd_on";
    public static final String click_target_vpd_off = "click_target_vpd_off";

    public static final String click_light_switch = "click_light_switch";
    public static final String click_bottom_switch = "click_bottom_switch";
    public static final String click_white_noise_on = "click_white_noise_on";
    public static final String click_white_noise_off = "click_white_noise_off";

    public static final String motion_mode_detail = "motion_mode_detail";
    public static final String motion_mode_turn_on_scene = "motion_mode_turn_on_scene";
    public static final String motion_mode_turn_off_scene = "motion_mode_turn_off_scene";
    public static final String motion_mode_scene_num_ = "motion_mode_scene_num_";

    public static final String click_cascade = "click_cascade";
    public static final String cascade_subdevice_num = "cascade_subdevice_num_";
    public static final String relieve_cascade = "relieve_cascade";
    public static final String into_share_search = "into_share_search";
    public static final String search_result_no_data = "search_result_no_data";
    public static final String search_result_error = "search_result_error";
    public static final String search_result_suc = "search_result_suc";
    public static final String click_share_diy_ = "click_share_diy_";
    public static final String share_video_play_ = "share_video_play_";
    public static final String click_add_diy = "click_add_diy";
    public static final String click_manager_add_diy = "click_manager_add_diy";
    public static final String click_manage_diy = "click_manage_diy";
    public static final String click_edit_my_diy = "click_edit_my_diy";
    public static final String click_edit_other_diy = "click_edit_other_diy";

    public static final String click_manager_edit_my_diy = "click_manager_edit_my_diy";
    public static final String click_manager_edit_other_diy = "click_manager_edit_other_diy";
    public static final String click_group_manage = "click_group_manage";

    public static final String click_visible_manage = "click_visible_manage";
    public static final String click_sort_to_ascii = "click_sort_to_ascii";
    public static final String click_sort_to_time = "click_sort_to_time";
    public static final String click_search = "click_search";

    public static final String click_delete_group = "click_delete_group";
    public static final String click_group_detail = "click_group_detail";
    public static final String click_add_group = "click_add_group";
    public static final String move_diy_to_other = "move_diy_to_other";
    public static final String click_delete_diys = "click_delete_diys";

    public static final String click_edit_group_name = "click_edit_group_name";
    public static final String click_copy = "click_copy";
    public static final String click_share = "click_share";
    public static final String click_name = "click_name";
    public static final String click_icon = "click_icon";

    public static final String click_group = "click_group";
    public static final String click_delete = "click_delete";
    public static final String click_share_suc = "click_share_suc";
    public static final String click_share_no_creater = "click_share_no_creater";
    public static final String click_share_no_su = "click_share_no_su";

    public static final String click_apply = "click_apply";
    public static final String send_cmd_in_edit = "send_cmd_in_edit";
    public static final String click_save = "click_save";
    public static final String click_color_recommend = "click_color_recommend";
    public static final String click_Intelligent_color_selection = "click_Intelligent_color_selection";

    public static final String click_sample_diy = "click_sample_diy";
    public static final String click_mix_diy = "click_mix_diy";
    public static final String click_high_diy = "click_high_diy";
    public static final String show_diy_gif_dialog = "show_diy_gif_dialog";
    public static final String click_diy_effect_ = "click_diy_effect_";
    public static final String click_mix_diy_effect_ = "click_mix_diy_effect_";

    public static final String float_bar = "float_bar";
    public static final String community_rules = "community_rules";
    public static final String community_rules_detail = "community_rules_detail";
    public static final String show_float_bar = "show_float_bar";

    public static final String hide_float_bar = "hide_float_bar";
    public static final String discuss_turn_on_pure_mode = "discuss_turn_on_pure_mode";
    public static final String discuss_turn_off_pure_mode = "discuss_turn_off_pure_mode";
    public static final String reply = "reply";

    public static final String translate = "translate";
    public static final String think = "think";
    public static final String expect = "expect";
    public static final String generate_chat_summary = "generate_chat_summary";

    public static final String filter_all = "filter_all";
    public static final String filter_related_to_me = "filter_related_to_me";
    public static final String filter_long_post_only = "filter_long_post_only";
    public static final String filter_govee_speech = "filter_govee_speech";
    public static final String topic_turn_on_pure_mode = "topic_turn_on_pure_mode";
    public static final String topic_turn_off_pure_mode = "topic_turn_off_pure_mode";
    public static final String draft_dialog_save = "draft_dialog_save";
    public static final String draft_dialog_cancel = "draft_dialog_cancel";

    public static final String clear_label_dialog_confirm = "clear_label_dialog_confirm";
    public static final String clear_label_dialog_cancel = "clear_label_dialog_cancel";
    public static final String insert_label_dialog_confirm = "insert_label_dialog_confirm";
    public static final String insert_label_dialog_cancel = "insert_label_dialog_cancel";

    public static final String multi_device_compare_time_1_hour = "multi_device_compare_time_1_hour";
    public static final String click_set_time_range = "click_set_time_range";
    public static final String multi_device_compare_time_1_day = "multi_device_compare_time_1_day";
    public static final String multi_device_compare_time_1_week = "multi_device_compare_time_1_week";
    public static final String multi_device_compare_time_1_month = "multi_device_compare_time_1_month";
    public static final String multi_device_compare_time_1_year = "multi_device_compare_time_1_year";
    public static final String click_compare_history_data = "click_compare_history_data";
    public static final String compare_history_time_2_hours = "compare_history_time_2_hours";
    public static final String compare_history_time_2_days = "compare_history_time_2_days";
    public static final String compare_history_time_2_weeks = "compare_history_time_2_weeks";
    public static final String compare_history_time_2_months = "compare_history_time_2_months";
    public static final String compare_history_time_2_years = "compare_history_time_2_years";
    public static final String click_filter = "click_filter";
    public static final String click_set_time_range_1 = "click_set_time_range_1";
    public static final String click_set_time_range_2 = "click_set_time_range_2";
    public static final String turn_on_laser = "turn_on_laser";
    public static final String click_laser_flicker = "click_laser_flicker";
    public static final String click_laser_breathe = "click_laser_breathe";
    public static final String click_laser_swimming = "click_laser_swimming";
    public static final String turn_on_aurora = "turn_on_aurora";
    /****************** V6.2.0     ******************/
    public static final String apply_library_material_gif = "click_material_gif_";
    public static final String apply_material_gif = "apply_material_gif";
    public static final String click_material_gif_topic = "click_material_gif_topic_";
    public static final String click_export_log = "click_export_log";
    public static final String click_send_in_export_mail = "click_send_in_export_mail";

    /****************** V6.2.3 ******************/
    public static final String click_lamp_beads_recognize = "click_lamp_beads_recognize";
    public static final String click_lamp_beads_recognize_3d = "click_lamp_beads_recognize_3d";
    public static final String click_lamp_beads_recognize_2d = "click_lamp_beads_recognize_2d";
    public static final String click_lamp_beads_recognize_fail = "click_lamp_beads_recognize_fail";
    public static final String click_lamp_beads_recognize_suc = "click_lamp_beads_recognize_suc";
    public static final String click_lamp_beads_recognize_from_diy = "click_lamp_beads_recognize_from_diy";
    public static final String click_lamp_beads_recognize_from_grif = "click_lamp_beads_recognize_from_grif";

    /****************** V6.2.4 ******************/
    public static final String click_select_cookbook = "click_select_cookbook";
    public static final String click_timer_enter = "click_timer_enter";
    public static final String click_target_tem_high = "click_target_tem_high";
    public static final String click_lamp_beads_recognize_setting = "click_lamp_beads_recognize_setting";
    public static final String click_target_tem_low = "click_target_tem_low";
    public static final String click_environment_tem_high = "click_environment_tem_high";

    public static final String click_environment_tem_low = "click_environment_tem_low";
    public static final String click_recently_use = "click_recently_use";
    public static final String click_change_probe = "click_change_probe";
    public static final String click_cookbook_ = "click_cookbook_";
    public static final String click_pre_worn_on = "click_pre_worn_on";
    public static final String click_pre_worn_off = "click_pre_worn_off";
    public static final String click_bind_probe_again = "click_bind_probe_again";
    public static final String bind_probe_again = "bind_probe_again";
    public static final String click_buzzer_on = "click_buzzer_on";
    public static final String click_buzzer_off = "click_buzzer_off";


    public static final String show_back_top_btn = "show_back_top_btn";
    public static final String click_back_top_btn = "click_back_top_btn";

    public static final String unedit_click_name = "unedit_click_name";
    public static final String unedit_click_icon = "unedit_click_icon";
    public static final String unedit_click_group = "unedit_click_group";
    public static final String unedit_click_delete = "unedit_click_delete";


    /****************** V6.3.0     ******************/
    public static final String click_tag_ = "click_tag_";
    public static final String click_comment = "click_comment";
    public static final String click_content_more = "click_content_more";
    public static final String click_like = "click_like";
    public static final String click_dislike = "click_dislike";
    public static final String click_right_top_more = "click_right_top_more";
    public static final String show_complain_dialog = "show_complain_dialog";
    public static final String show_ = "show_";
    public static final String click_ = "click_";
    public static final String click_translate_content = "click_translate_content";
    public static final String click_translate_comment = "click_translate_comment";
    public static final String show_editable_hint_dialog = "show_editable_hint_dialog";
    public static final String from_communicate = "from_communicate";
    public static final String from_user_share = "from_user_share";
    public static final String from_square = "from_square";
    public static final String from_homepage = "from_homepage";
    public static final String tag_ = "tag_";
    public static final String click_filter_when_search = "click_filter_when_search";
    public static final String click_filter_all = "click_filter_all";
    public static final String click_filter_editable = "click_filter_editable";
    public static final String click_filter_cur_model = "click_filter_cur_model";

    public static final String click_music_effect_when_ble_sound = "click_music_effect_when_ble_sound";
    public static final String speaker_pairing_switch = "speaker_pairing_switch";
    public static final String expose_upload_log = "expose_upload_log";
    public static final String click_upload_log = "click_upload_log";
    public static final String agree = "agree";
    public static final String disagree = "disagree";
    public static final String text_load_fail = "text_load_fail";
    public static final String click_hdmi_input_icon_ = "click_hdmi_input_icon_";
    public static final String click_voice_assistant = "click_voice_assistant";
    public static final String click_local_service = "click_local_service";

    /***** V6.3.1 *****/
    public static final String turn_on_sound_warn = "turn_on_sound_warn";
    public static final String turn_off_sound_warn = "turn_off_sound_warn";
    public static final String diy_bind_music = "diy_bind_music";
    public static final String graffiti_click_superpose = "graffiti_click_superpose";
    public static final String graffiti_click_banner = "graffiti_click_banner";
    public static final String graffiti_click_mix = "graffiti_click_mix";
    public static final String graffiti_superpose_change_2_icon = "graffiti_superpose_change_2_icon";
    public static final String graffiti_superpose_click_add_layer = "graffiti_superpose_click_add_layer";
    public static final String graffiti_superpose_click_delete_layer = "graffiti_superpose_click_delete_layer";
    public static final String graffiti_superpose_click_copy_layer = "graffiti_superpose_click_copy_layer";
    public static final String graffiti_superpose_click_magic_wand = "graffiti_superpose_click_magic_wand";
    public static final String graffiti_superpose_click_edit_text = "graffiti_superpose_click_edit_text";
    public static final String graffiti_superpose_click_up_floor = "graffiti_superpose_click_up_floor";
    public static final String graffiti_superpose_click_down_floor = "graffiti_superpose_click_down_floor";
    public static final String graffiti_banner_change_2_icon = "graffiti_banner_change_2_icon";
    public static final String graffiti_banner_click_add_layer = "graffiti_banner_click_add_layer";
    public static final String graffiti_banner_click_delete_layer = "graffiti_banner_click_delete_layer";
    public static final String graffiti_banner_click_copy_layer = "graffiti_banner_click_copy_layer";
    public static final String graffiti_banner_click_magic_wand = "graffiti_banner_click_magic_wand";
    public static final String graffiti_banner_click_edit_text = "graffiti_banner_click_edit_text";
    public static final String graffiti_banner_click_up_floor = "graffiti_banner_click_up_floor";
    public static final String graffiti_banner_click_down_floor = "graffiti_banner_click_down_floor";
    public static final String graffiti_mix_turn_on_background = "graffiti_mix_turn_on_background";
    public static final String graffiti_mix_turn_off_background = "graffiti_mix_turn_off_background";
    public static final String graffiti_mix_change_2_icon = "graffiti_mix_change_2_icon";
    public static final String graffiti_mix_click_add_layer = "graffiti_mix_click_add_layer";
    public static final String graffiti_mix_click_delete_layer = "graffiti_mix_click_delete_layer";
    public static final String graffiti_mix_click_copy_layer = "graffiti_mix_click_copy_layer";
    public static final String graffiti_mix_click_magic_wand = "graffiti_mix_click_magic_wand";
    public static final String graffiti_mix_click_edit_text = "graffiti_mix_click_edit_text";
    public static final String graffiti_mix_click_up_floor = "graffiti_mix_click_up_floor";
    public static final String graffiti_mix_click_down_floor = "graffiti_mix_click_down_floor";
    public static final String graffiti_mix_animation_turn_on = "graffiti_mix_animation_turn_on";
    public static final String graffiti_mix_animation_turn_off = "graffiti_mix_animation_turn_off";
    public static final String graffiti_mix_animation_change_2_icon = "graffiti_mix_animation_change_2_icon";
    public static final String graffiti_mix_animation_click_add_layer = "graffiti_mix_animation_click_add_layer";
    public static final String graffiti_mix_animation_click_delete_layer = "graffiti_mix_animation_click_delete_layer";
    public static final String graffiti_mix_animation_click_copy_layer = "graffiti_mix_animation_click_copy_layer";
    public static final String graffiti_mix_animation_click_magic_wand = "graffiti_mix_animation_click_magic_wand";
    public static final String graffiti_mix_animation_click_edit_text = "graffiti_mix_animation_click_edit_text";
    public static final String graffiti_mix_animation_click_up_floor = "graffiti_mix_animation_click_up_floor";
    public static final String graffiti_mix_animation_click_down_floor = "graffiti_mix_animation_click_down_floor";
    public static final String graffiti_mix_prospect_turn_on = "graffiti_mix_prospect_turn_on";
    public static final String graffiti_mix_prospect_turn_off = "graffiti_mix_prospect_turn_off";
    public static final String graffiti_mix_prospect_change_2_icon = "graffiti_mix_prospect_change_2_icon";
    public static final String graffiti_mix_prospect_click_add_layer = "graffiti_mix_prospect_click_add_layer";
    public static final String graffiti_mix_prospect_click_delete_layer = "graffiti_mix_prospect_click_delete_layer";
    public static final String graffiti_mix_prospect_click_copy_layer = "graffiti_mix_prospect_click_copy_layer";
    public static final String graffiti_mix_prospect_click_magic_wand = "graffiti_mix_prospect_click_magic_wand";
    public static final String graffiti_mix_prospect_click_edit_text = "graffiti_mix_prospect_click_edit_text";
    public static final String graffiti_mix_prospect_click_up_floor = "graffiti_mix_prospect_click_up_floor";
    public static final String graffiti_mix_prospect_click_down_floor = "graffiti_mix_prospect_click_down_floor";
    public static final String graffiti_full_click_open_layer = "graffiti_full_click_open_layer";
    public static final String graffiti_full_click_fold_layer = "graffiti_full_click_fold_layer";
    public static final String graffiti_full_click_apply_layer = "graffiti_full_click_apply_layer";
    public static final String graffiti_full_click_paint = "graffiti_full_click_paint";
    public static final String graffiti_full_click_rubber = "graffiti_full_click_rubber";
    public static final String graffiti_full_click_bucket = "graffiti_full_click_bucket";
    public static final String graffiti_full_click_pipette = "graffiti_full_click_pipette";
    public static final String graffiti_full_click_symmetry = "graffiti_full_click_symmetry";
    public static final String graffiti_full_click_image = "graffiti_full_click_image";
    public static final String graffiti_full_click_selection = "graffiti_full_click_selection";
    public static final String graffiti_full_click_material = "graffiti_full_click_material";
    public static final String graffiti_full_click_gradual = "graffiti_full_click_gradual";
    public static final String graffiti_full_click_upload_image = "graffiti_full_click_upload_image";
    public static final String graffiti_full_click_upload_gif = "graffiti_full_click_upload_gif";
    public static final String graffiti_full_click_revoke = "graffiti_full_click_revoke";
    public static final String graffiti_full_click_recover = "graffiti_full_click_recover";
    public static final String graffiti_full_click_clear = "graffiti_full_click_clear";
    public static final String graffiti_full_click_preview = "graffiti_full_click_preview";
    public static final String click_display_weather = "click_display_weather";
    public static final String click_display_weather_set_city = "click_display_weather_set_city";
    public static final String click_display_time = "click_display_time";
    public static final String click_display_contest = "click_display_contest";
    public static final String click_display_contest_main = "click_display_contest_main";
    public static final String click_display_contest_today = "click_display_contest_today";
    public static final String click_display_finance = "click_display_finance";
    public static final String click_display_stopwatch = "click_display_stopwatch";
    public static final String click_display_countdown = "click_display_countdown";
    public static final String turn_auto_update_on = "turn_auto_update_on";
    public static final String turn_auto_update_off = "turn_auto_update_off";
    public static final String quick_keys_time_panel = "quick_keys_time_panel";
    public static final String quick_keys_weather = "quick_keys_weather";
    public static final String quick_keys_countdown = "quick_keys_countdown";
    public static final String quick_keys_stopwatch = "quick_keys_stopwatch";
    public static final String quick_keys_music_mode = "quick_keys_music_mode";
    public static final String turn_on_power_on_prompt_sound = "turn_on_power_on_prompt_sound";
    public static final String turn_off_power_on_prompt_sound = "turn_off_power_on_prompt_sound";
    public static final String turn_on_power_off_prompt_sound = "turn_on_power_off_prompt_sound";
    public static final String turn_off_power_off_prompt_sound = "turn_off_power_off_prompt_sound";
    public static final String turn_on_keys_prompt_sound = "turn_on_keys_prompt_sound";
    public static final String turn_off_keys_prompt_sound = "turn_off_keys_prompt_sound";
    public static final String turn_on_countdown_prompt_sound = "turn_on_countdown_prompt_sound";
    public static final String turn_off_countdown_prompt_sound = "turn_off_countdown_prompt_sound";
    public static final String turn_on_indictor_light = "turn_on_indictor_light";
    public static final String turn_off_indictor_light = "turn_off_indictor_light";
    public static final String timer_awaken_turn_on_light_effect = "timer_awaken_turn_on_light_effect";
    public static final String timer_awaken_turn_off_light_effect = "timer_awaken_turn_off_light_effect";
    public static final String timer_awaken_turn_on_sound = "timer_awaken_turn_on_sound";
    public static final String timer_awaken_turn_off_sound = "timer_awaken_turn_off_sound";
    public static final String timer_sleep_turn_on_light_effect = "timer_sleep_turn_on_light_effect";
    public static final String timer_sleep_turn_off_light_effect = "timer_sleep_turn_off_light_effect";
    public static final String timer_sleep_turn_on_sound = "timer_sleep_turn_on_sound";
    public static final String timer_sleep_turn_off_sound = "timer_sleep_turn_off_sound";
    public static final String oneclick_sleep_turn_on_light_effect = "oneclick_sleep_turn_on_light_effect";
    public static final String oneclick_sleep_turn_off_light_effect = "oneclick_sleep_turn_off_light_effect";
    public static final String oneclick_sleep_turn_on_sound = "oneclick_sleep_turn_on_sound";
    public static final String oneclick_sleep_turn_off_sound = "oneclick_sleep_turn_off_sound";
    public static final String linkage_sleep_turn_on_light_effect = "linkage_sleep_turn_on_light_effect";
    public static final String linkage_sleep_turn_off_light_effect = "linkage_sleep_turn_off_light_effect";
    public static final String linkage_sleep_turn_on_sound = "linkage_sleep_turn_on_sound";
    public static final String linkage_sleep_turn_off_sound = "linkage_sleep_turn_off_sound";
    public static final String banner_background_music_turn_on = "banner_background_music_turn_on";
    public static final String banner_background_music_turn_off = "banner_background_music_turn_off";
    public static final String banner_background_music_relate = "banner_background_music_relate";
    public static final String banner_apply = "banner_apply";
    public static final String delete_banner_plan = "delete_banner_plan";
    public static final String diy_high_laser_order_apply = "diy_high_laser_order_apply";
    public static final String diy_high_laser_random_apply = "diy_high_laser_random_apply";
    public static final String set_effect_switch_turn_on = "set_effect_switch_turn_on";
    public static final String set_effect_switch_turn_off = "set_effect_switch_turn_off";


    public static final String click_effect_center = "click_effect_center";
    public static final String reset_effect_center = "reset_effect_center";
    public static final String graffiti_model_angle_reset = " graffiti_model_angle_reset";
    public static final String graffiti_model_enlarge = "graffiti_model_enlarge";
    public static final String graffiti_model_shrink = "graffiti_model_shrink";


    public static final String click_mode_banner = "click_mode_banner";
    public static final String send_cmd_interaction = "send_cmd_interaction";
    public static final String send_cmd_operation = "send_cmd_operation";
    public static final String send_cmd_banner = "send_cmd_banner";
    public static final String use_mode_operation = "use_mode_operation";
    public static final String expose_luminblend = "expose_luminblend";
    public static final String send_cmd_luminblend = "send_cmd_luminblend";


    public static final String space_music_feast_turn_on = "space_music_feast_turn_on";
    public static final String space_music_feast_turn_detail = "space_music_feast_turn_detail";
    public static final String space_music_feast_detail = "space_music_feast_detail";
    public static final String turn_on_space_music_feast = "turn_on_space_music_feast";
    public static final String turn_off_space_music_feast = "turn_off_space_music_feast";
    public static final String expose_space_music_feast = "expose_space_music_feast";
    public static final String apply_effect = "apply_effect";
    public static final String space_music_feast_distribute_area = "space_music_feast_distribute_area";
    public static final String show_add_device_dialog = "show_add_device_dialog";
    public static final String click_modify_device_dialog_toset = "click_modify_device_dialog_toset";
    public static final String click_add_device_dialog_toset = "click_add_device_dialog_toset";
    public static final String show_modify_device_dialog = "show_modify_device_dialog";

    public static final String turn_on_ble = "turn_on_ble";
    public static final String turn_off_ble = "turn_off_ble";
    public static final String blackout_select_old_status = "blackout_select_old_status";
    public static final String blackout_select_keep_light = "blackout_select_keep_light";
    public static final String show_led_recognize_init_dialog = "show_led_recognize_init_dialog";
    public static final String show_led_recognize_init_dialog_fail = "show_led_recognize_init_dialog_fail";
    public static final String click_close_led_recognize_init_dialog = "click_close_led_recognize_init_dialog";
    public static final String input_text_ = "input_text_";
    public static final String send_image_ = "send_image_";
    public static final String click_set = "click_set";
    public static final String click_history = "click_history";
    public static final String click_ai_build_save = "click_ai_build_save";
    public static final String click_history_diy_apply = "click_history_diy_apply";
    public static final String click_function_feedback = "click_function_feedback";
    public static final String show_android_lib_init_dialog = "show_android_lib_init_dialog";
    public static final String show_android_lib_init_dialog_fail = "show_android_lib_init_dialog_fail";
    public static final String click_close_android_lib_init_dialog = "click_close_android_lib_init_dialog";
    public static final String camera_foam_foam_use_model = "camera_foam_foam_use_model";
    public static final String camera_foam_foam_use_default = "camera_foam_foam_use_default";
    public static final String save_color_in_diy = "save_color_in_diy";
    public static final String number = "number";
    public static final String send_cmd_in_widget = "send_cmd_in_widget";
    public static final String send_cmd_in_panel = "send_cmd_in_panel";
    public static final String send_cmd_in_list = "send_cmd_in_list";
    public static final String send_cmd_in_detail = "send_cmd_in_detail";
    public static final String send_cmd_in_alexa = "send_cmd_in_alexa";
    public static final String send_cmd_in_other = "send_cmd_in_other";

    //V6.4.0 失败前置预警
    public static final String account_exception = "account_exception";
    public static final String password_exception = "password_exception";
    public static final String email_exception = "email_exception";
    public static final String verification_code_not_received = "verification_code_not_received";
    public static final String retrieve_password = "retrieve_password";
    public static final String register_account = "register_account";
    public static final String cancel_account = "cancel_account";
    public static final String app_version_error = "app_version_error";
    public static final String sku_protocol_error = "sku_protocol_error";
    public static final String device_lock_exception = "device_lock_exception";
    public static final String device_special = "device_special";
    public static final String distribution_network_overtime = "distribution_network_overtime";
    public static final String distribution_network_cannot_connect = "distribution_network_cannot_connect";
    public static final String disconnect_iot_times = "disconnect_iot_times";
    public static final String disconnect_iot_time_ = "disconnect_iot_time_";
    public static final String ble_connect_exception = "ble_connect_exception";
    public static final String device_into_detail_excetion = "device_into_detail_excetion";
    public static final String send_cmd_fail_card_switch = "send_cmd_fail_card_switch";
    public static final String send_cmd_fail_video_mode = "send_cmd_fail_video_mode";
    public static final String send_cmd_fail_illuminate_mode = "send_cmd_fail_illuminate_mode";
    public static final String send_cmd_fail_color_mode = "send_cmd_fail_color_mode";
    public static final String send_cmd_fail_scene_mode = "send_cmd_fail_scene_mode";
    public static final String send_cmd_fail_graffiti_mode = "send_cmd_fail_graffiti_mode";
    public static final String send_cmd_fail_AI_mode = "send_cmd_fail_AI_mode";
    public static final String send_cmd_fail_more_mode = "send_cmd_fail_more_mode";
    public static final String send_cmd_fail_work_shop = "send_cmd_fail_work_shop";
    public static final String send_cmd_fail_effect_lib = "send_cmd_fail_effect_lib";
    public static final String auto_play_exception = "auto_play_exception";
    public static final String send_cmd_fail_music_mode = "send_cmd_fail_music_mode";
    public static final String send_cmd_fail_DIY_mode = "send_cmd_fail_DIY_mode";
    public static final String OTA_process = "OTA_process";
    public static final String OTA_fail = "OTA_fail";
    public static final String show_update_dialog_repeat = "show_update_dialog_repeat";
    public static final String acquire_points_exception = "acquire_points_exception";
    public static final String bind_device_app_version_error = "bind_device_app_version_error";
    public static final String send_cmd_fail_switch_mode = "send_cmd_fail_switch_mode";
    public static final String click_mode_operation = "click_mode_operation";
    public static final String press_voice = "press_voice";
    public static final String click_ai_tab_in_scene_feast = "click_ai_tab_in_scene_feast";
    public static final String click_govee_preset_in_scene_feast = "click_govee_preset_in_scene_feast";
    public static final String input_text = "input_text";
    public static final String send_image = "send_image";
    public static final String turn_on_timer_delay_off = "turn_on_timer_delay_off";
    public static final String turn_off_timer_delay_off = "turn_off_timer_delay_off";
    //自定义统计
    public static final String bug_recognition_failed = "bug_recognition_failed";
    public static final String bug_tensorflow_failed = "bug_tensorflow_failed";
    public static final String bug_tf_model_failed = "bug_tensorflow_failed";


    //H5044+H5059
    public static final String click_sub_device = "click_sub_device";
    public static final String click_add_sub_device = "click_add_sub_device";
    public static final String show_max_device_hint = "show_max_device_hint";
    public static final String click_indictor_light_switch = "click_indictor_light_switch";
    public static final String turn_on_app_offline_warn = "turn_on_app_offline_warn";
    public static final String turn_off_app_offline_warn = "turn_off_app_offline_warn";
    public static final String turn_on_email_warn = "turn_on_email_warn";
    public static final String turn_off_email_warn = "turn_off_email_warn";
    public static final String set_buzzer_quict = "set_buzzer_quict";
    public static final String set_buzzer_low = "set_buzzer_low";
    public static final String set_buzzer_medium = "set_buzzer_medium";
    public static final String set_buzzer_loud = "set_buzzer_loud";
    public static final String click_how_bind_sub_device = "click_how_bind_sub_device";
    public static final String click_warn_dialog_yes = "click_warn_dialog_yes";
    public static final String click_warn_dialog_no = "click_warn_dialog_no";
    public static final String click_know = "click_know";
    public static final String click_check_network = "click_check_network";
    public static final String check_network_suc = "check_network_suc";
    public static final String check_network_exception = "check_network_exception";
    public static final String turn_on_leak_water_warn_normal = "turn_on_leak_water_warn_normal";
    public static final String turn_off_leak_water_warn_normal = "turn_off_leak_water_warn_normal";
    public static final String turn_on_leak_water_warn_email = "turn_on_leak_water_warn_email";
    public static final String turn_off_leak_water_warn_email = "turn_off_leak_water_warn_email";
    public static final String set_low_battery_warn = "set_low_battery_warn";
    public static final String set_wolume_quict = "set_wolume_quict";
    public static final String set_wolume_low = "set_wolume_low";
    public static final String set_wolume_medium = "set_wolume_medium";
    public static final String set_wolume_loud = "set_wolume_loud";
    public static final String click_search_sub_device = "click_search_sub_device";
    public static final String turn_off_device_warn = "turn_off_device_warn";
    public static final String turn_off_low_battery_warn = "turn_off_low_battery_warn";
    //V6.5
    public static final String shortcuts = "shortcuts";
    public static final String people = "people";
    public static final String time_1_10s = "1-10s";
    public static final String time_11_30s = "11-30s";
    public static final String time_31_60s = "31-60s";
    public static final String time_1_3min = "1-3min";
    public static final String time_3_5min = "3-5min";
    public static final String time_5_10min = "5-10min";
    public static final String time_11_15min = "11-15min";
    public static final String m15min = "m15min";
    public static final String expose = "expose";
    public static final String click_more = "click_more";
    public static final String click_h5_download_url = "click_h5_download_url";
    public static final String click_feedback = "click_feedback";
    public static final String click_newest_version = "click_newest_version";
    public static final String click_no_tips = "click_no_tips";
    public static final String click_create_group = "click_create_group";
    public static final String click_add_card = "click_add_card";
    public static final String click_close = "click_close";
    public static final String click_to_edit = "click_to_edit";
    public static final String shortcut = "shortcut";
    public static final String one_click = "one_click";
    public static final String feast_scene = "feast_scene";
    public static final String feast_music = "feast_music";
    public static final String feast_video = "feast_video";
    public static final String same_mode = "same_mode";
    public static final String base_group = "base_group";
    public static final String same_group = "same_group";
    public static final String show_more_than_6 = "show_more_than_6";
    public static final String show_dialog_hint_1 = "show_dialog_hint_1";
    public static final String show_dialog_hint_2 = "show_dialog_hint_2";
    public static final String click_use_continue = "click_use_continue";
    public static final String click_to_theme_center = "click_to_theme_center";
    public static final String one_click_execution = "one_click_execution";
    public static final String add_base = "add_base";
    public static final String add_same = "add_same";
    public static final String all_on = "all_on";
    public static final String all_off = "all_off";
    public static final String customize = "customize";
    public static final String ai_apply_light_effect = "ai_apply_light_effect";
    public static final String ai_confirm_light_effect = "ai_confirm_light_effect";
    public static final String OTA_update_md5_mismatch = "OTA_update_md5_mismatch";
    public static final String OTA_exception = "OTA_exception";
    public static final String click_music_share_zone = "click_music_share_zone";
    public static final String click_music_share_zone_users = "click_music_share_zone_users";
    public static final String click_music_create_users = "click_music_create_users";
    public static final String apply_music_create_detail = "apply_music_create_detail";
    public static final String apply_music_create_edit = "apply_music_create_edit";
    public static final String apply_music_create_share_video = "apply_music_create_share_video";
    public static final String apply_music_create_homepage_list = "apply_music_create_homepage_list";
    public static final String apply_music_create_homepage_video = "apply_music_create_homepage_video";
    public static final String click_check_HDR = "click_check_HDR";
    public static final String click_music_create_hint = "click_music_create_hint";
    public static final String click_music_create_sort = "click_music_create_sort";
    public static final String click_save_color_ring = "click_save_color_ring";
    public static final String click_save_color_bank = "click_save_color_bank";


    public static final String graffiti_click_ai_robot = "graffiti_click_ai_robot";
    public static final String graffiti_apply_ai_material = "graffiti_apply_ai_material";
    public static final String graffiti_save_ai_material = "graffiti_save_ai_material";
    public static final String graffiti_ai_material_set_ = "graffiti_ai_material_set_";
    public static final String save_diy_apply_ai_material = "save_diy_apply_ai_material";

    public static final String click_set_format_24 = "click_set_format_24";
    public static final String click_set_format_12 = "click_set_format_12";
    public static final String click_set_tem_C = "click_set_tem_C";
    public static final String click_set_tem_F = "click_set_tem_F";
    public static final String click_color_random_more = "click_color_random_more";
    public static final String apply_color_random_more = "apply_color_random_more";
    public static final String save_color_random_more = "save_color_random_more";
    public static final String click_work_shop_user_share = "click_work_shop_user_share";
    public static final String bug_timev2_dialog = "bug_timev2_dialog_";

    public static final String pairing_failure_times = "pairing_failure_times";


    public static final String click_night_model = "click_night_model";
    public static final String apply_simple_diy = "apply_simple_diy";
    public static final String click_auto_device_pickup = "click_auto_device_pickup";
    public static final String apply_main_light_music_effect = "apply_main_light_music_effect";
    public static final String apply_side_light_music_effect = "apply_side_light_music_effect";
    public static final String apply_advance_diy_main_light = "apply_advance_diy_main_light";
    public static final String operation_mode_turn_off_down_light = "operation_mode_turn_off_down_light";
    public static final String operation_mode_turn_on_down_light = "operation_mode_turn_on_down_light";
    public static final String operation_mode_click_temp_down_light = "operation_mode_click_temp_down_light";
    public static final String operation_mode_turn_on_main_light = "operation_mode_turn_on_main_light";
    public static final String operation_mode_adjust_flow_speed_main_light = "operation_mode_adjust_flow_speed_main_light";
    public static final String operation_mode_click_temp_main_light = "operation_mode_click_temp_main_light";
    public static final String click_edit_preset_scenes = "click_edit_preset_scenes";
    public static final String click_edit_preset_temp = "click_edit_preset_temp";
    public static final String operation_mode_turn_off_main_light = "operation_mode_turn_off_main_light";
    public static final String apply_advance_diy_side_light = "apply_advance_diy_side_light";
    public static final String share_mode = "share_mode";
    public static final String homepager_effect_share = "homepager_effect_share";
    public static final String workshop_share_list = "workshop_share_list";
    public static final String ai_effect = "ai_effect";
    public static final String post_diy_share = "post_diy_share";
    public static final String video_play_page = "video_play_page";
    public static final String my_space_effect_share = "my_space_effect_share";
    public static final String my_space_selected = "my_space_selected";
    public static final String my_space_post_diy_share = "my_space_post_diy_share";
    public static final String select_diy_save = "select_diy_save";
    public static final String select_default_save = "select_default_save";
    public static final String click_diy_save_set = "click_diy_save_set";
    public static final String from_diy = "from_diy";
    public static final String from_workshop = "from_workshop";
    public static final String into_diy_share_page = "into_diy_share_page";
    public static final String turn_on_support_edit = "turn_on_support_edit";
    public static final String turn_off_support_edit = "turn_off_support_edit";
    public static final String turn_on_sync_diy_info = "turn_on_sync_diy_info";
    public static final String turn_off_sync_diy_info = "turn_off_sync_diy_info";
    public static final String click_back_edited = "click_back_edited";
    public static final String click_back_no_edit = "click_back_no_edit";
    public static final String click_connect_type_set = "click_connect_type_set";
    public static final String set_connect_type_wifi = "set_connect_type_wifi";
    public static final String set_connect_type_ble = "set_connect_type_ble";
    public static final String set_wifi_control_min_6 = "set_wifi_control_min_6";
    public static final String change_batter_icon = "change_batter_icon";
    public static final String click_replace_batter_tutorial = "click_replace_batter_tutorial";
    public static final String operation_mode_turn_on_side_light = "operation_mode_turn_on_side_light";
    public static final String operation_mode_turn_off_side_light = "operation_mode_turn_off_side_light";
    public static final String operation_mode_click_temp_side_light = "operation_mode_click_temp_side_light";
    public static final String apply_advance_diy_back_light = "apply_advance_diy_back_light";

    public static final String color_suc = "color_suc";
    public static final String diy_suc = "diy_suc";
    public static final String workshop_suc = "workshop_suc";
    public static final String workshop = "workshop";
    public static final String music_create = "music_create";
    public static final String music_create_suc = "music_create_suc";

    public static final String feast = "feast";
    public static final String group_control = "group_control";

    //耗时统计
    public static final String took_time_100ms = "100ms";
    public static final String took_time_200ms = "200ms";
    public static final String took_time_300ms = "300ms";
    public static final String took_time_500ms = "500ms";
    public static final String took_time_1s = "1s";
    public static final String took_time_2s = "2s";
    public static final String took_time_3s = "3s";
    public static final String took_time_4s = "4s";
    public static final String took_time_over_3s = "大于3s";
    public static final String took_time_5s = "5s";
    public static final String took_time_8s = "8s";
    public static final String took_time_over_8s = "大于8s";

    public static final String id_error_ = "id_error_";
    // v6.6.1
    public static final String click_auto_gear_low = "click_auto_gear_low";
    public static final String click_auto_gear_medium = "click_auto_gear_medium";
    public static final String click_auto_gear_high = "click_auto_gear_high";
    public static final String set_reservation_time = "set_reservation_time";
    public static final String turn_on_shutdown_drying = "turn_on_shutdown_drying";
    public static final String turn_off_shutdown_drying = "turn_off_shutdown_drying";
    public static final String turn_on_shutdown_water_pump = "turn_on_shutdown_water_pump";
    public static final String turn_off_shutdown_water_pump = "turn_off_shutdown_water_pump";
    public static final String click_close_drainage = "click_close_drainage";
    public static final String click_close_drying = "click_close_drying";
    public static final String click_water_pump_exception = "click_water_pump_exception";
    public static final String turn_on_water_full_remind = "turn_on_water_full_remind";
    public static final String turn_off_water_full_remind = "turn_off_water_full_remind";
    public static final String turn_on_auto_restart = "turn_on_auto_restart";
    public static final String turn_off_auto_restart = "turn_off_auto_restart";
    public static final String turn_on_show_th_in_card = "turn_on_show_th_in_card";
    public static final String turn_off_show_th_in_card = "turn_off_show_th_in_card";

    public static final String click_auto_default = "click_auto_default";
    public static final String click_auto_gear_quietness = "click_auto_gear_quietness";
    public static final String click_auto_gear_maintain = "click_auto_gear_maintain";

    //6.6.2
    public static final String click_buzzer_gear_ = "click_buzzer_gear_";
    public static final String turn_on_brightness_auto_down = "turn_on_brightness_auto_down";
    public static final String set_brightness_ = "set_brightness_";
    public static final String set_drop_bright_duration_ = "set_drop_bright_duration_";
    public static final String set_brightness_duration_ = "set_brightness_duration_";
    public static final String pair_probe_fail = "pair_probe_fail";
    /****  V6.7.0 ********/
    public static final String turn_on_bg_music = "turn_on_bg_music";
    public static final String turn_off_bg_music = "turn_off_bg_music";
    public static final String click_matter = "click_matter";
    public static final String click_question = "click_question";
    public static final String click_general_poster = "click_general_poster";
    public static final String get_info_fail = "get_info_fail";
    public static final String click_to_authorize = "click_to_authorize";
    public static final String authorize_fail = "authorize_fail";
    public static final String show_mode_hint = "show_mode_hint";
    public static final String click_snapshot_list = "click_snapshot_list";
    public static final String click_snapshot_list_question = "click_snapshot_list_question";
    public static final String click_add_snapshot = "click_add_snapshot";
    public static final String click_floatbar_retract = "click_floatbar_retract";
    public static final String click_floatbar_display = "click_floatbar_display";
    public static final String click_msg_notification = "click_msg_notification";
    public static final String turn_on_msg_notify = "turn_on_msg_notify";
    public static final String turn_off_msg_notify = "turn_off_msg_notify";
    public static final String power_memory_keep_old = "power_memory_keep_old";
    public static final String power_memory_keep_light = "power_memory_keep_light";
    public static final String power_memory_keep_off = "power_memory_keep_off";
    public static final String click_comb_stereo = "click_comb_stereo";
    public static final String select_effect_sync = "select_effect_sync";
    public static final String select_effect_out_sync = "select_effect_out_sync";
    public static final String select_effect_symmetry = "select_effect_symmetry";
    public static final String select_effect_asymmetry = "select_effect_asymmetry";
    public static final String click_stereo_guide = "click_stereo_guide";
    public static final String select_mono = "select_mono";
    public static final String select_dual_channel = "select_dual_channel";
    public static final String turn_on_feast_laser = "turn_on_feast_laser";
    public static final String turn_off_feast_laser = "turn_off_feast_laser";
    public static final String click_cur_tem = "click_cur_tem";
    public static final String set_enviro_tem = "set_enviro_tem";
    public static final String click_chart = "click_chart";
    public static final String click_add_timer_task = "click_add_timer_task";
    public static final String click_quick_timer_task = "click_quick_timer_task";
    public static final String set_diy_target_tem = "set_diy_target_tem";
    public static final String set_diy_target_under_tem = "set_diy_target_under_tem";
    public static final String apply_latest_preset_tem = "apply_latest_preset_tem";
    public static final String click_start_cook_in_CP = "click_start_cook_in_CP";
    public static final String click_add_cook_in_CP = "click_add_cook_in_CP";
    public static final String click_edit_in_CP = "click_edit_in_CP";
    public static final String click_end_cook_in_CP = "click_end_cook_in_CP";
    public static final String apply_latest_cook_record_in_CP = "apply_latest_cook_record_in_CP";
    public static final String click_chart_in_CP = "click_chart_in_CP";
    public static final String click_cookbook_in_CP_ = "click_cookbook_in_CP_";
    public static final String add_tem_notify_in_CP = "add_tem_notify_in_CP";
    public static final String add_timer_task_in_CP = "add_timer_task_in_CP";
    public static final String click_quick_timer_task_in_CP = "click_quick_timer_task_in_CP";
    public static final String click_journey_record_in_CP = "click_journey_record_in_CP";
    public static final String add_note_in_CP = "add_note_in_CP";
    public static final String share_to_community_in_CP = "share_to_community_in_CP";
    public static final String set_buzzer_gear_in_CP_ = "set_buzzer_gear_in_CP_";
    public static final String click_compare_probe = "click_compare_probe";

    public static final String not_disturb_cancel = "not_disturb_cancel";

    public static final String click_dialog_uninstall_back_cover = "click_dialog_uninstall_back_cover";
    public static final String uninstall_back_cover_times = "uninstall_back_cover_times";

    public static final String click_add = "click_add";
    public static final String click_task_detail = "click_task_detail";
    public static final String click_execute = "click_execute";
    public static final String click_fold = "click_fold";
    public static final String click_expand = "click_expand";
    public static final String click_create_by_week = "click_create_by_week";
    public static final String click_create_by_date = "click_create_by_date";
    public static final String click_create_by_sun = "click_create_by_sun";
    public static final String click_create_by_device_status = "click_create_by_device_status";
    public static final String click_create_by_NFC = "click_create_by_NFC";

    public static final String click_go_to_feast_video = "click_go_to_feast_video";
    public static final String click_go_to_feast_music = "click_go_to_feast_music";
    public static final String click_go_to_feast_scene = "click_go_to_feast_scene";
    public static final String click_go_to_base_group = "click_go_to_base_group";
    public static final String click_location_calibration = "click_location_calibration";
    public static final String expose_discover = "expose_discover";
    public static final String expose_circle = "expose_circle";
    public static final String expose_follow = "expose_follow";
    public static final String click_hot_topic_ = "click_hot_topic_";
    public static final String expose_btn_follow_in_post_detail = "expose_btn_follow_in_post_detail";
    public static final String click_user_Recom_show = "click_user_Recom_show";
    public static final String click_user_Recom_hide = "click_user_Recom_hide";
    public static final String click_view_switch = "click_view_switch";
    public static final String click_homepage_tab_about_me = "click_homepage_tab_about_me";
    public static final String click_homepage_tab_post = "click_homepage_tab_post";
    public static final String click_homepage_tab_effect_share = "click_homepage_tab_effect_share";
    public static final String click_homepage_tab_circle = "click_homepage_tab_circle";
    public static final String click_homepage_tab_project = "click_homepage_tab_project";
    public static final String click_all_medal = "click_all_medal";
    public static final String click_all_device = "click_all_device";
    public static final String click_all_feature_select = "click_all_feature_select";
    public static final String click_post_sub_all = "click_post_sub_all";
    public static final String click_post_sub_video = "click_post_sub_video";
    public static final String click_post_sub_question = "click_post_sub_question";
    public static final String click_share_sub_diy = "click_share_sub_diy";
    public static final String click_share_sub_workshop = "click_share_sub_workshop";
    public static final String click_share_sub_music_create = "click_share_sub_music_create";
    public static final String click_share_sub_picture = "click_share_sub_picture";
    public static final String click_tab_my_collection = "click_tab_my_collection";
    public static final String click_tab_browse_history = "click_tab_browse_history";
    public static final String click_tab_I_participate = "click_tab_I_participate";
    public static final String click_sub_my_likes = "click_sub_my_likes";
    public static final String click_sub_my_comment = "click_sub_my_comment";
    public static final String turn_on_precool = "turn_on_precool";
    public static final String turn_off_precool = "turn_off_precool";


    public static final String click_Co_Create_more = "click_Co_Create_more";
    public static final String click_Co_Create_img_default = "click_Co_Create_img_default";
    public static final String click_Co_Create_img_ = "click_Co_Create_img_";
    public static final String click_activity_more = "click_activity_more";
    public static final String click_activity_img_default = "click_activity_img_default";
    public static final String click_topic_next_pack = "click_topic_next_pack";
    public static final String click_recommend_topic_ = "click_recommend_topic_";
    public static final String click_post_list_set = "click_post_list_set";
    public static final String set_post_type_all = "set_post_type_all";
    public static final String set_post_type_question = "set_post_type_question";
    public static final String set_post_type_post = "set_post_type_post";
    public static final String set_post_type_video = "set_post_type_video";
    public static final String set_post_order_default = "set_post_order_default";
    public static final String set_post_order_latest_release = "set_post_order_latest_release";
    public static final String set_post_order_latest_reply = "set_post_order_latest_reply";
    public static final String set_post_order_hot_week = "set_post_order_hot_week";
    public static final String set_post_view_type_default = "set_post_view_type_default";
    public static final String set_post_view_type_simple = "set_post_view_type_simple";
    public static final String click_community_rule = "click_community_rule";
    public static final String expose_community_rule = "expose_community_rule";
    public static final String close_community_rule = "close_community_rule";
    public static final String click_img_video_thumbnail = "click_img_video_thumbnail";
    public static final String click_next_guide_1 = "click_next_guide_1";
    public static final String click_next_guide_2 = "click_next_guide_2";
    public static final String click_next_guide_3 = "click_next_guide_3";
    public static final String click_skip_guide_1 = "click_skip_guide_1";
    public static final String click_skip_guide_2 = "click_skip_guide_2";
    public static final String click_skip_guide_3 = "click_skip_guide_3";
    public static final String click_I_know = "click_I_know";
    public static final String click_search_topic = "click_search_topic";
    public static final String expose_search_topic_hot_device = "expose_search_topic_hot_device";
    public static final String click_search_topic_hot_device = "click_search_topic_hot_device";
    public static final String expose_search_topic_Func_discuss = "expose_search_topic_Func_discuss";
    public static final String click_search_topic_Func_discuss = "click_search_topic_Func_discuss";
    public static final String expose_search_technology_tutorials = "expose_search_technology_tutorials";
    public static final String click_search_technology_tutorials = "click_search_technology_tutorials";
    public static final String click_search_T_tutorials_more = "click_search_T_tutorials_more";
    public static final String expose_search_T_tutorials_list = "expose_search_T_tutorials_list";
    public static final String expose_post_detail_btn_follow = "expose_post_detail_btn_follow";
    public static final String click_post_detail_btn_follow = "click_post_detail_btn_follow";
    public static final String click_post_detail_circle_card = "click_post_detail_circle_card";
    public static final String click_post_detail_circle_follow = "click_post_detail_circle_follow";
    public static final String expose_post_detail_circle_follow = "expose_post_detail_circle_follow";
    public static final String expose_post_detail_previous = "expose_post_detail_previous";
    public static final String expose_post_detail_next = "expose_post_detail_next";
    public static final String click_post_detail_previous = "click_post_detail_previous";
    public static final String click_post_detail_next = "click_post_detail_next";
    public static final String click_post_detail_Fuc_feast = "click_post_detail_Fuc_feast";
    public static final String click_post_detail_Fuc_oneclick = "click_post_detail_Fuc_oneclick";
    public static final String click_post_detail_Fuc_group = "click_post_detail_Fuc_group";
    public static final String click_post_detail_Fuc_auto = "click_post_detail_Fuc_auto";
    public static final String click_user_Recom = "click_user_Recom";
    public static final String click_user_Recom_Home_follow = "click_user_Recom_Home_follow";
    public static final String click_follow_view_switch = "click_follow_view_switch";
    public static final String click_tab_about_me = "click_tab_about_me";
    public static final String click_tab_post = "click_tab_post";
    public static final String click_tab_effect_share = "click_tab_effect_share";
    public static final String click_tab_circle = "click_tab_circle";
    public static final String click_tab_project = "click_tab_project";
    public static final String click_look_all_recommend_topic = "click_look_all_recommend_topic";
    public static final String expose_recommend_topic = "expose_recommend_topic";
    public static final String click_recommend_topic = "click_recommend_topic";
    public static final String click_to_answer_in_invite_me_card = "click_to_answer_in_invite_me_card";
    public static final String expose_invite_me_answer_ = "expose_invite_me_answer_";
    public static final String click_invite_answer_look_all = "click_invite_answer_look_all";
    public static final String expose_question_post_ = "expose_question_post_";
    public static final String click_tab_circle_ = "click_tab_circle_";
    public static final String click_follow_Rec_circle_in_dialog = "click_follow_Rec_circle_in_dialog";
    public static final String click_follow_in_circle_page = "click_follow_in_circle_page";
    public static final String click_follow_in_post_detail = "click_follow_in_post_detail";
    public static final String click_follow_in_circle_detail = "click_follow_in_circle_detail";
    public static final String expose_post_list = "expose_post_list";
    public static final String click_post_list = "click_post_list";
    public static final String expose_topic_detail_ = "expose_topic_detail_";
    public static final String click_topic_detail_post = "click_topic_detail_post";
    public static final String expose_topic_list_ = "expose_topic_list_";
    public static final String click_topic_ = "click_topic_";
    public static final String expose_post_page_post = "expose_post_page_post";
    public static final String expose_post_page_video = "expose_post_page_video";
    public static final String expose_post_page_question = "expose_post_page_question";
    public static final String click_send_question_post_img = "click_send_question_post_img";
    public static final String click_send_question_post_video = "click_send_question_post_video";
    public static final String click_send_question_post_color = "click_send_question_post_color";
    public static final String click_send_question_post_DIY = "click_send_question_post_DIY";
    public static final String click_send_question_post_workshop = "click_send_question_post_workshop";
    public static final String click_send_question_post_Goods_Url = "click_send_question_post_Goods_Url";
    public static final String click_send_question_post_add_Fuc = "click_send_question_post_add_Fuc";
    public static final String click_save_draft = "click_save_draft";
    public static final String expose_draft_not_enough_space = "expose_draft_not_enough_space";
    public static final String click_draft_dialog_cancel = "click_draft_dialog_cancel";
    public static final String click_draft_dialog_2_clean = "click_draft_dialog_2_clean";
    public static final String click_draft_box = "click_draft_box";
    public static final String expose_draft_box = "expose_draft_box";
    public static final String click_edit_in_draft_box = "click_edit_in_draft_box";
    public static final String click_manager_in_draft_box = "click_manager_in_draft_box";
    public static final String click_delete_in_draft_box = "click_delete_in_draft_box";
    public static final String click_cancel_delete_in_draft_box = "click_cancel_delete_in_draft_box";
    public static final String click_confirm_delete_in_draft_box = "click_confirm_delete_in_draft_box";
    public static final String click_2_answer_in_question_detail = "click_2_answer_in_question_detail";
    public static final String mark_useful_in_question_detail = "mark_useful_in_question_detail";
    public static final String mark_less_in_question_detail = "mark_less_in_question_detail";
    public static final String click_send_write_answer = "click_send_write_answer";
    public static final String expose_homepage = "expose_homepage";
    public static final String expose_co_create_activity = "expose_co_create_activity";
    public static final String open_Co_Create_float_bar = "open_Co_Create_float_bar";
    public static final String expose_activity = "expose_activity";
    public static final String click_activity_ = "click_activity_";
    public static final String expose_govee_post = "expose_govee_post";
    public static final String click_govee_post_ = "click_govee_post_";
    public static final String click_questionnaire_detail = "click_questionnaire_detail";
    public static final String click_earlybird_detail = "click_earlybird_detail";
    public static final String click_trial_detail = "click_trial_detail";
    public static final String click_trial_registration = "click_trial_registration";
    public static final String click_co_create_discuss_apply_ = "click_co_create_discuss_apply_";
    public static final String click_co_create_discuss_outline_ = "click_co_create_discuss_outline_";
    public static final String click_co_create_discuss_join_ = "click_co_create_discuss_join_";
    public static final String click_co_create_normal_apply_ = "click_co_create_normal_apply_";
    public static final String click_co_create_normal_detail_ = "click_co_create_normal_detail_";
    public static final String click_co_create_normal_join_ = "click_co_create_normal_join_";
    public static final String click_co_create_normal_review_ = "click_co_create_normal_review_";
    public static final String click_like_in_post_list = "click_like_in_post_list";
    public static final String click_like_in_post_detail = "click_like_in_post_detail";
    public static final String click_collect_in_post_detail = "click_collect_in_post_detail";
    //6.6.3
    public static final String click_main_switch = "click_main_switch";
    public static final String click_sub_switch = "click_sub_switch";
    public static final String click_night_light_switch = "click_night_light_switch";
    public static final String click_night_light_color = "click_night_light_color";
    public static final String click_night_light_scene = "click_night_light_scene";
    public static final String click_night_light_diy = "click_night_light_diy";
    public static final String click_sub_plug_timer = "click_sub_plug_timer";
    public static final String click_night_light_timer = "click_night_light_timer";
    public static final String turn_on_child_lock = "turn_on_child_lock";
    public static final String turn_on_indicator_light = "turn_on_indicator_light";
    public static final String select_indicator_duration = "select_indicator_duration";
    public static final String click_shape_reference_skip_in_bind = "click_shape_reference_skip_in_bind";
    public static final String click_finish_in_shape_confirm = "click_finish_in_shape_confirm";
    public static final String click_shape_caliber_in_set = "click_shape_caliber_in_set";
    public static final String click_finish_shape_Recg = "click_finish_shape_Recg";

    public static final String click_display_match_remind = "click_display_match_remind";
    public static final String click_display_enviro_info_remind = "click_display_enviro_info_remind";
    public static final String set_display_clock = "set_display_clock";
    public static final String set_display_clock_later_remind_on = "set_display_clock_later_remind_on";
    public static final String set_display_clock_later_remind_off = "set_display_clock_later_remind_off";
    public static final String add_display_time_auto_watch_dial = "add_display_time_auto_watch_dial";
    public static final String save_display_watch_dial_effect = "save_display_watch_dial_effect";
    public static final String save_music_auto_effect = "save_music_auto_effect";
    public static final String click_C_in_discover_post_list = "click_C_in_discover_post_list";
    public static final String click_C_in_circle_page_post_list = "click_C_in_circle_page_post_list";
    public static final String click_C_in_circle_detail_post_list = "click_C_in_circle_detail_post_list";
    public static final String click_C_in_topic_detail_post_list = "click_C_in_topic_detail_post_list";
    public static final String click_C_in_follow_post_list = "click_C_in_follow_post_list";
    public static final String click_C_in_post_detail_input = "click_C_in_post_detail_input";
    public static final String click_C_icon_in_post_detail = "click_C_icon_in_post_detail";

    public static final String apply_filter_origin = "apply_filter_origin";
    public static final String apply_filter_1 = "apply_filter_1";
    public static final String apply_filter_2 = "apply_filter_2";
    public static final String apply_filter_3 = "apply_filter_3";
    public static final String apply_filter_4 = "apply_filter_4";
    public static final String apply_artistic_deconste_origin = "apply_artistic_deconste_origin";
    public static final String apply_artistic_deconste_4 = "apply_artistic_deconste_4";
    public static final String apply_artistic_deconste_6 = "apply_artistic_deconste_6";
    public static final String apply_artistic_deconste_8 = "apply_artistic_deconste_8";


    public static final String save_gif_effect = "save_gif_effect";
    public static final String click_graffiti_guide = "click_graffiti_guide";
    public static final String set_layer_effect_all = "set_layer_effect_all";
    public static final String set_animation_effect_all = "set_animation_effect_all";

    public static final String graffiti_change_2_screen_portrait = "graffiti_change_2_screen_portrait";
    public static final String graffiti_change_2_screen_landscape = "graffiti_change_2_screen_landscape";
    public static final String graffiti_click_guide_line = "graffiti_click_guide_line";
    public static final String apply_red_laser = "apply_red_laser";
    public static final String apply_green_laser = "apply_green_laser";
    public static final String apply_blue_laser = "apply_blue_laser";
    public static final String apply_pattern_laser_red_ = "apply_pattern_laser_red_";
    public static final String apply_pattern_laser_green_ = "apply_pattern_laser_green_";
    public static final String apply_pattern_laser_blue_ = "apply_pattern_laser_blue_";
    public static final String apply_pattern_laser_ = "apply_pattern_laser_";
    public static final String expose_translate_in_post = "expose_translate_in_post";
    public static final String click_translate_in_post = "click_translate_in_post";
    public static final String click_ecology_fuction_in_post = "click_ecology_fuction_in_post";
    public static final String click_create_from_post = "click_create_from_post";
    public static final String create_feast_suc_from_post = "create_feast_suc_from_post";
    public static final String create_oneclick_suc_from_post = "create_oneclick_suc_from_post";
    public static final String create_group_suc_from_post = "create_group_suc_from_post";
    public static final String create_auto_suc_from_post = "create_auto_suc_from_post";
    public static final String click_create_feast_suc_from_post = "click_create_feast_suc_from_post";
    public static final String click_create_oneclick_suc_from_post = "click_create_oneclick_suc_from_post";
    public static final String click_create_group_suc_from_post = "click_create_group_suc_from_post";
    public static final String click_create_auto_suc_from_post = "click_create_auto_suc_from_post";
    public static final String sync_to_other_probe = "sync_to_other_probe";
    public static final String select_big_card = "select_big_card";
    public static final String select_small_card = "select_small_card";

    public static final String work_mode_descale = "work_mode_descale";

    public static final String click_hint_close = "click_hint_close";
    public static final String click_dialog_room_manage = "click_dialog_room_manage";
    public static final String click_long_2_edit = "click_long_2_edit";
    public static final String click_empty_2_add = "click_empty_2_add";
    public static final String add_device_in_tab_1 = "add_device_in_tab_1";
    public static final String add_device_in_tab_2 = "add_device_in_tab_2";
    public static final String click_search_in_add = "click_search_in_add";
    public static final String click_add_gateway = "click_add_gateway";
    public static final String click_bind_gateway = "click_bind_gateway";
    public static final String click_no_bind_gateway = "click_no_bind_gateway";
    public static final String show_over_limit_dialog_915 = "show_over_limit_dialog_915";
    public static final String th_show_over_limit_dialog = "th_show_over_limit_dialog";
    public static final String edit_device_name = "edit_device_name";
    public static final String click_set_icon_fresh_ = "click_set_icon_fresh_";
    public static final String click_set_icon_freeze_ = "click_set_icon_freeze_";
    public static final String click_set_icon_fish_tank_ = "click_set_icon_fish_tank_";
    public static final String click_set_icon_bathtub_ = "click_set_icon_bathtub_";
    public static final String click_set_icon_pool_ = "click_set_icon_pool_";
    public static final String click_set_icon_cabinet_ = "click_set_icon_cabinet_";
    public static final String click_set_icon_plant_T_ = "click_set_icon_plant_T_";
    public static final String click_set_icon_motor_H_ = "click_set_icon_motor_H_";
    public static final String click_tem_brightest_ever = "click_tem_brightest_ever";
    public static final String click_tem_follow_sub_switch = "click_tem_follow_sub_switch";
    public static final String click_keep_status_B_outage = "click_keep_status_B_outage";
    public static final String click_keep_light = "click_keep_light";
    public static final String click_keep_off = "click_keep_off";
    public static final String click_turn_on_W_ripple_L = "click_turn_on_W_ripple_L";
    public static final String click_turn_off_W_ripple_L = "click_turn_off_W_ripple_L";
    public static final String click_tab_W_ripple_L = "click_tab_W_ripple_L";
    public static final String click_turn_on_side_L = "click_turn_on_side_L";
    public static final String click_turn_off_side_L = "click_turn_off_side_L";
    public static final String click_tab_side_L = "click_tab_side_L";
    public static final String click_turn_on_below_L = "click_turn_on_below_L";
    public static final String click_turn_off_below_L = "click_turn_off_below_L";
    public static final String click_tab_below_L = "click_tab_below_L";
    public static final String click_sing_color_In_video_M = "click_sing_color_In_video_M";
    public static final String click_smooth_In_video_M = "click_smooth_In_video_M";
    public static final String click_vivid_In_video_M = "click_vivid_In_video_M";
    public static final String click_specific_In_video_M = "click_specific_In_video_M";
    public static final String click_on2off_black_clear = "click_on2off_black_clear";
    public static final String click_off2on_black_clear = "click_off2on_black_clear";
    public static final String click_turn_on_AI_filter = "click_turn_on_AI_filter";
    public static final String click_turn_off_AI_filter = "click_turn_off_AI_filter";
    public static final String click_replace_filter = "click_replace_filter";
    public static final String click_on_delay_off_filter = "click_on_delay_off_filter";
    public static final String click_off_delay_off_filter = "click_off_delay_off_filter";
    public static final String click_set_delay_off = "click_set_delay_off";
    public static final String click_delay_off_time_ = "click_delay_off_time_";
    public static final String click_search_AI_filter = "click_search_AI_filter";
    public static final String click_apply_AI_filter = "click_apply_AI_filter";
    public static final String click_apply_R_use_filter = "click_apply_R_use_filter";
    public static final String click_apply_preset_filter = "click_apply_preset_filter";
    public static final String click_apply_jointly_filter = "click_apply_jointly_filter";
    public static final String apply_filter_all_Rec = "apply_filter_all_Rec";
    public static final String apply_filter_all_like_most = "apply_filter_all_like_most";
    public static final String apply_filter_all_save_most = "apply_filter_all_save_most";
    public static final String apply_filter_all_recent = "apply_filter_all_recent";
    public static final String apply_filter_M_like_most = "apply_filter_M_like_most";
    public static final String apply_filter_M_save_most = "apply_filter_M_save_most";
    public static final String apply_filter_M_recent = "apply_filter_M_recent";
    public static final String apply_filter_cur_M_like_most = "apply_filter_cur_M_like_most";
    public static final String apply_filter_cur_M_save_most = "apply_filter_cur_M_save_most";
    public static final String apply_filter_cur_M_recent = "apply_filter_cur_M_recent";
    public static final String apply_F_all_Rec_banner = "apply_F_all_Rec_banner";
    public static final String apply_F_all_like_most_banner = "apply_F_all_like_most_banner";
    public static final String apply_F_all_save_most_banner = "apply_F_all_save_most_banner";
    public static final String apply_F_all_recent_banner = "apply_F_all_recent_banner";
    public static final String apply_F_M_like_most_banner = "apply_F_M_like_most_banner";
    public static final String apply_F_M_save_most_banner = "apply_F_M_save_most_banner";
    public static final String apply_F_M_recent_banner = "apply_F_M_recent_banner";
    public static final String apply_fail_O_watch_dial = "apply_fail_O_watch_dial";
    public static final String apply_fail_A_watch_dial = "apply_fail_A_watch_dial";
    public static final String apply_fail_weather_dial = "apply_fail_weather_dial";
    public static final String apply_fail_F_bitcoin_dial = "apply_fail_F_bitcoin_dial";
    public static final String apply_fail_F_stock_dial = "apply_fail_F_stock_dial";
    public static final String apply_fail_more_countdown = "apply_fail_more_countdown";
    public static final String apply_fail_more_stopwatch = "apply_fail_more_stopwatch";
    public static final String apply_fail_more_alarm = "apply_fail_more_alarm";
    public static final String apply_fail_more_E_info = "apply_fail_more_E_info";
    public static final String apply_fail_graffiti = "apply_fail_graffiti";
    public static final String apply_fail_music = "apply_fail_music";
    public static final String apply_fail_scene = "apply_fail_scene";
    public static final String apply_fail_play_list = "apply_fail_play_list";
    public static final String apply_fail_diy = "apply_fail_diy";
    public static final String save_fail_diy = "save_fail_diy";
    public static final String disconnect_in_detail = "disconnect_in_detail";
    public static final String turn_on_water_light = "turn_on_water_light";
    public static final String turn_off_water_light = "turn_off_water_light";
    public static final String turn_on_star_light = "turn_on_star_light";
    public static final String turn_off_star_light = "turn_off_star_light";
    public static final String turn_on_star_cloud_light = "turn_on_star_cloud_light";
    public static final String turn_off_star_cloud_light = "turn_off_star_cloud_light";
    public static final String apply_all_part_star_C = "apply_all_part_star_C";
    public static final String apply_part_star_C = "apply_part_star_C";
    public static final String work_mode_DESCALE = "work_mode_DESCALE";

    public static final String save_B_card_effect = "save_B_card_effect";
    public static final String delete_B_card_effect = "delete_B_card_effect";
    public static final String apply_effect_in_card = "apply_effect_in_card";
    public static final String click_switch_in_new_C = "click_switch_in_new_C";
    public static final String click_B_in_new_card = "click_B_in_new_card";
    public static final String apply_fail_in_new_C = "apply_fail_in_new_C";
    public static final String click_sync_apply_card_size = "click_sync_apply_card_size";
    public static final String click_no_sync_card_size = "click_no_sync_card_size";
    public static final String select_classic_card_no_login = "select_classic_card_no_login";
    public static final String select_new_card_no_login = "select_new_card_no_login";
    public static final String select_small_card_kitchen = "select_small_card_kitchen";
    public static final String select_big_card_kitchen = "select_big_card_kitchen";
    public static final String select_classic_card_login = "select_classic_card_login";
    public static final String select_new_card_login = "select_new_card_login";
    public static final String click_post_page_select = "click_post_page_select";
    public static final String click_post_page_finish_select = "click_post_page_finish_select";
    public static final String click_post_page_camera = "click_post_page_camera";
    public static final String click_post_page_finish_camera = "click_post_page_finish_camera";
    public static final String click_HDR_gear_ = "click_HDR_gear_";

    public static final String clear_app_cache_suc = "clear_app_cache_suc";
    public static final String click_on2off = "click_on2off";
    public static final String click_off2on = "click_off2on";
    public static final String show_search_list = "show_search_list";
    public static final String click_back_in_result = "click_back_in_result";
    public static final String click_generate_poster = "click_generate_poster";
    public static final String click_save_poster = "click_save_poster";
    public static final String result_suc = "result_suc";
    public static final String result_overtime = "result_overtime";


    public static String click_recommend_(int position) {
        return "click_recommend_(" + position + ")";
    }

    public static String click_search_(String lastKey, String nowKey) {
        return "click_search_" + lastKey + "_" + nowKey;
    }

    //mtu支持检测统计
    public static final String mtu_unsupport_42 = "mtu_unsupport_42";
    public static final String mtu_support_size = "mtu_support_";
    public static final String click_Alexa_guide= "click_Alexa_guide";
    public static final String click_Google_guide= "click_Google_guide";
    public static final String click_Siri_guide= "click_Siri_guide";
    public static final String click_hot_topic= "click_hot_topic";
    public static final String click_new_topic= "click_new_topic";
    public static final String click_no_new_hot_topic= "click_no_new_hot_topic";
    public static final String click_no_add_topic= "click_no_add_topic";
    public static final String show_2_create_topic= "show_2_create_topic";
    public static final String click_create_topic= "click_create_topic";
    public static final String click_edit_topic= "click_edit_topic";
    public static final String click_cancel_edit_topic= "click_cancel_edit_topic";
    public static final String click_confirm_edit_topic= "click_confirm_edit_topic";
    public static final String comment_msg= "comment_msg";
    public static final String new_follow_msg= "new_follow_msg";
    public static final String like_msg= "like_msg";
    public static final String save_msg= "save_msg";
    public static final String click_clean_temp= "click_clean_temp";
    public static final String clean_temp_suc= "clean_temp_suc";
    public static final String click_on2off_auto_clean= "click_on2off_auto_clean";
    public static final String click_off2on_auto_clean= "click_off2on_auto_clean";
    public static final String click_manage_TH= "click_manage_TH";
    public static final String clean_TH_suc= "clean_TH_suc";
    public static final String click_no_select_all= "click_no_select_all";
    public static final String click_TH_duration_7_D= "click_TH_duration_7_D";
    public static final String click_TH_duration_1_M= "click_TH_duration_1_M";
    public static final String click_TH_duration_3_M= "click_TH_duration_3_M";
    public static final String click_TH_duration_6_M= "click_TH_duration_6_M";
    public static final String click_default_color_block_= "click_default_color_block_";
    public static final String expose_my_color_strip= "expose_my_color_strip";
    public static final String click_my_color_strip= "click_my_color_strip";
    public static final String click_my_color_entry= "click_my_color_entry";

    public static final String expose_HSV_plate= "expose_HSV_plate";
    public static final String click_HSV_plate= "click_HSV_plate";
    public static final String no_color_plate_config= "no_color_plate_config";
    public static final String click_add_color_group= "click_add_color_group";
    public static final String click_manage_color_group= "click_manage_color_group";
    public static final String rename_color_group= "rename_color_group";
    public static final String rename_color_strip= "rename_color_strip";
    public static final String add_colorful_strip= "add_colorful_strip";
    public static final String expose_scene_banner= "expose_scene_banner";
    public static final String click_scene_banner= "click_scene_banner";
    public static final String click_scene_banner_close= "click_scene_banner_close";
    public static final String click_edit_scene= "click_edit_scene";
    public static final String apply_scene_duration_1= "apply_scene_duration_1";
    public static final String apply_scene__duration_1= "apply_scene__duration_1";
    public static final String click_edit_S_P_in_detail= "click_edit_S_P_in_detail";
    public static final String click_edit_S_P_in_oneClick= "click_edit_S_P_in_oneClick";
    public static final String click_edit_S_P_in_autoExe= "click_edit_S_P_in_autoExe";
    public static final String click_edit_S_P_in_deviceL= "click_edit_S_P_in_deviceL";
    public static final String click_save_scene_= "click_save_scene_";
    public static final String click_S_P_color_restore= "click_S_P_color_restore";
    public static final String click_S_P_color_guide= "click_S_P_color_guide";
    public static final String click_S_P_color_= "click_S_P_color_";
    public static final String in_sku_detail= "in_sku_detail";
    public static final String in_oneClick= "in_oneClick";
    public static final String in_autoExe= "in_autoExe";
    public static final String in_deviceL= "in_deviceL";
    public static final String click_recently_in_share_S= "click_recently_in_share_S";
    public static final String click_recom_in_share_S= "click_recom_in_share_S";
    public static final String share_effect_tag_num_= "share_effect_tag_num_";
    public static final String share_effect_input_tag= "share_effect_input_tag";
    public static final String share_effect_use_recom_tag= "share_effect_use_recom_tag";
    public static final String click_keep_status_before_outage= "click_keep_status_before_outage";
    public static final String select_big_card_light= "select_big_card_light";
    public static final String select_big_card_th= "select_big_card_th";
    public static final String select_big_card_meat_th= "select_big_card_meat_th";
    public static final String select_big_card_Envir_Elec= "select_big_card_Envir_Elec";
    public static final String select_big_card_plug= "select_big_card_plug";
    public static final String select_big_card_sensor= "select_big_card_sensor";
    public static final String select_small_card_light= "select_small_card_light";
    public static final String select_small_card_th= "select_small_card_th";
    public static final String select_small_card_meat_th= "select_small_card_meat_th";
    public static final String select_small_card_Envir_Elec= "select_small_card_Envir_Elec";
    public static final String select_small_card_plug= "select_small_card_plug";
    public static final String select_small_card_sensor= "select_small_card_sensor";
    public static final String select_big_card_lively= "select_big_card_lively";
    public static final String select_small_card_lively= "select_small_card_lively";
    public static final String transfer_TH_data_start= "transfer_TH_data_start";
    public static final String transfer_TH_data_suc= "transfer_TH_data_suc";
    public static final String transfer_TH_data_fail= "transfer_TH_data_fail";
    public static final String work_mode= "work_mode";

    public static final String click_off2on_device_lock = "click_off2on_device_lock";
    public static final String click_on2off_device_lock = "click_on2off_device_lock";
    public static final String use_mode_illumination = "use_mode_illumination";
    public static final String click_new_color_all = "click_new_color_all";
    public static final String click_new_color_part = "click_new_color_part";
    public static final String click_new_color_colorful = "click_new_color_colorful";
    public static final String click_color_match_tool = "click_color_match_tool";
    public static final String click_color_invert = "click_color_invert";
    public static final String click_save_to_my_color_all = "click_save_to_my_color_all";
    public static final String click_save_to_my_color_part = "click_save_to_my_color_part";
    public static final String click_save_to_my_color_colorful = "click_save_to_my_color_colorful";
    public static final String save_colorful_num_ = "save_colorful_num_";
    public static final String click_color_panel_LuMing = "click_color_panel_LuMing";
    public static final String click_color_panel_tab = "click_color_panel_tab";
    public static final String click_color_panel_my = "click_color_panel_my";
    public static final String click_color_panel_standard = "click_color_panel_standard";
    public static final String click_color_panel_HSV = "click_color_panel_HSV";
    public static final String click_color_panel_gradient = "click_color_panel_gradient";
    public static final String click_color_panel_recommend = "click_color_panel_recommend";
    public static final String click_recomment_block = "click_recomment_block";
    public static final String click_recomment_strip = "click_recomment_strip";
    public static final String click_color_ring_tab_yideng = "click_color_ring_tab_yideng";
    public static final String click_color_ring_tab_gradient = "click_color_ring_tab_gradient";
    public static final String click_color_ring_yideng = "click_color_ring_yideng";
    public static final String click_color_ring_gradient = "click_color_ring_gradient";

    /******** end **********/
    public static String wifiSecurityType(int pos) {
        switch (pos) {
            case 1:
                return WEP;
            case 2:
                return WPA_WPA2_personal;
            case 3:
                return WPA3_personal;
        }
        return None;
    }

    public static void analyticHideWifiResult(String sku, int position, boolean result) {
        String security = wifiSecurityType(position);
        String valueStr = (result ? hide_wifi_connect_suc : hide_wifi_connect_fail) + "_" + security;
        AnalyticsRecorder.getInstance().recordUseCount(sku, valueStr);
        AnalyticsRecorder.getInstance().recordUseCount(sku, result ? hide_wifi_connect_suc : hide_wifi_connect_fail);
    }
}
