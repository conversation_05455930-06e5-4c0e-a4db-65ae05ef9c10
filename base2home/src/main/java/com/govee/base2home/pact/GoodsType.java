package com.govee.base2home.pact;

import androidx.annotation.Nullable;

import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.SafeLog;

import kotlin.Unit;
import kotlin.jvm.functions.Function1;

/**
 * Create by <PERSON><PERSON><PERSON><PERSON> on 2019-11-27
 * 产品类型定义描述$
 */
public class GoodsType {
    private static final String TAG = "GoodsType";

    private GoodsType() {
    }

    /*----------------------------产品类型------------------------------*/

    /**
     * 不支持的产品类型
     */
    public static final int GOODES_TYPE_NO_SUPPORT = 0;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+wifi控制的灯带
     */
    public static final int GOODS_TYPE_VALUE_STRING_LIGHT_BLE_WIFI_V2 = 1;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙控制的灯带
     */
    public static final int GOODS_TYPE_VALUE_STRING_LIGHT_BLE_V2 = 2;

    /**
     * 产品类型
     * <p>
     * 仅支持蓝牙的球泡灯串
     */
    public static final int GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1 = 3;

    /**
     * 产品类型
     * <p>
     * wifi控制的插座-单口插座
     */
    public static final int GOODS_TYPE_VALUE_PLUG_IOT_V1 = 4;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙和wifi的球泡串
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1 = 5;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙的车载灯带
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_CAR_LIGHT_BLE_V1 = 6;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+wifi的温湿度计
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_TH_BLE_WIFI_V1 = 7;

    /**
     * 产品类型
     * <p>
     * 仅支持蓝牙的温湿度计
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_TH_BLE_V1 = 8;

    /**
     * 产品类型
     * <p>
     * 支持wifi的球泡灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BULB_LIGHT_IOT_V1 = 9;

    /**
     * 产品类型
     * <p>
     * 支持wifi的单暖白球泡灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_WHITE_BULB_LIGHT_IOT_V1 = 10;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙的底盘灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1 = 11;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙的TV 灯带
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_TV_LIGHT_BLE_V1 = 12;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙的幻彩 分段裸灯带
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1 = 13;

    /**
     * 产品类型
     * <p>
     * 支持1拖1的蓝牙温湿度计
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_TH_BLE_MULTI_V1 = 14;

    /**
     * 产品类型
     * <p>
     * 支持wifi 室外双孔插座
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_OUTDOOR_PLUG_IOT_V1 = 15;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+WIFI的 RGBWW的for home灯带
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_HOME_LIGHT_RGBWW_V1 = 16;

    /**
     * 产品类型
     * <p>
     * 仅支持蓝牙的幻彩灯带
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1 = 17;

    /**
     * 产品类型
     * <p>
     * 仅支持蓝牙的幻彩灯带-带限流
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1 = 18;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+wifi的幻彩灯带
     * H6163、H6117、H61A8
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1 = 19;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙的幻彩球泡灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_LED_BULB_BLE_V1 = 20;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+wifi的幻彩灯带V2版-新透传iot协议
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2 = 21;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+Wi-Fi的床头灯-新透传iot协议
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_TABLE_LAMP_BLE_WIFI_V1 = 22;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+双灯柱 tv灯带
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V2 = 23;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+wifi的摄像头tv灯带
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V2 = 24;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+wifi的摄像头 双灯柱tv灯带
     * 6054 6049
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V4 = 25;
    /**
     * 产品类型
     * <p>
     * 纯wifi款的墙壁开关
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_WALL_SWITCH_ONE_IOT_V1_1 = 26;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+wifi的立式镂空灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_VERTICAL_HOLLOW_LAMP_BLE_WIFI_V1 = 27;

    /**
     * 产品类型
     * <p>
     * 28-单探针有屏烤肉温度计
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BARBECUE_BLE_V1 = 28;
    /**
     * 产品类型
     * <p>
     * 29-单探针无屏烤肉温度计
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BARBECUE_BLE_NO_SCREEN_V1 = 29;

    /**
     * 产品类型
     * <p>
     * 30-立式直头落地灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1 = 30;

    /**
     * 产品类型
     * <p>
     * 31-支持蓝牙+wifi的幻彩球泡灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BULB_LIGHT_BLE_WIFI_V1 = 31;

    /**
     * 产品类型
     * <p>
     * 32-外置群控蓝牙拾音盒
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_PICKUP_BOX_V1 = 32;

    /**
     * 产品类型
     * <p>
     * 33-支持蓝牙的双探针烤肉计
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BARBECUE_BLE_MULTI_V2 = 33;

    /**
     * 产品类型
     * <p>
     * 34-支持蓝牙的四探针烤肉计
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BARBECUE_BLE_MULTI_V4 = 34;

    /**
     * 产品类型
     * <p>
     * 35-支持蓝牙的球泡灯串
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V2 = 35;

    /**
     * 产品类型
     * <p>
     * 36-支持蓝牙的露营灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_CAMPING_LIGHT_BLE_V1 = 36;

    /**
     * 产品类型
     * <p>
     * 37-支持蓝牙+wifi的床头灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BED_LIGHT_BLE_WIFI_V1 = 37;

    /**
     * 产品类型
     * <p>
     * 38-支持蓝牙+wifi的RGB球泡灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BULB_LIGHT_BLE_WIFI_V2 = 38;

    /**
     * 产品类型
     * <p>
     * 支持ble-RGB类的BK cost down类型
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BK_BLE_RGB = 39;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGB类的BK cost down类型
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BK_BLE_WIFI_RGB = 40;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGBIC类拼接条状灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT = 41;

    /**
     * 产品类型
     * <p>
     * 支持ble-RGB类移动氛围灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_MOVE_ATMOSPHERE_LIGHT = 42;

    /**
     * 产品类型
     * <p>
     * 支持ble-RGBIC类车载灯带
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_CAR_LIGHT = 44;

    /**
     * 产品类型
     * <p>
     * 5083 5080 5085
     * 支持ble/iot-带开关的插座
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_PLUG_BLE_IOT_V2 = 43;

    /**
     * 产品类型
     * <p>
     * 支持ble-RGBIC类车载底盘灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT = 45;
    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-点光源路径灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_PATH_LIGHT = 46;
    /**
     * 产品类型
     * <p>
     * 支持ble-四探针无屏烤肉温度计
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BBQ_NO_SCREEN_4 = 47;

    /**
     * 产品类型
     * <p>
     * 支持ble-二探针无屏烤肉温度计
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BBQ_NO_SCREEN_2 = 48;
    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-暖白球泡灯串
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_WARM_WHITE_BULB_LIGHT = 49;

    /**
     * 产品类型
     * <p>
     * 支持ble/iot-带开关的双孔插座
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_TWO_PLUG_BLE_IOT_V2 = 50;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGBIC类墙面灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_CUBE_LIGHT = 51;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGBIC类tv灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_TV_ATMOSPHERE_LIGHT = 52;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-户外球泡灯串
     * 7020
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BULB_STRING = 53;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-户外球泡灯串
     * 7021
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BULB_STRING_V2 = 54;

    /**
     * 产品类型
     * <p>
     * 支持ble-RGBIC类车载底盘灯 8盏灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_8 = 55;

    /**
     * 产品类型
     * <p>
     * 支持ble-RGBIC类车载底盘灯 6盏灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_6 = 56;

    /**
     * 产品类型
     * <p>
     * 支持ble + wifi -暖白鹅颈落地灯（球泡）
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_GOOSENECK_FLOOR_LAMP_BLE_WIFI_V1 = 57;

    /**
     * 产品类型
     * <p>
     * 支持ble + wifi - RGBWW 圆盘向上发光灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_UPGLOW_LAMP_BLE_WIFI_V1 = 58;

    /**
     * 产品类型
     * <p>
     * 支持ble + wifi -移动氛围灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_MOVE_ATMOSPHERE_LIGHT_V2 = 59;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-取暖器
     * 7130
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HEATER = 60;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-加湿器
     * 7141
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HUMIDIFIER = 61;
    /**
     * 产品类型
     * <p>
     * 支持ble-户外球泡灯串
     * 7015
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BULB_STRING_V3 = 62;

    /**
     * 产品类型
     * <p>
     * 支持ble-户外球泡灯串
     * 7016
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BULB_STRING_V4 = 63;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-球泡灯
     * 6008
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BULB = 64;

    /**
     * 产品类型 H5151
     * <p>
     * 支持ble + wifi -1拖多的温湿度计网关
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_TH_BLE_IOT_GATEWAY = 65;

    /**
     * 产品类型 H5100
     * <p>
     * 支持ble  -温湿度计，可以被添加到H5151
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_TH_BLE = 66;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi- WW 圆盘向上落地灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_UPGLOW_FLOOR_LAMP_WW_BLE_WIFI_V1 = 67;

    /**
     * 产品类型 H6178-H616B
     * <p>
     * 支持ble的rgb-支持蓝牙下的Alexa
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_ALEXA_RGB = 68;
    public static final int GOODS_TYPE_VALUE_BLE_ALEXA_RGB_LIMIT = 78;


    /**
     * 产品类型
     * <p>
     * 支持ble+wifi- RgbIcW 立式直头落地灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2 = 69;

    /**
     * 产品类型 H617C/E
     * <p>
     * 纯蓝牙款的rgbic-支持设备校验
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET = 70;

    /**
     * 产品类型 H618C/E H619C/E
     * <p>
     * 蓝牙+Wi-Fi款的rgbic-支持设备校验
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET = 71;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-空气净化器
     * </p>
     * h7121
     */
    public static final int GOODS_TYPE_VALUE_AIR_CLEANER = 72;

    /**
     * 产品类型 H617A 颜色模式15段-不支持渐变
     * <p>
     * 纯蓝牙款的rgbic-支持设备校验
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1 = 73;

    /**
     * 产品类型 H618A 颜色模式15段-不支持渐变
     * <p>
     * 蓝牙+Wi-Fi款的rgbic-支持设备校验
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1 = 74;

    /**
     * 产品类型 H619A 颜色模式10段
     * <p>
     * 蓝牙+Wi-Fi款的rgbic-支持设备校验
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2 = 75;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGBIC泛光灯 H7060
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_FLOOD_LIGHT = 76;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGBIC泛光灯 H6087
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_CRUEL_WALL_LIGHT = 77;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-空气净化器
     * 7122
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_AIR_CLEANER_V2 = 79;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGBIC条形灯 H610A
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2 = 80;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-加湿器
     * 7142
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HUMIDIFIER_V2 = 81;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-灯柱+rgbic灯带
     * 605B
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_TV_RGBIC_LIGHT_V1 = 82;
    /**
     * 产品类型 H619Z - 12段颜色模式 - 无渐变
     * <p>
     * 蓝牙+Wi-Fi款的rgbic-支持设备校验
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3 = 83;
    /**
     * 产品类型 H6059
     * <p>
     * 支持ble+wifi- RGB儿童灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_RGB_BLE_WIFI_CHILDREN_LAMP_V1 = 84;

    /**
     * 产品类型
     * <p>
     * 84-支持 蓝牙/iot 的四探针有屏烤肉计
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BBQ_BLE_IOT_MULTI_V4 = 85;

    /**
     * 产品类型
     * <p>
     * H7111
     * 支持 蓝牙/iot 的台式空气循环扇
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_CIRCULATION_FAN_BLE_IOT_DESKTOP_AIR_V1 = 86;

    /**
     * 产品类型
     * <p>
     * 支持 蓝牙的拾音盒 1162
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_PICKUP_BOX_V2 = 87;


    /**
     * 产品类型
     * <p>
     * 支持 蓝牙/WI-FI 的洗墙灯条
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_WASH_THE_WALL_LIGHT = 89;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-除湿机
     * 7150
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_DEHUMIDIFIER_V1 = 94;
    public static final int PACT_TYPE_VALUE_BLE_WIFI_WASH_THE_WALL_LIGHT_V1_1 = 1;//洗墙灯协议类型
    public static final int PACT_CODE_VALUE_BLE_WIFI_WASH_THE_WALL_LIGHT_V1_1 = 1;//洗墙灯协议代码
    /**
     * 产品类型
     * <p>
     * 支持ble/iot-带开关的三孔插座
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_TRIPLE_PLUG_BLE_IOT = 90;

    /**
     * 产品类型
     * <p>
     * 支持ble/iot-球泡串，H7031/H7032
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_WARM_WHITE_BULB_LIGHT_V2 = 91;
    public static final int PACT_TYPE_4_WARM_WHITE_BULB_LIGHT_V2_1 = 1;
    public static final int PACT_CODE_4_WARM_WHITE_BULB_LIGHT_V2_1 = 1;

    /**
     * 产品类型
     * <p>
     * 支持 蓝牙/iot 的塔扇
     * h7102
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BLE_IOT_TOWER_FAN_V1 = 93;

    /**
     * 产品类型
     * <p>
     * 支持 ble+wifi 的 RGBIC 三角形灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_LAMP_TRIANGLE = 97;

    /**
     * 产品类型
     * <p>
     * ble+wifi 7061
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_FLOOD_LIGHT = 98;

    /**
     * 产品类型
     * <p>
     * ble+wifi 604a
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST = 95;
    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-香薰加湿机
     * 7160
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HUMIDIFIER_V3 = 99;
    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-香薰加湿机
     * 7161
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_146 = 146;
    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-香薰加湿机
     * 7162
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_189 = 189;
    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-电饭煲
     * 7180
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_152 = 152;
    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGBW户外球泡串
     * H7041
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB = 100;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGBW户外球泡串
     * H7042
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB_V2 = 102;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-星空灯
     * H6091
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_START_SKY_LIGHT = 101;
    public static final int PACT_TYPE_VALUE_START_SKY_LIGHT_1 = 1;
    public static final int PACT_CODE_VALUE_START_SKY_LIGHT_1 = 1;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi- 鹅颈壶
     * H7170
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_GOOSE_NECK_POT = 103;


    /**
     * 产品类型
     * <p>
     * 支持ble+wifi- 鹅颈壶
     * H7175
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_168 = 168;


    /**
     * 产品类型
     * <p>
     * 支持ble+wifi- 鹅颈壶
     * H7173
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_177 = 177;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-空气净化器
     * </p> H7126
     */
    public static final int GOODS_TYPE_VALUE_160 = 160;
    /**
     * 产品类型
     * <p>
     * 制冰机
     * H7172
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_IC_MAKER = 117;
    public static final int PACT_TYPE_VALUE_ICE_MAKER_1 = 1;
    public static final int PACT_CODE_VALUE_ICE_MAKER_1 = 1;


    /**
     * 产品类型
     * 支持ble+wifi-PC游戏灯柱
     * H6047
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_TV_GAME_FEAST = 119;
    public static final int PACT_TYPE_VALUE_TV_GAME_FEAST_1 = 1;
    public static final int PACT_CODE_VALUE_TV_GAME_FEAST_1 = 1;

    /**
     * 产品类型
     * 支持ble+wifi-电视灯柱
     * H6043
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6043 = 196;
    public static final int PACT_TYPE_VALUE_TV_FEAST_6043 = 1;
    public static final int PACT_CODE_VALUE_TV_FEAST_6043 = 1;
    public static final int PACT_TYPE_VALUE_TV_FEAST_6043_ENCRYPT = 2;
    public static final int PACT_CODE_VALUE_TV_FEAST_6043_ENCRYPT = 1;


    /**
     * 产品类型
     * 支持ble+wifi-电视灯柱
     * H6042
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6042 = 197;
    public static final int PACT_TYPE_VALUE_TV_FEAST_6042 = 1;
    public static final int PACT_CODE_VALUE_TV_FEAST_6042 = 1;
    public static final int PACT_TYPE_VALUE_TV_FEAST_6042_ENCRYPT = 2;
    public static final int PACT_CODE_VALUE_TV_FEAST_6042_ENCRYPT = 1;

    /**
     * 产品类型
     * <p>
     * ble+wifi 604b 盛宴两件套
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2 = 109;

    /**
     * 产品类型
     * <p>
     * ble+wifi H604D 游戏两件套
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4 = 133;


    public static final int PACT_TYPE_4_SWITCH_TRIPLE_BLE_IOT = 1;
    public static final int PACT_CODE_4_SWITCH_TRIPLE_BLE_IOT = 1;
    public static final int PACT_TYPE_4_SWITCH_TRIPLE_INDOOR_BLE_IOT = 2;
    public static final int PACT_CODE_4_SWITCH_TRIPLE_INDOOR_BLE_IOT = 1;


    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-六边形方块灯，立体
     * 6066
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_CUBE_LIGHT_V2 = 96;

    public static final int PACT_TYPE_4_CUBE_LIGHT_V2_1 = 1;
    public static final int PACT_CODE_4_CUBE_LIGHT_V2_1 = 1;
    public static final int PACT_CODE_4_CUBE_LIGHT_V2_2 = 2;

    /**
     * 产品类型
     * <p>
     * h6057儿童灯 ble+wif
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_CHILD_LIGHT = 104;
    public static final int PACT_TYPE_VALUE_BLE_WIFI_CHILD_LIGHT_V1_1 = 1;//儿童灯协议类型
    public static final int PACT_CODE_VALUE_BLE_WIFI_CHILD_LIGHT_V1_1 = 1;//儿童灯协议代码
    //h6057配置结尾-------------注：请勿挪动6057的goodsType与pactType、pactCode

    /**
     * 产品类型
     * <p>
     * 支持ble/iot-点光源路径灯，H7051
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_PATH_LIGHT_V2 = 105;
    public static final int PACT_TYPE_4_PATH_LIGHT_V2_1 = 1;
    public static final int PACT_CODE_4_PATH_LIGHT_V2_1 = 1;

    /**
     * 产品类型
     * <p>
     * 支持ble/iot-点光源路径灯，H7062
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_FLOOD_LIGHT_V2 = 107;
    public static final int PACT_TYPE_4_FlOOD_LIGHT_V2 = 1;
    public static final int PACT_CODE_4_FlOOD_LIGHT_V2 = 1;


    /**
     * 产品类型
     * <p>
     * h615E 108-ble+wif RGB灯带、BK芯片
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_RGB_LIGHT = 108;
    public static final int PACT_TYPE_VALUE_BLE_WIFI_RGB_LIGHT_V1_1 = 2;
    public static final int PACT_CODE_VALUE_BLE_WIFI_RGB_LIGHT_V1_1 = 1;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-空气净化器空气净化器
     * 7123
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_AIR_CLEANER_V3 = 110;

    /**
     * 产品类型
     * <p>
     * h601A 111-ble+wif RGB筒灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_RGB_FLUSH_LIGHT_PANEL = 111;
    public static final int PACT_TYPE_VALUE_BLE_WIFI_RGB_FLUSH_LIGHT_PANEL_V1_1 = 1;
    public static final int PACT_TYPE_VALUE_BLE_WIFI_RGB_FLUSH_LIGHT_PANEL_ENCRYPT = 2;
    public static final int PACT_CODE_VALUE_BLE_WIFI_RGB_FLUSH_LIGHT_PANEL_V1_1 = 1;

    /**
     * 产品类型
     * <p>
     * 支持ble/iot-射灯，H7065
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_SHOOT_LIGHT_V1 = 113;
    public static final int PACT_TYPE_4_SHOOT_LIGHT_V1 = 1;
    public static final int PACT_CODE_4_SHOOT_LIGHT_V1 = 1;
    /**
     * 产品类型
     * <p>
     * 支持ble+iot-音乐灯条，H610B
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_MUSIC_LIGHT_BAR = 114;
    public static final int PACT_TYPE_4_MUSIC_LIGHT_BAR = 1;
    public static final int PACT_CODE_4_MUSIC_LIGHT_BAR = 1;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-塔式取暖器
     * 7132
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HEATER_7132 = 115;

    /**
     * 产品类型
     * <p>
     * ble+iot 4段灯 7055
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_LED_PATH_LIGHTS = 116;
    public static final int PACT_TYPE_4_LED_PATH_LIGHTS = 1;
    public static final int PACT_CODE_4_LED_PATH_LIGHTS = 1;


    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGBIC类tv灯 h6046
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_TV_ATMOSPHERE_LIGHT_V2 = 112;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-空气净化器空气净化器
     * 7120
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_AIR_CLEANER_V4 = 118;

    /**
     * 产品类型
     * <p>
     * ble+wifi的rgbic灯带-H605c
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT = 120;
    public static final int PACT_TYPE_VALUE_4_BLE_WIFI_RGBIC_LIGHT = 1;
    public static final int PACT_CODE_VALUE_4_BLE_WIFI_RGBIC_LIGHT = 1;
    public static final int PACT_CODE_VALUE_4_H605C_V1 = 2;


    /**
     * 产品类型
     * <p>
     * 支持 ble+wifi 的 RGBIC Y 形灯
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_LAMP_Y = 121;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-604c
     *
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3 = 122;
    /**
     * 协议类型-ble+wifi 604c
     */
    public static final int PACT_TYPE_GAME_FEAST_V3 = 1;
    /**
     * 协议code 604c
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_GAME_FEAST_V3}
     * </p>
     */
    public static final int PACT_CODE_GAME_FEAST_V3 = 1;

    /**
     * 产品类型
     * <p>
     * ble+wifi的高端RGBICW灯带-H61E1
     * H61E1、H61E0、H61B1、H61B5
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END = 123;
    public static final int PACT_TYPE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END = 2;
    public static final int PACT_TYPE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END_V2 = 3;//支持蓝牙加密
    public static final int PACT_CODE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END = 1;
    public static final int PACT_CODE_VALUE_4_BLE_WIFI_RGBIC_HIGH_END_V2 = 2;/*支持刷新ic数*/

    /**
     * 产品类型
     * <p>
     * 支持ble-RGBIC类车载灯带 H7095
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_CAR_LIGHT_H7095 = 127;
    public static final int PACT_TYPE_VALUE_4_BLE_CAR_LIGHT_H7095 = 1;
    public static final int PACT_CODE_VALUE_4_BLE_CAR_LIGHT_H7095 = 1;
    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGBIC WW通体发光落地灯 H6078
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP = 128;
    public static final int PACT_TYPE_VALUE_4_RGBIC_WW_FLOOR_LAMP = 1;
    public static final int PACT_CODE_VALUE_4_RGBIC_WW_FLOOR_LAMP = 1;

    /**
     * 产品类型
     * <p>
     * ble+wifi的户外点光源 H705a
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_OUTDOOR_LAMP = 125;
    public static final int PACT_TYPE_VALUE_4_BLE_WIFI_OUTDOOR_LAMP = 1;
    public static final int PACT_CODE_VALUE_4_BLE_WIFI_OUTDOOR_LAMP = 1;
    public static final int PACT_CODE_VALUE_4_BLE_WIFI_H705A = 2;//h705a迭代 功能升级
    /**
     * 产品类型
     * <p>
     * ble+wifi的RGBIC 的TV灯带 H6168
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT = 136;
    public static final int PACT_TYPE_VALUE_4_BLE_WIFI_RGBIC_TV_LIGHT = 2;
    public static final int PACT_CODE_VALUE_4_BLE_WIFI_RGBIC_TV_LIGHT = 1;
    /**
     * 产品类型
     * <p>
     * ble的AI 蓝牙拾音盒 H1163
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_AI_MUSIC_BOX = 137;
    public static final int PACT_TYPE_VALUE_BLE_AI_MUSIC_BOX = 1;
    public static final int PACT_CODE_VALUE_BLE_AI_MUSIC_BOX = 1;
    /**
     * 产品类型
     * <p>
     * ble的AI 户外蓝牙拾音盒 H1167
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_MUSIC_BOX_1167 = 182;
    public static final int PACT_TYPE_VALUE_BLE_MUSIC_BOX_1167 = 1;
    public static final int PACT_CODE_VALUE_BLE_MUSIC_BOX_1167 = 1;

    /**
     * 产品类型
     * <p>
     * ble的AI 户外蓝牙拾音盒 H1167
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_MUSIC_BOX_1168 = 186;
    public static final int PACT_TYPE_VALUE_BLE_MUSIC_BOX_1168 = 1;
    public static final int PACT_CODE_VALUE_BLE_MUSIC_BOX_1168 = 1;

    /**
     * 产品类型
     * <p>
     * h70B1铜线窗帘灯 ble+wif
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT = 134;
    public static final int PACT_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT_V1_1 = 1;//h70B1铜线窗帘灯协议类型
    public static final int PACT_CODE_VALUE_BLE_WIFI_CURTAIN_LIGHT_V1_1 = 1;//h70B1铜线窗帘灯协议代码
    public static final int PACT_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT_V2_2 = 2;//h70B1铜线窗帘灯协议类型-支持拼接 gif
    //h70B1配置结尾-------------注：请勿挪动h70B1的goodsType与pactType、pactCode

    /**
     * h70b2窗帘灯
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT_H70B2 = 185;
    public static final int PACT_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT_H70B2_V1_1 = 1;//h70B2铜线窗帘灯协议类型
    public static final int PACT_CODE_VALUE_BLE_WIFI_CURTAIN_LIGHT_H70B2_V1_1 = 1;//h70B2铜线窗帘灯协议代码
    // TODO :@张臣祥 2023/8/1 16:10 暂时用2 是为了调试，到时候还要从1开始
    public static final int PACT_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT_H70B2_V1_2 = 2;
    public static final int PACT_CODE_VALUE_BLE_WIFI_CURTAIN_LIGHT_H70B2_V1_2 = 2;
    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-热雾加湿机
     * 7143
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HUMIDIFIER_V4 = 126;
    public static final int PACT_TYPE_HUMIDIFIER_V4 = 1;
    public static final int PACT_TYPE_HUMIDIFIER_V4_2 = 2;
    public static final int PACT_CODE_HUMIDIFIER_V4 = 1;
    public static final int PACT_CODE_HUMIDIFIER_V4_2 = 2;


    /**
     * 产品类型
     * <p>
     * 支持ble+wifi- 鹅颈壶
     * H7171
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_GOOSE_NECK_POT_V2 = 135;
    /*----------------------------协议类型------------------------------*/
    public static final int PACT_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2_1 = 1;
    public static final int PACT_CODE_VALUE_SPLICE_STRIP_LIGHT_V2_1 = 1;


    /**
     * 产品类型
     * <p>
     * ble+wifi的rgbic电视灯带 支持HDMI H6601
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT_HDMI = 138;

    /**
     * 产品类型
     * <p>
     * h61C3 桌边勾勒霓虹灯带 ble+wif
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_DESK_LIGHT = 141;
    public static final int PACT_TYPE_VALUE_BLE_WIFI_DESK_LIGHT_V1_1 = 1;//协议类型
    public static final int PACT_TYPE_VALUE_BLE_WIFI_DESK_LIGHT_V1_2 = 2;//协议类型
    public static final int PACT_CODE_VALUE_BLE_WIFI_DESK_LIGHT_V1_1 = 1;//协议代码
    //h61C3配置结尾-------------注：请勿挪动h61C3的goodsType与pactType、pactCode


    /**
     * 产品类型 6pacts 酷壁灯
     * H6088
     */
    public static final int GOODS_TYPE_VALUE_H6088 = 157;
    public static final int PACT_TYPE_VALUE_H6088 = 1;
    public static final int PACT_CODE_VALUE_H6088 = 1;
    public static final int PACT_TYPE_VALUE_H6088_ENCRYPT = 2;
    public static final int PACT_CODE_VALUE_H6088_ENCRYPT = 1;


    /**
     * 产品类型 4pacts
     * H7066
     */
    public static final int GOODS_TYPE_VALUE_H7066 = 171;
    public static final int PACT_TYPE_VALUE_H7066 = 1;
    public static final int PACT_CODE_VALUE_H7066 = 1;

    /**
     * 产品类型
     * H608a
     */
    public static final int GOODS_TYPE_VALUE_H608a = 184;
    public static final int PACT_TYPE_VALUE_H608a = 1;
    public static final int PACT_CODE_VALUE_H608a = 1;
    public static final int PACT_TYPE_VALUE_H608a_encryption = 2;
    public static final int PACT_CODE_VALUE_H608a_encryption = 1;


    /**
     * 产品类型
     * H7184
     */
    public static final int GOODS_TYPE_VALUE_H7184 = 208;
    public static final int PACT_TYPE_H7184 = 1;
    public static final int PACT_CODE_H7184 = 1;
    /**
     * 产品类型 H7112  Wi-Fi+蓝牙 空气循环扇
     */
    public static final int GOODS_TYPE_VALUE_H7112 = 236;
    public static final int PACT_TYPE_H7112 = 1;
    public static final int PACT_CODE_H7112 = 1;

    /**
     * 产品类型 制冰机
     * H7178
     * ble + wifi
     */
    public static final int GOODS_TYPE_VALUE_H7178 = 212;

    /**
     * 产品类型 制冰机
     * H717D
     * ble + wifi
     */
    public static final int GOODS_TYPE_VALUE_H717D = 211;

    /**
     * 产品类型 H60b2  Wi-Fi+蓝牙 落地灯
     */
    public static final int GOODS_TYPE_VALUE_H60B2 = 312;
    public static final int PACT_TYPE_H60B2 = 1;
    public static final int PACT_CODE_H60B2 = 1;

    /**
     * 产品类型 H5171  纯蓝牙 温湿度计
     */
    public static final int GOODS_TYPE_VALUE_H5171 = 320;

    /**
     * 协议类型-stripLight ble+wifi
     * <p>
     */
    public static final int PACT_TYPE_4_STRIP_LIGHT_BLE_WIFI_V2_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #GOODS_TYPE_VALUE_STRING_LIGHT_BLE_WIFI_V2}
     */
    public static final int PACT_CODE_4_STRIP_LIGHT_BLE_WIFI_V2_1_1 = 1;
    public static final int PACT_CODE_4_STRIP_LIGHT_BLE_WIFI_V2_1_2 = 2;
    /**
     * 协议类型-stripLight ble+wifi
     * <p>
     */
    public static final int PACT_TYPE_4_STRIP_LIGHT_BLE_WIFI_V2_2 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #GOODS_TYPE_VALUE_STRING_LIGHT_BLE_WIFI_V2}
     */
    public static final int PACT_CODE_4_STRIP_LIGHT_BLE_WIFI_V2_2_1 = 1;

    /**
     * 协议类型-stringLight ble-蓝牙款灯带默认第一款pactType从2开始，协议暂定支持1类
     * <p>
     */
    public static final int PACT_TYPE_4_STRING_LIGHT_BLE_V2_1 = 2;
    public static final int PACT_TYPE_4_STRING_LIGHT_BLE_V2_2 = 1;
    /**
     * 协议类型-stringLight ble-蓝牙款灯带默认第一款pactType从2开始，支持BK cost down
     */
    public static final int PACT_TYPE_4_STRING_LIGHT_BLE_V2_3 = 10;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_STRING_LIGHT_BLE_V2_1}{@link #PACT_TYPE_4_STRING_LIGHT_BLE_V2_2}
     */
    public static final int PACT_CODE_4_STRING_LIGHT_BLE_V2_1_1 = 1;
    public static final int PACT_CODE_4_STRING_LIGHT_BLE_V2_1_2 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_STRING_LIGHT_BLE_V2_3}
     */
    public static final int PACT_CODE_4_STRING_LIGHT_BLE_V2_3_1 = 1;
    public static final int PACT_CODE_4_STRING_LIGHT_BLE_V2_3_1_frk = 2;

    /**
     * 协议类型-bulbStringLight ble-球泡串蓝牙默认第一款的pactType从3开始，协议暂定支持1，2，3类
     * <p>
     */
    public static final int PACT_TYPE_4_BULB_LIGHT_STRING_BLE_V1_1 = 3;
    public static final int PACT_TYPE_4_BULB_LIGHT_STRING_BLE_V1_2 = 2;
    public static final int PACT_TYPE_4_BULB_LIGHT_STRING_BLE_V1_3 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_BULB_LIGHT_STRING_BLE_V1_1}
     */
    public static final int PACT_CODE_4_BULB_LIGHT_STRING_BLE_V1_1_1 = 1;

    /**
     * 协议类型-plug单孔
     * <p>
     */
    public static final int PACT_TYPE_4_PLUG_ONE_IOT_V1_1 = 4;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_PLUG_ONE_IOT_V1_1}
     */
    public static final int PACT_CODE_4_PLUG_ONE_IOT_V1_1_1 = 1;

    /**
     * 协议类型-bulbStringLight ble+iot-球泡串蓝牙Wi-Fi默认第一款的pactType从5开始，协议暂定支持1，2，3，4类
     * <p>
     */
    public static final int PACT_TYPE_4_BULB_LIGHT_STRING_BLE_IOT_V1_1 = 5;
    public static final int PACT_TYPE_4_BULB_LIGHT_STRING_BLE_IOT_V1_2 = 4;
    public static final int PACT_TYPE_4_BULB_LIGHT_STRING_BLE_IOT_V1_3 = 3;
    public static final int PACT_TYPE_4_BULB_LIGHT_STRING_BLE_IOT_V1_4 = 2;
    public static final int PACT_TYPE_4_BULB_LIGHT_STRING_BLE_IOT_V1_5 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_BULB_LIGHT_STRING_BLE_IOT_V1_1}
     */
    public static final int PACT_CODE_4_BULB_LIGHT_STRING_BLE_IOT_V1_1 = 1;

    /**
     * 协议类型-carLight
     * <p>
     */
    public static final int PACT_TYPE_4_CAR_LIGHT_BLE_V1_1 = 1;
    public static final int PACT_TYPE_4_CAR_LIGHT_BLE_V1_1_FRK = 10;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_CAR_LIGHT_BLE_V1_1}
     */
    public static final int PACT_CODE_4_CAR_LIGHT_BLE_V1_1 = 1;
    public static final int PACT_CODE_4_CAR_LIGHT_BLE_V1_2 = 2;
    public static final int PACT_CODE_4_CAR_LIGHT_BLE_V1_FRK = 1;

    /**
     * 协议类型-ble+wifi的th
     * <p>
     */
    public static final int PACT_TYPE_4_TH_BLE_WIFI_V1_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TH_BLE_WIFI_V1_1}
     */
    public static final int PACT_CODE_4_TH_BLE_WIFI_V1_1 = 1;

    /**
     * 协议类型-ble的th
     * <p>
     */
    public static final int PACT_TYPE_4_TH_BLE_V1_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TH_BLE_V1_1}
     */
    public static final int PACT_CODE_4_TH_BLE_V1_1 = 1;

    /**
     * 协议类型-bulbLight iot
     * <p>
     */
    public static final int PACT_TYPE_4_BULB_LIGHT_IOT_V1_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_BULB_LIGHT_IOT_V1_1}
     */
    public static final int PACT_CODE_4_BULB_LIGHT_IOT_V1_1 = 1;

    /**
     * 协议类型-bulbLight iot
     * <p>
     */
    public static final int PACT_TYPE_4_WHITE_BULB_LIGHT_IOT_V1_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_WHITE_BULB_LIGHT_IOT_V1_1}
     */
    public static final int PACT_CODE_4_WHITE_BULB_LIGHT_IOT_V1_1 = 1;

    /**
     * 协议类型-chLight
     * <p>
     */
    public static final int PACT_TYPE_4_CH_LIGHT_BLE_V1_1 = 1;
    public static final int PACT_TYPE_4_CH_LIGHT_BLE_V1_BK = 10;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_CH_LIGHT_BLE_V1_1}
     */
    public static final int PACT_CODE_4_CH_LIGHT_BLE_V1_1 = 1;
    public static final int PACT_CODE_4_CH_LIGHT_BLE_V1_BK = 1;

    /**
     * 协议类型-tvLight
     * <p>
     */
    public static final int PACT_TYPE_4_TV_LIGHT_BLE_V1_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TV_LIGHT_BLE_V1_1}
     */
    public static final int PACT_CODE_4_TV_LIGHT_BLE_V1_1 = 1;

    /**
     * 协议类型-bare Light
     * <p>
     */
    public static final int PACT_TYPE_4_BARE_LIGHT_BLE_V1_1 = 1;
    public static final int PACT_TYPE_4_BARE_LIGHT_BLE_BK_V1 = 10;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_BARE_LIGHT_BLE_V1_1}
     */
    public static final int PACT_CODE_4_BARE_LIGHT_BLE_V1_1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_BARE_LIGHT_BLE_V1_1}
     */
    public static final int PACT_CODE_4_BARE_LIGHT_BLE_V1_2 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_BARE_LIGHT_BLE_BK_V1}
     */
    public static final int PACT_CODE_4_BARE_LIGHT_BLE_BK_V1_1 = 1;

    /**
     * 协议类型-1拖1的蓝牙温湿度计
     * <p>
     */
    public static final int PACT_TYPE_4_TH_MULTI_BLE_V1_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TH_MULTI_BLE_V1_1}
     */
    public static final int PACT_CODE_4_TH_MULTI_BLE_V1_1 = 1;


    /**
     * 协议类型-plug室外双孔
     * <p>
     */
    public static final int PACT_TYPE_4_OUTDOOR_PLUG_IOT_V1_1 = 4;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_CODE_4_OUTDOOR_PLUG_IOT_V1_1_1}
     */
    public static final int PACT_CODE_4_OUTDOOR_PLUG_IOT_V1_1_1 = 1;

    /**
     * 协议类型-RGBWW的forHome灯带
     * <p>
     */
    public static final int PACT_TYPE_4_HOME_LIGHT_GRBWW_V1_1 = 1;
    public static final int PACT_TYPE_4_HOME_LIGHT_GRBWW_BK_V1_1 = 2;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_HOME_LIGHT_GRBWW_V1_1}
     */
    public static final int PACT_CODE_4_HOME_LIGHT_GRBWW_V1_1_1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_HOME_LIGHT_GRBWW_BK_V1_1}
     */
    public static final int PACT_CODE_4_HOME_LIGHT_GRBWW_BK_V1_1_1 = 1;

    /**
     * 协议类型-ble幻彩的灯带
     * <p>
     */
    public static final int PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_V1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_V1 = 1;
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_V2 = 2;

    /**
     * 协议类型-ble幻彩的灯带 -bk方案
     * <p>
     */
    public static final int PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_BK_V1 = 10;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_BK_V1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_BK_V1 = 1;
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_BK_V1_FRK = 2;
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_BK_V1_FRK_V1 = 3;/*H6171-FRK方案-ic数有变化*/

    /**
     * 协议类型-ble幻彩的灯带-支持限流
     * <p>
     */
    public static final int PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V1 = 1;
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V2 = 2;

    /**
     * 协议类型-ble幻彩的灯带-支持限流
     * <p>
     */
    public static final int PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_BK_V1 = 10;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_BK_V1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_BK_V1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_BK_V1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_BK_V1_FRK = 2;

    /**
     * 协议类型-ble+wifi幻彩的灯带
     * <p>
     */
    public static final int PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_WIFI_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_WIFI_1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_WIFI_1 = 1;
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_WIFI_2 = 2;

    /**
     * 协议类型-ble+wifi幻彩的灯带-BK方案
     * <p>
     */
    public static final int PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_WIFI_BK_1 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_BLE_WIFI_BK_1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_WIFI_BK_1 = 1;
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_BLE_WIFI_FRK_1 = 2;


    /**
     * 协议类型-bulbLight
     * <p>
     */
    public static final int PACT_TYPE_4_BULB_LIGHT_BLE_V1_1 = 1;

    /**
     * 协议code---bulbLight
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_BULB_LIGHT_BLE_V1_1}
     */
    public static final int PACT_CODE_4_BULB_LIGHT_BLE_V1_1 = 1;

    /**
     * 协议类型-ble+wifi幻彩的灯带V2版本
     * <p>
     */
    public static final int PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V2_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V2_1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V2_1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V2_1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V2_2 = 2;

    /**
     * 协议类型-ble+wifi幻彩的灯带V3版本-bk方案
     * <p>
     */
    public static final int PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V3_1 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V3_1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_WIFI_V3_1 = 1;


    /**
     * 协议类型-ble+双灯柱+幻彩的灯带
     * <p>
     */
    public static final int PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V2_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V2_1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V2_1 = 1;

    /**
     * 协议类型-ble+双灯柱+幻彩的灯带 6053 芯片迭代
     * <p>
     */
    public static final int PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V2_2 = 2;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V2_1}
     */
    public static final int PACT_CODE_4_DREAM_COLOR_LIGHT_LIMIT_BLE_V2_2 = 1;

    /**
     * 协议类型-ble+wifi带摄像头 tv灯带
     * <p>
     */
    public static final int PACT_TYPE_4_TV_LIGHT_CAMERA_V2 = 1;
    /**
     * 协议类型-ble+wifi带摄像头 tv灯带 新协议扩展
     */
    public static final int PACT_TYPE_4_TV_LIGHT_CAMERA_V2_1 = 2;

    /**
     * 协议类型-ble+wifi带摄像头 6199XD2
     */
    public static final int PACT_TYPE_4_TV_LIGHT_CAMERA_V3_1 = 3;

    /**
     * 协议类型-ble+wifi带摄像头 6198
     */
    public static final int PACT_TYPE_4_TV_LIGHT_CAMERA_V4_1 = 4;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TV_LIGHT_CAMERA_V2}
     */
    public static final int PACT_CODE_4_4_TV_LIGHT_CAMERA_V2 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TV_LIGHT_CAMERA_V2_1}
     */
    public static final int PACT_CODE_4_4_TV_LIGHT_CAMERA_V2_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TV_LIGHT_CAMERA_V3_1}
     */
    public static final int PACT_CODE_4_4_TV_LIGHT_CAMERA_V3_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TV_LIGHT_CAMERA_V4_1}
     */
    public static final int PACT_CODE_4_4_TV_LIGHT_CAMERA_V4_1 = 1;

    /**
     * 协议类型-ble+wifi的床头灯
     * <p>
     */
    public static final int PACT_TYPE_4_TABLE_LAMP_BLE_WIFI_V1 = 1;
    /**
     * 协议类型-ble+wifi的床头灯-富芮坤方案
     */
    public static final int PACT_TYPE_4_TABLE_LAMP_BLE_WIFI_V2 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TABLE_LAMP_BLE_WIFI_V1}
     */
    public static final int PACT_CODE_4_TABLE_LAMP_BLE_WIFI_V1_1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TABLE_LAMP_BLE_WIFI_V2}
     */
    public static final int PACT_CODE_4_TABLE_LAMP_BLE_WIFI_V2_1 = 1;

    /**
     * 协议类型-ble+wifi的立式直头落地灯
     * <p>
     */
    public static final int PACT_TYPE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TABLE_LAMP_BLE_WIFI_V1}
     */
    public static final int PACT_CODE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1_1 = 1;

    /**
     * 协议类型-ble+wifi的立式直头落地灯
     * <p>芯片迭代
     */
    public static final int PACT_TYPE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1_2 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TABLE_LAMP_BLE_WIFI_V1}
     */
    public static final int PACT_CODE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1_2 = 1;

    /**
     * 协议类型-ble+wifi带摄像头 双灯柱tv灯带
     * <p>
     */
    public static final int PACT_TYPE_4_TV_LIGHT_CAMERA_V4 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TV_LIGHT_CAMERA_V4}
     */
    public static final int PACT_CODE_4_4_TV_LIGHT_CAMERA_V4 = 1;

    /**
     * 协议类型-ble+wifi带摄像头 双灯柱tv灯带 芯片迭代
     * <p>
     */
    public static final int PACT_TYPE_4_TV_LIGHT_CAMERA_V4_V2 = 2;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TV_LIGHT_CAMERA_V4_V2}
     */
    public static final int PACT_CODE_4_4_TV_LIGHT_CAMERA_V4_V2 = 1;

    /**
     * 协议类型-ble+wifi带摄像头 双灯柱tv灯带 H6049
     * <p>
     */
    public static final int PACT_TYPE_4_TV_LIGHT_CAMERA_V4_V3 = 3;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TV_LIGHT_CAMERA_V4_V3}
     */
    public static final int PACT_CODE_4_4_TV_LIGHT_CAMERA_V4_V3 = 1;

    /**
     * 协议类型-wifi 智能墙壁开关
     * <p>
     */
    public static final int PACT_TYPE_4_SWITCH_ONE_IOT_V1_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_SWITCH_ONE_IOT_V1_1}
     */
    public static final int PACT_CODE_4_SWITCH_ONE_IOT_V1_1_1 = 1;

    /**
     * 协议类型-立式镂空灯
     * <p>
     */
    public static final int PACT_TYPE_4_VERTICAL_HOLLOW_LAMP_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_VERTICAL_HOLLOW_LAMP_V1}
     */
    public static final int PACT_CODE_4_VERTICAL_HOLLOW_LAMP_V1 = 1;

    /**
     * 协议类型-支持ble+IOT的灯泡 bulbLight
     * <p>
     */
    public static final int PACT_TYPE_4_BULB_LIGHT_BLE_IOT_V1_1 = 1;

    /**
     * 协议code---bulbLight
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_BULB_LIGHT_BLE_IOT_V1_1}
     */
    public static final int PACT_CODE_4_BULB_LIGHT_BLE_IOT_V1_1 = 1;

    /**
     * 协议code -- bbq 单探针
     * <p>
     * 支持的协议类型是{@link #PACT_CODE_4_BBQ_V1_BLE_1}
     */
    public static final int PACT_CODE_4_BBQ_V1_BLE_1 = 1;

    /**
     * 协议类型-ble的th
     * <p>
     */
    public static final int PACT_TYPE_4_BBQ_V1_BLE_V1_1 = 1;

    /**
     * 协议code -- bbq 多探针（2）
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TH_BLE_WIFI_V1_1}
     */
    public static final int PACT_CODE_4_BBQ_MULTI_BLE_1 = 1;

    /**
     * 协议类型-ble --  bbq 多探针（2）
     * <p>
     */
    public static final int PACT_TYPE_4_BBQ_MULTI_BLE_V1_1 = 1;
    /**
     * 协议code -- bbq 多探针（4）
     * <p>
     */
    public static final int PACT_CODE_4_BBQ_MULTI_BLE_2 = 1;

    /**
     * 协议类型-ble --  bbq 多探针（4）
     * <p>
     */
    public static final int PACT_TYPE_4_BBQ_MULTI_BLE_V1_2 = 1;

    /**
     * 协议code -- bbq 4探针无屏
     * <p>
     */
    public static final int PACT_CODE_BBQ_NO_SCREEN_BLE_V1_4 = 1;

    /**
     * 协议类型-ble --  bbq 4探针无屏
     * <p>
     */
    public static final int PACT_TYPE_BBQ_NO_SCREEN_BLE_V1_4 = 1;

    /**
     * 协议code -- bbq 2探针无屏
     * <p>
     */
    public static final int PACT_CODE_BBQ_NO_SCREEN_BLE_V1_2 = 1;

    /**
     * 协议类型-ble --  bbq 2探针无屏
     * <p>
     */
    public static final int PACT_TYPE_BBQ_NO_SCREEN_BLE_V1_2 = 1;

    /**
     * 协议类型-外置蓝牙拾音盒
     * <p>
     */
    public static final int PACT_TYPE_4_PICKUP_BOX_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_PICKUP_BOX_V1}
     */
    public static final int PACT_CODE_4_PICKUP_BOX_V1 = 1;

    /**
     * 协议类型-外置蓝牙拾音盒迭代
     * <p>
     */
    public static final int PACT_TYPE_4_PICKUP_BOX_V2 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_PICKUP_BOX_V1}
     */
    public static final int PACT_CODE_4_PICKUP_BOX_V2 = 2;

    /**
     * 协议类型-外置蓝牙拾音盒迭代_兼容设备 ef读回来是2 2,蓝牙广播是1 2，需要app兼容
     * <p>
     */
    public static final int PACT_TYPE_4_PICKUP_BOX_V3 = 2;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_PICKUP_BOX_V3}
     */
    public static final int PACT_CODE_4_PICKUP_BOX_V3 = 2;

    /**
     * 协议类型-支持ble的灯泡串 bulbLightString v2
     * <p>
     */
    public static final int PACT_TYPE_4_BULB_LIGHT_STRING_BLE_V2_1 = 1;

    /**
     * 协议code-支持ble的灯泡串 bulbLightString v2
     * <p>
     * 支持的协议类型是{@link #PACT_CODE_4_BULB_LIGHT_STRING_BLE_V2_1}
     */
    public static final int PACT_CODE_4_BULB_LIGHT_STRING_BLE_V2_1 = 1;

    /**
     * 协议类型-露营灯
     * <p>
     */
    public static final int PACT_TYPE_4_CAMPING_LIGHT_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_CAMPING_LIGHT_V1}
     */
    public static final int PACT_CODE_4_CAMPING_LIGHT_V1 = 1;
    /**
     * 协议类型-床头灯
     * <p>
     */
    public static final int PACT_TYPE_4_BED_LIGHT_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_BED_LIGHT_V1}
     */
    public static final int PACT_CODE_4_BED_LIGHT_V1 = 1;

    /**
     * 协议类型-支持ble+IOT的灯泡 bulbLight
     * <p>
     */
    public static final int PACT_TYPE_4_BULB_LIGHT_BLE_IOT_V2_1 = 1;

    /**
     * 协议code---bulbLight
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_BULB_LIGHT_BLE_IOT_V2_1}
     */
    public static final int PACT_CODE_4_BULB_LIGHT_BLE_IOT_V2_1 = 1;

    /**
     * 协议类型-bk的ble-rgb灯
     */
    public static final int PACT_TYPE_4_BK_BLE_RGB_V1 = 10;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_BK_BLE_RGB_V1}
     * </p>
     */
    public static final int PACT_CODE_4_BK_BLE_RGB_V1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_BK_BLE_RGB_V1}
     * </p>
     */
    public static final int PACT_CODE_4_BK_BLE_RGB_V2 = 2;

    /**
     * 协议类型-bk的ble+wifi-rgb灯
     */
    public static final int PACT_TYPE_4_BK_BLE_WIFI_RGB_V1 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_BK_BLE_WIFI_RGB_V1}
     * </p>
     */
    public static final int PACT_CODE_4_BK_BLE_WIFI_RGB_V1 = 1;


    /**
     * 协议类型-ble+wifi-拼接条状灯
     */
    public static final int PACT_TYPE_4_SPLICE_STRIP_LIGHT_V1_1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_SPLICE_STRIP_LIGHT_V1_1}
     * </p>
     */
    public static final int PACT_CODE_4_SPLICE_STRIP_LIGHT_V1_1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_SPLICE_STRIP_LIGHT_V1_1}
     * </p>
     */
    public static final int PACT_CODE_4_SPLICE_STRIP_LIGHT_V1_1_2 = 2;

    /**
     * 协议类型-ble+wifi-拼接条状灯-富芮坤方案
     */
    public static final int PACT_TYPE_4_SPLICE_STRIP_LIGHT_V1_2 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_SPLICE_STRIP_LIGHT_V1_2}
     * </p>
     */
    public static final int PACT_CODE_4_SPLICE_STRIP_LIGHT_V1_2 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_SPLICE_STRIP_LIGHT_V1_2}
     * </p>
     */
    public static final int PACT_CODE_4_SPLICE_STRIP_LIGHT_V1_2_2 = 2;
    /**
     * 协议类型-ble+wifi-拼接条状灯-6062迭代
     */
    public static final int PACT_TYPE_4_SPLICE_STRIP_LIGHT_V1_3 = 3;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_SPLICE_STRIP_LIGHT_V1_3}
     * </p>
     */
    public static final int PACT_CODE_4_SPLICE_STRIP_LIGHT_V1_3 = 1;
    /**
     * 协议类型-ble+wifi-拼接条状灯-6062迭代
     */
    public static final int PACT_TYPE_4_SPLICE_STRIP_LIGHT_V1_4 = 4;
    /**
     * 协议code
     */
    public static final int PACT_CODE_4_SPLICE_STRIP_LIGHT_V1_4 = 1;

    /**
     * 协议类型-ble-移动氛围灯
     */
    public static final int PACT_TYPE_4_MOVE_ATMOSPHERE_LIGHT = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_MOVE_ATMOSPHERE_LIGHT}
     * </p>
     */
    public static final int PACT_CODE_4_MOVE_ATMOSPHERE_LIGHT = 1;

    /**
     * 协议类型-wifi/Ble 智能墙壁开关
     * <p>
     */
    public static final int PACT_TYPE_4_SWITCH_ONE_BLE_IOT_V1_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_SWITCH_ONE_IOT_V1_1}
     */
    public static final int PACT_CODE_4_SWITCH_ONE_BLE_IOT_V1_1 = 1;
    /**
     * 协议code
     * <p> H5083、H5085,与H5080仅仅只有外观不一样
     * 支持的协议类型是{@link #PACT_TYPE_4_SWITCH_ONE_IOT_V1_1}
     */
    public static final int PACT_CODE_4_SWITCH_ONE_BLE_IOT_V2_1 = 2;

    /**
     * 协议类型-wifi/Ble 智能墙壁开关
     * <p>
     */
    public static final int PACT_TYPE_4_SWITCH_TWO_BLE_IOT_V1_1 = 1;

    /**
     * 协议code
     * <p>智能墙壁开关 两孔 支持10个定时
     * 支持的协议类型是{@link #PACT_TYPE_4_SWITCH_ONE_IOT_V1_1}
     */
    public static final int PACT_CODE_4_SWITCH_TWO_BLE_IOT_V2 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_SWITCH_ONE_IOT_V1_1}
     */
    public static final int PACT_CODE_4_SWITCH_TWO_BLE_IOT_V1_1 = 1;

    /**
     * 协议类型-ble-车载灯带
     */
    public static final int PACT_TYPE_4_CAR_LIGHT_V1 = 10;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_CAR_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_4_CAR_LIGHT_V1 = 1;
    public static final int PACT_CODE_4_CAR_LIGHT_V2_H6119_FRK = 2;

    /**
     * 协议类型-ble-车载灯带
     */
    public static final int PACT_TYPE_4_CAR_BOTTOM_LIGHT_V1 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_CODE_4_CAR_BOTTOM_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_4_CAR_BOTTOM_LIGHT_V1 = 1;

    /**
     * 协议类型-ble-车载灯带 8段
     */
    public static final int PACT_TYPE_4_CAR_BOTTOM_LIGHT_8_V1 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_CODE_4_CAR_BOTTOM_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_4_CAR_BOTTOM_LIGHT_8_V1 = 1;

    /**
     * 协议类型-ble-车载灯带 6段
     */
    public static final int PACT_TYPE_4_CAR_BOTTOM_LIGHT_6_V1 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_CODE_4_CAR_BOTTOM_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_4_CAR_BOTTOM_LIGHT_6_V1 = 1;

    /**
     * 协议类型-ble+wifi-点光源路径灯
     */
    public static final int PACT_TYPE_4_PATH_LIGHT_V1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_PATH_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_4_PATH_LIGHT_V1 = 1;


    /**
     * 协议类型-ble+wifi-墙面灯
     */
    public static final int PACT_TYPE_4_CUBE_LIGHT_V1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_CUBE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_4_CUBE_STRIP_LIGHT_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_CUBE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_4_CUBE_STRIP_LIGHT_V2 = 2;
    public static final int PACT_CODE_4_CUBE_STRIP_LIGHT_V3 = 3;
    /**
     * 协议类型-ble+wifi-加湿器
     */
    public static final int PACT_TYPE_HUMIDIFIER_V1 = 1;
    public static final int PACT_TYPE_HUMIDIFIER_V2 = 2;

    /**
     * 协议类型-ble+wifi-加湿器 - h7145
     */
    public static final int PACT_TYPE_HUMIDIFIER_7145 = 1;
    public static final int PACT_TYPE_HUMIDIFIER_7145_V2 = 2;
    /**
     * 协议code- 加湿器
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_CUBE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_HUMIDIFIER_V1 = 1;
    public static final int PACT_CODE_HUMIDIFIER_V2 = 2;
    /**
     * 协议code- 加湿器
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_CUBE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_HUMIDIFIER_7145 = 1;

    /**
     * 协议类型-ble+wifi-取暖器
     */
    public static final int PACT_TYPE_HEATER_V1 = 1;
    public static final int PACT_TYPE_HEATER_V2 = 2;
    /**
     * 协议code- 取暖器
     * <p>
     * 支持的CODE{@link #PACT_TYPE_4_CUBE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_HEATER_V1 = 1;
    public static final int PACT_CODE_HEATER_V2 = 2;

    /**
     * 协议类型-ble+wifi-暖白球泡灯串
     */
    public static final int PACT_TYPE_4_WARM_WHITE_BULB_LIGHT_V1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_WARM_WHITE_BULB_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_4_WARM_WHITE_BULB_LIGHT_V1 = 1;

    /**
     * 协议类型-ble+wifi-电视氛围灯 H6056
     */
    public static final int PACT_TYPE_4_TV_ATMOSPHERE_LIGHT_V1 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_TV_ATMOSPHERE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_4_TV_ATMOSPHERE_LIGHT_V1 = 1;

    /**
     * 协议类型-ble+wifi-电视氛围灯 H6046
     */
    public static final int PACT_TYPE_4_TV_ATMOSPHERE_LIGHT_V2_1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_TV_ATMOSPHERE_LIGHT_V2_1}
     * </p>
     */
    public static final int PACT_CODE_4_TV_ATMOSPHERE_LIGHT_V2_1 = 1;

    /**
     * 协议类型-ble+wifi-户外球泡灯串 7020
     */
    public static final int PACT_TYPE_4_BULB_STRING = 1;
    /**
     * 协议类型-ble+wifi-户外球泡灯串 7020 蓝牙加密
     */
    public static final int PACT_TYPE_4_BULB_STRING_2 = 2;

    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_BULB_STRING}
     * </p>
     */
    public static final int PACT_CODE_4_BULB_STRING = 1;

    /**
     * 协议类型-ble+wifi-户外球泡灯串 7021
     */
    public static final int PACT_TYPE_4_BULB_STRING_V2 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_BULB_STRING_V2}
     * </p>
     */
    public static final int PACT_CODE_4_BULB_STRING_V2 = 1;


    /**
     * 协议类型-ble + wifi -暖白鹅颈落地灯（球泡）
     */
    public static final int PACT_TYPE_4_GOOSENECK_FLOOR_LAMP_BLE_WIFI_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_GOOSENECK_FLOOR_LAMP_BLE_WIFI_V1}
     * </p>
     */
    public static final int PACT_CODE_4_GOOSENECK_FLOOR_LAMP_BLE_WIFI_V1 = 1;


    /**
     * 协议类型-ble + wifi - RGBWW 圆盘向上发光灯
     */
    public static final int PACT_TYPE_4_UPGLOW_LAMP_BLE_WIFI_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_UPGLOW_LAMP_BLE_WIFI_V1}
     * </p>
     */
    public static final int PACT_CODE_4_UPGLOW_LAMP_BLE_WIFI_V1 = 1;


    /**
     * 协议类型-ble + wifi -移动氛围灯
     */
    public static final int PACT_TYPE_4_MOVE_ATMOSPHERE_LIGHT_V2 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_MOVE_ATMOSPHERE_LIGHT_V2}
     * </p>
     */
    public static final int PACT_CODE_4_MOVE_ATMOSPHERE_LIGHT_V2 = 1;

    /**
     * 协议类型-ble的th
     * <p>
     */
    public static final int PACT_TYPE_4_TH_BLE_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TH_BLE_1}
     */
    public static final int PACT_CODE_4_TH_BLE_1 = 1;

    /**
     * 协议类型-ble的th
     * <p>
     */
    public static final int PACT_TYPE_4_TH_BLE_IOT_GATEWAY_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TH_BLE_1}
     */
    public static final int PACT_CODE_4_TH_BLE_IOT_GATEWAY_1 = 1;

    /**
     * 协议类型-ble-户外球泡灯串 7015
     */
    public static final int PACT_TYPE_4_BULB_STRING_V3 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_BULB_STRING_V3}
     * </p>
     */
    public static final int PACT_CODE_4_BULB_STRING_V3 = 1;

    /**
     * 协议类型-ble-户外球泡灯串 7016
     */
    public static final int PACT_TYPE_4_BULB_STRING_V4 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_BULB_STRING_V4}
     * </p>
     */
    public static final int PACT_CODE_4_BULB_STRING_V4 = 1;

    /**
     * 协议类型-ble+wifi-球泡灯
     */
    public static final int PACT_TYPE_4_BULB_V1 = 1;
    public static final int PACT_TYPE_4_BULB_ENCRYPT = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_BULB_V1}
     * </p>
     */
    public static final int PACT_CODE_4_BULB_V1 = 1;

    /**
     * 协议类型-ble+wifi-球泡灯-支持蓝牙加密
     */
    public static final int PACT_TYPE_4_BULB_V2 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_BULB_V2}
     * </p>
     */
    public static final int PACT_CODE_4_BULB_V2 = 1;

    /**
     * 协议类型-ble-rgb-支持alexa
     * <p>
     * goodsType={@link #GOODS_TYPE_VALUE_BLE_ALEXA_RGB}
     * </p>
     */
    public static final int PACT_TYPE_4_BLE_RGB_ALEXA_V1 = 10;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_BLE_RGB_ALEXA_V1}
     * <p/>
     */
    public static final int PACT_CODE_4_BLE_RGB_ALEXA_V1 = 2;
    /**
     * 协议类型-ble-rgbic-支持设备校验
     * <p>
     * goodsType={@link #GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET}
     * </p>
     */
    public static final int PACT_TYPE_4_RGBIC_BLE_SECRET = 10;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_RGBIC_BLE_SECRET}
     * <p/>
     */
    public static final int PACT_CODE_4_RGBIC_BLE_SECRET = 1;

    /**
     * 协议类型-ble-rgbic-支持设备校验
     * <p>
     * goodsType={@link #GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET}
     * </p>
     */
    public static final int PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET = 2;
    public static final int PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET_V3 = 3;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_RGBIC_BLE_WIFI_SECRET}
     * <p/>
     */
    public static final int PACT_CODE_4_RGBIC_BLE_WIFI_SECRET = 1;
    public static final int PACT_CODE_4_RGBIC_BLE_WIFI_SECRET_FRK = 2;


    /**
     * 协议类型-ble+wifi- WW 圆盘向上落地灯
     */
    public static final int PACT_TYPE_4_UPGLOW_FLOOR_LAMP_WW_BLE_WIFI_V1 = 1;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_UPGLOW_FLOOR_LAMP_WW_BLE_WIFI_V1}
     * </p>
     */
    public static final int PACT_CODE_4_UPGLOW_FLOOR_LAMP_WW_BLE_WIFI_V1 = 1;


    /**
     * 协议类型-ble+wifi- RgbIcW 立式直头落地灯
     */
    public static final int PACT_TYPE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2 = 1;
    public static final int PACT_TYPE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V3 = 2;
    /**
     * 协议code
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2}
     * </p>
     */
    public static final int PACT_CODE_4_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2 = 1;

    /**
     * 协议类型-ble+wifi的空气净化器
     * <p>
     */
    public static final int PACT_TYPE_4_AIR_CLEANER_V1_1 = 1;
    public static final int PACT_TYPE_4_AIR_CLEANER_V1_2 = 2;

    public static final int PACT_CODE_4_AIR_CLEANER_V1_1 = 1;
    public static final int PACT_CODE_4_AIR_CLEANER_V1_2 = 2;


    /**
     * 协议类型-ble+wifi的空气净化器
     * <p> 7126
     */
    public static final int PACT_TYPE_7126_AIR_CLEANER_V1_1 = 1;
    public static final int PACT_TYPE_7126_AIR_CLEANER_V1_2 = 2;

    public static final int PACT_CODE_7126_AIR_CLEANER_V1_1 = 1;
    public static final int PACT_CODE_7126_AIR_CLEANER_V1_2 = 2;

    /**
     * 协议类型-泛光灯
     * <p>
     */
    public static final int PACT_TYPE_4_FLOOD_LIGHT_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_FLOOD_LIGHT_V1}
     */
    public static final int PACT_CODE_4_FLOOD_LIGHT_V1 = 1;

    /**
     * 协议类型-酷壁灯
     * <p>
     */
    public static final int PACT_TYPE_4_CRUEL_WALL_LIGHT_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_CRUEL_WALL_LIGHT_V1}
     */
    public static final int PACT_CODE_4_CRUEL_WALL_LIGHT_V1 = 1;

    /**
     * 协议类型-tv灯+RGBIC灯带
     * <p>
     */
    public static final int PACT_TYPE_4_TV_RGBIC_LIGHT_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_TV_RGBIC_LIGHT_V1}
     */
    public static final int PACT_CODE_4_TV_RGBIC_LIGHT_V1 = 1;

    /**
     * 协议类型-支持ble+wifi-RGBIC条形灯 H610A
     * <p>
     */
    public static final int PACT_TYPE_4_SPLICE_STRIP_LIGHT_V2_1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_SPLICE_STRIP_LIGHT_V2_1}
     */
    public static final int PACT_CODE_4_SPLICE_STRIP_LIGHT_V2_1 = 1;

    /**
     * 协议类型-支持ble+wifi-RGB条形灯 H6059
     * <p>
     */
    public static final int PACT_TYPE_4_RGB_BLE_WIFI_CHILDREN_LAMP_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_RGB_BLE_WIFI_CHILDREN_LAMP_V1}
     */
    public static final int PACT_CODE_4_RGB_BLE_WIFI_CHILDREN_LAMP_V1 = 1;

    /**
     * 协议code -支持 蓝牙/iot 的四探针有屏烤肉计
     * <p>
     */
    public static final int PACT_CODE_BBQ_BLE_IOT_V1 = 1;

    /**
     * 协议类型-ble -支持 蓝牙/iot 的四探针有屏烤肉计
     * <p>
     */
    public static final int PACT_TYPE_BBQ_BLE_IOT_V1 = 1;

    /**
     * 协议code -支持 蓝牙/iot 的台式空气循环扇
     * <p>
     */
    public static final int PACT_CODE_CIRCULATION_FAN_BLE_IOT_V1 = 1;

    /**
     * 协议类型-ble -支持 蓝牙/iot 的台式空气循环扇
     * <p>
     */
    public static final int PACT_TYPE_CIRCULATION_FAN_BLE_IOT_V1 = 1;
    public static final int PACT_TYPE_CIRCULATION_FAN_BLE_IOT_V2 = 2;

    /**
     * 协议code -支持 蓝牙/iot 的塔扇
     * <p>
     */
    public static final int PACT_CODE_TOWER_FAN_BLE_IOT_V1 = 1;

    /**
     * 协议类型-ble -支持 蓝牙/iot 的塔扇
     * <p>
     */
    public static final int PACT_TYPE_TOWER_FAN_BLE_IOT_V1 = 1;
    public static final int PACT_TYPE_TOWER_FAN_BLE_IOT_V2 = 2;

    /**
     * 协议类型-外置蓝牙拾音盒 H1162
     * <p>
     */
    public static final int PACT_TYPE_4_PICKUP_BOX2_V1 = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_PICKUP_BOX2_V1}
     */
    public static final int PACT_CODE_4_PICKUP_BOX2_V1 = 1;

    /**
     * 协议类型-泛光灯 H7061
     * <p>
     */
    public static final int PACT_TYPE_4_BLE_WIFI_FLOOD_LIGHT = 1;

    /**
     * 协议code
     * <p>
     * 支持的协议类型是{@link #PACT_TYPE_4_BLE_WIFI_FLOOD_LIGHT}
     */
    public static final int PACT_CODE_4_BLE_WIFI_FLOOD_LIGHT = 1;

    /**
     * 协议类型-ble+wifi-除湿机H7150
     */
    public static final int PACT_TYPE_DEHUMIDIFIER_V1 = 1;
    /**
     * 协议code- 除湿机
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_CUBE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_DEHUMIDIFIER_V1 = 1;

    /**
     * 协议类型-ble+wifi-游戏盛宴604a
     */
    public static final int PACT_TYPE_GAME_FEAST = 1;
    /**
     * 协议code- 除湿机
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_GAME_FEAST}
     * </p>
     */
    public static final int PACT_CODE_GAME_FEAST = 1;

    /**
     * 协议类型-ble+wifi-香薰加湿机H7160
     */
    public static final int PACT_TYPE_HUMIDIFIER_V3 = 1;
    public static final int PACT_TYPE_HUMIDIFIER_V3_2 = 2;
    /**
     * 协议code- 除湿机
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_CUBE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_HUMIDIFIER_V3 = 1;
    public static final int PACT_CODE_HUMIDIFIER_V3_V2 = 2;


    /**
     * 协议类型-ble+wifi-香薰加湿机H7161 / H7162
     */
    public static final int PACT_TYPE_HUMIDIFIER_V5 = 1;
    public static final int PACT_TYPE_HUMIDIFIER_V5_2 = 2;
    /**
     * 协议类型-ble+wifi-电饭煲H7180
     */
    public static final int PACT_TYPE_7180 = 1;

    /**
     * 协议类型-ble+wifi-鹅颈壶
     */
    public static final int PACT_TYPE_7175 = 1;
    /**
     * 协议类型-ble+wifi-鹅颈壶
     */
    public static final int PACT_TYPE_7173 = 1;
    /**
     * 协议code- 香薰加湿机H7161
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_CUBE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_HUMIDIFIER_V5 = 1;
    public static final int PACT_CODE_HUMIDIFIER_V5_2 = 2;
    /**
     * 协议code- 电饭煲H7180
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_CUBE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_7180 = 1;
    /**
     * 协议code- 7175鹅颈壶
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_CUBE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_7175 = 1;
    /**
     * 协议code- 7173鹅颈壶
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_4_CUBE_LIGHT_V1}
     * </p>
     */
    public static final int PACT_CODE_7173 = 1;

    /**
     * 协议type -支持 蓝牙/iot 的鹅颈壶
     * <p>
     */
    public static final int PACT_CODE_GOOSE_NECK_POT_V1 = 1;

    /**
     * 协议类型-ble -支持 蓝牙/iot 的鹅颈壶
     * <p>
     */
    public static final int PACT_TYPE_GOOSE_NECK_POT_V1 = 1;

    /**
     * 协议类型-ble+wifi-RGBW户外球泡串 7041 7042
     */
    public static final int PACT_TYPE_OUTDOOR_LIGHT_BULB = 1;


    /**
     * 协议code- ble+wifi-RGBW户外球泡串 7041 7042
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_OUTDOOR_LIGHT_BULB}
     * </p>
     */
    public static final int PACT_CODE_OUTDOOR_LIGHT_BULB = 2;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+wifi的有屏温湿度计 H5103
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_TH_BLE_WIFI_V2 = 106;
    public static final int PACT_TYPE_4_TH_BLE_WIFI_V2 = 1;
    public static final int PACT_CODE_4_TH_BLE_WIFI_V2 = 1;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+wifi的有屏温湿度计带pm2.5显示 H5106
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_THP_BLE_WIFI = 124;
    public static final int PACT_TYPE_4_THP_BLE_WIFI = 1;
    public static final int PACT_CODE_4_THP_BLE_WIFI = 1;

    /**
     * 产品类型
     * <p>
     * 支持蓝牙+wifi的有屏温湿度计带pm2.5显示 H5106
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_H5140 = 319;
    public static final int PACT_TYPE_VALUE_H5140 = 1;
    public static final int PACT_TYPE_VALUE_H5140_2 = 2;
    public static final int PACT_CODE_VALUE_H5140 = 1;
    public static final int PACT_CODE_VALUE_H5140_2 = 2;

    /**
     * 产品类型-->人感sensor(H5121)
     */
    public static final int GOODS_TYPE_VALUE_HUMAN_SENSOR = 130;
    public static final int PACT_TYPE_4_COMMON_SENSOR = 1;
    public static final int PACT_CODE_4_COMMON_SENSOR = 1;
    /**
     * 产品类型-->按键sensor(H5122)
     */
    public static final int GOODS_TYPE_VALUE_KEY_SENSOR = 131;
    /**
     * 产品类型-->门磁sensor(H5123)
     */
    public static final int GOODS_TYPE_VALUE_DOOR_SENSOR = 132;
    /**
     * 产品类型-->动静贴(H5124)
     */
    public static final int GOODS_TYPE_VALUE_GOING_TO_STICK = 139;

    /**
     * 协议类型-ble+wifi-游戏盛宴两件套 604b/604d
     */
    public static final int PACT_TYPE_GAME_FEAST_V2 = 1;
    /**
     * 协议code 604b/604d
     * <p>
     * 支持的协议类型{@link #PACT_TYPE_GAME_FEAST_V2}
     * </p>
     */
    public static final int PACT_CODE_GAME_FEAST_V2 = 1;


    /**
     * 产品类型
     * <p>
     * 支持 蓝牙/iot 的 7100 42英寸塔扇
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_TOWER_FAN_BLE_IOT = 129;
    /**
     * 协议类型-ble+wifi-7100 42英寸塔扇
     */
    public static final int PACT_TYPE_VALUE_TOWER_V1 = 1;
    /**
     * 协议code 7100 42英寸塔扇
     * <p>
     * 支持的协议类型{@link #PACT_CODE_VALUE_TOWER_V1}
     * </p>
     */
    public static final int PACT_CODE_VALUE_TOWER_V1 = 1;
    /**
     * 协议类型-HDMI灯带 - H6602
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT = 142;
    public static final int PACT_TYPE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT = 1;
    public static final int PACT_CODE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT = 1;

    /**
     * 产品类型--> ble-RGBIC 车载灯带(H6192)
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_BLE_CAR_LIGHT = 143;
    public static final int PACT_TYPE_VALUE_BLE_RGBIC_CAR_LIGHT = 1;
    public static final int PACT_CODE_VALUE_BLE_RGBIC_CAR_LIGHT = 1;

    /**
     * 产品类型-->六按键遥控器sensor(H5125)
     */
    public static final int GOODS_TYPE_VALUE_SIX_BUTTON_SENSOR = 144;
    /**
     * 产品类型-->双按键sensor(H5126)
     */
    public static final int GOODS_TYPE_VALUE_TWO_BUTTON_SENSOR = 145;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-除湿机
     * 7151
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_DEHUMIDIFIER_V2 = 147;
    public static final int PACT_TYPE_DEHUMIDIFICATION_V1 = 1;
    public static final int PACT_TYPE_DEHUMIDIFICATION_V2 = 2;
    public static final int PACT_CODE_DEHUMIDIFICATION_V1 = 1;
    public static final int PACT_CODE_DEHUMIDIFICATION_V2 = 2;


    /**
     * 产品类型 支持ble+wifi-除湿机(H7152)
     */
    public static final int GOODS_TYPE_VALUE_H7152 = 308;
    public static final int PACT_TYPE_H7152 = 1;
    public static final int PACT_CODE_H7152 = 1;

    /**
     * 产品类型 支持ble+wifi-除湿机(H7153)
     */
    public static final int GOODS_TYPE_VALUE_H7153 = 309;

    /**
     * 产品类型--> ble-暖白球泡灯串(H7019)
     */
    public static final int GOODS_TYPE_VALUE_BLE_BALL_LIGHT = 148;
    public static final int PACT_TYPE_VALUE_BLE_BALL_LIGHT = 1;
    public static final int PACT_CODE_VALUE_BLE_BALL_LIGHT = 1;

    /**
     * 产品类型--> ble-RGBWW 球泡灯串(H7029)
     */
    public static final int GOODS_TYPE_VALUE_BLE_RGBWW_BALL_LIGHT = 149;
    public static final int PACT_TYPE_VALUE_BLE_RGBWW_BALL_LIGHT = 1;
    public static final int PACT_CODE_VALUE_BLE_RGBWW_BALL_LIGHT = 1;

    /**
     * 产品类型
     * <p>
     * 三合一婴儿加湿器
     * 7140
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HUMIDIFIER_V6 = 151;
    public static final int PACT_TYPE_HUMIDIFIER_V6 = 1;
    public static final int PACT_TYPE_HUMIDIFIER_V6_V2 = 2;
    public static final int PACT_CODE_HUMIDIFIER_V6 = 1;
    public static final int PACT_CODE_HUMIDIFIER_V6_V2 = 2;

    /**
     * 产品类型
     * <p>
     * 三合一婴儿加湿器
     * 714E
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_H714E = 259;
    public static final int PACT_TYPE_VALUE_H714E = 1;
    public static final int PACT_TYPE_VALUE_H714E_2 = 2;
    public static final int PACT_CODE_VALUE_H714E = 1;
    public static final int PACT_CODE_VALUE_H714E_2 = 2;

    /**
     * 产品类型
     * <p>
     * 暖风机
     * 7135
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HEATER_7135 = 153;
    public static final int PACT_TYPE_HEATER_7135 = 1;
    public static final int PACT_CODE_HEATER_7135 = 1;
    public static final int PACT_CODE_HEATER_7135_V2 = 2;
    /**
     * 蓝牙通信加密+固件安全升级
     */
    public static final int PACT_TYPE_HEATER_7135_V2 = 2;

    /**
     * 蓝牙温湿度计H5104(功能同H5075)
     */
    public static final int GOODS_TYPE_VALUE_4_TH_H5104 = 154;
    public static final int PACT_TYPE_H5104 = 1;
    public static final int PACT_CODE_H5104 = 1;
    /**
     * 双探针无线烤肉机H5199
     */
    public static final int GOODS_TYPE_VALUE_4_MEAT_TH_H5199 = 155;
    public static final int PACT_TYPE_H5199 = 1;
    public static final int PACT_CODE_H5199 = 1;
    /**
     * 双探针无线烤肉机H5196
     */
    public static final int GOODS_TYPE_VALUE_4_MEAT_TH_H5196 = 169;
    public static final int PACT_TYPE_H5196 = 1;
    public static final int PACT_CODE_H5196 = 1;
    /**
     * H5191
     */
    public static final int GOODS_TYPE_VALUE_4_MEAT_TH_H5191 = 281;
    /**
     * H5192
     */
    public static final int GOODS_TYPE_VALUE_4_MEAT_TH_H5192 = 282;

    /**
     * 产品类型
     * <p>
     * Wi-Fi + BLE RGBICWW 落地灯
     * H6079
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HEATER_h6079 = 156;
    public static final int PACT_TYPE_HEATER_h6079 = 1;
    public static final int PACT_CODE_HEATER_h6079 = 1;

    /**
     * 产品类型
     * <p>
     * 网关H5043
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_GATEWAY_H5043 = 158;
    public static final int PACT_TYPE_GATEWAY_H5043 = 1;
    public static final int PACT_TYPE_GATEWAY_H5043_V1 = 2;//蓝牙加密的PactType
    public static final int PACT_CODE_GATEWAY_H5043 = 1;
    /**
     * 产品类型
     * <p>
     * 网关H5043的子设备->H5058
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_GW_SUB_H5058 = 159;

    /**
     * 产品类型
     * <p>
     * 网关H5043的子设备->H5107
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_GW_SUB_H5107 = 220;

    /**
     * Wi-Fi + BLE RGBICW 户外球泡灯串 H7033
     */
    public static final int GOODS_TYPE_VALUE_OUTDOOR_h7033 = 161;
    public static final int PACT_TYPE_OUTDOOR_h7033 = 1;
    public static final int PACT_CODE_OUTDOOR_h7033 = 1;
    public static final int PACT_CODE_OUTDOOR_h7033_NEW = 2;

    /**
     * BLE RGBIC 车载底盘灯带 H7092
     */
    public static final int GOODS_TYPE_VALUE_CAR_LIGHT_h7092 = 162;
    public static final int PACT_TYPE_CAR_LIGHT_h7092 = 1;
    public static final int PACT_CODE_CAR_LIGHT_h7092 = 1;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-六边形方块灯，立体
     * 606A
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_CUBE_LIGHT_606A = 163;

    public static final int PACT_TYPE_4_CUBE_LIGHT_606A = 1;
    public static final int PACT_CODE_4_CUBE_LIGHT_606A = 1;
    public static final int PACT_TYPE_4_CUBE_LIGHT_606A_ENCRYPT = 2;
    public static final int PACT_CODE_4_CUBE_LIGHT_606A_ENCRYPT = 1;

    /**
     * 713A取暖器
     */
    public static final int GOODS_TYPE_VALUE_h713A = 164;
    public static final int PACT_TYPE_h713A = 1;
    public static final int PACT_CODE_h713A = 1;

    /**
     * 713B取暖器
     */
    public static final int GOODS_TYPE_VALUE_h713B = 165;
    public static final int PACT_TYPE_h713B = 1;
    public static final int PACT_CODE_h713B = 1;

    /**
     * 713C取暖器
     */
    public static final int GOODS_TYPE_VALUE_h713C = 166;
    public static final int PACT_TYPE_h713C = 1;
    public static final int PACT_CODE_h713C = 1;

    /**
     * h6092星空灯
     */
    public static final int GOODS_TYPE_VALUE_h6092 = 167;
    public static final int PACT_TYPE_h6092 = 1;
    public static final int PACT_TYPE_h6092_v2 = 2;
    public static final int PACT_CODE_h6092 = 1;
    public static final int PACT_CODE_h6092_v2 = 2;

    /**
     * 产品类型7145
     * <p>
     * 支持ble+wifi-加湿器
     * 7142
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HUMIDIFIER_170 = 170;


    /**
     * 产品类型
     * <p>
     * 暖风机
     * 7133
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HEATER_7133 = 174;
    public static final int PACT_TYPE_HEATER_7133 = 1;
    public static final int PACT_CODE_HEATER_7133 = 1;

    /**
     * 20段 RGBIC灯带-支持串联涂鸦-支持断电记忆-带渐变
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE = 175;
    public static final int PACT_TYPE_RGBIC_LIGHT_H61BE_ENCRYPT = 2;
    public static final int PACT_TYPE_RGBIC_LIGHT_H61BE = 1;
    public static final int PACT_CODE_RGBIC_LIGHT_H61BE = 1;
    /**
     * 15段 RGBIC灯带-支持断电记忆-带渐变
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC = 176;
    public static final int PACT_TYPE_RGBIC_LIGHT_H61BC = 1;
    public static final int PACT_TYPE_RGBIC_LIGHT_H61BC_ENCRYPT = 2;
    public static final int PACT_CODE_RGBIC_LIGHT_H61BC = 1;
    /**
     * 15段 RGBIC灯带-支持断电记忆-无渐变
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA = 178;
    public static final int PACT_TYPE_RGBIC_LIGHT_H61BA = 1;
    public static final int PACT_TYPE_RGBIC_LIGHT_H61BA_ENCRYPT = 2;
    public static final int PACT_CODE_RGBIC_LIGHT_H61BA = 1;


    /**
     * 产品类型
     * <p>
     * ble+wifi rgbic pc灯带 H6609
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_PC_LIGHT = 173;
    public static final int PACT_TYPE_RGBIC_BLE_WIFI_RGBIC_PC_LIGHT = 1;
    public static final int PACT_CODE_RGBIC_BLE_WIFI_RGBIC_PC_LIGHT = 1;
    public static final int PACT_TYPE_RGBIC_BLE_WIFI_RGBIC_PC_LIGHT_ENCRYPT = 2;
    public static final int PACT_CODE_RGBIC_BLE_WIFI_RGBIC_PC_LIGHT_ENCRYPT = 1;

    /**
     * 产品类型
     * <p>
     * 6608
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_H6608 = 172;
    public static final int PACT_TYPE_H6608 = 1;
    public static final int PACT_CODE_H6608 = 1;

    /**
     * 产品类型
     * <p>
     * ble + wifi RGBICW 户外屋檐灯 H706A/H706B/H706C
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_H706A_B_C = 180;
    public static final int PACT_TYPE_H706A_B_C = 1;
    //pactType迭代时请注意分灯场景的版本判断
    public static final int PACT_CODE_H706A_B_C = 1;
    public static final int PACT_CODE_H706A_B_C_V2 = 2;//迭代了分灯场景
    public static final int PACT_TYPE_H706A_B_C_V2 = 2;//迭代蓝牙加密

    /**
     * 产品类型
     * <p>
     * ble+wifi 圣诞灯串 H70C1/H70C2 - 10段 FRK+Ai
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_H70C1_H70C2 = 181;
    public static final int PACT_TYPE_BLE_WIFI_H70C1_H70C2 = 1;
    public static final int PACT_TYPE_BLE_WIFI_H70C1_H70C2_V2 = 2;
    public static final int PACT_CODE_BLE_WIFI_H70C1_H70C2 = 1;
    /**
     * 产品类型
     * <p>
     * ble+wifi 微波雷达存在传感器 H5127
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_H5127 = 183;
    public static final int PACT_TYPE_H5127 = 1;
    public static final int PACT_CODE_H5127 = 1;

    /**
     * 产品类型
     * <p>
     * ble+wifi 微波雷达存在传感器 H7134
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_H7134 = 179;

    /**
     * 产品类型
     * <p>
     * ble+wifi 户外灯带 H6175 - 10段 FRK
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_H6175 = 187;
    public static final int PACT_TYPE_H6175 = 1;
    public static final int PACT_CODE_H6175 = 1;

    /**
     * 产品类型
     * <p>
     * ble+wifi rgbic智能吸顶灯 H60A0
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_H60A0 = 188;
    public static final int PACT_TYPE_BLE_WIFI_H60A0 = 1;
    public static final int PACT_CODE_BLE_WIFI_H60A0 = 1;

    /**
     * 产品类型
     * <p>
     * ble 墨水屏温湿度计
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_TH_BLE_H5105 = 190;
    public static final int PACT_TYPE_H5105 = 1;
    public static final int PACT_CODE_H5105 = 1;

    /**
     * 产品类型
     * <p>
     * Wi-Fi+BLE RGBIC摄像头+电视灯带 H6099
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6099 = 191;
    public static final int PACT_TYPE_H6099 = 1;
    public static final int PACT_CODE_H6099 = 1;
    public static final int PACT_TYPE_H6099_ENCRYPT = 2;
    public static final int PACT_CODE_H6099_ENCRYPT = 1;

    /**
     * 产品类型
     * <p>
     * Wi-Fi+ble AI 取色 三件套 H6603
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_H6603 = 192;
    public static final int PACT_TYPE_H6603 = 1;
    public static final int PACT_CODE_H6603 = 1;
    /**
     * 蓝牙加密
     */
    public static final int PACT_TYPE_H6603_2 = 2;
    public static final int PACT_CODE_H6603_2 = 1;

    /**
     * 盛宴支持像素屏作为子设备
     */
    public static final int PACT_TYPE_H6603_3 = 3;
    public static final int PACT_CODE_H6603_3 = 1;

    public static final int GOODS_TYPE_VALUE_H70A1_A2_A3 = 193;
    public static final int PACT_TYPE_H70A1_A2_A3 = 1;
    public static final int PACT_CODE_H70A1_A2_A3 = 1;
    public static final int PACT_TYPE_H70A1_A2_A3_ENCRYPT = 3; //嵌入式那边直接从1加到了3，原因未知
    public static final int PACT_CODE_H70A1_A2_A3_ENCRYPT = 1;

    /**
     * 冰箱温湿度计(H5108)
     * ble
     */
    public static final int GOODS_TYPE_VALUE_4_TH_H5108 = 194;
    public static final int PACT_TYPE_H5108 = 1;
    public static final int PACT_CODE_H5108 = 1;
    /**
     * 智能插座
     * WiFi + ble
     */
    public static final int GOODS_TYPE_VALUE_SMART_PLUG_PRO_H5086 = 195;
    public static final int PACT_TYPE_H5086 = 1;
    public static final int PACT_CODE_H5086 = 1;
    /**
     * 泳池温度计网关->H5042
     * WiFi + ble
     */
    public static final int GOODS_TYPE_VALUE_4_GATEWAY_H5042 = 198;
    public static final int PACT_TYPE_H5042 = 1;
    public static final int PACT_TYPE_H5042_V1 = 2;
    public static final int PACT_CODE_H5042 = 1;
    /**
     * 泳池温度计->H5109
     * ble
     */
    public static final int GOODS_TYPE_VALUE_4_H5109 = 199;
    public static final int PACT_TYPE_H5109 = 1;
    public static final int PACT_CODE_H5109 = 1;

    /**
     * 塔扇->H7106
     * ble
     */
    public static final int GOODS_TYPE_VALUE_4_H7106 = 200;
    public static final int PACT_TYPE_H7106 = 1;
    public static final int PACT_TYPE_H7106_2 = 2;
    public static final int PACT_CODE_H7106 = 1;


    /**
     * 产品类型
     * <p>
     * 支持 蓝牙/iot 的 7105
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_4_H7105 = 201;
    public static final int PACT_TYPE_H7105 = 1;
    public static final int PACT_TYPE_H7105_2 = 2;
    public static final int PACT_CODE_H7105 = 1;


    /**
     * 产品类型
     * <p>
     * 支持 蓝牙/iot 的 7107
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_4_H7107 = 297;
    public static final int PACT_TYPE_H7107 = 1;
    public static final int PACT_TYPE_H7107_2 = 2;
    public static final int PACT_CODE_H7107 = 1;


    /**
     * 产品类型
     * <p>
     * 支持 蓝牙/iot 的 7103
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_4_H7103 = 251;
    public static final int PACT_TYPE_H7103 = 1;
    public static final int PACT_CODE_H7103 = 1;

    /**
     * 霓虹灯 H61D3 H61D5
     * WiFi + ble
     */
    public static final int GOODS_TYPE_VALUE_H61D3_D5 = 202;
    public static final int PACT_TYPE_H61D3_D5 = 1;
    public static final int PACT_CODE_H61D3_D5 = 1;
    /**
     * ble+wifi 户外霓虹灯带 H61A9
     */
    public static final int GOODS_TYPE_VALUE_H61A9 = 204;
    public static final int PACT_TYPE_H61A9 = 1;
    public static final int PACT_CODE_H61A9 = 1;

    /**
     * 产品类型
     * <p>
     * Wi-Fi+BLE RGBIC摄像头+电视灯带 H6098
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098 = 203;
    public static final int PACT_TYPE_H6098 = 1;
    public static final int PACT_CODE_H6098 = 1;
    /**
     * H6098支持蓝牙加密
     */
    public static final int PACT_TYPE_H6098_2 = 2;
    public static final int PACT_CODE_H6098_2 = 1;
    /**
     * H8098支持蓝牙加密
     */
    public static final int PACT_TYPE_H8098 = 3;
    public static final int PACT_CODE_H8098 = 1;

    /**
     * 产品类型
     * <p>
     * 214-ble+wifi 取暖器（美规） H7136
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HEATER_H7136 = 214;
    public static final int PACT_TYPE_H7136 = 1;
    public static final int PACT_CODE_H7136 = 1;
    /**
     * 产品类型
     * <p>
     * 215-ble+wifi 取暖器（欧英日澳规） H713D
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HEATER_H713D = 215;
    public static final int PACT_TYPE_H713D = 1;
    public static final int PACT_CODE_H713D = 1;

    /**
     * BLE + WiFi
     * RGBIC 户外智能壁灯 H7075
     */
    public static final int GOODS_TYPE_VALUE_WALL_LAMP_H7075 = 205;
    //diy 分区最大支持4个分区
    public static final int PACT_TYPE_H7075 = 1;
    //diy 分区最大支持5个分区
    public static final int PACT_TYPE_H7075_V1 = 2;
    public static final int PACT_CODE_H7075 = 1;
    //diy 分区最大支持5个分区
    public static final int PACT_TYPE_H7075_V2 = 3;
    public static final int PACT_CODE_H7075_2 = 1;

    /**
     * 产品类型
     * <p>
     * 人感sensor H5129
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_HUMAN_SENSOR_V2 = 206;

    /**
     * 产品类型
     * <p>
     * H616c/d/e
     * </p>
     */
    public static final int GOODS_TYPE_VALUE_H616C = 207;
    public static final int PACT_TYPE_H616C = 1;
    public static final int PACT_CODE_H616C = 1;
    //蓝牙加密版本
    public static final int PACT_TYPE_H616C_ENCRYPT = 2;

    /**
     * 产品类型
     * <p>
     * 支持ble+wifi-RGBIC WW直头落地灯 H607C
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C = 209;
    public static final int PACT_TYPE_VALUE_4_RGBIC_WW_FLOOR_LAMP_H607C = 1;
    public static final int PACT_CODE_VALUE_4_RGBIC_WW_FLOOR_LAMP_H607C = 1;


    /**
     * 产品类型
     * H6020 - RGBICWW 高端床头灯
     */
    public static final int GOODS_TYPE_VALUE_H6020 = 210;
    public static final int PACT_TYPE_VALUE_H6020 = 1;
    public static final int PACT_CODE_VALUE_H6020 = 1;

    /**
     * H7146 ble + wifi 6L冷雾加湿器
     */
    public static final int GOODS_TYPE_VALUE_H7146 = 217;
    public static final int PACT_TYPE_VALUE_H7146 = 1;
    public static final int PACT_CODE_VALUE_H7146 = 1;

    /**
     * 产品类型
     * H70BC -  70b1同款线下款窗帘灯 ble+wifi
     */
    public static final int GOODS_TYPE_VALUE_H70BC = 216;
    public static final int PACT_TYPE_VALUE_H70BC = 1;
    public static final int PACT_CODE_VALUE_H70BC = 1;
    public static final int PACT_TYPE_VALUE_H70BC_2 = 2;

    /**
     * BLE + WiFi
     * RGBICW 户外埋地灯 H7052
     */
    public static final int GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7052 = 218;
    public static final int PACT_TYPE_H7052 = 1;
    public static final int PACT_TYPE_H7052_V2 = 2;
    public static final int PACT_CODE_H7052 = 1;
    /**
     * BLE + WiFi
     * RGBICW 户外埋地灯 H7053
     */
    public static final int GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7053 = 219;
    public static final int PACT_TYPE_H7053 = 1;
    public static final int PACT_TYPE_H7053_V2 = 2;
    public static final int PACT_CODE_H7053 = 1;

    /**
     * 产品类型
     * H6032 - 调光开关
     */
    public static final int GOODS_TYPE_VALUE_H6032 = 213;
    public static final int PACT_TYPE_VALUE_H6032 = 1;
    public static final int PACT_CODE_VALUE_H6032 = 1;

    /**
     * BLE + WiFi
     * RGBICW 球泡灯串 H7037/H7038/H7039
     */
    public static final int GOODS_TYPE_VALUE_H7037 = 221;
    public static final int PACT_TYPE_VALUE_H7037 = 1;
    public static final int PACT_CODE_VALUE_H7037 = 1;


    /**
     * ble + wifi
     * H7137 仰角暖风机
     */
    public static final int GOODS_TYPE_VALUE_H7137 = 222;
    public static final int PACT_TYPE_H7137 = 1;
    public static final int PACT_CODE_H7137 = 1;

    /**
     * ble + wifi
     * H713E 仰角暖风机
     */
    public static final int GOODS_TYPE_VALUE_H713E = 237;
    public static final int PACT_TYPE_H713E = 1;
    public static final int PACT_CODE_H713E = 1;

    /**
     * ble + wifi
     * H7138 智能暖风机
     */
    public static final int GOODS_TYPE_VALUE_H7138 = 252;
    public static final int PACT_TYPE_H7138 = 1;
    public static final int PACT_CODE_H7138 = 1;

    /**
     * (自研) 135CFN变形空气净化器（不带sensor） H712c
     */
    public static final int GOODS_TYPE_VALUE_H712C = 223;
    /**
     * (自研) 135CFN变形空气净化器（带sensor） H7124
     */
    public static final int GOODS_TYPE_VALUE_H7124 = 224;
    /**
     * (自研) 圆形空气净化器，sensor+matter+灯效 H7128
     */
    public static final int GOODS_TYPE_VALUE_H7128 = 225;
    /**
     * -（自研）空气净化器+matter H7129
     */
    public static final int GOODS_TYPE_VALUE_H7129 = 226;

    /**
     * 7063
     */
    public static final int GOODS_TYPE_VALUE_H7063 = 227;
    public static final int PACT_TYPE_H7063 = 1;
    public static final int PACT_CODE_H7063 = 1;

    /**
     * 6063 - WIFI+BLE RGBIC 风状游戏方块灯
     */
    public static final int GOODS_TYPE_VALUE_H6063 = 228;
    public static final int PACT_TYPE_H6063 = 1;
    public static final int PACT_CODE_H6063 = 1;
    public static final int PACT_TYPE_H6063_ENCRYPT = 2;
    public static final int PACT_CODE_H6063_ENCRYPT = 1;

    /**
     * 产品类型
     * H70B3 - 窗帘灯
     */
    public static final int GOODS_TYPE_VALUE_H70B3 = 229;
    public static final int PACT_TYPE_VALUE_H70B3 = 1;
    public static final int PACT_TYPE_VALUE_H70B3_2 = 2;//蓝牙加密
    public static final int PACT_CODE_VALUE_H70B3 = 1;
    public static final int PACT_CODE_VALUE_H70B3_2 = 2;

    /**
     * 产品类型
     * H70B4 - 窗帘灯
     */
    public static final int GOODS_TYPE_VALUE_H70B4 = 230;
    public static final int PACT_TYPE_VALUE_H70B4 = 1;
    public static final int PACT_TYPE_VALUE_H70B4_2 = 2;//蓝牙加密
    public static final int PACT_CODE_VALUE_H70B4 = 1;
    public static final int PACT_CODE_VALUE_H70B4_2 = 2;

    /**
     * 产品类型
     * H70B5 - 窗帘灯
     */
    public static final int GOODS_TYPE_VALUE_H70B5 = 231;
    public static final int PACT_TYPE_VALUE_H70B5 = 1;
    public static final int PACT_TYPE_VALUE_H70B5_2 = 2;//蓝牙加密
    public static final int PACT_CODE_VALUE_H70B5 = 1;
    public static final int PACT_CODE_VALUE_H70B5_2 = 2;

    /**
     * H7147 ble + wifi 加湿器
     */
    public static final int GOODS_TYPE_VALUE_H7147 = 232;
    public static final int PACT_TYPE_VALUE_H7147 = 1;
    public static final int PACT_TYPE_VALUE_H7147_V2 = 2;
    public static final int PACT_CODE_VALUE_H7147 = 1;

    /**
     * H7148 ble + wifi 加湿器
     */
    public static final int GOODS_TYPE_VALUE_H7148 = 233;
    public static final int PACT_TYPE_VALUE_H7148 = 1;
    /**
     * 迭代type-蓝牙加密
     */
    public static final int PACT_TYPE_VALUE_H7148_V2 = 2;
    public static final int PACT_CODE_VALUE_H7148 = 1;

    /**
     * H7149 ble + wifi 加湿器
     */
    public static final int GOODS_TYPE_VALUE_H7149 = 258;
    public static final int PACT_TYPE_VALUE_H7149 = 1;
    public static final int PACT_TYPE_VALUE_H7149_V2 = 2;
    public static final int PACT_CODE_VALUE_H7149 = 1;

    /**
     * H8121 ble + wifi 制冰机
     */
    public static final int GOODS_TYPE_VALUE_H8121 = 306;
    /**
     * H8120 ble + wifi 制冰机
     */
    public static final int GOODS_TYPE_VALUE_H8120 = 317;


    public static final int PACT_TYPE_VALUE_ICE_MACHINE_1 = 1;
    public static final int PACT_CODE_VALUE_ICE_MACHINE_1 = 1;
    /**
     * H612x
     */
    public static final int GOODS_TYPE_VALUE_H612x = 234;
    public static final int PACT_TYPE_VALUE_H612x = 1;
    public static final int PACT_CODE_VALUE_H612x = 1;
    public static final int PACT_CODE_VALUE_H612x_2 = 2; //改了长度校准
    public static final int PACT_TYPE_VALUE_H612x_encryption = 2; //蓝牙加密
    public static final int PACT_CODE_VALUE_H612x_encryption = 1;
    /**
     * 产品类型
     * H60A1
     */
    public static final int GOODS_TYPE_VALUE_BLE_WIFI_H60A1 = 235;
    public static final int PACT_TYPE_BLE_WIFI_H60A1 = 1;
    public static final int PACT_TYPE_BLE_WIFI_H60A1_V2 = 2;
    public static final int PACT_CODE_BLE_WIFI_H60A1 = 1;

    /**
     * H6640 踢脚线霓虹灯带
     */
    public static final int GOODS_TYPE_VALUE_H6640 = 242;
    public static final int PACT_TYPE_VALUE_H6640 = 1;
    public static final int PACT_CODE_VALUE_H6640 = 1;
    public static final int PACT_TYPE_VALUE_H6640_ENCRYPT = 2;
    public static final int PACT_CODE_VALUE_H6640_ENCRYPT = 1;

    /**
     * H6641 踢脚线霓虹灯带
     */
    public static final int GOODS_TYPE_VALUE_H6641 = 247;
    public static final int PACT_TYPE_VALUE_H6641 = 1;
    public static final int PACT_CODE_VALUE_H6641 = 1;
    public static final int PACT_TYPE_VALUE_H6641_ENCRYPT = 2;
    public static final int PACT_CODE_VALUE_H6641_ENCRYPT = 1;

    /**
     * 按键+压力传感器-->H5130
     */
    public static final int GOODS_TYPE_4_H5130 = 238;

    /**
     * 产品类型
     * H705D/H705E/H705F  H805A/H805B/H805C
     */
    public static final int GOODS_TYPE_VALUE_H705DEF_H805ABC = 240;
    public static final int PACT_TYPE_VALUE_H705DEF_H805ABC = 1;
    /**
     * 蓝牙通信加密迭代
     */
    public static final int PACT_TYPE_VALUE_H805C_V2 = 2;
    //pactType迭代时请注意分灯场景的版本判断
    public static final int PACT_CODE_VALUE_H705DEF_H805ABC = 1;
    public static final int PACT_CODE_VALUE_H705DEF_H805ABC_v2 = 2;//迭代了分灯场景

    /**
     * 协议类型-HDMI灯带 - H6604
     */
    public static final int GOODS_TYPE_VALUE_RGBIC_AI_HDMI_H6604 = 243;
    public static final int PACT_TYPE_RGBIC_BLE_WIFI_AI_HDMI_H6604 = 1;
    public static final int PACT_CODE_RGBIC_BLE_WIFI_AI_HDMI_H6604 = 1;
    /**
     * 蓝牙加密
     */
    public static final int PACT_TYPE_RGBIC_BLE_WIFI_AI_HDMI_H6604_2 = 2;
    public static final int PACT_CODE_RGBIC_BLE_WIFI_AI_HDMI_H6604_2 = 1;
    /**
     * 盛宴支持像素屏作为子设备
     */
    public static final int PACT_TYPE_RGBIC_BLE_WIFI_AI_HDMI_H6604_3 = 3;
    public static final int PACT_CODE_RGBIC_BLE_WIFI_AI_HDMI_H6604_3 = 1;

    /**
     * H61E5 COB 蓝牙+Wi-Fi 霓虹套管高端灯带
     */
    public static final int GOODS_TYPE_VALUE_H61E5 = 239;
    public static final int PACT_TYPE_VALUE_H61E5 = 1;
    public static final int PACT_CODE_VALUE_H61E5 = 1;
    public static final int PACT_TYPE_VALUE_H61E5_ENCRYPT = 2;
    public static final int PACT_CODE_VALUE_H61E5_ENCRYPT = 1;


    /**
     * H61E6 COB 蓝牙+Wi-Fi 霓虹套管高端灯带
     */
    public static final int GOODS_TYPE_VALUE_H61E6 = 246;
    public static final int PACT_TYPE_VALUE_H61E6 = 1;
    public static final int PACT_CODE_VALUE_H61E6 = 1;
    public static final int PACT_TYPE_VALUE_H61E6_ENCRYPT = 2;
    public static final int PACT_CODE_VALUE_H61E6_ENCRYPT = 1;

    /**
     * BLE + WiFi
     * RGBIC 户外智能壁灯 H7078
     */
    public static final int GOODS_TYPE_VALUE_WALL_LAMP_H7078 = 245;
    public static final int PACT_TYPE_H7078 = 1;
    public static final int PACT_TYPE_H7078_V2 = 2;
    public static final int PACT_CODE_H7078 = 1;

    /**
     * BLE + WiFi
     * H7072 RGBIC+WW 户外路灯
     */
    public static final int GOODS_TYPE_VALUE_H7072 = 294;
    public static final int PACT_TYPE_H7072 = 1;
    public static final int PACT_CODE_H7072 = 1;

    /**
     * 产品类型
     * H6022 - RGBICWW 高端床头灯
     */
    public static final int GOODS_TYPE_VALUE_H6022 = 249;
    public static final int PACT_TYPE_VALUE_H6022 = 1;
    public static final int PACT_CODE_VALUE_H6022 = 1;
    /**
     * 蓝牙加密
     */
    public static final int PACT_TYPE_VALUE_H6022_2 = 2;
    public static final int PACT_CODE_VALUE_H6022_2 = 1;


    /**
     * H7067/68/69 户外栅栏灯
     */
    public static final int GOODS_TYPE_VALUE_H7067_68_69 = 250;
    public static final int PACT_TYPE_VALUE_H7067_68_69 = 1;
    public static final int PACT_CODE_VALUE_H7067_68_69 = 1;

    /**
     * H7067/68/69 户外栅栏灯
     */
    public static final int GOODS_TYPE_VALUE_H7127 = 254;
    public static final int PACT_TYPE_VALUE_H7127 = 1;
    public static final int PACT_TYPE_VALUE_H7127_2 = 2;
    public static final int PACT_CODE_VALUE_H7127 = 1;
    public static final int PACT_CODE_VALUE_H7127_2 = 2;

    /**
     * H7057 户外泛光灯
     */
    public static final int GOODS_TYPE_VALUE_H7057 = 274;
    public static final int PACT_TYPE_VALUE_H7057 = 1;
    public static final int PACT_CODE_VALUE_H7057 = 1;

    /**
     * H7058 户外泛光灯
     */
    public static final int GOODS_TYPE_VALUE_H7058 = 275;
    public static final int PACT_TYPE_VALUE_H7058 = 1;
    public static final int PACT_CODE_VALUE_H7058 = 1;


    //<editor-fold desc="H70D123光帘灯">
    /**
     * H70D1光帘灯
     */
    public static final int GOODS_TYPE_VALUE_H70D1 = 267;
    public static final int PACT_TYPE_VALUE_H70D1 = 1;
    public static final int PACT_TYPE_VALUE_H70D1_2 = 2;//支持加密版本
    public static final int PACT_CODE_VALUE_H70D1 = 1;

    /**
     * h70D2
     */
    public static final int GOODS_TYPE_VALUE_H70D2 = 268;
    public static final int PACT_TYPE_VALUE_H70D2 = 1;
    public static final int PACT_TYPE_VALUE_H70D2_2 = 2;//支持蓝牙加密版本
    public static final int PACT_CODE_VALUE_H70D2 = 1;

    /**
     * h70D3
     */
    public static final int GOODS_TYPE_VALUE_H70D3 = 269;
    public static final int PACT_TYPE_VALUE_H70D3 = 1;
    public static final int PACT_TYPE_VALUE_H70D3_2 = 2;//支持蓝牙加密版本
    public static final int PACT_CODE_VALUE_H70D3 = 1;

    //</editor-fold>

    /**
     * H605a
     * 253-灯柱+摄像头+灯带
     */
    public static final int GOODS_TYPE_VALUE_H605A = 253;
    public static final int PACT_TYPE_VALUE_H605a = 1;
    public static final int PACT_CODE_VALUE_H605a = 1;
    public static final int PACT_TYPE_H605a_ENCRYPT = 2;
    public static final int PACT_CODE_H605a_ENCRYPT = 1;

    /**
     * H70c4/5/6/7/8/9 圣诞灯串
     */
    public static final int GOODS_TYPE_VALUE_H70C4_5_6_7_8_9 = 260;
    public static final int PACT_TYPE_VALUE_H70C4_5_6_7_8_9 = 1;
    public static final int PACT_TYPE_VALUE_H70C4_5_6_7_8_9_v2 = 2;
    public static final int PACT_CODE_VALUE_H70C4_5_6_7_8_9 = 1;

    /**
     * H61F6 滴胶灯带
     */
    public static final int GOODS_TYPE_VALUE_H61F6 = 261;
    public static final int PACT_TYPE_VALUE_H61F6 = 2;
    public static final int PACT_CODE_VALUE_H61F6 = 2;
    /**
     * 蓝牙加密
     */
    public static final int PACT_TYPE_VALUE_H61F6_3 = 3;
    public static final int PACT_CODE_VALUE_H61F6_3 = 1;

    /**
     * 滴胶TV灯带
     * H6169
     * ble + wifi
     */
    public static final int GOODS_TYPE_VALUE_H6169 = 262;
    public static final int PACT_TYPE_VALUE_H6169 = 2;
    public static final int PACT_CODE_VALUE_H6169 = 1;

    /**
     * H61B0/B3/B6 蓝牙+Wi-Fi 扩散罩灯带
     */
    public static final int GOODS_TYPE_VALUE_H61B0_B3_B6 = 264;
    public static final int PACT_TYPE_VALUE_H61B0_B3_B6 = 1;
    public static final int PACT_CODE_VALUE_H61B0_B3_B6 = 1;

    /**
     * H6800
     * 圣诞衣灯
     */
    public static final int GOODS_TYPE_VALUE_H6800 = 272;
    public static final int PACT_TYPE_VALUE_H6800 = 1;
    public static final int PACT_CODE_VALUE_H6800 = 1;
    public static final int PACT_CODE_VALUE_H6800_2 = 2;
    public static final int PACT_TYPE_VALUE_H6800_ENCRYPT = 2;
    public static final int PACT_CODE_VALUE_H6800_ENCRYPT = 1;


    /**
     * H6097 Wi-Fi+BLE RGBIC摄像头+电视灯带
     */
    public static final int GOODS_TYPE_VALUE_H6097 = 273;
    public static final int PACT_TYPE_VALUE_H6097 = 1;
    public static final int PACT_CODE_VALUE_H6097 = 1;
    public static final int PACT_TYPE_H6097_ENCRYPT = 2;
    public static final int PACT_CODE_H6097_ENCRYPT = 1;


    /**
     * 产品类型 户外网状灯
     * H6810
     */
    public static final int GOODS_TYPE_VALUE_H6810 = 255;
    public static final int PACT_TYPE_VALUE_H6810 = 1;
    public static final int PACT_TYPE_VALUE_H6810_2 = 2;
    public static final int PACT_CODE_VALUE_H6810 = 1;
    /**
     * 产品类型 户外网状灯
     * H6811
     */
    public static final int GOODS_TYPE_VALUE_H6811 = 256;
    public static final int PACT_TYPE_VALUE_H6811 = 1;
    public static final int PACT_TYPE_VALUE_H6811_2 = 2;
    public static final int PACT_CODE_VALUE_H6811 = 1;

    /**
     * 产品类型 户外网状灯
     * H6812
     */
    public static final int GOODS_TYPE_VALUE_H6812 = 257;
    public static final int PACT_TYPE_VALUE_H6812 = 1;
    public static final int PACT_CODE_VALUE_H6812 = 1;

    /**
     * H6093 极光星空灯
     */
    public static final int GOODS_TYPE_VALUE_H6093 = 271;
    public static final int PACT_TYPE_VALUE_H6093 = 1;
    public static final int PACT_CODE_VALUE_H6093 = 1;
    //第1次迭代pactCode,经典蓝牙断开功能+操作模式基础高阶功能
    public static final int PACT_CODE_VALUE_H6093_V2 = 2;
    //第2次迭代pactType,蓝牙加密功能+盛宴激光功能
    public static final int PACT_TYPE_VALUE_H6093_V2 = 2;

    /**
     * H6039 壁灯
     */
    public static final int GOODS_TYPE_VALUE_H6039 = 277;
    public static final int PACT_TYPE_VALUE_H6039 = 1;
    public static final int PACT_CODE_VALUE_H6039 = 1;
    public static final int PACT_TYPE_VALUE_H6039_2 = 2;

    /**
     * H6630 桌面像素屏
     * H8630(H6630的线下款)
     */
    public static final int GOODS_TYPE_VALUE_H6630 = 285;
    public static final int PACT_TYPE_VALUE_H6630 = 1;
    public static final int PACT_TYPE_VALUE_H6630_2 = 2;
    public static final int PACT_CODE_VALUE_H6630 = 1;
    public static final int PACT_CODE_VALUE_H6630_2 = 2;
    public static final int PACT_CODE_VALUE_H6630_3 = 3;

    /**
     * H6631 桌面像素屏
     */
    public static final int GOODS_TYPE_VALUE_H6631 = 286;
    public static final int PACT_TYPE_VALUE_H6631 = 1;
    public static final int PACT_TYPE_VALUE_H6631_2 = 2;
    public static final int PACT_CODE_VALUE_H6631 = 1;
    public static final int PACT_CODE_VALUE_H6631_2 = 2;
    public static final int PACT_CODE_VALUE_H6631_3 = 3;


    /**
     * 产品类型
     * H6840 - 瀑布灯
     */
    public static final int GOODS_TYPE_VALUE_H6840 = 278;
    public static final int PACT_TYPE_VALUE_H6840 = 1;
    public static final int PACT_TYPE_VALUE_H6840_2 = 2;
    public static final int PACT_CODE_VALUE_H6840 = 1;
    public static final int PACT_CODE_VALUE_H6840_2 = 2;

    /**
     * 产品类型
     * H6841 - 瀑布灯
     */
    public static final int GOODS_TYPE_VALUE_H6841 = 325;
    public static final int PACT_TYPE_VALUE_H6841 = 1;
    public static final int PACT_CODE_VALUE_H6841 = 1;

    /**
     * H703A、H703B
     * WIFI+BLE RGBICW 户外屋檐灯
     */
    public static final int GOODS_TYPE_VALUE_H703A_B = 263;
    public static final int PACT_TYPE_VALUE_H703A_B = 1;
    public static final int PACT_CODE_VALUE_H703A_B = 1;

    /**
     * H7085（BLE RGBIC车载底盘Rock灯）
     */
    public static final int GOODS_TYPE_VALUE_H7085 = 276;
    public static final int PACT_TYPE_VALUE_H7085 = 1;
    public static final int PACT_CODE_VALUE_H7085 = 1;


    /**
     * 产品类型
     * H6820 流星雨灯
     */
    public static final int GOODS_TYPE_VALUE_H6820 = 279;
    public static final int PACT_TYPE_VALUE_H6820 = 1;
    public static final int PACT_CODE_VALUE_H6820 = 1;
    /**
     * 产品类型
     * H6821 -流星雨灯
     */
    public static final int GOODS_TYPE_VALUE_H6821 = 280;
    public static final int PACT_TYPE_VALUE_H6821 = 1;
    public static final int PACT_CODE_VALUE_H6821 = 1;
    /**
     * H7070 户外激光灯
     */
    public static final int GOODS_TYPE_VALUE_H7070 = 283;
    public static final int PACT_TYPE_VALUE_H7070 = 1;
    public static final int PACT_CODE_VALUE_H7070 = 1;
    //迭代pactCode，灯效切换
    public static final int PACT_CODE_VALUE_H7070_V2 = 2;
    //迭代pactType，蓝牙加密
    public static final int PACT_TYPE_VALUE_H7070_V2 = 2;
    /**
     * H5110 蓝牙温湿度计(H5100的升级迭代款)
     */
    public static final int GOODS_TYPE_VALUE_4_H5110 = 287;
    public static final int PACT_TYPE_VALUE_H5110 = 1;
    public static final int PACT_TYPE_VALUE_H5110_V1 = 2;
    public static final int PACT_CODE_VALUE_H5110 = 1;
    public static final int PACT_CODE_VALUE_H5110_V1 = 1;

    /**
     * H6069 正方形方块灯 rgbic
     * H8069
     */
    public static final int GOODS_TYPE_VALUE_H6069 = 284;
    public static final int PACT_TYPE_VALUE_H6069 = 1;
    public static final int PACT_CODE_VALUE_H6069 = 1;
    public static final int PACT_TYPE_VALUE_H6069_ENCRYPT = 2;
    public static final int PACT_CODE_VALUE_H6069_ENCRYPT = 1;
    /**
     * H5044网关
     */
    public static final int GOODS_TYPE_VALUE_4_GW_H5044 = 291;
    public static final int PACT_TYPE_VALUE_4_H5044 = 1;
    public static final int PACT_CODE_VALUE_4_H5044 = 1;
    /**
     * H5059漏水检测器(搭配H5044使用)
     */
    public static final int GOODS_TYPE_VALUE_4_H5059 = 292;
    /**
     * H7025  RGBW 户外透明球泡灯串
     */
    public static final int GOODS_TYPE_VALUE_H7025 = 296;
    public static final int PACT_TYPE_VALUE_H7025 = 1;
    public static final int PACT_CODE_VALUE_H7025 = 1;

    /**
     * H7026、H8026  RGB 户外透明球泡灯串
     */
    public static final int GOODS_TYPE_VALUE_H7026 = 318;
    public static final int PACT_TYPE_VALUE_H7026 = 1;
    public static final int PACT_CODE_VALUE_H7026 = 1;

    /**
     * H7093 户外射灯2pack
     */
    public static final int GOODS_TYPE_VALUE_H7093 = 289;
    public static final int PACT_TYPE_VALUE_H7093 = 1;
    public static final int PACT_CODE_VALUE_H7093 = 1;

    /**
     * H7094 户外射灯4pack
     */
    public static final int GOODS_TYPE_VALUE_H7094 = 290;
    public static final int PACT_TYPE_VALUE_H7094 = 1;
    public static final int PACT_CODE_VALUE_H7094 = 1;

    /**
     * H61b8/9 rgbic
     */
    public static final int GOODS_TYPE_VALUE_H61b8 = 299;
    public static final int PACT_TYPE_VALUE_H61b8 = 1;
    public static final int PACT_CODE_VALUE_H61b8 = 1;
    //解决kol出去的固件问题
    public static final int PACT_TYPE_VALUE_H61b8_kol = 2;
    public static final int PACT_CODE_VALUE_H61b8_kol = 2;

    /**
     * H7086 RGBICW 户外花园灯
     */
    public static final int GOODS_TYPE_VALUE_H7086 = 293;
    public static final int PACT_TYPE_VALUE_H7086 = 1;
    public static final int PACT_CODE_VALUE_H7086 = 1;
    public static final int PACT_CODE_VALUE_H7086_V2 = 2;//对比 PACT_CODE_VALUE_H7086 多了灯头校准功能

    /**
     * H7030 球泡灯串
     */
    public static final int GOODS_TYPE_VALUE_H7030 = 295;
    public static final int PACT_TYPE_VALUE_H7030 = 1;
    public static final int PACT_CODE_VALUE_H7030 = 1;


    /**
     * H6038 2pck 壁灯
     */
    public static final int GOODS_TYPE_VALUE_H6038 = 288;
    public static final int PACT_TYPE_VALUE_H6038 = 1;
    public static final int PACT_CODE_VALUE_H6038 = 1;


    /**
     * H7076 RGBWW 户外洗墙壁灯
     */
    public static final int GOODS_TYPE_VALUE_H7076 = 313;
    public static final int PACT_TYPE_VALUE_H7076 = 1;
    public static final int PACT_CODE_VALUE_H7076 = 1;

    /**
     * 产品类型
     * <p>
     * h601E/F 筒灯
     * <p/>
     */
    public static final int GOODS_TYPE_VALUE_H601EF = 311;
    public static final int PACT_TYPE_VALUE_H601EF = 1;
    public static final int PACT_CODE_VALUE_H601EF = 1;
    // 色温设置
    public static final int PACT_CODE_VALUE_H601EF_v1 = 2;


    /**
     * H60B1幻彩洗顶落地灯 GRBWWIC
     */
    public static final int GOODS_TYPE_VALUE_H60B1 = 300;
    public static final int PACT_TYPE_VALUE_H60B1 = 1;
    public static final int PACT_CODE_VALUE_H60B1 = 1;

    /**
     * 冰箱温度计 H5111
     */
    public static final int GOODS_TYPE_VALUE_4_H5111 = 310;
    public static final int PACT_TYPE_VALUE_4_H5111 = 1;
    public static final int PACT_CODE_VALUE_4_H5111 = 1;
    /**
     * 4探针无线烤肉计 H5194
     */
    public static final int GOODS_TYPE_VALUE_4_H5194 = 314;
    public static final int PACT_TYPE_VALUE_4_H5194 = 1;
    public static final int PACT_CODE_VALUE_4_H5194 = 1;

    /**
     * H5089 夜灯插座
     */
    public static final int GOODS_TYPE_VALUE_4_H5089 = 307;
    public static final int PACT_TYPE_VALUE_4_H5089 = 1;
    public static final int PACT_CODE_VALUE_4_H5089 = 2;

    /**
     * 抱树灯 H7087
     */
    public static final int GOODS_TYPE_VALUE_H7087 = 315;
    public static final int PACT_TYPE_VALUE_H7087 = 1;
    public static final int PACT_CODE_VALUE_H7087 = 1;

    /**
     * H60B0 三光源复合落地灯
     */
    public static final int GOODS_TYPE_VALUE_H60B0 = 301;
    public static final int PACT_TYPE_VALUE_H60B0 = 1;
    public static final int PACT_CODE_VALUE_H60B0 = 1;
    public static final int PACT_CODE_VALUE_H60B0_V2 = 2;

    /**
     * H60A4 Wi-Fi+蓝⽛智能吸顶灯
     */
    public static final int GOODS_TYPE_VALUE_H60A4 = 302;
    public static final int PACT_TYPE_VALUE_H60A4 = 1;
    public static final int PACT_CODE_VALUE_H60A4 = 1;

    /**
     * H6048 赛博风游戏灯柱
     */
    public static final int GOODS_TYPE_VALUE_H6048 = 298;
    public static final int PACT_TYPE_VALUE_H6048 = 1;
    public static final int PACT_CODE_VALUE_H6048 = 1;

    /**
     * BLE + WiFi
     * RGBICW 球泡灯串 H702A/H702B/H702C
     */
    public static final int GOODS_TYPE_VALUE_H702ABC = 316;
    public static final int PACT_TYPE_VALUE_H702ABC = 1;
    public static final int PACT_CODE_VALUE_H702ABC = 1;

    /**
     * H7056 RGBWIC 户外路径灯
     */
    public static final int GOODS_TYPE_VALUE_H7056 = 304;
    public static final int PACT_TYPE_VALUE_H7056 = 1;
    public static final int PACT_CODE_VALUE_H7056 = 1;

    /**
     * H60A6 Wi-Fi+蓝⽛智能吸顶灯
     */
    public static final int GOODS_TYPE_VALUE_H60A6 = 303;
    public static final int PACT_TYPE_VALUE_H60A6 = 1;
    public static final int PACT_CODE_VALUE_H60A6 = 1;
    public static final int PACT_CODE_VALUE_H60A6_2 = 2;//迭代功能-分段颜色
    public static final int PACT_CODE_VALUE_H60A6_3 = 3;//迭代功能-分段色温

    /**
     * H8066 户外射灯
     */
    public static final int GOODS_TYPE_VALUE_H8066 = 305;
    public static final int PACT_TYPE_VALUE_H8066 = 1;
    public static final int PACT_CODE_VALUE_H8066 = 1;

    /**
     * H5112 双探针温湿度计
     */
    public static final int GOODS_TYPE_VALUE_4_H5112 = 330;
    public static final int PACT_TYPE_VALUE_4_H5112 = 1;
    public static final int PACT_CODE_VALUE_4_H5112 = 1;

    /**
     * H8015
     */
    public static final int GOODS_TYPE_VALUE_H8015 = 328;
    public static final int PACT_TYPE_VALUE_H8015 = 1;
    public static final int PACT_CODE_VALUE_H8015 = 1;

    /**
     * H66A0 3摄+灯带
     */
    public static final int GOODS_TYPE_VALUE_H66A0 = 327;
    public static final int PACT_TYPE_VALUE_H66A0 = 1;
    public static final int PACT_CODE_VALUE_H66A0 = 1;

    /**
     * H60C1 餐吊灯 H80C1
     */
    public static final int GOODS_TYPE_VALUE_H60C1 = 324;
    public static final int PACT_TYPE_VALUE_H60C1 = 1;
    public static final int PACT_CODE_VALUE_H60C1 = 1;

    /**
     * H70B6窗帘灯
     */
    public static final int GOODS_TYPE_VALUE_H70B6 = 329;
    public static final int PACT_TYPE_VALUE_H70B6 = 1;
    public static final int PACT_CODE_VALUE_H70B6 = 1;

    /**
     * H6095 极光星空灯
     */
    public static final int GOODS_TYPE_VALUE_H6095 = 323;
    public static final int PACT_TYPE_VALUE_H6095 = 1;
    public static final int PACT_CODE_VALUE_H6095 = 1;

    /**
     * H6094 极光星空灯
     */
    public static final int GOODS_TYPE_VALUE_H6094 = 322;
    public static final int PACT_TYPE_VALUE_H6094 = 1;
    public static final int PACT_CODE_VALUE_H6094 = 1;

    /**
     * H609D 户外射灯
     */
    public static final int GOODS_TYPE_VALUE_H609D = 331;
    public static final int PACT_TYPE_VALUE_H609D = 1;
    public static final int PACT_CODE_VALUE_H609D = 1;
    /**
     *
     * H707A/B RGBWWIC 户外屋檐点光源
     */
    public static final int GOODS_TYPE_VALUE_H707ABC = 326;
    public static final int PACT_TYPE_VALUE_H707ABC = 1;
    public static final int PACT_CODE_VALUE_H707ABC = 1;

    /**
     * H6690
     */
    public static final int GOODS_TYPE_VALUE_H6690 = 336;
    public static final int PACT_TYPE_VALUE_H6690 = 1;
    public static final int PACT_CODE_VALUE_H6690 = 1;

    /**
     * H6671/72
     */
    public static final int GOODS_TYPE_VALUE_H6671_72 = 332;
    public static final int PACT_TYPE_VALUE_H6671_72 = 1;
    public static final int PACT_CODE_VALUE_H6671_72 = 1;


    /**
     * H7073（Wi-Fi+BLE）户外激光射灯
     */
    public static final int GOODS_TYPE_VALUE_H7073 = 333;
    public static final int PACT_TYPE_VALUE_H7073 = 1;
    public static final int PACT_CODE_VALUE_H7073 = 1;
    /**
     * H7071
     */
    public static final int GOODS_TYPE_VALUE_H7071 = 334;
    public static final int PACT_TYPE_VALUE_H7071 = 1;
    public static final int PACT_CODE_VALUE_H7071 = 1;

    /*----------------------------协议逻辑处理------------------------------*/

    /**
     * 构建协议对象
     *
     * @param pactType 协议类型<p>数值采用的是当前类提供的常量#PACT_TYPE_4_#开头的常量值
     * @param pactCode 协议code<p>数值采用的是当前类提供的常量#PACT_CODE_4_#开头的常量值
     * @return
     */
    public static Protocol beProtocol(int pactType, int pactCode) {
        return new Protocol(pactType, pactCode);
    }

    /**
     * 解析对应产品类型的广播包内的协议类型
     *
     * @param goodsType
     * @param scanRecord
     * @return
     */
    public static Protocol parseBleBroadcastPactInfo(int goodsType, byte[] scanRecord) {
        return parseBleBroadcastPactInfo(goodsType, scanRecord, null);
    }

    /**
     * 解析对应产品类型的广播包内的协议类型
     *
     * @param goodsType
     * @param scanRecord
     * @param broadcastVersionCall 回调广播协议版本，目前支持 灯/家电/插座：parseBleBroadcastPact解析方法，可空
     * @return
     */
    public static Protocol parseBleBroadcastPactInfo(int goodsType, byte[] scanRecord, @Nullable Function1<BleBroadCastInfo, Unit> broadcastVersionCall) {
        /*灯带设备*/
        if (GOODS_TYPE_VALUE_STRING_LIGHT_BLE_WIFI_V2 == goodsType
                || GOODS_TYPE_VALUE_STRING_LIGHT_BLE_V2 == goodsType
                || GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V1 == goodsType
                || GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_IOT_V1 == goodsType
                || GOODS_TYPE_VALUE_CAR_LIGHT_BLE_V1 == goodsType
                || GOODS_TYPE_VALUE_CH_LIGHT_BLE_V1 == goodsType
                || GOODS_TYPE_VALUE_TV_LIGHT_BLE_V1 == goodsType
                || GOODS_TYPE_VALUE_BARE_LIGHT_BLE_V1 == goodsType
                || GOODS_TYPE_VALUE_HOME_LIGHT_RGBWW_V1 == goodsType
                || GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V1 == goodsType
                || GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_LIMIT_BLE_V1 == goodsType
                || GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V1 == goodsType
                || GOODS_TYPE_VALUE_LED_BULB_BLE_V1 == goodsType
                || GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_WIFI_V2 == goodsType
                || GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V2 == goodsType
                || GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V2 == goodsType
                || GOODS_TYPE_VALUE_TV_LIGHT_BLE_WIFI_V4 == goodsType
                || GOODS_TYPE_VALUE_TABLE_LAMP_BLE_WIFI_V1 == goodsType
                || GOODS_TYPE_VALUE_VERTICAL_HOLLOW_LAMP_BLE_WIFI_V1 == goodsType
                || GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1 == goodsType
                || GOODS_TYPE_VALUE_BULB_LIGHT_BLE_WIFI_V1 == goodsType
                || GOODS_TYPE_VALUE_BULB_LIGHT_STRING_BLE_V2 == goodsType
                || GOODS_TYPE_VALUE_PICKUP_BOX_V1 == goodsType
                || GOODS_TYPE_VALUE_CAMPING_LIGHT_BLE_V1 == goodsType
                || GOODS_TYPE_VALUE_BED_LIGHT_BLE_WIFI_V1 == goodsType
                || GOODS_TYPE_VALUE_BULB_LIGHT_BLE_WIFI_V2 == goodsType
                || GOODS_TYPE_VALUE_BK_BLE_RGB == goodsType
                || GOODS_TYPE_VALUE_BK_BLE_WIFI_RGB == goodsType
                || GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT == goodsType
                || GOODS_TYPE_VALUE_MOVE_ATMOSPHERE_LIGHT == goodsType
                || GOODS_TYPE_VALUE_CAR_LIGHT == goodsType
                || GOODS_TYPE_VALUE_CAR_LIGHT_H7095 == goodsType
                || GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT == goodsType
                || GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_8 == goodsType
                || GOODS_TYPE_VALUE_CAR_BOTTOM_LIGHT_6 == goodsType
                || GOODS_TYPE_VALUE_PATH_LIGHT == goodsType
                || GOODS_TYPE_VALUE_CUBE_LIGHT == goodsType
                || GOODS_TYPE_VALUE_WARM_WHITE_BULB_LIGHT == goodsType
                || GOODS_TYPE_VALUE_TV_ATMOSPHERE_LIGHT == goodsType
                || GOODS_TYPE_VALUE_BULB_STRING == goodsType
                || GOODS_TYPE_VALUE_BULB_STRING_V2 == goodsType
                || GOODS_TYPE_VALUE_HEATER == goodsType
                || GOODS_TYPE_VALUE_HUMIDIFIER == goodsType
                || GOODS_TYPE_VALUE_AIR_CLEANER_V3 == goodsType
                || GOODS_TYPE_VALUE_HUMIDIFIER_V2 == goodsType
                || GOODS_TYPE_VALUE_HUMIDIFIER_V3 == goodsType
                || GOODS_TYPE_VALUE_146 == goodsType
                || GOODS_TYPE_VALUE_189 == goodsType
                || GOODS_TYPE_VALUE_152 == goodsType
                || GOODS_TYPE_VALUE_DEHUMIDIFIER_V1 == goodsType
                || GOODS_TYPE_VALUE_DEHUMIDIFIER_V2 == goodsType
                || GOODS_TYPE_VALUE_GOOSENECK_FLOOR_LAMP_BLE_WIFI_V1 == goodsType
                || GOODS_TYPE_VALUE_UPGLOW_LAMP_BLE_WIFI_V1 == goodsType
                || GOODS_TYPE_VALUE_MOVE_ATMOSPHERE_LIGHT_V2 == goodsType
                || GOODS_TYPE_VALUE_BULB_STRING_V3 == goodsType
                || GOODS_TYPE_VALUE_BULB_STRING_V4 == goodsType
                || GOODS_TYPE_VALUE_BULB == goodsType
                || GOODS_TYPE_VALUE_BLE_ALEXA_RGB == goodsType
                || GOODS_TYPE_VALUE_BLE_ALEXA_RGB_LIMIT == goodsType
                || GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET == goodsType
                || GOODS_TYPE_VALUE_RGBIC_BLE_4_SECRET_V1 == goodsType
                || GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_TV_LIGHT == goodsType
                || GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V1 == goodsType
                || GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V2 == goodsType
                || GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_4_SECRET_V3 == goodsType
                || GOODS_TYPE_VALUE_UPGLOW_FLOOR_LAMP_WW_BLE_WIFI_V1 == goodsType
                || GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2 == goodsType
                || GOODS_TYPE_VALUE_AIR_CLEANER == goodsType
                || GOODS_TYPE_VALUE_160 == goodsType
                || GOODS_TYPE_VALUE_AIR_CLEANER_V2 == goodsType
                || GOODS_TYPE_VALUE_AIR_CLEANER_V4 == goodsType
                || GOODS_TYPE_VALUE_HEATER_7132 == goodsType
                || GOODS_TYPE_VALUE_FLOOD_LIGHT == goodsType
                || GOODS_TYPE_VALUE_CRUEL_WALL_LIGHT == goodsType
                || GOODS_TYPE_VALUE_TV_RGBIC_LIGHT_V1 == goodsType
                || GOODS_TYPE_VALUE_RGB_BLE_WIFI_CHILDREN_LAMP_V1 == goodsType
                || GOODS_TYPE_VALUE_SPLICE_STRIP_LIGHT_V2 == goodsType
                || GOODS_TYPE_VALUE_CIRCULATION_FAN_BLE_IOT_DESKTOP_AIR_V1 == goodsType
                || GOODS_TYPE_VALUE_BLE_IOT_TOWER_FAN_V1 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_WASH_THE_WALL_LIGHT == goodsType
                || GOODS_TYPE_VALUE_WARM_WHITE_BULB_LIGHT_V2 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_LAMP_TRIANGLE == goodsType
                || GOODS_TYPE_VALUE_PICKUP_BOX_V2 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_FLOOD_LIGHT == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST == goodsType
                || GOODS_TYPE_VALUE_CUBE_LIGHT_V2 == goodsType
                || GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB == goodsType
                || GOODS_TYPE_VALUE_START_SKY_LIGHT == goodsType
                || GOODS_TYPE_VALUE_GOOSE_NECK_POT == goodsType
                || GOODS_TYPE_VALUE_168 == goodsType
                || GOODS_TYPE_VALUE_177 == goodsType
                || GOODS_TYPE_VALUE_IC_MAKER == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_CHILD_LIGHT == goodsType//H6057儿童灯
                || GOODS_TYPE_VALUE_OUTDOOR_LIGHT_BULB_V2 == goodsType
                || GOODS_TYPE_VALUE_PATH_LIGHT_V2 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V2 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V4 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_RGB_LIGHT == goodsType
                || GOODS_TYPE_VALUE_FLOOD_LIGHT_V2 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_RGB_FLUSH_LIGHT_PANEL == goodsType
                || GOODS_TYPE_VALUE_SHOOT_LIGHT_V1 == goodsType
                || GOODS_TYPE_VALUE_MUSIC_LIGHT_BAR == goodsType
                || GOODS_TYPE_VALUE_TV_ATMOSPHERE_LIGHT_V2 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_GAME_FEAST_V3 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_TV_GAME_FEAST == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6043 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6042 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_HIGH_END == goodsType
                || GOODS_TYPE_VALUE_LED_PATH_LIGHTS == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_LAMP_Y == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT == goodsType
                || GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_OUTDOOR_LAMP == goodsType
                || GOODS_TYPE_VALUE_BLE_AI_MUSIC_BOX == goodsType
                || GOODS_TYPE_VALUE_BLE_MUSIC_BOX_1167 == goodsType
                || GOODS_TYPE_VALUE_BLE_MUSIC_BOX_1168 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT_H70B2 == goodsType
                || GOODS_TYPE_VALUE_HUMIDIFIER_V4 == goodsType
                || GOODS_TYPE_VALUE_TOWER_FAN_BLE_IOT == goodsType
                || GOODS_TYPE_VALUE_GOOSE_NECK_POT_V2 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_LIGHT_HDMI == goodsType
                || GOODS_TYPE_VALUE_RGBIC_BLE_WIFI_HDMI_ONLY_LIGHT == goodsType
                || GOODS_TYPE_VALUE_RGBIC_BLE_CAR_LIGHT == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_DESK_LIGHT == goodsType
                || GOODS_TYPE_VALUE_BLE_BALL_LIGHT == goodsType
                || GOODS_TYPE_VALUE_BLE_RGBWW_BALL_LIGHT == goodsType
                || GOODS_TYPE_VALUE_HUMIDIFIER_V6 == goodsType
                || GOODS_TYPE_VALUE_H714E == goodsType
                || GOODS_TYPE_VALUE_HEATER_7135 == goodsType
                || GOODS_TYPE_VALUE_HEATER_h6079 == goodsType
                || GOODS_TYPE_VALUE_h6092 == goodsType
                || GOODS_TYPE_VALUE_OUTDOOR_h7033 == goodsType
                || GOODS_TYPE_VALUE_CAR_LIGHT_h7092 == goodsType
                || GOODS_TYPE_VALUE_h713A == goodsType
                || GOODS_TYPE_VALUE_h713B == goodsType
                || GOODS_TYPE_VALUE_h713C == goodsType
                || GOODS_TYPE_VALUE_H6088 == goodsType
                || GOODS_TYPE_VALUE_H7066 == goodsType
                || GOODS_TYPE_VALUE_HUMIDIFIER_170 == goodsType
                || GOODS_TYPE_VALUE_HEATER_7133 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_H7134 == goodsType
                || GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BE == goodsType
                || GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BC == goodsType
                || GOODS_TYPE_VALUE_H70A1_A2_A3 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_H6175 == goodsType
                || GOODS_TYPE_VALUE_RGBIC_LIGHT_H61BA == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_PC_LIGHT == goodsType
                || GOODS_TYPE_VALUE_H6608 == goodsType
                || GOODS_TYPE_VALUE_CUBE_LIGHT_606A == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_H70C1_H70C2 == goodsType
                || GOODS_TYPE_VALUE_H706A_B_C == goodsType
                || GOODS_TYPE_VALUE_H608a == goodsType
                || GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6099 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_H60A0 == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_RGBIC_H6603 == goodsType
                || GOODS_TYPE_VALUE_H61D3_D5 == goodsType
                || GOODS_TYPE_VALUE_H61E5 == goodsType
                || GOODS_TYPE_VALUE_H61E6 == goodsType
                || GOODS_TYPE_VALUE_H6640 == goodsType
                || GOODS_TYPE_VALUE_H6641 == goodsType
                || GOODS_TYPE_VALUE_H61A9 == goodsType
                || GOODS_TYPE_VALUE_RGBIC_CAMERA_TV_H6098 == goodsType
                || GOODS_TYPE_VALUE_4_H7106 == goodsType
                || GOODS_TYPE_VALUE_4_H7105 == goodsType
                || GOODS_TYPE_VALUE_4_H7107 == goodsType
                || GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C == goodsType
                || GOODS_TYPE_VALUE_WALL_LAMP_H7075 == goodsType
                || GOODS_TYPE_VALUE_H6020 == goodsType
                || GOODS_TYPE_VALUE_H6032 == goodsType
                || GOODS_TYPE_VALUE_H70BC == goodsType
                || GOODS_TYPE_VALUE_H616C == goodsType
                || GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7052 == goodsType
                || GOODS_TYPE_VALUE_UNDERGROUND_LAMP_H7053 == goodsType
                || GOODS_TYPE_VALUE_H7146 == goodsType
                || GOODS_TYPE_VALUE_H7147 == goodsType
                || GOODS_TYPE_VALUE_H7148 == goodsType
                || GOODS_TYPE_VALUE_H7149 == goodsType
                || GOODS_TYPE_VALUE_HEATER_H7136 == goodsType
                || GOODS_TYPE_VALUE_HEATER_H713D == goodsType
                || GOODS_TYPE_VALUE_H7137 == goodsType
                || GOODS_TYPE_VALUE_H713E == goodsType  //6.1.1
                || GOODS_TYPE_VALUE_H7063 == goodsType
                || GOODS_TYPE_VALUE_H6063 == goodsType
                || GOODS_TYPE_VALUE_H717D == goodsType
                || GOODS_TYPE_VALUE_H7129 == goodsType
                || GOODS_TYPE_VALUE_H7128 == goodsType
                || GOODS_TYPE_VALUE_H7124 == goodsType
                || GOODS_TYPE_VALUE_H712C == goodsType
                || GOODS_TYPE_VALUE_H7184 == goodsType
                || GOODS_TYPE_VALUE_H7037 == goodsType
                || GOODS_TYPE_VALUE_H70B3 == goodsType
                || GOODS_TYPE_VALUE_H70B4 == goodsType
                || GOODS_TYPE_VALUE_H70B5 == goodsType
                || GOODS_TYPE_VALUE_H612x == goodsType
                || GOODS_TYPE_VALUE_BLE_WIFI_H60A1 == goodsType
                || GOODS_TYPE_VALUE_RGBIC_AI_HDMI_H6604 == goodsType
                || GOODS_TYPE_VALUE_H705DEF_H805ABC == goodsType
                || GOODS_TYPE_VALUE_WALL_LAMP_H7078 == goodsType
                || GOODS_TYPE_VALUE_H6022 == goodsType
                || GOODS_TYPE_VALUE_H7067_68_69 == goodsType
                || GOODS_TYPE_VALUE_H7127 == goodsType
                || GOODS_TYPE_VALUE_H7112 == goodsType
                || GOODS_TYPE_VALUE_H7057 == goodsType
                || GOODS_TYPE_VALUE_H7058 == goodsType
                || GOODS_TYPE_VALUE_H70C4_5_6_7_8_9 == goodsType
                || GOODS_TYPE_VALUE_H70D1 == goodsType
                || GOODS_TYPE_VALUE_H70D2 == goodsType
                || GOODS_TYPE_VALUE_H70D3 == goodsType
                || GOODS_TYPE_VALUE_H605A == goodsType
                || GOODS_TYPE_VALUE_H7138 == goodsType
                || GOODS_TYPE_VALUE_4_H7103 == goodsType
                || GOODS_TYPE_VALUE_H61B0_B3_B6 == goodsType
                || GOODS_TYPE_VALUE_H6169 == goodsType
                || GOODS_TYPE_VALUE_H6800 == goodsType
                || GOODS_TYPE_VALUE_H61F6 == goodsType
                || GOODS_TYPE_VALUE_H6097 == goodsType
                || GOODS_TYPE_VALUE_H6810 == goodsType
                || GOODS_TYPE_VALUE_H6811 == goodsType
                || GOODS_TYPE_VALUE_H6812 == goodsType
                || GOODS_TYPE_VALUE_H6093 == goodsType
                || GOODS_TYPE_VALUE_H6039 == goodsType
                || GOODS_TYPE_VALUE_H6038 == goodsType
                || GOODS_TYPE_VALUE_H6630 == goodsType
                || GOODS_TYPE_VALUE_H6631 == goodsType
                || GOODS_TYPE_VALUE_H703A_B == goodsType
                || GOODS_TYPE_VALUE_H7085 == goodsType
                || GOODS_TYPE_VALUE_H7070 == goodsType
                || GOODS_TYPE_VALUE_H6069 == goodsType
                || GOODS_TYPE_VALUE_H6840 == goodsType
                || GOODS_TYPE_VALUE_H6841 == goodsType
                || GOODS_TYPE_VALUE_H7093 == goodsType
                || GOODS_TYPE_VALUE_H7094 == goodsType
                || GOODS_TYPE_VALUE_H61b8 == goodsType
                || GOODS_TYPE_VALUE_H7086 == goodsType
                || GOODS_TYPE_VALUE_H7025 == goodsType
                || GOODS_TYPE_VALUE_H6048 == goodsType
                || GOODS_TYPE_VALUE_H601EF == goodsType
                || GOODS_TYPE_VALUE_H60B1 == goodsType
                || GOODS_TYPE_VALUE_H7072 == goodsType
                || GOODS_TYPE_VALUE_H7076 == goodsType
                || GOODS_TYPE_VALUE_H60B2 == goodsType
                || GOODS_TYPE_VALUE_H60B0 == goodsType
                || GOODS_TYPE_VALUE_H60A4 == goodsType
                || GOODS_TYPE_VALUE_H7056 == goodsType
                || GOODS_TYPE_VALUE_H60A6 == goodsType
                || GOODS_TYPE_VALUE_H8066 == goodsType
                || GOODS_TYPE_VALUE_H7152 == goodsType
                || GOODS_TYPE_VALUE_H60C1 == goodsType
                || GOODS_TYPE_VALUE_H702ABC == goodsType
                || GOODS_TYPE_VALUE_H7153 == goodsType
                || GOODS_TYPE_VALUE_H7026 == goodsType
                || GOODS_TYPE_VALUE_H7087 == goodsType
                || GOODS_TYPE_VALUE_H8121 == goodsType
                || GOODS_TYPE_VALUE_H8120 == goodsType
                || GOODS_TYPE_VALUE_H8015 == goodsType
                || GOODS_TYPE_VALUE_H70B6 == goodsType
                || GOODS_TYPE_VALUE_H66A0 == goodsType
                || GOODS_TYPE_VALUE_H6095 == goodsType
                || GOODS_TYPE_VALUE_H6094 == goodsType
                || GOODS_TYPE_VALUE_H609D == goodsType
                || GOODS_TYPE_VALUE_H707ABC == goodsType
                || GOODS_TYPE_VALUE_H6690 == goodsType
                || GOODS_TYPE_VALUE_H6671_72 == goodsType
                || GOODS_TYPE_VALUE_H7073 == goodsType
                || GOODS_TYPE_VALUE_H7071 == goodsType
        ) {
            BleBroadCastInfo result = BleUtil.parseBleBroadcastPact(scanRecord);
            if (result.getFlag() == BleUtil.SUC_FLAG) {
                SafeLog.Companion.v(TAG, () -> "解析广播协议goodsType：" + goodsType + " version:" + result.getBbVersion() + " SupportEncryption: " + result.getSupportEncryption());
                if (broadcastVersionCall != null && result.getValidIntNum() > 3) {
                    broadcastVersionCall.invoke(result);
                }
                return new Protocol(result.getPactType(), result.getPactCode());
            }
            return null;
        }
        //温湿度计设备(H5179)+网关设备
        else if (GOODS_TYPE_VALUE_TH_BLE_WIFI_V1 == goodsType
                || GOODS_TYPE_VALUE_TH_BLE_IOT_GATEWAY == goodsType
                || GOODS_TYPE_VALUE_GATEWAY_H5043 == goodsType
                || GOODS_TYPE_VALUE_4_GATEWAY_H5042 == goodsType
                || GOODS_TYPE_VALUE_4_GW_H5044 == goodsType
        ) {
            BleBroadCastInfo result = BleUtil.parseBleBroadcastPact4Th(scanRecord);
            if (result.getFlag() == BleUtil.SUC_FLAG) {
                if (broadcastVersionCall != null && result.getValidIntNum() > 3) {
                    broadcastVersionCall.invoke(result);
                }
                return new Protocol(result.getPactType(), result.getPactCode());
            } else {
                //新款H5179的广播跟其他51系列的温湿度计广播一样
                if (GOODS_TYPE_VALUE_TH_BLE_WIFI_V1 == goodsType) {
                    BleBroadCastInfo resultV1 = BleUtil.parseBleBroadcastPact4MultiTh(scanRecord);
                    if (resultV1.getFlag() == BleUtil.SUC_FLAG) {
                        if (broadcastVersionCall != null && resultV1.getValidIntNum() > 3) {
                            broadcastVersionCall.invoke(resultV1);
                        }
                        return new Protocol(resultV1.getPactType(), resultV1.getPactCode());
                    }
                }
            }
        }
        //温湿度计
        else if (GOODS_TYPE_VALUE_TH_BLE_V1 == goodsType
                || GOODS_TYPE_VALUE_TH_BLE == goodsType
                || GOODS_TYPE_VALUE_TH_BLE_MULTI_V1 == goodsType
                || GOODS_TYPE_VALUE_TH_BLE_WIFI_V2 == goodsType
                || GOODS_TYPE_VALUE_THP_BLE_WIFI == goodsType
                || GOODS_TYPE_VALUE_H5140 == goodsType
                || GOODS_TYPE_VALUE_4_TH_H5104 == goodsType
                || GOODS_TYPE_VALUE_TH_BLE_H5105 == goodsType
                || GOODS_TYPE_VALUE_4_TH_H5108 == goodsType
                || GOODS_TYPE_VALUE_4_H5110 == goodsType
                || GOODS_TYPE_VALUE_4_H5111 == goodsType
                || GOODS_TYPE_VALUE_H5171 == goodsType
                || GOODS_TYPE_VALUE_4_H5112 == goodsType
        ) {
            if (goodsType == GOODS_TYPE_VALUE_H5140) {// H5140广播格式不一样
                BleBroadCastInfo result = BleUtil.parseH5140BleBroadcastPact(scanRecord);
                if (result.getFlag() == BleUtil.SUC_FLAG) {
                    if (broadcastVersionCall != null && result.getValidIntNum() > 3) {
                        broadcastVersionCall.invoke(result);
                    }
                    return new Protocol(result.getPactType(), result.getPactCode());
                }
            }
            BleBroadCastInfo result = BleUtil.parseBleBroadcastPact4MultiTh(scanRecord);
            if (result.getFlag() == BleUtil.SUC_FLAG) {
                if (broadcastVersionCall != null && result.getValidIntNum() > 3) {
                    broadcastVersionCall.invoke(result);
                }
                return new Protocol(result.getPactType(), result.getPactCode());
            }
        }
        //智能插座
        else if (GOODS_TYPE_VALUE_PLUG_BLE_IOT_V2 == goodsType
                || GOODS_TYPE_VALUE_TWO_PLUG_BLE_IOT_V2 == goodsType
                || GOODS_TYPE_VALUE_TRIPLE_PLUG_BLE_IOT == goodsType
                || GOODS_TYPE_VALUE_SMART_PLUG_PRO_H5086 == goodsType
                || GOODS_TYPE_VALUE_4_H5089 == goodsType
        ) {
            BleBroadCastInfo result = BleUtil.parseBleBroadcastPact(scanRecord);
            if (result.getFlag() == BleUtil.SUC_FLAG) {
                SafeLog.Companion.v(TAG, () -> "解析广播协议goodsType：" + goodsType + " version:" + result.getBbVersion());
                if (broadcastVersionCall != null && result.getValidIntNum() > 3) {
                    broadcastVersionCall.invoke(result);
                }
                return new Protocol(result.getPactType(), result.getPactCode());
            }
        }
        //烤肉计
        else if (goodsType == GOODS_TYPE_VALUE_BARBECUE_BLE_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BARBECUE_BLE_NO_SCREEN_V1
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BARBECUE_BLE_MULTI_V4
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BBQ_NO_SCREEN_2
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BBQ_NO_SCREEN_4
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BBQ_BLE_IOT_MULTI_V4
                || goodsType == GoodsType.GOODS_TYPE_VALUE_4_MEAT_TH_H5199
                || goodsType == GoodsType.GOODS_TYPE_VALUE_4_MEAT_TH_H5192
                || goodsType == GoodsType.GOODS_TYPE_VALUE_4_MEAT_TH_H5191
                || goodsType == GoodsType.GOODS_TYPE_VALUE_4_MEAT_TH_H5196
                || goodsType == GoodsType.GOODS_TYPE_VALUE_4_H5194
                || goodsType == GOODS_TYPE_VALUE_BARBECUE_BLE_MULTI_V2) {
            int[] result = BleUtil.parseBleBroadcastPactBbqV1(scanRecord);
            if (result[0] == BleUtil.SUC_FLAG) {
                return new Protocol(result[1], result[2]);
            }
            //512x sensor相关设备的广播解析
        } else if (GOODS_TYPE_VALUE_HUMAN_SENSOR == goodsType
                || GOODS_TYPE_VALUE_KEY_SENSOR == goodsType
                || GOODS_TYPE_VALUE_DOOR_SENSOR == goodsType
                || GOODS_TYPE_VALUE_GOING_TO_STICK == goodsType
                || GOODS_TYPE_VALUE_SIX_BUTTON_SENSOR == goodsType
                || GOODS_TYPE_VALUE_TWO_BUTTON_SENSOR == goodsType
                || GOODS_TYPE_VALUE_HUMAN_SENSOR_V2 == goodsType
                || GOODS_TYPE_4_H5130 == goodsType
        ) {
            int[] result = BleUtil.parseH512xBleAddBroadcast(scanRecord);
            if (result[0] == BleUtil.SUC_FLAG) {
                return new Protocol(result[1], result[2]);
            }
        } else {
            if (LogInfra.openLog()) {
                LogInfra.Log.e(TAG, "parseBleBroadcastPactInfo() goodsType = " + goodsType + " ; 未找到对应的广播解析Protocol定义");
            }
        }
        return null;
    }

    public static boolean isMusicBoxProduct(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_MUSIC_BOX_1168
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_MUSIC_BOX_1167
                || goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_AI_MUSIC_BOX
                || goodsType == GoodsType.GOODS_TYPE_VALUE_PICKUP_BOX_V2;
    }

    public static boolean isCubeProduct(int goodsType) {
        return goodsType == GOODS_TYPE_VALUE_CUBE_LIGHT ||
                goodsType == GOODS_TYPE_VALUE_CUBE_LIGHT_V2 ||
                goodsType == GOODS_TYPE_VALUE_BLE_WIFI_LAMP_TRIANGLE ||
                goodsType == GOODS_TYPE_VALUE_BLE_WIFI_LAMP_Y ||
                goodsType == GOODS_TYPE_VALUE_CUBE_LIGHT_606A ||
                goodsType == GOODS_TYPE_VALUE_H6063 ||
                goodsType == GOODS_TYPE_VALUE_H6069
                ;
    }

    //是否是双灯柱产品 从电源开始 1-2-3-4-5-6
    public static boolean isLampStandard(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_FEAST_6043 || //6043
                goodsType == GoodsType.GOODS_TYPE_VALUE_H6048;
    }

    //区域⚡️1-2-3   4-5-6-⚡️
    //是否是双灯柱产品, 且特殊分段产品6047，6046，6053，6056
    public static boolean isLampStandardSpecial(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_TV_ATMOSPHERE_LIGHT_V2 ||//6046
                goodsType == GoodsType.GOODS_TYPE_VALUE_BLE_WIFI_TV_GAME_FEAST || // 6047
                goodsType == GoodsType.GOODS_TYPE_VALUE_DREAM_COLOR_LIGHT_BLE_V2 ||//6053
                goodsType == GoodsType.GOODS_TYPE_VALUE_TV_ATMOSPHERE_LIGHT//6056
                ;
    }

    //是否是落地灯
    public static boolean isFloorLamp(int goodsType) {
        return goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V1 ||//6072，8072
                goodsType == GoodsType.GOODS_TYPE_VALUE_STRAIGHT_FLOOR_LAMP_BLE_WIFI_V2 ||//6076，8076
                goodsType == GoodsType.GOODS_TYPE_VALUE_HEATER_h6079 ||
                goodsType == GoodsType.GOODS_TYPE_VALUE_RGBIC_WW_FLOOR_LAMP_H607C ||//607C 807C
                goodsType == GoodsType.GOODS_TYPE_VALUE_H60B1//60B1
                ;
    }


    /**
     * 是否支持设备串联拼接，类似H70B1
     *
     * @return
     */
    public static boolean isSupportSplicing(int goodsType, int pactType, int pactCode) {
        boolean isSupport = false;
        if (goodsType == GOODS_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT) {
            isSupport = pactType >= PACT_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT_V2_2;
        }
        if (goodsType == GOODS_TYPE_VALUE_BLE_WIFI_CURTAIN_LIGHT_H70B2) {
            isSupport = true;
        }
        if (goodsType == GOODS_TYPE_VALUE_H70BC) {
            isSupport = true;
        }
        return isSupport;
    }
}