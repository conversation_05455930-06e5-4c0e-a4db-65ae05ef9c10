package com.govee.base2home.sku;

import android.text.TextUtils;

import com.ihoment.base2app.infra.AbsConfig;
import com.ihoment.base2app.infra.LogInfra;
import com.ihoment.base2app.infra.StorageInfra;

import java.util.HashMap;

/**
 * Create by l<PERSON><PERSON><PERSON> on 11/25/20
 * 保存Wi-Fi密码
 */
public class WifiPswConfig extends AbsConfig {
    private static final String TAG = WifiPswConfig.class.getSimpleName();
    private HashMap<String, String> wifiMap = new HashMap<>();
    private boolean savePsw;


    @Override
    protected void initDefaultAttrs() {
        savePsw = false;
        wifiMap = new HashMap<>();
    }

    public static WifiPswConfig read() {
        WifiPswConfig config = StorageInfra.get(WifiPswConfig.class);
        if (config == null) {
            config = new WifiPswConfig();
            config.writeDef();
        }
        return config;
    }

    public boolean getSavePswOpen() {
        return savePsw;
    }

    public void savePswOpen(boolean savePsw) {
        this.savePsw = savePsw;
        if (LogInfra.openLog()) {
            LogInfra.Log.w(TAG, "保存开关状态：" + savePsw);
        }
        writeDef();
    }

    public String getSavePsw(String wifiName) {
        if (TextUtils.isEmpty(wifiName)) return "";
        String password = wifiMap.get(wifiName);
        return password == null ? "" : password;
    }

    public void savePsw(boolean isSave, String wifiName, String wifiPsw) {
        if (isSave && !TextUtils.isEmpty(wifiPsw)) {
            if (LogInfra.openLog()) {
                LogInfra.Log.w(TAG, "保存Wi-Fi密码，wifiName:" + wifiName + "---password:" + wifiPsw);
            }
            wifiMap.put(wifiName, wifiPsw);
        } else {
            if (LogInfra.openLog()) {
                LogInfra.Log.w(TAG, "清除Wi-Fi密码，wifiName:" + wifiName);
            }
            wifiMap.remove(wifiName);
        }
        writeDef();
    }

    public void clearAll() {
        savePsw = false;
        wifiMap.clear();
    }
}