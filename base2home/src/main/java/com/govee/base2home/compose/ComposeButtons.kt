package com.govee.base2home.compose

import androidx.annotation.ColorRes
import androidx.annotation.DimenRes
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.BoxScope
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.google.accompanist.drawablepainter.rememberDrawablePainter
import com.govee.ui.R
import com.ihoment.base2app.util.ResUtil

/**
 * 确认按钮，使用Image绘制阴影
 */
@Composable
fun ConfirmDrawableButton(
    text: String,
    onClick: () -> Unit,
    clickInterval: Long = 500L,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    @ColorRes textColor: Int = R.color.ui_btn_style_3_1_text_color,
    @DimenRes textSize: Int = R.dimen.ui_btn_style_3_1_text_size,
    @ColorRes normalColor: Int = R.color.ui_btn_style_3_1_color,
    @ColorRes pressedColor: Int = R.color.ui_btn_style_3_2_color,
    @ColorRes disabledColor: Int = R.color.ui_btn_style_3_1_color,
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val bgColor = if (enabled) {
        colorResource(if (isPressed) pressedColor else normalColor)
    } else {
        colorResource(disabledColor)
    }
    Box(
        modifier = modifier
    ) {
        Image(
            painter = rememberDrawablePainter(ResUtil.getDrawable(R.drawable.new_btn_symbol_pattern_03)),
            modifier = Modifier
                .fillMaxSize(),
            contentScale = ContentScale.FillBounds,
            contentDescription = null
        )
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(start = 9.dp, end = 9.dp, bottom = 20.dp)
                .clip(RoundedCornerShape(50))
                .background(bgColor)
                .clickNotFast(
                    interactionSource = interactionSource,
                    indication = null,
                    clickInterval = clickInterval,
                    enabled = enabled
                ) {
                    onClick()
                },
        ) {
            Text(
                text = text,
                fontSize = dimensionResourceSp(textSize),
                color = colorResource(textColor),
                modifier = Modifier
                    .wrapContentSize()
                    .align(Alignment.Center)
            )
        }
    }
}


/**
 * 确认按钮，使用graphicsLayer绘制阴影
 */
@Composable
fun ConfirmLayerButton(
    modifier: Modifier = Modifier,
    text: String,
    onClick: () -> Unit,
    enabled: Boolean = true,
    @ColorRes textColor: Int = R.color.ui_btn_style_3_1_text_color,
    @DimenRes textSize: Int = R.dimen.ui_btn_style_3_1_text_size,
    @ColorRes normalColor: Int = R.color.ui_btn_style_3_1_color,
    @ColorRes pressedColor: Int = R.color.ui_btn_style_3_2_color,
    @ColorRes disabledColor: Int = R.color.ui_btn_style_3_1_color,
    @ColorRes shadowColor: Int = R.color.ui_btn_style_3_1_color,
    elevation: Dp = 30.dp
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val bgColor = if (enabled) {
        colorResource(if (isPressed) pressedColor else normalColor)
    } else {
        colorResource(disabledColor)
    }
    val graphicsShadowColor = colorResource(shadowColor)
    Box(
        modifier = modifier
            .graphicsLayer {
                shadowElevation = elevation.toPx() // 阴影的扩散程度
                shape = CircleShape // 圆形形状
                clip = true // 裁剪内容到形状内部
                ambientShadowColor = graphicsShadowColor // 设置光晕颜色
                spotShadowColor = graphicsShadowColor // 设置光晕颜色
            }
            .padding(start = 9.dp, end = 9.dp, bottom = 20.dp)
            .clip(RoundedCornerShape(50))
            .background(bgColor)
            .clickable(
                enabled = enabled,
                interactionSource = interactionSource,
                indication = null
            ) {
                onClick()
            }

    ) {
        Text(
            text = text,
            fontSize = dimensionResourceSp(textSize),
            color = colorResource(textColor),
            modifier = Modifier
                .wrapContentSize()
                .align(Alignment.Center)
        )
    }
}


/**
 * 完全自定义用这个 使用graphicsLayer绘制阴影
 *
 * @param modifier
 * @param enabled
 * @param normalColor
 * @param pressedColor
 * @param disabledColor
 * @param shadowColor
 * @param shadowPaddingValues 按钮与阴影的padding间隔
 * @param elevation 扩散程度
 * @param onClick
 * @param content 内容
 */
@Composable
fun ConfirmBaseBtn(
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    normalColor: Color = R.color.ui_btn_style_3_1_color.color,
    pressedColor: Color = R.color.ui_btn_style_3_2_color.color,
    disabledColor: Color = R.color.ui_btn_style_3_1_color.color.copy(0.3f),
    shadowColor: Color = R.color.ui_btn_style_3_1_color.color,
    shadowPaddingValues: PaddingValues = PaddingValues(start = 9.dp, end = 9.dp, bottom = 20.dp),
    elevation: Dp = 30.dp,
    onClick: () -> Unit = {},
    content: @Composable BoxScope.() -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    val bgColor = if (enabled) {
        if (isPressed) pressedColor else normalColor
    } else {
        disabledColor
    }
    Box(
        modifier = modifier
            .padding(shadowPaddingValues)
            .graphicsLayer {
                shadowElevation = elevation.toPx() // 阴影的扩散程度
                shape = CircleShape // 圆形形状
                clip = true // 裁剪内容到形状内部
                ambientShadowColor = shadowColor // 设置光晕颜色
                spotShadowColor = shadowColor // 设置光晕颜色
            }
            .clip(RoundedCornerShape(50))
            .background(bgColor)
            .clickable(
                enabled = enabled,
                interactionSource = interactionSource,
                indication = null
            ) {
                onClick()
            },
        contentAlignment = Alignment.Center
    ) {
        content.invoke(this)
    }
}

//确认按钮
@Composable
fun ConfirmBtn(
    modifier: Modifier = Modifier,
    text: String = stringResource(R.string.confirm),
    enabled: Boolean = true,
    onClick: () -> Unit
) {
    ConfirmBaseBtn(
        modifier = modifier.height(65.dp),
        enabled = enabled,
        onClick = onClick,
        shadowPaddingValues = PaddingValues(bottom = 20.dp)
    ) {
        Text(
            text = text,
            fontSize = dimensionResourceSp(R.dimen.ui_btn_style_3_1_text_size),
            color = R.color.ui_btn_style_3_1_text_color.color,
            modifier = Modifier
                .wrapContentSize()
                .align(Alignment.Center)
        )
    }
}

//取消按钮
@Composable
fun CancelBtn(
    modifier: Modifier = Modifier,
    text: String = stringResource(R.string.cancel),
    enabled: Boolean = true,
    onClick: () -> Unit
) {
    ConfirmBaseBtn(
        modifier = modifier.height(65.dp),
        enabled = enabled,
        normalColor = R.color.ui_btn_style_5_1_color.color,
        pressedColor = R.color.ui_btn_style_5_2_color.color,
        disabledColor = R.color.ui_btn_style_5_1_color.color.copy(alpha = 0.3f),
        shadowPaddingValues = PaddingValues(bottom = 20.dp),
        onClick = onClick
    ) {
        Text(
            text = text,
            fontSize = dimensionResourceSp(R.dimen.ui_btn_style_3_1_text_size),
            color = R.color.ui_btn_style_5_1_text_color.color,
            modifier = Modifier
                .wrapContentSize()
                .align(Alignment.Center)
        )
    }
}


@Stable
inline val Int.color: Color get() = colorResource(this)