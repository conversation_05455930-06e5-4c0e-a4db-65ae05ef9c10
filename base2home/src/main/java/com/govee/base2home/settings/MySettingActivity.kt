@file:Suppress("PrivatePropertyName")

package com.govee.base2home.settings

import android.os.Bundle
import androidx.lifecycle.ViewModelProvider
import com.govee.base2home.Constant
import com.govee.base2home.account.AccountM
import com.govee.base2home.account.AccountManagerAc
import com.govee.base2home.account.LoginActivity
import com.govee.base2home.analytics.AnalyticsRecorder
import com.govee.base2home.analytics.EventKey
import com.govee.base2home.analytics.ParamFixedValue
import com.govee.base2home.analytics.ParamKey
import com.govee.base2home.community.msg.MsgSettingNewAc
import com.govee.base2home.community.user.UserInfoAc
import com.govee.base2home.custom.LoadingDialog
import com.govee.base2home.databinding.ActivityMySettingBinding
import com.govee.base2home.h5.WebActivity
import com.govee.base2home.main.about.AboutUsAc
import com.govee.base2home.storage.StorageManagementActivity
import com.govee.base2home.util.EmailUtil
import com.govee.base2home.util.TimeFormatM
import com.govee.base2kt.ac.AbsAc
import com.govee.base2kt.ext.clickDelay
import com.govee.base2kt.ext.visibleByBoolean
import com.govee.home.account.config.AccountConfig
import com.govee.ui.R
import com.govee.ui.dialog.ConfirmDialog
import com.ihoment.base2app.infra.SafeLog
import com.ihoment.base2app.util.JumpUtil
import com.ihoment.base2app.util.ResUtil

class MySettingActivity : AbsAc<ActivityMySettingBinding>() {
    private val h5_url_4_apply_4_api = "https://%1\$s/common/apply-for-api"
    private val vm: Vm4MySetting by lazy { ViewModelProvider(this)[Vm4MySetting::class.java] }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        /**初始化UI*/
        initUi()
        /**点击事件*/
        initOpClick()
        /**监听*/
        initObserver()
        /**触发检测*/
        initOp()
    }

    private fun initOp() {
        /**检测登录状态*/
        vm.checkLogin()
        /**检测openApi是否支持*/
        vm.checkOpenApi { showOpenApi ->
            SafeLog.i(TAG) { "initOp() showOpenApi = $showOpenApi" }
            viewBinding.run {
                llApiApply.visibleByBoolean(showOpenApi)
                line8.visibleByBoolean(showOpenApi)
            }
        }
        /**时间格式*/
        vm.timeFormatChange.postValue(true)
    }

    private fun initObserver() {
        /**是否登录*/
        vm.hadLogin.observe(this) { hadLogin ->
            viewBinding.line1.visibleByBoolean(hadLogin)
            viewBinding.llAccountSecurity.visibleByBoolean(hadLogin)
            viewBinding.btn2Login.visibleByBoolean(!hadLogin)
            viewBinding.btn2Logout.visibleByBoolean(hadLogin)
            hideLoading()
        }
        /**时间格式化变更*/
        vm.timeFormatChange.observe(this) { change ->
            val drawable = ResUtil.getDrawable(
                if (TimeFormatM.getInstance().is24Hours(false)
                ) R.mipmap.new_public_btn_switch_on
                else R.mipmap.new_public_btn_switch_off
            ) ?: return@observe
            viewBinding.ivTimeSetArrow.setImageDrawable(drawable)
        }
    }

    override fun getAcContentRootViewId(): Int = com.govee.base2home.R.id.ac_container

    override fun adapterInsetViewId(): Int = com.govee.base2home.R.id.top_flag

    override fun statusBarLightMode(): Boolean = false

    override fun layoutId(): Int = com.govee.base2home.R.layout.activity_my_setting

    private fun showLoading() {
        LoadingDialog.createDialog(this, com.ihoment.base2app.R.style.DialogDim, 30 * 1000L)
            .setEventKey(TAG).show()
    }

    private fun hideLoading() {
        LoadingDialog.hideDialog(TAG)
    }

    private fun initOpClick() {
        viewBinding.run {
            /**返回按钮*/
            back.clickDelay {
                finish()
            }
            /**个人信息*/
            llUserInfo.clickDelay {
                JumpUtil.jump(this@MySettingActivity, UserInfoAc::class.java, false)
            }
            /**账号与安全*/
            llAccountSecurity.clickDelay {
                JumpUtil.jump(this@MySettingActivity, AccountManagerAc::class.java)
            }
            /**消息设置*/
            llNotification.clickDelay {
                JumpUtil.jump(this@MySettingActivity, MsgSettingNewAc::class.java)
            }
            /**24小时*/
            ivTimeSetArrow.clickDelay {
                TimeFormatM.getInstance().changeSwitch(this@MySettingActivity)
            }
            /**清除缓存*/
            llClearCache.clickDelay {
                toCacheManagement()

            }
            /**开放API申请*/
            llApiApply.clickDelay {
                toApiApply()
            }
            /**联系我们-Email*/
            llEmailUs.clickDelay {
                EmailUtil.toSendContactUs(this@MySettingActivity)
                AnalyticsRecorder.getInstance()
                    .recordTimes(
                        EventKey.email_us_profile,
                        ParamKey.times,
                        ParamFixedValue.my_profile
                    )
            }
            /**关于我们*/
            llAboutUs.clickDelay {
                JumpUtil.jump(this@MySettingActivity, AboutUsAc::class.java)
            }
            /**去登录*/
            btn2Login.clickDelay {
                toLogin()
            }
            /**去登出*/
            btn2Logout.clickDelay {
                toLogout()
            }
        }
    }

    private fun toCacheManagement() {
        SafeLog.i(TAG) { "toCacheManagement() " }
        JumpUtil.jump(this, StorageManagementActivity::class.java)
    }

    private fun toApiApply() {
        val hadLogin = AccountConfig.read().isHadToken
        SafeLog.i(TAG) { "toApiApply() hadLogin = $hadLogin" }
        if (hadLogin) {
            /**已登陆-则跳转引入webAc*/
            val url = String.format(h5_url_4_apply_4_api, Constant.getH5RunModeDomainV2())
            WebActivity.jump2WebAc(this, ResUtil.getString(R.string.open_api_apply_h5_title), url)
            return
        }
        /**若未登录-则跳转到登录界面*/
        JumpUtil.jump(this, LoginActivity::class.java)
    }

    private fun toLogout() {
        val hadToken = AccountConfig.read().isHadToken
        SafeLog.i(TAG) { "toLogout() hadLogin = $hadToken" }
        if (!hadToken) return
        ConfirmDialog.showConfirmDialog(
            this,
            ResUtil.getString(R.string.dialog_logout_des),
            ResUtil.getString(R.string.no),
            ResUtil.getString(R.string.yes)
        ) {
            showLoading()
            AccountM.getInstance.logout()
        }
    }

    private fun toLogin() {
        val hadToken = AccountConfig.read().isHadToken
        SafeLog.i(TAG) { "toLogin() hadLogin = $hadToken" }
        if (hadToken) return
        JumpUtil.jump(this, LoginActivity::class.java)
    }

    private fun initUi() {
        SafeLog.i(TAG) { "initUi() " }
    }

    override fun onResume() {
        super.onResume()
        vm.checkLogin()
    }

}