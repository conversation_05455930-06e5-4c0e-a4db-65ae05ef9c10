<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ac_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/ui_bg_color_style_1"
        >

        <TextView
            android:id="@+id/top_flag"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:maxHeight="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            android:id="@+id/acp_title"
            android:layout_width="0dp"
            android:layout_height="34dp"
            android:layout_marginTop="30.5dp"
            android:gravity="center"
            android:text="@string/base2home_storage_management_title"
            android:textColor="@color/font_style_20_textColor"
            android:textSize="@dimen/font_style_20_textSize"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/top_flag"
            />

        <ImageView
            android:id="@+id/back"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="12dp"
            android:background="@mipmap/new_btn_bg_top_corner"
            android:contentDescription="@null"
            android:padding="5dp"
            android:src="@mipmap/new_sensor_setting_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="@+id/acp_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/acp_title"
            />

        <!-- 内容区域 -->
        <androidx.core.widget.NestedScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="13dp"
            android:layout_marginTop="10.5dp"
            android:layout_marginEnd="13dp"
            android:background="@drawable/component_bg_color_style_1_lr"
            android:fillViewport="true"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/acp_title"
            >

            <LinearLayout
                android:visibility="gone"
                android:id="@+id/llContent"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                >

                <!-- 应用内存概览 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="7dp"
                    android:layout_marginEnd="7dp"
                    android:orientation="vertical"
                    >

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/base2home_storage_app_memory_overview"
                        android:textColor="@color/font_style_20_textColor"
                        android:textSize="@dimen/font_style_20_textSize"
                        />

                    <TextView
                        android:id="@+id/tvTotalSize"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="5dp"
                        android:textColor="@color/font_style_97_1_textColor"
                        android:textSize="@dimen/font_style_97_1_textSize"
                        tools:text="1.25 GB"
                        />

                    <TextView
                        android:id="@+id/tvStoragePercentage"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="4dp"
                        android:textColor="@color/font_style_11_2_textColor"
                        android:textSize="@dimen/font_style_11_2_textSize"
                        tools:text="占用手机2%的储存空间"
                        />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:background="@drawable/component_bg_style_4"
                    android:orientation="vertical"
                    >

                    <!-- 临时缓存数据 -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="10dp"
                        android:layout_marginTop="15.5dp"
                        android:orientation="vertical"
                        >

                        <TextView
                            android:id="@+id/tv_content1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/base2home_storage_app_cache_data"
                            android:textColor="@color/font_style_20_textColor"
                            android:textSize="@dimen/font_style_20_textSize"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            />

                        <TextView
                            android:id="@+id/tvTempCacheSize"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:textColor="@color/font_style_97_1_textColor"
                            android:textSize="@dimen/font_style_97_1_textSize"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tv_content1"
                            tools:text="3.33GB"
                            />

                        <TextView
                            android:id="@+id/tvContent2"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/base2home_storage_app_cache_data_tips"
                            android:textColor="@color/font_style_11_2_textColor"
                            android:textSize="@dimen/font_style_11_2_textSize"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvTempCacheSize"
                            />

                        <TextView
                            android:id="@+id/btnClearCache"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:background="@drawable/component_btn_style_41"
                            android:paddingStart="19dp"
                            android:paddingTop="3.5dp"
                            android:paddingEnd="19dp"
                            android:paddingBottom="3.5dp"
                            android:text="@string/base2home_storage_app_cache_data_clean"
                            android:textColor="@color/ui_btn_style_41_1_text_color"
                            android:textSize="@dimen/ui_btn_style_41_1_text_size"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            />

                        <TextView
                            android:id="@+id/tvContent3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="14dp"
                            android:text="@string/base2home_storage_app_cache_data_clean_auto"
                            android:textColor="@color/font_style_95_6_textColor"
                            android:textSize="@dimen/font_style_95_6_textSize"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvContent2"
                            />

                        <ImageView
                            android:id="@+id/ivAutoClean"
                            android:layout_width="49dp"
                            android:layout_height="24dp"
                            android:contentDescription="@null"
                            android:src="@drawable/component_btn_switch"
                            app:layout_constraintBottom_toBottomOf="@+id/tvContent3"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="@+id/tvContent3"
                            />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:layout_width="match_parent"
                        android:layout_height="0.1dp"
                        android:layout_marginTop="20.5dp"
                        android:background="@color/ui_split_line_style_1_1"
                        />

                    <!-- 温湿度设备数据 -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/container_th_device_data"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="10dp"
                        android:layout_marginTop="18.5dp"
                        android:orientation="vertical"
                        tools:visibility="gone"
                        >

                        <TextView
                            android:id="@+id/tvThContent1"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/base2home_storage_app_th_data"
                            android:textColor="@color/font_style_20_textColor"
                            android:textSize="@dimen/font_style_20_textSize"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            />

                        <TextView
                            android:id="@+id/tvThDeviceCacheSize"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:textColor="@color/font_style_97_1_textColor"
                            android:textSize="@dimen/font_style_97_1_textSize"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvThContent1"
                            tools:text="3.33GB"
                            />

                        <TextView
                            android:id="@+id/tvThContent3"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/base2home_storage_app_th_data_tips"
                            android:textColor="@color/font_style_11_2_textColor"
                            android:textSize="@dimen/font_style_11_2_textSize"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvThDeviceCacheSize"
                            />

                        <TextView
                            android:id="@+id/tvManageDeviceData"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:background="@drawable/component_btn_style_41"
                            android:paddingStart="19dp"
                            android:paddingTop="3.5dp"
                            android:paddingEnd="19dp"
                            android:paddingBottom="3.5dp"
                            android:text="@string/base2home_storage_app_th_manager"
                            android:textColor="@color/ui_btn_style_41_1_text_color"
                            android:textSize="@dimen/ui_btn_style_41_1_text_size"
                            app:layout_constraintRight_toRightOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            />

                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <View
                        android:id="@+id/viewTHLine"
                        android:layout_width="match_parent"
                        android:layout_height="0.1dp"
                        tools:visibility="gone"
                        android:layout_marginTop="15dp"
                        android:background="@color/ui_split_line_style_1_1"
                        />

                    <!-- 系统数据 -->
                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:id="@+id/container_system_data"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="10dp"
                        android:layout_marginTop="15dp"
                        android:paddingBottom="15dp"
                        >

                        <TextView
                            android:id="@+id/tvSystemData"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="@string/base2home_storage_app_system_data"
                            android:textColor="@color/font_style_20_textColor"
                            android:textSize="@dimen/font_style_20_textSize"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toTopOf="parent"
                            />

                        <TextView
                            android:id="@+id/tvSystemDataSize"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:textColor="@color/font_style_97_1_textColor"
                            android:textSize="@dimen/font_style_97_1_textSize"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvSystemData"
                            tools:text="3.33GB"
                            />

                        <TextView
                            android:id="@+id/tvSystemDataTips"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginTop="4dp"
                            android:text="@string/base2home_storage_system_data_tips"
                            android:textColor="@color/font_style_11_2_textColor"
                            android:textSize="@dimen/font_style_11_2_textSize"
                            app:layout_constraintLeft_toLeftOf="parent"
                            app:layout_constraintTop_toBottomOf="@+id/tvSystemDataSize"
                            />

                    </androidx.constraintlayout.widget.ConstraintLayout>
                </LinearLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <!-- 加载指示器 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="75dp"
            android:layout_height="75dp"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            android:id="@+id/tvLoadingHint"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="25dp"
            android:gravity="center"
            android:text="@string/base2home_storage_loading_data"
            android:textColor="@color/font_style_4_2_textColor"
            android:textSize="@dimen/font_style_4_2_textSize"
            app:layout_constraintLeft_toLeftOf="@+id/progress_bar"
            app:layout_constraintRight_toRightOf="@+id/progress_bar"
            app:layout_constraintTop_toBottomOf="@+id/progress_bar"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
