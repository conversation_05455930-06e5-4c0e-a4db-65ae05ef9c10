<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:minHeight="78dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        >

        <!-- SKU图标 -->
        <ImageView
            android:id="@+id/ivDeviceIcon"
            android:layout_width="54dp"
            android:layout_height="54dp"
            android:layout_marginStart="11dp"
            android:contentDescription="@null"
            android:scaleType="centerCrop"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            android:id="@+id/tvDeviceName"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="9.5dp"
            android:maxWidth="166dp"
            android:maxLines="2"
            android:textColor="@color/font_style_14_1_textColor"
            android:textSize="@dimen/font_style_14_1_textSize"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/ivDeviceIcon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="客厅温湿度计"
            />

        <ImageView
            android:id="@+id/ivSelectIcon"
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:layout_marginEnd="11dp"
            android:contentDescription="@null"
            android:scaleType="centerCrop"
            android:src="@mipmap/new_public_icon_choose_type_unchoose"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            android:id="@+id/tvDataSize"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="11.5dp"
            android:textColor="@color/font_style_5_3_textColor"
            android:textSize="@dimen/font_style_5_3_textSize"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/ivSelectIcon"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="本地数据：25.6 MB"
            />


        <View
            android:id="@+id/viewLine"
            android:layout_width="match_parent"
            android:layout_height="0.1dp"
            android:layout_marginTop="20.5dp"
            android:background="@color/ui_split_line_style_1_1"
            app:layout_constraintBottom_toBottomOf="parent"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>