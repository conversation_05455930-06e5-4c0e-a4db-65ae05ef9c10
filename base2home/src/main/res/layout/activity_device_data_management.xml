<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ac_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/ui_bg_color_style_1"
        >

        <TextView
            android:id="@+id/top_flag"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:maxHeight="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            android:id="@+id/acp_title"
            android:layout_width="0dp"
            android:layout_height="34dp"
            android:layout_marginTop="30.5dp"
            android:gravity="center"
            android:text="@string/base2home_management_title"
            android:textColor="@color/font_style_20_textColor"
            android:textSize="@dimen/font_style_20_textSize"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/top_flag"
            />

        <ImageView
            android:id="@+id/back"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="12dp"
            android:background="@mipmap/new_btn_bg_top_corner"
            android:contentDescription="@null"
            android:padding="5dp"
            android:src="@mipmap/new_sensor_setting_icon_arrow_left"
            app:layout_constraintBottom_toBottomOf="@+id/acp_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/acp_title"
            />

        <TextView
            android:id="@+id/tvContentTips"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="28.5dp"
            android:gravity="center"
            android:maxWidth="288dp"
            android:text="@string/base2home_management_select_device_tipis"
            android:textColor="@color/font_style_5_3_textColor"
            android:textSize="@dimen/font_style_5_3_textSize"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/back"
            />

        <TextView
            android:id="@+id/tvSelectAll"
            android:layout_width="wrap_content"
            android:layout_height="34dp"
            android:layout_marginEnd="13dp"
            android:gravity="center"
            android:text="@string/b2light_select_all"
            android:textColor="@color/font_style_9_1_textColor"
            android:textSize="@dimen/font_style_9_1_textSize"
            app:layout_constraintBottom_toBottomOf="@+id/tvContentTips"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@+id/tvContentTips"
            />

        <!-- 设备列表 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvDeviceList"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="13.5dp"
            android:layout_marginTop="21.5dp"
            android:layout_marginEnd="13.5dp"
            android:layout_marginBottom="10dp"
            app:layout_constrainedHeight="true"
            android:background="@drawable/component_bg_style_4"
            app:layout_constraintBottom_toTopOf="@+id/tvClearData"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvContentTips"
            app:layout_constraintVertical_bias="0"
            />

        <!-- 底部按钮 -->
        <TextView
            android:id="@+id/tvClearData"
            android:layout_width="wrap_content"
            android:layout_height="65dp"
            android:layout_marginStart="7dp"
            android:background="@drawable/component_btn_style_3"
            android:gravity="center"
            android:minWidth="185dp"
            android:paddingBottom="20dp"
            android:text="@string/base2home_management_clear_data"
            android:textColor="@color/ui_btn_style_11_1_text_color"
            android:textSize="@dimen/ui_btn_style_11_1_text_size"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_goneMarginStart="5dp"
            />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
