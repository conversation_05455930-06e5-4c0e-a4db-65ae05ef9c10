<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    >

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/ac_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/ui_bg_color_style_1"
        >

        <TextView
            android:id="@+id/top_flag"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:maxHeight="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <ImageView
            android:id="@+id/iv_top_bg"
            android:layout_width="0dp"
            android:layout_height="275dp"
            android:contentDescription="@null"
            android:scaleType="fitXY"
            android:src="@mipmap/new_bg_device_blue"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />

        <TextView
            android:id="@+id/acp_title"
            android:layout_width="0dp"
            android:layout_height="34dp"
            android:layout_marginTop="30.5dp"
            android:gravity="center"
            android:text="@string/gateway_setting_title"
            android:textColor="@color/font_style_1_textColor"
            android:textSize="@dimen/font_style_1_textSize"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/top_flag"
            />

        <ImageView
            android:id="@+id/back"
            android:layout_width="34dp"
            android:layout_height="34dp"
            android:layout_marginStart="12dp"
            android:background="@drawable/component_bg_public_top_corner"
            android:contentDescription="@null"
            android:padding="5dp"
            android:src="@mipmap/new_home_icon_top_corner_back"
            app:layout_constraintBottom_toBottomOf="@+id/acp_title"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="@+id/acp_title"
            />

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scroll_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="10.5dp"
            android:background="@drawable/component_bg_color_style_1_lr"
            android:fillViewport="true"
            android:overScrollMode="never"
            android:scrollbars="none"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/acp_title"
            >

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                >

                <TextView
                    android:id="@+id/tv_bg_1"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="13.5dp"
                    android:layout_marginEnd="13.5dp"
                    android:background="@drawable/component_bg_style_4"
                    app:layout_constraintBottom_toBottomOf="@+id/ll_clear_cache"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/ll_user_info"
                    />

                <!--个人信息-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_user_info"
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:layout_marginTop="18dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    >

                    <ImageView
                        android:id="@+id/iv_user_info"
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="7dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_setting_icon_personal"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/tv_user_info"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <TextView
                        android:id="@+id/tv_user_info"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_toStartOf="@+id/iv_user_info_arrow"
                        android:layout_toEndOf="@+id/iv_user_info"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:text="@string/app_user_info"
                        android:textColor="@color/font_style_19_textColor"
                        android:textSize="@dimen/font_style_19_textSize"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/iv_user_info"
                        app:layout_constraintRight_toLeftOf="@+id/iv_user_info_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <ImageView
                        android:id="@+id/iv_user_info_arrow"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginEnd="23.5dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_update_list_arrow"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/tv_user_info"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--账户与安全-->
                <View
                    android:id="@+id/line_1"
                    android:layout_width="0dp"
                    android:layout_height="1px"
                    android:background="@color/ui_split_line_style_1_1"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_bg_1"
                    app:layout_constraintRight_toRightOf="@+id/tv_bg_1"
                    app:layout_constraintTop_toBottomOf="@+id/ll_user_info"
                    />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_account_security"
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/line_1"
                    >

                    <ImageView
                        android:id="@+id/iv_account_security"
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="7dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_setting_icon_security"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/tv_account_security"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <TextView
                        android:id="@+id/tv_account_security"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:text="@string/app_account_security"
                        android:textColor="@color/font_style_19_textColor"
                        android:textSize="@dimen/font_style_19_textSize"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/iv_account_security"
                        app:layout_constraintRight_toLeftOf="@+id/iv_account_security_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <ImageView
                        android:id="@+id/iv_account_security_arrow"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginEnd="23.5dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_update_list_arrow"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/tv_account_security"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--消息设置-->
                <View
                    android:id="@+id/line_2"
                    android:layout_width="0dp"
                    android:layout_height="1px"
                    android:background="@color/ui_split_line_style_1_1"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_bg_1"
                    app:layout_constraintRight_toRightOf="@+id/tv_bg_1"
                    app:layout_constraintTop_toBottomOf="@+id/ll_account_security"
                    />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_notification"
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/line_2"
                    >


                    <ImageView
                        android:id="@+id/iv_notification"
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="7dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_setting_icon_notification"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/tv_msg_setting"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <TextView
                        android:id="@+id/tv_msg_setting"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:text="@string/msg_setting"
                        android:textColor="@color/font_style_19_textColor"
                        android:textSize="@dimen/font_style_19_textSize"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/iv_notification"
                        app:layout_constraintRight_toLeftOf="@+id/iv_notification_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <ImageView
                        android:id="@+id/iv_notification_arrow"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginEnd="23.5dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_update_list_arrow"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/tv_msg_setting"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <!--24小时制-->
                <View
                    android:id="@+id/line_6"
                    android:layout_width="0dp"
                    android:layout_height="1px"
                    android:background="@color/ui_split_line_style_1_1"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_bg_1"
                    app:layout_constraintRight_toRightOf="@+id/tv_bg_1"
                    app:layout_constraintTop_toBottomOf="@+id/ll_notification"
                    />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_time_set"
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/line_6"
                    >


                    <ImageView
                        android:id="@+id/iv_hour"
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="7dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_setting_icon_time_set"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/tv_time_set"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <TextView
                        android:id="@+id/tv_time_set"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="@string/app_time_format_label"
                        android:textColor="@color/font_style_19_textColor"
                        android:textSize="@dimen/font_style_19_textSize"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/iv_hour"
                        app:layout_constraintRight_toLeftOf="@+id/iv_time_set_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <ImageView
                        android:id="@+id/iv_time_set_arrow"
                        android:layout_width="49dp"
                        android:layout_height="30dp"
                        android:layout_marginEnd="28dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_public_btn_switch_off"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/tv_time_set"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"

                        />
                </androidx.constraintlayout.widget.ConstraintLayout>
                <!--清除缓存-->
                <View
                    android:id="@+id/line_hours_divide_cache"
                    android:layout_width="0dp"
                    android:layout_height="1px"
                    android:background="@color/ui_split_line_style_1_1"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_bg_1"
                    app:layout_constraintRight_toRightOf="@+id/tv_bg_1"
                    app:layout_constraintTop_toBottomOf="@+id/ll_time_set"
                    />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_clear_cache"
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/line_hours_divide_cache"
                    >

                    <ImageView
                        android:id="@+id/iv_clear_cache"
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="7dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_setting_icon_clean"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/tv_clear_cache"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <TextView
                        android:id="@+id/tv_clear_cache"
                        android:layout_width="167dp"
                        android:layout_height="0dp"
                        android:layout_marginStart="7dp"
                        android:gravity="center_vertical"
                        android:maxLines="2"
                        android:text="@string/base2home_storage_management_title"
                        android:textColor="@color/font_style_19_textColor"
                        android:textSize="@dimen/font_style_19_textSize"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/iv_clear_cache"
                        app:layout_constraintRight_toLeftOf="@+id/tv_clear_cache_mb"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <ImageView
                        android:id="@+id/iv_clear_cache_arrow"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginEnd="23.5dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_update_list_arrow"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/tv_clear_cache_mb"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/tv_bg_2"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:layout_marginStart="13.5dp"
                    android:layout_marginEnd="13.5dp"
                    android:background="@drawable/component_bg_style_4"
                    app:layout_constraintBottom_toBottomOf="@+id/ll_about_us"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@+id/ll_api_apply"
                    />

                <androidx.constraintlayout.widget.Placeholder
                    android:id="@+id/interval"
                    android:layout_width="0dp"
                    android:layout_height="18dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_bg_1"
                    />

                <!--开放API申请-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_api_apply"
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    android:visibility="gone"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/interval"
                    >

                    <ImageView
                        android:id="@+id/iv_api_apply"
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="7dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_setting_icon_api"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/tv_api_apply"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <TextView
                        android:id="@+id/tv_api_apply"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:text="@string/open_api_apply_h5_title"
                        android:textColor="@color/font_style_19_textColor"
                        android:textSize="@dimen/font_style_19_textSize"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/iv_api_apply"
                        app:layout_constraintRight_toLeftOf="@+id/iv_api_apply_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <ImageView
                        android:id="@+id/iv_api_apply_arrow"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginEnd="23.5dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_update_list_arrow"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/tv_api_apply"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/line_8"
                    android:layout_width="0dp"
                    android:layout_height="1px"
                    android:background="@color/ui_split_line_style_1_1"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_bg_2"
                    app:layout_constraintRight_toRightOf="@+id/tv_bg_2"
                    app:layout_constraintTop_toBottomOf="@+id/ll_api_apply"
                    />

                <!--email 联系我们-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_email_us"
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/line_8"
                    >


                    <ImageView
                        android:id="@+id/iv_email_us"
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="7dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_setting_icon_email_us"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/tv_email_us"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <TextView
                        android:id="@+id/tv_email_us"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:text="@string/help_hint"
                        android:textColor="@color/font_style_19_textColor"
                        android:textSize="@dimen/font_style_19_textSize"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/iv_email_us"
                        app:layout_constraintRight_toLeftOf="@+id/iv_email_us_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <ImageView
                        android:id="@+id/iv_email_us_arrow"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginEnd="23.5dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_update_list_arrow"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/tv_email_us"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <View
                    android:id="@+id/line_7"
                    android:layout_width="0dp"
                    android:layout_height="1px"
                    android:background="@color/ui_split_line_style_1_1"
                    app:layout_constraintLeft_toLeftOf="@+id/tv_bg_2"
                    app:layout_constraintRight_toRightOf="@+id/tv_bg_2"
                    app:layout_constraintTop_toBottomOf="@+id/ll_email_us"
                    />

                <!--关于我们-->
                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/ll_about_us"
                    android:layout_width="0dp"
                    android:layout_height="70dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/line_7"
                    >


                    <ImageView
                        android:id="@+id/iv_about_us"
                        android:layout_width="50dp"
                        android:layout_height="60dp"
                        android:layout_marginStart="20dp"
                        android:layout_marginEnd="7dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_setting_icon_about_govee"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHorizontal_chainStyle="packed"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toLeftOf="@+id/tv_about_us"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <TextView
                        android:id="@+id/tv_about_us"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_weight="1"
                        android:gravity="center_vertical"
                        android:maxLines="1"
                        android:text="@string/about_us_label"
                        android:textColor="@color/font_style_19_textColor"
                        android:textSize="@dimen/font_style_19_textSize"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/iv_about_us"
                        app:layout_constraintRight_toLeftOf="@+id/iv_about_us_arrow"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                    <ImageView
                        android:id="@+id/iv_about_us_arrow"
                        android:layout_width="30dp"
                        android:layout_height="30dp"
                        android:layout_marginEnd="23.5dp"
                        android:contentDescription="@null"
                        android:src="@mipmap/new_update_list_arrow"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toRightOf="@+id/tv_about_us"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        />

                </androidx.constraintlayout.widget.ConstraintLayout>

                <TextView
                    android:id="@+id/btn2Login"
                    android:layout_width="wrap_content"
                    android:layout_height="65dp"
                    android:layout_marginTop="41.5dp"
                    android:background="@drawable/component_btn_style_11"
                    android:gravity="center"
                    android:minWidth="185dp"
                    android:paddingStart="15dp"
                    android:paddingEnd="15dp"
                    android:paddingBottom="20dp"
                    android:text="@string/to_login_now"
                    android:textColor="@color/ui_btn_style_11_1_text_color"
                    android:textSize="@dimen/ui_btn_style_6_1_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_bg_2"
                    />

                <TextView
                    android:id="@+id/btn2Logout"
                    android:layout_width="wrap_content"
                    android:layout_height="65dp"
                    android:layout_marginTop="41.5dp"
                    android:background="@drawable/component_btn_style_6"
                    android:gravity="center"
                    android:minWidth="185dp"
                    android:paddingStart="15dp"
                    android:paddingEnd="15dp"
                    android:paddingBottom="20dp"
                    android:text="@string/log_out"
                    android:textColor="@color/ui_btn_style_6_1_text_color"
                    android:textSize="@dimen/ui_btn_style_6_1_text_size"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_bg_2"
                    />

                <androidx.constraintlayout.widget.Placeholder
                    android:layout_width="0dp"
                    android:layout_height="121.5dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/tv_bg_2"
                    />
            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.core.widget.NestedScrollView>


    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>
