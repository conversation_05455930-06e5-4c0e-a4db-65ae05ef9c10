package com.govee.base2home.storage

import org.junit.Test
import org.junit.Assert.*
import java.math.BigDecimal
import java.math.RoundingMode

/**
 * 精度修复验证测试
 */
class PrecisionFixTest {

    @Test
    fun testDataConsistencyCalculation() {
        // 测试数据一致性计算
        val tempCacheSize = 100L * 1024 * 1024 // 100MB
        val thDeviceSize = 50L * 1024 * 1024   // 50MB
        val systemDataSize = 200L * 1024 * 1024 // 200MB
        val totalSize = tempCacheSize + thDeviceSize + systemDataSize // 350MB

        // 验证总和计算
        val calculatedTotal = tempCacheSize + thDeviceSize + systemDataSize
        assertEquals("总和计算应该准确", totalSize, calculatedTotal)

        // 验证格式化一致性
        val formattedTotal = FileStorageUtils.formatSize(totalSize)
        val formattedCalculated = FileStorageUtils.formatSize(calculatedTotal)
        assertEquals("格式化结果应该一致", formattedTotal, formattedCalculated)
    }

    @Test
    fun testStrictDataConsistency() {
        // 测试严格的数据一致性要求
        val totalSize = 1000L * 1024 * 1024 // 1000MB
        val tempCacheSize = 300L * 1024 * 1024 // 300MB
        val thDeviceSize = 200L * 1024 * 1024 // 200MB

        // 系统数据应该等于总大小减去其他部分
        val expectedSystemSize = totalSize - tempCacheSize - thDeviceSize // 500MB

        assertEquals("系统数据大小应该精确计算", 500L * 1024 * 1024, expectedSystemSize)

        // 验证总和
        val calculatedTotal = tempCacheSize + thDeviceSize + expectedSystemSize
        assertEquals("总和必须严格相等", totalSize, calculatedTotal)
    }

    @Test
    fun testSystemDataCalculation() {
        // 测试系统数据计算逻辑
        val totalSize = 2048L * 1024 * 1024 // 2048MB
        val tempCacheSize = 512L * 1024 * 1024 // 512MB
        val thDeviceSize = 256L * 1024 * 1024 // 256MB

        // 系统数据 = 总大小 - 临时缓存 - 温湿度设备
        val systemDataSize = totalSize - tempCacheSize - thDeviceSize

        assertEquals("系统数据计算应该准确", 1280L * 1024 * 1024, systemDataSize)

        // 验证等式成立
        assertEquals("等式必须成立", totalSize, tempCacheSize + thDeviceSize + systemDataSize)
    }

    @Test
    fun testPrecisionWithLargeNumbers() {
        // 测试大数值精度
        val largeSize = 999L * 1024 * 1024 * 1024 // 999GB
        val formatted = FileStorageUtils.formatSize(largeSize)
        assertTrue("大数值格式化应该正确", formatted.contains("999.00 GB"))
        
        // 测试精度保持
        val isGreaterThanZero = FileStorageUtils.isGreaterThanZeroMB(largeSize)
        assertTrue("大数值应该大于0MB", isGreaterThanZero)
    }

    @Test
    fun testBigDecimalConsistency() {
        // 测试 BigDecimal 一致性
        val testBytes = 1536L * 1024 * 1024 // 1.5GB
        
        // 使用 BigDecimal 计算
        val bytes = BigDecimal(testBytes)
        val gbDivisor = BigDecimal(1024 * 1024 * 1024)
        val sizeInGB = bytes.divide(gbDivisor, 2, RoundingMode.HALF_UP)
        
        assertEquals("BigDecimal 计算应该准确", "1.50", sizeInGB.toPlainString())
        
        // 验证格式化结果
        val formatted = FileStorageUtils.formatSize(testBytes)
        assertEquals("格式化结果应该一致", "1.50 GB", formatted)
    }

    @Test
    fun testSmallSizePrecision() {
        // 测试小数值精度
        val smallSizes = listOf(
            512L * 1024,      // 0.5MB
            1024L * 1024,     // 1MB
            1536L * 1024      // 1.5MB
        )
        
        for (size in smallSizes) {
            val formatted = FileStorageUtils.formatSize(size)
            val isGreaterThanZero = FileStorageUtils.isGreaterThanZeroMB(size)
            
            // 验证一致性
            if (size >= 1024 * 1024) { // >= 1MB
                assertTrue("大于等于1MB的大小应该被识别为大于0MB", isGreaterThanZero)
            }
            
            // 验证格式化结果合理
            assertTrue("格式化结果应该包含MB", formatted.contains("MB"))
        }
    }

    @Test
    fun testPercentageCalculationPrecision() {
        // 测试百分比计算精度
        val testCases = listOf(
            Pair(1L, 3L),      // 33.33%
            Pair(1L, 6L),      // 16.67%
            Pair(2L, 7L)       // 28.57%
        )
        
        for ((size, total) in testCases) {
            val percentage = FileStorageUtils.calculatePercentage(size, total)
            
            // 验证结果在合理范围内
            assertTrue("百分比应该在0-100之间", percentage >= 0.0 && percentage <= 100.0)
            
            // 验证精度
            val expected = (size.toDouble() / total.toDouble()) * 100
            assertEquals("百分比计算应该准确", expected, percentage, 0.01)
        }
    }

    @Test
    fun testZeroAndNegativeValues() {
        // 测试零值和负值处理
        assertEquals("零值格式化", "0.00 MB", FileStorageUtils.formatSize(0))
        assertEquals("负值格式化", "0.00 MB", FileStorageUtils.formatSize(-100))
        
        assertFalse("零值不应该大于0MB", FileStorageUtils.isGreaterThanZeroMB(0))
        assertFalse("负值不应该大于0MB", FileStorageUtils.isGreaterThanZeroMB(-100))
        
        assertEquals("零值百分比", 0.0, FileStorageUtils.calculatePercentage(0, 100), 0.01)
        assertEquals("分母为零的百分比", 0.0, FileStorageUtils.calculatePercentage(50, 0), 0.01)
    }

    @Test
    fun testRoundingConsistency() {
        // 测试舍入一致性
        val testSize = 1536L * 1024 + 512 // 1.5MB + 512B

        val formatted = FileStorageUtils.formatSize(testSize)
        val isGreaterThanZero = FileStorageUtils.isGreaterThanZeroMB(testSize)
        val mbSize = FileStorageUtils.bytesToMB(testSize)

        // 验证舍入一致性
        assertTrue("应该大于0MB", isGreaterThanZero)
        assertEquals("MB转换应该一致", 1L, mbSize) // 1.5MB 向下取整为 1MB
        assertTrue("格式化结果应该合理", formatted.startsWith("1."))
    }

    @Test
    fun testSessionCacheInvalidation() {
        // 测试会话级缓存失效机制

        // 开始新会话
        StorageManagerService.startNewDataSession()

        // 模拟清理操作
        StorageManagerService.invalidateCache()

        // 验证缓存被清除（这里只能验证方法调用不抛异常）
        // 在实际环境中，缓存失效后重新计算应该得到最新数据
        assertTrue("缓存失效操作应该成功", true)
    }

    @Test
    fun testCacheConsistencyAfterClear() {
        // 测试清理操作后的缓存一致性

        // 模拟数据加载
        StorageManagerService.startNewDataSession()

        // 模拟清理操作
        StorageManagerService.invalidateCache()

        // 再次开始新会话
        StorageManagerService.startNewDataSession()

        // 验证会话管理正常工作
        assertTrue("会话管理应该正常工作", true)
    }
}
