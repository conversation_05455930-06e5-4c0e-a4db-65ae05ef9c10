package com.govee.base2home.storage

import org.junit.Test
import org.junit.Assert.*

/**
 * 编译测试 - 验证所有代码都能正确编译
 */
class CompilationTest {

    @Test
    fun testStorageManagerServiceCompilation() {
        // 测试 StorageManagerService 的方法签名是否正确
        
        // 验证会话管理方法存在
        StorageManagerService.startNewDataSession()
        StorageManagerService.invalidateCache()
        
        // 验证自动清理设置方法存在
        StorageManagerService.setAutoCleanEnabled(true)
        val isEnabled = StorageManagerService.isAutoCleanEnabled()
        assertTrue("自动清理设置应该正常工作", isEnabled)
        
        StorageManagerService.setAutoCleanEnabled(false)
        assertFalse("自动清理设置应该正常工作", StorageManagerService.isAutoCleanEnabled())
    }

    @Test
    fun testFileStorageUtilsCompilation() {
        // 测试 FileStorageUtils 的方法是否正确
        
        val testSize = 1024L * 1024L // 1MB
        
        // 验证格式化方法
        val formatted = FileStorageUtils.formatSize(testSize)
        assertNotNull("格式化方法应该返回非空结果", formatted)
        
        // 验证大小判断方法
        val isGreaterThanZero = FileStorageUtils.isGreaterThanZeroMB(testSize)
        assertTrue("1MB应该大于0MB", isGreaterThanZero)
        
        // 验证百分比计算方法
        val percentage = FileStorageUtils.calculatePercentage(testSize, testSize * 2)
        assertEquals("百分比计算应该正确", 50.0, percentage, 0.01)
    }

    @Test
    fun testDataModelCompilation() {
        // 测试数据模型是否正确定义
        
        val cacheData = com.govee.base2home.storage.model.CacheDataModel(
            type = com.govee.base2home.storage.model.CacheType.VIDEO_CACHE,
            name = "测试缓存",
            size = 1024L,
            paths = listOf("/test/path")
        )
        
        assertEquals("缓存数据模型应该正确", "测试缓存", cacheData.name)
        
        val deviceData = com.govee.base2home.storage.model.DeviceDataModel(
            sku = "H5100",
            device = "test_device",
            deviceName = "测试设备",
            size = 2048L,
            isSelected = false
        )
        
        assertEquals("设备数据模型应该正确", "测试设备", deviceData.deviceName)
        
        val memoryOverview = com.govee.base2home.storage.model.AppMemoryOverviewModel(
            totalSize = 1024L * 1024L,
            deviceTotalStorage = 10L * 1024L * 1024L,
            percentage = 10.0
        )
        
        assertEquals("内存概览模型应该正确", 10.0, memoryOverview.percentage, 0.01)
    }

    @Test
    fun testEnumCompilation() {
        // 测试枚举类型是否正确定义
        
        val cacheType = com.govee.base2home.storage.model.CacheType.VIDEO_CACHE
        assertNotNull("缓存类型枚举应该存在", cacheType)
        
        val cleanPeriod = com.govee.base2home.storage.model.CleanPeriodType.KEEP_7_DAYS
        assertEquals("清理时间段应该正确", 7, cleanPeriod.value)
        assertEquals("清理时间段显示名应该正确", "保留最近7天", cleanPeriod.displayName)
    }
}
